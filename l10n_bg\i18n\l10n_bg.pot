# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_bg
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-04-13 07:19+0000\n"
"PO-Revision-Date: 2022-04-13 07:19+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_bg
#: model:account.tax,description:l10n_bg.1_l10n_bg_sale_vat_0_140
#: model:account.tax,description:l10n_bg.2_l10n_bg_sale_vat_0_140
#: model:account.tax,name:l10n_bg.1_l10n_bg_sale_vat_0_140
#: model:account.tax,name:l10n_bg.2_l10n_bg_sale_vat_0_140
#: model:account.tax.template,description:l10n_bg.l10n_bg_sale_vat_0_140
#: model:account.tax.template,name:l10n_bg.l10n_bg_sale_vat_0_140
msgid "0% Art. 140, 146, 173"
msgstr ""

#. module: l10n_bg
#: model:account.tax,description:l10n_bg.1_l10n_bg_sale_vat_0_21
#: model:account.tax,description:l10n_bg.2_l10n_bg_sale_vat_0_21
#: model:account.tax,name:l10n_bg.1_l10n_bg_sale_vat_0_21
#: model:account.tax,name:l10n_bg.2_l10n_bg_sale_vat_0_21
#: model:account.tax.template,description:l10n_bg.l10n_bg_sale_vat_0_21
#: model:account.tax.template,name:l10n_bg.l10n_bg_sale_vat_0_21
msgid "0% Art. 21"
msgstr ""

#. module: l10n_bg
#: model:account.tax,description:l10n_bg.1_l10n_bg_sale_vat_0_exempt
#: model:account.tax,description:l10n_bg.2_l10n_bg_sale_vat_0_exempt
#: model:account.tax,name:l10n_bg.1_l10n_bg_sale_vat_0_exempt
#: model:account.tax,name:l10n_bg.2_l10n_bg_sale_vat_0_exempt
#: model:account.tax.template,description:l10n_bg.l10n_bg_sale_vat_0_exempt
#: model:account.tax.template,name:l10n_bg.l10n_bg_sale_vat_0_exempt
msgid "0% Exempt"
msgstr ""

#. module: l10n_bg
#: model:account.tax,description:l10n_bg.1_l10n_bg_sale_vat_0_export
#: model:account.tax,description:l10n_bg.2_l10n_bg_sale_vat_0_export
#: model:account.tax,name:l10n_bg.1_l10n_bg_sale_vat_0_export
#: model:account.tax,name:l10n_bg.2_l10n_bg_sale_vat_0_export
#: model:account.tax.template,description:l10n_bg.l10n_bg_sale_vat_0_export
#: model:account.tax.template,name:l10n_bg.l10n_bg_sale_vat_0_export
msgid "0% Export"
msgstr ""

#. module: l10n_bg
#: model:account.tax,name:l10n_bg.1_l10n_bg_sale_vat_0_icd
#: model:account.tax,name:l10n_bg.2_l10n_bg_sale_vat_0_icd
#: model:account.tax.template,name:l10n_bg.l10n_bg_sale_vat_0_icd
msgid "0% ICD"
msgstr ""

#. module: l10n_bg
#: model:account.tax,description:l10n_bg.1_l10n_bg_sale_vat_0_icd
#: model:account.tax,description:l10n_bg.2_l10n_bg_sale_vat_0_icd
#: model:account.tax.template,description:l10n_bg.l10n_bg_sale_vat_0_icd
msgid "0% Intra-Community Deliveries"
msgstr ""

#. module: l10n_bg
#: model:account.tax,name:l10n_bg.1_l10n_bg_purchase_vat_0_otc_exempt
#: model:account.tax,name:l10n_bg.2_l10n_bg_purchase_vat_0_otc_exempt
#: model:account.tax.template,name:l10n_bg.l10n_bg_purchase_vat_0_otc_exempt
msgid "0% OTC (Exempt)"
msgstr ""

#. module: l10n_bg
#: model:account.tax,name:l10n_bg.1_l10n_bg_purchase_vat_0_otc_ica
#: model:account.tax,name:l10n_bg.2_l10n_bg_purchase_vat_0_otc_ica
#: model:account.tax.template,name:l10n_bg.l10n_bg_purchase_vat_0_otc_ica
msgid "0% OTC (ICA)"
msgstr ""

#. module: l10n_bg
#: model:account.tax,description:l10n_bg.1_l10n_bg_purchase_vat_0_otc_exempt
#: model:account.tax,description:l10n_bg.2_l10n_bg_purchase_vat_0_otc_exempt
#: model:account.tax.template,description:l10n_bg.l10n_bg_purchase_vat_0_otc_exempt
msgid "0% Other Tax Credit – Exempt"
msgstr ""

#. module: l10n_bg
#: model:account.tax,description:l10n_bg.1_l10n_bg_purchase_vat_0_otc_ica
#: model:account.tax,description:l10n_bg.2_l10n_bg_purchase_vat_0_otc_ica
#: model:account.tax.template,description:l10n_bg.l10n_bg_purchase_vat_0_otc_ica
msgid "0% Other Tax Credit – Intra-Community Acquisition"
msgstr ""

#. module: l10n_bg
#: model:account.tax,description:l10n_bg.1_l10n_bg_purchase_vat_0_tri
#: model:account.tax,description:l10n_bg.1_l10n_bg_sale_vat_0_tri
#: model:account.tax,description:l10n_bg.2_l10n_bg_purchase_vat_0_tri
#: model:account.tax,description:l10n_bg.2_l10n_bg_sale_vat_0_tri
#: model:account.tax,name:l10n_bg.1_l10n_bg_purchase_vat_0_tri
#: model:account.tax,name:l10n_bg.1_l10n_bg_sale_vat_0_tri
#: model:account.tax,name:l10n_bg.2_l10n_bg_purchase_vat_0_tri
#: model:account.tax,name:l10n_bg.2_l10n_bg_sale_vat_0_tri
#: model:account.tax.template,description:l10n_bg.l10n_bg_purchase_vat_0_tri
#: model:account.tax.template,description:l10n_bg.l10n_bg_sale_vat_0_tri
#: model:account.tax.template,name:l10n_bg.l10n_bg_purchase_vat_0_tri
#: model:account.tax.template,name:l10n_bg.l10n_bg_sale_vat_0_tri
msgid "0% Tripartite"
msgstr ""

#. module: l10n_bg
#: model:account.tax.group,name:l10n_bg.tax_group_vat_0
msgid "0% VAT"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,tag_name:l10n_bg.l10n_bg_tax_report_11
msgid "11"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,tag_name:l10n_bg.l10n_bg_tax_report_12_1
msgid "12_1"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,tag_name:l10n_bg.l10n_bg_tax_report_12_2
msgid "12_2"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,tag_name:l10n_bg.l10n_bg_tax_report_13
msgid "13"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,tag_name:l10n_bg.l10n_bg_tax_report_14
msgid "14"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,tag_name:l10n_bg.l10n_bg_tax_report_15
msgid "15"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,tag_name:l10n_bg.l10n_bg_tax_report_16
msgid "16"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,tag_name:l10n_bg.l10n_bg_tax_report_17
msgid "17"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,tag_name:l10n_bg.l10n_bg_tax_report_18
msgid "18"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,tag_name:l10n_bg.l10n_bg_tax_report_19
msgid "19"
msgstr ""

#. module: l10n_bg
#: model:account.tax,name:l10n_bg.1_l10n_bg_purchase_vat_20_ftc
#: model:account.tax,name:l10n_bg.2_l10n_bg_purchase_vat_20_ftc
#: model:account.tax.template,name:l10n_bg.l10n_bg_purchase_vat_20_ftc
msgid "20% FTC"
msgstr ""

#. module: l10n_bg
#: model:account.tax,name:l10n_bg.1_l10n_bg_purchase_vat_20_ftc_82
#: model:account.tax,name:l10n_bg.2_l10n_bg_purchase_vat_20_ftc_82
#: model:account.tax.template,name:l10n_bg.l10n_bg_purchase_vat_20_ftc_82
msgid "20% FTC (Art. 82)"
msgstr ""

#. module: l10n_bg
#: model:account.tax,name:l10n_bg.1_l10n_bg_purchase_vat_20_ftc_exempt_ica
#: model:account.tax,name:l10n_bg.2_l10n_bg_purchase_vat_20_ftc_exempt_ica
#: model:account.tax.template,name:l10n_bg.l10n_bg_purchase_vat_20_ftc_exempt_ica
msgid "20% FTC (Exempt ICA)"
msgstr ""

#. module: l10n_bg
#: model:account.tax,name:l10n_bg.1_l10n_bg_purchase_vat_20_ftc_ica
#: model:account.tax,name:l10n_bg.2_l10n_bg_purchase_vat_20_ftc_ica
#: model:account.tax.template,name:l10n_bg.l10n_bg_purchase_vat_20_ftc_ica
msgid "20% FTC (ICA)"
msgstr ""

#. module: l10n_bg
#: model:account.tax,description:l10n_bg.1_l10n_bg_purchase_vat_20_ftc
#: model:account.tax,description:l10n_bg.2_l10n_bg_purchase_vat_20_ftc
#: model:account.tax.template,description:l10n_bg.l10n_bg_purchase_vat_20_ftc
msgid "20% Foreign Tax Credit"
msgstr ""

#. module: l10n_bg
#: model:account.tax,description:l10n_bg.1_l10n_bg_purchase_vat_20_ftc_82
#: model:account.tax,description:l10n_bg.2_l10n_bg_purchase_vat_20_ftc_82
#: model:account.tax.template,description:l10n_bg.l10n_bg_purchase_vat_20_ftc_82
msgid "20% Foreign Tax Credit – Art. 82"
msgstr ""

#. module: l10n_bg
#: model:account.tax,description:l10n_bg.1_l10n_bg_purchase_vat_20_ftc_exempt_ica
#: model:account.tax,description:l10n_bg.2_l10n_bg_purchase_vat_20_ftc_exempt_ica
#: model:account.tax.template,description:l10n_bg.l10n_bg_purchase_vat_20_ftc_exempt_ica
msgid "20% Foreign Tax Credit – Exempt Intra-Community Acquisition"
msgstr ""

#. module: l10n_bg
#: model:account.tax,description:l10n_bg.1_l10n_bg_purchase_vat_20_ftc_ica
#: model:account.tax,description:l10n_bg.2_l10n_bg_purchase_vat_20_ftc_ica
#: model:account.tax.template,description:l10n_bg.l10n_bg_purchase_vat_20_ftc_ica
msgid "20% Foreign Tax Credit – Intra-Community Acquisition"
msgstr ""

#. module: l10n_bg
#: model:account.tax,name:l10n_bg.1_l10n_bg_purchase_vat_20_otc
#: model:account.tax,name:l10n_bg.2_l10n_bg_purchase_vat_20_otc
#: model:account.tax.template,name:l10n_bg.l10n_bg_purchase_vat_20_otc
msgid "20% OTC"
msgstr ""

#. module: l10n_bg
#: model:account.tax,description:l10n_bg.1_l10n_bg_purchase_vat_20_otc
#: model:account.tax,description:l10n_bg.2_l10n_bg_purchase_vat_20_otc
#: model:account.tax.template,description:l10n_bg.l10n_bg_purchase_vat_20_otc
msgid "20% Other Tax Credit"
msgstr ""

#. module: l10n_bg
#: model:account.tax,name:l10n_bg.1_l10n_bg_purchase_vat_20_ptc
#: model:account.tax,name:l10n_bg.2_l10n_bg_purchase_vat_20_ptc
#: model:account.tax.template,name:l10n_bg.l10n_bg_purchase_vat_20_ptc
msgid "20% PTC"
msgstr ""

#. module: l10n_bg
#: model:account.tax,name:l10n_bg.1_l10n_bg_purchase_vat_20_ptc_82
#: model:account.tax,name:l10n_bg.2_l10n_bg_purchase_vat_20_ptc_82
#: model:account.tax.template,name:l10n_bg.l10n_bg_purchase_vat_20_ptc_82
msgid "20% PTC (Art. 82)"
msgstr ""

#. module: l10n_bg
#: model:account.tax,name:l10n_bg.1_l10n_bg_purchase_vat_20_ptc_exempt_ica
#: model:account.tax,name:l10n_bg.2_l10n_bg_purchase_vat_20_ptc_exempt_ica
#: model:account.tax.template,name:l10n_bg.l10n_bg_purchase_vat_20_ptc_exempt_ica
msgid "20% PTC (Exempt ICA)"
msgstr ""

#. module: l10n_bg
#: model:account.tax,name:l10n_bg.1_l10n_bg_purchase_vat_20_ptc_ica
#: model:account.tax,name:l10n_bg.2_l10n_bg_purchase_vat_20_ptc_ica
#: model:account.tax.template,name:l10n_bg.l10n_bg_purchase_vat_20_ptc_ica
msgid "20% PTC (ICA)"
msgstr ""

#. module: l10n_bg
#: model:account.tax,description:l10n_bg.1_l10n_bg_purchase_vat_20_ptc
#: model:account.tax,description:l10n_bg.2_l10n_bg_purchase_vat_20_ptc
#: model:account.tax.template,description:l10n_bg.l10n_bg_purchase_vat_20_ptc
msgid "20% Payable Tax Credit"
msgstr ""

#. module: l10n_bg
#: model:account.tax,description:l10n_bg.1_l10n_bg_purchase_vat_20_ptc_82
#: model:account.tax,description:l10n_bg.2_l10n_bg_purchase_vat_20_ptc_82
#: model:account.tax.template,description:l10n_bg.l10n_bg_purchase_vat_20_ptc_82
msgid "20% Payable Tax Credit - Art. 82"
msgstr ""

#. module: l10n_bg
#: model:account.tax,description:l10n_bg.1_l10n_bg_purchase_vat_20_ptc_exempt_ica
#: model:account.tax,description:l10n_bg.2_l10n_bg_purchase_vat_20_ptc_exempt_ica
#: model:account.tax.template,description:l10n_bg.l10n_bg_purchase_vat_20_ptc_exempt_ica
msgid "20% Payable Tax Credit – Exempt Intra-Community Acquisition"
msgstr ""

#. module: l10n_bg
#: model:account.tax,description:l10n_bg.1_l10n_bg_purchase_vat_20_ptc_ica
#: model:account.tax,description:l10n_bg.2_l10n_bg_purchase_vat_20_ptc_ica
#: model:account.tax.template,description:l10n_bg.l10n_bg_purchase_vat_20_ptc_ica
msgid "20% Payable Tax Credit – Intra-Community Acquisition"
msgstr ""

#. module: l10n_bg
#: model:account.tax,description:l10n_bg.1_l10n_bg_sale_vat_20_personal
#: model:account.tax,description:l10n_bg.2_l10n_bg_sale_vat_20_personal
#: model:account.tax,name:l10n_bg.1_l10n_bg_sale_vat_20_personal
#: model:account.tax,name:l10n_bg.2_l10n_bg_sale_vat_20_personal
#: model:account.tax.template,description:l10n_bg.l10n_bg_sale_vat_20_personal
#: model:account.tax.template,name:l10n_bg.l10n_bg_sale_vat_20_personal
msgid "20% Personal use"
msgstr ""

#. module: l10n_bg
#: model:account.tax,description:l10n_bg.1_l10n_bg_sale_vat_20_remote
#: model:account.tax,description:l10n_bg.2_l10n_bg_sale_vat_20_remote
#: model:account.tax,name:l10n_bg.1_l10n_bg_sale_vat_20_remote
#: model:account.tax,name:l10n_bg.2_l10n_bg_sale_vat_20_remote
#: model:account.tax.template,description:l10n_bg.l10n_bg_sale_vat_20_remote
#: model:account.tax.template,name:l10n_bg.l10n_bg_sale_vat_20_remote
msgid "20% Remote"
msgstr ""

#. module: l10n_bg
#: model:account.tax,description:l10n_bg.1_l10n_bg_sale_vat_20
#: model:account.tax,description:l10n_bg.2_l10n_bg_sale_vat_20
#: model:account.tax,name:l10n_bg.1_l10n_bg_sale_vat_20
#: model:account.tax,name:l10n_bg.2_l10n_bg_sale_vat_20
#: model:account.tax.group,name:l10n_bg.tax_group_vat_20
#: model:account.tax.template,description:l10n_bg.l10n_bg_sale_vat_20
#: model:account.tax.template,name:l10n_bg.l10n_bg_sale_vat_20
msgid "20% VAT"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,tag_name:l10n_bg.l10n_bg_tax_report_21
msgid "21"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,tag_name:l10n_bg.l10n_bg_tax_report_22
msgid "22"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,tag_name:l10n_bg.l10n_bg_tax_report_23
msgid "23"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,tag_name:l10n_bg.l10n_bg_tax_report_24
msgid "24"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,tag_name:l10n_bg.l10n_bg_tax_report_30
msgid "30"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,tag_name:l10n_bg.l10n_bg_tax_report_31
msgid "31"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,tag_name:l10n_bg.l10n_bg_tax_report_32
msgid "32"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,tag_name:l10n_bg.l10n_bg_tax_report_41
msgid "41"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,tag_name:l10n_bg.l10n_bg_tax_report_42
msgid "42"
msgstr ""

#. module: l10n_bg
#: model:account.tax,name:l10n_bg.1_l10n_bg_purchase_vat_9_ftc
#: model:account.tax,name:l10n_bg.2_l10n_bg_purchase_vat_9_ftc
#: model:account.tax.template,name:l10n_bg.l10n_bg_purchase_vat_9_ftc
msgid "9% FTC"
msgstr ""

#. module: l10n_bg
#: model:account.tax,description:l10n_bg.1_l10n_bg_purchase_vat_9_ftc
#: model:account.tax,description:l10n_bg.2_l10n_bg_purchase_vat_9_ftc
#: model:account.tax.template,description:l10n_bg.l10n_bg_purchase_vat_9_ftc
msgid "9% Foreign Tax Credit"
msgstr ""

#. module: l10n_bg
#: model:account.tax,name:l10n_bg.1_l10n_bg_purchase_vat_9_otc
#: model:account.tax,name:l10n_bg.2_l10n_bg_purchase_vat_9_otc
#: model:account.tax.template,name:l10n_bg.l10n_bg_purchase_vat_9_otc
msgid "9% OTC"
msgstr ""

#. module: l10n_bg
#: model:account.tax,description:l10n_bg.1_l10n_bg_purchase_vat_9_otc
#: model:account.tax,description:l10n_bg.2_l10n_bg_purchase_vat_9_otc
#: model:account.tax.template,description:l10n_bg.l10n_bg_purchase_vat_9_otc
msgid "9% Other Tax Credit"
msgstr ""

#. module: l10n_bg
#: model:account.tax,description:l10n_bg.1_l10n_bg_sale_vat_9_personal
#: model:account.tax,description:l10n_bg.2_l10n_bg_sale_vat_9_personal
#: model:account.tax,name:l10n_bg.1_l10n_bg_sale_vat_9_personal
#: model:account.tax,name:l10n_bg.2_l10n_bg_sale_vat_9_personal
#: model:account.tax.template,description:l10n_bg.l10n_bg_sale_vat_9_personal
#: model:account.tax.template,name:l10n_bg.l10n_bg_sale_vat_9_personal
msgid "9% Personal use"
msgstr ""

#. module: l10n_bg
#: model:account.tax,description:l10n_bg.1_l10n_bg_sale_vat_9
#: model:account.tax,description:l10n_bg.2_l10n_bg_sale_vat_9
#: model:account.tax,name:l10n_bg.1_l10n_bg_sale_vat_9
#: model:account.tax,name:l10n_bg.2_l10n_bg_sale_vat_9
#: model:account.tax.group,name:l10n_bg.tax_group_vat_9
#: model:account.tax.template,description:l10n_bg.l10n_bg_sale_vat_9
#: model:account.tax.template,name:l10n_bg.l10n_bg_sale_vat_9
msgid "9% VAT"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_422
#: model:account.account,name:l10n_bg.2_l10n_bg_422
#: model:account.account.template,name:l10n_bg.l10n_bg_422
msgid "Accountable persons"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_5
#: model:account.group,name:l10n_bg.2_l10n_bg_group_5
#: model:account.group.template,name:l10n_bg.l10n_bg_group_5
msgid "Accounts for financial resources"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_2
#: model:account.group,name:l10n_bg.2_l10n_bg_group_2
#: model:account.group.template,name:l10n_bg.l10n_bg_group_2
msgid "Accounts for fixed assets"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_3
#: model:account.group,name:l10n_bg.2_l10n_bg_group_3
#: model:account.group.template,name:l10n_bg.l10n_bg_group_3
msgid "Accounts for inventories"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_4
#: model:account.group,name:l10n_bg.2_l10n_bg_group_4
#: model:account.group.template,name:l10n_bg.l10n_bg_group_4
msgid "Accounts for settlements"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_982
#: model:account.account,name:l10n_bg.2_l10n_bg_982
#: model:account.account.template,name:l10n_bg.l10n_bg_982
msgid "Accounts receivable"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_68
#: model:account.group,name:l10n_bg.2_l10n_bg_group_68
#: model:account.group.template,name:l10n_bg.l10n_bg_group_68
msgid "Active reinsurance expenses"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_61
#: model:account.group,name:l10n_bg.2_l10n_bg_group_61
#: model:account.group.template,name:l10n_bg.l10n_bg_group_61
msgid "Activity costs"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_614
#: model:account.account,name:l10n_bg.2_l10n_bg_614
#: model:account.account.template,name:l10n_bg.l10n_bg_614
msgid "Administrative costs"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_402
#: model:account.account,name:l10n_bg.2_l10n_bg_402
#: model:account.account.template,name:l10n_bg.l10n_bg_402
msgid "Advance providers"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_242
#: model:account.account,name:l10n_bg.2_l10n_bg_242
#: model:account.account.template,name:l10n_bg.l10n_bg_242
msgid "Amortization of intangible fixed assets"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_612
#: model:account.account,name:l10n_bg.2_l10n_bg_612
#: model:account.account.template,name:l10n_bg.l10n_bg_612
msgid "Ancillary activity costs"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_316
#: model:account.account,name:l10n_bg.2_l10n_bg_316
#: model:account.account.template,name:l10n_bg.l10n_bg_316
msgid "Animals for experimental purposes"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_315
#: model:account.account,name:l10n_bg.2_l10n_bg_315
#: model:account.account.template,name:l10n_bg.l10n_bg_315
msgid "Animals for fattening"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_274
#: model:account.account,name:l10n_bg.2_l10n_bg_274
#: model:account.account.template,name:l10n_bg.l10n_bg_274
msgid "Animals in major herds"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_962
#: model:account.account,name:l10n_bg.2_l10n_bg_962
#: model:account.account.template,name:l10n_bg.l10n_bg_962
msgid "Assets in use, reported"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_16
#: model:account.group,name:l10n_bg.2_l10n_bg_group_16
#: model:account.group.template,name:l10n_bg.l10n_bg_group_16
msgid "Attracted funds on deposit accounts"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_445
#: model:account.account,name:l10n_bg.2_l10n_bg_445
#: model:account.account.template,name:l10n_bg.l10n_bg_445
msgid "Awarded receivables"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_141
#: model:account.account,name:l10n_bg.2_l10n_bg_141
#: model:account.account.template,name:l10n_bg.l10n_bg_141
msgid "Banknotes for circulation"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_313
#: model:account.account,name:l10n_bg.2_l10n_bg_313
#: model:account.account.template,name:l10n_bg.l10n_bg_313
msgid "Bee families"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_312
#: model:account.account,name:l10n_bg.2_l10n_bg_312
#: model:account.account.template,name:l10n_bg.l10n_bg_312
msgid "Bird-main flocks"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_17
#: model:account.group,name:l10n_bg.2_l10n_bg_group_17
#: model:account.group.template,name:l10n_bg.l10n_bg_group_17
msgid "Borrowed funds on current and other accounts"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_203
#: model:account.account,name:l10n_bg.2_l10n_bg_203
#: model:account.account.template,name:l10n_bg.l10n_bg_203
msgid "Buildings and structures"
msgstr ""

#. module: l10n_bg
#: model:ir.ui.menu,name:l10n_bg.bg_reports_menu
msgid "Bulgaria"
msgstr ""

#. module: l10n_bg
#: model:account.chart.template,name:l10n_bg.l10n_bg_chart_template
msgid "Bulgaria - National Chart of Accounts"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_271
#: model:account.account,name:l10n_bg.2_l10n_bg_271
#: model:account.account.template,name:l10n_bg.l10n_bg_271
msgid "Burning"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_953
#: model:account.account,name:l10n_bg.2_l10n_bg_953
#: model:account.account.template,name:l10n_bg.l10n_bg_953
msgid "Cancellable commitments"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_10
#: model:account.group,name:l10n_bg.2_l10n_bg_group_10
#: model:account.group.template,name:l10n_bg.l10n_bg_group_10
msgid "Capital"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_1
#: model:account.group,name:l10n_bg.2_l10n_bg_group_1
#: model:account.group.template,name:l10n_bg.l10n_bg_group_1
msgid "Capital accounts"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_104
#: model:account.account,name:l10n_bg.2_l10n_bg_104
#: model:account.account.template,name:l10n_bg.l10n_bg_104
msgid "Capital of non-profit enterprises"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_105
#: model:account.account,name:l10n_bg.2_l10n_bg_105
#: model:account.account.template,name:l10n_bg.l10n_bg_105
msgid "Capital premiums"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_11
#: model:account.group,name:l10n_bg.2_l10n_bg_group_11
#: model:account.group.template,name:l10n_bg.l10n_bg_group_11
msgid "Capital reserves"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_50
#: model:account.group,name:l10n_bg.2_l10n_bg_group_50
#: model:account.group.template,name:l10n_bg.l10n_bg_group_50
msgid "Cash"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_502
#: model:account.account,name:l10n_bg.2_l10n_bg_502
#: model:account.account.template,name:l10n_bg.l10n_bg_502
msgid "Cash in foreign currency"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_14
#: model:account.group,name:l10n_bg.2_l10n_bg_group_14
#: model:account.group.template,name:l10n_bg.l10n_bg_group_14
msgid "Cash issues"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_501
#: model:account.account,name:l10n_bg.2_l10n_bg_501
#: model:account.account.template,name:l10n_bg.l10n_bg_501
msgid "Cash register in bgn"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_444
#: model:account.account,name:l10n_bg.2_l10n_bg_444
#: model:account.account.template,name:l10n_bg.l10n_bg_444
msgid "Claims in litigation"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_412
#: model:account.account,name:l10n_bg.2_l10n_bg_412
#: model:account.account.template,name:l10n_bg.l10n_bg_412
msgid "Clients on advances"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_413
#: model:account.account,name:l10n_bg.2_l10n_bg_413
#: model:account.account.template,name:l10n_bg.l10n_bg_413
msgid "Clients on trade credits"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_142
#: model:account.account,name:l10n_bg.2_l10n_bg_142
#: model:account.account.template,name:l10n_bg.l10n_bg_142
msgid "Coins in circulation"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_983
#: model:account.account,name:l10n_bg.2_l10n_bg_983
#: model:account.account.template,name:l10n_bg.l10n_bg_983
msgid "Commitments on authorized loans"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_925
#: model:account.account,name:l10n_bg.2_l10n_bg_925
#: model:account.account.template,name:l10n_bg.l10n_bg_925
msgid "Commitments under contracts"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_6041
#: model:account.account,name:l10n_bg.2_l10n_bg_6041
#: model:account.account.template,name:l10n_bg.l10n_bg_6041
msgid "Compensation costs"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_204
#: model:account.account,name:l10n_bg.2_l10n_bg_204
#: model:account.account.template,name:l10n_bg.l10n_bg_204
msgid "Computer equipment"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_9
#: model:account.group,name:l10n_bg.2_l10n_bg_group_9
#: model:account.group.template,name:l10n_bg.l10n_bg_group_9
msgid "Conditional liabilities and contingent assets accounts"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_94
#: model:account.group,name:l10n_bg.2_l10n_bg_group_94
#: model:account.group.template,name:l10n_bg.l10n_bg_group_94
msgid "Contingent assets"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_95
#: model:account.group,name:l10n_bg.2_l10n_bg_group_95
#: model:account.group.template,name:l10n_bg.l10n_bg_group_95
msgid "Contingent creditors"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_956
#: model:account.account,name:l10n_bg.2_l10n_bg_956
#: model:account.account.template,name:l10n_bg.l10n_bg_956
msgid "Contingent liabilities on other banking operations"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_945
#: model:account.account,name:l10n_bg.2_l10n_bg_945
#: model:account.account.template,name:l10n_bg.l10n_bg_945
msgid "Contingent receivables from other banking operations"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_60
#: model:account.group,name:l10n_bg.2_l10n_bg_group_60
#: model:account.group.template,name:l10n_bg.l10n_bg_group_60
msgid "Costs by economic elements"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_602
#: model:account.account,name:l10n_bg.2_l10n_bg_602
#: model:account.account.template,name:l10n_bg.l10n_bg_602
msgid "Costs for external services"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_662
#: model:account.account,name:l10n_bg.1_l10n_bg_682
#: model:account.account,name:l10n_bg.2_l10n_bg_662
#: model:account.account,name:l10n_bg.2_l10n_bg_682
#: model:account.account.template,name:l10n_bg.l10n_bg_662
#: model:account.account.template,name:l10n_bg.l10n_bg_682
msgid "Costs for participation in the result"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_606452
#: model:account.account,name:l10n_bg.2_l10n_bg_606452
#: model:account.account.template,name:l10n_bg.l10n_bg_606452
msgid "Costs of income tax estimates"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_503
#: model:account.account,name:l10n_bg.2_l10n_bg_503
#: model:account.account.template,name:l10n_bg.l10n_bg_503
msgid "Current account in bgn"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_504
#: model:account.account,name:l10n_bg.2_l10n_bg_504
#: model:account.account.template,name:l10n_bg.l10n_bg_504
msgid "Current account in foreign currency"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_31
#: model:account.group,name:l10n_bg.2_l10n_bg_group_31
#: model:account.group.template,name:l10n_bg.l10n_bg_group_31
msgid "Current biological assets"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_51
#: model:account.group,name:l10n_bg.2_l10n_bg_group_51
#: model:account.group.template,name:l10n_bg.l10n_bg_group_51
msgid "Current financial assets"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_52
#: model:account.group,name:l10n_bg.2_l10n_bg_group_52
#: model:account.group.template,name:l10n_bg.l10n_bg_group_52
msgid "Current receivables and loans"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_411
#: model:account.account,name:l10n_bg.2_l10n_bg_411
#: model:account.account.template,name:l10n_bg.l10n_bg_411
msgid "Customers"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_41
#: model:account.group,name:l10n_bg.2_l10n_bg_group_41
#: model:account.group.template,name:l10n_bg.l10n_bg_group_41
msgid "Customers and related accounts"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_153
#: model:account.account,name:l10n_bg.2_l10n_bg_153
#: model:account.account.template,name:l10n_bg.l10n_bg_153
msgid "Debt instruments"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_933
#: model:account.account,name:l10n_bg.2_l10n_bg_933
#: model:account.account.template,name:l10n_bg.l10n_bg_933
msgid "Debtors by collection operations"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_943
#: model:account.account,name:l10n_bg.2_l10n_bg_943
#: model:account.account.template,name:l10n_bg.l10n_bg_943
msgid "Debtors on collection operations in banks"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_93
#: model:account.group,name:l10n_bg.2_l10n_bg_group_93
#: model:account.group.template,name:l10n_bg.l10n_bg_group_93
msgid "Debtors on contingent receivables"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_106
#: model:account.account,name:l10n_bg.2_l10n_bg_106
#: model:account.account.template,name:l10n_bg.l10n_bg_106
msgid "Deductions (discount) related to capital"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_75
#: model:account.group,name:l10n_bg.2_l10n_bg_group_75
#: model:account.group.template,name:l10n_bg.l10n_bg_group_75
msgid "Deferred income and financing"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_496
#: model:account.account,name:l10n_bg.2_l10n_bg_496
#: model:account.account.template,name:l10n_bg.l10n_bg_496
msgid "Deferred tax estimates"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,name:l10n_bg.l10n_bg_tax_report_12_2
msgid "Deliveries under Art. 82, para. 2-6"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_301
#: model:account.account,name:l10n_bg.2_l10n_bg_301
#: model:account.account.template,name:l10n_bg.l10n_bg_301
msgid "Delivery"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_507
#: model:account.account,name:l10n_bg.2_l10n_bg_507
#: model:account.account.template,name:l10n_bg.l10n_bg_507
msgid "Deposits provided"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_24
#: model:account.group,name:l10n_bg.2_l10n_bg_group_24
#: model:account.group.template,name:l10n_bg.l10n_bg_group_24
msgid "Depreciation"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_603
#: model:account.account,name:l10n_bg.2_l10n_bg_603
#: model:account.account.template,name:l10n_bg.l10n_bg_603
msgid "Depreciation costs"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_2413
#: model:account.account,name:l10n_bg.2_l10n_bg_2413
#: model:account.account.template,name:l10n_bg.l10n_bg_2413
msgid "Depreciation of buildings and structures"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_2414
#: model:account.account,name:l10n_bg.2_l10n_bg_2414
#: model:account.account.template,name:l10n_bg.l10n_bg_2414
msgid "Depreciation of computer equipment"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_2421
#: model:account.account,name:l10n_bg.2_l10n_bg_2421
#: model:account.account.template,name:l10n_bg.l10n_bg_2421
msgid "Depreciation of development products"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_2415
#: model:account.account,name:l10n_bg.2_l10n_bg_2415
#: model:account.account.template,name:l10n_bg.l10n_bg_2415
msgid "Depreciation of equipment"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_2424
#: model:account.account,name:l10n_bg.2_l10n_bg_2424
#: model:account.account.template,name:l10n_bg.l10n_bg_2424
msgid "Depreciation of industrial property rights"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_2423
#: model:account.account,name:l10n_bg.2_l10n_bg_2423
#: model:account.account.template,name:l10n_bg.l10n_bg_2423
msgid "Depreciation of intellectual property rights"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_2416
#: model:account.account,name:l10n_bg.2_l10n_bg_2416
#: model:account.account.template,name:l10n_bg.l10n_bg_2416
msgid "Depreciation of machinery and equipment"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_2418
#: model:account.account,name:l10n_bg.2_l10n_bg_2418
#: model:account.account.template,name:l10n_bg.l10n_bg_2418
msgid "Depreciation of office furniture"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_2429
#: model:account.account,name:l10n_bg.2_l10n_bg_2429
#: model:account.account.template,name:l10n_bg.l10n_bg_2429
msgid "Depreciation of other intangible fixed assets"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_2419
#: model:account.account,name:l10n_bg.2_l10n_bg_2419
#: model:account.account.template,name:l10n_bg.l10n_bg_2419
msgid "Depreciation of other tangible assets"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_2422
#: model:account.account,name:l10n_bg.2_l10n_bg_2422
#: model:account.account.template,name:l10n_bg.l10n_bg_2422
msgid "Depreciation of software products"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_241
#: model:account.account,name:l10n_bg.2_l10n_bg_241
#: model:account.account.template,name:l10n_bg.l10n_bg_241
msgid "Depreciation of tangible fixed assets"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_2417
#: model:account.account,name:l10n_bg.2_l10n_bg_2417
#: model:account.account.template,name:l10n_bg.l10n_bg_2417
msgid "Depreciation of vehicles"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_211
#: model:account.account,name:l10n_bg.2_l10n_bg_211
#: model:account.account.template,name:l10n_bg.l10n_bg_211
msgid "Development products"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_66
#: model:account.group,name:l10n_bg.2_l10n_bg_group_66
#: model:account.group.template,name:l10n_bg.l10n_bg_group_66
msgid "Direct insurance costs"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_914
#: model:account.account,name:l10n_bg.2_l10n_bg_914
#: model:account.account.template,name:l10n_bg.l10n_bg_914
msgid "Documents under special reporting"
msgstr ""

#. module: l10n_bg
#: model:account.fiscal.position,name:l10n_bg.1_fiscal_position_template_dom
#: model:account.fiscal.position,name:l10n_bg.2_fiscal_position_template_dom
#: model:account.fiscal.position.template,name:l10n_bg.fiscal_position_template_dom
msgid "Domestic"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_464
#: model:account.account,name:l10n_bg.2_l10n_bg_464
#: model:account.account.template,name:l10n_bg.l10n_bg_464
msgid "Estimates for one-time benefits and child allowances"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_4612
#: model:account.account,name:l10n_bg.2_l10n_bg_4612
#: model:account.account.template,name:l10n_bg.l10n_bg_4612
msgid "Estimates for smps fund"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_4614
#: model:account.account,name:l10n_bg.2_l10n_bg_4614
#: model:account.account.template,name:l10n_bg.l10n_bg_4614
msgid "Estimates for the gvrs fund"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_4611
#: model:account.account,name:l10n_bg.2_l10n_bg_4611
#: model:account.account.template,name:l10n_bg.l10n_bg_4611
msgid "Estimates for the social security fund"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_4615
#: model:account.account,name:l10n_bg.2_l10n_bg_4615
#: model:account.account.template,name:l10n_bg.l10n_bg_4615
msgid "Estimates for the tzpb fund"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_4613
#: model:account.account,name:l10n_bg.2_l10n_bg_4613
#: model:account.account.template,name:l10n_bg.l10n_bg_4613
msgid "Estimates for the upf fund"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_45
#: model:account.group,name:l10n_bg.2_l10n_bg_group_45
#: model:account.group.template,name:l10n_bg.l10n_bg_group_45
msgid "Estimates from the budget and departments"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_454
#: model:account.account,name:l10n_bg.2_l10n_bg_454
#: model:account.account.template,name:l10n_bg.l10n_bg_454
msgid "Estimates of personal income taxes"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_481
#: model:account.account,name:l10n_bg.2_l10n_bg_481
#: model:account.account.template,name:l10n_bg.l10n_bg_481
msgid "Estimates of upcoming payments"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_453
#: model:account.account,name:l10n_bg.2_l10n_bg_453
#: model:account.account.template,name:l10n_bg.l10n_bg_453
msgid "Estimates of value added tax"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_474
#: model:account.account,name:l10n_bg.2_l10n_bg_474
#: model:account.account.template,name:l10n_bg.l10n_bg_474
msgid "Estimates under intergovernmental agreements"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_691
#: model:account.account,name:l10n_bg.2_l10n_bg_691
#: model:account.account.template,name:l10n_bg.l10n_bg_691
#: model:account.group,name:l10n_bg.1_l10n_bg_group_69
#: model:account.group,name:l10n_bg.2_l10n_bg_group_69
#: model:account.group.template,name:l10n_bg.l10n_bg_group_69
msgid "Exceptional costs"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_456
#: model:account.account,name:l10n_bg.2_l10n_bg_456
#: model:account.account.template,name:l10n_bg.l10n_bg_456
msgid "Excise estimates"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_606459
#: model:account.account,name:l10n_bg.2_l10n_bg_606459
#: model:account.account.template,name:l10n_bg.l10n_bg_606459
msgid "Expenditures for other estimates with the budget and with departments"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_606451
#: model:account.account,name:l10n_bg.2_l10n_bg_606451
#: model:account.account.template,name:l10n_bg.l10n_bg_606451
msgid "Expenditures for settlements with municipalities"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_6052
#: model:account.account,name:l10n_bg.2_l10n_bg_6052
#: model:account.account.template,name:l10n_bg.l10n_bg_6052
msgid "Expenditures for smps fund"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_6054
#: model:account.account,name:l10n_bg.2_l10n_bg_6054
#: model:account.account.template,name:l10n_bg.l10n_bg_6054
msgid "Expenditures for the gvrs fund"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_6056
#: model:account.account,name:l10n_bg.2_l10n_bg_6056
#: model:account.account.template,name:l10n_bg.l10n_bg_6056
msgid "Expenditures for the health fund"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_6051
#: model:account.account,name:l10n_bg.2_l10n_bg_6051
#: model:account.account.template,name:l10n_bg.l10n_bg_6051
msgid "Expenditures for the social security fund"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_6053
#: model:account.account,name:l10n_bg.2_l10n_bg_6053
#: model:account.account.template,name:l10n_bg.l10n_bg_6053
msgid "Expenditures for the upf fund"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_6055
#: model:account.account,name:l10n_bg.2_l10n_bg_6055
#: model:account.account.template,name:l10n_bg.l10n_bg_6055
msgid "Expenditures for tzpb fund"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_6
#: model:account.group,name:l10n_bg.2_l10n_bg_group_6
#: model:account.group.template,name:l10n_bg.l10n_bg_group_6
msgid "Expense accounts"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_613
#: model:account.account,name:l10n_bg.2_l10n_bg_613
#: model:account.account.template,name:l10n_bg.l10n_bg_613
msgid "Expenses for acquisition of fixed assets"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_6042
#: model:account.account,name:l10n_bg.2_l10n_bg_6042
#: model:account.account.template,name:l10n_bg.l10n_bg_6042
msgid "Expenses for additional remuneration"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_684
#: model:account.account,name:l10n_bg.2_l10n_bg_684
#: model:account.account.template,name:l10n_bg.l10n_bg_684
msgid "Expenses for assigned reinsurance commissions and fees"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_606454
#: model:account.account,name:l10n_bg.2_l10n_bg_606454
#: model:account.account.template,name:l10n_bg.l10n_bg_606454
msgid "Expenses for estimates of personal income taxes"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_661
#: model:account.account,name:l10n_bg.2_l10n_bg_661
#: model:account.account.template,name:l10n_bg.l10n_bg_661
msgid "Expenses for insurance amounts"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_681
#: model:account.account,name:l10n_bg.2_l10n_bg_681
#: model:account.account.template,name:l10n_bg.l10n_bg_681
msgid "Expenses for insurance benefits"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_616
#: model:account.account,name:l10n_bg.2_l10n_bg_616
#: model:account.account.template,name:l10n_bg.l10n_bg_616
msgid "Expenses for liquidation of tangible fixed assets"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_683
#: model:account.account,name:l10n_bg.2_l10n_bg_683
#: model:account.account.template,name:l10n_bg.l10n_bg_683
msgid "Expenses for participation in the liquidation"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_671
#: model:account.account,name:l10n_bg.2_l10n_bg_671
#: model:account.account.template,name:l10n_bg.l10n_bg_671
msgid "Expenses for reinsurers' premiums"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_672
#: model:account.account,name:l10n_bg.2_l10n_bg_672
#: model:account.account.template,name:l10n_bg.l10n_bg_672
msgid "Expenses for released reserves for passive reinsurance"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_6045
#: model:account.account,name:l10n_bg.2_l10n_bg_6045
#: model:account.account.template,name:l10n_bg.l10n_bg_6045
msgid "Expenses for remuneration of self-insured persons"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_685
#: model:account.account,name:l10n_bg.2_l10n_bg_685
#: model:account.account.template,name:l10n_bg.l10n_bg_685
msgid "Expenses for set aside reserves for active reinsurance"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_606
#: model:account.account,name:l10n_bg.2_l10n_bg_606
#: model:account.account.template,name:l10n_bg.l10n_bg_606
msgid "Expenses for taxes, fees and other similar payments"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_608
#: model:account.account,name:l10n_bg.2_l10n_bg_608
#: model:account.account.template,name:l10n_bg.l10n_bg_608
msgid "Expenses from subsequent valuations of assets"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_625
#: model:account.account,name:l10n_bg.2_l10n_bg_625
#: model:account.account.template,name:l10n_bg.l10n_bg_625
msgid ""
"Expenses from subsequent valuations of financial assets and instruments"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_617
#: model:account.account,name:l10n_bg.2_l10n_bg_617
#: model:account.account.template,name:l10n_bg.l10n_bg_617
msgid "Expenses on activities in non-profit enterprises"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_624
#: model:account.account,name:l10n_bg.2_l10n_bg_624
#: model:account.account.template,name:l10n_bg.l10n_bg_624
msgid "Expenses on foreign exchange operations"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_623
#: model:account.account,name:l10n_bg.2_l10n_bg_623
#: model:account.account.template,name:l10n_bg.l10n_bg_623
msgid "Expenses on operations with financial assets and instruments"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_791
#: model:account.account,name:l10n_bg.2_l10n_bg_791
#: model:account.account.template,name:l10n_bg.l10n_bg_791
#: model:account.group,name:l10n_bg.1_l10n_bg_group_79
#: model:account.group,name:l10n_bg.2_l10n_bg_group_79
#: model:account.group.template,name:l10n_bg.l10n_bg_group_79
msgid "Extraordinary income"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_205
#: model:account.account,name:l10n_bg.2_l10n_bg_205
#: model:account.account.template,name:l10n_bg.l10n_bg_205
msgid "Facilities"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_511
#: model:account.account,name:l10n_bg.2_l10n_bg_511
#: model:account.account.template,name:l10n_bg.l10n_bg_511
msgid "Financial assets held for trading"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_225
#: model:account.account,name:l10n_bg.2_l10n_bg_225
#: model:account.account.template,name:l10n_bg.l10n_bg_225
msgid "Financial assets held to maturity"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_513
#: model:account.account,name:l10n_bg.2_l10n_bg_513
#: model:account.account.template,name:l10n_bg.l10n_bg_513
msgid "Financial assets pledged as collateral"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_227
#: model:account.account,name:l10n_bg.2_l10n_bg_227
#: model:account.account.template,name:l10n_bg.l10n_bg_227
msgid "Financial assets pledged as compensation"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_226
#: model:account.account,name:l10n_bg.2_l10n_bg_226
#: model:account.account.template,name:l10n_bg.l10n_bg_226
msgid "Financial assets put up for sale"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_62
#: model:account.group,name:l10n_bg.2_l10n_bg_group_62
#: model:account.group.template,name:l10n_bg.l10n_bg_group_62
msgid "Financial costs"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_652
#: model:account.account,name:l10n_bg.2_l10n_bg_652
#: model:account.account.template,name:l10n_bg.l10n_bg_652
msgid "Financial expenses for future periods"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_72
#: model:account.group,name:l10n_bg.2_l10n_bg_group_72
#: model:account.group.template,name:l10n_bg.l10n_bg_group_72
msgid "Financial income"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_752
#: model:account.account,name:l10n_bg.2_l10n_bg_752
#: model:account.account.template,name:l10n_bg.l10n_bg_752
msgid "Financial income for future periods"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_12
#: model:account.group,name:l10n_bg.2_l10n_bg_group_12
#: model:account.group.template,name:l10n_bg.l10n_bg_group_12
msgid "Financial results"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_518
#: model:account.account,name:l10n_bg.2_l10n_bg_518
#: model:account.account.template,name:l10n_bg.l10n_bg_518
msgid "Financing"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_753
#: model:account.account,name:l10n_bg.2_l10n_bg_753
#: model:account.account.template,name:l10n_bg.l10n_bg_753
msgid "Financing for fixed assets"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_754
#: model:account.account,name:l10n_bg.2_l10n_bg_754
#: model:account.account.template,name:l10n_bg.l10n_bg_754
msgid "Financing the current activity"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_101
#: model:account.account,name:l10n_bg.2_l10n_bg_101
#: model:account.account.template,name:l10n_bg.l10n_bg_101
msgid "Fixed capital required for registration"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_102
#: model:account.account,name:l10n_bg.2_l10n_bg_102
#: model:account.account.template,name:l10n_bg.l10n_bg_102
msgid "Fixed capital that does not require registration"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_92
#: model:account.group,name:l10n_bg.2_l10n_bg_group_92
#: model:account.group.template,name:l10n_bg.l10n_bg_group_92
msgid "Foreign financial assets"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_924
#: model:account.account,name:l10n_bg.2_l10n_bg_924
#: model:account.account.template,name:l10n_bg.l10n_bg_924
msgid "Foreign financial assets held on behalf of customers"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_921
#: model:account.account,name:l10n_bg.2_l10n_bg_921
#: model:account.account.template,name:l10n_bg.l10n_bg_921
msgid "Foreign financial assets provided as collateral"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_91
#: model:account.group,name:l10n_bg.2_l10n_bg_group_91
#: model:account.group.template,name:l10n_bg.l10n_bg_group_91
msgid "Foreign tangible and intangible assets"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_911
#: model:account.account,name:l10n_bg.2_l10n_bg_911
#: model:account.account.template,name:l10n_bg.l10n_bg_911
msgid "Foreign tangible and intangible assets provided as collateral"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_913
#: model:account.account,name:l10n_bg.2_l10n_bg_913
#: model:account.account.template,name:l10n_bg.l10n_bg_913
msgid "Foreign tangible assets received under a consignment agreement"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_18
#: model:account.group,name:l10n_bg.2_l10n_bg_group_18
#: model:account.group.template,name:l10n_bg.l10n_bg_group_18
msgid "Funds raised on accounts of participants in insurance funds"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_111
#: model:account.account,name:l10n_bg.2_l10n_bg_111
#: model:account.account.template,name:l10n_bg.l10n_bg_111
msgid "General reserves"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_304
#: model:account.account,name:l10n_bg.2_l10n_bg_304
#: model:account.account.template,name:l10n_bg.l10n_bg_304
msgid "Goods"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_23
#: model:account.group,name:l10n_bg.2_l10n_bg_group_23
#: model:account.group.template,name:l10n_bg.l10n_bg_group_23
msgid "Goodwill"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_228
#: model:account.account,name:l10n_bg.1_l10n_bg_515
#: model:account.account,name:l10n_bg.2_l10n_bg_228
#: model:account.account,name:l10n_bg.2_l10n_bg_515
#: model:account.account.template,name:l10n_bg.l10n_bg_228
#: model:account.account.template,name:l10n_bg.l10n_bg_515
msgid "Government securities"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_492
#: model:account.account,name:l10n_bg.2_l10n_bg_492
#: model:account.account.template,name:l10n_bg.l10n_bg_492
msgid "Guarantee estimates"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_951
#: model:account.account,name:l10n_bg.2_l10n_bg_951
#: model:account.account.template,name:l10n_bg.l10n_bg_951
msgid "Guarantees and other similar contingent liabilities"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_463
#: model:account.account,name:l10n_bg.2_l10n_bg_463
#: model:account.account.template,name:l10n_bg.l10n_bg_463
msgid "Health insurance estimates"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_6044
#: model:account.account,name:l10n_bg.2_l10n_bg_6044
#: model:account.account.template,name:l10n_bg.l10n_bg_6044
msgid "Hospital costs"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_631
#: model:account.account,name:l10n_bg.2_l10n_bg_631
#: model:account.account.template,name:l10n_bg.l10n_bg_631
#: model:account.group,name:l10n_bg.1_l10n_bg_group_63
#: model:account.group,name:l10n_bg.2_l10n_bg_group_63
#: model:account.group.template,name:l10n_bg.l10n_bg_group_63
msgid "Impairment loss (expense)"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_632
#: model:account.account,name:l10n_bg.2_l10n_bg_632
#: model:account.account.template,name:l10n_bg.l10n_bg_632
msgid "Impairment losses on current assets"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_7
#: model:account.group,name:l10n_bg.2_l10n_bg_group_7
#: model:account.group.template,name:l10n_bg.l10n_bg_group_7
msgid "Income accounts"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_532
#: model:account.account,name:l10n_bg.2_l10n_bg_532
#: model:account.account.template,name:l10n_bg.l10n_bg_532
msgid "Income for future periods"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_78
#: model:account.group,name:l10n_bg.2_l10n_bg_group_78
#: model:account.group.template,name:l10n_bg.l10n_bg_group_78
msgid "Income from active reinsurance"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_782
#: model:account.account,name:l10n_bg.2_l10n_bg_782
#: model:account.account.template,name:l10n_bg.l10n_bg_782
msgid "Income from active reinsurance from previous years"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_783
#: model:account.account,name:l10n_bg.2_l10n_bg_783
#: model:account.account.template,name:l10n_bg.l10n_bg_783
msgid "Income from active reinsurance recourses"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_784
#: model:account.account,name:l10n_bg.2_l10n_bg_784
#: model:account.account.template,name:l10n_bg.l10n_bg_784
msgid "Income from active reinsurance reserves"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_76
#: model:account.group,name:l10n_bg.2_l10n_bg_group_76
#: model:account.group.template,name:l10n_bg.l10n_bg_group_76
msgid "Income from direct insurance"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_724
#: model:account.account,name:l10n_bg.2_l10n_bg_724
#: model:account.account.template,name:l10n_bg.l10n_bg_724
msgid "Income from foreign exchange operations"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_771
#: model:account.account,name:l10n_bg.2_l10n_bg_771
#: model:account.account.template,name:l10n_bg.l10n_bg_771
msgid "Income from indemnities received from reinsurers"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_761
#: model:account.account,name:l10n_bg.2_l10n_bg_761
#: model:account.account.template,name:l10n_bg.l10n_bg_761
msgid "Income from insurance premiums"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_723
#: model:account.account,name:l10n_bg.2_l10n_bg_723
#: model:account.account.template,name:l10n_bg.l10n_bg_723
msgid "Income from operations with financial assets and instruments"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_772
#: model:account.account,name:l10n_bg.2_l10n_bg_772
#: model:account.account.template,name:l10n_bg.l10n_bg_772
msgid "Income from participation in the result of reinsurers"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_722
#: model:account.account,name:l10n_bg.2_l10n_bg_722
#: model:account.account.template,name:l10n_bg.l10n_bg_722
msgid "Income from participations"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_731
#: model:account.account,name:l10n_bg.2_l10n_bg_731
#: model:account.account.template,name:l10n_bg.l10n_bg_731
#: model:account.group,name:l10n_bg.1_l10n_bg_group_73
#: model:account.group,name:l10n_bg.2_l10n_bg_group_73
#: model:account.group.template,name:l10n_bg.l10n_bg_group_73
msgid "Income from recovered impairment losses"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_775
#: model:account.account,name:l10n_bg.2_l10n_bg_775
#: model:account.account.template,name:l10n_bg.l10n_bg_775
msgid "Income from reinsurance reserves"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_705
#: model:account.account,name:l10n_bg.2_l10n_bg_705
#: model:account.account.template,name:l10n_bg.l10n_bg_705
msgid "Income from sales of fixed assets"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_725
#: model:account.account,name:l10n_bg.2_l10n_bg_725
#: model:account.account.template,name:l10n_bg.l10n_bg_725
msgid "Income from subsequent valuations of financial assets and instruments"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_214
#: model:account.account,name:l10n_bg.2_l10n_bg_214
#: model:account.account.template,name:l10n_bg.l10n_bg_214
msgid "Industrial property rights"
msgstr ""

#. module: l10n_bg
#: model:account.fiscal.position,name:l10n_bg.1_fiscal_position_template_in_eu
#: model:account.fiscal.position,name:l10n_bg.2_fiscal_position_template_in_eu
#: model:account.fiscal.position.template,name:l10n_bg.fiscal_position_template_in_eu
msgid "Inside EU"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_48
#: model:account.group,name:l10n_bg.2_l10n_bg_group_48
#: model:account.group.template,name:l10n_bg.l10n_bg_group_48
msgid "Insurance and co-insurance estimates"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_664
#: model:account.account,name:l10n_bg.2_l10n_bg_664
#: model:account.account.template,name:l10n_bg.l10n_bg_664
msgid "Insurance commission expenses"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_605
#: model:account.account,name:l10n_bg.2_l10n_bg_605
#: model:account.account.template,name:l10n_bg.l10n_bg_605
msgid "Insurance costs"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_494
#: model:account.account,name:l10n_bg.2_l10n_bg_494
#: model:account.account.template,name:l10n_bg.l10n_bg_494
msgid "Insurance estimates"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_763
#: model:account.account,name:l10n_bg.2_l10n_bg_763
#: model:account.account.template,name:l10n_bg.l10n_bg_763
msgid "Insurance income from previous years"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_665
#: model:account.account,name:l10n_bg.2_l10n_bg_665
#: model:account.account.template,name:l10n_bg.l10n_bg_665
msgid "Insurance reserve costs"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_213
#: model:account.account,name:l10n_bg.2_l10n_bg_213
#: model:account.account.template,name:l10n_bg.l10n_bg_213
msgid "Intellectual property rights"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_495
#: model:account.account,name:l10n_bg.2_l10n_bg_495
#: model:account.account.template,name:l10n_bg.l10n_bg_495
msgid "Interest calculations"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_621
#: model:account.account,name:l10n_bg.2_l10n_bg_621
#: model:account.account.template,name:l10n_bg.l10n_bg_621
msgid "Interest expenses"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_721
#: model:account.account,name:l10n_bg.2_l10n_bg_721
#: model:account.account.template,name:l10n_bg.l10n_bg_721
msgid "Interest income"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_430
#: model:account.account,name:l10n_bg.2_l10n_bg_430
#: model:account.account.template,name:l10n_bg.l10n_bg_430
msgid "Internal calculations"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_432
#: model:account.account,name:l10n_bg.2_l10n_bg_432
#: model:account.account.template,name:l10n_bg.l10n_bg_432
msgid "Internal settlements on interbank operations"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_433
#: model:account.account,name:l10n_bg.2_l10n_bg_433
#: model:account.account.template,name:l10n_bg.l10n_bg_433
msgid "Internal settlements on intrabank operations"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,name:l10n_bg.l10n_bg_tax_report_12_1
msgid "Intra-community acquisitions"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_224
#: model:account.account,name:l10n_bg.2_l10n_bg_224
#: model:account.account.template,name:l10n_bg.l10n_bg_224
msgid "Investment property"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_222
#: model:account.account,name:l10n_bg.2_l10n_bg_222
#: model:account.account.template,name:l10n_bg.l10n_bg_222
msgid "Investments in associates"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_223
#: model:account.account,name:l10n_bg.2_l10n_bg_223
#: model:account.account.template,name:l10n_bg.l10n_bg_223
msgid "Investments in joint ventures"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_221
#: model:account.account,name:l10n_bg.2_l10n_bg_221
#: model:account.account.template,name:l10n_bg.l10n_bg_221
msgid "Investments in subsidiaries"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_952
#: model:account.account,name:l10n_bg.2_l10n_bg_952
#: model:account.account.template,name:l10n_bg.l10n_bg_952
msgid "Irrevocable commitments"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_971
#: model:account.account,name:l10n_bg.2_l10n_bg_971
#: model:account.account.template,name:l10n_bg.l10n_bg_971
msgid "Issue reserve"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_202
#: model:account.account,name:l10n_bg.2_l10n_bg_202
#: model:account.account.template,name:l10n_bg.l10n_bg_202
msgid "Land improvements"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_201
#: model:account.account,name:l10n_bg.2_l10n_bg_201
#: model:account.account.template,name:l10n_bg.l10n_bg_201
msgid "Lands (terrains)"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_912
#: model:account.account,name:l10n_bg.2_l10n_bg_912
#: model:account.account.template,name:l10n_bg.l10n_bg_912
msgid "Leased foreign assets"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_505
#: model:account.account,name:l10n_bg.2_l10n_bg_505
#: model:account.account.template,name:l10n_bg.l10n_bg_505
msgid "Letters of credit in bgn"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_514
#: model:account.account,name:l10n_bg.2_l10n_bg_514
#: model:account.account.template,name:l10n_bg.l10n_bg_514
msgid "Repurchased own liabilities"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_506
#: model:account.account,name:l10n_bg.2_l10n_bg_506
#: model:account.account.template,name:l10n_bg.l10n_bg_506
msgid "Letters of credit in foreign currency"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_423
#: model:account.account,name:l10n_bg.2_l10n_bg_423
#: model:account.account.template,name:l10n_bg.l10n_bg_423
msgid "Liabilities for unused leave"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_955
#: model:account.account,name:l10n_bg.2_l10n_bg_955
#: model:account.account.template,name:l10n_bg.l10n_bg_955
msgid "Liabilities under derivative transactions"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_954
#: model:account.account,name:l10n_bg.2_l10n_bg_954
#: model:account.account.template,name:l10n_bg.l10n_bg_954
msgid "Liabilities under spot operations"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_131
#: model:account.account,name:l10n_bg.2_l10n_bg_131
#: model:account.account.template,name:l10n_bg.l10n_bg_131
msgid "Life insurance reserves"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_618
#: model:account.account,name:l10n_bg.2_l10n_bg_618
#: model:account.account.template,name:l10n_bg.l10n_bg_618
msgid "Liquidation and insolvency costs"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_103
#: model:account.account,name:l10n_bg.2_l10n_bg_103
#: model:account.account.template,name:l10n_bg.l10n_bg_103
msgid "Liquidation capital in case of insolvency and liquidation"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_663
#: model:account.account,name:l10n_bg.2_l10n_bg_663
#: model:account.account.template,name:l10n_bg.l10n_bg_663
msgid "Liquidation costs"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_chart_template_liquidity_transfer
#: model:account.account,name:l10n_bg.2_l10n_bg_chart_template_liquidity_transfer
#: model:account.account.template,name:l10n_bg.l10n_bg_chart_template_liquidity_transfer
msgid "Liquidity Transfer"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_15
#: model:account.group,name:l10n_bg.2_l10n_bg_group_15
#: model:account.group.template,name:l10n_bg.l10n_bg_group_15
msgid "Loans received"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_27
#: model:account.group,name:l10n_bg.2_l10n_bg_group_27
#: model:account.group.template,name:l10n_bg.l10n_bg_group_27
msgid "Long-term biological assets"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_22
#: model:account.group,name:l10n_bg.2_l10n_bg_group_22
#: model:account.group.template,name:l10n_bg.l10n_bg_group_22
msgid "Long-term financial assets"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_152
#: model:account.account,name:l10n_bg.2_l10n_bg_152
#: model:account.account.template,name:l10n_bg.l10n_bg_152
msgid "Long-term loans received"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_26
#: model:account.group,name:l10n_bg.2_l10n_bg_group_26
#: model:account.group.template,name:l10n_bg.l10n_bg_group_26
msgid "Long-term receivables and loans"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_264
#: model:account.account,name:l10n_bg.2_l10n_bg_264
#: model:account.account.template,name:l10n_bg.l10n_bg_264
msgid "Long-term receivables and loans granted as collateral"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_261
#: model:account.account,name:l10n_bg.2_l10n_bg_261
#: model:account.account.template,name:l10n_bg.l10n_bg_261
msgid ""
"Long-term receivables and loans to banks and other financial institutions"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_262
#: model:account.account,name:l10n_bg.2_l10n_bg_262
#: model:account.account.template,name:l10n_bg.l10n_bg_262
msgid ""
"Long-term receivables and loans to non-financial corporations and other "
"customers"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_206
#: model:account.account,name:l10n_bg.2_l10n_bg_206
#: model:account.account.template,name:l10n_bg.l10n_bg_206
msgid "Machinery and equipment"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_601
#: model:account.account,name:l10n_bg.2_l10n_bg_601
#: model:account.account.template,name:l10n_bg.l10n_bg_601
msgid "Material costs"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_302
#: model:account.account,name:l10n_bg.2_l10n_bg_302
#: model:account.account.template,name:l10n_bg.l10n_bg_302
msgid "Materials"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_30
#: model:account.group,name:l10n_bg.2_l10n_bg_group_30
#: model:account.group.template,name:l10n_bg.l10n_bg_group_30
msgid "Materials, products and goods"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_98
#: model:account.group,name:l10n_bg.2_l10n_bg_group_98
#: model:account.group.template,name:l10n_bg.l10n_bg_group_98
msgid "Miscellaneous contingent asset accounts"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_49
#: model:account.group,name:l10n_bg.2_l10n_bg_group_49
#: model:account.group.template,name:l10n_bg.l10n_bg_group_49
msgid "Miscellaneous debtors and creditors"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_923
#: model:account.account,name:l10n_bg.2_l10n_bg_923
#: model:account.account.template,name:l10n_bg.l10n_bg_923
msgid "Miscellaneous emissions for circulation"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_232
#: model:account.account,name:l10n_bg.2_l10n_bg_232
#: model:account.account.template,name:l10n_bg.l10n_bg_232
msgid "Negative commercial reputation"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_915
#: model:account.account,name:l10n_bg.2_l10n_bg_915
#: model:account.account.template,name:l10n_bg.l10n_bg_915
msgid "Non-financial assets accepted for safekeeping"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_651
#: model:account.account,name:l10n_bg.2_l10n_bg_651
#: model:account.account.template,name:l10n_bg.l10n_bg_651
msgid "Non-financial expenses for future periods"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_751
#: model:account.account,name:l10n_bg.2_l10n_bg_751
#: model:account.account.template,name:l10n_bg.l10n_bg_751
msgid "Non-financial income for future periods"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_425
#: model:account.account,name:l10n_bg.2_l10n_bg_425
#: model:account.account.template,name:l10n_bg.l10n_bg_425
msgid "Obligations for participation"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_208
#: model:account.account,name:l10n_bg.2_l10n_bg_208
#: model:account.account.template,name:l10n_bg.l10n_bg_208
msgid "Office furniture"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_611
#: model:account.account,name:l10n_bg.2_l10n_bg_611
#: model:account.account.template,name:l10n_bg.l10n_bg_611
msgid "Operating expenses"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_689
#: model:account.account,name:l10n_bg.2_l10n_bg_689
#: model:account.account.template,name:l10n_bg.l10n_bg_689
msgid "Other active reinsurance costs"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_509
#: model:account.account,name:l10n_bg.2_l10n_bg_509
#: model:account.account.template,name:l10n_bg.l10n_bg_509
msgid "Other cash"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_989
#: model:account.account,name:l10n_bg.2_l10n_bg_989
#: model:account.account.template,name:l10n_bg.l10n_bg_989
msgid "Other contingent asset accounts"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_991
#: model:account.account,name:l10n_bg.2_l10n_bg_991
#: model:account.account.template,name:l10n_bg.l10n_bg_991
msgid "Other contingent liability accounts"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_499
#: model:account.account,name:l10n_bg.2_l10n_bg_499
#: model:account.account.template,name:l10n_bg.l10n_bg_499
msgid "Other creditors"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_319
#: model:account.account,name:l10n_bg.2_l10n_bg_319
#: model:account.account.template,name:l10n_bg.l10n_bg_319
msgid "Other current biological assets"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_529
#: model:account.account,name:l10n_bg.2_l10n_bg_529
#: model:account.account.template,name:l10n_bg.l10n_bg_529
msgid "Other current receivables and loans"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_498
#: model:account.account,name:l10n_bg.2_l10n_bg_498
#: model:account.account.template,name:l10n_bg.l10n_bg_498
msgid "Other debtors"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_669
#: model:account.account,name:l10n_bg.2_l10n_bg_669
#: model:account.account.template,name:l10n_bg.l10n_bg_669
msgid "Other direct insurance costs"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_489
#: model:account.account,name:l10n_bg.2_l10n_bg_489
#: model:account.account.template,name:l10n_bg.l10n_bg_489
msgid "Other estimates"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_459
#: model:account.account,name:l10n_bg.2_l10n_bg_459
#: model:account.account.template,name:l10n_bg.l10n_bg_459
msgid "Other estimates with the budget and with departments"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_609
#: model:account.account,name:l10n_bg.2_l10n_bg_609
#: model:account.account.template,name:l10n_bg.l10n_bg_609
msgid "Other expenses"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_629
#: model:account.account,name:l10n_bg.2_l10n_bg_629
#: model:account.account.template,name:l10n_bg.l10n_bg_629
msgid "Other financial expenses"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_729
#: model:account.account,name:l10n_bg.2_l10n_bg_729
#: model:account.account.template,name:l10n_bg.l10n_bg_729
msgid "Other financial income"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_279
#: model:account.account,name:l10n_bg.2_l10n_bg_279
#: model:account.account.template,name:l10n_bg.l10n_bg_279
msgid "Other fixed biological assets"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_789
#: model:account.account,name:l10n_bg.2_l10n_bg_789
#: model:account.account.template,name:l10n_bg.l10n_bg_789
msgid "Other income from active reinsurance"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_779
#: model:account.account,name:l10n_bg.2_l10n_bg_779
#: model:account.account.template,name:l10n_bg.l10n_bg_779
msgid "Other income from passive reinsurance"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_719
#: model:account.account,name:l10n_bg.2_l10n_bg_719
#: model:account.account.template,name:l10n_bg.l10n_bg_719
msgid "Other incomes"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_769
#: model:account.account,name:l10n_bg.2_l10n_bg_769
#: model:account.account.template,name:l10n_bg.l10n_bg_769
msgid "Other insurance income"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_219
#: model:account.account,name:l10n_bg.2_l10n_bg_219
#: model:account.account.template,name:l10n_bg.l10n_bg_219
msgid "Other intangible fixed assets"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_439
#: model:account.account,name:l10n_bg.2_l10n_bg_439
#: model:account.account.template,name:l10n_bg.l10n_bg_439
msgid "Other internal estimates"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_159
#: model:account.account,name:l10n_bg.2_l10n_bg_159
#: model:account.account.template,name:l10n_bg.l10n_bg_159
msgid "Other loans and debts"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_229
#: model:account.account,name:l10n_bg.2_l10n_bg_229
#: model:account.account.template,name:l10n_bg.l10n_bg_229
msgid "Other long-term financial assets"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_269
#: model:account.account,name:l10n_bg.2_l10n_bg_269
#: model:account.account.template,name:l10n_bg.l10n_bg_269
msgid "Other long-term receivables and loans"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_709
#: model:account.account,name:l10n_bg.2_l10n_bg_709
#: model:account.account.template,name:l10n_bg.l10n_bg_709
msgid "Other operating income"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_679
#: model:account.account,name:l10n_bg.2_l10n_bg_679
#: model:account.account.template,name:l10n_bg.l10n_bg_679
msgid "Other passive reinsurance costs"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_4972
#: model:account.account,name:l10n_bg.2_l10n_bg_4972
#: model:account.account.template,name:l10n_bg.l10n_bg_4972
msgid "Other provisions and similar liabilities"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_119
#: model:account.account,name:l10n_bg.2_l10n_bg_119
#: model:account.account.template,name:l10n_bg.l10n_bg_119
msgid "Other reserves"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_136
#: model:account.account,name:l10n_bg.2_l10n_bg_136
#: model:account.account.template,name:l10n_bg.l10n_bg_136
msgid "Other reserves under insurance contracts"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_419
#: model:account.account,name:l10n_bg.2_l10n_bg_419
#: model:account.account.template,name:l10n_bg.l10n_bg_419
msgid "Other settlements with clients"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_469
#: model:account.account,name:l10n_bg.2_l10n_bg_469
#: model:account.account.template,name:l10n_bg.l10n_bg_469
msgid "Other settlements with insurers"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_429
#: model:account.account,name:l10n_bg.2_l10n_bg_429
#: model:account.account.template,name:l10n_bg.l10n_bg_429
msgid "Other settlements with staff and partners"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_409
#: model:account.account,name:l10n_bg.2_l10n_bg_409
#: model:account.account.template,name:l10n_bg.l10n_bg_409
msgid "Other settlements with suppliers"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_519
#: model:account.account,name:l10n_bg.2_l10n_bg_519
#: model:account.account.template,name:l10n_bg.l10n_bg_519
msgid "Other short-term financial assets"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_209
#: model:account.account,name:l10n_bg.2_l10n_bg_209
#: model:account.account.template,name:l10n_bg.l10n_bg_209
msgid "Other tangible fixed assets"
msgstr ""

#. module: l10n_bg
#: model:account.fiscal.position,name:l10n_bg.1_fiscal_position_template_out_eu
#: model:account.fiscal.position,name:l10n_bg.2_fiscal_position_template_out_eu
#: model:account.fiscal.position.template,name:l10n_bg.fiscal_position_template_out_eu
msgid "Outside EU"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_932
#: model:account.account,name:l10n_bg.2_l10n_bg_932
#: model:account.account.template,name:l10n_bg.l10n_bg_932
msgid "Outstanding receivables written off"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_263
#: model:account.account,name:l10n_bg.2_l10n_bg_263
#: model:account.account.template,name:l10n_bg.l10n_bg_263
msgid "Overdue long-term receivables and loans granted"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_524
#: model:account.account,name:l10n_bg.2_l10n_bg_524
#: model:account.account.template,name:l10n_bg.l10n_bg_524
msgid "Overdue short-term receivables and loans"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_96
#: model:account.group,name:l10n_bg.2_l10n_bg_group_96
#: model:account.group.template,name:l10n_bg.l10n_bg_group_96
msgid "Own assets not included in economic turnover"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_972
#: model:account.account,name:l10n_bg.2_l10n_bg_972
#: model:account.account.template,name:l10n_bg.l10n_bg_972
msgid "Own issues out of circulation with expired term for exchange"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_97
#: model:account.group,name:l10n_bg.2_l10n_bg_group_97
#: model:account.group.template,name:l10n_bg.l10n_bg_group_97
msgid "Own liabilities not included in economic turnover"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_6043
#: model:account.account,name:l10n_bg.2_l10n_bg_6043
#: model:account.account.template,name:l10n_bg.l10n_bg_6043
msgid "Paid leave expenses"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_67
#: model:account.group,name:l10n_bg.2_l10n_bg_group_67
#: model:account.group.template,name:l10n_bg.l10n_bg_group_67
msgid "Passive reinsurance expenses"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_77
#: model:account.group,name:l10n_bg.2_l10n_bg_group_77
#: model:account.group.template,name:l10n_bg.l10n_bg_group_77
msgid "Passive reinsurance income"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_774
#: model:account.account,name:l10n_bg.2_l10n_bg_774
#: model:account.account.template,name:l10n_bg.l10n_bg_774
msgid "Passive reinsurance income from previous years"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_508
#: model:account.account,name:l10n_bg.2_l10n_bg_508
#: model:account.account.template,name:l10n_bg.l10n_bg_508
msgid "Payment checks"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_272
#: model:account.account,name:l10n_bg.2_l10n_bg_272
#: model:account.account.template,name:l10n_bg.l10n_bg_272
msgid "Permanent plantations - fruitful"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_273
#: model:account.account,name:l10n_bg.2_l10n_bg_273
#: model:account.account.template,name:l10n_bg.l10n_bg_273
msgid "Permanent plantations - infertile"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_922
#: model:account.account,name:l10n_bg.2_l10n_bg_922
#: model:account.account.template,name:l10n_bg.l10n_bg_922
msgid "Pledged policies"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_231
#: model:account.account,name:l10n_bg.2_l10n_bg_231
#: model:account.account.template,name:l10n_bg.l10n_bg_231
msgid "Positive commercial reputation"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_516
#: model:account.account,name:l10n_bg.2_l10n_bg_516
#: model:account.account.template,name:l10n_bg.l10n_bg_516
msgid "Precious metals and precious stones"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_19
#: model:account.group,name:l10n_bg.2_l10n_bg_group_19
#: model:account.group.template,name:l10n_bg.l10n_bg_group_19
msgid "Premiums and discounts on financial instruments"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_531
#: model:account.account,name:l10n_bg.2_l10n_bg_531
#: model:account.account.template,name:l10n_bg.l10n_bg_531
#: model:account.group,name:l10n_bg.1_l10n_bg_group_65
#: model:account.group,name:l10n_bg.2_l10n_bg_group_65
#: model:account.group.template,name:l10n_bg.l10n_bg_group_65
msgid "Prepaid expenses"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_443
#: model:account.account,name:l10n_bg.2_l10n_bg_443
#: model:account.account.template,name:l10n_bg.l10n_bg_443
msgid "Price differences by shortages and readings"
msgstr ""

#. module: l10n_bg
#: model:account.fiscal.position,name:l10n_bg.1_fiscal_position_bg_eu_private
#: model:account.fiscal.position,name:l10n_bg.2_fiscal_position_bg_eu_private
#: model:account.fiscal.position.template,name:l10n_bg.fiscal_position_bg_eu_private
msgid "Private EU"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_707
#: model:account.account,name:l10n_bg.2_l10n_bg_707
#: model:account.account.template,name:l10n_bg.l10n_bg_707
msgid "Proceeds from liquidation and insolvency"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_303
#: model:account.account,name:l10n_bg.2_l10n_bg_303
#: model:account.account.template,name:l10n_bg.l10n_bg_303
msgid "Products"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_123
#: model:account.account,name:l10n_bg.2_l10n_bg_123
#: model:account.account.template,name:l10n_bg.l10n_bg_123
msgid "Profit and loss from the current year"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_452
#: model:account.account,name:l10n_bg.2_l10n_bg_452
#: model:account.account.template,name:l10n_bg.l10n_bg_452
msgid "Profit tax estimates"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_401
#: model:account.account,name:l10n_bg.2_l10n_bg_401
#: model:account.account.template,name:l10n_bg.l10n_bg_401
msgid "Providers"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_40
#: model:account.group,name:l10n_bg.2_l10n_bg_group_40
#: model:account.group.template,name:l10n_bg.l10n_bg_group_40
msgid "Providers and related accounts"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_607
#: model:account.account,name:l10n_bg.2_l10n_bg_607
#: model:account.account.template,name:l10n_bg.l10n_bg_607
msgid "Provision costs"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_622
#: model:account.account,name:l10n_bg.2_l10n_bg_622
#: model:account.account.template,name:l10n_bg.l10n_bg_622
msgid "Provisioning costs for risky assets"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_4971
#: model:account.account,name:l10n_bg.2_l10n_bg_4971
#: model:account.account.template,name:l10n_bg.l10n_bg_4971
msgid "Provisions for pensions and other similar liabilities"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_497
#: model:account.account,name:l10n_bg.2_l10n_bg_497
#: model:account.account.template,name:l10n_bg.l10n_bg_497
msgid "Provisions recognized as liabilities"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_442
#: model:account.account,name:l10n_bg.2_l10n_bg_442
#: model:account.account.template,name:l10n_bg.l10n_bg_442
msgid "Receivables for losses and deductions"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_44
#: model:account.group,name:l10n_bg.2_l10n_bg_group_44
#: model:account.group.template,name:l10n_bg.l10n_bg_group_44
msgid "Receivables from absences, deductions and litigation"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_934
#: model:account.account,name:l10n_bg.2_l10n_bg_934
#: model:account.account.template,name:l10n_bg.l10n_bg_934
msgid "Receivables from derivative transactions"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_931
#: model:account.account,name:l10n_bg.2_l10n_bg_931
#: model:account.account.template,name:l10n_bg.l10n_bg_931
msgid "Receivables from loans granted under interstate agreements"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_424
#: model:account.account,name:l10n_bg.2_l10n_bg_424
#: model:account.account.template,name:l10n_bg.l10n_bg_424
msgid "Receivables from participations"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_941
#: model:account.account,name:l10n_bg.2_l10n_bg_941
#: model:account.account.template,name:l10n_bg.l10n_bg_941
msgid "Receivables from spot transactions"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_426
#: model:account.account,name:l10n_bg.2_l10n_bg_426
#: model:account.account.template,name:l10n_bg.l10n_bg_426
msgid "Receivables from subscribed share contributions"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_441
#: model:account.account,name:l10n_bg.2_l10n_bg_441
#: model:account.account.template,name:l10n_bg.l10n_bg_441
msgid "Receivables on claims"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_151
#: model:account.account,name:l10n_bg.2_l10n_bg_151
#: model:account.account.template,name:l10n_bg.l10n_bg_151
msgid "Received short-term loans"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_482
#: model:account.account,name:l10n_bg.2_l10n_bg_482
#: model:account.account.template,name:l10n_bg.l10n_bg_482
msgid "Reinsurance and co-insurance estimates"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_781
#: model:account.account,name:l10n_bg.2_l10n_bg_781
#: model:account.account.template,name:l10n_bg.l10n_bg_781
msgid "Reinsurance premium income"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_135
#: model:account.account,name:l10n_bg.2_l10n_bg_135
#: model:account.account.template,name:l10n_bg.l10n_bg_135
msgid "Reinsurance reserves"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_512
#: model:account.account,name:l10n_bg.2_l10n_bg_512
#: model:account.account.template,name:l10n_bg.l10n_bg_512
msgid "Repurchased own shares"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_117
#: model:account.account,name:l10n_bg.2_l10n_bg_117
#: model:account.account.template,name:l10n_bg.l10n_bg_117
msgid "Reserve according to constitutive act"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_981
#: model:account.account,name:l10n_bg.2_l10n_bg_981
#: model:account.account.template,name:l10n_bg.l10n_bg_981
msgid "Reserve account"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_961
#: model:account.account,name:l10n_bg.2_l10n_bg_961
#: model:account.account.template,name:l10n_bg.l10n_bg_961
msgid "Reserve fund"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_116
#: model:account.account,name:l10n_bg.2_l10n_bg_116
#: model:account.account.template,name:l10n_bg.l10n_bg_116
msgid "Reserve related to repurchased own shares"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_133
#: model:account.account,name:l10n_bg.2_l10n_bg_133
#: model:account.account.template,name:l10n_bg.l10n_bg_133
msgid "Reserves for forthcoming payments"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_134
#: model:account.account,name:l10n_bg.2_l10n_bg_134
#: model:account.account.template,name:l10n_bg.l10n_bg_134
msgid "Reserves for reserve fund"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_115
#: model:account.account,name:l10n_bg.2_l10n_bg_115
#: model:account.account.template,name:l10n_bg.l10n_bg_115
msgid "Reserves from share issue"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_113
#: model:account.account,name:l10n_bg.2_l10n_bg_113
#: model:account.account.template,name:l10n_bg.l10n_bg_113
msgid "Reserves from subsequent valuation of current assets"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_114
#: model:account.account,name:l10n_bg.2_l10n_bg_114
#: model:account.account.template,name:l10n_bg.l10n_bg_114
msgid "Reserves from subsequent valuation of financial instruments"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_112
#: model:account.account,name:l10n_bg.2_l10n_bg_112
#: model:account.account.template,name:l10n_bg.l10n_bg_112
msgid "Reserves from subsequent valuation of fixed assets"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_137
#: model:account.account,name:l10n_bg.2_l10n_bg_137
#: model:account.account.template,name:l10n_bg.l10n_bg_137
msgid "Reserves of investment companies"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_138
#: model:account.account,name:l10n_bg.2_l10n_bg_138
#: model:account.account.template,name:l10n_bg.l10n_bg_138
msgid "Reserves of pension companies"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_124
#: model:account.account,name:l10n_bg.2_l10n_bg_124
#: model:account.account.template,name:l10n_bg.l10n_bg_124
msgid "Result in bankruptcy and liquidation"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_125
#: model:account.account,name:l10n_bg.2_l10n_bg_125
#: model:account.account.template,name:l10n_bg.l10n_bg_125
msgid "Result of the activity of non-profit enterprises"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_122
#: model:account.account,name:l10n_bg.2_l10n_bg_122
#: model:account.account.template,name:l10n_bg.l10n_bg_122
msgid "Retained earnings from previous years"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_764
#: model:account.account,name:l10n_bg.2_l10n_bg_764
#: model:account.account.template,name:l10n_bg.l10n_bg_764
msgid "Revenue from recourses"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_762
#: model:account.account,name:l10n_bg.2_l10n_bg_762
#: model:account.account.template,name:l10n_bg.l10n_bg_762
msgid "Revenues from commissions and fees"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_773
#: model:account.account,name:l10n_bg.2_l10n_bg_773
#: model:account.account.template,name:l10n_bg.l10n_bg_773
msgid "Revenues from commissions from reinsurers"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_704
#: model:account.account,name:l10n_bg.2_l10n_bg_704
#: model:account.account.template,name:l10n_bg.l10n_bg_704
msgid "Revenues from financing"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_714
#: model:account.account,name:l10n_bg.2_l10n_bg_714
#: model:account.account.template,name:l10n_bg.l10n_bg_714
msgid "Revenues from government donations"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_712
#: model:account.account,name:l10n_bg.2_l10n_bg_712
#: model:account.account.template,name:l10n_bg.l10n_bg_712
msgid "Revenues from membership fees"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_715
#: model:account.account,name:l10n_bg.2_l10n_bg_715
#: model:account.account.template,name:l10n_bg.l10n_bg_715
msgid "Revenues from other donations"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_713
#: model:account.account,name:l10n_bg.2_l10n_bg_713
#: model:account.account.template,name:l10n_bg.l10n_bg_713
msgid "Revenues from program financing"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_711
#: model:account.account,name:l10n_bg.2_l10n_bg_711
#: model:account.account.template,name:l10n_bg.l10n_bg_711
msgid "Revenues from regulated activities"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_726
#: model:account.account,name:l10n_bg.2_l10n_bg_726
#: model:account.account.template,name:l10n_bg.l10n_bg_726
msgid "Revenues from reintegrated provisions"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_765
#: model:account.account,name:l10n_bg.2_l10n_bg_765
#: model:account.account.template,name:l10n_bg.l10n_bg_765
msgid "Revenues from released reserves"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_702
#: model:account.account,name:l10n_bg.2_l10n_bg_702
#: model:account.account.template,name:l10n_bg.l10n_bg_702
msgid "Revenues from sales of goods"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_706
#: model:account.account,name:l10n_bg.2_l10n_bg_706
#: model:account.account.template,name:l10n_bg.l10n_bg_706
msgid "Revenues from sales of materials"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_701
#: model:account.account,name:l10n_bg.2_l10n_bg_701
#: model:account.account.template,name:l10n_bg.l10n_bg_701
msgid "Revenues from sales of products"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_703
#: model:account.account,name:l10n_bg.2_l10n_bg_703
#: model:account.account.template,name:l10n_bg.l10n_bg_703
msgid "Revenues from sales of services"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_71
#: model:account.group,name:l10n_bg.2_l10n_bg_group_71
#: model:account.group.template,name:l10n_bg.l10n_bg_group_71
msgid "Revenues in non-profit enterprises"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_615
#: model:account.account,name:l10n_bg.2_l10n_bg_615
#: model:account.account.template,name:l10n_bg.l10n_bg_615
msgid "Sales costs"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_414
#: model:account.account,name:l10n_bg.2_l10n_bg_414
#: model:account.account.template,name:l10n_bg.l10n_bg_414
msgid "Sales customers under certain conditions"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_415
#: model:account.account,name:l10n_bg.2_l10n_bg_415
#: model:account.account.template,name:l10n_bg.l10n_bg_415
msgid "Sales customers with related parties"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_70
#: model:account.group,name:l10n_bg.2_l10n_bg_group_70
#: model:account.group.template,name:l10n_bg.l10n_bg_group_70
msgid "Sales revenue"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,name:l10n_bg.l10n_bg_tax_report_a
msgid "Section A: Data on value added tax charged"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,name:l10n_bg.l10n_bg_tax_report_b
msgid "Section B: Data on the exercised right to a tax credit"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,name:l10n_bg.l10n_bg_tax_report_c
msgid "Section C: Result for the period"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_431
#: model:account.account,name:l10n_bg.2_l10n_bg_431
#: model:account.account.template,name:l10n_bg.l10n_bg_431
msgid "Settlements by interbank operations"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_457
#: model:account.account,name:l10n_bg.2_l10n_bg_457
#: model:account.account.template,name:l10n_bg.l10n_bg_457
msgid "Settlements with customs"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_46
#: model:account.group,name:l10n_bg.2_l10n_bg_group_46
#: model:account.group.template,name:l10n_bg.l10n_bg_group_46
msgid "Settlements with insurers"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_47
#: model:account.group,name:l10n_bg.2_l10n_bg_group_47
#: model:account.group.template,name:l10n_bg.l10n_bg_group_47
msgid "Settlements with international financial institutions"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_455
#: model:account.account,name:l10n_bg.2_l10n_bg_455
#: model:account.account.template,name:l10n_bg.l10n_bg_455
msgid "Settlements with ministries"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_451
#: model:account.account,name:l10n_bg.2_l10n_bg_451
#: model:account.account.template,name:l10n_bg.l10n_bg_451
msgid "Settlements with municipalities"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_473
#: model:account.account,name:l10n_bg.2_l10n_bg_473
#: model:account.account.template,name:l10n_bg.l10n_bg_473
msgid "Settlements with other international financial institutions"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_493
#: model:account.account,name:l10n_bg.2_l10n_bg_493
#: model:account.account.template,name:l10n_bg.l10n_bg_493
msgid "Settlements with owners"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_483
#: model:account.account,name:l10n_bg.2_l10n_bg_483
#: model:account.account.template,name:l10n_bg.l10n_bg_483
msgid "Settlements with reinsurers"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_406
#: model:account.account,name:l10n_bg.2_l10n_bg_406
#: model:account.account.template,name:l10n_bg.l10n_bg_406
msgid "Settlements with related parties for purchases"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_416
#: model:account.account,name:l10n_bg.2_l10n_bg_416
#: model:account.account.template,name:l10n_bg.l10n_bg_416
msgid "Settlements with related parties for sales"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_484
#: model:account.account,name:l10n_bg.2_l10n_bg_484
#: model:account.account.template,name:l10n_bg.l10n_bg_484
msgid "Settlements with sedants"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_471
#: model:account.account,name:l10n_bg.2_l10n_bg_471
#: model:account.account.template,name:l10n_bg.l10n_bg_471
msgid "Settlements with the international monetary fund"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_461
#: model:account.account,name:l10n_bg.2_l10n_bg_461
#: model:account.account.template,name:l10n_bg.l10n_bg_461
msgid "Settlements with the national social security institute"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_472
#: model:account.account,name:l10n_bg.2_l10n_bg_472
#: model:account.account.template,name:l10n_bg.l10n_bg_472
msgid "Settlements with the world bank"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_517
#: model:account.account,name:l10n_bg.2_l10n_bg_517
#: model:account.account.template,name:l10n_bg.l10n_bg_517
msgid "Shares and interests in group companies"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_521
#: model:account.account,name:l10n_bg.2_l10n_bg_521
#: model:account.account.template,name:l10n_bg.l10n_bg_521
msgid ""
"Short-term receivables and loans from banks and other financial institutions"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_522
#: model:account.account,name:l10n_bg.2_l10n_bg_522
#: model:account.account.template,name:l10n_bg.l10n_bg_522
msgid ""
"Short-term receivables and loans from non-financial corporations and other "
"customers"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_523
#: model:account.account,name:l10n_bg.2_l10n_bg_523
#: model:account.account.template,name:l10n_bg.l10n_bg_523
msgid "Short-term receivables and loans pledged as collateral"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_311
#: model:account.account,name:l10n_bg.2_l10n_bg_311
#: model:account.account.template,name:l10n_bg.l10n_bg_311
msgid "Small productive animals"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_212
#: model:account.account,name:l10n_bg.2_l10n_bg_212
#: model:account.account.template,name:l10n_bg.l10n_bg_212
msgid "Software products"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_13
#: model:account.group,name:l10n_bg.2_l10n_bg_group_13
#: model:account.group.template,name:l10n_bg.l10n_bg_group_13
msgid "Specific non-capital reserves"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_421
#: model:account.account,name:l10n_bg.2_l10n_bg_421
#: model:account.account.template,name:l10n_bg.l10n_bg_421
msgid "Staff"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_42
#: model:account.group,name:l10n_bg.2_l10n_bg_group_42
#: model:account.group.template,name:l10n_bg.l10n_bg_group_42
msgid "Staff and partners"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_405
#: model:account.account,name:l10n_bg.2_l10n_bg_405
#: model:account.account.template,name:l10n_bg.l10n_bg_405
msgid "Suppliers of related party supplies"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_404
#: model:account.account,name:l10n_bg.2_l10n_bg_404
#: model:account.account.template,name:l10n_bg.l10n_bg_404
msgid "Suppliers of supplies under certain conditions"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_20
#: model:account.group,name:l10n_bg.1_l10n_bg_group_21
#: model:account.group,name:l10n_bg.2_l10n_bg_group_20
#: model:account.group,name:l10n_bg.2_l10n_bg_group_21
#: model:account.group.template,name:l10n_bg.l10n_bg_group_20
#: model:account.group.template,name:l10n_bg.l10n_bg_group_21
msgid "Tangible fixed assets"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,name:l10n_bg.l10n_bg_tax_report_b_2
msgid ""
"Tax base of the received deliveries, VAT, the received deliveries under art."
" 82, para 2-6 of the VAT Act, the import, as well as the tax base of the "
"received deliveries, used for making deliveries under art. 69, para 2 of the"
" VAT Act"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,name:l10n_bg.l10n_bg_tax_report_a_2
msgid "Tax base subject to 0% tax"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,name:l10n_bg.l10n_bg_tax_report_a_1
msgid "Tax base subject to taxation at a rate of 20%"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_403
#: model:account.account,name:l10n_bg.2_l10n_bg_403
#: model:account.account.template,name:l10n_bg.l10n_bg_403
msgid "Trade credit providers"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_43
#: model:account.group,name:l10n_bg.2_l10n_bg_group_43
#: model:account.group.template,name:l10n_bg.l10n_bg_group_43
msgid "Translation estimates and internal estimates"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_132
#: model:account.account,name:l10n_bg.2_l10n_bg_132
#: model:account.account.template,name:l10n_bg.l10n_bg_132
msgid "Transmission-premium reserves"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_491
#: model:account.account,name:l10n_bg.2_l10n_bg_491
#: model:account.account.template,name:l10n_bg.l10n_bg_491
msgid "Trustees"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_121
#: model:account.account,name:l10n_bg.2_l10n_bg_121
#: model:account.account.template,name:l10n_bg.l10n_bg_121
msgid "Uncovered loss from previous years"
msgstr ""

#. module: l10n_bg
#: model:account.group,name:l10n_bg.1_l10n_bg_group_99
#: model:account.group,name:l10n_bg.2_l10n_bg_group_99
#: model:account.group.template,name:l10n_bg.l10n_bg_group_99
msgid "Various contingent liability accounts"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_984
#: model:account.account,name:l10n_bg.2_l10n_bg_984
#: model:account.account.template,name:l10n_bg.l10n_bg_984
msgid "Various statistical accounts"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_4531
#: model:account.account,name:l10n_bg.2_l10n_bg_4531
#: model:account.account.template,name:l10n_bg.l10n_bg_4531
msgid "Vat purchases"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_4538
#: model:account.account,name:l10n_bg.2_l10n_bg_4538
#: model:account.account.template,name:l10n_bg.l10n_bg_4538
msgid "Vat recovery"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_4532
#: model:account.account,name:l10n_bg.2_l10n_bg_4532
#: model:account.account.template,name:l10n_bg.l10n_bg_4532
msgid "Vat sales"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_4534
#: model:account.account,name:l10n_bg.2_l10n_bg_4534
#: model:account.account.template,name:l10n_bg.l10n_bg_4534
msgid "Vat sales outside the country"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_4539
#: model:account.account,name:l10n_bg.2_l10n_bg_4539
#: model:account.account.template,name:l10n_bg.l10n_bg_4539
msgid "Vat to pay"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_207
#: model:account.account,name:l10n_bg.2_l10n_bg_207
#: model:account.account.template,name:l10n_bg.l10n_bg_207
msgid "Vehicles"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_462
#: model:account.account,name:l10n_bg.2_l10n_bg_462
#: model:account.account.template,name:l10n_bg.l10n_bg_462
msgid "Voluntary social security estimates"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_604
#: model:account.account,name:l10n_bg.2_l10n_bg_604
#: model:account.account.template,name:l10n_bg.l10n_bg_604
msgid "Wage costs (remuneration)"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_305
#: model:account.account,name:l10n_bg.2_l10n_bg_305
#: model:account.account.template,name:l10n_bg.l10n_bg_305
msgid "Work in progress"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_942
#: model:account.account,name:l10n_bg.2_l10n_bg_942
#: model:account.account.template,name:l10n_bg.l10n_bg_942
msgid "Write-off of outstanding receivables in banks"
msgstr ""

#. module: l10n_bg
#: model:account.account,name:l10n_bg.1_l10n_bg_314
#: model:account.account,name:l10n_bg.2_l10n_bg_314
#: model:account.account.template,name:l10n_bg.l10n_bg_314
msgid "Young animals"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,name:l10n_bg.l10n_bg_tax_report_01
msgid ""
"[01] Total amount of the tax bases for VAT taxation (amount from class 11 to"
" class 16)"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,name:l10n_bg.l10n_bg_tax_report_11
msgid ""
"[11] - tax base of taxable supplies, incl. deliveries under the conditions "
"of distance sales with a place of performance on the territory of the "
"country"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,name:l10n_bg.l10n_bg_tax_report_12
msgid ""
"[12] - tax base of VAT and tax base of received supplies under Article 82, "
"paragraphs 2-6 of the VAT Act"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,name:l10n_bg.l10n_bg_tax_report_13
msgid "[13] Tax base of taxable supplies at a rate of 9%"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,name:l10n_bg.l10n_bg_tax_report_14
msgid "[14] Tax base for supplies under Chapter Three of the VAT Act"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,name:l10n_bg.l10n_bg_tax_report_15
msgid "[15] Tax base of AEO of goods"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,name:l10n_bg.l10n_bg_tax_report_16
msgid ""
"[16] Tax base of supplies under Articles 140, 146 and 173 of the VAT Act "
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,name:l10n_bg.l10n_bg_tax_report_17
msgid ""
"[17] Tax base for supplies of services under Article 21, paragraph 2 with a "
"place of performance on the territory of another member state"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,name:l10n_bg.l10n_bg_tax_report_18
msgid ""
"[18] Tax base of supplies under Article 69, paragraph 2 of the VAT Act, "
"incl. deliveries on the basis of distance selling with a place of "
"performance in the territory of another Member State, as well as deliveries "
"as an intermediary in a tripartite"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,name:l10n_bg.l10n_bg_tax_report_19
msgid "[19] Tax base of exempt supplies and exempt VOP"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,name:l10n_bg.l10n_bg_tax_report_20
msgid "[20] All VAT charged (amount from class 21 to class 24)"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,name:l10n_bg.l10n_bg_tax_report_21
msgid "[21] VAT charged"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,name:l10n_bg.l10n_bg_tax_report_22
msgid ""
"[22] VAT charged for VAT and for received deliveries under Art. 82, para 2-6"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,name:l10n_bg.l10n_bg_tax_report_23
msgid "[23] Tax charged on supplies of goods and services for personal use"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,name:l10n_bg.l10n_bg_tax_report_24
msgid "[24] VAT charged (9%)"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,name:l10n_bg.l10n_bg_tax_report_30
msgid ""
"[30] Tax base and tax on the received deliveries, VAT, the received "
"deliveries under art. 82, para. 2-6 of the VAT Act and imports without the "
"right to a tax credit or without tax"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,name:l10n_bg.l10n_bg_tax_report_31
msgid "[31] - entitled to a full tax credit"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,name:l10n_bg.l10n_bg_tax_report_32
msgid "[32] - with the right to a partial tax credit"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,name:l10n_bg.l10n_bg_tax_report_33
msgid "[33] Coefficient under Article 73, paragraph 5 of the VAT Act"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,name:l10n_bg.l10n_bg_tax_report_40
msgid "[40] Total tax credit (41 + 42 x class 33 + 43)"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,name:l10n_bg.l10n_bg_tax_report_41
msgid "[41] VAT eligible for a full tax credit"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,name:l10n_bg.l10n_bg_tax_report_42
msgid "[42] VAT with the right to a partial tax credit"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,name:l10n_bg.l10n_bg_tax_report_43
msgid "[43] Annual adjustment under Article 73, paragraph 8 (+/-)"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,name:l10n_bg.l10n_bg_tax_report_50
msgid "[50] VAT to be paid (class 20 - class 40) >= 0"
msgstr ""

#. module: l10n_bg
#: model:account.tax.report.line,name:l10n_bg.l10n_bg_tax_report_60
msgid "[60] VAT for refund (class 20 - class 40) < 0"
msgstr ""
