<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Fiscal Position Templates -->

    <record id="fiscal_position_template_1" model="account.fiscal.position.template">
            <field name="sequence">1</field>
            <field name="name">Régime National</field>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="auto_apply" eval="True"/>
            <field name="vat_required" eval="True"/>
            <field name="country_id" ref="base.be"/>
    </record>

    <record id="fiscal_position_template_5" model="account.fiscal.position.template">
            <field name="sequence">2</field>
            <field name="name">EU privé</field>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="auto_apply" eval="True"/>
            <field name="country_group_id" ref="base.europe"/>
    </record>

    <record id="fiscal_position_template_2" model="account.fiscal.position.template">
            <field name="sequence">4</field>
            <field name="name">Régime Extra-Communautaire</field>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="auto_apply" eval="True"/>
    </record>

    <record id="fiscal_position_template_3" model="account.fiscal.position.template">
            <field name="sequence">3</field>
            <field name="name">Régime Intra-Communautaire</field>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="auto_apply" eval="True"/>
            <field name="vat_required" eval="True"/>
            <field name="country_group_id" ref="base.europe"/>
    </record>

    <record id="fiscal_position_template_4" model="account.fiscal.position.template">
            <field name="name">Régime Cocontractant</field>
            <field name="sequence">5</field>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
    </record>

    <!-- Fiscal Position Account Templates -->

    <record id="fiscal_position_account_template_3" model="account.fiscal.position.account.template">
            <field name="position_id" ref="fiscal_position_template_3"  />
            <field name="account_src_id" ref="l10n_be.a7000" />
            <field name="account_dest_id" ref="l10n_be.a7001" />
    </record>

    <record id="fiscal_position_account_template_4" model="account.fiscal.position.account.template">
            <field name="position_id" ref="fiscal_position_template_3"  />
            <field name="account_src_id" ref="l10n_be.a7010" />
            <field name="account_dest_id" ref="l10n_be.a7011" />
    </record>

    <record id="fiscal_position_account_template_6" model="account.fiscal.position.account.template">
            <field name="position_id" ref="fiscal_position_template_3"  />
            <field name="account_src_id" ref="l10n_be.a7050" />
            <field name="account_dest_id" ref="l10n_be.a7051" />
    </record>

    <record id="fiscal_position_account_template_7" model="account.fiscal.position.account.template">
            <field name="position_id" ref="fiscal_position_template_2"  />
            <field name="account_src_id" ref="l10n_be.a7000" />
            <field name="account_dest_id" ref="l10n_be.a7002" />
    </record>
    <record id="fiscal_position_account_template_8" model="account.fiscal.position.account.template">
            <field name="position_id" ref="fiscal_position_template_2"  />
            <field name="account_src_id" ref="l10n_be.a7010" />
            <field name="account_dest_id" ref="l10n_be.a7012" />
    </record>
    <record id="fiscal_position_account_template_10" model="account.fiscal.position.account.template">
            <field name="position_id" ref="fiscal_position_template_2"  />
            <field name="account_src_id" ref="l10n_be.a7050" />
            <field name="account_dest_id" ref="l10n_be.a7052" />
    </record>
</odoo>
