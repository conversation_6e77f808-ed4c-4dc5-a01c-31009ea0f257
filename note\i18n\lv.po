# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* note
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <arn<PERSON>@allegro.lv>, 2022
# <PERSON><PERSON><PERSON>, 2022
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:49+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: ievaputnina <<EMAIL>>, 2023\n"
"Language-Team: Latvian (https://app.transifex.com/odoo/teams/41243/lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : 2);\n"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_kanban
msgid "<i class=\"fa fa-check\" role=\"img\" aria-label=\"Opened\" title=\"Opened\"/>"
msgstr ""

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_kanban
msgid "<i class=\"fa fa-undo\" role=\"img\" aria-label=\"Closed\" title=\"Closed\"/>"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_mail_activity_type__category
msgid "Action"
msgstr "Darbība"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_needaction
msgid "Action Needed"
msgstr "Nepieciešama darbība"

#. module: note
#: model:ir.model.fields,help:note.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"Darbības var palaist noteiktu uzvedību, piemēram, atvērt kalendāra skatu vai"
" automātiski atzīmēt kā pabeigtu, kad dokuments tiek augšupielādēts."

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__open
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Active"
msgstr "Aktīvs Sistēmā"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_ids
msgid "Activities"
msgstr "Aktivitātes"

#. module: note
#: model:ir.model,name:note.model_mail_activity
msgid "Activity"
msgstr "Aktivitāte"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Aktivitātes izņēmuma noformējums"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_state
msgid "Activity State"
msgstr "Darbības stāvoklis"

#. module: note
#: model:ir.model,name:note.model_mail_activity_type
msgid "Activity Type"
msgstr "Aktivitātes tips"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_type_icon
msgid "Activity Type Icon"
msgstr "Aktivitātes veida ikona"

#. module: note
#: model_terms:ir.actions.act_window,help:note.action_note_note
msgid "Add a new personal note"
msgstr ""

#. module: note
#: model_terms:ir.actions.act_window,help:note.note_tag_action
msgid "Add a new tag"
msgstr "Pievienot jaunu tagu"

#. module: note
#. openerp-web
#: code:addons/note/static/src/xml/systray.xml:0
#, python-format
msgid "Add a note"
msgstr "Pievienot piezīmi"

#. module: note
#. openerp-web
#: code:addons/note/static/src/xml/systray.xml:0
#, python-format
msgid "Add new note"
msgstr ""

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Archive"
msgstr "Arhīvs"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_attachment_count
msgid "Attachment Count"
msgstr "Pielikumu skaits"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "By sticky note Category"
msgstr "Pēc piespraužu kategorijām"

#. module: note
#. openerp-web
#: code:addons/note/static/src/xml/systray.xml:0
#, python-format
msgid "Channel"
msgstr "Kanāls"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__color
#: model:ir.model.fields,field_description:note.field_note_tag__color
msgid "Color Index"
msgstr "Krāsas Indekss"

#. module: note
#: model:ir.ui.menu,name:note.menu_note_configuration
msgid "Configuration"
msgstr "Uzstādījumi"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__create_uid
#: model:ir.model.fields,field_description:note.field_note_stage__create_uid
#: model:ir.model.fields,field_description:note.field_note_tag__create_uid
msgid "Created by"
msgstr "Izveidoja"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__create_date
#: model:ir.model.fields,field_description:note.field_note_stage__create_date
#: model:ir.model.fields,field_description:note.field_note_tag__create_date
msgid "Created on"
msgstr "Izveidots"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__date_done
msgid "Date done"
msgstr "Pabeig. datums"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_kanban
msgid "Delete"
msgstr "Izdzēst"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__display_name
#: model:ir.model.fields,field_description:note.field_note_stage__display_name
#: model:ir.model.fields,field_description:note.field_note_tag__display_name
msgid "Display Name"
msgstr "Attēlotais nosaukums"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_kanban
msgid "Dropdown menu"
msgstr "Nolaižamā izvēlne"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_stage__fold
msgid "Folded by Default"
msgstr ""

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_kanban
msgid "Follower"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_follower_ids
msgid "Followers"
msgstr "Sekotāji"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_partner_ids
msgid "Followers (Partners)"
msgstr "Sekotāji (Partneri)"

#. module: note
#: model:ir.model.fields,help:note.field_note_note__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Fonts awesome icon piem. fa-tasks"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Future Activities"
msgstr "Nākotnes aktivitātes"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Group By"
msgstr "Grupēt pēc"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__has_message
msgid "Has Message"
msgstr "ir ziņojums"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__id
#: model:ir.model.fields,field_description:note.field_note_stage__id
#: model:ir.model.fields,field_description:note.field_note_tag__id
msgid "ID"
msgstr "ID"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_exception_icon
msgid "Icon"
msgstr "Icon"

#. module: note
#: model:ir.model.fields,help:note.field_note_note__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikona izņēmuma aktivitātes identificēšanai."

#. module: note
#: model:ir.model.fields,help:note.field_note_note__message_needaction
#: model:ir.model.fields,help:note.field_note_note__message_unread
msgid "If checked, new messages require your attention."
msgstr "Ja atzīmēts, jums jāpievērš uzmanība jauniem ziņojumiem."

#. module: note
#: model:ir.model.fields,help:note.field_note_note__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Ja atzīmēts, daži ziņojumi satur piegādes kļūdu."

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_is_follower
msgid "Is Follower"
msgstr "Ir sekotājs"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note____last_update
#: model:ir.model.fields,field_description:note.field_note_stage____last_update
#: model:ir.model.fields,field_description:note.field_note_tag____last_update
msgid "Last Modified on"
msgstr "Pēdējoreiz modificēts"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__write_uid
#: model:ir.model.fields,field_description:note.field_note_stage__write_uid
#: model:ir.model.fields,field_description:note.field_note_tag__write_uid
msgid "Last Updated by"
msgstr "Pēdējoreiz atjaunoja"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__write_date
#: model:ir.model.fields,field_description:note.field_note_stage__write_date
#: model:ir.model.fields,field_description:note.field_note_tag__write_date
msgid "Last Updated on"
msgstr "Pēdējoreiz atjaunots"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Late Activities"
msgstr "Pēdējās aktivitātes"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_main_attachment_id
msgid "Main Attachment"
msgstr "Galvenais pielikums"

#. module: note
#: model:note.stage,name:note.note_stage_01
msgid "Meeting Minutes"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_has_error
msgid "Message Delivery error"
msgstr "Ziņojuma piegādes kļūda"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_ids
msgid "Messages"
msgstr "Ziņojumi"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Manas aktivitātes izpildes termiņš"

#. module: note
#: model:note.stage,name:note.note_stage_00
msgid "New"
msgstr "Jauns"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Nākamais aktivitāšu kalendāra pasākums"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Nākamās darbības beigu termiņš"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_summary
msgid "Next Activity Summary"
msgstr "Nākamās darbības kopsavilkums"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_type_id
msgid "Next Activity Type"
msgstr "Nākamās darbības veids"

#. module: note
#: model:ir.model,name:note.model_note_note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
#: model_terms:ir.ui.view,arch_db:note.view_note_note_form
msgid "Note"
msgstr "Piezīmes"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__memo
msgid "Note Content"
msgstr "Piezīmes saturs"

#. module: note
#: model:ir.model,name:note.model_note_stage
msgid "Note Stage"
msgstr "Piezīmes stāvoklis"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__name
msgid "Note Summary"
msgstr "Piezīmes kopsavilkums"

#. module: note
#: model:ir.model,name:note.model_note_tag
msgid "Note Tag"
msgstr "Piezīmes birka"

#. module: note
#: code:addons/note/models/res_users.py:0
#: model:ir.actions.act_window,name:note.action_note_note
#: model:ir.ui.menu,name:note.menu_note_notes
#: model:note.stage,name:note.note_stage_02
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
#, python-format
msgid "Notes"
msgstr "Piezīmes"

#. module: note
#: model_terms:ir.actions.act_window,help:note.action_note_note
msgid ""
"Notes are private, unless you share them by inviting follower on a note.\n"
"            (Useful for meeting minutes)."
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_needaction_counter
msgid "Number of Actions"
msgstr "Darbību skaits"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_has_error_counter
msgid "Number of errors"
msgstr "Kļūdu skaits"

#. module: note
#: model:ir.model.fields,help:note.field_note_note__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Ziņojumu, kuriem nepieciešama darbība, skaits"

#. module: note
#: model:ir.model.fields,help:note.field_note_note__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Ziņojumu, kas satur piegādes kļūdu, skaits"

#. module: note
#: model:ir.model.fields,help:note.field_note_note__message_unread_counter
msgid "Number of unread messages"
msgstr "Neizlasīto ziņojumu skaits"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__user_id
#: model:ir.model.fields,field_description:note.field_note_stage__user_id
msgid "Owner"
msgstr "Īpašnieks"

#. module: note
#: model:ir.model.fields,help:note.field_note_stage__user_id
msgid "Owner of the note stage"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_mail_activity__note_id
msgid "Related Note"
msgstr ""

#. module: note
#. openerp-web
#: code:addons/note/static/src/xml/systray.xml:0
#, python-format
msgid "Remember..."
msgstr ""

#. module: note
#: model:ir.model.fields.selection,name:note.selection__mail_activity_type__category__reminder
#: model:mail.activity.type,name:note.mail_activity_data_reminder
msgid "Reminder"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_user_id
msgid "Responsible User"
msgstr "Atbildīgie lietotāji"

#. module: note
#. openerp-web
#: code:addons/note/static/src/xml/systray.xml:0
#, python-format
msgid "SAVE"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__sequence
#: model:ir.model.fields,field_description:note.field_note_stage__sequence
msgid "Sequence"
msgstr "Secība"

#. module: note
#. openerp-web
#: code:addons/note/static/src/xml/systray.xml:0
#: code:addons/note/static/src/xml/systray.xml:0
#, python-format
msgid "Set date and time"
msgstr ""

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Show all records which has next action date is before today"
msgstr ""
"Rādīt visus ierakstus, kuriem  nākamais darbības datums ir pirms šodienas"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__stage_id
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Stage"
msgstr "Stadija"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_stage__name
msgid "Stage Name"
msgstr "Posma nosaukums"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_stage_form
msgid "Stage of Notes"
msgstr "Piezīmju stāvokļi"

#. module: note
#: model:ir.actions.act_window,name:note.action_note_stage
#: model:ir.ui.menu,name:note.menu_notes_stage
#: model_terms:ir.ui.view,arch_db:note.view_note_note_tree
msgid "Stages"
msgstr "Posmi"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_stage_tree
msgid "Stages of Notes"
msgstr "Piezīmes stāvokļi"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__stage_ids
msgid "Stages of Users"
msgstr "Lietotāju stāvokļi"

#. module: note
#: model:ir.model.fields,help:note.field_note_note__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Statuss balstīts uz darbībām\n"
"Kavēts: termiņš jauy ir pagājis\n"
"Šodien: darbības datums ir šodien\n"
"Plānots: nākotnes aktivitātes."

#. module: note
#: model:ir.model.fields,field_description:note.field_note_tag__name
msgid "Tag Name"
msgstr "Tag Name"

#. module: note
#: model:ir.model.constraint,message:note.constraint_note_tag_name_uniq
msgid "Tag name already exists !"
msgstr "Birkas nosaukums jau eksistē !"

#. module: note
#: model:ir.actions.act_window,name:note.note_tag_action
#: model:ir.model.fields,field_description:note.field_note_note__tag_ids
#: model:ir.ui.menu,name:note.notes_tag_menu
#: model_terms:ir.ui.view,arch_db:note.note_tag_view_form
#: model_terms:ir.ui.view,arch_db:note.note_tag_view_tree
#: model_terms:ir.ui.view,arch_db:note.view_note_note_form
msgid "Tags"
msgstr "Birkas"

#. module: note
#. openerp-web
#: code:addons/note/static/src/js/systray_activity_menu.js:0
#, python-format
msgid "Today"
msgstr "Šodien"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Today Activities"
msgstr "Šodienas aktivitātes"

#. module: note
#: model:note.stage,name:note.note_stage_03
msgid "Todo"
msgstr "Todo"

#. module: note
#: model:ir.model.fields,help:note.field_note_note__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Reģistrētās izņēmuma aktivitātes veids."

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_unread
msgid "Unread Messages"
msgstr "Neizlasīti ziņojumi"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Neizlasīto ziņojumu skaitītājs"

#. module: note
#: model:ir.model.fields,help:note.field_note_stage__sequence
msgid "Used to order the note stages"
msgstr ""

#. module: note
#: model:ir.model,name:note.model_res_users
msgid "Users"
msgstr "Lietotāji"
