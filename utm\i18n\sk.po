# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* utm
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON> <<EMAIL>>, 2022
# ka<PERSON><PERSON><PERSON> schustero<PERSON> <karolina.schustero<PERSON>@vdp.sk>, 2022
# <PERSON>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <jar<PERSON>.b<PERSON><PERSON>@ekoenergo.sk>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Slovak (https://app.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_medium__active
msgid "Active"
msgstr "Aktívne"

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_campaign__is_auto_campaign
msgid "Allows us to filter relevant Campaigns"
msgstr ""

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Approval-based Flow"
msgstr ""

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Approved"
msgstr "Schválené"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_search
msgid "Archived"
msgstr "Archivovaný"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.action_view_utm_tag
msgid "Assign tags to your campaigns to organize, filter and track them."
msgstr ""

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Audience-driven Flow"
msgstr ""

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__is_auto_campaign
msgid "Automatically Generated Campaign"
msgstr ""

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_mixin__campaign_id
msgid "Campaign"
msgstr "Kampaň"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__name
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form_quick_create
msgid "Campaign Name"
msgstr "Názov kampane"

#. module: utm
#: model:ir.model,name:utm.model_utm_stage
msgid "Campaign Stage"
msgstr "Štádium kampane"

#. module: utm
#: model:ir.actions.act_window,name:utm.action_view_utm_tag
#: model_terms:ir.ui.view,arch_db:utm.utm_tag_view_tree
msgid "Campaign Tags"
msgstr "Tagy kampane"

#. module: utm
#: model:ir.actions.act_window,name:utm.utm_campaign_action
#: model:ir.ui.menu,name:utm.menu_utm_campaign_act
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Campaigns"
msgstr "Kampane"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_campaign_action
msgid ""
"Campaigns are used to centralize your marketing efforts and track their "
"results."
msgstr ""

#. module: utm
#: model:utm.campaign,name:utm.utm_campaign_christmas_special
msgid "Christmas Special"
msgstr "Vianočný špeciál"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Collect ideas, design creative content and publish it once reviewed."
msgstr ""

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__color
#: model:ir.model.fields,field_description:utm.field_utm_tag__color
msgid "Color Index"
msgstr "Index farieb"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Copywriting"
msgstr "Copywriting"

#. module: utm
#: model:utm.source,name:utm.utm_source_craigslist
msgid "Craigslist"
msgstr ""

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_medium_action
msgid "Create a Medium"
msgstr ""

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.action_view_utm_tag
msgid "Create a Tag"
msgstr ""

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_campaign_action
msgid "Create a campaign"
msgstr "Vytvorte kampaň"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.action_view_utm_stage
msgid "Create a stage for your campaigns"
msgstr "Vytvorte štádia pre svoje kampane"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__create_uid
#: model:ir.model.fields,field_description:utm.field_utm_medium__create_uid
#: model:ir.model.fields,field_description:utm.field_utm_source__create_uid
#: model:ir.model.fields,field_description:utm.field_utm_stage__create_uid
#: model:ir.model.fields,field_description:utm.field_utm_tag__create_uid
msgid "Created by"
msgstr "Vytvoril"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__create_date
#: model:ir.model.fields,field_description:utm.field_utm_medium__create_date
#: model:ir.model.fields,field_description:utm.field_utm_source__create_date
#: model:ir.model.fields,field_description:utm.field_utm_stage__create_date
#: model:ir.model.fields,field_description:utm.field_utm_tag__create_date
msgid "Created on"
msgstr "Vytvorené"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Creative Flow"
msgstr ""

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_kanban
msgid "Delete"
msgstr "Zmazať"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Deploy"
msgstr "Rozmiestniť"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Deployed"
msgstr ""

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: model:utm.stage,name:utm.campaign_stage_2
#, python-format
msgid "Design"
msgstr "Dizajn"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__display_name
#: model:ir.model.fields,field_description:utm.field_utm_medium__display_name
#: model:ir.model.fields,field_description:utm.field_utm_source__display_name
#: model:ir.model.fields,field_description:utm.field_utm_stage__display_name
#: model:ir.model.fields,field_description:utm.field_utm_tag__display_name
msgid "Display Name"
msgstr "Zobrazovaný názov"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Done"
msgstr "Hotové"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_kanban
msgid "Dropdown menu"
msgstr "Rozbaľovacie menu"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_kanban
msgid "Edit"
msgstr "Upraviť"

#. module: utm
#: model:utm.campaign,name:utm.utm_campaign_email_campaign_products
msgid "Email Campaign - Products"
msgstr "Emailová kampaň - produkty"

#. module: utm
#: model:utm.campaign,name:utm.utm_campaign_email_campaign_services
msgid "Email Campaign - Services"
msgstr "Emailová kampaň - služby"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Event-driven Flow"
msgstr ""

#. module: utm
#: model:utm.source,name:utm.utm_source_facebook
msgid "Facebook"
msgstr "Facebook"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Gather Data"
msgstr ""

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid ""
"Gather data, build a recipient list and write content based on your "
"Marketing target."
msgstr ""

#. module: utm
#: model:utm.source,name:utm.utm_source_glassdoor
msgid "Glassdoor"
msgstr ""

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Group By"
msgstr "Zoskupiť podľa"

#. module: utm
#: model:ir.model,name:utm.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP smerovanie"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__id
#: model:ir.model.fields,field_description:utm.field_utm_medium__id
#: model:ir.model.fields,field_description:utm.field_utm_source__id
#: model:ir.model.fields,field_description:utm.field_utm_stage__id
#: model:ir.model.fields,field_description:utm.field_utm_tag__id
msgid "ID"
msgstr "ID"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Ideas"
msgstr "Nápady"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign____last_update
#: model:ir.model.fields,field_description:utm.field_utm_medium____last_update
#: model:ir.model.fields,field_description:utm.field_utm_source____last_update
#: model:ir.model.fields,field_description:utm.field_utm_stage____last_update
#: model:ir.model.fields,field_description:utm.field_utm_tag____last_update
msgid "Last Modified on"
msgstr "Posledná úprava"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__write_uid
#: model:ir.model.fields,field_description:utm.field_utm_medium__write_uid
#: model:ir.model.fields,field_description:utm.field_utm_source__write_uid
#: model:ir.model.fields,field_description:utm.field_utm_stage__write_uid
#: model:ir.model.fields,field_description:utm.field_utm_tag__write_uid
msgid "Last Updated by"
msgstr "Naposledy upravoval"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__write_date
#: model:ir.model.fields,field_description:utm.field_utm_medium__write_date
#: model:ir.model.fields,field_description:utm.field_utm_source__write_date
#: model:ir.model.fields,field_description:utm.field_utm_stage__write_date
#: model:ir.model.fields,field_description:utm.field_utm_tag__write_date
msgid "Last Updated on"
msgstr "Naposledy upravované"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Later"
msgstr "Neskôr"

#. module: utm
#: model:utm.source,name:utm.utm_source_mailing
msgid "Lead Recall"
msgstr "Zrušenie potenciálnej príležitosti"

#. module: utm
#: model:ir.ui.menu,name:utm.menu_link_tracker_root
msgid "Link Tracker"
msgstr "Sledovač linky"

#. module: utm
#: model:utm.source,name:utm.utm_source_linkedin
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "List-Building"
msgstr ""

#. module: utm
#: model:utm.tag,name:utm.utm_tag_1
msgid "Marketing"
msgstr "Marketing"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_mixin__medium_id
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_form
msgid "Medium"
msgstr "Médium"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_medium__name
msgid "Medium Name"
msgstr "Názov média"

#. module: utm
#: model:ir.actions.act_window,name:utm.utm_medium_action
#: model:ir.ui.menu,name:utm.menu_utm_medium
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_tree
msgid "Mediums"
msgstr "Mediá"

#. module: utm
#: model:utm.source,name:utm.utm_source_monster
msgid "Monster"
msgstr ""

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_stage__name
#: model:ir.model.fields,field_description:utm.field_utm_tag__name
msgid "Name"
msgstr "Meno"

#. module: utm
#: model:utm.stage,name:utm.default_utm_stage
msgid "New"
msgstr "Nové"

#. module: utm
#: model:utm.source,name:utm.utm_source_newsletter
msgid "Newsletter"
msgstr "Informačný leták"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_source_action
msgid "No Sources yet!"
msgstr ""

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Pre-Launch"
msgstr ""

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Prepare Campaigns and get them approved before making them go live."
msgstr ""

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid ""
"Prepare your Campaign, test it with part of your audience and deploy it "
"fully afterwards."
msgstr ""

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Report"
msgstr "Report"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__user_id
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Responsible"
msgstr "Zodpovedný"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Review"
msgstr "Prehľad"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Running"
msgstr "Aktuálne"

#. module: utm
#: model:utm.campaign,name:utm.utm_campaign_fall_drive
msgid "Sale"
msgstr "Predaj"

#. module: utm
#: model:utm.stage,name:utm.campaign_stage_1
msgid "Schedule"
msgstr "Rozvrh"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_search
msgid "Search UTM Medium"
msgstr "Vyhľadajte médium UTM"

#. module: utm
#: model:utm.source,name:utm.utm_source_search_engine
msgid "Search engine"
msgstr "Vyhľadávač"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Send"
msgstr "Poslať"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: model:utm.stage,name:utm.campaign_stage_3
#, python-format
msgid "Sent"
msgstr "Poslané"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_stage__sequence
msgid "Sequence"
msgstr "Postupnosť"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Soft-Launch"
msgstr ""

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Soft-Launch Flow"
msgstr ""

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_mixin__source_id
#: model_terms:ir.ui.view,arch_db:utm.utm_source_view_form
#: model_terms:ir.ui.view,arch_db:utm.utm_source_view_tree
msgid "Source"
msgstr "Zdroj"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_source__name
msgid "Source Name"
msgstr "Meno zdroja"

#. module: utm
#: model:ir.actions.act_window,name:utm.utm_source_action
#: model:ir.ui.menu,name:utm.menu_utm_source
msgid "Sources"
msgstr "Zdroje"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__stage_id
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Stage"
msgstr "Štádium"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_stage_view_search
#: model_terms:ir.ui.view,arch_db:utm.utm_stage_view_tree
msgid "Stages"
msgstr "Štádia"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.action_view_utm_stage
msgid ""
"Stages allow you to organize your workflow  (e.g. : plan, design, in "
"progress,  done, …)."
msgstr ""
"Fázy vám umožňujú usporiadať pracovný postup (napr. plán, návrh, prebieh, "
"hotové, ...)."

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_tag__color
msgid ""
"Tag color. No color means no display in kanban to distinguish internal tags "
"from public categorization tags."
msgstr ""

#. module: utm
#: model:ir.model.constraint,message:utm.constraint_utm_tag_name_uniq
msgid "Tag name already exists !"
msgstr "Názov značky už existuje!"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__tag_ids
msgid "Tags"
msgstr "Tagy"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "This Month"
msgstr "Tento mesiac"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "This Week"
msgstr "Tento týždeň"

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_mixin__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""
"Toto je názov, ktorý vám pomáha sledovať vaše rôzne kampane, napr. "
"Fall_Drive, Christmas_Special"

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_mixin__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr ""
"Toto je spôsob dodávania, napr. pohľadnica, e-mail alebo bannerová reklama"

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_mixin__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr ""
"Toto je zdroj odkazu, napr. vyhľadávací prostriedok, iná doména, alebo názov"
" emailového zoznamu"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "To be Approved"
msgstr ""

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid ""
"Track incoming events (e.g. : Christmas, Black Friday, ...) and publish "
"timely content."
msgstr ""

#. module: utm
#: model:utm.source,name:utm.utm_source_twitter
msgid "Twitter"
msgstr "Twitter"

#. module: utm
#: model:ir.model,name:utm.model_utm_campaign
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form
msgid "UTM Campaign"
msgstr "UTM kampaň"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_tree
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "UTM Campaigns"
msgstr "UTM kampane "

#. module: utm
#: model:ir.model,name:utm.model_utm_medium
msgid "UTM Medium"
msgstr "UTM médium"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_medium_action
msgid ""
"UTM Mediums track the mean that was used to attract traffic (e.g. "
"\"Website\", \"Twitter\", ...)."
msgstr ""

#. module: utm
#: model:ir.model,name:utm.model_utm_mixin
msgid "UTM Mixin"
msgstr "UTM mixin"

#. module: utm
#: model:ir.model,name:utm.model_utm_source
msgid "UTM Source"
msgstr "UTM zdroj "

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_source_action
msgid ""
"UTM Sources track where traffic comes from  (e.g. \"May Newsletter\", \"\", "
"...)."
msgstr ""

#. module: utm
#: model:ir.actions.act_window,name:utm.action_view_utm_stage
msgid "UTM Stages"
msgstr "UTM štádium"

#. module: utm
#: model:ir.model,name:utm.model_utm_tag
msgid "UTM Tag"
msgstr "UTM tag"

#. module: utm
#: model:ir.ui.menu,name:utm.marketing_utm
msgid "UTMs"
msgstr "UTMs"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Use This For My Campaigns"
msgstr ""

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form_quick_create
msgid "e.g. Black Friday"
msgstr ""
