<?xml version="1.0"?>
<odoo>

    <record id="hr_assessment_annual_form" model="ir.ui.view">
        <field name="name">hr.contract.assessment.annual.form</field>
        <field name="model">hr.contract.assessment.annual</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button id="make_done" name="make_done" type="object" string="تأكيد واحتساب" states="draft" class="btn-primary" groups="hr_employees_masarat.group_hr_masarat_assessment_annual"/>
                    <button id="make_done" name="make_draft" type="object" string="ارجاع كمسودة" states="done"  groups="hr_employees_masarat.group_hr_masarat_assessment_annual"/>
                    <field name="state" widget="statusbar" statusbar_visible=" "/>
                </header>
                <sheet>
                    <div>
                        <h2>
                            <field name="employee_id" attrs="{'readonly':[('state','=','done')]}" placeholder="Employee"/>
                        </h2>
                        <h2>
                            <field name="year" attrs="{'readonly':[('state','=','done')]}" placeholder="Year"/>
                            <field name="month" attrs="{'readonly':[('state','=','done')]}" placeholder="Month"/>
                        </h2>
                    </div>
                    <group>
                        <group>
                            <field name="current_user_id" attrs="{'readonly':[('state','=','done')]}"/>
                            <field name="department_id" attrs="{'readonly':[('state','=','done')]}"/>
                            <field name="job_id" attrs="{'readonly':[('state','=','done')]}"/>
                            <field name="is_hr_group" invisible="1"/>
<!--                            <field name="contract_id"/>-->
                        </group>
                    </group>
                    <div>
                        <h2>
                            Result :
                            <field name="final_result"/>
                        </h2>
                    </div>
                    <notebook>
                        <page string="Time and Attendance">
                            <group>
                                <field name="time_attendance_ids" widget="one2many_list" attrs="{'readonly':[('state','=','done')]}">
                                    <tree editable="top" create="false" delete="false">
                                        <field name="seq_number" readonly="1"/>
                                        <field name="item_name" readonly="1"/>
                                        <field name="item_value" readonly="1"/>
                                        <field name="item_score"/>
                                    </tree>
                                </field>
                                <field name="time_attendance_wight" attrs="{'readonly':[('state','=','done')]}"/>
                                <field name="time_attendance_result"/>
                            </group>
                        </page>
                        <page string="Performance">
                            <group>
                                <field name="performance_ids" widget="one2many_list" attrs="{'readonly':[('state','=','done')]}">
                                    <tree editable="top" create="false" delete="false">
                                        <field name="seq_number" readonly="1"/>
                                        <field name="item_name" readonly="1"/>
                                        <field name="item_value" readonly="1"/>
                                        <field name="item_score"/>
                                    </tree>
                                </field>
                                <field name="performance_wight" attrs="{'readonly':[('state','=','done')]}"/>
                                <field name="performance_result"/>
                            </group>
                        </page>
                        <page string="Behaviors">
                            <group>
                                <field name="behaviors_ids" widget="one2many_list" attrs="{'readonly':[('state','=','done')]}">
                                    <tree editable="top" create="false" delete="false">
                                        <field name="seq_number" readonly="1"/>
                                        <field name="item_name" readonly="1"/>
                                        <field name="item_value" readonly="1"/>
                                        <field name="item_score"/>
                                    </tree>
                                </field>
                                <field name="behaviors_wight" attrs="{'readonly':[('state','=','done')]}"/>
                                <field name="behaviors_result"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>


    <record id="hr_assessmint_year_tree" model="ir.ui.view">
        <field name="name">hr.contract.assessment.annual.tree</field>
        <field name="model">hr.contract.assessment.annual</field>
        <field name="arch" type="xml">
            <tree>
                <field name="assessment_name"/>
                <field name="employee_id"/>
                <field name="department_id"/>
                <field name="job_id"/>
                <field name="state"/>
                <field name="final_result"/>
            </tree>
        </field>
    </record>

    <record id="hr_assessment_annual_action" model="ir.actions.act_window">
        <field name="name">تقيم الموظفين السنوي</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">hr.contract.assessment.annual</field>
        <field name="view_mode">tree,form</field>
    </record>

    <menuitem id="hr_assessment_by_employee"
              name="تقييم الموظفين السنوي"
              parent="hr_assessment_section1"
              action="hr_assessment_annual_action"
              sequence="3"/>

</odoo>
