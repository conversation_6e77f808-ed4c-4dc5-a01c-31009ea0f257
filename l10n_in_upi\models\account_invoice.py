# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.
import base64

from odoo import models
from odoo.tools.image import image_data_uri


class AccountMove(models.Model):
    _inherit = "account.move"

    def generate_qr_code(self):
        self.ensure_one()
        if self.company_id.country_code == 'IN' and self.is_sale_document(include_receipts=True):
            payment_url = 'upi://pay?pa=%s&pn=%s&am=%s&tr=%s&tn=%s' % (
                self.company_id.l10n_in_upi_id,
                self.company_id.name,
                self.amount_residual,
                self.payment_reference or self.name,
                ("Payment for %s" % self.name))
            barcode = self.env['ir.actions.report'].barcode(barcode_type="QR", value=payment_url, width=120, height=120)
            return image_data_uri(base64.b64encode(barcode))
        return super().generate_qr_code()
