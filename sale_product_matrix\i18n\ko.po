# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_product_matrix
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: Linkup <<EMAIL>>, 2021\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_product_matrix
#: model:ir.model.fields,field_description:sale_product_matrix.field_product_product__product_add_mode
#: model:ir.model.fields,field_description:sale_product_matrix.field_product_template__product_add_mode
msgid "Add product mode"
msgstr "품목 모드 추가하기"

#. module: sale_product_matrix
#: model:ir.model.fields,help:sale_product_matrix.field_product_product__product_add_mode
#: model:ir.model.fields,help:sale_product_matrix.field_product_template__product_add_mode
msgid ""
"Configurator: choose attribute values to add the matching         product variant to the order.\n"
"Grid: add several variants at once from the grid of attribute values"
msgstr ""
"구성기 : 속성 값을 선택하여 주문에 일치하는 파생 품목을 추가하십시오.\n"
"그리드 : 속성 값 그리드에서 여러 파생 품목을 한 번에 추가"

#. module: sale_product_matrix
#: model:ir.model.fields,field_description:sale_product_matrix.field_sale_order__grid_product_tmpl_id
msgid "Grid Product Tmpl"
msgstr "그리드 품목 양식"

#. module: sale_product_matrix
#: model:ir.model.fields,field_description:sale_product_matrix.field_sale_order__grid_update
msgid "Grid Update"
msgstr "그리드 갱신"

#. module: sale_product_matrix
#: model:ir.model.fields,help:sale_product_matrix.field_sale_order__report_grids
msgid ""
"If set, the matrix of the products configurable by matrix will be shown on "
"the report of the order."
msgstr "설정된 경우 매트릭스로 구성 할 수 있는 품목의 매트릭스가 주문 보고서에 표시됩니다."

#. module: sale_product_matrix
#: model:ir.model.fields,field_description:sale_product_matrix.field_sale_order__grid
msgid "Matrix local storage"
msgstr "매트릭스 로컬 스토리지"

#. module: sale_product_matrix
#: model:ir.model.fields.selection,name:sale_product_matrix.selection__product_template__product_add_mode__matrix
msgid "Order Grid Entry"
msgstr "주문 그리드 항목"

#. module: sale_product_matrix
#: model:ir.model.fields,field_description:sale_product_matrix.field_sale_order__report_grids
msgid "Print Variant Grids"
msgstr "파생 그리드 인쇄"

#. module: sale_product_matrix
#: model:ir.model.fields.selection,name:sale_product_matrix.selection__product_template__product_add_mode__configurator
msgid "Product Configurator"
msgstr "품목 구성기"

#. module: sale_product_matrix
#: model:ir.model,name:sale_product_matrix.model_product_template
msgid "Product Template"
msgstr "품목 양식"

#. module: sale_product_matrix
#: model:ir.model,name:sale_product_matrix.model_sale_order
msgid "Sales Order"
msgstr "판매 주문"

#. module: sale_product_matrix
#: model_terms:ir.ui.view,arch_db:sale_product_matrix.product_template_grid_view_form
msgid "Sales Variant Selection"
msgstr "판매 파생 선택"

#. module: sale_product_matrix
#: model:ir.model.fields,help:sale_product_matrix.field_sale_order__grid_product_tmpl_id
msgid "Technical field for product_matrix functionalities."
msgstr "product_matrix 기능에 대한 기술 필드."

#. module: sale_product_matrix
#: model:ir.model.fields,help:sale_product_matrix.field_sale_order__grid
msgid ""
"Technical local storage of grid. \n"
"If grid_update, will be loaded on the SO. \n"
"If not, represents the matrix to open."
msgstr ""
"그리드의 기술 로컬 스토리지. \n"
"grid_update이면 SO에 로드됩니다. \n"
"그렇지 않은 경우 열어야 할 매트릭스를 나타냅니다."

#. module: sale_product_matrix
#: model:ir.model.fields,help:sale_product_matrix.field_sale_order__grid_update
msgid "Whether the grid field contains a new matrix to apply or not."
msgstr "그리드 필드에 적용할 새 매트릭스가 있는지 여부."

#. module: sale_product_matrix
#: code:addons/sale_product_matrix/models/sale_order.py:0
#, python-format
msgid ""
"You cannot change the quantity of a product present in multiple sale lines."
msgstr "여러 판매 방법에 존재하는 품목의 수량은 변경할 수 없습니다."
