// ------- View with SearchPanel -------
$o-searchpanel-active-bg: rgba(108, 193, 237, 0.3);
$o-searchpanel-p: $o-horizontal-padding;
$o-searchpanel-p-small: $o-horizontal-padding*0.5;
$o-searchpanel-p-tiny: $o-searchpanel-p-small*0.5;

$o-searchpanel-category-default-color: $o-brand-primary;
$o-searchpanel-filter-default-color: #D59244;

.o_component_with_search_panel,
.o_controller_with_searchpanel {
    display: flex;
    align-items: flex-start;

    .o_renderer,
    .o_renderer_with_searchpanel {
        flex: 1 1 100%;
        overflow: auto; // make the renderer and search panel scroll individually
        max-height: 100%;
        position: relative;
    }
}

.o_search_panel {
    flex: 0 0 220px;
    overflow: auto;
    height: 100%;
    padding: $o-searchpanel-p-small $o-searchpanel-p-small $o-searchpanel-p*2 $o-searchpanel-p;
    border-right: 1px solid $gray-300;
    background-color: white;

    .o_search_panel_category .o_search_panel_section_icon {
        color: $o-brand-odoo;
    }

    .o_search_panel_filter .o_search_panel_section_icon {
        color: $o-searchpanel-filter-default-color;
    }

    .o_toggle_fold {
        text-align: center;
        width: 1.5rem;
    }

    .o_search_panel_label {
        align-items: center;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        user-select: none;
        width: 100%;
    }

    .o_search_panel_section_header {
        cursor: default;
        padding: $o-searchpanel-p-small 0;
    }

    .list-group {
        padding-bottom: $o-searchpanel-p-tiny;
    }

    .list-group-item {
        padding: 0;

        .list-group-item {
            padding: 0 0 0 $custom-control-gutter;
            margin-bottom: $o-searchpanel-p-tiny*0.5;
        }

        .o_search_panel_label_title {
            color: $headings-color;
            width: 100%;
            @include o-text-overflow;
        }

        header.active {
            background-color: $o-searchpanel-active-bg;
        }
    }

    .o_search_panel_category_value {
        cursor: pointer;

        header {
            align-items: center;
            display: flex;
            justify-content: space-between;
            padding: 4px 6px 4px 0px;
        }

        .o_search_panel_label.o_with_counters {
            overflow: hidden;
        }

        .o_search_panel_category_value {
            margin-bottom: 0;
            padding-left: $o-searchpanel-p;
            position: relative;

            &:before,
            &:after {
                content: '';
                background: $gray-500;
                margin-left: 4px;
                @include o-position-absolute(0, $left: $o-searchpanel-p-tiny);
                @include size(1px, 100%);
            }

            &:after {
                top: 12px;
                @include size(8px, 1px);
            }

            &:last-child:before {
                height: 12px;
            }
        }
    }

    .o_search_panel_group_header .custom-control {
        width: 100%;
    }

    .o_search_panel_filter_value,
    .o_search_panel_filter_group {
        cursor: pointer;
        padding-bottom: $o-searchpanel-p-small;

        .o_search_panel_label,
        .o_search_panel_label_title {
            padding-right: 6px;
        }
    }

    .o_search_panel_filter_group {

        header {
            display: flex;
        }

        .o_search_panel_label.o_with_counters {
            justify-content: flex-start;
        }
    }
}
