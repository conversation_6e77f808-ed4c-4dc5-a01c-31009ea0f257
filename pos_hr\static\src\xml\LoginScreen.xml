<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="LoginScreen" owl="1">
        <div class="login-overlay">
            <div class="screen-login">
                <div class="login-title"><small>Log in to </small>
                    <t t-esc="shopName" />
                </div>
                <div class="login-body">
                    <span class="login-element">
                        <img class="login-barcode-img"
                             src="/point_of_sale/static/img/barcode.png" />
                        <div class="login-barcode-text">Scan your badge</div>
                    </span>
                    <span class="login-or">or</span>
                    <span class="login-element">
                        <button class="login-button select-employee"
                                t-on-click="selectCashier">Select Cashier</button>
                    </span>
                </div>
            </div>
        </div>
    </t>
</templates>
