# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_event_track
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2016-02-20 10:41+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Spanish (Bolivia) (http://www.transifex.com/odoo/odoo-9/"
"language/es_BO/)\n"
"Language: es_BO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event_count_sponsor
msgid "# Sponsors"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_view
msgid "<b>Date</b><br/>"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Mail</b>:"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Phone</b>:"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Proposed By</b>:"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Speakers Biography</b>:"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Talk Introduction</b>:"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Title</b>:"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_view
msgid ""
"<br/>\n"
"                            <b>Duration</b><br/>"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_view
msgid ""
"<br/>\n"
"                            <b>Location</b><br/>"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda
msgid "<span id=\"search_number\">0</span> Found"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<strong>Lightning Talks</strong>. These are 30 minutes talks on many\n"
"                                    different topics. Most topics are "
"accepted in lightning talks."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_social
msgid "<strong>Participate on Twitter</strong>"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<strong>Regular Talks</strong>. These are standard talks with slides,\n"
"                                    alocated in slots of 60 minutes."
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track7
msgid ""
"A technical explanation of Odoo as a CMS and a eCommerce platform for "
"version 8."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_view
msgid "About The Author"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track19
msgid "Advanced lead management with Odoo: tips and tricks from the fields"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track13
msgid "Advanced reporting with Google Spreadsheets integration."
msgstr ""

#. module: website_event_track
#: code:addons/website_event_track/models/event.py:160
#, python-format
msgid "Agenda"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_filter
msgid "All Tags"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"Allow video and audio recording of their\n"
"                                    presentation, for publishing on our "
"website."
msgstr ""

#. module: website_event_track
#: selection:event.track,state:0
msgid "Announced"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Application"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event_allowed_track_tag_ids
msgid "Available Track Tags"
msgstr ""

#. module: website_event_track
#: model:event.sponsor.type,name:website_event_track.event_sponsor_type1
msgid "Bronze"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Call for Proposals"
msgstr ""

#. module: website_event_track
#: selection:event.track,state:0
msgid "Cancelled"
msgstr "Cancelado"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_from_event
msgid "Click to add a track."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_color
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_color
msgid "Color Index"
msgstr ""

#. module: website_event_track
#: selection:event.track,state:0
msgid "Confirmed"
msgstr "Confirmado"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type_create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location_create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type_create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location_create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_create_date
msgid "Created on"
msgstr "Creado en"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Date"
msgstr "Fecha"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "Delete"
msgstr "Eliminar"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track6
msgid ""
"Detailed roadmap of accounting new modules and improvements for version 8."
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track8
msgid ""
"Discover Odoo CRM: How to optimize your sales, from leads to sales orders."
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track11
msgid "Discover Odoo Point-of-Sale: Your shop ready to use in 30 minutes."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type_display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location_display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_display_name
msgid "Display Name"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_view
msgid "Documents"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_duration
msgid "Duration"
msgstr "Duración"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "Edit Track"
msgstr ""

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_event
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_event_id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_event_id
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Event"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_location_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_location_tree
msgid "Event Location"
msgstr ""

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track_location
msgid "Event Locations"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_sponsor_type_tree
msgid "Event Sponsor Type"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_sponsor_type_form
msgid "Event Sponsor Types"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_sponsor_search
msgid "Event Sponsors"
msgstr ""

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_tree
msgid "Event Track"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_tag_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_tag_tree
msgid "Event Track Tag"
msgstr ""

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_sponsor_from_event
#: model:ir.actions.act_window,name:website_event_track.action_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track_from_event
#: model:ir.ui.menu,name:website_event_track.menu_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_calendar
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Event Tracks"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Fill this form to propose your talk."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda
msgid "Filter Tracks..."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_social
msgid ""
"Find out what people see and say about this event, \n"
"                        and join the conversation."
msgstr ""

#. module: website_event_track
#: model:event.sponsor.type,name:website_event_track.event_sponsor_type3
msgid "Gold"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Group By"
msgstr "Agrupar por"

#. module: website_event_track
#: selection:event.track,priority:0
msgid "High"
msgstr ""

#. module: website_event_track
#: selection:event.track,priority:0
msgid "Highest"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track18
msgid ""
"How to build your marketing strategy for the purpose of generating leads "
"with Odoo."
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track1
msgid "How to develop a website module."
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track4
msgid "How to develop automated tests in the Odoo web client."
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track3
msgid "How to develop real time apps, the website live chat module explained."
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track2
msgid "How to integrate hardware materials with the Odoo point of sale."
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track9
msgid ""
"How to use Odoo for your HR process: recruitment, leaves management, "
"appraisals, expenses, etc."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_id
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type_id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location_id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_id
msgid "ID"
msgstr "ID"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_image
msgid "Image"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Introduction"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track23
msgid "Key Success factors selling Odoo."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor___last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type___last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track___last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location___last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag___last_update
msgid "Last Modified on"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type_write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location_write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type_write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location_write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: website_event_track
#: model:ir.ui.menu,name:website_event_track.menu_event_track_location
msgid "Locations"
msgstr "Ubicaciones"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_image_medium
msgid "Logo"
msgstr ""

#. module: website_event_track
#: selection:event.track,priority:0
msgid "Low"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track31
msgid "Lunch"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track22
msgid "Manage your KPIs (recomended to openERP partners)."
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track12
msgid "Manage your events with Odoo, the new training modules."
msgstr ""

#. module: website_event_track
#: selection:event.track,priority:0
msgid "Medium"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_sponsor_image_medium
#: model:ir.model.fields,help:website_event_track.field_event_track_image
msgid ""
"Medium-sized image of this contact. It is automatically resized as a "
"128x128px image, with aspect ratio preserved. Use this field in form views "
"or some kanban views."
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track25
msgid "Merge proposals review, code sprint (entire afternoon)"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track24
msgid "Merge proposals review, code sprint (entire day)."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "More"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track30
msgid "Morning break"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track20
msgid "New Certification Program (valid from Oct. 2013)."
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track14
msgid "New Paypal modules (portal, handling, installments)."
msgstr ""

#. module: website_event_track
#: model:mail.message.subtype,name:website_event_track.mt_event_track
msgid "New Track"
msgstr ""

#. module: website_event_track
#: model:mail.message.subtype,description:website_event_track.mt_event_track
msgid "New Track Created"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "New track proposal"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks
msgid "No tracks found!"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track15
msgid "Odoo Mobile for Notes, Meetings and Messages."
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track28
msgid "Odoo Status & Strategy 2014"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track16
msgid "Odoo as your Enterprise Social Network."
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track27
msgid "Odoo in 2014"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_sponsor
msgid "Our Sponsors"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_partner_biography
msgid "Partner Biography"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_partner_email
msgid "Partner Email"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_partner_name
msgid "Partner Name"
msgstr "Nombre de la empresa"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_partner_phone
msgid "Partner Phone"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_view
msgid "Practical Info"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_priority
msgid "Priority"
msgstr "Prioridad"

#. module: website_event_track
#: selection:event.track,state:0
msgid "Proposal"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Proposals are closed!"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_partner_id
#: model_terms:ir.ui.view,arch_db:website_event_track.track_view
msgid "Proposed by"
msgstr ""

#. module: website_event_track
#: selection:event.track,state:0
msgid "Published"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_view
msgid ""
"Put here the list of documents, like slides of\n"
"                            the presentations. Remove the above t-if when\n"
"                            it's implemented."
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track10
msgid "Raising qualitive insights with the survey app"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track21
msgid "Recruiting high skilled talents with Odoo HR apps"
msgstr ""

#. module: website_event_track
#: selection:event.track,state:0
msgid "Refused"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_user_id
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Responsible"
msgstr "Responsable"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location_id_8478
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location_name
msgid "Room"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_sequence
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type_sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event_show_tracks
msgid "Show Tracks on Website"
msgstr ""

#. module: website_event_track
#: model:event.sponsor.type,name:website_event_track.event_sponsor_type2
msgid "Silver"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_social
msgid "Social Stream"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Speaker Biography"
msgstr ""

#. module: website_event_track
#: code:addons/website_event_track/models/event.py:116
#: model:ir.model.fields,field_description:website_event_track.field_event_track_speaker_ids
#, python-format
msgid "Speakers"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_type_name
msgid "Sponsor Type"
msgstr ""

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_sponsor_type
#: model:ir.ui.menu,name:website_event_track.menu_event_sponsor_type
msgid "Sponsor Types"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_url
msgid "Sponsor Website"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_partner_id
msgid "Sponsor/Customer"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_sponsor_sponsor_type_id
msgid "Sponsoring Type"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event_sponsor_ids
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_form
msgid "Sponsors"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_state
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Status"
msgstr "Estado"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Submission Agreement"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Submit Proposal"
msgstr ""

#. module: website_event_track
#: model:web.tip,description:website_event_track.event_track_tip_1
msgid "Switch to the calendar view to see the availability of each room."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_name
msgid "Tag"
msgstr ""

#. module: website_event_track
#: sql_constraint:event.track.tag:0
msgid "Tag name already exists !"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_ids
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Tags"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Talk Introduction"
msgstr ""

#. module: website_event_track
#: code:addons/website_event_track/models/event.py:162
#, python-format
msgid "Talk Proposals"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Talk Title"
msgstr ""

#. module: website_event_track
#: code:addons/website_event_track/models/event.py:159
#, python-format
msgid "Talks"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Talks Types"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_success
msgid "Thank you for your proposal."
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track17
msgid "The Art of Making an Odoo Demo."
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track29
msgid "The new marketing strategy."
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track5
msgid ""
"The new way to promote your modules in the Apps platform and Odoo website."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "This event does not accept proposals."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"Timely release of presentation material (slides),\n"
"                                    for publishing on our website."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_name
msgid "Title"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Track"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_date
msgid "Track Date"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_description
msgid "Track Description"
msgstr ""

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_location
msgid "Track Location"
msgstr ""

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_tag
msgid "Track Tag"
msgstr ""

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track_tag
#: model:ir.model.fields,field_description:website_event_track.field_event_event_tracks_tag_ids
#: model:ir.ui.menu,name:website_event_track.menu_event_track_tag
msgid "Track Tags"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event_count_tracks
#: model:ir.model.fields,field_description:website_event_track.field_event_event_track_ids
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_track_ids
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_graph
msgid "Tracks"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event_show_track_proposal
msgid "Tracks Proposals"
msgstr ""

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_from_event
msgid ""
"Tracks define the agenda of your event. These can be a talk, a round table, "
"a meeting, etc."
msgstr ""

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track
msgid ""
"Tracks define the agenda of your event. These can bea talk, a round table, a "
"meeting, etc."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Unread Messages"
msgstr "Mensajes sin leer"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_social
msgid "Use this tag:"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "View Track"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "We require speakers to accept an agreement in which they commit to:"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"We will accept a broad range of\n"
"                                presentations, from reports on academic and\n"
"                                commercial projects to tutorials and case\n"
"                                studies. As long as the presentation is\n"
"                                interesting and potentially useful to the\n"
"                                audience, it will be considered for\n"
"                                inclusion in the programme."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_success
msgid "We will evaluate your proposition and get back to you shortly."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Your Email"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Your Name"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Your Phone"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "e.g. Inspiring Business Talk"
msgstr ""

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_sponsor
msgid "event.sponsor"
msgstr ""

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_sponsor_type
msgid "event.sponsor.type"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "hours"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks
msgid "not published"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda
msgid "talks"
msgstr ""

#~ msgid "Date of the last message posted on the record."
#~ msgstr "Fecha del último mensaje publicado en el registro."

#~ msgid "Followers"
#~ msgstr "Seguidores"

#~ msgid "If checked new messages require your attention."
#~ msgstr "Si está marcado, hay nuevos mensajes que requieren su atención"

#~ msgid "Last Message Date"
#~ msgstr "Fecha del último mensaje"

#~ msgid "Messages"
#~ msgstr "Mensajes"

#~ msgid "Messages and communication history"
#~ msgstr "Mensajes e historial de comunicación"
