# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sales_team
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON>ca <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# Arnau <PERSON>, 2021
# <PERSON><PERSON>, 2021
# jabe<PERSON><PERSON>, 2021
# marc<PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: marc<PERSON>, 2022\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_kanban_dashboard
msgid "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"
msgstr "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_kanban
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "<i class=\"fa fa-envelope mr-1\" role=\"img\" aria-label=\"Email\" title=\"Email\"/>"
msgstr "<i class=\"fa fa-envelope mr-1\" role=\"img\" aria-label=\"Email\" title=\"Email\"/>"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_kanban
msgid "<span class=\"bg-error\">Archived</span>"
msgstr "<span class=\"bg-error\">Arxivat</span>"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_kanban_dashboard
msgid "<span>New</span>"
msgstr "<span>Nou</span>"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_kanban_dashboard
msgid "<span>Reporting</span>"
msgstr "<span>Informes</span>"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_kanban_dashboard
msgid "<span>View</span>"
msgstr "<span>Vista</span>"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_needaction
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_needaction
msgid "Action Needed"
msgstr "Cal fer alguna acció"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__active
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__active
msgid "Active"
msgstr "Actiu"

#. module: sales_team
#: model:ir.actions.act_window,name:sales_team.mail_activity_type_action_config_sales
msgid "Activity Types"
msgstr "Tipus d'activitats"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__crm_team_member_ids
msgid ""
"Add members to automatically assign their documents to this sales team."
msgstr ""
"Afegeix membres per assignar automàticament els seus documents a aquest "
"equip de vendes."

#. module: sales_team
#: code:addons/sales_team/models/crm_team.py:0
#, python-format
msgid ""
"Adding %(user_name)s in this team would remove him/her from its current team"
" %(team_name)s."
msgstr ""
"Afegir%(user_name)s en aquest equip l'eliminaria del seu equip "
"actual%(team_name)s."

#. module: sales_team
#: code:addons/sales_team/models/crm_team_member.py:0
#, python-format
msgid ""
"Adding %(user_name)s in this team would remove him/her from its current "
"teams %(team_names)s."
msgstr ""
"Afegir %(user_name)s en aquest equip l'eliminaria dels seus equips "
"actuals%(team_names)s."

#. module: sales_team
#: code:addons/sales_team/models/crm_team.py:0
#, python-format
msgid ""
"Adding %(user_names)s in this team would remove them from their current "
"teams (%(team_names)s)."
msgstr ""
"Afegir %(user_names)s en aquest equip els eliminarien dels seus equips "
"actuals (%(team_names)s)."

#. module: sales_team
#: model:res.groups,name:sales_team.group_sale_manager
msgid "Administrator"
msgstr "Administrador"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_form
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_search
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_search
msgid "Archived"
msgstr "Arxivat"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_attachment_count
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_attachment_count
msgid "Attachment Count"
msgstr "Nombre d'adjunts"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_kanban
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "Avatar"
msgstr "Avatar"

#. module: sales_team
#: code:addons/sales_team/models/crm_team.py:0
#, python-format
msgid "Big Pretty Button :)"
msgstr "Gran botó bonic :)"

#. module: sales_team
#: model:ir.model,name:sales_team.model_crm_tag
msgid "CRM Tag"
msgstr "Etiqueta CRM"

#. module: sales_team
#: code:addons/sales_team/models/crm_team.py:0
#, python-format
msgid "Cannot delete default team \"%s\""
msgstr "No es pot suprimir l'equip per defecte \"%s\""

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__color
msgid "Color"
msgstr "Color"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__color
msgid "Color Index"
msgstr "Índex de color"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__company_id
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__company_id
msgid "Company"
msgstr "Empresa"

#. module: sales_team
#: model:ir.ui.menu,name:sales_team.menu_sale_config
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_kanban_dashboard
msgid "Configuration"
msgstr "Configuració"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor7
msgid "Consulting"
msgstr "Consultoria"

#. module: sales_team
#: model:ir.model,name:sales_team.model_res_partner
msgid "Contact"
msgstr "Contacte"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.sales_team_crm_tag_action
msgid "Create CRM Tags"
msgstr "Crea etiquetes CRM"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_action_config
msgid "Create a Sales Team"
msgstr "Crea un equip de vendes"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_member_action
msgid "Create a new salesman"
msgstr "Crea un nou venedor"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.mail_activity_type_action_config_sales
msgid "Create an Activity Type"
msgstr "Crea un tipus d'activitat"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__create_uid
#: model:ir.model.fields,field_description:sales_team.field_crm_team__create_uid
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__create_uid
msgid "Created by"
msgstr "Creat per"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__create_date
#: model:ir.model.fields,field_description:sales_team.field_crm_team__create_date
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__create_date
msgid "Created on"
msgstr "Creat el"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__currency_id
msgid "Currency"
msgstr "Divisa"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__dashboard_button_name
msgid "Dashboard Button"
msgstr "Botó del tauler de control"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__dashboard_graph_data
msgid "Dashboard Graph Data"
msgstr "Dades del gràfic del tauler de control"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_action_pipeline
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_action_sales
msgid "Define a new sales team"
msgstr "Defineix un equip de vendes nou"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor5
msgid "Design"
msgstr "Disseny"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__display_name
#: model:ir.model.fields,field_description:sales_team.field_crm_team__display_name
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__display_name
msgid "Display Name"
msgstr "Nom a mostrar"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__email
msgid "Email"
msgstr "Correu electrònic"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__favorite_user_ids
msgid "Favorite Members"
msgstr "Membres preferits"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__is_favorite
msgid ""
"Favorite teams to display them in the dashboard and access them easily."
msgstr ""
"Els equips preferits per mostrar-los al tauler de control i accedir-hi "
"fàcilment."

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid ""
"Follow this salesteam to automatically track the events associated to users "
"of this team."
msgstr ""
"Seguiu aquest equip de vendes per seguir automàticament els esdeveniments "
"associats als usuaris d'aquest equip."

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_follower_ids
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_follower_ids
msgid "Followers"
msgstr "Seguidors"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_partner_ids
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidors (Partners)"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_search
msgid "Group By"
msgstr "Agrupar per"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_search
msgid "Group By..."
msgstr "Agrupa per..."

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__has_message
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__has_message
msgid "Has Message"
msgstr "Té un missatge"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__id
#: model:ir.model.fields,field_description:sales_team.field_crm_team__id
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__id
msgid "ID"
msgstr "ID"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__is_membership_multi
#: model:ir.model.fields,help:sales_team.field_crm_team_member__is_membership_multi
msgid ""
"If True, users may belong to several sales teams. Otherwise membership is "
"limited to a single sales team."
msgstr ""
"Si és Veritable, els usuaris poden pertànyer a diversos equips de vendes. En"
" cas contrari, la pertinença es limita a un sol equip de vendes."

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__message_needaction
#: model:ir.model.fields,help:sales_team.field_crm_team__message_unread
#: model:ir.model.fields,help:sales_team.field_crm_team_member__message_needaction
#: model:ir.model.fields,help:sales_team.field_crm_team_member__message_unread
msgid "If checked, new messages require your attention."
msgstr ""
"Si està marcat, hi ha nous missatges que requereixen la vostra atenció."

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__message_has_error
#: model:ir.model.fields,help:sales_team.field_crm_team_member__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Si està marcat, alguns missatges tenen un error d'entrega."

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_res_partner__team_id
#: model:ir.model.fields,help:sales_team.field_res_users__team_id
msgid ""
"If set, this Sales Team will be used for sales and assignments related to "
"this partner"
msgstr ""
"Si està establert, aquest equip de vendes s'utilitzarà per a les vendes i "
"assignacions relacionades amb aquest soci"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__active
msgid ""
"If the active field is set to false, it will allow you to hide the Sales "
"Team without removing it."
msgstr ""
"Si el camp actiu està establert a fals, us permetrà amagar l'equip de vendes"
" sense eliminar-lo."

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__image_1920
msgid "Image"
msgstr "Imatge"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__image_128
msgid "Image (128)"
msgstr "Imatge (128)"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor4
msgid "Information"
msgstr "Informació"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_is_follower
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_is_follower
msgid "Is Follower"
msgstr "És seguidor"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag____last_update
#: model:ir.model.fields,field_description:sales_team.field_crm_team____last_update
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member____last_update
msgid "Last Modified on"
msgstr "Última modificació el "

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__write_uid
#: model:ir.model.fields,field_description:sales_team.field_crm_team__write_uid
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__write_uid
msgid "Last Updated by"
msgstr "Última actualització per"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__write_date
#: model:ir.model.fields,field_description:sales_team.field_crm_team__write_date
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__write_date
msgid "Last Updated on"
msgstr "Última actualització el"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_member_action
msgid ""
"Link salespersons to sales teams. Set their monthly lead capacity\n"
"                and configure automatic lead assignment."
msgstr ""
"Enllaça els venedors amb els equips de vendes. Estableix la seva capacitat de lideratge mensual\n"
"                i configurar l'assignació automàtica de l'inici."

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_main_attachment_id
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_main_attachment_id
msgid "Main Attachment"
msgstr "Adjunt principal"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_res_users__sale_team_id
msgid ""
"Main user sales team. Used notably for pipeline, or to set sales team in "
"invoicing or subscription."
msgstr ""
"Usuari principal de l'equip de vendes. S'utilitza especialment per al "
"pipeline, o per a establir l'equip de vendes en la facturació o la "
"subscripció."

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__member_company_ids
msgid "Member Company"
msgstr "Empresa membre"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__member_warning
msgid "Member Warning"
msgstr "Avís membre"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "Members"
msgstr "Membres"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__member_warning
msgid "Membership Issue Warning"
msgstr "Advertiment sobre el nombre de membres"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_has_error
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_has_error
msgid "Message Delivery error"
msgstr "Error d'entrega del missatge"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_ids
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_ids
msgid "Messages"
msgstr "Missatges"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__mobile
msgid "Mobile"
msgstr "Mòbil"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__is_membership_multi
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__is_membership_multi
msgid "Multiple Memberships Allowed"
msgstr "Múltiples afiliacions permeses"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__name
msgid "Name"
msgstr "Nom"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_needaction_counter
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'accions"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_has_error_counter
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'errors"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__message_needaction_counter
#: model:ir.model.fields,help:sales_team.field_crm_team_member__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Nombre de missatges que requereixen una acció"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__message_has_error_counter
#: model:ir.model.fields,help:sales_team.field_crm_team_member__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de missatges amb error d'entrega"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__message_unread_counter
#: model:ir.model.fields,help:sales_team.field_crm_team_member__message_unread_counter
msgid "Number of unread messages"
msgstr "Nombre de missatges no llegits"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor8
msgid "Other"
msgstr "Altres"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__phone
msgid "Phone"
msgstr "Telèfon"

#. module: sales_team
#: model:crm.team,name:sales_team.pos_sales_team
msgid "Point of Sale"
msgstr "Punt de Venda"

#. module: sales_team
#: model:crm.team,name:sales_team.crm_team_1
msgid "Pre-Sales"
msgstr "Prevendes"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor1
msgid "Product"
msgstr "Producte"

#. module: sales_team
#: model:crm.team,name:sales_team.team_sales_department
msgid "Sales"
msgstr "Vendes"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_form
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_tree
msgid "Sales Men"
msgstr "Home de vendes"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_search
msgid "Sales Person"
msgstr "Persona de venda"

#. module: sales_team
#: model:ir.model,name:sales_team.model_crm_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__name
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__crm_team_id
#: model:ir.model.fields,field_description:sales_team.field_res_partner__team_id
#: model:ir.model.fields,field_description:sales_team.field_res_users__team_id
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_member_view_search
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_tree
msgid "Sales Team"
msgstr "Equip de vendes"

#. module: sales_team
#: model:ir.model,name:sales_team.model_crm_team_member
msgid "Sales Team Member"
msgstr "Membre de l'equip de vendes"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__crm_team_member_ids
#: model:ir.model.fields,field_description:sales_team.field_res_users__crm_team_member_ids
msgid "Sales Team Members"
msgstr "Membres de l'equip de vendes"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__crm_team_member_all_ids
msgid "Sales Team Members (incl. inactive)"
msgstr "Membres de l'equip de vendes (incl. inactiu)"

#. module: sales_team
#: model:ir.actions.act_window,name:sales_team.crm_team_action_config
#: model:ir.actions.act_window,name:sales_team.crm_team_action_sales
#: model:ir.model.fields,field_description:sales_team.field_res_users__crm_team_ids
msgid "Sales Teams"
msgstr "Equips de vendes"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__user_id
msgid "Salesperson"
msgstr "Comercial"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__member_ids
msgid "Salespersons"
msgstr "Comercials"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_search
msgid "Salesteams Search"
msgstr "Cerca d'equips de vendes"

#. module: sales_team
#: code:addons/sales_team/models/crm_team.py:0
#, python-format
msgid "Sample data"
msgstr "Dades d'exemple"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__sequence
msgid "Sequence"
msgstr "Seqüència"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor3
msgid "Services"
msgstr "Serveis"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__is_favorite
msgid "Show on dashboard"
msgstr "Mostra al tauler de control"

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor2
msgid "Software"
msgstr "Software"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_tag__name
msgid "Tag Name"
msgstr "Nom de l'etiqueta"

#. module: sales_team
#: model:ir.model.constraint,message:sales_team.constraint_crm_tag_name_uniq
msgid "Tag name already exists !"
msgstr "Nom d'etiqueta ja existeix!"

#. module: sales_team
#: model:ir.actions.act_window,name:sales_team.sales_team_crm_tag_action
#: model_terms:ir.ui.view,arch_db:sales_team.sales_team_crm_tag_view_form
#: model_terms:ir.ui.view,arch_db:sales_team.sales_team_crm_tag_view_tree
msgid "Tags"
msgstr "Etiquetes"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "Team Details"
msgstr "Detalls de l'equip"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__user_id
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_search
msgid "Team Leader"
msgstr "Líder de l'equip"

#. module: sales_team
#: model:ir.actions.act_window,name:sales_team.crm_team_member_action
msgid "Team Members"
msgstr "Membres de l'equip"

#. module: sales_team
#: model:ir.actions.act_window,name:sales_team.crm_team_action_pipeline
msgid "Teams"
msgstr "Equips"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__color
msgid "The color of the channel"
msgstr "El color del canal"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team_member__company_id
msgid "The default company for this user."
msgstr "L'empresa per defecte d'aquest usuari."

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.mail_activity_type_action_config_sales
msgid ""
"Those represent the different categories of things you have to do (e.g. "
"\"Call\" or \"Prepare meeting\")."
msgstr ""
"Aquestes representen les diferents categories de coses que heu de fer (p. "
"ex. \"Call\" o \"Preparar reunió\")."

#. module: sales_team
#: code:addons/sales_team/models/crm_team.py:0
#, python-format
msgid ""
"To add a Salesperson into multiple Teams, activate the Multi-Team option in "
"settings."
msgstr ""
"Per afegir un venedor en diversos equips, activeu l'opció Multi-Team a la "
"configuració."

#. module: sales_team
#: model:crm.tag,name:sales_team.categ_oppor6
msgid "Training"
msgstr "Formació"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team_member__user_in_teams_ids
msgid ""
"UX: Give users not to add in the currently chosen team to avoid duplicates"
msgstr ""
"UX: Doneu als usuaris que no afegeixin a l'equip actualment seleccionat per "
"evitar duplicats"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__member_company_ids
#: model:ir.model.fields,help:sales_team.field_crm_team_member__user_company_ids
msgid "UX: Limit to team company or all if no company"
msgstr "team: Limitar a l'empresa d'equip o a tot si no és empresa"

#. module: sales_team
#: code:addons/sales_team/models/crm_team.py:0
#: code:addons/sales_team/models/crm_team.py:0
#, python-format
msgid "Undefined graph model for Sales Team: %s"
msgstr "Model de gràfic indefinit per a l'equip de vendes: %s"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_unread
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_unread
msgid "Unread Messages"
msgstr "Missatges pendents de llegir"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team__message_unread_counter
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Comptador de missatges no llegits"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_action_config
msgid ""
"Use Sales Teams to organize your sales departments and draw up reports."
msgstr ""
"Utilitzeu els equips de vendes per organitzar els vostres departaments de "
"vendes i elaborar informes."

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_action_pipeline
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_action_sales
msgid ""
"Use Sales Teams to organize your sales departments.\n"
"                Each team will work with a separate pipeline."
msgstr ""
"Utilitzeu els equips de vendes per organitzar els vostres departaments de vendes.\n"
"                Cada equip treballarà amb un conducte separat."

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.sales_team_crm_tag_action
msgid ""
"Use Tags to manage and track your Opportunities (product structure, sales "
"type, ...)"
msgstr ""
"Utilitzi etiquetes per a gestionar i fer un seguiment de les seves "
"oportunitats (estructura del producte, tipus de venda, ...)"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__user_company_ids
msgid "User Company"
msgstr "Empresa d'usuari"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member__user_in_teams_ids
msgid "User In Teams"
msgstr "Usuari en equips"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_res_users__sale_team_id
msgid "User Sales Team"
msgstr "Equip de vendes de l'usuari"

#. module: sales_team
#: model:res.groups,name:sales_team.group_sale_salesman_all_leads
msgid "User: All Documents"
msgstr "Usuari: Tots els documents"

#. module: sales_team
#: model:res.groups,name:sales_team.group_sale_salesman
msgid "User: Own Documents Only"
msgstr "Usuari: només mostrar documents propis"

#. module: sales_team
#: model:ir.model,name:sales_team.model_res_users
msgid "Users"
msgstr "Usuaris"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team__member_ids
msgid "Users assigned to this team."
msgstr "Usuaris assignats a aquest equip."

#. module: sales_team
#: model:crm.team,name:sales_team.salesteam_website_sales
msgid "Website"
msgstr "Lloc web"

#. module: sales_team
#: code:addons/sales_team/models/crm_team_member.py:0
#, python-format
msgid ""
"You are trying to create duplicate membership(s). We found that "
"%(duplicates)s already exist(s)."
msgstr ""
"Esteu intentant crear membres duplicats. Hem trobat %(duplicates)s ja "
"existei(x)."

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "e.g. North America"
msgstr "p. ex. Amèrica del Nord"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.sales_team_crm_tag_view_form
msgid "e.g. Services"
msgstr "p. ex. Serveis"

#. module: sales_team
#: model:crm.team,name:sales_team.ebay_sales_team
msgid "eBay"
msgstr "eBay"

#. module: sales_team
#: model:res.groups,comment:sales_team.group_sale_salesman_all_leads
msgid ""
"the user will have access to all records of everyone in the sales "
"application."
msgstr ""
"l'usuari tindrà accés a tots els registres de tots de l'aplicació de vendes."

#. module: sales_team
#: model:res.groups,comment:sales_team.group_sale_salesman
msgid "the user will have access to his own data in the sales application."
msgstr ""
"l'usuari tindrà accés a la configuració de vendes així com als informes "
"estadístics."

#. module: sales_team
#: model:res.groups,comment:sales_team.group_sale_manager
msgid ""
"the user will have an access to the sales configuration as well as statistic"
" reports."
msgstr ""
"L'usuari tindrà accés a la configuració de vendes, així com als informes "
"estadístics."
