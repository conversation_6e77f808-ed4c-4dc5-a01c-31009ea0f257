<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_ae" model="res.partner">
        <field name="name">AE Company</field>
        <field name="vat"></field>
        <field name="street">A شارع ملقا</field>
        <field name="city">الشارقة</field>
        <field name="country_id" ref="base.ae"/>
        <field name="state_id" ref="base.state_ae_uq"/>
        <field name="zip">81730</field>
        <field name="phone">+971 50 123 4567</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.aeexample.com</field>
    </record>

    <record id="demo_company_ae" model="res.company">
        <field name="name">AE Company</field>
        <field name="partner_id" ref="partner_demo_company_ae"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_ae')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('demo_company_ae'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_ae.uae_chart_template_standard')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_ae.demo_company_ae')"/>
    </function>
</odoo>
