# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_quotation_builder
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> (Quartile) <tashi<PERSON>@roomsfor.hk>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# Junko <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: Junko Augias, 2023\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid ""
":\n"
"                                        this content will appear on the quotation only if this\n"
"                                        product is put on the quote."
msgstr ""
":\n"
"                                        この内容が見積書に表示されるのは\n"
"この\n"
"　　　　　　　　　　  プロダクトが見積書に記載されている場合のみです。"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid ""
":\n"
"                                        this content will appear on the quotation only if this\n"
"                                        product is used in the quote."
msgstr ""
":\n"
"                                        この内容が見積書に表示されるのは、この\n"
"　　　　　　　　　　  プロダクトが見積書に使用された場合のみです。"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.sale_order_portal_content_inherit_sale_quotation_builder
msgid ""
":\n"
"                        the content below will disappear if this\n"
"                        product is removed from the quote."
msgstr ""
":\n"
"                        このプロダクトが見積もりから削除された場合、\n"
"                        以下の内容は表示されなくなります。"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid ""
"<strong>Template Header:</strong> this content\n"
"                                    will appear on all quotations using this\n"
"                                    template."
msgstr ""
"<strong>テンプレートヘッダー：</strong> この内容は\n"
"                                    このテンプレートを使用した全ての見積書\n"
"                                    に表示されます。"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid "About us"
msgstr "会社概要"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.brand_promotion
msgid "An awesome"
msgstr "素晴らしい"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"As a leading professional services firm,\n"
"                                we know that success is all about the\n"
"                                commitment we put on strong services."
msgstr ""
"一流のプロフェッショナル・サービス企業として\n"
"　　　　　　　　私たちは、精力的なサービスにコミットする\n"
"　　　　　　　　ことが成功の鍵だと理解しています。"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.sale_order_portal_content_inherit_sale_quotation_builder
msgid "Close"
msgstr "閉じる"

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_res_company
msgid "Companies"
msgstr "会社"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.sale_order_template_view_form_inherit_sale_quotation_builder
msgid "Design Template"
msgstr "デザインテンプレート"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"Great quotation templates will significantly\n"
"                                <strong>boost your success rate</strong>. The\n"
"                                first section is usually about your company,\n"
"                                your references, your methodology or\n"
"                                guarantees, your team, SLA, terms and conditions, etc."
msgstr ""
"偉大な見積テンプレートは、\n"
"                               <strong>成功率を大幅に向上させる</strong>でしょう。\n"
"                                最初のセクションは通常、あなたの会社、参考資料、\n"
"                                方法論や保証、チーム、SLA、利用規約などです。"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"If you edit a quotation from the 'Preview' of a quotation, you will\n"
"                        update that quotation only. If you edit the quotation\n"
"                        template (from the Configuration menu), all future quotations will\n"
"                        use this modified template."
msgstr ""
"見積書の「プレビュー」から見積書を編集すると、その見積書のみが\n"
"　　　　　更新されます。見積書テンプレートを（設定メニューから）\n"
"　　　　　編集した場合、今後作成される全ての見積書はこの編集された\n"
"テンプレートを使用します。"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.brand_promotion
msgid "Open Source CRM"
msgstr "オープンソースCRM"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid "Optional Product:"
msgstr "オプションプロダクト:"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid "Our Offer"
msgstr "私たちのオファー"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid "Our Quality"
msgstr "私たちの品質"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid "Our Service"
msgstr "私たちのサービス"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid "Price"
msgstr "価格"

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_product_template
msgid "Product Template"
msgstr "プロダクトテンプレート"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"Product quality is the foundation we\n"
"                                stand on; we build it with a relentless\n"
"                                focus on fabric, performance and craftsmanship."
msgstr ""
"当社の土台は、プロダクトの品質です。\n"
"　　　　　　　　生地と性能、そして職人技に徹底的にこだわることで\n"
"　　　　　　　　土台を築き上げてきました。"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.sale_order_portal_content_inherit_sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid "Product:"
msgstr "プロダクト:"

#. module: sale_quotation_builder
#: model:ir.model.fields,field_description:sale_quotation_builder.field_product_product__quotation_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_product_template__quotation_description
msgid "Quotation Description"
msgstr "見積書説明"

#. module: sale_quotation_builder
#: model:ir.model.fields,field_description:sale_quotation_builder.field_product_product__quotation_only_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_product_template__quotation_only_description
msgid "Quotation Only Description"
msgstr "見積書説明のみ"

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_sale_order_template
msgid "Quotation Template"
msgstr "見積テンプレート"

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_sale_order_template_line
msgid "Quotation Template Line"
msgstr "見積書テンプレート明細"

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_sale_order_template_option
msgid "Quotation Template Option"
msgstr "見積書テンプレートオプション"

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_sale_order_option
msgid "Sale Options"
msgstr "販売オプション"

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_sale_order
msgid "Sales Order"
msgstr "販売オーダ"

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_sale_order_line
msgid "Sales Order Line"
msgstr "販売オーダ明細"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid "Terms &amp; Conditions"
msgstr "諸条件"

#. module: sale_quotation_builder
#: model:ir.model.fields,help:sale_quotation_builder.field_product_product__quotation_only_description
#: model:ir.model.fields,help:sale_quotation_builder.field_product_template__quotation_only_description
#: model:ir.model.fields,help:sale_quotation_builder.field_sale_order_template_line__website_description
msgid "The quotation description (not used on eCommerce)"
msgstr "見積書説明（eコマースでは使用しない）"

#. module: sale_quotation_builder
#: model:ir.model.fields,help:sale_quotation_builder.field_product_product__quotation_description
#: model:ir.model.fields,help:sale_quotation_builder.field_product_template__quotation_description
msgid ""
"This field uses the Quotation Only Description if it is defined, otherwise "
"it will try to read the eCommerce Description."
msgstr "このフィールドは、見積書のみの説明が定義されている場合はそれを使用し、そうでない場合はeコマースの説明を読み取ろうとします。"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"This is a <strong>sample quotation template</strong>. You should\n"
"                                customize it to fit your own needs from the <i>Sales</i>\n"
"                                application, using the menu: Configuration /\n"
"                                Quotation Templates."
msgstr ""
"これは <strong>サンプル見積書テンプレート</strong>です。 ご自分で\n"
"                                必要に応じて<i>販売</i>\n"
"                               アプリから設定メニュー/見積書テンプレート\n"
"　　　　　　　　を使ってカスタマイズして下さい。"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid "This is a preview of the sale order template."
msgstr "これは販売オーダテンプレートのプレビューです。"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid ""
"Titles with style <i>Heading 2</i> and\n"
"                                    <i>Heading 3</i> will be used to generate the\n"
"                                    table of content automatically."
msgstr ""
"スタイリッシュなタイトル <i>見出し2</i> と\n"
"                                    <i>見出し 3</i>が目次の自動生成に\n"
"                                    使用されます。"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"We always ensure that our products are\n"
"                                set at a fair price so that you will be\n"
"                                happy to buy them."
msgstr ""
"お客様に喜んで購入頂けるよう\n"
"                                当社は常にプロダクトの価格を\n"
"                                適正に設定しています。"

#. module: sale_quotation_builder
#: model:ir.model.fields,field_description:sale_quotation_builder.field_sale_order__website_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_sale_order_line__website_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_sale_order_option__website_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_sale_order_template__website_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_sale_order_template_line__website_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_sale_order_template_option__website_description
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.sale_order_template_view_form_inherit_sale_quotation_builder
msgid "Website Description"
msgstr "ウェブサイトの説明"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"You can <strong>set a description per product</strong>. Odoo will\n"
"                        automatically create a quotation using the descriptions\n"
"                        of all products in the proposal. The table of content\n"
"                        on the left is generated automatically using the styles you\n"
"                        used in your description (heading 1, heading 2, ...)"
msgstr ""
"<strong>商品ごとの説明を設定</strong>できます。Odooは、\n"
"                        プロポーザル内のすべての製品の説明を使用して自動的に\n"
"                        見積を作成します。 左側のコンテンツの表は、説明で\n"
"                        使用したスタイルを使用して自動的に生成されます\n"
"                        (見出し1、見出し2、...)"
