# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_sips
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON>Nesselbosch, 2022
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:49+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: payment_sips
#: code:addons/payment_sips/models/payment_transaction.py:0
#, python-format
msgid "Incorrect amount: received %(received).2f, expected %(expected).2f"
msgstr "Importo errato: ricevuto %(received).2f, atteso %(expected).2f"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_version
msgid "Interface Version"
msgstr "Versione interfaccia"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_merchant_id
msgid "Merchant ID"
msgstr "ID commerciante"

#. module: payment_sips
#: code:addons/payment_sips/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "Nessuna transazione trovata corrispondente al riferimento %s."

#. module: payment_sips
#: model:ir.model,name:payment_sips.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Sistema di pagamento"

#. module: payment_sips
#: model:ir.model,name:payment_sips.model_account_payment_method
msgid "Payment Methods"
msgstr "Metodi di pagamento"

#. module: payment_sips
#: model:ir.model,name:payment_sips.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transazione di pagamento"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_prod_url
msgid "Production URL"
msgstr "URL di produzione"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__provider
msgid "Provider"
msgstr "Fornitore"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_secret
msgid "SIPS Secret Key"
msgstr "SIPS Secret Key"

#. module: payment_sips
#: model_terms:ir.ui.view,arch_db:payment_sips.payment_acquirer_form
msgid "Secret Key"
msgstr "Chiave segreta"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_key_version
msgid "Secret Key Version"
msgstr "Versione chiave segreta"

#. module: payment_sips
#: model:account.payment.method,name:payment_sips.payment_method_sips
#: model:ir.model.fields.selection,name:payment_sips.selection__payment_acquirer__provider__sips
msgid "Sips"
msgstr "Sips"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_test_url
msgid "Test URL"
msgstr "Test URL"

#. module: payment_sips
#: model:ir.model.fields,help:payment_sips.field_payment_acquirer__sips_merchant_id
msgid "The ID solely used to identify the merchant account with Sips"
msgstr ""
"L'ID utilizzato esclusivamente per identificare il conto commerciante con "
"Sips"

#. module: payment_sips
#: model:ir.model.fields,help:payment_sips.field_payment_acquirer__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr ""
"Il fornitore di servizi di pagamento da utilizzare con questo acquirente"

#. module: payment_sips
#: code:addons/payment_sips/models/payment_transaction.py:0
#, python-format
msgid "This currency is not supported: %s."
msgstr "Questa valuta non è supportata: %s."

#. module: payment_sips
#: code:addons/payment_sips/models/payment_transaction.py:0
#, python-format
msgid "Unrecognized response received from the payment provider."
msgstr "Risposta non riconosciuta ricevuta dal fornitore di pagamenti."
