# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website
# 
# Translators:
# <PERSON><PERSON> Aura <<EMAIL>>, 2021
# <PERSON><PERSON> <jussi.heikki<PERSON>@panimo.com>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON>, 2021
# <PERSON><PERSON><PERSON> <heikki.kataji<PERSON>@myyntivoima.fi>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# Teemu Tiitinen, 2021
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# LINUX-SAUNA, 2022
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# Martin Trigaux, 2023
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-10 06:07+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: Ossi Mantylahti <<EMAIL>>, 2023\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_image_gallery/options.js:0
#, python-format
msgid " Add Images"
msgstr "Lisää kuvia"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "\" alert with a"
msgstr "\" varoitus jossa"

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL from\" can not be empty."
msgstr "\"URL-osoite\" ei voi olla tyhjä."

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" can not be empty."
msgstr "\"URL kohde\" ei voi olla tyhjä."

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" cannot contain parameter %s which is not used in \"URL from\"."
msgstr ""
"\"URL kohde\" ei voi sisältää parametria %s, jota ei käytetä kohdassa \"URL "
"lähde\"."

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" is invalid: %s"
msgstr "\"URL kohde\" on virheellinen: %s"

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" must contain parameter %s used in \"URL from\"."
msgstr ""
"\"URL kohteen\" pitää sisältää parametri %s, jota käytetään kohdassa \"URL "
"lähde\"."

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" must start with a leading slash."
msgstr "\"URL kohde\" täytyy alkaa kauttaviivalla."

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__page_count
msgid "# Visited Pages"
msgstr "# Vierailtuja sivuja"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__visit_count
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "# Visits"
msgstr "Vierailuja"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$10.50"
msgstr "$10.50"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$12.00"
msgstr "$12.00"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$15.50"
msgstr "$15.50"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$7.50"
msgstr "$7.50"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$9.00"
msgstr "$9.00"

#. module: website
#: code:addons/website/models/website.py:0
#: code:addons/website/models/website.py:0
#, python-format
msgid "%s (id:%s)"
msgstr "%s (id:%s)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "&amp;lt;/body&amp;gt;"
msgstr "&amp;lt;/body&amp;gt;"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "&amp;lt;head&amp;gt;"
msgstr "&amp;lt;head&amp;gt;"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "&gt;"
msgstr "&gt;"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "&nbsp;"
msgstr "&nbsp;"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
msgid "' did not match any pages."
msgstr "' ei vastannut yhtään sivua."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
msgid "' did not match anything."
msgstr "' ei vastannut mitään."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
msgid ""
"' did not match anything.\n"
"                        Results are displayed for '"
msgstr ""
"' ei vastannut mitään.\n"
"                        Tulokset näytetään  '"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "' to link to an anchor."
msgstr "' linkittääksesi ankkuriin."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"' to search a page.\n"
"                    '"
msgstr ""
"' etsiäksesi sivua.\n"
"                    '"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "'%s' is not a correct date"
msgstr "'%s' ei ole kelvollinen päivämäärä"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "'%s' is not a correct datetime"
msgstr "'%s' on virheellinen päivämäärä"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_searchbar/000.xml:0
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
#, python-format
msgid "'. Showing results for '"
msgstr "'. Näytetään tulokset  '"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "(could be used in"
msgstr "(voitaisiin käyttää"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.res_config_settings.xml:0
#, python-format
msgid ""
"(see e.g. Opinion\n"
"            04/2012 on Cookie Consent Exemption by the EU Art.29 WP)."
msgstr ""
"(katso esim. Lausunto\n"
"            04/2012 EU:n evästeiden suostumuspoikkeuksista, 29 artikla)."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "+ Field"
msgstr "+ Kenttä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_header_contact_oe_structure_header_contact_1
#: model_terms:ir.ui.view,arch_db:website.template_header_hamburger_oe_structure_header_hamburger_3
#: model_terms:ir.ui.view,arch_db:website.template_header_sidebar_oe_structure_header_sidebar_1
#: model_terms:ir.ui.view,arch_db:website.template_header_vertical_oe_structure_header_vertical_2
msgid "+1 (650) 555-0111"
msgstr "+1 (650) 555-0111"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid ", .s_searchbar_input"
msgstr ", .s_searchbar_input"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid ", .s_website_form"
msgstr ", .s_website_form"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid ", author:"
msgstr ", tekijä:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
".\n"
"                                                The website will still work if you reject or discard those cookies."
msgstr ""
".\n"
"                                                Verkkosivusto toimii edelleen, jos hylkäät tai ohitat evästeet."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid ""
".\n"
"            Changing its name will break these calls."
msgstr ""
".\n"
"            Nimen muuttaminen rikkoo nämä kutsut."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "...and switch the timeline contents to fit your needs."
msgstr "... ja muuta aikajanan sisältöä tarpeidesi mukaan."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "/contactus"
msgstr "/yhteys"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "1 km"
msgstr "1 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "1/2 - 1/2"
msgstr "1/2 - 1/2"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "1/3 - 2/3"
msgstr "1/3 - 2/3"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "1/4 - 3/4"
msgstr "1/4 - 3/4"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "10 m"
msgstr "10 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "100 km"
msgstr "100 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "100 m"
msgstr "100 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "100%"
msgstr "100%"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "1000 km"
msgstr "1000 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "12"
msgstr "12"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "15 km"
msgstr "15 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "2 <span class=\"sr-only\">(current)</span>"
msgstr "2 <span class=\"sr-only\">(nykyinen)</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "2 km"
msgstr "2 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "2.5 m"
msgstr "2.5 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "20 m"
msgstr "20 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "200 km"
msgstr "200 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "200 m"
msgstr "200 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "2000 km"
msgstr "2000 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "24x7 toll-free support"
msgstr "24x7 maksuton tuki"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "25%"
msgstr "25%"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid ""
"250 Executive Park Blvd, Suite 3400 <br/> San Francisco CA 94134 <br/>United"
" States"
msgstr ""
"250 Executive Park Blvd, Suite 3400 <br/> San Francisco CA 94134 <br/>United"
" States"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
msgid ""
"250 Executive Park Blvd, Suite 3400 • San Francisco CA 94134 • United States"
msgstr ""
"250 Executive Park Blvd, Suite 3400 • San Francisco CA 94134 • United States"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "30 km"
msgstr "30 km"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__301
#, python-format
msgid "301 Moved permanently"
msgstr "301 Siirretty pysyvästi"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__302
#, python-format
msgid "302 Moved temporarily"
msgstr "302 Siirretty väliaikaisesti"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__308
msgid "308 Redirect / Rewrite"
msgstr "308 Uudelleenohjaus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "4 km"
msgstr "4 km"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "4 steps"
msgstr "4 askelta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "400 km"
msgstr "400 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "400 m"
msgstr "400 m"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__404
msgid "404 Not Found"
msgstr "404 Ei löydy"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "5 m"
msgstr "5 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "50 km"
msgstr "50 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "50 m"
msgstr "50 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "50%"
msgstr "50%"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
msgid "50,000+ companies run Odoo to grow their businesses."
msgstr "Yli 50 000 yritystä käyttää Odoota liiketoiminnan kasvattamiseksi."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "75%"
msgstr "75%"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "8 km"
msgstr "8 km"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_progress_bar/options.js:0
#, python-format
msgid "80% Development"
msgstr "80% kehitys"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action
msgid "<b>50,000+ companies</b> run Odoo to grow their businesses."
msgstr "<b>50,000+ yritystä</b> käyttää Odoota kasvattaakseen yritystään."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Add</b> the selected image."
msgstr "<b>Lisää</b> valittu kuva."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click Edit</b> to start designing your homepage."
msgstr "<b>Klikkaa Muokkaa</b> alkaaksesi kotisivun suunnittelun."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click on a snippet</b> to access its options menu."
msgstr "<b>Klikkaa liitännäistä</b> nähdäksesi sen asetusvalikon."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click on a text</b> to start editing it."
msgstr "<b>Klikkaa tekstiä </b> muokataksesi sitä."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click</b> on this column to access its options."
msgstr "<b>Klikkaa</b> tätä saraketta päästäksesi sen asetuksiin."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click</b> on this header to configure it."
msgstr "<b>Klikkaa</b> tätä otsikkoa määrittääksesi sen."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click</b> on this option to change the %s of the block."
msgstr "<b>Klikkaa</b> tätä vaihtoehtoa, jos haluat muuttaa lohkon %s."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid ""
"<b>Customize</b> any block through this menu. Try to change the background "
"color of this block."
msgstr ""
"<b>Mukauta</b> mitä tahansa palikaa tämän menun kautta. Kokeile vaihtaa "
"tämän palikan taustaväriä."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid ""
"<b>Customize</b> any block through this menu. Try to change the background "
"image of this block."
msgstr ""
"<b>Mukauta</b> mitä tahansa palikaa tämän menun kautta. Kokeile vaihtaa "
"tämän palikan taustakuvaa."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
msgid "<b>Designed</b> <br/>for Companies"
msgstr "<b>Suunniteltu</b> <br/>yrityksille"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
msgid "<b>Designed</b> for companies"
msgstr "<b>Suunniteltu</b> yrityksille"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Double click on an icon</b> to change it with one of your choice."
msgstr ""
"<b>Tuplaklikkaa kuvaketta</b> korvataksesi sen valitsemallasi kuvakkeella."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Double click on an image</b> to change it with one of your choice."
msgstr "<b>Tuplaklikkaa kuvaa</b> korvataksesi sen valitsemallasi kuvalla."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
msgid ""
"<b>My Company</b><br/>250 Executive Park Blvd, Suite 3400 <br/> San "
"Francisco CA 94134 <br/>United States"
msgstr ""
"<b>Minun yritys</b><br/>250 Executive Park Blvd, Suite 3400 <br/> San "
"Francisco CA 94134 <br/>United States"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Select</b> a %s."
msgstr "<b>Valitse</b> %s."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Select</b> a Color Palette."
msgstr "<b>Valitse</b> väripaletti."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Slide</b> this button to change the %s padding"
msgstr "<b>Liu'uta</b> tätä nappia vaihtaaksesi %s toppausta"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Slide</b> this button to change the column size."
msgstr "<b>Liu'uta</b> tätä painiketta vaihtaaksesi sarakkeen kokoa."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_edit_robots
msgid ""
"<br/><br/>\n"
"                    Example of rule:<br/>"
msgstr ""
"<br/><br/>\n"
"                    Esimerkki säännöstä:<br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"<font style=\"background-color: rgb(255, 255, 255);\">Good writing is "
"simple, but not simplistic.</font>"
msgstr ""
"<font style=\"background-color: rgb(255, 255, 255);\">Hyvä teksti on "
"yksinkertaista, mutta ei liioitellusti.</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<font style=\"font-size: 14px;\">Created in 2021, the company is young and "
"dynamic. Discover the composition of the team and their skills.</font>"
msgstr ""
"<font style=\"font-size: 14px;\">Vuonna 2021 perustettu yritys on nuori ja "
"dynaaminen. Tutustu tiimin kokoonpanoon ja osaamiseen.</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"<font style=\"font-size: 62px; background-color: rgb(255, 255, 255);\">Edit "
"this title</font>"
msgstr ""
"<font style=\"font-size: 62px; background-color: rgb(255, 255, "
"255);\">Muokkaa tätä otsikkoa</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cover
msgid "<font style=\"font-size: 62px; font-weight: bold;\">Catchy Headline</font>"
msgstr ""
"<font style=\"font-size: 62px; font-weight: bold;\">Mieleenjäävä "
"Otsikko</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_picture
msgid "<font style=\"font-size: 62px;\">A punchy Headline</font>"
msgstr "<font style=\"font-size: 62px;\">Iskevä otsikko</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_banner
msgid "<font style=\"font-size: 62px;\">Sell Online. Easily.</font>"
msgstr "<font style=\"font-size: 62px;\">Myy verkossa. Helposti.</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "<font style=\"font-size: 62px;\">Slide Title</font>"
msgstr "<font style=\"font-size: 62px;\">Dian otsikko</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup
msgid "<font style=\"font-size: 62px;\">Win $20</font>"
msgstr "<font style=\"font-size: 62px;\">Voita 20€</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_title
msgid "<font style=\"font-size: 62px;\">Your Site Title</font>"
msgstr "<font style=\"font-size: 62px;\">Sivun otsikko</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card
msgid "<i class=\"fa fa-1x fa-clock-o mr8\"/><small>2 days ago</small>"
msgstr "<i class=\"fa fa-1x fa-clock-o mr8\"/><small>2 päivää sitten</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_page
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid ""
"<i class=\"fa fa-1x fa-fw fa-envelope "
"mr-2\"/><span><EMAIL></span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
msgid ""
"<i class=\"fa fa-1x fa-fw fa-map-marker mr-2\"/>250 Executive Park Blvd, "
"Suite 3400 • San Francisco CA 94134 • United States"
msgstr ""
"<i class=\"fa fa-1x fa-fw fa-map-marker mr-2\"/>250 Executive Park Blvd, "
"Suite 3400 • San Francisco CA 94134 • Yhdysvallat"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            Create a Google Project and Get a Key"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            Luo Google-projekti ja hanki Avain"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            Enable billing on your Google Project"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            Ota laskutus käyttöön Google-projektissasi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            How to get my Client ID"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            Kuinka saan Client ID:n"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            How to get my Measurement ID"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.report_viewhierarchy_children
msgid "<i class=\"fa fa-eye ml-2 text-muted\" title=\"Go to View\"/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.report_viewhierarchy_children
msgid "<i class=\"fa fa-files-o ml-2 text-muted\" title=\"Show Arch Diff\"/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-circle\"/> Circles"
msgstr "<i class=\"fa fa-fw fa-circle\"/> Ympyrät"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-heart\"/> Hearts"
msgstr "<i class=\"fa fa-fw fa-heart\"/> Sydämet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-refresh mr-1\"/> Replace Icon"
msgstr "<i class=\"fa fa-fw fa-refresh mr-1\"/> Vaihda kuvake"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-square\"/> Squares"
msgstr "<i class=\"fa fa-fw fa-square\"/> Neliöt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-star\"/> Stars"
msgstr "<i class=\"fa fa-fw fa-star\"/> Tähdet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-thumbs-up\"/> Thumbs"
msgstr "<i class=\"fa fa-fw fa-thumbs-up\"/> Peukut"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-circle text-danger\"/>\n"
"                            <span>Offline</span>"
msgstr ""
"<i class=\"fa fa-fw o_button_icon fa-circle text-danger\"/>\n"
"                            <span>Offline</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-circle text-success\"/>\n"
"                            <span>Connected</span>"
msgstr ""
"<i class=\"fa fa-fw o_button_icon fa-circle text-success\"/>\n"
"                            <span>Kirjautunut</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid ""
"<i class=\"fa fa-info-circle\"/> Edit the content below this line to adapt "
"the default <strong>Page not found</strong> page."
msgstr ""
"<i class=\"fa fa-info-circle\"/> Muokkaa tämän rivin alla olevaa sisältöä "
"mukauttamaan oletusvirheilmoitusta <strong>Sivua ei löydy</strong>."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.protected_403
msgid ""
"<i class=\"fa fa-lock fa-2x\"/><br/>\n"
"                            <span class=\"mt-1\">A password is required to access this page.</span>"
msgstr ""
"<i class=\"fa fa-lock fa-2x\"/><br/>\n"
"                            <span class=\"mt-1\">Tämän sivun avaamiseen tarvitaan salasana.</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.protected_403
msgid ""
"<i class=\"fa fa-lock fa-2x\"/><br/>\n"
"                            <span class=\"mt-1\">Wrong password</span>"
msgstr ""
"<i class=\"fa fa-lock fa-2x\"/><br/>\n"
"                            <span class=\"mt-1\">Väärä salasana</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_page
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid ""
"<i class=\"fa fa-map-marker fa-fw mr-2\"/><span "
"class=\"o_force_ltr\">Chaussée de Namur 40</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_page
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid ""
"<i class=\"fa fa-phone fa-fw mr-2\"/><span class=\"o_force_ltr\">+ 32 81 81 "
"37 00</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.language_selector_add_language
msgid ""
"<i class=\"fa fa-plus-circle\"/>\n"
"        <span>Add a language...</span>"
msgstr ""
"<i class=\"fa fa-plus-circle\"/>\n"
"        <span>Lisää kieli...</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "<i class=\"fa fa-th-large mr-2\"/> WEBSITE"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-calendar fa-fw mr-2\"/>\n"
"                            <b>Events</b>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-eye fa-fw mr-2\"/>\n"
"                            <b>About us</b>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-group fa-fw mr-2\"/>\n"
"                            <b>Partners</b>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-handshake-o fa-fw mr-2\"/>\n"
"                            <b>Services</b>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-headphones fa-fw mr-2\"/>\n"
"                            <b>Help center</b>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-map-o fa-fw mr-2\"/>\n"
"                            <b>Guides</b>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-newspaper-o fa-fw mr-2\"/>\n"
"                            <b>Our blog</b>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-star-o fa-fw mr-2\"/>\n"
"                            <b>Customers</b>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-tags fa-fw mr-2\"/>\n"
"                            <b>Products</b>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "<i class=\"s_mega_menu_thumbnails_icon fa fa-comments mr-2\"/> Contact us"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "<i class=\"s_mega_menu_thumbnails_icon fa fa-cube mr-2\"/> Free returns"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid ""
"<i class=\"s_mega_menu_thumbnails_icon fa fa-shopping-basket mr-2\"/> Pickup"
" in store"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid ""
"<i class=\"s_mega_menu_thumbnails_icon fa fa-truck mr-2\"/> Express delivery"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "<i title=\"Is the page SEO optimized?\" class=\"fa fa-search\"/>"
msgstr "<i title=\"Is the page SEO optimized?\" class=\"fa fa-search\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid ""
"<i title=\"Is the page included in the main menu?\" class=\"fa fa-thumb-"
"tack\"/>"
msgstr ""
"<i title=\"Is the page included in the main menu?\" class=\"fa fa-thumb-"
"tack\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "<i title=\"Is the page indexed by search engines?\" class=\"fa fa-globe\"/>"
msgstr "<i title=\"Is the page indexed by search engines?\" class=\"fa fa-globe\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "<i title=\"Is the page published?\" class=\"fa fa-eye\"/>"
msgstr "<i title=\"Is the page published?\" class=\"fa fa-eye\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<i>Instant setup, satisfied or reimbursed.</i>"
msgstr "<i>Välitön asennus, tyytyväinen tai hyvitetty.</i>"

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "<p>Attached files : </p>"
msgstr "<p>Liitetiedostot: </p>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_header_magazine_oe_structure_header_magazine_1
msgid "<small class=\"s_share_title d-none\"><b>Follow us</b></small>"
msgstr "<small class=\"s_share_title d-none\"><b>Seuraa meitä</b></small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_header_contact_oe_structure_header_contact_1
msgid "<small class=\"s_share_title text-muted d-none\"><b>Follow us</b></small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<small class=\"text-muted\">\n"
"                                            <i class=\"fa fa-info\"/>: type some of the first chars after 'google' is enough, we'll guess the rest.\n"
"                                        </small>"
msgstr ""
"<small class=\"text-muted\">\n"
"                                            <i class=\"fa fa-info\"/>: kirjoita osa ensimmäisistä merkeistä 'google' sanan jälkeen, me arvaamme loput.\n"
"                                        </small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid ""
"<small id=\"emailHelp\" class=\"form-text text-muted\">Form field help "
"text</small>"
msgstr ""
"<small id=\"emailHelp\" class=\"form-text text-muted\">Lomakekentän "
"ohjeteksti</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid ""
"<small id=\"emailHelp\" class=\"form-text text-muted\">We'll never share "
"your email with anyone else.</small>"
msgstr ""
"<small id=\"emailHelp\" class=\"form-text text-muted\">Emme jaa "
"sähköpostiasi koskaan kenenkään muun kanssa.</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<small>/ month</small>"
msgstr "<small>/ kuukaudessa</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "<small>TABS</small>"
msgstr "<small>VÄLILEHDET</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_header_magazine_oe_structure_header_magazine_1
msgid "<small>We help you grow your business</small>"
msgstr "<small>Autamme sinua kasvattamaan yritystäsi</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "<span class=\"bg-white\"><b>2015</b></span>"
msgstr "<span class=\"bg-white\"><b>2015</b></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "<span class=\"bg-white\"><b>2018</b></span>"
msgstr "<span class=\"bg-white\"><b>2018</b></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "<span class=\"bg-white\"><b>2019</b></span>"
msgstr "<span class=\"bg-white\"><b>2019</b></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                <span class=\"sr-only\">Next</span>"
msgstr ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                <span class=\"sr-only\">Seuraava</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                <span class=\"sr-only\">Previous</span>"
msgstr ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                <span class=\"sr-only\">Edellinen</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid ""
"<span class=\"d-block p-2\">\n"
"                            <b><font style=\"font-size:14px;\">Discover our new products</font></b>\n"
"                        </span>"
msgstr ""
"<span class=\"d-block p-2\">\n"
"                            <b><font style=\"font-size:14px;\">Tutustu uusiin tuotteisiimme</font></b>\n"
"                        </span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid ""
"<span class=\"fa fa-check-circle\"/>\n"
"                                            <span>Your message has been sent <b>successfully</b></span>"
msgstr ""
"<span class=\"fa fa-check-circle\"/>\n"
"                                            <span>Viestisi on lähetetty <b>onnistuneesti</b></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
msgid ""
"<span class=\"fa fa-chevron-left fa-2x text-white\"/>\n"
"                    <span class=\"sr-only\">Previous</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
msgid ""
"<span class=\"fa fa-chevron-right fa-2x text-white\"/>\n"
"                    <span class=\"sr-only\">Next</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "<span class=\"fa fa-pencil mr-2\"/>Edit"
msgstr "<span class=\"fa fa-pencil mr-2\"/>Muokkaa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "<span class=\"fa fa-plus mr-2\"/>New"
msgstr "<span class=\"fa fa-plus mr-2\"/>Uusi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "<span class=\"fa fa-sort fa-lg\" role=\"img\" aria-label=\"Sort\" title=\"Sort\"/>"
msgstr "<span class=\"fa fa-sort fa-lg\" role=\"img\" aria-label=\"Sort\" title=\"Sort\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.option_header_off_canvas
msgid "<span class=\"fa-2x\">×</span>"
msgstr "<span class=\"fa-2x\">×</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.language_selector_inline
msgid "<span class=\"list-inline-item\">|</span>"
msgstr "<span class=\"list-inline-item\">|</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<span class=\"mx-2\">/</span>"
msgstr "<span class=\"mx-2\">/</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.language_selector_inline
msgid ""
"<span class=\"o_add_language list-inline-item\" "
"groups=\"website.group_website_publisher\">|</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_copyright_company_name
msgid ""
"<span class=\"o_footer_copyright_name mr-2\">Copyright &amp;copy; Company "
"name</span>"
msgstr ""
"<span class=\"o_footer_copyright_name mr-2\">Copyright &amp;copy; Yrityksen "
"nimi</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Robots.txt</span>\n"
"                                    <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"o_form_label\">Robots.txt</span>\n"
"                                    <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Sitemap</span>\n"
"                                    <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"o_form_label\">Sivustokartta</span>\n"
"                                    <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_badge
msgid ""
"<span class=\"s_badge badge badge-secondary o_animable\" data-name=\"Badge\">\n"
"        <i class=\"fa fa-1x fa-fw fa-folder o_not-animable\"/>Category\n"
"    </span>"
msgstr ""
"<span class=\"s_badge badge badge-secondary o_animable\" data-name=\"Badge\">\n"
"        <i class=\"fa fa-1x fa-fw fa-folder o_not-animable\"/>Kategoria\n"
"    </span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"s_blockquote_author\"><b>Iris DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>Iiris Meikäläinen</b> • "
"Toimitusjohtaja MinunYrityksessä</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"s_blockquote_author\"><b>Jane DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>Jane Meikäläinen</b> • "
"Toimitusjohtaja MinunYrityksessä</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"s_blockquote_author\"><b>John DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>Matti Meikäläinen</b> • "
"Toimitusjohtaja MinunYrityksessä</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>125</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"
msgstr ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>125</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>35</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"
msgstr ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>35</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>65</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"
msgstr ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>65</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "<span class=\"s_number display-4\">12</span><br/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "<span class=\"s_number display-4\">37</span><br/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "<span class=\"s_number display-4\">45</span><br/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "<span class=\"s_number display-4\">8</span><br/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar
msgid "<span class=\"s_progress_bar_text\">80% Development</span>"
msgstr "<span class=\"s_progress_bar_text\">80% Etenemistä</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid "<span class=\"s_website_form_label_content\">Email To</span>"
msgstr "<span class=\"s_website_form_label_content\">Sähköposti</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid "<span class=\"s_website_form_label_content\">Phone Number</span>"
msgstr "<span class=\"s_website_form_label_content\">Puhelinnumero</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid ""
"<span class=\"s_website_form_label_content\">Subject</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Aihe</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid "<span class=\"s_website_form_label_content\">Your Company</span>"
msgstr "<span class=\"s_website_form_label_content\">Yritys</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid "<span class=\"s_website_form_label_content\">Your Question</span>"
msgstr "<span class=\"s_website_form_label_content\">Viesti</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "<span class=\"sr-only\">Toggle Dropdown</span>"
msgstr "<span class=\"sr-only\">Kytke pudotusvalikko</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid ""
"<span title=\"Mobile preview\" role=\"img\" aria-label=\"Mobile preview\" "
"class=\"fa fa-mobile\"/>"
msgstr ""
"<span title=\"Mobile preview\" role=\"img\" aria-label=\"Mobile preview\" "
"class=\"fa fa-mobile\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_publisher
msgid ""
"<span/>\n"
"                    <span class=\"css_publish\">Unpublished</span>\n"
"                    <span class=\"css_unpublish\">Published</span>"
msgstr ""
"<span/>\n"
"                    <span class=\"css_publish\">Julkaisematon</span>\n"
"                    <span class=\"css_unpublish\">Julkaistu</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "<span>Theme</span>"
msgstr "<span>Teema</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"A CDN helps you serve your website’s content with high availability and high"
" performance to any visitor wherever they are located."
msgstr ""
"CDN auttaa sinua palvelemaan verkkosivustosi sisältöä korkealla "
"saatavuudella ja korkealla suorituskyvyllä kaikille vierailijoille heidän "
"sijainnistaan riippumatta."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart
msgid "A Chart Title"
msgstr "Kaavion otsikko"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"A Google Map error occurred. Make sure to read the key configuration popup "
"carefully."
msgstr ""
"Google Mapissa tapahtui virhe. Muista lukea ohjeet avainten määrityksen "
"ponnahdusikkunasta huolellisesti."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "A Section Subtitle"
msgstr "Kappaleen alaotsikko"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card
msgid ""
"A card is a flexible and extensible content container. It includes options "
"for headers and footers, a wide variety of content, contextual background "
"colors, and powerful display options."
msgstr ""
"Kortti on joustava ja laajennettava sisältösäiliö. Se sisältää vaihtoehtoja "
"ylä- ja alatunnisteille, laajan valikoiman sisältöä, kontekstuaalisia "
"taustavärejä ja tehokkaita näyttövaihtoehtoja."

#. module: website
#: model:ir.model.fields,help:website.field_ir_actions_server__website_published
#: model:ir.model.fields,help:website.field_ir_cron__website_published
msgid ""
"A code server action can be executed from the website, using a dedicated "
"controller. The address is <base>/website/action/<website_path>. Set this "
"field as True to allow users to run this action. If it is set to False the "
"action cannot be run through the website."
msgstr ""
"Koodipalvelintoiminto voidaan suorittaa verkkosivustolta erillisen ohjaimen "
"avulla. Osoite on <base>/website/action/<website_path>. Aseta tämän kentän "
"arvoksi True, jotta käyttäjät voivat suorittaa tämän toiminnon. Jos sen arvo"
" on False, toimintoa ei voida suorittaa verkkosivuston kautta."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_color_blocks_2
msgid "A color block"
msgstr "A color block"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_image_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_default_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_image_texts_image_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_mosaic_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_reversed_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_texts_image_texts_template
msgid "A great title"
msgstr "Mahtava otsikko"

#. module: website
#: model:ir.model.fields,help:website.field_website_snippet_filter__field_names
msgid "A list of comma-separated field names"
msgstr "Luettelo pilkuilla erotetuista kenttien nimistä"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_stores_locator
msgid "A map and a listing of your stores"
msgstr "Kartta ja luettelo myymälöistäsi"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__visit_count
msgid ""
"A new visit is considered if last connection was more than 8 hours ago."
msgstr "Käynti lasketaan uudeksi, os edellisestä yhteydestä on yli 8 tuntia."

#. module: website
#: model:ir.model.constraint,message:website.constraint_website_visitor_partner_uniq
msgid "A partner is linked to only one visitor."
msgstr "Kumppani on kytketty vain yhteen kävijään."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "A short description of this great feature."
msgstr "Lyhyt kuvaus tästä upeasta ominaisuudesta."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "A small explanation of this great <br/>feature, in clear words."
msgstr ""
"Yksinkertainen selitys tästä mahtavasta <br/>ominaisuudesta, selkeästi "
"ilmaistuna."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid ""
"A timeline is a graphical representation on which important events are "
"marked."
msgstr "Aikajana on graafinen esitys, jolle tärkeät tapahtumat on merkitty."

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__is_connected
msgid ""
"A visitor is considered as connected if his last page view was within the "
"last 5 minutes."
msgstr ""
"Kävijä lasketaan aktiiviseksi jos edellinen sivunlataus on tehty viiden "
"minuutin sisällä."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#, python-format
msgid "API Key"
msgstr "API Avain"

#. module: website
#: model:website.configurator.feature,name:website.feature_page_about_us
msgid "About Us"
msgstr "Tietoja meistä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.aboutus
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "About us"
msgstr "Tietoa meistä"

#. module: website
#: code:addons/website/controllers/backend.py:0
#, python-format
msgid "Access Error"
msgstr "Käyttöoikeusvirhe"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__access_token
msgid "Access Token"
msgstr "Pääsytunniste"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.protected_403
msgid "Access to this page"
msgstr "Pääsy tälle sivulle"

#. module: website
#: model:ir.model.constraint,message:website.constraint_website_visitor_access_token_unique
msgid "Access token should be unique."
msgstr "Pääsytunnisteen pitää olla yksilöllinen."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Accessories"
msgstr "Tarvikkeet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Account &amp; Sales management"
msgstr "Tilien ja myynnin hallinta"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__redirect_type
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Action"
msgstr "Toiminto"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/res_config_settings.js:0
#, python-format
msgid "Activate anyway"
msgstr "Aktivoi joka tapauksessa"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__active
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__active
#: model:ir.model.fields,field_description:website.field_website_page__active
#: model:ir.model.fields,field_description:website.field_website_rewrite__active
#: model:ir.model.fields,field_description:website.field_website_visitor__active
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Active"
msgstr "Aktiivinen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"Adapt these three columns to fit your design need. To duplicate, delete or "
"move columns, select the column and use the top icons to perform your "
"action."
msgstr ""
"Mukauta nämä kolme saraketta suunnittelutarpeesi mukaan. Jos haluat "
"monistaa, poistaa tai siirtää sarakkeita, valitse sarake ja suorita toiminto"
" ylimpien kuvakkeiden avulla."

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/xml/website.seo.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#, python-format
msgid "Add"
msgstr "Lisää"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Add Features"
msgstr "Lisää toimintoja"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Add Item"
msgstr "Lisää kohde"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "Add Media"
msgstr "Lisää media"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Add Mega Menu Item"
msgstr "Lisää elementti megavalikkoon"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Add Menu Item"
msgstr "Lisää elementti valikkoon"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog_add_product_widget
msgid "Add Product"
msgstr "Lisää tuote"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Add Row"
msgstr "Lisää rivi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Add Serie"
msgstr "Lisää sarja"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Add Slide"
msgstr "Lisää dia"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Add Tab"
msgstr "Add Tab"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline_options
msgid "Add Year"
msgstr "Lisää vuosi"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Add a Google Font"
msgstr "Lisää fontti Googlesta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_picture
msgid "Add a caption to enhance the meaning of this image."
msgstr "Lisää kuvateksti parantaaksesi tämän kuvan merkitystä."

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_product_catalog/options.js:0
#, python-format
msgid "Add a description here"
msgstr "Lisää kuvaus tähän"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Add a great slogan."
msgstr "Lisää mahtava slogan."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Add a menu description."
msgstr "Lisää valikon kuvaus."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Add a menu item"
msgstr "Lisää elementti valikkoon"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Add a new field after this one"
msgstr "Lisää uusi kenttä tämän jälkeen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Add a new field at the end"
msgstr "Lisää uusi kenttä loppuu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Add features"
msgstr "Lisää ominaisuuksia"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Add links to social media on your website"
msgstr "Lisää sosiaalisen median linkit verkkosivuillesi"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Add new %s"
msgstr "Lisää uusi %s"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Add to cart"
msgstr "Lisää ostoskoriin"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Add to menu"
msgstr "Lisää valikkoon"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"Adding a font requires a reload of the page. This will save all your "
"changes."
msgstr ""
"Fontin lisääminen vaatii sivun uudelleenlatauksen. Tämä tallentaa kaikki "
"tekemäsi muutokset."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Address"
msgstr "Osoite"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Advertising &amp; Marketing"
msgstr "Mainonta &amp; markkinointi"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__after
msgid "After"
msgstr "Jälkeen"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Alert"
msgstr "Varoitus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Align Bottom"
msgstr "Kohdista alas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Align Middle"
msgstr "Kohdista keskelle"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Align Top"
msgstr "Kohdista ylös"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Alignment"
msgstr "Kohdistus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Aline Turner, CTO"
msgstr "Aline Turner, CTO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Aline is one of the iconic people in life who can say they love what they "
"do. She mentors 100+ in-house developers and looks after the community of "
"thousands of developers."
msgstr ""
"Alina on yksi ikonisimmista ihmisistä elämässä, joka voi sanoa rakastavansa "
"sitä, mitä hän tekee. Hän ohjaa yli 100 omaa kehittäjää ja huolehtii "
"tuhansien kehittäjien yhteisöstä."

#. module: website
#: model:ir.model.fields.selection,name:website.selection__ir_ui_view__visibility__
msgid "All"
msgstr "Kaikki"

#. module: website
#: model:ir.model,name:website.model_website_route
msgid "All Website Route"
msgstr "Kaikkia verkkosivuja koskeva reititys"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_qweb
msgid "All Websites"
msgstr "Kaikki verkkosivut"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "All informations you need"
msgstr "Kaikki tarvitsemasi tiedot"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "All pages"
msgstr "Kaikki sivut"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_searchbar/000.xml:0
#, python-format
msgid "All results"
msgstr "Kaikki tulokset"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "All these icons are completely free for commercial use."
msgstr "Kaikki nämä ikonit ovat täysin vapaita kaupalliseen käyttöön."

#. module: website
#: model:ir.model.fields,help:website.field_ir_ui_view__track
#: model:ir.model.fields,help:website.field_website_page__track
msgid "Allow to specify for one page of the website to be trackable or not"
msgstr "Salli määrittää, onko verkkosivuston yksi sivu seurattavissa vai ei"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model__website_form_access
msgid "Allowed to use in forms"
msgstr "Sallittu käytettäväksi lomakkeilla"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Already installed"
msgstr "Jo asennettu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Alternate Image Text"
msgstr "Vaihtoehtoinen kuvateksti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Alternate Text"
msgstr "Vaihtoehtoinen teksti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Alternate Text Image"
msgstr "Vaihtoehtoinen tekstikuva"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Alternate Text Image Text"
msgstr "Vaihtoehtoinen teksti Kuvateksti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"Although this Website may be linked to other websites, we are not, directly "
"or indirectly, implying any approval, association, sponsorship, endorsement,"
" or affiliation with any linked website, unless specifically stated herein."
msgstr ""
"Vaikka tämä verkkosivusto voi olla linkitetty muille verkkosivustoille, emme"
" suoraan tai epäsuorasti tarkoita hyväksyntää, yhdistystä, sponsorointia, "
"tukemista tai sidoksia mihinkään linkitetylle verkkosivustolle, ellei tässä "
"erikseen mainita."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Always Underlined"
msgstr "Aina aliviivattu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Always Visible"
msgstr "Näkyy aina"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Always visible"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "Amazing pages"
msgstr "Mahtavat sivut"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map
msgid "An address must be specified for a map to be embedded"
msgstr "Osoite on määritettävä, jotta kartta voidaan syvätä"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "An error has occured, the form has not been sent."
msgstr "Lomakkeen lähetyksessä tapahtui virhe eikä sitä pystytty lähettämään."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "An error occurred while rendering the template"
msgstr "Mallia piirrettäessä tapahtui virhe"

#. module: website
#: model:ir.actions.client,name:website.backend_dashboard
#: model:ir.ui.menu,name:website.menu_website_google_analytics
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Analytics"
msgstr "Työpöytä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Analytics cookies and privacy information."
msgstr "Analytics-evästeet ja tietosuojatiedot."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Anchor copied to clipboard<br>Link: %s"
msgstr "Ankkuri kopioitu leikepöydälle<br>Linkki: %s"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Anchor name"
msgstr "Ankkurin nimi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_image_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_default_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_image_texts_image_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_mosaic_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_reversed_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_texts_image_texts_template
msgid "And a great subtitle"
msgstr "Ja loistava alaotsikko"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Animate"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Animate text"
msgstr "Animoi tekstiä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Animated"
msgstr "Animoitu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Animation Delay"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Animation Duration"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Animation Launch"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_color_blocks_2
msgid "Another color block"
msgstr "Toinen väripalikka"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "Another feature"
msgstr "Toinen ominaisuus"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__append
msgid "Append"
msgstr "Lisää"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_add_features
#: model:ir.ui.menu,name:website.menu_website_add_features
msgid "Apps"
msgstr "Sovelluskauppa"

#. module: website
#: code:addons/website/controllers/main.py:0
#, python-format
msgid "Apps url"
msgstr "Apps url"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__arch
msgid "Arch"
msgstr "Arch"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_db
msgid "Arch Blob"
msgstr "Arch Blob"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_fs
msgid "Arch Filename"
msgstr "Arch Tiedostonnimi"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__arch_fs
msgid "Arch Fs"
msgstr "Arch Fs"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Archived"
msgstr "Arkistoitu"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__specific_user_account
msgid "Are newly created user accounts website specific"
msgstr "Ovatko uudet käyttäjätunnukset verkkosivukohtaisia"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "Are you sure you want to delete this page ?"
msgstr "Oletko varma että haluat poistaa tämän sivun?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Arrows"
msgstr "Nuolet"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
#, python-format
msgid "As promised, we will offer 4 free tickets to our next summit."
msgstr ""
"Kuten luvattiin, tarjoamme 4 ilmaista lippua seuraavaan huippukokoukseen."

#. module: website
#: model:ir.model,name:website.model_ir_asset
msgid "Asset"
msgstr "Varat"

#. module: website
#: model:ir.model,name:website.model_web_editor_assets
msgid "Assets Utils"
msgstr "Sisältötyökalut"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__copy_ids
msgid "Assets using a copy of me"
msgstr "Omaisuuserät, jotka käyttävät minun kopiotani"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "At The End"
msgstr "Lopuksi"

#. module: website
#: model:ir.model,name:website.model_ir_attachment
msgid "Attachment"
msgstr "Liite"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__copy_ids
msgid "Attachment using a copy of me"
msgstr "Liite käyttäen kopiota minusta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Authenticate users, protect user data and allow the website to deliver the services users expects,\n"
"                                                such as maintaining the content of their cart, or allowing file uploads."
msgstr ""
"Todenna käyttäjät, suojaa käyttäjätiedot ja anna verkkosivuston tarjota käyttäjien odottamia palveluita,\n"
"                                                kuten ostoskorin sisällön ylläpitäminen tai tiedostojen lataamisen salliminen."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_search
msgid "Author"
msgstr "Tekijä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Auto"
msgstr "Automaattinen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid ""
"Automatically opens the pop-up if the user stays on a page longer than the "
"specified time."
msgstr ""
"Avaa ponnahdusikkunan automaattisesti, jos käyttäjä pysyy sivulla määritetyn"
" ajan pidempään."

#. module: website
#: model:ir.model.fields,field_description:website.field_website__auto_redirect_lang
msgid "Autoredirect Language"
msgstr "Edelleenohjaa kieli"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Autosizing"
msgstr "Automaattinen koko"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_actions_server__website_published
#: model:ir.model.fields,field_description:website.field_ir_cron__website_published
msgid "Available on the Website"
msgstr "Saatavana verkkosivustolla"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "BSgzTvR5L1GB9jriT451iTN4huVPxHmltG6T6eo"
msgstr "BSgzTvR5L1GB9jriT451iTN4huVPxHmltG6T6eo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "BTS Base Colors"
msgstr "Perusvärit kulissien takana"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Backdrop"
msgstr "Tausta"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Background"
msgstr "Tausta"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/configurator_tour.js:0
#: code:addons/website/static/src/js/tours/configurator_tour.js:0
#, python-format
msgid "Background Shape"
msgstr "Taustan muoto"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Badge"
msgstr "Ansiomerkki"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Bags"
msgstr "Laukut"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Bar Horizontal"
msgstr "Palkki vaakasuorassa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Bar Vertical"
msgstr "Palkki pystysuorassa"

#. module: website
#: model:ir.model,name:website.model_base
msgid "Base"
msgstr "Perus"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_base
msgid "Base View Architecture"
msgstr "Perusnäkymän arkkitehtuuri"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_ui_view__mode__primary
msgid "Base view"
msgstr "Perusnäkymä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Basic sales &amp; marketing for up to 2 users"
msgstr "Perustason myynti ja markkinointi jopa kahdelle käyttäjälle"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "Beautiful snippets"
msgstr "Kauniita palikoita"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Beef Carpaccio"
msgstr "Naudanliha carpaccio"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__before
msgid "Before"
msgstr "Ennen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Beginner"
msgstr "Aloittelija"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Below Each Other"
msgstr "Toistensa alapuolella"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
msgid "Big"
msgstr "Suuri"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Big Icons Subtitles"
msgstr "Suuret kuvakkeiden kuvatekstit"

#. module: website
#: model:ir.model.fields,help:website.field_ir_model_fields__website_form_blacklisted
msgid "Blacklist this field for web forms"
msgstr "Lisää tämä kenttä verkkosivun lomakkeiden estolistalle"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model_fields__website_form_blacklisted
msgid "Blacklisted in web forms"
msgstr "Verkkosivun lomakkeiden estolistalla"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Blazers"
msgstr "Blazer"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Block"
msgstr "Ryhmä"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Blockquote"
msgstr "Sitaatti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Blog"
msgstr "Blogi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Blog Post"
msgstr "Blogikirjoitus"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_news
msgid "Blogging and posting relevant content"
msgstr "Bloggaaminen ja merkityksellisen sisällön tuottaminen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Books"
msgstr "Kirjat"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Bootstrap-based templates"
msgstr "Bootstrappiiin perustuvat mallit"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_widgets
#, python-format
msgid "Border"
msgstr "Reunus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Border Bottom"
msgstr "Alareunus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
msgid "Border Color"
msgstr "Reunuksen väri"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Border Radius"
msgstr "Reunuksen säde"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Border Width"
msgstr "Reunuksen leveys"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bordered"
msgstr "Reunustettu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Bottom"
msgstr "Alhaalla"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Bottom to Top"
msgstr "Alhaalta ylös"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bounce In"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bounce In-Down"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bounce In-Left"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bounce In-Right"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Boxed"
msgstr "Laatikkomainen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Boxes"
msgstr "Laatikot"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Breadcrumb"
msgstr "Murupolku"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Build my website"
msgstr "Rakenna verkkosivustoni"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Building blocks system"
msgstr "Rakennuspalikoiden järjestelmä"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Building your %s"
msgstr "Rakennetaan sinun %s"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Building your website..."
msgstr "Rakennetaan sinun verkkosivu..."

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__bundle
msgid "Bundle"
msgstr "Tuotepaketti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Button"
msgstr "Nappi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Button Position"
msgstr "Painikkeen sijainti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Buttons"
msgstr "Painikkeet"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#, python-format
msgid "By clicking on this banner, you give us permission to collect data."
msgstr "Klikkaamalla tätä banneria annat meille luvan kerätä tietoja."

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__cdn_url
#: model:ir.model.fields,field_description:website.field_website__cdn_url
msgid "CDN Base URL"
msgstr "CDN perus-URL"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__cdn_filters
#: model:ir.model.fields,field_description:website.field_website__cdn_filters
msgid "CDN Filters"
msgstr "CDN suotimet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "CTA"
msgstr "CTA"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__cache_key_expr
msgid "Cache Key Expr"
msgstr "Välimuistin avain vanhenee"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__cache_time
msgid "Cache Time"
msgstr "Välimuistin aika"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Call to Action"
msgstr "Kehotustoiminta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "Call us"
msgstr "Soita meille"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Call-to-action"
msgstr "Kehotus toimintaan"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Camera"
msgstr "Kamera"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_partner__can_publish
#: model:ir.model.fields,field_description:website.field_res_users__can_publish
#: model:ir.model.fields,field_description:website.field_website_page__can_publish
#: model:ir.model.fields,field_description:website.field_website_published_mixin__can_publish
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__can_publish
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__can_publish
msgid "Can Publish"
msgstr "Voi julkaista"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/dashboard.js:0
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: code:addons/website/static/src/js/menu/new_content.js:0
#: code:addons/website/static/src/js/utils.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/xml/website.xml:0
#: model_terms:ir.ui.view,arch_db:website.qweb_500
#: model_terms:ir.ui.view,arch_db:website.view_edit_robots
#: model_terms:ir.ui.view,arch_db:website.view_website_form_view_themes_modal
#, python-format
msgid "Cancel"
msgstr "Peruuta"

#. module: website
#: code:addons/website/models/res_lang.py:0
#, python-format
msgid "Cannot deactivate a language that is currently used on a website."
msgstr "Kieltä ei voi deaktivoida jos se on käytössä verkkosivulla."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/content/website_root.js:0
#, python-format
msgid "Cannot load google map."
msgstr "Google-karttaa ei voi ladata."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Card"
msgstr "Kortti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Card Body"
msgstr "Kortin runko"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Card Footer"
msgstr "Kortin alatunniste"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Card Header"
msgstr "Kortin ylätunniste"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
msgid "Card Style"
msgstr "Korttityyli"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Cards"
msgstr "Kortit"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_career
msgid "Career"
msgstr "Työpaikat"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Case Studies"
msgstr "Tapaustutkimuksia"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_search
msgid "Category"
msgstr "Tuoteryhmä(t)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Category of Cookie"
msgstr "Evästeen kategoria"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Center"
msgstr "Keskellä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Centered"
msgstr "Keskitetty"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Centered Logo"
msgstr "Keskitetty logo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Change Icons"
msgstr "Vaihda ikonit"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Change theme in a few clicks, and browse through Odoo's catalog of\n"
"                            ready-to-use themes available in our app store."
msgstr ""
"Vaihda teema muutamalla napsautuksella ja selaa Odoon\n"
"                            käyttövalmiita teemoja, jotka ovat saataville sovelluskaupassamme."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"Changing the color palette will reset all your color customizations, are you"
" sure you want to proceed?"
msgstr ""
"Värivalikoiman muuttaminen nollaa kaikki värimuokkauksesi. Haluatko varmasti"
" jatkaa?"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"Changing theme requires to leave the editor. This will save all your "
"changes, are you sure you want to proceed? Be careful that changing the "
"theme will reset all your color customizations."
msgstr ""
"Teeman vaihtaminen edellyttää poistumista editorista. Tämä tallentaa kaikki "
"tekemäsi muutokset. Haluatko varmasti jatkaa? Varoituksena, että teeman "
"vaihtaminen nollaa kaikki värimuokkauksesi."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#, python-format
msgid "Chart"
msgstr "Kaavio"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_live_chat
msgid "Chat with visitors to improve traction"
msgstr "Keskustele kävijöiden kanssa vetovoiman parantamiseksi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup
msgid "Check out now and get $20 off your first order."
msgstr "Osta nyt ja saat 20€ alennuksen ensimmäisestä tilauksesta."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/content/website_root.js:0
#, python-format
msgid "Check your configuration."
msgstr "Tarkista konfiguraatiosi."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Check your connection and try again"
msgstr "Tarkista yhteys ja yritä uudelleen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Checkbox"
msgstr "Valintaruutu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Cheese Onion Rings"
msgstr "Juusto-sipulirenkaita"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Chefs Fresh Soup of the Day"
msgstr "Kokin päivän tuorekeitto"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__field_parent
msgid "Child Field"
msgstr "Alakenttä"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__child_id
#: model_terms:ir.ui.view,arch_db:website.website_menus_form_view
msgid "Child Menus"
msgstr "Alavalikot"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Children"
msgstr "Alatunnukset"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Choose"
msgstr "Valitse"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_picture
msgid ""
"Choose a vibrant image and write an inspiring paragraph about it.<br/> It "
"does not have to be long, but it should reinforce your image."
msgstr ""
"Valitse eloisa kuva ja kirjoita siitä inspiroiva kappale.<br/> Sen ei "
"tarvitse olla pitkä, mutta sen pitäisi vahvistaa imagoasi."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Choose an anchor name"
msgstr "Valitse ankkurin nimi"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#, python-format
msgid "Choose another theme"
msgstr "Valitse toinen teema"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Choose your favorite"
msgstr "Valitse suosikkisi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
msgid "Circle"
msgstr "Rinki"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Classic"
msgstr "Klassinen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Clean"
msgstr "Siisti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "Clever Slogan"
msgstr "Fiksu slogan"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Click and change content directly from the front-end: no complex back\n"
"                            end to deal with."
msgstr ""
"Klikkaa ja muuta sisältöä suoraan etuosasta: ei monimutkaista takaosaa."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "Click here to go back to block tab."
msgstr "Klikkaa tätä palataksesi palikka-välilehteen."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Click on"
msgstr "Klikkaa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Click on the icon to adapt it <br/>to your purpose."
msgstr "Klikkaa kuvaketta mukauttaaksesi <br/>sen tarkoitukseesi."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Click to choose more images"
msgstr "Klikkaa valitaksesi lisää kuvia"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Click to select"
msgstr "Klikkaa valitaksesi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Client ID"
msgstr "Asiakkaan tunniste/ID"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Client Secret"
msgstr "Client Secret"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Clone this page"
msgstr "Kloonaa tämä sivu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/xml/website.xml:0
#: model_terms:ir.ui.view,arch_db:website.qweb_500
#: model_terms:ir.ui.view,arch_db:website.s_popup
#: model_terms:ir.ui.view,arch_db:website.show_website_info
#, python-format
msgid "Close"
msgstr "Sulje"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Close Button Color"
msgstr "Sulje -painikkeen väri"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Clothes"
msgstr "Vaatteet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
msgid "Code"
msgstr "Koodi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Code Injection"
msgstr "Koodiinjektio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
msgid "Collapse Icon"
msgstr "Yhteenvetokuvake"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_badge_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Color"
msgstr "Väri"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_color_blocks_2
msgid ""
"Color blocks are a simple and effective way to <b>present and highlight your"
" content</b>. Choose an image or a color for the background. You can even "
"resize and duplicate the blocks to create your own layout. Add images or "
"icons to customize the blocks."
msgstr ""
"Väripalikat ovat yksinketainen ja tehokas tapa <b>esitellä ja korostaa "
"sisältöäsi</b>. Valitse taustalle kuva tai väri. Voit jopa muuttaa "
"palikkojen kokoa ja monistaa ne luodaksesi oman asettelun. Lisää kuvia tai "
"kuvakkeita palikkojen mukauttamiseksi."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Color filter"
msgstr "Värisuodatin"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Colors"
msgstr "Värit"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Columns"
msgstr "Sarakkeet"

#. module: website
#: model:ir.model.fields,help:website.field_website_configurator_feature__website_config_preselection
msgid ""
"Comma-separated list of website type/purpose for which this feature should "
"be pre-selected"
msgstr ""
"Pilkulla erotettu luettelo verkkosivuston tyypistä/käyttötarkoituksesta, "
"jota varten tämä toiminto olisi valittava valmiiksi"

#. module: website
#: model:ir.model,name:website.model_res_company
msgid "Companies"
msgstr "Yritykset"

#. module: website
#: code:addons/website/models/website.py:0
#: model:ir.model.fields,field_description:website.field_website__company_id
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#, python-format
msgid "Company"
msgstr "Yritys"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Complete CRM for any size team"
msgstr "Täydellinen CRM kaikenkokoisille tiimeille"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Components"
msgstr "Komponentit"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Computers"
msgstr "Tietokoneet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Computers &amp; Devices"
msgstr "Tietokoneet ja laitteet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Conditionally"
msgstr "Ehdollisesti"

#. module: website
#: model:ir.model,name:website.model_res_config_settings
msgid "Config Settings"
msgstr "Konfiguraatioasetukset"

#. module: website
#: model:ir.ui.menu,name:website.menu_website_global_configuration
msgid "Configuration"
msgstr "Asetukset"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__configurator_done
msgid "Configurator Done"
msgstr "Konfiguraattori Valmis"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_social_network
msgid "Configure Social Network"
msgstr "Konfiguroi sosiaalinen media"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/dashboard.js:0
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Connect Google Analytics"
msgstr "Kytke Google Analytics"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Connect with us"
msgstr "Ole meihin yhteydessä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Connected"
msgstr "Yhdistetty"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_google_search_console
msgid "Console Google Search"
msgstr "Konsoli Google-haku"

#. module: website
#: model:ir.model,name:website.model_res_partner
#: model:ir.model.fields,field_description:website.field_website_visitor__partner_id
#: model_terms:ir.ui.view,arch_db:website.s_tabs
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Contact"
msgstr "Kontakti"

#. module: website
#: code:addons/website/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website.header_call_to_action
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.s_banner
#, python-format
msgid "Contact Us"
msgstr "Ota yhteyttä"

#. module: website
#: code:addons/website/models/website_visitor.py:0
#, python-format
msgid "Contact Visitor"
msgstr "Ota yhteyttä vierailijaan"

#. module: website
#: model:website.menu,name:website.menu_contactus
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
#: model_terms:ir.ui.view,arch_db:website.s_cover
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#: model_terms:website.page,arch_db:website.contactus_page
msgid "Contact us"
msgstr "Ota yhteyttä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid ""
"Contact us about anything related to our company or services.<br/>\n"
"                                    We'll do our best to get back to you as soon as possible."
msgstr ""
"Ota meihin yhteyttä mistä tahansa yritykseemme tai palveluihimme liittyvästä asiasta.<br/>\n"
"                                    Teemme parhaamme vastataksemme sinulle mahdollisimman pian."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "Contact us anytime"
msgstr "Ota meihin yhteyttä milloin tahansa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Contact us for any issue or question"
msgstr "Ota meihin yhteyttä kaikissa asioissa tai kysymyksissä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Contacts"
msgstr "Yhteystiedot"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Contains"
msgstr "Sisältää"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_robots__content
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Content"
msgstr "Sisältö"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__cdn_activated
#: model:ir.model.fields,field_description:website.field_website__cdn_activated
msgid "Content Delivery Network (CDN)"
msgstr "Content Delivery Network (CDN)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Content Width"
msgstr "Sisällön leveys"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/translator.xml:0
#, python-format
msgid "Content to translate"
msgstr "Kännettävä sisältö"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Continue"
msgstr "Jatka"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Continue reading <i class=\"fa fa-long-arrow-right align-middle ml-1\"/>"
msgstr "Jatka lukemista <i class=\"fa fa-long-arrow-right align-middle ml-1\"/>"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
#: model_terms:ir.ui.view,arch_db:website.cookies_bar
#, python-format
msgid "Cookie Policy"
msgstr "Evästekäytäntö"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.res_config_settings.xml:0
#, python-format
msgid "Cookie bars may significantly impair the experience"
msgstr "Evästepalkit voivat heikentää kokemusta merkittävästi"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_cookies_bar
#: model:ir.model.fields,field_description:website.field_website__cookies_bar
msgid "Cookies Bar"
msgstr "Evästeet -palkki"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Cookies are small bits of text sent by our servers to your computer or device when you access our services.\n"
"                            They are stored in your browser and later sent back to our servers so that we can provide contextual content.\n"
"                            Without cookies, using the web would be a much more frustrating experience.\n"
"                            We use them to support your activities on our website. For example, your session (so you don't have to login again) or your shopping cart.\n"
"                            <br/>\n"
"                            Cookies are also used to help us understand your preferences based on previous or current activity on our website (the pages you have\n"
"                            visited), your language and country, which enables us to provide you with improved services.\n"
"                            We also use cookies to help us compile aggregate data about site traffic and site interaction so that we can offer\n"
"                            better site experiences and tools in the future."
msgstr ""
"Evästeet ovat pieniä tekstinpätkiä, jotka palvelimemme lähettävät tietokoneellesi tai laitteellesi, kun käytät palveluitamme.\n"
"                            Ne tallennetaan selaimeesi ja lähetetään myöhemmin takaisin palvelimillemme, jotta voimme tarjota asiayhteyteen liittyvää sisältöä.\n"
"                            Ilman evästeitä verkon käyttö olisi paljon turhauttavampi kokemus.\n"
"                            Käytämme niitä tukemaan toimintaasi verkkosivuillamme. Esimerkiksi istuntosi (jotta sinun ei tarvitse kirjautua uudelleen) tai ostoskorisi.\n"
"                            <br/>\n"
"                            Evästeitä käytetään myös auttamaan meitä ymmärtämään mieltymystesi aiemman tai nykyisen toiminnan perusteella verkkosivustollamme\n"
"                            (sivuillasi vierailut), kielesi ja maasi, minkä ansiosta voimme tarjota sinulle parempia palveluita.\n"
"                            Käytämme myös evästeitä, jotka auttavat meitä kokoamaan aggregoituja tietoja sivuston liikenteestä ja sivuston vuorovaikutuksesta, jotta voimme tarjota\n"
"                           parempia sivustokokemuksia ja työkaluja tulevaisuudessa."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Copyright"
msgstr "Tekijänoikeus"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#, python-format
msgid "Countdown ends in"
msgstr "Ajastin päättyy"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
#, python-format
msgid "Countdown is over - Firework"
msgstr "Lähtölaskenta on ohi - ilotulitus"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__country_id
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Country"
msgstr "Maa"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__country_flag
msgid "Country Flag"
msgstr "Maan lippu"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_country_group_ids
#: model:ir.model.fields,field_description:website.field_website__country_group_ids
msgid "Country Groups"
msgstr "Maaryhmät"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Course"
msgstr "Kurssi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.record_cover
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
msgid "Cover"
msgstr "Kansi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
msgid "Cover Photo"
msgstr "Kansikuva"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_cover_properties_mixin__cover_properties
msgid "Cover Properties"
msgstr "Etusivun ominaisuudet"

#. module: website
#: model:ir.model,name:website.model_website_cover_properties_mixin
msgid "Cover Properties Website Mixin"
msgstr "Kannen ominaisuudet verkkosivulla mixin"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/utils.js:0
#: model_terms:ir.ui.view,arch_db:website.view_website_form_view_themes_modal
#, python-format
msgid "Create"
msgstr "Luo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid "Create Page"
msgstr "Luo sivu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.brand_promotion
msgid "Create a"
msgstr "Luo"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Create a Google Project and Get a Key"
msgstr "Luo Google-projekti ja hanki avain"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Create a New Website"
msgstr "Luo uusi verkkosivu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Create a link to target this section"
msgstr "Luo linkki kohdistaaksesi tähän osioon"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Create new"
msgstr "Luo uusi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Create your page from scratch by dragging and dropping pre-made,\n"
"                            fully customizable building blocks."
msgstr ""
"Luo sivusi tyhjästä vetämällä ja pudottamalla valmiita,\n"
"                            täysin muokattavissa olevat rakennuspalikat."

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__create_uid
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__create_uid
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__create_uid
#: model:ir.model.fields,field_description:website.field_theme_website_menu__create_uid
#: model:ir.model.fields,field_description:website.field_theme_website_page__create_uid
#: model:ir.model.fields,field_description:website.field_website__create_uid
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__create_uid
#: model:ir.model.fields,field_description:website.field_website_menu__create_uid
#: model:ir.model.fields,field_description:website.field_website_page__create_uid
#: model:ir.model.fields,field_description:website.field_website_rewrite__create_uid
#: model:ir.model.fields,field_description:website.field_website_robots__create_uid
#: model:ir.model.fields,field_description:website.field_website_route__create_uid
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__create_uid
#: model:ir.model.fields,field_description:website.field_website_visitor__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Created in 2021, the company is young and dynamic. Discover the composition "
"of the team and their skills."
msgstr ""
"Vuonna 2021 perustettu yritys on nuori ja dynaaminen. Tutustu tiimin "
"kokoonpanoon ja osaamiseen."

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__create_date
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__create_date
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__create_date
#: model:ir.model.fields,field_description:website.field_theme_website_menu__create_date
#: model:ir.model.fields,field_description:website.field_theme_website_page__create_date
#: model:ir.model.fields,field_description:website.field_website__create_date
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__create_date
#: model:ir.model.fields,field_description:website.field_website_menu__create_date
#: model:ir.model.fields,field_description:website.field_website_page__create_date
#: model:ir.model.fields,field_description:website.field_website_rewrite__create_date
#: model:ir.model.fields,field_description:website.field_website_robots__create_date
#: model:ir.model.fields,field_description:website.field_website_route__create_date
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__create_date
msgid "Created on"
msgstr "Luotu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#, python-format
msgid "Custom"
msgstr "Mukautettu"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__custom_code_head
msgid "Custom <head> code"
msgstr "Kustomointi <head> koodi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "Custom Code"
msgstr "Kustomoitu koodi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Custom Key"
msgstr "Mukautettu avain"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Custom Url"
msgstr "Mukautettu URL"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__custom_code_footer
msgid "Custom end of <body> code"
msgstr "Mukautettu loppu <body> koodille"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Custom end of body code"
msgstr "Muokattu runkokoodin loppu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Custom field"
msgstr "Räätälöity kenttä"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Custom head code"
msgstr "Mukautettu pääkoodi"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__auth_signup_uninvited
#: model:ir.model.fields,field_description:website.field_website__auth_signup_uninvited
msgid "Customer Account"
msgstr "Asiakastili"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Customers"
msgstr "Asiakkaat"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Customization tool"
msgstr "Mukautustyökalu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Customize"
msgstr "Mukauta"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__customize_show
msgid "Customize Show"
msgstr "Mukauta näkymää"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "D - H - M"
msgstr "Päivä - Tunti - Minuutti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "D - H - M - S"
msgstr "Päivä - Tunti - Minuutti - Sekunti"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "DRAG BUILDING BLOCKS HERE"
msgstr "RAAHAA RAKENNE-ELEMENTTEJÄ TÄHÄN"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
msgid "Danger"
msgstr "Vaara"

#. module: website
#: model:ir.ui.menu,name:website.menu_dashboard
msgid "Dashboard"
msgstr "Työpöytä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_line_widgets
msgid "Dashed"
msgstr "Katkoviiva"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Data"
msgstr "Data"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Data Border"
msgstr "Datareunus"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Data Color"
msgstr "Datan väri"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Dataset Border"
msgstr "Tietojoukon reunus"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Dataset Color"
msgstr "Tietojoukon väri"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
msgid "Date"
msgstr "Päivämäärä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Date &amp; Time"
msgstr "Päivämäärä ja aika"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#, python-format
msgid "Days"
msgstr "Päivää"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Decimal Number"
msgstr "Desimaalinumero"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Default"
msgstr "Oletus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Default Access Rights"
msgstr "Oletuskäyttöoikeudet"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__default_lang_id
msgid "Default Language"
msgstr "Oletuskieli"

#. module: website
#: model:website.menu,name:website.main_menu
msgid "Default Main Menu"
msgstr "Oletuspäävalikko"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Default Reversed"
msgstr "Käänteinen oletus"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_default_image
#: model:ir.model.fields,field_description:website.field_website__social_default_image
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Default Social Share Image"
msgstr "Oletus sosiaalisen median jakokuva"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Default Value"
msgstr "Oletusarvo"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_default_lang_id
msgid "Default language"
msgstr "Oletuskieli"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_default_lang_code
msgid "Default language code"
msgstr "Oletuskielikoodi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Delay"
msgstr "Viive"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Delete Blocks"
msgstr "Poista lohkoja"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Delete Menu Item"
msgstr "Poista valikkoelementti"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Delete Page"
msgstr "Poista sivu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"Delete the above image or replace it with a picture that illustrates your "
"message. Click on the picture to change its <em>rounded corner</em> style."
msgstr ""
"Poista yllä oleva kuva tai korvaa se kuvalla, joka havainnollistaa "
"viestiäsi. Klikkaa kuvaa muuttaaksesi sen <em>pyöristetyn kulman</em> "
"tyyliä."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Delete this font"
msgstr "Poista tämä fontti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Delete this page"
msgstr "Poista tämä sivu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"Deleting a font requires a reload of the page. This will save all your "
"changes and reload the page, are you sure you want to proceed?"
msgstr ""
"Kirjasimen poistaminen vaatii sivun uudelleenlatauksen. Tämä tallentaa "
"kaikki tekemäsi muutokset ja lataa sivun uudelleen. Haluatko varmasti "
"jatkaa?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "Deliveries"
msgstr "Toimitukset"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "Departments"
msgstr "Osastot"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Dependencies"
msgstr "Riippuvuudet"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "Describe your field here."
msgstr "Kuvaile alaa tässä."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__description
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
#, python-format
msgid "Description"
msgstr "Kuvaus"

#. module: website
#: model:website.configurator.feature,description:website.feature_page_our_services
msgid "Description of your services offer"
msgstr "Palvelutarjontanne kuvaus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog_options
msgid "Descriptions"
msgstr "Kuvaukset"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Descriptive"
msgstr "Kuvaava"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Design"
msgstr "Suunnittelu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Design features"
msgstr "Suunnitteluominaisuuksia"

#. module: website
#: model:website.configurator.feature,description:website.feature_page_pricing
msgid "Designed to drive conversion"
msgstr "Suunniteltu avustamaan konversiota"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#, python-format
msgid "Desktop"
msgstr "Työpöytä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Desktop computers"
msgstr "Pöytätietokoneet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Detail"
msgstr "Yksityiskohta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Details"
msgstr "Tiedot"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Detect"
msgstr "Tunnista"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Direction"
msgstr "Suunta"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__directive
msgid "Directive"
msgstr "Direktiivi"

#. module: website
#: model:ir.actions.server,name:website.website_disable_unused_snippets_assets_ir_actions_server
#: model:ir.cron,cron_name:website.website_disable_unused_snippets_assets
#: model:ir.cron,name:website.website_disable_unused_snippets_assets
msgid "Disable unused snippets assets"
msgstr "Poista käytöstä käyttämättömät snippetit"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Disabled"
msgstr "Pois käytöstä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Disappearing"
msgstr "Katoava"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Disappears"
msgstr "Katoaa"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/js/menu/content.js:0
#: code:addons/website/static/src/js/menu/seo.js:0
#: code:addons/website/static/src/snippets/s_embed_code/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Discard"
msgstr "Hylkää"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Discard & Edit in backend"
msgstr "Hylkää ja muokkaa taustaohjelmassa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Discover"
msgstr "Löydä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "Discover all the features"
msgstr "Löydä kaikki ominaisuudet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
msgid "Discover more"
msgstr "Löydä lisää"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Discover our culture and our values"
msgstr "Tutustu kulttuuriimme ja arvoihimme"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Discover our legal notice"
msgstr "Tutustu oikeudelliseen ilmoitukseemme"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Discover our realisations"
msgstr "Tutustu toteutuksiimme"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid "Discover our team"
msgstr "Tutustu tiimiimme"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Discrete"
msgstr "Diskreetti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Discussion Group"
msgstr "Keskusteluryhmä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_share_options
msgid "Disk"
msgstr "Kiekko"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Display"
msgstr "Näytä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "Display Inline"
msgstr "Näytä upotettuna"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__display_name
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__display_name
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__display_name
#: model:ir.model.fields,field_description:website.field_theme_website_menu__display_name
#: model:ir.model.fields,field_description:website.field_theme_website_page__display_name
#: model:ir.model.fields,field_description:website.field_website__display_name
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__display_name
#: model:ir.model.fields,field_description:website.field_website_menu__display_name
#: model:ir.model.fields,field_description:website.field_website_page__display_name
#: model:ir.model.fields,field_description:website.field_website_rewrite__display_name
#: model:ir.model.fields,field_description:website.field_website_robots__display_name
#: model:ir.model.fields,field_description:website.field_website_route__display_name
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__display_name
#: model:ir.model.fields,field_description:website.field_website_track__display_name
#: model:ir.model.fields,field_description:website.field_website_visitor__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_cookies_bar
#: model:ir.model.fields,help:website.field_website__cookies_bar
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Display a customizable cookies bar on your website."
msgstr "Näytä mukautettava evästepalkki verkkosivustollasi."

#. module: website
#: code:addons/website/models/ir_qweb_fields.py:0
#, python-format
msgid "Display the badges"
msgstr "Näytä ansiomerkit"

#. module: website
#: code:addons/website/models/ir_qweb_fields.py:0
#, python-format
msgid "Display the biography"
msgstr "Näytä kuvaus"

#. module: website
#: code:addons/website/models/ir_qweb_fields.py:0
#, python-format
msgid "Display the website description"
msgstr "Näytä verkkosivun kuvaus"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_logo
#: model:ir.model.fields,help:website.field_website__logo
msgid "Display this logo on the website."
msgstr "Näytä logo verkkosivuilla"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Display this website when users visit this domain"
msgstr "Näytä tämä verkkosivu kun kävijä tulee tähän verkkotunnukseen"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/res_config_settings.js:0
#, python-format
msgid "Do not activate"
msgstr "Älä aktivoi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Do you need specific information? Our specialists will help you with "
"pleasure."
msgstr ""
"Tarvitsetko erityisiä tietoja? Asiantuntijamme auttavat sinua mielellään."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Do you want to edit the company data ?"
msgstr "Haluatko muokata yrityksen tietoja?"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Do you want to install the \"%s\" App?"
msgstr "Haluatko asentaa \"%s\" sovelluksen?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Documentation"
msgstr "Dokumentaatio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Doesn't contain"
msgstr "Ei sisällä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Domain"
msgstr "Verkkotunnus"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "Don't forget to update all links referring to this page."
msgstr "Muista päivittää kaikki linkit jotka viittaavat tähän sivuun."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/theme_preview_kanban.js:0
#, python-format
msgid "Don't worry, you can switch later."
msgstr "Älä huoli, voit vaihtaa myöhemmin."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Donation"
msgstr "Lahjoitus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Donation Button"
msgstr "Lahjoituspainike"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Dots"
msgstr "Pisteet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_line_widgets
msgid "Dotted"
msgstr "Pisteviiva"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_line_widgets
msgid "Double"
msgstr "Tuplaviiva"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Double click an icon to replace it with one of your choice."
msgstr "Tuplaklikkaa ikonia korvataksesi sen valitsemallasi ikonilla."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Doughnut"
msgstr "Donitsi"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid ""
"Drag the <b>%s</b> building block and drop it at the bottom of the page."
msgstr "Vedä <b>%s</b> rakennuspalikka ja pudota se sivun alareunaan."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Drag to the right to get a submenu"
msgstr "Raahaa oikealle saadaksesi alavalikon"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Dresses"
msgstr "Mekot"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Dropdown"
msgstr "Alasveto"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Dropdown menu"
msgstr "Alasvetovalikko"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Due Date"
msgstr "Eräpäivä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Duplicate"
msgstr "Kopioi"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Duplicate Page"
msgstr "Kopioi sivu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Duplicate blocks <br/>to add more steps."
msgstr "Kopioi palikat <br/>lisätäksesi lisää vaiheita."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Duplicate blocks and columns to add more features."
msgstr "Kahdenna lohkoja ja sarakkeita lisätäksesi ominaisuuksia."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Dynamic Content"
msgstr "Dynaaminen sisältö"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_domain
#: model:ir.model.fields,help:website.field_website__domain
msgid "E.g. https://www.mydomain.com"
msgstr "Esim. https://www.minunverkkosivuni.fi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Easily design your own Odoo templates thanks to clean HTML\n"
"                            structure and bootstrap CSS."
msgstr ""
"Suunnittele helposti omia Odoo-mallejasi siistin HTML\n"
"                            rakenteen ja bootstrap CSS:n avulla."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#, python-format
msgid "Edit"
msgstr "Muokkaa"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#: code:addons/website/static/src/js/widgets/link_popover_widget.js:0
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Edit Menu"
msgstr "Muokkaa valikkoa"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Edit Menu Item"
msgstr "Muokkaa valikkoelementtiä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Edit Message"
msgstr "Muokkaa viestiä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Edit Styles"
msgstr "Muokkaa tyylejä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Edit Top Menu"
msgstr "Muokkaa päävalikkoa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Edit code in backend"
msgstr "Muokkaa koodia hallinnassa."

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_embed_code/options.js:0
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#, python-format
msgid "Edit embedded code"
msgstr "Muokkaa upotettua koodia"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Edit in backend"
msgstr "Muokkaa hallinnassa"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Edit my Analytics Client ID"
msgstr "Muokkaa Analytics Client ID:tä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Edit robots.txt"
msgstr "Muokkaa robots.txt tiedostoa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Edit video"
msgstr "Muokkaa videota"

#. module: website
#: model:res.groups,name:website.group_website_designer
msgid "Editor and Designer"
msgstr "Muokkaaja ja suunnittelija"

#. module: website
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "Either action_server_id or filter_id must be provided."
msgstr "Joko action_server_id tai filter_id on annettava."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Electronics"
msgstr "Elektroniikka"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__email
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
msgid "Email"
msgstr "Sähköposti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Email address"
msgstr "Sähköpostiosoite"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Email support"
msgstr "Sähköpostituki"

#. module: website
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "Empty field name in %r"
msgstr "Tyhjä kentän nimi %r:ssä"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Enable billing on your Google Project"
msgstr "Ota laskutus käyttöön Google-projektissasi"

#. module: website
#: model:ir.model.fields,help:website.field_ir_model__website_form_access
msgid "Enable the form builder feature for this model."
msgstr "Salli lomakkeiden rakentaminen tälle mallille."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Enable the right google map APIs in your google account"
msgstr "Ota oikeat Google Map API:t käyttöön Google-tililläsi"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Enter an API Key"
msgstr "Syötä API-avain"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"Enter code that will be added before the </body> of every page of your site."
msgstr ""
"Anna koodi, joka lisätään sivustollesi jokaisen sivun </body> tagin eteen."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Enter code that will be added into every page of your site"
msgstr "Anna koodi, joka lisätään sivustosi jokaiselle sivulle"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"Enter code that will be added into the <head> of every page of your site."
msgstr ""
"Anna koodi, joka lisätään sivustosi jokaiselle sivulle <head> tagin sisään."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Enter email"
msgstr "Lisää sähköpostiosoite"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Equal Widths"
msgstr "Yhtä leveät"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form.xml:0
#: code:addons/website/static/src/xml/website_form.xml:0
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Error"
msgstr "Virhe"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Essential oils"
msgstr "Eteeriset öljyt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Event"
msgstr "Tapahtuma"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Event heading"
msgstr "Tapahtuman otsikko"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_event
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Events"
msgstr "Tapahtumat"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Every Time"
msgstr "Joka kerta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Everything"
msgstr "Kaikki"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Examples"
msgstr "Esimerkkejä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Expert"
msgstr "Asiantuntija"

#. module: website
#: model:website.configurator.feature,description:website.feature_page_privacy_policy
msgid "Explain how you protect privacy"
msgstr "Selitä, miten suojaat yksityisyyttä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert
msgid ""
"Explain the benefits you offer. <br/>Don't write about products or services "
"here, write about solutions."
msgstr ""
"Selitä tarjoamasi edut. <br/>Älä kirjoita tänne tuotteista tai palveluista, "
"kirjoita ratkaisuista."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Explore"
msgstr "Tutki"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__cache_key_expr
msgid ""
"Expression (tuple) to evaluate the cached key. \n"
"E.g.: \"(request.params.get(\"currency\"), )\""
msgstr ""
"Ilmaisu (tuple) välimuistissa olevan avaimen laskemiseksi. \n"
"Esim.: \"(request.params.get(\"currency\"), )\""

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_ui_view__mode__extension
msgid "Extension View"
msgstr "Lisäosan näkymä"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_actions_server__xml_id
#: model:ir.model.fields,field_description:website.field_ir_cron__xml_id
#: model:ir.model.fields,field_description:website.field_website_page__xml_id
msgid "External ID"
msgstr "Ulkoinen ID"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Extra Large"
msgstr "Exstra suuri"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Extra link"
msgstr "Lisälinkki"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Extra-Large"
msgstr "Erittäin suuri"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Extra-Small"
msgstr "Extra pieni"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "F.A.Q."
msgstr "UKK"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Facebook"
msgstr "Facebook"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_facebook
#: model:ir.model.fields,field_description:website.field_website__social_facebook
msgid "Facebook Account"
msgstr "Facebook-tili"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade"
msgstr "Häivytä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade In"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade In-Down"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade In-Left"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade In-Right"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade In-Up"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade Out"
msgstr "Häivytä pois"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Failed to install \"%s\""
msgstr "Asennus epäonnistui: \"%s\""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Farm Friendly Chicken Supreme"
msgstr "Farm Friendly Chicken Supreme"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__favicon
msgid "Favicon"
msgstr "Favicon"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature One"
msgstr "Ominaisuus yksi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature Three"
msgstr "Ominaisuus kolme"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card
msgid "Feature Title"
msgstr "Ominaisuuden otsikko"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature Two"
msgstr "Ominaisuus kaksi"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__feature_url
msgid "Feature Url"
msgstr "Ominaisuuden Url"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.snippets
#, python-format
msgid "Features"
msgstr "Ominaisuudet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
msgid "Fetched elements"
msgstr "Haettu elementit"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__field_names
msgid "Field Names"
msgstr "Kenttien nimet"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model__website_form_default_field_id
msgid "Field for custom form data"
msgstr "Kenttien nimet"

#. module: website
#: model:ir.model,name:website.model_ir_model_fields
msgid "Fields"
msgstr "Kentät"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "File Upload"
msgstr "Tiedoston lähetys"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch_fs
msgid ""
"File from where the view originates.\n"
"                                                          Useful to (hard) reset broken views or to read arch from file in dev-xml mode."
msgstr ""
"Tiedosto, josta näkymä on peräisin.\n"
"                                                          Hyödyllinen (kova) nollaamaan rikkoutuneita näkymiä tai lukemaan kaaria tiedostosta dev-xml-tilassa."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Filet Mignon 8oz"
msgstr "Filet Mignon 225g"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fill"
msgstr "Täytä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Fill and justify"
msgstr "Täytä ja tasaa"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__filter_id
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
msgid "Filter"
msgstr "Suodatin"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Filter Intensity"
msgstr "Suodattimen voimakkuus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Find a store near you"
msgstr "Etsi myymälä lähelläsi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Find all information about our deliveries, express deliveries and all you "
"need to know to return a product."
msgstr ""
"Löydät kaikki tiedot toimituksistamme, pikatoimituksista ja kaikesta, mitä "
"sinun on tiedettävä tuotteen palauttamisesta."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Find out how we were able helping them and set in place solutions adapted to"
" their needs."
msgstr ""
"Lue, miten pystyimme auttamaan heitä ja luomaan heidän tarpeisiinsa sopivia "
"ratkaisuja."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Find the perfect solution for you"
msgstr "Löydä täydellinen ratkaisu sinulle"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__create_date
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "First Connection"
msgstr "Ensimmäinen yhteys"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "First Feature"
msgstr "Ensimmäinen ominaisuus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "First Menu"
msgstr "Ensimmäinen valikko"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "First Time Only"
msgstr "Vain ensimmäistä kertaa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "First feature"
msgstr "Ensimmäinen ominaisuus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "First list of Features"
msgstr "Ensimmäinen ominaisuuslista"

#. module: website
#: model:ir.model.fields,help:website.field_ir_ui_view__first_page_id
#: model:ir.model.fields,help:website.field_website_page__first_page_id
msgid "First page linked to this view"
msgstr "Ensimmäinen tähän näkymään linkitetty sivu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fit content"
msgstr "Sovita sisältö"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fit text"
msgstr "Sovita teksti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Fixed"
msgstr "Kiinteä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flag"
msgstr "Lippu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flag and Text"
msgstr "Lippu ja teksti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flash"
msgstr "Vilkkuva"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "Flat"
msgstr "Litteä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flip-In-X"
msgstr "Käännä-X"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flip-In-Y"
msgstr "Käännä-Y"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Float"
msgstr "Liukuluku"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
#: model_terms:ir.ui.view,arch_db:website.template_header_boxed_oe_structure_header_boxed_1
#: model_terms:ir.ui.view,arch_db:website.template_header_hamburger_full_oe_structure_header_hamburger_full_1
#: model_terms:ir.ui.view,arch_db:website.template_header_hamburger_oe_structure_header_hamburger_3
#: model_terms:ir.ui.view,arch_db:website.template_header_sidebar_oe_structure_header_sidebar_1
#: model_terms:ir.ui.view,arch_db:website.template_header_vertical_oe_structure_header_vertical_1
msgid "Follow us"
msgstr "Seuraa meitä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Follow your website traffic in Odoo."
msgstr "Seuraa verkkosivun käyttöä Odoossa."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Font"
msgstr "Kirjasinlaji"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Font Size"
msgstr "Fontin koko"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Font family"
msgstr "Fonttiperhe"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Font size"
msgstr "Fonttikoko"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__footer_visible
msgid "Footer Visible"
msgstr "Alatunniste näkyvissä"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.res_config_settings.xml:0
#, python-format
msgid "For session cookies, authentification and analytics,"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Force your user to create an account per website"
msgstr "Pakota käyttäjät luomaan sivukohtainen käyttäjätunnus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Form"
msgstr "Lomake"

#. module: website
#: model:ir.model.fields,help:website.field_ir_model__website_form_label
msgid ""
"Form action label. Ex: crm.lead could be 'Send an e-mail' and project.issue "
"could be 'Create an Issue'."
msgstr ""
"Lomakkeen toimintatunniste. Esim: crm.lead voisi olla 'Lähetä sähköpostia' "
"ja project.issue voisi olla 'Luo häiriö'."

#. module: website
#: model:website.configurator.feature,name:website.feature_module_forum
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Forum"
msgstr "Keskustelupalsta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Founder and chief visionary, Tony is the driving force behind the company. He loves\n"
"                                to keep his hands full by participating in the development of the software,\n"
"                                marketing, and customer experience strategies."
msgstr ""
"Perustaja ja päävisionääri Tony on yrityksen liikkeellepaneva voima. Hän rakastaa\n"
"                                pitää kätensä täynnä osallistumalla ohjelmiston kehittämiseen,\n"
"                                markkinointiin ja asiakaskokemusstrategiointiin."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Framed"
msgstr "Kehystetty"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website__auth_signup_uninvited__b2c
msgid "Free sign up"
msgstr "Vapaa rekisteröityminen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
msgid "Friends' Faces"
msgstr "Ystävien kasvot"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"From seminars to team building activities, we offer a wide choice of events "
"to organize."
msgstr ""
"Tarjoamme laajan valikoiman järjestettäviä tapahtumia seminaareista "
"tiiminrakennustapahtumiin."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Full"
msgstr "Täysi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Full Screen"
msgstr "Kokosivu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Full Width"
msgstr "Täysi leveys"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Full screen"
msgstr "Koko näyttö"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Full-Width"
msgstr "Täysi leveys"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Furniture"
msgstr "Huonekalut"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#, python-format
msgid "G-XXXXXXXXXX"
msgstr "G-XXXXXXXXXXXXXX"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "GPS &amp; navigation"
msgstr "GPS-navigointi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Gaming"
msgstr "Pelaaminen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Get Delivered"
msgstr "Saa toimitettuna"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Get access to all modules"
msgstr "Hanki pääsy kaikkiin moduuleihin"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Get access to all modules and features"
msgstr "Hanki pääsy kaikkiin moduuleihin ja ominaisuuksiin"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Get in touch"
msgstr "Ota yhteyttä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "GitHub"
msgstr "GitHub"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_github
#: model:ir.model.fields,field_description:website.field_website__social_github
msgid "GitHub Account"
msgstr "Github-tili"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_forum
msgid "Give visitors the information they need"
msgstr "Anna kävijöille heidän tarvitsemansa tiedot"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Glasses"
msgstr "Lasit"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Go To Page"
msgstr "Siirry sivulle"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Go to"
msgstr "Siirry"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_form_extend
msgid "Go to Page Manager"
msgstr "Siirry sivuhallintaan"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#: code:addons/website/static/src/xml/website.backend.xml:0
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#, python-format
msgid "Go to Website"
msgstr "Siirry verkkosivustolle"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "Go to the Theme tab"
msgstr "Siirry Teema-välilehdelle"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"Good copy starts with understanding how your product or service helps your "
"customers. Simple words communicate better than big words and pompous "
"language."
msgstr ""
"Hyvä tekstisuunnittelu alkaa ymmärtämällä, kuinka tuotteesi tai palvelusi "
"auttaa asiakkaitasi. Yksinkertaiset sanat viestivät paremmin kuin pitkät "
"sanat ja mahtipontinen kieli."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "Good job! It's time to <b>Save</b> your work."
msgstr "Hyvää työtä! On aika <b>Tallentaa</b> työsi."

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_google_analytics
msgid "Google Analytics"
msgstr "Google Analytics"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_google_analytics_dashboard
msgid "Google Analytics Dashboard"
msgstr "Google Analytics -työpöytä"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_analytics_key
#: model:ir.model.fields,field_description:website.field_website__google_analytics_key
msgid "Google Analytics Key"
msgstr "Google Analytics -avain"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid ""
"Google Analytics initialization failed. Maybe this domain is not whitelisted"
" in your Google Analytics project for this client ID."
msgstr ""
"Google Analyticsin alustus epäonnistui. Ehkä tätä verkkotunnusta ei ole "
"lisätty sallittujen luetteloon Google Analytics -projektissasi tälle "
"asiakastunnukselle."

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_management_client_id
#: model:ir.model.fields,field_description:website.field_website__google_management_client_id
msgid "Google Client ID"
msgstr "Google Client ID"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_management_client_secret
#: model:ir.model.fields,field_description:website.field_website__google_management_client_secret
msgid "Google Client Secret"
msgstr "Google Client Secret"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Google Font address"
msgstr "Google-fontin osoite"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Google Map"
msgstr "Google kartta"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Google Map API Key"
msgstr "Google kartan API-avain"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_google_maps
msgid "Google Maps"
msgstr "Google Maps"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_maps_api_key
#: model:ir.model.fields,field_description:website.field_website__google_maps_api_key
msgid "Google Maps API Key"
msgstr "Google Maps API Key"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_search_console
#: model:ir.model.fields,field_description:website.field_website__google_search_console
msgid "Google Search Console"
msgstr "Google Search Console"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid ""
"Google deprecated both its \"Universal Analytics\" and \"Google Sign-In\" "
"API. It means that only accounts and keys created before 2020 will be able "
"to integrate their Analytics dashboard in Odoo (or any other website). This "
"will be possible only up to mid 2023. After that, those services won't work "
"anymore, at all."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__google_search_console
#: model:ir.model.fields,help:website.field_website__google_search_console
msgid "Google key, or Enable to access first reply"
msgstr "Google avain, tai ota käyttöön pääsy ensimmäiseen vastaukseen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Gray #{grayCode}"
msgstr "Harmaa #{grayCode}"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Grays"
msgstr "Grays"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Great Value"
msgstr "Suuri arvo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid ""
"Great stories are <b>for everyone</b> even when only written <b>for just one"
" person</b>. If you try to write with a wide, general audience in mind, your"
" story will sound fake and lack emotion. No one will be interested. Write "
"for one person. If it’s genuine for the one, it’s genuine for the rest."
msgstr ""
"Hienot tarinat <b>sopivat kaikille</b>, vaikka ne olisi kirjoitettu <b>vain "
"yhdelle henkilölle</b>. Jos yrität kirjoittaa laajalle yleisölle, tarinasi "
"kuulostaa valheelta ja siitä puuttuu tunteita. Ketään ei kiinnosta. Kirjoita"
" yhdelle henkilölle. Jos se on aito yhdelle, se on aito muille."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid ""
"Great stories have a <b>personality</b>. Consider telling a great story that"
" provides personality. Writing a story with personality for potential "
"clients will assist with making a relationship connection. This shows up in "
"small quirks like word choices or phrases. Write from your point of view, "
"not from someone else's experience."
msgstr ""
"Hienoilla tarinoilla on <b>persoonallisuus</b>. Harkitse hienon tarinan "
"kertomista, joka luo persoonallisuutta. Persoonallisen tarinan "
"kirjoittaminen potentiaalisille asiakkaille auttaa suhteiden luomisessa. "
"Tämä näkyy pienissä omituisuuksissa, kuten sanavalinnoissa tai lauseissa. "
"Kirjoita omasta näkökulmastasi, älä jonkun muun kokemuksesta."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Grid"
msgstr "Taulukko"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Group By"
msgstr "Ryhmittely"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__groups_id
msgid "Groups"
msgstr "Ryhmät"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "H1"
msgstr "H1"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "H2"
msgstr "H2"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "H4 Card title"
msgstr "H4 kortin otsikko"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "H5 Card subtitle"
msgstr "H5 kortin alaotsikko"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "HTML/CSS/JS Editor"
msgstr "HTML/CSS/JS-editori"

#. module: website
#: model:ir.model,name:website.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP-reititys"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Half Screen"
msgstr "Puoli näyttöä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Half screen"
msgstr "Puoli näyttöä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Hamburger Full"
msgstr "Hampurilainen täysi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Hamburger Type"
msgstr "Hampurilaisen tyyppi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Hamburger menu"
msgstr "Hampurilaisvalikko"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
#, python-format
msgid "Happy Odoo Anniversary!"
msgstr "Hyvää Odoon vuosipäivää!"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__has_social_default_image
msgid "Has Social Default Image"
msgstr "Oletus sosiaalisen median jakokuva"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Header"
msgstr "Ylätunniste"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__header_color
msgid "Header Color"
msgstr "Otsikon väri"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__header_overlay
msgid "Header Overlay"
msgstr "Otsikon päällys"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Header Position"
msgstr "Otsikon sijainti"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__header_visible
msgid "Header Visible"
msgstr "Otsikko näkyvissä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 1"
msgstr "Otsikot 1"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 2"
msgstr "Otsikot 2"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 3"
msgstr "Otsikot 3"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 4"
msgstr "Otsikot 4"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 5"
msgstr "Otsikot 5"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 6"
msgstr "Otsikot 6"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Headline"
msgstr "Otsikko"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Height"
msgstr "Korkeus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Height (Scrolled)"
msgstr "Korkeus (vieritetty)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Help center"
msgstr "Ohjekeskus"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/translator.xml:0
#, python-format
msgid "Here are the visuals used to help you translate efficiently:"
msgstr "Tässä on käytetyt visuaalit, jotka auttavat käännöstyössä:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Here is an overview of the cookies that may be stored on your device when "
"you visit our website:"
msgstr ""
"Seuraavassa on yleiskatsaus evästeistä, joita saatetaan tallentaa "
"laitteeseesi, kun vierailet verkkosivustollamme:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Hidden"
msgstr "Piilotettu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_conditional_visibility
msgid "Hidden for"
msgstr "Piilotettu seuraavilta"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Hidden on mobile"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Hide"
msgstr "Piilota"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Hide For"
msgstr "Piilota seuraavilta"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Hide this page from search results"
msgstr "Piilota tämä sivu hakutuloksista"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "High"
msgstr "Korkea"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"Hint: How to use Google Map on your website (Contact Us page and as a "
"snippet)"
msgstr ""
"Vihje: Google-kartan käyttäminen verkkosivustollasi (Yhteystiedot-sivulla ja"
" snippettinä)"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"Hint: Type '/' to search an existing page and '#' to link to an anchor."
msgstr ""
"Vihje: Kirjoita '/' hakeaksesi olemassa olevaa sivua ja '#' linkittääksesi "
"ankkuriin."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#: model:website.menu,name:website.menu_home
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_tabs
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Home"
msgstr "Etusivu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Home <span class=\"sr-only\">(current)</span>"
msgstr "Koti <span class=\"sr-only\">(nykyinen)</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Home audio"
msgstr "Äänentoisto kotona"

#. module: website
#. openerp-web
#: code:addons/website/models/website.py:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model:ir.model.fields,field_description:website.field_website__homepage_id
#: model:ir.model.fields,field_description:website.field_website_page__is_homepage
#: model_terms:ir.ui.view,arch_db:website.one_page_line
#, python-format
msgid "Homepage"
msgstr "Verkkosivu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Hoodies"
msgstr "Hupparit"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Horizontal"
msgstr "Vaakataso"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#, python-format
msgid "Hours"
msgstr "Tunnit"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "How can we help?"
msgstr "How can we help?"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "How to get my Client ID"
msgstr "Kuinka saada Client ID"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "How to get my Measurement ID"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "Hybrid"
msgstr "Hybridi"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: model_terms:ir.ui.view,arch_db:website.cookies_bar
#, python-format
msgid "I agree"
msgstr "Hyväksyn"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "I want"
msgstr "Haluan"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__id
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__id
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__id
#: model:ir.model.fields,field_description:website.field_theme_website_menu__id
#: model:ir.model.fields,field_description:website.field_theme_website_page__id
#: model:ir.model.fields,field_description:website.field_website__id
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__id
#: model:ir.model.fields,field_description:website.field_website_menu__id
#: model:ir.model.fields,field_description:website.field_website_page__id
#: model:ir.model.fields,field_description:website.field_website_rewrite__id
#: model:ir.model.fields,field_description:website.field_website_robots__id
#: model:ir.model.fields,field_description:website.field_website_route__id
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__id
#: model:ir.model.fields,field_description:website.field_website_track__id
#: model:ir.model.fields,field_description:website.field_website_visitor__id
msgid "ID"
msgstr "Tunniste (ID)"

#. module: website
#: model:ir.model.fields,help:website.field_ir_actions_server__xml_id
#: model:ir.model.fields,help:website.field_ir_cron__xml_id
msgid "ID of the action if defined in a XML file"
msgstr "Toiminnon ID, jos on määritetty XML-tiedostossa"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__xml_id
msgid "ID of the view defined in xml file"
msgstr "XML tiedostossa määritelty näkymän ID"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__iap_page_code
msgid "Iap Page Code"
msgstr "Iap Sivukoodi"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__icon
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "Icon"
msgstr "Kuvake"

#. module: website
#: model:ir.model.fields,help:website.field_website__specific_user_account
msgid "If True, new accounts will be associated to the current website"
msgstr "Jos tosi, uudet tilit liittyvät nykyseen verkkosivuun"

#. module: website
#: model:ir.model.fields,help:website.field_website_configurator_feature__menu_sequence
msgid "If set, a website menu will be created for the feature."
msgstr ""
"Jos tämä asetetaan, ominaisuutta varten luodaan verkkosivuston valikko."

#. module: website
#: model:ir.model.fields,help:website.field_website_configurator_feature__menu_company
msgid ""
"If set, add the menu as a second level menu, as a child of \"Company\" menu."
msgstr ""
"Jos valikko on määritetty, lisätään toisen tason valikkona, joka on "
"\"Yritys\"-valikon alivalikko."

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__social_default_image
#: model:ir.model.fields,help:website.field_website__social_default_image
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "If set, replaces the website logo as the default social share image."
msgstr ""
"Jos asetetaan, korvaa verkkosivuston logon oletusarvoisena sosiaaliseen "
"mediaan jakamisen kuvana."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid ""
"If this error is caused by a change of yours in the templates, you have the "
"possibility to reset the template to its <strong>factory settings</strong>."
msgstr ""
"Jos tämä virhe johtuu siitä, että olet muuttanut malleja, voit palauttaa "
"mallin <strong>tehdasasetukset</strong>."

#. module: website
#: model:ir.model.fields,help:website.field_website_page__groups_id
msgid ""
"If this field is empty, the view applies to all users. Otherwise, the view "
"applies to the users of those groups only."
msgstr ""
"Jos tämä kenttä on tyhjä, niin näkymä on käytössä kaikille käyttäjille. "
"Muutoin on käytössä vain ryhmään kuuluville käyttäjille."

#. module: website
#: model:ir.model.fields,help:website.field_website_page__active
msgid ""
"If this view is inherited,\n"
"* if True, the view always extends its parent\n"
"* if False, the view currently does not extend its parent but can be enabled\n"
"         "
msgstr ""
"Jos tämä näkymä periytyy,\n"
"* jos True, näkymä laajentaa aina ylätason näkymänsä\n"
"* jos False, näkymä ei tällä hetkellä laajenna ylätasoaan, mutta se voidaan ottaa käyttöön\n"
"         "

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid ""
"If you discard the current edits, all unsaved changes will be lost. You can "
"cancel to return to edit mode."
msgstr ""
"Jos perut nykyiset muutokset, kaikki tallentamattomat muokkaukset katoavat. "
"Voit perua palataksesi muokkaustilaan."

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_image_gallery/options.js:0
#: model:ir.model.fields,field_description:website.field_website_visitor__partner_image
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_header_brand
#, python-format
msgid "Image"
msgstr "Kuva"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Image Cover"
msgstr "Kuvan kansi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Image Menu"
msgstr "Kuva-valikko"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "Image Size"
msgstr "Kuvan koko"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Image Text Image"
msgstr "Kuva Teksti Kuva"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Images"
msgstr "Kuvat"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Images Spacing"
msgstr "Kuvien väli"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Images Subtitles"
msgstr "Kuvien aliotsikot"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "In main menu"
msgstr "Päävalikossa"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "In the meantime we invite you to visit our"
msgstr "Sillä välin kutsumme teidät vierailemaan"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/translator.xml:0
#, python-format
msgid ""
"In this mode, you can only translate texts. To change the structure of the page, you must edit the master page.\n"
"        Each modification on the master page is automatically applied to all translated versions."
msgstr ""

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__include
msgid "Include"
msgstr "Sisällytä"

#. module: website
#: code:addons/website/controllers/backend.py:0
#, python-format
msgid "Incorrect Client ID / Key"
msgstr "Virheellinen Client ID / Avain"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model_terms:ir.ui.view,arch_db:website.index_management
#: model_terms:ir.ui.view,arch_db:website.one_page_line
#, python-format
msgid "Indexed"
msgstr "Indeksoitu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Indicators"
msgstr "Indikaattorit"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Info"
msgstr "Info"

#. module: website
#: model:website.configurator.feature,description:website.feature_page_about_us
msgid "Info and stats about your company"
msgstr "Tietoa ja tilastoja yrityksestäsi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Information about the"
msgstr "Tietoa kohteesta"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__inherit_id
msgid "Inherit"
msgstr "Peri"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__inherit_id
msgid "Inherited View"
msgstr "Peritty näkymä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Inline"
msgstr "Linjassa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Inner"
msgstr "Sisäinen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Inner content"
msgstr "Sisäinen sisältö"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Input Aligned"
msgstr "Kohdistettu syöte"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Input Type"
msgstr "Syötteen tyyppi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Inputs"
msgstr "Syötteet"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a badge snippet."
msgstr "Lisää merkkiosio."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a blockquote snippet."
msgstr "Lisää lohkositaattikatkelma."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a card snippet."
msgstr "Aseta korttipätkä."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a chart snippet."
msgstr "Lisää kaavion katkelma."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a progress bar snippet."
msgstr "Lisää edistymispalkin pätkä."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a rating snippet."
msgstr "Lisää luokituspätkä."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a share snippet."
msgstr "Lisää osakkeen pätkä."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a text Highlight snippet."
msgstr "Lisää teksti Korosta pätkä."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert an alert snippet."
msgstr "Lisää hälytyspätkä."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert an horizontal separator sippet."
msgstr "Aseta vaakasuora erotinsiippi."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Insert text styles like headers, bold, italic, lists, and fonts with\n"
"                            a simple WYSIWYG editor. Flexible and easy to use."
msgstr ""
"Lisää tekstityylejä, kuten otsikoita, lihavointia, kursiivia, luetteloita ja fontteja, joissa on käytetty\n"
"                            yksinkertaisella WYSIWYG-editorilla. Joustava ja helppokäyttöinen."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Inset"
msgstr "Sisennä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Instagram"
msgstr "Instagram"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_instagram
#: model:ir.model.fields,field_description:website.field_website__social_instagram
msgid "Instagram Account"
msgstr "Instagram-tili"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Install"
msgstr "Asenna"

#. module: website
#: model:ir.model,name:website.model_base_language_install
msgid "Install Language"
msgstr "Asenna kieli"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Install new language"
msgstr "Asenna uusi kieli"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Installed Applications"
msgstr "Asennetut ohjelmistot"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Installed Localizations / Account Charts"
msgstr "Asennetut lokalisoinnit / tilikartat"

#. module: website
#: model:ir.model.fields,help:website.field_website__theme_id
msgid "Installed theme"
msgstr "Asennettu teema"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Installing \"%s\""
msgstr "Asennetaan \"%s\""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Interaction History"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Intuitive system"
msgstr "Intuitiivinen järjestelmä"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Invalid API Key. The following error was returned by Google:"
msgstr "Väärä API-avain. Google palautti seuraavan virheen:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Iris Joe, CFO"
msgstr "Iris Joe, CFO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Iris, with her international experience, helps us easily understand the "
"numbers and improves them. She is determined to drive success and delivers "
"her professional acumen to bring the company to the next level."
msgstr ""
"Kansainvälisen kokemuksensa ansiosta Iiris auttaa meitä ymmärtämään "
"numeroita ja kehittämään niitä. Hän pyrkii määrätietoisesti edistämään "
"menestystä ja tuo ammattitaitonsa avulla yrityksen seuraavalle tasolle."

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__website_indexed
msgid "Is Indexed"
msgstr "On indeksoitu"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_module_module__is_installed_on_current_website
msgid "Is Installed On Current Website"
msgstr "On asennettu nykyiselle verkkosivustolle"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__is_mega_menu
msgid "Is Mega Menu"
msgstr "On megavalikko"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_partner__is_published
#: model:ir.model.fields,field_description:website.field_res_users__is_published
#: model:ir.model.fields,field_description:website.field_website_page__is_published
#: model:ir.model.fields,field_description:website.field_website_published_mixin__is_published
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__is_published
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__is_published
#: model_terms:ir.ui.view,arch_db:website.website_pages_tree_view
msgid "Is Published"
msgstr "On julkaistu"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__is_visible
#: model:ir.model.fields,field_description:website.field_website_page__is_visible
msgid "Is Visible"
msgstr "On näkyvä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is after"
msgstr "On sen jälkeen, kun"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is after or equal to"
msgstr "On vähintään yhtä suuri kuin"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is before"
msgstr "On ennen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is before or equal to"
msgstr "On ennen tai yhtä suuri kuin"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is between (included)"
msgstr "On välillä (sisältyy)"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__is_connected
msgid "Is connected ?"
msgstr "On kirjautunut"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is equal to"
msgstr "On yhtä suuri kuin"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is greater than"
msgstr "On suurempi kuin"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is greater than or equal to"
msgstr "On suurempi tai yhtä suuri kuin"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is less than"
msgstr "On pienempi kuin"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is less than or equal to"
msgstr "On pienempi tai yhtä suuri kuin"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is not between (excluded)"
msgstr "Ei välissä (pois suljettu)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is not equal to"
msgstr "Ei ole yhtä suuri kuin"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#, python-format
msgid "Is not set"
msgstr "Ei ole asetettu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#, python-format
msgid "Is set"
msgstr "On asetettu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"It appears you are in debug=assets mode, all theme customization options "
"require a page reload in this mode."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid ""
"It appears your website is still using the old color system of\n"
"            Odoo 13.0 in some places. We made sure it is still working but\n"
"            we recommend you to try to use the new color system, which is\n"
"            still customizable."
msgstr ""
"Näyttää siltä, että sivustosi käyttää edelleen vanhaa värijärjestelmää, joka on\n"
"            Odoo 13.0:aa joissakin paikoissa. Varmistimme, että se toimii edelleen, mutta\n"
"            suosittelemme, että käytät uutta värijärjestelmää. Se on\n"
"            edelleen muokattavissa."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "It looks like your file is being called by"
msgstr "Näyttäisi että tiedostoa kutsuu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Item"
msgstr "Kohde"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Item 1"
msgstr "Elementti 1"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Item 2"
msgstr "Elementti 2"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options
msgid "Items per row"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.dynamic_snippet_carousel_options_template
msgid "Items per slide"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Jacket"
msgstr "Takki"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Jeans"
msgstr "Farkut"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Job Offer"
msgstr "Työtarjous"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
msgid "Join us and make your company a better place."
msgstr "Liity meihin ja tee yrityksestäsi parempi paikka."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Keep empty to use default value"
msgstr "Jätä tyhjäksi käyttääksesi oletusarvoa"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_asset__key
#: model:ir.model.fields,field_description:website.field_ir_attachment__key
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__key
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__key
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__key
#: model:ir.model.fields,field_description:website.field_website_page__key
msgid "Key"
msgstr "Avain"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Keyboards"
msgstr "Näppäimistöt"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Keyword"
msgstr "Avainsana"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Keywords"
msgstr "Avainsanat"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Label"
msgstr "Otsikko"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model__website_form_label
msgid "Label for form action"
msgstr "Lomakkeen toiminnon etiketti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Labels Width"
msgstr "Otsikkojen leveys"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__lang_id
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Language"
msgstr "Kieli"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Language Selector"
msgstr "Kielivalitsin"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__lang_id
msgid "Language from the website when visitor has been created"
msgstr "Verkkosivuston kieli, kun vierailija on luotu"

#. module: website
#: model:ir.model,name:website.model_res_lang
#: model:ir.model.fields,field_description:website.field_res_config_settings__language_ids
#: model:ir.model.fields,field_description:website.field_website__language_ids
msgid "Languages"
msgstr "Kielet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Languages available on your website"
msgstr "Sivustolla saatavilla olevat kielet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Laptops"
msgstr "Kannettavat tietokoneet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Large"
msgstr "Suuri"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Last 7 Days"
msgstr "Viimeiset 7 päivää"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Last Action"
msgstr "Viimeisin toimenpide"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__last_connection_datetime
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Last Connection"
msgstr "Viimeisin yhteys"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "Last Feature"
msgstr "Viimeinen ominaisuus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Last Menu"
msgstr "Viimeinen valikko"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset____last_update
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment____last_update
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view____last_update
#: model:ir.model.fields,field_description:website.field_theme_website_menu____last_update
#: model:ir.model.fields,field_description:website.field_theme_website_page____last_update
#: model:ir.model.fields,field_description:website.field_website____last_update
#: model:ir.model.fields,field_description:website.field_website_configurator_feature____last_update
#: model:ir.model.fields,field_description:website.field_website_menu____last_update
#: model:ir.model.fields,field_description:website.field_website_page____last_update
#: model:ir.model.fields,field_description:website.field_website_rewrite____last_update
#: model:ir.model.fields,field_description:website.field_website_robots____last_update
#: model:ir.model.fields,field_description:website.field_website_route____last_update
#: model:ir.model.fields,field_description:website.field_website_snippet_filter____last_update
#: model:ir.model.fields,field_description:website.field_website_track____last_update
#: model:ir.model.fields,field_description:website.field_website_visitor____last_update
msgid "Last Modified on"
msgstr "Viimeksi muokattu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Last Month"
msgstr "Edellinen kuukausi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
msgid "Last Page"
msgstr "Viimeinen sivu"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__write_uid
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__write_uid
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__write_uid
#: model:ir.model.fields,field_description:website.field_theme_website_menu__write_uid
#: model:ir.model.fields,field_description:website.field_theme_website_page__write_uid
#: model:ir.model.fields,field_description:website.field_website__write_uid
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__write_uid
#: model:ir.model.fields,field_description:website.field_website_menu__write_uid
#: model:ir.model.fields,field_description:website.field_website_page__write_uid
#: model:ir.model.fields,field_description:website.field_website_rewrite__write_uid
#: model:ir.model.fields,field_description:website.field_website_robots__write_uid
#: model:ir.model.fields,field_description:website.field_website_route__write_uid
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__write_uid
#: model:ir.model.fields,field_description:website.field_website_visitor__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivittänyt"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__write_date
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__write_date
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__write_date
#: model:ir.model.fields,field_description:website.field_theme_website_menu__write_date
#: model:ir.model.fields,field_description:website.field_theme_website_page__write_date
#: model:ir.model.fields,field_description:website.field_website__write_date
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__write_date
#: model:ir.model.fields,field_description:website.field_website_menu__write_date
#: model:ir.model.fields,field_description:website.field_website_page__write_date
#: model:ir.model.fields,field_description:website.field_website_rewrite__write_date
#: model:ir.model.fields,field_description:website.field_website_robots__write_date
#: model:ir.model.fields,field_description:website.field_website_route__write_date
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__write_date
#: model:ir.model.fields,field_description:website.field_website_visitor__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__last_visited_page_id
msgid "Last Visited Page"
msgstr "Viimeinen vieraltu sivu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Last Week"
msgstr "Viime viikko"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Last Year"
msgstr "Edellinen vuosi"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__time_since_last_action
msgid "Last action"
msgstr "Viimeinen toiminto"

#. module: website
#: code:addons/website/controllers/main.py:0
#, python-format
msgid "Last modified pages"
msgstr "Viimeksi muutetut sivut"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__last_connection_datetime
msgid "Last page view date"
msgstr "Viimeisen sivun katselun päivämäärä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Latests news and case studies"
msgstr "Viimeisimmät uutiset ja tapaustutkimukset"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Layout"
msgstr "Ulkoasu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Layout Background"
msgstr "Asettelun tausta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Layout Background Color"
msgstr "Asettelun taustaväri"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "Learn more"
msgstr "Lue lisää"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Left"
msgstr "Vasen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menu_image_menu
msgid "Left Menu"
msgstr "Vasen valikko"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Legal"
msgstr "Laillinen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Legal Notice"
msgstr "Oikeudellinen huomautus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Legend"
msgstr "Legenda"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Let your customers follow <br/>and understand your process."
msgstr "Anna asiakkaidesi seurata <br/>ja ymmärtää toimintatapasi."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Let your customers log in to see their documents"
msgstr "Salli asiakkaidesi kirjautua sisään ja nähdä asiakirjansa"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Let's do it"
msgstr "Eiköhän tehdä tämä"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Let's go!"
msgstr "Aloitetaan!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Library"
msgstr "Kirjasto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
msgid "Light"
msgstr "Vaalea"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__limit
msgid "Limit"
msgstr "Raja"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Limited customization"
msgstr "Rajattu mukautus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Line"
msgstr "Rivi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline_options
msgid "Line Color"
msgstr "Viivan väri"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Link"
msgstr "Linkki"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Link Anchor"
msgstr "Linkkiankkuri"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Link Style"
msgstr "Linkin tyyli"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Link button"
msgstr "Linkkipainike"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Link text"
msgstr "Linkin teksti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_linkedin
#: model:ir.model.fields,field_description:website.field_website__social_linkedin
msgid "LinkedIn Account"
msgstr "LinkedIn-tili"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Linkedin"
msgstr "Linkedin"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Links"
msgstr "Linkit"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Links Style"
msgstr "Linkkien tyyli"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Links to other Websites"
msgstr "Linkit muille verkkosivuille"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Little Icons"
msgstr "Pienet ikonit"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_live_chat
msgid "Live Chat"
msgstr "Reaaliaikainen keskustelu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_kanban
msgid "Live Preview"
msgstr "Esikatselu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Livechat Widget"
msgstr "Livechat-osio"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#: code:addons/website/static/src/xml/website.background.video.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Loading..."
msgstr "Lataa..."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Logo"
msgstr "Logo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Logo Type"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Logo of MyCompany"
msgstr "Oman yrityksen logo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Logos"
msgstr "Logot"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Long Text"
msgstr "Pitkä teksti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Low"
msgstr "Alin"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Magazine"
msgstr "Lipas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Main Course"
msgstr "Pääruoka"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__menu_id
msgid "Main Menu"
msgstr "Päävalikko"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#, python-format
msgid "Main actions"
msgstr "Päätoiminnot"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Make sure billing is enabled"
msgstr "Varmista, että laskutus on käytössä"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"Make sure to wait if errors keep being shown: sometimes enabling an API "
"allows to use it immediately but Google keeps triggering errors for a while"
msgstr ""
"Varmista, että odotat, jos virheitä näytetään jatkuvasti: joskus API:n "
"käyttöönotto mahdollistaa sen käytön välittömästi, mutta Google näyttää "
"virheitä jonkin aikaa"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Make sure your settings are properly configured:"
msgstr "Varmista, että asetukset on määritetty oikein:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Manage Pages"
msgstr "Hallinnoi sivuja"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "Manage Your Pages"
msgstr "Hallinnoi sivujasi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Manage Your Website Pages"
msgstr "Hallinnoi verkkosivujasi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Manage this page"
msgstr "Hallinnoi tätä sivua"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Maps JavaScript API"
msgstr "Karttojen JavaScript API"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Maps Static API"
msgstr "Karttojen staattinen API"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Mark Text"
msgstr "Merkkaa teksti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Marked Fields"
msgstr "Merkatut kentät"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "Marker style"
msgstr "Valitsijan tyyli"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Marketplace"
msgstr "Markkinapaikka"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Masonry"
msgstr "Tiiliseinä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Measurement ID"
msgstr "Mittauksen tunnus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "Media"
msgstr "Media"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Media heading"
msgstr "Mediaotsikko"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Medium"
msgstr "Media"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Mega Menu"
msgstr "Megavalikko"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__mega_menu_classes
msgid "Mega Menu Classes"
msgstr "Megavalikon luokat"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__mega_menu_content
msgid "Mega Menu Content"
msgstr "Megavalikon sisältö"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Men"
msgstr "Miehet"

#. module: website
#: code:addons/website/models/website.py:0
#: model:ir.model.fields,field_description:website.field_website_menu__name
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Menu"
msgstr "Valikko"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__menu_company
msgid "Menu Company"
msgstr "Valikon yritys"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menu_image_menu
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Menu Item %s"
msgstr "Valikkoelementti %s"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Menu Label"
msgstr "Valikon otsikko"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__menu_sequence
msgid "Menu Sequence"
msgstr "Valikon järjestys"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_menu__copy_ids
msgid "Menu using a copy of me"
msgstr "Valikko käyttäen kopiota minusta"

#. module: website
#: code:addons/website/models/website.py:0
#: model:ir.ui.menu,name:website.menu_website_menu_list
#, python-format
msgid "Menus"
msgstr "Valikot"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
msgid "Messages"
msgstr "Viestit"

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "Metadata"
msgstr "Metatiedot"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Mich Stark, COO"
msgstr "Mich Stark, COO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Mich loves taking on challenges. With his multi-year experience as "
"Commercial Director in the software industry, Mich has helped the company to"
" get where it is today. Mich is among the best minds."
msgstr ""
"Mikko rakastaa haasteita. Monivuotisen kokemuksensa ansiosta, jonka hän on "
"saanut kaupallisena johtajana ohjelmistoteollisuudessa, Mikko on auttanut "
"yritystä pääsemään nykyiseen asemaansa. Mikko kuuluu firman fiksuimpien "
"joukkoon."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Middle"
msgstr "Keskellä"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Min-Height"
msgstr "Vähimmäiskorkeus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Minimalist"
msgstr "Minimaalinen"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#, python-format
msgid "Minutes"
msgstr "Minuutit"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#: model:ir.model.fields,field_description:website.field_website_visitor__mobile
#, python-format
msgid "Mobile"
msgstr "Matkapuhelin"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Mobile Alignment"
msgstr "Tasaus mobiililaitteille"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Mobile menu"
msgstr "Matkapuhelinvalikko"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/mobile_view.js:0
#, python-format
msgid "Mobile preview"
msgstr "Esikatselu mobiililaitteille"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__mode
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Mode"
msgstr "Toimintatila"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__model
msgid "Model"
msgstr "Malli"

#. module: website
#: model:ir.model,name:website.model_ir_model_data
#: model:ir.model.fields,field_description:website.field_website_page__model_data_id
msgid "Model Data"
msgstr "Mallin data"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__model_name
msgid "Model name"
msgstr "Malli"

#. module: website
#: model:ir.model,name:website.model_ir_model
msgid "Models"
msgstr "Mallit"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_updated
msgid "Modified Architecture"
msgstr "Muokattu arkkitehtuuri"

#. module: website
#: model:ir.model,name:website.model_ir_module_module
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__module_id
msgid "Module"
msgstr "Moduuli"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Monitor Google Search results data"
msgstr "Seuraa Google-hakutulosten tietoja"

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_0
msgid ""
"Monitor your visitors while they are browsing your website with the Odoo "
"Social app. Engage with them in just a click using a live chat request or a "
"push notification. If they have completed one of your forms, you can send "
"them an SMS, or call them right away while they are browsing your website."
msgstr ""
"Seuraa kävijöitäsi, kun he selaavat verkkosivustoasi Odoo Social "
"-sovelluksella. Ota heihin yhteyttä vain yhdellä klikkauksella live-chat-"
"pyynnön tai push-ilmoituksen avulla. Jos he ovat täyttäneet jonkin "
"lomakkeistasi, voit lähettää heille tekstiviestin tai soittaa heille heti, "
"kun he selaavat verkkosivustoasi."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Monitors"
msgstr "Monitorit"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_color_blocks_2
msgid "More Details"
msgstr "More Details"

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_4
msgid ""
"More than 90 shapes exist and their colors are picked to match your Theme."
msgstr "Muotoja on yli 90, ja niiden värit on valittu teemaan sopiviksi."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "More than one group has been set on the view."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Mosaic"
msgstr "Mosaiikki"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Most searched topics related to your keyword, ordered by importance"
msgstr "Hakusanaan liittyvät eniten haetut aiheet tärkeysjärjestyksessä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Mouse"
msgstr "Hiiri"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move Backward"
msgstr "Siirry taaksepäin"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move Forward"
msgstr "Eteenpäin"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Move to first"
msgstr "Siirrä ensimmäiseksi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Move to last"
msgstr "Siirrä viimeiseksi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Move to next"
msgstr "Siirrä seuraavaksi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Move to previous"
msgstr "Siirrä edelliseksi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Multi Menus"
msgstr "Monivalikot"

#. module: website
#: model:ir.model,name:website.model_website_multi_mixin
msgid "Multi Website Mixin"
msgstr "Multi Website Mixin"

#. module: website
#: model:ir.model,name:website.model_website_published_multi_mixin
msgid "Multi Website Published Mixin"
msgstr "Multi Website Published Mixin"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__group_multi_website
#: model:res.groups,name:website.group_multi_website
msgid "Multi-website"
msgstr "Moniverkkosivu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Multimedia"
msgstr "Multimedia"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Multiple Checkboxes"
msgstr "Monivalinta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_qweb
msgid "Multiple tree exists for this view"
msgstr "Tässä näkymässä on useita puita"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_page
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid "My Company"
msgstr "Oma yritykseni"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.option_header_brand_name
#: model_terms:ir.ui.view,arch_db:website.option_header_off_canvas
msgid "My Website"
msgstr "Oma verkkosivu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "MyCompany"
msgstr "OmaYritys"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__name
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__name
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__name
#: model:ir.model.fields,field_description:website.field_theme_website_menu__name
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__name
#: model:ir.model.fields,field_description:website.field_website_rewrite__name
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__name
#: model:ir.model.fields,field_description:website.field_website_visitor__name
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
#: model_terms:ir.ui.view,arch_db:website.menu_search
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
#, python-format
msgid "Name"
msgstr "Nimi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Name (A-Z)"
msgstr "Nimi (A-Ö)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Name (Z-A)"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Name and favicon of your website"
msgstr "Verkkosivun nimi ja favicon"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_qweb
msgid "Name, id or key"
msgstr "Nimi, id tai avain"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Narrow"
msgstr "Kapea"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Navbar"
msgstr "Navbar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Need to pick up your order at one of our stores? Discover the nearest to "
"you."
msgstr ""
"Haluatko noutaa tilauksesi jostakin myymälästämme? Löydä lähin "
"toimipaikkamme."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Network Advertising Initiative opt-out page"
msgstr "Verkostomainonta-aloitteen hylkäyssivu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Networks"
msgstr "Verkot"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "New"
msgstr "Uusi"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid ""
"New Google Analytics accounts and keys are now using Google Analytics 4 "
"which, for now, can't be integrated/embed in external websites."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "New Page"
msgstr "Uusi sivu"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_menu__new_window
#: model:ir.model.fields,field_description:website.field_website_menu__new_window
msgid "New Window"
msgstr "Uusi ikkuna"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "New collection"
msgstr "Uusi kokoelma"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup
msgid "New customer"
msgstr "Uusi asiakas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "New page"
msgstr "Uusi sivu"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_news
msgid "News"
msgstr "Ajankohtaista"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Newsletter"
msgstr "Uutiskirje"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Newsletter Popup"
msgstr "Uutiskirje-ponnahdusikkuna"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Next"
msgstr "Seuraava"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "No Animation"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "No Slide Effect"
msgstr "Ei siirtymätehostetta"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_widget.xml:0
#, python-format
msgid "No Url"
msgstr "Ei URL:ia"

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitors_action
msgid "No Visitors yet!"
msgstr "Ei vielä vierailijoita!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "No customization"
msgstr "Ei kustomointeja"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "No matching record !"
msgstr "Ei vastaavaa tietuetta !"

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitor_page_action
msgid "No page views yet for this visitor"
msgstr "Tällä kävijällä ei ole vielä sivun katselukertoja"

#. module: website
#: model_terms:ir.actions.act_window,help:website.visitor_partner_action
msgid "No partner linked for this visitor"
msgstr "Tähän kävijään ei ole liitetty kumppania"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "No result found, broaden your search."
msgstr "Tuloksia ei löytynyt, laajenna hakua."

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_searchbar/000.xml:0
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
#, python-format
msgid "No results found for '"
msgstr "Ei tuloksia. Kokeile jotain muuta hakusanaa. '"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_searchbar/000.xml:0
#, python-format
msgid "No results found. Please try another search."
msgstr "Ei tuloksia. Koita toista hakusanaa."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "No support"
msgstr "Ei tukea"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_header_brand
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "None"
msgstr "Ei mitään"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Normal"
msgstr "Normaali"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Not SEO optimized"
msgstr "Ei hakukoneoptimoitu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Not in main menu"
msgstr "Ei päävalikossa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Not indexed"
msgstr "Ei indeksoitu"

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_3
msgid ""
"Not only can you search for royalty-free illustrations, their colors are "
"also converted so that they always fit your Theme."
msgstr ""
"Sen lisäksi, että voit etsiä rojaltivapaita kuvituksia, niiden värit "
"muunnetaan niin, että ne sopivat aina teemaan."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Not published"
msgstr "Ei julkaistu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Not tracked"
msgstr "Ei seurattu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Note that some third-party services may install additional cookies on your "
"browser in order to identify you."
msgstr ""
"Huomaa, että jotkin kolmannen osapuolen palvelut saattavat asentaa "
"selaimeesi ylimääräisiä evästeitä, jotta sinut voidaan tunnistaa."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Note: To hide this page, uncheck it from the top Customize menu."
msgstr ""
"Huom: piilottaaksesi tämän sivun, poista sen valinta yläpalkin valikon "
"muokkauksesta."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Nothing"
msgstr "Ei mitään"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Number"
msgstr "Numero"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_language_count
msgid "Number of languages"
msgstr "Kielten määrä"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "OR"
msgstr "TAI"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Odoo Logo"
msgstr "Odoo Logo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Odoo Menu"
msgstr "Odoo-valikko"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Odoo Version"
msgstr "Odoon versio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Off-Canvas"
msgstr "Off-Canvas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Office audio"
msgstr "Toimiston ääni"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Office screens"
msgstr "Toimiston näytöt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Offline"
msgstr "Poissa"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "Ok"
msgstr "OK"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "Ok, never show me this again"
msgstr "Ok, älä näytä tätä uudelleen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "On Click"
msgstr "Kun klikataan"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "On Exit"
msgstr "Kun poistutaan"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "On Hover"
msgstr "Kun hiirikursori viedään päälle"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "On Success"
msgstr "Onnistuttaessa"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "On Website"
msgstr "Verkkosivulla"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website__auth_signup_uninvited__b2b
msgid "On invitation"
msgstr "Vain kutsulla"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"Once the selection of available websites by domain is done, you can filter "
"by country group."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid ""
"Once the user closes the popup, it won't be shown again for that period of "
"time."
msgstr ""
"Kun käyttäjä sulkee ponnahdusikkunan, sitä ei enää näytetä aikajaksolla."

#. module: website
#: code:addons/website/models/website_configurator_feature.py:0
#, python-format
msgid ""
"One and only one of the two fields 'page_view_id' and 'module_id' should be "
"set"
msgstr "Vain yksi kentistä 'page_view_id' ja 'module_id' on asetettava"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Online"
msgstr "Verkossa"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__mode
msgid ""
"Only applies if this view inherits from an other one (inherit_id is not False/Null).\n"
"\n"
"* if extension (default), if this view is requested the closest primary view\n"
"is looked up (via inherit_id), then all views inheriting from it with this\n"
"view's model are applied\n"
"* if primary, the closest primary view is fully resolved (even if it uses a\n"
"different model than this one), then this view's inheritance specs\n"
"(<xpath/>) are applied, and the result is used as if it were this view's\n"
"actual arch.\n"
msgstr ""
"Koskee vain, jos tämä näkymä periytyy toisesta näkymästä (inherit_id ei ole False/Null).\n"
"\n"
"* jos laajennus (oletusarvo), jos tätä näkymää pyydetään lähimpänä olevaa ensisijaista näkymää\n"
"etsitään (inherit_id:n kautta), sitten kaikki siitä periytyvät näkymät, joilla on tämä nimi\n"
"näkymän mallia sovelletaan\n"
"* jos ensisijainen, lähin primäärinen näkymä ratkaistaan kokonaan (vaikka se käyttäisi\n"
"eri mallia kuin tämä näkymä), niin tämän näkymän periytymispesifikaatiot\n"
"(<xpath/>) sovelletaan, ja tulosta käytetään ikään kuin se olisi tämän näkymän nimi\n"
"varsinainen arch.\n"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Open Source ERP"
msgstr "Avoimen lähdekoodin toiminnanohjausjärjestelmä (ERP)"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Optimize SEO"
msgstr "Hakukoneoptimoi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Optimize SEO of this page"
msgstr "Hakukoneoptimoi tämä sivu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Option 1"
msgstr "Vaihtoehto 1"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Option 2"
msgstr "Vaihtoehto 2"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Option 3"
msgstr "Vaihtoehto 3"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Optional"
msgstr "Vapaaehtoinen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Order by"
msgstr "Järjestä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Order now"
msgstr "Tilaa nyt"

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "Other Information:"
msgstr "Muut tiedot:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Our Company"
msgstr "Yrityksemme"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid "Our References"
msgstr "Referenssimme"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Our seminars and trainings for you"
msgstr "Seminaarit ja koulutukset sinulle"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "Our team"
msgstr "Tiimimme"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "Our team will message you back as soon as possible."
msgstr "Olemme yhteydessä niin pian kuin mahdollista."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Outline"
msgstr "Ääriviivat"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Outset"
msgstr "Loitonna"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "Outstanding images"
msgstr "Erinomaisia kuvia"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Over The Content"
msgstr "Sisällön yli"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Paddings"
msgstr "Pehmusteet"

#. module: website
#: code:addons/website/models/website.py:0
#: code:addons/website/models/website.py:0
#: model:ir.model,name:website.model_website_page
#: model:ir.model.fields,field_description:website.field_ir_ui_view__page_ids
#: model:ir.model.fields,field_description:website.field_theme_website_menu__page_id
#: model:ir.model.fields,field_description:website.field_website_page__page_ids
#: model:ir.model.fields,field_description:website.field_website_track__page_id
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#, python-format
msgid "Page"
msgstr "Sivu"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "Page <b>%s</b> contains a link to this page"
msgstr "Sivu <b>%s</b> viittaa tälle sivulle"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "Page <b>%s</b> is calling this file"
msgstr "Sivu <b>%s</b> kutsuu tätä tiedostoa"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_page__website_indexed
msgid "Page Indexed"
msgstr "Sivu indeksoitu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Page Layout"
msgstr "Sivun asettelu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "Page Name"
msgstr "Sivun nimi"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Page Properties"
msgstr "Sivun ominaisuudet"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Page Title"
msgstr "Sivun otsikko"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields,field_description:website.field_website_page__url
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
#, python-format
msgid "Page URL"
msgstr "Sivun URL"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__page_view_id
msgid "Page View"
msgstr "Sivun näkymä"

#. module: website
#: model:ir.actions.act_window,name:website.website_visitor_view_action
#: model:ir.model.fields,field_description:website.field_website_visitor__visitor_page_count
#: model:ir.ui.menu,name:website.menu_visitor_view_menu
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Page Views"
msgstr "Sivun katselukerrat"

#. module: website
#: model:ir.actions.act_window,name:website.website_visitor_page_action
msgid "Page Views History"
msgstr "Sivun katseluhistoria"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Page Visibility"
msgstr "Sivun näkyvyys"

#. module: website
#: model:ir.model.fields,help:website.field_website_configurator_feature__iap_page_code
msgid ""
"Page code used to tell IAP website_service for which page a snippet list "
"should be generated"
msgstr ""
"Sivun koodi, jota käytetään kertomaan IAP website_service -palvelulle, mille"
" sivulle on luotava pätkäluettelo"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_page__copy_ids
msgid "Page using a copy of me"
msgstr "Sivu, jossa käytetään kopiotani"

#. module: website
#. openerp-web
#: code:addons/website/models/website.py:0
#: code:addons/website/models/website.py:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: model:ir.ui.menu,name:website.menu_website_pages_list
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
#, python-format
msgid "Pages"
msgstr "Sivut"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Pagination"
msgstr "Sivutus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Pants"
msgstr "Housut"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid ""
"Paragraph text. Lorem <b>ipsum dolor sit amet</b>, consectetur adipiscing "
"elit. <i>Integer posuere erat a ante</i>."
msgstr ""
"Kappaleen teksti. Lorem <b>ipsum dolor sit amet</b>, consectetur adipiscing "
"elit. <i>Integer posuere erat a ante</i>."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid ""
"Paragraph with <strong>bold</strong>, <span class=\"text-"
"muted\">muted</span> and <em>italic</em> texts"
msgstr ""
"Kohta, jossa on <strong>lihavoituja</strong>, <span class=\"text-"
"muted\">vaimennettuja</span> ja <em>kursiivisia</em> tekstejä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Paragraph."
msgstr "Kappale."

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_menu__parent_id
msgid "Parent"
msgstr "Ylätaso"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__parent_id
msgid "Parent Menu"
msgstr "Ylätason valikko"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__parent_path
msgid "Parent Path"
msgstr "Ylempi polku"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__partner_id
msgid "Partner of the last logged in user."
msgstr "Viimeksi kirjautuneen käyttäjän kumppani."

#. module: website
#: model:ir.model.fields,help:website.field_website__partner_id
msgid "Partner-related data of the user"
msgstr "Käyttäjän kumppanitiedot"

#. module: website
#: model:ir.actions.act_window,name:website.visitor_partner_action
msgid "Partners"
msgstr "Kumppanit"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Password"
msgstr "Salasana"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__path
msgid "Path"
msgstr "Polku"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Pattern"
msgstr "Kuvio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Pay"
msgstr "Maksa"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Phone Number"
msgstr "Puhelinnumero"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Phones"
msgstr "Puhelimet"

#. module: website
#: model:ir.actions.act_window,name:website.theme_install_kanban_action
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Pick a Theme"
msgstr "Valitse teema"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Pie"
msgstr "Piirakka"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Pill"
msgstr "Pilleri"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Pills"
msgstr "Pillerit"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Placeholder"
msgstr "Paikanpitäjä"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Places API"
msgstr "Places API"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Plain"
msgstr "Kuvioton"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/res_config_settings.js:0
#, python-format
msgid "Please confirm"
msgstr "Ole hyvä ja vahvista"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "Please fill in the form correctly."
msgstr "Täytä lomakkeen vaaditut kentät."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "Points of sale"
msgstr "Kassapääte"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Popup"
msgstr "Ponnahdusikkuna"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Portfolio"
msgstr "Portfolio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
msgid "Position"
msgstr "Asema"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Post heading"
msgstr "Mediaotsikko"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Postcard"
msgstr "Postikortti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Preferences"
msgstr "Asetukset"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__prepend
msgid "Prepend"
msgstr "Prepend"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Preset"
msgstr "Esiasetus"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "Press"
msgstr "Paina"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/view_hierarchy.js:0
#, python-format
msgid "Press %s for next %s"
msgstr "Paina %s seuraavaa %s"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Preview"
msgstr "Esikatselu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Previous"
msgstr "Edellinen"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_prev
msgid "Previous View Architecture"
msgstr "Aiempi näkymäarkkitehtuuri"

#. module: website
#: model:website.configurator.feature,name:website.feature_page_pricing
#: model_terms:ir.ui.view,arch_db:website.pricing
msgid "Pricing"
msgstr "Hinnoittelu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Primary"
msgstr "Ensisijainen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Primary Style"
msgstr "Ensisijainen tyyli"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Printers"
msgstr "Tulostimet"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__priority
msgid "Priority"
msgstr "Prioriteetti"

#. module: website
#: code:addons/website/models/website.py:0
#: model:website.configurator.feature,name:website.feature_page_privacy_policy
#: model_terms:ir.ui.view,arch_db:website.privacy_policy
#, python-format
msgid "Privacy Policy"
msgstr "Tietosuojakäytäntö"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog_add_product_widget
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Product"
msgstr "Tuote"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Products"
msgstr "Tuotteet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Professional"
msgstr "Ammattikäyttöön"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Professional themes"
msgstr "Ammattimaiset teemat"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "Profile"
msgstr "Profiili"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Progress Bar"
msgstr "Edistymispalkki"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Progress Bar Color"
msgstr "Edistymispalkin väri"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Progress Bar Style"
msgstr "Edistymispalkin tyyli"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Progress Bar Weight"
msgstr "Edistymispalkin paino"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Projectors"
msgstr "Projektorit"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Promote"
msgstr "Paranna näkyvyyttä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Promote page on the web"
msgstr "Paranna tämän sivun näkyvyyttä verkossa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Promotions"
msgstr "Kampanjat"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Public"
msgstr "Julkinen"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__partner_id
msgid "Public Partner"
msgstr "Julkinen kumppani"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__user_id
msgid "Public User"
msgstr "Julkinen käyttäjä"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/button.js:0
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Publish"
msgstr "Julkaise"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_career
msgid "Publish job offers and let people apply"
msgstr "Julkaise työtarjouksia ja anna ihmisten hakea niitä"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_event
msgid "Publish on-site and online events"
msgstr "Julkaise tapahtumia paikan päällä ja verkossa"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/button.js:0
#: code:addons/website/static/src/js/backend/button.js:0
#: model_terms:ir.ui.view,arch_db:website.one_page_line
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.publish_short
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
#, python-format
msgid "Published"
msgstr "Julkaistu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields,field_description:website.field_website_page__date_publish
#, python-format
msgid "Publishing Date"
msgstr "Julkaisupäivä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Pulse"
msgstr "Sykkivä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Purpose"
msgstr "Käyttötarkoitus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_highlight
msgid "Put the focus on what you have to say!"
msgstr "Keskity siihen, mitä sinulla on sanottavaa!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating
msgid "Quality"
msgstr "Laatu"

#. module: website
#: model:ir.model,name:website.model_ir_qweb
msgid "Qweb"
msgstr "Qweb"

#. module: website
#: model:ir.model,name:website.model_ir_qweb_field_contact
msgid "Qweb Field Contact"
msgstr "Qweb kentän kontakti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Radar"
msgstr "Tutka"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Radio Buttons"
msgstr "Radiopainikkeet"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Rating"
msgstr "Arvostelu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Re-order"
msgstr "Tilaa uudelleen"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Ready to build the"
msgstr "Valmiina rakentamaan"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Recipient Email"
msgstr "Vastaanottajan sähköposti"

#. module: website
#: model:ir.model,name:website.model_ir_rule
msgid "Record Rule"
msgstr "Kirjaa sääntö"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Redirect"
msgstr "Uudelleenohjaus"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Redirect Old URL"
msgstr "Uudelleenohjaa vanha URL"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Redirect to URL in a new tab"
msgstr "Uudelleenohjaa URL-osoitteeseen uudessa välilehdessä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
msgid "Redirection Type"
msgstr "Uudelleenohjauksen tyyppi"

#. module: website
#: model:ir.ui.menu,name:website.menu_website_rewrite
msgid "Redirects"
msgstr "Uudelleenohjaukset"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_rewrite_form
msgid "Refresh route's list"
msgstr "Virkistä reittien lista"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Regular"
msgstr "Tavallinen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_form_view
msgid "Related Menu Items"
msgstr "Liittyvät valikkoelementit"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__menu_ids
msgid "Related Menus"
msgstr "Liittyvät valikot"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__page_id
msgid "Related Page"
msgstr "Liittyvä sivu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Related keywords"
msgstr "Liittyvät avainsanat"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Remember information about the preferred look or behavior of the website, "
"such as your preferred language or region."
msgstr ""
"Muista tiedot verkkosivuston haluamastasi ulkoasusta tai käyttäytymisestä, "
"kuten haluamastasi kielestä tai alueesta."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__remove
#, python-format
msgid "Remove"
msgstr "Poista"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Remove Row"
msgstr "Poista rivi"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Remove Serie"
msgstr "Poista sarja"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Remove Slide"
msgstr "Poista dia"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Remove Tab"
msgstr "Poista välilehti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Remove all"
msgstr "Poista kaikki"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_kanban
msgid "Remove theme"
msgstr "Poista teema"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "Rename Page To:"
msgstr "Uudelleennimeä sivu:"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__replace
msgid "Replace"
msgstr "Korvaa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_embed_code
msgid "Replace this with your own HTML code"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Required"
msgstr "Vaadittu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Reset templates"
msgstr "Nollaa mallipohjat"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Reset to initial version (hard reset)."
msgstr "Nollaa alkuperäiseen versioon (hard reset)."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Resources"
msgstr "Resurssit"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#, python-format
msgid "Respecting your privacy is our priority."
msgstr "Yksityisyytesi kunnioittaminen on meille ensisijaisen tärkeää."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Restore previous version (soft reset)."
msgstr "Nollaa edelliseen versioon (soft reset)."

#. module: website
#: model:ir.model.fields,help:website.field_res_partner__website_id
#: model:ir.model.fields,help:website.field_res_users__website_id
#: model:ir.model.fields,help:website.field_website_multi_mixin__website_id
#: model:ir.model.fields,help:website.field_website_page__website_id
#: model:ir.model.fields,help:website.field_website_published_multi_mixin__website_id
#: model:ir.model.fields,help:website.field_website_snippet_filter__website_id
msgid "Restrict publishing to this website."
msgstr "Rajoita julkaisu tälle verkkosivustolle."

#. module: website
#: model:res.groups,name:website.group_website_publisher
msgid "Restricted Editor"
msgstr "Rajoitettu muokkaaja"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__ir_ui_view__visibility__restricted_group
msgid "Restricted Group"
msgstr "Rajoitettu ryhmä"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_rewrite_list
msgid "Rewrite"
msgstr "Uudelleenkirjoita"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Right"
msgstr "Oikea"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menu_image_menu
msgid "Right Menu"
msgstr "Oikea valikko"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Ripple Effect"
msgstr "Vyöryvaikutus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Road"
msgstr "Maantie"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "RoadMap"
msgstr "Tiekartta"

#. module: website
#: code:addons/website/models/res_config_settings.py:0
#: model:ir.model.fields,field_description:website.field_website__robots_txt
#, python-format
msgid "Robots.txt"
msgstr "Robots.txt"

#. module: website
#: model:ir.model,name:website.model_website_robots
msgid "Robots.txt Editor"
msgstr "Muokkaa robots.txt tiedostoa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"Robots.txt: This file tells to search engine crawlers which pages or files "
"they can or can't request from your site.<br/>"
msgstr ""
"Robots.txt: Tämä tiedosto kertoo hakukoneiden indeksoijille, mitä sivuja tai"
" tiedostoja ne voivat tai eivät voi pyytää sivustoltasi.<br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Rotate In"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Rotate In-Down-Left"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Rotate In-Down-Right"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_widgets
msgid "Round Corners"
msgstr "Pyöristetyt kulmat"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Rounded"
msgstr "Pyöristetty"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Rounded Miniatures"
msgstr "Pyöristetyt miniatyyrit"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__route_id
#: model:ir.model.fields,field_description:website.field_website_route__path
msgid "Route"
msgstr "Reitti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "SEO"
msgstr "Hakukoneoptimointi"

#. module: website
#: model:ir.model,name:website.model_website_seo_metadata
msgid "SEO metadata"
msgstr "Hakukoneoptimoinnin (SEO) metadata"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__is_seo_optimized
#: model:ir.model.fields,field_description:website.field_website_page__is_seo_optimized
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__is_seo_optimized
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "SEO optimized"
msgstr "Hakukoneoptimoitu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Same as desktop"
msgstr "Sama kuin työpöydällä"

#. module: website
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "Sample %s"
msgstr "Malli %s"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Sample Icons"
msgstr "Esimerkki-ikonit"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Satellite"
msgstr "Satelliitti"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/dashboard.js:0
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/js/menu/content.js:0
#: code:addons/website/static/src/js/menu/seo.js:0
#: code:addons/website/static/src/snippets/s_embed_code/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: model_terms:ir.ui.view,arch_db:website.view_edit_robots
#, python-format
msgid "Save"
msgstr "Tallenna"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Save & Reload"
msgstr "Tallenna ja lataa uudelleen"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Save & copy"
msgstr "Tallenna ja kopioi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Save the block to use it elsewhere"
msgstr "Tallenna palikka käyttääksesi sitä muualla"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "Score"
msgstr "Pisteet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Screens"
msgstr "Näytöt"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_module_module__image_ids
msgid "Screenshots"
msgstr "Ruutukuvat"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll"
msgstr "Vieritä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll Effect"
msgstr "Vieritystehoste"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.option_footer_scrolltop
msgid "Scroll To Top"
msgstr "Vieritä ylös"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll Top Button"
msgstr "Vieritä ylös -painike"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll down button"
msgstr "Vieritä alas -painike"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Scroll down to next section"
msgstr "Scroll down to next section"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.view_view_qweb
#: model_terms:ir.ui.view,arch_db:website.website_search_box
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Search"
msgstr "Haku"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
msgid "Search Menus"
msgstr "Hakuvalikot"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
msgid "Search Redirect"
msgstr "Haun uudelleenohjaus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
msgid "Search Results"
msgstr "Hakutulokset"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Search Visitor"
msgstr "Hae vierailijoita"

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_1
msgid ""
"Search in the media dialogue when you need photos to illustrate your "
"website. Odoo's integration with Unsplash, featuring millions of royalty "
"free and high quality photos, makes it possible for you to get the perfect "
"picture, in just a few clicks."
msgstr ""
"Hae media-dialogista, kun tarvitset kuvia verkkosivujesi rikastamiseen. "
"Odoon on yhteydessä Unsplashiin. Siellä on miljoonia rojaltivapaita ja "
"laadukkaita kuvia. Tätä voi käyttää täydellisen kuvan hankkimisen muutamalla"
" klikkauksella."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_searchbar
msgid "Search on our website"
msgstr "Haku verkkosivuillamme"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Search within"
msgstr "Haku kodistaen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_search_box
msgid "Search..."
msgstr "Hae..."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Second Feature"
msgstr "Toinen ominaisuus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Second Menu"
msgstr "Toinen valikko"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "Second feature"
msgstr "Toinen ominaisuus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Second list of Features"
msgstr "Toinen ominaisuuslista"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Secondary"
msgstr "Toissijainen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Secondary Style"
msgstr "Toissijainen tyyli"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#, python-format
msgid "Seconds"
msgstr "Sekuntia"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
msgid "Section Subtitle"
msgstr "Kappaleen alaotsikko"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Select a Menu"
msgstr "Valitse valikko"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Select a website to load its settings."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Select an image for social share"
msgstr "Valitse kuva sosiaalisen median jakoon"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Select and delete blocks <br/>to remove some steps."
msgstr "Valitse ja poista lohkot <br/>poistaaksesi joitakin vaiheita."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Select and delete blocks to remove features."
msgstr "Valitse ja poista lohkoja poistaaksesi ominaisuuksia."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Select one font on"
msgstr "Valitse yksi fontti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Select the Website to Configure"
msgstr "Valitse verkkosivu konfiguroitavaksi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Selection"
msgstr "Valinta"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_shop
msgid "Sell more with an eCommerce"
msgstr "Myy enemmän verkkokaupan avulla"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Send Email"
msgstr "Lähetä sähköposti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "Send us a message"
msgstr "Lähetä meille viesti"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__seo_name
#: model:ir.model.fields,field_description:website.field_website_page__seo_name
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__seo_name
msgid "Seo name"
msgstr "SEO-nimi"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "Separate email addresses with a comma."
msgstr "Erottele useampi sähköpostiosoite pilkulla"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Separated link"
msgstr "Erotettu linkki"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Separator"
msgstr "Erotin"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__sequence
#: model:ir.model.fields,field_description:website.field_theme_website_menu__sequence
#: model:ir.model.fields,field_description:website.field_website__sequence
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__sequence
#: model:ir.model.fields,field_description:website.field_website_menu__sequence
#: model:ir.model.fields,field_description:website.field_website_page__priority
#: model:ir.model.fields,field_description:website.field_website_rewrite__sequence
msgid "Sequence"
msgstr "Järjestys"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Serve font from Google servers"
msgstr "Fontin tarjoaminen Googlen palvelimilta"

#. module: website
#: model:ir.model,name:website.model_ir_actions_server
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__action_server_id
msgid "Server Action"
msgstr "Palvelintoiminto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Service"
msgstr "Palvelu"

#. module: website
#: model:website.configurator.feature,name:website.feature_page_our_services
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.our_services
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Services"
msgstr "Palvelut"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Session &amp; Security"
msgstr ""

#. module: website
#: model:ir.actions.act_window,name:website.action_website_configuration
#: model:ir.ui.menu,name:website.menu_website_website_settings
msgid "Settings"
msgstr "Asetukset"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Settings on this page will apply to this website"
msgstr "Tämän sivun asetukset koskee tätä verkkosivua"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Shadow"
msgstr "Varjo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Shadows"
msgstr "Varjot"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Shake"
msgstr "Ravistus"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: model_terms:ir.ui.view,arch_db:website.s_share
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#, python-format
msgid "Share"
msgstr "Jaa"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_elearning
msgid "Share knowledge publicly or for a fee"
msgstr "Jaa tietoa julkisesti tai maksua vastaan"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_success_stories
msgid "Share your best case studies"
msgstr "Jaa parhaat esimerkkitapaukset"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Shoes"
msgstr "Kengät"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_shop
msgid "Shop"
msgstr "Kauppa"

#. module: website
#: model:ir.model.fields,help:website.field_website__auto_redirect_lang
msgid "Should users be redirected to their browser's language"
msgstr "Ohjataanko käyttäjät selaimen kielen perusteella"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__customize_show
msgid "Show As Optional Inherit"
msgstr "Näytä valinnaisena periytymisenä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Show Header"
msgstr "Näytä otsikko"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Show Message"
msgstr "Näytä viesti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Show Message and hide countdown"
msgstr "Näytä viesti ja piilota lähtölaskenta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Show Message and keep countdown"
msgstr "Näytä viesti ja pidä lähtölaskenta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Show Sign In"
msgstr "Näytä Kirjautuminen"

#. module: website
#: model:ir.actions.act_window,name:website.action_show_viewhierarchy
msgid "Show View Hierarchy"
msgstr "Näytä näkymän hierarkia"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Show in Top Menu"
msgstr "Näytä päävalikossa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_qweb
msgid "Show inactive views"
msgstr "Näytä inaktiiviset näkymät"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Show on"
msgstr "Näytä osoitteessa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Show reCaptcha Policy"
msgstr "Näytä reCaptcha-käytänteet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Sidebar"
msgstr "Sivupalkki"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Sign in"
msgstr "Kirjaudu sisään"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields.selection,name:website.selection__ir_ui_view__visibility__connected
#, python-format
msgid "Signed In"
msgstr "Kirjautunut sisään"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"Sitemap.xml: Help search engine crawlers to find out what pages are present "
"and which have recently changed, and to crawl your site accordingly. This "
"file is automatically generated by Odoo."
msgstr ""
"Sitemap.xml: Auttaa hakukoneiden indeksoijia selvittämään, mitkä sivut ovat "
"olemassa ja mitkä ovat äskettäin muuttuneet, ja indeksoimaan sivustosi sen "
"mukaisesti. Odoo luo tämän tiedoston automaattisesti."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Size"
msgstr "Koko"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Skip and start from scratch"
msgstr "Ohita ja aloita alusta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slide"
msgstr "Esitys"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Slide Down"
msgstr "Liu'uta alas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slide Hover"
msgstr "Liu'uta kun hiirikursori viedään päälle"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Slide Left"
msgstr "Liu'uta vasemmalle"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Slide Right"
msgstr "Liu'uta oikealla"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Slide Up"
msgstr "Slide Up"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slideout Effect"
msgstr "Poisliukenemisen efekti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.dynamic_snippet_carousel_options_template
msgid "Slider Speed"
msgstr "Diavaihtajan nopeus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Slideshow"
msgstr "Diaesitys"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slogan"
msgstr "Iskulause"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Small"
msgstr "Pieni"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
msgid "Small Header"
msgstr "Pieni otsikko"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid ""
"Small text. Lorem <b>ipsum dolor sit amet</b>, consectetur adipiscing elit. "
"<i>Integer posuere erat a ante</i>."
msgstr ""
"Pieni teksti. Lorem <b>ipsum dolor sit amet</b>, consectetur adipiscing "
"elit. <i>Integer posuere erat a ante</i>."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Smartphones"
msgstr "Älypuhelimet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Social Media"
msgstr "Sosiaalinen media"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Social Preview"
msgstr "SOME-esikatselu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_line_widgets
msgid "Solid"
msgstr "Kiinteä"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Some Users"
msgstr "Osa käyttäjistä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Something else here"
msgstr "Jotain muuta tässä"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Something went wrong."
msgstr "Jotain meni pieleen."

#. module: website
#: code:addons/website/controllers/main.py:0
#, python-format
msgid "Sort by Name"
msgstr "Järjestä nimen mukaan"

#. module: website
#: code:addons/website/controllers/main.py:0
#, python-format
msgid "Sort by Url"
msgstr "Järjestä urlin perusteella"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Sound"
msgstr "Ääni"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid ""
"Speakers from all over the world will join our experts to give inspiring "
"talks on various topics. Stay on top of the latest business management "
"trends &amp; technologies"
msgstr ""
"Asiantuntijoidemme kanssa eri puolilta maailmaa tulevat puhujat pitävät "
"inspiroivia puheenvuoroja eri aiheista. Pysy ajan tasalla uusimmista "
"liikkeenjohdon trendeistä ja teknologioista"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__specific_user_account
#: model:ir.model.fields,field_description:website.field_website__specific_user_account
msgid "Specific User Account"
msgstr "Erityinen käyttäjätili"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
msgid "Specify a search term."
msgstr "Määritä hakusana."

#. module: website
#: model:ir.model.fields,help:website.field_ir_model__website_form_default_field_id
msgid ""
"Specify the field which will contain meta and custom form fields datas."
msgstr ""
"Määritä kenttä, joka sisältää tiedot metakentistä ja mukautetuista "
"lomakekentistä."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Speed"
msgstr "Nopeus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Spring collection has arrived !"
msgstr "Kevätmallisto on saapunut !"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_share_options
msgid "Square"
msgstr "Neliö"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Squared Miniatures"
msgstr "Pyöristetyt miniatyyrit"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Stacked"
msgstr "Pinottu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Standard"
msgstr "Normaali"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
msgid "Start Button"
msgstr "Start Button"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#, python-format
msgid "Start Now"
msgstr "Aloita nyt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Start now"
msgstr "Aloita nyt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
#: model_terms:ir.ui.view,arch_db:website.s_tabs
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "Start with the customer – find out what they want and give it to them."
msgstr "Aloita asiakkaasta – selvitä, mitä hän haluaa ja anna se hänelle."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "Start your journey"
msgstr "Aloita matkasi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Starter"
msgstr "Aloitus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Status Colors"
msgstr "Statusvärit"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Stay informed of our latest news and discover what will happen in the next "
"weeks."
msgstr ""
"Pysy ajan tasalla viimeisimmistä uutisistamme ja selvitä, mitä lähiviikkoina"
" tapahtuu."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
msgid "Sticky"
msgstr "Kiinteä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Storage"
msgstr "Varasto"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_stores_locator
msgid "Stores Locator"
msgstr "Kartta ja luettelo myymälöistäsi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "Storytelling is powerful.<br/> It draws readers in and engages them."
msgstr ""
"Tarinankerronta on väkevää.<br/> Se vetää lukijat puoleensa ja sitouttaa "
"heidät."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Stretch to Equal Height"
msgstr "Venytä yhtä korkeaksi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Striped"
msgstr "Raidallinen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Structure"
msgstr "Estructura"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Style"
msgstr "Tyyli"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Styling"
msgstr "Muotoilu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Sub Menus"
msgstr "Alivalikot"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Subject"
msgstr "Aihe"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.s_website_form
#: model_terms:website.page,arch_db:website.contactus_page
msgid "Submit"
msgstr "Tallenna"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Submit sitemap to Google"
msgstr "Lähetä sivukartta Googlelle"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form.xml:0
#: code:addons/website/static/src/xml/website_form.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Success"
msgstr "Onnistuminen"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_success_stories
msgid "Success Stories"
msgstr "Menestystarinoita"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Suggestions"
msgstr "Ehdotukset"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Surrounded"
msgstr "Ympäröity"

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "Suspicious activity detected by Google reCaptcha."
msgstr "Google reCaptcha on havainnut epäilyttävää toimintaa."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Switch Theme"
msgstr "Vaihda teema"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "T-shirts"
msgstr "T-paidat"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#, python-format
msgid "TIP: Once loaded, follow the"
msgstr "VINKKI: Kun olet ladannut, seuraa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid ""
"TIP: Once loaded, follow the\n"
"                    <span class=\"o_tooltip o_tooltip_visible bottom o_animated position-relative\"/>\n"
"                    <br/>pointer to build the perfect page in 7 steps."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "TRANSLATE"
msgstr "KÄÄNNÄ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Tablets"
msgstr "Tabletit"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Tabs"
msgstr "Välilehdet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Tabs color"
msgstr "Välilehtien väri"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Tada"
msgstr "Tada"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__target
msgid "Target"
msgstr "Tavoite"

#. module: website
#: model:ir.model.fields,help:website.field_ir_asset__key
msgid ""
"Technical field used to resolve multiple assets in a multi-website "
"environment."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_ir_attachment__key
msgid ""
"Technical field used to resolve multiple attachments in a multi-website "
"environment."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Technical name:"
msgstr "Tekninen nimi:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Telephone"
msgstr "Puhelin"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Televisions"
msgstr "Televisiot"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Tell what's the value for the <br/>customer for this feature."
msgstr "Kerro, mikä on tämän ominaisuuden arvo <br/>asiakkaalle."

#. module: website
#: code:addons/website/models/website.py:0
#: code:addons/website/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Template"
msgstr "Malli"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "Template <b>%s (id:%s)</b> contains a link to this page"
msgstr "Malli <b>%s (id:%s)</b> sisältää linkin tälle sivulle"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "Template <b>%s (id:%s)</b> is calling this file"
msgstr "Malli <b>%s (id:%s)</b> kutsuu tätä tiedostoa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Template fallback"
msgstr "Rikkinäinen sivupohja"

#. module: website
#: code:addons/website/models/website.py:0
#: code:addons/website/models/website.py:0
#, python-format
msgid "Templates"
msgstr "Mallit"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "Terms of Services"
msgstr "Terms of Services"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Terms of service"
msgstr "Käyttöehdot"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "Terrain"
msgstr "Maasto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_edit_robots
msgid "Test your robots.txt with Google Search Console"
msgstr "Testaa robots.txt-tiedostosi Google Search Console -palvelun avulla"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_header_brand
msgid "Text"
msgstr "Teksti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Text Alignment"
msgstr "Tekstin asettelu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Text Color"
msgstr "Tekstin väri"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: model_terms:ir.ui.view,arch_db:website.s_text_highlight
#, python-format
msgid "Text Highlight"
msgstr "Tekstin korostus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Text Image Text"
msgstr "Tekstin kuvateksti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Text Inline"
msgstr "Teksti Inline"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "Text Position"
msgstr "Tekstin sijainti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Text muted. Lorem <b>ipsum dolor sit amet</b>, consectetur."
msgstr "Teksti mykistetty. Lorem <b>ipsum dolor sit amet</b>, consectetur."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "Thank You For Your Feedback"
msgstr "Kiitos palautteestasi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid "Thank You!"
msgstr "Kiitos!"

#. module: website
#: code:addons/website/controllers/backend.py:0
#, python-format
msgid "The Google Analytics Client ID or Key you entered seems incorrect."
msgstr ""
"Antamasi Google Analytics Client ID tai Avain jonka annoit, näyttää "
"virheelliseltä."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "The chosen name already exists"
msgstr "Valittu nimi on jo olemassa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "The company this website belongs to"
msgstr "Tämä verkkosivu kuuluu yritykselle"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid ""
"The current text selection cannot be animated. Try clearing the format and "
"try again."
msgstr ""
"Nykyistä tekstivalintaa ei voi animoida. Yritä tyhjentää muotoilu ja yritä "
"uudelleen."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid ""
"The description will be generated by search engines based on page content "
"unless you specify one."
msgstr ""
"Hakukoneet luovat kuvauksen sivun sisällön perusteella, ellet määritä sitä."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid ""
"The description will be generated by social media based on page content "
"unless you specify one."
msgstr ""
"Sosiaalinen media luo kuvauksen sivun sisällön perusteella, ellet itse "
"määritä kuvausta."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form.xml:0
#, python-format
msgid "The form has been sent successfully."
msgstr "Lomake lähetettiin onnistuneesti."

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "The form's specified model does not exist"
msgstr "Lomakkeen määritettyä mallia ei ole olemassa"

#. module: website
#: model:ir.model.fields,help:website.field_res_partner__website_url
#: model:ir.model.fields,help:website.field_res_users__website_url
#: model:ir.model.fields,help:website.field_website_page__website_url
#: model:ir.model.fields,help:website.field_website_published_mixin__website_url
#: model:ir.model.fields,help:website.field_website_published_multi_mixin__website_url
#: model:ir.model.fields,help:website.field_website_snippet_filter__website_url
msgid "The full URL to access the document through the website."
msgstr "Dokumentin URL-osoite verkkosivustolla."

#. module: website
#: model:ir.model.fields,help:website.field_ir_actions_server__website_url
#: model:ir.model.fields,help:website.field_ir_cron__website_url
msgid "The full URL to access the server action through the website."
msgstr "Täydellinen URL palvelintoimintoon verkkosivun kautta."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "The installation of an App is already in progress."
msgstr "Sovelluksen asennus on jo käynnissä."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "The language of the keyword and related keywords."
msgstr "Avainsanan ja siihen liittyvien avainsanojen kieli."

#. module: website
#: model:ir.model.fields,help:website.field_website_snippet_filter__limit
msgid "The limit is the maximum number of records retrieved"
msgstr "Raja on haettujen tietueiden enimmäismäärä"

#. module: website
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "The limit must be between 1 and 16."
msgstr "Rajan on oltava välillä 1 ja 16."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "The message will be visible once the countdown ends"
msgstr "Viesti tulee näkyviin, kun lähtölaskenta päättyy"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "The selected templates will be reset to their factory settings."
msgstr "Valitut mallipohjat nollataan tehdasasetuksille."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid "The team"
msgstr "Tiimi"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "The title will take a default value unless you specify one."
msgstr "Otsikko ottaa oletusarvon, ellet määritä sitä."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"The website will not work properly if you reject or discard those cookies."
msgstr "Verkkosivusto ei toimi oikein, jos estät tai hylkäät nämä evästeet."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "The website will still work if you reject or discard those cookies."
msgstr "Verkkosivusto toimii edelleen, jos estät tai hylkäät nämä evästeet."

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: model:ir.model.fields,field_description:website.field_website__theme_id
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.theme_view_search
#, python-format
msgid "Theme"
msgstr "Teema"

#. module: website
#: model:ir.model,name:website.model_theme_ir_asset
msgid "Theme Asset"
msgstr "Teeman resurssi"

#. module: website
#: model:ir.model,name:website.model_theme_ir_attachment
msgid "Theme Attachments"
msgstr "Teeman liitteet"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
#, python-format
msgid "Theme Colors"
msgstr "Teeman värit"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Theme Options"
msgstr "Teeman asetukset"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_asset__theme_template_id
#: model:ir.model.fields,field_description:website.field_ir_attachment__theme_template_id
#: model:ir.model.fields,field_description:website.field_ir_ui_view__theme_template_id
#: model:ir.model.fields,field_description:website.field_website_menu__theme_template_id
#: model:ir.model.fields,field_description:website.field_website_page__theme_template_id
msgid "Theme Template"
msgstr "Teeman pohja"

#. module: website
#: model:ir.model,name:website.model_theme_ir_ui_view
msgid "Theme UI View"
msgstr "Teema UI-näkymä"

#. module: website
#: model:ir.model,name:website.model_theme_utils
msgid "Theme Utils"
msgstr "Teeman työkalut"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
msgid "There are currently no pages for this website."
msgstr "Tällä hetkellä tällä sivustolla ei ole sivuja."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "There are currently no pages for your website."
msgstr "Verkkosivustollasi ei tällä hetkellä ole yhtään sivua."

#. module: website
#: code:addons/website/models/website_visitor.py:0
#, python-format
msgid "There are no contact and/or no email linked to this visitor."
msgstr "Tähän kävijään ei ole liitetty yhteystietoja ja/tai sähköpostia."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "There is no data currently available."
msgstr "Dataa ei ole saatavilla."

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "There is no field available for this option."
msgstr "Tätä vaihtoehtoa varten ei ole käytettävissä kenttää."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"There is no website available for this company. You could create a new one."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"These terms of service (\"Terms\", \"Agreement\") are an agreement between "
"the website (\"Website operator\", \"us\", \"we\" or \"our\") and you "
"(\"User\", \"you\" or \"your\"). This Agreement sets forth the general terms"
" and conditions of your use of this website and any of its products or "
"services (collectively, \"Website\" or \"Services\")."
msgstr ""
"Nämä käyttöehdot (\"Ehdot\", \"Sopimus\") ovat verkkosivuston "
"(\"Verkkosivuston ylläpitäjä\", \"me\", \"me\" tai \"meidän\") ja sinun "
"(\"Käyttäjä\", \"sinä\" tai \"sinun\") välinen sopimus. Tässä sopimuksessa "
"esitetään yleiset ehdot tämän verkkosivuston ja minkä tahansa sen tuotteiden"
" tai palvelujen (yhdessä \"verkkosivusto\" tai \"palvelut\") käytöstä."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "They trust us since years"
msgstr "He luottavat meihin jo vuosien ajan"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Thick"
msgstr "Paksu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Thin"
msgstr "Ohut"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Third Feature"
msgstr "Kolmas ominaisuus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Third Menu"
msgstr "Kolmas valikko"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__favicon
#: model:ir.model.fields,help:website.field_website__favicon
msgid "This field holds the image used to display a favicon on the website."
msgstr "Tähän kenttään tulee verkkosivun favicon."

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch_base
msgid "This field is the same as `arch` field without translations"
msgstr "Tämä kenttä on sama kuin `arch`-kenttä ilman käännöksiä"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_default_lang_code
msgid "This field is used to set/get locales for user"
msgstr "Tämän kentän avulla haetaan asetetaan käyttäjän lokalisaatio"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch
msgid ""
"This field should be used when accessing view arch. It will use translation.\n"
"                               Note that it will read `arch_db` or `arch_fs` if in dev-xml mode."
msgstr ""
"Tätä kenttää on käytettävä, kun käytetään näkymäkaarta. Se käyttää käännöstä.\n"
"                               Huomaa, että se lukee `arch_db` tai `arch_fs`, jos se on dev-xml-tilassa."

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch_db
msgid "This field stores the view arch."
msgstr "Tähän kenttään tallennetaan näkymän arch."

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch_prev
msgid ""
"This field will save the current `arch_db` before writing on it.\n"
"                                                                         Useful to (soft) reset a broken view."
msgstr ""
"Tämä kenttä tallentaa nykyisen `arch_db`-tietokannan ennen kuin siihen kirjoitetaan.\n"
"                                                                         Hyödyllinen rikkinäisen näkymän (pehmeään) nollaamiseen."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"This font already exists, you can only add it as a local font to replace the"
" server version."
msgstr ""
"Tämä fontti on jo olemassa, voit vain lisätä sen paikallisena fonttina "
"palvelinversion tilalle."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "This font is hosted and served to your visitors by Google servers"
msgstr ""
"Fonttia säilytetään Googlen palvelimilla ja se välitetään kävijöille myös "
"sieltä."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "This is a \""
msgstr "Tämä on \""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_banner
msgid ""
"This is a simple hero unit, a simple jumbotron-style component for calling "
"extra attention to featured content or information."
msgstr ""
"Tämä on yksinkertainen sankariyksikkö. Yksinkertainen jumbotron-tyylinen "
"komponentti, jolla kiinnitetään erityistä huomiota esillä olevaan sisältöön "
"tai tietoon."

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "This message has been posted on your website!"
msgstr "Tämä viesti on lähetetty verkkosivustoltasi!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "This page"
msgstr "Tämä sivu"

#. module: website
#: code:addons/website/models/website_visitor.py:0
#, python-format
msgid "This operator is not supported"
msgstr "Operaattoria ei tueta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid ""
"This page does not exist, but you can create it as you are editor of this "
"site."
msgstr ""
"Tätä sivua ei ole olemassa, mutta voit luoda sen, koska olet tämän sivuston "
"muokkaaja."

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "This page is in the menu <b>%s</b>"
msgstr "Tämä sivu on valikossa <b>%s</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "This page will be published on {{ date_formatted }}"
msgstr "Tämä sivu julkaistaan {{ date_formatted }}"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "This translation is not editable."
msgstr "Tätä käännöstä ei voi muokata."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid ""
"This value will be escaped to be compliant with all major browsers and used "
"in url. Keep it empty to use the default name of the record."
msgstr ""
"Arvon asettaminen on pakollista, jotta se on yhteensopiva kaikkien "
"tärkeimpien selainten kanssa, ja sitä käytetään url-osoitteessa. Pidä se "
"tyhjänä, jos haluat käyttää tietueen oletusnimeä."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.report_viewhierarchy_children
msgid "This view arch has been modified"
msgstr "Tätä näkymä-archia on muutettu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid ""
"Those accounts should now check their Analytics dashboard in the Google "
"platform directly."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Thumbnails"
msgstr "Pienoiskuvat"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__time_since_last_action
msgid "Time since last page view. E.g.: 2 minutes ago"
msgstr "Aikaa viimeisestä sivun katselusta. Esim. 2 minuuttia sitten"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__cache_time
msgid "Time to cache the page. (0 = no cache)"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
#, python-format
msgid "Time's up! You can now visit"
msgstr "Aika loppui! Voit nyt käydä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
msgid "Timeline"
msgstr "Aikajana"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__timezone
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Timezone"
msgstr "Aikavyöhyke"

#. module: website
#: model:digest.tip,name:website.digest_tip_website_4
#: model_terms:digest.tip,tip_description:website.digest_tip_website_4
msgid "Tip: Add shapes to energize your Website"
msgstr "Vinkki: Lisää muotoja verkkosivusi voimannuttamiseksi"

#. module: website
#: model:digest.tip,name:website.digest_tip_website_0
#: model_terms:digest.tip,tip_description:website.digest_tip_website_0
msgid "Tip: Engage with visitors to convert them into leads"
msgstr "Vinkki: Osallistuta kävijöitä, jotta saat heistä liidejä"

#. module: website
#: model:digest.tip,name:website.digest_tip_website_2
#: model_terms:digest.tip,tip_description:website.digest_tip_website_2
msgid "Tip: Search Engine Optimization (SEO)"
msgstr "Vihje: Hakukoneoptimointi (SEO)"

#. module: website
#: model:digest.tip,name:website.digest_tip_website_3
#: model_terms:digest.tip,tip_description:website.digest_tip_website_3
msgid "Tip: Use illustrations to spice up your website"
msgstr "Vinkki: Käytä kuvitusta verkkosivujesi piristämiseen"

#. module: website
#: model:digest.tip,name:website.digest_tip_website_1
#: model_terms:digest.tip,tip_description:website.digest_tip_website_1
msgid "Tip: Use royalty-free photos"
msgstr "Vinkki: Käytä rojaltivapaita kuvia"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Title"
msgstr "Otsikko"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_share_options
msgid "Title Position"
msgstr "Nimike ja asema"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"To add a fourth column, reduce the size of these three columns using the "
"right icon of each block. Then, duplicate one of the columns to create a new"
" one as a copy."
msgstr ""
"Jos haluat lisätä neljännen sarakkeen, pienennä näiden kolmen sarakkeen "
"kokoa kunkin lohkon oikeanpuoleisen kuvakkeen avulla. Kopioi sitten yksi "
"sarakkeista luodaksesi uuden sarakkeen kopiona."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "To be successful your content needs to be useful to your readers."
msgstr "Onnistuakseen sisältösi on oltava hyödyllistä lukijoille."

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_2
msgid ""
"To get more visitors, you should target keywords that are often searched in "
"Google. With the built-in SEO tool, once you define a few keywords, Odoo "
"will recommend you the best keywords to target. Then adapt your title and "
"description accordingly to boost your traffic."
msgstr ""
"Saadaksesi enemmän kävijöitä sinun tulisi kohdistaa hakusanat, joita "
"etsitään usein Googlessa. Sisäänrakennetun SEO-työkalun avulla Odoo "
"suosittelee sinulle parhaita kohdennettavia avainsanoja, kun määrittelet "
"muutaman avainsanan. Mukauta sitten otsikkoasi ja kuvaustasi sen mukaisesti "
"lisätäksesi liikennettäsi."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"To send invitations in B2B mode, open a contact or select several ones in "
"list view and click on 'Portal Access Management' option in the dropdown "
"menu *Action*."
msgstr ""
"Lähettääksesi kutsuja B2B-tilassa, avaa kontakti tai valitse useita "
"listanäkymässä, ja klikkaa \"Portaalin käyttöoikeuksien hallinta\" "
"\"Toiminto\"-pudotusvalikosta."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Toggle"
msgstr "Kytke"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.option_header_off_canvas
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Toggle navigation"
msgstr "Kytke navigaatio päälle/pois"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Tony Fred, CEO"
msgstr "Tony Fred, CEO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Tooltip"
msgstr "Työkaluvinkki"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Top"
msgstr "Ylin"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Top Menu"
msgstr "Päävalikko"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "Top Menu for Website %s"
msgstr "Päävalikko verkkosivulle %s"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Top to Bottom"
msgstr "Ylhäältä alas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Tops"
msgstr "Ylimmät"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__page_count
msgid "Total number of tracked page visited"
msgstr "Kokonaismäärä seurattujen sivujen käynneistä"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__visitor_page_count
msgid "Total number of visits on tracked pages"
msgstr "Kokonaismäärä käynneistä seuratuilla sivuilla"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__track
#: model:ir.model.fields,field_description:website.field_website_page__track
msgid "Track"
msgstr "Esitys"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/track_page.xml:0
#, python-format
msgid "Track Visitor"
msgstr "Esityksen vierailijat"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Track visits in Google Analytics"
msgstr "Seuraa vierailuja Google Analyticsissä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Tracked"
msgstr "Seuratut"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Transition"
msgstr "Siirtymä"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "Translate Attribute"
msgstr "Käännä ominaisuus"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "Translate Selection Option"
msgstr "Käännä valintavaihtoehto"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "Translate header in the text. Menu is generated automatically."
msgstr "Käännä otsikko tekstissä. Valikko luodaan automaattisesti."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/translator.xml:0
#, python-format
msgid "Translated content"
msgstr "Käännetty sisältö"

#. module: website
#: model:ir.model,name:website.model_ir_translation
msgid "Translation"
msgstr "Käännös"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "Translation Info"
msgstr "Käännöksen tiedot"

#. module: website
#: model:ir.model.fields,help:website.field_website__configurator_done
msgid "True if configurator has been completed or ignored"
msgstr ""
"True, jos konfiguraattori on valmis tai sitä ei ole jätetty huomioimatta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Tuna and Salmon Burger"
msgstr "Tonnikala ja lohihampurilainen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Turn every feature into a benefit for your reader."
msgstr "Muuta jokainen ominaisuus hyödyksi lukijallesi."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Twitter"
msgstr "Twitter"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_twitter
#: model:ir.model.fields,field_description:website.field_website__social_twitter
msgid "Twitter Account"
msgstr "Twitter-tili"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Twitter Scroller"
msgstr "Twitter Scroller"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__type
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Type"
msgstr "Tyyppi"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Type '"
msgstr "Tyyppi '"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid ""
"Type '<i class=\"confirm_word\">yes</i>' in the box below if you want to "
"confirm."
msgstr ""
"Kirjoita '<i class=\"confirm_word\">yes</i>' alla olevaan laatikkoon jos "
"haluat vahvistaa."

#. module: website
#: model:ir.model.fields,help:website.field_website_rewrite__redirect_type
msgid ""
"Type of redirect/Rewrite:\n"
"\n"
"        301 Moved permanently: The browser will keep in cache the new url.\n"
"        302 Moved temporarily: The browser will not keep in cache the new url and ask again the next time the new url.\n"
"        404 Not Found: If you want remove a specific page/controller (e.g. Ecommerce is installed, but you don't want /shop on a specific website)\n"
"        308 Redirect / Rewrite: If you want rename a controller with a new url. (Eg: /shop -> /garden - Both url will be accessible but /shop will automatically be redirected to /garden)\n"
"    "
msgstr ""
"Uudelleenohjauksen/uudelleen kirjoittamisen tyyppi:\n"
"\n"
"        301 Moved permanently: Selain säilyttää uuden url-osoitteen välimuistissa.\n"
"        302 Siirretty väliaikaisesti: Selain ei pidä uutta url-osoitetta välimuistissa ja kysyy seuraavan kerran uutta url-osoitetta uudelleen.\n"
"        404 Not Found: Jos haluat poistaa tietyn sivun/ohjaimen (esim. verkkokauppa on asennettu, mutta et halua /shopia tietylle sivustolle)\n"
"        308 Redirect / Rewrite: Jos haluat nimetä ohjaimen uudelleen uudella url-osoitteella. (Esim: /shop -> /garden - Molemmat url:t ovat käytettävissä, mutta /shop ohjataan automaattisesti /gardeniin)\n"
"    "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "URL"
msgstr "URL"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__url_from
#: model_terms:ir.ui.view,arch_db:website.view_website_rewrite_form
msgid "URL from"
msgstr "URL lähde"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__cdn_filters
#: model:ir.model.fields,help:website.field_website__cdn_filters
msgid "URL matching those filters will be rewritten using the CDN Base URL"
msgstr ""
"URL-osoite, joka vastaa näitä suodattimia, kirjoitetaan uudelleen käyttäen "
"CDN Base URL-osoitetta"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__url_to
msgid "URL to"
msgstr "URL kohde"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Unalterable unique identifier"
msgstr "Muuttumaton yksilöllinen tunniste"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Underline On Hover"
msgstr "Alleviivaa kun hiirikursori viedään päälle"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Understand how visitors engage with our website, via Google Analytics.\n"
"                                                Learn more about"
msgstr ""
"Opi, miten kävijät käyttävät verkkosivustoamme Google Analyticsin avulla.\n"
"                                                Lisätietoja"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.index_management
msgid "Unindexed"
msgstr "Indeksoimaton"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Unlimited CRM power and support"
msgstr "Rajoittamaton CRM teho ja tuki"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Unlimited customization"
msgstr "Rajoittamaton mukautus"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/button.js:0
#, python-format
msgid "Unpublish"
msgstr "Poista näkyvistä"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/button.js:0
#: code:addons/website/static/src/js/backend/button.js:0
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.publish_short
#, python-format
msgid "Unpublished"
msgstr "Julkaisematon"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Unregistered"
msgstr "Rekisteröimätön"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_kanban
msgid "Update theme"
msgstr "Päivitä teema"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Upload"
msgstr "Upload"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "Uploaded file is too large."
msgstr "Lähetetty tiedosto on liian suuri."

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__url
#: model:ir.model.fields,field_description:website.field_theme_website_menu__url
#: model:ir.model.fields,field_description:website.field_theme_website_page__url
#: model:ir.model.fields,field_description:website.field_website_menu__url
#: model:ir.model.fields,field_description:website.field_website_track__url
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
#: model_terms:ir.ui.view,arch_db:website.menu_search
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
msgid "Url"
msgstr "Verkko-osoite"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__country_flag
msgid "Url of static flag image"
msgstr "Staattisen lippukuvan Url"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
msgid "Urls & Pages"
msgstr "URL:it ja sivut"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Use Google Map on your website ("
msgstr "Käytä Google Mapsia verkkosivulla ("

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Use Google Map on your website (Contact Us page, snippets, etc)."
msgstr ""
"Käytä Google Mapia verkkosivustollasi (Yhteystiedot-sivulla, snippeteissä "
"jne.)."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Use a CDN to optimize the availability of your website's content"
msgstr "Käytä CDN:ia optimoidaksesi verkkosivun sisällön saatavuuden"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_default_share_image
msgid "Use a image by default for sharing"
msgstr "Käytä oletusarvoisesti kuvaa jakamiseen"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Use as Homepage"
msgstr "Käytä kotisivuna"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Use of Cookies"
msgstr "Käytä evästeitä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid ""
"Use this component for creating a list of featured elements to which you "
"want to bring attention."
msgstr ""
"Käytä tätä komponenttia luodaksesi luettelon esillä olevista elementeistä, "
"joihin haluat kiinnittää huomiota."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid ""
"Use this snippet to build various types of components that feature a left- "
"or right-aligned image alongside textual content. Duplicate the element to "
"create a list that fits your needs."
msgstr ""
"Tämän pätkän avulla voit rakentaa erilaisia komponentteja, joissa on "
"vasemmalle tai oikealle kohdistettu kuva tekstisisällön ohella. Monista "
"elementti luodaksesi tarpeisiisi sopivan luettelon."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"Use this snippet to presents your content in a slideshow-like format. Don't "
"write about products or services here, write about solutions."
msgstr ""
"Käytä tätä pätkää sisällön esittämiseen diaesityksen kaltaisessa muodossa. "
"Älä kirjoita tässä tuotteista tai palveluista vaan ratkaisuista."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_kanban
msgid "Use this theme"
msgstr "Käytä tätä teemaa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid ""
"Use this timeline as a part of your resume, to show your visitors what "
"you've done in the past."
msgstr ""
"Käytä tätä aikajanaa osana ansioluetteloasi ja näytä vierailijoille, mitä "
"olet tehnyt aiemmin."

#. module: website
#: model:ir.model.fields,help:website.field_ir_model__website_form_key
msgid "Used in FormBuilder Registry"
msgstr "Käytetään FormBuilder-rekisterissä"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Used in page content"
msgstr "Käytetään sivun sisällössä"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Used in page description"
msgstr "Käytetään sivun kuvauksessa"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Used in page first level heading"
msgstr "Käytetään sivun ensimmäisen tason otsikossa"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Used in page second level heading"
msgstr "Käytetään sivun toisen tason otsikossa"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Used in page title"
msgstr "Käytetään sivun otsikossa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Used to collect information about your interactions with the website, the pages you've seen,\n"
"                                                and any specific marketing campaign that brought you to the website."
msgstr ""
"Käytetään keräämään tietoja vuorovaikutuksestasi verkkosivuston kanssa ja näkemistäsi sivuista,\n"
"                                                ja kaikista markkinointikampanjoista, jotka toivat sinut verkkosivustolle."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Used to make advertising more engaging to users and more valuable to publishers and advertisers,\n"
"                                                such as providing more relevant ads when you visit other websites that display ads or to improve reporting on ad campaign performance."
msgstr ""
"Käytetään tekemään mainonnasta houkuttelevampaa käyttäjille ja arvokkaampaa julkaisijoille ja mainostajille,\n"
"                                                esimerkiksi tarjoamalla relevantimpia mainoksia, kun vierailet muilla mainoksia näyttävillä verkkosivustoilla, tai parantamalla mainoskampanjan suorituskykyä koskevaa raportointia."

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_country_group_ids
#: model:ir.model.fields,help:website.field_website__country_group_ids
msgid "Used when multiple websites have the same domain."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Useful Links"
msgstr "Hyödyllisiä linkkejä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "Useful options"
msgstr "Hyödyllisiä vaihtoehtoja"

#. module: website
#: model:ir.model.fields,help:website.field_website_menu__group_ids
msgid "User need to be at least in one of these groups to see the menu"
msgstr ""
"Käyttäjän on kuuluttava vähintään yhteen näistä ryhmistä nähdäkseen valikon"

#. module: website
#: model:ir.model,name:website.model_res_users
msgid "Users"
msgstr "Käyttäjät"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Utilities &amp; Typography"
msgstr "Apuohjelmat ja Typografia"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Value"
msgstr "Arvo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Vert. Alignment"
msgstr "Pystysuun. kohdistus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Vertical"
msgstr "Pystysuuntainen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Vertical Alignment"
msgstr "Pystysuuntainen kohdistus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Video"
msgstr "Video"

#. module: website
#: model:ir.model,name:website.model_ir_ui_view
#: model:ir.model.fields,field_description:website.field_theme_website_page__view_id
#: model:ir.model.fields,field_description:website.field_website_page__view_id
msgid "View"
msgstr "Näytä"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch
msgid "View Architecture"
msgstr "Näkymäarkkitehtuuri"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__name
msgid "View Name"
msgstr "Näkymän nimi"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__type
msgid "View Type"
msgstr "Näkymän tyyppi"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__mode
msgid "View inheritance mode"
msgstr "Näkymän perintätapa"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__copy_ids
msgid "Views using a copy of me"
msgstr "Näytä käyttäen kopiotani"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__inherit_children_ids
msgid "Views which inherit from this one"
msgstr "Tästä näkymästä periytyvät näkymät"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields,field_description:website.field_ir_ui_view__visibility
#: model:ir.model.fields,field_description:website.field_website_page__visibility
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Visibility"
msgstr "Näkyvyys"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__visibility_password
#: model:ir.model.fields,field_description:website.field_website_page__visibility_password
#: model_terms:ir.ui.view,arch_db:website.view_view_form_extend
msgid "Visibility Password"
msgstr "Näkyvyyden salasana"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__visibility_password_display
#: model:ir.model.fields,field_description:website.field_website_page__visibility_password_display
msgid "Visibility Password Display"
msgstr "Näkyvyyden salasanan näyttäminen"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__group_ids
msgid "Visible Groups"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_conditional_visibility
msgid "Visible for"
msgstr "Näkyvissä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Visible for Everyone"
msgstr "Näkyy kaikille"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Visible for Logged In"
msgstr "Näkyy kirjautuneille"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Visible for Logged Out"
msgstr "Näkyy uloskirjautuneille"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_partner__website_published
#: model:ir.model.fields,field_description:website.field_res_users__website_published
#: model:ir.model.fields,field_description:website.field_website_page__website_published
#: model:ir.model.fields,field_description:website.field_website_published_mixin__website_published
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__website_published
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__website_published
msgid "Visible on current website"
msgstr "Näkyy nykysellä verkkosivulla"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Visible on mobile"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Visible only if"
msgstr "Näkyy vain, jos"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_track__visit_datetime
msgid "Visit Date"
msgstr "Vierailupäivä"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
#, python-format
msgid "Visit our Facebook page to know if you are one of the lucky winners."
msgstr "Käy Facebook-sivullamme! Onni on voinut potkaista."

#. module: website
#: model:ir.model,name:website.model_website_track
#: model:ir.model.fields,field_description:website.field_website_visitor__page_ids
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Visited Pages"
msgstr "Vieraillut sivut"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__website_track_ids
msgid "Visited Pages History"
msgstr "Vierailtujen sivujen historia"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_track__visitor_id
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Visitor"
msgstr "Vierailija"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_graph
msgid "Visitor Page Views"
msgstr "Vierailijan sivujen katselut"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_tree
msgid "Visitor Page Views History"
msgstr "Vierailijan sivujen katseluhistoria"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_track_view_graph
msgid "Visitor Views"
msgstr "Vierailijan katselut"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_track_view_tree
msgid "Visitor Views History"
msgstr "Vierailijan katseluhistoria"

#. module: website
#: model:ir.actions.act_window,name:website.website_visitors_action
#: model:ir.model.fields,field_description:website.field_res_partner__visitor_ids
#: model:ir.model.fields,field_description:website.field_res_users__visitor_ids
#: model:ir.ui.menu,name:website.menu_visitor_sub_menu
#: model:ir.ui.menu,name:website.website_visitor_menu
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_graph
msgid "Visitors"
msgstr "Vierailijat"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#, python-format
msgid "Visits"
msgstr "Käyntiä"

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitor_view_action
msgid ""
"Wait for visitors to come to your website to see the pages they viewed."
msgstr ""
"Odota, että kävijät tulevat verkkosivustollesi nähdäksesi heidän "
"katselemansa sivut."

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitors_action
msgid ""
"Wait for visitors to come to your website to see their history and engage "
"with them."
msgstr ""
"Odota, että kävijät tulevat verkkosivustollesi nähdäksesi heidän historiansa"
" ja ottaaksesi heihin yhteyttä."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Warning"
msgstr "Varoitus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Watches"
msgstr "Seurannat"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid ""
"We are a team of passionate people whose goal is to improve everyone's life through disruptive products. We build great products to solve your business problems.\n"
"                            <br/><br/>Our products are designed for small to medium size companies willing to optimize their performance."
msgstr ""
"Olemme intohimoisten ihmisten tiimi, jonka tavoitteena on parantaa kaikkien elämää mullistavien tuotteiden avulla. Rakennamme loistavia tuotteita liiketoimintasi ongelmien ratkaisemiseksi.\n"
"                           <br/><br/>Tuotteemme on suunniteltu pienille ja keskisuurille yrityksille, jotka haluavat optimoida suorituskykynsä."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
msgid ""
"We are a team of passionate people whose goal is to improve everyone's life "
"through disruptive products. We build great products to solve your business "
"problems. Our products are designed for small to medium size companies "
"willing to optimize their performance."
msgstr ""
"Olemme intohimoisten ihmisten tiimi, jonka tavoitteena on parantaa kaikkien "
"elämää mullistavien tuotteiden avulla. Rakennamme loistavia tuotteita "
"liiketoimintasi ongelmien ratkaisemiseksi. Tuotteemme on suunniteltu "
"pienille ja keskisuurille yrityksille, jotka haluavat optimoida "
"suorituskykynsä."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
msgid ""
"We are a team of passionate people whose goal is to improve everyone's "
"life.<br/>Our services are designed for small to medium size companies."
msgstr ""
"Olemme joukko intohimoisia ihmisiä, joiden tavoitteena on parantaa kaikkien "
"elämää.<br/>Palvelumme on suunniteltu pienille ja keskisuurille yrityksille."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar
msgid "We are almost done!"
msgstr "Olemme melkein valmiita!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid "We are in good company."
msgstr "Olemme hyvässä seurassa."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"We do not currently support Do Not Track signals, as there is no industry "
"standard for compliance."
msgstr ""
"Emme tällä hetkellä tue Do Not Track -signaaleja, koska niiden noudattamista"
" varten ei ole olemassa alan standardia."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "We found these ones:"
msgstr "Löysimme nämä:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_header_slogan_oe_structure_header_slogan_1
msgid "We help <b>you</b> grow your business"
msgstr "Autamme <b>sinua</b> kasvattamaan liiketoimintaasi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"We may not be able to provide the best service to you if you reject those "
"cookies, but the website will work."
msgstr ""
"Emme välttämättä pysty tarjoamaan sinulle parasta palvelua, jos hylkäät nämä"
" evästeet, mutta verkkosivusto toimii silti."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "We offer tailor-made products according to your needs and your budget."
msgstr "Tarjoamme räätälöityjä tuotteita tarpeidesi ja budjettisi mukaan."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: model_terms:ir.ui.view,arch_db:website.cookies_bar
#, python-format
msgid "We use cookies to provide you a better user experience."
msgstr "Käytämme evästeitä tarjotaksemme sinulle paremman käyttökokemuksen."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#, python-format
msgid ""
"We use them to store info about your habits on our website. It will helps us"
" to provide you the very best experience and customize what you see."
msgstr ""
"Käytämme niitä tallentamaan tietoja tottumuksistasi verkkosivustollamme. Se "
"auttaa meitä tarjoamaan sinulle parhaan kokemuksen ja mukauttamaan "
"näkemääsi."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid "We will get back to you shortly."
msgstr "Otamme sinuun yhteyttä mahdollisimman nopeasti."

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "We'll set you up and running in"
msgstr "Saatamme sinut alkuun ja käyntiin"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
msgid "Web Visitors"
msgstr "Verkkosivun vierailijat"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/xml/website.backend.xml:0
#: model:ir.actions.act_url,name:website.action_website
#: model:ir.model,name:website.model_website
#: model:ir.model.fields,field_description:website.field_ir_asset__website_id
#: model:ir.model.fields,field_description:website.field_ir_attachment__website_id
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_id
#: model:ir.model.fields,field_description:website.field_res_company__website_id
#: model:ir.model.fields,field_description:website.field_res_partner__website_id
#: model:ir.model.fields,field_description:website.field_res_users__website_id
#: model:ir.model.fields,field_description:website.field_website_menu__website_id
#: model:ir.model.fields,field_description:website.field_website_multi_mixin__website_id
#: model:ir.model.fields,field_description:website.field_website_page__website_id
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__website_id
#: model:ir.model.fields,field_description:website.field_website_rewrite__website_id
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__website_id
#: model:ir.model.fields,field_description:website.field_website_visitor__website_id
#: model:ir.ui.menu,name:website.menu_website_configuration
#: model_terms:ir.ui.view,arch_db:website.menu_search
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.view_server_action_search_website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
#, python-format
msgid "Website"
msgstr "Verkkosivu"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_company_id
msgid "Website Company"
msgstr "Verkkosivun yritys"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__website_config_preselection
msgid "Website Config Preselection"
msgstr "Verkkosivuston konfiguroinnin esivalinta"

#. module: website
#: model:ir.actions.act_url,name:website.start_configurator_act_url
msgid "Website Configurator"
msgstr "Verkkosivuston konfiguraattori"

#. module: website
#: model:ir.model,name:website.model_website_configurator_feature
msgid "Website Configurator Feature"
msgstr "Verkkosivuston konfiguraattorin ominaisuus"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_domain
#: model:ir.model.fields,field_description:website.field_website__domain
msgid "Website Domain"
msgstr "Verkkosivun verkkotunnus"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__favicon
msgid "Website Favicon"
msgstr "Verkkosivun favicon"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model__website_form_key
msgid "Website Form Key"
msgstr "Verkkosivun lomakkeen avain"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.ir_model_view
msgid "Website Forms"
msgstr "Verkkosivun lomakkeet"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_logo
#: model:ir.model.fields,field_description:website.field_website__logo
#, python-format
msgid "Website Logo"
msgstr "Verkkosivun logo"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_menu
#: model:ir.model,name:website.model_website_menu
msgid "Website Menu"
msgstr "Verkkosivuston valikko"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_menus_form_view
msgid "Website Menus Settings"
msgstr "Verkkosivun valikoiden asetukset"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_name
#: model:ir.model.fields,field_description:website.field_website__name
msgid "Website Name"
msgstr "Verkkosivun nimi"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__first_page_id
#: model:ir.model.fields,field_description:website.field_website_page__first_page_id
msgid "Website Page"
msgstr "Verkkosivun sivu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_form_view
msgid "Website Page Settings"
msgstr "Verkkosivun sivun asetukset"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_pages_list
#: model_terms:ir.ui.view,arch_db:website.website_pages_tree_view
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Website Pages"
msgstr "Verkkosivun sivut"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_actions_server__website_path
#: model:ir.model.fields,field_description:website.field_ir_cron__website_path
msgid "Website Path"
msgstr "Verkkosivun polku"

#. module: website
#: model:ir.model,name:website.model_website_published_mixin
msgid "Website Published Mixin"
msgstr "Website Published Mixin"

#. module: website
#: model:ir.model,name:website.model_website_searchable_mixin
msgid "Website Searchable Mixin"
msgstr "Verkkosivuston hakukelpoinen Mixin"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: model_terms:ir.ui.view,arch_db:website.view_website_form
#, python-format
msgid "Website Settings"
msgstr "Verkkosivuston asetukset"

#. module: website
#: model:ir.model,name:website.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "Verkkosivuston pätkäsuodatin"

#. module: website
#: model:ir.model,name:website.model_theme_website_menu
msgid "Website Theme Menu"
msgstr "Verkkosivun teemavalikko"

#. module: website
#: model:ir.model,name:website.model_theme_website_page
msgid "Website Theme Page"
msgstr "Verkkosivun teemasivu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Website Title"
msgstr "Verkkosivun otsikko"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_attachment__website_url
#: model:ir.model.fields,field_description:website.field_res_partner__website_url
#: model:ir.model.fields,field_description:website.field_res_users__website_url
#: model:ir.model.fields,field_description:website.field_website_page__website_url
#: model:ir.model.fields,field_description:website.field_website_published_mixin__website_url
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__website_url
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__website_url
msgid "Website URL"
msgstr "Verkkosivuston osoite"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_actions_server__website_url
#: model:ir.model.fields,field_description:website.field_ir_cron__website_url
msgid "Website Url"
msgstr "Verkkosivun URL"

#. module: website
#: model:ir.model,name:website.model_website_visitor
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Website Visitor"
msgstr "Verkkosivun vierailija"

#. module: website
#: code:addons/website/models/website_visitor.py:0
#, python-format
msgid "Website Visitor #%s"
msgstr "Verkkosivun vierailija #%s"

#. module: website
#: model:ir.actions.server,name:website.website_visitor_cron_ir_actions_server
#: model:ir.cron,cron_name:website.website_visitor_cron
#: model:ir.cron,name:website.website_visitor_cron
msgid "Website Visitor : Archive old visitors"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"Website may use cookies to personalize and facilitate maximum navigation of "
"the User by this site. The User may configure his / her browser to notify "
"and reject the installation of the cookies sent by us."
msgstr ""
"Verkkosivusto voi käyttää evästeitä yksilöidäkseen ja helpottaakseen "
"käyttäjän mahdollisimman hyvää navigointia tällä sivustolla. Käyttäjä voi "
"määrittää selaimensa ilmoittamaan ja hylkäämään lähettämiemme evästeiden "
"asennuksen."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_tree
msgid "Website menu"
msgstr "Verkkosivuston valikko"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_description
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_description
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_description
msgid "Website meta description"
msgstr "Verkkosivuston metakuvaus"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_keywords
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_keywords
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_keywords
msgid "Website meta keywords"
msgstr "Verkkosivuston meta-avainsanat"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_title
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_title
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_title
msgid "Website meta title"
msgstr "Verkkosivuston metaotsikko"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_og_img
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_og_img
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_og_img
msgid "Website opengraph image"
msgstr "Verkkosivun opengraph kuva"

#. module: website
#: model:ir.model,name:website.model_website_rewrite
msgid "Website rewrite"
msgstr "Verkkosivuston uudelleenkirjoitus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_rewrite_form
msgid "Website rewrite Settings"
msgstr "Verkkosivuston uudelleenkirjoitus Asetukset"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.action_website_rewrite_tree
msgid "Website rewrites"
msgstr "Verkkosivuston uudelleenkirjoitukset"

#. module: website
#: model:ir.actions.server,name:website.ir_actions_server_website_google_analytics
msgid "Website: Analytics"
msgstr "Verkkosivusto: Analytics"

#. module: website
#: model:ir.actions.server,name:website.ir_actions_server_website_dashboard
msgid "Website: Dashboard"
msgstr "Verkkosivusto: Dashboard"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_list
#: model:ir.ui.menu,name:website.menu_website_websites_list
#: model_terms:ir.ui.view,arch_db:website.view_website_tree
msgid "Websites"
msgstr "Verkkosivut"

#. module: website
#: model:ir.model.fields,field_description:website.field_base_language_install__website_ids
msgid "Websites to translate"
msgstr "Käännettävät verkkosivustot"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Websites-shared page"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Welcome to your"
msgstr "Tervetuloa sinun"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "What you see is what you get"
msgstr "Mitä näet, sitä myös saat"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Width"
msgstr "Leveys"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__ir_ui_view__visibility__password
msgid "With Password"
msgstr "Salasanalla"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Women"
msgstr "Naiset"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid ""
"Would you like to save before being redirected? Unsaved changes will be "
"discarded."
msgstr ""
"Haluatko tallentaa ennen uudelleenohjausta? Tallentamattomat muutokset "
"hylätään."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"Write a quote here from one of your customers. Quotes are a great way to "
"build confidence in your products or services."
msgstr ""
"Kirjoita tähän lainaus joltakin asiakkaaltasi. Lainaukset ovat hyvä tapa "
"lisätä luottamusta tuotteisiisi tai palveluihisi."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "Write one or two paragraphs describing your product or services."
msgstr ""
"Kirjoita yksi tai kaksi kappaletta, joissa kuvailet tuotteesi tai palvelusi."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid ""
"Write one or two paragraphs describing your product or services. To be "
"successful your content needs to be useful to your readers."
msgstr ""
"Kirjoita yksi tai kaksi kappaletta, joissa kuvailet tuotteesi tai palvelusi."
" Onnistuakseen sisältösi on oltava hyödyllistä lukijoille."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cover
msgid ""
"Write one or two paragraphs describing your product, services or a specific "
"feature.<br/> To be successful your content needs to be useful to your "
"readers."
msgstr ""
"Kirjoita yksi tai kaksi kappaletta, joissa kuvataan tuotetta, palveluja tai "
"tiettyä ominaisuutta.<br/> Onnistuakseen sisältösi on oltava hyödyllistä "
"lukijoille."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid ""
"Write what the customer would like to know, <br/>not what you want to show."
msgstr ""
"Kirjoita se, mitä asiakas haluaa tietää, <br/>ei sitä, mitä haluat näyttää."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline_options
msgid "Year"
msgstr "Vuosi"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/translator.xml:0
#, python-format
msgid "You are about to enter the translation mode."
msgstr "Olet siirtymässä käännöstilaan."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"You can choose to have your computer warn you each time a cookie is being sent, or you can choose to turn off all cookies.\n"
"                            Each browser is a little different, so look at your browser's Help menu to learn the correct way to modify your cookies."
msgstr ""
"Voit valita, että tietokoneesi varoittaa sinua aina, kun eväste lähetetään, tai voit poistaa kaikki evästeet käytöstä.\n"
"                            Jokainen selain on hieman erilainen, joten katso selaimesi Ohje-valikkoa saadaksesi tietää oikean tavan muokata evästeitäsi."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "You can edit colors and backgrounds to highlight features."
msgstr "Voit muokata värejä ja taustoja korostamaan ominaisuuksia."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "You can edit, duplicate..."
msgstr "Voit muokata, kopioida..."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"You can have 2 websites with same domain AND a condition on country group to"
" select wich website use."
msgstr ""

#. module: website
#: code:addons/website/models/res_users.py:0
#: model:ir.model.constraint,message:website.constraint_res_users_login_key
#, python-format
msgid "You can not have two users with the same login!"
msgstr "Sinulla ei voi olla kahta käyttäjää, joilla on sama käyttäjätunnus!"

#. module: website
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "You can only use template prefixed by dynamic_filter_template_ "
msgstr ""
"Voit käyttää vain mallia, jonka etuliitteenä on dynamic_filter_template_"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "You can't duplicate a model field."
msgstr "Et voi kahdentaa mallin kenttää."

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "You can't duplicate the submit button of the form."
msgstr "Et voi kahdentaa lomakkeen lähetyspainiketta."

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "You can't remove a field that is required by the model itself."
msgstr "Et voi poistaa kenttää jonka malli vaatii."

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "You can't remove the submit button of the form"
msgstr "Et voi poistaa lomakkeen lähetyspainiketta"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid ""
"You cannot delete default website %s. Try to change its settings instead"
msgstr ""
"Et voi poistaa oletusverkkosivua %s. Yritä ennemmin muuttaa sen asetuksia\n"
" "

#. module: website
#: code:addons/website/controllers/backend.py:0
#, python-format
msgid "You do not have sufficient rights to perform that action."
msgstr ""

#. module: website
#: code:addons/website/models/mixins.py:0
#, python-format
msgid "You do not have the rights to publish/unpublish"
msgstr "Sinulla ei ole oikeuksia julkaista/julkaisematta jättämiseen"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "You do not seem to have access to this Analytics Account."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid ""
"You have hidden this page from search results. It won't be indexed by search"
" engines."
msgstr ""
"Olet piilottanut tämän sivun hakutuloksista. Hakukoneet eivät indeksoi sitä."

#. module: website
#: code:addons/website/models/res_config_settings.py:0
#, python-format
msgid "You haven't defined your domain"
msgstr "Et ole määritellyt verkkotunnusta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "You may opt-out of a third-party's use of cookies by visiting the"
msgstr ""
"Voit kieltää kolmannen osapuolen evästeiden käytön vierailemalla osoitteessa"
" osoitteessa"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "You must keep at least one website."
msgstr "Sinun on pidettävä vähintään yksi verkkosivusto."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "You need to log in to your Google Account before:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"You should carefully review the legal statements and other conditions of use"
" of any website which you access through a link from this Website. Your "
"linking to any other off-site pages or other websites is at your own risk."
msgstr ""
"Sinun on syytä tutustua huolellisesti kaikkien sellaisten verkkosivustojen "
"oikeudellisiin lausekkeisiin ja muihin käyttöehtoihin, joihin pääset tältä "
"verkkosivustolta linkin kautta. Linkittämisesi muille sivustojen "
"ulkopuolisille sivuille tai muille verkkosivustoille tapahtuu omalla "
"vastuullasi."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_searchbar
msgid "You will get results from blog posts, products, etc"
msgstr "Saat tuloksia blogikirjoituksista, tuotteista jne"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "You'll be able to create your pages later on."
msgstr "Voit luoda sivut myöhemmin."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "YouTube"
msgstr "YouTube"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Your Client ID:"
msgstr "Client ID:"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Your Company"
msgstr "Oma yritys"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_template
msgid ""
"Your Dynamic Snippet will be displayed here... This message is displayed "
"because you did not provided both a filter and a template to use.<br/>"
msgstr ""
"Dynaaminen pätkäsi näytetään tässä... Tämä viesti näytetään, koska et ole "
"antanut sekä suodatinta että mallia käytettäväksi.<br/>"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Your Email"
msgstr "Sähköpostisi"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Your Measurement ID:"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Your Name"
msgstr "Nimesi"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Your Question"
msgstr "Oma kysymys"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid "Your description looks too long."
msgstr "Kuvauksesi on liian pitkä"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid "Your description looks too short."
msgstr "Kuvauksesi on liian lyhyt."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Your experience may be degraded if you discard those cookies, but the "
"website will still work."
msgstr ""
"Kokemuksesi voi heikentyä, jos hylkäät nämä evästeet, mutta verkkosivusto "
"toimii silti."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
msgid "Your search '"
msgstr "Hakusi '"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "Your title"
msgstr "Otsikkosi"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_youtube
#: model:ir.model.fields,field_description:website.field_website__social_youtube
msgid "Youtube Account"
msgstr "YouTube-tili"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Zoom"
msgstr "Zoomaa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Zoom In"
msgstr "Lähennä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Zoom In-Down"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Zoom In-Left"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Zoom In-Right"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Zoom Out"
msgstr "Loitonna"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"__gads (Google)<br/>\n"
"                                            __gac (Google)"
msgstr ""
"__gads (Google)<br/>\n"
"                                            __gac (Google)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"_ga (Google)<br/>\n"
"                                            _gat (Google)<br/>\n"
"                                            _gid (Google)<br/>\n"
"                                            _gac_* (Google)"
msgstr ""
"_ga (Google)<br/>\n"
"                                            _gat (Google)<br/>\n"
"                                            _gid (Google)<br/>\n"
"                                            _gac_* (Google)"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "a blog"
msgstr "blogi"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "a business website"
msgstr "yrityssivusto"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "a new image"
msgstr "uusi kuva"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "a pre-made Palette"
msgstr "valmiiksi tehty paletti"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "an elearning platform"
msgstr "verkko-oppimisympäristö"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "an event website"
msgstr "tapahtuman verkkosivusto"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "an online store"
msgstr "verkkokauppa"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "and"
msgstr "ja"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "and copy paste the address of the font page here."
msgstr "ja kopioi ja liitä kirjasinsivun osoite tänne."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "big"
msgstr "Suuri"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "breadcrumb"
msgstr "murupolku"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "btn-outline-primary"
msgstr "btn-outline-primary"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "btn-outline-secondary"
msgstr "btn-outline-secondary"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "btn-primary"
msgstr "btn-primary"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "btn-secondary"
msgstr "btn-secondary"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "business"
msgstr "liiketoiminta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "celebration, launch"
msgstr "juhla, lanseeraus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "chart, table, diagram, pie"
msgstr "kaavio, taulukko, kaavio, piirakka"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "cite"
msgstr "sitaatti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "columns, description"
msgstr "sarakkeet, kuvaus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "common answers, common questions"
msgstr "yhteiset vastaukset, yhteiset kysymykset"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "content"
msgstr "sisältö"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "customers, clients"
msgstr "asiakkaat, asiakkaat"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "days"
msgstr "päivää"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "develop the brand"
msgstr "kehittää brändiä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "e.g. /my-awesome-page"
msgstr "esim. /oma-hieno-sivu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "e.g. About Us"
msgstr "esim. Tietoa meistä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "e.g. De Brouckere, Brussels, Belgium"
msgstr "esim. De Brouckere, Bryssel, Belgia"

#. module: website
#: model:ir.ui.menu,name:website.menu_website_dashboard
msgid "eCommerce Dashboard"
msgstr "Verkkokaupan työpöytä"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_elearning
msgid "eLearning"
msgstr "Verkko-oppiminen"

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "email"
msgstr "sähköposti"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "esc"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "evolution, growth"
msgstr "evoluutio, kasvu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "fonts.google.com"
msgstr "fonts.google.com"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "for my"
msgstr "minun"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "found(s)"
msgstr "löytynyttä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.brand_promotion
msgid "free website"
msgstr "ilmainen verkkosivu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "from Logo"
msgstr "logosta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "frontend_lang (Odoo)"
msgstr "frontend_lang (Odoo)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "gallery, carousel"
msgstr "galleria, karuselli"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "get leads"
msgstr "saada myyntiliidejä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "google1234567890123456.html"
msgstr "google1234567890123456.html"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "heading, h1"
msgstr "otsikko, h1"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "hero, jumbotron"
msgstr "hero, jumbotron"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "https://fonts.google.com/specimen/Roboto"
msgstr "https://fonts.google.com/specimen/Roboto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "https://www.odoo.com"
msgstr "https://www.odoo.com"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "iPhone"
msgstr "iPhone"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"im_livechat_previous_operator_pid (Odoo)<br/>\n"
"                                            utm_campaign (Odoo)<br/>\n"
"                                            utm_source (Odoo)<br/>\n"
"                                            utm_medium (Odoo)"
msgstr ""
"im_livechat_previous_operator_pid (Odoo)<br/>\n"
"                                            utm_campaign (Odoo)<br/>\n"
"                                            utm_source (Odoo)<br/>\n"
"                                            utm_medium (Odoo)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "image, media, illustration"
msgstr "kuva, media, kuvitus"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "in the top right corner to start designing."
msgstr "oikeassa yläkulmassa aloittaaksesi suunnittelun."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#: model_terms:ir.ui.view,arch_db:website.template_header_contact_oe_structure_header_contact_1
#: model_terms:ir.ui.view,arch_db:website.template_header_hamburger_oe_structure_header_hamburger_3
#: model_terms:ir.ui.view,arch_db:website.template_header_sidebar_oe_structure_header_sidebar_1
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "inform customers"
msgstr "tiedottaa asiakkaille"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "instance of Odoo, the"
msgstr "Odoosta,"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_ui_view__inherit_id__ir_ui_view
msgid "ir.ui.view"
msgstr "ir.ui.view"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "link"
msgstr "linkki"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "masonry, grid"
msgstr "Tiiliseinä, ruudukko"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "menu, pricing"
msgstr "valikko, hinnoittelu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "no value"
msgstr "ei arvoa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "o-color-"
msgstr "o-color-"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.res_config_settings.xml:0
#, python-format
msgid ""
"of\n"
"            your visitors. We recommend you avoid them unless you have\n"
"            verified with a legal advisor that you absolutely need cookie\n"
"            consent in your country."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "or Edit Master"
msgstr "tai Muokkaa lähdekieltä"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "organization, structure"
msgstr "organisaatio, rakenne"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "page, snippets, ...)"
msgstr "sivu, kappaleet, ...)"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "perfect website?"
msgstr "täydellinen sivusto?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_form_preview
msgid "phone"
msgstr "puhelin"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "placeholder"
msgstr "paikanvaraaja"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#, python-format
msgid "pointer to build the perfect page in 7 steps."
msgstr "vinkit täydellisen sivun rakentamiseen seitsemällä askeleella"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "pricing"
msgstr "hinnoittelu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "promotion, characteristic, quality"
msgstr "edistäminen, ominaisuudet, laatu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "results"
msgstr "tulokset"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "rows"
msgstr "riviä"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "schedule appointments"
msgstr "aikatauluta tapaamiset"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "sell more"
msgstr "myy lisää"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "separator, divider"
msgstr "erotin, jakaja"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "session_id (Odoo)<br/>"
msgstr "session_id (Odoo)<br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "statistics, stats, KPI"
msgstr "tilastotiedot, tilastot, KPI"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "testimonials"
msgstr "suositukset"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "text link"
msgstr "tekstilinkki"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_ui_view__inherit_id__theme_ir_ui_view
msgid "theme.ir.ui.view"
msgstr "theme.ir.ui.view"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
#, python-format
msgid "this page"
msgstr "tätä sivua"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "to exit full screen"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "true"
msgstr "tosi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "valuation, rank"
msgstr "arvostus, sijoitus"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_id
#, python-format
msgid "website"
msgstr "verkkosivusto"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "with the main objective to"
msgstr "päätavoitteena on"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "yes"
msgstr "kyllä"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.res_config_settings.xml:0
#, python-format
msgid "you do not need to ask for the consent"
msgstr "sinun ei tarvitse kysyä suostumusta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "⌙ Active"
msgstr "⌙ Aktiivinen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Background"
msgstr "⌙ Tausta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "⌙ Blur"
msgstr "⌙ Sumeus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Buttons"
msgstr "⌙ Painikkeet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "⌙ Color"
msgstr "⌙ Väri"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Colors"
msgstr "⌙ Värit"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Country"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "⌙ Delay"
msgstr "⌙ Viive"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
msgid "⌙ Desktop"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "⌙ Display"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Headings"
msgstr "⌙ Otsikot"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "⌙ Height"
msgstr "⌙ Korkeus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Hue"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "⌙ Inactive"
msgstr "⌙ Inaktiivinen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "⌙ Intensity"
msgstr "⌙ Intensiteetti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Label"
msgstr "⌙ Nimiö"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Languages"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Large"
msgstr "⌙ Suuri"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
msgid "⌙ Mobile"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Off-Canvas Logo"
msgstr "⌙ Kanvaasin ulkopuolinen logo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "⌙ Offset (X, Y)"
msgstr "⌙ Kompensointi (X, Y)"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "⌙ Page Anchor"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "⌙ Parallax"
msgstr "⌙ Parallaksi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "⌙ Position"
msgstr "⌙ Paikka"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Saturation"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog_options
msgid "⌙ Separator"
msgstr "⌙ Erotin"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Small"
msgstr "⌙ Pieni"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Spacing"
msgstr "⌙ Välit"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "⌙ Spread"
msgstr "⌙ Levitys"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "⌙ Style"
msgstr "⌙ Tyyli"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ UTM Campaign"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ UTM Medium"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ UTM Source"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Users"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "⌙ Video"
msgstr "⌙ Video"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Width"
msgstr "⌙ Leveys"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "└ Height"
msgstr ""
