<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="stock_account_report_invoice_document" inherit_id="account.report_invoice_document">
        <xpath expr="//div[@id='total']" position="after">
          <t groups="stock_account.group_lot_on_invoice">
            <t t-set="lot_values" t-value="o._get_invoiced_lot_values()"/>
            <t t-if="lot_values">
                <br/>
                <table class="table table-sm" style="width: 50%;" name="invoice_snln_table">
                    <thead>
                        <tr>
                            <th><span>Product</span></th>
                            <th class="text-right"><span>Quantity</span></th>
                            <th class="text-right"><span>SN/LN</span></th>
                        </tr>
                    </thead>
                    <tbody>
                        <t t-foreach="lot_values" t-as="snln_line">
                            <tr>
                                <td><t t-esc="snln_line['product_name']"/></td>
                                <td class="text-right">
                                    <t t-esc="snln_line['quantity']"/>
                                    <t t-esc="snln_line['uom_name']" groups="uom.group_uom"/>
                                </td>
                                <td class="text-right"><t t-esc="snln_line['lot_name']"/></td>
                            </tr>
                        </t>
                    </tbody>
                </table>
            </t>
          </t>
        </xpath>
    </template>
</odoo>
