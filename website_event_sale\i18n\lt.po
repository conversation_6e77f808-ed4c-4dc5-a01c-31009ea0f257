# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_sale
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: website_event_sale
#: code:addons/website_event_sale/models/product_pricelist.py:0
#, python-format
msgid ""
"A pricelist item with a positive min. quantity cannot be applied to this "
"event tickets product."
msgstr ""
"Šiam renginio bilietų produktui negalima taikyti kainyno elemento su "
"teigiamu minimaliu kiekiu."

#. module: website_event_sale
#: code:addons/website_event_sale/models/product_pricelist.py:0
#, python-format
msgid ""
"A pricelist item with a positive min. quantity will not be applied to the "
"event tickets products."
msgstr ""
"Šiam renginio bilietų produktui negalima taikyti kainyno elemento su "
"teigiamu minimaliu kiekiu."

#. module: website_event_sale
#: model:ir.model.fields,field_description:website_event_sale.field_product_product__event_ticket_ids
msgid "Event Tickets"
msgstr "Renginio bilietai"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.registration_template
msgid "Free"
msgstr "Laisvas"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.registration_template
msgid "From"
msgstr "Nuo"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_product_pricelist_item
msgid "Pricelist Rule"
msgstr "Kainoraščio taisyklė"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_product_product
msgid "Product"
msgstr "Produktas"

#. module: website_event_sale
#: code:addons/website_event_sale/controllers/main.py:0
#, python-format
msgid "Registration"
msgstr "Registracija"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_sale_order
msgid "Sales Order"
msgstr "Pardavimo užsakymas"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "Pardavimo užsakymo eilutė"

#. module: website_event_sale
#: code:addons/website_event_sale/models/sale_order.py:0
#, python-format
msgid "Sorry, The %(ticket)s tickets for the %(event)s event are sold out."
msgstr "Atsiprašome, %(ticket)s bilietai į %(event)s renginį jau išparduoti."

#. module: website_event_sale
#: code:addons/website_event_sale/models/sale_order.py:0
#, python-format
msgid ""
"Sorry, only %(remaining_seats)d seats are still available for the %(ticket)s"
" ticket for the %(event)s event."
msgstr ""
"Atsiprašome, tik %(remaining_seats)d vietos yra likusios %(ticket)s "
"bilietams į %(event)s renginį."

#. module: website_event_sale
#: code:addons/website_event_sale/models/sale_order.py:0
#, python-format
msgid "The ticket doesn't match with this product."
msgstr "Bilietas nesutampa su produktu."

#. module: website_event_sale
#: code:addons/website_event_sale/models/product_pricelist.py:0
#, python-format
msgid "Warning"
msgstr "Įspėjimas"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_website
msgid "Website"
msgstr "Svetainė"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.registration_template
msgid "to"
msgstr "iki"
