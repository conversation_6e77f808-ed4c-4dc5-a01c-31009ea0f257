<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="account_group_1" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Activos</field>
            <field name="code_prefix_start">1</field>
        </record>
        <record id="account_group_11" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Activos Corrientes</field>
            <field name="code_prefix_start">11</field>
        </record>
        <record id="account_group_1101" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Efectivo y Equivalentes de Efectivo</field>
            <field name="code_prefix_start">1101</field>
        </record>
        <record id="account_group_110101" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Caja</field>
            <field name="code_prefix_start">110101</field>
        </record>
        <record id="account_group_110102" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Bancos</field>
            <field name="code_prefix_start">110102</field>
        </record>
        <record id="account_group_110103" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Inversiones Temporales Plazo Menor 90 días</field>
            <field name="code_prefix_start">110103</field>
        </record>
        <record id="account_group_1102" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Inversiones en Valores a Corto Plazo</field>
            <field name="code_prefix_start">1102</field>
        </record>
        <record id="account_group_1103" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Cuentas y Documentos por Cobrar</field>
            <field name="code_prefix_start">1103</field>
        </record>
        <record id="account_group_110301" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Documentos por Cobrar</field>
            <field name="code_prefix_start">110301</field>
        </record>
        <record id="account_group_110302" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Cuentas por Cobrar</field>
            <field name="code_prefix_start">110302</field>
        </record>
        <record id="account_group_1104" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Provisión para Cuentas Incobrables</field>
            <field name="code_prefix_start">1104</field>
        </record>
        <record id="account_group_1105" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Inventarios</field>
            <field name="code_prefix_start">1105</field>
        </record>
        <record id="account_group_1106" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Deterioro Acumulado de Valor de Inventarios</field>
            <field name="code_prefix_start">1106</field>
        </record>
        <record id="account_group_1107" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Estimación por Obsolencia de Inventario</field>
            <field name="code_prefix_start">1107</field>
        </record>
        <record id="account_group_1108" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Impuestos Adelantados</field>
            <field name="code_prefix_start">1108</field>
        </record>
        <record id="account_group_110801" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">ITBIS Adelantado en Compras</field>
            <field name="code_prefix_start">110801</field>
        </record>
        <record id="account_group_110803" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Otros Impuestos y Saldos</field>
            <field name="code_prefix_start">110803</field>
        </record>
        <record id="account_group_1109" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Inversiones Temporales</field>
            <field name="code_prefix_start">1109</field>
        </record>
        <record id="account_group_1110" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Pagos Anticipados</field>
            <field name="code_prefix_start">1110</field>
        </record>
        <record id="account_group_12" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Activo Fijos</field>
            <field name="code_prefix_start">12</field>
        </record>
        <record id="account_group_1201" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Propiedades, Planta y Equipo</field>
            <field name="code_prefix_start">1201</field>
        </record>
        <record id="account_group_120101" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Bienes Inmuebles (CAT 1)</field>
            <field name="code_prefix_start">120101</field>
        </record>
        <record id="account_group_120102" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Bienes Muebles (CAT 2)</field>
            <field name="code_prefix_start">120102</field>
        </record>
        <record id="account_group_120103" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Bienes Muebles (CAT 3)</field>
            <field name="code_prefix_start">120103</field>
        </record>
        <record id="account_group_1202" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Depreciación Acumulada de Propiedades, Planta y Equipo</field>
            <field name="code_prefix_start">1202</field>
        </record>
        <record id="account_group_1203" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Deterioro de Valor Acumulado de Propiedades, Planta y Equipo</field>
            <field name="code_prefix_start">1203</field>
        </record>
        <record id="account_group_1204" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Activos Intangibles</field>
            <field name="code_prefix_start">1204</field>
        </record>
        <record id="account_group_1205" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Bienes en Arrendamiento</field>
            <field name="code_prefix_start">1205</field>
        </record>
        <record id="account_group_1206" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Depreciación Acumulada de Bienes en Arrendamiento</field>
            <field name="code_prefix_start">1206</field>
        </record>
        <record id="account_group_1207" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Deterioro de Valor Acumulado de Bienes en Arrendamiento</field>
            <field name="code_prefix_start">1207</field>
        </record>
        <record id="account_group_1208" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Inversiones Permanentes</field>
            <field name="code_prefix_start">1208</field>
        </record>
        <record id="account_group_1209" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Activos Diferidos</field>
            <field name="code_prefix_start">1209</field>
        </record>
        <record id="account_group_13" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Costos de Construcción</field>
            <field name="code_prefix_start">13</field>
        </record>
        <record id="account_group_1301" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Construcción en Proceso</field>
            <field name="code_prefix_start">1301</field>
        </record>
        <record id="account_group_130101" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Costos de Costrucción en Proceso</field>
            <field name="code_prefix_start">130101</field>
        </record>
        <record id="account_group_2" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Pasivos</field>
            <field name="code_prefix_start">2</field>
        </record>
        <record id="account_group_21" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Pasivo Corriente</field>
            <field name="code_prefix_start">21</field>
        </record>
        <record id="account_group_2101" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Cuentas y Documentos por Pagar a Corto Plazo</field>
            <field name="code_prefix_start">2101</field>
        </record>
        <record id="account_group_2102" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Beneficios por Pagar a Corto Plazo</field>
            <field name="code_prefix_start">2102</field>
        </record>
        <record id="account_group_2103" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Impuestos y Retenciones</field>
            <field name="code_prefix_start">2103</field>
        </record>
        <record id="account_group_210301" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">ITBIS por Pagar</field>
            <field name="code_prefix_start">210301</field>
        </record>
        <record id="account_group_210302" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">ITBIS Retenido</field>
            <field name="code_prefix_start">210302</field>
        </record>
        <record id="account_group_210303" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">ISR Retenido</field>
            <field name="code_prefix_start">210303</field>
        </record>
        <record id="account_group_210304" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Retenciones en Nómina</field>
            <field name="code_prefix_start">210304</field>
        </record>
        <record id="account_group_210305" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Otros Impuestos o Retenciones</field>
            <field name="code_prefix_start">210305</field>
        </record>
        <record id="account_group_2104" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Provisiones a Corto Plazo</field>
            <field name="code_prefix_start">2104</field>
        </record>
        <record id="account_group_2105" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Tarjetas de Crédito</field>
            <field name="code_prefix_start">2105</field>
        </record>
        <record id="account_group_22" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Pasivo No Corriente</field>
            <field name="code_prefix_start">22</field>
        </record>
        <record id="account_group_2201" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Cuentas y Documentos por Pagar a Largo Plazo</field>
            <field name="code_prefix_start">2201</field>
        </record>
        <record id="account_group_2202" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Provisión para Obligaciones Laborales</field>
            <field name="code_prefix_start">2202</field>
        </record>
        <record id="account_group_2203" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Anticipos de Clientes y Pagos no Identificados</field>
            <field name="code_prefix_start">2203</field>
        </record>
        <record id="account_group_2204" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Provisiones a Largo Plazo</field>
            <field name="code_prefix_start">2204</field>
        </record>
        <record id="account_group_3" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Capital</field>
            <field name="code_prefix_start">3</field>
        </record>
        <record id="account_group_31" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Capital Contable</field>
            <field name="code_prefix_start">31</field>
        </record>
        <record id="account_group_3101" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Capital Social</field>
            <field name="code_prefix_start">3101</field>
        </record>
        <record id="account_group_3102" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Superávit por Revaluación de Activos</field>
            <field name="code_prefix_start">3102</field>
        </record>
        <record id="account_group_32" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Utilidades Restringidas</field>
            <field name="code_prefix_start">32</field>
        </record>
        <record id="account_group_3201" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Reserva Legal</field>
            <field name="code_prefix_start">3201</field>
        </record>
        <record id="account_group_33" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Resultados</field>
            <field name="code_prefix_start">33</field>
        </record>
        <record id="account_group_3301" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Resultados Acumulados</field>
            <field name="code_prefix_start">3301</field>
        </record>
        <record id="account_group_3302" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Resultados del Ejercicio</field>
            <field name="code_prefix_start">3302</field>
        </record>
        <record id="account_group_3304" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Otras Reservas de Patrimonio</field>
            <field name="code_prefix_start">3304</field>
        </record>
        <record id="account_group_4" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Ingresos y Ganancias</field>
            <field name="code_prefix_start">4</field>
        </record>
        <record id="account_group_41" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Ingresos por Operaciones</field>
            <field name="code_prefix_start">41</field>
        </record>
        <record id="account_group_4101" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Ventas de Bienes</field>
            <field name="code_prefix_start">4101</field>
        </record>
        <record id="account_group_4102" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Ventas de Servicios</field>
            <field name="code_prefix_start">4102</field>
        </record>
        <record id="account_group_4103" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Devoluciones</field>
            <field name="code_prefix_start">4103</field>
        </record>
        <record id="account_group_4104" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Descuentos</field>
            <field name="code_prefix_start">4104</field>
        </record>
        <record id="account_group_42" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Ingresos No Operacionales</field>
            <field name="code_prefix_start">42</field>
        </record>
        <record id="account_group_4201" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Intereses Ganados</field>
            <field name="code_prefix_start">4201</field>
        </record>
        <record id="account_group_4202" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Ventas de Activos</field>
            <field name="code_prefix_start">4202</field>
        </record>
        <record id="account_group_4203" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Dividendos Ganados</field>
            <field name="code_prefix_start">4203</field>
        </record>
        <record id="account_group_4204" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Ingresos Extraordinarios</field>
            <field name="code_prefix_start">4204</field>
        </record>
        <record id="account_group_5" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Costos, Gastos y Pérdidas</field>
            <field name="code_prefix_start">5</field>
        </record>
        <record id="account_group_51" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Costos de Operación</field>
            <field name="code_prefix_start">51</field>
        </record>
        <record id="account_group_5101" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Costos de Ventas</field>
            <field name="code_prefix_start">5101</field>
        </record>
        <record id="account_group_5102" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Costos de Producción</field>
            <field name="code_prefix_start">5102</field>
        </record>
        <record id="account_group_6" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Gastos y Pérdidas</field>
            <field name="code_prefix_start">6</field>
        </record>
        <record id="account_group_61" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Gastos de Operación</field>
            <field name="code_prefix_start">61</field>
        </record>
        <record id="account_group_6101" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Gastos de Personal</field>
            <field name="code_prefix_start">6101</field>
        </record>
        <record id="account_group_610101" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Aportes a la Seguridad Social</field>
            <field name="code_prefix_start">610101</field>
        </record>
        <record id="account_group_610102" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Otras Cargas Patronales</field>
            <field name="code_prefix_start">610102</field>
        </record>
        <record id="account_group_6102" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Gastos de Administración</field>
            <field name="code_prefix_start">6102</field>
        </record>
        <record id="account_group_6103" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Gastos por Trabajo, Suministros y Servicios</field>
            <field name="code_prefix_start">6103</field>
        </record>
        <record id="account_group_610301" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Gastos Honorarios por Servicios Profesionales (P. Física)</field>
            <field name="code_prefix_start">610301</field>
        </record>
        <record id="account_group_610302" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Gastos Honorarios por Servicios Profesionales (P. Jurídica)</field>
            <field name="code_prefix_start">610302</field>
        </record>
        <record id="account_group_610303" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Gastos Honorarios por Servicios Profesionales (P. Jurídica)</field>
            <field name="code_prefix_start">610303</field>
        </record>
        <record id="account_group_6104" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Gastos por Depreciación</field>
            <field name="code_prefix_start">6104</field>
        </record>
        <record id="account_group_6105" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Gastos por Reparaciones</field>
            <field name="code_prefix_start">6105</field>
        </record>
        <record id="account_group_6106" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Gastos de Representación</field>
            <field name="code_prefix_start">6106</field>
        </record>
        <record id="account_group_6107" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Gastos Financieros</field>
            <field name="code_prefix_start">6107</field>
        </record>
        <record id="account_group_6108" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Gastos Extraordinarios</field>
            <field name="code_prefix_start">6108</field>
        </record>
        <record id="account_group_7" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Cuentas Liquidadoras de Resultados</field>
            <field name="code_prefix_start">7</field>
        </record>
        <record id="account_group_71" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Cuenta Liquidadora</field>
            <field name="code_prefix_start">71</field>
        </record>
        <record id="account_group_7101" model="account.group.template">
            <field name="chart_template_id" ref="do_chart_template"/>
            <field name="name">Pérdidas y Ganancias</field>
            <field name="code_prefix_start">7101</field>
        </record>
    </data>
</odoo>
