<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <template id="certification_report_view_modern">
            <!-- Style classes to be applyed to '#o_survey_certification': [no class](purple), gold, blue  -->
            <div id="o_survey_certification" t-att-data-oe-model="user_input._name" t-att-data-oe-id="user_input.id" t-att-class="'article certification-wrapper modern %s' % layout_color">
                <div class="certification">
                    <div class="certification-seal" t-if="user_input.scoring_success"/>
                    <div class="certification-top">
                        <h1><b>Certificate</b>
                            <br/><span t-if="user_input.scoring_success">of achievement</span>
                        </h1>
                    </div>

                    <div class="certification-content">
                        <div t-if="user_input.scoring_success">
                            <p>This certificate is presented to
                                <br/>
                                <t t-set="certif_style" t-value="''"/>
                                <t t-set="certified_name" t-value="user_input.partner_id.name or user_input.email or ''"/>
                                <t t-if="certified_name.isupper()">
                                    <t t-set="certif_style" t-value="certif_style + 'font-family: certification-serif;'"/>
                                </t>
                                <t t-if="len(certified_name) > 20">
                                    <t t-set="certif_style" t-value="certif_style + 'font-size: 40px; line-height: 4;'"/>
                                </t>
                                <span t-att-style="certif_style" class="user-name" t-esc="certified_name"/>

                                <br/>by <span class="certification-company" t-field="user_input.env.company.display_name"/> for successfully completing
                                <br/><b><span class="certification-name" t-field="user_input.survey_id.display_name"/></b>
                             </p>
                        </div>
                        <div t-else="" class="certification-failed">
                            <p>Certification Failed</p>
                        </div>
                    </div>

                    <div class="certification-bottom">
                        <div class="certification-date-wrapper">
                            <div class="certification-date" t-field="user_input.create_date" t-options='{"widget": "date"}'/>
                            <span>Date</span>
                        </div>
                        <div class="certification-company">
                            <span class="certification-company-logo" t-field="user_input.env.company.logo" t-options="{'widget': 'image'}" role="img"/>
                        </div>

                    </div>
                    <div class="certification-number" t-if="user_input.scoring_success">
                        Certification n°<t t-esc="str(user_input.id).rjust(10, '0')"/>
                    </div>
                </div>
                <div t-if="user_input.test_entry" class="test-entry"/>
            </div>
        </template>

        <template id="certification_report_view_classic">
            <!-- Style classes to be applyed to '#o_survey_certification': [no class](purple), gold, blue  -->
            <div id="o_survey_certification" t-att-data-oe-model="user_input._name" t-att-data-oe-id="user_input.id" t-att-class="'article certification-wrapper classic %s' % layout_color">
                <div t-if="user_input.test_entry" class="test-entry"/>
                <div class="certification">
                    <div class="certification-top">
                        <h1><b>Certificate</b>
                            <br/><span t-if="user_input.scoring_success">of achievement</span>
                        </h1>
                    </div>

                    <div class="certification-content">
                        <div t-if="user_input.scoring_success">
                            <p>This certificate is presented to
                                 <t t-set="certif_style" t-value="''"/>
                                <t t-set="certified_name" t-value="user_input.partner_id.name or user_input.email or ''"/>
                                <t t-if="certified_name.isupper()">
                                    <t t-set="certif_style" t-value="certif_style + 'font-family: certification-serif;'"/>
                                </t>
                                <t t-if="len(certified_name) > 35">
                                    <t t-set="certif_style" t-value="certif_style + 'font-size: 20px; line-height: 4; font-family: certification-serif; '"/>
                                </t>
                                <t t-elif="len(certified_name) > 20">
                                    <t t-set="certif_style" t-value="certif_style + 'font-size: 30px; line-height: 4;'"/>
                                </t>
                                <br/>
                                <span t-att-style="certif_style" class="user-name" t-esc="certified_name"/>

                                <br/>by <span class="certification-company" t-field="user_input.env.company.display_name"/>
                                for successfully completing
                                <br/><b><span class="certification-name" t-field="user_input.survey_id.display_name"/></b>
                             </p>
                        </div>
                        <div t-else="" class="certification-failed">
                            <p>Certification Failed</p>
                        </div>
                    </div>

                    <div class="certification-bottom">
                            <div class="certification-date-wrapper">
                                <b><div class="certification-date" t-field="user_input.create_date" t-options='{"widget": "date"}'/></b>
                                <span>Date</span>
                            </div>
                            <div class="certification-seal"/>
                            <div class="certification-company">
                                <span class="certification-company-logo" t-field="user_input.env.company.logo" t-options="{'widget': 'image'}" role="img"/>
                            </div>
                        <div class="certification-number" t-if="user_input.scoring_success">
                            Certification n°<t t-esc="str(user_input.id).rjust(10, '0')"/>
                        </div>
                    </div>
                </div>
            </div>
        </template>

        <template id="certification_report_view">
            <t t-call="web.html_container">
                <t t-foreach="docs" t-as="user_input">
                    <t t-set="layout_values" t-value="user_input.survey_id.certification_report_layout.split('_') if user_input.survey_id.certification_report_layout else ['modern', 'purple']"/>
                    <t t-set="layout_template" t-value="'survey.certification_report_view_%s' % (layout_values[0])"/>
                    <t t-set="layout_color" t-value="layout_values[1]"/>
                    <t t-call="{{layout_template}}"/>
                </t>
            </t>
        </template>
    </data>
</odoo>
