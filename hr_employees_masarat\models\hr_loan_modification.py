# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from datetime import datetime
from dateutil.relativedelta import relativedelta
from odoo.exceptions import ValidationError, UserError


class HrLoanInherited(models.Model):
    _inherit = 'hr.loan'

    garnted_person = fields.Char(string='أسم الضامن')
    garnted_person_phone = fields.Char(string='رقم هاتف الضامن')

    account_debit = fields.Many2one('account.account',string='Debit account')
    account_credit = fields.Many2one('account.account',string='Credit account')
    journal_id = fields.Many2one('account.journal',string='يومية السلف')