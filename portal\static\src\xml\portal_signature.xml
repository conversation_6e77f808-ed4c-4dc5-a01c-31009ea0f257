<templates id="template" xml:space="preserve">

    <!-- Template for the widget SignatureForm. -->
    <t t-name="portal.portal_signature">
        <form method="POST">
            <input type="hidden" name="csrf_token" t-att-value="widget.csrf_token"/>
            <div class="o_web_sign_name_and_signature"/>
            <div class="o_portal_sign_controls my-3">
                <div class="text-right my-3">
                    <button type="submit" class="o_portal_sign_submit btn btn-primary" disabled="disabled">
                        <i class="fa fa-check"/>
                        <t t-esc="widget.sendLabel"/>
                    </button>
                </div>
            </div>
        </form>
    </t>
    <!-- Template when the sign rpc is successful. -->
    <t t-name="portal.portal_signature_success">
        <div class="alert alert-success" role="status">
            <span t-if="widget.message" t-esc="widget.message"/>
            <span t-else="">Thank You!</span>
            <a t-if="widget.redirect_url" t-att-href="widget.redirect_url">
                <t t-if="widget.redirect_message" t-esc="widget.redirect_message"/>
                <t t-else="">Click here to see your document.</t>
            </a>
        </div>
    </t>
    <!-- Template when the sign rpc returns an error. -->
    <t t-name="portal.portal_signature_error">
        <div class="o_portal_sign_error_msg alert alert-danger" role="status">
            <t t-esc="widget.error"/>
        </div>
    </t>
</templates>
