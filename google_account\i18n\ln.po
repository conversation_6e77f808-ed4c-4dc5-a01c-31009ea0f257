# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * google_account
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2015-09-07 18:42+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: <PERSON><PERSON> (http://www.transifex.com/odoo/odoo-9/language/ln/)\n"
"Language: ln\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service_create_uid
msgid "Created by"
msgstr ""

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service_create_date
msgid "Created on"
msgstr ""

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service_display_name
msgid "Display Name"
msgstr ""

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service_id
msgid "ID"
msgstr "ID"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service___last_update
msgid "Last Modified on"
msgstr ""

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service_write_uid
msgid "Last Updated by"
msgstr ""

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service_write_date
msgid "Last Updated on"
msgstr ""

#. module: google_account
#: code:addons/google_account/google_account.py:98
#, python-format
msgid ""
"Something went wrong during your token generation. Maybe your Authorization "
"Code is invalid"
msgstr ""

#. module: google_account
#: code:addons/google_account/google_account.py:37
#, python-format
msgid ""
"Something went wrong during your token generation. Maybe your Authorization "
"Code is invalid or already expired"
msgstr ""

#. module: google_account
#: code:addons/google_account/google_account.py:128
#, python-format
msgid ""
"Something went wrong during your token generation. Maybe your Authorization "
"Code is invalid or already expired [%s]"
msgstr ""

#. module: google_account
#: code:addons/google_account/google_account.py:173
#, python-format
msgid "Something went wrong with your request to google"
msgstr ""

#. module: google_account
#: model:ir.model,name:google_account.model_google_service
msgid "google.service"
msgstr ""
