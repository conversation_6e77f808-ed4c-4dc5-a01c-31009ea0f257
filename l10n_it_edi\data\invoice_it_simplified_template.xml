<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <template id="account_invoice_line_it_simplified_FatturaPA">
            <DatiBeniServizi>
                <Descrizione>
                    <t t-esc="format_alphanumeric(line.name[:1000])"/>
                    <t t-if="not line.name" t-esc="'NO NAME'"/>
                </Descrizione>
                <Importo t-esc="format_monetary(line.price_total, currency)"/>
                <DatiIVA>
                    <Imposta t-esc="format_monetary(line.price_total - line.price_subtotal, currency)"/>
                </DatiIVA>
                <Natura t-if="line.tax_ids.l10n_it_has_exoneration" t-esc="line.tax_ids.l10n_it_kind_exoneration"/>
            </DatiBeniServizi>
        </template>

        <template id="account_invoice_it_simplified_FatturaPA_export">
            <t t-set="currency" t-value="record.currency_id or record.company_currency_id"/>
            <t t-set="bank" t-value="record.partner_bank_id"/>
            <p:FatturaElettronicaSemplificata  t-att-versione="formato_trasmissione" xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:p="http://ivaservizi.agenziaentrate.gov.it/docs/xsd/fatture/v1.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://ivaservizi.agenziaentrate.gov.it/docs/xsd/fatture/v1.0 http://www.fatturapa.gov.it/export/fatturazione/sdi/fatturapa/v1.0/Schema_del_file_xml_FatturaPA_versione_1.0.xsd">
            <FatturaElettronicaHeader>
                    <DatiTrasmissione>
                        <IdTrasmittente>
                            <IdPaese t-esc="get_vat_country(record.company_id.vat)"/>
                            <IdCodice t-esc="normalize_codice_fiscale(record.company_id.l10n_it_codice_fiscale) or get_vat_number(record.company_id.vat)"/>
                        </IdTrasmittente>
                        <ProgressivoInvio t-esc="format_alphanumeric(record.name.replace('/','')[-10:])"/>
                        <FormatoTrasmissione t-esc="formato_trasmissione"/>
                        <CodiceDestinatario t-if="record.commercial_partner_id.l10n_it_pa_index" t-esc="record.commercial_partner_id.l10n_it_pa_index.upper()"/>
                        <CodiceDestinatario t-if="not record.commercial_partner_id.l10n_it_pa_index" t-esc="'0000000'"/>
                        <PECDestinatario t-if="record.commercial_partner_id.l10n_it_pec_email" t-esc="record.commercial_partner_id.l10n_it_pec_email"/>
                    </DatiTrasmissione>
                    <CedentePrestatore>
                        <IdFiscaleIVA>
                            <IdPaese t-esc="get_vat_country(record.company_id.vat)"/>
                            <IdCodice t-esc="get_vat_number(record.company_id.vat)"/>
                        </IdFiscaleIVA>
                        <CodiceFiscale t-if="record.company_id.l10n_it_codice_fiscale" t-esc="normalize_codice_fiscale(record.company_id.l10n_it_codice_fiscale)"/>
                        <Denominazione t-esc="format_alphanumeric(record.company_id.partner_id.display_name[:80])"/>
                        <t t-call="l10n_it_edi.account_invoice_it_FatturaPA_sede">
                            <t t-set="partner" t-value="record.company_id.partner_id"/>
                        </t>
                        <RappresentanteFiscale t-if="record.company_id.l10n_it_has_tax_representative">
                            <IdFiscaleIVA>
                                <IdPaese t-esc="get_vat_country(record.company_id.l10n_it_tax_representative_partner_id.vat)"/>
                                <IdCodice t-esc="get_vat_number(record.company_id.l10n_it_tax_representative_partner_id.vat)"/>
                            </IdFiscaleIVA>
                            <Anagrafica>
                                <Denominazione t-if="record.commercial_partner_id.is_company" t-esc="format_alphanumeric(record.commercial_partner_id.display_name[:80])"/>
                                <Nome t-if="not record.commercial_partner_id.is_company" t-esc="format_alphanumeric(' '.join(record.commercial_partner_id.name.split()[:1])[:60])"/>
                                <Cognome t-if="not record.commercial_partner_id.is_company" t-esc="format_alphanumeric(' '.join(record.commercial_partner_id.name.split()[1:])[:60])"/>
                            </Anagrafica>
                        </RappresentanteFiscale>
                        <IscrizioneREA t-if="record.company_id.l10n_it_has_eco_index">
                            <Ufficio t-esc="record.company_id.l10n_it_eco_index_office.code"/>
                            <NumeroREA t-esc="format_alphanumeric(record.company_id.l10n_it_eco_index_number)"/>
                            <CapitaleSociale t-if="record.company_id.l10n_it_eco_index_share_capital != 0" t-esc="format_numbers_two(record.company_id.l10n_it_eco_index_share_capital)"/>
                            <SocioUnico t-if="record.company_id.l10n_it_eco_index_sole_shareholder != 'NO'" t-esc="record.company_id.l10n_it_eco_index_sole_shareholder"/>
                            <StatoLiquidazione t-esc="record.company_id.l10n_it_eco_index_liquidation_state"/>
                        </IscrizioneREA>
                        <RegimeFiscale t-esc="record.company_id.l10n_it_tax_system"/>
                    </CedentePrestatore>
                    <CessionarioCommittente>
                        <IdentificativiFiscali>
                            <IdFiscaleIVA t-if="record.commercial_partner_id.vat and in_eu(record.commercial_partner_id)">
                                <IdPaese t-esc="get_vat_country(record.commercial_partner_id.vat)"/>
                                <IdCodice t-esc="get_vat_number(record.commercial_partner_id.vat)"/>
                            </IdFiscaleIVA>
                            <CodiceFiscale t-if="record.commercial_partner_id.l10n_it_codice_fiscale" t-esc="normalize_codice_fiscale(record.commercial_partner_id.l10n_it_codice_fiscale)"/>
                        </IdentificativiFiscali>
                    </CessionarioCommittente>
                </FatturaElettronicaHeader>
                <FatturaElettronicaBody>
                    <DatiGenerali>
                        <DatiGeneraliDocumento>
                            <!--2.1.1-->
                            <TipoDocumento t-esc="document_type"/>
                            <Divisa t-esc="currency.name"/>
                            <Data t-esc="format_date(record.invoice_date)"/>
                            <Numero t-esc="format_alphanumeric(record.name[-20:])"/>
                        </DatiGeneraliDocumento>
                        <DatiFatturaRettificata t-if="record.move_type == 'out_refund' and record.reversed_entry_id">
                            <NumeroFR t-esc="format_alphanumeric(record.reversed_entry_id.name[-20:])"/>
                            <DataFR t-esc="format_date(record.reversed_entry_id.invoice_date)"/>
                            <ElementiRettificati t-esc="format_alphanumeric(record.ref[:1000])"/>
                        </DatiFatturaRettificata>
                    </DatiGenerali>
                    <!-- Invoice lines. -->
                    <t t-foreach="record.invoice_line_ids.filtered(lambda l: not l.display_type)" t-as="line">
                        <t t-call="l10n_it_edi.account_invoice_line_it_simplified_FatturaPA"/>
                    </t>
                    <Allegati t-if="pdf">
                        <NomeAttachment t-esc="format_alphanumeric(pdf_name[:60])"/>
                        <FormatoAttachment>PDF</FormatoAttachment>
                        <Attachment t-esc="pdf"/>
                    </Allegati>
                </FatturaElettronicaBody>
            </p:FatturaElettronicaSemplificata>
        </template>
    </data>
</odoo>
