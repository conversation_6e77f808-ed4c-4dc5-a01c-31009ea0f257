# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_epson_printer
# 
# Translators:
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:49+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: erpgo translator <<EMAIL>>, 2023\n"
"Language-Team: Azerbaijani (https://app.transifex.com/odoo/teams/41243/az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_epson_printer
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.pos_iot_config_view_form
msgid "Cashdrawer"
msgstr "Kassa qutusu"

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid ""
"Check on the printer configuration for the 'Device ID' setting. It should be"
" set to: "
msgstr ""

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "Connection to the printer failed"
msgstr "Printerə qoşulma uğursuz oldu"

#. module: pos_epson_printer
#: model:ir.model.fields,field_description:pos_epson_printer.field_pos_config__epson_printer_ip
msgid "Epson Printer IP"
msgstr ""

#. module: pos_epson_printer
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.pos_iot_config_view_form
msgid "Epson Receipt Printer IP Address"
msgstr ""

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid ""
"If you are on a secure server (HTTPS) please make sure you manually accepted"
" the certificate by accessing %s"
msgstr ""

#. module: pos_epson_printer
#: model:ir.model.fields,help:pos_epson_printer.field_pos_config__epson_printer_ip
msgid "Local IP address of an Epson receipt printer."
msgstr ""

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "No paper was detected by the printer"
msgstr ""

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "Please check if the printer has enough paper and is ready to print."
msgstr ""

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "Please check if the printer is still connected."
msgstr "Zəhmət olmasa printerin qoşulu olub-olmadığını yoxlayın."

#. module: pos_epson_printer
#: model:ir.model,name:pos_epson_printer.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Satış Nöqtəsi Konfiqurasiyası"

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "Printing failed"
msgstr ""

#. module: pos_epson_printer
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.pos_iot_config_view_form
msgid ""
"The Epson receipt printer will be used instead of the receipt printer "
"connected to the IoT Box."
msgstr ""

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "The following error code was given by the printer:"
msgstr ""

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "The printer was successfully reached, but it wasn't able to print."
msgstr ""

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "To find more details on the error reason, please search online for:"
msgstr ""
