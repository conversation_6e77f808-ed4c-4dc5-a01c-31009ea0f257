# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_quotation_builder
# 
# Translators:
# <PERSON>, 2021
# RHTodoo, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid ""
":\n"
"                                        this content will appear on the quotation only if this\n"
"                                        product is put on the quote."
msgstr ""
":\n"
"                                        ce produit n'apparaitra sur le devis que si cet\n"
"                                        article est présent dedans."

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid ""
":\n"
"                                        this content will appear on the quotation only if this\n"
"                                        product is used in the quote."
msgstr ""
":\n"
"                                        ce contenu n'apparaitra sur le devis que si cet\n"
"                                        article est présent dans le devis."

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.sale_order_portal_content_inherit_sale_quotation_builder
msgid ""
":\n"
"                        the content below will disappear if this\n"
"                        product is removed from the quote."
msgstr ""
":\n"
"                        Le contenu ci-dessous disparaîtra si \n"
"                        l'article est supprimé du devis."

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid ""
"<strong>Template Header:</strong> this content\n"
"                                    will appear on all quotations using this\n"
"                                    template."
msgstr ""
"<strong>En-tête du Modèle:</strong> ce contenu\n"
"                                    apparaitra sur tous les devis en utilisant ce\n"
"                                    modèle."

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid "About us"
msgstr "À propos"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.brand_promotion
msgid "An awesome"
msgstr "Un génial"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"As a leading professional services firm,\n"
"                                we know that success is all about the\n"
"                                commitment we put on strong services."
msgstr ""
"En tant que firme de services professionnels de premier plan,\n"
"                                nous savons que le succès dépend entièrement de\n"
"                                notre engagement dans des services de qualité."

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.sale_order_portal_content_inherit_sale_quotation_builder
msgid "Close"
msgstr "Fermer"

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_res_company
msgid "Companies"
msgstr "Sociétés"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.sale_order_template_view_form_inherit_sale_quotation_builder
msgid "Design Template"
msgstr "Concevez un modèle"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"Great quotation templates will significantly\n"
"                                <strong>boost your success rate</strong>. The\n"
"                                first section is usually about your company,\n"
"                                your references, your methodology or\n"
"                                guarantees, your team, SLA, terms and conditions, etc."
msgstr ""
"Des modèles de devis travaillés augmenteront de manière\n"
"                                <strong>significative votre taux de réussite</strong>. La\n"
"                                première partie concerne généralement votre entreprise,\n"
"                                vos références, votre méthodologie ou\n"
"                                vos garanties, votre équipe, vos accords de niveau de service, vos conditions générales, etc."

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"If you edit a quotation from the 'Preview' of a quotation, you will\n"
"                        update that quotation only. If you edit the quotation\n"
"                        template (from the Configuration menu), all future quotations will\n"
"                        use this modified template."
msgstr ""
"Modifier un devis depuis l'« Aperçu » de ce dernier\n"
"                        met seulement à jour le devis du client. Si vous mettez à jour le modèle de devis\n"
"                        (depuis le menu Configuration), tous les autres devis utiliseront\n"
"                        ce modèle modifié."

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.brand_promotion
msgid "Open Source CRM"
msgstr "CRM Open Source"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid "Optional Product:"
msgstr "Produit optionnel:"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid "Our Offer"
msgstr "Notre Offre"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid "Our Quality"
msgstr "Notre qualité"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid "Our Service"
msgstr "Notre Service"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid "Price"
msgstr "Prix"

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_product_template
msgid "Product Template"
msgstr "Modèle d'article"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"Product quality is the foundation we\n"
"                                stand on; we build it with a relentless\n"
"                                focus on fabric, performance and craftsmanship."
msgstr ""
"La qualité des produits est la fondation sur laquelle\n"
"                                nous nous tenons ; nous la construisons avec une concentration\n"
"                               inébranlable sur la matière, la performance et le savoir-faire."

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.sale_order_portal_content_inherit_sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid "Product:"
msgstr "Article :"

#. module: sale_quotation_builder
#: model:ir.model.fields,field_description:sale_quotation_builder.field_product_product__quotation_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_product_template__quotation_description
msgid "Quotation Description"
msgstr "Description du Devis"

#. module: sale_quotation_builder
#: model:ir.model.fields,field_description:sale_quotation_builder.field_product_product__quotation_only_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_product_template__quotation_only_description
msgid "Quotation Only Description"
msgstr "Description uniquement Devis"

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_sale_order_template
msgid "Quotation Template"
msgstr "Modèle de devis"

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_sale_order_template_line
msgid "Quotation Template Line"
msgstr "Ligne Modèle de devis"

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_sale_order_template_option
msgid "Quotation Template Option"
msgstr "Option Modèle de devis"

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_sale_order_option
msgid "Sale Options"
msgstr "Options de vente"

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_sale_order
msgid "Sales Order"
msgstr "Bon de commande"

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_sale_order_line
msgid "Sales Order Line"
msgstr "Ligne de bons de commande"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid "Terms &amp; Conditions"
msgstr "Conditions générales"

#. module: sale_quotation_builder
#: model:ir.model.fields,help:sale_quotation_builder.field_product_product__quotation_only_description
#: model:ir.model.fields,help:sale_quotation_builder.field_product_template__quotation_only_description
#: model:ir.model.fields,help:sale_quotation_builder.field_sale_order_template_line__website_description
msgid "The quotation description (not used on eCommerce)"
msgstr "La description du devis (pas utilisée sur eCommerce)"

#. module: sale_quotation_builder
#: model:ir.model.fields,help:sale_quotation_builder.field_product_product__quotation_description
#: model:ir.model.fields,help:sale_quotation_builder.field_product_template__quotation_description
msgid ""
"This field uses the Quotation Only Description if it is defined, otherwise "
"it will try to read the eCommerce Description."
msgstr ""
"Ce champs utilise la Description uniquement Devis si elle est définie, sinon"
" il tentera de lire la Description de l'eCommerce."

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"This is a <strong>sample quotation template</strong>. You should\n"
"                                customize it to fit your own needs from the <i>Sales</i>\n"
"                                application, using the menu: Configuration /\n"
"                                Quotation Templates."
msgstr ""
"Il s'agit d'un <strong>modèle de devis</strong>. Vous devez\n"
"                                l'adapter à vos besoins depuis l'application <i>Vente</i>\n"
"                                à l'aide du menu : Configuration /\n"
"                                Modèles de devis."

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid "This is a preview of the sale order template."
msgstr "Ceci est un aperçu du modèle d'ordre de vente."

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid ""
"Titles with style <i>Heading 2</i> and\n"
"                                    <i>Heading 3</i> will be used to generate the\n"
"                                    table of content automatically."
msgstr ""
"Les titres avec les styles <i>Heading 2</i> et\n"
"                                    <i>Heading 3</i> seront utilisés pour générer la\n"
"                                    table des matières automatiquement."

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"We always ensure that our products are\n"
"                                set at a fair price so that you will be\n"
"                                happy to buy them."
msgstr ""
"Nous veillons toujours à définir un\n"
"                                       juste prix pour nos produits pour\n"
"                                       que vous soyez heureux de les acheter."

#. module: sale_quotation_builder
#: model:ir.model.fields,field_description:sale_quotation_builder.field_sale_order__website_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_sale_order_line__website_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_sale_order_option__website_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_sale_order_template__website_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_sale_order_template_line__website_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_sale_order_template_option__website_description
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.sale_order_template_view_form_inherit_sale_quotation_builder
msgid "Website Description"
msgstr "Description du site Web"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"You can <strong>set a description per product</strong>. Odoo will\n"
"                        automatically create a quotation using the descriptions\n"
"                        of all products in the proposal. The table of content\n"
"                        on the left is generated automatically using the styles you\n"
"                        used in your description (heading 1, heading 2, ...)"
msgstr ""
"Vous pouvez <strong>configurer une description par produit</strong>. Odoo créera\n"
"                        automatiquement un devis à l'aide des descriptions\n"
"                        de tous les produits de la proposition. La table des matières\n"
"                        à gauche est générée automatiquement à l'aide des styles que vous\n"
"                        avez utilisés dans votre description (titre 1, titre 2, ...)"
