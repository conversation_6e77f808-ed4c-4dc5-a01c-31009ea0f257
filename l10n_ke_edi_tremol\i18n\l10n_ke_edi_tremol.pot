# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_ke_edi_tremol
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-08 23:34+0000\n"
"PO-Revision-Date: 2023-01-08 23:34+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_ke_edi_tremol
#: model_terms:ir.ui.view,arch_db:l10n_ke_edi_tremol.l10n_ke_invoice
msgid "<b>Date and Time of Signing: </b><br/>"
msgstr ""

#. module: l10n_ke_edi_tremol
#: model_terms:ir.ui.view,arch_db:l10n_ke_edi_tremol.l10n_ke_invoice
msgid "<b>Invoice Number: </b><br/>"
msgstr ""

#. module: l10n_ke_edi_tremol
#: model_terms:ir.ui.view,arch_db:l10n_ke_edi_tremol.l10n_ke_invoice
msgid "<b>Kenyan Fiscal Device Info</b>"
msgstr ""

#. module: l10n_ke_edi_tremol
#: model_terms:ir.ui.view,arch_db:l10n_ke_edi_tremol.l10n_ke_invoice
msgid "<b>Serial Number: </b><br/>"
msgstr ""

#. module: l10n_ke_edi_tremol
#: model_terms:ir.ui.view,arch_db:l10n_ke_edi_tremol.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Tremol Device Settings</span>\n"
"                                <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""

#. module: l10n_ke_edi_tremol
#: model_terms:ir.ui.view,arch_db:l10n_ke_edi_tremol.l10n_ke_invoice
msgid "<strong class=\"text-center\">TIMS URL</strong><br/><br/>"
msgstr ""

#. module: l10n_ke_edi_tremol
#: model:ir.model.fields,field_description:l10n_ke_edi_tremol.field_account_bank_statement_line__l10n_ke_cu_invoice_number
#: model:ir.model.fields,field_description:l10n_ke_edi_tremol.field_account_move__l10n_ke_cu_invoice_number
#: model:ir.model.fields,field_description:l10n_ke_edi_tremol.field_account_payment__l10n_ke_cu_invoice_number
msgid "CU Invoice Number"
msgstr ""

#. module: l10n_ke_edi_tremol
#: model:ir.model.fields,field_description:l10n_ke_edi_tremol.field_account_bank_statement_line__l10n_ke_cu_qrcode
#: model:ir.model.fields,field_description:l10n_ke_edi_tremol.field_account_move__l10n_ke_cu_qrcode
#: model:ir.model.fields,field_description:l10n_ke_edi_tremol.field_account_payment__l10n_ke_cu_qrcode
msgid "CU QR Code"
msgstr ""

#. module: l10n_ke_edi_tremol
#: model:ir.model.fields,field_description:l10n_ke_edi_tremol.field_account_bank_statement_line__l10n_ke_cu_serial_number
#: model:ir.model.fields,field_description:l10n_ke_edi_tremol.field_account_move__l10n_ke_cu_serial_number
#: model:ir.model.fields,field_description:l10n_ke_edi_tremol.field_account_payment__l10n_ke_cu_serial_number
msgid "CU Serial Number"
msgstr ""

#. module: l10n_ke_edi_tremol
#: model:ir.model.fields,field_description:l10n_ke_edi_tremol.field_account_bank_statement_line__l10n_ke_cu_datetime
#: model:ir.model.fields,field_description:l10n_ke_edi_tremol.field_account_move__l10n_ke_cu_datetime
#: model:ir.model.fields,field_description:l10n_ke_edi_tremol.field_account_payment__l10n_ke_cu_datetime
msgid "CU Signing Date and Time"
msgstr ""

#. module: l10n_ke_edi_tremol
#: model:ir.model,name:l10n_ke_edi_tremol.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_ke_edi_tremol
#: model:ir.model,name:l10n_ke_edi_tremol.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: l10n_ke_edi_tremol
#: model:ir.model,name:l10n_ke_edi_tremol.model_res_partner
msgid "Contact"
msgstr ""

#. module: l10n_ke_edi_tremol
#. openerp-web
#: code:addons/l10n_ke_edi_tremol/static/src/js/send_invoice.js:0
#, python-format
msgid "Error trying to connect to Odoo. Check your internet connection"
msgstr ""

#. module: l10n_ke_edi_tremol
#. openerp-web
#: code:addons/l10n_ke_edi_tremol/static/src/js/send_invoice.js:0
#, python-format
msgid "Error trying to connect to the middleware. Is the middleware running?"
msgstr ""

#. module: l10n_ke_edi_tremol
#: model:ir.model.fields,field_description:l10n_ke_edi_tremol.field_res_partner__l10n_ke_exemption_number
#: model:ir.model.fields,field_description:l10n_ke_edi_tremol.field_res_users__l10n_ke_exemption_number
msgid "Exemption Number"
msgstr ""

#. module: l10n_ke_edi_tremol
#: model:ir.model.fields,field_description:l10n_ke_edi_tremol.field_res_company__l10n_ke_cu_proxy_address
#: model:ir.model.fields,field_description:l10n_ke_edi_tremol.field_res_config_settings__l10n_ke_cu_proxy_address
msgid "Fiscal Device Proxy Address"
msgstr ""

#. module: l10n_ke_edi_tremol
#: model_terms:ir.ui.view,arch_db:l10n_ke_edi_tremol.l10n_ke_inherit_product_template_form_view
msgid "HS Code"
msgstr ""

#. module: l10n_ke_edi_tremol
#: code:addons/l10n_ke_edi_tremol/models/account_move.py:0
#, python-format
msgid ""
"Invalid invoice configuration on %s:\n"
"%s\n"
"\n"
msgstr ""

#. module: l10n_ke_edi_tremol
#: model:ir.model,name:l10n_ke_edi_tremol.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_ke_edi_tremol
#: model:ir.model.fields,field_description:l10n_ke_edi_tremol.field_product_product__l10n_ke_hsn_code
#: model:ir.model.fields,field_description:l10n_ke_edi_tremol.field_product_template__l10n_ke_hsn_code
#: model_terms:ir.ui.view,arch_db:l10n_ke_edi_tremol.l10n_ke_inherit_product_product_form_view
msgid "KRA Item Code"
msgstr ""

#. module: l10n_ke_edi_tremol
#: model:ir.model.fields,field_description:l10n_ke_edi_tremol.field_product_product__l10n_ke_hsn_name
#: model:ir.model.fields,field_description:l10n_ke_edi_tremol.field_product_template__l10n_ke_hsn_name
msgid "KRA Item Description"
msgstr ""

#. module: l10n_ke_edi_tremol
#: model_terms:ir.ui.view,arch_db:l10n_ke_edi_tremol.res_partner_view_form
msgid "Kenya Accounting Details"
msgstr ""

#. module: l10n_ke_edi_tremol
#: model_terms:ir.ui.view,arch_db:l10n_ke_edi_tremol.l10n_ke_inherit_account_move_search_view
msgid "Kenya CU Invoice Number"
msgstr ""

#. module: l10n_ke_edi_tremol
#: model_terms:ir.ui.view,arch_db:l10n_ke_edi_tremol.res_config_settings_view_form
msgid "Kenya TIMS Integration"
msgstr ""

#. module: l10n_ke_edi_tremol
#: code:addons/l10n_ke_edi_tremol/models/account_move.py:0
#, python-format
msgid ""
"On line %s, a product with a HS Code and HS Name must be selected, since the"
" tax is 0%% or exempt."
msgstr ""

#. module: l10n_ke_edi_tremol
#: code:addons/l10n_ke_edi_tremol/models/account_move.py:0
#, python-format
msgid "On line %s, you must select one and only one tax."
msgstr ""

#. module: l10n_ke_edi_tremol
#. openerp-web
#: code:addons/l10n_ke_edi_tremol/static/src/js/send_invoice.js:0
#, python-format
msgid "Posting an invoice has failed, with the message: \n"
msgstr ""

#. module: l10n_ke_edi_tremol
#: model:ir.model,name:l10n_ke_edi_tremol.model_product_template
msgid "Product Template"
msgstr ""

#. module: l10n_ke_edi_tremol
#: model:ir.model.fields,help:l10n_ke_edi_tremol.field_product_product__l10n_ke_hsn_name
#: model:ir.model.fields,help:l10n_ke_edi_tremol.field_product_template__l10n_ke_hsn_name
msgid "Product code description needed when not 16% VAT rated. "
msgstr ""

#. module: l10n_ke_edi_tremol
#: model:ir.model.fields,help:l10n_ke_edi_tremol.field_product_product__l10n_ke_hsn_code
#: model:ir.model.fields,help:l10n_ke_edi_tremol.field_product_template__l10n_ke_hsn_code
msgid "Product code needed when not 16% VAT rated. "
msgstr ""

#. module: l10n_ke_edi_tremol
#: model_terms:ir.ui.view,arch_db:l10n_ke_edi_tremol.l10n_ke_invoice
msgid "QR Code"
msgstr ""

#. module: l10n_ke_edi_tremol
#: model_terms:ir.ui.view,arch_db:l10n_ke_edi_tremol.l10n_ke_inherit_account_move_form
msgid "Send To Fiscal Device"
msgstr ""

#. module: l10n_ke_edi_tremol
#: model:ir.actions.server,name:l10n_ke_edi_tremol.action_send_invoices_to_device
msgid "Send to fiscal device"
msgstr ""

#. module: l10n_ke_edi_tremol
#: code:addons/l10n_ke_edi_tremol/models/account_move.py:0
#, python-format
msgid ""
"Tax '%s' is used, but only taxes of 16%%, 8%%, 0%% or Exempt can be sent. "
"Please reconfigure or change the tax."
msgstr ""

#. module: l10n_ke_edi_tremol
#: code:addons/l10n_ke_edi_tremol/models/account_move.py:0
#, python-format
msgid ""
"Tax exempt report line cannot be found, please update the l10n_ke module."
msgstr ""

#. module: l10n_ke_edi_tremol
#: model:ir.model.fields,help:l10n_ke_edi_tremol.field_res_company__l10n_ke_cu_proxy_address
#: model:ir.model.fields,help:l10n_ke_edi_tremol.field_res_config_settings__l10n_ke_cu_proxy_address
msgid "The address of the proxy server for the fiscal device."
msgstr ""

#. module: l10n_ke_edi_tremol
#: code:addons/l10n_ke_edi_tremol/models/account_move.py:0
#, python-format
msgid ""
"The document already has details related to the fiscal device. Please make "
"sure that the invoice has not already been sent."
msgstr ""

#. module: l10n_ke_edi_tremol
#: code:addons/l10n_ke_edi_tremol/models/account_move.py:0
#, python-format
msgid "The document being sent should be an invoice or credit note."
msgstr ""

#. module: l10n_ke_edi_tremol
#: model:ir.model.fields,help:l10n_ke_edi_tremol.field_res_partner__l10n_ke_exemption_number
#: model:ir.model.fields,help:l10n_ke_edi_tremol.field_res_users__l10n_ke_exemption_number
msgid ""
"The exemption number of the partner. Provided by the Kenyan government."
msgstr ""

#. module: l10n_ke_edi_tremol
#: model_terms:ir.ui.view,arch_db:l10n_ke_edi_tremol.res_config_settings_view_form
msgid ""
"The tremol device makes use of a proxy server, which can be running locally on your computer or on an IoT Box.\n"
"                                    The proxy server must be on the same network as the fiscal device."
msgstr ""

#. module: l10n_ke_edi_tremol
#: code:addons/l10n_ke_edi_tremol/models/account_move.py:0
#, python-format
msgid ""
"This credit note must reference the previous invoice, and this previous "
"invoice must have already been submitted."
msgstr ""

#. module: l10n_ke_edi_tremol
#: code:addons/l10n_ke_edi_tremol/models/account_move.py:0
#, python-format
msgid ""
"This invoice is not a Kenyan invoice and therefore can not be sent to the "
"device."
msgstr ""

#. module: l10n_ke_edi_tremol
#: code:addons/l10n_ke_edi_tremol/models/account_move.py:0
#, python-format
msgid ""
"This invoice's company currency is not in Kenyan Shillings, conversion to "
"KES is not possible."
msgstr ""

#. module: l10n_ke_edi_tremol
#: code:addons/l10n_ke_edi_tremol/models/account_move.py:0
#, python-format
msgid ""
"This invoice/credit note has not been posted. Please confirm it to continue."
msgstr ""

#. module: l10n_ke_edi_tremol
#: model_terms:ir.ui.view,arch_db:l10n_ke_edi_tremol.l10n_ke_inherit_account_move_search_view
msgid "To Send to TIMS"
msgstr ""

#. module: l10n_ke_edi_tremol
#: model_terms:ir.ui.view,arch_db:l10n_ke_edi_tremol.l10n_ke_inherit_account_move_form
msgid "Tremol GO3 Fiscal Device"
msgstr ""
