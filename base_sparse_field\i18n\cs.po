# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_sparse_field
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <jan.ho<PERSON><PERSON><PERSON>@centrum.cz>, 2021
# <PERSON><PERSON><PERSON>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:21+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2021\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: base_sparse_field
#: model:ir.model,name:base_sparse_field.model_base
msgid "Base"
msgstr "Základní část"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__boolean
msgid "Boolean"
msgstr "Boolean"

#. module: base_sparse_field
#: code:addons/base_sparse_field/models/models.py:0
#, python-format
msgid "Changing the storing system for field \"%s\" is not allowed."
msgstr "Změna systému ukládání pole \"%s\" není povolena."

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__char
msgid "Char"
msgstr "Znak"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__create_uid
msgid "Created by"
msgstr "Vytvořeno od"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__create_date
msgid "Created on"
msgstr "Vytvořeno"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__data
msgid "Data"
msgstr "Data"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__display_name
msgid "Display Name"
msgstr "Zobrazované jméno"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_ir_model_fields__ttype
msgid "Field Type"
msgstr "Typ pole"

#. module: base_sparse_field
#: model:ir.model,name:base_sparse_field.model_ir_model_fields
msgid "Fields"
msgstr "Pole"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__float
msgid "Float"
msgstr "Plovoucí"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__id
msgid "ID"
msgstr "ID"

#. module: base_sparse_field
#: model:ir.model.fields,help:base_sparse_field.field_ir_model_fields__serialization_field_id
msgid ""
"If set, this field will be stored in the sparse structure of the "
"serialization field, instead of having its own database column. This cannot "
"be changed after creation."
msgstr ""
"Pokud je nastaveno, toto pole bude uloženo v ojedinělé struktuře "
"serializačního pole namísto toho, aby měl vlastní sloupec databáze. To po "
"vytvoření již nelze změnit."

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__integer
msgid "Integer"
msgstr "Celé číslo"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test____last_update
msgid "Last Modified on"
msgstr "Naposled změněno"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__write_uid
msgid "Last Updated by"
msgstr "Naposledy upraveno od"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__write_date
msgid "Last Updated on"
msgstr "Naposled upraveno"

#. module: base_sparse_field
#: model:ir.model.fields.selection,name:base_sparse_field.selection__sparse_fields_test__selection__one
msgid "One"
msgstr "Jeden"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__partner
msgid "Partner"
msgstr "Partner"

#. module: base_sparse_field
#: code:addons/base_sparse_field/models/models.py:0
#, python-format
msgid "Renaming sparse field \"%s\" is not allowed"
msgstr "Přejmenování 'sparse field' \"%s\" není povoleno"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__selection
msgid "Selection"
msgstr "Výběr"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_ir_model_fields__serialization_field_id
msgid "Serialization Field"
msgstr "Pole serializace"

#. module: base_sparse_field
#: code:addons/base_sparse_field/models/models.py:0
#, python-format
msgid "Serialization field %r not found for sparse field %s!"
msgstr "Serializační pole %r nebylo nalezeno pro sparse pole %s!"

#. module: base_sparse_field
#: model:ir.model,name:base_sparse_field.model_sparse_fields_test
msgid "Sparse fields Test"
msgstr "Test ojedinělého pole"

#. module: base_sparse_field
#: model:ir.model.fields.selection,name:base_sparse_field.selection__sparse_fields_test__selection__two
msgid "Two"
msgstr "Dva"

#. module: base_sparse_field
#: model:ir.model.fields.selection,name:base_sparse_field.selection__ir_model_fields__ttype__serialized
msgid "serialized"
msgstr "serializováno"
