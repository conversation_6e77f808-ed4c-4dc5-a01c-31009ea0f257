# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * survey
# 
# Translators:
# Sit<PERSON><PERSON>n LY <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 06:49+0000\n"
"PO-Revision-Date: 2018-10-08 06:49+0000\n"
"Last-Translator: <PERSON> Souphorn <<EMAIL>>, 2018\n"
"Language-Team: Khmer (https://www.transifex.com/odoo/teams/41243/km/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: km\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_tree
msgid "#Questions"
msgstr ""

#. module: survey
#: model:mail.template,subject:survey.email_template_survey
msgid "${object.title}: Survey"
msgstr ""

#. module: survey
#: code:addons/survey/models/survey.py:122
#, python-format
msgid "%s (copy)"
msgstr "%s (ចម្លង)"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.page
msgid "&amp;times;"
msgstr "&amp;times;"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.403
msgid "403: Forbidden"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid "<b>Question </b>"
msgstr ""

#. module: survey
#: model:mail.template,body_html:survey.email_template_survey
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hello<br/><br/>\n"
"        We are conducting a survey, and your response would be appreciated.\n"
"        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"            <a href=\"__URL__\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                Start Survey\n"
"            </a>\n"
"        </div>\n"
"        Thanks for your participation!\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-2x\" role=\"img\" aria-label=\"Numeric\" title=\"Numeric\">123..</i>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_matrix
msgid ""
"<i class=\"fa fa-bar-chart\"/>\n"
"                    Graph"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_choice
msgid "<i class=\"fa fa-bar-chart-o\"/> Graph"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_choice
msgid "<i class=\"fa fa-bar-chart-o\"/> Pie Chart"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-check-square-o fa-lg\"/> answer"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-circle-o  fa-lg\"/> answer"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-dot-circle-o fa-lg\"/> answer"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_number
msgid ""
"<i class=\"fa fa-list-alt\"/>\n"
"                    All Data"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_matrix
msgid ""
"<i class=\"fa fa-list-alt\"/>\n"
"                    Data"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_choice
msgid "<i class=\"fa fa-list-alt\"/> Data"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_number
msgid ""
"<i class=\"fa fa-list-ol\"/>\n"
"                    Most Common"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-square-o fa-lg\"/> answer"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid "<i class=\"fa fa-times\"/> Clear All Filters"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid ""
"<span class=\"badge badge-primary only_left_radius filter-all\">All "
"surveys</span><span class=\"badge badge-secondary only_right_radius filter-"
"finished\">Finished surveys</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid ""
"<span class=\"badge badge-secondary only_left_radius filter-all\">All "
"surveys</span><span class=\"badge badge-primary only_right_radius filter-"
"finished\">Finished surveys</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_number
msgid "<span class=\"badge badge-secondary only_left_radius\">Average </span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_number
msgid "<span class=\"badge badge-secondary only_left_radius\">Maximum </span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_number
msgid "<span class=\"badge badge-secondary only_left_radius\">Minimum </span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_number
msgid "<span class=\"badge badge-secondary only_left_radius\">Sum </span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid "<span class=\"fa fa-filter\"/>  Filters"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.no_result
msgid ""
"<span>\n"
"                            <i style=\"font-size:1.8em\" class=\"fa fa-users float-right\" role=\"img\" aria-label=\"No answer\" title=\"No answer\"/>\n"
"                        </span>\n"
"                        Sorry, No one answered this survey yet"
msgstr ""

#. module: survey
#: code:addons/survey/models/survey.py:688
#, python-format
msgid "A label must be attached to only one question."
msgstr ""

#. module: survey
#: sql_constraint:survey.question:0
msgid "A length must be positive!"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__description
msgid "A long description of the purpose of the survey"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_label__quizz_mark
msgid ""
"A positive score indicates a correct choice; a negative or null score "
"indicates a wrong answer"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.page
msgid "A problem has occured"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.frow_2_2_4
msgid "A process is defined for all enterprise flows"
msgstr ""

#. module: survey
#: sql_constraint:survey.user_input:0
msgid "A token must be unique!"
msgstr ""

#. module: survey
#: model:survey.page,title:survey.feedback_1
msgid "About your Odoo usage"
msgstr ""

#. module: survey
#: selection:survey.mail.compose.message,moderation_status:0
msgid "Accepted"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_needaction
msgid "Action Needed"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__active
msgid "Active"
msgstr "សកម្ម"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__active_domain
msgid "Active domain"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_ids
msgid "Activities"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_state
msgid "Activity State"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__add_sign
msgid "Add Sign"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid ""
"Add a list of email of recipients (will not be converted into contacts). "
"Separated by commas, semicolons or newline..."
msgstr ""

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Add a new survey"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid "Add existing contacts..."
msgstr ""

#. module: survey
#: model:survey.label,value:survey.fcol_2_1_3
#: model:survey.label,value:survey.fcol_2_2_3
#: model:survey.label,value:survey.fcol_2_5_3
#: model:survey.label,value:survey.fcol_2_7_3
msgid "Agree"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Allow Comments"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_page__description
msgid "An introductory text to your page"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Analyze Answers"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_choice
msgid "Answer Choices"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__type
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__answer_type
msgid "Answer Type"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid "Answered"
msgstr ""

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_user_input
#: model:ir.model.fields,field_description:survey.field_survey_question__user_input_line_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__user_input_line_ids
#: model:ir.ui.menu,name:survey.menu_survey_type_form1
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Answers"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_mail_compose_message__no_auto_thread
msgid ""
"Answers do not go in the original document discussion thread. This has an "
"impact on the generated message-id."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Archived"
msgstr "ឯកសារ"

#. module: survey
#: model:survey.question,question:survey.feedback_1_1
msgid "Are you using Odoo on a daily basis?"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__attachment_ids
msgid "Attachments"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_mail_compose_message__attachment_ids
msgid ""
"Attachments are linked to a document through model / res_id and to the "
"message through this field."
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__author_id
msgid "Author"
msgstr "អ្នកបង្កើត"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_mail_compose_message__author_id
msgid ""
"Author of the message. If not set, email_from may hold an email address that"
" did not match any partner."
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__author_avatar
msgid "Author's avatar"
msgstr ""

#. module: survey
#: model:ir.model,name:survey.model_ir_autovacuum
msgid "Automatic Vacuum"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.back
msgid "Back to Survey"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_1_2_4
msgid "CRM"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid "Cancel"
msgstr "លុបចោល"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__channel_ids
msgid "Channels"
msgstr "ឆានែល"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__child_ids
msgid "Child Messages"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Choices"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.simple_choice
msgid "Choose..."
msgstr ""

#. module: survey
#: code:addons/survey/models/survey.py:110
#, python-format
msgid "Click here to start survey"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.page
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid "Close"
msgstr "បិទ"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_stage__closed
#: model:survey.stage,name:survey.stage_closed
msgid "Closed"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Color"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__color
msgid "Color Index"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_comments
#: selection:survey.mail.compose.message,message_type:0
msgid "Comment"
msgstr "មតិយោបល់"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comment_count_as_answer
msgid "Comment Field is an Answer Choice"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comments_message
msgid "Comment Message"
msgstr ""

#. module: survey
#: model:survey.page,title:survey.feedback_3
msgid "Community and contributors"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.layout
msgid "Company name"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_tree
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_search
#: selection:survey.user_input,state:0
msgid "Completed"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid "Compose Email"
msgstr "សរសេរអុីម៉ែល"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__composition_mode
msgid "Composition mode"
msgstr ""

#. module: survey
#: model:ir.ui.menu,name:survey.menu_surveys_configuration
msgid "Configuration"
msgstr "កំណត់ផ្លាស់ប្តូរ"

#. module: survey
#: model:survey.label,value:survey.frow_2_7_2
msgid "Configuration wizard exists for each important setting"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Constraints"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__body
msgid "Contents"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid ""
"Copy and paste the HTML code below to add this web link to any webpage."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid "Copy, paste and share the web link below to your audience."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.layout
msgid "Copyright &amp;copy;"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__date_create
msgid "Create Date"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.layout
msgid "Create a"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_page__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_question__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_stage__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_survey__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__create_uid
msgid "Created by"
msgstr "បង្កើតដោយ"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label__create_date
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__create_date
#: model:ir.model.fields,field_description:survey.field_survey_page__create_date
#: model:ir.model.fields,field_description:survey.field_survey_question__create_date
#: model:ir.model.fields,field_description:survey.field_survey_stage__create_date
#: model:ir.model.fields,field_description:survey.field_survey_survey__create_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input__create_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__create_date
msgid "Created on"
msgstr "បង្កើតនៅ"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__date_create
msgid "Creation Date"
msgstr "ថ្ងៃបង្កើត"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_mail_compose_message__starred
msgid "Current user has a starred notification linked to this message"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__date
#: selection:survey.question,type:0
#: selection:survey.user_input_line,answer_type:0
msgid "Date"
msgstr "កាលបរិច្ឆេត"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_date
msgid "Date answer"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_user_input__deadline
msgid "Date by which the person can open the survey and submit answers"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__deadline
msgid "Deadline"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_mail_compose_message__date_deadline
msgid ""
"Deadline to which the invitation to respond for this survey is valid. If the"
" field is empty,        the invitation is still valid."
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__date_deadline
msgid "Deadline to which the invitation to respond is valid"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Delete"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__auto_delete
msgid "Delete Emails"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__auto_delete_message
msgid "Delete Message Copy"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_mail_compose_message__auto_delete
msgid "Delete sent emails (mass mailing only)"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_page__description
#: model:ir.model.fields,field_description:survey.field_survey_question__description
#: model:ir.model.fields,field_description:survey.field_survey_survey__description
msgid "Description"
msgstr "ការពិពណ៌​នា​"

#. module: survey
#: model:survey.label,value:survey.frow_2_7_1
msgid "Descriptions and help tooltips are clear enough"
msgstr ""

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid ""
"Design easily your survey, send invitations to answer by email and analyse "
"answers."
msgstr ""

#. module: survey
#: model:survey.label,value:survey.fcol_2_1_2
#: model:survey.label,value:survey.fcol_2_2_2
#: model:survey.label,value:survey.fcol_2_5_2
#: model:survey.label,value:survey.fcol_2_7_2
msgid "Disagree"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__display_mode
msgid "Display Mode"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label__display_name
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__display_name
#: model:ir.model.fields,field_description:survey.field_survey_page__display_name
#: model:ir.model.fields,field_description:survey.field_survey_question__display_name
#: model:ir.model.fields,field_description:survey.field_survey_stage__display_name
#: model:ir.model.fields,field_description:survey.field_survey_survey__display_name
#: model:ir.model.fields,field_description:survey.field_survey_user_input__display_name
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__display_name
msgid "Display Name"
msgstr "ឈ្មោះសំរាប់បង្ហាញ"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Display mode"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_mail_compose_message__auto_delete_message
msgid ""
"Do not keep a copy of the email in the document communication history (mass "
"mailing only)"
msgstr ""

#. module: survey
#: model:survey.question,question:survey.feedback_3_3
msgid "Do you have a proposition to attract new contributors?"
msgstr ""

#. module: survey
#: model:survey.question,question:survey.feedback_3_2
msgid "Do you have a proposition to help people to contribute?"
msgstr ""

#. module: survey
#: model:survey.question,question:survey.feedback_2_3
msgid "Do you have suggestions on how to improve the process view ?"
msgstr ""

#. module: survey
#: model:survey.stage,name:survey.stage_draft
msgid "Draft"
msgstr "ព្រៀង"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Dropdown menu"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__email
msgid "E-mail"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Edit Pages and Questions"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Edit Survey"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_search
#: selection:survey.mail.compose.message,message_type:0
msgid "Email"
msgstr "អុីម៉ែល"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__email_template_id
msgid "Email Template"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_mail_compose_message__email_from
msgid ""
"Email address of the sender. This field is set when no matching partner is "
"found and replaces the author_id field in the chatter."
msgstr ""

#. module: survey
#: model:ir.model,name:survey.model_survey_mail_compose_message
msgid "Email composition wizard for Survey"
msgstr ""

#. module: survey
#: model:survey.page,title:survey.feedback_2
msgid "Ergonomy and ease of use"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__constr_error_msg
msgid "Error message"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__partner_ids
msgid "Existing contacts"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.frow_2_7_3
msgid "Extra modules proposed are relevant"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__starred_partner_ids
msgid "Favorited By"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_choice
msgid "Filter question"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_1_2_3
msgid "Financial Management"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_stage__fold
msgid "Folded in kanban view"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_follower_ids
msgid "Followers"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_channel_ids
msgid "Followers (Channels)"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Format"
msgstr ""

#. module: survey
#: selection:survey.user_input_line,answer_type:0
msgid "Free Text"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_free_text
msgid "Free Text answer"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__email_from
msgid "From"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_search
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
#: model_terms:ir.ui.view,arch_db:survey.survey_response_line_search
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_search
msgid "Group By"
msgstr "ជា​ក្រុម​តាម"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__has_error
#: model:ir.model.fields,help:survey.field_survey_mail_compose_message__has_error
msgid "Has error"
msgstr ""

#. module: survey
#: model:survey.question,question:survey.feedback_3_1
msgid "How do you contribute or plan to contribute to Odoo?"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_1_2_6
msgid "Human Resources"
msgstr "ធនធានមនុស្ស"

#. module: survey
#: model:survey.label,value:survey.choice_3_1_3
msgid "I develop new features"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_4_1_4
msgid "I do not publish my developments"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_3_1_4
msgid "I help to translate"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_4_1_3
msgid "I host them on my own website"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_3_1_1
msgid "I participate to discussion and forums"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_4_1_1
msgid "I use Github, like all official Odoo projects"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_4_1_2
msgid "I use another repository system (SourceForge...)"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.frow_2_1_3
msgid "I use the contextual help in Odoo"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_3_1_5
msgid "I write documentations"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_3_1_2
msgid "I'd like to contribute but I don't know how?"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label__id
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__id
#: model:ir.model.fields,field_description:survey.field_survey_page__id
#: model:ir.model.fields,field_description:survey.field_survey_question__id
#: model:ir.model.fields,field_description:survey.field_survey_stage__id
#: model:ir.model.fields,field_description:survey.field_survey_survey__id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__id
msgid "ID"
msgstr "ID"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__token
msgid "Identification token"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_unread
msgid "If checked new messages require your attention."
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__users_can_go_back
msgid "If checked, users can go back to previous pages."
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_stage__closed
#: model:ir.model.fields,help:survey.field_survey_survey__is_closed
msgid "If closed, people won't be able to answer to surveys in this column."
msgstr ""

#. module: survey
#: code:addons/survey/models/survey.py:471
#: model:survey.question,comments_message:survey.feedback_1_1
#: model:survey.question,comments_message:survey.feedback_1_2
#: model:survey.question,comments_message:survey.feedback_2_1
#: model:survey.question,comments_message:survey.feedback_2_2
#: model:survey.question,comments_message:survey.feedback_2_3
#: model:survey.question,comments_message:survey.feedback_2_4
#: model:survey.question,comments_message:survey.feedback_2_5
#: model:survey.question,comments_message:survey.feedback_2_6
#: model:survey.question,comments_message:survey.feedback_2_7
#: model:survey.question,comments_message:survey.feedback_3_1
#: model:survey.question,comments_message:survey.feedback_3_2
#: model:survey.question,comments_message:survey.feedback_3_3
#: model:survey.question,comments_message:survey.feedback_4_1
#, python-format
msgid "If other, please specify:"
msgstr ""

#. module: survey
#: model_terms:survey.page,description:survey.feedback_4
msgid "If you do not contribute or develop in Odoo, skip this page."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.sfinished
msgid "If you wish, you can"
msgstr ""

#. module: survey
#: model:survey.stage,name:survey.stage_in_progress
msgid "In progress"
msgstr ""

#. module: survey
#: code:addons/survey/wizard/survey_email_compose_message.py:72
#, python-format
msgid "Incorrect Email Address: %s"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_mail_compose_message__parent_id
msgid "Initial thread message."
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_email
msgid "Input must be an email"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_tree
msgid "Invitations sent"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__is_closed
msgid "Is closed"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__designed
msgid "Is designed?"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_2_4_2
msgid "It can be improved"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.frow_2_1_2
msgid "It helps in the beginning"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.frow_2_1_5
msgid "It is clear"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.frow_2_1_4
msgid "It is complete"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.frow_2_1_1
msgid "It is up-to-date"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.frow_2_2_5
msgid "It's easy to find the process you need"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label__sequence
msgid "Label Sequence order"
msgstr ""

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_label_form
#: model:ir.ui.menu,name:survey.menu_survey_label_form1
msgid "Labels"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label____last_update
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message____last_update
#: model:ir.model.fields,field_description:survey.field_survey_page____last_update
#: model:ir.model.fields,field_description:survey.field_survey_question____last_update
#: model:ir.model.fields,field_description:survey.field_survey_stage____last_update
#: model:ir.model.fields,field_description:survey.field_survey_survey____last_update
#: model:ir.model.fields,field_description:survey.field_survey_user_input____last_update
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line____last_update
msgid "Last Modified on"
msgstr "កាលបរិច្ឆេតកែប្រែចុងក្រោយ"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_page__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_question__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_stage__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_survey__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__write_uid
msgid "Last Updated by"
msgstr "ផ្លាស់ប្តូរចុងក្រោយ"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label__write_date
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__write_date
#: model:ir.model.fields,field_description:survey.field_survey_page__write_date
#: model:ir.model.fields,field_description:survey.field_survey_question__write_date
#: model:ir.model.fields,field_description:survey.field_survey_stage__write_date
#: model:ir.model.fields,field_description:survey.field_survey_survey__write_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input__write_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__write_date
msgid "Last Updated on"
msgstr "ផ្លាស់ប្តូរចុងក្រោយ"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__last_displayed_page_id
msgid "Last displayed page"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Late Activities"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__layout
msgid "Layout"
msgstr ""

#. module: survey
#: selection:survey.user_input,type:0
msgid "Link"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__multi_email
msgid "List of emails"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__is_log
msgid "Log an Internal Note"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__auth_required
#: model_terms:ir.ui.view,arch_db:survey.auth_required
msgid "Login required"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.layout
msgid "Logo"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__mail_activity_type_id
msgid "Mail Activity Type"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: survey
#: model:res.groups,name:survey.group_survey_manager
msgid "Manager"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__constr_mandatory
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Mandatory Answer"
msgstr ""

#. module: survey
#: selection:survey.user_input,type:0
msgid "Manually"
msgstr ""

#. module: survey
#: selection:survey.question,type:0
msgid "Matrix"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__matrix_subtype
msgid "Matrix Type"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid "Matrix:"
msgstr ""

#. module: survey
#: sql_constraint:survey.question:0
msgid "Max date cannot be smaller than min date!"
msgstr ""

#. module: survey
#: sql_constraint:survey.question:0
msgid "Max length cannot be smaller than min length!"
msgstr ""

#. module: survey
#: sql_constraint:survey.question:0
msgid "Max value cannot be smaller than min value!"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_date
msgid "Maximum Date"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_length_max
msgid "Maximum Text Length"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_float_value
msgid "Maximum value"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.403
msgid "Maybe you were looking for"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__record_name
msgid "Message Record Name"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_mail_compose_message__message_type
msgid ""
"Message type: email for email message, notification for system message, "
"comment for other messages such as user replies"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_mail_compose_message__message_id
msgid "Message unique identifier"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__message_id
msgid "Message-Id"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_ids
msgid "Messages"
msgstr "សារ"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_date
msgid "Minimum Date"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_length_min
msgid "Minimum Text Length"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_float_value
msgid "Minimum value"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__moderator_id
msgid "Moderated By"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__moderation_status
msgid "Moderation Status"
msgstr ""

#. module: survey
#: selection:survey.question,type:0
msgid "Multiple Lines Text Box"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Multiple choice with multiple answers"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Multiple choice with one answer"
msgstr ""

#. module: survey
#: selection:survey.question,type:0
msgid "Multiple choice: multiple answers allowed"
msgstr ""

#. module: survey
#: selection:survey.question,type:0
msgid "Multiple choice: only one answer"
msgstr ""

#. module: survey
#: selection:survey.question,matrix_subtype:0
msgid "Multiple choices per row"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "My Activities"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_stage__name
msgid "Name"
msgstr "ឈ្មោះ"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_mail_compose_message__record_name
msgid "Name get of the related document."
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__needaction
#: model:ir.model.fields,help:survey.field_survey_mail_compose_message__needaction
msgid "Need Action"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__need_moderation
msgid "Need moderation"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_search
msgid "New"
msgstr "ថ្មី"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.page
msgid "Next page"
msgstr ""

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_page_form
msgid "No pages found"
msgstr ""

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_question_form
msgid "No questions found"
msgstr ""

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_label_form
msgid "No survey labels found"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__no_auto_thread
msgid "No threading for answers"
msgstr ""

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_user_input_line
msgid "No user input lines found"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_1_1_4
msgid "No, I just tested it"
msgstr ""

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_selected_survey_user_input
#: model_terms:ir.actions.act_window,help:survey.action_survey_user_input
msgid "Nobody has replied to your surveys yet"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.notopen
msgid "Not open"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.nopages
msgid "Not ready"
msgstr ""

#. module: survey
#: selection:survey.user_input,state:0
msgid "Not started yet"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__notification_ids
msgid "Notifications"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__notify
msgid "Notify followers"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_mail_compose_message__notify
msgid "Notify followers of the document (mass post only)"
msgstr ""

#. module: survey
#: selection:survey.user_input_line,answer_type:0
msgid "Number"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__column_nb
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Number of columns"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__tot_comp_survey
msgid "Number of completed surveys"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_error_counter
msgid "Number of error"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__tot_sent_survey
msgid "Number of sent surveys"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__tot_start_survey
msgid "Number of started surveys"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_unread_counter
msgid "Number of unread messages"
msgstr ""

#. module: survey
#: selection:survey.question,type:0
msgid "Numerical Value"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_number
msgid "Numerical answer"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_number
msgid "Occurence"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.layout
msgid "Odoo"
msgstr ""

#. module: survey
#: selection:survey.question,matrix_subtype:0
msgid "One choice per row"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Options"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__mail_server_id
msgid "Outgoing mail server"
msgstr ""

#. module: survey
#: selection:survey.survey,activity_state:0
msgid "Overdue"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__page_id
#: model_terms:ir.ui.view,arch_db:survey.page
#: model_terms:ir.ui.view,arch_db:survey.survey_page_search
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Page"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_page__title
#: model_terms:ir.ui.view,arch_db:survey.survey_page_form
msgid "Page Title"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_page__sequence
msgid "Page number"
msgstr ""

#. module: survey
#: model:ir.actions.act_window,name:survey.act_survey_pages
#: model:ir.actions.act_window,name:survey.action_survey_page_form
#: model:ir.model.fields,field_description:survey.field_survey_survey__page_ids
#: model:ir.ui.menu,name:survey.menu_survey_page_form1
msgid "Pages"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__parent_id
msgid "Parent Message"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_search
msgid "Partially Completed"
msgstr ""

#. module: survey
#: selection:survey.user_input,state:0
msgid "Partially completed"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__partner_id
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_search
msgid "Partner"
msgstr "ដៃគូ"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__needaction_partner_ids
msgid "Partners with Need Action"
msgstr ""

#. module: survey
#: selection:survey.mail.compose.message,moderation_status:0
msgid "Pending Moderation"
msgstr ""

#. module: survey
#: model:survey.stage,name:survey.stage_permanent
msgid "Permanent"
msgstr ""

#. module: survey
#: selection:survey.survey,activity_state:0
msgid "Planned"
msgstr ""

#. module: survey
#: code:addons/survey/wizard/survey_email_compose_message.py:188
#, python-format
msgid "Please enter at least one valid recipient."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.page
msgid "Previous page"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Print Survey"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_form
msgid "Print These Answers"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__print_url
msgid "Print link"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_1_2_5
msgid "Project Management"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__public_url_html
msgid "Public HTML web link"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__public_url
msgid "Public link"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__public_url_html
msgid "Public link (html version)"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__print_url
msgid "Public link to the empty survey"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__result_url
msgid "Public link to the survey results"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__public_url
msgid "Public url"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_1_2_2
msgid "Purchases Management"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label__question_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__question_id
#: model_terms:ir.ui.view,arch_db:survey.survey_label_search
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Question"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label__question_id_2
msgid "Question 2"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__question
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Question Name"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Question name"
msgstr ""

#. module: survey
#: model:ir.actions.act_window,name:survey.act_survey_page_question
#: model:ir.actions.act_window,name:survey.act_survey_question
#: model:ir.actions.act_window,name:survey.action_survey_question_form
#: model:ir.model.fields,field_description:survey.field_survey_page__question_ids
#: model:ir.ui.menu,name:survey.menu_survey_question_form1
msgid "Questions"
msgstr ""

#. module: survey
#: model:survey.page,title:survey.feedback_4
msgid "Questions for developers"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__quizz_mode
msgid "Quizz Mode"
msgstr ""

#. module: survey
#: selection:survey.question,display_mode:0
msgid "Radio Buttons"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__rating_value
msgid "Rating Value"
msgstr ""

#. module: survey
#: selection:survey.mail.compose.message,moderation_status:0
msgid "Rejected"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__res_id
msgid "Related Document ID"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__model
msgid "Related Document Model"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__rating_ids
msgid "Related ratings"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_mail_compose_message__reply_to
msgid ""
"Reply email address. Setting the reply_to bypasses the automatic thread "
"creation."
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__reply_to
msgid "Reply-To"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__result_url
msgid "Results link"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_suggested_row
msgid "Row answer"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row1"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row2"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row3"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Rows"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__labels_ids_2
msgid "Rows of the Matrix"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.frow_2_7_4
msgid "Running the configuration wizards is a good way to spare time"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_1_2_1
msgid "Sales Management"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid "Save as a new template"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid "Save as new template"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__quizz_score
msgid "Score for the quiz"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label__quizz_mark
msgid "Score for this choice"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__quizz_mark
msgid "Score given for this choice"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_label_search
msgid "Search Label"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_search
msgid "Search Page"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Search Question"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_search
msgid "Search Survey"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_response_line_search
msgid "Search User input lines"
msgstr ""

#. module: survey
#: selection:survey.question,display_mode:0
msgid "Selection Box"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid "Send"
msgstr "បញ្ជូន"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_form
msgid "Send Invitation Again"
msgstr ""

#. module: survey
#: selection:survey.mail.compose.message,public:0
msgid "Send by email the public web link to your audience."
msgstr ""

#. module: survey
#: selection:survey.mail.compose.message,public:0
msgid ""
"Send private invitation to your audience (only one response per recipient "
"and per invitation)."
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__sequence
#: model:ir.model.fields,field_description:survey.field_survey_stage__sequence
msgid "Sequence"
msgstr "លំដាប់"

#. module: survey
#: sql_constraint:survey.stage:0
msgid "Sequence number MUST be a natural"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Share &amp;amp; Invite"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Share and invite by email"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__public
msgid "Share options"
msgstr ""

#. module: survey
#: selection:survey.mail.compose.message,public:0
msgid "Share the public web link to your audience."
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comments_allowed
msgid "Show Comments Field"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Show all records which has next action date is before today"
msgstr ""

#. module: survey
#: selection:survey.question,type:0
msgid "Single Line Text Box"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__skipped
#: model_terms:ir.ui.view,arch_db:survey.result
msgid "Skipped"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_mail_compose_message__author_avatar
msgid ""
"Small-sized image of this contact. It is automatically resized as a 64x64px "
"image, with aspect ratio preserved. Use this field anywhere a small image is"
" required."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.page
msgid ""
"Something went wrong while contacting survey server. <strong class=\"text-"
"danger\">Your answers have probably not been recorded.</strong> Try "
"refreshing."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result
msgid "Sorry, No one answered this question."
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__stage_id
#: model_terms:ir.ui.view,arch_db:survey.survey_stage_form
msgid "Stage"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__starred
msgid "Starred"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_init
msgid "Start Survey"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_tree
msgid "Started"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__state
msgid "Status"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__subject
msgid "Subject"
msgstr "ចំណងជើង"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid "Subject..."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.page
msgid "Submit survey"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__subtype_id
msgid "Subtype"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_suggested
msgid "Suggested answer"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_label__value
msgid "Suggested value"
msgstr ""

#. module: survey
#: selection:survey.user_input_line,answer_type:0
msgid "Suggestion"
msgstr ""

#. module: survey
#: model:ir.model,name:survey.model_survey_survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_page__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_question__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__survey_id
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_page_search
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
#: model_terms:ir.ui.view,arch_db:survey.survey_response_line_search
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_tree
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_search
msgid "Survey"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_response_line_tree
msgid "Survey Answer Line"
msgstr ""

#. module: survey
#: model:ir.model,name:survey.model_survey_label
#: model_terms:ir.ui.view,arch_db:survey.survey_label_tree
msgid "Survey Label"
msgstr ""

#. module: survey
#: model:ir.model,name:survey.model_survey_page
#: model_terms:ir.ui.view,arch_db:survey.survey_page_form
#: model_terms:ir.ui.view,arch_db:survey.survey_page_tree
msgid "Survey Page"
msgstr ""

#. module: survey
#: model:ir.model,name:survey.model_survey_question
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_tree
msgid "Survey Question"
msgstr ""

#. module: survey
#: model:ir.model,name:survey.model_survey_stage
msgid "Survey Stage"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Survey Title"
msgstr ""

#. module: survey
#: model:ir.model,name:survey.model_survey_user_input
msgid "Survey User Input"
msgstr ""

#. module: survey
#: model:ir.model,name:survey.model_survey_user_input_line
msgid "Survey User Input Line"
msgstr ""

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_user_input_line
msgid "Survey User Input lines"
msgstr ""

#. module: survey
#: model:ir.actions.act_window,name:survey.action_selected_survey_user_input
msgid "Survey User input"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_form
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_tree
msgid "Survey User inputs"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_matrix
msgid "Survey filter"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__page_id
msgid "Survey page"
msgstr ""

#. module: survey
#: model:ir.actions.server,name:survey.survey_action_server_clean_test_answers
msgid "Survey: Clean test answers"
msgstr ""

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_form
#: model:ir.ui.menu,name:survey.menu_survey_form
#: model:ir.ui.menu,name:survey.menu_surveys
msgid "Surveys"
msgstr ""

#. module: survey
#: selection:survey.mail.compose.message,message_type:0
msgid "System notification"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Test"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_search
msgid "Test Entries"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__test_entry
msgid "Test Entry"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Test Survey"
msgstr ""

#. module: survey
#: selection:survey.user_input_line,answer_type:0
msgid "Text"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_text
msgid "Text answer"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.sfinished
msgid "Thank you!"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__thank_you_message
msgid "Thanks Message"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.frow_2_5_3
msgid "The 'Usability/Extended View' group helps in daily work"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.frow_2_5_4
msgid "The 'Usability/Extended View' group hides only optional fields"
msgstr ""

#. module: survey
#: code:addons/survey/models/survey.py:819
#, python-format
msgid "The answer must be in the right type"
msgstr ""

#. module: survey
#: code:addons/survey/models/survey.py:485
#: model:survey.question,validation_error_msg:survey.feedback_1_1
#: model:survey.question,validation_error_msg:survey.feedback_1_2
#: model:survey.question,validation_error_msg:survey.feedback_2_1
#: model:survey.question,validation_error_msg:survey.feedback_2_2
#: model:survey.question,validation_error_msg:survey.feedback_2_3
#: model:survey.question,validation_error_msg:survey.feedback_2_4
#: model:survey.question,validation_error_msg:survey.feedback_2_5
#: model:survey.question,validation_error_msg:survey.feedback_2_6
#: model:survey.question,validation_error_msg:survey.feedback_2_7
#: model:survey.question,validation_error_msg:survey.feedback_3_1
#: model:survey.question,validation_error_msg:survey.feedback_3_2
#: model:survey.question,validation_error_msg:survey.feedback_3_3
#: model:survey.question,validation_error_msg:survey.feedback_4_1
#, python-format
msgid "The answer you entered has an invalid format."
msgstr ""

#. module: survey
#: code:addons/survey/wizard/survey_email_compose_message.py:163
#, python-format
msgid ""
"The content of the text don't contain '__URL__'.                     __URL__"
" is automaticaly converted into the special url of the survey."
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_2_4_1
msgid "The current menu structure is good"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.frow_2_5_5
msgid "The groups set on menu items are relevant"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_2_6_3
msgid "The number of groups is good"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.403
msgid "The page you were looking for could not be authorized."
msgstr ""

#. module: survey
#: model:survey.label,value:survey.frow_2_5_1
msgid "The security rules defined on groups are useful"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_2_6_2
msgid "There are too few groups defined, security isn't accurate enough"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_2_6_1
msgid "There are too many groups defined, security is too complex to set"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_2_4_3
msgid "There are too much menus, it's complex to understand"
msgstr ""

#. module: survey
#: model_terms:survey.page,description:survey.feedback_2
msgid ""
"These questions relate to the ergonomy and ease of use of Odoo. Try to remind your firsts days on Odoo and\n"
"what have been your difficulties."
msgstr ""

#. module: survey
#: model:survey.label,value:survey.frow_2_2_2
msgid "They are clean and correct"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.frow_2_2_3
msgid "They are useful on a daily usage"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.frow_2_2_1
msgid "They help new users to understand Odoo"
msgstr ""

#. module: survey
#: code:addons/survey/models/survey.py:544
#: code:addons/survey/tests/test_survey.py:86
#, python-format
msgid "This answer must be an email address"
msgstr ""

#. module: survey
#: code:addons/survey/models/survey.py:588
#: code:addons/survey/tests/test_survey.py:107
#, python-format
msgid "This is not a date"
msgstr ""

#. module: survey
#: code:addons/survey/models/survey.py:565
#: code:addons/survey/tests/test_survey.py:97
#, python-format
msgid "This is not a number"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_mail_compose_message__multi_email
msgid ""
"This list of emails of recipients will not be converted in contacts.        "
"Emails must be separated by commas, semicolons or newline."
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__thank_you_message
msgid "This message will be displayed when survey is completed"
msgstr ""

#. module: survey
#: code:addons/survey/models/survey.py:806
#, python-format
msgid "This question cannot be unanswered or skipped."
msgstr ""

#. module: survey
#: code:addons/survey/models/survey.py:489
#: model:survey.question,constr_error_msg:survey.feedback_1_1
#: model:survey.question,constr_error_msg:survey.feedback_1_2
#: model:survey.question,constr_error_msg:survey.feedback_2_1
#: model:survey.question,constr_error_msg:survey.feedback_2_2
#: model:survey.question,constr_error_msg:survey.feedback_2_3
#: model:survey.question,constr_error_msg:survey.feedback_2_4
#: model:survey.question,constr_error_msg:survey.feedback_2_5
#: model:survey.question,constr_error_msg:survey.feedback_2_6
#: model:survey.question,constr_error_msg:survey.feedback_2_7
#: model:survey.question,constr_error_msg:survey.feedback_3_1
#: model:survey.question,constr_error_msg:survey.feedback_3_2
#: model:survey.question,constr_error_msg:survey.feedback_3_3
#: model:survey.question,constr_error_msg:survey.feedback_4_1
#, python-format
msgid "This question requires an answer."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.nopages
msgid "This survey has no pages by now!"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.notopen
msgid "This survey is not open. Thank you for your interest!"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.auth_required
msgid "This survey is open only to registered people. Please"
msgstr ""

#. module: survey
#: model_terms:survey.survey,description:survey.feedback_form
msgid "This survey should take less than five minutes."
msgstr ""

#. module: survey
#: model:survey.label,value:survey.frow_2_5_2
msgid ""
"Those security rules are standard and can be used out-of-the-box in most "
"cases"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__title
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Title"
msgstr "ចំណងជើង​"

#. module: survey
#: selection:survey.survey,activity_state:0
msgid "Today"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Today Activities"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.fcol_2_1_4
#: model:survey.label,value:survey.fcol_2_2_4
#: model:survey.label,value:survey.fcol_2_5_4
#: model:survey.label,value:survey.fcol_2_7_4
msgid "Totally agree"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.fcol_2_1_1
#: model:survey.label,value:survey.fcol_2_2_1
#: model:survey.label,value:survey.fcol_2_5_1
#: model:survey.label,value:survey.fcol_2_7_1
msgid "Totally disagree"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_mail_compose_message__tracking_value_ids
msgid ""
"Tracked values are stored in a separate model. This field allow to "
"reconstruct the tracking and to generate statistics on the model."
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__tracking_value_ids
msgid "Tracking values"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__message_type
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Type"
msgstr "ប្រភេទ"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__type
msgid "Type of Question"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Type of answers"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__labels_ids
msgid "Types of answers"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_unread
msgid "Unread Messages"
msgstr "សារមិនទាន់អាន"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Upcoming Activities"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__use_active_domain
msgid "Use active domain"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_mail_compose_message__template_id
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid "Use template"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__description
msgid ""
"Use this field to add         additional explanations about your question"
msgstr ""

#. module: survey
#: model:res.groups,name:survey.group_survey_user
msgid "User"
msgstr ""

#. module: survey
#: model:survey.survey,title:survey.feedback_form
msgid "User Feedback Form"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__user_input_id
#: model_terms:ir.ui.view,arch_db:survey.survey_response_line_search
msgid "User Input"
msgstr ""

#. module: survey
#: model:ir.ui.menu,name:survey.menu_survey_response_line_form
msgid "User Input Lines"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_choice
#: model_terms:ir.ui.view,arch_db:survey.result_number
#: model_terms:ir.ui.view,arch_db:survey.result_text
msgid "User Responses"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "User can come back in the previous page"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_form
msgid "User input line details"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__user_input_ids
msgid "User responses"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__users_can_go_back
msgid "Users can go back"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__auth_required
msgid ""
"Users with a public link will be requested to login before taking part to "
"the survey"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_required
msgid "Validate entry"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_error_msg
msgid "Validation Error message"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_form
msgid "View Results"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "View results"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.result_choice
msgid "Vote"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__website_message_ids
msgid "Website Messages"
msgstr "សារវែបសាយ"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__website_message_ids
msgid "Website communication history"
msgstr "ប្រវត្តិទំនាក់ទំនងវែបសាយ"

#. module: survey
#: model:survey.question,question:survey.feedback_2_7
msgid "What do you think about configuration wizards?"
msgstr ""

#. module: survey
#: model:survey.question,question:survey.feedback_2_1
msgid "What do you think about the documentation available on doc.odoo.com?"
msgstr ""

#. module: survey
#: model:survey.question,question:survey.feedback_2_5
msgid "What do you think about the groups of users?"
msgstr ""

#. module: survey
#: model:survey.question,question:survey.feedback_2_2
msgid ""
"What do you think about the process views of Odoo, available in the web "
"client ?"
msgstr ""

#. module: survey
#: model:survey.question,question:survey.feedback_2_4
#: model:survey.question,question:survey.feedback_2_6
msgid "What do you think about the structure of the menus?"
msgstr ""

#. module: survey
#: model:survey.question,question:survey.feedback_4_1
msgid "Where do you develop your new features?"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_mail_compose_message__is_log
msgid "Whether the message is an internal note (comment mode only)"
msgstr ""

#. module: survey
#: model:survey.question,question:survey.feedback_1_2
msgid "Which modules are you using/testing?"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"YYYY-MM-DD\n"
"                                            <i class=\"fa fa-calendar fa-2x\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_1_1_1
msgid "Yes, I use a version < 7.0"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_1_1_2
msgid "Yes, I use the 7.0 version, installed locally"
msgstr ""

#. module: survey
#: model:survey.label,value:survey.choice_1_1_3
msgid "Yes, I use the online version of Odoo"
msgstr ""

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid ""
"You can create surveys for different purposes: customer opinion, services "
"feedback, recruitment interviews, employee's periodical evaluations, "
"marketing campaigns, etc."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_email_compose_message
msgid ""
"You can share your survey web public link and/or send private invitations to"
" your audience. People can answer once per invitation, and whenever they "
"want with the public web link (in this case, the \"Login Required\" setting "
"must be disabled)."
msgstr ""

#. module: survey
#: code:addons/survey/models/survey.py:315
#, python-format
msgid "You cannot send an invitation for a survey that has no questions."
msgstr ""

#. module: survey
#: code:addons/survey/models/survey.py:318
#, python-format
msgid "You cannot send invitations for closed surveys."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.sfinished
msgid "You scored"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "ans"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.layout
msgid "free website"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.auth_required
msgid "log in"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.page
msgid "of"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.sfinished
msgid "points."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.sfinished
msgid "review your answers"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.403
msgid "this page"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.layout
msgid "with"
msgstr ""
