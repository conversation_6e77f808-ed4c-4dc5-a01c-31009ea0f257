<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record model="ir.cron" id="refresh_pos_cache_cron">
            <field name="name">PoS: refresh cache</field>
            <field name="model_id" ref="model_pos_cache"/>
            <field name="state">code</field>
            <field name="code">model.refresh_all_caches()</field>
            <field name="active" eval="False"/>
            <field name="interval_number">1</field>
            <field name="interval_type">hours</field>
            <field name="numbercall">-1</field>
            <field name="doall" eval="False"/>
        </record>
    </data>
</odoo>
