# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * base_setup
# 
# Translators:
# <PERSON><PERSON><PERSON> <neman<PERSON><PERSON><PERSON><EMAIL>>, 2017
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-11-16 08:08+0000\n"
"PO-Revision-Date: 2017-11-16 08:08+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back here to set up the feature."
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Access Rights"
msgstr "Prava pristupa"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings_module_google_calendar
msgid "Allow the users to synchronize their calendar  with Google Calendar"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings_module_base_import
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Allow users to import data from CSV/XLS/XLSX/ODS files"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,help:base_setup.field_res_config_settings_group_multi_currency
msgid "Allows to work in a multi currency environment"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings_module_voip
msgid "Asterisk (VoIP)"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings_module_google_drive
msgid "Attach Google documents to any record"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Automate inter-company transactions"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Business Documents"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"By default, new users get highest access rights for all installed apps."
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Change Document Template"
msgstr ""

#. module: base_setup
#: code:addons/base_setup/models/res_config_settings.py:100
#, python-format
msgid "Choose Your Document Layout"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Choose your document's header and footer layout"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings_module_pad
msgid "Collaborative Pads"
msgstr "Kolaborativni blokovi"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Common Contact Book"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Companies"
msgstr "Preduzeća"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings_company_id
msgid "Company"
msgstr "Preduzeće"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"Configure company rules to automatically create SO/PO when one of your "
"company sells/buys to another of your company."
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Create and attach Google Drive documents to any record"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings_default_custom_report_footer
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings_report_footer
msgid "Custom Report Footer"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings_default_user_rights
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Default Access Rights"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings_external_report_layout
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Document Template"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Edit Header"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings_default_external_email_server
msgid "External Email Servers"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Extract and analyze Odoo data from Google Spreadsheet"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,help:base_setup.field_res_config_settings_report_footer
msgid "Footer text displayed at the bottom of all reports."
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Format"
msgstr ""

#. module: base_setup
#: model:ir.ui.menu,name:base_setup.menu_config
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "General Settings"
msgstr "Generalna Podešavanja"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Gengo Translations"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Google Calendar"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Google Drive"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings_module_google_spreadsheet
msgid "Google Spreadsheet"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Import & Export"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Integrations"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Inter-Company Transactions"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings_module_auth_ldap
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "LDAP Authentication"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings_module_inter_company_rules
msgid "Manage Inter Company"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings_group_multi_company
msgid "Manage multiple companies"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Manage multiple legal entities with separate accounting"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings_group_multi_currency
msgid "Multi-Currencies"
msgstr "Više valuta"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Multi-company"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "OAuth Authentication"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings_paperformat_id
msgid "Paper format"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Preview Document"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Set custom access rights for new users"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Set the paper format of printed documents"
msgstr ""

#. module: base_setup
#: model:ir.actions.act_window,name:base_setup.action_general_configuration
msgid "Settings"
msgstr "Podešavanja"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings_company_share_partner
msgid "Share partners to all companies"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,help:base_setup.field_res_config_settings_company_share_partner
msgid ""
"Share your partners to all companies defined in your instance.\n"
" * Checked : Partners are visible for every companies, even if a company is defined on the partner.\n"
" * Unchecked : Each company can see only its partner (partners where company is defined). Partners not related to a company are visible for all companies."
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Synchronize your calendar with Google Calendar"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Template"
msgstr "Šablon"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings_module_base_gengo
msgid "Translate Your Website with Gengo"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Translate your website with Gengo"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Use LDAP credentials to log in"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Use external accounts to log in (Google, Facebook, etc.)"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings_module_auth_oauth
msgid "Use external authentication providers (OAuth)"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Use external pads in Odoo Notes"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Users"
msgstr "Korisnici"

#. module: base_setup
#: model:ir.model,name:base_setup.model_res_config_settings
msgid "res.config.settings"
msgstr ""
