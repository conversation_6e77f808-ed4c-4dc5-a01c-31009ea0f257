<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
    <path id="path-1" d="M12 0a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1H1a1 1 0 0 1-1-1V1a1 1 0 0 1 1-1zm0 1.292L7.844 4.11a2 2 0 0 1-2.168.05L1 1.29V8h11V1.292zM11.426 1h-9.67L5.64 3.534a2 2 0 0 0 2.245-.04L11.425 1z"/>
    <filter id="filter-2" width="107.7%" height="122.2%" x="-3.8%" y="-5.6%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.292012675 0"/>
    </filter>
    <path id="path-3" d="M43 0v9H16V0h27zm-1 1H17v7h25V1z"/>
    <filter id="filter-4" width="103.7%" height="122.2%" x="-1.9%" y="-5.6%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.292012675 0"/>
    </filter>
    <linearGradient id="linearGradient-5" x1="50%" x2="50%" y1="0%" y2="100%">
      <stop offset="0%" stop-color="#00A09D"/>
      <stop offset="100%" stop-color="#00E2FF"/>
    </linearGradient>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_group">
      <rect width="82" height="60" class="bg"/>
      <g class="group" transform="translate(12 26)">
        <g class="shape">
          <use fill="#000" filter="url(#filter-2)" xlink:href="#path-1"/>
          <use fill="#FFF" fill-opacity=".78" xlink:href="#path-1"/>
        </g>
        <g class="combined_shape">
          <use fill="#000" filter="url(#filter-4)" xlink:href="#path-3"/>
          <use fill="#FFF" fill-opacity=".78" xlink:href="#path-3"/>
        </g>
        <path fill="url(#linearGradient-5)" d="M57 3.556c0 .643-.245 1.238-.734 1.784-.49.547-1.158.978-2.004 1.295-.847.318-1.767.476-2.762.476-.448 0-.906-.037-1.375-.111a6.978 6.978 0 0 1-2.172.889 7.31 7.31 0 0 1-.672.111h-.023a.262.262 0 0 1-.16-.056.224.224 0 0 1-.09-.145.128.128 0 0 1-.008-.046c0-.016.001-.03.004-.045a.136.136 0 0 1 .016-.041l.02-.035.026-.038.032-.035.035-.035.031-.03c.026-.029.086-.087.18-.175.093-.088.161-.156.203-.204l.176-.202a1.75 1.75 0 0 0 .195-.267c.055-.093.108-.195.16-.306-.646-.333-1.154-.743-1.523-1.229-.37-.486-.555-1.005-.555-1.555 0-.644.245-1.239.734-1.785.49-.546 1.158-.978 2.004-1.295A7.816 7.816 0 0 1 51.5 0c.995 0 1.915.159 2.762.476.846.317 1.514.749 2.004 1.295.49.546.734 1.141.734 1.785zm3 1.558c0 .61-.181 1.182-.543 1.714-.363.531-.86.98-1.493 1.347.051.122.104.234.157.336.054.102.118.2.191.294l.173.221c.04.053.107.128.199.225a5.34 5.34 0 0 1 .271.302.344.344 0 0 1 .027.042l.02.038.015.046.003.05-.007.049a.283.283 0 0 1-.1.168.231.231 0 0 1-.168.053 6.45 6.45 0 0 1-.658-.122 6.444 6.444 0 0 1-2.128-.977 7.745 7.745 0 0 1-1.347.122c-1.382 0-2.586-.336-3.612-1.007a8.104 8.104 0 0 0 3.038-.313 6.782 6.782 0 0 0 2.02-.985c.638-.468 1.128-1.007 1.47-1.618A3.91 3.91 0 0 0 57.865 2c.658.361 1.178.814 1.561 1.359.383.544.574 1.13.574 1.755z" class="comments"/>
      </g>
    </g>
  </g>
</svg>
