<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="phone_blacklist_view_tree" model="ir.ui.view">
        <field name="name">phone.blacklist.view.tree</field>
        <field name="model">phone.blacklist</field>
        <field name="arch" type="xml">
            <tree string="Phone Blacklist">
                <field name="create_date" string="Blacklist Date"/>
                <field name="number"/>
            </tree>
        </field>
    </record>

    <record id="phone_blacklist_view_form" model="ir.ui.view">
        <field name="name">phone.blacklist.view.form</field>
        <field name="model">phone.blacklist</field>
        <field name="arch" type="xml">
            <form string="Phone Blacklist" duplicate="false" edit="false">
                <header>
                    <button name="phone_action_blacklist_remove" string="Unblacklist"
                        type="object" class="oe_highlight" context="{'default_phone': number}"
                        attrs="{'invisible': ['|', ('active', '=', False), ('number', '=', False)]}"/>
                    <button name="action_add" string="Blacklist"
                        type="object" class="oe_highlight"
                        attrs="{'invisible': ['|', ('active', '=', True), ('number', '=', False)]}"/>
                </header>
                <sheet>
                <widget name="web_ribbon" title="Archived" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                    <group>
                        <group>
                            <field name="number"/>
                            <field name="active" readonly="1"/>
                            <br/>
                        </group>
                    </group>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="phone_blacklist_view_search" model="ir.ui.view">
        <field name="name">phone.blacklist.view.search</field>
        <field name="model">phone.blacklist</field>
        <field name="arch" type="xml">
            <search>
                <field name="number"/>
                <filter string="Archived" name="inactive" domain="[('active','=',False)]"/>
            </search>
        </field>
    </record>

    <record id="phone_blacklist_action" model="ir.actions.act_window">
        <field name="name">Blacklisted Phone Numbers</field>
        <field name="res_model">phone.blacklist</field>
        <field name="view_id" ref="phone_blacklist_view_tree"/>
        <field name="search_view_id" ref="phone_blacklist_view_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Add a phone number in the blacklist
            </p><p>
                Blacklisted phone numbers won't receive SMS Mailings anymore.
            </p>
        </field>
    </record>

    <!-- Technical Menu -->
    <menuitem id="phone_menu_main"
        name="Phone / SMS"
        parent="base.menu_custom"
        sequence="3"/>

    <menuitem id="phone_blacklist_menu"
        name="Phone Blacklist"
        parent="phone_validation.phone_menu_main"
        sequence="3"
        action="phone_blacklist_action"/>

</odoo>
