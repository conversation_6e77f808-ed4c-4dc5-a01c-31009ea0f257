
.o_field_selector {
    position: relative;

    > .o_field_selector_value {
        display: flex;
        flex-flow: row wrap;
        align-items: center;
        height: 100%;
        min-height: 20px; // needed when there is no value in it and used standalone
        &:active, &:focus, &:active:focus {
            outline: none;
        }

        > .o_field_selector_chain_part {
            padding: 0px 1px;
            border: 1px solid darken($o-brand-lightsecondary, 10%);
            background: $o-brand-lightsecondary;
            margin-bottom: 1px;
        }
        > i {
            align-self: center;
            margin: 0 2px;
            font-size: 10px;
        }
    }
    > .o_field_selector_controls {
        @include o-position-absolute(0, 0, 1px);
        display: flex;
        align-items: center;
        cursor: pointer;
    }

    &.o_edit_mode {
        > .o_field_selector_controls::after {
            @include o-caret-down;
        }

        > .o_field_selector_popover {
            $o-field-selector-arrow-height: 7px;
            @include o-position-absolute($top: 100%, $left: 0);
            z-index: 1051;
            width: 265px;
            margin-top: $o-field-selector-arrow-height;
            background: white;
            box-shadow: 0 3px 10px rgba(0,0,0,.4);

            &:focus {
                outline: none;
            }

            .o_field_selector_popover_header {
                color: white;
                background: theme-color('primary');
                font-weight: bold;
                padding: 5px 0 5px 0.4em;

                .o_field_selector_title {
                    width: 100%;
                    @include o-text-overflow;
                    padding: 0px 35px;
                    text-align: center;
                }
                .o_field_selector_search {
                    padding-right: 0.4rem;
                    > .o_input {
                        padding: 5px 0.4rem;
                    }
                }
                .o_field_selector_popover_option {
                    @include o-position-absolute($top: 0);
                    padding: 8px;

                    &.o_field_selector_prev_page {
                        left: 0;
                    }
                    &.o_field_selector_close {
                        right: 0;
                    }
                    &:hover {
                        background: darken(theme-color('primary'), 10%);
                    }
                }
                &:before {
                    @include o-position-absolute($top: -$o-field-selector-arrow-height, $left: $o-field-selector-arrow-height);
                    content: "";
                    border-left: $o-field-selector-arrow-height solid rgba(0, 0, 0, 0);
                    border-right: $o-field-selector-arrow-height solid rgba(0, 0, 0, 0);
                    border-bottom: $o-field-selector-arrow-height solid theme-color('primary');
                }
            }
            .o_field_selector_popover_body {
                .o_field_selector_page {
                    position: relative;
                    max-height: 320px;
                    overflow: auto;
                    margin: 0;
                    padding: 0;

                    > .o_field_selector_item {
                        list-style: none;
                        position: relative;
                        padding: 5px 0 5px 0.4em;
                        cursor: pointer;
                        font-family: Arial;
                        font-size: 13px;
                        color: #444;
                        border-bottom: 1px solid #eee;
                        &.active {
                            background: #f5f5f5;
                        }
                        .o_field_selector_item_title {
                            font-size: 12px;
                        }
                        .o_field_selector_relation_icon {
                            @include o-position-absolute($top: 0, $right: 0, $bottom: 0);
                            display: flex;
                            align-items: center;
                            padding: 10px;
                        }
                    }
                }
            }
            .o_field_selector_popover_footer {
                background: theme-color('primary');
                padding: 5px 0.4em;

                > input {
                    width: 100%;
                }
            }
        }
    }
}
