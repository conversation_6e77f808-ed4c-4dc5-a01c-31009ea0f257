<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="TextAreaPopup" owl="1">
        <div role="dialog" class="modal-dialog">
            <Draggable>
                <div class="popup popup-textarea">
                    <header class="title drag-handle">
                        <t t-esc="props.title" />
                    </header>
                    <textarea t-model="state.inputValue" t-ref="input"></textarea>
                    <footer class="footer">
                        <div class="button confirm" t-on-click="confirm">
                            <t t-esc="props.confirmText" />
                        </div>
                        <div class="button cancel" t-on-click="cancel">
                            <t t-esc="props.cancelText" />
                        </div>
                    </footer>
                </div>
            </Draggable>
        </div>
    </t>

</templates>
