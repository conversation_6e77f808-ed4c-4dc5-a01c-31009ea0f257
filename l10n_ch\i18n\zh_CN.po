# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_ch
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# 宇洛 李, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2023
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-11-24 13:58+0000\n"
"PO-Revision-Date: 2023-11-24 13:58+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: l10n_ch
#: model:ir.actions.report,print_report_name:l10n_ch.l10n_ch_isr_report
msgid "'ISR-%s' % object.name"
msgstr ""

#. module: l10n_ch
#: model:ir.actions.report,print_report_name:l10n_ch.l10n_ch_qr_report
msgid "'QR-bill-%s' % object.name"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_other_movements_910
#: model:account.tax.template,name:l10n_ch.vat_other_movements_910
msgid "0% - Donations, dividends, compensation"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_other_movements_900
#: model:account.tax.template,name:l10n_ch.vat_other_movements_900
msgid "0% - Subsidies, tourist taxes"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_O_exclude
#: model:account.tax.template,name:l10n_ch.vat_O_exclude
msgid "0% Excluded"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_XO
#: model:account.tax.template,name:l10n_ch.vat_XO
msgid "0% Export"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_O_import
#: model:account.tax.template,name:l10n_ch.vat_O_import
msgid "0% Import"
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_other_movements_910
#: model:account.tax.template,description:l10n_ch.vat_other_movements_910
msgid "0% dons"
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_O_exclude
#: model:account.tax.template,description:l10n_ch.vat_O_exclude
msgid "0% excl."
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_O_import
#: model:account.tax.template,description:l10n_ch.vat_O_import
msgid "0% import."
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_other_movements_900
#: model:account.tax.template,description:l10n_ch.vat_other_movements_900
msgid "0% subventions"
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_100_import
#: model:account.tax.template,description:l10n_ch.vat_100_import
msgid "100% imp."
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_100_import_invest
#: model:account.tax.template,description:l10n_ch.vat_100_import_invest
msgid "100% imp.invest."
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_25_incl
#: model:account.tax.template,description:l10n_ch.vat_25_incl
msgid "2.5% Incl."
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_25
#: model:account.tax.template,name:l10n_ch.vat_25
msgid "2.5% Sales"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_25_incl
#: model:account.tax.template,name:l10n_ch.vat_25_incl
msgid "2.5% Sales (incl.)"
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_25_invest
#: model:account.tax.template,description:l10n_ch.vat_25_invest
msgid "2.5% invest."
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_25_invest_incl
#: model:account.tax.template,description:l10n_ch.vat_25_invest_incl
msgid "2.5% invest. Incl."
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_25_purchase
#: model:account.tax.template,name:l10n_ch.vat_25_purchase
msgid "2.5% on goods and services"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_25_purchase_incl
#: model:account.tax.template,name:l10n_ch.vat_25_purchase_incl
msgid "2.5% on goods and services (incl.)"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_25_invest
#: model:account.tax.template,name:l10n_ch.vat_25_invest
msgid "2.5% on invest. and others expenses"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_25_invest_incl
#: model:account.tax.template,name:l10n_ch.vat_25_invest_incl
msgid "2.5% on invest. and others expenses (incl.)"
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_25_purchase
#: model:account.tax.template,description:l10n_ch.vat_25_purchase
msgid "2.5% purch."
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_25_purchase_incl
#: model:account.tax.template,description:l10n_ch.vat_25_purchase_incl
msgid "2.5% purch. Incl."
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_25
#: model:account.tax.template,description:l10n_ch.vat_25
msgid "2.50%"
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_sale_26
#: model:account.tax.template,description:l10n_ch.vat_sale_26
msgid "2.6%"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_sale_26
#: model:account.tax.template,name:l10n_ch.vat_sale_26
msgid "2.6% Sales"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_sale_26_incl
#: model:account.tax.template,name:l10n_ch.vat_sale_26_incl
msgid "2.6% Sales (incl.)"
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_sale_26_incl
#: model:account.tax.template,description:l10n_ch.vat_sale_26_incl
msgid "2.6% incl."
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_purchase_26_invest
#: model:account.tax.template,description:l10n_ch.vat_purchase_26_invest
msgid "2.6% invest."
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_purchase_26_invest_incl
#: model:account.tax.template,description:l10n_ch.vat_purchase_26_invest_incl
msgid "2.6% invest. Incl."
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_purchase_26
#: model:account.tax.template,name:l10n_ch.vat_purchase_26
msgid "2.6% on goods and services"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_purchase_26_incl
#: model:account.tax.template,name:l10n_ch.vat_purchase_26_incl
msgid "2.6% on goods and services (incl.)"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_purchase_26_invest
#: model:account.tax.template,name:l10n_ch.vat_purchase_26_invest
msgid "2.6% on invest. and others expenses"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_purchase_26_invest_incl
#: model:account.tax.template,name:l10n_ch.vat_purchase_26_invest_incl
msgid "2.6% on invest. and others expenses (incl.)"
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_purchase_26
#: model:account.tax.template,description:l10n_ch.vat_purchase_26
msgid "2.6% purch."
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_purchase_26_incl
#: model:account.tax.template,description:l10n_ch.vat_purchase_26_incl
msgid "2.6% purch. Incl."
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_200
msgid ""
"200 - Total amount of agreed or collected consideration incl. from supplies "
"opted for taxation, transfer of supplies acc. to the notification procedure "
"and supplies provided abroad (worldwide turnover)"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,tag_name:l10n_ch.account_tax_report_line_chtax_205
msgid "205"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_205
msgid ""
"205 - Consideration reported in Ref. 200 from supplies exempt from the tax "
"without credit (art. 21) where the option for their taxation according to "
"art. 22 has been exercised"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,tag_name:l10n_ch.account_tax_report_line_chtax_220_289
msgid "220"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_220_289
msgid ""
"220 - Supplies exempt from the tax (e.g. export, art. 23) and supplies "
"provided to institutional and individual beneficiaries that are exempt from "
"liability for tax (art. 107 para. 1 lit. a)"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,tag_name:l10n_ch.account_tax_report_line_chtax_221
msgid "221"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_221
msgid "221 - Supplies provided abroad"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,tag_name:l10n_ch.account_tax_report_line_chtax_225
msgid "225"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_225
msgid ""
"225 - Transfer of supplies according to the notification procedure (art. 38,"
" please submit Form 764)"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,tag_name:l10n_ch.account_tax_report_line_chtax_230
msgid "230"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_230
msgid ""
"230 - Supplies provided on Swiss territory exempt from the tax without "
"credit (art. 21) and where the option for their taxation according to art. "
"22 has not been exercised"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,tag_name:l10n_ch.account_tax_report_line_chtax_235
msgid "235"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_235
msgid "235 - Reduction of consideration (discounts, rebates etc.)"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,tag_name:l10n_ch.account_tax_report_line_chtax_280
msgid "280"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_280
msgid ""
"280 - Miscellaneous (e.g. land value, purchase prices in case of margin "
"taxation)"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_289
msgid "289 - Deductions (Total Ref. 220 to 280)"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_299
msgid "299 - Taxable turnover (Ref. 200 minus Ref. 289)"
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_37_incl
#: model:account.tax.template,description:l10n_ch.vat_37_incl
msgid "3.7% Incl."
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_37
#: model:account.tax.template,name:l10n_ch.vat_37
msgid "3.7% Sales"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_37_incl
#: model:account.tax.template,name:l10n_ch.vat_37_incl
msgid "3.7% Sales (incl.)"
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_37_invest
#: model:account.tax.template,description:l10n_ch.vat_37_invest
msgid "3.7% invest"
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_37_invest_incl
#: model:account.tax.template,description:l10n_ch.vat_37_invest_incl
msgid "3.7% invest Incl."
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_37_purchase
#: model:account.tax.template,name:l10n_ch.vat_37_purchase
msgid "3.7% on goods and services"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_37_purchase_incl
#: model:account.tax.template,name:l10n_ch.vat_37_purchase_incl
msgid "3.7% on goods and services (incl.)"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_37_invest
#: model:account.tax.template,name:l10n_ch.vat_37_invest
msgid "3.7% on invest. and others expenses"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_37_invest_incl
#: model:account.tax.template,name:l10n_ch.vat_37_invest_incl
msgid "3.7% on invest. and others expenses (incl.)"
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_37_purchase
#: model:account.tax.template,description:l10n_ch.vat_37_purchase
msgid "3.7% purch."
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_37_purchase_incl
#: model:account.tax.template,description:l10n_ch.vat_37_purchase_incl
msgid "3.7% purch. Incl."
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_37
#: model:account.tax.template,description:l10n_ch.vat_37
msgid "3.70%%"
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_sale_38
#: model:account.tax.template,description:l10n_ch.vat_sale_38
msgid "3.8%"
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_sale_38_incl
#: model:account.tax.template,description:l10n_ch.vat_sale_38_incl
msgid "3.8% Incl."
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_sale_38
#: model:account.tax.template,name:l10n_ch.vat_sale_38
msgid "3.8% Sales"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_sale_38_incl
#: model:account.tax.template,name:l10n_ch.vat_sale_38_incl
msgid "3.8% Sales (incl.)"
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_purchase_38_invest
#: model:account.tax.template,description:l10n_ch.vat_purchase_38_invest
msgid "3.8% invest"
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_purchase_38_invest_incl
#: model:account.tax.template,description:l10n_ch.vat_purchase_38_invest_incl
msgid "3.8% invest Incl."
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_purchase_38
#: model:account.tax.template,name:l10n_ch.vat_purchase_38
msgid "3.8% on goods and services"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_purchase_38_incl
#: model:account.tax.template,name:l10n_ch.vat_purchase_38_incl
msgid "3.8% on goods and services (incl.)"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_purchase_38_invest
#: model:account.tax.template,name:l10n_ch.vat_purchase_38_invest
msgid "3.8% on invest. and others expenses"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_purchase_38_invest_incl
#: model:account.tax.template,name:l10n_ch.vat_purchase_38_invest_incl
msgid "3.8% on invest. and others expenses (incl.)"
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_purchase_38
#: model:account.tax.template,description:l10n_ch.vat_purchase_38
msgid "3.8% purch."
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_purchase_38_incl
#: model:account.tax.template,description:l10n_ch.vat_purchase_38_incl
msgid "3.8% purch. Incl."
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,tag_name:l10n_ch.account_tax_report_line_chtax_302a
msgid "302a"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_302a
msgid "302a - Standard rate (7,7%): Supplies CHF to 31.12.2023"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,tag_name:l10n_ch.account_tax_report_line_chtax_302b
msgid "302b"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_302b
msgid "302b - Standard rate (7,7%): Tax amount CHF / cent. to 31.12.2023"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,tag_name:l10n_ch.account_tax_report_line_chtax_303a
msgid "303a"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_303a
msgid "303a - Standard rate (8,1%): Supplies CHF from 01.01.2024"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,tag_name:l10n_ch.account_tax_report_line_chtax_303b
msgid "303b"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_303b
msgid "303b - Standard rate (8,1%): Tax amount CHF / cent. from 01.01.2024"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,tag_name:l10n_ch.account_tax_report_line_chtax_312a
msgid "312a"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_312a
msgid "312a - Reduced rate (2,5%): Supplies CHF to 31.12.2023"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,tag_name:l10n_ch.account_tax_report_line_chtax_312b
msgid "312b"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_312b
msgid "312b - Reduced rate (2,5%): Tax amount CHF / cent. to 31.12.2023"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,tag_name:l10n_ch.account_tax_report_line_chtax_313a
msgid "313a"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_313a
msgid "313a - Reduced rate (2,6%): Supplies CHF from 01.01.2024"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,tag_name:l10n_ch.account_tax_report_line_chtax_313b
msgid "313b"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_313b
msgid "313b - Reduced rate (2,6%): Tax amount CHF / cent. from 01.01.2024"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,tag_name:l10n_ch.account_tax_report_line_chtax_342a
msgid "342a"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_342a
msgid "342a - Accommodation rate (3,7%): Supplies CHF to 31.12.2023"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,tag_name:l10n_ch.account_tax_report_line_chtax_342b
msgid "342b"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_342b
msgid "342b - Accommodation rate (3,7%): Tax amount CHF / cent. to 31.12.2023"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,tag_name:l10n_ch.account_tax_report_line_chtax_343a
msgid "343a"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_343a
msgid "343a - Accommodation rate (3,8%): Supplies CHF from 01.01.2024"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,tag_name:l10n_ch.account_tax_report_line_chtax_343b
msgid "343b"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_343b
msgid ""
"343b - Accommodation rate (3,8%): Tax amount CHF / cent. from 01.01.2024"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,tag_name:l10n_ch.account_tax_report_line_chtax_382a
msgid "382a"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_382a
msgid "382a - Acquisition tax: Supplies CHF to 31.12.2023"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,tag_name:l10n_ch.account_tax_report_line_chtax_382b
msgid "382b"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_382b
msgid "382b - Acquisition tax: Tax amount CHF / cent. to 31.12.2023"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,tag_name:l10n_ch.account_tax_report_line_chtax_383a
msgid "383a"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_383a
msgid "383a - Acquisition tax: Supplies CHF from 01.01.2024"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,tag_name:l10n_ch.account_tax_report_line_chtax_383b
msgid "383b"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_383b
msgid "383b - Acquisition tax: Tax amount CHF / cent. from 01.01.2024"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_399
msgid "399 - Total amount of tax due"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,tag_name:l10n_ch.account_tax_report_line_chtax_400
msgid "400"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_400
msgid "400 - Input tax on cost of materials and supplies of services"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,tag_name:l10n_ch.account_tax_report_line_chtax_405
msgid "405"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_405
msgid "405 - Input tax on investments and other operating costs"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,tag_name:l10n_ch.account_tax_report_line_chtax_410
msgid "410"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_410
msgid "410 - De-taxation (art. 32)"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,tag_name:l10n_ch.account_tax_report_line_chtax_415
msgid "415"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_415
msgid ""
"415 - Correction of the input tax deduction: mixed use (art. 30), own use "
"(art. 31)"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,tag_name:l10n_ch.account_tax_report_line_chtax_420
msgid "420"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_420
msgid ""
"420 - Reduction of the input tax deduction: Flow of funds, which are not "
"deemed to be consideration, such as subsidies, tourist charges (art. 33 "
"para. 2)"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_479
msgid "479 - Total Ref. 400 to 420"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_500
msgid "500 - Amount payable"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_510
msgid "510 - Credit in favour of the taxable person"
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_77_incl
#: model:account.tax.template,description:l10n_ch.vat_77_incl
msgid "7.7% Incl."
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_77_purchase_return
#: model:account.tax.template,name:l10n_ch.vat_77_purchase_return
msgid "7.7% Purchase (reverse)"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_77
#: model:account.tax.template,name:l10n_ch.vat_77
msgid "7.7% Sales"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_77_incl
#: model:account.tax.template,name:l10n_ch.vat_77_incl
msgid "7.7% Sales (incl.)"
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_77_invest
#: model:account.tax.template,description:l10n_ch.vat_77_invest
msgid "7.7% invest."
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_77_invest_incl
#: model:account.tax.template,description:l10n_ch.vat_77_invest_incl
msgid "7.7% invest. Incl."
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_77_purchase
#: model:account.tax.template,name:l10n_ch.vat_77_purchase
msgid "7.7% on goods and services"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_77_purchase_incl
#: model:account.tax.template,name:l10n_ch.vat_77_purchase_incl
msgid "7.7% on goods and services (incl.)"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_77_invest
#: model:account.tax.template,name:l10n_ch.vat_77_invest
msgid "7.7% on invest. and others expenses"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_77_invest_incl
#: model:account.tax.template,name:l10n_ch.vat_77_invest_incl
msgid "7.7% on invest. and others expenses (incl.)"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_77_purchase_reverse
#: model:account.tax.template,name:l10n_ch.vat_77_purchase_reverse
msgid "7.7% on purchase of service abroad (reverse charge)"
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_77_purchase
#: model:account.tax.template,description:l10n_ch.vat_77_purchase
msgid "7.7% purch."
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_77_purchase_return
#: model:account.tax.template,description:l10n_ch.vat_77_purchase_return
msgid "7.7% purch. (reverse)"
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_77_purchase_incl
#: model:account.tax.template,description:l10n_ch.vat_77_purchase_incl
msgid "7.7% purch. Incl."
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_77_purchase_reverse
#: model:account.tax.template,description:l10n_ch.vat_77_purchase_reverse
msgid "7.7% rev."
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_77
#: model:account.tax.template,description:l10n_ch.vat_77
msgid "7.70%"
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_sale_81
#: model:account.tax.template,description:l10n_ch.vat_sale_81
msgid "8.1%"
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_sale_81_incl
#: model:account.tax.template,description:l10n_ch.vat_sale_81_incl
msgid "8.1% Incl."
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_purchase_81_return
#: model:account.tax.template,name:l10n_ch.vat_purchase_81_return
msgid "8.1% Purchase (reverse)"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_sale_81
#: model:account.tax.template,name:l10n_ch.vat_sale_81
msgid "8.1% Sales"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_sale_81_incl
#: model:account.tax.template,name:l10n_ch.vat_sale_81_incl
msgid "8.1% Sales (incl.)"
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_purchase_81_invest
#: model:account.tax.template,description:l10n_ch.vat_purchase_81_invest
msgid "8.1% invest."
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_purchase_81_invest_incl
#: model:account.tax.template,description:l10n_ch.vat_purchase_81_invest_incl
msgid "8.1% invest. Incl."
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_purchase_81
#: model:account.tax.template,name:l10n_ch.vat_purchase_81
msgid "8.1% on goods and services"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_purchase_81_incl
#: model:account.tax.template,name:l10n_ch.vat_purchase_81_incl
msgid "8.1% on goods and services (incl.)"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_purchase_81_invest
#: model:account.tax.template,name:l10n_ch.vat_purchase_81_invest
msgid "8.1% on invest. and others expenses"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_purchase_81_invest_incl
#: model:account.tax.template,name:l10n_ch.vat_purchase_81_invest_incl
msgid "8.1% on invest. and others expenses (incl.)"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_purchase_81_reverse
#: model:account.tax.template,name:l10n_ch.vat_purchase_81_reverse
msgid "8.1% on purchase of service abroad (reverse charge)"
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_purchase_81
#: model:account.tax.template,description:l10n_ch.vat_purchase_81
msgid "8.1% purch."
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_purchase_81_return
#: model:account.tax.template,description:l10n_ch.vat_purchase_81_return
msgid "8.1% purch. (reverse)"
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_purchase_81_incl
#: model:account.tax.template,description:l10n_ch.vat_purchase_81_incl
msgid "8.1% purch. Incl."
msgstr ""

#. module: l10n_ch
#: model:account.tax,description:l10n_ch.1_vat_purchase_81_reverse
#: model:account.tax.template,description:l10n_ch.vat_purchase_81_reverse
msgid "8.1% rev"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,tag_name:l10n_ch.account_tax_report_line_chtax_900
msgid "900"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_900
msgid ""
"900 - Subsidies, tourist funds collected by tourist offices, contributions "
"from cantonal water, sewage or waste funds (art. 18 para. 2 lit. a to c)"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,tag_name:l10n_ch.account_tax_report_line_chtax_910
msgid "910"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_910
msgid ""
"910 - Donations, dividends, payments of damages etc. (art. 18 para. 2 lit. d"
" to l)"
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.res_config_settings_view_form
msgid "<span class=\"o_form_label\">ISR scan line offset</span>"
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.l10n_ch_swissqr_template
msgid "<span class=\"title\">Acceptance point</span>"
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.l10n_ch_swissqr_template
msgid "<span class=\"title\">Reference</span>"
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.l10n_ch_swissqr_template
msgid "<span>Account / Payable to</span>"
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.l10n_ch_swissqr_template
msgid "<span>Account / Payable to</span><br/>"
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.l10n_ch_swissqr_template
msgid "<span>Additional information</span>"
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.l10n_ch_swissqr_template
msgid "<span>Amount</span>"
msgstr "<span>金额</span>"

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.l10n_ch_swissqr_template
msgid "<span>Amount</span><br/>"
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.l10n_ch_swissqr_template
msgid "<span>Currency</span>"
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.l10n_ch_swissqr_template
msgid "<span>Payable by</span>"
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.l10n_ch_swissqr_template
msgid "<span>Payment part</span>"
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.l10n_ch_swissqr_template
msgid "<span>Receipt</span>"
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.l10n_ch_swissqr_template
msgid "<span>Reference</span>"
msgstr "<span>编号</span>"

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_2000
#: model:account.account.template,name:l10n_ch.ch_coa_2000
msgid "Accounts payable from goods and services (Creditors)"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1100
#: model:account.account.template,name:l10n_ch.ch_coa_1100
msgid "Accounts receivable from goods and services (Debtors)"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1300
#: model:account.account.template,name:l10n_ch.ch_coa_1300
msgid "Accrued revenue and deferred expense (Accounts paid in advance)"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1579
#: model:account.account.template,name:l10n_ch.ch_coa_1579
msgid "Accumulated depreciation on Equipments and Facilities"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1267
#: model:account.account.template,name:l10n_ch.ch_coa_1267
msgid "Accumulated depreciation on Finished products"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1207
#: model:account.account.template,name:l10n_ch.ch_coa_1207
msgid "Accumulated depreciation on Goods / Merchandise (Trade)"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1599
#: model:account.account.template,name:l10n_ch.ch_coa_1599
msgid "Accumulated depreciation on Other movable tangible assets"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1709
#: model:account.account.template,name:l10n_ch.ch_coa_1709
msgid "Accumulated depreciation on Patents, Licences"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1277
#: model:account.account.template,name:l10n_ch.ch_coa_1277
msgid "Accumulated depreciation on Products in process / Unfinished products"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1519
#: model:account.account.template,name:l10n_ch.ch_coa_1519
msgid "Accumulated depreciation on equipment"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1779
#: model:account.account.template,name:l10n_ch.ch_coa_1779
msgid "Accumulated depreciation on goodwill"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1449
#: model:account.account.template,name:l10n_ch.ch_coa_1449
msgid "Accumulated depreciation on long term receivables"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1409
#: model:account.account.template,name:l10n_ch.ch_coa_1409
msgid "Accumulated depreciation on long-term securities"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1509
#: model:account.account.template,name:l10n_ch.ch_coa_1509
msgid "Accumulated depreciation on machinery"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1529
#: model:account.account.template,name:l10n_ch.ch_coa_1529
msgid "Accumulated depreciation on office equipment (incl. ICT)"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1489
#: model:account.account.template,name:l10n_ch.ch_coa_1489
msgid "Accumulated depreciation on participations"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1217
#: model:account.account.template,name:l10n_ch.ch_coa_1217
msgid "Accumulated depreciation on raw material"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1609
#: model:account.account.template,name:l10n_ch.ch_coa_1609
msgid "Accumulated depreciation on real estate"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1069
#: model:account.account.template,name:l10n_ch.ch_coa_1069
msgid "Accumulated depreciation on securities"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1199
#: model:account.account.template,name:l10n_ch.ch_coa_1199
msgid "Accumulated depreciation on short-terms receivables"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1549
#: model:account.account.template,name:l10n_ch.ch_coa_1549
msgid "Accumulated depreciation on tools"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1539
#: model:account.account.template,name:l10n_ch.ch_coa_1539
msgid "Accumulated depreciation on vehicles"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1559
#: model:account.account.template,name:l10n_ch.ch_coa_1559
msgid "Accumulated depreciation on warehouse"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1287
#: model:account.account.template,name:l10n_ch.ch_coa_1287
msgid "Accumulated depreciation on work in progress"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_6500
#: model:account.account.template,name:l10n_ch.ch_coa_6500
msgid "Administration expenses"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1140
#: model:account.account.template,name:l10n_ch.ch_coa_1140
msgid "Advances and loans"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1149
#: model:account.account.template,name:l10n_ch.ch_coa_1149
msgid "Advances and loans adjustments"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_2979
#: model:account.account.template,name:l10n_ch.ch_coa_2979
msgid "Annual profit or annual loss"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1220
#: model:account.account.template,name:l10n_ch.ch_coa_1220
msgid "Auxiliary material"
msgstr ""

#. module: l10n_ch
#: model:ir.model,name:l10n_ch.model_res_partner_bank
msgid "Bank Accounts"
msgstr "银行帐户"

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_2100
#: model:account.account.template,name:l10n_ch.ch_coa_2100
msgid "Bank Overdraft (Bank)"
msgstr ""

#. module: l10n_ch
#: model:ir.model,name:l10n_ch.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "银行对账单明细"

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_2400
#: model:account.account.template,name:l10n_ch.ch_coa_2400
msgid "Bank debts"
msgstr ""

#. module: l10n_ch
#: model:ir.model,name:l10n_ch.model_account_setup_bank_manual_config
msgid "Bank setup manual config"
msgstr "银行设置手动配置"

#. module: l10n_ch
#: model:ir.model.fields,help:l10n_ch.field_res_company__l10n_ch_isr_print_bank_location
#: model:ir.model.fields,help:l10n_ch.field_res_config_settings__l10n_ch_isr_print_bank_location
msgid ""
"Boolean option field indicating whether or not the alternate layout (the one"
" printing bank name and address) must be used when generating an ISR."
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,help:l10n_ch.field_account_bank_statement_line__l10n_ch_isr_sent
#: model:ir.model.fields,help:l10n_ch.field_account_move__l10n_ch_isr_sent
#: model:ir.model.fields,help:l10n_ch.field_account_payment__l10n_ch_isr_sent
msgid ""
"Boolean value telling whether or not the ISR corresponding to this invoice "
"has already been printed or sent by mail."
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,help:l10n_ch.field_account_bank_statement_line__l10n_ch_isr_valid
#: model:ir.model.fields,help:l10n_ch.field_account_move__l10n_ch_isr_valid
#: model:ir.model.fields,help:l10n_ch.field_account_payment__l10n_ch_isr_valid
msgid ""
"Boolean value. True iff all the data required to generate the ISR are "
"present"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_account_setup_bank_manual_config__l10n_ch_isr_subscription_chf
#: model:ir.model.fields,field_description:l10n_ch.field_res_partner_bank__l10n_ch_isr_subscription_chf
msgid "CHF ISR Subscription Number"
msgstr ""

#. module: l10n_ch
#: code:addons/l10n_ch/models/account_invoice.py:0
#, python-format
msgid ""
"Cannot generate the QR-bill. Please check you have configured the address of"
" your company and debtor. If you are using a QR-IBAN, also check the "
"invoice's payment reference is a QR reference."
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_4992
#: model:account.account.template,name:l10n_ch.ch_coa_4992
msgid "Cash Difference Gain"
msgstr "现金差价收益"

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_4991
#: model:account.account.template,name:l10n_ch.ch_coa_4991
msgid "Cash Difference Loss"
msgstr "现金差额损失"

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_3901
#: model:account.account.template,name:l10n_ch.ch_coa_3901
msgid "Change in inventories of finished goods"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_4800
#: model:account.account.template,name:l10n_ch.ch_coa_4800
msgid "Change in inventories of goods"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_4801
#: model:account.account.template,name:l10n_ch.ch_coa_4801
msgid "Change in raw material inventories"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_3940
#: model:account.account.template,name:l10n_ch.ch_coa_3940
msgid "Change in the value of unbilled services"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_3900
#: model:account.account.template,name:l10n_ch.ch_coa_3900
msgid "Changes in inventories of unfinished and finished products"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_4521
#: model:account.account.template,name:l10n_ch.ch_coa_4521
msgid "Coal, briquettes, wood"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_3804
#: model:account.account.template,name:l10n_ch.ch_coa_3804
msgid "Collection fees"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_4903
#: model:account.account.template,name:l10n_ch.ch_coa_4903
msgid "Commissions on purchases"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_account_bank_statement_import_journal_creation__invoice_reference_model
#: model:ir.model.fields,field_description:l10n_ch.field_account_journal__invoice_reference_model
msgid "Communication Standard"
msgstr "通信标准"

#. module: l10n_ch
#: model:ir.model,name:l10n_ch.model_res_company
msgid "Companies"
msgstr "公司"

#. module: l10n_ch
#: model:ir.model,name:l10n_ch.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1250
#: model:account.account.template,name:l10n_ch.ch_coa_1250
msgid "Consignments Goods "
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1230
#: model:account.account.template,name:l10n_ch.ch_coa_1230
msgid "Consumables"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1269
#: model:account.account.template,name:l10n_ch.ch_coa_1269
msgid "Correction on Finished products"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1209
#: model:account.account.template,name:l10n_ch.ch_coa_1209
msgid "Correction on Goods / Merchandise (Trade)"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1279
#: model:account.account.template,name:l10n_ch.ch_coa_1279
msgid "Correction on Products in process / Unfinished products"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1219
#: model:account.account.template,name:l10n_ch.ch_coa_1219
msgid "Correction on raw material"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1289
#: model:account.account.template,name:l10n_ch.ch_coa_1289
msgid "Correction on work in progress"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_4200
#: model:account.account.template,name:l10n_ch.ch_coa_4200
msgid "Cost of materials (Trade)"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_4400
#: model:account.account.template,name:l10n_ch.ch_coa_4400
msgid "Cost of purchased services"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_4000
#: model:account.account.template,name:l10n_ch.ch_coa_4000
msgid "Cost of raw materials (Manufacturing)"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_account_bank_statement_line__l10n_ch_currency_name
#: model:ir.model.fields,field_description:l10n_ch.field_account_move__l10n_ch_currency_name
#: model:ir.model.fields,field_description:l10n_ch.field_account_payment__l10n_ch_currency_name
msgid "Currency Name"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_100_import
#: model:account.tax.template,name:l10n_ch.vat_100_import
msgid "Customs VAT on goods and services"
msgstr ""

#. module: l10n_ch
#: model:account.tax,name:l10n_ch.1_vat_100_import_invest
#: model:account.tax.template,name:l10n_ch.vat_100_import_invest
msgid "Customs VAT on invest. and others expenses"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_4071
#: model:account.account.template,name:l10n_ch.ch_coa_4071
msgid "Customs duties on importation"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_2430
#: model:account.account.template,name:l10n_ch.ch_coa_2430
msgid "Debentures"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_4009
#: model:account.account.template,name:l10n_ch.ch_coa_4009
msgid "Deductions obtained on purchases"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_3009
#: model:account.account.template,name:l10n_ch.ch_coa_3009
msgid "Deductions on sales"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1301
#: model:account.account.template,name:l10n_ch.ch_coa_1301
msgid "Deferred expense (Accounts paid in advance)"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_2301
#: model:account.account.template,name:l10n_ch.ch_coa_2301
msgid "Deferred revenue (Accounts Received in Advance)"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_2300
#: model:account.account.template,name:l10n_ch.ch_coa_2300
msgid "Deferred revenue and accrued expenses (Accounts received in advance)"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1109
#: model:account.account.template,name:l10n_ch.ch_coa_1109
msgid "Del credere (Acc. depr. on debtors)"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_6800
#: model:account.account.template,name:l10n_ch.ch_coa_6800
msgid "Depreciations"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_2160
#: model:account.account.template,name:l10n_ch.ch_coa_2160
msgid "Dettes envers l'actionnaire"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_2208
#: model:account.account,name:l10n_ch.1_ch_coa_8900
#: model:account.account.template,name:l10n_ch.ch_coa_2208
#: model:account.account.template,name:l10n_ch.ch_coa_8900
msgid "Direct Taxes"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_3801
#: model:account.account.template,name:l10n_ch.ch_coa_3801
msgid "Discounts and price reduction"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_4901
#: model:account.account.template,name:l10n_ch.ch_coa_4901
msgid "Discounts and price reductions"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_2261
#: model:account.account.template,name:l10n_ch.ch_coa_2261
msgid "Dividend payouts resolved (Dividends)"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1208
#: model:account.account.template,name:l10n_ch.ch_coa_1208
msgid "Downpayment on Goods / Merchandise (Trade)"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1218
#: model:account.account.template,name:l10n_ch.ch_coa_1218
msgid "Downpayment on raw material"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_account_setup_bank_manual_config__l10n_ch_isr_subscription_eur
#: model:ir.model.fields,field_description:l10n_ch.field_res_partner_bank__l10n_ch_isr_subscription_eur
msgid "EUR ISR Subscription Number"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_4500
#: model:account.account.template,name:l10n_ch.ch_coa_4500
msgid "Electricity"
msgstr ""

#. module: l10n_ch
#: model:ir.model,name:l10n_ch.model_mail_template
msgid "Email Templates"
msgstr "EMail模板"

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_6400
#: model:account.account.template,name:l10n_ch.ch_coa_6400
msgid "Energy expenses & disposal expenses"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1510
#: model:account.account.template,name:l10n_ch.ch_coa_1510
msgid "Equipment"
msgstr "设备"

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1570
#: model:account.account.template,name:l10n_ch.ch_coa_1570
msgid "Equipments and Facilities"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_3806
#: model:account.account,name:l10n_ch.1_ch_coa_4906
#: model:account.account.template,name:l10n_ch.ch_coa_3806
#: model:account.account.template,name:l10n_ch.ch_coa_4906
msgid "Exchange rate differences"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_7510
#: model:account.account.template,name:l10n_ch.ch_coa_7510
msgid "Expenses from operational real estate"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_8500
#: model:account.account.template,name:l10n_ch.ch_coa_8500
msgid "Extraordinary expenses"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_8510
#: model:account.account.template,name:l10n_ch.ch_coa_8510
msgid "Extraordinary revenues"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_2420
#: model:account.account.template,name:l10n_ch.ch_coa_2420
msgid "Finance lease commitments"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_4900
#: model:account.account.template,name:l10n_ch.ch_coa_4900
msgid "Financial Discounts"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_3800
#: model:account.account.template,name:l10n_ch.ch_coa_3800
msgid "Financial discount"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_6900
#: model:account.account.template,name:l10n_ch.ch_coa_6900
msgid ""
"Financial expenses (Interest expenses, Securities expenses, Participations "
"expenses)"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_6950
#: model:account.account.template,name:l10n_ch.ch_coa_6950
msgid ""
"Financial revenues (Interest revenues, Securities revenues, Participations "
"revenues)"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1260
#: model:account.account.template,name:l10n_ch.ch_coa_1260
msgid "Finished products"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_4520
#: model:account.account.template,name:l10n_ch.ch_coa_4520
msgid "Fuel oil"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_4510
#: model:account.account.template,name:l10n_ch.ch_coa_4510
msgid "Gas"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1200
#: model:account.account.template,name:l10n_ch.ch_coa_1200
msgid "Goods / Merchandise (Trade)"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1770
#: model:account.account.template,name:l10n_ch.ch_coa_1770
msgid "Goodwill"
msgstr "商誉"

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_res_config_settings__l10n_ch_isr_scan_line_left
msgid "Horizontal offset"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chiffre_af
msgid "I. TURNOVER"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_calc_impot
msgid "II. TAX CALCULATION"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_chtax_autres_mouv
msgid "III. OTHER CASH FLOWS"
msgstr ""

#. module: l10n_ch
#: model:ir.actions.report,name:l10n_ch.l10n_ch_isr_report
msgid "ISR"
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.isr_partner_bank_form
#: model_terms:ir.ui.view,arch_db:l10n_ch.setup_bank_account_wizard_inherit
msgid "ISR Client Identification Number"
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.l10n_ch_isr_report_template
msgid "ISR for invoice"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,help:l10n_ch.field_account_bank_statement_line__l10n_ch_isr_number_spaced
#: model:ir.model.fields,help:l10n_ch.field_account_move__l10n_ch_isr_number_spaced
#: model:ir.model.fields,help:l10n_ch.field_account_payment__l10n_ch_isr_number_spaced
msgid ""
"ISR number split in blocks of 5 characters (right-justified), to generate "
"ISR report."
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.isr_invoice_search_view
msgid "ISR reference number"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,help:l10n_ch.field_account_bank_statement_line__l10n_ch_isr_subscription
#: model:ir.model.fields,help:l10n_ch.field_account_move__l10n_ch_isr_subscription
#: model:ir.model.fields,help:l10n_ch.field_account_payment__l10n_ch_isr_subscription
msgid ""
"ISR subscription number identifying your company or your bank to generate "
"ISR."
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,help:l10n_ch.field_account_bank_statement_line__l10n_ch_isr_subscription_formatted
#: model:ir.model.fields,help:l10n_ch.field_account_move__l10n_ch_isr_subscription_formatted
#: model:ir.model.fields,help:l10n_ch.field_account_payment__l10n_ch_isr_subscription_formatted
msgid ""
"ISR subscription number your company or your bank, formated with '-' and "
"without the padding zeros, to generate ISR report."
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_6570
#: model:account.account.template,name:l10n_ch.ch_coa_6570
msgid "IT leasing"
msgstr ""

#. module: l10n_ch
#: model:account.fiscal.position,name:l10n_ch.1_fiscal_position_template_import
#: model:account.fiscal.position.template,name:l10n_ch.fiscal_position_template_import
msgid "Import/Export"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1171
#: model:account.account.template,name:l10n_ch.ch_coa_1171
msgid "Input Tax (VAT) receivable on investments, other operating expenses"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1170
#: model:account.account.template,name:l10n_ch.ch_coa_1170
msgid "Input Tax (VAT) receivable on material, goods, services, energy"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_6300
#: model:account.account.template,name:l10n_ch.ch_coa_6300
msgid "Insurance premiums"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_4008
#: model:account.account,name:l10n_ch.1_ch_coa_4080
#: model:account.account.template,name:l10n_ch.ch_coa_4008
#: model:account.account.template,name:l10n_ch.ch_coa_4080
msgid "Inventory changes"
msgstr ""

#. module: l10n_ch
#: model:ir.model,name:l10n_ch.model_account_journal
msgid "Journal"
msgstr "日记账"

#. module: l10n_ch
#: model:ir.model,name:l10n_ch.model_account_move
msgid "Journal Entry"
msgstr "会计凭证"

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_account_bank_statement_line__l10n_ch_isr_needs_fixing
#: model:ir.model.fields,field_description:l10n_ch.field_account_move__l10n_ch_isr_needs_fixing
#: model:ir.model.fields,field_description:l10n_ch.field_account_payment__l10n_ch_isr_needs_fixing
msgid "L10N Ch Isr Needs Fixing"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_account_bank_statement_line__l10n_ch_isr_number
#: model:ir.model.fields,field_description:l10n_ch.field_account_move__l10n_ch_isr_number
#: model:ir.model.fields,field_description:l10n_ch.field_account_payment__l10n_ch_isr_number
msgid "L10N Ch Isr Number"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_account_bank_statement_line__l10n_ch_isr_number_spaced
#: model:ir.model.fields,field_description:l10n_ch.field_account_move__l10n_ch_isr_number_spaced
#: model:ir.model.fields,field_description:l10n_ch.field_account_payment__l10n_ch_isr_number_spaced
msgid "L10N Ch Isr Number Spaced"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_account_bank_statement_line__l10n_ch_isr_optical_line
#: model:ir.model.fields,field_description:l10n_ch.field_account_move__l10n_ch_isr_optical_line
#: model:ir.model.fields,field_description:l10n_ch.field_account_payment__l10n_ch_isr_optical_line
msgid "L10N Ch Isr Optical Line"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_account_bank_statement_line__l10n_ch_isr_sent
#: model:ir.model.fields,field_description:l10n_ch.field_account_move__l10n_ch_isr_sent
#: model:ir.model.fields,field_description:l10n_ch.field_account_payment__l10n_ch_isr_sent
msgid "L10N Ch Isr Sent"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_account_bank_statement_line__l10n_ch_isr_subscription
#: model:ir.model.fields,field_description:l10n_ch.field_account_move__l10n_ch_isr_subscription
#: model:ir.model.fields,field_description:l10n_ch.field_account_payment__l10n_ch_isr_subscription
msgid "L10N Ch Isr Subscription"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_account_bank_statement_line__l10n_ch_isr_subscription_formatted
#: model:ir.model.fields,field_description:l10n_ch.field_account_move__l10n_ch_isr_subscription_formatted
#: model:ir.model.fields,field_description:l10n_ch.field_account_payment__l10n_ch_isr_subscription_formatted
msgid "L10N Ch Isr Subscription Formatted"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_account_bank_statement_line__l10n_ch_isr_valid
#: model:ir.model.fields,field_description:l10n_ch.field_account_move__l10n_ch_isr_valid
#: model:ir.model.fields,field_description:l10n_ch.field_account_payment__l10n_ch_isr_valid
msgid "L10N Ch Isr Valid"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_account_setup_bank_manual_config__l10n_ch_show_subscription
#: model:ir.model.fields,field_description:l10n_ch.field_res_partner_bank__l10n_ch_show_subscription
msgid "L10N Ch Show Subscription"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_2120
#: model:account.account.template,name:l10n_ch.ch_coa_2120
msgid "Leasing bondings"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_6105
#: model:account.account.template,name:l10n_ch.ch_coa_6105
msgid "Leasing movable tangible fixed assets"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_2900
#: model:account.account.template,name:l10n_ch.ch_coa_2900
msgid "Legal capital reserves"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_2950
#: model:account.account.template,name:l10n_ch.ch_coa_2950
msgid "Legal retained earnings (Reserves)"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_l10nch_chart_template_liquidity_transfer
#: model:account.account.template,name:l10n_ch.l10nch_chart_template_liquidity_transfer
msgid "Liquidity Transfer"
msgstr "流动性转移"

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1440
#: model:account.account.template,name:l10n_ch.ch_coa_1440
msgid "Loan (Asset)"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_2450
#: model:account.account.template,name:l10n_ch.ch_coa_2450
msgid "Loans"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_2600
#: model:account.account.template,name:l10n_ch.ch_coa_2600
msgid "Long-term provisions"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1400
#: model:account.account.template,name:l10n_ch.ch_coa_1400
msgid "Long-term securities"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_4086
#: model:account.account.template,name:l10n_ch.ch_coa_4086
msgid "Loss of material"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_3805
#: model:account.account.template,name:l10n_ch.ch_coa_3805
msgid "Losses from bad debts"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1500
#: model:account.account.template,name:l10n_ch.ch_coa_1500
msgid "Machinery"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_6100
#: model:account.account.template,name:l10n_ch.ch_coa_6100
msgid "Maintenance & repair expenses"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1441
#: model:account.account,name:l10n_ch.1_ch_coa_2451
#: model:account.account.template,name:l10n_ch.ch_coa_1441
#: model:account.account.template,name:l10n_ch.ch_coa_2451
msgid "Mortgages"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_7010
#: model:account.account.template,name:l10n_ch.ch_coa_7010
msgid "Non-core business expenses"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_7000
#: model:account.account.template,name:l10n_ch.ch_coa_7000
msgid "Non-core business revenues"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_8000
#: model:account.account.template,name:l10n_ch.ch_coa_8000
msgid "Non-operational expenses"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_8100
#: model:account.account.template,name:l10n_ch.ch_coa_8100
msgid "Non-operational revenues"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1850
#: model:account.account.template,name:l10n_ch.ch_coa_1850
msgid "Non-paid-in share capital"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1520
#: model:account.account.template,name:l10n_ch.ch_coa_1520
msgid "Office Equipment (including Information & Communication Technology)"
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.res_config_settings_view_form
msgid "Offset to move the scan line in mm"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,help:l10n_ch.field_account_bank_statement_line__l10n_ch_isr_optical_line
#: model:ir.model.fields,help:l10n_ch.field_account_move__l10n_ch_isr_optical_line
#: model:ir.model.fields,help:l10n_ch.field_account_payment__l10n_ch_isr_optical_line
msgid "Optical reading line, as it will be printed on ISR"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_2140
#: model:account.account.template,name:l10n_ch.ch_coa_2140
msgid "Other interest-bearing short terms liabilities"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_2500
#: model:account.account.template,name:l10n_ch.ch_coa_2500
msgid "Other long term liabilities"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1590
#: model:account.account.template,name:l10n_ch.ch_coa_1590
msgid "Other movable tangible assets"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_6700
#: model:account.account.template,name:l10n_ch.ch_coa_6700
msgid "Other operating expenses"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_3600
#: model:account.account.template,name:l10n_ch.ch_coa_3600
msgid "Other revenues"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1190
#: model:account.account.template,name:l10n_ch.ch_coa_1190
msgid "Other short-term receivables"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_5800
#: model:account.account.template,name:l10n_ch.ch_coa_5800
msgid "Other staff cost"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_2210
#: model:account.account.template,name:l10n_ch.ch_coa_2210
msgid "Others short term liabilities"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_3710
#: model:account.account.template,name:l10n_ch.ch_coa_3710
msgid "Own consumption"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_3700
#: model:account.account.template,name:l10n_ch.ch_coa_3700
msgid "Own services"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1480
#: model:account.account.template,name:l10n_ch.ch_coa_1480
msgid "Participations"
msgstr "参与"

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1700
#: model:account.account.template,name:l10n_ch.ch_coa_1700
msgid "Patents, Licences"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_4530
#: model:account.account.template,name:l10n_ch.ch_coa_4530
msgid "Petrol"
msgstr ""

#. module: l10n_ch
#: model:account.chart.template,name:l10n_ch.l10nch_chart_template
msgid "Plan comptable 2015 (Suisse)"
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.isr_invoice_form
msgid ""
"Please fill in a correct ISR reference in the payment reference.  The banks "
"will refuse your payment file otherwise."
msgstr ""

#. module: l10n_ch
#: code:addons/l10n_ch/models/res_bank.py:0
#, python-format
msgid "Postal"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_2030
#: model:account.account.template,name:l10n_ch.ch_coa_2030
msgid "Prepayments received"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_res_company__l10n_ch_isr_preprinted_account
#: model:ir.model.fields,field_description:l10n_ch.field_res_config_settings__l10n_ch_isr_preprinted_account
msgid "Preprinted account"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_res_company__l10n_ch_isr_preprinted_bank
#: model:ir.model.fields,field_description:l10n_ch.field_res_config_settings__l10n_ch_isr_preprinted_bank
msgid "Preprinted bank"
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.isr_invoice_form
msgid "Print QR-bill"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_res_company__l10n_ch_isr_print_bank_location
msgid "Print bank location"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_res_config_settings__l10n_ch_isr_print_bank_location
msgid "Print bank on ISR"
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.res_config_settings_view_form
msgid ""
"Print the coordinates of your bank under the 'Payment for' title of the ISR.\n"
"                                Your address will be moved to the 'in favour of' section."
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1270
#: model:account.account.template,name:l10n_ch.ch_coa_1270
msgid "Products in process / Unfinished products"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_2970
#: model:account.account.template,name:l10n_ch.ch_coa_2970
msgid "Profits brought forward / Losses brought forward"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_6600
#: model:account.account.template,name:l10n_ch.ch_coa_6600
msgid "Promotion and advertising expenses"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_4070
#: model:account.account.template,name:l10n_ch.ch_coa_4070
msgid "Purchase Loans"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,help:l10n_ch.field_account_setup_bank_manual_config__l10n_ch_qr_iban
#: model:ir.model.fields,help:l10n_ch.field_res_partner_bank__l10n_ch_qr_iban
msgid ""
"Put the QR-IBAN here for your own bank accounts.  That way, you can still "
"use the main IBAN in the Account Number while you will see the QR-IBAN for "
"the barcode.  "
msgstr ""

#. module: l10n_ch
#: code:addons/l10n_ch/models/account_invoice.py:0
#, python-format
msgid ""
"QR-Bill can not be generated on paid invoices. If the invoice is not fully "
"paid, please make sure Recipient Bank field is not empty and try again."
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_account_setup_bank_manual_config__l10n_ch_qr_iban
#: model:ir.model.fields,field_description:l10n_ch.field_res_partner_bank__l10n_ch_qr_iban
msgid "QR-IBAN"
msgstr ""

#. module: l10n_ch
#: code:addons/l10n_ch/models/res_bank.py:0
#, python-format
msgid "QR-IBAN '%s' is invalid."
msgstr ""

#. module: l10n_ch
#: code:addons/l10n_ch/models/res_bank.py:0
#, python-format
msgid "QR-IBAN numbers are only available in Switzerland."
msgstr ""

#. module: l10n_ch
#: model:ir.actions.report,name:l10n_ch.l10n_ch_qr_report
msgid "QR-bill"
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.l10n_ch_swissqr_template
msgid "QR-bill for invoice"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1210
#: model:account.account.template,name:l10n_ch.ch_coa_1210
msgid "Raw materials"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1600
#: model:account.account.template,name:l10n_ch.ch_coa_1600
msgid "Real Estate"
msgstr "房地产"

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_3802
#: model:account.account,name:l10n_ch.1_ch_coa_4092
#: model:account.account.template,name:l10n_ch.ch_coa_3802
#: model:account.account.template,name:l10n_ch.ch_coa_4092
msgid "Rebates"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1101
#: model:account.account.template,name:l10n_ch.ch_coa_1101
msgid "Receivable (PoS)"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1180
#: model:account.account.template,name:l10n_ch.ch_coa_1180
msgid "Receivables from social insurances and social security institutions"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_6000
#: model:account.account.template,name:l10n_ch.ch_coa_6000
msgid "Rent"
msgstr "租金"

#. module: l10n_ch
#: model:ir.model,name:l10n_ch.model_ir_actions_report
msgid "Report Action"
msgstr "报告动作"

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_7500
#: model:account.account.template,name:l10n_ch.ch_coa_7500
msgid "Revenues from operational real estate"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_3400
#: model:account.account.template,name:l10n_ch.ch_coa_3400
msgid "Revenues from services"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_2200
#: model:account.account.template,name:l10n_ch.ch_coa_2200
msgid "Sales Tax (VAT) owed"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_3200
#: model:account.account.template,name:l10n_ch.ch_coa_3200
msgid "Sales of goods (Trade)"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_3000
#: model:account.account.template,name:l10n_ch.ch_coa_3000
msgid "Sales of products (Manufacturing)"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_res_company__l10n_ch_isr_scan_line_left
msgid "Scan line horizontal offset (mm)"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_res_company__l10n_ch_isr_scan_line_top
msgid "Scan line vertical offset (mm)"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1060
#: model:account.account.template,name:l10n_ch.ch_coa_1060
msgid "Securities (with stock exchange price)"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_2800
#: model:account.account.template,name:l10n_ch.ch_coa_2800
msgid "Share capital"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_3807
#: model:account.account.template,name:l10n_ch.ch_coa_3807
msgid "Shipping & Returns"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_2330
#: model:account.account.template,name:l10n_ch.ch_coa_2330
msgid "Short-term provisions"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_5700
#: model:account.account.template,name:l10n_ch.ch_coa_5700
msgid "Social benefits"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_2270
#: model:account.account.template,name:l10n_ch.ch_coa_2270
msgid "Social insurances owed"
msgstr ""

#. module: l10n_ch
#: model:account.fiscal.position,name:l10n_ch.1_fiscal_position_template_1
#: model:account.fiscal.position.template,name:l10n_ch.fiscal_position_template_1
msgid "Suisse national"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_supplies_1
msgid "Supplies CHF from 01.01.2024"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_supplies_2
msgid "Supplies CHF to 31.12.2023"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_account_setup_bank_manual_config__l10n_ch_postal
#: model:ir.model.fields,field_description:l10n_ch.field_res_partner_bank__l10n_ch_postal
msgid "Swiss Postal Account"
msgstr ""

#. module: l10n_ch
#: code:addons/l10n_ch/models/res_bank.py:0
#, python-format
msgid "Swiss QR bill"
msgstr ""

#. module: l10n_ch
#: model:ir.model,name:l10n_ch.model_report_l10n_ch_qr_report_main
msgid "Swiss QR-bill report"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields.selection,name:l10n_ch.selection__account_journal__invoice_reference_model__ch
#: model:ir.ui.menu,name:l10n_ch.account_reports_ch_statements_menu
msgid "Switzerland"
msgstr "瑞士"

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_tax_amount_1
msgid "Tax amount CHF / cent. from 01.01.2024"
msgstr ""

#. module: l10n_ch
#: model:account.tax.report.line,name:l10n_ch.account_tax_report_line_tax_amount_2
msgid "Tax amount CHF / cent. to 31.12.2023"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_5900
#: model:account.account.template,name:l10n_ch.ch_coa_5900
msgid "Temporary staff expenditures"
msgstr ""

#. module: l10n_ch
#: code:addons/l10n_ch/models/res_bank.py:0
#, python-format
msgid ""
"The ISR subcription {} for {} number is not valid.\n"
"It must starts with {} and we a valid postal number format. eg. {}"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,help:l10n_ch.field_account_bank_statement_line__l10n_ch_currency_name
#: model:ir.model.fields,help:l10n_ch.field_account_move__l10n_ch_currency_name
#: model:ir.model.fields,help:l10n_ch.field_account_payment__l10n_ch_currency_name
msgid "The name of this invoice's currency"
msgstr ""

#. module: l10n_ch
#: code:addons/l10n_ch/models/res_bank.py:0
#, python-format
msgid ""
"The partner must have a complete postal address (street, zip, city and "
"country)."
msgstr ""

#. module: l10n_ch
#: code:addons/l10n_ch/models/res_bank.py:0
#, python-format
msgid ""
"The partner set on the bank account meant to receive the payment (%s) must "
"have a complete postal address (street, zip, city and country)."
msgstr ""

#. module: l10n_ch
#: code:addons/l10n_ch/models/res_bank.py:0
#, python-format
msgid ""
"The postal number {} is not valid.\n"
"It must be a valid postal number format. eg. 10-8060-7"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,help:l10n_ch.field_account_bank_statement_line__l10n_ch_isr_number
#: model:ir.model.fields,help:l10n_ch.field_account_move__l10n_ch_isr_number
#: model:ir.model.fields,help:l10n_ch.field_account_payment__l10n_ch_isr_number
msgid "The reference number associated with this invoice"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,help:l10n_ch.field_account_setup_bank_manual_config__l10n_ch_isr_subscription_chf
#: model:ir.model.fields,help:l10n_ch.field_res_partner_bank__l10n_ch_isr_subscription_chf
msgid ""
"The subscription number provided by the bank or Postfinance to identify the "
"bank, used to generate ISR in CHF. eg. 01-162-8"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,help:l10n_ch.field_account_setup_bank_manual_config__l10n_ch_isr_subscription_eur
#: model:ir.model.fields,help:l10n_ch.field_res_partner_bank__l10n_ch_isr_subscription_eur
msgid ""
"The subscription number provided by the bank or Postfinance to identify the "
"bank, used to generate ISR in EUR. eg. 03-162-5"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_3803
#: model:account.account.template,name:l10n_ch.ch_coa_3803
msgid "Third-party commissions"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,help:l10n_ch.field_account_setup_bank_manual_config__l10n_ch_postal
#: model:ir.model.fields,help:l10n_ch.field_res_partner_bank__l10n_ch_postal
msgid ""
"This field is used for the Swiss postal account number on a vendor account "
"and for the client number on your own account. The client number is mostly 6"
" numbers without -, while the postal account number can be e.g. 01-162-8"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1540
#: model:account.account.template,name:l10n_ch.ch_coa_1540
msgid "Tools"
msgstr "工具"

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1091
#: model:account.account.template,name:l10n_ch.ch_coa_1091
msgid "Transfer account: Salaries"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1099
#: model:account.account.template,name:l10n_ch.ch_coa_1099
msgid "Transfer account: miscellaneous"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_4072
#: model:account.account.template,name:l10n_ch.ch_coa_4072
msgid "Transport costs at purchase"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_2980
#: model:account.account.template,name:l10n_ch.ch_coa_2980
msgid "Treasury stock, shares, participation rights (negative item) "
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,help:l10n_ch.field_account_bank_statement_line__l10n_ch_isr_needs_fixing
#: model:ir.model.fields,help:l10n_ch.field_account_move__l10n_ch_isr_needs_fixing
#: model:ir.model.fields,help:l10n_ch.field_account_payment__l10n_ch_isr_needs_fixing
msgid ""
"Used to show a warning banner when the vendor bill needs a correct ISR "
"payment reference. "
msgstr ""

#. module: l10n_ch
#: model:account.tax.group,name:l10n_ch.tax_group_tva_0
msgid "VAT 0%"
msgstr ""

#. module: l10n_ch
#: model:account.tax.group,name:l10n_ch.tax_group_tva_100
msgid "VAT 100%"
msgstr ""

#. module: l10n_ch
#: model:account.tax.group,name:l10n_ch.tax_group_tva_25
msgid "VAT 2.5%"
msgstr ""

#. module: l10n_ch
#: model:account.tax.group,name:l10n_ch.tax_group_vat_26
msgid "VAT 2.6%"
msgstr ""

#. module: l10n_ch
#: model:account.tax.group,name:l10n_ch.tax_group_tva_37
msgid "VAT 3.7%"
msgstr ""

#. module: l10n_ch
#: model:account.tax.group,name:l10n_ch.tax_group_vat_38
msgid "VAT 3.8%"
msgstr ""

#. module: l10n_ch
#: model:account.tax.group,name:l10n_ch.tax_group_tva_77
msgid "VAT 7.7%"
msgstr ""

#. module: l10n_ch
#: model:account.tax.group,name:l10n_ch.tax_group_vat_81
msgid "VAT 8.1%"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_2201
#: model:account.account.template,name:l10n_ch.ch_coa_2201
msgid "VAT payable"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_2940
#: model:account.account.template,name:l10n_ch.ch_coa_2940
msgid "Valuation Reserves"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_6200
#: model:account.account.template,name:l10n_ch.ch_coa_6200
msgid "Vehicle expenses"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1530
#: model:account.account.template,name:l10n_ch.ch_coa_1530
msgid "Vehicles"
msgstr "车辆"

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_6260
#: model:account.account.template,name:l10n_ch.ch_coa_6260
msgid "Vehicules leasing and renting"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_res_config_settings__l10n_ch_isr_scan_line_top
msgid "Vertical offset"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_2960
#: model:account.account.template,name:l10n_ch.ch_coa_2960
msgid "Voluntary retained earnings"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_5000
#: model:account.account.template,name:l10n_ch.ch_coa_5000
msgid "Wages and salaries"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1550
#: model:account.account.template,name:l10n_ch.ch_coa_1550
msgid "Warehouse"
msgstr "仓库"

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_4540
#: model:account.account.template,name:l10n_ch.ch_coa_4540
msgid "Water"
msgstr "水"

#. module: l10n_ch
#: code:addons/l10n_ch/models/res_bank.py:0
#, python-format
msgid ""
"When using a QR-IBAN as the destination account of a QR-code, the payment "
"reference must be a QR-reference."
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_2206
#: model:account.account.template,name:l10n_ch.ch_coa_2206
msgid "Withholding Tax (WT) owed"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1176
#: model:account.account.template,name:l10n_ch.ch_coa_1176
msgid "Withholding Tax (WT) receivable"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1189
#: model:account.account.template,name:l10n_ch.ch_coa_1189
msgid "Withholding tax"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_2279
#: model:account.account.template,name:l10n_ch.ch_coa_2279
msgid "Withholding taxes"
msgstr ""

#. module: l10n_ch
#: model:account.account,name:l10n_ch.1_ch_coa_1280
#: model:account.account.template,name:l10n_ch.ch_coa_1280
msgid "Work in progress"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,help:l10n_ch.field_account_bank_statement_import_journal_creation__invoice_reference_model
#: model:ir.model.fields,help:l10n_ch.field_account_journal__invoice_reference_model
msgid ""
"You can choose different models for each type of reference. The default one "
"is the Odoo reference."
msgstr "您可以为每种参考类型选择不同的模型。默认值是Odoo引用。"

#. module: l10n_ch
#: code:addons/l10n_ch/models/account_invoice.py:0
#, python-format
msgid ""
"You cannot generate an ISR yet.\n"
"\n"
"                                   For this, you need to :\n"
"\n"
"                                   - set a valid postal account number (or an IBAN referencing one) for your company\n"
"\n"
"                                   - define its bank\n"
"\n"
"                                   - associate this bank with a postal reference for the currency used in this invoice\n"
"\n"
"                                   - fill the 'bank account' field of the invoice with the postal to be used to receive the related payment. A default account will be automatically set for all invoices created after you defined a postal account for your company."
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.l10n_ch_swissqr_template
msgid "padding-top:6.2mm; padding-left:8.2mm; padding-right:8.2mm;"
msgstr ""
