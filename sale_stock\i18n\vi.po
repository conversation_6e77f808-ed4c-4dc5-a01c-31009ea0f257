# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_stock
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# Vo Thanh Thuy, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: Vo Thanh Thuy, 2021\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
msgid ""
".\n"
"                Manual actions may be needed."
msgstr ""
".\n"
"                Có thể cần tác vụ thủ công. "

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-clock-o\"/> <b>Preparation</b>"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/> <b>Chuẩn bị</b>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-times\"/> <b>Cancelled</b>"
msgstr "<i class=\"fa fa-fw fa-times\"/> <b>Đã hủy</b>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-truck\"/> <b>Shipped</b>"
msgstr "<i class=\"fa fa-fw fa-truck\"/> <b>Đã vận chuyển</b>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Giá trị đặt ở đây áp dụng cho"
" công ty cụ thể. \" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">Sales</span>"
msgstr "<span class=\"o_stat_text\">Bán hàng</span>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.product_template_view_form_inherit_stock
msgid "<span class=\"o_stat_text\">Sold</span>"
msgstr "<span class=\"o_stat_text\">Đã bán</span>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_delivery_document_inherit_sale_stock
msgid "<strong>Customer Reference:</strong>"
msgstr "<strong>Mã khách hàng:</strong>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<strong>Delivery Orders</strong>"
msgstr "<strong>Đơn giao hàng</strong>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<strong>Incoterm: </strong>"
msgstr "<strong>Incoterm: </strong>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_invoice_document_inherit_sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_saleorder_document_inherit_sale_stock
msgid "<strong>Incoterm:</strong>"
msgstr "<strong>Incoterm:</strong>"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order_line__product_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""
"Sản phẩm lưu kho là sản phẩm mà bạn phải quản lý kho. Ứng dụng Tồn kho phải được cài đặt.\n"
"Sản phẩm tiêu thụ là sản phẩm không cần quản lý kho.\n"
"Dịch vụ là sản phẩm phi vật chất mà bạn cung cấp. "

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism :\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"Theo cấu hình sản phẩm, số lượng được giao có thể được tính tự động theo cơ chế :\n"
"  - Thủ công: số lượng được đặt thủ công trên dòng\n"
"  - Phân tích từ chi phí: số lượng là số lượng tổng từ các chi phí đã đăng\n"
"  - Bảng chấm công: số lượng là tổng số giờ lưu trên nhiệm vụ liên kết với dòng bán hàng\n"
"  - Dịch chuyển kho: số lượng đến từ số lần lấy hàng đã xác nhận\n"

#. module: sale_stock
#: model:ir.ui.menu,name:sale_stock.menu_aftersale
msgid "After-Sale"
msgstr "Hậu mãi"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "All planned operations included"
msgstr "Bao gồm tất cả vận hành theo kế hoạch"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_rules_report__so_route_ids
msgid "Apply specific routes"
msgstr "Áp dụng cho tuyến nhất định"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__picking_policy__direct
msgid "As soon as possible"
msgstr "Càng sớm càng tốt"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/js/qty_at_date_widget.js:0
#, python-format
msgid "Availability"
msgstr "Tính khả dụng"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "Available"
msgstr "Có hàng"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "Available in stock"
msgstr "Có hàng trong kho"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_stock_rules_report__so_route_ids
msgid "Choose to apply SO lines specific routes."
msgstr "Chọn áp dụng dòng đơn bán hàng cho tuyến nhất định. "

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_res_company
msgid "Companies"
msgstr "Công ty"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__effective_date
msgid "Completion date of the first delivery order."
msgstr "Ngày hoàn thành đơn hàng giao hàng đầu tiên."

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_res_config_settings
msgid "Config Settings"
msgstr "Thiết lập cấu hình"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "Date:"
msgstr "Ngày:"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_users__property_warehouse_id
msgid "Default Warehouse"
msgstr "Nhà kho mặc định"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.view_order_form_inherit_sale_stock
msgid "Delivery"
msgstr "Giao hàng"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_cancel__display_delivery_alert
msgid "Delivery Alert"
msgstr "Cảnh báo giao hàng"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__delivery_count
msgid "Delivery Orders"
msgstr "Đơn giao hàng"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__expected_date
msgid ""
"Delivery date you can promise to the customer, computed from the minimum "
"lead time of the order lines in case of Service products. In case of "
"shipping, the shipping policy of the order will be taken into account to "
"either use the minimum or maximum lead time of the order lines."
msgstr ""
"Ngày giao hàng bạn có thể hứa với khách hàng, được tính toán từ thời gian "
"dẫn tối thiểu của dòng đơn hàng trong trường hợp sản phẩm Dịch vụ. Trong "
"trường hợp vận chuyển, chính sách vận chuyển của đơn hàng sẽ được tính đến "
"để sử dụng thời gian dẫn tối thiểu hoặc tối đa của dòng đơn hàng. "

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__display_qty_widget
msgid "Display Qty Widget"
msgstr "Hiển thị tiện ích SL"

#. module: sale_stock
#: model:res.groups,name:sale_stock.group_display_incoterm
msgid "Display incoterms on Sales Order and related invoices"
msgstr ""
"Hiển thị điều khoản thương mại quốc tế trên đơn bán hàng và hóa đơn liên "
"quan "

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_sale
msgid "Display incoterms on orders &amp; invoices"
msgstr "Hiển thị điều khoản thương mại quốc tế trên đơn hàng  &amp; hóa đơn"

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid ""
"Do not forget to change the partner on the following delivery orders: %s"
msgstr "Đừng quên đổi đối tác cho đơn giao hàng sau: %s"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "Documentation"
msgstr "Tài liệu"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__effective_date
msgid "Effective Date"
msgstr "Ngày hiệu lực"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
msgid "Exception(s) occurred on the picking:"
msgstr "(Các) Ngoại lệ đã xảy ra khi lấy hàng: "

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Exception(s) occurred on the sale order(s):"
msgstr "(Các) ngoại lệ đã xảy ra trên (các) đơn bán hàng: "

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Exception(s):"
msgstr "(Các) Ngoại lệ: "

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__expected_date
msgid "Expected Date"
msgstr "Ngày dự kiến"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "Expected Delivery"
msgstr "Dự kiến giao"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.view_order_form_inherit_sale_stock
msgid "Expected:"
msgstr "Dự kiến: "

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__forecast_expected_date
msgid "Forecast Expected Date"
msgstr "Dự báo ngày dự kiến"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "Forecasted Stock"
msgstr "Tồn kho dự báo"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__free_qty_today
msgid "Free Qty Today"
msgstr "SL miễn phí hôm nay"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__show_json_popover
msgid "Has late picking"
msgstr "Lấy hàng muộn"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__picking_policy
msgid ""
"If you deliver all products at once, the delivery order will be scheduled "
"based on the greatest product lead time. Otherwise, it will be based on the "
"shortest."
msgstr ""
"Nếu bạn giao tất cả sản phẩm cùng lúc, đơn giao hàng sẽ được lên lịch dựa vào thời gian dẫn sản phẩm dài nhất. \n"
"Nếu không, đơn giao hàng sẽ được dựa trên thời gian ngắn nhất. "

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Impacted Transfer(s):"
msgstr "Điều chuyển bị ảnh hưởng"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__incoterm
msgid "Incoterm"
msgstr "Incoterm"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__group_display_incoterm
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_sale
msgid "Incoterms"
msgstr "Incoterms"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__incoterm
msgid ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."
msgstr ""
"Điều khoản thương mại Quốc tế (Incoterms) là một chuỗi các điều khoản thương"
" mại được định trước để sử dụng trong các giao dịch quốc tế."

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_users_view_form
msgid "Inventory"
msgstr "Tồn kho"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_location_route
msgid "Inventory Routes"
msgstr "Định tuyến kho"

#. module: sale_stock
#: model:ir.ui.menu,name:sale_stock.menu_invoiced
msgid "Invoicing"
msgstr "Lập hóa đơn"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__is_mto
msgid "Is Mto"
msgstr "Là SX theo Y/C"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__json_popover
msgid "JSON data for the popover widget"
msgstr "Dữ liệu JSON cho tiện ích chú thích"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_account_move
msgid "Journal Entry"
msgstr "Bút toán"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_account_move_line
msgid "Journal Item"
msgstr "Định khoản"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_production_lot
msgid "Lot/Serial"
msgstr "Lô/sê-ri"

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid "Make To Order"
msgstr "Sản xuất theo đơn hàng"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Manual actions may be needed."
msgstr "Có thể cần tác vụ thủ công."

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_res_config_settings__use_security_lead
msgid ""
"Margin of error for dates promised to customers. Products will be scheduled "
"for delivery that many days earlier than the actual promised date, to cope "
"with unexpected delays in the supply chain."
msgstr ""
"Biên độ lỗi cho các ngày đã hứa với khách hàng. Sản phẩm sẽ được lên kế "
"hoạch phân phối sớm hơn từng đó ngày so với ngày thực tế đã hứa, để đối phó "
"với chậm trễ ngoài dự kiến trong chuỗi cung ứng."

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_res_company__security_lead
#: model:ir.model.fields,help:sale_stock.field_res_config_settings__security_lead
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid ""
"Margin of error for dates promised to customers. Products will be scheduled "
"for procurement and delivery that many days earlier than the actual promised"
" date, to cope with unexpected delays in the supply chain."
msgstr ""
"Biên độ lỗi cho các ngày đã hứa với khách hàng. Sản phẩm sẽ được lên kế "
"hoạch thu mua và giao hàng sớm hơn từng đó ngày so với ngày thực tế đã hứa, "
"để đối phó với chậm trễ ngoài dự kiến trong chuỗi cung ứng. "

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "Phương thức cập nhật SL đã giao"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "Move forward expected delivery dates by"
msgstr "Tăng ngày giao hàng dự kiến thêm "

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "No enough future availaibility"
msgstr "Không đủ hàng trong tương lai"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "No future availaibility"
msgstr "Không có hàng trong tương lai"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "On"
msgstr "Trên "

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid "Ordered quantity decreased!"
msgstr "Số lượng đặt đã giảm!"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__default_picking_policy
msgid "Picking Policy"
msgstr "Chính sách lấy hàng"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_procurement_group
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__procurement_group_id
msgid "Procurement Group"
msgstr "Nhóm thu mua"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_product_template
msgid "Product Template"
msgstr "Mẫu sản phẩm"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__product_type
msgid "Product Type"
msgstr "Loại sản phẩm"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__qty_available_today
msgid "Qty Available Today"
msgstr "SL hiện có hôm nay"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__qty_to_deliver
msgid "Qty To Deliver"
msgstr "SL sẽ giao"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_report_product_product_replenishment
msgid "Quotations"
msgstr "Báo giá"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "Remaining demand available at"
msgstr "Nhu cầu còn lại hiện có ở"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "Reserved"
msgstr "Đã đặt trước"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__route_id
msgid "Route"
msgstr "Tuyến "

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_move__sale_line_id
msgid "Sale Line"
msgstr "Dòng bán hàng"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_procurement_group__sale_id
msgid "Sale Order"
msgstr "Đơn bán hàng"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.stock_production_lot_view_form
msgid "Sale Orders"
msgstr "Đơn bán hàng"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_production_lot__sale_order_count
msgid "Sale order count"
msgstr "Số đơn hàng bán"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_report
msgid "Sales Analysis Report"
msgstr "Báo cáo phân tích kinh doanh"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order
#: model:ir.model.fields,field_description:sale_stock.field_stock_picking__sale_id
msgid "Sales Order"
msgstr "Đơn bán hàng"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order_cancel
msgid "Sales Order Cancel"
msgstr "Hủy đơn bán hàng"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order_line
msgid "Sales Order Line"
msgstr "Dòng đơn bán hàng"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.stock_location_route_view_form_inherit_sale_stock
msgid "Sales Order Lines"
msgstr "Dòng đơn bán hàng"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_production_lot__sale_order_ids
msgid "Sales Orders"
msgstr "Đơn bán hàng"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_company__security_lead
msgid "Sales Safety Days"
msgstr "Ngày bán an toàn"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "Schedule deliveries earlier to avoid delays"
msgstr "Lập lịch giao hàng sớm hơn để tránh trì hoãn"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__scheduled_date
msgid "Scheduled Date"
msgstr "Ngày theo kế hoạch"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__security_lead
msgid "Security Lead Time"
msgstr "Thời gian dẫn an toàn"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__use_security_lead
msgid "Security Lead Time for Sales"
msgstr "Thời gian dẫn an toàn cho bán hàng"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_location_route__sale_selectable
msgid "Selectable on Sales Order Line"
msgstr "Có thể chọn trên dòng đơn hàng"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__res_config_settings__default_picking_policy__one
msgid "Ship all products at once"
msgstr "Giao tất cả sản phẩm một lần"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__res_config_settings__default_picking_policy__direct
msgid "Ship products as soon as available, with back orders"
msgstr "Giao sản phẩm ngay khi có hàng, cùng với đơn hàng dự trữ"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__picking_policy
msgid "Shipping Policy"
msgstr "Chính sách giao hàng"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.product_template_view_form_inherit_stock
msgid "Sold in the last 365 days"
msgstr "Được bán trong 365 ngày qua"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_cancel_view_form_inherit
msgid ""
"Some products have already been delivered. Returns can be created from the "
"Delivery Orders."
msgstr ""
"Một số sản phẩm đã được giao hàng. Trả lại hàng có thể được tạo từ Đơn giao "
"hàng. "

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_move
msgid "Stock Move"
msgstr "Dịch chuyển kho"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__move_ids
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order_line__qty_delivered_method__stock_move
msgid "Stock Moves"
msgstr "Dịch chuyển kho"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_report_stock_report_product_product_replenishment
msgid "Stock Replenishment Report"
msgstr "Báo cáo bổ sung kho"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_rule
msgid "Stock Rule"
msgstr "Quy tắc kho"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_rules_report
msgid "Stock Rules report"
msgstr "Báo cáo quy tắc kho"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr "Báo cáo quy tác kho"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "The delivery"
msgstr "Giao hàng "

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid ""
"The delivery address has been changed on the Sales Order<br/>\n"
"                        From <strong>\"%s\"</strong> To <strong>\"%s\"</strong>,\n"
"                        You should probably update the partner on this document."
msgstr ""
"Địa chỉ giao hàng đã thay đổi trong đơn bán hàng<br/>\n"
"                        Từ <strong>\"%s\"</strong> thành <strong>\"%s\"</strong>,\n"
"                        Có thể bạn nên cập nhật đối tác trong tài liệu này. "

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "This product is replenished on demand."
msgstr "Sản phẩm được bổ sung theo yêu cầu. "

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_picking
msgid "Transfer"
msgstr "Điều chuyển"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__picking_ids
msgid "Transfers"
msgstr "Điều chuyển hàng"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_res_users
msgid "Users"
msgstr "Người dùng"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "View Forecast"
msgstr "Xem dự báo"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__virtual_available_at_date
msgid "Virtual Available At Date"
msgstr "Có hàng ảo vào ngày"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__warehouse_id
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__warehouse_id
#: model:ir.model.fields,field_description:sale_stock.field_sale_report__warehouse_id
msgid "Warehouse"
msgstr "Nhà kho"

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid "Warning!"
msgstr "Cảnh báo!"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__picking_policy__one
msgid "When all products are ready"
msgstr "Khi tất cả sản phẩm đã sẵn sàng"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "When to start shipping"
msgstr "Thời điểm bắt đầu giao hàng"

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid ""
"You are decreasing the ordered quantity! Do not forget to manually update "
"the delivery order if needed."
msgstr ""
"Bạn đang giảm số lượng được đặt hàng! Đừng quên cập nhật lại đơn giao hàng "
"nếu cần thiết. "

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid ""
"You cannot decrease the ordered quantity of a sale order line below its delivered quantity.\n"
"Create a return in your inventory first."
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "cancelled"
msgstr "đã hủy"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "days"
msgstr "ngày"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "of"
msgstr "của"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "ordered instead of"
msgstr "đã đặt thay vì"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
msgid "processed instead of"
msgstr "đã được xử lý thay vì"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "will be late."
msgstr "sẽ bị trễ. "
