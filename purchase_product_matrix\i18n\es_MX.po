# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase_product_matrix
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order_line__product_template_attribute_value_ids
msgid "Attribute Values"
msgstr "Valores de atributo"

#. module: purchase_product_matrix
#. openerp-web
#: code:addons/purchase_product_matrix/static/src/js/product_matrix_configurator.js:0
#: code:addons/purchase_product_matrix/static/src/js/product_matrix_configurator.js:0
#, python-format
msgid "Edit Configuration"
msgstr "Editar configuración"

#. module: purchase_product_matrix
#. openerp-web
#: code:addons/purchase_product_matrix/static/src/js/product_matrix_configurator.js:0
#: code:addons/purchase_product_matrix/static/src/js/product_matrix_configurator.js:0
#, python-format
msgid "External Link"
msgstr "Enlace externo"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order__grid
msgid "Grid"
msgstr "Rejilla"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order__grid_product_tmpl_id
msgid "Grid Product Tmpl"
msgstr "Plantilla de las rejillas del producto"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order__grid_update
msgid "Grid Update"
msgstr "Actualización de las rejillas"

#. module: purchase_product_matrix
#: model:ir.model.fields,help:purchase_product_matrix.field_purchase_order__report_grids
msgid ""
"If set, the matrix of configurable products will be shown on the report of "
"this order."
msgstr ""
"Si se establece, la matriz de productos configurables se mostrará en el "
"reporte de esta orden."

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order_line__is_configurable_product
msgid "Is the product configurable?"
msgstr "¿El producto es configurable?"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order__report_grids
msgid "Print Variant Grids"
msgstr "Imprimir cuadrículas de variantes"

#. module: purchase_product_matrix
#: model_terms:ir.ui.view,arch_db:purchase_product_matrix.purchase_order_form_matrix
msgid "Product"
msgstr "Producto"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order_line__product_template_id
msgid "Product Template"
msgstr "Plantilla de producto"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order_line__product_no_variant_attribute_value_ids
msgid "Product attribute values that do not create variants"
msgstr "Valores de atributos del producto que no crean variantes"

#. module: purchase_product_matrix
#: model:ir.model,name:purchase_product_matrix.model_purchase_order
msgid "Purchase Order"
msgstr "Orden de compra"

#. module: purchase_product_matrix
#: model:ir.model,name:purchase_product_matrix.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "Línea de la orden de compra"

#. module: purchase_product_matrix
#: model:ir.model.fields,help:purchase_product_matrix.field_purchase_order__grid_product_tmpl_id
msgid "Technical field for product_matrix functionalities."
msgstr "Campo técnico de las funcionalidades de product_matrix."

#. module: purchase_product_matrix
#: model:ir.model.fields,help:purchase_product_matrix.field_purchase_order__grid
msgid ""
"Technical storage of grid. \n"
"If grid_update, will be loaded on the PO. \n"
"If not, represents the matrix to open."
msgstr ""
"Almacenamiento técnico de rejilla.\n"
"Si grid_update se cargará en la orden de compra.\n"
"Si no, representa la matriz a abrir."

#. module: purchase_product_matrix
#: model:ir.model.fields,help:purchase_product_matrix.field_purchase_order__grid_update
msgid "Whether the grid field contains a new matrix to apply or not."
msgstr "Si el campo de rejilla contiene una nueva matriz para aplicar o no."

#. module: purchase_product_matrix
#: code:addons/purchase_product_matrix/models/purchase.py:0
#, python-format
msgid ""
"You cannot change the quantity of a product present in multiple purchase "
"lines."
msgstr ""
"No puede cambiar la cantidad de un producto presente en varias líneas de "
"compra."
