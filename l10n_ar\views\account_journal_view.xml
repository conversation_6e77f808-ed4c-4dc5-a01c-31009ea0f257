<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="view_account_journal_form" model="ir.ui.view">
        <field name="model">account.journal</field>
        <field name="name">account.journal.form</field>
        <field name="inherit_id" ref="l10n_latam_invoice_document.view_account_journal_form"/>
        <field name="arch" type="xml">
            <field name="l10n_latam_use_documents" position="after">
                <field name="company_partner" invisible="1"/>
                <field name="l10n_ar_afip_pos_system" attrs="{'invisible':['|', '|', ('country_code', '!=', 'AR'), ('l10n_latam_use_documents', '=', False), ('type', '!=', 'sale')], 'required':[('country_code', '=', 'AR'), ('l10n_latam_use_documents', '=', True), ('type', '=', 'sale')]}"/>
                <field name="l10n_ar_afip_pos_number" attrs="{'invisible':['|', '|', ('country_code', '!=', 'AR'), ('l10n_latam_use_documents', '=', False), ('type', '!=', 'sale')], 'required':[('country_code', '=', 'AR'), ('l10n_latam_use_documents', '=', True), ('type', '=', 'sale')]}"/>
                <field name="l10n_ar_afip_pos_partner_id" attrs="{'invisible':['|', '|', ('country_code', '!=', 'AR'), ('l10n_latam_use_documents', '=', False), ('type', '!=', 'sale')], 'required':[('country_code', '=', 'AR'), ('l10n_latam_use_documents', '=', True), ('type', '=', 'sale')]}"/>
                <field name="l10n_ar_share_sequences" attrs="{'invisible':[('l10n_ar_afip_pos_system', '!=', 'II_IM')]}"/>
            </field>
        </field>
    </record>

</odoo>
