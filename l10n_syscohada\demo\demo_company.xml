<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_syscohada" model="res.partner">
        <field name="name">SN Company</field>
        <field name="vat">0001462 2G3</field>
        <field name="street">Rue SC 98</field>
        <field name="city">Dakar</field>
        <field name="country_id" ref="base.sn"/>
        
        <field name="zip">10200</field>
        <field name="phone">+*********** 890</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.syscohadaexample.com</field>
    </record>

    <record id="demo_company_syscohada" model="res.company">
        <field name="name">SN Company</field>
        <field name="partner_id" ref="partner_demo_company_syscohada"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_syscohada')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_syscohada.demo_company_syscohada'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_syscohada.syscohada_chart_template')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_syscohada.demo_company_syscohada')"/>
    </function>
</odoo>
