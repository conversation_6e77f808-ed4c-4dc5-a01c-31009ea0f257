<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- SLIDE.TAG -->
        <record id="view_slide_tag_form" model="ir.ui.view">
            <field name="name">slide.tag.form</field>
            <field name="model">slide.tag</field>
            <field name="arch" type="xml">
                <form string="Tag">
                    <sheet>
                        <group>
                            <field name="name"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="view_slide_tag_tree" model="ir.ui.view">
            <field name="name">slide.tag.tree</field>
            <field name="model">slide.tag</field>
            <field name="arch" type="xml">
                <tree string="Tags" editable="bottom">
                    <field name="name"/>
                </tree>
            </field>
        </record>

        <record id="action_slide_tag" model="ir.actions.act_window">
            <field name="name">Content Tags</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">slide.tag</field>
            <field name="view_mode">tree,form</field>
        </record>

        <!-- SLIDE.SLIDE -->
        <record id="view_slide_slide_form" model="ir.ui.view">
            <field name="name">slide.slide.form</field>
            <field name="model">slide.slide</field>
            <field name="arch" type="xml">
                <form string="Lesson">
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <field name="is_published" widget="website_redirect_button"
                                   attrs="{'invisible': ['|',('is_category', '=', True), ('channel_id', '=', False)]}"/>
                        </div>
                        <widget name="web_ribbon" title="Archived" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                        <field name="image_1920" widget="image" class="oe_avatar" options='{"preview_image": "image_256"}'
                            attrs="{'invisible': [('is_category', '=', True)]}"/>
                        <div class="oe_title">
                            <div>
                                <label for="name" string="Content Title"/>
                            </div>
                            <h1>
                                <field name="name" default_focus="1" placeholder="e.g. How to grow your business with Odoo?"/>
                                <field name="is_category" invisible="1"/>
                            </h1>
                            <field name="tag_ids" attrs="{'invisible': [('is_category', '=', True)]}" widget="many2many_tags" placeholder="Tags..."/>
                        </div>
                        <notebook attrs="{'invisible': [('is_category', '=', True)]}">
                            <page name="document" string="Document">
                                <group>
                                    <group name="lesson_details">
                                        <field name="active" invisible="1"/>
                                        <field name="channel_id"/>
                                        <field name="slide_type"/>
                                        <field name="url" attrs="{
                                            'required': [('slide_type', 'in', ('video'))],
                                            'invisible': [('slide_type', 'not in', ('video'))]}" />
                                        <field name="document_id" invisible="1"/>
                                        <field name="mime_type" force_save="1" readonly="1" groups="base.group_no_one"/>
                                        <field name="datas" string="Attachment"
                                            attrs="{'invisible': [('slide_type', 'not in', ('document', 'presentation'))]}"/>
                                    </group>
                                    <group name="related_details">
                                        <field name="user_id"/>
                                        <label for="completion_time"/>
                                        <div>
                                            <field name="completion_time" widget="float_time" class="oe_inline"/>
                                            <span> hours</span>
                                        </div>
                                        <field name="is_preview"/>
                                        <field name="slide_resource_downloadable" attrs="{'invisible': [('slide_type', 'not in', ['presentation', 'document'])]}"/>
                                        <field name="date_published" string="Published Date" attrs="{'invisible': [('date_published', '=', False)]}" groups="base.group_no_one"/>
                                    </group>
                                </group>
                            </page>
                            <page name="description" string="Description">
                                <field name="description" placeholder="e.g. In this video, we'll give you the keys on how Odoo can help you to grow your business. At the end, we'll propose you a quiz to test your knowledge."/>
                            </page>
                            <page string="Additional Resources" name="external_links" >
                                <group string="External Links">
                                    <field name="link_ids" widget="one2many" nolabel="1">
                                        <tree editable="top">
                                            <field name="name"/>
                                            <field name="link" widget="url" placeholder="e.g. https://www.odoo.com"/>
                                        </tree>
                                    </field>
                                </group>
                                <group string="Resources">
                                    <field name="slide_resource_ids" widget="one2many" nolabel="1">
                                        <tree editable="top">
                                            <field name="name"/>
                                            <field name="data" string="Size" required="1"/>
                                        </tree>
                                    </field>
                                </group>
                            </page>
                            <page name="quiz" string="Quiz">
                                <group name="quiz_details">
                                    <group name="quiz_rewards" string="Rewards">
                                        <group>
                                            <field string="First attempt" name="quiz_first_attempt_reward"/>
                                            <field string="Second attempt" name="quiz_second_attempt_reward"/>
                                            <field string="Third attempt" name="quiz_third_attempt_reward"/>
                                            <field string="Fourth and more attempt" name="quiz_fourth_attempt_reward"/>
                                        </group>
                                    </group>
                                    <group name="questions" string="Questions">
                                        <field name="question_ids" nolabel="1">
                                            <tree>
                                                <field name="sequence" widget="handle"/>
                                                <field name="question"/>
                                            </tree>
                                        </field>
                                    </group>
                                </group>
                            </page>
                            <page name="statistics" string="Statistics">
                                <group>
                                    <group name="view_statistics" string="Views">
                                        <field string="Member" name="slide_views"/>
                                        <field string="Public" name="public_views" readonly="1"/>
                                        <field string="Total" name="total_views"/>
                                        <hr attrs="{'invisible': [('channel_allow_comment', '!=', True), ('channel_type', '=', 'training')]}"/>
                                        <field name="channel_type" invisible="1" readonly="1"/>
                                        <field name="channel_allow_comment" invisible="1" readonly="1"/>
                                        <field name="likes" attrs="{'invisible': [('channel_type', '=', 'training')]}"/>
                                        <field name="dislikes" attrs="{'invisible': [('channel_type', '=', 'training')]}"/>
                                        <field name="comments_count" string="Comments" attrs="{'invisible': [('channel_allow_comment', '!=', True)]}"/>
                                    </group>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>

        <record id="view_slide_slide_form_wo_channel_id" model="ir.ui.view">
            <field name="name">slide.slide.form.wo.channel_id</field>
            <field name="model">slide.slide</field>
            <field name="inherit_id" ref="view_slide_slide_form"/>
            <field name="priority" eval="50"/>
            <field name="mode">primary</field>
            <field name="type">form</field>
            <field name="arch" type="xml">
                <field name="channel_id" position="attributes">
                    <attribute name="invisible">1</attribute>
                    <attribute name="required">0</attribute>
                </field>
            </field>
        </record>

        <record id="slide_slide_view_kanban" model="ir.ui.view">
            <field name="name">slide.slide.view.kanban</field>
            <field name="model">slide.slide</field>
            <field name="arch" type="xml">
                <kanban edit="false" group_create="0"
                    records_draggable="0"
                    class="o_slide_kanban"
                    sample="1">
                    <field name="id"/>
                    <field name="channel_id"/>
                    <field name="slide_type"/>
                    <field name="user_id"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_global_click o_kanban_record_has_image_fill">
                                <t t-set="placeholder" t-value="'/website_slides/static/src/img/channel-training-default.jpg'"/>
                                <div class="o_kanban_image_fill_left d-none d-md-block"
                                    t-attf-style="background-image:url('#{kanban_image('slide.slide', 'image_128', record.id.raw_value,  placeholder)}')">
                                    <img class="o_kanban_image_inner_pic"
                                        t-att-alt="record.channel_id.value"
                                        t-att-src="kanban_image('slide.channel', 'image_128', record.channel_id.raw_value)"/>
                                </div>
                                <div class="o_kanban_image rounded-circle d-md-none"
                                    t-attf-style="background-image:url('#{kanban_image('slide.slide', 'image_128', record.id.raw_value,  placeholder)}')">
                                    <img class="o_kanban_image_inner_pic"
                                        t-att-alt="record.channel_id.value"
                                        t-att-src="kanban_image('slide.channel', 'image_128', record.channel_id.raw_value)"/>
                                </div>
                                <div class="oe_kanban_details d-flex flex-column">
                                    <strong class="o_kanban_record_title oe_partner_heading"><field name="name"/></strong>
                                    <div class="text-mutex"><field name="channel_id"/></div>
                                    <div class="o_kanban_tags_section mb-2">
                                        <span class="oe_kanban_list_many2many">
                                            <field name="tag_ids" widget="many2many_tags"/>
                                        </span>
                                    </div>
                                    <div class="o_kanban_record_bottom mt-auto d-flex justify-content-between align-items-end">
                                        <span>
                                            <i class="fa fa-clock-o mr-2" aria-label="Duration" role="img" title="Duration"/><field name="completion_time" widget="float_time"/>
                                        </span>
                                        <span>
                                            <i class="fa fa-question mr-2" aria-label="Number of Questions" role="img" title="Number of Questions"/><field name="questions_count"/>
                                        </span>
                                        <span>
                                            <i class="fa fa-eye mr-2" aria-label="Views" role="img" title="Views"/><field name="total_views"/>
                                        </span>
                                        <span>
                                            <t t-if="record.slide_type.raw_value == 'infographic'">
                                                <i class="fa fa-file-image-o mr-2" aria-label="Infographic" role="img" title="Infographic"/>
                                            </t>
                                            <t t-elif="record.slide_type.raw_value == 'webpage'">
                                                <i class="fa fa-file-code-o mr-2" aria-label="Webpage" role="img" title="Webpage"/>
                                            </t>
                                            <t t-elif="record.slide_type.raw_value == 'video'">
                                                <i class="fa fa-file-video-o mr-2" aria-label="Video" role="img" title="Video"/>
                                            </t>
                                            <t t-elif="record.slide_type.raw_value == 'quiz'">
                                                <i class="fa fa-flag mr-2" aria-label="Quiz" role="img" title="Quiz"/>
                                            </t>
                                            <t t-else=""><i class="fa fa-file-pdf-o mr-2" aria-label="Document" role="img" title="Document"/></t>
                                            <field name="slide_type"/>
                                        </span>
                                        <field name="user_id" widget="many2one_avatar_user"/>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record id="view_slide_slide_tree" model="ir.ui.view">
            <field name="name">slide.slide.tree</field>
            <field name="model">slide.slide</field>
            <field name="arch" type="xml">
                <tree string="Contents" sample="1">
                    <field name="name"/>
                    <field name="website_id" groups="website.group_multi_website"/>
                    <field name="active" invisible="1"/>
                    <field name="slide_type"/>
                    <field name="channel_id"/>
                    <field name="category_id"/>
                    <field name="date_published"/>
                    <field name="slide_views"/>
                    <field name="public_views"/>
                    <field name="total_views"/>
                    <field name="completion_time"/>
                </tree>
            </field>
        </record>

        <record id="view_slide_slide_search" model="ir.ui.view">
            <field name="name">slide.slide.filter</field>
            <field name="model">slide.slide</field>
            <field name="arch" type="xml">
                <search string="Search Contents">
                    <field name="name"/>
                    <filter name="published" string="Published" domain="[('is_published', '=', True)]"/>
                    <filter name="not_published" string="Waiting for validation" domain="[('is_published', '=', False)]"/>
                    <separator/>
                    <filter string="Archived" name="inactive" domain="[('active','=',False)]"/>
                    <group expand="0" string="Group By">
                        <filter string="Course" name="groupby_channel" domain="[]" context="{'group_by': 'channel_id'}"/>
                        <filter string="Category" name="groupby_category" domain="[]" context="{'group_by': 'category_id'}"/>
                        <filter string="Type" name="groupby_type" domain="[]" context="{'group_by': 'slide_type'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="slide_slide_view_graph" model="ir.ui.view">
            <field name="name">slide.slide.view.graph</field>
            <field name="model">slide.slide</field>
            <field name="arch" type="xml">
                <graph string="Graph of Contents" stacked="0" sample="1">
                    <field name="channel_id"/>
                    <field name="slide_type"/>
                    <field name="total_views" type="measure"/>
                </graph>
            </field>
        </record>

        <record id="slide_slide_view_pivot" model="ir.ui.view">
            <field name="name">slide.slide.view.pivot</field>
            <field name="model">slide.slide</field>
            <field name="arch" type="xml">
                <pivot sample="1">
                    <field name="channel_id" type="row"/>
                    <field name="total_views" type="measure"/>
                </pivot>
            </field>
        </record>

        <record id="slide_slide_action" model="ir.actions.act_window">
            <field name="name">Contents</field>
            <field name="res_model">slide.slide</field>
            <field name="view_mode">kanban,tree,form</field>
            <field name="context"></field>
            <field name="domain">[('is_category', '=', False)]</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Add a new lesson
                </p>
            </field>
        </record>

        <record id="slide_slide_action_report" model="ir.actions.act_window">
            <field name="name">Contents</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">slide.slide</field>
            <field name="view_mode">graph,tree,form,pivot</field>
            <field name="view_id" ref="slide_slide_view_graph"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No data yet!
                </p><p>
                    Create new content for your eLearning
                </p>
            </field>
        </record>
    </data>
</odoo>
