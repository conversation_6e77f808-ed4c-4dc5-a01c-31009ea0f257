<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="hr_custom_report_tree_view" model="ir.ui.view">
            <field name="name">ir.model.fields.inherit.tree.x3</field>
            <field name="model">ir.model.fields</field>
            <field eval="100000" name="priority"/>
            <field name="arch" type="xml">
                <tree create="false" edit="false" import="false" delete="false">
                    <field name="name" readonly="1"/>
                    <field name="field_description" readonly="1"/>
                    <field name="ttype" readonly="1"/>
                </tree>
            </field>
        </record>

        <record id="hr_custom_report_wizard_form" model="ir.ui.view">
            <field name="name">HR Custom Report</field>
            <field name="model">hr.custom.wizard</field>
            <field name="arch" type="xml">
                <form string="HR Custom Report">
                    <notebook>
                        <page string=" Selected Fields">
                            <field name="fieldx_ids" widget='many2many'
                                   options="{'no_open':True,'no_quick_create':True, 'no_create': True, 'no_create_edit':True}"
                                   context="{'tree_view_ref': 'hr_custom_report.hr_custom_report_tree_view'}"
                                   nolabel="1">
                                <tree>
                                    <field name="name"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>

                    <footer>
                        <button name="generate_report" type="object" string="Print" class="oe_highlight"/>
                        <button type="object" string="Cancel" special="cancel"/>
                    </footer>

                </form>
            </field>
        </record>

        <record id="action_hr_custom_report_wizard_menu" model="ir.actions.act_window">
            <field name="name">HR Custom Report Wizard</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">hr.custom.wizard</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="hr_custom_report_wizard_form"/>
            <field name="target">new</field>
        </record>


        <menuitem id="hr_custom_report_wizard_menu" name="HR Custom Report"
                  parent="hr.menu_human_resources_configuration"
                  action="action_hr_custom_report_wizard_menu"
                  sequence="1"/>

    </data>
</odoo>