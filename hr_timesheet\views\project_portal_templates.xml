<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <template id="portal_my_task" inherit_id="project.portal_my_task" name="Portal: My Task with Timesheets">
        <xpath expr="//t[@t-set='card_body']" position="inside">
            <div class="container" t-if="timesheets">
                <hr class="mt-4 mb-1"/>
                <h5 class="mt-2 mb-2">Timesheets</h5>
                <t t-call="hr_timesheet.portal_timesheet_table"/>
            </div>
        </xpath>
        <xpath expr="//div[@name='portal_my_task_planned_hours']" position="after">
            <div t-if="task.planned_hours > 0"><strong>Progress:</strong> <span t-field="task.progress"/>%</div>
        </xpath>
        <xpath expr="//div[@name='portal_my_task_planned_hours']/t" position="replace">
            <t t-call="hr_timesheet.portal_my_task_planned_hours_template"></t>
        </xpath>
    </template>

    <template id="portal_my_task_planned_hours_template">
        <t t-if="is_uom_day and timesheets._convert_hours_to_days(task.planned_hours) > 0">
            <strong>Planned Days:</strong> <span t-esc="timesheets._convert_hours_to_days(task.planned_hours)" t-options='{"widget": "timesheet_uom"}'/>
        </t>
        <t t-if="not is_uom_day and task.planned_hours > 0" t-call="project.portal_my_task_planned_hours_template"></t>
    </template>

    <template id="portal_tasks_list_inherit" inherit_id="project.portal_tasks_list" name="Portal: My Tasks with Timesheets">
        <xpath expr="//t[@t-foreach='tasks']/tr" position="before">
            <t t-set="timesheet_ids" t-value="task.sudo().timesheet_ids"/>
            <t t-set="is_uom_day" t-value="timesheet_ids._is_timesheet_encode_uom_day()"/>
        </xpath>
        <xpath expr="//thead/tr/th[@name='project_portal_assignees']" position="after">
            <th t-if="is_uom_day">Days Spent</th>
            <th t-else="">Hours Spent</th>
        </xpath>
        <xpath expr="//tbody/t/tr/td[@name='project_portal_assignees']" position="after">
            <td>
                <t t-if="is_uom_day">
                    <t t-out="timesheet_ids._convert_hours_to_days(task.effective_hours)"/>
                    <span t-if="task.planned_hours > 0"> / <t t-out="timesheet_ids._convert_hours_to_days(task.planned_hours)"/></span>
                    <span> day(s)</span>
                </t>
                <t t-else="">
                    <span t-field="task.effective_hours" t-options='{"widget": "float_time"}'/>
                    <t t-if="task.planned_hours > 0">
                        /
                        <span t-field="task.planned_hours" t-options='{"widget": "float_time"}'/>
                    </t>
                    <span> hour(s)</span>
                </t>
            </td>
        </xpath>
    </template>

    <template id="portal_timesheet_table" name="Portal Timesheet Table">
        <table class="table table-sm">
            <thead>
              <tr>
                <th>Date</th>
                <th>Employee</th>
                <th>Description</th>
                <th t-if="is_uom_day" class="text-right">Duration (Days)</th>
                <th t-else="" class="text-right">Duration (Hours)</th>
              </tr>
            </thead>
            <tr t-foreach="timesheets" t-as="timesheet">
                <td><t t-esc="timesheet.date" t-options='{"widget": "date"}'/></td>
                <td><t t-esc="timesheet.employee_id.name"/></td>
                <td><t t-esc="timesheet.name"/></td>
                <td class="text-right">
                    <span t-if="is_uom_day" t-esc="timesheet._get_timesheet_time_day()" t-options='{"widget": "timesheet_uom"}'/>
                    <span t-else="" t-field="timesheet.unit_amount" t-options='{"widget": "float_time"}'/>
                </td>
            </tr>
            <tfoot>
                <tr>
                    <th colspan="3"></th>
                    <th class="text-right">
                        <t t-set="timesheets_amount" t-value="round(sum(timesheets.mapped('unit_amount')), 2) or 0.0"></t>
                        <div t-if="is_uom_day"><strong>Days recorded:</strong> <span t-esc="timesheets._convert_hours_to_days(timesheets_amount)" t-options='{"widget": "timesheet_uom"}'/></div>
                        <div t-else=""><strong>Hours recorded:</strong> <span t-esc="timesheets_amount" t-options='{"widget": "float_time"}'/></div>
                        <t t-set="timesheets_by_subtask_amount" t-value="round(sum(sum(timesheet_by_subtask.mapped('unit_amount') or 0.0) for timesheet_by_subtask in timesheets_by_subtask.values()), 2) or 0.0"></t>
                        <div t-if="timesheets_by_subtask">
                            <div t-if="is_uom_day">Days recorded on sub-tasks: <span t-esc="timesheets._convert_hours_to_days(timesheets_by_subtask_amount)" t-options='{"widget": "timesheet_uom"}'/></div>
                            <div t-else="">Hours recorded on sub-tasks: <span t-esc="timesheets_by_subtask_amount" t-options='{"widget": "float_time"}'/></div>
                        </div>
                        <t t-set="planned_time" t-value="task.planned_hours"></t>
                        <div t-if="planned_time > 0" name="planned_time">
                            <div t-if="is_uom_day">Remaining Days: <span t-esc="timesheets._convert_hours_to_days(planned_time - timesheets_amount - timesheets_by_subtask_amount)" t-options='{"widget": "timesheet_uom"}'/></div>
                            <div t-else="">Remaining Hours: <span t-esc="planned_time - timesheets_amount - timesheets_by_subtask_amount" t-options='{"widget": "float_time"}'/></div>
                        </div>
                    </th>
                </tr>
            </tfoot>
        </table>
    </template>

</odoo>
