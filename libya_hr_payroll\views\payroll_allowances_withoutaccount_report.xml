<?xml version="1.0" encoding="utf-8"?>
<odoo>
<template id="report_libya_allowances_withoutaccount">
    <t t-call="web.html_container">
<!--        <t t-foreach="docs" t-as="o">-->
            <t t-call="web.external_layout">

                <div class="page" style="direction: rtl;"><br/><br/><br/><br/>
                    <h5 class="text-center">كشف بالمرتب وتفصيل الإضافات</h5>
                    <t t-if="location">
                        <h5 class="text-center">   الموقع  : <span t-esc="location"/></h5>
                    </t>
                    <h6 class="text-center"> &#160;لشهر <strong t-esc="date_month"/>&#160;<span>/</span><span t-esc="date_year"/></h6>

                    <table class="table table-bordered">
                           <thead>
                               <tr class="text-center">
                                   <th>م</th>
                                   <th>الاسم</th>
                                   <th>إجمالي المرتب</th>
                                   <th>الإضافي</th>
                                   <th>المكأفأة</th>
                                   <th>بدل وقود</th>
                                   <th>بدل مركوب</th>
                                   <th>علاوة منطقة</th>
                                   <th>بدل إعاشة</th>
                                   <th>علاوة وردية</th>
                                   <th>م.هواتف</th>
                                   <th>الخصميات</th>
                                   <th>الدخل الصافي</th>
                               </tr>
                           </thead>
                           <tbody>
                                <tr t-foreach="payslip_ids" t-as="payslip" class="text-center">


                                    <td style="background-color:#D0D0D0;"><span t-esc="payslip_index + 1" /></td>
                                    <td>
                                        <span t-esc="payslip['employee_name']"/> <br/>
                                        <span t-esc="payslip['position_name']"/>
                                    </td>
                                    <td><span t-esc="'%.3f'% payslip['basic']"/></td>

                                    <t t-if="payslip['ALW1']">
                                        <td ><span t-esc="'%.3f'% payslip['ALW1']"/></td>
                                    </t>
                                    <t t-else="">
                                        <td ><span>-</span></td>
                                    </t>

                                    <t t-if="payslip['ALW2']">
                                        <td ><span t-esc="'%.3f'% payslip['ALW2']"/></td>
                                    </t>
                                    <t t-else="">
                                        <td ><span>-</span></td>
                                    </t>

                                    <t t-if="payslip['ALW3']">
                                        <td ><span t-esc="'%.3f'% payslip['ALW3']"/></td>
                                    </t>
                                    <t t-else="">
                                        <td ><span>-</span></td>
                                    </t>

                                    <t t-if="payslip['ALW4']">
                                        <td ><span t-esc="'%.3f'% payslip['ALW4']"/></td>
                                    </t>
                                    <t t-else="">
                                        <td ><span>-</span></td>
                                    </t>

                                    <t t-if="payslip['ALW5']">
                                        <td ><span t-esc="'%.3f'% payslip['ALW5']"/></td>
                                    </t>
                                    <t t-else="">
                                        <td ><span>-</span></td>
                                    </t>

                                    <t t-if="payslip['ALW6']">
                                        <td ><span t-esc="'%.3f'% payslip['ALW6']"/></td>
                                    </t>
                                    <t t-else="">
                                        <td ><span>-</span></td>
                                    </t>

                                    <t t-if="payslip['ALW7']">
                                        <td ><span t-esc="'%.3f'% payslip['ALW7']"/></td>
                                    </t>
                                    <t t-else="">
                                        <td ><span>-</span></td>
                                    </t>

                                    <t t-if="payslip['ALW8']">
                                        <td ><span t-esc="'%.3f'% payslip['ALW8']"/></td>
                                    </t>
                                    <t t-else="">
                                        <td ><span>-</span></td>
                                    </t>

                                    <td ><span t-esc="'%.3f'% payslip['total_taxes']"/></td>
                                    <td style="background-color:white;"><span t-esc="'%.3f'% payslip['net_salary']"/></td>

                                </tr>
                           </tbody>
                           <thead style="background-color:#D0D0D0;">
                                <tr class="text-center">
                                    <th colspan="2">الإجمالي</th>
                                    <th ><span t-esc="'%.2f'% sum(m['basic'] for m in payslip_ids)" /></th>
                                    <th ><span t-esc="'%.2f'% sum(m['ALW1'] for m in payslip_ids)" /></th>
                                    <th ><span t-esc="'%.2f'% sum(m['ALW2'] for m in payslip_ids)" /></th>
                                    <th ><span t-esc="'%.2f'% sum(m['ALW3'] for m in payslip_ids)" /></th>
                                    <th ><span t-esc="'%.2f'% sum(m['ALW4'] for m in payslip_ids)" /></th>
                                    <th ><span t-esc="'%.2f'% sum(m['ALW5'] for m in payslip_ids)" /></th>
                                    <th ><span t-esc="'%.2f'% sum(m['ALW6'] for m in payslip_ids)" /></th>
                                    <th ><span t-esc="'%.2f'% sum(m['ALW7'] for m in payslip_ids)" /></th>
                                    <th ><span t-esc="'%.2f'% sum(m['ALW8'] for m in payslip_ids)" /></th>
                                    <th ><span t-esc="'%.2f'% sum(m['total_taxes'] for m in payslip_ids)" /></th>
                                    <th ><span t-esc="'%.2f'% sum(m['net_salary'] for m in payslip_ids)" /></th>

                                </tr>
                            </thead>

                    </table><br/>
                    <div class="row">
                            <h5 style="margin-left:420px;margin-right:100px;">
                                 إعداد
                            </h5>
                            <h5 style="margin-left:350px;">
                                 المراجعة الداخلية
                            </h5>
                            <h5 >
                                 المديـــــــر العام
                            </h5>
                        </div>
                        <div class="row">
                            <h5 style="margin-left:280px;margin-right:30px;">
                                 ------------------------------
                            </h5>
                            <h5 style="margin-left:280px;">
                                 ------------------------------
                            </h5>
                            <h5 >
                                 ------------------------------
                            </h5>
                        </div><br/><br/>
                        <div class="text-center">
                            <h5>
                                توقيع وختم المصرف بالإستلام
                            </h5>
                            <h5>
                                 --------------------------------
                            </h5>
                        </div>

                </div>
            </t>
        </t>
<!--    </t>-->
</template>
</odoo>
