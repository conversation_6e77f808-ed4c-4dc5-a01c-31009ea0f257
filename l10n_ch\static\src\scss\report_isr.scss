@font-face {
    font-family: ocrb;
    src: url('../font/ocrb.otf') format('opentype');
}

.l10n_ch_isr {
    /*
        all elements are positionned for A4 format (210 x 297mm) but the viewport
        is bigger than this, hence we zoom the whole page to be bigger than the viewport
        which will then be resized down by wkhtmltopdf to get back to an A4 format
     */
    zoom: 1.25;

    &.o_in_iframe {
        zoom: 1;

        /* display dummy check bacground in html rendering to help see the end result */
        #isr {
            background: url(../img/background_virgin_isr.png) bottom no-repeat;
            background-size: 100%;

            &.isr-print-bank {
                background-image: url(../img/background_virgin_isr_bank.png);
            }
        }
    }

    /* content outside isr needs margins to not overlap header */
    #content_outside_isr {
        padding: 15px;
        padding-top: 150px;
    }

    /* ISR is intended for pre-printed paper, we don't want stylistic background */
    .o_report_layout_striped {
        background: none;
        min-height: 0;
    }

#isr {
    height: 106mm;
    width: 210mm;

    position: absolute;
    /* position bvr at bottom of page */
    top: 297 - 106mm;
    left: 0;

    overflow: hidden;
    font-family: ocrb;
    line-height: 0.16in;
    font-size: 10pt;

    p {
        margin-bottom: 0;
    }

    #voucher {
        height: 4in;
        width: 2.4in;

        position: absolute;
        /* header title of BVR section */
        top: 0.167in;
        /* right of voucher corresponds to left of slip */
        right: 5.9in;
    }

    #voucher > * {
        position: absolute;
        /* default left margin for fields content */
        left: 0.15in;

        &#voucher-for-contact {
            top: 0.25in;
        }

        &#voucher-for-bank {
            top: 0.18in;
        }

        &#voucher-for-bank + #voucher-for-contact {
            top: 0.68in;
        }

        &#voucher-bank {
            top: 1.51in;
            left: 1.1in;
        }

        &#voucher-amount_units {
            width: 1.62in;
            top: 1.87in;

            text-align: right;
            font-size: 14px;
            letter-spacing: 0.095in;
        }

        &#voucher-amount_cents {
            width: 0.4in;
            top: 1.87in;
            left: 1.96in;

            font-size: 14px;
            letter-spacing: 0.095in;
        }

        &#voucher-by {
            top: 2.22in;
            font-size: 9pt;
            max-width: 2in;

            #voucher-by_reference_number {
                font-size: 7.5pt;
                margin-bottom: 0.05in;
            }
        }
    }

    #slip {
        height: 4in;
        width: 5.9in;

        position: absolute;
        /* header title of BVR section */
        top: 0.167in;
        /* right of slip corresponds to right of slip */
        right: 0;
    }

    #slip > * {
        position: absolute;
        /* default left margin for fields content */
        left: 0.15in;

        &#slip-for-contact {
            top: 0.25in;

            p {
                margin-bottom: 0;
            }
        }

        &#slip-for-bank {
            top: 0.18in;
        }

        &#slip-for-bank + #slip-for-contact {
            top: 0.68in;
        }

        &#slip-bank {
            top: 1.51in;
            left: 1.1in;
        }

        &#slip-amount_units {
            width: 1.62in;
            top: 1.87in;

            text-align: right;
            font-size: 14px;
            letter-spacing: 0.095in;
        }

        &#slip-amount_cents {
            width: 0.4in;
            top: 1.87in;
            left: 1.96in;

            font-size: 14px;
            letter-spacing: 0.095in;
        }

        &#slip-reference {
            width: 3.3in;
            top: 1.2in;
            left: 2.5in;
            text-align: center;
        }

        &#slip-by {
            top: 1.85in;
            left: 2.55in;
        }

        &#slip-optical-line {
            right: 0.32in;
            bottom: 0.667in;

            /* Positioning is set for OCRB of Tsukurimashou Font Family */
            font-family: ocrb;
            font-size: 10pt;
            line-height: 0.1in;

            > div {
                position: relative;

                > div {
                    position: absolute;
                    bottom: 0;
                }
            }
        }
    }
}
}
