<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.website</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="20"/>
        <field name="inherit_id" ref="base.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[hasclass('settings')]//div[@id='website_settings']" position="inside">
                <div class="col-12 col-lg-6 o_setting_box" id="events_app_setting">
                    <div class="o_setting_right_pane">
                        <label for="events_app_name" string="Events PWA"/>
                        <span class="fa fa-lg fa-globe" title="Values set here are website-specific." groups="website.group_multi_website"/>
                        <div class="text-muted">
                            Name of your website's Events Progressive Web Application
                        </div>
                        <div class="content-group">
                            <div class="row mt16">
                                <label class="col-lg-3 o_light_label" string="Name" for="events_app_name"/>
                                <field name="events_app_name" attrs="{'required': [('website_id', '!=', False)]}"/>
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>
</odoo>
