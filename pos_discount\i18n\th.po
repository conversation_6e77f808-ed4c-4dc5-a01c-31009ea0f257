# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_discount
# 
# Translators:
# <PERSON>, 2021
# <PERSON>ichano<PERSON> Jamwutthipreecha, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:49+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: Wichanon Jamwutthipreecha, 2022\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_discount
#: model:ir.model.fields,help:pos_discount.field_pos_config__iface_discount
msgid "Allow the cashier to give discounts on the whole order."
msgstr "อนุญาตให้แคชเชียร์มอบส่วนลดสำหรับคำสั่งทั้งหมด"

#. module: pos_discount
#. openerp-web
#: code:addons/pos_discount/static/src/xml/DiscountButton.xml:0
#, python-format
msgid "Discount"
msgstr "ส่วนลด"

#. module: pos_discount
#: model_terms:ir.ui.view,arch_db:pos_discount.pos_config_view_form_inherit_pos_discount
msgid "Discount %"
msgstr "ส่วนลด %"

#. module: pos_discount
#. openerp-web
#: code:addons/pos_discount/static/src/js/DiscountButton.js:0
#: model:ir.model.fields,field_description:pos_discount.field_pos_config__discount_pc
#, python-format
msgid "Discount Percentage"
msgstr "เปอร์เซ็นต์ส่วนลด"

#. module: pos_discount
#: model:ir.model.fields,field_description:pos_discount.field_pos_config__discount_product_id
#: model_terms:ir.ui.view,arch_db:pos_discount.pos_config_view_form_inherit_pos_discount
msgid "Discount Product"
msgstr "สินค้าลดราคา"

#. module: pos_discount
#. openerp-web
#: code:addons/pos_discount/static/src/js/DiscountButton.js:0
#, python-format
msgid "No discount product found"
msgstr "ไม่พบสินค้าลดราคา"

#. module: pos_discount
#: model:ir.model.fields,field_description:pos_discount.field_pos_config__iface_discount
msgid "Order Discounts"
msgstr "คำสั่งลดราคา"

#. module: pos_discount
#: model:ir.model,name:pos_discount.model_pos_config
msgid "Point of Sale Configuration"
msgstr "กำหนดค่าการขายหน้าร้าน"

#. module: pos_discount
#: model:ir.model.fields,help:pos_discount.field_pos_config__discount_pc
msgid "The default discount percentage"
msgstr "เปอร์เซ็นต์ส่วนลดเริ่มต้น"

#. module: pos_discount
#. openerp-web
#: code:addons/pos_discount/static/src/js/DiscountButton.js:0
#, python-format
msgid ""
"The discount product seems misconfigured. Make sure it is flagged as 'Can be"
" Sold' and 'Available in Point of Sale'."
msgstr ""
"ดูเหมือนว่าสินค้าส่วนลดจะกำหนดค่าไม่ถูกต้อง "
"ตรวจสอบให้แน่ใจว่าถูกตั้งค่าสถานะเป็น 'สามารถขายได้' และ "
"'มีจำหน่ายในการขายหน้าร้าน'"

#. module: pos_discount
#: model:ir.model.fields,help:pos_discount.field_pos_config__discount_product_id
msgid "The product used to model the discount."
msgstr "สินค้าที่ใช้ในการจำลองส่วนลด"
