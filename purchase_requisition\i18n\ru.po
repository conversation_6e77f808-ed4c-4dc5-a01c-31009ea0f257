# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase_requisition
# 
# Translators:
# Андрей <PERSON>ев <<EMAIL>>, 2021
# <PERSON> <ye<PERSON><PERSON><PERSON><PERSON>@itpp.dev>, 2021
# <PERSON><PERSON><PERSON><PERSON> <k<PERSON><PERSON><EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# Сергей <PERSON>нин <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2023
# alenafairy, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: alenafairy, 2023\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: purchase_requisition
#: model:ir.actions.report,print_report_name:purchase_requisition.action_report_purchase_requisitions
msgid "'Tender - %s' % (object.name)"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Call for Tender Reference:</strong><br/>"
msgstr "<strong>Призыв к Тендерной Ссылке:</strong><br/>"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Date</strong>"
msgstr "<strong>Дата</strong>"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Description</strong>"
msgstr "<strong>Описание</strong>"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Product</strong>"
msgstr "<strong>Продукт</strong>"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Product UoM</strong>"
msgstr "<strong>ЕИ Продукта</strong>"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Qty</strong>"
msgstr "<strong>Кол-во</strong>"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Reference </strong>"
msgstr "<strong>Ссылки </strong>"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Scheduled Date</strong>"
msgstr "<strong>Запланированная Дата</strong>"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Scheduled Ordering Date:</strong><br/>"
msgstr "<strong>Планируемая Дата Закупки:</strong><br/>"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Selection Type:</strong><br/>"
msgstr "<Strong> Тип выбора: </ strong> <br/>"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Source:</strong><br/>"
msgstr "<strong>Источник:</strong><br/>"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Vendor </strong>"
msgstr "<strong>Поставщик </strong>"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_needaction
msgid "Action Needed"
msgstr "Требует внимания"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__active
msgid "Active"
msgstr "Активно"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_ids
msgid "Activities"
msgstr "Действия"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Оформление исключения активности"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_state
msgid "Activity State"
msgstr "Статус действия"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_type_icon
msgid "Activity Type Icon"
msgstr "Иконка типа действия"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_product_supplierinfo__purchase_requisition_id
msgid "Agreement"
msgstr "Договор"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__date_end
msgid "Agreement Deadline"
msgstr "Срок действия соглашения"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__exclusive
msgid "Agreement Selection Type"
msgstr "Тип выбора соглашения"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__type_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__name
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_type_form
msgid "Agreement Type"
msgstr "Тип соглашения"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.res_config_settings_view_form
msgid "Agreement Types"
msgstr "Типы соглашений"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__account_analytic_id
msgid "Analytic Account"
msgstr "Аналитический счёт"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__analytic_tag_ids
msgid "Analytic Tags"
msgstr "Теги аналитики"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_type_form
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_type_search
msgid "Archived"
msgstr "Архивировано"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_attachment_count
msgid "Attachment Count"
msgstr "Количество вложений"

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state__open
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state_blanket_order__open
msgid "Bid Selection"
msgstr "Выбор ставки"

#. module: purchase_requisition
#: model:purchase.requisition.type,name:purchase_requisition.type_single
msgid "Blanket Order"
msgstr "Заказать одеяло"

#. module: purchase_requisition
#: model:purchase.requisition.type,name:purchase_requisition.type_multi
msgid "Call for Tender"
msgstr "Тендер"

#. module: purchase_requisition
#: model:ir.actions.report,name:purchase_requisition.action_report_purchase_requisitions
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "Call for Tenders"
msgstr "Вызов для вызовов"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Cancel"
msgstr "Отмена"

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state__cancel
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state_blanket_order__cancel
msgid "Cancelled"
msgstr "Отменено"

#. module: purchase_requisition
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
#, python-format
msgid "Cancelled by the agreement associated to this quotation."
msgstr "Отменено соглашением, связанным с этим коммерческим."

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__product_uom_category_id
msgid "Category"
msgstr "Категория"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Close"
msgstr "Закрыть"

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state__done
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state_blanket_order__done
msgid "Closed"
msgstr "Закрыт"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__company_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__company_id
msgid "Company"
msgstr "Компания"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Confirm"
msgstr "Подтвердить"

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state__in_progress
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state_blanket_order__in_progress
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Confirmed"
msgstr "Подтвержден"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Преобразование между единицами измерения возможно, если они принадлежат "
"одной категории. Преобразование будет сделано на основе коэффициентов."

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__product_template__purchase_requisition__rfq
msgid "Create a draft purchase order"
msgstr "Создать черновик заказа на поставку"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_product_product__purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_product_template__purchase_requisition
msgid ""
"Create a draft purchase order: Based on your product configuration, the "
"system will create a draft purchase order.Propose a call for tender : If the"
" 'purchase_requisition' module is installed and this option is selected, the"
" system will create a draft call for tender."
msgstr ""
"Создайте черновик заказ на покупку: на основе настройки вашего товара "
"система создаст черновик заказ на покупку. Подайте заявку на тендер, если "
"установлено модуль buy_requisition и этот параметр, система создаст проект "
"заявки для участия в тендере."

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__create_uid
msgid "Created by"
msgstr "Создан"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__create_date
msgid "Created on"
msgstr "Создан"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__currency_id
msgid "Currency"
msgstr "Валюта"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__product_description_variants
msgid "Custom Description"
msgstr "Пользовательское описание"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_type_form
msgid "Data for new quotations"
msgstr "Данные для новых коммерческих"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__schedule_date
msgid "Delivery Date"
msgstr "Дата доставки"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__description
msgid "Description"
msgstr "Описание"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition_type__line_copy__none
msgid "Do not create RfQ lines automatically"
msgstr "Не создавать строки запроса цен автоматически "

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Done"
msgstr "Сделано"

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state__draft
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state_blanket_order__draft
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Draft"
msgstr "Черновик"

#. module: purchase_requisition
#: model_terms:ir.actions.act_window,help:purchase_requisition.action_purchase_requisition
msgid ""
"Example of purchase agreements include call for tenders and blanket orders."
msgstr ""
"Примеры соглашений о покупке включают призыв к тендерам и общим заказам."

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_follower_ids
msgid "Followers"
msgstr "Подписчики"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_partner_ids
msgid "Followers (Partners)"
msgstr "Подписчики (Партнеры)"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Иконка со шрифтом Font awesome, например. fa-tasks"

#. module: purchase_requisition
#: model_terms:ir.actions.act_window,help:purchase_requisition.action_purchase_requisition
msgid ""
"For a blanket order, you can record an agreement for a specifc period\n"
"            (e.g. a year) and you order products within this agreement, benefiting\n"
"            from the negotiated prices."
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Future Activities"
msgstr "Будущие действия"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Group By"
msgstr "Группировка"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__has_message
msgid "Has Message"
msgstr "Есть сообщение"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__id
msgid "ID"
msgstr "Идентификатор"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_exception_icon
msgid "Icon"
msgstr "Иконка"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Значок, обозначающий действие исключения."

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_needaction
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_unread
msgid "If checked, new messages require your attention."
msgstr "Если отмечено, новые сообщения будут требовать вашего внимания."

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_has_error
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Если отмечено - некоторые сообщения имеют ошибку доставки."

#. module: purchase_requisition
#: model_terms:ir.actions.act_window,help:purchase_requisition.action_purchase_requisition
msgid ""
"In a call for tenders, you can record the products you need to buy\n"
"            and generate the creation of RfQs to vendors. Once the tenders have\n"
"            been registered, you can review and compare them and you can\n"
"            validate some and cancel others."
msgstr ""
"В объявлении тендеров вы можете записать продукты, которые вам нужно купить\n"
"             и генерировать создание запросов на коммерческое для поставщиков. Как только тендеры\n"
"             будут зарегистрированы, вы можете просмотреть и сравнить их и вы можете\n"
"             проверить одни и отменить другие."

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "In negotiation"
msgstr "В переговорах"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_is_follower
msgid "Is Follower"
msgstr "Подписчик"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition____last_update
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line____last_update
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type____last_update
msgid "Last Modified on"
msgstr "Последнее изменение"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__write_uid
msgid "Last Updated by"
msgstr "Последний раз обновил"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Late Activities"
msgstr "Просроченные действия"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__line_copy
msgid "Lines"
msgstr "Строк"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_main_attachment_id
msgid "Main Attachment"
msgstr "Основное вложение"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_has_error
msgid "Message Delivery error"
msgstr "Ошибка доставки сообщения"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_ids
msgid "Messages"
msgstr "Сообщения"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "Multiple Requisitions"
msgstr "Несколько запросов"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Крайний срок моей активности "

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "My Agreements"
msgstr "Мои сделки"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
msgid "Name, TIN, Email, or Reference"
msgstr "Имя, TIN, Email или ссылки"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "New Agreements"
msgstr "Новые соглашения"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "New Quotation"
msgstr "Новое коммерческое предложение"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Календарное событие для следующего действия"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Крайний срок следующего действия"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_summary
msgid "Next Activity Summary"
msgstr "Краткое описание следующего действия"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_type_id
msgid "Next Activity Type"
msgstr "Тип следующего действия"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_needaction_counter
msgid "Number of Actions"
msgstr "Количество действий"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__order_count
msgid "Number of Orders"
msgstr "Количество заказов"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_has_error_counter
msgid "Number of errors"
msgstr "Количество ошибок"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Количество сообщений, требующих действия"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Количество сообщений с ошибкой отправки"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_unread_counter
msgid "Number of unread messages"
msgstr "Количество непрочитанных сообщений"

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state__ongoing
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state_blanket_order__ongoing
msgid "Ongoing"
msgstr "Постоянно"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__qty_ordered
msgid "Ordered Quantities"
msgstr "Упорядоченные количества"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__ordering_date
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Ordering Date"
msgstr "Дата заказа"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_product_product__purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_product_template__purchase_requisition
msgid "Procurement"
msgstr "Поставка"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_product_product
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__product_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__product_id
msgid "Product"
msgstr "Продукт"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_product_template
msgid "Product Template"
msgstr "Шаблон продукта"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__product_uom_id
msgid "Product Unit of Measure"
msgstr "Ед. изм. продукта"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Products"
msgstr "Продукты"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__line_ids
msgid "Products to Purchase"
msgstr "ТМЦ для закупки"

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__product_template__purchase_requisition__tenders
msgid "Propose a call for tenders"
msgstr "Предложить конкурс заявок"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order__requisition_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__requisition_id
msgid "Purchase Agreement"
msgstr "Договор о покупке"

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.tender_type_action
#: model:ir.ui.menu,name:purchase_requisition.menu_purchase_requisition_type
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_type_form
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_type_tree
msgid "Purchase Agreement Types"
msgstr "Типы соглашения о покупке"

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.action_purchase_requisition
#: model:ir.ui.menu,name:purchase_requisition.menu_purchase_requisition_pro_mgt
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_tree
msgid "Purchase Agreements"
msgstr "Соглашения о покупке"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_order
msgid "Purchase Order"
msgstr "Заказ на закупку"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "Позиция заказа на закупку"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__purchase_ids
msgid "Purchase Orders"
msgstr "Заказы закупки"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_search_inherit
msgid "Purchase Orders with requisition"
msgstr "Заказы закупки с запросом"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__user_id
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Purchase Representative"
msgstr "представитель закупок"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition
msgid "Purchase Requisition"
msgstr "Запрос закупки"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition_line
#: model:ir.model.fields,field_description:purchase_requisition.field_product_supplierinfo__purchase_requisition_line_id
msgid "Purchase Requisition Line"
msgstr "Позиция запроса закупки"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition_type
msgid "Purchase Requisition Type"
msgstr "Тип реквизитов покупки"

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.act_res_partner_2_purchase_order
msgid "Purchase orders"
msgstr "Заказы закупки"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order__is_quantity_copy
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__is_quantity_copy
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__quantity_copy
msgid "Quantities"
msgstr "Количество"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__product_qty
msgid "Quantity"
msgstr "Количество"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "RFQs/Orders"
msgstr "ЗЦП/Заказы"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__name
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Reference"
msgstr "Ссылка"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.product_template_form_view_inherit
msgid "Reordering"
msgstr "дозаказа"

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.action_purchase_requisition_to_so
msgid "Request for Quotation"
msgstr "Запрос цены поставщика"

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.action_purchase_requisition_list
msgid "Request for Quotations"
msgstr "Запрос цен поставщика"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "Requests for Quotation Details"
msgstr "Детали запроса цен поставщика"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_search_inherit
msgid "Requisition"
msgstr "Запрос"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Reset to Draft"
msgstr "Переместить в черновики"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_user_id
msgid "Responsible User"
msgstr "Ответственный"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Ошибка доставки SMS"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__schedule_date
msgid "Scheduled Date"
msgstr "Запланированная дата"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Search Purchase Agreements"
msgstr "Поиск соглашений о покупке"

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition_type__exclusive__multiple
msgid "Select multiple RFQ (non-exclusive)"
msgstr "Выбрать несколько запросов цен (неисключительных)"

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition_type__exclusive__exclusive
msgid "Select only one RFQ (exclusive)"
msgstr "Выбрать только один ЗЦП (эксклюзивный)"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_type__exclusive
msgid ""
"Select only one RFQ (exclusive):  when a purchase order is confirmed, cancel the remaining purchase order.\n"
"\n"
"                    Select multiple RFQ (non-exclusive): allows multiple purchase orders. On confirmation of a purchase order it does not cancel the remaining orders"
msgstr ""
"Выберите только один ЗЦП (эксклюзивный): при подтверждении заказа на поставку отмените остальные заказы.\n"
"\n"
" Выберите несколько ЗЦП (неэксклюзивных): допускается несколько заказов на поставку. Подтверждении заказа на поставку не отменяет остальные заказы."

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__sequence
msgid "Sequence"
msgstr "Нумерация"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_type__active
msgid ""
"Set active to false to hide the Purchase Agreement Types without removing "
"it."
msgstr "Снимите флажок \"Активно\", чтобы скрыть виды договора не удаляя их."

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition_type__quantity_copy__none
msgid "Set quantities manually"
msgstr "Устанавливать количество вручную"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Show all records which has next action date is before today"
msgstr ""
"Показать все записи, у которых дата следующего действия не превышает текущей"
" даты"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__origin
msgid "Source Document"
msgstr "Документ-источник"

#. module: purchase_requisition
#: model_terms:ir.actions.act_window,help:purchase_requisition.action_purchase_requisition
msgid "Start a new purchase agreement"
msgstr "Начните новое соглашение закупок"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__state_blanket_order
msgid "State Blanket Order"
msgstr "Этап долгосрочного заказ"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__state
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Status"
msgstr "Статус"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Статус основан на плановых действиях\n"
"Просрочено: срок исполнения истек\n"
"Сегодня: выполнить сегодня\n"
"Запланировано: срок в будущем."

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__supplier_info_ids
msgid "Supplier Info"
msgstr "информация поставщика"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_product_supplierinfo
msgid "Supplier Pricelist"
msgstr "Каталог поставщика"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Terms and Conditions"
msgstr "Сроки и условия..."

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__schedule_date
msgid ""
"The expected and scheduled delivery date where all the products are received"
msgstr "Ожидаемая и запланированная дата поставки всех продуктов получены"

#. module: purchase_requisition
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
#, python-format
msgid ""
"There is already an open blanket order for this supplier. We suggest you "
"complete this open blanket order, instead of creating a new one."
msgstr ""
"Для данного поставщика уже существует открытый общий заказ. Мы предлагаем не"
" создавать новый заказ, а завершить открытый заказ."

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Today Activities"
msgstr "Действия на сегодня"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Тип активности исключения в записи."

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__price_unit
msgid "Unit Price"
msgstr "Цена за ед."

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_unread
msgid "Unread Messages"
msgstr "Непрочитанные сообщения"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Счетчик непрочитанных сообщений"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "UoM"
msgstr "ЕИТ"

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition_type__line_copy__copy
msgid "Use lines of agreement"
msgstr "Использовать линии соглашения"

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition_type__quantity_copy__copy
msgid "Use quantities of agreement"
msgstr "Использовать количества соглашений"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Validate"
msgstr "Утвердить"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__vendor_id
msgid "Vendor"
msgstr "Поставщик"

#. module: purchase_requisition
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
#, python-format
msgid "Warning for %s"
msgstr "Предупреждение для %s "

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__website_message_ids
msgid "Website Messages"
msgstr "Сообщения с сайта"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__website_message_ids
msgid "Website communication history"
msgstr "История общения с сайтом"

#. module: purchase_requisition
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
#, python-format
msgid "You can only delete draft requisitions."
msgstr "Вы можете удалить только одну черновик реквизитов."

#. module: purchase_requisition
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
#, python-format
msgid "You cannot confirm agreement '%s' because there is no product line."
msgstr "Вы не можете подтвердить соглашение ` %s ьтому что нет строки товара."

#. module: purchase_requisition
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
#, python-format
msgid "You cannot confirm the blanket order without price."
msgstr "Вы не можете подтвердить долгосрочное заказ без цены."

#. module: purchase_requisition
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
#, python-format
msgid "You cannot confirm the blanket order without quantity."
msgstr "Вы не можете подтвердить долгосрочное заказ без количестве."

#. module: purchase_requisition
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
#, python-format
msgid ""
"You have to cancel or validate every RfQ before closing the purchase "
"requisition."
msgstr ""
"Перед закрытием заявки необходимо отменить или проверить каждый Запрос на "
"Коммерческое."

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "e.g. PO0025"
msgstr "прим. PO0025"
