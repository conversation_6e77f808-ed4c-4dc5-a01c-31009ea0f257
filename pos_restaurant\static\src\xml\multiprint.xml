<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="NameWrapped">
        <t t-foreach="change.name_wrapped.slice(1)" t-as="wrapped_line">
            <div style="text-align: right">
                <span t-esc="wrapped_line"/>
            </div>
        </t>
    </t>

    <t t-name="OrderChangeReceipt">
        <div class="pos-receipt">
            <div class="pos-receipt-order-data"><t t-esc="changes.name" /></div>
            <t t-if="changes.floor || changes.table">
                <br />
                <div class="pos-receipt-title">
                    <t t-esc="changes.floor" /> / <t t-esc="changes.table"/>
                </div>
            </t>
            <br />
            <br />
            <t t-if="changes.cancelled.length > 0">
                <div class="pos-order-receipt-cancel">
                    <div class="pos-receipt-title">
                        CANCELLED
                        <t t-esc='changes.time.hours'/>:<t t-esc='changes.time.minutes'/>
                    </div>
                    <br />
                    <br />
                    <t t-foreach="changes.cancelled" t-as="change">
                        <div class="multiprint-flex">
                            <t t-esc="change.qty"/>
                            <span t-esc="change.name_wrapped[0]"/>
                        </div>
                        <t t-call="NameWrapped"/>
                        <t t-if="change.note">
                            <div>
                                NOTE
                                <span class="pos-receipt-right-align">...</span>
                            </div>
                            <div><span class="pos-receipt-left-padding">--- <t t-esc="change.note" /></span></div>
                            <br/>
                        </t>
                    </t>
                    <br />
                    <br />
                </div>
            </t>
            <t t-if="changes.new.length > 0">
                <div class="pos-receipt-title">
                    NEW
                    <t t-esc='changes.time.hours'/>:<t t-esc='changes.time.minutes'/>
                </div>
                <br />
                <br />
                <t t-foreach="changes.new" t-as="change">
                    <div class="multiprint-flex">
                        <t t-esc="change.qty"/>
                        <span t-esc="change.name_wrapped[0]"/>
                    </div>
                    <t t-call="NameWrapped"/>
                    <t t-if="change.note">
                        <div>
                            NOTE
                            <span class="pos-receipt-right-align">...</span>
                        </div>
                        <div><span class="pos-receipt-left-padding">--- <t t-esc="change.note" /></span></div>
                        <br/>
                    </t>
                </t>
                <br />
                <br />
            </t>
        </div>
    </t>

</templates>
