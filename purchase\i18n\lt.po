# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON>j, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON>as Versada <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# Naglis Jonaitis, 2022
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:18+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: Ramunė ViaLaurea <<EMAIL>>, 2023\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: purchase
#: model:ir.actions.report,print_report_name:purchase.action_report_purchase_order
msgid ""
"\n"
"                (object.state in ('draft', 'sent') and 'Request for Quotation - %s' % (object.name) or\n"
"                'Purchase Order - %s' % (object.name))"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_partner__supplier_invoice_count
#: model:ir.model.fields,field_description:purchase.field_res_users__supplier_invoice_count
msgid "# Vendor Bills"
msgstr "# tiekėjų sąskaitų"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__nbr_lines
msgid "# of Lines"
msgstr "# eilučių"

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "%(product)s from %(original_receipt_date)s to %(new_receipt_date)s"
msgstr ""

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "%s confirmed the receipt will take place on %s."
msgstr ""

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "%s modified receipt dates for the following products:"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.mail_notification_confirm
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "&amp;times;"
msgstr "&amp;times;"

#. module: purchase
#: model:ir.actions.report,print_report_name:purchase.report_purchase_quotation
msgid "'Request for Quotation - %s' % (object.name)"
msgstr "'Komercinio pasiūlymo užklausa - %s' % (object.name)"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_qty_received_template
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "-&gt;"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "3-way matching"
msgstr "3 dalių sutapimas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__module_account_3way_match
msgid "3-way matching: purchases, receptions and bills"
msgstr "3 dalių sutapimas: pirkimai, priėmimai ir sąskaitos"

#. module: purchase
#: model:mail.template,body_html:purchase.email_template_edi_purchase_reminder
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>\n"
"        <t t-if=\"object.partner_id.parent_id\">\n"
"            (<t t-out=\"object.partner_id.parent_id.name or ''\">Azure Interior</t>)\n"
"        </t>\n"
"        <br/><br/>\n"
"        Here is a reminder that the delivery of the purchase order <strong t-out=\"object.name or ''\">P00015</strong>\n"
"        <t t-if=\"object.partner_ref\">\n"
"            <strong>(<t t-out=\"object.partner_ref or ''\">REF_XXX</t>)</strong>\n"
"        </t>\n"
"        is expected for \n"
"        <t t-if=\"object.date_planned\">\n"
"            <strong t-out=\"format_date(object.date_planned) or ''\">05/05/2021</strong>.\n"
"        </t>\n"
"         <t t-else=\"\">\n"
"            <strong>undefined</strong>.\n"
"        </t>\n"
"        Could you please confirm it will be delivered on time?\n"
"    </p>\n"
"</div>"
msgstr ""

#. module: purchase
#: model:mail.template,body_html:purchase.email_template_edi_purchase_done
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>\n"
"        <t t-if=\"object.partner_id.parent_id\">\n"
"            (<t t-out=\"object.partner_id.parent_id.name or ''\">Azure Interior</t>)\n"
"        </t>\n"
"        <br/><br/>\n"
"        Here is in attachment a purchase order <strong t-out=\"object.name or ''\">P00015</strong>\n"
"        <t t-if=\"object.partner_ref\">\n"
"            with reference: <t t-out=\"object.partner_ref or ''\">REF_XXX</t>\n"
"        </t>\n"
"        amounting in <strong t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</strong>\n"
"        from <t t-out=\"object.company_id.name or ''\">YourCompany</t>. \n"
"        <br/><br/>\n"
"        <t t-if=\"object.date_planned\">\n"
"            The receipt is expected for <strong t-out=\"format_date(object.date_planned) or ''\">05/05/2021</strong>.\n"
"            <br/><br/>\n"
"            Could you please acknowledge the receipt of this order?\n"
"        </t>\n"
"    </p>\n"
"</div>"
msgstr ""

#. module: purchase
#: model:mail.template,body_html:purchase.email_template_edi_purchase
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>\n"
"        <t t-if=\"object.partner_id.parent_id\">\n"
"            (<t t-out=\"object.partner_id.parent_id.name or ''\">Azure Interior</t>)\n"
"        </t>\n"
"        <br/><br/>\n"
"        Here is in attachment a request for quotation <strong t-out=\"object.name or ''\">P00015</strong>\n"
"        <t t-if=\"object.partner_ref\">\n"
"            with reference: <t t-out=\"object.partner_ref or ''\">REF_XXX</t>\n"
"        </t>\n"
"        from <t t-out=\"object.company_id.name or ''\">YourCompany</t>.\n"
"        <br/><br/>\n"
"        If you have any questions, please do not hesitate to contact us.\n"
"        <br/><br/>\n"
"        Best regards,\n"
"    </p>\n"
"</div>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<i class=\"fa fa-comment\"/> Send message"
msgstr "<i class=\"fa fa-comment\"/>Siųsti žinutę"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<i class=\"fa fa-download\"/> Download"
msgstr "<i class=\"fa fa-download\"/>Atsisiųsti"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid ""
"<i class=\"fa fa-fw fa-check\" role=\"img\" aria-label=\"Done\" "
"title=\"Done\"/><span class=\"d-none d-md-inline\"> Done</span>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> <b>Paid</b>"
msgstr "<i class=\"fa fa-fw fa-check\"/> <b>Apmokėta</b>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<i class=\"fa fa-fw fa-clock-o\"/> <b>Waiting Payment</b>"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/> <b>Laukia mokėjimo</b>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_partner_kanban_view
msgid ""
"<i class=\"fa fa-fw fa-credit-card\" role=\"img\" aria-label=\"Purchases\" "
"title=\"Purchases\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-credit-card\" role=\"img\" aria-label=\"Purchases\" "
"title=\"Purchases\"/>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid ""
"<i class=\"fa fa-fw fa-file-text\" role=\"img\" aria-label=\"Waiting for "
"Bill\" title=\"Waiting for Bill\"/><span class=\"d-none d-md-inline\"> "
"Waiting for Bill</span>"
msgstr ""
"<i class=\"fa fa-fw fa-file-text\" role=\"img\" aria-label=\"Waiting for "
"Bill\" title=\"Waiting for Bill\"/><span class=\"d-none d-md-inline\"> "
"Laukiama sąskaitos</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid ""
"<i class=\"fa fa-fw fa-remove\" role=\"img\" aria-label=\"Cancelled\" "
"title=\"Cancelled\"/><span class=\"d-none d-md-inline\"> Cancelled</span>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<i class=\"fa fa-print\"/> Print"
msgstr "<i class=\"fa fa-print\"/> Spausdinti"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid ""
"<span class=\"d-none d-md-inline\">Confirmation Date</span>\n"
"                          <span class=\"d-block d-md-none\">Confirmation</span>"
msgstr ""
"<span class=\"d-none d-md-inline\">Patvirtinimo data</span>\n"
"                          <span class=\"d-block d-md-none\">Patvirtinimas</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid ""
"<span class=\"d-none d-md-inline\">Purchase Order #</span>\n"
"                          <span class=\"d-block d-md-none\">Ref.</span>"
msgstr ""
"<span class=\"d-none d-md-inline\">Pirkimo užsakymo nr.</span>\n"
"                          <span class=\"d-block d-md-none\">Ref.</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
msgid ""
"<span class=\"d-none d-md-inline\">Request for Quotation #</span>\n"
"                        <span class=\"d-block d-md-none\">Ref.</span>"
msgstr ""
"<span class=\"d-none d-md-inline\">Komercinio pasiūlymo nr.</span>\n"
"                        <span class=\"d-block d-md-none\">Ref.</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid ""
"<span class=\"o_form_label\" attrs=\"{'invisible': [('state','not in',('draft','sent'))]}\">Request for Quotation </span>\n"
"                        <span class=\"o_form_label\" attrs=\"{'invisible': [('state','in',('draft','sent'))]}\">Purchase Order </span>"
msgstr ""
"<span class=\"o_form_label\" attrs=\"{'invisible': [('state','not in',('draft','sent'))]}\">Komercinio pasiūlymo užklausa </span>\n"
"                        <span class=\"o_form_label\" attrs=\"{'invisible': [('state','in',('draft','sent'))]}\">Pirkimo užsakymas </span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.product_normal_form_view_inherit_purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_template_purchase_buttons_from
msgid "<span class=\"o_stat_text\">Purchased</span>"
msgstr "<span class=\"o_stat_text\">Nupirkta</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid ""
"<span class=\"text-muted\" attrs=\"{'invisible': "
"[('mail_reception_confirmed','=', False)]}\">(confirmed by vendor)</span>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid ""
"<span class=\"text-muted\" attrs=\"{'invisible': "
"[('mail_reminder_confirmed', '=', False)]}\">(confirmed by vendor)</span>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid ""
"<span groups=\"account.group_show_line_subtotals_tax_excluded\">Amount</span>\n"
"                              <span groups=\"account.group_show_line_subtotals_tax_included\">Total Price</span>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_partner_property_form
msgid "<span> day(s) before</span>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "<span>Ask confirmation</span>"
msgstr "<span>Prašyti patvirtinimo</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<span>Taxes</span>"
msgstr "<span>Mokesčiai</span>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<strong class=\"d-block mb-1\">From:</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<strong class=\"d-block mb-1\">Invoices</strong>"
msgstr "<strong class=\"d-block mb-1\">Sąskaitos-faktūros</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong class=\"mr16\">Subtotal</strong>"
msgstr "<strong class=\"mr16\">Tarpinė suma</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<strong class=\"text-muted\">Purchase Representative</strong>"
msgstr "<strong class=\"text-muted\">Pirkimo atstovas</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Amount</strong>"
msgstr "<strong>Suma</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<strong>Confirmation Date:</strong>"
msgstr "<strong>Patvirtinimo data:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Date Req.</strong>"
msgstr "<strong>Pristatymo data</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Description</strong>"
msgstr "<strong>Aprašymas</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Expected Date</strong>"
msgstr "<strong>Numatoma data</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Order Date:</strong>"
msgstr "<strong>Užsakymo data:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Purchase Representative:</strong>"
msgstr "<strong>Pirkimo atstovas:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Qty</strong>"
msgstr "<strong>Kiekis</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<strong>Receipt Date:</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<strong>Request For Quotation Date:</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Shipping address:</strong>"
msgstr "<strong>Pristatymo adresas:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Taxes</strong>"
msgstr "<strong>Mokesčiai</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "<strong>The ordered quantity has been updated.</strong>"
msgstr "<strong>Užsakytas kiekis buvo atnaujintas.</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_qty_received_template
msgid "<strong>The received quantity has been updated.</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<strong>This purchase has been canceled.</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Unit Price</strong>"
msgstr "<strong>Vieneto kaina</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "<strong>Update Here</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Your Order Reference:</strong>"
msgstr "<strong>Jūsų užsakymo numeris:</strong>"

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "A sample email has been sent to %s."
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_line__product_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""
"Kaupiamas produktas yra produktas, kuriam valdote atsargas. Inventoriaus programa turi būti įdiegta.\n"
"Vartojamas produktas yra produktas, kuriam nėra valdomos atsargos.\n"
"Paslauga yra nematerialus produktas, kurį suteikiate."

#. module: purchase
#: model:res.groups,name:purchase.group_warning_purchase
msgid "A warning can be set on a product or a customer (Purchase)"
msgstr "Įspėjimas gali būti nustatomas produktui arba klientui (Pirkimai)"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"Ability to select a package type in purchase orders and to force a quantity "
"that is a multiple of the number of units per package."
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__access_warning
msgid "Access warning"
msgstr "Prieigos įspėjimas"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_line__qty_received_method
msgid ""
"According to product configuration, the received quantity can be automatically computed by mechanism :\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_accrued_expense_entry
msgid "Accrued Expense Entry"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_needaction
msgid "Action Needed"
msgstr "Reikalingas veiksmas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_ids
msgid "Activities"
msgstr "Veiklos"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Veiklos Išimties Dekoravimas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_state
msgid "Activity State"
msgstr "Veiklos būsena"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_type_icon
msgid "Activity Type Icon"
msgstr "Veiklos tipo ikona"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Add a note"
msgstr "Pridėti pastabą"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Add a product"
msgstr "Pridėti produktą"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Add a section"
msgstr "Pridėti sekciją"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Add several variants to the purchase order from a grid"
msgstr ""

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/js/tours/purchase.js:0
#, python-format
msgid "Add some products or services to your quotation."
msgstr ""

#. module: purchase
#: model:res.groups,name:purchase.group_purchase_manager
msgid "Administrator"
msgstr "Administratorius"

#. module: purchase
#: code:addons/purchase/controllers/portal.py:0
#, python-format
msgid "All"
msgstr "Visi"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/xml/purchase_dashboard.xml:0
#, python-format
msgid "All Draft RFQs"
msgstr "Visos juodraštinės užklausos"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/xml/purchase_dashboard.xml:0
#, python-format
msgid "All Late RFQs"
msgstr "Visos vėluojančios užklausos"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/xml/purchase_dashboard.xml:0
#, python-format
msgid "All RFQs"
msgstr "Visos užklausos"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/xml/purchase_dashboard.xml:0
#, python-format
msgid "All Waiting RFQs"
msgstr "Visos laukiančios užklausos"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_config_settings__group_send_reminder
msgid "Allow automatically send email to remind your vendor the receipt date"
msgstr ""

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_company__po_lock__edit
msgid "Allow to edit purchase orders"
msgstr "Leisti redaguoti pirkimo užsakymus"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__amount
msgid "Amount"
msgstr "Suma"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_report__delay_pass
msgid ""
"Amount of time between date planned and order by date for each purchase "
"order line."
msgstr ""
"Laikotarpis nuo suplanuotos datos iki užsakymo pagal datą kiekvienoje "
"pirkimo užsakymo eilutėje."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_report__avg_days_to_purchase
msgid ""
"Amount of time between purchase approval and document creation date. Due to "
"a hack needed to calculate this,               every record will show the "
"same average value, therefore only use this as an aggregated value with "
"group_operator=avg"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_report__delay
msgid "Amount of time between purchase approval and order by date."
msgstr "Laikotarpis nuo pirkimo patvirtinimo iki užsakymo pagal datą."

#. module: purchase
#: model:ir.model,name:purchase.model_account_analytic_account
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__account_analytic_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__account_analytic_id
msgid "Analytic Account"
msgstr "Analitinė sąskaita"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__analytic_tag_ids
msgid "Analytic Tags"
msgstr "Analitinės žymos"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Approve Order"
msgstr "Patvirtinti užsakymą"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_attachment_count
msgid "Attachment Count"
msgstr "Prisegtukų skaičius"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Attributes"
msgstr "Atributai"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_move_form_inherit_purchase
msgid "Auto-Complete"
msgstr "Automatinis užpildymas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_account_bank_statement_line__purchase_vendor_bill_id
#: model:ir.model.fields,field_description:purchase.field_account_move__purchase_vendor_bill_id
#: model:ir.model.fields,field_description:purchase.field_account_payment__purchase_vendor_bill_id
msgid "Auto-complete"
msgstr "Automatinis užpuldymas"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_account_bank_statement_line__purchase_vendor_bill_id
#: model:ir.model.fields,help:purchase.field_account_move__purchase_vendor_bill_id
#: model:ir.model.fields,help:purchase.field_account_payment__purchase_vendor_bill_id
msgid "Auto-complete from a past bill / purchase order."
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_account_bank_statement_line__purchase_id
#: model:ir.model.fields,help:purchase.field_account_move__purchase_id
#: model:ir.model.fields,help:purchase.field_account_payment__purchase_id
msgid "Auto-complete from a past purchase order."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Automatically lock confirmed orders to prevent editing"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Automatically remind the receipt date to your vendors"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__receipt_reminder_email
#: model:ir.model.fields,help:purchase.field_res_partner__receipt_reminder_email
#: model:ir.model.fields,help:purchase.field_res_users__receipt_reminder_email
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid ""
"Automatically send a confirmation email to the vendor X days before the "
"expected receipt date, asking him to confirm the exact date."
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__price_average
msgid "Average Cost"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__avg_days_to_purchase
msgid "Average Days to Purchase"
msgstr ""

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/xml/purchase_dashboard.xml:0
#, python-format
msgid "Avg Order Value ("
msgstr "Vidutinė užsakymo vertė ("

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.mail_notification_confirm
msgid "Best regards,"
msgstr "Pagarbiai"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__default_purchase_method
msgid "Bill Control"
msgstr "Mokėjimų valdymas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__invoice_count
msgid "Bill Count"
msgstr "Sąskaitų skaičius"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__invoice_lines
msgid "Bill Lines"
msgstr "Mokėjimo eilutės"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Billed"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__qty_invoiced
msgid "Billed Qty"
msgstr "Kiekis, už kurį pateiktos sąskaitos"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Billed Quantity"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "Billed Quantity:"
msgstr "Kiekis, už kurį pateiktos sąskaitos:"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__invoice_status
msgid "Billing Status"
msgstr "Mokėjimo būsena"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__invoice_ids
msgid "Bills"
msgstr "Sąskaitos"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
msgid "Bills Received"
msgstr "Gauta sąskaitų"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__product_template__purchase_line_warn__block
#: model:ir.model.fields.selection,name:purchase.selection__res_partner__purchase_warn__block
msgid "Blocking Message"
msgstr "Blokuojanti žinutė"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_calendar
msgid "Calendar View"
msgstr "Kalendoriaus peržiūra"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"Calls for tenders are used when you want to generate requests for quotations"
" to several vendors for a given set of products. You can configure per "
"product if you directly do a Request for Quotation to one vendor or if you "
"want a Call for Tenders to compare offers from several vendors."
msgstr ""
"Kvietimai pateikti pasiūlymą yra naudojami, kai norite sugeneruoti užklausas"
" komerciniams pasiūlymams iš kelių tiekėjų tam tikram produktų rinkiniui. "
"Galite nustatyti kiekvienam produktui, jei darote tiesioginę komercinio "
"pasiūlymo užklausą tiekėjui, arba, jei norite, palyginti pasiūlymus iš kelių"
" tiekėjų."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Cancel"
msgstr "Atšaukti"

#. module: purchase
#: code:addons/purchase/controllers/portal.py:0
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__cancel
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__cancel
#, python-format
msgid "Cancelled"
msgstr "Atšauktas"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "Cancelled Purchase Order #"
msgstr "Atšauktų pirkimo užsakymų #"

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "Cannot delete a purchase order line which is in state '%s'."
msgstr "Negalima ištrinti pirkimo užsakymo, kuris yra '%s' būsenos."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_uom_category_id
msgid "Category"
msgstr "Kategorija"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__commercial_partner_id
msgid "Commercial Entity"
msgstr "Komercinis juridinis asmuo"

#. module: purchase
#: model:ir.model,name:purchase.model_res_company
msgid "Companies"
msgstr "Įmonės"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__company_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order__company_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__company_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__company_id
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Company"
msgstr "Įmonė"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__company_currency_id
msgid "Company Currency"
msgstr "Įmonės naudojama valiuta"

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "Compose Email"
msgstr "Rašyti el. laišką"

#. module: purchase
#: model:ir.model,name:purchase.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigūracijos nustatymai"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_config
msgid "Configuration"
msgstr "Konfigūracija"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.mail_notification_confirm
msgid "Confirm"
msgstr "Patvirtinti"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Confirm Order"
msgstr "Patvirtinti užsakymą"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Confirm Receipt Date"
msgstr "Patvirtinti gavimo datą"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_company__po_double_validation__one_step
msgid "Confirm purchase orders in one step"
msgstr "Vienu žingsniu patvirtinkite pirkimo užsakymus "

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/js/tours/purchase.js:0
#, python-format
msgid "Confirm your purchase."
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__date_approve
#: model:ir.model.fields,field_description:purchase.field_purchase_report__date_approve
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Confirmation Date"
msgstr "Patvirtinimo data"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Confirmation Date Last Year"
msgstr "Patvirtinimo data praėjusiais metais"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_company__po_lock__lock
msgid "Confirmed purchase orders are not editable"
msgstr "Patvirtinti pirkimo užsakymai yra neredaguojami"

#. module: purchase
#: model:ir.model,name:purchase.model_res_partner
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Contact"
msgstr "Kontaktas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product__purchase_method
#: model:ir.model.fields,field_description:purchase.field_product_template__purchase_method
msgid "Control Policy"
msgstr "Valdymo politika"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Konvertavimas galimas tik tarp tai pačiai grupei priklausančių matavimo "
"vienetų. Konvertuojant kiekiai bus paskaičiuoti pagal santykį."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Create Bill"
msgstr "Sukurti sąskaitą"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
msgid "Create Bills"
msgstr "Sukurti pirkimo sąskaitas"

#. module: purchase
#: model:ir.actions.server,name:purchase.action_purchase_batch_bills
msgid "Create Vendor Bills"
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_product_action
msgid "Create a new product variant"
msgstr "Sukurkite naują produkto variantą"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__create_uid
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__create_uid
msgid "Created by"
msgstr "Sukūrė"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__create_date
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__create_date
msgid "Created on"
msgstr "Sukurta"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__currency_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order__currency_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__currency_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__currency_id
msgid "Currency"
msgstr "Valiuta"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__currency_rate
msgid "Currency Rate"
msgstr "Valiutos kursas"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__access_url
msgid "Customer Portal URL"
msgstr "Klientų portalo adresas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__date
msgid "Date"
msgstr "Data"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__date_calendar_start
msgid "Date Calendar Start"
msgstr ""

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "Date Updated"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Date:"
msgstr "Data:"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/xml/purchase_dashboard.xml:0
#, python-format
msgid "Days"
msgstr "Dienos"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__reminder_date_before_receipt
#: model:ir.model.fields,field_description:purchase.field_res_partner__reminder_date_before_receipt
#: model:ir.model.fields,field_description:purchase.field_res_users__reminder_date_before_receipt
msgid "Days Before Receipt"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__delay
msgid "Days to Confirm"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__delay_pass
msgid "Days to Receive"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Define your terms and conditions ..."
msgstr "Apibrėžkite savo sąlygas..."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__date_planned
msgid "Delivery Date"
msgstr "Pristatymo data"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_line__date_planned
msgid ""
"Delivery date expected from vendor. This date respectively defaults to "
"vendor pricelist lead time then today's date."
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__date_planned
msgid ""
"Delivery date promised by vendor. This date is used to determine expected "
"arrival of products."
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_report__date_order
msgid ""
"Depicts the date when the Quotation should be validated and converted into a"
" purchase order."
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__date_order
#: model:ir.model.fields,help:purchase.field_purchase_order_line__date_order
msgid ""
"Depicts the date within which the Quotation should be confirmed and "
"converted into a purchase order."
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__name
msgid "Description"
msgstr "Aprašymas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__display_name
#: model:ir.model.fields,field_description:purchase.field_purchase_order__display_name
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__display_name
#: model:ir.model.fields,field_description:purchase.field_purchase_report__display_name
msgid "Display Name"
msgstr "Rodomas pavadinimas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__display_type
msgid "Display Type"
msgstr "Ekrano tipas"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Documentation"
msgstr "Dokumentacija"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__done
msgid "Done"
msgstr "Atlikta"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company__po_double_validation_amount
msgid "Double validation amount"
msgstr "Dvigumo patvirtinimo kiekis"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Download"
msgstr "Atsisiųsti"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__draft
msgid "Draft RFQ"
msgstr "Pirkimo užklausos juodraštis"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Draft RFQs"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__dest_address_id
msgid "Dropship Address"
msgstr "Trikampės Prekybos Pristatymo Adresas"

#. module: purchase
#: model:ir.model,name:purchase.model_mail_compose_message
msgid "Email composition wizard"
msgstr "El. laiško kūrimo vedlys"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Extended Filters"
msgstr "Išplėstiniai filtrai"

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "Extra line with %s "
msgstr "Papildoma eilutė su %s "

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__fiscal_position_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__fiscal_position_id
msgid "Fiscal Position"
msgstr "Mokestinė aplinka"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_follower_ids
msgid "Followers"
msgstr "Sekėjai"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_partner_ids
msgid "Followers (Partners)"
msgstr "Sekėjai (partneriai)"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome piktograma, pvz., fa-tasks"

#. module: purchase
#: model:ir.model.constraint,message:purchase.constraint_purchase_order_line_non_accountable_null_fields
msgid "Forbidden values on non-accountable purchase order line"
msgstr ""

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__invoice_status__invoiced
msgid "Fully Billed"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Future Activities"
msgstr "Būsimos veiklos"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_company__po_double_validation__two_step
msgid "Get 2 levels of approvals to confirm a purchase order"
msgstr "Gauti 2 lygių patikrinimus užsakymo patvirtinimui"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Get warnings in orders for products or vendors"
msgstr "Užsakymuose gauti perspėjimus dėl produktų ar tiekėjų"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__weight
msgid "Gross Weight"
msgstr "Bruto svoris"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Group By"
msgstr "Grupuoti pagal"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__has_message
msgid "Has Message"
msgstr "Turi žinutę"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
msgid "Hide cancelled lines"
msgstr "Paslėpti atšauktas eilutes"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "History"
msgstr "Istorija"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__id
#: model:ir.model.fields,field_description:purchase.field_purchase_order__id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__id
msgid "ID"
msgstr "ID"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_exception_icon
msgid "Icon"
msgstr "Piktograma"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Išimties veiklą žyminti piktograma."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__message_needaction
#: model:ir.model.fields,help:purchase.field_purchase_order__message_unread
msgid "If checked, new messages require your attention."
msgstr "Jeigu pažymėta, naujiems pranešimams reikės jūsų dėmesio."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__message_has_error
#: model:ir.model.fields,help:purchase.field_purchase_order__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Jei pažymėta, yra žinučių, turinčių pristatymo klaidų."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"If enabled, activates 3-way matching on vendor bills : the items must be "
"received in order to pay the invoice."
msgstr ""
"Jei įjungta, aktyvuoja 3 dalių sutapimą tiekėtjų sąskaitose: prekės turi "
"būti gautos, kad būtų galima apmokėti sąskaitą."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"If installed, the product variants will be added to purchase orders through "
"a grid entry."
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_packaging__purchase
msgid "If true, the packaging can be used for purchase orders"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.mail_notification_confirm
msgid "If you have any questions, please do not hesitate to contact us."
msgstr ""

#. module: purchase
#: code:addons/purchase/models/product.py:0
#, python-format
msgid "Import Template for Products"
msgstr "Importuoti produktų šabloną"

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "In order to delete a purchase order, you must cancel it first."
msgstr ""
"Jei norite ištrinti savo pirkimo užsakymą, pirmiausia turite jį atšaukti."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__incoterm_id
msgid "Incoterm"
msgstr "Incoterm sąlygos"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/js/tours/purchase.js:0
#, python-format
msgid "Indicate the product quantity you want to order."
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__incoterm_id
msgid ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."
msgstr ""
"Tarptautinės prekybos (sutarčių) sąlygos, numatančios specifines prekių "
"pristatymo sąlygas užsienio prekyboje."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Invoices and Incoming Shipments"
msgstr "Sąskaitos faktūros ir atvykstančios siuntos"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Invoicing"
msgstr "Apskaita"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_is_follower
msgid "Is Follower"
msgstr "Yra sekėjas"

#. module: purchase
#: model:ir.model,name:purchase.model_account_move
msgid "Journal Entry"
msgstr "Žurnalo įrašas"

#. module: purchase
#: model:ir.model,name:purchase.model_account_move_line
msgid "Journal Item"
msgstr "Žurnalo įrašas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union____last_update
#: model:ir.model.fields,field_description:purchase.field_purchase_order____last_update
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line____last_update
#: model:ir.model.fields,field_description:purchase.field_purchase_report____last_update
msgid "Last Modified on"
msgstr "Paskutinį kartą keista"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__write_uid
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__write_uid
msgid "Last Updated by"
msgstr "Paskutinį kartą atnaujino"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__write_date
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__write_date
msgid "Last Updated on"
msgstr "Paskutinį kartą atnaujinta"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/xml/purchase_dashboard.xml:0
#, python-format
msgid "Late"
msgstr "Vėluoja"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Late Activities"
msgstr "Vėluojančios veiklos"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Late RFQs"
msgstr ""

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/xml/purchase_dashboard.xml:0
#, python-format
msgid "Lead Time to Purchase"
msgstr "Pristatymo laikas pirkimams"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/js/tours/purchase.js:0
#, python-format
msgid "Let's create your first request for quotation."
msgstr ""

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/js/tours/purchase.js:0
#: code:addons/purchase/static/src/js/tours/purchase.js:0
#, python-format
msgid ""
"Let's try the Purchase app to manage the flow from purchase to reception and"
" invoice control."
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company__po_double_validation
msgid "Levels of Approvals"
msgstr "Patvirtinimų lygiai"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__po_double_validation
msgid "Levels of Approvals *"
msgstr "Patvirtinimų lygiai *"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Lock"
msgstr "Užrakinti"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__lock_confirmed_po
msgid "Lock Confirmed Orders"
msgstr "Užrakinti patvirtintus užsakymus"

#. module: purchase
#: code:addons/purchase/controllers/portal.py:0
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__done
#, python-format
msgid "Locked"
msgstr "Užrakinta"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_main_attachment_id
msgid "Main Attachment"
msgstr "Pagrindinis prisegtukas"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"Make sure you only pay bills for which you received the goods you ordered"
msgstr ""
"Įsitikinkite, kad apmokate tik tas sąskaitas, už kurias gavote užsakytas "
"prekes"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Manage your purchase agreements (call for tenders, blanket orders)"
msgstr ""
"Valdykite savo pirkimo susitarimus (ilgalaikiai užsakymai, pasiūlymų "
"kvietimas)"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order_line__qty_received_method__manual
msgid "Manual"
msgstr "Rankinis"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_form2
msgid "Manual Invoices"
msgstr "Rankiniu būdu įvedamos sąskaitos faktūros"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__qty_received_manual
msgid "Manual Received Qty"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company__po_lead
#: model:ir.model.fields,help:purchase.field_res_config_settings__po_lead
msgid ""
"Margin of error for vendor lead times. When the system generates Purchase "
"Orders for procuring products, they will be scheduled that many days earlier"
" to cope with unexpected vendor delays."
msgstr ""
"Tiekėjų laiko paklaida. Kai sistema sugeneruoja pirkimo užsakymus "
"įsigyjamiems produktams, jie bus suplanuoti tiek dienų anksčiau, kad būtų "
"išvengta neplanuotų tiekėjų vėlavimų."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_config_settings__use_po_lead
msgid ""
"Margin of error for vendor lead times. When the system generates Purchase "
"Orders for reordering products,they will be scheduled that many days earlier"
" to cope with unexpected vendor delays."
msgstr ""
"Tiekėjų laiko paklaida. Kai sistema sugeneruoja pirkimo užsakymus "
"pakartotinai užsakomiems produktams, jie bus suplanuoti tiek dienų anksčiau,"
" kad būtų išvengta neplanuotų tiekėjų vėlavimų."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_has_error
msgid "Message Delivery error"
msgstr "Žinutės pristatymo klaida"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_partner__purchase_warn_msg
#: model:ir.model.fields,field_description:purchase.field_res_users__purchase_warn_msg
msgid "Message for Purchase Order"
msgstr "Žinutė pirkimo užsakymui"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product__purchase_line_warn_msg
#: model:ir.model.fields,field_description:purchase.field_product_template__purchase_line_warn_msg
msgid "Message for Purchase Order Line"
msgstr "Žinutė pardavimų užsakymo eilutei"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_ids
msgid "Messages"
msgstr "Žinutės"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__po_double_validation_amount
msgid "Minimum Amount"
msgstr "Minimalus kiekis"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company__po_double_validation_amount
#: model:ir.model.fields,help:purchase.field_res_config_settings__po_double_validation_amount
msgid "Minimum amount for which a double validation is required"
msgstr "Minimalus kiekis, kuriam reikalingas dvigubas patvirtinimas"

#. module: purchase
#: model:ir.model.constraint,message:purchase.constraint_purchase_order_line_accountable_required_fields
msgid "Missing required fields on accountable purchase order line."
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Veiklos paskutinis terminas"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/xml/purchase_dashboard.xml:0
#, python-format
msgid "My Draft RFQs"
msgstr ""

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/xml/purchase_dashboard.xml:0
#, python-format
msgid "My Late RFQs"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
msgid "My Orders"
msgstr "Mano užsakymai"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "My Purchases"
msgstr "Mano pirkimai"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/xml/purchase_dashboard.xml:0
#, python-format
msgid "My RFQs"
msgstr ""

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/xml/purchase_dashboard.xml:0
#, python-format
msgid "My Waiting RFQs"
msgstr ""

#. module: purchase
#: code:addons/purchase/controllers/portal.py:0
#, python-format
msgid "Name"
msgstr "Pavadinimas"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Name, TIN, Email, or Reference"
msgstr "Pavadinimas, mokėtojo kodas, el. paštas arba numeris"

#. module: purchase
#: code:addons/purchase/controllers/portal.py:0
#, python-format
msgid "Newest"
msgstr "Naujausia"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Kitas veiklos kalendoriaus įvykis"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Kito veiksmo terminas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_summary
msgid "Next Activity Summary"
msgstr "Kito veiksmo santrauka"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_type_id
msgid "Next Activity Type"
msgstr "Kito veiksmo tipas"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__product_template__purchase_line_warn__no-message
#: model:ir.model.fields.selection,name:purchase.selection__res_partner__purchase_warn__no-message
msgid "No Message"
msgstr "Žinučių nėra"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_action_dashboard_kanban
#: model_terms:ir.actions.act_window,help:purchase.purchase_action_dashboard_list
msgid "No RFQs to display"
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_normal_action_puchased
msgid "No product found. Let's create one!"
msgstr "Nerastas joks produktas. Sukurkite naują!"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_form_action
msgid "No purchase order found. Let's create one!"
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_rfq
msgid "No request for quotation found. Let's create one!"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.mail_notification_confirm
msgid "No, Update Dates"
msgstr ""

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__priority__0
msgid "Normal"
msgstr "Įprastas"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
msgid "Not Acknowledged"
msgstr ""

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order_line__display_type__line_note
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Note"
msgstr "Pastaba"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Notes"
msgstr "Pastabos"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__invoice_status__no
msgid "Nothing to Bill"
msgstr "Nėra už ką išrašyti sąskaitą"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_needaction_counter
msgid "Number of Actions"
msgstr "Veiksmų skaičius"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__reminder_date_before_receipt
#: model:ir.model.fields,help:purchase.field_res_partner__reminder_date_before_receipt
#: model:ir.model.fields,help:purchase.field_res_users__reminder_date_before_receipt
msgid "Number of days to send reminder email before the promised receipt date"
msgstr ""
"Dienų sk. prieš prieš susitartą pristatymo datą, kada tiekėjui bus išsiųstas"
" priminimo laiškas "

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_has_error_counter
msgid "Number of errors"
msgstr "Klaidų kiekis"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Žinučių, kurioms reikia jūsų veiksmo, skaičius"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Žinučių su pristatymo klaida skaičius"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__message_unread_counter
msgid "Number of unread messages"
msgstr "Neperskaitytų žinučių skaičius"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__product_template__purchase_method__purchase
msgid "On ordered quantities"
msgstr "Užsakytiems kiekiams"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_product__purchase_method
#: model:ir.model.fields,help:purchase.field_product_template__purchase_method
msgid ""
"On ordered quantities: Control bills based on ordered quantities.\n"
"On received quantities: Control bills based on received quantities."
msgstr ""
"Užsakytiems kiekiams: valdykite sąskaitas pagal užsakytus kiekius.\n"
"Gautiems kiekiams: valdykite sąskaitas pagal gautus kiekius."

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__product_template__purchase_method__receive
msgid "On received quantities"
msgstr "Gautiems kiekiams"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/js/tours/purchase.js:0
#, python-format
msgid ""
"Once you get the price from the vendor, you can complete the purchase order "
"with the right price."
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_form_action
msgid ""
"Once you ordered your products to your supplier, confirm your request for "
"quotation and it will turn into a purchase order."
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__order_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Order"
msgstr "Užsakymas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__date_order
#: model:ir.model.fields,field_description:purchase.field_purchase_report__date_order
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Order Date"
msgstr "Užsakymo data"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__date_order
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
msgid "Order Deadline"
msgstr "Užsakymo galiojimo paskutinė data"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__order_line
msgid "Order Lines"
msgstr "Užsakymo eilutės"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__name
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__order_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
msgid "Order Reference"
msgstr "Užsakymo numeris"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "Ordered Quantity:"
msgstr "Užsakytas kiekis"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_config_settings__default_purchase_method__purchase
msgid "Ordered quantities"
msgstr "Užsakyti kiekiai"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_procurement_management
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Orders"
msgstr "Užsakymai"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Other Information"
msgstr "Kita informacija"

#. module: purchase
#: model:mail.template,report_name:purchase.email_template_edi_purchase_done
#: model:mail.template,report_name:purchase.email_template_edi_purchase_reminder
msgid "PO_{{ (object.name or '').replace('/','_') }}"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_packaging_id
msgid "Packaging"
msgstr "Pakavimas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_packaging_qty
msgid "Packaging Quantity"
msgstr "Pakuočių kiekis"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__partner_id
msgid "Partner"
msgstr "Partneris"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__country_id
msgid "Partner Country"
msgstr "Partnerio šalis"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__payment_term_id
msgid "Payment Terms"
msgstr "Mokėjimo sąlygos"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Payment terms"
msgstr "Mokėjimo sąlygos"

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "Please define an accounting purchase journal for the company %s (%s)."
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__access_url
msgid "Portal Access URL"
msgstr "Portalo prieigos nuoroda"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Preview the reminder email by sending it to yourself."
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_category__property_account_creditor_price_difference_categ
#: model:ir.model.fields,field_description:purchase.field_product_product__property_account_creditor_price_difference
#: model:ir.model.fields,field_description:purchase.field_product_template__property_account_creditor_price_difference
msgid "Price Difference Account"
msgstr "Kainų skirtumo sąskaita"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Pricing"
msgstr "Kainodara"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Print"
msgstr "Spausdinti"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Print RFQ"
msgstr "Spausdinti pirkimo užklausą"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__priority
msgid "Priority"
msgstr "Svarba"

#. module: purchase
#: model:ir.model,name:purchase.model_product_product
#: model:ir.model.fields,field_description:purchase.field_purchase_order__product_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__product_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Product"
msgstr "Produktas"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_product_attribute_action
msgid "Product Attributes"
msgstr "Produkto atributai"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_product_category_config_purchase
msgid "Product Categories"
msgstr "Produktų kategorijos"

#. module: purchase
#: model:ir.model,name:purchase.model_product_category
#: model:ir.model.fields,field_description:purchase.field_purchase_report__category_id
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Product Category"
msgstr "Produkto kategorija"

#. module: purchase
#: model:ir.model,name:purchase.model_product_packaging
msgid "Product Packaging"
msgstr "Produkto pakavimas"

#. module: purchase
#: model:ir.model,name:purchase.model_product_template
#: model:ir.model.fields,field_description:purchase.field_purchase_report__product_tmpl_id
msgid "Product Template"
msgstr "Produkto šablonas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_type
msgid "Product Type"
msgstr "Produkto tipas"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.product_product_action
#: model:ir.ui.menu,name:purchase.product_product_menu
msgid "Product Variants"
msgstr "Produkto variantai"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.product_normal_action_puchased
#: model:ir.ui.menu,name:purchase.menu_procurement_partner_contact_form
#: model:ir.ui.menu,name:purchase.menu_product_in_config_purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_products
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Products"
msgstr "Produktai"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company__po_double_validation
#: model:ir.model.fields,help:purchase.field_res_config_settings__po_double_validation
msgid "Provide a double validation mechanism for purchases"
msgstr "Pateikite dvigubo patvirtinimo mechanizmą pirkimams"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_packaging__purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_root
#: model:ir.ui.menu,name:purchase.purchase_report
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Purchase"
msgstr "Pirkimas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__module_purchase_requisition
msgid "Purchase Agreements"
msgstr "Pirkimo susitarimai"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_purchase_order_report_all
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_graph
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_pivot
msgid "Purchase Analysis"
msgstr "Pirkimų analizė"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.action_purchase_order_report_all
msgid ""
"Purchase Analysis allows you to easily check and analyse your company "
"purchase history and performance. From this menu you can track your "
"negotiation performance, the delivery performance of your vendors, etc."
msgstr ""
"Pirkimų analizė leidžia jums lengvai patikrinti ir analizuoti jūsų "
"kompanijos pirkimo istoriją ir planų įvykdymą. Iš šio meniu galite sekti "
"savo derybų sėkmę, jūsų tiekėjų pristatymo sėkmę ir kt."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_supplier_inherit
msgid "Purchase Description"
msgstr "Pirkimo aprašymas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__module_purchase_product_matrix
msgid "Purchase Grid Entry"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company__po_lead
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__po_lead
msgid "Purchase Lead Time"
msgstr "Pirkimo terminas"

#. module: purchase
#: code:addons/purchase/controllers/portal.py:0
#: code:addons/purchase/models/purchase.py:0
#: code:addons/purchase/models/purchase.py:0
#: model:ir.actions.report,name:purchase.action_report_purchase_order
#: model:ir.model,name:purchase.model_purchase_order
#: model:ir.model.fields,field_description:purchase.field_account_bank_statement_line__purchase_id
#: model:ir.model.fields,field_description:purchase.field_account_move__purchase_id
#: model:ir.model.fields,field_description:purchase.field_account_move_line__purchase_order_id
#: model:ir.model.fields,field_description:purchase.field_account_payment__purchase_id
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__purchase_order_id
#: model:ir.model.fields,field_description:purchase.field_res_partner__purchase_warn
#: model:ir.model.fields,field_description:purchase.field_res_users__purchase_warn
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_graph
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_pivot
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_activity
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
#, python-format
msgid "Purchase Order"
msgstr "Pirkimo užsakymas"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "Purchase Order #"
msgstr "Pirkimo užsakymo nr."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__po_order_approval
msgid "Purchase Order Approval"
msgstr "Pirkimo užsakymų patvirtinimas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_account_analytic_account__purchase_order_count
#: model:ir.model.fields,field_description:purchase.field_res_partner__purchase_order_count
#: model:ir.model.fields,field_description:purchase.field_res_users__purchase_order_count
msgid "Purchase Order Count"
msgstr "Pirkimo užsakymų skaičius"

#. module: purchase
#: model:ir.model,name:purchase.model_purchase_order_line
#: model:ir.model.fields,field_description:purchase.field_account_move_line__purchase_line_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_form2
msgid "Purchase Order Line"
msgstr "Pirkimo užsakymo eilutė "

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product__purchase_line_warn
#: model:ir.model.fields,field_description:purchase.field_product_template__purchase_line_warn
msgid "Purchase Order Line Warning"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_tree
msgid "Purchase Order Lines"
msgstr "Pirkimo užsakymo eilutės"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company__po_lock
msgid "Purchase Order Modification"
msgstr "Pirkimo užsakymų modifikavimas "

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__po_lock
msgid "Purchase Order Modification *"
msgstr "Pirkimo užsakymų modifikavimas *"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company__po_lock
#: model:ir.model.fields,help:purchase.field_res_config_settings__po_lock
msgid ""
"Purchase Order Modification used when you want to purchase order editable "
"after confirm"
msgstr ""
"Pirkimo užsakymo modifikacija yra naudojama tada, kai norite, kad pirkimo "
"užsakymai liktų redaguojami po patvirtinimo"

#. module: purchase
#: model:mail.template,name:purchase.email_template_edi_purchase_done
msgid "Purchase Order: Send PO"
msgstr ""

#. module: purchase
#: model:mail.template,name:purchase.email_template_edi_purchase
msgid "Purchase Order: Send RFQ"
msgstr ""

#. module: purchase
#: model:mail.template,name:purchase.email_template_edi_purchase_reminder
msgid "Purchase Order: Vendor Reminder"
msgstr ""

#. module: purchase
#: code:addons/purchase/models/analytic_account.py:0
#: model:ir.actions.act_window,name:purchase.purchase_form_action
#: model:ir.ui.menu,name:purchase.menu_purchase_form_action
#: model_terms:ir.ui.view,arch_db:purchase.account_analytic_account_view_form_purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_home_menu_purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_home_purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_bill_union_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
#, python-format
msgid "Purchase Orders"
msgstr "Pirkimų užsakymai"

#. module: purchase
#: model:ir.model,name:purchase.model_purchase_report
msgid "Purchase Report"
msgstr "Pirkimo ataskaita"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__user_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__user_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Purchase Representative"
msgstr "Pirkimų atstovas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__group_warning_purchase
msgid "Purchase Warnings"
msgstr "Pirkimo įspėjimai"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
msgid "Purchase orders that have been invoiced."
msgstr "Pirkimo užsakymai, kuriems buvo išrašytas važtaraštis."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
msgid "Purchase orders that include lines not invoiced."
msgstr ""
"Pirkimo užsakymai, kurie turi eilučių, už kurias nebuvo pateiktos sąskaitos."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Purchase products by multiple of unit # per package"
msgstr ""

#. module: purchase
#: model:ir.actions.server,name:purchase.purchase_send_reminder_mail_ir_actions_server
#: model:ir.cron,cron_name:purchase.purchase_send_reminder_mail
#: model:ir.cron,name:purchase.purchase_send_reminder_mail
msgid "Purchase reminder"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Purchase variants of a product using attributes (size, color, etc.)"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product__purchased_product_qty
#: model:ir.model.fields,field_description:purchase.field_product_template__purchased_product_qty
msgid "Purchased"
msgstr "Nupirkta"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/xml/purchase_dashboard.xml:0
#, python-format
msgid "Purchased Last 7 Days ("
msgstr "Per pask. 7 dienas nupirkta ("

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.product_normal_form_view_inherit_purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_template_purchase_buttons_from
msgid "Purchased in the last 365 days"
msgstr "Nupirkta per paskutines 365 dienas"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_partner_view_purchase_buttons
msgid "Purchases"
msgstr "Pirkimai"

#. module: purchase
#: model:ir.model,name:purchase.model_purchase_bill_union
msgid "Purchases & Bills Union"
msgstr "Pirkimai ir sąskaitų junginiai"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__dest_address_id
msgid ""
"Put an address if you want to deliver directly from the vendor to the "
"customer. Otherwise, keep empty to deliver to your own company."
msgstr ""
"Įveskite adresą, jei norite pristatyti tiesiogiai iš tiekėjo klientui. Jei "
"norite, kad pristatytų į jūsų įmonę, palikite tuščia."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__qty_billed
msgid "Qty Billed"
msgstr "Fakt. kiekis"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__qty_ordered
msgid "Qty Ordered"
msgstr "Užsakytas kiekis"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__qty_received
msgid "Qty Received"
msgstr "Gautas kiekis"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__qty_to_be_billed
msgid "Qty to be Billed"
msgstr "Nefakt. kiekis"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Quantities billed by vendors"
msgstr "Kiekiai, už kuriuos tiekėjai pateikė sąskaitas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_qty
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Quantity"
msgstr "Kiekis"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Quantity:"
msgstr "Kiekis:"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__draft
msgid "RFQ"
msgstr "Pirkimo užklausa"

#. module: purchase
#: model:mail.message.subtype,name:purchase.mt_rfq_approved
msgid "RFQ Approved"
msgstr "Pirkimo užklausa patvirtinta"

#. module: purchase
#: model:mail.message.subtype,name:purchase.mt_rfq_confirmed
msgid "RFQ Confirmed"
msgstr "Pirkimo užklausa patvirtinta"

#. module: purchase
#: model:mail.message.subtype,name:purchase.mt_rfq_done
msgid "RFQ Done"
msgstr "Pirkimo užklausa atlikta"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__sent
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__sent
msgid "RFQ Sent"
msgstr "Pirkimo užklausa išsiųsta"

#. module: purchase
#: model:mail.template,report_name:purchase.email_template_edi_purchase
msgid "RFQ_{{ (object.name or '').replace('/','_') }}"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "RFQs"
msgstr "Pirkimo užklausos"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/xml/purchase_dashboard.xml:0
#, python-format
msgid "RFQs Sent Last 7 Days"
msgstr "Per pask. 7 dienas išsiųstos pirkimo užklausos"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.act_res_partner_2_purchase_order
msgid "RFQs and Purchases"
msgstr "Pirkimo užklausos ir pirkimai"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__currency_rate
msgid "Ratio between the purchase order currency and the company currency"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Re-Send by Email"
msgstr "Persiųsti el. paštu"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__date_planned
msgid "Receipt Date"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__group_send_reminder
#: model:ir.model.fields,field_description:purchase.field_res_partner__receipt_reminder_email
#: model:ir.model.fields,field_description:purchase.field_res_users__receipt_reminder_email
msgid "Receipt Reminder"
msgstr "Pristatymo priminimas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__receipt_reminder_email
msgid "Receipt Reminder Email"
msgstr "Pristatymo priminimo el. laiškas"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Received"
msgstr "Gauta"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__qty_received
msgid "Received Qty"
msgstr "Gautas kiekis"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__qty_received_method
msgid "Received Qty Method"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Received Quantity"
msgstr "Gautas kiekis"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_qty_received_template
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "Received Quantity:"
msgstr "Gautas kiekis:"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__res_config_settings__default_purchase_method__receive
msgid "Received quantities"
msgstr "Gautas kiekis"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__mail_reception_confirmed
msgid "Reception Confirmed"
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_supplier_invoices
msgid "Record a new vendor bill"
msgstr "Įrašyti naują tiekėjo sąskaitą"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__name
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_bill_union_filter
msgid "Reference"
msgstr "Numeris"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_bill_union_tree
msgid "Reference Document"
msgstr "Nuorodos dokumentas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__product_uom
msgid "Reference Unit of Measure"
msgstr "Etaloninis matavimo vienetas"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__origin
msgid ""
"Reference of the document that generated this purchase order request (e.g. a"
" sales order)"
msgstr ""
"Dokumento numeris, pagal kurį sugeneruotas šis pirkimo užsakymas (pvz. "
"pardavimo užsakymas)."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__partner_ref
msgid ""
"Reference of the sales order or bid sent by the vendor. It's used to do the "
"matching when you receive the products as this reference is usually written "
"on the delivery order sent by your vendor."
msgstr ""
"Pardavimo užsakymo arba pasiūlymo numeris, atsiųstas tiekėjo. Jis naudojamas"
" susiejimu, kai gaunate produktus. Šis numeris įprastai užrašomas ant "
"tiekėjo atsiųsto pristatymo užsakymo."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__mail_reminder_confirmed
msgid "Reminder Confirmed"
msgstr "Priminimas patvirtintas"

#. module: purchase
#: model:ir.ui.menu,name:purchase.purchase_report_main
msgid "Reporting"
msgstr "Ataskaitos"

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#: model:ir.actions.report,name:purchase.report_purchase_quotation
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
#, python-format
msgid "Request for Quotation"
msgstr "Užklausos prašymas"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "Request for Quotation #"
msgstr "Komercinio pasiūlymo užklausos nr."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Request managers to approve orders above a minimum amount"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
msgid "Requests For Quotation"
msgstr "Pirkimo užklausa"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_rfq_form
#: model:ir.actions.act_window,name:purchase.purchase_action_dashboard_kanban
#: model:ir.actions.act_window,name:purchase.purchase_action_dashboard_list
#: model:ir.actions.act_window,name:purchase.purchase_rfq
#: model:ir.ui.menu,name:purchase.menu_purchase_rfq
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_home_menu_purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_home_purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Requests for Quotation"
msgstr "Komercinio pasiūlymo užklausos"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_rfq
msgid ""
"Requests for quotation are documents that will be sent to your suppliers to request prices for different products you consider buying.\n"
"                Once an agreement has been found with the supplier, they will be confirmed and turned into purchase orders."
msgstr ""
"Pirkimo užklausos yra dokumentai, kuriuos siųsime tiekėjams, siekiant užklausti įvairių produktų, kuriuos ketinate pirkti, kainas.\n"
"Sudarius susitarimą su tiekėju, jis bus patvirtintas ir vėliau paverstas pirkimo užsakymais."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__activity_user_id
msgid "Responsible User"
msgstr "Atsakingas vartotojas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS pristatymo klaida"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Scheduled Date"
msgstr "Suplanuota data"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Search Purchase Order"
msgstr "Ieškoti pirkimo užsakymo "

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_bill_union_filter
msgid "Search Reference Document"
msgstr "Ieškoti nuorodos dokumento"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/js/tours/purchase.js:0
#, python-format
msgid "Search a vendor name, or create one on the fly."
msgstr "Surasti tiekėjo pavadinimą arba iškart sukurti naują."

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order_line__display_type__line_section
msgid "Section"
msgstr "Sekcija"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Section Name (eg. Products, Services)"
msgstr "Sekcijos pavadinimas (pvz., produktai, paslaugos)"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings__use_po_lead
msgid "Security Lead Time for Purchase"
msgstr "Saugus gaminimo laikas pirkimams"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__access_token
msgid "Security Token"
msgstr "Apsaugos prieigos raktas"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/js/tours/purchase.js:0
#, python-format
msgid "Select a product, or create a new one on the fly."
msgstr "Pasirinkite produktą arba pridėkite naują eigoje."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_move_form_inherit_purchase
msgid "Select a purchase order or an old bill"
msgstr "Pasirinkite pirkimo užsakymą arba seną sąskaitą"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_product__purchase_line_warn
#: model:ir.model.fields,help:purchase.field_product_template__purchase_line_warn
#: model:ir.model.fields,help:purchase.field_res_partner__purchase_warn
#: model:ir.model.fields,help:purchase.field_res_users__purchase_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"Pasirinkus 'įspėjimo' opciją, vartotojas gaus perspėjimą su žinute, "
"pasirinkus 'blokuojanti žinutė' bus parodoma klaida su žinute ir blokuojami "
"tolimesni žingsniai. Žinutė turi būti įvesta kitame laukelyje. "

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Send PO by Email"
msgstr "Siųsti PU el. paštu "

#. module: purchase
#: model:ir.actions.server,name:purchase.action_purchase_send_reminder
msgid "Send Reminder"
msgstr "Siųsti priminimą"

#. module: purchase
#: model:res.groups,name:purchase.group_send_reminder
msgid "Send an automatic reminder email to confirm delivery"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Send by Email"
msgstr "Siųsti el. paštu"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/js/tours/purchase.js:0
#: code:addons/purchase/static/src/js/tours/purchase.js:0
#, python-format
msgid "Send the request for quotation to your vendor."
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__sequence
msgid "Sequence"
msgstr "Seka"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Set to Draft"
msgstr "Nustatyti kaip juodraštį"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_purchase_configuration
#: model:ir.ui.menu,name:purchase.menu_purchase_general_settings
msgid "Settings"
msgstr "Nustatymai"

#. module: purchase
#: model:ir.actions.server,name:purchase.model_purchase_order_action_share
msgid "Share"
msgstr "Dalintis"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Show all records which has next action date is before today"
msgstr ""
"Rodyti visus įrašus, kurių sekančio veiksmo data yra ankstesnė nei šiandiena"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__reference
msgid "Source"
msgstr "Šaltinis"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__origin
msgid "Source Document"
msgstr "Šaltinio dokumentas"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Starred"
msgstr "Pažymėtas žvaigždute"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__state
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__state
#: model:ir.model.fields,field_description:purchase.field_purchase_report__state
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Status"
msgstr "Būsena"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Būsena, paremta veiklomis\n"
"Vėluojantis: Termino data jau praėjo\n"
"Šiandien: Veikla turi būti baigta šiandien\n"
"Suplanuotas: Ateities veiklos."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__price_subtotal
msgid "Subtotal"
msgstr "Tarpinė suma"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
msgid "Sum of Qty Billed"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
msgid "Sum of Qty Ordered"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
msgid "Sum of Qty Received"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
msgid "Sum of Total"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_report_view_tree
msgid "Sum of Untaxed Total"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_partner__property_purchase_currency_id
#: model:ir.model.fields,field_description:purchase.field_res_users__property_purchase_currency_id
msgid "Supplier Currency"
msgstr "Tiekėjo valiuta "

#. module: purchase
#: model:ir.model,name:purchase.model_product_supplierinfo
msgid "Supplier Pricelist"
msgstr "Tiekėjo kainoraštis"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__price_tax
msgid "Tax"
msgstr "Mokesčiai"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__tax_country_id
msgid "Tax Country"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__tax_totals_json
msgid "Tax Totals Json"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__amount_tax
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__taxes_id
msgid "Taxes"
msgstr "Mokesčiai"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_line__display_type
msgid "Technical field for UX purpose."
msgstr "Techninis laukas UX reikmėms."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__tax_country_id
msgid ""
"Technical field to filter the available taxes depending on the fiscal "
"country and fiscal position."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Terms &amp; Conditions"
msgstr "Sąlygos"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__notes
msgid "Terms and Conditions"
msgstr "Sąlygos"

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "The order receipt has been acknowledged by %s."
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_purchase_order
msgid ""
"The request for quotation is the first step of the purchases flow. Once\n"
"                    converted into a purchase order, you will be able to control the receipt\n"
"                    of the products and the vendor bill."
msgstr ""
"Komercinio pasiūlymo užklausa yra pirmas žingsnis pirkimo procese.\n"
"Kai jis paverčiamas į pirkimo užsakymą, galite valdyti produktų\n"
"kvitus ir tiekėjo mokėjimą."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid "There are currently no purchase orders for your account."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
msgid "There are currently no requests for quotation for your account."
msgstr ""

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid ""
"There is no invoiceable line. If a product has a control policy based on "
"received quantity, please make sure that a quantity has been received."
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_product__property_account_creditor_price_difference
#: model:ir.model.fields,help:purchase.field_product_template__property_account_creditor_price_difference
msgid ""
"This account is used in automated inventory valuation to record the price "
"difference between a purchase order and its related vendor bill when "
"validating this vendor bill."
msgstr ""
"Ši sąskaita naudojama automatiniame inventoriaus vertinime, kad būtų "
"įrašytas kainų skirtumas tarp pirkimo užsakymo ir su juo susijusios tiekėjo "
"kainos, kai patvirtinama ši tiekėjo sąskaita."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_category__property_account_creditor_price_difference_categ
msgid ""
"This account will be used to value price difference between purchase price "
"and accounting cost."
msgstr ""
"Ši sąskaita bus naudojama įvertinti kainų skirtumus tarp pirkimo kainos ir "
"apskaitos kaštų."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_partner__property_purchase_currency_id
#: model:ir.model.fields,help:purchase.field_res_users__property_purchase_currency_id
msgid ""
"This currency will be used, instead of the default one, for purchases from "
"the current partner"
msgstr ""
"Pirkimams iš esamo partnerio vietoje numatytosios valiutos bus naudojama ši"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_config_settings__default_purchase_method
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"This default value is applied to any new product created. This can be "
"changed in the product detail form."
msgstr ""
"Ši numatytoji vertė yra pritaikoma bet kuriam naujai sukurtam produktui. Tai"
" galite pakeisti produkto informacijos formoje."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_supplier_inherit
msgid "This note is added to purchase orders."
msgstr "Šis aprašas yra pridedamas į pirkimo užsakymus."

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid ""
"This product is packaged by %(pack_size).2f %(pack_name)s. You should "
"purchase %(quantity).2f %(unit)s."
msgstr ""

#. module: purchase
#: code:addons/purchase/models/account_invoice.py:0
#, python-format
msgid "This vendor bill has been created from: %s"
msgstr "Ši tiekėjo sąskaita buvo sukurta iš: %s"

#. module: purchase
#: code:addons/purchase/models/account_invoice.py:0
#, python-format
msgid "This vendor bill has been modified from: %s"
msgstr "Ši tiekėjo sąskaita buvo modifikuota iš: %s"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_purchase_order
msgid "This vendor has no purchase order. Create a new RfQ"
msgstr ""
"Šis tiekėjas neturi pirkimo užsakymo. Sukurkite naują pirkimo užklausą"

#. module: purchase
#: model:digest.tip,name:purchase.digest_tip_purchase_0
#: model_terms:digest.tip,tip_description:purchase.digest_tip_purchase_0
msgid "Tip: How to keep late receipts under control?"
msgstr ""

#. module: purchase
#: model:digest.tip,name:purchase.digest_tip_purchase_1
#: model_terms:digest.tip,tip_description:purchase.digest_tip_purchase_1
msgid "Tip: Never miss a purchase order"
msgstr ""

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__state__to_approve
#: model:ir.model.fields.selection,name:purchase.selection__purchase_report__state__to_approve
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "To Approve"
msgstr "Reikia patvirtinti"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__qty_to_invoice
msgid "To Invoice Quantity"
msgstr "Pateikti sąskaita už kiekį"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/xml/purchase_dashboard.xml:0
#, python-format
msgid "To Send"
msgstr "Išsiųsti"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Today Activities"
msgstr "Šiandienos veiklos"

#. module: purchase
#: code:addons/purchase/controllers/portal.py:0
#: model:ir.model.fields,field_description:purchase.field_purchase_order__amount_total
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__price_total
#: model:ir.model.fields,field_description:purchase.field_purchase_report__price_total
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_rfqs
#, python-format
msgid "Total"
msgstr "Suma"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_uom_qty
msgid "Total Quantity"
msgstr "Visas kiekis"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
msgid "Total Untaxed amount"
msgstr "Iš viso, be mokesčių"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
msgid "Total amount"
msgstr "Iš viso"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__mail_reception_confirmed
msgid "True if PO reception is confirmed by the vendor."
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__mail_reminder_confirmed
msgid "True if the reminder email is confirmed by the vendor."
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Įrašytos išimties veiklos tipas."

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid ""
"Unable to cancel this purchase order. You must first cancel the related "
"vendor bills."
msgstr ""
"Negalima atšaukti šio pirkimo užsakymo. Pirmiausia turite atšaukti "
"susijusias tiekėjo sąskaitas."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__price_unit
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_portal_content
msgid "Unit Price"
msgstr "Vieneto kaina"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Unit Price:"
msgstr "Vieneto kaina:"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line__product_uom
msgid "Unit of Measure"
msgstr "Mato vienetas"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_uom_form_action
msgid "Units of Measure"
msgstr "Matavimo vienetai"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_uom_categ_form_action
msgid "Units of Measure Categories"
msgstr "Mato vienetų kategorijos"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_unit_of_measure_in_config_purchase
msgid "Units of Measures"
msgstr "Matavimo vienetai"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Unlock"
msgstr "Atrakinti"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_unread
msgid "Unread Messages"
msgstr "Neperskaitytos žinutės"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Neperskaitytų žinučių skaičiavimas"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_kpis_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_tree
msgid "Untaxed"
msgstr "Be mokesčių"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__amount_untaxed
msgid "Untaxed Amount"
msgstr "Suma be mokesčių"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__untaxed_total
msgid "Untaxed Total"
msgstr "Viso, be mokesčių"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "UoM"
msgstr "Mat. vnt."

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__priority__1
msgid "Urgent"
msgstr "Skubu"

#. module: purchase
#: model:res.groups,name:purchase.group_purchase_user
msgid "User"
msgstr "Vartotojas"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_config_settings__company_currency_id
msgid "Utility field to express amount currency"
msgstr "Pagalbinis laukas, išreiškiantis sumą valiuta"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Variant Grid Entry"
msgstr "Variantų tinklelio įvestis"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__partner_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order__partner_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report__partner_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_tree
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Vendor"
msgstr "Tiekėjas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_bill_union__vendor_bill_id
msgid "Vendor Bill"
msgstr "Tiekėjo sąskaita"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.act_res_partner_2_supplier_invoices
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.res_partner_view_purchase_account_buttons
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_bill_union_filter
msgid "Vendor Bills"
msgstr "Tiekėjų sąskaitos"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Vendor Country"
msgstr "Tiekėjo šalis"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_product_pricelist_action2_purchase
msgid "Vendor Pricelists"
msgstr "Tiekėjo kainoraščiai"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__partner_ref
msgid "Vendor Reference"
msgstr "Sąskaitos nr."

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_procurement_management_supplier_name
msgid "Vendors"
msgstr "Tiekėjai"

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_supplier_invoices
msgid ""
"Vendors bills can be pre-generated based on purchase\n"
"                    orders or receipts. This allows you to control bills\n"
"                    you receive from your vendor according to the draft\n"
"                    document in Odoo."
msgstr ""
"Tiekėjo mokėjimai gali būti iš anksto sugeneruojami pagal\n"
"pirkimo užsakymus ir kvitus. Tai leidžia jums valdyti mokėjimus,\n"
"kuriuos gaunate iš savo tiekėjo pagal \"Odoo\" esantį dokumento\n"
"juodraštį."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report__volume
msgid "Volume"
msgstr "Tūris"

#. module: purchase
#. openerp-web
#: code:addons/purchase/static/src/xml/purchase_dashboard.xml:0
#, python-format
msgid "Waiting"
msgstr "Laukia"

#. module: purchase
#: model:ir.model.fields.selection,name:purchase.selection__purchase_order__invoice_status__to_invoice
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
msgid "Waiting Bills"
msgstr "Laukiančios sąskaitos"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Waiting RFQs"
msgstr ""

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#: model:ir.model.fields.selection,name:purchase.selection__product_template__purchase_line_warn__warning
#: model:ir.model.fields.selection,name:purchase.selection__res_partner__purchase_warn__warning
#, python-format
msgid "Warning"
msgstr "Įspėjimas"

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid "Warning for %s"
msgstr "Įspėjimas %s"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_partner_view_purchase_buttons
msgid "Warning on the Purchase Order"
msgstr "Pirkimo užsakymo įspėjimas "

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_supplier_inherit
msgid "Warning when Purchasing this Product"
msgstr "Įspėjimas, kai perkamas šis produktas"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_view_search
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Warnings"
msgstr "Įspėjimai"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order__website_message_ids
msgid "Website Messages"
msgstr "Interneto svetainės žinutės"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__website_message_ids
msgid "Website communication history"
msgstr "Svetainės komunikacijos istorija"

#. module: purchase
#: model_terms:digest.tip,tip_description:purchase.digest_tip_purchase_0
msgid ""
"When creating a purchase order, have a look at the vendor's <i>On Time "
"Delivery</i> rate: the percentage of products shipped on time. If it is too "
"low, activate the <i>automated reminders</i>. A few days before the due "
"shipment, Odoo will send the vendor an email to ask confirmation of shipment"
" dates and keep you informed in case of any delays. To get the vendor's "
"performance statistics, click on the OTD rate."
msgstr ""
"Kurdami pirkimo užsakymą, atkreipkite dėmesį į <i>Tiekėjo Patikimumą</i>. "
"Jei jis per mažas, aktyvuokite <i>automatinius priminimus</i>. Likus kelioms"
" dienoms iki pristatymo datos, Odoo išsiųs tiekėjui el. laišką, prašydamas "
"patvirtinti siuntimo datas ir informuos Jus, jei tiekėjas vėluos. Norėdami "
"gauti pardavėjo našumo statistiką, spustelėkite ant Tiekėjo Patikimumo "
"rodiklio."

#. module: purchase
#: model_terms:digest.tip,tip_description:purchase.digest_tip_purchase_1
msgid ""
"When sending a purchase order by email, Odoo asks the vendor to acknowledge "
"the reception of the order. When the vendor acknowledges the order by "
"clicking on a button in the email, the information is added on the purchase "
"order. Use filters to track orders that have not been acknowledged."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.mail_notification_confirm
msgid "Yes"
msgstr "Taip"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order__partner_id
#: model:ir.model.fields,help:purchase.field_purchase_order_line__partner_id
msgid "You can find a vendor by its Name, TIN, Email or Internal Reference."
msgstr ""
"Galite rasti tiekėją pagal jo pavadinimą, mokėtojo kodą, el. paštą ar vidinį"
" numerį."

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid ""
"You cannot change the type of a purchase order line. Instead you should "
"delete the current line and create a new line of the proper type."
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_product_action
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                whether it's a storable product, a consumable or a service."
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_normal_action_puchased
msgid ""
"You must define a product for everything you sell or purchase,\n"
"            whether it's a storable product, a consumable or a service."
msgstr ""

#. module: purchase
#: code:addons/purchase/models/purchase.py:0
#, python-format
msgid ""
"Your quotation contains products from company %(product_company)s whereas your quotation belongs to company %(quote_company)s. \n"
" Please change the company of your quotation or remove the products from other companies (%(bad_products)s)."
msgstr ""
"Jūsų komerciniame pasiūlyme yra produktų iš įmonės %(product_company)s, o jūsų pasiūlymas priklauso %(quote_company)s įmonei . \n"
"Prašome pakeisti įmonę savo komerciniame pasiūlyme arba pašalinkite kitų įmonių produktus (%(bad_products)s)."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "close"
msgstr "uždaryti"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "day(s) before"
msgstr ""

#. module: purchase
#: model:mail.template,subject:purchase.email_template_edi_purchase
#: model:mail.template,subject:purchase.email_template_edi_purchase_done
#: model:mail.template,subject:purchase.email_template_edi_purchase_reminder
msgid "{{ object.company_id.name }} Order (Ref {{ object.name or 'n/a' }})"
msgstr ""
