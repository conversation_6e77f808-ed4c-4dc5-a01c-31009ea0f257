# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* fleet
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# Muft<PERSON> Syuhada <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# whenwesober, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: Abe Manyo, 2023\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.res_config_settings_view_form
msgid "<span class=\"o_form_label\">End Date Contract Alert</span>"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.res_config_settings_view_form
msgid "<span> days before the end date</span>"
msgstr "<span> hari sebelum tanggal akhir </span>"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.res_config_settings_view_form
msgid "<span>Send an alert </span>"
msgstr "<span>Kirimkan peringatan </span>"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "<span>cm</span>"
msgstr "<span>cm</span>"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "<span>g/km</span>"
msgstr "<span>g/km</span>"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "<span>kW</span>"
msgstr "<span>kW</span>"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_1
msgid "A/C Compressor Replacement"
msgstr "A / C Compressor Penggantian"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_2
msgid "A/C Condenser Replacement"
msgstr "A / C kondensor Penggantian"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_3
msgid "A/C Diagnosis"
msgstr "A / C Diagnosis"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_4
msgid "A/C Evaporator Replacement"
msgstr "A / C Evaporator Penggantian"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_5
msgid "A/C Recharge"
msgstr "A / C Recharge"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_needaction
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_needaction
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_needaction
msgid "Action Needed"
msgstr "Perlu Tindakan"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Activation Cost"
msgstr "Biaya aktivasi"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__active
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__active
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__active
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__active
msgid "Active"
msgstr "Aktif"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_ids
msgid "Activities"
msgstr "Aktivitas"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_exception_decoration
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_exception_decoration
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Dekorasi Pengecualian Aktivitas"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_state
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_state
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_state
msgid "Activity State"
msgstr "Status Aktivitas"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_type_icon
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_type_icon
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikon Jenis Aktifitas"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.mail_activity_type_action_config_fleet
#: model:ir.ui.menu,name:fleet.fleet_menu_config_activity_type
msgid "Activity Types"
msgstr "Jenis Aktivitas"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_tag_action
msgid "Add a new tag"
msgstr "Tambahkan tanda baru"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__description
msgid "Add a note about this vehicle"
msgstr ""

#. module: fleet
#: model:res.groups,name:fleet.fleet_group_manager
msgid "Administrator"
msgstr "Administrator"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_6
msgid "Air Filter Replacement"
msgstr "Air Filter Penggantian"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "All vehicles"
msgstr "Semua kendaraan"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_7
msgid "Alternator Replacement"
msgstr "Alternator Penggantian"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Apply New Driver"
msgstr "Terapkan Pengemudi Baru"

#. module: fleet
#. openerp-web
#: code:addons/fleet/static/src/js/fleet_form.js:0
#, python-format
msgid "Archive"
msgstr "Arsip"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Archived"
msgstr "Diarsipkan"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__next_assignation_date
msgid "Assignment Date"
msgstr "Tanggal Tugas"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__log_drivers
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_assignation_log_view_list
msgid "Assignment Logs"
msgstr "Log Tugas"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_9
msgid "Assistance"
msgstr "bantuan"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_attachment_count
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_attachment_count
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_attachment_count
msgid "Attachment Count"
msgstr "Hitungan Lampiran"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_kanban
msgid "Attention: renewal overdue"
msgstr "Perhatian: pembaruan terlambat"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__transmission__automatic
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__transmission__automatic
msgid "Automatic"
msgstr "Otomatis"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Available"
msgstr "Tersedia"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_8
msgid "Ball Joint Replacement"
msgstr "Ball Joint Replacement"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_9
msgid "Battery Inspection"
msgstr "Pemeriksaan baterai"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_10
msgid "Battery Replacement"
msgstr "Penggantian baterai"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_cost_report__vehicle_type__bike
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__vehicle_type__bike
msgid "Bike"
msgstr "Sepeda"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Bikes"
msgstr "Sepeda Motor"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_11
msgid "Brake Caliper Replacement"
msgstr "Rem Caliper Penggantian"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_12
msgid "Brake Inspection"
msgstr "Rem Inspeksi"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_13
msgid "Brake Pad(s) Replacement"
msgstr "Brake Pad (s) Penggantian"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__brand_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Brand"
msgstr "Brand"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_model_brand
msgid "Brand of the vehicle"
msgstr "Merek kendaraan"

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_break
msgid "Break"
msgstr "Istirahat"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__cng
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__cng
msgid "CNG"
msgstr "CNG"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__co2
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__default_co2
msgid "CO2 Emissions"
msgstr "Emisi CO2"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_tree
msgid "CO2 Emissions g/km"
msgstr "Emisi CO2 g/km"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__co2_standard
msgid "CO2 Standard"
msgstr "Standar CO2"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__co2
msgid "CO2 emissions of the vehicle"
msgstr "Emisi CO2 dari kendaraan"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_1
msgid "Calculation Benefit In Kind"
msgstr "Manfaat perhitungan Dalam Kind"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Cancel"
msgstr "Batal"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_services__state__cancelled
msgid "Cancelled"
msgstr "Dibatalkan"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_cost_report__vehicle_type__car
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__vehicle_type__car
msgid "Car"
msgstr "Mobil"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_14
msgid "Car Wash"
msgstr "Mencuci mobil"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Cars"
msgstr "Mobil-Mobil"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__car_value
msgid "Catalog Value (VAT Incl.)"
msgstr "Nilai Katalog (Termasuk Pajak)"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_15
msgid "Catalytic Converter Replacement"
msgstr "Catalytic Converter Penggantian"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__category
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__category_id
msgid "Category"
msgstr "Kategori"

#. module: fleet
#: model:ir.model.constraint,message:fleet.constraint_fleet_vehicle_model_category_name_uniq
msgid "Category name must be unique"
msgstr "Nama kategori harus unik"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_model_category
msgid "Category of the model"
msgstr "Kategori modelnya"

#. module: fleet
#: model:mail.message.subtype,description:fleet.mt_fleet_driver_updated
#: model:mail.message.subtype,name:fleet.mt_fleet_driver_updated
msgid "Changed Driver"
msgstr "Pengemudi Diubah"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_16
msgid "Charging System Diagnosis"
msgstr "Pengisian Sistem Diagnosis"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__vin_sn
msgid "Chassis Number"
msgstr "Chassis Number"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__state
msgid "Choose whether the contract is still valid or not"
msgstr "Pilih apakah kontrak masih berlaku atau tidak"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_service_type__category
msgid ""
"Choose whether the service refer to contracts, vehicle services or both"
msgstr ""
"Pilih apakah layanan meruju ke kontrak, layanan kendaraan atau keduanya"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Close Contract"
msgstr "Tutup Kontrak"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__contract_state__closed
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__state__closed
msgid "Closed"
msgstr "Ditutup"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__co2_standard
msgid "Co2 Standard"
msgstr "Standar Co2"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__color
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__color
msgid "Color"
msgstr "Warna"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__color
msgid "Color Index"
msgstr "Indeks Warna"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__color
msgid "Color of the vehicle"
msgstr "Warna kendaraan"

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_compact
msgid "Compact"
msgstr "Kompak"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__company_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__company_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__company_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__company_id
msgid "Company"
msgstr "Perusahaan"

#. module: fleet
#: model:ir.model,name:fleet.model_res_config_settings
msgid "Config Settings"
msgstr "Pengaturan Konfigurasi"

#. module: fleet
#: model:ir.ui.menu,name:fleet.fleet_configuration
msgid "Configuration"
msgstr "Konfigurasi"

#. module: fleet
#: model:ir.model,name:fleet.model_res_partner
msgid "Contact"
msgstr "Kontak"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_search
msgid "Contains Vehicles"
msgstr "Memiliki Kendaraan"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_service_type__category__contract
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_cost_report__cost_type__contract
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Contract"
msgstr "Kontrak"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_graph
msgid "Contract Costs Per Month"
msgstr "Biaya kontrak Per Bulan"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__contract_count
msgid "Contract Count"
msgstr "Jumlah Kontrak"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__expiration_date
msgid "Contract Expiration Date"
msgstr "Kontrak Tanggal Kadaluarsa"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Contract Information"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__start_date
msgid "Contract Start Date"
msgstr "Tanggal Mulai Kontrak"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_tree
msgid "Contract logs"
msgstr "Log kontrak"

#. module: fleet
#: model:mail.activity.type,name:fleet.mail_act_fleet_contract_to_renew
msgid "Contract to Renew"
msgstr "Kontrak untuk Diperbarui"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_kanban
msgid "Contract(s)"
msgstr "Kontrak"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_log_contract_action
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__log_contracts
#: model:ir.ui.menu,name:fleet.fleet_vehicle_log_contract_menu
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Contracts"
msgstr "Kontrak"

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_convertible
msgid "Convertible"
msgstr "Konversi"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__cost
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__amount
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__amount
msgid "Cost"
msgstr "Modal"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__cost_type
msgid "Cost Type"
msgstr "Biaya Jenis"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Cost that is paid only once at the creation of the contract"
msgstr "Biaya yang dibayar hanya sekali pada saat penciptaan kontrak"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__cost_subtype_id
msgid "Cost type purchased with this cost"
msgstr "Jenis biaya dibeli dengan biaya ini"

#. module: fleet
#: model:ir.ui.menu,name:fleet.menu_fleet_reporting_costs
msgid "Costs"
msgstr "Biaya"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_costs_reporting_action
msgid "Costs Analysis"
msgstr "Analisis biaya"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__country_id
msgid "Country"
msgstr "Negara"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__country_code
msgid "Country Code"
msgstr "Kode Negara"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_model_category_action
msgid "Create a new category"
msgstr "Buat kategori baru"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_contract_action
msgid "Create a new contract"
msgstr "Buat kontrak baru"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_model_brand_action
msgid "Create a new manufacturer"
msgstr "Buat produsen baru"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_model_action
msgid "Create a new model"
msgstr "Buat model baru"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_odometer_action
msgid "Create a new odometer log"
msgstr "Buat log odometer baru"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_services_action
msgid "Create a new service entry"
msgstr "Buat entri layanan baru"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_service_types_action
msgid "Create a new type of service"
msgstr "Buat tipe layanan baru"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_state_action
msgid "Create a new vehicle status"
msgstr "Buat status kendaraan baru"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__create_uid
msgid "Created by"
msgstr "Dibuat oleh"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__create_date
msgid "Created on"
msgstr "Dibuat pada"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__currency_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__currency_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__currency_id
msgid "Currency"
msgstr "Mata Uang"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Current Driver"
msgstr "Pengemudi Saat Ini"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__state_id
msgid "Current state of the vehicle"
msgstr "Keadaan saat kendaraan"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__cost_frequency__daily
msgid "Daily"
msgstr "Harian"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__date_start
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__date
msgid "Date"
msgstr "Tanggal"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__date
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__date
msgid "Date when the cost has been executed"
msgstr "Tanggal saat biaya tersebut telah dieksekusi"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__start_date
msgid "Date when the coverage of the contract begins"
msgstr "Tanggal ketika cakupan kontrak dimulai"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__expiration_date
msgid ""
"Date when the coverage of the contract expirates (by default, one year after"
" begin date)"
msgstr ""
"Tanggal ketika cakupan dari expirates kontrak (secara default, satu tahun "
"setelah mulai tanggal)"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__acquisition_date
msgid "Date when the vehicle has been immatriculated"
msgstr "Tanggal saat kendaraan telah didaftarkan plat nomornya"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_res_config_settings__delay_alert_contract
msgid "Delay alert contract outdated"
msgstr "Tunda peringatan kontrak habis masa berlaku"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_2
msgid "Depreciation and Interests"
msgstr "Penyusutan dan Minat"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__description
msgid "Description"
msgstr "Deskripsi"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__frame_type__diamant
msgid "Diamant"
msgstr "Diamant"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__diesel
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__diesel
msgid "Diesel"
msgstr "Diesel"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__display_name
msgid "Display Name"
msgstr "Nama Tampilan"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_services__state__done
msgid "Done"
msgstr "Selesai"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_17
msgid "Door Window Motor/Regulator Replacement"
msgstr "Pintu Jendela motor / Regulator Penggantian"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__doors
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__doors
msgid "Doors Number"
msgstr "Pintu Nomor"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_downgraded
msgid "Downgraded"
msgstr "Downgraded"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__driver_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__driver_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__driver_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__purchaser_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__purchaser_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__driver_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Driver"
msgstr "pengemudi"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__driver_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__purchaser_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_odometer__driver_id
msgid "Driver address of the vehicle"
msgstr "Alamat pengemudi untuk kendaraan"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Drivers"
msgstr "Pengemudi"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Drivers History"
msgstr "Sejarah Pengemudi"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__history_count
msgid "Drivers History Count"
msgstr "Jumlah Sejarah Pengemudi"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_assignation_log
msgid "Drivers history on a vehicle"
msgstr "Sejarah pengemudi untuk kendaraan"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_kanban
msgid "Dropdown menu"
msgstr "Menu dropdown"

#. module: fleet
#. openerp-web
#: code:addons/fleet/static/src/js/fleet_form.js:0
#, python-format
msgid ""
"Each Services and contracts of this vehicle will be considered as Archived. "
"Are you sure that you want to archive this record?"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_contract_action
msgid ""
"Each contract (e.g.: leasing) may include several services\n"
"            (reparation, insurances, periodic maintenance)."
msgstr ""
"Setiap kontrak (contoh: leasing) mungkin termasuk beberapa layanan\n"
"            (perbaikan, asuransi, maintenance periodik)."

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_service_types_action
msgid "Each service can used in contracts, as a standalone service or both."
msgstr ""
"Setiap layanan dapat digunakan dalam kontrak, sebagai layanan mandiri atau "
"keduanya."

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__electric
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__electric
msgid "Electric"
msgstr "listrik"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__electric_assistance
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__electric_assistance
msgid "Electric Assistance"
msgstr "Bantuan Listrik"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_17
msgid "Emissions"
msgstr "Emisi"

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_leasing
msgid "Employee Car"
msgstr "Mobil Karyawan"

#. module: fleet
#: code:addons/fleet/models/fleet_vehicle_log_services.py:0
#, python-format
msgid "Emptying the odometer value of a vehicle is not allowed."
msgstr "Mengosongkan nilai odometer kendaraan tidak diperbolehkan."

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__date_end
msgid "End Date"
msgstr "Tanggal Berakhir"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Engine"
msgstr "Mesin"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_18
msgid "Engine Belt Inspection"
msgstr "Mesin Belt Inspeksi"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_19
msgid "Engine Coolant Replacement"
msgstr "Mesin Pendingin Penggantian"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_20
msgid "Engine/Drive Belt(s) Replacement"
msgstr "Mesin / Belt drive (s) Penggantian"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_13
msgid "Entry into service tax"
msgstr "Masuk ke layanan pajak"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_21
msgid "Exhaust Manifold Replacement"
msgstr "Exhaust Manifold Penggantian"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__contract_state__expired
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__state__expired
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
msgid "Expired"
msgstr "Berakhir"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__first_contract_date
msgid "First Contract Date"
msgstr "Tanggal Kontrak Pertama"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Fiscality"
msgstr "Fiskalitas"

#. module: fleet
#: model:ir.module.category,name:fleet.module_fleet_category
#: model:ir.ui.menu,name:fleet.fleet_vehicle_menu
#: model:ir.ui.menu,name:fleet.fleet_vehicles
#: model:ir.ui.menu,name:fleet.menu_root
#: model_terms:ir.ui.view,arch_db:fleet.res_config_settings_view_form
msgid "Fleet"
msgstr "Armada kapal"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_cost_report
msgid "Fleet Analysis Report"
msgstr "Laporan Analisis Armada"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_graph
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vechicle_costs_report_view_tree
msgid "Fleet Costs Analysis"
msgstr "Analisis Biaya Armada"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.res_config_settings_view_form
msgid "Fleet Management"
msgstr "Manajemen Armada"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__manager_id
msgid "Fleet Manager"
msgstr "Manajer Armada"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_service_type
msgid "Fleet Service Type"
msgstr "Tipe Layanan Armada"

#. module: fleet
#: model:ir.actions.server,name:fleet.ir_cron_contract_costs_generator_ir_actions_server
#: model:ir.cron,cron_name:fleet.ir_cron_contract_costs_generator
#: model:ir.cron,name:fleet.ir_cron_contract_costs_generator
msgid "Fleet: Generate contracts costs based on costs frequency"
msgstr "Armada: Buat biaya armada berdasarkan cost frequency"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_follower_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_follower_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_follower_ids
msgid "Followers"
msgstr "Pengikut"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_partner_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_partner_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_partner_ids
msgid "Followers (Partners)"
msgstr "Pengikut (Rekanan)"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__activity_type_icon
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__activity_type_icon
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Ikon font awesome, misalnya fa-tasks"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__frame_size
msgid "Frame Size"
msgstr "Ukuran Rangka"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__frame_type
msgid "Frame Type"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__frame_type
msgid "Frame type of the bike"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__cost_frequency
msgid "Frequency of the recuring cost"
msgstr "Frekuensi biaya berulang"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__fuel_type
msgid "Fuel"
msgstr "Bahan bakar"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_22
msgid "Fuel Injector Replacement"
msgstr "Fuel Injector Penggantian"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_23
msgid "Fuel Pump Replacement"
msgstr "Fuel Pump Penggantian"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__fuel_type
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__default_fuel_type
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Fuel Type"
msgstr "Jenis Bahan Bakar"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__fuel_type
msgid "Fuel Used by the vehicle"
msgstr "BBM Digunakan oleh kendaraan"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Future Activities"
msgstr "Kegiatan - Kegiatan Mendatang"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__future_driver_id
msgid "Future Driver"
msgstr "Pengemudi Berikutnya"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_kanban
msgid "Future Driver :"
msgstr "Pengemudi Berikutnya :"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__gasoline
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__gasoline
msgid "Gasoline"
msgstr "Bensin"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Group By"
msgstr "Dikelompokkan berdasarkan"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__contract_renewal_overdue
msgid "Has Contracts Overdue"
msgstr "Memiliki kontrak terlambat"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__contract_renewal_due_soon
msgid "Has Contracts to renew"
msgstr "Memiliki Kontrak untuk memperbaharui"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__has_message
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__has_message
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__has_message
msgid "Has Message"
msgstr "Memiliki Pesan"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_24
msgid "Head Gasket(s) Replacement"
msgstr "Kepala Gasket (s) Penggantian"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_25
msgid "Heater Blower Motor Replacement"
msgstr "Heater motor Blower Penggantian"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_26
msgid "Heater Control Valve Replacement"
msgstr "Pemanas Control Valve Penggantian"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_27
msgid "Heater Core Replacement"
msgstr "Pemanas Inti Penggantian"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_28
msgid "Heater Hose Replacement"
msgstr "Pemanas Selang Penggantian"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__horsepower
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__horsepower
msgid "Horsepower"
msgstr "Horsepower"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__horsepower_tax
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__horsepower_tax
msgid "Horsepower Taxation"
msgstr "Horsepower Perpajakan"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__hybrid
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__hybrid
msgid "Hybrid Diesel"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__full_hybrid_gasoline
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__full_hybrid_gasoline
msgid "Hybrid Gasoline"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__hydrogen
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__hydrogen
msgid "Hydrogen"
msgstr "Hidrogen"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__id
msgid "ID"
msgstr "ID"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_exception_icon
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_exception_icon
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_exception_icon
msgid "Icon"
msgstr "Ikon"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__activity_exception_icon
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__activity_exception_icon
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikon untuk menunjukkan sebuah aktivitas pengecualian."

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__message_needaction
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__message_unread
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__message_needaction
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__message_unread
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__message_needaction
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__message_unread
msgid "If checked, new messages require your attention."
msgstr "Jika dicentang, pesan baru memerlukan penanganan dan perhatian Anda."

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__message_has_error
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__message_has_error
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Jika dicentang, beberapa pesan mempunyai kesalahan dalam pengiriman."

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_29
msgid "Ignition Coil Replacement"
msgstr "Ignition Coil Penggantian"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__acquisition_date
msgid "Immatriculation Date"
msgstr "Tanggal Pendaftaran"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__contract_state__open
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__state__open
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
msgid "In Progress"
msgstr "Dalam Proses"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__service_ids
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Included Services"
msgstr "Layanan termasuk"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__contract_state__futur
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__state__futur
msgid "Incoming"
msgstr "Barang Masuk"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
msgid "Information"
msgstr "Informasi"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_30
msgid "Intake Manifold Gasket Replacement"
msgstr "Intake Manifold Gasket Penggantian"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Invoice Date"
msgstr "Tanggal Faktur"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_is_follower
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_is_follower
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_is_follower
msgid "Is Follower"
msgstr "Pengikut"

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_junior
msgid "Junior"
msgstr "Junior"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_kanban
msgid "Km"
msgstr "Km"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__lpg
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__lpg
msgid "LPG"
msgstr "LPG"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__contract_state
msgid "Last Contract State"
msgstr "Status Kontrak Terakhir"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag____last_update
msgid "Last Modified on"
msgstr "Terakhir diubah pada"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__odometer
msgid "Last Odometer"
msgstr "Odometer terakhir"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__write_uid
msgid "Last Updated by"
msgstr "Terakhir diperbarui oleh"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__write_date
msgid "Last Updated on"
msgstr "Terakhir diperbarui pada"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Late Activities"
msgstr "Aktifitas terakhir"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_contract_leasing
msgid "Leasing"
msgstr "Lokasi"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_action
msgid "Let's create your first vehicle."
msgstr "Mari buat kendaraan pertama Anda."

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__license_plate
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "License Plate"
msgstr "Pelat"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__license_plate
msgid "License plate number of the vehicle (i = plate number for a car)"
msgstr "Nomor plat kendaraan (i = nomor plat untuk mobil)"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__location
msgid "Location"
msgstr "Lokasi"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__location
msgid "Location of the vehicle (garage, ...)"
msgstr "Lokasi kendaraan (garasi, ...)"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__image_128
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__image_128
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__image_128
msgid "Logo"
msgstr "Logo"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_kanban
msgid "MODELS"
msgstr "MODELS"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_main_attachment_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_main_attachment_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_main_attachment_id
msgid "Main Attachment"
msgstr "Lampiran Utama"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__name
msgid "Make"
msgstr "Membuat"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_contract_action
msgid ""
"Manage all your contracts (leasing, insurances, etc.) with\n"
"            their related services, costs. Odoo will automatically warn\n"
"            you when some contracts have to be renewed."
msgstr ""
"Kelola semua kontrak Anda (leasing, asuransi, dsb.) dengan\n"
"            layanan mereka yang terkait, serta biaya. Odoo akan otomatis\n"
"            mengingatkan Anda saat beberapa kontrak harus diperbarui."

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_costs_reporting_action
msgid "Manage efficiently your different effective vehicles Costs with Odoo."
msgstr ""
"Kelola dengan efisien Biaya efektif beragam kendaraan Anda dengan Odoo."

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_11
msgid "Management Fee"
msgstr "Biaya manajemen"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__transmission__manual
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__transmission__manual
msgid "Manual"
msgstr "Manual"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__brand_id
msgid "Manufacturer"
msgstr "Produsen"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__brand_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_model__brand_id
msgid "Manufacturer of the vehicle"
msgstr ""

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_model_brand_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_model_brand_menu
msgid "Manufacturers"
msgstr "Produsen-Produsen"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_has_error
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_has_error
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_has_error
msgid "Message Delivery error"
msgstr "Kesalahan Pengiriman Pesan"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_ids
msgid "Messages"
msgstr "Pesan"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__model_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__model_ids
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Model"
msgstr "Model"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_model_category_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_model_category_menu
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_category_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_category_view_tree
msgid "Model Category"
msgstr "Kategori Model"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__model_count
msgid "Model Count"
msgstr "Jumlah Model"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_tree
msgid "Model Make"
msgstr "Membuat model"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__model_year
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__model_year
msgid "Model Year"
msgstr "Tahun Model"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__name
msgid "Model name"
msgstr "Contoh"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_model
msgid "Model of a vehicle"
msgstr "Model kendaraan"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__model_id
msgid "Model of the vehicle"
msgstr "Model kendaraan"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_kanban
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_tree
msgid "Models"
msgstr "Model"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__cost_frequency__monthly
msgid "Monthly"
msgstr "Bulanan"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__my_activity_date_deadline
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__my_activity_date_deadline
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Deadline Kegiatan Saya"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__name
msgid "Name"
msgstr "Nama"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__contract_renewal_name
msgid "Name of contract to renew soon"
msgstr "Nama kontrak untuk memperbaharui segera"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Need Action"
msgstr "Butuh Tindakan"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_services__state__new
msgid "New"
msgstr "Baru"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_new_request
msgid "New Request"
msgstr "Permintaan Baru"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_calendar_event_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_calendar_event_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Kalender Acara Aktivitas Berikutnya"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_date_deadline
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_date_deadline
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Batas Waktu Aktivitas Berikutnya"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_summary
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_summary
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_summary
msgid "Next Activity Summary"
msgstr "Ringkasan Aktivitas Berikutnya"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_type_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_type_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_type_id
msgid "Next Activity Type"
msgstr "Tipe Aktivitas Berikutnya"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__future_driver_id
msgid "Next Driver Address of the vehicle"
msgstr "Alamat Pengemudi Berikutnya untuk kendaraan"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__cost_frequency__no
msgid "No"
msgstr "Tidak"

#. module: fleet
#: code:addons/fleet/models/fleet_vehicle.py:0
#, python-format
msgid "No Plate"
msgstr "Tidak ada Plat Kendaraan"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_costs_reporting_action
msgid "No data for analysis"
msgstr "Tidak ada data untuk dianalisis"

#. module: fleet
#: code:addons/fleet/models/fleet_vehicle.py:0
#, python-format
msgid "No plate"
msgstr "Tidak ada plat kendaraan"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Note"
msgstr "Catatan"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__notes
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
msgid "Notes"
msgstr "Catatan"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_needaction_counter
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_needaction_counter
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_needaction_counter
msgid "Number of Actions"
msgstr "Jumlah Tindakan"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__doors
msgid "Number of doors of the vehicle"
msgstr "Jumlah pintu kendaraan"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_has_error_counter
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_has_error_counter
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_has_error_counter
msgid "Number of errors"
msgstr "Jumlah kesalahan"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__message_needaction_counter
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__message_needaction_counter
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Jumlah pesan yang butuh tindakan"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__message_has_error_counter
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__message_has_error_counter
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Jumlah dari pesan dengan kesalahan pengiriman"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__seats
msgid "Number of seats of the vehicle"
msgstr "Jumlah kursi kendaraan"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__message_unread_counter
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__message_unread_counter
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__message_unread_counter
msgid "Number of unread messages"
msgstr "Jumlah pesan yang belum dibaca"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__odometer_count
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__odometer_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Odometer"
msgstr "Odometer"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_tree
msgid "Odometer Logs"
msgstr "Log odometer"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__odometer_unit
msgid "Odometer Unit"
msgstr "Satuan odometer"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__odometer
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__value
msgid "Odometer Value"
msgstr "Nilai odometer"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_graph
msgid "Odometer Values Per Vehicle"
msgstr "Nilai odometer Per Kendaraan"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_odometer
msgid "Odometer log for a vehicle"
msgstr "Log odometer untuk kendaraan"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__odometer
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__odometer
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__odometer_id
msgid "Odometer measure of the vehicle at the moment of this log"
msgstr "Ukuran odometer kendaraan pada saat log ini"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_odometer_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_odometer_menu
msgid "Odometers"
msgstr "Odometer"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_31
msgid "Oil Change"
msgstr "Ubah ke .."

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_32
msgid "Oil Pump Replacement"
msgstr "Minyak Pompa Penggantian"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_contract_omnium
msgid "Omnium"
msgstr "Omnium"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_16
msgid "Options"
msgstr "Opsi"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_ordered
msgid "Ordered"
msgstr "Memerintahkan"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_33
msgid "Other Maintenance"
msgstr "Pemeliharaan lainnya"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_34
msgid "Oxygen Sensor Replacement"
msgstr "Saturasi Oksigen"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__plan_to_change_bike
#: model:ir.model.fields,field_description:fleet.field_res_partner__plan_to_change_bike
#: model:ir.model.fields,field_description:fleet.field_res_users__plan_to_change_bike
msgid "Plan To Change Bike"
msgstr "Rencana Untuk Mengganti Sepeda Motor"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__plan_to_change_car
#: model:ir.model.fields,field_description:fleet.field_res_partner__plan_to_change_car
#: model:ir.model.fields,field_description:fleet.field_res_users__plan_to_change_car
msgid "Plan To Change Car"
msgstr "Rencana Untuk Mengganti Mobil"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Planned for Change"
msgstr "Direncanakan untuk Diganti"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__plug_in_hybrid_diesel
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__plug_in_hybrid_diesel
msgid "Plug-in Hybrid Diesel"
msgstr "Plug-in Hybrid Diesel"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__plug_in_hybrid_gasoline
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__plug_in_hybrid_gasoline
msgid "Plug-in Hybrid Gasoline"
msgstr "Plug-in Hybrid Gasoline"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__power
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__power
msgid "Power"
msgstr "kemampuan"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_35
msgid "Power Steering Hose Replacement"
msgstr "Power Steering Selang Penggantian"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_36
msgid "Power Steering Pump Replacement"
msgstr "Power Steering Pump Penggantian"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__power
msgid "Power in kW of the vehicle"
msgstr "Daya dalam kW kendaraan"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_kanban
msgid "Properties"
msgstr "Properti"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__net_car_value
msgid "Purchase Value"
msgstr "Nilai Beli"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__net_car_value
msgid "Purchase value of the vehicle"
msgstr "Nilai beli kendaraan"

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_purchased
msgid "Purchased"
msgstr "Dibeli"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_37
msgid "Radiator Repair"
msgstr "Radiator Perbaikan"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_action
msgid "Ready to manage your fleet more efficiently ?"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__cost_generated
msgid "Recurring Cost"
msgstr "Biaya Berulang"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__cost_frequency
msgid "Recurring Cost Frequency"
msgstr "Biaya berulang Frekuensi"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__ins_ref
msgid "Reference"
msgstr "Referensi"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_refueling
msgid "Refueling"
msgstr "Pengisian bahan bakar"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_registered
msgid "Registered"
msgstr "Terdaftar"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_12
msgid "Rent (Excluding VAT)"
msgstr "Dapat disewakan"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_8
msgid "Repair and maintenance"
msgstr "Perbaikan dan pemeliharaan"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_contract_repairing
msgid "Repairing"
msgstr "Perbaikan"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_10
msgid "Replacement Vehicle"
msgstr "Penggantian Kendaraan"

#. module: fleet
#: model:ir.ui.menu,name:fleet.menu_fleet_reporting
msgid "Reporting"
msgstr "Laporan"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_reserve
msgid "Reserve"
msgstr "Cadangan"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Reset To Draft"
msgstr "Reset To Draft"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__residual_value
msgid "Residual Value"
msgstr "Nilai residual"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_15
msgid "Residual value (Excluding VAT)"
msgstr "Nilai sisa (Tidak Termasuk PPN)"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_19
msgid "Residual value in %"
msgstr "Nilai sisa di%"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__user_id
msgid "Responsible"
msgstr "Penanggung Jawab"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_user_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_user_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_user_id
msgid "Responsible User"
msgstr "Tanggung-jawab"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_38
msgid "Resurface Rotors"
msgstr "Muncul kembali rotor"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_39
msgid "Rotate Tires"
msgstr "Putar Ban"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_40
msgid "Rotor Replacement"
msgstr "Rotor Penggantian"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_services__state__running
msgid "Running"
msgstr "Sedang berjalan"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__seats
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__seats
msgid "Seats Number"
msgstr "Kursi Nomor"

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_sedan
msgid "Sedan"
msgstr "Sedan"

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_senior
msgid "Senior"
msgstr "Senior"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__sequence
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__sequence
msgid "Sequence"
msgstr "Urutan"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_service_type__category__service
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_cost_report__cost_type__service
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_search
msgid "Service"
msgstr "Jasa"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__service_type_id
msgid "Service Type"
msgstr "Jenis layanan"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_service_types_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_service_types_menu
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_service_types_view_tree
msgid "Service Types"
msgstr "Jenis layanan"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_log_services_action
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__service_count
#: model:ir.ui.menu,name:fleet.fleet_vehicle_log_services_menu
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Services"
msgstr "Jasa"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_graph
msgid "Services Costs Per Month"
msgstr "Layanan Biaya Per Bulan"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__log_services
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_tree
msgid "Services Logs"
msgstr "Layanan"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_log_services
msgid "Services for vehicles"
msgstr "Layanan untuk kendaraan"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_config_settings_action
#: model:ir.ui.menu,name:fleet.fleet_config_settings_menu
msgid "Settings"
msgstr "Pengaturan"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Show all records which has next action date is before today"
msgstr "Tampilkan semua dokumen dengan aksi berikut sebelum hari ini"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_6
msgid "Snow tires"
msgstr "Ban salju"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_41
msgid "Spark Plug Replacement"
msgstr "Spark Plug Penggantian"

#. module: fleet
#: code:addons/fleet/models/fleet_vehicle.py:0
#, python-format
msgid "Specify the End date of %s"
msgstr "Tentukan Tanggal akhir untuk %s"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__state
msgid "Stage"
msgstr "Tahapan"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Start Contract"
msgstr "Mulai Kontrak"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__date_start
msgid "Start Date"
msgstr "Tanggal Mulai"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_42
msgid "Starter Replacement"
msgstr "Penggantian Starter"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__state_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_state_view_tree
msgid "State"
msgstr "Status"

#. module: fleet
#: model:ir.model.constraint,message:fleet.constraint_fleet_vehicle_state_fleet_state_name_unique
msgid "State name already exists"
msgstr "Nama negara sudah ada"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__state
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Status"
msgstr "Status"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__activity_state
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__activity_state
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status berdasarkan aktivitas\n"
"Terlambat: Batas waktu telah terlewati\n"
"Hari ini: Tanggal aktivitas adalah hari ini\n"
"Direncanakan: Aktivitas yang akan datang."

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vechicle_costs_report_view_tree
msgid "Sum of Cost"
msgstr "Jumlah Biaya"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_5
#: model:fleet.service.type,name:fleet.type_service_service_7
msgid "Summer tires"
msgstr "Ban musim panas"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__name
msgid "Tag Name"
msgstr "Nama Tag"

#. module: fleet
#: model:ir.model.constraint,message:fleet.constraint_fleet_vehicle_tag_name_uniq
msgid "Tag name already exists !"
msgstr "Nama tag sudah ada !"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__tag_ids
msgid "Tags"
msgstr "Label"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Tax Info"
msgstr "Informasi Pajak"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_3
msgid "Tax roll"
msgstr "Gulungan pajak"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__notes
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Terms and Conditions"
msgstr "Syarat dan Ketentuan"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"Kode ISO negara dalam dua karakter.\n"
"Anda dapat menggunakan kolom ini untuk pencarian cepat."

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_43
msgid "Thermostat Replacement"
msgstr "Penggantian Thermostat"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__next_assignation_date
msgid ""
"This is the date at which the car will be available, if not set it means "
"available instantly"
msgstr ""
"Ini adalah tanggal di mana mobil tersedia, bila tidak ditetapkan berarti "
"mobil tersedia instan"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_44
msgid "Tie Rod End Replacement"
msgstr "Tie Rod End Penggantian"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_45
msgid "Tire Replacement"
msgstr "Penggantian Pajak"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_46
msgid "Tire Service"
msgstr "Layanan"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_to_order
msgid "To Order"
msgstr "Untuk Dipesan"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Today Activities"
msgstr "Aktivitas Hari ini"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_tree
msgid "Total"
msgstr "Total"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_14
msgid "Total expenses (Excluding VAT)"
msgstr "Jumlah beban (Tidak Termasuk PPN)"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__contract_renewal_total
msgid "Total of contracts due or overdue minus one"
msgstr "Total kontrak jatuh tempo atau terlambat minus satu"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_18
msgid "Touring Assistance"
msgstr "Bantuan tur"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_services_action
msgid ""
"Track all the services done on your vehicle.\n"
"            Services can be of many types: occasional repair, fixed maintenance, etc."
msgstr ""
"Lacak semua layanan yang dilakukan pada kendaraan Anda.\n"
"            Layanan dapat merupakan banyak tipe: perbaikan sesekali, maintenance tetap, dsb."

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__trailer_hook
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__trailer_hook
msgid "Trailer Hitch"
msgstr "Trailer Hitch"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Trailer Hook"
msgstr "Trailer Hook"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__transmission
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__transmission
msgid "Transmission"
msgstr "Transmisi"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_47
msgid "Transmission Filter Replacement"
msgstr "Transmisi Filter Penggantian"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_48
msgid "Transmission Fluid Replacement"
msgstr "Transmisi Fluid Replacement"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_49
msgid "Transmission Replacement"
msgstr "Penggantian transmisi"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__transmission
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_model__transmission
msgid "Transmission Used by the vehicle"
msgstr "Transmisi Digunakan oleh kendaraan"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__frame_type__trapez
msgid "Trapez"
msgstr "Trapez"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__cost_subtype_id
msgid "Type"
msgstr "Jenis"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__activity_exception_decoration
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__activity_exception_decoration
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Jenis dari aktivitas pengecualian pada rekaman data."

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__vin_sn
msgid "Unique number written on the vehicle motor (VIN/SN number)"
msgstr "Nomor unik yang ditulis pada kendaraan bermotor (VIN / nomor SN)"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__odometer_unit
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__unit
msgid "Unit"
msgstr "Satuan"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__odometer_unit
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__odometer_unit
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_odometer__unit
msgid "Unit of the odometer "
msgstr "Unit odometer"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_unread
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_unread
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_unread
msgid "Unread Messages"
msgstr "Pesan Belum Dibaca"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_unread_counter
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_unread_counter
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Penghitung Pesan yang Belum Dibaca"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_state__sequence
msgid "Used to order the note stages"
msgstr "Digunakan untuk memesan tahap catatan"

#. module: fleet
#: model:res.groups,name:fleet.fleet_group_user
msgid "User"
msgstr "Pengguna"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__car_value
msgid "Value of the bought vehicle"
msgstr "Nilai kendaraan membeli"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__vehicle_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__vehicle_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__vehicle_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__vehicle_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__vehicle_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_tree
msgid "Vehicle"
msgstr "Armada"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_log_contract
msgid "Vehicle Contract"
msgstr "Kontrak Kendaraan"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__vehicle_count
msgid "Vehicle Count"
msgstr "Jumlah Kendaraan"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__description
msgid "Vehicle Description"
msgstr "Deskripsi Kendaraan"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
msgid "Vehicle Information"
msgstr "Informasi Kendaraan"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_model_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_model_menu
msgid "Vehicle Models"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__name
msgid "Vehicle Name"
msgstr "Nama Kendaraan"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_state_action
#: model:ir.model,name:fleet.model_fleet_vehicle_state
#: model:ir.ui.menu,name:fleet.fleet_vehicle_state_menu
msgid "Vehicle Status"
msgstr "Status kendaraan"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_tag
msgid "Vehicle Tag"
msgstr "Tanda Kendaraan"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_tag_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_tag_menu
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_tag_view_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_tag_view_view_tree
msgid "Vehicle Tags"
msgstr "Tanda Kendaraan"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__vehicle_type
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__vehicle_type
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__vehicle_type
msgid "Vehicle Type"
msgstr "Tipe Kendaraan"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__vehicle_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__vehicle_id
msgid "Vehicle concerned by this log"
msgstr "Kendaraan prihatin dengan log ini"

#. module: fleet
#: code:addons/fleet/models/fleet_vehicle_model.py:0
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_action
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_tree
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_activity
#, python-format
msgid "Vehicles"
msgstr "Armada"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_activity
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
msgid "Vehicles Contracts"
msgstr "Armada"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_search
msgid "Vehicles costs"
msgstr "Biaya kendaraan"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_search
msgid "Vehicles odometers"
msgstr "Kendaraan odometers"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__insurer_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__vendor_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
msgid "Vendor"
msgstr "Pemasok"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__inv_ref
msgid "Vendor Reference"
msgstr "Referensi Pemasok"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__vendors
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
msgid "Vendors"
msgstr "Pemasok"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_waiting_list
msgid "Waiting List"
msgstr "Daftar Tunggu"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__days_left
msgid "Warning Date"
msgstr "Peringatan Tanggal"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_kanban
msgid "Warning: renewal due soon"
msgstr "Peringatan: pembaruan sebentar lagi"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_50
msgid "Water Pump Replacement"
msgstr "Pompa air Penggantian"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__frame_type__wave
msgid "Wave"
msgstr "Wave"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__cost_frequency__weekly
msgid "Weekly"
msgstr "Mingguan"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_51
msgid "Wheel Alignment"
msgstr "Keselarasan roda"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_52
msgid "Wheel Bearing Replacement"
msgstr "Roda Bantalan Penggantian"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_53
msgid "Windshield Wiper(s) Replacement"
msgstr "Wiper (s) Penggantian"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_search
msgid "With Models"
msgstr "Dengan Model"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Write here all other information relative to this contract"
msgstr "Tulis di sini semua informasi relatif lainnya untuk kontrak ini"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__notes
msgid "Write here all supplementary information relative to this contract"
msgstr ""
"Tulislah disini semua informasi tambahan yang berkaitan dengan kontrak ini"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
msgid "Write here any other information related to the service completed."
msgstr "Menulis di sini informasi lain yang berkaitan dengan layanan lengkap."

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Write here any other information related to this vehicle"
msgstr "Tulis di sini informasi lain terkait kendaraan"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__model_year
msgid "Year of the model"
msgstr "Tahun model"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__cost_frequency__yearly
msgid "Yearly"
msgstr "Tahunan"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_odometer_action
msgid "You can add various odometer entries for all vehicles."
msgstr "Anda dapat menambahkan beragam entri odometer untuk semua kendaraan."

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_state_action
msgid ""
"You can customize available status to track the evolution of\n"
"            each vehicle. Example: active, being repaired, sold."
msgstr ""
"Anda dapat kustomisasi status tersedia untuk melacak evolusi dari\n"
"            setiap kendaraan. Contoh: aktif, sedang diperbaiki, dijual."

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_model_action
msgid "You can define several models (e.g. A3, A4) for each make (Audi)."
msgstr ""
"Anda dapat menentukan beberapa model (misalnya A3, A4) untuk masing-masing "
"membuat (Audi)."

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "e.g. Model S"
msgstr "misalnya Model S"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "e.g. PAE 326"
msgstr "misalnya PAE 326"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
msgid "e.g. Tesla"
msgstr "misalnya Tesla"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_kanban
msgid "img"
msgstr "img"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__odometer_unit__kilometers
msgid "km"
msgstr "km"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__odometer_unit__miles
msgid "mi"
msgstr "mi"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "show the contract for this vehicle"
msgstr "menunjukkan kontrak untuk kendaraan ini"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "show the odometer logs for this vehicle"
msgstr "menunjukkan log odometer untuk kendaraan ini"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "show the services logs for this vehicle"
msgstr "menunjukkan layanan log untuk kendaraan ini"
