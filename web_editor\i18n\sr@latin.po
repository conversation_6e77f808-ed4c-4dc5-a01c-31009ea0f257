# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * web_editor
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON><PERSON> Jo<PERSON>v <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:54+0000\n"
"PO-Revision-Date: 2017-09-20 09:54+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> Jovev <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:193
#, python-format
msgid "(URL or Embed)"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:922
#, python-format
msgid "(YouTube, Vimeo, Vine, Instagram, DailyMotion or Youku)"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "25% Black"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "25% White"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "50% Black"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "50% White"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "75% Black"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "75% White"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "<i class=\"fa fa-th-large\"/> First Panel"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/widgets/ace.js:569
#, python-format
msgid ""
"A server error occured. Please check you correctly signed in and that the "
"file you are saving is well-formed."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:198
#, python-format
msgid "Accepts"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:974
#, python-format
msgid "Action"
msgstr "Akcija"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:114
#, python-format
msgid "Add"
msgstr "Dodaj"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:111
#, python-format
msgid "Add an image URL"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "Add blocks"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:956
#, python-format
msgid "Align center"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:955
#, python-format
msgid "Align left"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:957
#, python-format
msgid "Align right"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Alpha"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:99
#, python-format
msgid "Alternate Upload"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:315
#, python-format
msgid "Assign a focal point that will always be visible"
msgstr ""

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment_local_url
msgid "Attachment URL"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:11
#, python-format
msgid "Auto size"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:204
#, python-format
msgid "Autoplay"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:963
#: code:addons/web_editor/static/src/xml/snippets.xml:32
#, python-format
msgid "Background Color"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:964
#, python-format
msgid "Background Image Sizing"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:354
#, python-format
msgid "Background height"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:364
#, python-format
msgid "Background position"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:386
#, python-format
msgid "Background repeat"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:342
#, python-format
msgid "Background size"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:345
#, python-format
msgid "Background width"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Beta"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Black"
msgstr "Crna"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:204
#, python-format
msgid "Block"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:883
#, python-format
msgid "Bold"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/widgets/ace.js:827
#, python-format
msgid "Careful !"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:95
#, python-format
msgid "Center"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/widgets/widgets.js:55
#, python-format
msgid "Change media description and tooltip"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:972
#: code:addons/web_editor/static/src/js/editor/translator.js:52
#: code:addons/web_editor/static/src/xml/ace.xml:30
#, python-format
msgid "Close"
msgstr "Zatvori"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:934
#, python-format
msgid "Code"
msgstr "Šifra"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:949
#, python-format
msgid "Code View"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:265
#, python-format
msgid "Color"
msgstr "Boja"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:202
#, python-format
msgid "Column"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:61
#, python-format
msgid "Common colors"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:310
#, python-format
msgid "Contain"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/translator.xml:7
#, python-format
msgid "Content to translate"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:194
#, python-format
msgid "Copy-paste your URL or embed code here"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:309
#, python-format
msgid "Cover"
msgstr ""

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub_create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub_create_date
msgid "Created on"
msgstr "Datum kreiranja"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:311
#, python-format
msgid "Custom"
msgstr "Prilagođeno"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:19
#, python-format
msgid "Customize"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:198
#, python-format
msgid "Dailymotion"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Danger"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/summernote.js:1899
#, python-format
msgid "Default"
msgstr "Podrazumevano"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:387
#, python-format
msgid "Define if/how the background image will be repeated"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Delta"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:165
#, python-format
msgid "Description"
msgstr "Opis"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:34
#, python-format
msgid "Description (alt tag)"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:968
#: code:addons/web_editor/static/src/js/widgets/widgets.js:24
#: code:addons/web_editor/static/src/xml/editor.xml:12
#: code:addons/web_editor/static/src/xml/editor.xml:140
#, python-format
msgid "Discard"
msgstr ""

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub_display_name
msgid "Display Name"
msgstr "Naziv za prikaz"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:51
#, python-format
msgid "Document"
msgstr "Dokument"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:976
#, python-format
msgid "Document Style"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:860
#, python-format
msgid "Double-click to edit"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:903
#, python-format
msgid "Drag an image here"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:22
#, python-format
msgid "Drag to Move"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:23
#, python-format
msgid "Duplicate Container"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:151
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:912
#, python-format
msgid "Edit"
msgstr "Uredi"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Epsilon"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/widgets/ace.js:93
#, python-format
msgid "Expected "
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:257
#, python-format
msgid "Extra Small"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:895
#, python-format
msgid "File / Image"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:12
#, python-format
msgid "Fixed size"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:900
#, python-format
msgid "Float Left"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:902
#, python-format
msgid "Float None"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:901
#, python-format
msgid "Float Right"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:964
#, python-format
msgid "Font Color"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:891
#, python-format
msgid "Font Family"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:892
#, python-format
msgid "Font Size"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:37
#, python-format
msgid "Format"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:948
#, python-format
msgid "Full Screen"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Gamma"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Gray"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Gray Dark"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Gray Darker"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Gray Light"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Gray Lighter"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_http
msgid "HTTP routing"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:935
#, python-format
msgid "Header 1"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:936
#, python-format
msgid "Header 2"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:937
#, python-format
msgid "Header 3"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:938
#, python-format
msgid "Header 4"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:939
#, python-format
msgid "Header 5"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:940
#, python-format
msgid "Header 6"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:947
#, python-format
msgid "Help"
msgstr "Pomoć"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/translator.xml:5
#, python-format
msgid "Here are the visuals used to help you translate efficiently:"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:219
#, python-format
msgid "Hide Dailymotion logo"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:216
#, python-format
msgid "Hide Youtube logo"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:213
#, python-format
msgid "Hide fullscreen button"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:210
#, python-format
msgid "Hide player controls"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:222
#, python-format
msgid "Hide sharing button"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:367
#, python-format
msgid "Horizontal"
msgstr ""

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub_id
msgid "ID"
msgstr "ID"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/editor.js:125
#, python-format
msgid ""
"If you discard the current edition, all unsaved changes will be lost. You "
"can cancel to return to the edition mode."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/widgets/ace.js:826
#, python-format
msgid ""
"If you reset this file, all your customizations will be lost as it will be "
"reverted to the default file."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:50
#, python-format
msgid "Image"
msgstr "Slika"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:905
#, python-format
msgid "Image URL"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/translator.xml:10
#, python-format
msgid ""
"In this mode, you can only translate texts. To change the structure of the page, you must edit the master page.\n"
"        Each modification on the master page is automatically applied to all translated versions."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:23
#, python-format
msgid "Include All LESS Files"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:17
#, python-format
msgid "Include Asset Bundles"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:954
#, python-format
msgid "Indent"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Info"
msgstr "Informacije"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:928
#, python-format
msgid "Insert Horizontal Rule"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:896
#, python-format
msgid "Insert Image"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:910
#, python-format
msgid "Insert Link"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:920
#, python-format
msgid "Insert Video"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:198
#, python-format
msgid "Instagram"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:1028
#, python-format
msgid "Install"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:884
#, python-format
msgid "Italic"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:958
#, python-format
msgid "Justify full"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:971
#, python-format
msgid "Keyboard shortcuts"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:11
#, python-format
msgid "LESS (CSS)"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:109
#: code:addons/web_editor/static/src/xml/editor.xml:260
#, python-format
msgid "Large"
msgstr ""

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub___last_update
msgid "Last Modified on"
msgstr "Zadnja promena"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub_write_uid
msgid "Last Updated by"
msgstr "Promenio"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub_write_date
msgid "Last Updated on"
msgstr "Vreme promene"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/widgets/ace.js:360
#, python-format
msgid "Less file: %s"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:890
#, python-format
msgid "Line Height"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:909
#, python-format
msgid "Link"
msgstr "Veza"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:242
#, python-format
msgid "Link Label"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/widgets/widgets.js:1085
#, python-format
msgid "Link to"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:207
#, python-format
msgid "Loop"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:108
#: code:addons/web_editor/static/src/xml/editor.xml:259
#, python-format
msgid "Medium"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:962
#, python-format
msgid "More Color"
msgstr ""

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub_name
msgid "Name"
msgstr "Naziv"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:60
#, python-format
msgid "Next"
msgstr "Sledeće"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:389
#, python-format
msgid "No repeat"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:106
#: code:addons/web_editor/static/src/xml/snippets.xml:34
#, python-format
msgid "None"
msgstr "Prazno"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:932
#, python-format
msgid "Normal"
msgstr "Normalni"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:967
#: code:addons/web_editor/static/src/js/editor/translator.js:94
#, python-format
msgid "Ok"
msgstr "Ok"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/translator.js:93
#, python-format
msgid "Ok, never show me this again"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:915
#: code:addons/web_editor/static/src/xml/editor.xml:286
#, python-format
msgid "Open in new window"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:201
#, python-format
msgid "Options"
msgstr "Opcije"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:944
#, python-format
msgid "Ordered list"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:953
#, python-format
msgid "Outdent"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:113
#, python-format
msgid "Padding"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:952
#, python-format
msgid "Paragraph"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:975
#, python-format
msgid "Paragraph formatting"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:52
#, python-format
msgid "Pictogram"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:228
#: code:addons/web_editor/static/src/xml/editor.xml:294
#, python-format
msgid "Preview"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:57
#, python-format
msgid "Previous"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Primary"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:933
#, python-format
msgid "Quote"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.js:307
#, python-format
msgid "Readonly field"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:961
#, python-format
msgid "Recent Color"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:980
#, python-format
msgid "Redo"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:155
#, python-format
msgid "Remove"
msgstr "Ukloni"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:24
#, python-format
msgid "Remove Block"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:889
#, python-format
msgid "Remove Font Style"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:906
#, python-format
msgid "Remove Image"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:329
#, python-format
msgid "Repeat"
msgstr "Ponovi"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:390
#, python-format
msgid "Repeat both"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:391
#, python-format
msgid "Repeat x"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:392
#, python-format
msgid "Repeat y"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:967
#: code:addons/web_editor/static/src/xml/ace.xml:36
#, python-format
msgid "Reset"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:968
#, python-format
msgid "Reset to default"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/widgets/ace.js:485
#, python-format
msgid "Reseting views is not supported yet"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:897
#, python-format
msgid "Resize Full"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:898
#, python-format
msgid "Resize Half"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:899
#, python-format
msgid "Resize Quarter"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:12
#, python-format
msgid "Resize to force the height of this block"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/widgets/widgets.js:23
#: code:addons/web_editor/static/src/xml/ace.xml:29
#: code:addons/web_editor/static/src/xml/editor.xml:13
#, python-format
msgid "Save"
msgstr "Sačuvaj"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:49
#, python-format
msgid "Search"
msgstr "Pronađi"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:47
#, python-format
msgid "Search Contact"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:17
#, python-format
msgid "Select Parent Container"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/widgets/widgets.js:930
#, python-format
msgid "Select a Media"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:133
#, python-format
msgid "Select a Picture"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:904
#, python-format
msgid "Select from files"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/widgets/ace.js:567
#, python-format
msgid "Server error"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:365
#, python-format
msgid "Set the starting position of the background image."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:966
#, python-format
msgid "Set transparent"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:343
#, python-format
msgid ""
"Sets the width and height of the background image in percent of the parent "
"element."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:120
#, python-format
msgid "Shadow"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:254
#, python-format
msgid "Size"
msgstr "Veličina"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:107
#: code:addons/web_editor/static/src/xml/editor.xml:258
#, python-format
msgid "Small"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:128
#, python-format
msgid "Spin"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:886
#, python-format
msgid "Strikethrough"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:931
#, python-format
msgid "Style"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:887
#, python-format
msgid "Subscript"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Success"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:888
#, python-format
msgid "Superscript"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:925
#, python-format
msgid "Table"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/widgets/ace.js:358
#, python-format
msgid "Template ID: %s"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:973
#, python-format
msgid "Text formatting"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:913
#, python-format
msgid "Text to display"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:158
#, python-format
msgid ""
"The image could not be deleted because it is used in the\n"
"               following pages or views:"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/widgets/widgets.js:871
#, python-format
msgid "The provided url does not reference any supported video"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/widgets/widgets.js:865
#, python-format
msgid "The provided url is not valid"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:53
#, python-format
msgid "Theme colors"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/editor.js:85
#: code:addons/web_editor/static/src/js/editor/translator.js:177
#, python-format
msgid "This document is not saved!"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:914
#, python-format
msgid "To what URL should this link go?"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:40
#, python-format
msgid "Tooltip"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/translator.js:50
#, python-format
msgid "Translate Attribute"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/translator.xml:8
#, python-format
msgid "Translated content"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/translator.js:91
#, python-format
msgid "Translation Info"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:965
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#, python-format
msgid "Transparent"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:248
#, python-format
msgid "URL or Email"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:885
#, python-format
msgid "Underline"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:979
#, python-format
msgid "Undo"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/widgets/ace.js:86
#, python-format
msgid "Unexpected "
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:911
#, python-format
msgid "Unlink"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:943
#, python-format
msgid "Unordered list"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:96
#, python-format
msgid "Upload an image"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:103
#, python-format
msgid "Upload image without optimization"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:107
#, python-format
msgid "Uploading..."
msgstr "Učitavanje..."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:376
#, python-format
msgid "Vertical"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:918
#: code:addons/web_editor/static/src/xml/editor.xml:53
#, python-format
msgid "Video"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:919
#, python-format
msgid "Video Link"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:921
#, python-format
msgid "Video URL?"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:193
#, python-format
msgid "Video code"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:198
#, python-format
msgid "Vimeo"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:198
#, python-format
msgid "Vine.co"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Warning"
msgstr "Upozorenje"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "White"
msgstr "Bijela"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:8
#: code:addons/web_editor/static/src/xml/ace.xml:10
#, python-format
msgid "XML (HTML)"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/rte.summernote.js:110
#, python-format
msgid "Xl"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/translator.xml:4
#, python-format
msgid "You are about to enter the translation mode."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:198
#, python-format
msgid "Youku"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:198
#, python-format
msgid "Youtube"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:198
#, python-format
msgid "and"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:348
#: code:addons/web_editor/static/src/xml/editor.xml:357
#: code:addons/web_editor/static/src/xml/editor.xml:370
#: code:addons/web_editor/static/src/xml/editor.xml:379
#, python-format
msgid "auto"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:113
#, python-format
msgid "https://www.odoo.com/logo.png"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_attachment
msgid "ir.attachment"
msgstr "ir.attachment"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb
msgid "ir.qweb"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field
msgid "ir.qweb.field"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_contact
msgid "ir.qweb.field.contact"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_date
msgid "ir.qweb.field.date"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_datetime
msgid "ir.qweb.field.datetime"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_duration
msgid "ir.qweb.field.duration"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_float
msgid "ir.qweb.field.float"
msgstr "ir.qweb.field.float"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_html
msgid "ir.qweb.field.html"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_image
msgid "ir.qweb.field.image"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_integer
msgid "ir.qweb.field.integer"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_many2one
msgid "ir.qweb.field.many2one"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_monetary
msgid "ir.qweb.field.monetary"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_qweb
msgid "ir.qweb.field.qweb"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_relative
msgid "ir.qweb.field.relative"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_selection
msgid "ir.qweb.field.selection"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_text
msgid "ir.qweb.field.text"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_translation
msgid "ir.translation"
msgstr "ir.translation"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_ui_view
msgid "ir.ui.view"
msgstr "ir.ui.view"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:198
#, python-format
msgid "videos"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_converter_test
msgid "web_editor.converter.test"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_converter_test_sub
msgid "web_editor.converter.test.sub"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:110
#, python-format
msgid "— or —"
msgstr ""
