<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data noupdate="1">


    <!--  Maintenance teams -->
    <record id="equipment_team_metrology" model="maintenance.team">
        <field name="name">Metrology</field>
    </record>
    <record id="equipment_team_subcontractor" model="maintenance.team">
        <field name="name">Subcontractor</field>
    </record>

    <!-- Equipment categories -->
    <record id="equipment_computer" model="maintenance.equipment.category">
        <field name="name">Computers</field>
    </record>
    <record id="equipment_software" model="maintenance.equipment.category">
        <field name="name">Software</field>
    </record>
    <record id="equipment_printer" model="maintenance.equipment.category">
        <field name="name">Printers</field>
    </record>
    <record id="equipment_monitor" model="maintenance.equipment.category">
        <field name="name">Monitors</field>
        <field name="technician_user_id" ref="base.user_admin"/>
        <field name="color">3</field>
    </record>
    <record id="equipment_phone" model="maintenance.equipment.category">
        <field name="name">Phones</field>
        <field name="technician_user_id" ref="base.user_admin"/>
    </record>

    <!-- Equipments -->
    <record id="equipment_monitor1" model="maintenance.equipment">
        <field name="name">Samsung Monitor 15"</field>
        <field name="category_id" ref="equipment_monitor"/>
        <field name="owner_user_id" ref="base.user_admin"/>
        <field name="technician_user_id" ref="base.user_admin"/>
        <field name="assign_date" eval="time.strftime('%Y-%m-10')"/>
        <field name="serial_no">MT/122/11112222</field>
        <field name="model">NP300E5X</field>
    </record>
    <record id="equipment_monitor4" model="maintenance.equipment">
        <field name="name">Samsung Monitor 15"</field>
        <field name="category_id" ref="equipment_monitor"/>
        <field name="owner_user_id" ref="base.user_admin"/>
        <field name="technician_user_id" ref="base.user_admin"/>
        <field name="assign_date" eval="time.strftime('%Y-01-01')"/>
        <field name="serial_no">MT/125/22778837</field>
        <field name="model">NP355E5X</field>
    </record>
    <record id="equipment_monitor6" model="maintenance.equipment">
        <field name="name">Samsung Monitor 15"</field>
        <field name="category_id" ref="equipment_monitor"/>
        <field name="owner_user_id" ref="base.user_demo"/>
        <field name="technician_user_id" ref="base.user_demo"/>
        <field name="assign_date" eval="time.strftime('%Y-02-01')"/>
        <field name="serial_no">MT/127/18291018</field>
        <field name="model">NP355E5X</field>
        <field name="color">3</field>
    </record>
     <record id="equipment_computer3" model="maintenance.equipment">
        <field name="name">Acer Laptop</field>
        <field name="category_id" ref="equipment_computer"/>
        <field name="owner_user_id" ref="base.user_demo"/>
        <field name="technician_user_id" ref="base.user_admin"/>
        <field name="assign_date" eval="time.strftime('%Y-03-08')"/>
        <field name="serial_no">LP/203/19281928</field>
        <field name="model">NE56R</field>
     </record>
     <record id="equipment_computer5" model="maintenance.equipment">
        <field name="name">Acer Laptop</field>
        <field name="category_id" ref="equipment_computer"/>
        <field name="owner_user_id" ref="base.user_admin"/>
        <field name="technician_user_id" ref="base.user_demo"/>
        <field name="assign_date" eval="time.strftime('%Y-04-08')"/>
        <field name="serial_no">LP/205/12928291</field>
        <field name="model">V5131</field>
     </record>
     <record id="equipment_computer9" model="maintenance.equipment">
        <field name="name">HP Laptop</field>
        <field name="category_id" ref="equipment_computer"/>
        <field name="owner_user_id" ref="base.user_admin"/>
        <field name="technician_user_id" ref="base.user_demo"/>
        <field name="assign_date" eval="time.strftime('%Y-%m-11')"/>
        <field name="serial_no">LP/303/28292090</field>
        <field name="model">17-j059nr</field>
     </record>
     <record id="equipment_computer11" model="maintenance.equipment">
        <field name="name">HP Laptop</field>
        <field name="category_id" ref="equipment_computer"/>
        <field name="owner_user_id" ref="base.user_demo"/>
        <field name="technician_user_id" ref="base.user_demo"/>
        <field name="assign_date" eval="time.strftime('%Y-05-01')"/>
        <field name="serial_no">LP/305/17281718</field>
     </record>
     <record id="equipment_printer1" model="maintenance.equipment">
        <field name="name">HP Inkjet printer</field>
        <field name="category_id" ref="equipment_printer"/>
        <field name="technician_user_id" ref="base.user_demo"/>
        <field name="serial_no">PR/011/2928191889</field>
     </record>

    <!--Maintenance Request-->

    <record id="m_request_3" model="maintenance.request">
        <field name="name">Resolution is bad</field>
        <field name="user_id" ref="base.user_demo"/>
        <field name="owner_user_id" ref="base.user_admin"/>
        <field name="equipment_id" ref="equipment_monitor6"/>
        <field name="color">7</field>
        <field name="stage_id" ref="stage_3"/>
        <field name="maintenance_team_id" ref="equipment_team_maintenance"/>
    </record>
    <record id="m_request_4" model="maintenance.request">
        <field name="name">Some keys are not working</field>
        <field name="user_id" ref="base.user_admin"/>
        <field name="owner_user_id" ref="base.user_admin"/>
        <field name="equipment_id" ref="equipment_computer3"/>
        <field name="stage_id" ref="stage_0"/>
        <field name="maintenance_team_id" ref="equipment_team_maintenance"/>
    </record>
    <record id="m_request_6" model="maintenance.request">
        <field name="name">Motherboard failed</field>
        <field name="user_id" ref="base.user_demo"/>
        <field name="owner_user_id" ref="base.user_admin"/>
        <field name="equipment_id" ref="equipment_computer5"/>
         <field name="stage_id" ref="stage_4"/>
         <field name="maintenance_team_id" ref="equipment_team_maintenance"/>
    </record>
    <record id="m_request_7" model="maintenance.request">
        <field name="name">Battery drains fast</field>
        <field name="user_id" ref="base.user_demo"/>
        <field name="owner_user_id" ref="base.user_demo"/>
        <field name="equipment_id" ref="equipment_computer9"/>
        <field name="stage_id" ref="stage_1"/>
        <field name="maintenance_team_id" ref="equipment_team_maintenance"/>
    </record>
    <record id="m_request_8" model="maintenance.request">
        <field name="name">Touchpad not working</field>
        <field name="user_id" ref="base.user_demo"/>
        <field name="owner_user_id" ref="base.user_demo"/>
        <field name="equipment_id" ref="equipment_computer11"/>
        <field name="stage_id" ref="stage_1"/>
        <field name="maintenance_team_id" ref="equipment_team_maintenance"/>
    </record>
</data>
</odoo>
