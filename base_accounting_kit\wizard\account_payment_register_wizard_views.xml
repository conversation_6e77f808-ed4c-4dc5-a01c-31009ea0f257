<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="view_account_payment_register_form" model="ir.ui.view">
        <field name="model">account.payment.register</field>
        <field name="inherit_id"
               ref="account.view_account_payment_register_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='payment_date']"
                   position="after">
                <field name="effective_date"
                       attrs="{'invisible': [('payment_method_code', '!=', 'pdc')]}"/>
            </xpath>
            <xpath expr="//field[@name='partner_bank_id']"
                   position="after">
                <field name="bank_reference"/>
            </xpath>
            <xpath expr="//field[@name='partner_bank_id']"
                   position="after">
                <field name="cheque_reference"/>
            </xpath>
        </field>
    </record>
</odoo>