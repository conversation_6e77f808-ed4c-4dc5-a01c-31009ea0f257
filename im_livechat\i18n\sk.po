# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* im_livechat
# 
# Translators:
# SAKod<PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON> <brencic<PERSON><EMAIL>>, 2022
# g<PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <alexandra.brenci<PERSON><EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# karol<PERSON>a schustero<PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-08-24 01:53+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Slovak (https://app.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "# Messages"
msgstr "# Správy"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_count
msgid "# Ratings"
msgstr "# Hodnotenia"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__nbr_channel
msgid "# of Sessions"
msgstr "Počet relácií"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__nbr_speaker
msgid "# of speakers"
msgstr "# reproduktorov"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "% Happy"
msgstr "% Spokojný"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_rating
msgid "% of Happiness"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "%s and %s are typing..."
msgstr "%s a %s píšu..."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "%s is typing..."
msgstr "%s píše..."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "%s, %s and more are typing..."
msgstr "%s, %s a ďalší píšu..."

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__action
msgid ""
"* 'Display the button' displays the chat button on the pages.\n"
"* 'Auto popup' displays the button and automatically open the conversation pane.\n"
"* 'Hide the button' hides the chat button on the pages."
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid ", on the"
msgstr ", na"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "-------- Show older messages --------"
msgstr "-------- Zobraziť staršie správy --------"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid ""
"<i class=\"fa fa-smile-o text-success\" title=\"Percentage of happy "
"ratings\" role=\"img\" aria-label=\"Happy face\"/>"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "<i class=\"fa fa-user\" role=\"img\" aria-label=\"User\" title=\"User\"/>"
msgstr "<i class=\"fa fa-user\" role=\"img\" aria-label=\"User\" title=\"User\"/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"<span class=\"text-muted\">Define rules for your live support channel. You "
"can apply an action for the given URL, and per country.<br/>To identify the "
"country, GeoIP must be installed on your server, otherwise, the countries of"
" the rule will not be taken into account.</span>"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span style=\"font-size: 10px;\">Livechat Conversation</span><br/>"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span>Best regards,</span><br/><br/>"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span>Hello,</span><br/>Here's a copy of your conversation with"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__is_without_answer
msgid ""
"A session is without answer if the operator did not answer. \n"
"                                       If the visitor is also the operator, the session will always be answered."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__action
msgid "Action"
msgstr "Akcia"

#. module: im_livechat
#: model:res.groups,name:im_livechat.im_livechat_group_manager
msgid "Administrator"
msgstr "Správca"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
msgid "Anonymous"
msgstr "Anonymné"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__anonymous_name
msgid "Anonymous Name"
msgstr "Anonymný názov"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__are_you_inside
msgid "Are you inside the matrix?"
msgstr "Ste vo vnútri matrice?"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Ask something ..."
msgstr "Opýtajte sa niečo ..."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "Attendees"
msgstr "Účastníci"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__auto_popup
msgid "Auto popup"
msgstr "Auto popup"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__auto_popup_timer
msgid "Auto popup timer"
msgstr "Auto popup časovač"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
msgid "Avatar"
msgstr "Avatar"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__duration
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__duration
msgid "Average duration"
msgstr "Priemerné trvanie"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__nbr_message
msgid "Average message"
msgstr "Priemerná správa"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__time_to_answer
msgid "Average time in seconds to give the first answer to the visitor"
msgstr "Priemerný čas v sekundách na prvé odpovedanie návštevníkovi"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__time_to_answer
msgid "Average time to give the first answer to the visitor"
msgstr "Priemerný čas na danie prvej odpovede návštevníkovi"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Bad"
msgstr "Zlé"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Bounced"
msgstr "Odmietnuté"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_background_color
msgid "Button Background Color"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_text_color
msgid "Button Text Color"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Canceled"
msgstr "Zrušené"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_canned_response_action
#: model:ir.ui.menu,name:im_livechat.canned_responses
msgid "Canned Responses"
msgstr "Konzervované odpovede"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_canned_response_action
msgid ""
"Canned responses allow you to insert prewritten responses in\n"
"                your messages by typing <i>:shortcut</i>. The shortcut is\n"
"                replaced directly in your message, so that you can still edit\n"
"                it before sending."
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Changed"
msgstr "Zmenené"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__livechat_channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__livechat_channel_id
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__livechat_channel_id
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Channel"
msgstr "Kanál"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Channel Header Color"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__channel_name
msgid "Channel Name"
msgstr "Názov kanála"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "Channel Rule"
msgstr "Pravidlo kanála"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Channel Rules"
msgstr "Pravidlá kanála"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__channel_type
msgid "Channel Type"
msgstr "Typ kanálu"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.support_channels
msgid "Channels"
msgstr "Kanály"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__input_placeholder
msgid "Chat Input Placeholder"
msgstr "Vlastník vstupu chatu-u"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_mail_channel__channel_type
msgid ""
"Chat is private and unique between 2 persons. Group is private among invited"
" persons. Channel can be freely joined (depending on its configuration)."
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Chat with one of our collaborators"
msgstr "Chatujte s jedným z našich spolupracovníkov"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Close"
msgstr "Zatvoriť"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Close chat window"
msgstr "Zavri okno chatu"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Close conversation"
msgstr "Zatvoriť konverzáciu"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__technical_name
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "Code"
msgstr "Kód"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.livechat_config
msgid "Configuration"
msgstr "Konfigurácia"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__channel_id
msgid "Conversation"
msgstr "Konverzácia"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Conversation Sent"
msgstr "Poslať konverzáciu"

#. module: im_livechat
#: code:addons/im_livechat/models/mail_channel.py:0
#, python-format
msgid "Conversation with %s"
msgstr "Konverzácia s %s"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_conversations
msgid "Conversations handled"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Copy and paste this code into your website, within the &lt;head&gt; tag:"
msgstr ""
"Skopírujte a vložte tento kód do vašej webstránky, v rámci  &lt;head&gt; "
"tagu:"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__country_ids
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__country_id
msgid "Country"
msgstr "Štát"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__country_id
msgid "Country of the visitor"
msgstr "Krajina návštavníka"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_mail_channel__country_id
msgid "Country of the visitor of the channel"
msgstr "Krajina návštevníka kanálu"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.mail_channel_action
msgid "Create a channel and start chatting to fill up your history."
msgstr "Vytvorte kanál a začnite živý chat, aby ste vyplnili svoju históriu."

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_canned_response_action
msgid "Create a new canned response"
msgstr "Vytvorte novú konzervovanú odpoveď"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__create_uid
msgid "Created by"
msgstr "Vytvoril"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__create_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__create_date
msgid "Created on"
msgstr "Vytvorené"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Creation Date"
msgstr "Dátum vytvorenia"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Creation date"
msgstr "Dátum vytvorenia"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Creation date (hour)"
msgstr "Dátum vytvorenia (hodina)"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.rating_rating_action_livechat_report
#: model:ir.ui.menu,name:im_livechat.rating_rating_menu_livechat
msgid "Customer Ratings"
msgstr "Hodnotenie zákazníkov"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__day_number
msgid "Day Number"
msgstr "Číslo dňa"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__day_number
msgid "Day number of the session (1 is Monday, 7 is Sunday)"
msgstr "Číslo dňa relácie (1 je pondelok, 7 je nedeľa)"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__days_of_activity
msgid "Days of activity"
msgstr "Dni aktivity"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_background_color
msgid "Default background color of the Livechat button"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__header_background_color
msgid "Default background color of the channel header once open"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_text_color
msgid "Default text color of the Livechat button"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_text
msgid "Default text displayed on the Livechat Support Button"
msgstr "Prednastavený text zobrazený na Tlačidle podpory živého chatu"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__title_color
msgid "Default title color of the channel once open"
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_channel_action
msgid "Define a new website live chat channel"
msgstr "Definujte nový kanál živého chat-u webstránok"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__auto_popup_timer
msgid ""
"Delay (in seconds) to automatically open the conversation window. Note: the "
"selected action must be 'Auto popup' otherwise this parameter will not be "
"taken into account."
msgstr ""
"Oneskorenie (v sekundách), aby sa automaticky otvorilo okno konverzácie. "
"Poznámka: zvolená akcia musí byť „automatické vyskakovacie okno“, inak sa "
"tento parameter nebude brať do úvahy."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Delete"
msgstr "Zmazať"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Did we correctly answer your question ?"
msgstr "Odpovedali sme správne na vašu otázku?"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_digest_digest
msgid "Digest"
msgstr "Prehľad"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_mail_channel
msgid "Discussion Channel"
msgstr "Diskusný kanál"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__display_name
msgid "Display Name"
msgstr "Zobrazovaný názov"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__display_button
msgid "Display the button"
msgstr "Zobraziť tlačidlo"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Document not downloadable"
msgstr "Dokument nie je možné stiahnuť"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Download"
msgstr "Stiahnuť"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__duration
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__duration
msgid "Duration of the conversation (in seconds)"
msgstr "Trvanie konverzácie (v sekundách)"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Error"
msgstr "Chyba"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Explain your note"
msgstr "Vysvetlite svoju poznámku"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"For websites built with the Odoo CMS, go to Website &gt; Configuration &gt; "
"Settings and select the Website Live Chat Channel you want to add to your "
"website."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__sequence
msgid ""
"Given the order to find a matching rule. If 2 rules are matching for the "
"given url/country, the one with the lowest sequence will be chosen."
msgstr ""
"Dané poradie pre nájdenie zodpovedajúceho pravidla. Ak 2 pravidlá "
"zodpovedajú danej url/krajine, tá s najnižšou sekvenciou bude zvolená."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Good"
msgstr "Dobré"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Group By..."
msgstr "Zoskupiť podľa..."

#. module: im_livechat
#: code:addons/im_livechat/models/im_livechat_channel.py:0
#: model:im_livechat.channel,button_text:im_livechat.im_livechat_channel_data
#, python-format
msgid "Have a Question? Chat with us."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__header_background_color
msgid "Header Background Color"
msgstr ""

#. module: im_livechat
#: model:im_livechat.channel,default_message:im_livechat.im_livechat_channel_data
msgid "Hello, how may I help you?"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__hide_button
msgid "Hide the button"
msgstr "Skryť tlačidlo"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.mail_channel_action
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "History"
msgstr "História"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_date_hour
msgid "Hour of start Date of session"
msgstr "Hodina začiatku Dátum relácie"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/models/im_livechat_channel.py:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "How may I help you?"
msgstr "Ako Vám môžem pomôcť?"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "How to use the Website Live Chat widget?"
msgstr "Ako pouźiť miniaplikáciu Živý chat webstránky?"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__id
msgid "ID"
msgstr "ID"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Idle"
msgstr "Nečinný"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__image_128
#, python-format
msgid "Image"
msgstr "Obrázok"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Invalid email address"
msgstr "Neplatná emailová adresa"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users_settings__is_discuss_sidebar_category_livechat_open
msgid "Is category livechat open"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__livechat_active
msgid "Is livechat ongoing?"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_anonymous
msgid "Is visitor anonymous"
msgstr "Návštevník je anonymný"

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_3
msgid "Joel Willis, Marc Demo"
msgstr ""

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_2
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_6
msgid "Joel Willis, Mitchell Admin"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Join"
msgstr "Pripojiť"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Join Channel"
msgstr "Pripojiť sa ku kanálu"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_conversations_value
msgid "Kpi Livechat Conversations Value"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_rating_value
msgid "Kpi Livechat Rating Value"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_response_value
msgid "Kpi Livechat Response Value"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Last 24h"
msgstr "Posledných 24h"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel____last_update
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule____last_update
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel____last_update
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator____last_update
msgid "Last Modified on"
msgstr "Posledná úprava"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__write_uid
msgid "Last Updated by"
msgstr "Naposledy upravoval"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__write_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__write_date
msgid "Last Updated on"
msgstr "Naposledy upravované"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Leave"
msgstr "Voľno"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Leave Channel"
msgstr "Opustiť kanál"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_mail_channel_partner
msgid "Listeners of a Channel"
msgstr "Poslucháči kanála"

#. module: im_livechat
#: model:ir.module.category,name:im_livechat.module_category_im_livechat
#: model:ir.ui.menu,name:im_livechat.menu_livechat_root
#: model_terms:ir.ui.view,arch_db:im_livechat.digest_digest_view_form_inherit
msgid "Live Chat"
msgstr "Živý chat"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_search
msgid "LiveChat Channel Search"
msgstr "Vyhľadávanie kanálov živý chat"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/components/discuss/discuss.js:0
#: code:addons/im_livechat/static/src/components/thread_icon/thread_icon.xml:0
#: code:addons/im_livechat/static/src/models/messaging_initializer/messaging_initializer.js:0
#: model_terms:ir.ui.view,arch_db:im_livechat.res_users_form_view
#, python-format
msgid "Livechat"
msgstr "Živý chat"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Button"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Button Color"
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_channel
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "Livechat Channel"
msgstr "Živý chat kanál"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_channel_rule
msgid "Livechat Channel Rules"
msgstr "Pravidlá kanála živý chat"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__mail_channel__channel_type__livechat
msgid "Livechat Conversation"
msgstr "Livechat konverzácia"

#. module: im_livechat
#: model:ir.model.constraint,message:im_livechat.constraint_mail_channel_livechat_operator_id
msgid "Livechat Operator ID is required for a channel of type livechat."
msgstr "Pre kanál typu živý chat sa vyžaduje ID operátora živý chat."

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_report_channel
msgid "Livechat Support Channel Report"
msgstr "Správa o kanáli podpory živý chat"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_channel_action
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_operator_action
msgid ""
"Livechat Support Channel Statistics allows you to easily check and analyse "
"your company livechat session performance. Extract information about the "
"missed sessions, the audiance, the duration of a session, etc."
msgstr ""
"Štatistika kanála podpory živý chat vám umožňuje ľahko skontrolovať a "
"analyzovať výkon relácie živý chat vašej spoločnosti. Extrahujte informácie "
"o zmeškaných reláciách, počte divákov, trvaní relácie atď."

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_report_operator
msgid "Livechat Support Operator Report"
msgstr "Správa operátora živý chat"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_pivot
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_pivot
msgid "Livechat Support Statistics"
msgstr "Štatistiky podpory živého chatu"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users__livechat_username
msgid "Livechat Username"
msgstr "Používateľské meno živý chat"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Window"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_mail_channel__livechat_active
msgid "Livechat session is active until visitor leave the conversation."
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Loading"
msgstr "Nahrávanie"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Loading older messages..."
msgstr "Načítanie starších správ...."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Mark as Read"
msgstr "Označiť ako prečítané"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Mark as Todo"
msgstr "Označiť ako Úloha"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Mark as todo"
msgstr "Označiť ako úloha"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__sequence
msgid "Matching order"
msgstr "Zodpovedajúca objednávka"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_mail_message
msgid "Message"
msgstr "Správa"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Missed sessions"
msgstr "Zmeškané relácie"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__name
msgid "Name"
msgstr "Meno"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "New messages"
msgstr "Nové správy"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Next"
msgstr "Ďalšie"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "No available collaborator, please try again later."
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.rating_rating_action_livechat_report
msgid "No customer ratings on live chat session yet"
msgstr "Zatiaľ nie sú k dispozícii žiadne hodnotenia zákazníkov v živom chate"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_channel_time_to_answer_action
msgid "No data yet!"
msgstr "Zatiaľ žiadne údaje!"

#. module: im_livechat
#: code:addons/im_livechat/models/mail_channel.py:0
#, python-format
msgid "No history found"
msgstr "Nenašla sa žiadna história"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Note by"
msgstr "Záznam od"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__nbr_channel
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__nbr_channel
msgid "Number of conversation"
msgstr "Počet konverzácií"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__days_of_activity
msgid "Number of days since the first session of the operator"
msgstr "Počet dní od prvého zasadnutia operátora"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__nbr_speaker
msgid "Number of different speakers"
msgstr "Počet rôznych reporduktorov"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__nbr_message
msgid "Number of message in the conversation"
msgstr "Počet správ v konverzácii"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "OK"
msgstr "OK"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "Odoo"
msgstr "Odoo"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Offline"
msgstr "Offline"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Online"
msgstr "Online"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.res_users_form_view_simple_modif
msgid "Online Chat Name"
msgstr "Meno na online chate"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Oops! Something went wrong."
msgstr "Ojoj! Niečo sa pokazilo."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__partner_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__partner_id
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__livechat_operator_id
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Operator"
msgstr "Operátor"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_operator_action
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat_operator
msgid "Operator Analysis"
msgstr "Analýza operátora"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_mail_channel__livechat_operator_id
msgid "Operator for this specific channel"
msgstr "Prevádzkovateľ pre tento konkrétny kanál"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__user_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Operators"
msgstr "Operátori"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid ""
"Operators\n"
"                                            <br/>\n"
"                                            <i class=\"fa fa-comments\" role=\"img\" aria-label=\"Comments\" title=\"Comments\"/>"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Operators that do not show any activity In Odoo for more than 30 minutes "
"will be considered as disconnected."
msgstr ""
"Prevádzkovatelia, ktorí v Odoo nevykazujú žiadnu aktivitu dlhšie ako 30 "
"minút, sa budú považovať za odpojených."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Options"
msgstr "Možnosti"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "PDF file"
msgstr "Súbor PDF"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr "Percento spokojných hodnotení"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Please check your internet connection."
msgstr "Skontrolujte svoje internetové pripojenie."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Please wait"
msgstr "Prosím čakajte"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Please wait..."
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "Powered by"
msgstr "Prinášané"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Previous"
msgstr "Predchádzajúce"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Print"
msgstr "Tlač"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_rating_rating
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__rating
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_form_livechat
msgid "Rating"
msgstr "Hodnotenie"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_avg
msgid "Rating Average"
msgstr "Hodnotenie priemer"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Hodnotenie posledná spätná väzba"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_last_image
msgid "Rating Last Image"
msgstr "Hodnotenie posledný obrázok"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_last_value
msgid "Rating Last Value"
msgstr "Hodnotenie posledná hodnota"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "Spokojnosť s hodnotením"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel__rating_count
msgid "Rating count"
msgstr "Počet hodnotení"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Rating: %s"
msgstr "Hodnotenie: %s"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_ids
msgid "Ratings"
msgstr "Hodnotenia"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.rating_rating_action_livechat
msgid "Ratings for livechat channel"
msgstr "Hodnotenia pre kanál živý chat"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Ready"
msgstr "Pripravené"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_mail_channel__rating_last_feedback
msgid "Reason of the rating"
msgstr "Dôvod hodnotenia"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Receive a copy of this conversation"
msgstr "Získajte kópiu tejto konverzácie"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Received by Everyone"
msgstr "Každý je prijímateľom"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Received by:"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__regex_url
msgid ""
"Regular expression specifying the web pages this rule will be applied on."
msgstr ""
"Regulárny výraz určujúci webstránky, na ktoré sa toto pravidlo bude "
"vzťahovať."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Reply"
msgstr "Odpoveď"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat
msgid "Report"
msgstr "Report"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Reset Zoom"
msgstr "Zruš priblíženie"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Reset to default colors"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Rotate"
msgstr "Otočiť"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rule_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_tree
msgid "Rules"
msgstr "Pravidlá"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__rating_text
msgid "Satisfaction Rate"
msgstr "Miera spokojnosti"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Save your Channel to get your configuration widget."
msgstr "Uložte si kanál a získajte konfiguračný widget."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Say something"
msgstr "Napíšte niečo"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__script_external
msgid "Script (external)"
msgstr "Skript (externý)"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Search history"
msgstr "Vyhľadať históriu"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Search report"
msgstr "Hľadať výkaz"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "See 15 last visited pages"
msgstr "Zobraziť 15 naposledy navštívených stránok"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Seen by Everyone"
msgstr "Videli všetci"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Seen by:"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Send"
msgstr "Poslať"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Sent"
msgstr "Poslané"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "Session Date"
msgstr "Dátum Relácie"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
msgid "Session Form"
msgstr "Formulár relácie"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_channel_action
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_channel_time_to_answer_action
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat_channel
msgid "Session Statistics"
msgstr "Štatistiky relácie"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Session expired... Please refresh and try again."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_unrated
msgid "Session not rated"
msgstr "Relácia nie je hodnotená"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_without_answer
msgid "Session(s) without answer"
msgstr "Relácia(e) bez odpovede"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.mail_channel_action_from_livechat_channel
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__channel_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Sessions"
msgstr "Relácie"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.session_history
msgid "Sessions History"
msgstr "História relácií"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__start_date
msgid "Start Date of session"
msgstr "Dátum začiatku relácie"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_hour
msgid "Start Hour of session"
msgstr "Začiatočná hodina relácie"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__start_date
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__start_date
msgid "Start date of the conversation"
msgstr "Dátum začiatku konverzácie"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__start_hour
msgid "Start hour of the conversation"
msgstr "Začiatok hodiny konverzácie"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_text
msgid "Text of the Button"
msgstr "Text tlačidla"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__input_placeholder
msgid "Text that prompts the user to initiate the chat."
msgstr "Text, ktorý vyzve používateľa na začatie chatu."

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Thank you for your feedback"
msgstr "Ďakujeme vám za vašu reakciu"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__channel_id
msgid "The channel of the rule"
msgstr "Kanál pravidla"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__name
msgid "The name of the channel"
msgstr "Názov kanála"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__country_ids
msgid ""
"The rule will only be applied for these countries. Example: if you select "
"'Belgium' and 'United States' and that you set the action to 'Hide Button', "
"the chat button will be hidden on the specified URL from the visitors "
"located in these 2 countries. This feature requires GeoIP installed on your "
"server."
msgstr ""
"Pravidlo sa bude uplatňovať iba pre tieto krajiny. Príklad: ak vyberiete "
"možnosť „Belgicko“ a „Spojené štáty“ a akciu nastavíte na možnosť „Skryť "
"tlačidlo“, bude tlačidlo chat-u skryté na zadanej adrese URL pred "
"návštevníkmi nachádzajúcimi sa v týchto dvoch krajinách. Táto funkcia "
"vyžaduje, aby bol na vašom serveri nainštalovaný GeoIP."

#. module: im_livechat
#: model:res.groups,comment:im_livechat.im_livechat_group_manager
msgid "The user will be able to delete support channels."
msgstr "Používateľ bude môcť zmazať kanály podpory."

#. module: im_livechat
#: model:res.groups,comment:im_livechat.im_livechat_group_user
msgid "The user will be able to join support channels."
msgstr "Používateľ sa bude môcť pripojiť na kanály podpory."

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.rating_rating_action_livechat
msgid "There is no rating for this channel at the moment"
msgstr "Pre tento kanál momentálne nie je k dispozícii žiadne hodnotenie"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "This Week"
msgstr "Tento týždeň"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__default_message
msgid ""
"This is an automated 'welcome' message that your visitor will see when they "
"initiate a new conversation."
msgstr ""
"Toto je automatizovaná 'vitajte' správa ktorú váš návštevník uvidí pri "
"iniciovaní novej konverzácie."

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_res_users__livechat_username
msgid "This username will be used as your name in the livechat channels."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__time_to_answer
msgid "Time to answer"
msgstr "Čas na odpoveď"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__time_to_answer
msgid "Time to answer (sec)"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_digest_digest__kpi_livechat_response
msgid "Time to answer the user in second."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_response
msgid "Time to answer(sec)"
msgstr ""

#. module: im_livechat
#: model:digest.tip,name:im_livechat.digest_tip_im_livechat_0
#: model_terms:digest.tip,tip_description:im_livechat.digest_tip_im_livechat_0
msgid "Tip: Use canned responses to chat faster"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__title_color
msgid "Title Color"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Today"
msgstr "Dnes"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Treated sessions"
msgstr "Spracované relácie"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Try again"
msgstr ""

#. module: im_livechat
#: code:addons/im_livechat/models/mail_channel.py:0
#, python-format
msgid "Type <b>:shortcut</b> to insert a canned response in your message.<br>"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__regex_url
msgid "URL Regex"
msgstr "URL Regex"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__web_page
msgid ""
"URL to a static page where you client can discuss with the operator of the "
"channel."
msgstr ""
"URL na statickú stránku kde váš klient môže diskutovať s operátorom kanálu."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__uuid
msgid "UUID"
msgstr "UUID"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Undefined"
msgstr "Nedefinované"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Uploaded"
msgstr "Nahrané"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Uploading"
msgstr "Nahrávam"

#. module: im_livechat
#: model_terms:digest.tip,tip_description:im_livechat.digest_tip_im_livechat_0
msgid ""
"Use canned responses to define templates of messages in the livechat app. To"
" load a canned response, start your sentence with ':' and select the "
"template."
msgstr ""

#. module: im_livechat
#: model:res.groups,name:im_livechat.im_livechat_group_user
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "User"
msgstr "Užívateľ"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_partner__user_livechat_username
#: model:ir.model.fields,field_description:im_livechat.field_res_users__user_livechat_username
msgid "User Livechat Username"
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_users_settings
msgid "User Settings"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "User is idle"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "User is offline"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "User is online"
msgstr "Používateľ je online"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_users
msgid "Users"
msgstr "Užívatelia"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Video"
msgstr "Video"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Viewer"
msgstr "Prehliadač"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/controllers/main.py:0
#: code:addons/im_livechat/models/im_livechat_channel.py:0
#: code:addons/im_livechat/models/mail_channel.py:0
#: code:addons/im_livechat/models/mail_channel.py:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Visitor"
msgstr "Návštevník"

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_0
msgid "Visitor #234, Mitchell Admin"
msgstr ""

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_1
msgid "Visitor #323, Marc Demo"
msgstr ""

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_4
msgid "Visitor #532, Mitchell Admin"
msgstr ""

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_5
msgid "Visitor #649, Mitchell Admin"
msgstr ""

#. module: im_livechat
#: model:mail.channel,name:im_livechat.im_livechat_mail_channel_data_7
msgid "Visitor #722, Marc Demo"
msgstr ""

#. module: im_livechat
#: code:addons/im_livechat/models/mail_channel.py:0
#, python-format
msgid "Visitor has left the conversation."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_happy
msgid "Visitor is Happy"
msgstr ""

#. module: im_livechat
#: model:mail.channel,name:im_livechat.mail_channel_livechat_1
msgid "Visitor, Mitchell Admin"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__web_page
msgid "Web Page"
msgstr "Webstránka"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_channel_action
msgid "Website Live Chat Channels"
msgstr "Kanály živého chatu webstránky"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__default_message
msgid "Welcome Message"
msgstr "Uvítacia správa"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Widget"
msgstr "Widget"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "Yesterday"
msgstr "Včera"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_channel_action
msgid ""
"You can create channels for each website on which you want\n"
"                to integrate the website live chat widget, allowing your website\n"
"                visitors to talk in real time with your operators."
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.mail_channel_action
msgid "Your chatter history is empty"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Zoom In"
msgstr "Priblížiť"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "Zoom Out"
msgstr "Vzdialiť"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "e.g. /contactus"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "e.g. Hello, how may I help you?"
msgstr "napr. Dobrý deň, ako vám môžem pomôcť?"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "e.g. YourWebsite.com"
msgstr "napr. VasaWebstranka.com"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "from"
msgstr "od"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "<EMAIL>"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.xml:0
#, python-format
msgid "on"
msgstr "na"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "or copy this url and send it by email to your customers or suppliers:"
msgstr ""
"alebo skopírujte túto URL a pošlite ju emailom svojim zákazníkom alebo "
"dodávateľom:"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "read less"
msgstr "čítať menej"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "read more"
msgstr "čítať viac"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "seconds"
msgstr "sekundy"

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/legacy/public_livechat.js:0
#, python-format
msgid "unnamed"
msgstr "nepomenovaný"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "{{author_name}}"
msgstr ""
