<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="tax_report" model="account.tax.report">
        <field name="name">Tax Report</field>
        <field name="country_id" ref="base.de"/>
    </record>

    <!-- First level, one for base, one for tax -->
    <record id="tax_report_de_tag_01" model="account.tax.report.line">
        <field name="name">Bemessungsgrundlage</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">10</field>
        <field name="formula">None</field>
    </record>
    <record id="tax_report_de_tag_02" model="account.tax.report.line">
        <field name="name">Steuer</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">20</field>
        <field name="formula">None</field>
    </record>


    <!-- BASE -->
    <record id="tax_report_de_tag_17" model="account.tax.report.line">
        <field name="name">I. Anmeldung der Umsatzsteuer-Vorauszahlung</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">10</field>
        <field name="parent_id" ref="tax_report_de_tag_01"/>
        <field name="formula">None</field>
    </record>

    <record id="tax_report_de_tag_18" model="account.tax.report.line">
        <field name="name">Lieferungen und sonstige Leistungen</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">10</field>
        <field name="parent_id" ref="tax_report_de_tag_17"/>
    </record>

    <record id="tax_report_de_tag_19" model="account.tax.report.line">
        <field name="name">Steuerpflichtige Umsätze</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">10</field>
        <field name="parent_id" ref="tax_report_de_tag_18"/>
    </record>

    <record id="tax_report_de_tag_81" model="account.tax.report.line">
        <field name="name">81. zum Steuersatz von 19 % (zeile 12)</field>
        <field name="tag_name">81_BASE</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">10</field>
        <field name="parent_id" ref="tax_report_de_tag_19"/>
        <field name="code">81</field>
    </record>
    <record id="tax_report_de_tag_86" model="account.tax.report.line">
        <field name="name">86. zum Steuersatz von 7 % (zeile 13)</field>
        <field name="tag_name">86_BASE</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">20</field>
        <field name="parent_id" ref="tax_report_de_tag_19"/>
        <field name="code">86</field>
    </record>
    <record id="tax_report_de_tag_87" model="account.tax.report.line">
        <field name="name">87. zum Steuersatz von 0 % (zeile 14)</field>
        <field name="tag_name">87_BASE</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">30</field>
        <field name="parent_id" ref="tax_report_de_tag_19"/>
        <field name="code">87</field>
    </record>
    <record id="tax_report_de_tag_35" model="account.tax.report.line">
        <field name="name">35. zu anderen Steuersätzen (zeile 15)</field>
        <field name="tag_name">35</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">40</field>
        <field name="parent_id" ref="tax_report_de_tag_19"/>
        <field name="code">35</field>
    </record>
    <record id="tax_report_de_tag_77" model="account.tax.report.line">
        <field name="name">77. Lieferungen land- und forstwirtschaftlicher Betriebe nach § 24 UStG an Abnehmer mit USt-IdNr. (zeile 16)</field>
        <field name="tag_name">77</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">50</field>
        <field name="parent_id" ref="tax_report_de_tag_19"/>
        <field name="code">77</field>
    </record>
    <record id="tax_report_de_tag_76" model="account.tax.report.line">
        <field name="name">76. Umsätze, für die eine Steuer nach § 24 UStG zu entrichten ist (zeile 17)</field>
        <field name="tag_name">76</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">60</field>
        <field name="parent_id" ref="tax_report_de_tag_19"/>
        <field name="code">76</field>
    </record>

    <record id="tax_report_de_tag_25" model="account.tax.report.line">
        <field name="name">Steuerfreie Umsätze mit Vorsteuerabzug</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">20</field>
        <field name="parent_id" ref="tax_report_de_tag_18"/>
    </record>

    <record id="tax_report_de_tag_41" model="account.tax.report.line">
        <field name="name">41. an Abnehmer mit USt-IdNr (zeile 18)</field>
        <field name="tag_name">41</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">10</field>
        <field name="parent_id" ref="tax_report_de_tag_25"/>
        <field name="code">41</field>
    </record>
    <record id="tax_report_de_tag_44" model="account.tax.report.line">
        <field name="name">44. neuer Fahrzeuge an Abnehmer ohne USt-IdNr (zeile 19)</field>
        <field name="tag_name">44</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">20</field>
        <field name="parent_id" ref="tax_report_de_tag_25"/>
        <field name="code">44</field>
    </record>
    <record id="tax_report_de_tag_49" model="account.tax.report.line">
        <field name="name">49. neuer Fahrzeuge außerhalb eines Unternehmens (zeile 20)</field>
        <field name="tag_name">49</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">30</field>
        <field name="parent_id" ref="tax_report_de_tag_25"/>
        <field name="code">49</field>
    </record>
    <record id="tax_report_de_tag_43" model="account.tax.report.line">
        <field name="name">43. Weitere steuerfreie Umsätze mit Vorsteuerabzug (zeile 21)</field>
        <field name="tag_name">43</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">40</field>
        <field name="parent_id" ref="tax_report_de_tag_25"/>
        <field name="code">43</field>
    </record>

    <record id="tax_report_de_tag_24" model="account.tax.report.line">
        <field name="name">48. Steuerfreie Umsätze ohne Vorsteuerabzug (zeile 22)</field>
        <field name="tag_name">48</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">30</field>
        <field name="parent_id" ref="tax_report_de_tag_18"/>
        <field name="code">48</field>
    </record>

    <record id="tax_report_de_tag_31" model="account.tax.report.line">
        <field name="name">Innergemeinschaftliche Erwerbe</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">40</field>
        <field name="parent_id" ref="tax_report_de_tag_18"/>
    </record>

    <record id="tax_report_de_tag_91" model="account.tax.report.line">
        <field name="name">91. Steuerfreie innergemeinschaftliche Erwerbe (zeile 23)</field>
        <field name="tag_name">91</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">10</field>
        <field name="parent_id" ref="tax_report_de_tag_31"/>
        <field name="code">91</field>
    </record>
    <record id="tax_report_de_tag_89" model="account.tax.report.line">
        <field name="name">89. Steuerpflichtige innergemeinschaftliche Erwerbe zum Steuersatz von 19 % (zeile 24)</field>
        <field name="tag_name">89_BASE</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">20</field>
        <field name="parent_id" ref="tax_report_de_tag_31"/>
        <field name="code">89</field>
    </record>
    <record id="tax_report_de_tag_93" model="account.tax.report.line">
        <field name="name">93. zum Steuersatz von 7 % (zeile 25)</field>
        <field name="tag_name">93_BASE</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">30</field>
        <field name="parent_id" ref="tax_report_de_tag_31"/>
        <field name="code">93</field>
    </record>
    <record id="tax_report_de_tag_90" model="account.tax.report.line">
        <field name="name">90. zum Steuersatz von 0 % (zeile 26)</field>
        <field name="tag_name">90_BASE</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">40</field>
        <field name="parent_id" ref="tax_report_de_tag_31"/>
        <field name="code">90</field>
    </record>
    <record id="tax_report_de_tag_95" model="account.tax.report.line">
        <field name="name">95. zu anderen Steuersätzen (zeile 27)</field>
        <field name="tag_name">95</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">50</field>
        <field name="parent_id" ref="tax_report_de_tag_31"/>
        <field name="code">95</field>
    </record>
    <record id="tax_report_de_tag_94" model="account.tax.report.line">
        <field name="name">94. neuer Fahrzeuge von Lieferern ohne (zeile 28)</field>
        <field name="tag_name">94</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">60</field>
        <field name="parent_id" ref="tax_report_de_tag_31"/>
        <field name="code">94</field>
    </record>

    <record id="tax_report_de_tag_46" model="account.tax.report.line">
        <field name="name">Leistungsempfänger als Steuerschuldner</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">20</field>
        <field name="parent_id" ref="tax_report_de_tag_17"/>
    </record>

    <record id="tax_report_de_tag_48" model="account.tax.report.line">
        <field name="name">46. Steuerpflichtige sonstige Leistungen eines im übrigen Gemeinschaftsgebiet ansässigen Unternehmers (zeile 29)</field>
        <field name="tag_name">46</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">10</field>
        <field name="parent_id" ref="tax_report_de_tag_46"/>
        <field name="code">46</field>
    </record>
    <record id="tax_report_de_tag_73" model="account.tax.report.line">
        <field name="name">73. Lieferungen sicherungsübereigneter Gegenstände und Umsätze, die unter das GrEStG fallen (zeile 30)</field>
        <field name="tag_name">73</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">20</field>
        <field name="parent_id" ref="tax_report_de_tag_46"/>
        <field name="code">73</field>
    </record>
    <record id="tax_report_de_tag_84" model="account.tax.report.line">
        <field name="name">84. Andere Leistungen (zeile 31)</field>
        <field name="tag_name">84</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">30</field>
        <field name="parent_id" ref="tax_report_de_tag_46"/>
        <field name="code">84</field>
    </record>

    <record id="tax_report_de_tag_37" model="account.tax.report.line">
        <field name="name">Ergänzende Angaben zu Umsätzen</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">30</field>
        <field name="parent_id" ref="tax_report_de_tag_17"/>
    </record>

    <record id="tax_report_de_tag_42" model="account.tax.report.line">
        <field name="name">42. Dreiecksgeschäften (zeile 32)</field>
        <field name="tag_name">42</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">10</field>
        <field name="parent_id" ref="tax_report_de_tag_37"/>
        <field name="code">42</field>
    </record>
    <record id="tax_report_de_tag_60" model="account.tax.report.line">
        <field name="name">60. Übrige steuerpflichtige Umsätze, für die der Leistungsempfänger die Steuer nach § 13b Abs. 5 UStG schuldet (zeile 33)</field>
        <field name="tag_name">60</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">20</field>
        <field name="parent_id" ref="tax_report_de_tag_37"/>
        <field name="code">60</field>
    </record>
    <record id="tax_report_de_tag_21" model="account.tax.report.line">
        <field name="name">21. Nicht steuerbare sonstige Leistungen (zeile 34)</field>
        <field name="tag_name">21</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">30</field>
        <field name="parent_id" ref="tax_report_de_tag_37"/>
        <field name="code">21</field>
    </record>
    <record id="tax_report_de_tag_45" model="account.tax.report.line">
        <field name="name">45. Übrige nicht steuerbare Umsätze (zeile 35)</field>
        <field name="tag_name">45_BASE</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">40</field>
        <field name="parent_id" ref="tax_report_de_tag_37"/>
        <field name="code">45</field>
    </record>

    <!-- TAX -->

    <record id="tax_report_de_tax_tag_17" model="account.tax.report.line">
        <field name="name">Anmeldung der Umsatzsteuer-Vorauszahlung</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">10</field>
        <field name="parent_id" ref="tax_report_de_tag_02"/>
        <field name="code"></field>
    </record>

    <record id="tax_report_de_tax_tag_18" model="account.tax.report.line">
        <field name="name">Lieferungen und sonstige Leistungen</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">10</field>
        <field name="parent_id" ref="tax_report_de_tax_tag_17"/>
    </record>

    <record id="tax_report_de_tax_tag_19" model="account.tax.report.line">
        <field name="name">Steuerpflichtige Umsätze</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">10</field>
        <field name="parent_id" ref="tax_report_de_tax_tag_18"/>
    </record>
    <!-- Row 20 - 21 - 22 - 24 -->
    <record id="tax_report_de_tag_26" model="account.tax.report.line">
        <field name="name">zum Steuersatz von 19 % (zeile 12)</field>
        <field name="tag_name">81_TAX</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">10</field>
        <field name="parent_id" ref="tax_report_de_tax_tag_19"/>
        <field name="code"></field>
    </record>
    <record id="tax_report_de_tag_27" model="account.tax.report.line">
        <field name="name">zum Steuersatz von 7 % (zeile 13)</field>
        <field name="tag_name">86_TAX</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">20</field>
        <field name="parent_id" ref="tax_report_de_tax_tag_19"/>
        <field name="code"></field>
    </record>
    <record id="tax_report_de_tag_36" model="account.tax.report.line">
        <field name="name">36. zu anderen Steuersatzen (zeile 15)</field>
        <field name="tag_name">36</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">30</field>
        <field name="parent_id" ref="tax_report_de_tax_tag_19"/>
        <field name="code">36</field>
    </record>
    <record id="tax_report_de_tag_80" model="account.tax.report.line">
        <field name="name">80. Umsatze, fur die eine Steuer nach § 24 UStG zu entrichten ist (zeile 17)</field>
        <field name="tag_name">80</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">40</field>
        <field name="parent_id" ref="tax_report_de_tax_tag_19"/>
        <field name="code">80</field>
    </record>

    <record id="tax_report_de_tax_tag_31" model="account.tax.report.line">
        <field name="name">Innergemeinschaftliche Erwerbe</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">20</field>
        <field name="parent_id" ref="tax_report_de_tax_tag_18"/>
    </record>

    <record id="tax_report_de_tag_33" model="account.tax.report.line">
        <field name="name">89. zum Steuersatz von 19 % (zeile 24)</field>
        <field name="tag_name">89_TAX</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">10</field>
        <field name="parent_id" ref="tax_report_de_tax_tag_31"/>
        <field name="code"></field>
    </record>
    <record id="tax_report_de_tag_34" model="account.tax.report.line">
        <field name="name">93. zum Steuersatz von 7 % (zeile 25)</field>
        <field name="tag_name">93_TAX</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">20</field>
        <field name="parent_id" ref="tax_report_de_tax_tag_31"/>
        <field name="code"></field>
    </record>
    <record id="tax_report_de_tag_98" model="account.tax.report.line">
        <field name="name">98. zu anderen Steuersatzen (zeile 27)</field>
        <field name="tag_name">98</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">30</field>
        <field name="parent_id" ref="tax_report_de_tax_tag_31"/>
        <field name="code">98</field>
    </record>
    <record id="tax_report_de_tag_96" model="account.tax.report.line">
        <field name="name">96. neuer Fahrzeuge von Lieferern ohne USt-IdNr. zum allgemeinen Steuersatz (zeile 28)</field>
        <field name="tag_name">96</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">40</field>
        <field name="parent_id" ref="tax_report_de_tax_tag_31"/>
        <field name="code">96</field>
    </record>

    <record id="tax_report_de_tax_tag_46" model="account.tax.report.line">
        <field name="name">Leistungsempfänger als Steuerschuldner</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">20</field>
        <field name="parent_id" ref="tax_report_de_tax_tag_17"/>
    </record>

    <record id="tax_report_de_tag_47" model="account.tax.report.line">
        <field name="name">47. Steuerpflichtige sonstige Leistungen eines im übrigen Gemeinschaftsgebietansässigen Unternehmers (zeile 29)</field>
        <field name="tag_name">47</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">10</field>
        <field name="parent_id" ref="tax_report_de_tax_tag_46"/>
        <field name="code">47</field>
    </record>
    <record id="tax_report_de_tag_74" model="account.tax.report.line">
        <field name="name">74. Lieferungen sicherungsübereigneter Gegenstände und Umsätze, die unter das GrEStG fallen (zeile 30)</field>
        <field name="tag_name">74</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">20</field>
        <field name="parent_id" ref="tax_report_de_tax_tag_46"/>
        <field name="code">74</field>
    </record>
    <record id="tax_report_de_tag_85" model="account.tax.report.line">
        <field name="name">85. Andere Leistungen eines im Ausland ansässigen Unternehmers (zeile 31)</field>
        <field name="tag_name">85</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">30</field>
        <field name="parent_id" ref="tax_report_de_tax_tag_46"/>
        <field name="code">85</field>
    </record>

    <record id="tax_report_de_tax_tag_55" model="account.tax.report.line">
        <field name="name">Abziehbare Vorsteuerbetrage</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">30</field>
        <field name="parent_id" ref="tax_report_de_tax_tag_17"/>
    </record>

    <record id="tax_report_de_tag_66" model="account.tax.report.line">
        <field name="name">66. Vorsteuerbeträge aus Rechnungen von anderen Unternehmern (zeile 37)</field>
        <field name="tag_name">66</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">10</field>
        <field name="parent_id" ref="tax_report_de_tax_tag_55"/>
        <field name="code">66</field>
    </record>
    <record id="tax_report_de_tag_61" model="account.tax.report.line">
        <field name="name">61. Vorsteuerbeträge aus dem innergemeinschaftlichen Erwerb von Gegenständen (zeile 38)</field>
        <field name="tag_name">61</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">20</field>
        <field name="parent_id" ref="tax_report_de_tax_tag_55"/>
        <field name="code">61</field>
    </record>
    <record id="tax_report_de_tag_62" model="account.tax.report.line">
        <field name="name">62. Entstandene Einfuhrumsatzsteuer (zeile 39)</field>
        <field name="tag_name">62</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">30</field>
        <field name="parent_id" ref="tax_report_de_tax_tag_55"/>
        <field name="code">62</field>
    </record>
    <record id="tax_report_de_tag_67" model="account.tax.report.line">
        <field name="name">67. Vorsteuerbeträge aus Leistungen im Sinne des § 13b UStG (zeile 40)</field>
        <field name="tag_name">67</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">40</field>
        <field name="parent_id" ref="tax_report_de_tax_tag_55"/>
        <field name="code">67</field>
    </record>
    <record id="tax_report_de_tag_63" model="account.tax.report.line">
        <field name="name">63. Vorsteuerbeträge, die nach allgemeinen Durchschnittssätzen berechnet sind (zeile 41)</field>
        <field name="tag_name">63</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">50</field>
        <field name="parent_id" ref="tax_report_de_tax_tag_55"/>
        <field name="code">63</field>
    </record>
    <record id="tax_report_de_tag_59" model="account.tax.report.line">
        <field name="name">59. Vorsteuerabzug für innergemeinschaftliche Lieferungen neuer Fahrzeuge außerhalb eines Unternehmens (zeile 42)</field>
        <field name="tag_name">59</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">60</field>
        <field name="parent_id" ref="tax_report_de_tax_tag_55"/>
        <field name="code">59</field>
    </record>
    <record id="tax_report_de_tag_64" model="account.tax.report.line">
        <field name="name">64. Berichtigung des Vorsteuerabzugs (zeile 43)</field>
        <field name="tag_name">64</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">70</field>
        <field name="parent_id" ref="tax_report_de_tax_tag_55"/>
        <field name="code">64</field>
    </record>

    <record id="tax_report_de_tax_tag_64" model="account.tax.report.line">
        <field name="name">Andere Steuerbetrage</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">40</field>
        <field name="parent_id" ref="tax_report_de_tax_tag_17"/>
    </record>

    <record id="tax_report_de_tag_65" model="account.tax.report.line">
        <field name="name">65. Steuer infolge Wechsels der Besteuerungsform sowie Nachsteuer auf versteuerte Anzahlungen u. ä. wegen Steuersatzänderung (zeile 45)</field>
        <field name="tag_name">65</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">10</field>
        <field name="parent_id" ref="tax_report_de_tax_tag_64"/>
        <field name="code">65</field>
    </record>
    <record id="tax_report_de_tag_69" model="account.tax.report.line">
        <field name="name">69. In Rechnungen unrichtig oder unberechtigt ausgewiesene Steuerbeträge (zeile 46)</field>
        <field name="tag_name">69</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">20</field>
        <field name="parent_id" ref="tax_report_de_tax_tag_64"/>
        <field name="code">69</field>
    </record>
    <record id="tax_report_de_tag_39" model="account.tax.report.line">
        <field name="name">39. Abzug der festgesetzten Sondervorauszahlung für Dauerfristverlängerung (zeile 48)</field>
        <field name="tag_name">39</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">60</field>
        <field name="parent_id" ref="tax_report_de_tax_tag_17"/>
        <field name="code">39</field>
    </record>
    <record id="tax_report_de_tag_83" model="account.tax.report.line">
        <field name="name">83. Verbleibende Umsatzsteuer-Vorauszahlung (zeile 68)</field>
        <field name="tag_name">83</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">70</field>
        <field name="parent_id" ref="tax_report_de_tax_tag_17"/>
        <field name="code">83</field>
    </record>

    <record id="tax_report_de_tag_71" model="account.tax.report.line">
        <field name="name">Minderung</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">30</field>
        <field name="formula">None</field>
    </record>
    <record id="tax_report_de_tag_50" model="account.tax.report.line">
        <field name="name">50. Minderung der Bemessungsgrundlage (zeile 50)</field>
        <field name="tag_name">50</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">10</field>
        <field name="parent_id" ref="tax_report_de_tag_71"/>
        <field name="code">50</field>
    </record>
    <record id="tax_report_de_tag_37_74" model="account.tax.report.line">
        <field name="name">37. Minderung der abziehbaren Vorsteuerbeträge (zeile 51)</field>
        <field name="tag_name">37</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence">20</field>
        <field name="parent_id" ref="tax_report_de_tag_71"/>
        <field name="code">37</field>
    </record>


    <record id="tag_de_intracom_community_delivery" model="account.account.tag">
        <field name="name">Innergemeinschaftliche Lieferung</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.de"/>
    </record>
    <record id="tag_de_intracom_community_supplies" model="account.account.tag">
        <field name="name">Sonstige Leistungen</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.de"/>
    </record>
    <record id="tag_de_intracom_ABC" model="account.account.tag">
        <field name="name">Dreiecksgeschäfte</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.de"/>
    </record>

    <!-- Profit and loss tags -->
    <record id="tag_de_pl_01" model="account.account.tag">
        <field name="name">G&amp;V: 1-Umsatzerlöse</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_pl_02" model="account.account.tag">
        <field name="name">G&amp;V: 2-Erhöhung oder Verminderung des Bestands an fertigen und unfertigen Erzeugnissen</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_pl_03" model="account.account.tag">
        <field name="name">G&amp;V: 3-Andere aktivierte Eigenleistungen</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_pl_04" model="account.account.tag">
        <field name="name">G&amp;V: 4-Sonstige betrieliche Erträge</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_pl_05" model="account.account.tag">
        <field name="name">G&amp;V: 5-Materialaufwand</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_pl_06" model="account.account.tag">
        <field name="name">G&amp;V: 6-Personalaufwand</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_pl_07" model="account.account.tag">
        <field name="name">G&amp;V: 7-Abschreibungen</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_pl_08_1" model="account.account.tag">
        <field name="name">G&amp;V: 8.1-Raumkosten</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_pl_08_2" model="account.account.tag">
        <field name="name">G&amp;V: 8.2-Versicherungen, Beiträge und Abgaben</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_pl_08_3" model="account.account.tag">
        <field name="name">G&amp;V: 8.3-Reparaturen und Instandhaltungen</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_pl_08_4" model="account.account.tag">
        <field name="name">G&amp;V: 8.4-Fahrzeugkosten</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_pl_08_5" model="account.account.tag">
        <field name="name">G&amp;V: 8.5-Werbe- und Reisekosten</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_pl_08_6" model="account.account.tag">
        <field name="name">G&amp;V: 8.6-Kosten der Warenabgabe</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_pl_08_7" model="account.account.tag">
        <field name="name">G&amp;V: 8.7-verschiedene betriebliche Kosten</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_pl_09" model="account.account.tag">
        <field name="name">G&amp;V: 9-Erträge aus Beteiligungen</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_pl_10" model="account.account.tag">
        <field name="name">G&amp;V: 10-Erträge aus anderen Wertpapieren und Ausleihungen des Finanzanlagevermögens</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_pl_11" model="account.account.tag">
        <field name="name">G&amp;V: 11-Sonstige Zinsen und ähnliche Erträge</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_pl_12" model="account.account.tag">
        <field name="name">G&amp;V: 12-Abschreibungen auf Finanzanlagen und auf Wertpapiere des Umlaufvermögens</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_pl_13" model="account.account.tag">
        <field name="name">G&amp;V: 13-Zinsen und ähnliche Aufwendungen</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_pl_14" model="account.account.tag">
        <field name="name">G&amp;V: 14-Steuern vom Einkommen und Ertrag</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_pl_15" model="account.account.tag">
        <field name="name">G&amp;V: 15-Sonstige Steuern</field>
        <field name="applicability">accounts</field>
    </record>

    <!-- Balance sheet tags -->
    <record id="tag_de_asset_bs_A_I_1" model="account.account.tag">
        <field name="name">Bilanz-Aktiva: A I 1-Selbst geschaffene gewerbliche Schutzrechte und ähnliche Rechte und Werte</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_asset_bs_A_I_2" model="account.account.tag">
        <field name="name">Bilanz-Aktiva: A I 2-Konzessionen, Lizenzen und ähnliche Rechte und Werte</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_asset_bs_A_I_3" model="account.account.tag">
        <field name="name">Bilanz-Aktiva: A I 3-Geschäfts- oder Firmenwert</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_asset_bs_A_I_4" model="account.account.tag">
        <field name="name">Bilanz-Aktiva: A I 4-geleistete Anzahlungen</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_asset_bs_A_II_1" model="account.account.tag">
        <field name="name">Bilanz-Aktiva: A II 1-Grundstücke. grundstücksgleiche Rechte und Bauten</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_asset_bs_A_II_2" model="account.account.tag">
        <field name="name">Bilanz-Aktiva: A II 2-Technische  Anlagen und Maschinen</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_asset_bs_A_II_3" model="account.account.tag">
        <field name="name">Bilanz-Aktiva: A II 3-Andere Anlagen. Betriebs- und Geschäftsausstattung</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_asset_bs_A_II_4" model="account.account.tag">
        <field name="name">Bilanz-Aktiva: A II 4-Geleistete Anzahlungen und Anlagen im Bau</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_asset_bs_A_III_1" model="account.account.tag">
        <field name="name">Bilanz-Aktiva: A III 1-Anteile an verbundenen Unternehmen</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_asset_bs_A_III_2" model="account.account.tag">
        <field name="name">Bilanz-Aktiva: A III 2-Ausleihungen an verbundene Unternehmen</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_asset_bs_A_III_3" model="account.account.tag">
        <field name="name">Bilanz-Aktiva: A III 3-Beteiligungen</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_asset_bs_A_III_4" model="account.account.tag">
        <field name="name">Bilanz-Aktiva: A III 4-Ausleihungen an Unternehmen, mit denen ein Beteiligungsverhältnis besteht</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_asset_bs_A_III_5" model="account.account.tag">
        <field name="name">Bilanz-Aktiva: A III 5-Wertpapiere des Anlagevermögens</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_asset_bs_A_III_6" model="account.account.tag">
        <field name="name">Bilanz-Aktiva: A III 6-sonstige Ausleihungen</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_asset_bs_B_I_1" model="account.account.tag">
        <field name="name">Bilanz-Aktiva: B I 1-Roh-, Hilfs- und Betriebsstoffe</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_asset_bs_B_I_2" model="account.account.tag">
        <field name="name">Bilanz-Aktiva: B I 2-Unfertige Erzeugnisse, unfertige Leistungen</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_asset_bs_B_I_3" model="account.account.tag">
        <field name="name">Bilanz-Aktiva: B I 3-Fertige Erzeugnisse und Waren</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_asset_bs_B_I_4" model="account.account.tag">
        <field name="name">Bilanz-Aktiva: B I 4-Geleistete Anzahlungen</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_asset_bs_B_II_1" model="account.account.tag">
        <field name="name">Bilanz-Aktiva: B II 1-Forderungen aus Lieferungen und Leistungen</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_asset_bs_B_II_2" model="account.account.tag">
        <field name="name">Bilanz-Aktiva: B II 2-Forderungen gegen verbundene Unternehmen</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_asset_bs_B_II_3" model="account.account.tag">
        <field name="name">Bilanz-Aktiva: B II 3-Forderungen gegen Unternehmen, mit denen ein Beteiligungsverhältnis besteht</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_asset_bs_B_II_4" model="account.account.tag">
        <field name="name">Bilanz-Aktiva: B II 4-Sonstige Vermögensgegenstände</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_asset_bs_B_III_1" model="account.account.tag">
        <field name="name">Bilanz-Aktiva: B III 1-Anteile an verbundenen Unternehmen</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_asset_bs_B_III_2" model="account.account.tag">
        <field name="name">Bilanz-Aktiva: B III 2-sonstige Wertpapiere</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_asset_bs_B_IV" model="account.account.tag">
        <field name="name">Bilanz-Aktiva: B IV-Kassenbestand, Bundesbankguthaben, Guthaben bei Kreditinstituten und Schecks</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_asset_bs_C" model="account.account.tag">
        <field name="name">Bilanz-Aktiva: C-Rechnungsabgrenzungsposten</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_asset_bs_D" model="account.account.tag">
        <field name="name">Bilanz-Aktiva: D-Aktive latente Steuern</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_asset_bs_E" model="account.account.tag">
        <field name="name">Bilanz-Aktiva: E-Aktiver Unterschiedsbetrag aus der Vermögensverrechnung</field>
        <field name="applicability">accounts</field>
    </record>

    <record id="tag_de_liabilities_bs_A_I" model="account.account.tag">
        <field name="name">Bilanz-Passiva: A I-Gezeichnetes Kapital</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_liabilities_bs_A_II" model="account.account.tag">
        <field name="name">Bilanz-Passiva: A II-Kapitalrücklage</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_liabilities_bs_A_III_1" model="account.account.tag">
        <field name="name">Bilanz-Passiva: A III 1-Gesetzliche Rücklage</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_liabilities_bs_A_III_2" model="account.account.tag">
        <field name="name">Bilanz-Passiva: A III 2-Rücklage für Anteile an einem herrschenden oder mehrheitlich beteiligten Unternehmen</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_liabilities_bs_A_III_3" model="account.account.tag">
        <field name="name">Bilanz-Passiva: A III 3-Satzungsmäßige Rücklagen</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_liabilities_bs_A_III_4" model="account.account.tag">
        <field name="name">Bilanz-Passiva: A III 4-Andere Gewinnrücklagen</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_liabilities_bs_A_IV" model="account.account.tag">
        <field name="name">Bilanz-Passiva: A IV-Gewinnvortrag/Verlustvortrag</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_liabilities_bs_A_V" model="account.account.tag">
        <field name="name">Bilanz-Passiva: A V-Jahresüberschuß/Jahresfehlbetrag</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_liabilities_bs_B_1" model="account.account.tag">
        <field name="name">Bilanz-Passiva: B 1-Rückstellungen für Pensionen und ähnliche Verpflichtungen</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_liabilities_bs_B_2" model="account.account.tag">
        <field name="name">Bilanz-Passiva: B 2-Steuerrückstellungen</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_liabilities_bs_B_3" model="account.account.tag">
        <field name="name">Bilanz-Passiva: B 3-Sonstige Rückstellungen</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_liabilities_bs_C_1" model="account.account.tag">
        <field name="name">Bilanz-Passiva: C 1-Anleihen, davon konvertibeln</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_liabilities_bs_C_2" model="account.account.tag">
        <field name="name">Bilanz-Passiva: C 2-Verbindlichkeiten gegenüber Kreditinstituten</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_liabilities_bs_C_3" model="account.account.tag">
        <field name="name">Bilanz-Passiva: C 3-Erhaltene Anzahlungen auf Bestellungen</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_liabilities_bs_C_4" model="account.account.tag">
        <field name="name">Bilanz-Passiva: C 4-Verbindlichkeiten aus Lieferungen und Leistungen</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_liabilities_bs_C_5" model="account.account.tag">
        <field name="name">Bilanz-Passiva: C 5-Verbindlichkeiten aus der Annahme gezogener Wechsel und der Ausstellung eigener Wechsel</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_liabilities_bs_C_6" model="account.account.tag">
        <field name="name">Bilanz-Passiva: C 6-Verbindlichkeiten gegenüber verbundenen Unternehmen</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_liabilities_bs_C_7" model="account.account.tag">
        <field name="name">Bilanz-Passiva: C 7-Verbindlichkeiten gegenüber Unternehmen, mit denen ein Beteiligungsverhältnis besteht</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_liabilities_bs_C_8" model="account.account.tag">
        <field name="name">Bilanz-Passiva: C 8-Sonstige Verbindlichkeiten, davon aus Steuern, davon im Rahmen der sozialen Sicherheit</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_liabilities_bs_D" model="account.account.tag">
        <field name="name">Bilanz-Passiva: D-Rechnungsabgrenzungsposten</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="tag_de_liabilities_bs_E" model="account.account.tag">
        <field name="name">Bilanz-Passiva: E-Passive latente Steuern</field>
        <field name="applicability">accounts</field>
    </record>

</odoo>
