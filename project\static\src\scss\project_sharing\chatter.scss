.o_FormRenderer_chatterContainer {
    display: flex;
    background-color: $white;
    border-color: $border-color;

    .o_portal_chatter {
        width: 100%;

        .o_portal_chatter_header {
            text-align: center;
            vertical-align: middle;
        }

        div.o_portal_chatter_composer,
        div.o_portal_chatter_messages {
            div.media {
                gap: 10px;

                .o_portal_chatter_attachments {
                    margin-bottom: 1rem;
                    .o_portal_chatter_attachment {
                        .o_portal_chatter_attachment_delete {
                            @include o-position-absolute($top: 0, $right: 0);
                            opacity: 0;
                        }
                        &:hover .o_portal_chatter_attachment_delete {
                            opacity: 1;
                        }
                    }
                }
            }
        }

        .o_portal_chatter_avatar {
            width: 45px;
            height: 45px;
        }

        .o_portal_message_internal_off {
            .btn-danger {
                display: none;
            }
        }

        .o_portal_message_internal_on {
            .btn-success {
                display: none;
            }
        }
    }

    &.o-aside {
        flex-direction: column;

        .o_portal_chatter {
            .o_portal_chatter_header {
                padding-top: 1rem;
                padding-bottom: 2px;
            }

            .o_portal_chatter_composer, .o_portal_chatter_messages {
                margin-left: 1rem;
                margin-right: 1rem;
            }
        }
    }
}

