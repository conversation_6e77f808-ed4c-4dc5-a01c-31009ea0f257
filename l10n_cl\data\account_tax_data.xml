<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- Account Tags -->

    <record id="ITAX_19" model="account.tax.template">
        <field name="chart_template_id" ref="cl_chart_template"/>
        <field name="name">IVA 19% Venta</field>
        <field name="description">IVA 19% Vta</field>
        <field name="amount">19</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="l10n_cl_sii_code">14</field>
        <field name="tax_group_id" ref="tax_group_iva_19"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_ventas_netas_gravadas_c_iva')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_210710'),
                'plus_report_line_ids': [ref('tax_report_iva_debito_fiscal')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_ventas_netas_gravadas_c_iva')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_210710'),
                'minus_report_line_ids': [ref('tax_report_iva_debito_fiscal')],
            }),
        ]"/>
    </record>

    <record id="OTAX_19" model="account.tax.template">
      <field name="chart_template_id" ref="cl_chart_template"/>
      <field name="name">IVA 19% Compra</field>
      <field name="description">IVA 19% Comp</field>
      <field name="amount">19</field>
      <field name="amount_type">percent</field>
      <field name="type_tax_use">purchase</field>
      <field name="l10n_cl_sii_code">14</field>
      <field name="tax_group_id" ref="tax_group_iva_19"/>
      <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_compras_netas_gr_iva_recup')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_110710'),
                'plus_report_line_ids': [ref('tax_report_compras_iva_recup')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_compras_netas_gr_iva_recup')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_110710'),
                'minus_report_line_ids': [ref('tax_report_compras_iva_recup')],
            }),
        ]"/>
    </record>

    <record id="I_IU2C" model="account.tax.template">
        <field name="chart_template_id" ref="cl_chart_template"/>
        <field name="name">Ret. 2da Categoría</field>
        <field name="description">Ret. 2da Cat</field>
        <field name="amount">-10.75</field>
        <field name="sequence" eval="2"/>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="l10n_cl_sii_code">15</field>
        <field name="tax_group_id" ref="tax_group_2da_categ"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_base_retencion_segunda_categ')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_210740'),
                'plus_report_line_ids': [ref('tax_report_retencion_segunda_categ')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_base_retencion_segunda_categ')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_210740'),
                'minus_report_line_ids': [ref('tax_report_retencion_segunda_categ')],
            }),
        ]"/>
    </record>

    <record id="I_RTI" model="account.tax.template">
        <field name="chart_template_id" ref="cl_chart_template"/>
        <field name="name">Retención Total IVA</field>
        <field name="description">Retención total IVA</field>
        <field name="amount">-19.00</field>
        <field name="sequence" eval="2"/>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="l10n_cl_sii_code">15</field>
        <field name="tax_group_id" ref="tax_group_retenciones"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_retencion_total_compras')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_210715'),
                'plus_report_line_ids': [ref('tax_report_retencion_total_compras')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_retencion_total_compras')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_210715'),
                'minus_report_line_ids': [ref('tax_report_retencion_total_compras')],
            }),
        ]"/>
    </record>

    <record id="especifico_compra" model="account.tax.template">
        <field name="chart_template_id" ref="cl_chart_template"/>
        <field name="name">Específico Compra</field>
        <field name="description">Espec. Comp</field>
        <field name="amount">63</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="l10n_cl_sii_code">29</field>
        <field name="sequence" eval="5"/>
        <field name="tax_group_id" ref="tax_group_impuestos_especificos"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_420220'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('account_420220'),
            }),
        ]"/>
    </record>

    <record id="iva_activo_fijo" model="account.tax.template">
        <field name="chart_template_id" ref="cl_chart_template"/>
        <field name="name">IVA Compra 19% Activo Fijo</field>
        <field name="description">IVA 19% ActF</field>
        <field name="amount">19</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="l10n_cl_sii_code">14</field>
        <field name="sequence" eval="6"/>
        <field name="tax_group_id" ref="tax_group_iva_19"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_compras_activo_fijo')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('tax_report_compras_iva_activo_fijo')],
                'account_id': ref('account_110730'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_compras_activo_fijo')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('tax_report_compras_iva_activo_fijo')],
                'account_id': ref('account_110730'),
            }),
        ]"/>
    </record>

    <record id="iva_activo_fijo_uso_comun" model="account.tax.template">
        <field name="chart_template_id" ref="cl_chart_template"/>
        <field name="name">IVA Compra 19% Act. Fijo Uso Común</field>
        <field name="description">IVA 19% ActFUC</field>
        <field name="amount">19</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="l10n_cl_sii_code">14</field>
        <field name="sequence" eval="6"/>
        <field name="tax_group_id" ref="tax_group_iva_19"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_compras_activo_fijo_uso_comun')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('tax_report_compras_iva_activo_fijo_uso_comun')],
                'account_id': ref('account_110730'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_compras_activo_fijo_uso_comun')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('tax_report_compras_iva_activo_fijo_uso_comun')],
                'account_id': ref('account_110730'),
            }),
        ]"/>
    </record>

    <record id="iva_activo_fijo_uso_no_recup" model="account.tax.template">
        <field name="chart_template_id" ref="cl_chart_template"/>
        <field name="name">IVA Compra 19% Activo Fijo No Recup</field>
        <field name="description">IVA 19% ActFNR</field>
        <field name="amount">19</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="l10n_cl_sii_code">14</field>
        <field name="sequence" eval="6"/>
        <field name="tax_group_id" ref="tax_group_iva_19"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_compras_activo_fijo_no_recup')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('tax_report_compras_iva_activo_fijo_no_recup')],
                'account_id': ref('account_420220'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_compras_activo_fijo_no_recup')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('tax_report_compras_iva_activo_fijo_no_recup')],
                'account_id': ref('account_420220'),
            }),
        ]"/>
    </record>

    <record id="ila_a_100_p" model="account.tax.template">
        <field name="chart_template_id" ref="cl_chart_template"/>
        <field name="name">Beb. Analc. 10% (Compras)</field>
        <field name="description">ILA C 10%</field>
        <field name="amount">10</field>
        <field name="amount_type">percent</field>
        <field name="l10n_cl_sii_code">27</field>
        <field name="type_tax_use">purchase</field>
        <field name="sequence" eval="7"/>
        <field name="tax_group_id" ref="tax_group_ila"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_base_ila_compras')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('tax_report_tax_ila_compras')],
                'account_id': ref('account_420220'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_base_ila_compras')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('tax_report_tax_ila_compras')],
                'account_id': ref('account_420220'),
            }),
        ]"/>
    </record>

    <record id="ila_a_180_p" model="account.tax.template">
        <field name="chart_template_id" ref="cl_chart_template"/>
        <field name="name">Beb. Analc 18% (Compras)</field>
        <field name="description">ILA C 18%</field>
        <field name="amount">18</field>
        <field name="amount_type">percent</field>
        <field name="l10n_cl_sii_code">26</field>
        <field name="type_tax_use">purchase</field>
        <field name="sequence" eval="7"/>
        <field name="tax_group_id" ref="tax_group_ila"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_base_ila_compras')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('tax_report_tax_ila_compras')],
                'account_id': ref('account_420220'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_base_ila_compras')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('tax_report_tax_ila_compras')],
                'account_id': ref('account_420220'),
            }),
        ]"/>
    </record>

    <record id="ila_v_205_p" model="account.tax.template">
        <field name="chart_template_id" ref="cl_chart_template"/>
        <field name="name">Vinos (Compras)</field>
        <field name="description">ILA C 20.5%</field>
        <field name="amount">20.5</field>
        <field name="amount_type">percent</field>
        <field name="l10n_cl_sii_code">25</field>
        <field name="type_tax_use">purchase</field>
        <field name="sequence" eval="7"/>
        <field name="tax_group_id" ref="tax_group_ila"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_base_ila_compras')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('tax_report_tax_ila_compras')],
                'account_id': ref('account_420220'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_base_ila_compras')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('tax_report_tax_ila_compras')],
                'account_id': ref('account_420220'),
            }),
        ]"/>
    </record>

    <record id="ila_l_315_p" model="account.tax.template">
        <field name="chart_template_id" ref="cl_chart_template"/>
        <field name="name">Licores 31.5% (Compras)</field>
        <field name="description">ILA C 31.5%</field>
        <field name="amount">31.5</field>
        <field name="amount_type">percent</field>
        <field name="l10n_cl_sii_code">24</field>
        <field name="type_tax_use">purchase</field>
        <field name="sequence" eval="7"/>
        <field name="tax_group_id" ref="tax_group_ila"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_base_ila_compras')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('tax_report_tax_ila_compras')],
                'account_id': ref('account_420220'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_base_ila_compras')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('tax_report_tax_ila_compras')],
                'account_id': ref('account_420220'),
            }),
        ]"/>
    </record>

    <record id="ila_a_100_s" model="account.tax.template">
        <field name="chart_template_id" ref="cl_chart_template"/>
        <field name="name">Beb. Analc. 10% (Ventas)</field>
        <field name="description">ILA V 10%</field>
        <field name="amount">10</field>
        <field name="amount_type">percent</field>
        <field name="l10n_cl_sii_code">27</field>
        <field name="type_tax_use">sale</field>
        <field name="sequence" eval="7"/>
        <field name="tax_group_id" ref="tax_group_ila"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_base_ila_ventas')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('tax_report_tax_ila_ventas')],
                'account_id': ref('account_210760'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_base_ila_ventas')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('tax_report_tax_ila_ventas')],
                'account_id': ref('account_210760'),
            }),
        ]"/>
    </record>

    <record id="ila_a_180_s" model="account.tax.template">
        <field name="chart_template_id" ref="cl_chart_template"/>
        <field name="name">Beb. Analc 18% (Ventas)</field>
        <field name="description">ILA V 18%</field>
        <field name="amount">18</field>
        <field name="amount_type">percent</field>
        <field name="l10n_cl_sii_code">26</field>
        <field name="type_tax_use">sale</field>
        <field name="sequence" eval="7"/>
        <field name="tax_group_id" ref="tax_group_ila"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_base_ila_ventas')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('tax_report_tax_ila_ventas')],
                'account_id': ref('account_210760'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_base_ila_ventas')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('tax_report_tax_ila_ventas')],
                'account_id': ref('account_210760'),
            }),
        ]"/>
    </record>

    <record id="ila_v_205_s" model="account.tax.template">
        <field name="chart_template_id" ref="cl_chart_template"/>
        <field name="name">Vinos (Ventas)</field>
        <field name="description">ILA V 20.5%</field>
        <field name="amount">20.5</field>
        <field name="amount_type">percent</field>
        <field name="l10n_cl_sii_code">25</field>
        <field name="type_tax_use">sale</field>
        <field name="sequence" eval="7"/>
        <field name="tax_group_id" ref="tax_group_ila"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_base_ila_ventas')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('tax_report_tax_ila_ventas')],
                'account_id': ref('account_210760'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_base_ila_ventas')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('tax_report_tax_ila_ventas')],
                'account_id': ref('account_210760'),
            }),
        ]"/>
    </record>

    <record id="ila_l_315_s" model="account.tax.template">
        <field name="chart_template_id" ref="cl_chart_template"/>
        <field name="name">Licores 31.5% (Ventas)</field>
        <field name="description">ILA V 31.5%</field>
        <field name="amount">31.5</field>
        <field name="amount_type">percent</field>
        <field name="l10n_cl_sii_code">24</field>
        <field name="type_tax_use">sale</field>
        <field name="sequence" eval="7"/>
        <field name="tax_group_id" ref="tax_group_ila"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_base_ila_ventas')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('tax_report_tax_ila_ventas')],
                'account_id': ref('account_210760'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_base_ila_ventas')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('tax_report_tax_ila_ventas')],
                'account_id': ref('account_210760'),
            }),
        ]"/>
    </record>

    <record id="iva_compra_no_recup" model="account.tax.template">
        <field name="chart_template_id" ref="cl_chart_template"/>
        <field name="name">IVA Compra 19% No Recup.</field>
        <field name="description">IVA 19% NoR</field>
        <field name="amount">19</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="l10n_cl_sii_code">14</field>
        <field name="sequence" eval="6"/>
        <field name="tax_group_id" ref="tax_group_iva_19"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_compras_netas_gr_iva_no_recuperable')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('tax_report_compras_iva_no_recup')],
                'account_id': ref('account_420220'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_compras_netas_gr_iva_no_recuperable')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('tax_report_compras_iva_no_recup')],
                'account_id': ref('account_420220'),
            }),
        ]"/>
    </record>

    <record id="iva_compra_uso_comun" model="account.tax.template">
        <field name="chart_template_id" ref="cl_chart_template"/>
        <field name="name">IVA Compra 19% Uso Común</field>
        <field name="description">IVA 19% CUC</field>
        <field name="amount">19</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="l10n_cl_sii_code">14</field>
        <field name="sequence" eval="6"/>
        <field name="tax_group_id" ref="tax_group_iva_19"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_compras_netas_gr_iva_uso_comun')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('tax_report_compras_iva_uso_comun')],
                'account_id': ref('account_110730'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_compras_netas_gr_iva_uso_comun')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('tax_report_compras_iva_uso_comun')],
                'account_id': ref('account_110730'),
            }),
        ]"/>
    </record>

    <record id="iva_supermercado_recup" model="account.tax.template">
        <field name="chart_template_id" ref="cl_chart_template"/>
        <field name="name">IVA Compra 19% Superm.</field>
        <field name="description">IVA 19% SupMRec</field>
        <field name="amount">19</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="l10n_cl_sii_code">14</field>
        <field name="sequence" eval="6"/>
        <field name="tax_group_id" ref="tax_group_iva_19"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_compras_supermercado')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('tax_report_compras_iva_supermercado')],
                'account_id': ref('account_110710'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_compras_supermercado')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('tax_report_compras_iva_supermercado')],
                'account_id': ref('account_110710'),
            }),
        ]"/>
    </record>

</odoo>
