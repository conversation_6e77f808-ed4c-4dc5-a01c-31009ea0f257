# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* gift_card
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: erpgo translator <<EMAIL>>, 2023\n"
"Language-Team: Azerbaijani (https://app.transifex.com/odoo/teams/41243/az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: gift_card
#: model:ir.model.fields,help:gift_card.field_product_product__detailed_type
#: model:ir.model.fields,help:gift_card.field_product_template__detailed_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""
"Saxlanması mümkün olan məhsul ethiyatlarını idarə etdiyiniz məhsuldur. Anbar tətbiqi quraşdırılmalıdır.\n"
"Sərfiyyat məhsulu ehtiyatları idarə olunmayan məhsuldur.\n"
"Xidmət təqdim etdiyiniz qeyri- maddi məhsuldur."

#. module: gift_card
#: model:ir.model.fields,field_description:gift_card.field_gift_card__balance
msgid "Balance"
msgstr "Balans"

#. module: gift_card
#: model:ir.model.fields,field_description:gift_card.field_gift_card__code
msgid "Code"
msgstr "Kod"

#. module: gift_card
#: model:ir.model.fields,field_description:gift_card.field_gift_card__company_id
msgid "Company"
msgstr "Şirkət"

#. module: gift_card
#: model:ir.model.fields,field_description:gift_card.field_gift_card__create_uid
msgid "Created by"
msgstr "Tərəfindən yaradılıb"

#. module: gift_card
#: model:ir.model.fields,field_description:gift_card.field_gift_card__create_date
msgid "Created on"
msgstr "Tarixdə yaradıldı"

#. module: gift_card
#: model:ir.model.fields,field_description:gift_card.field_gift_card__currency_id
msgid "Currency"
msgstr "Valyuta"

#. module: gift_card
#: code:addons/gift_card/models/product.py:0
#, python-format
msgid "Deleting the Gift Card Pay product is not allowed."
msgstr ""

#. module: gift_card
#: model:ir.model.fields,field_description:gift_card.field_gift_card__display_name
msgid "Display Name"
msgstr "Ekran Adı"

#. module: gift_card
#: model:ir.model.fields.selection,name:gift_card.selection__gift_card__state__expired
msgid "Expired"
msgstr "Vaxtı bitmiş"

#. module: gift_card
#: model:ir.model.fields,field_description:gift_card.field_gift_card__expired_date
msgid "Expired Date"
msgstr ""

#. module: gift_card
#: code:addons/gift_card/models/gift_card.py:0
#, python-format
msgid "Gift #%s"
msgstr ""

#. module: gift_card
#: model:ir.model,name:gift_card.model_gift_card
#: model:ir.model.fields.selection,name:gift_card.selection__product_template__detailed_type__gift
#: model:product.product,name:gift_card.gift_card_product_50
#: model:product.product,name:gift_card.pay_with_gift_card_product
#: model:product.template,name:gift_card.gift_card_product_50_product_template
#: model:product.template,name:gift_card.pay_with_gift_card_product_product_template
#: model_terms:ir.ui.view,arch_db:gift_card.gift_card_view_search
#: model_terms:ir.ui.view,arch_db:gift_card.gift_card_view_tree
msgid "Gift Card"
msgstr ""

#. module: gift_card
#: model:ir.actions.act_window,name:gift_card.gift_card_action
msgid "Gift Cards"
msgstr ""

#. module: gift_card
#: model:ir.model.fields,field_description:gift_card.field_gift_card__id
msgid "ID"
msgstr "ID"

#. module: gift_card
#: model:ir.model.fields,help:gift_card.field_gift_card__partner_id
msgid "If empty, all users can use it"
msgstr ""

#. module: gift_card
#: model:ir.model.fields,field_description:gift_card.field_gift_card__initial_amount
msgid "Initial Amount"
msgstr ""

#. module: gift_card
#: model:ir.model.fields,field_description:gift_card.field_gift_card____last_update
msgid "Last Modified on"
msgstr "Son Dəyişdirilmə tarixi"

#. module: gift_card
#: model:ir.model.fields,field_description:gift_card.field_gift_card__write_uid
msgid "Last Updated by"
msgstr "Son Yeniləyən"

#. module: gift_card
#: model:ir.model.fields,field_description:gift_card.field_gift_card__write_date
msgid "Last Updated on"
msgstr "Son Yenilənmə tarixi"

#. module: gift_card
#: model:ir.model.fields,field_description:gift_card.field_gift_card__name
msgid "Name"
msgstr "Ad"

#. module: gift_card
#: model:ir.model.fields,field_description:gift_card.field_gift_card__partner_id
msgid "Partner"
msgstr "Tərəfdaş"

#. module: gift_card
#: model:ir.model,name:gift_card.model_product_template
msgid "Product Template"
msgstr "Məhsul Şablonu"

#. module: gift_card
#: model:ir.model.fields,field_description:gift_card.field_product_product__detailed_type
#: model:ir.model.fields,field_description:gift_card.field_product_template__detailed_type
msgid "Product Type"
msgstr "Məhsulun Növü"

#. module: gift_card
#: model:ir.model.fields,field_description:gift_card.field_gift_card__state
msgid "State"
msgstr "Bölgə"

#. module: gift_card
#: model:ir.model.constraint,message:gift_card.constraint_gift_card_unique_gift_card_code
msgid "The gift card code must be unique."
msgstr ""

#. module: gift_card
#: model:ir.model.constraint,message:gift_card.constraint_gift_card_check_amount
msgid "The initial amount must be positive."
msgstr ""

#. module: gift_card
#: model:product.product,uom_name:gift_card.gift_card_product_50
#: model:product.product,uom_name:gift_card.pay_with_gift_card_product
#: model:product.template,uom_name:gift_card.gift_card_product_50_product_template
#: model:product.template,uom_name:gift_card.pay_with_gift_card_product_product_template
msgid "Units"
msgstr "Vahidlər"

#. module: gift_card
#: model:ir.model.fields.selection,name:gift_card.selection__gift_card__state__valid
#: model_terms:ir.ui.view,arch_db:gift_card.gift_card_view_search
msgid "Valid"
msgstr "Düzgün"
