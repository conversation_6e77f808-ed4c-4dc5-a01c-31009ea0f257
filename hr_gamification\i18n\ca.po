# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_gamification
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <car<PERSON><PERSON><PERSON>@hotmail.com>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <man<PERSON><PERSON>@outlook.com>, 2021
# <PERSON><PERSON><PERSON>, 2021
# j<PERSON><PERSON><PERSON>, 2021
# <PERSON><PERSON><PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: ma<PERSON><PERSON>, 2022\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_gamification
#: model_terms:ir.actions.act_window,help:hr_gamification.goals_menu_groupby_action2
msgid ""
"A goal is defined by a user and a goal type.\n"
"                    Goals can be created automatically by using challenges."
msgstr ""
"Un objectiu es defineix per un usuari i un tipu sd'objectiu\n"
"                Els objectius es poden crear automàticament usant reptes."

#. module: hr_gamification
#: model:ir.model.fields,help:hr_gamification.field_hr_employee__badge_ids
#: model:ir.model.fields,help:hr_gamification.field_hr_employee_base__badge_ids
#: model:ir.model.fields,help:hr_gamification.field_hr_employee_public__badge_ids
msgid ""
"All employee badges, linked to the employee either directly or through the "
"user"
msgstr ""
"Totes les insígnies dels empleats, vinculades a l'empleat directament o a "
"través de l'usuari"

#. module: hr_gamification
#: model_terms:ir.actions.act_window,help:hr_gamification.challenge_list_action2
msgid ""
"Assign a list of goals to chosen users to evaluate them.\n"
"                    The challenge can use a period (weekly, monthly...) for automatic creation of goals.\n"
"                    The goals are created for the specified users or member of the group."
msgstr ""
"Assigna una llista d'objectius als usuaris seleccionats per avaluar-los.\n"
"                    El repte pot utilitzar un període (setmanal, mensual...) per a la creació automàtica d'objectius.\n"
"                    Els objectius es creen per als usuaris o membres del grup especificats."

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_res_users__badge_ids
#: model:ir.ui.menu,name:hr_gamification.gamification_badge_menu_hr
msgid "Badges"
msgstr "Insígnies"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_hr_employee_view_form
msgid ""
"Badges are rewards of good work. Give them to people you believe deserve it."
msgstr ""
"Les insígnies són recompenses de bona feina. Doneu-los a la gent que creieu "
"que es mereix."

#. module: hr_gamification
#: model:ir.model.fields,help:hr_gamification.field_hr_employee__direct_badge_ids
#: model:ir.model.fields,help:hr_gamification.field_hr_employee_base__direct_badge_ids
#: model:ir.model.fields,help:hr_gamification.field_hr_employee_public__direct_badge_ids
msgid "Badges directly linked to the employee"
msgstr "Insígnies directament vinculades a l'empleat"

#. module: hr_gamification
#: model:ir.model,name:hr_gamification.model_hr_employee_base
msgid "Basic Employee"
msgstr "Empleat bàsic"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.view_badge_wizard_reward
msgid "Cancel"
msgstr "Cancel·lar"

#. module: hr_gamification
#: model:ir.actions.act_window,name:hr_gamification.challenge_list_action2
#: model:ir.ui.menu,name:hr_gamification.gamification_challenge_menu_hr
#: model:ir.ui.menu,name:hr_gamification.menu_hr_gamification
msgid "Challenges"
msgstr "Desafiaments"

#. module: hr_gamification
#: model_terms:ir.actions.act_window,help:hr_gamification.challenge_list_action2
msgid "Create a new challenge"
msgstr "Crea un nou repte"

#. module: hr_gamification
#: model_terms:ir.actions.act_window,help:hr_gamification.goals_menu_groupby_action2
msgid "Create a new goal"
msgstr "Crea un objectiu nou"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.view_badge_wizard_reward
msgid "Describe what they did and why it matters (will be public)"
msgstr "Descriviu el que han fet i perquè és important (serà públic)"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee__direct_badge_ids
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_base__direct_badge_ids
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_public__direct_badge_ids
msgid "Direct Badge"
msgstr "Insígnia directa"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_gamification_badge_user__employee_id
#: model:ir.model.fields,field_description:hr_gamification.field_gamification_badge_user_wizard__employee_id
msgid "Employee"
msgstr "Empleat"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee__badge_ids
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_base__badge_ids
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_public__badge_ids
msgid "Employee Badges"
msgstr "Insígnies d'empleat"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee__goal_ids
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_base__goal_ids
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_public__goal_ids
msgid "Employee HR Goals"
msgstr "Objectius de l'HR de l'empleat"

#. module: hr_gamification
#: model:ir.model,name:hr_gamification.model_gamification_badge
msgid "Gamification Badge"
msgstr "Insíginia de gamificació"

#. module: hr_gamification
#: model:ir.model,name:hr_gamification.model_gamification_badge_user
msgid "Gamification User Badge"
msgstr "Insignia de gamificació de l'usuari"

#. module: hr_gamification
#: model:ir.model,name:hr_gamification.model_gamification_badge_user_wizard
msgid "Gamification User Badge Wizard"
msgstr "Assistent d'Insígnies d'Usuari de Gamificació"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_res_users__goal_ids
msgid "Goal"
msgstr "Meta"

#. module: hr_gamification
#: model:ir.actions.act_window,name:hr_gamification.goals_menu_groupby_action2
#: model:ir.ui.menu,name:hr_gamification.gamification_goal_menu_hr
msgid "Goals History"
msgstr "Historial dels objectius"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_hr_employee_view_form
msgid "Grant a Badge"
msgstr "Concedeix una insígnia"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_hr_employee_view_form
msgid "Grant this employee his first badge"
msgstr "Concedeix a aquest empleat la seva primera insígnia"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_badge_form_view
msgid "Granted"
msgstr "Concedit"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_gamification_badge__granted_employees_count
msgid "Granted Employees Count"
msgstr "Concedit Recompte d'empleats"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee__has_badges
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_base__has_badges
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_public__has_badges
msgid "Has Badges"
msgstr "Té insígnies"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_hr_employee_view_form
msgid "Received Badges"
msgstr "Insígnies rebudes"

#. module: hr_gamification
#: model:ir.model.fields,help:hr_gamification.field_gamification_badge_user_wizard__user_id
msgid "Related user name for the resource to manage its access."
msgstr "Usuari relacionat amb el recurs per gestionar-ne el seu accés."

#. module: hr_gamification
#: model:ir.actions.act_window,name:hr_gamification.action_reward_wizard
#: model_terms:ir.ui.view,arch_db:hr_gamification.view_badge_wizard_reward
msgid "Reward Employee"
msgstr "Recompensar a l'empleat"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.view_badge_wizard_reward
msgid "Reward Employee with"
msgstr "Recompensar a l'empleat amb"

#. module: hr_gamification
#: code:addons/hr_gamification/models/gamification.py:0
#, python-format
msgid "The selected employee does not correspond to the selected user."
msgstr "L'empleat seleccionat no correspon a l'usuari seleccionat."

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_gamification_badge_user_wizard__user_id
msgid "User"
msgstr "Usuari"

#. module: hr_gamification
#: model:ir.model,name:hr_gamification.model_res_users
msgid "Users"
msgstr "Usuaris"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.view_badge_wizard_reward
msgid "What are you thankful for?"
msgstr "Per què estàs agraït?"

#. module: hr_gamification
#: code:addons/hr_gamification/wizard/gamification_badge_user_wizard.py:0
#, python-format
msgid "You can not send a badge to yourself."
msgstr "No pot enviar-se una insígnia a si mateix."

#. module: hr_gamification
#: code:addons/hr_gamification/wizard/gamification_badge_user_wizard.py:0
#, python-format
msgid "You can send badges only to employees linked to a user."
msgstr "Només pots enviar insígnies als empleats enllaçats a un usuari."

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_hr_employee_view_form
msgid "to reward this employee for a good action"
msgstr "recompensar aquest empleat per una bona acció"
