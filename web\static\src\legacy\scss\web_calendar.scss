// Variables
$o-cw-color-today-accent: #FC3D39;
$o-cw-popup-avatar-size: 16px;
$o-cw-filter-avatar-size: 20px;

// Animations
@keyframes backgroundfade {
    from { background-color: rgba($info, 0.5); }
    to   { background-color: rgba($info, 0.1); }
}

.o_calendar_container {
    height: 100%;
    display: flex;
}

.o_calendar_view {
    flex: 1 1 auto;
    min-width: 0;
    height: 100%;
    background-color: gray('100');
    background: linear-gradient(-45deg, gray('100'), white);

    .fc-event {
        margin: 0 1px;
        border-style: solid;
        border-width: 0 0 0 3px;
        box-sizing: border-box;
        overflow: hidden;
        background: none;
        font-size: 11px;
        line-height: 1;

        &:not([href]):not([tabindex]) {
            color: $body-color;
        }

        &.fc-dragging.fc-day-grid-event.dayGridMonth .fc-content {
            @include text-truncate();
            margin: 4px 4px 3px;
        }

        .fc-bg {
            background-color: mix(theme-color('primary'), white); // Used for placeholder events only (on creation)
            @include size(101%); // Compensate border
            opacity: 0.7;
            transition: opacity 0.2s;
        }

        .fc-content {
            white-space: normal;
            margin: 0.25rem;
            font-size: 1.1em;
            font-weight: 500;
        }

        // Try to show one full lien for short event
        &.fc-short .fc-content {
            margin-top: 1px;
        }

        &.o_cw_custom_highlight {
            z-index: 10!important;

            .fc-bg{
                opacity: 0.95;
            }
        }
    }

    .o_calendar_widget {
        height: 100%;

        > .fc-view-container {
            height: 100%;
        }

        // ===  Adapt calendar table borders ===
        // =====================================

        td {
            border-color: $border-color;
        }

        .fc-time-grid .fc-slats .fc-minor td {
            border-top-color: gray('400');
        }

        .fc-widget-content {
            border-left-color: transparent;
        }

        .fc-widget-header {
            border-color: transparent;
            border-bottom-color: $border-color;
            padding: 3px 0 5px;
        }

        hr.fc-widget-header {
            padding: 1px;
            border: 0;
            background: gray('400');
        }

        .fc-timeGrid-view .fc-day-grid .fc-row .fc-content-skeleton {
            padding: .5em;
        }

        .fc-event-container {
            color: white;
        }

        .fc-more-popover {
            .fc-header {
                padding-left: 1rem;
                .fc-title {
                    font-weight: bold;
                }
            }

            .fc-body {
                max-height: 500px;
                overflow: auto;
            }
        }

        .o_calendar_disabled {
            background-color: $gray-200;
        }

        // ======  Specific agenda types ======
        // ====================================

        // ====== Both Day and Week agenda
        .fc-timeGridDay-view, .fc-timeGridWeek-view {
            .fc-axis {
                padding-left: $o-horizontal-padding;
            }

            .fc-widget-header.fc-today{
                  border-radius: 25px;
                  background: $o-brand-odoo;
                  color: white;
            }

            // Reinfornce default border color
            tbody td {
                border-top-color: gray('400');
            }

            // Remove dotted borders (half-hours)
            .fc-time-grid .fc-slats .fc-minor td {
                border-top-style: none;
            }

            // Align labels and timelines
            .fc-axis.fc-time {
                border-top-color: transparent;

                span {
                    max-width: 45px;
                    margin-top: -19px;
                    position: relative;
                    display: block;
                }
            }

            // Add a small gap on top to show the first time label (0:00)
            .fc-scroller .fc-time-grid > .fc-slats,
            .fc-scroller .fc-time-grid > .fc-bg {
                padding-top: 15px;
            }

            // Row containing "all day" events
            div.fc-day-grid {
                background-color: $o-view-background-color;
                box-shadow: 0 6px 12px -6px rgba(black, 0.16);
                border-left-color: rgba($o-brand-odoo, 0.3);
                border-right-color: rgba($o-brand-odoo, 0.3);

                + hr.fc-widget-header {
                    padding: 1px 0 0;
                    background: gray('300');
                }

                .fc-content-skeleton tr:not(:first-child) .fc-h-event{
                    margin-top: 3px
                }
            }

            // Create a 'preudo-border' for the first row. The actual border
            // it's hidden because of border-collapse settings.
            .fc-slats tr:first-child td.fc-widget-content:last-child {
                box-shadow: inset 0 1px 0 gray('400');
            }

            .fc-day.fc-widget-content.fc-today:not(.o_calendar_disabled) {
                border-left-color: rgba($o-brand-odoo, 0.3);
                border-right-color: rgba($o-brand-odoo, 0.3);
                background: $o-view-background-color;
            }

            .fc-event {
                // Prevent events with similar color to visually overlap each other
                box-shadow: 0 0 0 1px white;

                &.fc-event:not(.fc-h-event) {
                    border-width: 3px 0 0;

                    &.fc-not-start {
                        border-width: 0 0 3px;

                        &.fc-not-end {
                            border-width: 0;
                        }
                    }
                }
            }

            // Reset position to keep the "nowIndicator" line visible
            .fc-content-col {
                position: initial;
            }
        }

        // ====== Day only
        .fc-timeGridDay-view .fc-event {
            padding: 10px;
            font-size: 14px;

            // Try to avoid showing no title for short event
            &.fc-short {
                padding-top: 0;
                padding-bottom: 0;
            }

            .fc-content {
                margin-top: 1px!important;
            }
        }

        // ====== Week only
        .fc-timeGridWeek-view {
            .fc-now-indicator {
                left: $o-horizontal-padding;
            }

            // Expand tiny events on hover/select
            .fc-event:not(.fc-h-event).o_cw_custom_highlight, .fc-event:not(.fc-h-event).o_cw_custom_hover {
                transition: margin .1s .3s, left .1s .3s, right .1s .3s;
                margin: 0!important;
                right: 1px!important;
                left: 1px!important;
            }
        }

        // ====== Month only
        .fc-dayGridMonth-view {
            padding-left: $o-horizontal-padding;

            .fc-event {
                border-radius: 25px;
            }

            .fc-widget-header {
                padding: 3px 0;
            }

            .fc-week-number {
                background: none;
                font-size: 1.2rem;
                padding: 0.1rem 0.3rem 0.1rem 0 !important;
                line-height: 1;
            }
            .fc-day-top {
                    text-align: center;
                    padding-top: 3px;
                    padding-bottom: 3px;
                    .fc-day-number {
                        float: none !important;
                    }
            }

            .fc-day-number {
                margin: 5px;
                padding: 0.1rem 0.3rem 0.1rem 0;
                font-size: 1.2rem;
                color: gray('900');
                font-weight: 400;
                line-height: 1;
                float: none !important;
            }

            .fc-day-top.fc-other-month {
                opacity: 0.8;

                .fc-day-number {
                    color: gray('500');
                }
            }

            td:last-child {
                border-right-color: transparent;
            }

            .fc-bg .fc-today {
                border-color: gray('300');
            }
            .fc-bg .fc-today:not(.o_calendar_disabled) {
                background: $o-view-background-color;
            }

            .fc-content-skeleton .fc-today .fc-day-number {
                margin-top: 3px;
                padding: 0.4em 0.4em 0.35em;
                border-radius: 100%;
                min-width: 1.1em;
                background: $o-cw-color-today-accent;
                text-align: center;
                color: white;
                font-size: 1.1rem;
            }

            .fc-more-cell {
                > div, .fc-more  {
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            }

            .fc-event {
                margin: 0 3px 2px;

                .fc-content {
                    display: flex;
                    justify-content: start;
                    flex-direction: row;                    
                    margin: 4px 4px 3px;                    
                }

                .fc-time:not(:empty) {
                    /*rtl:ignore*/
                    padding-right: 0.5em;
                }

                .o_event_title {                    
                    @include text-truncate();
                }

                &.o_cw_nobg {

                    &.o_cw_custom_hover, &.o_cw_custom_highlight, &:hover {
                        .fc-bg {
                            visibility: visible;
                        }
                    }
                }

                &.fc-not-start {
                    border-right-width: 3px;

                    .fc-content {
                        padding-left: 6px;
                    }
                }

                &.fc-not-end {
                    margin-right: 0;

                    .fc-content {
                        padding-right: 6px;
                    }
                }
            }
        }
        // ====== Year only
        .fc-dayGridYear-view {
            border: none;
            height: 100%;
            padding-left: $o-horizontal-padding;
            box-sizing: border-box;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-evenly;
            overflow-y: auto;

            @include media-breakpoint-down(sm) {
                padding-right: 5px;
                padding-left: 5px;
            }

            @for $i from 1 through length($o-colors-complete) {
                $color: nth($o-colors-complete, $i);

                .fc-bgevent.o_calendar_striked {
                    background: linear-gradient(
                        transparent 0 40%,
                        $color 40% 60%,
                        transparent 60% 100%)
                }

                .o_cw_popover_link.o_calendar_color_#{$i - 1} {
                    border: solid 2px;
                    padding-left: 5px;
                    border-color: $color;
                    margin-top: 2px;
                    background: $color;
                    color: black;

                    &.o_attendee_status_tentative {
                        color: color-yiq($color);
                        background: repeating-linear-gradient(
                                    45deg,
                                    $color,
                                    $color 10px,
                                    rgba($color, 0.7) 10px,
                                    rgba($color, 0.7) 20px) !important;
                    }

                    &.o_attendee_status_needsAction {
                        background-color: rgba(white, 0.9)!important;
                    }

                    &.o_attendee_status_declined {
                        text-decoration: line-through;
                        background-color: rgba(white, 0.9)!important;
                    }
                }
            }

            > table {
                height: 100%;
            }

            table, tr, th, td {
                border: none;
            }

            &.fc-readonly-year-view {
                .fc-day-top:not(.fc-has-event) {
                    cursor: default !important;
                }
            }

            &:not(.fc-readonly-year-view) {
                .fc-day-top:hover > .fc-day-number {
                    font-weight: bold;
                    border-radius: 100%;
                    text-align: center;
                    background-color: rgba(#87c0d1, 0.5);
                    color: gray('900');
                }
            }

            > .fc-month-container {
                width: 25%;
                min-width: 25rem;
                box-sizing: border-box;

                @include media-breakpoint-down(sm) {
                    width: 50%;
                    min-width: 12rem;
                }

                > .fc-month {
                    width: 21rem;
                    margin: auto;

                    @include media-breakpoint-down(sm) {
                        width: 95%;
                    }

                    > .fc-toolbar.fc-header-toolbar {
                        padding-top: 10px;
                        margin-bottom: 4px;
                        cursor: default;

                        h2 {
                            font-size: 1.2rem;
                            color: gray('600');
                        }
                    }

                    .fc-widget-header {
                        padding: 2px 0;
                        cursor: default;
                    }

                    .fc-dayGridMonth-view {
                        padding-left: unset;

                        .fc-has-event {
                            background-color: #B4DFF5;
                        }
                    }

                    .fc-week.fc-row {
                        min-height: 2rem;
                    }

                    .fc-disabled-day {
                        background-color: unset;
                    }

                    .fc-day-top {
                        text-align: center;
                        padding: 0.4vh;
                        cursor: pointer;

                        > .fc-day-number {
                            display: block;
                            float: unset;
                            line-height: unset;
                            margin: auto;
                            padding: 0.1rem 0;
                            font-size: calc(11px + 0.2vh);
                        }

                        &.fc-today > .fc-day-number {
                            font-weight: bold;
                        }
                    }
                }
            }
        }
        // ====== RTL layout(s)
        &.fc-rtl {
            .fc-timeGrid-view .fc-event {
                border-width: 0 3px 0 0;
            }

            .fc-dayGridMonth-view .fc-event {
                border-width: 0 3px 0 0;

                &.fc-not-start {
                    margin: 0 0 1px 5px;
                    border-width: 0 0 0 3px;

                    .fc-content {
                        padding-right: 6px;
                        padding-left: 0;
                    }
                }

                &.fc-not-end {
                    margin: 0 5px 1px 0;

                    .fc-content {
                        padding-left: 6px;
                        padding-right: 0;
                    }
                }
            }
        }
    }
}

.o_calendar_sidebar_container {
    flex: 0 0 auto;
    position: relative;
    @include o-webclient-padding($top: $o-horizontal-padding/2);
    background-color: $o-view-background-color;
    border-left: 1px solid $border-color;
    overflow-y:auto;

    .o_calendar_sidebar {
        width: 200px;
        font-size: 14px;

        @include media-breakpoint-up('xl') {
            width: 250px;
        }
        // sync buttons are only displayed on calendar.event views
        .o_calendar_sync {
            padding-top: 0.5em;
        }
    }

    .ui-datepicker {
        margin: 0;
        width: 100%;
        padding: 0;

        &, td, .ui-datepicker-header, td a, td span {
            border: 0;
        }

        th {
            padding: .7em .2em;
            width: 14%;

            > span {
                color: #666666;
            }
        }

        td {
            padding: 0;

            a, span {
                padding: 5px 0;
                background: none;
                text-align: center;
                vertical-align: middle;
                font-size: 1.2rem;
                color: gray('900');
                font-weight: 400;
            }

            &.ui-datepicker-current-day a {
                background: $info;
                color: color-yiq($info);
                font-weight: bold;
            }

            &.ui-datepicker-today a {
                margin: auto;
                border-radius: 100%;
                padding: .1em;
                width: 25px;
                background: mix($o-cw-color-today-accent, white, 80%);
                color: white;
            }

            &.ui-datepicker-current-day.ui-datepicker-today a {
                background: $o-cw-color-today-accent;
            }
        }

        .ui-datepicker-header {
            background: none;
        }

        .ui-datepicker-header {
            border-radius: 0;

            .ui-datepicker-title {
                color: gray('600');
                font-size: 1.2rem;
                font-weight: normal;
            }

            .ui-icon {
                background-image: none;
                text-indent: 0;
                color: transparent;

                &:before {
                    font: normal normal normal 13px/1 FontAwesome;
                    content: "\f053";
                    color: gray('400');
                }
                &.ui-icon-circle-triangle-e:before {
                    content: "\f054"
                }
            }

            .ui-state-hover.ui-datepicker-next-hover, .ui-state-hover.ui-datepicker-prev-hover {
                background: none;
                border: none;
                cursor: pointer;

                span:before {
                    color: gray('800');
                }
            }
        }

        .o_selected_range.o_color:not(.ui-datepicker-unselectable) {
            background-color: $info;
            animation: backgroundfade 2s forwards;
        }
    }

    .o_calendar_filter {
        font-size: 0.9em;
        padding: 2em 0 1em;

        .o_cw_filter_collapse_icon {
            transition: all 0.3s ease;
            @include o-hover-opacity();
            font-size: 0.7em;
        }

        .collapsed .o_cw_filter_collapse_icon {
            transform: rotate(90deg);
            opacity: 1;
        }

        .o_calendar_filter_items_checkall, .o_calendar_filter_item {
            cursor: pointer;
            overflow: hidden;

            input {
                z-index: -1;
                opacity: 0;
            }

            .o_cw_filter_input_bg {
                @include size(1.3em);
                border-width: 2px;
                border-style: solid;
                border-radius: 1px;
                overflow: hidden;

                &.o_beside_avatar {
                    @include size($o-cw-filter-avatar-size);
                    border-radius: 2px;
                    object-fit: cover;
                }
            }

            input:not(:checked) + label .o_cw_filter_input_bg {
                background: transparent!important;

                i.fa {
                    visibility: hidden;
                }
            }


            .o_cw_filter_avatar {
                @include size($o-cw-filter-avatar-size);
                border-radius: 2px;

                &.fa {
                    padding: 4px 3px;
                }
            }

            .o_cw_filter_title {
                line-height: $o-line-height-base;
                flex-grow: 1;
            }

            button.o_remove {
                @include o-position-absolute(0,0,0);
                transform: translateX(100%);
                transition: transform 0.2s;
            }

            &:hover {
                button.o_remove {
                    transform: translateX(0%);
                }
            }
        }

        .o_field_many2one {
            margin-top: 1rem;
            width: 100%;
        }
    }
}

.o_cw_popover {
    min-width: 256px;
    max-width: 328px;
    font-size: $font-size-base;

    .card-header, .card-header .popover-header {
        font-size: 1.05em;
        font-weight: 500;
        line-height: 1;
    }

    .card-footer {
        background: none;
    }

    .o_footer_shrink {
        padding-top: 0px;
        padding-bottom: 0px;
    }
    .o_cw_popover_close {
        cursor: pointer;
    }

    .o_calendar_avatars {
        line-height: 1;
    }

    .o_calendar_avatars img {
        margin-right: 0.4rem;
        @include size($o-cw-popup-avatar-size);
        border-radius: 100%;
    }

    .list-group-item {
        padding: 0.5rem 1rem;
        border: none;
    }

    .o_cw_popover_fields_secondary {
        max-height: 170px; // Fallback for old browsers
        max-height: 25vh;
        overflow: auto;
        padding-bottom: 1px; // prevents the scrollbar to show when not needed

        &::-webkit-scrollbar {
            background: gray('200');
            width: 6px;
        }
        &::-webkit-scrollbar-thumb {
            background: gray('500');
        }
    }

    .fc-rtl & {
        text-align: right;
        .o_calendar_avatars {
            > div {
                justify-content: flex-end;
            }
            img {
                order: 2;
                margin: 0 0 0 0.4rem;
            }
        }
    }
}

// Generated with http://phrogz.net/css/distinct-colors.html
// Do not modify the first 12 colors of this list or it will break the colors in the Time Off Calendar
$o-filter_colors: lighten(#000, 46.7%), #F06050, #F4A460, #F7CD1F, #6CC1ED, #814968, #EB7E7F, #2C8397, #475577,
                    #D6145F, #30C381, #9365B8, #ff1e00, #bf9600, #0003b3, #ff006a, #cc3300, #ffea00, #00cc85,
                    #007ab3, #6900cc, #ff0048, #c2cc00, #00ffc8, #008cff, #bf0036, #b24400, #d0ff00, #00ffea,
                    #0062b3, #ea00ff, #ff0026, #8bcc00, #00bfaf, #006aff, #af00bf, #bf001d, #bf6300, #8cff00,
                    #00f2ff, #004ab3, #ff00d0, #ffa600, #3acc00, #00b6bf, #0048ff, #bf7c00, #04ff00, #00d0ff,
                    #0036bf, #ff008c, #00bf49, #0092b3, #0004ff, #b20062, #649173 !default;

// ===============  Generate color classes ===============
@for $i from 1 through length($o-filter_colors) {
    $color: nth($o-filter_colors, $i);

    .o_calendar_view .fc-view {
        .o_calendar_hatched_#{$i - 1} {
            background: repeating-linear-gradient(
                45deg,
                $color,
                $color 10px,
                rgba($color, 0.4) 10px,
                rgba($color, 0.4) 20px
            ) !important;

            .fc-bg {
                background: repeating-linear-gradient(
                    45deg,
                    $color,
                    $color 10px,
                    rgba($color, 0.5) 10px,
                    rgba($color, 0.5) 20px
                );
            }
        }
        .o_calendar_striked_#{$i - 1} {
            background: linear-gradient(
                        transparent 0 45%,
                        $color 45% 55%,
                        transparent 55% 100%);

            .fc-bg {
                background: none;
            }
        }
        .fc-bgevent.o_calendar_color_#{$i - 1} {
            border-color: $color;
            background-color: $color;
            opacity: 0.5;
        }
        .fc-event.o_calendar_striked_#{$i - 1} {
            border: none;
        }
        .fc-event.o_calendar_color_#{$i - 1} {
            border-color: $color;
            color: darken($color, 45%);
            opacity: 1;

            &.o_event_hightlight {
                opacity: 1;

                .fc-content {
                    font-weight: bold;
                }
            }

            &:not(.o_calendar_hatched):not(.o_calendar_striked){
                background: $color;
                .fc-bg {
                    background: mix($color, white);
                }
            }

            &.o_cw_custom_hover, &.o_cw_custom_highlight {
                box-shadow: 0 12px 12px -5px rgba($color, 0.3);
                color: color-yiq($color);
                z-index: 10!important;
                opacity: 1;
                right: 1px!important;
                left: 1px!important;
                margin-right: 0!important;
            }

            &.o_attendee_status_needsAction, &.o_attendee_status_tentative, &.o_attendee_status_declined, &.o_attendee_status_alone {
                border-width: 2px 2px 2px !important;

                &.o_cw_custom_highlight {
                    background-color: $color;
                }
            }
        
            &.o_attendee_status_tentative {
                color: color-yiq($color);
                .fc-bg {
                    background: repeating-linear-gradient(
                        45deg,
                        $color,
                        $color 10px,
                        rgba(white, 0.4) 10px,
                        rgba(white, 0.4) 20px
                    ) !important;
                }
            }

            &.o_attendee_status_alone, &.o_attendee_status_needsAction {
                background-color: rgba(white, 0.9)!important;
                color: black;
                .fc-bg {
                    background-color: rgba(white, 0.9)!important;
                }
            }

            &.o_attendee_status_declined {
                text-decoration: line-through;
                background-color: rgba(white, 0.9)!important;
                color: black;
                .fc-bg {
                    background-color: rgba(white, 0.9)!important;
                }
            }
        }
    }

    .o_cw_filter_color_#{$i - 1} {
        .o_cw_filter_input_bg {
            border-color: $color;
            background: $color;
            color: color-yiq($color);
        }
    }

    .o_cw_popover.o_calendar_color_#{$i - 1} {
        $color-subdle: mix(white, $color, 90%);

        .card-header, .card-header .popover-header {
            background-color: $color-subdle;
            color: color-yiq($color-subdle);
        }

        .card-header {
            padding-left: 2px;
            border-color: mix($card-border-color, mix(white, $color));
        }
    }
}

.modal {
    .o_attendee_head {
        width: 32px;
        margin-right: 5px;
    }
}

.o_dashboard {
    .o_calendar_container .o_calendar_sidebar_container {
        display: none;
    }
}
