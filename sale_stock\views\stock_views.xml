<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <menuitem id="menu_aftersale" name="After-Sale"
            groups="sales_team.group_sale_salesman"
            parent="sale.sale_menu_root" sequence="5" />
        <menuitem id="menu_invoiced" name="Invoicing" parent="menu_aftersale" sequence="1"/>

        <record id="stock_location_route_view_form_inherit_sale_stock" model="ir.ui.view">
            <field name="name">stock.location.route.form</field>
            <field name="inherit_id" ref="stock.stock_location_route_form_view"/>
            <field name="model">stock.location.route</field>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='warehouse_ids']" position="after">
                    <br/><field name="sale_selectable" string="Sales Order Lines"/>
                </xpath>
            </field>
        </record>

        <record id="product_template_view_form_inherit_sale" model="ir.ui.view">
            <field name="name">product.template.inherit.form</field>
            <field name="inherit_id" ref="sale.product_template_form_view_sale_order_button"/>
            <field name="model">product.template</field>
            <field name="arch" type="xml">
                <xpath expr="//button[@name='action_view_sales']" position="replace" />
            </field>
        </record>

        <record id="product_template_view_form_inherit_stock" model="ir.ui.view">
            <field name="name">product.template.inherit.form</field>
            <field name="inherit_id" ref="stock.product_template_form_view_procurement_button"/>
            <field name="model">product.template</field>
            <field name="arch" type="xml">
                <button name="action_view_orderpoints" position="after">
                    <button class="oe_stat_button" name="action_view_sales"
                        type="object" icon="fa-signal" attrs="{'invisible': [('sale_ok', '=', False)]}"
                        groups="sales_team.group_sale_salesman" help="Sold in the last 365 days">
                        <div class="o_field_widget o_stat_info">
                            <span class="o_stat_value">
                                <field name="sales_count" widget="statinfo" nolabel="1" class="mr4"/>
                                <field name="uom_name"/>
                            </span>
                            <span class="o_stat_text">Sold</span>
                        </div>
                    </button>
                </button>
            </field>
        </record>
        <record id="stock.view_stock_product_tree" model="ir.ui.view">
            <field name="groups_id" eval="[(4, ref('sales_team.group_sale_salesman'))]" />
        </record>
        <record id="stock.view_stock_product_template_tree" model="ir.ui.view">
            <field name="groups_id" eval="[(4, ref('sales_team.group_sale_salesman'))]" />
        </record>
        <record id="stock.product_template_kanban_stock_view" model="ir.ui.view">
            <field name="groups_id" eval="[(4, ref('sales_team.group_sale_salesman'))]" />
        </record>
    </data>
</odoo>
