# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_account
# 
# Translators:
# <PERSON><PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <ifara<PERSON><EMAIL>>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<PERSON><EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid " Product cost updated from %(previous)s to %(new_cost)s."
msgstr " هزینه محصول از %(previous)s به %(new_cost)s بروز شد."

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid "%(user)s changed cost from %(previous)s to %(new_price)s - %(product)s"
msgstr ""
"%(user)s هزینه را از %(previous)s به %(new_price)s تغییر داد - %(product)s"

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid ""
"%(user)s changed stock valuation from  %(previous)s to %(new_value)s - "
"%(product)s"
msgstr ""
"%(user)s ارزش گذاری موجودی را از %(previous)s به %(new_value)s تغییر داد - "
"%(product)s"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid ""
")\n"
"                            <small class=\"mx-2 font-italic\">Use a negative added value to record a decrease in the product value</small>"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form
msgid "<b>Set other input/output accounts on specific </b>"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "<span>Product</span>"
msgstr "<span>محصول</span>"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "<span>Quantity</span>"
msgstr "<span>تعداد</span>"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "<span>SN/LN</span>"
msgstr ""

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_chart_template
msgid "Account Chart Template"
msgstr "قالب درخت حساب"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_move__account_move_ids
msgid "Account Move"
msgstr "جابجایی حساب"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form
msgid "Account Stock Properties"
msgstr "ویژگی های موجودی حساب"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_quant__accounting_date
#: model:ir.model.fields,field_description:stock_account.field_stock_request_count__accounting_date
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__date
msgid "Accounting Date"
msgstr "تاریخ حسابداری"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_move_form_inherit
msgid "Accounting Entries"
msgstr "ورودی های حسابداری"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_location_form_inherit
msgid "Accounting Information"
msgstr "اطلاعات حسابداری"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Add Manual Valuation"
msgstr "افزودن ارزش گذاری دستی"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
msgid ""
"Add additional cost (transport, customs, ...) in the value of the product."
msgstr "اضافه کردن هزینه اضافی (حمل و نقل، گمرک، ...) به ارزش محصول."

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Added Value"
msgstr "ارزش افزوده"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__added_value
msgid "Added value"
msgstr "ارزش افزوده"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_res_config_settings__module_stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
msgid ""
"Affect landed costs on reception operations and split them among products to"
" update their cost price."
msgstr ""
"بر هزینه های تمام شده تا درب انبار در عملیات پذیرش تأثیر بگذارید و آنها را "
"بین محصولات تقسیم کنید تا قیمت تمام شده آنها بروز شود."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_move__analytic_account_line_id
msgid "Analytic Account Line"
msgstr "سطر حساب تحلیلی"

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_valuation__real_time
msgid "Automated"
msgstr "خودکار"

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_cost_method__average
msgid "Average Cost (AVCO)"
msgstr "میانگین هزینه (AVCO)"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Cancel"
msgstr "لغو"

#. module: stock_account
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"Cannot find a stock input account for the product %s. You must define one on"
" the product category, or on the location, before processing this operation."
msgstr ""
"نمی توان یک حساب ورودی موجودی برای محصول %s پیدا کرد. قبل از پردازش این "
"عملیات باید یکی را در دسته محصول یا مکان تعریف کنید."

#. module: stock_account
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"Cannot find a stock output account for the product %s. You must define one "
"on the product category, or on the location, before processing this "
"operation."
msgstr ""
"نمی توان یک حساب خروجی موجودی برای محصول %s پیدا کرد. قبل از پردازش این "
"عملیات باید یکی را در دسته محصول یا مکان تعریف کنید."

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"Changing your cost method is an important change that will impact your "
"inventory valuation. Are you sure you want to make that change?"
msgstr ""
"تغییر روش هزینه یک تغییر مهم است که بر ارزش گذاری موجودی شما تأثیر می گذارد."
" آیا مطمئنید که می خواهید آن تغییر را ایجاد کنید؟"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__company_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__company_id
msgid "Company"
msgstr "شرکت"

#. module: stock_account
#: model:ir.model,name:stock_account.model_res_config_settings
msgid "Config Settings"
msgstr "تنظیمات پیکربندی"

#. module: stock_account
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"Configuration error. Please configure the price difference account on the "
"product or its category to process this operation."
msgstr ""
"خطای پیکربندی لطفا حساب تفاوت قیمت محصول یا دسته آن را برای پردازش این "
"عملیات پیکربندی کنید."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_cost_method
#: model:ir.model.fields,field_description:stock_account.field_product_product__cost_method
#: model:ir.model.fields,field_description:stock_account.field_product_template__cost_method
msgid "Costing Method"
msgstr "روش هزینه یابی"

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid "Costing method change for product category %s: from %s to %s."
msgstr "تغییر روش هزینه‌یابی برای دسته محصول %s: از %s به %s."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__account_id
msgid "Counterpart Account"
msgstr "حساب طرف حساب"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_account_input_categ_id
msgid ""
"Counterpart journal items for all incoming stock moves will be posted in this account, unless there is a specific valuation account\n"
"                set on the source location. This is the default value for all products in this category. It can also directly be set on each product."
msgstr ""
"آیتم های روزنامه همتا برای همه انتقالات ورودی موجودی در این حساب پست می‌شود،"
" مگر اینکه حساب ارزش‌گذاری خاصی در محل منبع تنظیم شده باشد. این مقدار پیش "
"فرض برای همه محصولات این دسته است. همچنین می توان آن را مستقیما روی هر محصول"
" تنظیم کرد."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_picking__country_code
msgid "Country Code"
msgstr "کد کشور"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__create_uid
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__create_uid
msgid "Created by"
msgstr "ایجاد شده توسط"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__create_date
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__create_date
msgid "Created on"
msgstr "ایجاد شده در"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_quant__currency_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__currency_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__currency_id
msgid "Currency"
msgstr "ارز"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__current_quantity_svl
msgid "Current Quantity"
msgstr "مقدار فعلی"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__current_value_svl
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Current Value"
msgstr "ارزش کنونی"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_form
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Date"
msgstr "تاریخ"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_quant__accounting_date
msgid ""
"Date at which the accounting entries will be created in case of automated "
"inventory valuation. If empty, the inventory date will be used."
msgstr ""
"تاریخی که در صورت ارزیابی خودکار موجودی، ورودی های حسابداری ایجاد می شود. در"
" صورت خالی بودن، از تاریخ موجودی استفاده خواهد شد."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer__uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "واحد اندازه گیری پیش فرض برای همه عملیات موجودی استفاده می شود."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__description
msgid "Description"
msgstr "شرح"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_res_config_settings__group_lot_on_invoice
msgid "Display Lots & Serial Numbers on Invoices"
msgstr "نمایش سری ساخت و شماره سریال در فاکتورها"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__display_name
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__display_name
msgid "Display Name"
msgstr "نام نمایشی"

#. module: stock_account
#: model:res.groups,name:stock_account.group_lot_on_invoice
msgid "Display Serial & Lot Number on Invoices"
msgstr "نمایش شماره سریال و سری ساخت روی فاکتورها"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
msgid "Documentation"
msgstr "مستندات"

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"Due to a change of product category (from %s to %s), the costing method"
"                                has changed for product template %s: from %s"
" to %s."
msgstr ""
"به دلیل تغییر دسته محصول (از %s به %s)، روش هزینه یابی برای الگوی محصول %s "
"تغییر کرده است: از %s به %s."

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_cost_method__fifo
msgid "First In First Out (FIFO)"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Group by..."
msgstr "گروه‌بندی بر مبنای..."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__id
msgid "ID"
msgstr "شناسه"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_location
msgid "Inventory Locations"
msgstr "مکان های انبارداری"

#. module: stock_account
#: code:addons/stock_account/__init__.py:0
#: code:addons/stock_account/__init__.py:0
#: code:addons/stock_account/models/account_chart_template.py:0
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_valuation
#: model:ir.model.fields,field_description:stock_account.field_product_product__valuation
#: model:ir.model.fields,field_description:stock_account.field_product_template__valuation
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__property_valuation
#: model:ir.ui.menu,name:stock_account.menu_valuation
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form_stock
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
#, python-format
msgid "Inventory Valuation"
msgstr "ارزیابی انبار"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_account_move_line__is_anglo_saxon_line
msgid "Is Anglo Saxon Line"
msgstr "خط آنگلوساکسون است"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__account_journal_id
msgid "Journal"
msgstr "روزنامه"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_move
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__account_move_id
msgid "Journal Entry"
msgstr "داده روزنامه"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_move_line
msgid "Journal Item"
msgstr "آیتم روزنامه"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_res_config_settings__module_stock_landed_costs
msgid "Landed Costs"
msgstr "هزینه های سربار"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer____last_update
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation____last_update
msgid "Last Modified on"
msgstr "آخرین تغییر در"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__write_uid
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__write_uid
msgid "Last Updated by"
msgstr "آخرین تغییر توسط"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__write_date
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__write_date
msgid "Last Updated on"
msgstr "آخرین به روز رسانی در"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__stock_valuation_layer_id
msgid "Linked To"
msgstr "لینک شده به"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
msgid "Lots &amp; Serial numbers will appear on the invoice"
msgstr "سری ساخت &amp; شماره سریال روی فاکتور ظاهر می شود"

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_valuation__manual_periodic
msgid "Manual"
msgstr "دستی"

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid "Manual Stock Valuation: %s."
msgstr "ارزش گذاری موجودی دستی: %s."

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid "Manual Stock Valuation: No Reason Given."
msgstr "ارزش گذاری موجودی دستی: بدون دلیل."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_valuation
#: model:ir.model.fields,help:stock_account.field_product_product__valuation
#: model:ir.model.fields,help:stock_account.field_product_template__valuation
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer_revaluation__property_valuation
msgid ""
"Manual: The accounting entries to value the inventory are not posted automatically.\n"
"        Automated: An accounting entry is automatically created to value the inventory when a product enters or leaves the company.\n"
"        "
msgstr ""
"دفترچه راهنما: سندهای حسابداری برای ارزش گذاری موجودی به طور خودکار پست نمی شوند.\n"
"         خودکار: یک سند حسابداری به طور خودکار برای ارزش گذاری موجودی در هنگام ورود یا خروج یک محصول از شرکت ایجاد می شود.\n"
"        "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__new_value
msgid "New value"
msgstr "ارزش جدید"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__new_value_by_qty
msgid "New value by quantity"
msgstr "ارزش جدید بر اساس کمیت"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_product_product_replenishment
msgid "On Hand Value"
msgstr "ارزش در دست"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_form
msgid "Other Info"
msgstr "اطلاعات دیگر"

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_product
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__product_id
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Product"
msgstr "محصول"

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_category
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__categ_id
msgid "Product Category"
msgstr "دسته محصول"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "انتقال محصول (سطر انتقال کوجودی)"

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
#, python-format
msgid "Product Revaluation"
msgstr "ارزیابی مجدد محصول"

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_template
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__product_tmpl_id
msgid "Product Template"
msgstr "قالب محصول"

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid "Product value manually modified (from %s to %s)"
msgstr "مقدار محصول به صورت دستی اصلاح شد (از %s به %s)"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__quantity
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer__quantity
msgid "Quantity"
msgstr "تعداد"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__quantity_svl
msgid "Quantity Svl"
msgstr ""

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_quant
msgid "Quants"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__reason
msgid "Reason"
msgstr "دلیل"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer_revaluation__reason
msgid "Reason of the revaluation"
msgstr "دلیل ارزیابی مجدد"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__product_id
msgid "Related product"
msgstr "محصولات مرتبط"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__remaining_qty
msgid "Remaining Qty"
msgstr "تعداد باقی مانده"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__remaining_value
msgid "Remaining Value"
msgstr "مقدار باقی مانده"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_return_picking
msgid "Return Picking"
msgstr "برداشت بازگشت"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_return_picking_line
msgid "Return Picking Line"
msgstr "سطر برداشت بازگشت"

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid "Revaluation of %s"
msgstr "ارزیابی مجدد %s"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Revalue"
msgstr "ارزش گذاری مجدد"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer__categ_id
msgid "Select category for the current product"
msgstr "طبقه بندی برای این محصول انتخاب کنید"

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_cost_method__standard
msgid "Standard Price"
msgstr "قیمت استاندارد"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_cost_method
#: model:ir.model.fields,help:stock_account.field_product_product__cost_method
#: model:ir.model.fields,help:stock_account.field_product_template__cost_method
msgid ""
"Standard Price: The products are valued at their standard cost defined on the product.\n"
"        Average Cost (AVCO): The products are valued at weighted average cost.\n"
"        First In First Out (FIFO): The products are valued supposing those that enter the company first will also leave it first.\n"
"        "
msgstr ""
"قیمت استاندارد: محصولات به قیمت تمام شده استاندارد خود که روی محصول تعریف شده است، ارزش گذاری می شوند.\n"
"         میانگین هزینه (AVCO): محصولات با میانگین موزون بهای تمام شده ارزیابی می شوند.\n"
"         First In First Out (FIFO): محصولات با این فرض ارزش گذاری می شوند که محصولاتی که ابتدا وارد شرکت می شوند نیز ابتدا آن را ترک کنند.\n"
"         "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_account_input_categ_id
msgid "Stock Input Account"
msgstr "حساب ورودی موجودی"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_journal
msgid "Stock Journal"
msgstr "روزنامه موجودی"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_move
#: model:ir.model.fields,field_description:stock_account.field_account_bank_statement_line__stock_move_id
#: model:ir.model.fields,field_description:stock_account.field_account_move__stock_move_id
#: model:ir.model.fields,field_description:stock_account.field_account_payment__stock_move_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__stock_move_id
msgid "Stock Move"
msgstr "انتقال موجودی"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_account_output_categ_id
msgid "Stock Output Account"
msgstr "حساب خروجی موجودی"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_quantity_history
msgid "Stock Quantity History"
msgstr "تاریخچه کمیت موجودی"

#. module: stock_account
#: model:ir.model,name:stock_account.model_report_stock_report_product_product_replenishment
msgid "Stock Replenishment Report"
msgstr "گزارش جایگزینی انبار"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_request_count
msgid "Stock Request an Inventory Count"
msgstr ""

#. module: stock_account
#: model:ir.actions.act_window,name:stock_account.stock_valuation_layer_action
msgid "Stock Valuation"
msgstr "ارزش گذاری موجودی"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_valuation_account_id
msgid "Stock Valuation Account"
msgstr "حساب ارزش گذاری موجودی"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_location__valuation_in_account_id
msgid "Stock Valuation Account (Incoming)"
msgstr "حساب ارزش گذاری موجودی (ورودی)"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_location__valuation_out_account_id
msgid "Stock Valuation Account (Outgoing)"
msgstr "حساب ارزش گذاری موجودی (خروجی)"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_valuation_layer
#: model:ir.model.fields,field_description:stock_account.field_account_bank_statement_line__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_account_move__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_account_payment__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_product_product__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_stock_move__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__stock_valuation_layer_ids
msgid "Stock Valuation Layer"
msgstr "لایه ارزش گذاری موجودی"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_account_move_line__is_anglo_saxon_line
msgid "Technical field used to retrieve the anglo-saxon lines."
msgstr "فیلد فنی مورد استفاده برای بازیابی خطوط آنگلوساکسون."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_picking__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"کد کشور ISO در دو کاراکتر.\n"
"می توانید از این قسمت برای جستجوی سریع استفاده کنید."

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"The Stock Input and/or Output accounts cannot be the same as the Stock "
"Valuation account."
msgstr ""
"حساب های ورودی و/یا خروجی موجودی نمی توانند با حساب ارزش گذاری موجودی یکسان "
"باشند."

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"The action leads to the creation of a journal entry, for which you don't "
"have the access rights."
msgstr ""
"این اقدام منجر به ایجاد یک سند روزنامه می شود که شما حق دسترسی برای آن را "
"ندارید."

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid "The added value doesn't have any impact on the stock valuation"
msgstr "ارزش افزوده هیچ تاثیری بر ارزش گذاری موجودی ندارد"

#. module: stock_account
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"The move lines are not in a consistent state: some are entering and other "
"are leaving the company."
msgstr ""
"سطرهای انتقال در وضعیت ثابتی نیستند: برخی در حال ورود و برخی دیگر در حال "
"خروج از شرکت هستند."

#. module: stock_account
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"The move lines are not in a consistent states: they are doing an "
"intercompany in a single step while they should go through the intercompany "
"transit location."
msgstr ""
"خطوط انتقال در یک حالت ثابت نیستند: آنها یک شرکت بین شرکتی را در یک مرحله "
"انجام می دهند در حالی که باید از محل ترانزیت بین شرکتی عبور کنند."

#. module: stock_account
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"The move lines are not in a consistent states: they do not share the same "
"origin or destination company."
msgstr ""
"خطوط انتقال در حالت ثابتی نیستند: آنها شرکت مبدا یا مقصد یکسانی ندارند."

#. module: stock_account
#: model_terms:ir.actions.act_window,help:stock_account.stock_valuation_layer_action
msgid ""
"There is no valuation layers. Valuation layers are created when some product"
" moves should impact the valuation of the stock."
msgstr ""
"هیچ لایه ارزش گذاری وجود ندارد. لایه های ارزش گذاری زمانی ایجاد می شوند که "
"برخی از انتقال های محصول باید بر ارزش گذاری موجودی تأثیر بگذارد."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__value
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Total Value"
msgstr "ارزش کل"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_picking
msgid "Transfer"
msgstr "انتقال"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_move__to_refund
#: model:ir.model.fields,help:stock_account.field_stock_return_picking_line__to_refund
msgid ""
"Trigger a decrease of the delivered/received quantity in the associated Sale"
" Order/Purchase Order"
msgstr "باعث کاهش مقدار تحویل/دریافت شده در سفارش فروش/سفارش خرید مرتبط"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__unit_cost
msgid "Unit Value"
msgstr "ارزش واحد"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__uom_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__product_uom_name
msgid "Unit of Measure"
msgstr "واحد اندازه گیری"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_move__to_refund
#: model:ir.model.fields,field_description:stock_account.field_stock_return_picking_line__to_refund
msgid "Update quantities on SO/PO"
msgstr "بروز رسانی مقادیر در SO/PO"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_location__valuation_in_account_id
msgid ""
"Used for real-time inventory valuation. When set on a virtual location (non "
"internal type), this account will be used to hold the value of products "
"being moved from an internal location into this location, instead of the "
"generic Stock Output Account set on the product. This has no effect for "
"internal locations."
msgstr ""
"برای ارزیابی موجودی در زمان واقعی استفاده می شود. هنگامی که در یک مکان مجازی"
" (نوع غیر داخلی) تنظیم می شود، این حساب برای نگهداری ارزش محصولاتی که از یک "
"مکان داخلی به این مکان منتقل می شوند، به جای حساب خروجی موجودی عمومی که روی "
"محصول تنظیم شده است، استفاده می شود. این هیچ تاثیری برای مکان های داخلی "
"ندارد."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_location__valuation_out_account_id
msgid ""
"Used for real-time inventory valuation. When set on a virtual location (non "
"internal type), this account will be used to hold the value of products "
"being moved out of this location and into an internal location, instead of "
"the generic Stock Output Account set on the product. This has no effect for "
"internal locations."
msgstr ""
"برای ارزیابی موجودی در زمان واقعی استفاده می شود. وقتی روی یک مکان مجازی "
"(نوع غیر داخلی) تنظیم شود، این حساب برای نگهداری ارزش محصولاتی که به خارج از"
" این مکان منتقل می شوند و به یک مکان داخلی منتقل می شوند، به جای حساب خروجی "
"موجودی عمومی که روی محصول تنظیم شده است، استفاده می شود. این هیچ تاثیری برای"
" مکان های داخلی ندارد."

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_form
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_picking
msgid "Valuation"
msgstr "ارزیابی"

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid "Valuation method change for product category %s: from %s to %s."
msgstr "تغییر روش ارزش گذاری برای دسته محصول %s: از %s به %s."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_quant__value
msgid "Value"
msgstr "مقدار"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__value_svl
msgid "Value Svl"
msgstr ""

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid "Warning"
msgstr "هشدار"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_valuation_account_id
msgid ""
"When automated inventory valuation is enabled on a product, this account "
"will hold the current value of the products."
msgstr ""
"وقتی ارزیابی خودکار موجودی روی یک محصول فعال باشد، این حساب ارزش فعلی "
"محصولات را نگه می‌دارد."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_account_output_categ_id
msgid ""
"When doing automated inventory valuation, counterpart journal items for all outgoing stock moves will be posted in this account,\n"
"                unless there is a specific valuation account set on the destination location. This is the default value for all products in this category.\n"
"                It can also directly be set on each product."
msgstr ""
"هنگام انجام ارزیابی خودکار موجودی، اقلام روزنامه همتا برای همه جابجایی‌های "
"موجودی خروجی در این حساب پست می‌شود، مگر اینکه حساب ارزش‌گذاری خاصی در محل "
"مقصد تنظیم شده باشد. این مقدار پیش فرض برای همه محصولات این دسته است. همچنین"
" می توان آن را مستقیما روی هر محصول تنظیم کرد."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_journal
msgid ""
"When doing automated inventory valuation, this is the Accounting Journal in "
"which entries will be automatically posted when stock moves are processed."
msgstr ""
"هنگام انجام ارزیابی خودکار موجودی، این روزنامه حسابداری است که در آن زمانی "
"که جابجایی موجودی پردازش می شود، ورودی ها به طور خودکار پست می شوند."

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_valuation_layer_revaluation
msgid "Wizard model to reavaluate a stock inventory for a product"
msgstr "مدل ویزارد برای ارزیابی مجدد موجودی انبار برای یک محصول"

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid "You cannot revalue a product with a standard cost method."
msgstr ""
"شما نمی توانید یک محصول را با روش هزینه استاندارد ارزش گذاری مجدد کنید."

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid "You cannot revalue a product with an empty or negative stock."
msgstr ""
"شما نمی توانید یک محصول را با موجودی خالی یا منفی ارزش گذاری مجدد کنید."

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"You cannot update the cost of a product in automated valuation as it leads "
"to the creation of a journal entry, for which you don't have the access "
"rights."
msgstr ""
"شما نمی توانید هزینه یک محصول را در ارزیابی خودکار بروز کنید، زیرا منجر به "
"ایجاد یک ورودی روزنامه می شود که حق دسترسی برای آن را ندارید."

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"You don't have any input valuation account defined on your product category."
" You must define one before processing this operation."
msgstr ""
"شما هیچ حساب ارزیابی ورودی در دسته محصول خود ندارید. قبل از پردازش این "
"عملیات باید یکی را تعریف کنید."

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"You don't have any stock input account defined on your product category. You"
" must define one before processing this operation."
msgstr ""
"شما هیچ حساب ورودی موجودی تعریف شده در دسته محصول خود ندارید. قبل از پردازش "
"این عملیات باید یکی را تعریف کنید."

#. module: stock_account
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"You don't have any stock journal defined on your product category, check if "
"you have installed a chart of accounts."
msgstr ""
"شما هیچ روزنامه موجودی در دسته بندی محصول خود ندارید، بررسی کنید که آیا جدول"
" حساب را نصب کرده اید یا خیر."

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#: code:addons/stock_account/models/product.py:0
#: code:addons/stock_account/models/product.py:0
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"You don't have any stock valuation account defined on your product category."
" You must define one before processing this operation."
msgstr ""
"شما هیچ حساب ارزیابی موجودی در دسته بندی محصول خود ندارید. قبل از پردازش این"
" عملیات باید یکی را تعریف کنید."

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid "You must set a counterpart account on your product category."
msgstr "شما باید یک حساب همتا در دسته محصول خود تنظیم کنید."

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "by"
msgstr "توسط"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "for"
msgstr "برای"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form
msgid "locations"
msgstr "مکان ها"
