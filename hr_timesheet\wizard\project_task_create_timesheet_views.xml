<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <record id="project_task_create_timesheet_view_form" model="ir.ui.view">
        <field name="name">project.task.create.timesheet.wizard.form</field>
        <field name="model">project.task.create.timesheet</field>
        <field name="arch" type="xml">
            <form string="Save time">
                <group>
                    <field name="task_id" invisible="True"/>
                    <field name="time_spent" string="Duration" class="oe_inline" widget="float_time" required="True"/>
                </group>
                <field name="description" widget="text" nolabel="1" placeholder="Describe your activity..." />
                <footer>
                    <button string="Save" type="object" name="save_timesheet" class="btn btn-primary" data-hotkey="q"/>
                    <button string="Delete" type="object" name="action_delete_timesheet" class="btn btn-secondary" data-hotkey="w"/>
                    <button string="Discard" special="cancel" data-hotkey="z" type="object" class="btn btn-secondary"/>
                </footer>
            </form>
        </field>
    </record>

</odoo>
