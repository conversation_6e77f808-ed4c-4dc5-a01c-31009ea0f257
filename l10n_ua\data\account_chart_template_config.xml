<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>
        <record id="l10n_ua_psbo_chart_template" model="account.chart.template">
            <field name="property_account_receivable_id" ref="ua_psbp_361"/>
            <field name="property_account_payable_id" ref="ua_psbp_631"/>
            <field name="property_account_expense_categ_id" ref="ua_psbp_901"/>
            <field name="property_account_income_categ_id" ref="ua_psbp_701"/>
            <field name="complete_tax_set" eval="True"/>
            <field name="use_anglo_saxon" eval="True"/>
            <field name="property_stock_account_input_categ_id" ref="ua_psbp_2812"/>
            <field name="property_stock_account_output_categ_id" ref="ua_psbp_2811"/>
            <field name="property_stock_valuation_account_id" ref="ua_psbp_281"/>
            <field name="income_currency_exchange_account_id" ref="ua_psbp_711"/>
            <field name="expense_currency_exchange_account_id" ref="ua_psbp_942"/>
            <field name="default_pos_receivable_account_id" ref="ua_psbp_366" />
        </record>

        <record id="l10n_ua_ias_chart_template" model="account.chart.template">
            <field name="property_account_receivable_id" ref="ua_ias_1120"/>
            <field name="property_account_payable_id" ref="ua_ias_1200"/>
            <field name="property_account_expense_categ_id" ref="ua_ias_2200"/>
            <field name="property_account_income_categ_id" ref="ua_ias_2000"/>
            <field name="complete_tax_set" eval="True"/>
            <field name="use_anglo_saxon" eval="True"/>
            <field name="property_stock_account_input_categ_id" ref="ua_ias_1201"/>
            <field name="property_stock_account_output_categ_id" ref="ua_ias_1121"/>
            <field name="property_stock_valuation_account_id" ref="ua_ias_1100"/>
            <field name="income_currency_exchange_account_id" ref="ua_ias_2100"/>
            <field name="expense_currency_exchange_account_id" ref="ua_ias_2500"/>
            <field name="default_pos_receivable_account_id" ref="ua_ias_1122" />
        </record>
    </data>

    <data noupdate="1">
        <function model="account.chart.template" name="try_loading">
            <value eval="[ref('l10n_ua.l10n_ua_psbo_chart_template')]"/>
        </function>
    </data>
</odoo>
