.editor_enable .s_website_form[data-vcss="001"] {
    // Hidden field is only partially hidden in editor
    .s_website_form_field_hidden {
        display: block;
        opacity: 0.5;
    }
    // Fields with conditional visibility are visible and identifiable in the editor
    .s_website_form_field_hidden_if {
        display: block !important;
        background-color: $o-we-fg-light;
    }
    // Select inputs do not trigger the default browser behavior
    // Since we use a custom editable element
    .s_website_form_field select {
        pointer-events: none;
    }
    // Display the editable select as a single big field
    #editable_select.form-control {
        height: auto;

        .s_website_form_select_item {
            &.selected {
                font-weight: bold;
            }
            &:empty::before {
                content: attr(data-empty-value);
                pointer-events: none;
                font-style: italic;
                font-weight: normal;
                opacity: 0.6;
            }
        }
    }
}

.s_website_form[data-vcss="001"] {
    .s_website_form_label {
        @include media-breakpoint-down(xs) {
            width: auto !important;
        }
    }

    .s_website_form_field_hidden {
        display: none;
    }

    span.s_website_form_mark {
        font-size: 0.85em;
        font-weight: 400;
    }

    .s_website_form_dnone {
        display: none;
    }

    // The snippet editor uses padding and not margin.
    // This will include bootstrap margin in the dragable y axes
    .s_website_form_rows > .form-group {
        margin-bottom: 0;
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
    }

    .s_website_form_submit, .s_website_form_recaptcha {
        .s_website_form_label {
            float: left;
            height: 1px;
        }
    }
    .s_website_form_no_submit_label {
        .s_website_form_label {
            display: none;
        }
    }

    .form-control-file.is-invalid {
        padding-left: 3px;
        border-left: 3px solid $form-feedback-invalid-color;
    }
}

body:not(.editor_enable) .s_website_form[data-vcss="001"] {
    .s_website_form_date, .s_website_form_datetime {
        &:not(.s_website_form_datepicker_initialized) {
            [value] {
                color: transparent;
            }
        }
    }
}
