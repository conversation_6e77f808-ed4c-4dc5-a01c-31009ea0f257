<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="cz_chart_template" model="account.chart.template">
        <field name="account_journal_suspense_account_id" ref="chart_cz_261000"/>
        <field name="property_account_receivable_id" ref="chart_cz_311000"/>
        <field name="property_account_payable_id" ref="chart_cz_321000"/>
        <field name="property_account_expense_categ_id" ref="chart_cz_504000"/>
        <field name="property_account_income_categ_id" ref="chart_cz_604000"/>
        <field name="property_account_expense_id" ref="chart_cz_504000"/>
        <field name="property_account_income_id" ref="chart_cz_604000"/>
        <field name="income_currency_exchange_account_id" ref="chart_cz_663000"/>
        <field name="expense_currency_exchange_account_id" ref="chart_cz_563000"/>
        <field name="default_cash_difference_income_account_id" ref="chart_cz_668000"/>
        <field name="default_cash_difference_expense_account_id" ref="chart_cz_568000"/>
        <field name="property_stock_account_input_categ_id" ref="chart_cz_131000"/>
        <field name="property_stock_account_output_categ_id" ref="chart_cz_504000"/>
        <field name="property_stock_valuation_account_id" ref="chart_cz_132000"/>
        <field name="default_pos_receivable_account_id" ref="chart_cz_311001" />
    </record>
</odoo>
