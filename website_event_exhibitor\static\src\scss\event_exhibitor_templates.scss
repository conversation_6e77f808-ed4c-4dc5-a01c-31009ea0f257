.o_wesponsor_index {

    /*
     * COMMON
     */

    .o_wesponsor_gradient {
        background-image: linear-gradient(120deg, #875A7B, darken(#875A7B, 10%));
        opacity: 0.8;
    }

    .o_wesponsor_topbar_filters .dropdown-item {
        cursor: pointer;
    }

    /*
     * MAIN PAGE: LIST
     */

    // Sponsor card
    .o_wesponsor_card {
        .card-body {
            padding: 1rem;
        }

        .card-footer {
            padding: 0.75rem 1rem;
        }

        .o_wesponsor_card_header_badge {
            position: absolute;
            bottom: 0;
            width: 100%;
            padding: 0.25rem 1.25rem;
            text-align: right;
        }
        &.o_wesponsor_card_unpublished {
            opacity: 0.8;
        }

        .o_wesponsor_bg_image {
            background-size: contain;
            background-repeat: no-repeat;
            background-position:center;
        }

        // Display connect block on hover on large devices only
        .o_wesponsor_connect_button {
            z-index: 1;
            position: absolute !important;
            right: 0;
            top: 0;
            padding-top: 30%;
            margin-left: auto;
            margin-right: auto;
            width: 100%;
            height: 100%;
            text-align: center;
            background-color: rgba(0,0,0,0);

            a {
                display: none;
            }
            @include media-breakpoint-up(md) {
                &:hover {
                    cursor: pointer;
                    transition: background-color .2s linear;
                    background-color: rgba(0,0,0,.1);

                    a {
                        display: inline-block;
                    }
                }
            }
        }

    }

    /*
     * EXHIBITOR PAGE
     */

    .o_wevent_online_page_container {

        // Jitsi container
        #o_wsponsor_jitsi_iframe {
            height: 85vh;
        }

        .o_wesponsor_exhibitor_aside {
            .o_wesponsor_sponsor_name {
                line-height: 1rem;
            }

            .o_wesponsor_aside_logo {
                max-width: 40px;
                object-fit: contain;
                object-position: top;
            }

            // Ribbon color (FIXME)
            .ribbon_Gold {
                background-color: #e3aa24;
                color:$white;
            }

            .ribbon_Silver {
                background-color: #adb5bd;
                color: $white;
            }

            .ribbon_Bronze {
                background-color: #c7632a;
                color: $white;
            }
        }
    }
}
