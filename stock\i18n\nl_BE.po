# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * stock
#
# Translators:
# <PERSON> <<EMAIL>>, 2015
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:06+0000\n"
"PO-Revision-Date: 2016-01-30 10:36+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Dutch (Belgium) (http://www.transifex.com/odoo/odoo-9/"
"language/nl_BE/)\n"
"Language: nl_BE\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_state
msgid ""
"\n"
"                * Draft: not confirmed yet and will not be scheduled until "
"confirmed\n"
"\n"
"                * Waiting Another Operation: waiting for another move to "
"proceed before it becomes automatically available (e.g. in Make-To-Order "
"flows)\n"
"\n"
"                * Waiting Availability: still waiting for the availability "
"of products\n"
"\n"
"                * Partially Available: some products are available and "
"reserved\n"
"\n"
"                * Ready to Transfer: products reserved, simply waiting for "
"confirmation.\n"
"\n"
"                * Transferred: has been processed, can't be modified or "
"cancelled anymore\n"
"\n"
"                * Cancelled: has been cancelled, can't be confirmed anymore"
msgstr ""
"\n"
"                * Concept: Nog niet bevestigd en zolang deze niet bevestigd "
"is wordt deze niet gepland\n"
"\n"
"            * Wachten op een andere mutatie: Wachten op een andere mutatie "
"welke gedaan moet worden, voordat deze automatisch beschikbaar komt (bijv. "
"bij Maak op order producten)\n"
"\n"
"            * Wachten op beschikbaarheid: Nog wachtend op de beschikbaarheid "
"van de producten\n"
"\n"
"            * Gedeeltelijk beschikbaar: Sommige producten zijn beschikbaar "
"en sommige zijn gereserveerd\n"
"\n"
"            * Gereed voor verplaatsing: Producten zijn gereserveerd, "
"wachtend op bevestiging\n"
"\n"
"            * Overgeplaatst: Is verwerkt, kan niet meer worden aangepast of "
"worden geannuleerd \n"
"\n"
"            * Geannuleerd: Is geannuleerd, kan niet meer worden bevestigd"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_config_settings_module_stock_dropshipping
#, fuzzy
msgid ""
"\n"
"Creates the dropship route and add more complex tests\n"
"-This installs the module stock_dropshipping."
msgstr ""
"\n"
"Maakt de dropship route aan en voegt meer complexe testen toe.\n"
"- Dit installeert de module stock_dropshipping."

#. module: stock
#: code:addons/stock/stock.py:1873
#, python-format
msgid " (%s reserved)"
msgstr " (%s gereserveerd)"

#. module: stock
#: code:addons/stock/stock.py:1876
#, python-format
msgid " (reserved)"
msgstr " (gereserveerd)"

#. module: stock
#: code:addons/stock/stock.py:3548
#, python-format
msgid " MTO"
msgstr " Maak op order"

#. module: stock
#: code:addons/stock/product.py:211 code:addons/stock/product.py:384
#, python-format
msgid " On Hand"
msgstr " Beschikbaar"

#. module: stock
#: code:addons/stock/stock.py:3685 code:addons/stock/stock.py:3978
#, python-format
msgid " Sequence in"
msgstr " Reeks in"

#. module: stock
#: code:addons/stock/stock.py:3689 code:addons/stock/stock.py:3986
#, python-format
msgid " Sequence internal"
msgstr " Reeks intern"

#. module: stock
#: code:addons/stock/stock.py:3686 code:addons/stock/stock.py:3980
#, python-format
msgid " Sequence out"
msgstr " Reeks uit"

#. module: stock
#: code:addons/stock/stock.py:3687 code:addons/stock/stock.py:3982
#, python-format
msgid " Sequence packing"
msgstr " Reeks verpakking"

#. module: stock
#: code:addons/stock/stock.py:3688 code:addons/stock/stock.py:3984
#, python-format
msgid " Sequence picking"
msgstr " Reeks verzamellen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_form
msgid "#Products"
msgstr "# Producten"

#. module: stock
#: code:addons/stock/stock.py:2801
#, python-format
msgid "%s %s %s has been <b>moved to</b> scrap."
msgstr "%s %s %s is <b>verplaatst naar</b> scrap."

#. module: stock
#: code:addons/stock/stock.py:3365
#, python-format
msgid "%s: Supply Product from %s"
msgstr "%s: Lever product van %s"

#. module: stock
#: code:addons/stock/res_company.py:25
#, python-format
msgid "%s: Transit Location"
msgstr "%s: Tussen locatie"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_state
#, fuzzy
msgid ""
"* New: When the stock move is created and not yet confirmed.\n"
"* Waiting Another Move: This state can be seen when a move is waiting for "
"another one, for example in a chained flow.\n"
"* Waiting Availability: This state is reached when the procurement "
"resolution is not straight forward. It may need the scheduler to run, a "
"component to be manufactured...\n"
"* Available: When products are reserved, it is set to 'Available'.\n"
"* Done: When the shipment is processed, the state is 'Done'."
msgstr ""
"* Nieuw: Wanneer de voorraadmutatie is aangemaakt, maar nog niet is "
"bevestigd.\n"
"* Wachten op een andere mutatie: Deze status kan worden gezien wanneer de "
"mutatie wacht op een andere mutatie, bijvoorbeeld bij een gekoppeld proces.\n"
"* Wacht op beschikbaarheid: Deze staat wordt bereikt wanneer de verwerving "
"niet gelijk gedaan kan worden. Het kan zijn dat de planner moet worden "
"gestart.\n"
"* Beschikbaar: Wanneer de producten zijn gereserveerd, krijgt de mutatie de "
"status 'Beschikbaar'\n"
"* Gereed: Wanneer de levering is verwerkt, krijgt de mutatie de status "
"'Gereed'"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_usage
msgid ""
"* Vendor Location: Virtual location representing the source location for "
"products coming from your vendors\n"
"                       \n"
"* View: Virtual location used to create a hierarchical structures for your "
"warehouse, aggregating its child locations ; can't directly contain "
"products\n"
"                       \n"
"* Internal Location: Physical locations inside your own warehouses,\n"
"                       \n"
"* Customer Location: Virtual location representing the destination location "
"for products sent to your customers\n"
"                       \n"
"* Inventory Loss: Virtual location serving as counterpart for inventory "
"operations used to correct stock levels (Physical inventories)\n"
"                       \n"
"* Procurement: Virtual location serving as temporary counterpart for "
"procurement operations when the source (vendor or production) is not known "
"yet. This location should be empty when the procurement scheduler has "
"finished running.\n"
"                       \n"
"* Production: Virtual counterpart location for production operations: this "
"location consumes the raw material and produces finished products\n"
"                       \n"
"* Transit Location: Counterpart location that should be used in inter-"
"companies or inter-warehouses operations\n"
"                      "
msgstr ""
"* Leverancier locatie: Virtuele locatie die de bron locatie is voor "
"producten die afkomstig zijn van uw leveranciers\n"
"                       \n"
"* Weergave: Virtuele locatie welke gebruikt wordt om ​​een hiërarchische "
"structuur te creëren voor uw magazijn, deze bevat de onderliggende locaties "
"en kan zelf geen producten  bevatten\n"
"                       \n"
"* Interne locatie: Fysieke locaties in uw eigen magazijnen\n"
"                       \n"
"* Klant locatie: Virtuele locatie die de plaats van bestemming aangeeft voor "
"de producten naar uw klanten\n"
"                       \n"
"* Voorraadtelling: Virtuele locatie die dient als tegenhanger voor "
"voorraadtellingsactiviteiten gebruikt om de voorraden (Fysieke voorraden) te "
"corrigeren\n"
"                       \n"
"* Verwerving: Virtuele locatie die dient als tijdelijke tegenhanger voor "
"verwervingsactiviteiten, wanneer de oorsprong  (leverancier of productie) "
"nog niet bekend is. Deze locatie moet leeg zijn als de planner voor "
"verwerving klaar is.\n"
"                       \n"
"* Productie: virtuele locatie die dient als tijdelijke tegenhanger voor "
"productieactiviteiten: deze locatie verbruikt de grondstoffen en levert "
"gereed producten\n"
"\n"
"* Tussen locatie: Tegenlocatie welke moet worden gebruikt bij inter-company "
"of inter-warehouse bewerkingen\n"
"                      "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ", if accounting or purchase is installed"
msgstr ", indien boekhouding of inkoop is geïnstalleerd."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "- The Odoo Team"
msgstr "- Het Odoo Team"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"label label-default text-center odoo_purple\">'Available'</"
"span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"label label-default text-center odoo_purple\">Click on <span "
"class=\"fa fa-truck\"/> Delivery</span>"
msgstr ""
"<span class=\"label label-default text-center odoo_purple\">Klik op <span "
"class=\"fa fa-truck\"/> Levering</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"label label-default text-center odoo_purple\">Click on <span "
"class=\"fa fa-truck\"/> Shipment</span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"label label-default text-center odoo_purple\">Confirm Order</"
"span>"
msgstr ""
"<span class=\"label label-default text-center odoo_purple\">Bevestig order</"
"span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"label label-default text-center odoo_purple\">Confirm Sale</"
"span>"
msgstr ""
"<span class=\"label label-default text-center odoo_purple\">Bevestig "
"verkoop</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"label label-default text-center odoo_purple\">Find Incoming "
"Shipments</span>"
msgstr ""
"<span class=\"label label-default text-center odoo_purple\">Vind inkomende "
"zendingen</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"label label-default text-center odoo_purple\">Process the "
"products</span>"
msgstr ""
"<span class=\"label label-default text-center odoo_purple\">Verwerk de "
"producten</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"label label-default text-center odoo_purple\">Validate the "
"Delivery</span>"
msgstr ""
"<span class=\"label label-default text-center odoo_purple\">Valideer de "
"levering</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"label label-default text-center odoo_purple\">Validate the "
"Receipt Order</span>"
msgstr ""
"<span class=\"label label-default text-center odoo_purple\">Valideer het "
"orderticket</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Forecasted</span>"
msgstr "<span class=\"o_stat_text\">Voorspeld</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
msgid ""
"<span class=\"o_stat_text\">Min :</span>\n"
"                                <span class=\"o_stat_text\">Max:</span>"
msgstr ""
"<span class=\"o_stat_text\">Min :</span>\n"
"                                <span class=\"o_stat_text\">Max:</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid ""
"<span class=\"o_stat_text\">Min:</span>\n"
"                                <span class=\"o_stat_text\">Max:</span>"
msgstr ""
"<span class=\"o_stat_text\">Min:</span>\n"
"                                <span class=\"o_stat_text\">Max:</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon="
"\"&#57408;\"/>\n"
"                                        <strong>Import a file</strong><br/>\n"
"                                        <span class=\"small\">Recommended if "
"&gt;100 products</span>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon="
"\"&#57408;\"/>\n"
"                                        <strong>Een bestand importeren</"
"strong><br/>\n"
"                                        <span class=\"small\">Aanbevolen "
"indien &gt;100 producten</span>\n"
"                                    </span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon="
"\"&#57408;\"/>\n"
"                                        <strong>Import</strong><br/>\n"
"                                        <span class=\"small\">&gt; 50 "
"vendors</span>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon="
"\"&#57408;\"/>\n"
"                                        <strong>Import</strong><br/>\n"
"                                        <span class=\"small\">&gt; 50 "
"leveranciers</span>\n"
"                                    </span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon="
"\"&#57440;\"/>\n"
"                                        <strong> Create manually</strong><br/"
">\n"
"                                        <span class=\"small\">&lt; 50 "
"vendors</span>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon="
"\"&#57440;\"/>\n"
"                                        <strong> Maak manueel</strong><br/>\n"
"                                        <span class=\"small\">&lt; 50 "
"leveranciers</span>\n"
"                                    </span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon="
"\"&#57440;\"/>\n"
"                                        <strong> Create manually</strong><br/"
">\n"
"                                        <span class=\"small\">Recommended if "
"&lt;100 products</span>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon="
"\"&#57440;\"/>\n"
"                                        <strong> Handmatig aanmaken</"
"strong><br/>\n"
"                                        <span class=\"small\">Aanbevolen "
"indien &lt;100 producten</span>\n"
"                                    </span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>From the Inventory "
"application</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"<strong>Van de stock applicatie</strong>\n"
"</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>From the Purchase "
"application</strong>\n"
"                                    </span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>From the Sales application</"
"strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"<strong>Van de verkoop applicatie</strong>\n"
"</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Customer Address:</strong></span>"
msgstr "<span><strong>Klantadres:</strong></span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Delivery Address:</strong></span>"
msgstr "<span><strong>Afleveradres:</strong></span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Vendor Address:</strong></span>"
msgstr "<span><strong>Leverancier adres</strong></span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Warehouse Address:</strong></span>"
msgstr "<span><strong>Adres magazijn:</strong></span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<span>New</span>"
msgstr "<span>Nieuw</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<span>View</span>"
msgstr "<span>Bekijk</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span>You need to install the Accounting or Purchases app to manage vendors."
"</span>"
msgstr ""
"<span>U moet de app Boekhouding of Inkoop installeren om fabrikanten te "
"beheren.</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span>You need to install the Purchases Management app for this flow.</span>"
msgstr "<span>U moet de app Inkoopbeheer installeren voor deze flow.</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span>You need to install the Sales Management app for this flow.</span>"
msgstr "<span>U moet de app Verkoopbeheer installeren voor deze flow.</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "<strong>Activate <i>Track lots or serial numbers</i></strong> in your"
msgstr "<strong>Activeer <i>lot tracering of serienummers</i></strong> in uw"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>Automated flows</strong>: from sale to delivery, and purchase to "
"reception"
msgstr ""
"<strong>Geautomatiseerde flows</strong>: van verkoop naar levering, en van "
"aankoop naar ontvangst"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "<strong>Automated replenishment rules</strong>"
msgstr "<strong>Automatische aanvulregels</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>Available products</strong> are currently available for use in "
"filling a new order for purposes such as production or distribution. This "
"quantity does not include items already allocated to other orders or items "
"that are in transit from a supplier"
msgstr ""
"<strong>Beschikbare producten</strong> zijn beschikbaar voor een nieuwe "
"order te maken voor doelen zoals productie of distributie. Deze hoeveelheid "
"bevat geen items die al toegewezen zijn aan andere orders of items die "
"onderweg zijn vanuit een leverancier"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Barcode</strong>"
msgstr "<strong>Barcode</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>Buy:</strong> the product is bought from a vendor through a Purchase "
"Order"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Commitment Date</strong>"
msgstr "<strong>Datum toezegging</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>Consumable products</strong> are always assumed to be in sufficient "
"quantity in your stock, therefore their available quantities are not tracked"
msgstr ""
"Van <strong>Verbruik producten</strong> wordt verondersteld dat er altijd "
"genoeg in stock zijn, daarom is de hoeveelheid niet getraceerd"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Date</strong>"
msgstr "<strong>Datum</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Destination</strong>"
msgstr "<strong>Bestemming</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "<strong>Enjoy your Inventory management with Odoo!</strong>"
msgstr "<strong>Geniet van uw voorraadbeheer met Odoo!</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "<strong>Import data</strong>"
msgstr "<strong>Importeer data</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Inventory</strong>"
msgstr "<strong>Vooraaad</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Location</strong>"
msgstr "<strong>Locatie</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>Make to Order:</strong> the product is acquired only as demand "
"requires, each time a Sales Order is confirmed. This does not modify stock "
"in the medium term because you restock with the exact amount that was ordered"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>Make to Stock:</strong> your customers are supplied from available "
"stock. If the quantities in stock are too low to fulfill the order, a "
"Purchase Order is generated according the minimum stock rules in order to "
"get the products required"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>Manufacture:</strong> the product is manufactured internally or the "
"service is supplied from internal resources"
msgstr ""
"<strong>Produceer:</strong> het product is intern geproduceerd of de dienst "
"wordt aangeboden vanuit interne resources"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>No address defined on the supplier partner:</strong> you have to "
"complete an address for the default supplier for the product concerned."
msgstr ""
"<strong>Geen adres gedefinieerd op de leverancier partner:</strong> U moet "
"een adres invullen voor de standaard leverancier voor het betreffende "
"product."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>No bill of materials defined for production:</strong> you need to "
"create a BoM or indicate that the product can be purchased instead."
msgstr ""
"<strong>Geen materialenlijst gedefinieerd voor productie:</strong> u moet "
"een BoM aanmaken of aangeven dat het product in plaats kan worden aangekocht."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>No quantity available in stock:</strong> you have to create a "
"reordering rule and put it in the order, or manually procure it."
msgstr ""
"<strong>Geen hoeveelheid beschikbaar in stock:</strong> u moet een "
"aanvulopdracht regel aanmaken en het in de order plaatsen, of het manueel "
"produceren."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>No supplier available for a purchase:</strong> you have to define a "
"supplier in the Procurements tab of the product form."
msgstr ""
"<strong>Geen leverancier beschikbaar voor een inkoop:</strong> u moet een "
"nieuwe leverancier aanmaken in het Verwervingen tabblad op het "
"productformulier."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>On Hand products</strong> are physically located in the warehouse "
"location at the current time. This includes items that are already allocated "
"to fulfilling production needs or sales orders"
msgstr ""
"<strong>Aanwezige voorraad producten<strong> bevinden zich fysiek in de "
"magazijn locatie op dit moment Dit bevat ook items die al toegewezen zijn om "
"aan productie noden te voldoen voor verkooporders"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Order (Origin)</strong>"
msgstr "<strong>Order (bron)</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Package</strong>"
msgstr "<strong>Verpakking</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Product</strong>"
msgstr "<strong>Product</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Production Lot</strong>"
msgstr "<strong>Productie lot</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Quantity</strong>"
msgstr "<strong>Hoeveelheid</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Scheduled Date</strong>"
msgstr "<strong>Geplande datum</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<strong>Serial Number</strong>"
msgstr "<strong>Serienummer</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>Service products</strong> are non-material products provided by a "
"company or an individual"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Source</strong>"
msgstr "<strong>Bron</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>State</strong>"
msgstr "<strong>Staat</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<strong>Status</strong>"
msgstr "<strong>Status</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>Stockable products</strong> are subject to the full inventory "
"management system: minimum stock rules, automatic procurement, etc."
msgstr ""
"<strong>Voorraadproducten</strong> zijn onderhevig aan het volledige "
"voorraadbeheer systeem: minimale stockregels, automatische verwerving, enz."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Total Quantity</strong>"
msgstr "<strong>Totale hoeveelheid</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "<strong>Warehouse Locations</strong>"
msgstr "<strong>Magazijn locaties</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>When you process an incoming shipment, internal transfer or "
"delivery</strong>, assign a lot number or different lot numbers or serial "
"numbers to a product by clicking on the <span class=\"fa fa-list\"/> icon"
msgstr ""
"<strong>Wanneer u een inkomende zending, interne verplaatsing of levering "
"verwerkt</strong> wijst u een partijnummer of verschillende partijnummers of "
"serienummers toe aan een product door te klikken op het <span class=\"fa fa-"
"list\"/> icoon"

#. module: stock
#: code:addons/stock/stock.py:2941
#, python-format
msgid "A Pack"
msgstr "Een verpakking"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type_barcode_nomenclature_id
msgid "A barcode nomenclature"
msgstr "Een barcode nomenclatuur"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "A classic purchase flow looks like the following:"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "A classic sales flow looks like the following:"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
#, fuzzy
msgid ""
"A good inventory management aims to optimize stock levels: not too low (or\n"
"                        you may find yourself out of stock) and not too high "
"(your products occupy\n"
"                        space and may lose value)."
msgstr ""
"Een goede voorraadbeheer mikt op het optimaliseren van stockniveaus: niet te "
"laag (of\n"
"u valt misschien zonder stock) en niet te hoog (u producten nemen\n"
"ruimte in en u verliest mogelijk waarde)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "API Documentation"
msgstr "API documentatie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Accurate visibility on all your operations"
msgstr "Accurate zichtbaarheid op al uw operaties"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_lines_date_active
#: model:ir.model.fields,field_description:stock.field_stock_incoterms_active
#: model:ir.model.fields,field_description:stock.field_stock_location_active
#: model:ir.model.fields,field_description:stock.field_stock_location_path_active
#: model:ir.model.fields,field_description:stock.field_stock_location_route_active
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_active
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_active
msgid "Active"
msgstr "Actief"

#. module: stock
#: selection:stock.config.settings,module_stock_calendar:0
msgid "Adapt lead times using the suppliers' open days calendars (advanced)"
msgstr ""
"Wijzig doorlooptijd door de leverancier zijn open dagen kalender te "
"gebruiken (geavanceerd)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Add an internal note..."
msgstr "Voeg interne notitie toe..."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Additional Info"
msgstr "Aanvullende informatie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_comment
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Additional Information"
msgstr "Aanvullende informatie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_partner_id
msgid "Address"
msgstr "Adres"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_config_settings_module_claim_from_delivery
msgid ""
"Adds a Claim link to the delivery order.\n"
"-This installs the module claim_from_delivery."
msgstr ""
"Voegt een klachtenregistratie knop toe aan een uitgaande levering\n"
"- Dit installeert de module claim_from_delivery"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Advanced"
msgstr ""

#. module: stock
#: selection:stock.config.settings,group_stock_adv_location:0
msgid "Advanced routing of products using rules"
msgstr "Geavanceerde productieroutes van producten door middel van regels"

#. module: stock
#: selection:stock.move,procure_method:0
msgid "Advanced: Apply Procurement Rules"
msgstr "Geavanceerd: Pas verwervingsregels toe"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "All"
msgstr "Allemaal"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_type_list
msgid "All Operations"
msgstr "Alle bewerkingen"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_picking_action_picking_type
#: model:ir.ui.menu,name:stock.all_picking
msgid "All Transfers"
msgstr "Alle overboekingen"

#. module: stock
#: selection:stock.picking,move_type:0
msgid "All at once"
msgstr "Lever alles tegelijk"

#. module: stock
#: code:addons/stock/stock.py:2927
#, python-format
msgid "All products"
msgstr "Alle producten"

#. module: stock
#: selection:stock.config.settings,group_stock_tracking_owner:0
msgid "All products in your warehouse belong to your company"
msgstr "Alle producten in uw magazijn die behoren tot uw bedrijf"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_returned_move_ids
msgid "All returned moves"
msgstr "Alle retour mutaties"

#. module: stock
#: code:addons/stock/procurement.py:360
#, python-format
msgid "All stock moves have been cancelled for this procurement."
msgstr "Alle voorraad mutaties voor deze verwerving zijn geannuleerd."

#. module: stock
#: selection:stock.config.settings,module_claim_from_delivery:0
msgid "Allow claims on deliveries"
msgstr "Klachten op leveringen toestaan"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_show_entire_packs
msgid "Allow moving packs"
msgstr "Sta verplaatsing van verpakkingen toe"

#. module: stock
#: selection:stock.config.settings,module_stock_dropshipping:0
msgid "Allow suppliers to deliver directly to your customers"
msgstr "Sta leveranciers toe om direct te leveren aan uw klanten"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_config_settings_module_procurement_jit
msgid ""
"Allows you to automatically reserve the available\n"
"            products when confirming a sale order.\n"
"                This installs the module procurement_jit."
msgstr ""
"Staat u toe automatische de beschikbare\n"
"producten te reserveren bij het aanmaken van een verkooporder.\n"
"Dit installeert de module procurement_jit."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_config_settings_group_stock_packaging
msgid ""
"Allows you to create and manage your packaging dimensions and types you want "
"to be maintained in your system."
msgstr ""
"Geeft u de mogelijkheid om uw verpakking dimensies aan te maken en te "
"beheren."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_config_settings_group_uom
msgid ""
"Allows you to select and maintain different units of measure for products."
msgstr ""
"Hiermee kunt u verschillende maateenheden voor producten selecteren en "
"onderhouden."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Applicable On"
msgstr "Toepasbaar op"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route_product_selectable
msgid "Applicable on Product"
msgstr "Toepasbaar op product"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route_product_categ_selectable
msgid "Applicable on Product Category"
msgstr "Van toepassing op productcategorie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route_warehouse_selectable
msgid "Applicable on Warehouse"
msgstr "Toepasbaar op magazijn"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_rule_form_stock_inherit
msgid "Applied On"
msgstr "Toegepast op"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_config_settings
msgid "Apply"
msgstr "Toepassen"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_config_settings_decimal_precision
msgid ""
"As an example, a decimal precision of 2 will allow weights like: 9.99 kg, "
"whereas a decimal precision of 4 will allow weights like:  0.0231 kg."
msgstr ""
"Als een voorbeeld, een decimale precisie van 2 staat gewichten toe, zoals: "
"9,99 kg, waar een decimale precisie van 4, gewichten toestaat, zoals 0,00231 "
"kg."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_make_procurment_wizard
msgid "Ask New Products"
msgstr "Nieuwe producten aanvragen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Assign Owner"
msgstr "Eigenaar toewijzen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Assigned Moves"
msgstr "Mutaties toewijzen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_path_auto
#: selection:stock.location.path,auto:0
msgid "Automatic Move"
msgstr "Automatische verplaatsing"

#. module: stock
#: selection:stock.location.path,auto:0
msgid "Automatic No Step Added"
msgstr "Automatisch, geen stap toegevoegd"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_procurement
msgid "Automatic Procurements"
msgstr "Automatische verwervingen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_string_availability_info
msgid "Availability"
msgstr "Beschikbaarheid"

#. module: stock
#: selection:stock.move,state:0 selection:stock.pack.operation,state:0
#: selection:stock.picking,state:0
msgid "Available"
msgstr "Beschikbaar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_search_form_view_stock
msgid "Available Products"
msgstr "Beschikbare producten"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_backorder_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_backorder_id
msgid "Back Order of"
msgstr "Backorder van"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Back Orders"
msgstr "Backorders"

#. module: stock
#: code:addons/stock/wizard/stock_backorder_confirmation.py:33
#, python-format
msgid "Back order <em>%s</em> <b>cancelled</b>."
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:1127
#, python-format
msgid "Back order <em>%s</em> <b>created</b>."
msgstr "Backorder <em>%s</em> <b>aangemaakt</b>."

#. module: stock
#: model:ir.model,name:stock.model_stock_backorder_confirmation
msgid "Backorder Confirmation"
msgstr "Backorder bevestiging"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "Backorder creation"
msgstr "Backorder aanmaak"

#. module: stock
#: code:addons/stock/stock.py:4801
#, python-format
msgid "Backorder exists"
msgstr "Backorder bestaat"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_backorder
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Backorders"
msgstr "Backorders"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_barcode
msgid "Barcode"
msgstr "Barcode"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_config_settings
msgid "Barcode Interface"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_barcode_nomenclature_id
msgid "Barcode Nomenclature"
msgstr "Barcode nomenclatuur"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_wms_barcode_nomenclature_all
msgid "Barcode Nomenclatures"
msgstr "Barcode nomenclaturen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_module_stock_barcode
msgid "Barcode scanner support"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Before creating your products, here are a few concepts your should "
"understand:"
msgstr ""
"Voordat u uw product aanmaakt zijn er een paar concepten die u moet "
"begrijpen:"

#. module: stock
#: model:stock.location,name:stock.stock_location_4
msgid "Big Vendors"
msgstr "Grote fabrikanten"

#. module: stock
#: selection:stock.warehouse,delivery_steps:0
msgid "Bring goods to output location before shipping (Pick + Ship)"
msgstr ""
"Brengt goederen naar de leverlocatie, voor levering (verzamelen + leveren)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_quant_ids
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Bulk Content"
msgstr "Bulk inhoud"

#. module: stock
#: selection:product.template,tracking:0
msgid "By Lots"
msgstr "Op partijen"

#. module: stock
#: selection:product.template,tracking:0
msgid "By Unique Serial Number"
msgstr "Op unieke serienummer"

#. module: stock
#: code:addons/stock/stock.py:2218
#, python-format
msgid ""
"By changing this quantity here, you accept the new quantity as complete: "
"Odoo will not automatically generate a back order."
msgstr ""
"Door de hoeveelheid hier te wijzigen accepteert u de nieuwe hoeveel als "
"compleet. Odoo zal niet automatisch een backorderaanmaken."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"By default, Odoo measures products by 'units', which are generic and "
"represent just about anything"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_procure_method
msgid ""
"By default, the system will take from the stock in the source location and "
"passively wait for availability. The other possibility allows you to "
"directly create a procurement on the source location (and thus ignore its "
"current stock) to gather products. If we want to chain moves and have this "
"one to wait for the previous, this second option should be chosen."
msgstr ""
"Standaard zal het systeem de voorraad nemen van de bronlocatie en passief "
"afwachten voor de beschikbaarheid. De andere mogelijkheid is om direct "
"verwerving aan te maken op de bronlocatie (en dus de huidige voorraad te "
"negeren) om producten te verzamelen. Als we mutaties willen koppelen en "
"willen dat deze gekoppelde wacht op de vorige da dient deze tweede optie "
"gekozen te worden."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_active
msgid ""
"By unchecking the active field, you may hide a location without deleting it."
msgstr ""
"Door het uivinken van dit actief veld, kunt u een locatie verbergen zonder "
"deze te verwijderen."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_incoterms_active
msgid ""
"By unchecking the active field, you may hide an INCOTERM you will not use."
msgstr ""
"Door het veld actief uit te vinken kunt u deze INCOTERM verbergen zonder "
"deze te verwijderen."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_calendar
msgid "Calendar View"
msgstr "Kalenderoverzicht"

#. module: stock
#: code:addons/stock/stock.py:3424
#, python-format
msgid "Can't find any customer or supplier location."
msgstr "Kan geen klant of leverancier locatie vinden."

#. module: stock
#: code:addons/stock/stock.py:3532
#, python-format
msgid "Can't find any generic Make To Order route."
msgstr "Kan geen algemene 'Maak op order' route vinden."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_make_procurment_wizard
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_compute_wizard
#: model_terms:ir.ui.view,arch_db:stock.view_stock_config_settings
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_scrap_wizard
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
msgid "Cancel"
msgstr "Annuleren"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Cancel Inventory"
msgstr "Verwijder voorraadtelling"

#. module: stock
#: selection:stock.inventory,state:0 selection:stock.move,state:0
#: selection:stock.pack.operation,state:0 selection:stock.picking,state:0
msgid "Cancelled"
msgstr "Geannuleerd"

#. module: stock
#: code:addons/stock/stock.py:2048
#, python-format
msgid "Cannot unreserve a done move"
msgstr ""
"NIet mogelijk de reservering ongedaan te maken op een regel in de gereed "
"status"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_config_settings
msgid "Carriers"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_route_from_categ_ids
#: model:ir.model.fields,field_description:stock.field_product_template_route_from_categ_ids
msgid "Category Routes"
msgstr "Categorie routes"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_move_dest_exists
msgid "Chained Move Exists"
msgstr "Gekoppelde mutatie bestaat"

#. module: stock
#: model:ir.model,name:stock.model_stock_change_product_qty
msgid "Change Product Quantity"
msgstr "Hoeveelheid aanpassen"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_pack_operation_exist
msgid "Check the existance of pack operation on the picking"
msgstr "Controleer het bestaan van verpakking bewerkingen op de levering"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_quant_reserved_exist
msgid "Check the existance of quants linked to this picking"
msgstr "Controleer het bestaan van aantallen gelinkt aan deze levering"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory_move_ids_exist
msgid "Check the existance of stock moves linked to this inventory"
msgstr "Controleer het bestaan van stockmutaties gelinkt aan deze voorraad"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_return_location
msgid "Check this box to allow using this location as a return location."
msgstr ""
"Vink deze optie aan om deze locatie te gebruiken als een retour locatie."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_scrap_location
msgid ""
"Check this box to allow using this location to put scrapped/damaged goods."
msgstr ""
"Vink deze optie aan om deze locatie te gebruiken voor afgekeurde en/of "
"beschadigde producten."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_product_qty
msgid "Checked Quantity"
msgstr "Controleer hoeveelheid"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_partially_available
msgid "Checks if the move has some stock reserved"
msgstr "Controleer of de mutatie is gereserveerd"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_module_claim_from_delivery
msgid "Claims"
msgstr "Klachtenregistratie"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_picking_form
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_all
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_backorder
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_done
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_done_grouped
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_late
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_ready
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_waiting
#: model_terms:ir.actions.act_window,help:stock.stock_picking_action_picking_type
msgid "Click here to create a new transfer."
msgstr "Klik hier om een nieuwe overplaatsing aan te maken."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_deliver_move
msgid "Click to add a delivery order for this product."
msgstr "Klik om een uitgaande levering toe te voegen voor dit product."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid "Click to add a location."
msgstr "Klik om een locatie toe te voegen."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint_form
msgid "Click to add a reordering rule."
msgstr "Klik om een aanvulopdracht regel toe te voegen."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_routes_form
msgid "Click to add a route."
msgstr "Klik om een route toe te voegen."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_production_lot_form
msgid "Click to add a serial number."
msgstr "Klik om een serienummer toe te voegen."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_picking_type_action
msgid "Click to create a new picking type."
msgstr "Klik om een nieuwe picking soort aan te maken."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_move_form2
msgid "Click to create a stock movement."
msgstr "Klik om een nieuwe stockmutatie aan te maken."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree
msgid "Click to create a stock operation."
msgstr "Klik om een nieuwe stockoperatie aan te maken."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_picking_type_list
msgid "Click to define a new transfer."
msgstr "Klik om een nieuwe overplaatsing aan te maken."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_warehouse_form
msgid "Click to define a new warehouse."
msgstr "Klik om een nieuw magazijn te definiëren."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_receipt_picking_move
msgid "Click to register a product receipt."
msgstr "Klik om een nieuw product ticket te registreren."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_receive_move
msgid "Click to register a receipt for this product."
msgstr "Klik om een ticket voor dit project te registreren."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_inventory_form
msgid "Click to start an inventory."
msgstr "Klik om een voorraad te starten."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_incoterms_code
msgid "Code"
msgstr "Code"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_color
msgid "Color"
msgstr "Kleur"

#. module: stock
#: model:ir.model,name:stock.model_res_company
msgid "Companies"
msgstr "Bedrijven"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_company_id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_company_id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_company_id
#: model:ir.model.fields,field_description:stock.field_stock_location_company_id
#: model:ir.model.fields,field_description:stock.field_stock_location_path_company_id
#: model:ir.model.fields,field_description:stock.field_stock_location_route_company_id
#: model:ir.model.fields,field_description:stock.field_stock_move_company_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_company_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_company_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_company_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_company_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_company_id
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Company"
msgstr "Bedrijf"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_date_done
msgid "Completion Date of Transfer"
msgstr "Datum waarop verplaatsing voltooid is"

#. module: stock
#: model:ir.model,name:stock.model_procurement_orderpoint_compute
msgid "Compute Minimum Stock Rules"
msgstr "Bereken minimale voorraadregels"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_compute_wizard
msgid "Compute Stock"
msgstr "Voorraad berekenen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_form
msgid "Conditions"
msgstr "Voorwaarden"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_config_settings
msgid "Configuration"
msgstr "Instellingen"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_config_settings
#: model_terms:ir.ui.view,arch_db:stock.view_stock_config_settings
msgid "Configure Warehouse"
msgstr "Magazijn instellen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Confirmed"
msgstr "Bevestigd"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Confirmed Moves"
msgstr "Bevestigde mutaties"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Congratulations!"
msgstr "Gefeliciteerd!"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_lines_date_search
msgid "Consumable"
msgstr "Verbruiksartikel"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_children_ids
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Contained Packages"
msgstr "Bevat verpakkingen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_child_ids
msgid "Contains"
msgstr "Bevat"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Content"
msgstr "Inhoud"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_posx
msgid "Corridor (X)"
msgstr "Rij (X)"

#. module: stock
#: code:addons/stock/wizard/stock_immediate_transfer.py:31
#, python-format
msgid ""
"Could not reserve all requested products. Please use the 'Mark as Todo' "
"button to handle the reservation manually."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "Create Backorder"
msgstr "Maak backorder"

#. module: stock
#: code:addons/stock/stock.py:1601
#, python-format
msgid "Create Backorder?"
msgstr "Maak backorder?"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_use_create_lots
msgid "Create New Lots"
msgstr "Maak nieuwe partijen"

#. module: stock
#: selection:procurement.rule,procure_method:0
msgid "Create Procurement"
msgstr "Maak verwerving"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Create Vendors"
msgstr "Maak leveranciers aan"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Create a Quotation"
msgstr "Maak een offerte"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Create a RFQ"
msgstr "Maak een offerteaanvraag"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid ""
"Create a backorder, if you expect to process the remaining\n"
"                        products later.  Do not create a backorder if you "
"will not\n"
"                        supply the remaining products."
msgstr ""
"Maak een backorders als u verwacht om de producten\n"
"later te verwerken. Maak geen backorder aan als u niet de\n"
"overige producten aanvoert."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Create an Inventory Adjustment"
msgstr "Maak een voorraadaanpassing aan"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Create your products"
msgstr "Maak uw producten aan"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_move_ids
msgid "Created Moves"
msgstr "Maak mutatie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_procurement_ids
msgid "Created Procurements"
msgstr "Verwervingen aanmaken"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_make_procurement_create_uid
#: model:ir.model.fields,field_description:stock.field_procurement_orderpoint_compute_create_uid
#: model:ir.model.fields,field_description:stock.field_product_putaway_create_uid
#: model:ir.model.fields,field_description:stock.field_product_removal_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_incoterms_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_location_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_location_path_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_location_route_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_operation_link_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_scrap_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_lot_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_production_lot_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_make_procurement_create_date
#: model:ir.model.fields,field_description:stock.field_procurement_orderpoint_compute_create_date
#: model:ir.model.fields,field_description:stock.field_product_putaway_create_date
#: model:ir.model.fields,field_description:stock.field_product_removal_create_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_create_date
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_create_date
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_create_date
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat_create_date
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_create_date
#: model:ir.model.fields,field_description:stock.field_stock_incoterms_create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_create_date
#: model:ir.model.fields,field_description:stock.field_stock_location_create_date
#: model:ir.model.fields,field_description:stock.field_stock_location_path_create_date
#: model:ir.model.fields,field_description:stock.field_stock_location_route_create_date
#: model:ir.model.fields,field_description:stock.field_stock_move_operation_link_create_date
#: model:ir.model.fields,field_description:stock.field_stock_move_scrap_create_date
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_create_date
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_lot_create_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_create_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_create_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_create_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_create_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line_create_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_create_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_rule_form_stock_inherit
msgid "Creates"
msgstr "Maakt"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Creation"
msgstr "Aanmaken"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_create_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_date
#: model:ir.model.fields,field_description:stock.field_stock_production_lot_create_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_create_date
msgid "Creation Date"
msgstr "Aanmaakdatum"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_date
msgid "Creation Date, usually the time of the order"
msgstr "Aanmaakdatum, normaliter de datum van de order"

#. module: stock
#: code:addons/stock/stock.py:3872
#, python-format
msgid "Cross-Dock"
msgstr "Cross-Dock"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_crossdock_route_id
msgid "Crossdock Route"
msgstr "Crossdock route"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_forecast_cumulative_quantity
msgid "Cumulative Quantity"
msgstr "Cumulatieve hoeveelheid"

#. module: stock
#: model:ir.actions.act_window,name:stock.location_open_quants
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Current Stock"
msgstr "Huidige voorraad"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product_qty_available
msgid ""
"Current quantity of products.\n"
"In a context with a single Stock Location, this includes goods stored at "
"this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the "
"Stock Location of this Warehouse, or any of its children.\n"
"stored in the Stock Location of the Warehouse of this Shop, or any of its "
"children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' "
"type."
msgstr ""
"Werkelijke hoeveelheid. \n"
"Bij gebruik van een enkele voorraadlocatie, omvat dit de goederen opgeslagen "
"in deze locatie, of één van de onderliggende locaties. \n"
"Bij gebruik van een enkel magazijn, omvat dit de goederen die bij de locatie "
"voorraadlocatie van dit magazijn zijn opgeslagen, of één van de "
"onderliggende locaties. \n"
"Anders, dit omvat goederen die zijn opgeslagen op alle voorraadlocaties van "
"het type 'intern'."

#. module: stock
#: code:addons/stock/stock.py:4897
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
#, python-format
msgid "Customer"
msgstr "Klant"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_order_partner_dest_id
msgid "Customer Address"
msgstr "Klant adres"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_sale_delay
#: model:ir.model.fields,field_description:stock.field_product_template_sale_delay
msgid "Customer Lead Time"
msgstr "Levertijd aan klant"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner_property_stock_customer
#: selection:stock.location,usage:0
msgid "Customer Location"
msgstr "Klant locatie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Customer Locations"
msgstr "Klant locaties"

#. module: stock
#: model:stock.location,name:stock.stock_location_customers
#: selection:stock.picking,picking_type_code:0
#: selection:stock.picking.type,code:0
msgid "Customers"
msgstr "Klanten"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_module_delivery_dhl
msgid "DHL integration"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Daily Operations"
msgstr "Dagelijkse operaties"

#. module: stock
#: model:ir.ui.menu,name:stock.stock_picking_type_menu
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Dashboard"
msgstr "Dashboard"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_forecast_date
#: model:ir.model.fields,field_description:stock.field_stock_move_date
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_date
#: model_terms:ir.ui.view,arch_db:stock.stock_move_tree
msgid "Date"
msgstr "Datum"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_tree
msgid "Date Expected"
msgstr "Datum verwacht"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_date_done
msgid "Date of Transfer"
msgstr "Datum van verplaatsing"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_lines_date_date
msgid "Date of latest Inventory"
msgstr "Datum van laatste voorraadtelling"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_lines_date_move_date
msgid "Date of latest Stock Move"
msgstr "Datum van laatste voorraadmutatie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_lines_date_tree
msgid "Dates of Inventories"
msgstr "Datum voorraadtellingen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_lines_date_form
#: model_terms:ir.ui.view,arch_db:stock.report_stock_lines_date_search
msgid "Dates of Inventories & Moves"
msgstr "Datum van voorraadtellingen en mutaties"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_lines_date
msgid "Dates of Inventories and latest Moves"
msgstr "Datum van tellingen en laatste mutaties"

#. module: stock
#: selection:stock.warehouse.orderpoint,lead_type:0
msgid "Day(s) to get the products"
msgstr "Dag(en) om producten te krijgen"

#. module: stock
#: selection:stock.warehouse.orderpoint,lead_type:0
msgid "Day(s) to purchase"
msgstr "Dag(en) tot inkoop"

#. module: stock
#: model:res.company,overdue_msg:stock.res_company_1
msgid ""
"Dear Sir/Madam,\n"
"\n"
"Our records indicate that some payments on your account are still due. "
"Please find details below.\n"
"If the amount has already been paid, please disregard this notice. "
"Otherwise, please forward us the total amount stated below.\n"
"If you have any queries regarding your account, Please contact us.\n"
"\n"
"Thank you in advance for your cooperation.\n"
"Best Regards,"
msgstr ""
"Geachte heer/mevrouw,\n"
"\n"
"Volgens onze informatie zijn enkele facturen reeds vervallen. U vindt "
"onderstaand de details van de vervallen posten.\n"
"\n"
"Indien u reeds heeft betaald, kunt u dit bericht als niet verzonden "
"beschouwen. Indien u niet heeft betaald, wilt u het vervallen bedrag, zoals "
"onderstaand vermeld, aan ons overmaken.\n"
"\n"
"Indien u vragen heeft vernemen wij dit graag.\n"
"\n"
"Wij danken u voor uw medewerking.\n"
"Met vriendelijke groet,"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_decimal_precision
msgid "Decimal precision on weight"
msgstr "Decimale precisie van het gewicht"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_default_location_dest_id
msgid "Default Destination Location"
msgstr "Standaard bestemming locatie"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_owner_id
msgid "Default Owner"
msgstr "Standaard eigenaar"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_default_resupply_wh_id
msgid "Default Resupply Warehouse"
msgstr "Standaard magazijn heraanvullen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_default_location_src_id
msgid "Default Source Location"
msgstr "Standaard bron locatie"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_reception_steps
msgid "Default incoming route to follow"
msgstr "Standaard te volgen inkomende route"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_delivery_steps
msgid "Default outgoing route to follow"
msgstr "Standaard te volgen uitgaande route"

#. module: stock
#: selection:stock.move,procure_method:0
msgid "Default: Take From Stock"
msgstr "Standaard: Neem van voorraad"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_route_ids
msgid "Defaults routes through the warehouse"
msgstr "Standaard routes door het magazijn"

#. module: stock
#: selection:stock.config.settings,module_product_expiry:0
msgid "Define Expiration Date on serial numbers"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Define routes within your warehouse according to business needs, such as "
"Quality Control, After Sales Services or Supplier Returns"
msgstr ""
"Definieer productieroutes binnen uw magazijn afhankelijk van de noden van "
"het bedrijf, zoals kwaliteitscontrole, na-verkoop diensten of leverancier "
"retourneringen"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid ""
"Define your locations to reflect your warehouse structure and\n"
"                organization. Odoo is able to manage physical locations\n"
"                (warehouses, shelves, bin, etc), partner locations "
"(customers,\n"
"                vendors) and virtual locations which are the counterpart of\n"
"                the stock operations like the manufacturing orders\n"
"                consumptions, inventories, etc."
msgstr ""
"Definieer uw locaties om uw magazijnstructuur en organisatie\n"
"te reflecteren. Odoo kan fysieke locaties beheren\n"
"(magazijnen, planken, bakken, enz), relatie locaties (klanten,\n"
"fabrikanten) en virtuele locaties die de tegenrekening zijn van de\n"
"voorraad bewerkingen zoals de productieorder\n"
"samenstellingen, voorraden enz."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_putaway_strategy_id
msgid ""
"Defines the default method used for suggesting the exact location (shelf) "
"where to store the products. This method can be enforced at the product "
"category level, and a fallback is made on the parent locations if none is "
"set here."
msgstr ""
"Definieert de standaard methode, welke wordt gebruikt voor het voorstellen "
"van de exacte locatie (schap) waar de producten op te slaan. Deze methode "
"kan op productcategorie niveau worden afgedwongen, en een heeft een fallback "
"op de bovenliggende locaties als hier geen locatie is ingesteld."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_removal_strategy_id
msgid ""
"Defines the default method used for suggesting the exact location (shelf) "
"where to take the products from, which lot etc. for this location. This "
"method can be enforced at the product category level, and a fallback is made "
"on the parent locations if none is set here."
msgstr ""
"Definieert de standaard methode, welke wordt gebruikt voor het voorstellen "
"van de exacte locatie (schap) waar de producten vandaan te halen. Deze "
"methode kan op productcategorie niveau worden afgedwongen, en een heeft een "
"fallback op de bovenliggende locaties als hier geen locatie is ingesteld."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_path_form
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_rule_form_stock_inherit
msgid "Delay"
msgstr "Vertraging"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_path_delay
msgid "Delay (days)"
msgstr "Vertraging (dagen)"

#. module: stock
#: code:addons/stock/product.py:307
#, python-format
msgid "Delivered Qty"
msgstr "Geleverd aantal"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_deliver_move
#: model_terms:ir.ui.view,arch_db:stock.product_kanban_stock_view
msgid "Deliveries"
msgstr "Leveringen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_delivery_count
#: model_terms:ir.ui.view,arch_db:stock.product_kanban_stock_view
msgid "Delivery"
msgstr "Levering"

#. module: stock
#: code:addons/stock/stock.py:3733
#: model:stock.picking.type,name:stock.chi_picking_type_out
#: model:stock.picking.type,name:stock.picking_type_out
#, python-format
msgid "Delivery Orders"
msgstr "Uitgaande leveringen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_delivery_route_id
msgid "Delivery Route"
msgstr "Leveringsroute"

#. module: stock
#: model:ir.actions.report.xml,name:stock.action_report_delivery
msgid "Delivery Slip"
msgstr "Pakbon"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_move_type
#, fuzzy
msgid "Delivery Type"
msgstr "Pakbon"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product_route_ids
#: model:ir.model.fields,help:stock.field_product_template_route_ids
msgid ""
"Depending on the modules installed, this will allow you to define the route "
"of the product: whether it will be bought, manufactured, MTO/MTS,..."
msgstr ""
"Afhankelijk van de geïnstalleerde modules geeft u dit de mogelijkheid om de "
"route van het product aan te geven. Of het product gekocht wordt, gemaakt "
"wordt of MTO/MTS,..."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Deployment"
msgstr "Implementatie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_name
msgid "Description"
msgstr "Omschrijving"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Pickings"
msgstr "Omschrijving voor verzamellijsten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Pickings (Rack, Row and Case Information)"
msgstr "Omschrijving voor leveringen (Stelling, rij en schap informatie)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_description_picking
#: model:ir.model.fields,field_description:stock.field_product_template_description_picking
msgid "Description on Picking"
msgstr "Omschrijving bij een verzamellijst"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Destination"
msgstr "Bestemming"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_partner_id
msgid "Destination Address "
msgstr "Bestemming adres "

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_path_location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_move_location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_location_dest_id
msgid "Destination Location"
msgstr "Bestemming"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_location_dest_id
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form
msgid "Destination Location Zone"
msgstr "Bestemmingszone"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_order_move_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_move_move_dest_id
msgid "Destination Move"
msgstr "Bestemmingsmutatie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_result_package_id
msgid "Destination Package"
msgstr "Doel verpakking"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_route_ids
msgid "Destination route"
msgstr "Bestemmingsroute"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_path_form
msgid "Details"
msgstr "Details"

#. module: stock
#: model:ir.model.fields,help:stock.field_procurement_rule_procure_method
msgid ""
"Determines the procurement method of the stock move that will be generated: "
"whether it will need to 'take from the available stock' in its source "
"location or needs to ignore its stock and create a procurement over there."
msgstr ""
"Bepaald de verwervingsmethode van de te genereren voorraadmutatie: Of het de "
"beschikbare voorraad moet nemen in de bron locatie of dat het de voorraad "
"moet negeren en een verwervingsopdracht moet genereren."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_pack_operation_details_form_save
#: model_terms:ir.ui.view,arch_db:stock.view_pack_operation_lot_form
msgid "Discard"
msgstr "Negeren"

#. module: stock
#: model:stock.location,name:stock.location_dispatch_zone
msgid "Dispatch Zone"
msgstr "Leveringsgebied"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_make_procurement_display_name
#: model:ir.model.fields,field_description:stock.field_procurement_orderpoint_compute_display_name
#: model:ir.model.fields,field_description:stock.field_product_putaway_display_name
#: model:ir.model.fields,field_description:stock.field_product_removal_display_name
#: model:ir.model.fields,field_description:stock.field_report_stock_forecast_display_name
#: model:ir.model.fields,field_description:stock.field_report_stock_lines_date_display_name
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_display_name
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_display_name
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_display_name
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat_display_name
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_display_name
#: model:ir.model.fields,field_description:stock.field_stock_incoterms_display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory_display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_display_name
#: model:ir.model.fields,field_description:stock.field_stock_location_display_name
#: model:ir.model.fields,field_description:stock.field_stock_location_path_display_name
#: model:ir.model.fields,field_description:stock.field_stock_location_route_display_name
#: model:ir.model.fields,field_description:stock.field_stock_move_display_name
#: model:ir.model.fields,field_description:stock.field_stock_move_operation_link_display_name
#: model:ir.model.fields,field_description:stock.field_stock_move_scrap_display_name
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_display_name
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_lot_display_name
#: model:ir.model.fields,field_description:stock.field_stock_picking_display_name
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_display_name
#: model:ir.model.fields,field_description:stock.field_stock_production_lot_display_name
#: model:ir.model.fields,field_description:stock.field_stock_quant_display_name
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_display_name
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_display_name
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line_display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_stock_line_date
msgid ""
"Display the latest Inventories and Moves done on your products and easily "
"sort them with specific filtering criteria. If you do frequent and partial "
"inventories, you need this report in order to ensure that the stock of each "
"product is controlled at least once a year. This also lets you find out "
"which products have seen little move lately and may deserve special measures "
"(discounted sale, quality control...)"
msgstr ""
"Toon de laatste tellingen en mutaties gedaan op uw producten en sorteer deze "
"gemakkelijk met specifieke filters. Als uw frequente en gedeeltelijke "
"tellingen doet heeft u dit rapport nodig om ervoor te zorgen dat de voorraad "
"van elk product ten minste eenmaal per jaar wordt gecontroleerd. Hierdoor "
"kunt u te weten komen welke producten de laatste tijd weinig beweging laten "
"zien en extra aandacht verdienen  (extra korting, kwaliteitscontrole ...)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Do not hesitate to send us an email to describe your experience or to "
"suggest improvements!"
msgstr ""

#. module: stock
#: selection:stock.config.settings,module_claim_from_delivery:0
msgid "Do not manage claims"
msgstr "Beheer geen klachten"

#. module: stock
#: selection:stock.config.settings,group_stock_packaging:0
#: selection:stock.config.settings,group_stock_tracking_lot:0
msgid "Do not manage packaging"
msgstr "Beheer geen verpakking"

#. module: stock
#: selection:stock.config.settings,group_stock_production_lot:0
msgid "Do not track individual product items"
msgstr "Traceer geen individuele producten"

#. module: stock
#: selection:stock.config.settings,module_product_expiry:0
msgid "Do not use Expiration Date on serial numbers"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_lot_qty
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_processed_boolean
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_qty_done
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: selection:stock.move,state:0 selection:stock.pack.operation,state:0
#: selection:stock.picking,state:0
msgid "Done"
msgstr "Voltooid"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_done
msgid "Done Transfers"
msgstr "Verplaatsing gereed"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_done_grouped
msgid "Done Transfers by Date"
msgstr "Verplaatsingen gereed op datum"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Download the"
msgstr "Download het"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: selection:stock.inventory,state:0 selection:stock.pack.operation,state:0
#: selection:stock.picking,state:0
msgid "Draft"
msgstr "Concept"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Draft Moves"
msgstr "Concept-goederenstromen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_module_stock_dropshipping
msgid "Dropshipping"
msgstr "Dropship"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Edit its details or add new ones"
msgstr "Wijzig de details of voeg nieuwe toe"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "End"
msgstr "Einde"

#. module: stock
#: model:stock.location,name:stock.stock_location_7
msgid "European Customers"
msgstr "Europese afnemers"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid ""
"Every stock operation in Odoo moves the products from one\n"
"                location to another one.  For instance, if you receive "
"products\n"
"                from a vendor, Odoo will move products from the Vendor\n"
"                location to the Stock location. Each report can be performed "
"on\n"
"                physical, partner or virtual locations."
msgstr ""
"Elke stock handeling in Odoo verplaatst het product van een\n"
"locatie naar een andere. Bijvoorbeeld, als u producten ontvangt\n"
"van een fabrikant, zal Odoo de producten verplaatsen van de fabrikant\n"
"locatie naar de stock locatie. Elk rapport kan toegepast worden op \n"
"fysieke, partner of virtuele locaties."

#. module: stock
#: code:addons/stock/stock.py:4253
#, python-format
msgid "Everything inside a package should be in the same location"
msgstr "Alles in een verpakking moet zich in dezelfde locatie bevinden"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Excel template"
msgstr "Excel sjabloon"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_search_form_view_stock
msgid "Exhausted Stock"
msgstr "Nul voorraad"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_date_expected
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Expected Date"
msgstr "Datum verwacht"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_module_product_expiry
msgid "Expiration Dates"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "External note..."
msgstr "Externe notitie..."

#. module: stock
#: code:addons/stock/stock.py:1525
#, python-format
msgid "Extra Move: "
msgstr "Extra mutatie: "

#. module: stock
#: model:ir.model.fields,help:stock.field_product_removal_method
msgid "FIFO, LIFO..."
msgstr "FIFO, LIFO..."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_module_delivery_fedex
msgid "Fedex integration"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Filters"
msgstr "Filters"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway
msgid "Fixed Locations Per Categories"
msgstr "Vaste locaties per categorie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_putaway_fixed_location_ids
msgid "Fixed Locations Per Product Category"
msgstr "Vaste locaties per productcategorie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"For the Odoo Team,<br/>\n"
"                            Fabien Pinckaers, Founder"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Force Availability"
msgstr "Forceer beschikbaarheid"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category_removal_strategy_id
msgid "Force Removal Strategy"
msgstr "Forceer uitgaande voorraadmethode"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_virtual_available
msgid "Forecast Quantity"
msgstr "Virtuele hoeveelheid"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product_virtual_available
msgid ""
"Forecast quantity (computed as Quantity On Hand - Outgoing + Incoming)\n"
"In a context with a single Stock Location, this includes goods stored in "
"this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the "
"Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' "
"type."
msgstr ""
"Virtuele hoeveelheid (berekend als: Werkelijke voorraad - Uitgaand + "
"Inkomend). \n"
"Bij gebruik van een enkele voorraadlocatie, omvat dit de goederen opgeslagen "
"in deze locatie, of één van de onderliggende locaties. \n"
"Bij gebruik van een enkel magazijn, omvat dit de goederen die bij de locatie "
"voorraadlocatie van dit magazijn zijn opgeslagen, of één van de "
"onderliggende locaties. \n"
"Anders, dit omvat goederen die zijn opgeslagen op alle voorraadlocaties van "
"het type 'intern'."

#. module: stock
#: code:addons/stock/product.py:301
#: model:ir.model.fields,field_description:stock.field_product_template_virtual_available
#: model:ir.model.fields,field_description:stock.field_stock_move_availability
#, python-format
msgid "Forecasted Quantity"
msgstr "Virtuele voorraad"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_from_loc
msgid "From"
msgstr "Van"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_complete_name
msgid "Full Location Name"
msgstr "Volledige locatienaam"

#. module: stock
#: code:addons/stock/product.py:305
#, python-format
msgid "Future Deliveries"
msgstr "Toekomstige leveringen"

#. module: stock
#: code:addons/stock/product.py:311
#, python-format
msgid "Future P&L"
msgstr "Toekomstige W&V"

#. module: stock
#: code:addons/stock/product.py:323
#, python-format
msgid "Future Productions"
msgstr "Toekomstige productie"

#. module: stock
#: code:addons/stock/product.py:317
#, python-format
msgid "Future Qty"
msgstr "Toekomstige HvH"

#. module: stock
#: code:addons/stock/product.py:295
#, python-format
msgid "Future Receipts"
msgstr "Toekomstige ontvangsten"

#. module: stock
#: model:stock.location,name:stock.location_gate_a
msgid "Gate A"
msgstr "Toegang A"

#. module: stock
#: model:stock.location,name:stock.location_gate_b
msgid "Gate B"
msgstr "Toegang B"

#. module: stock
#: model:stock.location,name:stock.stock_location_5
msgid "Generic IT Vendors"
msgstr "Algemene IT-leveranciers"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_fixed_putaway_strat_sequence
msgid ""
"Give to the more specialized category, a higher priority to have them in top "
"of the list."
msgstr ""
"Geef de meer specifieke categorie een hogere prioriteit om deze bovenaan de "
"lijst te krijgen."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_path_filter
msgid "Global"
msgstr "Algemeen"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_procurement_rules
msgid "Global Procurement Rules"
msgstr "Globale verwervingsregels"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_location_path
msgid "Global Push Rules"
msgstr "Globale push regels"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_default_resupply_wh_id
msgid "Goods will always be resupplied from this warehouse"
msgstr "Goederen worden altijd heraangevuld van dit magazijn"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_level_forecast_filter
msgid "Graph"
msgstr "Diagram"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_filter
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_stock_level_forecast_filter
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Group By"
msgstr "Groeperen op"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Group by..."
msgstr "Groepeer op..."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_form_stock_inherit
msgid "Group's Pickings"
msgstr "Gegroepeerde ontvansten/leveringen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_pack_operation_exist
msgid "Has Pack Operations"
msgstr "Heeft verpak operatie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_move_ids_exist
msgid "Has Stock Moves"
msgstr "Heeft voorraadmutaties"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_quant_reserved_exist
msgid "Has quants already reserved"
msgstr "Heeft hoeveelheden al gereserveerd"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_posz
msgid "Height (Z)"
msgstr "Hoogte (Z)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Help rental management, by generating automated return moves for rented "
"products"
msgstr ""
"Help verhuurbeheer, door het genereren van automatische ruil verplaatsingen "
"voor gehuurde producten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Here are some usual problems and their solutions:"
msgstr "Hier zijn wat voorkomende problemen en hun oplossingen:"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_receipt_picking_move
msgid ""
"Here you can receive individual products, no matter what\n"
"                purchase order or picking order they come from. You will "
"find\n"
"                the list of all products you are waiting for. Once you "
"receive\n"
"                an order, you can filter based on the name of the vendor or\n"
"                the purchase order reference. Then you can confirm all "
"products\n"
"                received using the buttons on the right of each line."
msgstr ""
"Hier ontvangt u individuele producten, onafhankelijk of ze\n"
"komen van een inkooporder of levering order. U vind\n"
"de lijst van alle producten waar u op wacht. Eenmaal u een order\n"
"ontvangt kan u filteren gebaseerd op de naam van de fabrikant of\n"
"op de inkooporder referentie. U kan vervolgens alle ontvangen producten\n"
"bevestigen door de knoppen aan de rechterkant van elke lijn."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_deliver_move
msgid ""
"Here you will find the history of all past deliveries related to\n"
"                this product, as well as all the products you must deliver "
"to\n"
"                customers."
msgstr ""
"Hier vind u de geschiedenis van alle vorige leveringen gerelateerd aan\n"
"dit product en ook de producten die u moet leveren aan de\n"
"klanten."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_receive_move
msgid ""
"Here you will find the history of all receipts related to\n"
"                this product, as well as all future receipts you are "
"waiting\n"
"                from your suppliers."
msgstr ""
"Hier vind u de geschiedenis van alle betaalbewijzen gerelateerd aan\n"
"dit product, alsook de toekomstige betaalbewijzen waar u op wacht\n"
"van leveranciers."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form
msgid "History"
msgstr "Geschiedenis"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "How to use Lot Tracking:"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_make_procurement_id
#: model:ir.model.fields,field_description:stock.field_procurement_orderpoint_compute_id
#: model:ir.model.fields,field_description:stock.field_product_putaway_id
#: model:ir.model.fields,field_description:stock.field_product_removal_id
#: model:ir.model.fields,field_description:stock.field_report_stock_forecast_id
#: model:ir.model.fields,field_description:stock.field_report_stock_lines_date_id
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_id
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_id
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat_id
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_id
#: model:ir.model.fields,field_description:stock.field_stock_incoterms_id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_id
#: model:ir.model.fields,field_description:stock.field_stock_location_id
#: model:ir.model.fields,field_description:stock.field_stock_location_path_id
#: model:ir.model.fields,field_description:stock.field_stock_location_route_id
#: model:ir.model.fields,field_description:stock.field_stock_move_id
#: model:ir.model.fields,field_description:stock.field_stock_move_operation_link_id
#: model:ir.model.fields,field_description:stock.field_stock_move_scrap_id
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_id
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_lot_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_id_8069
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_id
msgid "ID"
msgstr "ID"

#. module: stock
#: code:addons/stock/stock.py:3243
#, python-format
msgid "INV:"
msgstr "TEL:"

#. module: stock
#: code:addons/stock/wizard/stock_change_product_qty.py:101
#, python-format
msgid "INV: %s"
msgstr "TEL: %s"

#. module: stock
#: model:stock.location,name:stock.stock_location_3
msgid "IT Vendors"
msgstr "IT fabrikanten"

#. module: stock
#: model:product.product,name:stock.product_icecream
#: model:product.template,name:stock.product_icecream_product_template
msgid "Ice Cream"
msgstr "IJs"

#. module: stock
#: model:product.product,description:stock.product_icecream
#: model:product.template,description:stock.product_icecream_product_template
msgid ""
"Ice cream can be mass-produced and thus is widely available in developed "
"parts of the world. Ice cream can be purchased in large cartons (vats and "
"squrounds) from supermarkets and grocery stores, in smaller quantities from "
"ice cream shops, convenience stores, and milk bars, and in individual "
"servings from small carts or vans at public events."
msgstr ""
"IJs kan in massa worden geproduceerd en is dus op grote schaal beschikbaar "
"in de ontwikkelde delen van de wereld. IJs kan worden gekocht in grote dozen "
"van supermarkten en kruidenierszaken, in kleinere hoeveelheden van "
"ijswinkels, en in individuele porties van kleine ijswagens of bestelwagens "
"bij publieke evenementen."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_name
msgid "Identifier"
msgstr "Identifier"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid ""
"If a product is not at the right place, set the checked quantity to 0 and "
"create a new line with correct location."
msgstr ""
"Als een product niet op de juiste plaats is zet u de hoeveelheid op 1 en "
"maakt u een nieuwe regel met de huidige locatie."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type_show_entire_packs
msgid ""
"If checked, this shows the packs to be moved as a whole in the Operations "
"tab all the time, even if there was no entire pack reserved."
msgstr ""
"Indien aangevinkt toont dit ten alle tijden de te verplaatsen verpakking als "
"één in het Handelingen tabblad, zelfs als er geen hele verpakkingen zijn "
"gereserveerd."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_path_propagate
msgid ""
"If checked, when the previous move is cancelled or split, the move generated "
"by this move will too"
msgstr ""
"Indien aangevinkt en de vorige mutatie wordt geannuleerd of gesplitst, dan "
"wordt de mutatie gegenereerd door deze mutatie ook geannuleerd of gesplitst"

#. module: stock
#: model:ir.model.fields,help:stock.field_procurement_rule_propagate
msgid ""
"If checked, when the previous move of the move (which was generated by a "
"next procurement) is cancelled or split, the move generated by this move "
"will too"
msgstr ""
"Indien aangevinkt en de vorige mutatie wordt geannuleerd of gesplitst, dan "
"wordt de mutatie gegenereerd door deze mutatie ook geannuleerd of gesplitst"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_propagate
msgid "If checked, when this move is cancelled, cancel the linked move too"
msgstr ""
"Indien aangevinkt en de mutatie wordt geannuleerd, annuleer dan tevens de "
"gekoppelde mutatie."

#. module: stock
#: model:ir.model.fields,help:stock.field_procurement_rule_route_id
msgid "If route_id is False, the rule is global"
msgstr "Als route_id is onwaar is, is de regel globaal"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_pack_operation_result_package_id
msgid "If set, the operations are packed into this package"
msgstr "Indien ingesteld worden de bewerkingen verpakt in deze verpakking"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint_active
msgid ""
"If the active field is set to False, it will allow you to hide the "
"orderpoint without removing it."
msgstr ""
"Als het actief veld uit staat, kunt u de minimale voorraadregel verbergen "
"zonder deze te verwijderen."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route_active
msgid ""
"If the active field is set to False, it will allow you to hide the route "
"without removing it."
msgstr ""
"Indien het actief veld is uitgevinkt, kunt u de route verbergen zonder deze "
"te verwijdern"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_path_filter
msgid "If the route is global"
msgstr "Als de route globaal is"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_negative_move_id
msgid ""
"If this is a negative quant, this will be the move that caused this negative "
"quant."
msgstr ""
"Indien dit een negatieve hoeveelheid is, dan is dit de mutatie dat deze "
"negatieve hoeveelheid heeft veroorzaakt."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type_use_create_lots
msgid ""
"If this is checked only, it will suppose you want to create new Serial "
"Numbers / Lots, so you can provide them in a text field. "
msgstr ""
"Als enkel dit is aangevinkt, betekend dit dat u een nieuwe Serienummer / "
"Partijnummer wilt aanmaken, dus u kan deze ingeven in het tekstveld."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type_use_existing_lots
msgid ""
"If this is checked, you will be able to choose the Serial Number / Lots. You "
"can also decide to not put lots in this picking type.  This means it will "
"create stock with no lot or not put a restriction on the lot taken. "
msgstr ""
"Indien aangevinkt kan u Serienummers / Partijen kiezen. U kan ook beslissen "
"om geen partijen in uw levering soort. Dit betekend dat er stock wordt "
"aangemaakt zonder partij of zonder restrictie op de genomen partij."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_backorder_id
msgid ""
"If this shipment was split, then this field links to the shipment which "
"contains the already processed part."
msgstr ""
"Als de levering wordt opgedeeld, dan zal dit veld de koppeling bevatten naar "
"de levering welke al is verwerkt."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"If you are a developer <strong>you can use our\n"
"                        API</strong> to load data automatically through\n"
"                        scripts: take a look at our"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"If you configured automatic procurement, Odoo automatically generates\n"
"                        Procurements Orders. You usually don't need to worry "
"about them, but\n"
"                        sometimes the system can remain blocked without "
"generating a\n"
"                        corresponding document, usually due to a "
"configuration problem."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory_filter
msgid ""
"If you do an entire inventory, you can choose 'All Products' and it will "
"prefill the inventory with the current stock.  If you only do some products  "
"(e.g. Cycle Counting) you can choose 'Manual Selection of Products' and the "
"system won't propose anything.  You can also let the system propose for a "
"single product / lot /... "
msgstr ""
"Indien u een complete voorraadtelling doet, kunt u 'Alle producten' "
"selecteren en de software zal de tellijst vullen met alle producten met hun "
"huidige voorraad. Indien u maar enkele producten wilt tellen (bijv. "
"cyclische telling) dan kunt u kiezen voor 'Handmatige selectie van "
"producten' en het systeem zal niets voorstellen. Het is ook mogelijk het "
"systeem een voorstel te laten doen voor een enkel product, lot, etc."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"If you have less than 50 vendors, we recommend you\n"
"                                        to create them manually."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "If you want to do it yourself:"
msgstr "Indien u dit zelf wenst te doen:"

#. module: stock
#: model:ir.model,name:stock.model_stock_immediate_transfer
msgid "Immediate Transfer"
msgstr "Directe verplaatsing"

#. module: stock
#: code:addons/stock/stock.py:1584
#, python-format
msgid "Immediate Transfer?"
msgstr "Directe verplaatsing?"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
msgid "Immediate transfer?"
msgstr "Directe verplaatsing?"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Import using the top left button in"
msgstr "Importeer met de knop links bovenaan in"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"In Odoo, <strong>Reordering Rules</strong> are used to replenish your "
"products.\n"
"                        Odoo will automatically propose a procurement to buy "
"new products if you are\n"
"                        running out of stock."
msgstr ""

#. module: stock
#: selection:stock.inventory,state:0
msgid "In Progress"
msgstr "In behandeling"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_in_type_id
msgid "In Type"
msgstr "In type"

#. module: stock
#: model:ir.model.fields,help:stock.field_procurement_order_partner_dest_id
msgid ""
"In case of dropshipping, we need to know the destination address more "
"precisely"
msgstr ""
"In het geval van een dropshipping dienen we meer te weten over het "
"bestemmingsadres"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"In case of unique serial numbers, each serial number corresponds\n"
"                        to exactly one piece.  In case of lots, you need to "
"supply the quantity\n"
"                        for each lot when you move that product."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"In short, you will get a more efficient warehouse management that leads\n"
"                        to inventory reduction and better efficiencies in "
"your daily operations."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_incoming_qty
#: model:ir.model.fields,field_description:stock.field_product_template_incoming_qty
msgid "Incoming"
msgstr "Inkomend"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_receipt_picking_move
msgid "Incoming  Products"
msgstr "Inkomende producten"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_in_date
msgid "Incoming Date"
msgstr "Ontvangst datum"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_reception_steps
msgid "Incoming Shipments"
msgstr "Inkomende leveringen"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_incoterms_code
msgid "Incoterm Standard Code"
msgstr "Incoterm standaard code"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_incoterms_tree
#: model:ir.model,name:stock.model_stock_incoterms
#: model:ir.ui.menu,name:stock.menu_action_incoterm_open
#: model_terms:ir.ui.view,arch_db:stock.stock_incoterms_form
#: model_terms:ir.ui.view,arch_db:stock.view_incoterms_tree
msgid "Incoterms"
msgstr "Leveringscondities"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_incoterms_name
msgid ""
"Incoterms are series of sales terms. They are used to divide transaction "
"costs and responsibilities between buyer and seller and reflect state-of-the-"
"art transportation practices."
msgstr ""
"Incoterms zijn een reeks van verkoop condities.   Deze worden gebruikt om de "
"transactiekosten en de verantwoordelijkheden tussen koper en verkoper te "
"verdelen."

#. module: stock
#: code:addons/stock/stock.py:2217
#, python-format
msgid "Information"
msgstr "Informatie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Initial Demand"
msgstr "Initiële aanvraag"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Initial Inventory"
msgstr "Initiële voorraad"

#. module: stock
#: code:addons/stock/stock.py:3821
#: model:stock.location,name:stock.stock_location_company
#, python-format
msgid "Input"
msgstr "Ontvangst"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_wh_input_stock_loc_id
msgid "Input Location"
msgstr "Ontvangst locatie"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_config_settings_module_stock_picking_wave
msgid ""
"Install the picking wave module which will help you grouping your pickings "
"and processing them in batch"
msgstr ""
"Installeer de picking wave module voor hulp bij het groeperen van uw "
"leveringen en deze te verwerken in een batch."

#. module: stock
#: model:stock.location,name:stock.stock_location_inter_wh
msgid "Inter Company Transit"
msgstr "Intrecompany verplaatsting"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
#: selection:stock.picking,picking_type_code:0
#: selection:stock.picking.type,code:0
msgid "Internal"
msgstr "Intern"

#. module: stock
#: selection:stock.location,usage:0
msgid "Internal Location"
msgstr "Interne Locatie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Internal Locations"
msgstr "Interne locaties"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_production_lot_ref
msgid "Internal Reference"
msgstr "Interne referentie"

#. module: stock
#: code:addons/stock/stock.py:3746
#: model:stock.picking.type,name:stock.picking_type_internal
#, python-format
msgid "Internal Transfers"
msgstr "Interne verplaatsingen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company_internal_transit_location_id
msgid "Internal Transit Location"
msgstr "Interne tussen locatie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_int_type_id
msgid "Internal Type"
msgstr "Interne soort"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_production_lot_ref
msgid ""
"Internal reference number in case it differs from the manufacturer's serial "
"number"
msgstr ""
"Interne referentienummer indien deze afwijkt van het partijnummer van de "
"fabrikant."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_location_id
msgid "Inventoried Location"
msgstr "Getelde locatie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_lot_id
msgid "Inventoried Lot/Serial Number"
msgstr "Getelde partijnummer"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_partner_id
msgid "Inventoried Owner"
msgstr "Voorraad eigenaar"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_package_id
msgid "Inventoried Pack"
msgstr "Getelde verpakking"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_product_id
msgid "Inventoried Product"
msgstr "Geteld product"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_ids
msgid "Inventories"
msgstr "Voorraden"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_filter
msgid "Inventories Month"
msgstr "Tellingen maand"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_picking_type_action
#: model:ir.actions.report.xml,name:stock.action_report_inventory
#: model:ir.model,name:stock.model_stock_inventory
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_inventory_id
#: model:ir.model.fields,field_description:stock.field_stock_move_inventory_id
#: model:ir.ui.menu,name:stock.menu_stock_root
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_pivot
msgid "Inventory"
msgstr "Voorraad"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Inventory Adjustment"
msgstr "Voorraad aanpassing"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_inventory_form
#: model:ir.ui.menu,name:stock.menu_action_inventory_form
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Inventory Adjustments"
msgstr "Voorraad aanpassingen"

#. module: stock
#: model:web.planner,tooltip_planner:stock.planner_inventory
msgid "Inventory Configuration: a step-by-step guide."
msgstr "Voorraad instellingen: Een stap voor stap hulp."

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_inventory_control
msgid "Inventory Control"
msgstr "Voorraad beheer"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_date
msgid "Inventory Date"
msgstr "Voorraad datum"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Inventory Details"
msgstr "Voorraad details"

#. module: stock
#: model:ir.model,name:stock.model_stock_inventory_line
msgid "Inventory Line"
msgstr "Voorraad regel"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory_line_ids
msgid "Inventory Lines."
msgstr "Voorraad regels"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_property_stock_inventory
#: model:ir.model.fields,field_description:stock.field_product_template_property_stock_inventory
msgid "Inventory Location"
msgstr "Voorraadtellingen locatie"

#. module: stock
#: model:ir.model,name:stock.model_stock_location
msgid "Inventory Locations"
msgstr "Voorraad locaties"

#. module: stock
#: selection:stock.location,usage:0
msgid "Inventory Loss"
msgstr "Voorraadverlies"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory_move_ids
msgid "Inventory Moves."
msgstr "Voorraad mutaties"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory_name
msgid "Inventory Name."
msgstr "Voorraad naam"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_name
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_filter
msgid "Inventory Reference"
msgstr "Voorraadtelling referentie"

#. module: stock
#: model:ir.model,name:stock.model_stock_location_route
msgid "Inventory Routes"
msgstr "Voorraad routes"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Inventory Settings"
msgstr "Voorraad instellingen"

#. module: stock
#: model:ir.actions.act_window,name:stock.quantsact
#: model:ir.ui.menu,name:stock.menu_quants
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_graph_value
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree
msgid "Inventory Valuation"
msgstr "Voorraadwaardering"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_inventory_value
msgid "Inventory Value"
msgstr "Voorraadwaarde"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid ""
"Inventory adjustments will be made by comparing the theoretical and the "
"checked quantities."
msgstr ""
"Voorraad aanpassingen worden gemaakt door het vergelijken van de "
"theoretische en gecontroleerde hoeveelheden."

#. module: stock
#: model:stock.location,name:stock.location_inventory
msgid "Inventory loss"
msgstr "Voorraadverschillen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_filter
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Inventory of"
msgstr "Voorraad van"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_return_location
msgid "Is a Return Location?"
msgstr "Is een retourneer locatie?"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_scrap_location
msgid "Is a Scrap Location?"
msgstr "Is afkeur locatie?"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"It is also possible to import your initial inventory from an Excel or CSV "
"file.\n"
"                        If you want to do that, contact your Odoo project "
"manager."
msgstr ""
"Het is ook mogelijk om uw initiële voorraad te importeren vanuit een Excel "
"of CSV bestand.\n"
"Als u dit wilt doen contacteert u uw Odoo projectbeheerder."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"It is therefore a good idea to check and try to resolve those procurement\n"
"                        exceptions. These are accessible from the Schedulers "
"menu (you need the Stock\n"
"                        Manager role to see it)."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "It is time to make your initial Inventory. In order to do so:"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_product_packaging
msgid ""
"It specifies attributes of packaging like type, quantity of packaging,etc."
msgstr ""
"Het specificeert de kenmerken van verpakkingen, zoals soort, hoeveelheid en "
"verpakking, etc."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_move_type
msgid "It specifies goods to be deliver partially or all at once"
msgstr ""
"Het specificeert goederen welke in één keer of gedeeltelijk geleverd moeten "
"worden"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_last_done_picking
msgid "Last 10 Done Pickings"
msgstr "Laatste 10 verzamelopdrachten"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_make_procurement___last_update
#: model:ir.model.fields,field_description:stock.field_procurement_orderpoint_compute___last_update
#: model:ir.model.fields,field_description:stock.field_product_putaway___last_update
#: model:ir.model.fields,field_description:stock.field_product_removal___last_update
#: model:ir.model.fields,field_description:stock.field_report_stock_forecast___last_update
#: model:ir.model.fields,field_description:stock.field_report_stock_lines_date___last_update
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation___last_update
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty___last_update
#: model:ir.model.fields,field_description:stock.field_stock_config_settings___last_update
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat___last_update
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer___last_update
#: model:ir.model.fields,field_description:stock.field_stock_incoterms___last_update
#: model:ir.model.fields,field_description:stock.field_stock_inventory___last_update
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line___last_update
#: model:ir.model.fields,field_description:stock.field_stock_location___last_update
#: model:ir.model.fields,field_description:stock.field_stock_location_path___last_update
#: model:ir.model.fields,field_description:stock.field_stock_location_route___last_update
#: model:ir.model.fields,field_description:stock.field_stock_move___last_update
#: model:ir.model.fields,field_description:stock.field_stock_move_operation_link___last_update
#: model:ir.model.fields,field_description:stock.field_stock_move_scrap___last_update
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation___last_update
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_lot___last_update
#: model:ir.model.fields,field_description:stock.field_stock_picking___last_update
#: model:ir.model.fields,field_description:stock.field_stock_picking_type___last_update
#: model:ir.model.fields,field_description:stock.field_stock_production_lot___last_update
#: model:ir.model.fields,field_description:stock.field_stock_quant___last_update
#: model:ir.model.fields,field_description:stock.field_stock_quant_package___last_update
#: model:ir.model.fields,field_description:stock.field_stock_return_picking___last_update
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line___last_update
#: model:ir.model.fields,field_description:stock.field_stock_warehouse___last_update
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint___last_update
msgid "Last Modified on"
msgstr "Laatst gewijzigd op"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_make_procurement_write_uid
#: model:ir.model.fields,field_description:stock.field_procurement_orderpoint_compute_write_uid
#: model:ir.model.fields,field_description:stock.field_product_putaway_write_uid
#: model:ir.model.fields,field_description:stock.field_product_removal_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_incoterms_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_location_path_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_location_route_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_location_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_operation_link_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_scrap_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_lot_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_production_lot_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_make_procurement_write_date
#: model:ir.model.fields,field_description:stock.field_procurement_orderpoint_compute_write_date
#: model:ir.model.fields,field_description:stock.field_product_putaway_write_date
#: model:ir.model.fields,field_description:stock.field_product_removal_write_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_write_date
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_write_date
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_write_date
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat_write_date
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_write_date
#: model:ir.model.fields,field_description:stock.field_stock_incoterms_write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_write_date
#: model:ir.model.fields,field_description:stock.field_stock_location_path_write_date
#: model:ir.model.fields,field_description:stock.field_stock_location_route_write_date
#: model:ir.model.fields,field_description:stock.field_stock_location_write_date
#: model:ir.model.fields,field_description:stock.field_stock_move_operation_link_write_date
#: model:ir.model.fields,field_description:stock.field_stock_move_scrap_write_date
#: model:ir.model.fields,field_description:stock.field_stock_move_write_date
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_lot_write_date
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_write_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_write_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_write_date
#: model:ir.model.fields,field_description:stock.field_stock_production_lot_write_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_write_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_write_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line_write_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_write_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_write_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: stock
#: code:addons/stock/stock.py:4799
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#, python-format
msgid "Late"
msgstr "Te laat"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_late
msgid "Late Transfers"
msgstr "Late verplaatsingen"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_line_date
#: model:ir.ui.menu,name:stock.menu_report_stock_line_date
msgid "Latest Inventories & Moves"
msgstr "Laatste tellingen & mutaties"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_launch_pack_operations
msgid "Launch Pack Operations"
msgstr "Lanceer verpak operaties"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_lead_days
msgid "Lead Time"
msgstr "Doorlooptijd"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_lead_type
msgid "Lead Type"
msgstr "Doorloop soort"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route_company_id
msgid "Leave this field empty if this route is shared between all companies"
msgstr "Laat dit veld leeg als u deze route wilt delen met alle bedrijven"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_parent_left
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_parent_left
msgid "Left Parent"
msgstr "Bovenliggende"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_company_id
msgid "Let this field empty if this location is shared between companies"
msgstr ""
"Laat dit veld leeg als de locatie gedeeld wordt door meerdere bedrijven"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Like with the sales flow, Odoo inventory management is\n"
"                        fully integrated with the purchase app."
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_move_operation_link
msgid "Link between stock moves and pack operations"
msgstr "Koppeling tussen voorraadmutaties en verpakking bewerkingen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_linked_move_operation_ids
msgid "Linked Moves"
msgstr "Gekoppelde mutaties"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_linked_move_operation_ids
msgid "Linked Operations"
msgstr "Gekoppelde bewerkingen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_propagated_from_id
msgid "Linked Quant"
msgstr "Gekoppeld aantal"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Localization"
msgstr "Lokalisatie"

#. module: stock
#: code:addons/stock/stock.py:4963
#: model:ir.model.fields,field_description:stock.field_product_product_location_id
#: model:ir.model.fields,field_description:stock.field_product_template_location_id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_location_id
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat_fixed_location_id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_location_id
#: model:ir.model.fields,field_description:stock.field_stock_move_scrap_location_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_location_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_location_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_location_id
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
#, python-format
msgid "Location"
msgstr "Locatie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_config_settings
msgid "Location & Warehouse"
msgstr "Locatie & Magazijn"

#. module: stock
#: model:ir.actions.report.xml,name:stock.action_report_location_barcode
msgid "Location BarCode"
msgstr "Locatie barcode"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_location_name
#: model:ir.model.fields,field_description:stock.field_stock_location_name
msgid "Location Name"
msgstr "Locatienaam"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_path_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_path_tree
msgid "Location Paths"
msgstr "Locatiepaden"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_lot_stock_id
msgid "Location Stock"
msgstr "Locatie voorraad"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_usage
msgid "Location Type"
msgstr "Locatiesoort"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_location_dest_id
msgid "Location where the system will stock the finished products."
msgstr "Locatie waar de eindproducten opgeslagen worden"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_location_form
#: model:ir.ui.menu,name:stock.menu_action_location_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_path_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_pack_operation_details_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Locations"
msgstr "Locaties"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_category_form_view_inherit
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Logistics"
msgstr "Logistiek"

#. module: stock
#: code:addons/stock/stock.py:4964
#: model:ir.model.fields,field_description:stock.field_stock_move_restrict_lot_id
#: model:ir.model.fields,field_description:stock.field_stock_move_scrap_restrict_lot_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_lot_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_lot_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#, python-format
msgid "Lot"
msgstr "Partij"

#. module: stock
#: model:ir.actions.report.xml,name:stock.action_report_lot_barcode
msgid "Lot BarCode"
msgstr "Partij barcode"

#. module: stock
#: code:addons/stock/stock.py:4542
#, python-format
msgid "Lot Details"
msgstr "Lotgegevens"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_tree
msgid "Lot Inventory"
msgstr "Partijvoorraad"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_lot_lot_name
msgid "Lot Name"
msgstr "Lotnaam"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Lot Split"
msgstr "Lot splitsen"

#. module: stock
#: constraint:stock.pack.operation.lot:0
msgid "Lot is required"
msgstr "Lot is vereist"

#. module: stock
#: model:ir.model,name:stock.model_stock_production_lot
msgid "Lot/Serial"
msgstr "Partij"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_lot_lot_id
msgid "Lot/Serial Number"
msgstr "Partijnummer"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_lot_ids
msgid "Lots"
msgstr "Partijen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_pack_lot_ids
msgid "Lots Used"
msgstr "Lots gebruikt"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_group_stock_production_lot
msgid "Lots and Serial Numbers"
msgstr "Lots en serienummers"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Lots can be encoded on incoming shipments, internal transfers and\n"
"                        outgoing deliveries according to the settings in the "
"picking type.\n"
"                        The tracking can be configured on every product: not "
"any tracing at\n"
"                        all, tracking by lot, or tracking by unique serial "
"number."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_mto_pull_id
msgid "MTO rule"
msgstr "Maak op orderregel"

#. module: stock
#: model:ir.model,name:stock.model_make_procurement
msgid "Make Procurements"
msgstr "Verwervingen maken"

#. module: stock
#: code:addons/stock/stock.py:3529
#: model:stock.location.route,name:stock.route_warehouse0_mto
#, python-format
msgid "Make To Order"
msgstr "Maak op order"

#. module: stock
#: selection:stock.warehouse,delivery_steps:0
msgid ""
"Make packages into a dedicated location, then bring them to the output "
"location for shipping (Pick + Pack + Ship)"
msgstr ""
"Maak verpakkingen in een specifieke locatie, breng deze vervolgens naar de "
"leverlocatie voor levering (Verzamel + Verpak + leveren)"

#. module: stock
#: model:res.groups,name:stock.group_tracking_owner
msgid "Manage Different Stock Owners"
msgstr "Beheer verschillende voorraad eigenaars"

#. module: stock
#: model:res.groups,name:stock.group_production_lot
msgid "Manage Lots / Serial Numbers"
msgstr "Beheer partijnummers"

#. module: stock
#: model:res.groups,name:stock.group_tracking_lot
msgid "Manage Packages"
msgstr "Beheer verpakkingen"

#. module: stock
#: model:res.groups,name:stock.group_adv_location
msgid "Manage Push and Pull inventory flows"
msgstr "Beheer push en pull voorraad flows"

#. module: stock
#: selection:stock.config.settings,group_stock_packaging:0
msgid "Manage available packaging options per products"
msgstr "Beheer beschikbare verpakkingsopties per product"

#. module: stock
#: selection:stock.config.settings,group_stock_tracking_owner:0
msgid "Manage consignee stocks (advanced)"
msgstr "Beheer geadresseerde stock (geavanceerd)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Manage default locations per product"
msgstr "Beheer standaardlocaties per product"

#. module: stock
#: model:res.groups,name:stock.group_stock_multi_locations
#, fuzzy
msgid "Manage multiple stock_locations"
msgstr "Beheer meerdere locaties en magazijnen"

#. module: stock
#: model:res.groups,name:stock.group_stock_multi_warehouses
#, fuzzy
msgid "Manage multiple warehouses"
msgstr "Beheer meerdere locaties en magazijnen"

#. module: stock
#: selection:stock.config.settings,warehouse_and_location_usage_level:0
msgid "Manage only 1 Warehouse with only 1 stock location"
msgstr ""

#. module: stock
#: selection:stock.config.settings,warehouse_and_location_usage_level:0
msgid "Manage only 1 Warehouse, composed by several stock locations"
msgstr ""

#. module: stock
#: selection:stock.config.settings,module_stock_picking_wave:0
msgid "Manage picking in batch per worker"
msgstr "Beheer leveringen in batch per werker"

#. module: stock
#: selection:stock.config.settings,module_stock_picking_wave:0
msgid "Manage pickings one at a time"
msgstr "Beheer leveringen één per één"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Manage product manufacturing chains"
msgstr "Beheer productieketens"

#. module: stock
#: selection:stock.config.settings,warehouse_and_location_usage_level:0
msgid "Manage several Warehouses, each one composed by several stock locations"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_group_stock_multi_locations
#, fuzzy
msgid "Manage several stock locations"
msgstr "Beheer meerdere locaties per magazijn"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_group_stock_multi_warehouses
#, fuzzy
msgid "Manage several warehouses"
msgstr "Beheer meerdere locaties per magazijn"

#. module: stock
#: model:res.groups,name:stock.group_stock_manager
msgid "Manager"
msgstr "Manager"

#. module: stock
#: selection:stock.location.path,auto:0
msgid "Manual Operation"
msgstr "Handmatige verwerking"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Mark as Todo"
msgstr "Markeer als taak"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_max_date
msgid "Max. Expected Date"
msgstr "Max. datum verwacht"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_product_max_qty
msgid "Maximum Quantity"
msgstr "Maximale hoeveelheid"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_putaway_method
#: model:ir.model.fields,field_description:stock.field_product_removal_method
msgid "Method"
msgstr "Methode"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company_propagation_minimum_delta
msgid "Minimum Delta for Propagation of a Date Change on moves linked together"
msgstr ""
"Minimale tijdsduur voor het doorvoeren van een datum wijziging op gekoppelde "
"mutaties"

#. module: stock
#: model:ir.model,name:stock.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "Minimale voorraadregel"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_product_min_qty
msgid "Minimum Quantity"
msgstr "Minimale hoeveelheid"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_order_orderpoint_id
msgid "Minimum Stock Rule"
msgstr "Minimale voorraadregel"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_orderpoint_ids
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_module_stock_calendar
msgid "Minimum Stock Rules"
msgstr "Minimale  voorraadregels"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_propagation_minimum_delta
msgid ""
"Minimum days to trigger a propagation of date change in pushed/pull flows."
msgstr ""
"Minimaal aantal dagen om een datum wijziging in een pushed/pull flow door te "
"geven."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "Misc"
msgstr "Overige"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Modify"
msgstr "Wijzig"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "More <i class=\"fa fa-caret-down\"/>"
msgstr "Meer <i class=\"fa fa-caret-down\"/>"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree
msgid ""
"Most operations are prepared automatically by Odoo according\n"
"                to your preconfigured logistics rules, but you can also "
"record\n"
"                manual stock movements."
msgstr ""
"De meeste operaties worden automatisch voorbereid door Odoo volgens uw "
"voorgeconfigureerde logistieke regels, maar u kan ook manuele\n"
"stockmutaties toevoegen."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_operation_link_move_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line_move_id
msgid "Move"
msgstr "Mutatie"

#. module: stock
#: code:addons/stock/procurement.py:25
#, python-format
msgid "Move From Another Location"
msgstr "Verplaats van andere locatie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_move_lines_related
msgid "Move Lines"
msgstr "Mutatie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_negative_move_id
msgid "Move Negative Quant"
msgstr "Verplaats negatieve hoeveelheid"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_split_from
msgid "Move Split From"
msgstr "Gesplitste mutatie van"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_procure_method
msgid "Move Supply Method"
msgstr "Bevooradingsmethode mutatie"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_date
msgid ""
"Move date: scheduled date until move is done, then date of actual move "
"processing"
msgstr ""
"Mutatiedatum: Geplande datum totdat de mutatie is uitgevoerd, daarna de "
"datum van de werkelijke mutatieverwerking"

#. module: stock
#: model:ir.model.fields,help:stock.field_procurement_order_move_dest_id
msgid "Move which caused (created) the procurement"
msgstr "Mutatie welke de verwerving veroorzaakte (aanmaakte)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_quant_ids
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_form
msgid "Moved Quants"
msgstr "Verplaatste hoeveelheid"

#. module: stock
#: model:ir.actions.act_window,name:stock.act_product_stock_move_open
#: model:ir.model.fields,field_description:stock.field_procurement_order_move_ids
#: model:ir.model.fields,field_description:stock.field_stock_quant_history_ids
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_product_return_moves
#: model_terms:ir.ui.view,arch_db:stock.stock_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking_board
msgid "Moves"
msgstr "Mutaties"

#. module: stock
#: model:ir.model.fields,help:stock.field_procurement_order_move_ids
msgid "Moves created by the procurement"
msgstr "Mutaties aangemaakt door de verwerving"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint_group_id
msgid ""
"Moves created through this orderpoint will be put in this procurement group. "
"If none is given, the moves generated by procurement rules will be grouped "
"into one big picking."
msgstr ""
"Mutaties welke welke worden aangemaakt door deze aanvulregel worden "
"geplaatst in deze verwervingsgroep. Indien niets is ingevoerd, worden de "
"mutaties, gegenereerd door de verwervingsregel gegroepeerd in één grote "
"levering."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_pack_operation_linked_move_operation_ids
msgid ""
"Moves impacted by this operation for the computation of the remaining "
"quantities"
msgstr ""
"Mutaties beïnvloed door deze bewerking voor de berekening van de resterende "
"hoeveelheden"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_history_ids
msgid "Moves that operate(d) on this quant"
msgstr "Mutaties dat bewerkingen hebben of uitvoeren op deze hoeveelheid"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_putaway_name
#: model:ir.model.fields,field_description:stock.field_product_removal_name
#: model:ir.model.fields,field_description:stock.field_stock_incoterms_name
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_complete_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_name
msgid "Name"
msgstr "Naam"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Negative"
msgstr "Negatief"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_negative_dest_location_id
msgid "Negative Destination Location"
msgstr "Negatieve doel locatie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_search_form_view_stock
msgid "Negative Stock"
msgstr "Negatieve voorraad"

#. module: stock
#: selection:stock.move,state:0
msgid "New"
msgstr "Nieuw"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_new_quantity
msgid "New Quantity on Hand"
msgstr "Nieuwe voorraad"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_form
msgid "New Transfer"
msgstr "Nieuwe overplaatsing"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_fresh_record
msgid "Newly created pack operation"
msgstr "Nieuw aangemaakte verpakkingsoperatie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "No Backorder"
msgstr "Geen backorder"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_lines_date_search
msgid "No Inventory yet"
msgstr "Nog geen voorraad"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_lines_date_search
msgid "No Stock Move yet"
msgstr "Nog geen voorraadmutatie"

#. module: stock
#: selection:product.template,tracking:0
msgid "No Tracking"
msgstr "Geen tracering"

#. module: stock
#: selection:stock.config.settings,group_stock_adv_location:0
msgid "No automatic routing of products"
msgstr "Geen automatische productorders van producten"

#. module: stock
#: code:addons/stock/stock.py:1614
#, python-format
msgid "No negative quantities allowed"
msgstr "Geen negatieve hoeveelheden toegestaan"

#. module: stock
#: code:addons/stock/wizard/stock_return_picking.py:75
#, python-format
msgid ""
"No products to return (only lines in Done state and not fully returned yet "
"can be returned)!"
msgstr ""
"Er zijn geen producten retour te nemen (alleen regels die verwerkt zijn en "
"nog niet volledig retour genomen zijn kunnen retour genomen worden)!"

#. module: stock
#: code:addons/stock/procurement.py:313
#, python-format
msgid "No source location defined!"
msgstr "Geen bronlocatie gedefinieerd!"

#. module: stock
#: selection:stock.config.settings,group_product_variant:0
msgid "No variants on products"
msgstr "Geen varianten op producten"

#. module: stock
#: model:stock.location,name:stock.stock_location_8
msgid "Non European Customers"
msgstr "Niet-europese afnemers"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_pack_operation_product_ids
msgid "Non pack"
msgstr "Geen verpakking"

#. module: stock
#: selection:stock.move,priority:0 selection:stock.picking,priority:0
msgid "Normal"
msgstr "Normaal"

#. module: stock
#: selection:stock.move,priority:0 selection:stock.picking,priority:0
msgid "Not urgent"
msgstr "Niet urgent"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_note
#: model:ir.model.fields,field_description:stock.field_stock_picking_note
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Notes"
msgstr "Notities"

#. module: stock
#: code:addons/stock/stock.py:1063
#, python-format
msgid "Nothing to check the availability for."
msgstr "Niets om de beschikbaarheid van te controleren."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Now, all your product quantities are correctly set."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_delay
msgid "Number of Days"
msgstr "Aantal dagen"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint_lead_days
msgid ""
"Number of days after the orderpoint is triggered to receive the products or "
"to order to the vendor"
msgstr ""
"Aantal dagen na het orderpunt is afgegaan om producten te ontvangen of een "
"order te maken bij de fabrikant"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_path_delay
msgid "Number of days needed to transfer the goods"
msgstr "Aantal dagen nodig om goederen te verplaatsen"

#. module: stock
#: code:addons/stock/stock.py:4803
#, python-format
msgid "OK"
msgstr "OK"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Odoo handles <strong>advanced push/pull routes configuration</strong>, for "
"example:"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Odoo has preconfigured <strong>one Warehouse</strong> for you."
msgstr "Odoo heeft één <strong>magazijn</strong> voor u geconfigureerd."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Odoo inventory management is fully integrated with sales and\n"
"                        invoicing process. Everything is automated from the "
"initial\n"
"                        quotation to the delivery and the final invoice."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Odoo is able to run advanced traceability by using Product Lots and Serial\n"
"                        Numbers, usually identified by bar codes stuck on "
"the products."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Of course, feel free to add your own. Please note that Odoo is able to "
"convert units within the same category, for example, liters to gallons in "
"the volume category"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "On Hand"
msgstr "Beschikbaar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "On Hand / Available Quantities"
msgstr "Beschikbaar / Beschikbare hoeveelheid"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_kanban_stock_view
msgid "On hand:"
msgstr "Aanwezige voorraad:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Once it's fully working, give us some feedback: we love to hear from our "
"customer. It would be great if you could send us a photo of your warehouse to"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:2939
#, python-format
msgid "One Lot/Serial Number"
msgstr "1 partijnummer"

#. module: stock
#: code:addons/stock/stock.py:2936
#, python-format
msgid "One owner only"
msgstr "1 eigenaar"

#. module: stock
#: code:addons/stock/stock.py:2937
#, python-format
msgid "One product for a specific owner"
msgstr "Een product voor een specifieke eigenaar"

#. module: stock
#: code:addons/stock/stock.py:2927
#, python-format
msgid "One product only"
msgstr "Één product"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_operation_link_operation_id
msgid "Operation"
msgstr "Handeling"

#. module: stock
#: code:addons/stock/stock.py:4559
#, python-format
msgid "Operation Details"
msgstr "Operatie details"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_path_name
msgid "Operation Name"
msgstr "Bewerkingsnaam"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_list
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_tree
#, fuzzy
msgid "Operation Types"
msgstr "Operatie soorten"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_warehouse_mgmt
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Operations"
msgstr "Bewerkingen"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_pickingtype
msgid "Operations Types"
msgstr "Operatie soorten"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_linked_move_operation_ids
msgid ""
"Operations that impact this move for the computation of the remaining "
"quantities"
msgstr ""
"Bewegingen beïnvloed door deze bewerking voor de berekening van de "
"resterende hoeveelheden"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_partner_id
msgid ""
"Optional address where goods are to be delivered, specifically used for "
"allotment"
msgstr "Optioneel adres waar de goederen moeten worden afgeleverd"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_posx
#: model:ir.model.fields,help:stock.field_stock_location_posy
#: model:ir.model.fields,help:stock.field_stock_location_posz
msgid "Optional localization details, for information purpose only"
msgstr "Optionele positie details, alleen voor informatieve redenen"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_returned_move_ids
msgid "Optional: all returned moves created from this move"
msgstr "Optioneel: Alle retour mutaties aangemaakt van deze mutatie"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_move_dest_id
msgid "Optional: next stock move when chaining them"
msgstr "Optioneel: volgende voorraadmutatie wanneer deze gekoppeld is"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_move_orig_ids
msgid "Optional: previous stock move when chaining them"
msgstr "Optioneel: vorige voorraadmutatie wanneer gekoppeld"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Order Date"
msgstr "Orderdatum"

#. module: stock
#: model:stock.location,name:stock.location_order
msgid "Order Processing"
msgstr "Order verwerking"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Orders processed Today or planned for Today"
msgstr "Orders verwerkt vandaag of geplant voor vandaag"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Origin"
msgstr "Bron"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_origin_returned_move_id
msgid "Origin return move"
msgstr "Originele retour mutatie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_move_orig_ids
msgid "Original Move"
msgstr "Originele mutatie"

#. module: stock
#: code:addons/stock/stock.py:577
#, python-format
msgid "Otherwise make sure the right stock/owner is set."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_out_type_id
msgid "Out Type"
msgstr "Uit type"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_outgoing_qty
#: model:ir.model.fields,field_description:stock.field_product_template_outgoing_qty
msgid "Outgoing"
msgstr "Uitgaand"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_delivery_steps
msgid "Outgoing Shippings"
msgstr "Uitgaande leveringen"

#. module: stock
#: code:addons/stock/stock.py:3823
#: model:stock.location,name:stock.stock_location_output
#, python-format
msgid "Output"
msgstr "Uitgaand"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_wh_output_stock_loc_id
msgid "Output Location"
msgstr "Leverlocatie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_partner_id
#: model:ir.model.fields,field_description:stock.field_stock_location_partner_id
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_owner_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_owner_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_owner_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_owner_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Owner"
msgstr "Eigenaar"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_restrict_partner_id
msgid "Owner "
msgstr "Eigenaar "

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_partner_id
msgid "Owner of the location if not internal"
msgstr "Eigenaar van de locatie, indien niet intern"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_pack_operation_owner_id
msgid "Owner of the quants"
msgstr "Eigenaar van de aantallen"

#. module: stock
#: code:addons/stock/product.py:313
#, python-format
msgid "P&L Qty"
msgstr "Winst&Verlies hoeveelheid"

#. module: stock
#: code:addons/stock/stock.py:3758
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_package_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_pack_operation_pack_ids
#, python-format
msgid "Pack"
msgstr "Verpakking"

#. module: stock
#: model:ir.actions.act_window,name:stock.pack_details
msgid "Pack Details"
msgstr "Verpakking details"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_pack_type_id
msgid "Pack Type"
msgstr "Type van verpakking"

#. module: stock
#: code:addons/stock/stock.py:4965
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_id
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_tree
#, python-format
msgid "Package"
msgstr "Verpakking"

#. module: stock
#: model:ir.actions.report.xml,name:stock.action_report_quant_package_barcode_small
msgid "Package BarCode"
msgstr "Barcode verpakking"

#. module: stock
#: model:ir.actions.report.xml,name:stock.action_report_quant_package_barcode
msgid "Package BarCode with Contents"
msgstr "Barcode verpakking met inhoud"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_complete_name
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
msgid "Package Name"
msgstr "Verpakkingsnaam"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_name
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Package Reference"
msgstr "Verpakking referentie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Package To Move"
msgstr "Verpakking te verplaatsen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Package Transfers"
msgstr "Verpakking overplaatsingen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_packaging_id
#, fuzzy
msgid "Package Type"
msgstr "Type van verpakking"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_packagings
#, fuzzy
msgid "Package Types"
msgstr "Type van verpakking"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_package_view
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_group_stock_tracking_lot
#: model:ir.ui.menu,name:stock.menu_package
#: model:ir.ui.menu,name:stock.menu_packages_config
#: model_terms:ir.ui.view,arch_db:stock.view_pack_operation_details_form
msgid "Packages"
msgstr "Verpakkingen"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_package_view
msgid ""
"Packages are usually created by pack operations made on transfers and can "
"contains several different products. You can then reuse a package to move "
"its whole content somewhere else, or to pack it into another bigger package. "
"A package can also be unpacked, allowing the disposal of its former content "
"as single units again."
msgstr ""
"Verpakkingen worden meestal aangemaakt door verpakking bewerkingen en kunnen "
"verschillende producten bevatten. U kunt dan de verpakking opnieuw gebruiken "
"om de gehele inhoud ergens anders heen te verplaatsen, of om het in te "
"pakken in een ander groter pakket. Een pakket kan ook worden uitgepakt, "
"waardoor de verkoop van de voormalige inhoud als afzonderlijke eenheden weer "
"mogelijk is.\n"
"            "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
msgid "Packaging"
msgstr "Verpakking"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_group_stock_packaging
msgid "Packaging Methods"
msgstr "Verpakkingsmethodes"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_wh_pack_stock_loc_id
msgid "Packing Location"
msgstr "Inpak locatie"

#. module: stock
#: model:ir.model,name:stock.model_stock_pack_operation
msgid "Packing Operation"
msgstr "Verpakking bewerking"

#. module: stock
#: code:addons/stock/stock.py:3824
#: model:stock.location,name:stock.location_pack_zone
#, python-format
msgid "Packing Zone"
msgstr "Verpakking zone"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Packs and Lots"
msgstr "Verpakkingen en partijen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_compute_wizard
msgid "Parameters"
msgstr "Parameters"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_location_id
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Parent Location"
msgstr "Hoofdlocatie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_parent_id
msgid "Parent Package"
msgstr "Bovenliggende verpakking"

#. module: stock
#: selection:stock.picking,move_type:0
msgid "Partial"
msgstr "Gedeeltelijk"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_partially_available
#: selection:stock.pack.operation,state:0 selection:stock.picking,state:0
msgid "Partially Available"
msgstr "Gedeeltelijk beschikbaar"

#. module: stock
#: model:ir.model,name:stock.model_res_partner
#: model:ir.model.fields,field_description:stock.field_procurement_group_partner_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_partner_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Partner"
msgstr "Relatie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_partner_address_id
msgid "Partner Address"
msgstr "Relatie adres"

#. module: stock
#: model:stock.location,name:stock.stock_location_locations_partner
msgid "Partner Locations"
msgstr "Relatie locaties"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_inventory_form
msgid ""
"Periodical Inventories are used to count the number of products\n"
"                available per location. You can use it once a year when you "
"do\n"
"                the general inventory or whenever you need it, to adapt the\n"
"                current inventory level of a product."
msgstr ""
"Periodieke voorraden worden gebruikt om het aantal van producten te tellen\n"
"dat beschikbaar is per locatie. U kan het eenmaal per jaar gebruiken wanneer "
"u de\n"
"stocktelling doet of wanneer u het nodig heeft, om de huidige\n"
"voorraadwaarde op een product te wijzigen."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Periodical Tasks"
msgstr "Periodieke taken"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_filter
msgid "Physical Inventories by Month"
msgstr "Fysieke tellingen per maand"

#. module: stock
#: model:stock.location,name:stock.stock_location_locations
msgid "Physical Locations"
msgstr "Fysieke locaties"

#. module: stock
#: model:ir.model,name:stock.model_stock_quant_package
msgid "Physical Packages"
msgstr "Fysieke verpakkkingen"

#. module: stock
#: code:addons/stock/stock.py:3770
#, python-format
msgid "Pick"
msgstr "Verzamelen"

#. module: stock
#: code:addons/stock/stock.py:3875
#, python-format
msgid "Pick + Pack + Ship"
msgstr "Verzamelen + inpakken + leveren"

#. module: stock
#: code:addons/stock/stock.py:3874
#, python-format
msgid "Pick + Ship"
msgstr "Verzamelen + leveren"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_pick_type_id
msgid "Pick Type"
msgstr "Verzameltype"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_pick_id
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_pick_id
msgid "Pick id"
msgstr "Lever id"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Picking"
msgstr "Verzamelen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Picking List"
msgstr "Verzamellijst"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Picking Lists"
msgstr "Verzamellijst"

#. module: stock
#: model:ir.actions.report.xml,name:stock.action_report_picking
msgid "Picking Operations"
msgstr "Lever operaties"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_picking_type_id
#: model:ir.model.fields,field_description:stock.field_stock_location_path_picking_type_id
#: model:ir.model.fields,field_description:stock.field_stock_move_picking_type_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_picking_type_id
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
msgid "Picking Type"
msgstr "Type levering"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_name
msgid "Picking Type Name"
msgstr "Verzameltype naam"

#. module: stock
#: model:ir.model.fields,help:stock.field_procurement_rule_picking_type_id
msgid ""
"Picking Type determines the way the picking should be shown in the view, "
"reports, ..."
msgstr ""
"De verzameltype bepaald de manier waarop de verzameling moet worden "
"weergegeven in de weergaven, rapporten, ..."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_return_picking_type_id
msgid "Picking Type for Returns"
msgstr "Verzameltype voor retouren"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_module_stock_picking_wave
msgid "Picking Waves"
msgstr "Picking waves"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Picking list"
msgstr "Verzamellijst"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.procurement_group_form_view_herited
msgid "Pickings"
msgstr "Leveringen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Pickings already processed"
msgstr "Verzamelopdracht reeds verwerkt"

#. module: stock
#: model:ir.actions.act_window,name:stock.do_view_pickings
msgid "Pickings for Groups"
msgstr "Verzamelingen voor groepen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Pickings that are late on scheduled time"
msgstr "Leveringen welke te laat zijn volgens planning"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_level_forecast_filter
msgid "Pivot"
msgstr "Pivot"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_make_procurement_date_planned
msgid "Planned Date"
msgstr "Datum gepland"

#. module: stock
#: model:ir.model,name:stock.model_web_planner
msgid "Planner"
msgstr "Planner"

#. module: stock
#: code:addons/stock/stock.py:1572
#, python-format
msgid ""
"Please create some Initial Demand or Mark as Todo and create some "
"Operations. "
msgstr ""
"Maak alsjeblieft een initiële aanvraag of markeer als To Do en maak "
"operaties aan."

#. module: stock
#: code:addons/stock/stock.py:1733
#, python-format
msgid "Please process some quantities to put in the pack first!"
msgstr "Verwerk aub eerst hoeveelheden om in de verpakking te steken!"

#. module: stock
#: code:addons/stock/stock.py:2774
#, python-format
msgid "Please provide a positive quantity to scrap."
msgstr "Geef een positieve hoeveelheid in om af te keuren."

#. module: stock
#: code:addons/stock/wizard/stock_return_picking.py:158
#, python-format
msgid "Please specify at least one non-zero quantity."
msgstr "Specificeer ten minste één waarde welke niet nul is."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Positive"
msgstr "Positief"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_make_procurement_route_ids
#: model:ir.model.fields,field_description:stock.field_procurement_order_route_ids
msgid "Preferred Routes"
msgstr "Voorkeur routes"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_route_ids
msgid "Preferred route to be followed by the procurement order"
msgstr "Voorkeursroute welke gevolgt dient te worden door de verwerving"

#. module: stock
#: model:ir.model.fields,help:stock.field_procurement_order_route_ids
msgid ""
"Preferred route to be followed by the procurement order. Usually copied from "
"the generating document (SO) but could be set up manually."
msgstr ""
"Voorkeursroute welke gevolgd dient te worden door de verwerving. Normaliter "
"gekopieerd van de verkooporder, maar kan ook handmatig zijn ingesteld."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Print"
msgstr "Afdrukken"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_printed
msgid "Printed"
msgstr "Afgedrukt"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat_sequence
#: model:ir.model.fields,field_description:stock.field_stock_move_priority
#: model:ir.model.fields,field_description:stock.field_stock_picking_priority
msgid "Priority"
msgstr "Prioriteit"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_priority
msgid ""
"Priority for this picking. Setting manually a value here would set it as "
"priority for all the moves"
msgstr ""
"Prioriteit voor deze levering. Het hier instellen van een waarde zal de "
"prioriteit voor alle mutaties instellen."

#. module: stock
#: model:ir.model,name:stock.model_procurement_order
#: model:ir.model.fields,field_description:stock.field_stock_move_procurement_id
#: selection:stock.location,usage:0
msgid "Procurement"
msgstr "Verwerving"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_procurement_action
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Procurement Exceptions"
msgstr "Verwervingsfouten"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_group_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_group_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_group_id
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Procurement Group"
msgstr "Verwervingsgroep"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_order_location_id
#: model:ir.model.fields,field_description:stock.field_procurement_rule_location_id
#: model:ir.model.fields,field_description:stock.field_product_product_property_stock_procurement
#: model:ir.model.fields,field_description:stock.field_product_template_property_stock_procurement
msgid "Procurement Location"
msgstr "Leveringslocatie"

#. module: stock
#: model:ir.actions.act_window,name:stock.act_make_procurement
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_product_view_form_easy_inherit_stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.view_make_procurment_wizard
msgid "Procurement Request"
msgstr "Verwervingsverzoek"

#. module: stock
#: model:ir.model,name:stock.model_procurement_group
msgid "Procurement Requisition"
msgstr "Verwervingsaanvraag"

#. module: stock
#: model:ir.model,name:stock.model_procurement_rule
#: model:ir.model.fields,field_description:stock.field_stock_move_rule_id
msgid "Procurement Rule"
msgstr "Verwerving regel"

#. module: stock
#: model:ir.actions.act_window,name:stock.procrules
#: model:ir.model.fields,field_description:stock.field_stock_location_route_pull_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Procurement Rules"
msgstr "Verwerving regels"

#. module: stock
#: model:ir.actions.act_window,name:stock.do_view_procurement_op
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_module_procurement_jit
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
#: model:stock.location,name:stock.location_procurement
msgid "Procurements"
msgstr "Verwervingen"

#. module: stock
#: code:addons/stock/product.py:325
#, python-format
msgid "Produced Qty"
msgstr "Geproduceerd aantal"

#. module: stock
#: model:ir.model,name:stock.model_product_product
#: model:ir.model.fields,field_description:stock.field_make_procurement_product_id
#: model:ir.model.fields,field_description:stock.field_report_stock_forecast_product_id
#: model:ir.model.fields,field_description:stock.field_report_stock_forecast_product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_report_stock_lines_date_product_id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_product_id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_product_id
#: model:ir.model.fields,field_description:stock.field_stock_move_product_id
#: model:ir.model.fields,field_description:stock.field_stock_move_scrap_product_id
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_product_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_product_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot_product_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_product_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line_product_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_product_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_lot_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Product"
msgstr "Product"

#. module: stock
#: code:addons/stock/stock.py:254
#: model:ir.model.fields,field_description:stock.field_stock_location_route_categ_ids
#: model:ir.ui.menu,name:stock.menu_product_category_config_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#, python-format
msgid "Product Categories"
msgstr "Productcategorieën"

#. module: stock
#: model:ir.model,name:stock.model_product_category
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat_category_id
msgid "Product Category"
msgstr "Productcategorie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_product_code
msgid "Product Code"
msgstr "Productcode"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
msgid "Product Lots"
msgstr "Productie partijen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
msgid "Product Lots Filter"
msgstr "Productie partij filter"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_product_name
msgid "Product Name"
msgstr "Productnaam"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_group_stock_tracking_owner
msgid "Product Owners"
msgstr "Producteigenaars"

#. module: stock
#: model:ir.model,name:stock.model_product_template
#: model:ir.model.fields,field_description:stock.field_stock_move_product_tmpl_id
msgid "Product Template"
msgstr "Productsjabloon"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Product Types"
msgstr "Productsoorten"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_move_scrap_product_uom
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_product_uom
msgid "Product Unit of Measure"
msgstr "Maateenheid product"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_group_product_variant
msgid "Product Variants"
msgstr "Product varianten"

#. module: stock
#: model:stock.location,name:stock.location_production
#: selection:stock.location,usage:0
msgid "Production"
msgstr "Productie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_property_stock_production
#: model:ir.model.fields,field_description:stock.field_product_template_property_stock_production
msgid "Production Location"
msgstr "Productielocatie"

#. module: stock
#: code:addons/stock/stock.py:244
#: model:ir.actions.act_window,name:stock.act_product_location_open
#: model:ir.model.fields,field_description:stock.field_stock_location_route_product_ids
#: model:ir.ui.menu,name:stock.menu_product_in_config_stock
#: model:ir.ui.menu,name:stock.menu_product_variant_config_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#: model_terms:ir.ui.view,arch_db:stock.view_pack_operation_details_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_config_settings
#, python-format
msgid "Products"
msgstr "Producten"

#. module: stock
#: selection:stock.config.settings,group_product_variant:0
msgid ""
"Products can have several attributes, defining variants (Example: size, "
"color,...)"
msgstr ""
"Producten kunnen meerdere attributen hebben die als varianten worden "
"gedefinieerd (Voorbeelden zijn: maat, kleur,...)"

#. module: stock
#: selection:stock.config.settings,group_uom:0
msgid "Products have only one unit of measure (easier)"
msgstr "Producten hebben slechts één maateenheid (eenvoudiger)"

#. module: stock
#: code:addons/stock/product.py:43
#, python-format
msgid "Products: "
msgstr "Producten: "

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_propagate
#: model:ir.model.fields,field_description:stock.field_stock_location_path_propagate
#: model:ir.model.fields,field_description:stock.field_stock_move_propagate
msgid "Propagate cancel and split"
msgstr "Annuleren en splitsen doorgeven"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Purchase Flow"
msgstr "Inkoop flow"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_push_rule_id
msgid "Push Rule"
msgstr "Push regel"

#. module: stock
#: model:ir.actions.act_window,name:stock.stolocpath
#: model:ir.model.fields,field_description:stock.field_stock_location_route_push_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Push Rules"
msgstr "Push regels"

#. module: stock
#: model:ir.model,name:stock.model_stock_location_path
msgid "Pushed Flows"
msgstr "'Pushed' pad"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat_putaway_id
msgid "Put Away Method"
msgstr "Wegzet methode"

#. module: stock
#: model:ir.model,name:stock.model_product_putaway
#: model:ir.model.fields,field_description:stock.field_stock_location_putaway_strategy_id
msgid "Put Away Strategy"
msgstr "Wegzet strategie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway
msgid "Putaway"
msgstr "Wegzetten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "Qty"
msgstr "Hvhd"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_qty_multiple
msgid "Qty Multiple"
msgstr "Hvh veelvoud"

#. module: stock
#: sql_constraint:stock.warehouse.orderpoint:0
msgid "Qty Multiple must be greater than or equal to zero."
msgstr "Veelvoud moet groter of gelijk dan nul zijn."

#. module: stock
#: code:addons/stock/stock.py:3822
#, python-format
msgid "Quality Control"
msgstr "Kwaliteitscontrole"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_wh_qc_stock_loc_id
msgid "Quality Control Location"
msgstr "Kwaliteitscontrole locatie"

#. module: stock
#: code:addons/stock/stock.py:2146
#, python-format
msgid ""
"Quantities, Units of Measure, Products and Locations cannot be modified on "
"stock moves that have already been processed (except by the Administrator)."
msgstr ""
"Hoeveelheden, Maateenheden, Producten en Locaties kunnen niet worden "
"gewijzigd op voorraadmutaties  die al zijn verwerkt (behalve door de "
"administrator)."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_make_procurement_qty
#: model:ir.model.fields,field_description:stock.field_report_stock_forecast_quantity
#: model:ir.model.fields,field_description:stock.field_stock_move_operation_link_qty
#: model:ir.model.fields,field_description:stock.field_stock_move_product_qty
#: model:ir.model.fields,field_description:stock.field_stock_move_product_uom_qty
#: model:ir.model.fields,field_description:stock.field_stock_move_scrap_product_qty
#: model:ir.model.fields,field_description:stock.field_stock_quant_qty
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line_quantity
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "Quantity"
msgstr "Hoeveelheid"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "Quantity Multiple"
msgstr "Veelvoud"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_qty_available
#: model:ir.model.fields,field_description:stock.field_product_template_qty_available
msgid "Quantity On Hand"
msgstr "Aanwezige voorraad"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_reserved_availability
msgid "Quantity Reserved"
msgstr "Aantal gereserveerd"

#. module: stock
#: code:addons/stock/wizard/stock_change_product_qty.py:90
#, python-format
msgid "Quantity cannot be negative."
msgstr "Hoeveelheid mag niet negatief zijn."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_availability
msgid "Quantity in stock that can still be reserved for this move"
msgstr ""
"Hoeveelheid op voorraad dat nog steeds kan worden gereserveerd voor deze "
"mutatie"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_product_qty
msgid "Quantity in the default UoM of the product"
msgstr "Hoeveelheid in de standaard maateenheid van het product"

#. module: stock
#: sql_constraint:stock.pack.operation.lot:0
msgid "Quantity must be greater than or equal to 0.0!"
msgstr "Hoeveelheid moet groter of gelijk aan 0.0 zijn!"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_qty
msgid ""
"Quantity of products in this quant, in the default unit of measure of the "
"product"
msgstr "Hoeveelheid van het product in de standaard maateenheid"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product_incoming_qty
msgid ""
"Quantity of products that are planned to arrive.\n"
"In a context with a single Stock Location, this includes goods arriving to "
"this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods arriving to the "
"Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods arriving to any Stock Location with "
"'internal' type."
msgstr ""
"Hoeveelheid producten die gepland zijn binnen te komen. \n"
"Bij gebruik van een enkele voorraadlocatie, omvat dit de goederen die op "
"deze locatie aankomen, of één van de onderliggende locaties. \n"
"Bij gebruik van een enkel magazijn, omvat dit de goederen die bij de "
"voorraadlocatie van dit magazijn aankomen, of één van de onderliggende "
"locaties. \n"
"Anders, dit omvat goederen die aan aankomen op alle voorraadlocaties van het "
"type 'intern'."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product_outgoing_qty
msgid ""
"Quantity of products that are planned to leave.\n"
"In a context with a single Stock Location, this includes goods leaving this "
"Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods leaving the Stock "
"Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods leaving any Stock Location with 'internal' "
"type."
msgstr ""
"Hoeveelheid producten die gepland zijn om te vertrekken. \n"
"Bij gebruik van een enkele voorraadlocatie, omvat dit de goederen die van "
"deze locatie vertrekken, of één van de onderliggende locaties. \n"
"Bij gebruik van een enkel magazijn, omvat dit de goederen die van de "
"voorraadlocatie van dit magazijn vertrekken, of één van de onderliggende "
"locaties. \n"
"Anders, dit omvat goederen die vertrekken op alle voorraadlocaties van het "
"type 'intern'."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_operation_link_qty
msgid ""
"Quantity of products to consider when talking about the contribution of this "
"pack operation towards the remaining quantity of the move (and inverse). "
"Given in the product main uom."
msgstr ""
"Hoeveelheid producten om te overwegen wanneer het gaat over de bijdrage van "
"deze verpakking bewerking naar de resterende hoeveelheid van de mutatie (en "
"omgekeerd). Gegeven in het product belangrijkste maateenheid."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_reserved_availability
msgid "Quantity that has already been reserved for this move"
msgstr "Hoeveelheid dat al is gereserveerd voor deze mutatie"

#. module: stock
#: model:ir.model,name:stock.model_stock_quant
#: model:ir.model.fields,field_description:stock.field_stock_production_lot_quant_ids
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Quants"
msgstr "Aantallen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Ready"
msgstr "Gereed"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Real Quantity"
msgstr "Echte hoeveelheid"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_reception_count
#: model_terms:ir.ui.view,arch_db:stock.product_kanban_stock_view
msgid "Receipt"
msgstr "Kassabon"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_reception_route_id
msgid "Receipt Route"
msgstr "Ontvangst route"

#. module: stock
#: code:addons/stock/stock.py:3869
#, python-format
msgid "Receipt in 1 step"
msgstr "Ontvang in 1 stap"

#. module: stock
#: code:addons/stock/stock.py:3870
#, python-format
msgid "Receipt in 2 steps"
msgstr "Ontvang in 2 stappen"

#. module: stock
#: code:addons/stock/stock.py:3871
#, python-format
msgid "Receipt in 3 steps"
msgstr "Ontvang in 3 stappen"

#. module: stock
#: code:addons/stock/stock.py:3722
#: model:ir.actions.act_window,name:stock.action_receive_move
#: model_terms:ir.ui.view,arch_db:stock.product_kanban_stock_view
#: model:stock.picking.type,name:stock.chi_picking_type_in
#: model:stock.picking.type,name:stock.picking_type_in
#, python-format
msgid "Receipts"
msgstr "Ontvangsten"

#. module: stock
#: selection:stock.warehouse,reception_steps:0
msgid "Receive goods directly in stock (1 step)"
msgstr "Ontvang goederen direct in de voorraad (1 stap)"

#. module: stock
#: code:addons/stock/product.py:297
#, python-format
msgid "Received Qty"
msgstr "Ontvangen aantal"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Recompute"
msgstr "Herbereken"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_recompute_pack_op
msgid "Recompute pack operation?"
msgstr "Herbereken verpakking bewerkingen"

#. module: stock
#: selection:stock.config.settings,group_stock_tracking_lot:0
msgid "Record packages used on packing: pallets, boxes, ..."
msgstr "Neem pakketten op gebruikt op de verpakking: palletten, dozen, ..."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_name
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking_board
msgid "Reference"
msgstr "Referentie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_sequence_id
msgid "Reference Sequence"
msgstr "Referentie reeks"

#. module: stock
#: sql_constraint:stock.picking:0
msgid "Reference must be unique per company!"
msgstr "Referentie moet uniek zijn per bedrijf!"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_origin
msgid "Reference of the document"
msgstr "Referentie van het document"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_pack_operation_ids
msgid "Related Packing Operations"
msgstr "Bijbehorende verpakking bewerkingen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_remaining_qty
msgid "Remaining Qty"
msgstr "Overgebleven hoeveelheid"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_remaining_qty
msgid "Remaining Quantity"
msgstr "Resterende hoeveelheid"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_remaining_qty
msgid ""
"Remaining Quantity in default UoM according to operations matched with this "
"move"
msgstr ""
"Resterende hoeveelheid is de standaard maateenheid, volgens de bewerkingen, "
"overeenkomstig deze mutatie."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Remaining parts of picking partially processed"
msgstr "Resterende deel van de leveringen zijn gedeeltelijk uitgevoerd"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_pack_operation_remaining_qty
msgid ""
"Remaining quantity in default UoM according to moves matched with this "
"operation. "
msgstr ""
"Resterende hoeveelheid is de standaard maateenheid, volgens de mutaties, "
"overeenkomstig deze bewerkingen. "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_removal
msgid "Removal"
msgstr "Verwijdering"

#. module: stock
#: model:ir.model,name:stock.model_product_removal
#: model:ir.model.fields,field_description:stock.field_stock_location_removal_strategy_id
msgid "Removal Strategy"
msgstr "Verwijder strategie"

#. module: stock
#: code:addons/stock/stock.py:533
#, python-format
msgid "Removal strategy %s not implemented."
msgstr "Uitgaande voorraadmethode %s is niet geïmplementeerd"

#. module: stock
#: model:ir.actions.act_window,name:stock.act_stock_warehouse_2_stock_warehouse_orderpoint
#: model:ir.actions.act_window,name:stock.action_orderpoint_form
#: model:ir.actions.act_window,name:stock.product_open_orderpoint
#: model:ir.model.fields,field_description:stock.field_product_product_nbr_reordering_rules
#: model:ir.model.fields,field_description:stock.field_product_template_nbr_reordering_rules
#: model:ir.ui.menu,name:stock.menu_reordering_rules_config
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Reordering Rules"
msgstr "Aanvulopdracht regels"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Reordering Rules Search"
msgstr "Aanvulopdrachtregels zoeken"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_warehouse_report
msgid "Reports"
msgstr "Rapportages"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_make_procurement_res_model
msgid "Res Model"
msgstr "Res Model"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Reserve"
msgstr "Reserveer"

#. module: stock
#: selection:stock.config.settings,module_procurement_jit:0
msgid "Reserve products immediately after the sale order confirmation"
msgstr ""

#. module: stock
#: selection:stock.config.settings,module_procurement_jit:0
msgid "Reserve products manually or based on automatic scheduler"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_form
msgid "Reserved"
msgstr "Gereserveerd"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_operation_link_reserved_quant_id
msgid "Reserved Quant"
msgstr "Gereserveerde hoeveelheid"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Reserved Quants"
msgstr "Gereserveerde hoeveelheden"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_reservation_id
msgid "Reserved for Move"
msgstr "Gereserveerd voor mutatie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_reserved_quant_ids
msgid "Reserved quants"
msgstr "Gereserveerde hoeveelheden"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Reset Operations"
msgstr "Operaties terugzetten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Resolve Procurement Exceptions"
msgstr "Los verwervingsfouten op"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_resupply_from_wh
msgid "Resupply From Other Warehouses"
msgstr "Aangevuld vanuit ander magazijn"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_resupply_route_ids
msgid "Resupply Routes"
msgstr "Aanvul routes"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_resupply_wh_ids
msgid "Resupply Warehouses"
msgstr "Opnieuw bevoorraden magazijn"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
msgid "Return"
msgstr "Retourneer"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_location_id
msgid "Return Location"
msgstr "Retourneer locatie"

#. module: stock
#: model:ir.model,name:stock.model_stock_return_picking
msgid "Return Picking"
msgstr "Retour boeken"

#. module: stock
#: code:addons/stock/wizard/stock_return_picking.py:181
#, python-format
msgid "Returned Picking"
msgstr "Retouren"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Reverse"
msgstr "Omkeren"

#. module: stock
#: model:ir.actions.act_window,name:stock.act_stock_return_picking
msgid "Reverse Transfer"
msgstr "Retour zending"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_parent_right
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_parent_right
msgid "Right Parent"
msgstr "Bovenliggende"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_route_id
#: model:ir.model.fields,field_description:stock.field_stock_location_path_route_id
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Route"
msgstr "Route"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route_name
msgid "Route Name"
msgstr "Routenaam"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_route_sequence
#: model:ir.model.fields,field_description:stock.field_stock_location_path_route_sequence
msgid "Route Sequence"
msgstr "Route reeks"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_routes_form
#: model:ir.model.fields,field_description:stock.field_product_category_route_ids
#: model:ir.model.fields,field_description:stock.field_product_product_route_ids
#: model:ir.model.fields,field_description:stock.field_product_template_route_ids
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_group_stock_adv_location
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_route_ids
#: model:ir.ui.menu,name:stock.menu_routes_config
#: model:ir.ui.menu,name:stock.menu_stock_routes
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_tree
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Routes"
msgstr "Routes"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Routes Management"
msgstr "Routebeheer"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_resupply_route_ids
msgid ""
"Routes will be created for these resupply warehouses and you can select them "
"on products and product categories"
msgstr ""
"Routes worden aangemaakt voor deze aanvul magazijnen en u kunt deze "
"selecteren op producten en product categorieën"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "Rules"
msgstr "Rechten"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_procurement_compute
#: model:ir.ui.menu,name:stock.menu_procurement_compute
msgid "Run Reordering Rules"
msgstr "Aanvulopdracht regels uitvoeren"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_proc_schedulers
msgid "Run Schedulers"
msgstr "Starten planners"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Sales Flow"
msgstr "Verkoop flow"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_pack_operation_details_form_save
#: model_terms:ir.ui.view,arch_db:stock.view_pack_operation_lot_form
msgid "Save"
msgstr "Opslaan"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Scheduled"
msgstr "Gepland"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_min_date
msgid "Scheduled Date"
msgstr "Geplande datum"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_date_expected
msgid "Scheduled date for the processing of this move"
msgstr "Geplande datum voor het verwerken van deze mutatie"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_min_date
msgid ""
"Scheduled time for the first part of the shipment to be processed. Setting "
"manually a value here would set it as expected date for all the stock moves."
msgstr ""
"De geplande tijd wanneer het eerste deel van levering wordt verwerkt. Het "
"hier instellen van een handmatige waarde zal deze datum instellen als "
"verwachte datum voor alle voorraadmutaties."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_max_date
msgid "Scheduled time for the last part of the shipment to be processed"
msgstr "Geplande verwerkingstijd voor het laatste deel van de levering"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_sched
msgid "Schedulers"
msgstr "Planners"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_scrap_wizard
msgid "Scrap"
msgstr "Afgekeurd"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_scrap_wizard
msgid "Scrap Location"
msgstr "Afkeurlocatie"

#. module: stock
#: model:ir.actions.act_window,name:stock.move_scrap
msgid "Scrap Move"
msgstr "Afkeur mutatie"

#. module: stock
#: model:ir.model,name:stock.model_stock_move_scrap
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_scrap_wizard
msgid "Scrap Products"
msgstr "Producten afkeuren"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_scrapped
#: model:stock.location,name:stock.stock_location_scrapped
msgid "Scrapped"
msgstr "Afgekeurd"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_filter
msgid "Search Inventory"
msgstr "Zoek voorraadtelling"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_path_filter
msgid "Search Stock Location Paths"
msgstr "Zoek voorraadlocatie paden"

#. module: stock
#: code:addons/stock/stock.py:2927
#, python-format
msgid "Select products manually"
msgstr "Selecteer manueel producten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Select the places where this route can be selected"
msgstr "Selecteer de plaatsen waar deze route kan worden geselecteerd"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_path_sequence
#: model:ir.model.fields,field_description:stock.field_stock_location_route_sequence
#: model:ir.model.fields,field_description:stock.field_stock_move_sequence
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_sequence
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Sequence"
msgstr "Reeks"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_lot_id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_prod_lot_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot_name
#: model:res.request.link,name:stock.req_link_tracking
msgid "Serial Number"
msgstr "Partijnummer"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_tree
msgid "Serial Number / Lots"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_prodlot_name
msgid "Serial Number Name"
msgstr "Naam partijnummer"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_production_lot_form
#: model:ir.ui.menu,name:stock.menu_action_production_lot_form
msgid "Serial Numbers / Lots"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_warehouse_id
msgid "Served Warehouse"
msgstr "Magazijn"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_category_removal_strategy_id
msgid ""
"Set a specific removal strategy that will be used regardless of the source "
"location for this product category"
msgstr ""
"Stel een specifieke uitgaande voorraadmethode is, welke wordt gebruikt "
"ongeacht de ingestelde voorraadlocatie voor deze productcategorie"

#. module: stock
#: selection:stock.config.settings,module_stock_calendar:0
msgid "Set lead times in calendar days (easy)"
msgstr "Stel doorlooptijd in in kalenderdagen (gemakkelijk)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Set the <i>Real Quantity</i> for each Product and <i>Validate the Inventory</"
"i>"
msgstr ""
"Zet de <i>Werkelijke hoeveelheid</i> voor elk product en <i>valideer de "
"voorraad</i>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Set the products you want to track with lots or serial numbers by setting "
"the Tracking field on the product form"
msgstr ""
"Stel de producten in die u wilt volgen met partij of serienummers door het "
"traceer veld in te stellen op het productformulier"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Set to Draft"
msgstr "Zet op concept"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_location_id
msgid ""
"Sets a location if you produce at a fixed location. This can be a partner "
"location if you subcontract the manufacturing operations."
msgstr ""
"Stel een locatie in als u produceert op een vaste locatie. Dit kan een "
"relatie locatie zijn als u de productie heeft uitbesteed."

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_general_settings
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Settings"
msgstr "Instellingen"

#. module: stock
#: model:stock.location,name:stock.stock_location_components
msgid "Shelf 1"
msgstr "Schap 1"

#. module: stock
#: model:stock.location,name:stock.stock_location_14
msgid "Shelf 2"
msgstr "Schap 2"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_posy
msgid "Shelves (Y)"
msgstr "Schap (Y)"

#. module: stock
#: code:addons/stock/stock.py:3873
#, python-format
msgid "Ship Only"
msgstr "Alleen leveren"

#. module: stock
#: selection:stock.warehouse,delivery_steps:0
msgid "Ship directly from stock (Ship only)"
msgstr "Lever direct vanuit voorraad (alleen leveren)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_config_settings
msgid "Shipping Connectors"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_code
msgid "Short Name"
msgstr "Korte naam"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_code
msgid "Short name used to identify your warehouse"
msgstr "Korte naam om uw magazijn te identificeren"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_string_availability_info
msgid "Show various information on stock availability for this move"
msgstr ""
"Geef diverse informatie over de beschikbaarheid van de voorraad voor deze "
"mutatie weer"

#. module: stock
#: model:stock.location,name:stock.location_refrigerator_small
msgid "Small Refrigerator"
msgstr "Kleine koelkast"

#. module: stock
#: selection:stock.config.settings,group_uom:0
#, fuzzy
msgid ""
"Some products may be sold/purchased in different units of measure (advanced)"
msgstr ""
"Sommige producten kunnen verkocht/aangekocht worden met verschillende "
"maateenheden (geavanceerd)"

#. module: stock
#: code:addons/stock/stock.py:1580
#, python-format
msgid "Some products require lots, so you need to specify those first!"
msgstr ""
"Sommige producten moeten partijen gebruiken, u moet deze eerste specifiëren!"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Source"
msgstr "Bron"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_origin
#: model:ir.model.fields,field_description:stock.field_stock_picking_origin
msgid "Source Document"
msgstr "Bron document"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_location_src_id
#: model:ir.model.fields,field_description:stock.field_stock_location_path_location_from_id
#: model:ir.model.fields,field_description:stock.field_stock_move_location_id
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_location_id
msgid "Source Location"
msgstr "Bronlocatie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_location_id
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form
msgid "Source Location Zone"
msgstr "Bron locatie zone"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_package_id
msgid "Source Package"
msgstr "Bron verpakking"

#. module: stock
#: model:ir.model.fields,help:stock.field_procurement_rule_location_src_id
msgid "Source location is action=move"
msgstr "Bron locatie is actie=mutatie"

#. module: stock
#: model:ir.model,name:stock.model_stock_pack_operation_lot
msgid "Specifies lot/serial number for pack operations that need it"
msgstr ""
"Specifieer partijen/serienummers voor verpakkingsbewerkingen die het nodig "
"hebben"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory_lot_id
msgid ""
"Specify Lot/Serial Number to focus your inventory on a particular Lot/Serial "
"Number."
msgstr ""
"Specificeer een partijnummer om de voorraad te bekijken voor een specifiek "
"partijnummer."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory_partner_id
msgid "Specify Owner to focus your inventory on a particular Owner."
msgstr ""
"Specificeer de eigenaar om de voorraad te bekijken voor een specifieke "
"eigenaar."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory_package_id
msgid "Specify Pack to focus your inventory on a particular Pack."
msgstr ""
"Specificeer een verpakking om de voorraad te bekijken voor een specifieke "
"verpakking."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory_product_id
msgid "Specify Product to focus your inventory on a particular Product."
msgstr ""
"Specificeer een product om de voorraad te bekijken voor een specifieke "
"product."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_pack_operation_details_form_save
msgid "Split"
msgstr "Splitsen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Start Inventory"
msgstr "Start voorraadtelling"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_state
#: model:ir.model.fields,field_description:stock.field_stock_inventory_state
#: model:ir.model.fields,field_description:stock.field_stock_move_state
#: model:ir.model.fields,field_description:stock.field_stock_picking_state
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_filter
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Status"
msgstr "Status"

#. module: stock
#: code:addons/stock/stock.py:3820
#: model:stock.location,name:stock.stock_location_shop0
#: model:stock.location,name:stock.stock_location_stock
#, python-format
msgid "Stock"
msgstr "Voorraad"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "Stock Inventory"
msgstr "Voorraadtelling"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_line_tree
msgid "Stock Inventory Lines"
msgstr "Stock Inventory Lines"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_level_forecast_report_product
#: model:ir.actions.act_window,name:stock.action_stock_level_forecast_report_template
msgid "Stock Level Forecast"
msgstr "Voorraadniveau prognose"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_level_forecast_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_level_forecast_graph
#: model_terms:ir.ui.view,arch_db:stock.view_stock_level_forecast_pivot
msgid "Stock Level forecast"
msgstr "Voorraadniveau prognose"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#: model_terms:ir.ui.view,arch_db:stock.view_location_tree2
msgid "Stock Location"
msgstr "Voorraadlocatie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Stock Locations"
msgstr "Voorraadlocaties"

#. module: stock
#: model:ir.model,name:stock.model_stock_move
msgid "Stock Move"
msgstr "Voorraadmutatie"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_move_form2
#: model:ir.model.fields,field_description:stock.field_stock_picking_move_lines
#: model:ir.ui.menu,name:stock.menu_action_move_form2
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "Stock Moves"
msgstr "Voorraadmutaties"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_graph
#: model_terms:ir.ui.view,arch_db:stock.view_move_pivot
msgid "Stock Moves Analysis"
msgstr "Voorraadmutatie analyse"

#. module: stock
#: model:ir.actions.act_window,name:stock.product_open_quants
#: model:ir.actions.act_window,name:stock.product_template_open_quants
msgid "Stock On Hand"
msgstr " Beschikbare voorraad"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_all
msgid "Stock Operations"
msgstr "Voorraad bewerkingen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_picking_id
msgid "Stock Picking"
msgstr "Stock Picking"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that are Available (Ready to process)"
msgstr "Voorraadmutaties welke beschikbaar zijn (gereed voor verwerking)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that are Confirmed, Available or Waiting"
msgstr "Voorraadmutaties welke zijn bevestigd, beschikbaar of wachtend."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that have been processed"
msgstr "Voorraadmutaties welke zijn verwerkt"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_lines_date_search
msgid "Stockable"
msgstr "Voorraad"

#. module: stock
#: code:addons/stock/product.py:400
#, python-format
msgid "Stockable Product"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route_supplied_wh_id
msgid "Supplied Warehouse"
msgstr "Geleverd magazijn"

#. module: stock
#: selection:stock.config.settings,module_stock_dropshipping:0
msgid "Suppliers always deliver to your warehouse(s)"
msgstr "Leveranciers leveren altijd aan uw magazijn(en)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Supply Chain"
msgstr "Bevoorrading"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_procure_method
msgid "Supply Method"
msgstr "Bevoorradingsmethode"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route_supplier_wh_id
msgid "Supplying Warehouse"
msgstr "Leverend magazijn"

#. module: stock
#: selection:procurement.rule,procure_method:0
msgid "Take From Stock"
msgstr "Neem van voorraad"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Technical Information"
msgstr "Technische informatie"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_operation_link_reserved_quant_id
msgid ""
"Technical field containing the quant that created this link between an "
"operation and a stock move. Used at the stock_move_obj.action_done() time to "
"avoid seeking a matching quant again"
msgstr ""
"Technisch veld dat de hoeveelheid bevat dat deze koppeling tussen de "
"bewerking en de voorraadmutatie heeft veroorzaakt. Gebruikt voor de "
"stock_move_obj.action_done() tijd om extra zoeken te voorkomen."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_warehouse_id
msgid ""
"Technical field depicting the warehouse to consider for the route selection "
"on the next procurement (if any)."
msgstr ""
"Technisch veld welke het magazijn voorstelt als keuze van de route selectie "
"voor de volgende verwerving (indien van toepassing)."

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company_internal_transit_location_id
msgid ""
"Technical field used for resupply routes between warehouses that belong to "
"this company"
msgstr ""
"Technisch veld welke wordt gebruikt voor herbevooradingsroutes tussen "
"magazijnen, welke behoren bij dit bedrijf"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_restrict_lot_id
msgid ""
"Technical field used to depict a restriction on the lot of quants to "
"consider when marking this move as 'done'"
msgstr ""
"Technisch veld welke een beperking weergeeft op de partij van de hoeveelheid "
"wanneer deze mutatie wordt gemarkeerd als 'verwerkt'"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_restrict_partner_id
msgid ""
"Technical field used to depict a restriction on the ownership of quants to "
"consider when marking this move as 'done'"
msgstr ""
"Technisch veld welke een beperking weergeeft op de eigenaar van de "
"hoeveelheid wanneer deze mutatie wordt gemarkeerd als 'verwerkt'"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_return_picking_move_dest_exists
msgid "Technical field used to hide help tooltip if not needed"
msgstr ""
"Technisch veld welke wordt gebruikt om de tooltip te verbergen indien deze "
"niet wordt gebruikt"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_negative_dest_location_id
msgid ""
"Technical field used to record the destination location of a move that "
"created a negative quant"
msgstr ""
"Technisch veld welke wordt gebruikt om de bestemminglocatie vast te leggen "
"bij een mutatie welke een negatieve hoeveelheid veroorzaakt"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_price_unit
msgid ""
"Technical field used to record the product cost set by the user during a "
"picking confirmation (when costing method used is 'average price' or "
"'real'). Value given in company currency and in product uom."
msgstr ""
"Technisch veld om de product kostprijs op te slaan, zoals ingesteld bij de "
"gebruiker, bij het bevestigen van de levering (wanneer de kostprijs methode "
"is ingesteld op 'gemiddelde prijs' of 'werkelijke prijs'). De waarde wordt "
"weergegeven in de valuta van het bedrijf en de maateenheid van het product."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_split_from
msgid ""
"Technical field used to track the origin of a split move, which can be "
"useful in case of debug"
msgstr ""
"Technisch veld welke wordt gebruikt om de originele regel te vinden van een "
"gesplitste regel. Dit kan handig zijn  bij het zoeken naar een fout."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_module_delivery_temando
#, fuzzy
msgid "Temando integration"
msgstr "Soort bewerking"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_make_procurement_product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_product_tmpl_id
msgid "Template"
msgstr "Sjabloon"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_path_auto
msgid ""
"The 'Automatic Move' / 'Manual Operation' value will create a stock move "
"after the current one.  With 'Automatic No Step Added', the location is "
"replaced in the original move."
msgstr ""
"De 'Automatische verwerking' / 'Manuele operatie' waarde maakt een "
"stockmutaties na de huidige. Met 'Automatisch geen stap toegevoegd' wordt de "
"locatie vervangen met de originele mutatie."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "The RFQ becomes a Purchase Order and a Transfer Order is created"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product_sale_delay
#: model:ir.model.fields,help:stock.field_product_template_sale_delay
msgid ""
"The average delay in days between the confirmation of the customer order and "
"the delivery of the finished products. It's the time you promise to your "
"customers."
msgstr ""
"De gemiddelde vertraging in dagen tussen de bevestiging door de klant en de "
"levering van de producten. Dit is de levertijd die u aan uw klanten beloofd."

#. module: stock
#: sql_constraint:stock.location:0
msgid "The barcode for a location must be unique per company !"
msgstr "De barcode voor een locatie moet per bedrijf uniek zijn!"

#. module: stock
#: code:addons/stock/stock.py:4383
#, python-format
msgid ""
"The chosen quantity for product %s is not compatible with the UoM rounding. "
"It will be automatically converted at confirmation"
msgstr ""
"De gekozen hoeveelheid voor het product %s is niet compatibel met de "
"maateenheid afronding. De hoeveelheid zal automatisch geconverteerd worden "
"bij bevestiging."

#. module: stock
#: sql_constraint:stock.warehouse:0
msgid "The code of the warehouse must be unique per company!"
msgstr "De code van het magazijn moet uniek zijn per bedrijf!"

#. module: stock
#: sql_constraint:stock.production.lot:0
msgid "The combination of serial number and product must be unique !"
msgstr "De combinatie van serienummer en product moet uniek zijn!"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_company_id
msgid "The company is automatically set from your user preferences."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_company_id
msgid "The company to which the quants belong"
msgstr "Het bedrijf waaraan de hoeveelheden behoren"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory_date
msgid ""
"The date that will be used for the stock level check of the products and the "
"validation of the stock move related to this inventory."
msgstr ""
"De datum die wordt gebruikt voor de voorraad niveau controle van de "
"producten en de geldigheid van de voorraadmutaties, behorende bij deze "
"telling."

#. module: stock
#: code:addons/stock/stock.py:4006
#, python-format
msgid ""
"The default resupply warehouse should be different than the warehouse itself!"
msgstr ""
"Het standaard aanvul magazijn moet een andere zijn dan het magazijn zelf!"

#. module: stock
#: code:addons/stock/stock.py:1215
#, python-format
msgid ""
"The destination location must be the same for all the moves of the picking."
msgstr ""
"De bestemmingslocatie dient hetzelfde te zijn voor alle mutaties van de "
"levering."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_category_form_view_inherit
msgid ""
"The following routes will apply to the products in this category taking into "
"account parent categories:"
msgstr ""
"De volgende routes worden toegepast op de producten in deze categorie, "
"rekening houdende met de bovenliggende categorie."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_reservation_id
msgid "The move the quant is reserved for"
msgstr "de mutatie waarvoor de hoeveelheid is gereserveerd"

#. module: stock
#: sql_constraint:stock.warehouse:0
msgid "The name of the warehouse must be unique per company!"
msgstr "De naam van het magazijn moet uniek zijn per bedrijf!"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_propagated_from_id
msgid "The negative quant this is coming from"
msgstr "De negatieve hoeveelheid komt van"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_path_location_dest_id
msgid "The new location where the goods need to go"
msgstr "De nieuwe locatie waar de goederen naar toe moeten gaan"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_package_parent_id
msgid "The package containing this item"
msgstr "Het verpakking bevat dit item"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_package_id
msgid "The package containing this quant"
msgstr "De verpakking dat deze hoeveelheid bevat"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_location_id
msgid ""
"The parent location that includes this location. Example : The 'Dispatch "
"Zone' is the 'Gate 1' parent location."
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_picking_type
msgid "The picking type determines the picking view"
msgstr "De verzamelsoort bepaald de weergave van de levering"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_picking_type_action
msgid ""
"The picking type system allows you to assign each stock\n"
"                operation a specific type which will alter its views "
"accordingly.\n"
"                On the picking type you could e.g. specify if packing is "
"needed by default,\n"
"                if it should show the customer."
msgstr ""
"Het type levering systeem staat u toe stock operaties toe\n"
"te wijzen aan een specifieke soort die de weergave overeenkomstig aanpast.\n"
"Op het type levering kan u bijvoorbeeld specificeren of verpakken standaard "
"nodig is,\n"
"of het moet getoond moet worden aan de klant."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint_qty_multiple
msgid ""
"The procurement quantity will be rounded up to this multiple.  If it is 0, "
"the exact quantity will be used.  "
msgstr ""
"De verwerving hoeveelheid zal worden afgerond op deze veelvoud. Bij 0 wordt "
"de exacte hoeveelheid gebruikt.  "

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_rule_id
msgid "The procurement rule that created this stock move"
msgstr "De verwervingsregel die de voorraad verplaatsing heeft aangemaakt"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_push_rule_id
msgid "The push rule that created this stock move"
msgstr "De push regel welke deze mutatie heeft aangemaakt"

#. module: stock
#: code:addons/stock/stock.py:4488
#, python-format
msgid "The quantity to split should be smaller than the quantity To Do.  "
msgstr ""
"De hoeveelheid om te splitsen moet kleiner zijn dan de hoeveel van To Do."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "The quotation becomes a Sale Order and a Transfer Order is created"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:1910
#, python-format
msgid ""
"The requested operation cannot be processed because of a programming error "
"setting the `product_qty` field instead of the `product_uom_qty`."
msgstr ""
"De gevraagde bewerking kan niet uitgevoerd worden door het fout instellen "
"van het 'product_qty' veld in plaats van het veld 'product_uom_qty'."

#. module: stock
#: code:addons/stock/stock.py:2703
#, fuzzy, python-format
msgid ""
"The roundings of your unit of measure %s on the move vs. %s on the product "
"don't allow to do these operations or you are not transferring the picking "
"at once. "
msgstr ""
"De afronding van uw maateenheid %s bij de mutatie vs. %s bij het product "
"laten deze bewerkingen niet toe. Of u verplaatst deze verzamellijst niet in "
"1 keer. "

#. module: stock
#: code:addons/stock/stock.py:4376
#, python-format
msgid ""
"The selected UoM for product %s is not compatible with the UoM set on the "
"product form. \n"
"Please choose an UoM within the same UoM category."
msgstr ""
"De geselecteerde maateenheid voor product %s is niet compatible met de "
"maateenheid zoals ingesteld op het product formulier. \n"
"Selecteer een maateenheid van dezelfde maateenheid categorie."

#. module: stock
#: constraint:stock.inventory:0
msgid "The selected inventory options are not coherent."
msgstr "De geselecteerde voorraad opties zijn niet eenduidig."

#. module: stock
#: code:addons/stock/stock.py:577
#, fuzzy, python-format
msgid "The serial number %s is already in stock."
msgstr "De serienummer %s is al in stock"

#. module: stock
#: code:addons/stock/stock.py:1218
#, python-format
msgid "The source location must be the same for all the moves of the picking."
msgstr ""
"De bronlocatie dient hetzelfde te zijn voor alle mutaties van de levering."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_pack_operation_picking_id
msgid "The stock operation where the packing has been made"
msgstr "De voorraad bewerking waar de verpakking is aangemaakt"

#. module: stock
#: model:ir.model.fields,help:stock.field_procurement_rule_warehouse_id
msgid "The warehouse this rule is for"
msgstr "Het magazijn waar deze regel voor is"

#. module: stock
#: model:ir.model.fields,help:stock.field_procurement_rule_propagate_warehouse_id
msgid ""
"The warehouse to propagate on the created move/procurement, which can be "
"different of the warehouse this rule is for (e.g for resupplying rules from "
"another warehouse)"
msgstr ""
"Het magazijn waaraan de aangemaakte mutatie/verwerving wordt doorgegeven. "
"Deze kan afwijkend zijn dan het magazijn waar deze regel voor is (bijv. voor "
"aanvulregels vanuit een ander magazijn)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_theoretical_qty
msgid "Theoretical Quantity"
msgstr "Theoretische hoeveelheid"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_config_settings_group_stock_tracking_lot
msgid ""
"This allows to manipulate packages.  You can put something in, take "
"something from a package, but also move entire packages and put them even in "
"another package.  "
msgstr ""
"Dit geeft u de mogelijkheid om verpakkingen te bewerken. U kunt er iets "
"instoppen. U kunt er iets uit halen, maar ook een verpakking in zijn geheel "
"verplaatsen of zelf in een andere verpakking opnemen.  "

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_config_settings_group_stock_production_lot
msgid ""
"This allows you to assign a lot (or serial number) to the pickings and "
"moves.  This can make it possible to know which production lot was sent to a "
"certain client, ..."
msgstr ""
"Dit geeft u de mogelijkheid om partijnummers te koppelen aan ontvangsten, "
"leveringen en voorraadmutaties. Dit maakt het mogelijk om uw product te "
"traceren door gebruik te maken van het partijnummer."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_config_settings_module_stock_calendar
msgid ""
"This allows you to handle minimum stock rules differently by the possibility "
"to take into account the purchase and delivery calendars \n"
"-This installs the module stock_calendar."
msgstr ""
"Dit staat u toe om minimale stockregels anders toe te passen om rekening te "
"houden met inkoop en leveringskalenders\n"
"- Dit installeert de module stock_calendar."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.quantsact
msgid ""
"This analysis gives you a fast overview on the current stock level of your "
"products and their current inventory value."
msgstr ""
"Deze analyse geeft u een snelle weergave van uw huidige voorraadniveau van "
"uw producten en hun huidige voorraadwaarde."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_package_packaging_id
msgid ""
"This field should be completed only if everything inside the package share "
"the same product, otherwise it doesn't really makes sense."
msgstr ""
"Dit veld moet worden ingevuld alleen als alles in de verpakking hetzelfde "
"product delen, anders is het niet echt zinvol."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"This guide helps getting started with Odoo Inventory.\n"
"                        Once you are done, you will benefit from:"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"This is quite complex to set up, so <strong>contact your Project Manager</"
"strong> to address this."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type_default_location_dest_id
msgid ""
"This is the default destination location when you create a picking manually "
"with this picking type. It is possible however to change it or that the "
"routes put another location. If it is empty, it will check for the customer "
"location on the partner. "
msgstr ""
"Dit is de standaard doellocatie wanneer u manueel een levering aanmaakt met "
"deze soort van levering. Het is mogelijk om te wijzigen of dat routes naar "
"een andere locatie gaan. Indien dit leeg is controleert het de klantlocatie "
"van de partner."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type_default_location_src_id
msgid ""
"This is the default source location when you create a picking manually with "
"this picking type. It is possible however to change it or that the routes "
"put another location. If it is empty, it will check for the supplier "
"location on the partner. "
msgstr ""
"Dit is de standaard doellocatie wanneer u manueel een levering aanmaakt met "
"deze soort van levering. Het is mogelijk om te wijzigen of dat routes naar "
"een andere locatie gaan. Indien dit leeg is controleert het de leverancier "
"locatie op de partner."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_production_lot_form
msgid ""
"This is the list of all the production lots you recorded. When\n"
"                you select a lot, you can get the traceability of the "
"products contained in lot."
msgstr ""
"Dit is de lijst van alle productie partijen die u heeft geregistreerd. "
"Wanneer\n"
"u een partij selecteert kan u de traceerbaarheid van de producten in een "
"partij ophalen."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_owner_id
msgid "This is the owner of the quant"
msgstr "Dit is de eigenaar van de hoeveelheid"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_path_picking_type_id
msgid "This is the picking type that will be put on the stock moves"
msgstr ""
"Dit is het type levering dat wordt geplaatst op de voorraad verplaatsing"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_product_uom_qty
msgid ""
"This is the quantity of products from an inventory point of view. For moves "
"in the state 'done', this is the quantity of products that were actually "
"moved. For other moves, this is the quantity of product that is planned to "
"be moved. Lowering this quantity does not generate a backorder. Changing "
"this quantity on assigned moves affects the product reservation, and should "
"be done with care."
msgstr ""
"Dit is de hoeveelheid producten vanuit voorraad oogpunt. Voor mutaties in de "
"staat 'gereed', dit is de hoeveelheid producten die daadwerkelijk werden "
"verplaatst. Voor andere mutaties, is dit de hoeveelheid product die zijn "
"gepland te worden verplaatst. Het verlagen van deze hoeveelheid genereert "
"geen backorder. Het wijzigen van deze hoeveelheid op toegewezen mutaties is "
"van invloed op de product reservering, en moet worden gedaan met zorg."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_move_form2
msgid ""
"This menu gives you the full traceability of inventory\n"
"                operations on a specific product. You can filter on the "
"product\n"
"                to see all the past or future movements for the product."
msgstr ""
"Deze menu geeft u de volledige traceerbaarheid van stock\n"
"operaties op een specifiek product. U kan op het product\n"
"filteren om alle vorige en toekomstige mutaties voor het product te zien."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_config_settings_group_stock_adv_location
msgid ""
"This option supplements the warehouse application by effectively "
"implementing Push and Pull inventory flows through Routes."
msgstr ""
"Deze optie is een aanvulling op de voorraad module  door het effectief "
"implementeren van Push en Pull flows, door gebruik te maken van routes."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
msgid ""
"This picking appears to be chained with another operation. Later, if you "
"receive the goods you are returning now, make sure to <b>reverse</b> the "
"returned picking in order to avoid logistic rules to be applied again (which "
"would create duplicated operations)"
msgstr ""
"Deze levering lijkt aan een andere operatie gekoppeld te zijn. Zorg er laten "
"voor, als u de goederen ontvangt die u nu terugstuurt, dat de teruggekeerde "
"levering <b>omgekeerd</b> wordt om te vermeiden da t logistieke regels niet "
"opnieuw worden toegepast (wat dubbele operaties zou aanmaken)"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_change_product_qty_new_quantity
msgid ""
"This quantity is expressed in the Default Unit of Measure of the product."
msgstr ""
"Deze hoeveelheid is uitgedrukt in de standaard maateenheid van het product."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_path_location_from_id
msgid ""
"This rule can be applied when a move is confirmed that has this location as "
"destination location"
msgstr ""
"Deze regel kan toegepast worden wanneer een mutatie bevestigd is die deze "
"locatie als bestemmingslocatie heeft"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner_property_stock_customer
msgid ""
"This stock location will be used, instead of the default one, as the "
"destination location for goods you send to this partner"
msgstr ""
"De voorraadlocatie zal gebruikte worden, in plaats van de standaard, voor de "
"goederen welke verzonden worden naar deze relatie."

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner_property_stock_supplier
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for goods you receive from the current partner"
msgstr ""
"Deze voorraadlocatie zal worden gebruikt in plaats van de standaardlocatie"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product_property_stock_production
#: model:ir.model.fields,help:stock.field_product_template_property_stock_production
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated by manufacturing orders."
msgstr ""
"Deze voorraadlocatie zal worden gebruikt, in plaats van de standaardlocatie, "
"als bronlocatie voor voorraadmutaties, gegenereerd door productieorders."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product_property_stock_procurement
#: model:ir.model.fields,help:stock.field_product_template_property_stock_procurement
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated by procurements."
msgstr ""
"Deze voorraadlocatie zal worden gebruikt, in plaats van de standaardlocatie, "
"als bronlocatie voor voorraadmutaties, gegenereerd door verwervingen."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product_property_stock_inventory
#: model:ir.model.fields,help:stock.field_product_template_property_stock_inventory
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated when you do an inventory."
msgstr ""
"Deze voorraadlocatie zal worden gebruikt, in plaats van de standaardlocatie, "
"als bronlocatie voor voorraadmutaties gegenereerd door een voorraadtelling"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_config_settings_group_stock_tracking_owner
msgid "This way you can receive products attributed to a certain owner. "
msgstr ""
"Op deze manier kunt u product kenmerken ontvangen van een specifieke "
"eigenaar. "

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_to_loc
msgid "To"
msgstr "Aan"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_ready
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_lot_qty_todo
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_product_qty
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "To Do"
msgstr "Te doen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "To Receive"
msgstr "Te ontvangen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"To better organize your stock, you can create\n"
"                            subdivisions of your Warehouse called "
"<strong>Locations</strong> (ex:\n"
"                            Shipping area, Merchandise return, Shelf 34 "
"etc).\n"
"                            Do not use Locations if you do not manage "
"inventory per zone."
msgstr ""
"Om uw stock beter te organiseren kan u\n"
"subdivisies van uw magazijn aanmaken, genaamd <strong>Locaties/strong> "
"(bijv:\n"
"Verzend gedeelte, terugkeer van koopwaard, Rek 34 enz).\n"
"Gebruik geen locaties als u voorraad niet per zonde beheerd."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"To check the trajectory of a lot, find it back in <strong><i>Inventory "
"Control &gt; Serial Numbers / lots</i></strong>.\n"
"                       Choose a lot in the list and click on "
"<i>Traceability</i>i&gt;. You may also\n"
"                       filter the Quantitative Valuation of a product with a "
"certain lot."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"To create them, click on <strong><span class=\"fa fa-refresh\"/> Reordering</"
"strong> on"
msgstr ""
"Om ze aan te maken, klikt u op <strong><span class=\"fa fa-refresh\"/> "
"Aanvulopdracht</strong> op"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"To use more precise units like pounds or kilograms, activate<i> Some "
"products may be sold/purchased in different unit of measures (advanced)</i> "
"in the"
msgstr ""
"Om nauwkeurigere eenheden te gebruiken zoals ponden of kilo's activeert u "
"<i>Sommige producten mogen verkocht/ingekocht worden in verschillende "
"maateenheden (geavanceerd)</i> in het"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Today"
msgstr "Vandaag"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category_total_route_ids
msgid "Total routes"
msgstr "Totale routes"

#. module: stock
#: code:addons/stock/stock.py:1773
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_config_settings
#, python-format
msgid "Traceability"
msgstr "Traceability"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_config_settings_module_product_expiry
msgid ""
"Track different dates on products and serial numbers.\n"
"                    The following dates can be tracked:\n"
"                    - end of life\n"
"                    - best before date\n"
"                    - removal date\n"
"                    - alert date.\n"
"                    This installs the module product_expiry."
msgstr ""
"Traceer verschillende datums op producten en serienummers.\n"
"De volgende datums kunnen getraceerd worden:\n"
"- Einde van leven\n"
"- Tenminste houdbaar tot datum\n"
"- Verwijderdatum\n"
"- Signalering datum.\n"
"Dit installeert de module product_expiry."

#. module: stock
#: selection:stock.config.settings,group_stock_production_lot:0
msgid "Track lots or serial numbers"
msgstr "Traceer partijnummers"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_tracking
#: model:ir.model.fields,field_description:stock.field_product_template_tracking
msgid "Tracking"
msgstr "Traceren"

#. module: stock
#: code:addons/stock/stock.py:2895
#: model:ir.model,name:stock.model_stock_picking
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#, python-format
msgid "Transfer"
msgstr "Verplaatsing"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_picking_partner_id
msgid "Transfer Destination Address"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_picking_id
msgid "Transfer Reference"
msgstr "Verplaatsingsreferentie"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Transfers"
msgstr "Transfers"

#. module: stock
#: selection:stock.location,usage:0
msgid "Transit Location"
msgstr "Tussen locatie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Transit Locations"
msgstr "Tussen locaties"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_recompute_pack_op
msgid ""
"True if reserved quants changed, which mean we might need to recompute the "
"package operations"
msgstr ""
"Waar indien de gereserveerde hoeveelheid is aangepast, wat betekend dat we "
"de verpakking bewerkingen wellicht opnieuw moeten berekenen."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_code
msgid "Type of Operation"
msgstr "Soort bewerking"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_packaging_type_id
msgid "Type of packaging"
msgstr "Soort verpakking"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_module_delivery_ups
msgid "UPS integration"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_module_delivery_usps
msgid "USPS integration"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:731
#, python-format
msgid "Under no circumstances should you delete or change quants yourselves!"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_production_lot_name
msgid "Unique Serial Number"
msgstr "Unieke partijnummer"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_cost
msgid "Unit Cost"
msgstr "Kostprijs per stuk"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_form
msgid "Unit Of Measure"
msgstr "Maateenheid"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_price_unit
msgid "Unit Price"
msgstr "Prijs"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_make_procurement_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_move_product_uom
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_product_uom_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking_board
msgid "Unit of Measure"
msgstr "Maateenheid"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_uom_categ_form_action
msgid "Unit of Measure Categories"
msgstr "Maateenheid categorieën"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_group_uom
#: model:ir.ui.menu,name:stock.menu_stock_unit_measure_stock
#: model:ir.ui.menu,name:stock.menu_stock_uom_form_action
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Units of Measure"
msgstr "Maateenheden"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Units of Measures"
msgstr "Maateenheden"

#. module: stock
#: code:addons/stock/stock.py:4236
#, python-format
msgid "Unknown Pack"
msgstr "Onbekende verpakking"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Unless you are starting a new business, you probably have a list of vendors "
"you would like to import."
msgstr ""

#. module: stock
#: selection:stock.warehouse,reception_steps:0
msgid "Unload in input location then go to stock (2 steps)"
msgstr "Uitladen naar ontvangstlocatie en ga dan naar voorraad (stap 2)"

#. module: stock
#: selection:stock.warehouse,reception_steps:0
msgid ""
"Unload in input location, go through a quality control before being admitted "
"in stock (3 steps)"
msgstr ""
"Uitladen bij de ontvangstlocatie, ga naar de kwaliteitscontrole, voor het "
"bijboeken op de voorraad (3 stappen)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Unpack"
msgstr "Uitpakken"

#. module: stock
#: code:addons/stock/product.py:319
#, python-format
msgid "Unplanned Qty"
msgstr "Niet geplande aantal"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Unreserve"
msgstr "Reservering ongedaan maken"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_resupply_from_wh
msgid "Unused field"
msgstr "Ongebruikt veld"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "UoM"
msgstr "Maateenheid"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_view_change_product_quantity
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
msgid "Update Product Quantity"
msgstr "Producthoeveelheid bijwerken"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_product_view_form_easy_inherit_stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "Update Qty On Hand"
msgstr "Update beschikbare Hvh"

#. module: stock
#: selection:stock.move,priority:0 selection:stock.picking,priority:0
msgid "Urgent"
msgstr "Dringend"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_use_existing_lots
msgid "Use Existing Lots"
msgstr "Gebruik bestaande partijen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_make_procurment_wizard
msgid ""
"Use this assistant to generate a procurement request for this\n"
"                        product. According to the product configuration, "
"this may\n"
"                        trigger a draft purchase order, a manufacturing "
"order or\n"
"                        a new task."
msgstr ""
"Gebruik dit scherm voor het aanmaken van een verwervingsverzoek voor dit\n"
"                        product. Op basis van de product instellingen, zal "
"dit een \n"
"                        inkooporder, een productieorder of een nieuwe taak \n"
"                        genereren."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type_sequence
msgid "Used to order the 'All Operations' kanban view"
msgstr "Wordt gebruikt om de \"lle bewerkingen\" kanban weergave te sorteren"

#. module: stock
#: model:res.groups,name:stock.group_stock_user
msgid "User"
msgstr "Gebruiker"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "VAT:"
msgstr "BTW:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Validate"
msgstr "Bevestig"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Validate Inventory"
msgstr "Goedkeuren voorraadtelling"

#. module: stock
#: selection:stock.inventory,state:0
msgid "Validated"
msgstr "Gecontroleerd"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_make_procurement_product_variant_count
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_product_variant_count
msgid "Variant Number"
msgstr "Variant nummer"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Vendor"
msgstr "Fabrikant"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner_property_stock_supplier
#: selection:stock.location,usage:0
msgid "Vendor Location"
msgstr "Plaats fabrikant"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Vendor Locations"
msgstr "Leverancier locaties"

#. module: stock
#: model:stock.location,name:stock.stock_location_suppliers
#: selection:stock.picking,picking_type_code:0
#: selection:stock.picking.type,code:0
msgid "Vendors"
msgstr "Fabrikanten"

#. module: stock
#: selection:stock.move,priority:0 selection:stock.picking,priority:0
msgid "Very Urgent"
msgstr "Zeer urgent"

#. module: stock
#: selection:stock.location,usage:0
msgid "View"
msgstr "Weergave"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "View Contained Packages content"
msgstr "Bekijk de inhoud van de verpakking"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_view_location_id
msgid "View Location"
msgstr "Bekijk locatie"

#. module: stock
#: model:stock.location,name:stock.stock_location_locations_virtual
msgid "Virtual Locations"
msgstr "Virtuele locatie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Waiting"
msgstr "Wachten"

#. module: stock
#: selection:stock.move,state:0
msgid "Waiting Another Move"
msgstr "Wachten op andere mutatie"

#. module: stock
#: selection:stock.pack.operation,state:0 selection:stock.picking,state:0
msgid "Waiting Another Operation"
msgstr "Wachten op andere verwerking"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: selection:stock.move,state:0 selection:stock.pack.operation,state:0
#: selection:stock.picking,state:0
msgid "Waiting Availability"
msgstr "Wachten op beschikbaarheid"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Waiting Moves"
msgstr "Wachtende mutaties"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_waiting
msgid "Waiting Transfers"
msgstr "Wachtende verplaatsingen"

#. module: stock
#: model:ir.model,name:stock.model_stock_warehouse
#: model:ir.model.fields,field_description:stock.field_make_procurement_warehouse_id
#: model:ir.model.fields,field_description:stock.field_procurement_order_warehouse_id
#: model:ir.model.fields,field_description:stock.field_product_product_warehouse_id
#: model:ir.model.fields,field_description:stock.field_product_template_warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_location_path_warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_move_warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_warehouse_id
#: model_terms:ir.ui.view,arch_db:stock.view_partner_stock_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_tree
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Warehouse"
msgstr "Magazijn"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Warehouse Configuration"
msgstr "Magazijninstellingen"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_warehouse_config
msgid "Warehouse Management"
msgstr "Magazijnbeheer"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_name
msgid "Warehouse Name"
msgstr "Magazijn naam"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_propagate_warehouse_id
msgid "Warehouse to Propagate"
msgstr "Door te geven magazijn"

#. module: stock
#: model:ir.model.fields,help:stock.field_procurement_order_warehouse_id
msgid "Warehouse to consider for the route selection"
msgstr "Magazijn welke moet worden overwogen bij het selecteren van een route"

#. module: stock
#: code:addons/stock/stock.py:4043
#, python-format
msgid "Warehouse's Routes"
msgstr "Magazijnroutes"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_warehouse_form
#: model:ir.model.fields,field_description:stock.field_stock_location_route_warehouse_ids
#: model:ir.ui.menu,name:stock.menu_action_warehouse_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Warehouses"
msgstr "Magazijnen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_config_settings_warehouse_and_location_usage_level
msgid "Warehouses and Locations usage level"
msgstr ""

#. module: stock
#: code:addons/stock/product.py:340
#, python-format
msgid "Warning!"
msgstr "Waarschuwing!"

#. module: stock
#: code:addons/stock/stock.py:4375
#, python-format
msgid "Warning: wrong UoM!"
msgstr "Waarschuwing: foute maateenheid!"

#. module: stock
#: code:addons/stock/stock.py:4382
#, python-format
msgid "Warning: wrong quantity!"
msgstr "Waarschuwing: foute hoeveelheid!"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"We handle the whole import process\n"
"                                        for you: simply send your Odoo "
"project\n"
"                                        manager a CSV file containing all "
"your\n"
"                                        data."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"We handle the whole import process\n"
"                                        for you: simply send your Odoo "
"project\n"
"                                        manager a CSV file containing all "
"your\n"
"                                        products."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "We hope this guide helped you implement Odoo Inventory."
msgstr ""
"We hopen dat deze handleiding u geholpen heeft bij het configureren van uw "
"eigen Odoo voorraad."

#. module: stock
#: code:addons/stock/stock.py:4962
#, fuzzy, python-format
msgid "Weighted Product"
msgstr "Geteld product"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Welcome"
msgstr "Welkom"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route_warehouse_selectable
msgid ""
"When a warehouse is selected for this route, this route should be seen as "
"the default route when products pass through this warehouse.  This behaviour "
"can be overridden by the routes on the Product/Product Categories or by the "
"Preferred Routes on the Procurement"
msgstr ""
"Wanneer een magazijn is geselecteerd voor deze productieroute zal deze "
"productieroute gezien worden als de standaard route wanneer producten door "
"uw magazijn passeren. Dit gedrag kan overschreven worden door de "
"productieroutes op de Product/Productcategorieën of door de verkozen "
"productieroutes op de verwerving"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route_product_selectable
msgid ""
"When checked, the route will be selectable in the Inventory tab of the "
"Product form.  It will take priority over the Warehouse route. "
msgstr ""
"Indien aangevinkt, zal de productieroute selecteerbaar zijn in het voorraad "
"tabblad op het productformulier. Het heeft prioriteit boven de magazijn "
"route."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route_product_categ_selectable
msgid ""
"When checked, the route will be selectable on the Product Category.  It will "
"take priority over the Warehouse route. "
msgstr ""
"Indien aangevinkt, zal de productieroute selecteerbaar zijn op de "
"productiecategorie. Het heeft prioriteit boven de magazijn route."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "When everything is set, click on <i>Start Inventory</i>"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_putaway_fixed_location_ids
msgid ""
"When the method is fixed, this location will be used to store the products"
msgstr ""
"Wanneer de methode vast is, zal deze locatie worden gebruikt om alle "
"producten op te slaan"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint_product_min_qty
msgid ""
"When the virtual stock goes below the Min Quantity specified for this field, "
"Odoo generates a procurement to bring the forecasted quantity to the Max "
"Quantity."
msgstr ""
"Wanneer een virtuele voorraad onder de minimale voorraad komt, zoals "
"gespecificeerd in dit veld, zal Odoo een verwervingsopdracht genereren om de "
"verwachte voorraad aan te vullen tot de maximale hoeveelheid."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint_product_max_qty
msgid ""
"When the virtual stock goes below the Min Quantity, Odoo generates a "
"procurement to bring the forecasted quantity to the Quantity specified as "
"Max Quantity."
msgstr ""
"Wanneer een virtuele voorraad onder de minimale voorraad komt, zoals "
"gespecificeerd in dit veld, zal Odoo een verwervingsopdracht genereren om de "
"verwachte voorraad aan te vullen tot de maximale hoeveelheid."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
msgid ""
"When you select a serial number (lot), the quantity is corrected with "
"respect to\n"
"                            the quantity of that serial number (lot) and not "
"to the total quantity of the product."
msgstr ""
"Wanneer u een partijnummer selecteert, wordt de hoeveelheid gecorrigeerd, "
"rekening houdende met\n"
"                            de hoeveelheid van deze partij en niet ten "
"opzichte van de totale hoeveelheid van het product."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line_wizard_id
msgid "Wizard"
msgstr "Assistent"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_compute_wizard
msgid ""
"Wizard checks all the stock minimum rules and generate procurement order."
msgstr ""
"Wizard controleert alle minimale voorraadregels en genereert "
"verwervingsopdracht."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_config_settings_group_product_variant
msgid ""
"Work with product variant allows you to define some variant of the same "
"products, an ease the product management in the ecommerce for example"
msgstr ""
"Werken met productvarianten biedt u de mogelijkheid om varianten voor "
"eenzelfde product te definiëren, en vergemakkelijkt het productbeheer voor "
"bijv. e-Commerce"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_routes_form
msgid ""
"You can define here the main routes that run through\n"
"                    your warehouses and that define the flows of your "
"products. These\n"
"                    routes can be assigned to a product, a product category "
"or be fixed\n"
"                    on procurement or sales order."
msgstr ""
"U kan hier de hoofd productieroutes definiëren die door uw\n"
"magazijn lopen en die de flow van uw producten definiëren.  Deze\n"
"productieroutes kunnen toegewezen worden aan een product, een "
"productcategorie of vastgesteld worden op\n"
"verwerving of verkooporder."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint_form
msgid ""
"You can define your minimum stock rules, so that Odoo will automatically "
"create draft manufacturing orders or request for quotations according to the "
"stock level. Once the virtual stock of a product (= stock on hand minus all "
"confirmed orders and reservations) is below the minimum quantity, Odoo will "
"generate a procurement request to increase the stock up to the maximum "
"quantity."
msgstr ""
" U kunt minimale voorraadregels aanmaken, zodat Odoo automatisch concept "
"productieorders of inkooporders kan aanmaken op basis van het "
"voorraadniveau. Wanneer de virtuele voorraad van een product (= beschikbare "
"voorraad min alle bevestigde verkooporder en reserveringen) onder de "
"minimale hoeveelheid ligt, zal OpenERP een verwerving verzoek aanmaken om de "
"voorraad aan te vullen naar de maximale hoeveelheid.\n"
"            "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "You can delete lines to ignore some products."
msgstr "Je kan regels verwijderen om sommige producten te negeren."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_picking_form
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_all
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_backorder
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_done
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_done_grouped
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_late
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_ready
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_waiting
#: model_terms:ir.actions.act_window,help:stock.action_picking_type_list
#: model_terms:ir.actions.act_window,help:stock.stock_picking_action_picking_type
#, fuzzy
msgid ""
"You can either do it immediately or mark it as Todo for future processing. "
"Use your scanner to validate the transferred quantity quicker."
msgstr ""
"U kan dit of manueel doen of markeren als To do voor verwerking in de "
"toekomst. Gebruik uw scanner om de overplaatsingshoeveelheid sneller te "
"valideren."

#. module: stock
#: code:addons/stock/product.py:519
#, python-format
msgid ""
"You can not change the unit of measure of a product that has already been "
"used in a done stock move. If you need to change the unit of measure, you "
"may deactivate this product."
msgstr ""
"Het is niet mogelijk de maateenheid van een product te wijzigen welke al is "
"gebruikt in een voorraad mutatie. Indien u de maateenheid wilt veranderen, "
"kunt u dit product de-activeren."

#. module: stock
#: code:addons/stock/stock.py:4499
#, python-format
msgid "You can not delete pack operations of a done picking"
msgstr ""
"U kan geen verpakkingshandelingen verwijderen van een uitgevoerde levering"

#. module: stock
#: code:addons/stock/stock.py:385
#, python-format
msgid "You can not reserve a negative quantity or a negative quant."
msgstr "Je kan geen negatieve hoeveelheid reserveren."

#. module: stock
#: code:addons/stock/stock.py:2758
#, python-format
msgid "You can only delete draft moves."
msgstr "Er kunnen alleen regels met de status 'Nieuw' worden verwijderd."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "You can review and edit the predefined units via the"
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:2492
#, python-format
msgid "You cannot cancel a stock move that has been set to 'Done'."
msgstr ""
"Het is niet mogelijk een voorraadmutatie te annuleren welke in de 'Gereed' "
"fase is."

#. module: stock
#: code:addons/stock/stock.py:3196
#, python-format
msgid ""
"You cannot have two inventory adjustements in state 'in Progess' with the "
"same product(%s), same location(%s), same package, same owner and same lot. "
"Please first validate the first inventory adjustement with this product "
"before creating another one."
msgstr ""

#. module: stock
#: code:addons/stock/stock.py:725
#, python-format
msgid "You cannot move to a location of type view %s."
msgstr ""
"U kunt geen verplaatsingen uitvoeren naar een locatie met van het type %s."

#. module: stock
#: code:addons/stock/stock.py:3006
#, python-format
msgid ""
"You cannot set a negative product quantity in an inventory line:\n"
"\t%s - qty: %s"
msgstr ""
"Het is niet mogelijk een negatieve product hoeveelheid in te voeren op een "
"telling regel:\n"
"\t%s - Hvh: %s"

#. module: stock
#: code:addons/stock/stock.py:2832
#, python-format
msgid "You cannot split a draft move. It needs to be confirmed first."
msgstr ""
"Het is niet mogelijk een concept regel op te splitsen. U dient deze eerst te "
"bevestigen."

#. module: stock
#: code:addons/stock/stock.py:2828
#, python-format
msgid "You cannot split a move done"
msgstr "Het is niet mogelijk een regel welke gereed is op te splitsen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid ""
"You do not have any products reserved for this picking.  Please click the "
"'Reserve' button\n"
"                                to check if products are available."
msgstr ""
"U heeft geen producten gereserveerd voor deze levering. Klik aub op de "
"'Reserveer' knop\n"
"om te controleren of producten beschikbaar zijn."

#. module: stock
#: code:addons/stock/stock.py:2654
#, python-format
msgid ""
"You have a difference between the quantity on the operation and the "
"quantities specified for the lots. "
msgstr ""
"U heeft een verschil tussen de hoeveelheid op de operatie en de hoeveelheid "
"gespecificeerd op de partij."

#. module: stock
#: sql_constraint:stock.pack.operation.lot:0
msgid "You have already mentioned this lot in another line"
msgstr "U heeft deze partij al vermeld op een andere lijn"

#. module: stock
#: sql_constraint:stock.pack.operation.lot:0
msgid "You have already mentioned this lot name in another line"
msgstr "U heeft deze partijnaam al vermeld in een andere lijn"

#. module: stock
#: code:addons/stock/wizard/stock_return_picking.py:132
#, python-format
msgid "You have manually created product lines, please delete them to proceed"
msgstr ""
"U heeft handmatig productregels aangemaakt. Verwijder deze om door te gaan."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "You have processed less products than the initial demand."
msgstr "U heeft minder producten verwerkt dan de initiële aanvraag."

#. module: stock
#: code:addons/stock/product.py:341
#, python-format
msgid ""
"You have products in stock that have no lot number.  You can assign serial "
"numbers by doing an inventory.  "
msgstr ""
"U heeft producten in stock die een partijnummer hebben. U kan serienummers "
"toewijzen door een voorraad telling te doen."

#. module: stock
#: constraint:stock.warehouse.orderpoint:0
msgid ""
"You have to select a product unit of measure in the same category than the "
"default unit of measure of the product"
msgstr ""
"U dient een product maateenheid in dezelfde categorie als de standaard "
"maateenheid."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
msgid ""
"You haven't set processed quantities. If you click <i>apply</i>,\n"
"                        Odoo will process all quantities to do."
msgstr ""
"U heeft geen verwerkte hoeveelheden ingesteld. Als u klikt op <i>toepassen</"
"i>,\n"
"zal Odoo alle hoeveelheden die te doen zijn verwerken."

#. module: stock
#: code:addons/stock/wizard/stock_return_picking.py:48
#, python-format
msgid "You may only return one picking at a time!"
msgstr "Je kan maar 1 levering tegelijk retourneren!"

#. module: stock
#: code:addons/stock/wizard/stock_return_picking.py:58
#, python-format
msgid "You may only return pickings that are Done!"
msgstr ""
"Het is alleen mogelijk retouren te verwerken van regels welke gereed zijn!"

#. module: stock
#: code:addons/stock/stock.py:2376 code:addons/stock/stock.py:4510
#, python-format
msgid "You need to provide a Lot/Serial Number for product %s"
msgstr "U moet een partij/serienummer toevoegen voor het product %s"

#. module: stock
#: code:addons/stock/stock.py:572
#, python-format
msgid "You should only receive by the piece with the same serial number"
msgstr "U zou enkel per stuk moeten ontvangen met hetzelfde serienummer"

#. module: stock
#: code:addons/stock/stock.py:4514
#, python-format
msgid "You should provide a different serial number for each piece"
msgstr "U moet een andere serienummer ingeven voor elk stuk"

#. module: stock
#: constraint:stock.move:0
msgid ""
"You try to move a product using a UoM that is not compatible with the UoM of "
"the product moved. Please use an UoM in the same UoM category."
msgstr ""
"U probeert een product te verplaatsen, met gebruik van een maateenheid, "
"welke niet compatible is met de maateenheid van het verplaatste product. "
"gebruik een maateenheid uit dezelfde maateenheid categorie."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Your Products"
msgstr "Uw producten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Your Situation"
msgstr "Uw situatie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Your Vendors"
msgstr "Uw fabrikanten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Your Warehouse"
msgstr "Uw magazijn"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
msgid "_Apply"
msgstr "_Toepassen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
msgid "_Cancel"
msgstr "_Annuleren"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "a stockable Product"
msgstr "een stockeerbaar product"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "and simply enter a minimum and maximum quantity."
msgstr "en vul simpelweg een minimum en maximum hoeveelheid in."

#. module: stock
#: model:ir.model,name:stock.model_barcode_rule
msgid "barcode.rule"
msgstr "barcode.rule"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "configuration menu"
msgstr "configuratie menu"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.stock_location_path_form
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_rule_form_stock_inherit
msgid "days"
msgstr "dagen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "e.g. Annual inventory"
msgstr "Bijv. Jaarlijkse voorraadtelling"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "e.g. LOT/0001/20121"
msgstr "bijv. LOT/0001/20121"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "e.g. PO0032"
msgstr "Bijv. PO0032"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"either by manually updating the Done quantity on the product lines, or let "
"Odoo do it automatically while validating"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"either by manually updating the Done quantity on the product lines, or scan "
"them with the Odoo Barcode app, or let Odoo do it automatically while "
"validating"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "for a customer and add products"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"for more\n"
"                        information."
msgstr ""
"voor meer\n"
"informatie."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "from your vendor with the products and the requested quantities"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"is displayed on the transfer if your products supply chain is properly "
"configured. Otherwise, <strong>Check the availability</strong> manually"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_origin_returned_move_id
msgid "move that created the return move"
msgstr "mutatie welke de retour mutatie heeft veroorzaakt"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_pack_operation_lot_form
msgid "of"
msgstr "of"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "on"
msgstr "aan"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"on the purchase order form or click on <i>Receive Products</i> to see the "
"Transfer Order"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "on the sale order form to see Transfer Order"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_product_packaging
msgid "preferred Packaging"
msgstr "Geprefereerde verpakking"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_forecast
msgid "report.stock.forecast"
msgstr "report.stock.forecast"

#. module: stock
#: model:ir.model,name:stock.model_stock_config_settings
msgid "stock.config.settings"
msgstr "stock.config.settings"

#. module: stock
#: model:ir.model,name:stock.model_stock_fixed_putaway_strat
msgid "stock.fixed.putaway.strat"
msgstr "stock.fixed.putaway.strat"

#. module: stock
#: model:ir.model,name:stock.model_stock_return_picking_line
msgid "stock.return.picking.line"
msgstr "stock.return.picking.line"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "the list of products"
msgstr "de lijst met producten"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "the list of vendors"
msgstr "de lijst van leveranciers"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "to mark the products as transferred to your stock location"
msgstr "om de producten te markeren als overgeplaatst naar uw stocklocatie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_reordering_max_qty
#: model:ir.model.fields,field_description:stock.field_product_product_reordering_min_qty
#: model:ir.model.fields,field_description:stock.field_product_template_reordering_max_qty
#: model:ir.model.fields,field_description:stock.field_product_template_reordering_min_qty
#: model:ir.model.fields,field_description:stock.field_stock_inventory_total_qty
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_lot_operation_id
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_lot_plus_visible
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_lots_visible
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_picking_destination_location_id
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_picking_source_location_id
#: model:ir.model.fields,field_description:stock.field_stock_pack_operation_state
#: model:ir.model.fields,field_description:stock.field_stock_picking_picking_type_code
#: model:ir.model.fields,field_description:stock.field_stock_picking_picking_type_entire_packs
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_count_picking
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_count_picking_backorders
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_count_picking_draft
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_count_picking_late
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_count_picking_ready
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_count_picking_waiting
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_rate_picking_backorders
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_rate_picking_late
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_original_location_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_parent_location_id
msgid "unknown"
msgstr "onbekend"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "via the"
msgstr "via de"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "when you receive the ordered products"
msgstr "wanneer u de bestelde producten ontvangt"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "with the <i>Validate</i> button"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "↳Put in Pack"
msgstr "↳ Plaats in verpakking"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "⇒ Set quantities to 0"
msgstr "⇒ Zet hoeveelheid op nul"

#~ msgid "<span><strong>Contact Address:</strong></span>"
#~ msgstr "<span><strong>Contact adres:</strong></span>"

#~ msgid "Action Needed"
#~ msgstr "Vereist actie"

#~ msgid "Date of the last message posted on the record."
#~ msgstr "Datum van het laatste bericht verstuurt op deze regel."

#~ msgid "Delivery Method"
#~ msgstr "Leveringsmethode"

#~ msgid "Do not record internal moves within a warehouse"
#~ msgstr "Beveel interne mutaties niet aan in een magazijn"

#~ msgid "Followers"
#~ msgstr "Volgers"

#~ msgid "Followers (Channels)"
#~ msgstr "Volgers (Kanalen)"

#~ msgid "Followers (Partners)"
#~ msgstr "Volgers (Partners)"

#~ msgid "Forecasted:"
#~ msgstr "Virtuele voorraad:"

#~ msgid "If checked new messages require your attention."
#~ msgstr "Indien aangevinkt zullen nieuwe berichten uw aandacht vragen."

#~ msgid "If checked, new messages require your attention."
#~ msgstr "Indien aangevinkt vragen nieuwe berichten uw aandacht."

#~ msgid ""
#~ "If you need help, do not hesitate to contact our experts\n"
#~ "                        using the <span class=\"fa fa-question-circle\"/> "
#~ "button on\n"
#~ "                        the top bar."
#~ msgstr ""
#~ "Aarzel niet om onze experten te contacteren als u hulp nodig heeft.\n"
#~ "Gebruik de <span class=\"fa fa-question-circle\"/> knop in\n"
#~ "de bovenste balk."

#~ msgid "Is Follower"
#~ msgstr "Is een volger"

#~ msgid "Last Message Date"
#~ msgstr "Laatste bericht datum"

#~ msgid "Messages"
#~ msgstr "Berichten"

#~ msgid "Multi Locations"
#~ msgstr "Multi-locaties"

#~ msgid "Number of Actions"
#~ msgstr "Aantal acties"

#~ msgid "Number of messages which requires an action"
#~ msgstr "Aantal berichten die actie vereisen"

#~ msgid "Number of unread messages"
#~ msgstr "Aantal ongelezen berichten"

#~ msgid "Open Warehouse Menu"
#~ msgstr "Magazijn menu openen"

#~ msgid "Packagings"
#~ msgstr "Verpakkingen"

#~ msgid "Picking Types"
#~ msgstr "Verzameltypes"

#~ msgid "Suppliers"
#~ msgstr "Leveranciers"

#~ msgid ""
#~ "This will show you the locations and allows you to define multiple "
#~ "picking types and warehouses."
#~ msgstr ""
#~ "Dit laat u de locaties zijn en geeft u de mogelijkheid om meerdere "
#~ "leveringssoorten en magazijnen te definieren."

#~ msgid "Unit of Measures"
#~ msgstr "Maateenheden"

#~ msgid "Unread Messages"
#~ msgstr "Ongelezen berichten"

#~ msgid "Unread Messages Counter"
#~ msgstr "Teller ongelezen berichten"

#~ msgid "Use your scanner to validate the transferred quantity quicker."
#~ msgstr ""
#~ "Gebruik uw scanner om de overplaatsingshoeveelheid sneller te valideren."

#~ msgid "Website Messages"
#~ msgstr "Website berichten"

#~ msgid "Website communication history"
#~ msgstr "Website communicatie geschiedenis"

#~ msgid ""
#~ "You can either do it immediatly or mark it as Todo for future processing."
#~ msgstr ""
#~ "U kan dit of direct doen of markeren als To do voor verwerking in de "
#~ "toekomst."
