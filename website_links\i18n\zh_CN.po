# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_links
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> CHEN <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: <PERSON><PERSON> CHEN <<EMAIL>>, 2022\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links_charts.js:0
#, python-format
msgid " clicks"
msgstr "点击"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links_charts.js:0
#, python-format
msgid " countries"
msgstr "国家"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links_charts.js:0
#, python-format
msgid "# of clicks"
msgstr "# 点击"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.share_page_menu
msgid "<span title=\"Track this page to count clicks\">Link Tracker</span>"
msgstr "<span title=\"Track this page to count clicks\">跟踪链</span>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Campaign</strong>"
msgstr "<strong>活动</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Medium</strong>"
msgstr "<strong>中等</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Original URL</strong>"
msgstr "<strong>原始网址</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Redirected URL</strong>"
msgstr "<strong>重新定向网址</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Source</strong>"
msgstr "<strong>来源</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Tracked Link</strong>"
msgstr "<strong>跟踪链</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "All Time"
msgstr "一直"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Campaign <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-"
"placement=\"top\" role=\"img\" aria-label=\"Tooltip info\" title=\"Defines "
"the context of your link. It might be an event you want to promote or a "
"special promotion.\"/>"
msgstr ""
"活动 <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-"
"placement=\"top\" role=\"img\" aria-label=\"Tooltip info\" title=\"Defines "
"the context of your link. It might be an event you want to promote or a "
"special promotion.\"/>"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#, python-format
msgid "Copied"
msgstr "已复制"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#, python-format
msgid "Copy"
msgstr "复制"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#: model_terms:ir.ui.view,arch_db:website_links.graphs
#, python-format
msgid "Edit code"
msgstr "编辑代码"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#, python-format
msgid "Generating link..."
msgstr "生成链接..."

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Get tracked link"
msgstr "获取跟踪链"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#, python-format
msgid "Icon"
msgstr "图标"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "Last Month"
msgstr "上月"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "Last Week"
msgstr "上周"

#. module: website_links
#: model:ir.model,name:website_links.model_link_tracker
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "Link Tracker"
msgstr "跟踪链"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Medium <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-"
"placement=\"top\" role=\"img\" aria-label=\"Tooltip info\" title=\"Defines "
"the medium used to share your link. It might be an email, or a Facebook Ads "
"for instance.\"/>"
msgstr ""
"中等 <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-"
"placement=\"top\" role=\"img\" aria-label=\"Tooltip info\" title=\"Defines "
"the medium used to share your link. It might be an email, or a Facebook Ads "
"for instance.\"/>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Most Clicked"
msgstr "点击最多"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Newest"
msgstr "最新"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links_charts.js:0
#, python-format
msgid "No data"
msgstr "无数据"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Recently Used"
msgstr "最近使用"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Share this page with a <strong>short link</strong> that includes "
"<strong>analytics trackers</strong>."
msgstr "使用包含<strong>分析跟踪器</strong>的<strong>短链接</strong> 来分享这个页面。"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Source <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-"
"placement=\"top\" role=\"img\" aria-label=\"Tooltip info\" title=\"Defines "
"the source from which your traffic will come from, Facebook or Twitter for "
"instance.\"/>"
msgstr ""
"源 <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-"
"placement=\"top\" role=\"img\" aria-label=\"Tooltip info\" title=\"Defines "
"the source from which your traffic will come from, Facebook or Twitter for "
"instance.\"/>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
#: model_terms:ir.ui.view,arch_db:website_links.link_tracker_view_tree
msgid "Statistics"
msgstr "统计信息"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#, python-format
msgid "Stats"
msgstr "统计数据"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#: code:addons/website_links/static/src/js/website_links_code_editor.js:0
#, python-format
msgid "The code cannot be left empty"
msgstr "密码不能为空"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links_charts.js:0
#: code:addons/website_links/static/src/js/website_links_charts.js:0
#: code:addons/website_links/static/src/js/website_links_charts.js:0
#, python-format
msgid "There is no data to show"
msgstr "没有数据显示"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#: code:addons/website_links/static/src/js/website_links_code_editor.js:0
#, python-format
msgid "This code is already taken"
msgstr "该密码已被使用"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Those trackers can be used in Google Analytics to track clicks and visitors,"
" or in Odoo reports to track opportunities and related revenues."
msgstr "这些跟踪器可以被谷歌分析去跟踪点击和访问，或者在odoo的报表里跟踪机会和相关收益。"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "URL"
msgstr "URL"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#, python-format
msgid "Unable to get recent links"
msgstr "无法获取最近链接"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links_charts.js:0
#, python-format
msgid "Undefined"
msgstr "未定义的"

#. module: website_links
#: code:addons/website_links/models/link_tracker.py:0
#, python-format
msgid "Visit Webpage Statistics"
msgstr "访问网页统计"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#, python-format
msgid "You don't have any recent links."
msgstr "您没有任何最近的链接。"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Your tracked links"
msgstr "您跟踪的链接"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#: model_terms:ir.ui.view,arch_db:website_links.graphs
#, python-format
msgid "cancel"
msgstr "取消"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#, python-format
msgid "clicks"
msgstr "点击"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "copy"
msgstr "复制"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#, python-format
msgid "e.g. Newsletter, Social Network, .."
msgstr "例如：快讯、社交网络、..."

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#, python-format
msgid "e.g. Promotion of June, Winter Newsletter, .."
msgstr "例如：六月促销，冬季通讯，..."

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#, python-format
msgid "e.g. Search Engine, Website page, .."
msgstr "例如：搜索引擎，网站页面，..."

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "e.g. https://www.odoo.com/contactus"
msgstr "e.g. https://www.odoo.com/contactus"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#: model_terms:ir.ui.view,arch_db:website_links.graphs
#, python-format
msgid "ok"
msgstr "确定"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#: model_terms:ir.ui.view,arch_db:website_links.graphs
#, python-format
msgid "or"
msgstr "或"
