# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_product_configurator
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.optional_product_items
msgid "<i class=\"fa fa-shopping-cart add-optionnal-item\"/> Add to cart"
msgstr "<i class=\"fa fa-shopping-cart add-optionnal-item\"/> Dodaj v voziček"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
msgid "<span class=\"label\">Price</span>"
msgstr "<span class=\"label\">Cena</span>"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
msgid "<span class=\"label\">Product</span>"
msgstr "<span class=\"label\">Izdelek</span>"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
msgid "<span class=\"label\">Quantity</span>"
msgstr "Količina"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
msgid "<strong>Total:</strong>"
msgstr "<strong>Skupaj:</strong>"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.sale_product_configurator_view_form
msgid "Add"
msgstr "Dodaj"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.product_quantity_config
msgid "Add one"
msgstr "Dodajte ga"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_order_line__product_template_attribute_value_ids
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__product_template_attribute_value_ids
msgid "Attribute Values"
msgstr "Vrednosti atributov"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
msgid "Available Options:"
msgstr "Možnosti:"

#. module: sale_product_configurator
#. openerp-web
#: code:addons/sale_product_configurator/static/src/js/product_configurator_controller.js:0
#, python-format
msgid "Back"
msgstr "Nazaj"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.sale_product_configurator_view_form
msgid "Cancel"
msgstr "Prekliči"

#. module: sale_product_configurator
#: model:product.template,name:sale_product_configurator.product_product_1_product_template
msgid "Chair floor protection"
msgstr "Zaščita tal za stole"

#. module: sale_product_configurator
#. openerp-web
#: code:addons/sale_product_configurator/static/src/js/product_configurator_controller.js:0
#, python-format
msgid "Configure"
msgstr "Nastavi"

#. module: sale_product_configurator
#: model:ir.actions.act_window,name:sale_product_configurator.sale_product_configurator_action
msgid "Configure a product"
msgstr "Nastavi proizvod"

#. module: sale_product_configurator
#. openerp-web
#: code:addons/sale_product_configurator/static/src/js/product_configurator_controller.js:0
#, python-format
msgid "Confirm"
msgstr "Potrdi"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__create_uid
msgid "Created by"
msgstr "Ustvaril"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__create_date
msgid "Created on"
msgstr "Ustvarjeno"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__product_custom_attribute_value_ids
msgid "Custom Values"
msgstr "Vrednosti po meri"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__product_no_variant_attribute_value_ids
msgid "Extra Values"
msgstr "Dodatne vrednosti"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__id
msgid "ID"
msgstr "ID"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_order_line__is_configurable_product
msgid "Is the product configurable?"
msgstr "Ali je izdelek konfigurbilen?"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator____last_update
msgid "Last Modified on"
msgstr "Zadnjič spremenjeno"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__write_uid
msgid "Last Updated by"
msgstr "Zadnji posodobil"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__write_date
msgid "Last Updated on"
msgstr "Zadnjič posodobljeno"

#. module: sale_product_configurator
#: model:product.template,description_sale:sale_product_configurator.product_product_1_product_template
msgid "Office chairs can harm your floor: protect it."
msgstr "Pisarniški stoli lahko poškodujejo tla: zaščitite jih."

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.optional_product_items
msgid "Option not available"
msgstr "Možnost ni na voljo"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_product_product__optional_product_ids
#: model:ir.model.fields,field_description:sale_product_configurator.field_product_template__optional_product_ids
msgid "Optional Products"
msgstr "Opcijski proizvodi"

#. module: sale_product_configurator
#: model:ir.model.fields,help:sale_product_configurator.field_product_product__optional_product_ids
#: model:ir.model.fields,help:sale_product_configurator.field_product_template__optional_product_ids
msgid ""
"Optional Products are suggested whenever the customer hits *Add to Cart* "
"(cross-sell strategy, e.g. for computers: warranty, software, etc.)."
msgstr ""
"Opcijski izdelki so predlagani vsakič, ko kupec pritisne *Dodaj v košarico* "
"(strategija navzkrižne prodaje, npr. za računalnike: garancija, programska "
"oprema itd.)."

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__pricelist_id
msgid "Pricelist"
msgstr "Cenik"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__product_template_id
msgid "Product"
msgstr "Proizvod"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure_optional_products
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.optional_product_items
msgid "Product Image"
msgstr "Slika izdelka"

#. module: sale_product_configurator
#: model:ir.model,name:sale_product_configurator.model_product_template
msgid "Product Template"
msgstr "Predloga izdelka"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.sale_order_view_form
msgid "Product Variant"
msgstr "Različica proizvoda"

#. module: sale_product_configurator
#: model:ir.model.fields,field_description:sale_product_configurator.field_sale_product_configurator__quantity
msgid "Quantity"
msgstr "Količina"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.product_template_view_form
msgid "Recommend when 'Adding to Cart' or quotation"
msgstr "Priporoči pri 'dodajanju v košarico' ali ponudbi"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.product_quantity_config
msgid "Remove one"
msgstr "Odstranite enega"

#. module: sale_product_configurator
#: model:ir.model,name:sale_product_configurator.model_sale_product_configurator
msgid "Sale Product Configurator"
msgstr "Konfigurator izdelkov za prodajo"

#. module: sale_product_configurator
#: model:ir.model,name:sale_product_configurator.model_sale_order_line
msgid "Sales Order Line"
msgstr "Postavka prodajnega naloga"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.sale_product_configurator_view_form
msgid "Save"
msgstr "Shrani"

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure
msgid "This combination does not exist."
msgstr "Ta kombinacija ne obstaja."

#. module: sale_product_configurator
#: model_terms:ir.ui.view,arch_db:sale_product_configurator.configure
msgid "This product has no valid combination."
msgstr "Ta izdelek nima veljavne kombinacije."

#. module: sale_product_configurator
#: model:product.template,uom_name:sale_product_configurator.product_product_1_product_template
msgid "Units"
msgstr "Enot"
