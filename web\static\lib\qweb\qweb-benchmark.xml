<?xml version="1.0" encoding="UTF-8"?>
<templates id="template">
<t t-name="benchmark"><div id="oe_notification" class="oe_notification">
        <div id="oe_notification_default">
            <a class="ui-notify-cross ui-notify-close" href="#">x</a>
            <h1>title</h1>
            <p>text</p>
        </div>
        <div id="oe_notification_alert" class="ui-state-error">
            <a class="ui-notify-cross ui-notify-close" href="#">x</a>
            <span style="float:left; margin:2px 5px 0 0;" class="ui-icon ui-icon-alert"></span>
            <h1>title</h1>
            <p>text</p>
        </div>
    </div>
    <t t-js="d">
        d.iter = 'one,two,three,four,five'.split(',')
    </t>
    <t t-foreach="iter" t-as="i">
        <t t-call="benchmark_call">
            + <t t-esc="i"/>
        </t>
    </t>
    <t t-set="enplus">1</t>
    <t t-set="novar">true</t>
    <div t-attf-class="id_#{enplus}"/>
    <div t-if="testing || true" t-att-class="novar || 'yes'" style="display: none">
        <t t-set="novar"></t>
        <t t-set="style">height: 200px; border: 1px solid red;</t>
        <div t-att="{ 'style' : style, 'disabled' : 'false', 'readonly' : novar or undefined }"/>
        <t t-foreach="{'my': 'first', 'my2': 'second' }" t-as="v">
            * <t t-esc="v"/> : <t t-esc="v_value"/>
        </t>
        Ok this is good <t t-esc="name"/>!
        <t t-set="myvar">Hi there !</t>
        [<t t-raw="myvar"/>]
        <t t-set="myvar2" t-value="'a,b,c,d,e'.split(',')"/>
        <t t-foreach="myvar2" t-as="i">
            (<t t-esc="i"/>)
        </t>
    </div>
    <div id="oe_notification" class="oe_notification">
        <div id="oe_notification_default">
            <a class="ui-notify-cross ui-notify-close" href="#">x</a>
            <h1>title</h1>
            <p>text</p>
        </div>
    </div>
</t>
<t t-name="benchmark_call">
    <div id="oe_notification_alert" class="ui-state-error">
        <a class="ui-notify-cross ui-notify-close" href="#">x</a>
        <span style="float:left; margin:2px 5px 0 0;" class="ui-icon ui-icon-alert"></span>
        <h1>Here's your value : (<t t-esc="0"/>) !!</h1>
    </div>
</t>
</templates>

