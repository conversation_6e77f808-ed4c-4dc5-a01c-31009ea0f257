<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="res_config_settings_view_form" model="ir.ui.view">
            <field name="name">res.config.settings.view.form.inherit.hr.fleet</field>
            <field name="model">res.config.settings</field>
            <field name="priority" eval="90"/>
            <field name="inherit_id" ref="base.res_config_settings_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//div[hasclass('settings')]" position="inside">
                    <div class="app_settings_block" data-string="Fleet" id="fleet" string="Fleet" data-key="fleet" groups="fleet.fleet_group_manager">
                        <h2>Fleet Management</h2>
                        <div class="row mt16 o_settings_container" id="end_contract_setting">
                            <div class="col-12 col-lg-6 o_setting_box">
                                <div class="o_setting_left_pane"/>
                                <div class="o_setting_right_pane">
                                    <span class="o_form_label">End Date Contract Alert</span>
                                    <div class="text-muted content-group mt16">
                                        <span>Send an alert </span>
                                        <field name="delay_alert_contract" class="text-center oe_inline" />
                                        <span> days before the end date</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </xpath>
            </field>
        </record>

        <record id="fleet_config_settings_action" model="ir.actions.act_window">
            <field name="name">Settings</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">res.config.settings</field>
            <field name="view_mode">form</field>
            <field name="target">inline</field>
            <field name="context">{'module' : 'fleet', 'bin_size': False}</field>
        </record>

        <menuitem id="fleet_config_settings_menu" name="Settings"
            parent="fleet.fleet_configuration" sequence="0" action="fleet_config_settings_action"
            groups="base.group_system"/>
    </data>
</odoo>
