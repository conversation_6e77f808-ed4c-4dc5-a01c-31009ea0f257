<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="ir_cron_twitter_actions" model="ir.cron">
        <field name="name">Twitter: Fetch new favorites</field>
        <field name="model_id" ref="model_website"/>
        <field name="state">code</field>
        <field name="code">model._refresh_favorite_tweets()</field>
        <field name="interval_number">2</field>
        <field name="interval_type">hours</field>
        <field name="numbercall">-1</field>
        <field name="doall" eval="False"/>
    </record>
</odoo>

