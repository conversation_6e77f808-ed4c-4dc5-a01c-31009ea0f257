# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# Dung <PERSON><PERSON>hi <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>le Collart <<EMAIL>>, 2021
# Vo Thanh Thuy, 2022
# Tr<PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# Wil Odoo, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-10 06:07+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: Thi Huong Nguyen, 2023\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_image_gallery/options.js:0
#, python-format
msgid " Add Images"
msgstr " Thêm hình"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "\" alert with a"
msgstr "\" kèm với "

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL from\" can not be empty."
msgstr "\"URL từ\" không thể trống."

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" can not be empty."
msgstr "\"URL tới\" không thể trống."

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" cannot contain parameter %s which is not used in \"URL from\"."
msgstr "\"URL tới\" không thể chứa biến %s không được sử dụng trong \"URL từ\". "

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" is invalid: %s"
msgstr "\"URL tới\" không hợp lệ: %s"

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" must contain parameter %s used in \"URL from\"."
msgstr "\"URL tới\" phải chứa biến %s được sử dụng trong \"URL từ\". "

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" must start with a leading slash."
msgstr "\"URL tới\" phải bắt đầu bằng dấu gạch chéo ở đầu."

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__page_count
msgid "# Visited Pages"
msgstr "# Trang đã truy cập"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__visit_count
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "# Visits"
msgstr "# Lượt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$10.50"
msgstr "242,000đ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$12.00"
msgstr "288,000đ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$15.50"
msgstr "360,000đ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$7.50"
msgstr "175,000đ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$9.00"
msgstr "200,000đ"

#. module: website
#: code:addons/website/models/website.py:0
#: code:addons/website/models/website.py:0
#, python-format
msgid "%s (id:%s)"
msgstr "%s (id:%s)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "&amp;lt;/body&amp;gt;"
msgstr "&amp;lt;/body&amp;gt;"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "&amp;lt;head&amp;gt;"
msgstr "&amp;lt;head&amp;gt;"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "&gt;"
msgstr "&gt;"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "&nbsp;"
msgstr "&nbsp;"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
msgid "' did not match any pages."
msgstr "' không khớp với bất kỳ trang nào."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
msgid "' did not match anything."
msgstr "' không khớp. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
msgid ""
"' did not match anything.\n"
"                        Results are displayed for '"
msgstr ""
"' không khớp.\n"
"                        Hiển thị kết quả cho '"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "' to link to an anchor."
msgstr "' để liên kết với một neo. "

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"' to search a page.\n"
"                    '"
msgstr ""
"' để tìm kiếm trang.\n"
"                    '"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "'%s' is not a correct date"
msgstr "'%s' không phải là ngày đúng"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "'%s' is not a correct datetime"
msgstr "'%s' không phải là ngày giờ đúng"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_searchbar/000.xml:0
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
#, python-format
msgid "'. Showing results for '"
msgstr "'. Đang hiện kết quả cho '"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "(could be used in"
msgstr "(có thể dùng trong"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.res_config_settings.xml:0
#, python-format
msgid ""
"(see e.g. Opinion\n"
"            04/2012 on Cookie Consent Exemption by the EU Art.29 WP)."
msgstr ""
"(xem v.d. Opinion\n"
"            04/2012 on Cookie Consent Exemption của EU Art.29 WP)."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "+ Field"
msgstr "+ Trường"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_header_contact_oe_structure_header_contact_1
#: model_terms:ir.ui.view,arch_db:website.template_header_hamburger_oe_structure_header_hamburger_3
#: model_terms:ir.ui.view,arch_db:website.template_header_sidebar_oe_structure_header_sidebar_1
#: model_terms:ir.ui.view,arch_db:website.template_header_vertical_oe_structure_header_vertical_2
msgid "+1 (650) 555-0111"
msgstr "+1 (650) 555-0111"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid ", .s_searchbar_input"
msgstr ", .s_searchbar_input"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid ", .s_website_form"
msgstr ", .s_website_form"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid ", author:"
msgstr ", tác giả:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
".\n"
"                                                The website will still work if you reject or discard those cookies."
msgstr ""
".\n"
"                                                Trang web sẽ vẫn hoạt động nếu bạn từ chối hoặc hủy các cookie đó."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid ""
".\n"
"            Changing its name will break these calls."
msgstr ""
".\n"
"            Thay đổi tên của nó sẽ phá vỡ các liên kết này."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "...and switch the timeline contents to fit your needs."
msgstr "...và chuyển đổi nội dung dòng thời gian cho phù hợp với nhu cầu. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "/contactus"
msgstr "/contactus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "1 km"
msgstr "1 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "1/2 - 1/2"
msgstr "1/2 - 1/2"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "1/3 - 2/3"
msgstr "1/3 - 2/3"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "1/4 - 3/4"
msgstr "1/4 - 3/4"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "10 m"
msgstr "10 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "100 km"
msgstr "100 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "100 m"
msgstr "100 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "100%"
msgstr "100%"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "1000 km"
msgstr "1000 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "12"
msgstr "12"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "15 km"
msgstr "15 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "2 <span class=\"sr-only\">(current)</span>"
msgstr "2 <span class=\"sr-only\">(hiện tại)</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "2 km"
msgstr "2 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "2.5 m"
msgstr "2.5 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "20 m"
msgstr "20 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "200 km"
msgstr "200 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "200 m"
msgstr "200 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "2000 km"
msgstr "2000 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "24x7 toll-free support"
msgstr "hỗ trợ gọi miễn phí 24/7"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "25%"
msgstr "25%"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid ""
"250 Executive Park Blvd, Suite 3400 <br/> San Francisco CA 94134 <br/>United"
" States"
msgstr ""
"250 Executive Park Blvd, Suite 3400 <br/> San Francisco CA 94134 <br/>United"
" States"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
msgid ""
"250 Executive Park Blvd, Suite 3400 • San Francisco CA 94134 • United States"
msgstr ""
"250 Executive Park Blvd, Suite 3400 • San Francisco CA 94134 • United States"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "30 km"
msgstr "30 km"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__301
#, python-format
msgid "301 Moved permanently"
msgstr "301 Đã chuyển vĩnh viễn"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__302
#, python-format
msgid "302 Moved temporarily"
msgstr "302 Đã chuyển tạm thời"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__308
msgid "308 Redirect / Rewrite"
msgstr "308 Chuyển hướng / Viết lại"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "4 km"
msgstr "4 km"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "4 steps"
msgstr "4 bước"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "400 km"
msgstr "400 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "400 m"
msgstr "400 m"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__404
msgid "404 Not Found"
msgstr "404 Không tìm thấy"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "5 m"
msgstr "5 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "50 km"
msgstr "50 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "50 m"
msgstr "50 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "50%"
msgstr "50%"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
msgid "50,000+ companies run Odoo to grow their businesses."
msgstr "Hơn 50.000 công ty sử dụng Odoo để phát triển doanh nghiệp. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "75%"
msgstr "75%"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "8 km"
msgstr "8 km"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_progress_bar/options.js:0
#, python-format
msgid "80% Development"
msgstr "Phát triển 80%"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action
msgid "<b>50,000+ companies</b> run Odoo to grow their businesses."
msgstr "<b>Hơn 50,000 công ty</b> dùng Odoo để tăng trưởng kinh doanh. "

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Add</b> the selected image."
msgstr "<b>Thêm</b> ảnh đã chọn."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click Edit</b> to start designing your homepage."
msgstr "<b>Bấm Sửa</b> để bắt đầu thiết kế trang chủ. "

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click on a snippet</b> to access its options menu."
msgstr "<b>Nhấp vào đoạn trích</b> để truy cập menu tùy chọn. "

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click on a text</b> to start editing it."
msgstr "<b>Bấm vào văn bản</b> để bắt đầu sửa. "

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click</b> on this column to access its options."
msgstr "<b>Bấm</b> vào cột này để truy cập các tùy chọn. "

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click</b> on this header to configure it."
msgstr "<b>Bấm</b> vào tiêu đề này để định cấu hình. "

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click</b> on this option to change the %s of the block."
msgstr "<b>Bấm</b> vào tùy chọn này để thay đổi %s của khối. "

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid ""
"<b>Customize</b> any block through this menu. Try to change the background "
"color of this block."
msgstr ""
"<b>Tùy chỉnh</b> bất kỳ khối nào thông qua menu này. Thử thay đổi màu nền "
"của khối này."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid ""
"<b>Customize</b> any block through this menu. Try to change the background "
"image of this block."
msgstr ""
"<b>Tùy chỉnh</b> bất kỳ khối nào thông qua menu này. Thử thay đổi hình nền "
"của khối này."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
msgid "<b>Designed</b> <br/>for Companies"
msgstr "<b>Được thiết kế</b> <br/>cho các công ty"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
msgid "<b>Designed</b> for companies"
msgstr "<b>Được thiết kế</b> cho các công ty"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Double click on an icon</b> to change it with one of your choice."
msgstr ""
"<b>Bấm đúp vào một biểu tượng</b> để thay thế bằng một lựa chọn khác. "

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Double click on an image</b> to change it with one of your choice."
msgstr "<b>Bấm đúp vào một hình ảnh</b> để thay thế bằng một lựa chọn khác. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
msgid ""
"<b>My Company</b><br/>250 Executive Park Blvd, Suite 3400 <br/> San "
"Francisco CA 94134 <br/>United States"
msgstr ""
"<b>My Company</b><br/>250 Executive Park Blvd, Suite 3400 <br/> San "
"Francisco CA 94134 <br/>United States"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Select</b> a %s."
msgstr "<b>Chọn</b> %s."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Select</b> a Color Palette."
msgstr "<b>Chọn</b> một bảng màu. "

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Slide</b> this button to change the %s padding"
msgstr "<b>Trượt</b> nút này để thay đổi đệm %s "

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Slide</b> this button to change the column size."
msgstr "<b>Trượt</b> nút này để thay đổi kích thước cột. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_edit_robots
msgid ""
"<br/><br/>\n"
"                    Example of rule:<br/>"
msgstr ""
"<br/><br/>\n"
"                    Quy tắc ví dụ:<br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"<font style=\"background-color: rgb(255, 255, 255);\">Good writing is "
"simple, but not simplistic.</font>"
msgstr ""
"<font style=\"background-color: rgb(255, 255, 255);\"> Bài viết tốt phải đơn"
" giản nhưng không đơn điệu. </font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<font style=\"font-size: 14px;\">Created in 2021, the company is young and "
"dynamic. Discover the composition of the team and their skills.</font>"
msgstr ""
"<font style=\"font-size: 14px;\">Được thành lập năm 2021, chúng tôi là một "
"công ty trẻ trung và năng động. Khám phá thành viên các nhóm và kỹ năng của "
"họ.</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"<font style=\"font-size: 62px; background-color: rgb(255, 255, 255);\">Edit "
"this title</font>"
msgstr ""
"<font style=\"font-size: 62px; background-color: rgb(255, 255, 255);\">Sửa "
"tiêu đề</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cover
msgid "<font style=\"font-size: 62px; font-weight: bold;\">Catchy Headline</font>"
msgstr ""
"<font style=\"font-size: 62px; font-weight: bold;\">Tiêu đề hấp dẫn</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_picture
msgid "<font style=\"font-size: 62px;\">A punchy Headline</font>"
msgstr "<font style=\"font-size: 62px;\">Tiêu đề cuốn hút </font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_banner
msgid "<font style=\"font-size: 62px;\">Sell Online. Easily.</font>"
msgstr "<font style=\"font-size: 62px;\">Bán trực tuyến. Thật dễ dàng.</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "<font style=\"font-size: 62px;\">Slide Title</font>"
msgstr "<font style=\"font-size: 62px;\">Tiêu đề slide</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup
msgid "<font style=\"font-size: 62px;\">Win $20</font>"
msgstr "<font style=\"font-size: 62px;\">Nhận được 400,000đ</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_title
msgid "<font style=\"font-size: 62px;\">Your Site Title</font>"
msgstr "<font style=\"font-size: 62px;\">Tên trang web</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card
msgid "<i class=\"fa fa-1x fa-clock-o mr8\"/><small>2 days ago</small>"
msgstr "<i class=\"fa fa-1x fa-clock-o mr8\"/><small>2 ngày trước</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_page
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid ""
"<i class=\"fa fa-1x fa-fw fa-envelope "
"mr-2\"/><span><EMAIL></span>"
msgstr ""
"<i class=\"fa fa-1x fa-fw fa-envelope "
"mr-2\"/><span><EMAIL></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
msgid ""
"<i class=\"fa fa-1x fa-fw fa-map-marker mr-2\"/>250 Executive Park Blvd, "
"Suite 3400 • San Francisco CA 94134 • United States"
msgstr ""
"<i class=\"fa fa-1x fa-fw fa-map-marker mr-2\"/>250 Executive Park Blvd, "
"Suite 3400 • San Francisco CA 94134 • United States"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            Create a Google Project and Get a Key"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            Tạo một dự án Google và nhận một khóa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            Enable billing on your Google Project"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            Bật thanh toán trên Google Project "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            How to get my Client ID"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            Cách lấy ID người dùng "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            How to get my Measurement ID"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            Cách lấy Measurement ID"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.report_viewhierarchy_children
msgid "<i class=\"fa fa-eye ml-2 text-muted\" title=\"Go to View\"/>"
msgstr "<i class=\"fa fa-eye ml-2 text-muted\" title=\"Đi tới Xem\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.report_viewhierarchy_children
msgid "<i class=\"fa fa-files-o ml-2 text-muted\" title=\"Show Arch Diff\"/>"
msgstr ""
"<i class=\"fa fa-files-o ml-2 text-muted\" title=\"Hiện chênh lệch kiến "
"trúc\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-circle\"/> Circles"
msgstr "<i class=\"fa fa-fw fa-circle\"/> Tròn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-heart\"/> Hearts"
msgstr "<i class=\"fa fa-fw fa-heart\"/> Tim"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-refresh mr-1\"/> Replace Icon"
msgstr "<i class=\"fa fa-fw fa-refresh mr-1\"/> Thay icon"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-square\"/> Squares"
msgstr "<i class=\"fa fa-fw fa-square\"/> Vuông"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-star\"/> Stars"
msgstr "<i class=\"fa fa-fw fa-star\"/> Sao"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-thumbs-up\"/> Thumbs"
msgstr "<i class=\"fa fa-fw fa-thumbs-up\"/> Ngón tay"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-circle text-danger\"/>\n"
"                            <span>Offline</span>"
msgstr ""
"<i class=\"fa fa-fw o_button_icon fa-circle text-danger\"/>\n"
"                            <span>Offline</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-circle text-success\"/>\n"
"                            <span>Connected</span>"
msgstr ""
"<i class=\"fa fa-fw o_button_icon fa-circle text-success\"/>\n"
"                            <span>Đã kết nối</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid ""
"<i class=\"fa fa-info-circle\"/> Edit the content below this line to adapt "
"the default <strong>Page not found</strong> page."
msgstr ""
"<i class=\"fa fa-info-circle\"/> Sửa nội dung phía dưới dòng này để điều "
"chỉnh mặc định trang <strong>Không tìm thấy trang.</strong>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.protected_403
msgid ""
"<i class=\"fa fa-lock fa-2x\"/><br/>\n"
"                            <span class=\"mt-1\">A password is required to access this page.</span>"
msgstr ""
"<i class=\"fa fa-lock fa-2x\"/><br/>\n"
"                            <span class=\"mt-1\">Cần mật khẩu để truy cập vào trang này.</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.protected_403
msgid ""
"<i class=\"fa fa-lock fa-2x\"/><br/>\n"
"                            <span class=\"mt-1\">Wrong password</span>"
msgstr ""
"<i class=\"fa fa-lock fa-2x\"/><br/>\n"
"                            <span class=\"mt-1\">Sai mật khẩu</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_page
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid ""
"<i class=\"fa fa-map-marker fa-fw mr-2\"/><span "
"class=\"o_force_ltr\">Chaussée de Namur 40</span>"
msgstr ""
"<i class=\"fa fa-map-marker fa-fw mr-2\"/><span "
"class=\"o_force_ltr\">Chaussée de Namur 40</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_page
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid ""
"<i class=\"fa fa-phone fa-fw mr-2\"/><span class=\"o_force_ltr\">+ 32 81 81 "
"37 00</span>"
msgstr ""
"<i class=\"fa fa-phone fa-fw mr-2\"/><span class=\"o_force_ltr\">+ 32 81 81 "
"37 00</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.language_selector_add_language
msgid ""
"<i class=\"fa fa-plus-circle\"/>\n"
"        <span>Add a language...</span>"
msgstr ""
"<i class=\"fa fa-plus-circle\"/>\n"
"        <span>Thêm một ngôn ngữ...</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "<i class=\"fa fa-th-large mr-2\"/> WEBSITE"
msgstr "<i class=\"fa fa-th-large mr-2\"/> WEBSITE"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-calendar fa-fw mr-2\"/>\n"
"                            <b>Events</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-calendar fa-fw mr-2\"/>\n"
"                            <b>Sự kiện</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-eye fa-fw mr-2\"/>\n"
"                            <b>About us</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-eye fa-fw mr-2\"/>\n"
"                            <b>Giới thiệu</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-group fa-fw mr-2\"/>\n"
"                            <b>Partners</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-group fa-fw mr-2\"/>\n"
"                            <b>Đối tác</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-handshake-o fa-fw mr-2\"/>\n"
"                            <b>Services</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-handshake-o fa-fw mr-2\"/>\n"
"                            <b>Dịch vụ</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-headphones fa-fw mr-2\"/>\n"
"                            <b>Help center</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-headphones fa-fw mr-2\"/>\n"
"                            <b>Trung tâm hỗ trợ</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-map-o fa-fw mr-2\"/>\n"
"                            <b>Guides</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-map-o fa-fw mr-2\"/>\n"
"                            <b>Hướng dẫn</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-newspaper-o fa-fw mr-2\"/>\n"
"                            <b>Our blog</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-newspaper-o fa-fw mr-2\"/>\n"
"                            <b>Blog</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-star-o fa-fw mr-2\"/>\n"
"                            <b>Customers</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-star-o fa-fw mr-2\"/>\n"
"                            <b>Khách hàng</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-tags fa-fw mr-2\"/>\n"
"                            <b>Products</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-tags fa-fw mr-2\"/>\n"
"                            <b>Sản phẩm</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "<i class=\"s_mega_menu_thumbnails_icon fa fa-comments mr-2\"/> Contact us"
msgstr "<i class=\"s_mega_menu_thumbnails_icon fa fa-comments mr-2\"/> Liên hệ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "<i class=\"s_mega_menu_thumbnails_icon fa fa-cube mr-2\"/> Free returns"
msgstr ""
"<i class=\"s_mega_menu_thumbnails_icon fa fa-cube mr-2\"/> Miễn phí trả lại"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid ""
"<i class=\"s_mega_menu_thumbnails_icon fa fa-shopping-basket mr-2\"/> Pickup"
" in store"
msgstr ""
"<i class=\"s_mega_menu_thumbnails_icon fa fa-shopping-basket mr-2\"/> Nhận "
"tại cửa hàng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid ""
"<i class=\"s_mega_menu_thumbnails_icon fa fa-truck mr-2\"/> Express delivery"
msgstr ""
"<i class=\"s_mega_menu_thumbnails_icon fa fa-truck mr-2\"/> Giao hàng nhanh"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "<i title=\"Is the page SEO optimized?\" class=\"fa fa-search\"/>"
msgstr "<i title=\"Trang này có tối ưu hóa SEO không? \" class=\"fa fa-search\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid ""
"<i title=\"Is the page included in the main menu?\" class=\"fa fa-thumb-"
"tack\"/>"
msgstr ""
"<i title=\"Trang này có nằm trong menu chính không?\" class=\"fa fa-thumb-"
"tack\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "<i title=\"Is the page indexed by search engines?\" class=\"fa fa-globe\"/>"
msgstr ""
"<i title=\"Trang này có được công cụ tìm kiếm lập chỉ mục không? \" "
"class=\"fa fa-globe\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "<i title=\"Is the page published?\" class=\"fa fa-eye\"/>"
msgstr "<i title=\"Trang này có được đăng không? \" class=\"fa fa-eye\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<i>Instant setup, satisfied or reimbursed.</i>"
msgstr "<i>Cài đặt ngay. Hoàn tiền nếu không hài lòng.</i>"

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "<p>Attached files : </p>"
msgstr "<p>Tệp đính kèm: </p>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_header_magazine_oe_structure_header_magazine_1
msgid "<small class=\"s_share_title d-none\"><b>Follow us</b></small>"
msgstr "<small class=\"s_share_title d-none\"><b>Theo dõi chúng tôi</b></small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_header_contact_oe_structure_header_contact_1
msgid "<small class=\"s_share_title text-muted d-none\"><b>Follow us</b></small>"
msgstr "<small class=\"s_share_title text-muted d-none\"><b>Theo dõi</b></small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<small class=\"text-muted\">\n"
"                                            <i class=\"fa fa-info\"/>: type some of the first chars after 'google' is enough, we'll guess the rest.\n"
"                                        </small>"
msgstr ""
"<small class=\"text-muted\">\n"
"                                            <i class=\"fa fa-info\"/>: gõ một vài ký tự đầu sau 'google' là đủ, chúng tôi sẽ đoán phần còn lại.\n"
"                                        </small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid ""
"<small id=\"emailHelp\" class=\"form-text text-muted\">Form field help "
"text</small>"
msgstr ""
"<small id=\"emailHelp\" class=\"form-text text-muted\">Hướng dẫn trường văn "
"bản</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid ""
"<small id=\"emailHelp\" class=\"form-text text-muted\">We'll never share "
"your email with anyone else.</small>"
msgstr ""
"<small id=\"emailHelp\" class=\"form-text text-muted\">Chúng tôi sẽ không "
"bao giờ chia sẻ email của bạn với bất cứ ai khác.</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<small>/ month</small>"
msgstr "<small>/ tháng</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "<small>TABS</small>"
msgstr "<small>TAB</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_header_magazine_oe_structure_header_magazine_1
msgid "<small>We help you grow your business</small>"
msgstr "<small>Chúng tôi giúp bạn phát triển doanh nghiệp</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "<span class=\"bg-white\"><b>2015</b></span>"
msgstr "<span class=\"bg-white\"><b>2015</b></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "<span class=\"bg-white\"><b>2018</b></span>"
msgstr "<span class=\"bg-white\"><b>2018</b></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "<span class=\"bg-white\"><b>2019</b></span>"
msgstr "<span class=\"bg-white\"><b>2019</b></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                <span class=\"sr-only\">Next</span>"
msgstr ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                <span class=\"sr-only\">Tiếp</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                <span class=\"sr-only\">Previous</span>"
msgstr ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                <span class=\"sr-only\">Trước</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid ""
"<span class=\"d-block p-2\">\n"
"                            <b><font style=\"font-size:14px;\">Discover our new products</font></b>\n"
"                        </span>"
msgstr ""
"<span class=\"d-block p-2\">\n"
"                            <b><font style=\"font-size:14px;\">Khám phá sản phẩm mới</font></b>\n"
"                        </span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid ""
"<span class=\"fa fa-check-circle\"/>\n"
"                                            <span>Your message has been sent <b>successfully</b></span>"
msgstr ""
"<span class=\"fa fa-check-circle\"/>\n"
"                                            <span>Tin nhắn của bạn đã được gửi <b>thành công</b></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
msgid ""
"<span class=\"fa fa-chevron-left fa-2x text-white\"/>\n"
"                    <span class=\"sr-only\">Previous</span>"
msgstr ""
"<span class=\"fa fa-chevron-left fa-2x text-white\"/>\n"
"                    <span class=\"sr-only\">Trước</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
msgid ""
"<span class=\"fa fa-chevron-right fa-2x text-white\"/>\n"
"                    <span class=\"sr-only\">Next</span>"
msgstr ""
"<span class=\"fa fa-chevron-right fa-2x text-white\"/>\n"
"                    <span class=\"sr-only\">Tiếp</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-globe\" title=\"Giá trị đặt tại đây áp dụng cho "
"website cụ thể\" groups=\"website.group_multi_website\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "<span class=\"fa fa-pencil mr-2\"/>Edit"
msgstr "<span class=\"fa fa-pencil mr-2\"/>Sửa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "<span class=\"fa fa-plus mr-2\"/>New"
msgstr "<span class=\"fa fa-plus mr-2\"/>Mới"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "<span class=\"fa fa-sort fa-lg\" role=\"img\" aria-label=\"Sort\" title=\"Sort\"/>"
msgstr ""
"<span class=\"fa fa-sort fa-lg\" role=\"img\" aria-label=\"Sort\" "
"title=\"Phân loại \"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.option_header_off_canvas
msgid "<span class=\"fa-2x\">×</span>"
msgstr "<span class=\"fa-2x\">×</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.language_selector_inline
msgid "<span class=\"list-inline-item\">|</span>"
msgstr "<span class=\"list-inline-item\">|</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<span class=\"mx-2\">/</span>"
msgstr "<span class=\"mx-2\">/</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.language_selector_inline
msgid ""
"<span class=\"o_add_language list-inline-item\" "
"groups=\"website.group_website_publisher\">|</span>"
msgstr ""
"<span class=\"o_add_language list-inline-item\" "
"groups=\"website.group_website_publisher\">|</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_copyright_company_name
msgid ""
"<span class=\"o_footer_copyright_name mr-2\">Copyright &amp;copy; Company "
"name</span>"
msgstr ""
"<span class=\"o_footer_copyright_name mr-2\">Bản quyền &amp;copy; Tên công "
"ty</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Robots.txt</span>\n"
"                                    <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"o_form_label\">Robots.txt</span>\n"
"                                    <span class=\"fa fa-lg fa-globe\" title=\"Giá trị đặt tại đây áp dụng cho website cụ thể\" groups=\"website.group_multi_website\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Sitemap</span>\n"
"                                    <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"o_form_label\">Sitemap</span>\n"
"                                    <span class=\"fa fa-lg fa-globe\" title=\"Giá trị đặt tại đây áp dụng cho website cụ thể\" groups=\"website.group_multi_website\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_badge
msgid ""
"<span class=\"s_badge badge badge-secondary o_animable\" data-name=\"Badge\">\n"
"        <i class=\"fa fa-1x fa-fw fa-folder o_not-animable\"/>Category\n"
"    </span>"
msgstr ""
"<span class=\"s_badge badge badge-secondary o_animable\" data-name=\"Huy hiệu\">\n"
"        <i class=\"fa fa-1x fa-fw fa-folder o_not-animable\"/>Danh mục\n"
"    </span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"s_blockquote_author\"><b>Iris DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>Iris DOE</b> • CEO của "
"MyCompany</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"s_blockquote_author\"><b>Jane DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>Jane DOE</b> • CEO của "
"MyCompany</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"s_blockquote_author\"><b>John DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>John DOE</b> • CEO của "
"MyCompany</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>125</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"
msgstr ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>125</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>35</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"
msgstr ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>35</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>65</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"
msgstr ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>65</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "<span class=\"s_number display-4\">12</span><br/>"
msgstr "<span class=\"s_number display-4\">12</span><br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "<span class=\"s_number display-4\">37</span><br/>"
msgstr "<span class=\"s_number display-4\">37</span><br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "<span class=\"s_number display-4\">45</span><br/>"
msgstr "<span class=\"s_number display-4\">45</span><br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "<span class=\"s_number display-4\">8</span><br/>"
msgstr "<span class=\"s_number display-4\">8</span><br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar
msgid "<span class=\"s_progress_bar_text\">80% Development</span>"
msgstr "<span class=\"s_progress_bar_text\">Phát triển 80%</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid "<span class=\"s_website_form_label_content\">Email To</span>"
msgstr "<span class=\"s_website_form_label_content\">Email tới</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid "<span class=\"s_website_form_label_content\">Phone Number</span>"
msgstr "<span class=\"s_website_form_label_content\">Số điện thoại</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid ""
"<span class=\"s_website_form_label_content\">Subject</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Tiêu đề</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid "<span class=\"s_website_form_label_content\">Your Company</span>"
msgstr "<span class=\"s_website_form_label_content\">Công ty của bạn</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Email của bạn</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Tên của bạn</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid "<span class=\"s_website_form_label_content\">Your Question</span>"
msgstr "<span class=\"s_website_form_label_content\">Câu hỏi</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "<span class=\"sr-only\">Toggle Dropdown</span>"
msgstr "<span class=\"sr-only\">Chuyển đổi thả xuống</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid ""
"<span title=\"Mobile preview\" role=\"img\" aria-label=\"Mobile preview\" "
"class=\"fa fa-mobile\"/>"
msgstr ""
"<span title=\"Xem bản di động\" role=\"img\" aria-label=\"Mobile preview\" "
"class=\"fa fa-mobile\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_publisher
msgid ""
"<span/>\n"
"                    <span class=\"css_publish\">Unpublished</span>\n"
"                    <span class=\"css_unpublish\">Published</span>"
msgstr ""
"<span/>\n"
"                    <span class=\"css_publish\">Không đăng</span>\n"
"                    <span class=\"css_unpublish\">Đã đăng</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "<span>Theme</span>"
msgstr "<span>Chủ đề</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"A CDN helps you serve your website’s content with high availability and high"
" performance to any visitor wherever they are located."
msgstr ""
"CDN giúp bạn tải nhanh chóng và hiển thị đầy đủ nội dung website cho bất kỳ "
"khách truy cập nào ở bất cứ nơi nào."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart
msgid "A Chart Title"
msgstr "Tiêu đề biểu đồ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"A Google Map error occurred. Make sure to read the key configuration popup "
"carefully."
msgstr "Đã xảy ra lỗi Google Map. Hãy chắc chắn đọc kỹ pop-up cấu hình chính."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "A Section Subtitle"
msgstr "Tiêu đề mục"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card
msgid ""
"A card is a flexible and extensible content container. It includes options "
"for headers and footers, a wide variety of content, contextual background "
"colors, and powerful display options."
msgstr ""
"Thẻ là hình thức trình bày chứa nội dung linh hoạt và có thể mở rộng. Hình "
"thức này có đủ các tuỳ chọn cho tiêu đề và chân trang, nội dung đa dạng, màu"
" nền theo ngữ cảnh và các tuỳ chọn hiển thị mạnh mẽ. "

#. module: website
#: model:ir.model.fields,help:website.field_ir_actions_server__website_published
#: model:ir.model.fields,help:website.field_ir_cron__website_published
msgid ""
"A code server action can be executed from the website, using a dedicated "
"controller. The address is <base>/website/action/<website_path>. Set this "
"field as True to allow users to run this action. If it is set to False the "
"action cannot be run through the website."
msgstr ""
"Một tác vụ máy chủ mã có thể được thực hiện từ trang web, sử dụng một bộ "
"điều khiển chuyên dụng. Địa chỉ là <base>/website/action/<website_path>. Đặt"
" trường này là \"True\" để cho phép người dùng chạy tác vụ này. Nếu nó được "
"đặt thành \"False\" thì tác vụ không thể thực hiện trên trang web."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_color_blocks_2
msgid "A color block"
msgstr "Một khối màu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_image_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_default_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_image_texts_image_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_mosaic_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_reversed_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_texts_image_texts_template
msgid "A great title"
msgstr "Một tiêu đề hay"

#. module: website
#: model:ir.model.fields,help:website.field_website_snippet_filter__field_names
msgid "A list of comma-separated field names"
msgstr "Danh sách tên trường ngăn cách bằng dấu phẩy"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_stores_locator
msgid "A map and a listing of your stores"
msgstr "Bản đồ và danh sách cửa hàng"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__visit_count
msgid ""
"A new visit is considered if last connection was more than 8 hours ago."
msgstr ""
"Sẽ coi là một lượt truy cập mới nếu kết nối lần cuối cách đây hơn 8 giờ."

#. module: website
#: model:ir.model.constraint,message:website.constraint_website_visitor_partner_uniq
msgid "A partner is linked to only one visitor."
msgstr "Một đối tác chỉ được liên kết với một khách truy cập."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "A short description of this great feature."
msgstr "Đoạn mô tả ngắn về tính năng này. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "A small explanation of this great <br/>feature, in clear words."
msgstr "Một lời giải thích nhỏ và rõ ràng <br/>về tính năng tuyệt vời này. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid ""
"A timeline is a graphical representation on which important events are "
"marked."
msgstr "Dòng thời gian là đồ họa đánh dấu các mốc sự kiện quan trọng. "

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__is_connected
msgid ""
"A visitor is considered as connected if his last page view was within the "
"last 5 minutes."
msgstr ""
"Một khách truy cập được coi là đã kết nối nếu lần xem trang cuối cùng của "
"anh ta là trong vòng 5 phút trước."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#, python-format
msgid "API Key"
msgstr "Khóa API"

#. module: website
#: model:website.configurator.feature,name:website.feature_page_about_us
msgid "About Us"
msgstr "Giới thiệu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.aboutus
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "About us"
msgstr "Giới thiệu"

#. module: website
#: code:addons/website/controllers/backend.py:0
#, python-format
msgid "Access Error"
msgstr "Lỗi truy cập"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__access_token
msgid "Access Token"
msgstr "Token truy cập"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.protected_403
msgid "Access to this page"
msgstr "Truy cập vào trang này"

#. module: website
#: model:ir.model.constraint,message:website.constraint_website_visitor_access_token_unique
msgid "Access token should be unique."
msgstr "Token truy cập phải là duy nhất."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Accessories"
msgstr "Phụ kiện"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Account &amp; Sales management"
msgstr "Kế toán &amp; Quản lý bán hàng"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__redirect_type
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Action"
msgstr "Tác vụ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/res_config_settings.js:0
#, python-format
msgid "Activate anyway"
msgstr "Vẫn kích hoạt"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__active
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__active
#: model:ir.model.fields,field_description:website.field_website_page__active
#: model:ir.model.fields,field_description:website.field_website_rewrite__active
#: model:ir.model.fields,field_description:website.field_website_visitor__active
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Active"
msgstr "Có hiệu lực"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"Adapt these three columns to fit your design need. To duplicate, delete or "
"move columns, select the column and use the top icons to perform your "
"action."
msgstr ""
"Điều chỉnh ba cột này để phù hợp với nhu cầu thiết kế của bạn. Để sao chép, "
"xóa hoặc di chuyển cột, hãy chọn cột và sử dụng các biểu tượng trên cùng để "
"thực hiện tác vụ. "

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/xml/website.seo.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#, python-format
msgid "Add"
msgstr "Thêm"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Add Features"
msgstr "Thêm tính năng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Add Item"
msgstr "Thêm mục"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "Add Media"
msgstr "Thêm phương tiện"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Add Mega Menu Item"
msgstr "Thêm mục Mega Menu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Add Menu Item"
msgstr "Thêm mục menu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog_add_product_widget
msgid "Add Product"
msgstr "Thêm sản phẩm"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Add Row"
msgstr "Thêm dòng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Add Serie"
msgstr "Thêm cột"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Add Slide"
msgstr "Thêm slide"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Add Tab"
msgstr "Thêm tab"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline_options
msgid "Add Year"
msgstr "Thêm năm"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Add a Google Font"
msgstr "Thêm Google Font"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_picture
msgid "Add a caption to enhance the meaning of this image."
msgstr "Thêm chú thích để biểu đạt thêm ý nghĩa cho hình ảnh. "

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_product_catalog/options.js:0
#, python-format
msgid "Add a description here"
msgstr "Thêm mô tả ở đây"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Add a great slogan."
msgstr "Thêm slogan hiệu quả. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Add a menu description."
msgstr "Thêm mô tả thực đơn."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Add a menu item"
msgstr "Thêm một mục menu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Add a new field after this one"
msgstr "Thêm trường mới sau trường này"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Add a new field at the end"
msgstr "Thêm trường mới vào cuối"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Add features"
msgstr "Thêm tính năng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Add links to social media on your website"
msgstr "Thêm liên kết đến mạng xã hội trên trang web của bạn"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Add new %s"
msgstr "Thêm %s mới"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Add to cart"
msgstr "Thêm vào giỏ hàng"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Add to menu"
msgstr "Thêm vào menu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"Adding a font requires a reload of the page. This will save all your "
"changes."
msgstr "Thêm phông chữ yêu cầu tải lại trang. Mọi thay đổi sẽ được lưu. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Address"
msgstr "Địa chỉ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Advertising &amp; Marketing"
msgstr "Quảng cáo &amp; Tiếp thị"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__after
msgid "After"
msgstr "Sau"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Alert"
msgstr "Cảnh báo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Align Bottom"
msgstr "Căn dưới"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Align Middle"
msgstr "Căn giữa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Align Top"
msgstr "Căn trên"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Alignment"
msgstr "Căn chỉnh"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Aline Turner, CTO"
msgstr "Aline Turner, CTO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Aline is one of the iconic people in life who can say they love what they "
"do. She mentors 100+ in-house developers and looks after the community of "
"thousands of developers."
msgstr ""
"Aline là một trong những người tiêu biểu trong cuộc sống, người có thể nói "
"rằng họ yêu thích những gì họ làm. Cô ấy cố vấn cho hơn 100 nhà phát triển "
"nội bộ và chăm sóc cộng đồng hàng nghìn nhà phát triển."

#. module: website
#: model:ir.model.fields.selection,name:website.selection__ir_ui_view__visibility__
msgid "All"
msgstr "Tất cả"

#. module: website
#: model:ir.model,name:website.model_website_route
msgid "All Website Route"
msgstr "Mọi tuyến website"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_qweb
msgid "All Websites"
msgstr "Tất cả website"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "All informations you need"
msgstr "Mọi thông tin bạn cần"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "All pages"
msgstr "Tất cả trang"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_searchbar/000.xml:0
#, python-format
msgid "All results"
msgstr "Tất cả kết quả"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "All these icons are completely free for commercial use."
msgstr "Tất cả các biểu tượng đều miễn phí cho mục đích thương mại. "

#. module: website
#: model:ir.model.fields,help:website.field_ir_ui_view__track
#: model:ir.model.fields,help:website.field_website_page__track
msgid "Allow to specify for one page of the website to be trackable or not"
msgstr ""
"Cho phép chỉ định một trang của trang web có thể theo dõi được hay không"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model__website_form_access
msgid "Allowed to use in forms"
msgstr "Cho phép sử dụng trong các biểu mẫu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Already installed"
msgstr "Đã cài đặt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Alternate Image Text"
msgstr "Ảnh chữ xen kẽ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Alternate Text"
msgstr "Chữ xen kẽ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Alternate Text Image"
msgstr "Chữ ảnh xen kẽ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Alternate Text Image Text"
msgstr "Chữ ảnh chữ xen kẽ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"Although this Website may be linked to other websites, we are not, directly "
"or indirectly, implying any approval, association, sponsorship, endorsement,"
" or affiliation with any linked website, unless specifically stated herein."
msgstr ""
"Mặc dù trang web này có thể được liên kết với các trang web khác, nhưng "
"chúng tôi không, trực tiếp hoặc gián tiếp, ngụ ý bất kỳ sự chấp thuận, liên "
"kết, tài trợ, chứng thực hoặc liên kết với bất kỳ trang web được liên kết "
"nào, trừ khi được quy định cụ thể ở đây."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Always Underlined"
msgstr "Luôn gạch chân"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Always Visible"
msgstr "Luôn hiện"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Always visible"
msgstr "Luôn hiện"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "Amazing pages"
msgstr "Trang tuyệt vời"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map
msgid "An address must be specified for a map to be embedded"
msgstr "Phải chỉ định một địa chỉ để nhúng bản đồ vào trang"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "An error has occured, the form has not been sent."
msgstr "Biểu mẫu chưa được gửi vì có lỗi xảy ra."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "An error occurred while rendering the template"
msgstr "Đã xảy ra lỗi trong khi kết xuất mẫu"

#. module: website
#: model:ir.actions.client,name:website.backend_dashboard
#: model:ir.ui.menu,name:website.menu_website_google_analytics
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Analytics"
msgstr "Phân tích"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Analytics cookies and privacy information."
msgstr "Cookie phân tích và thông tin quyền riêng tư."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Anchor copied to clipboard<br>Link: %s"
msgstr "Đã sao chép neo vào bộ nhớ đệm<br>Link: %s"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Anchor name"
msgstr "Tên neo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_image_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_default_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_image_texts_image_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_mosaic_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_reversed_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_texts_image_texts_template
msgid "And a great subtitle"
msgstr "Và một tiêu đề phụ lôi cuốn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Animate"
msgstr "Hiệu ứng động"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Animate text"
msgstr "Văn bản động"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Animated"
msgstr "Hiệu ứng động"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Animation Delay"
msgstr "Hoãn hiệu ứng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Animation Duration"
msgstr "Độ dài hiệu ứng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Animation Launch"
msgstr "Chạy hiệu ứng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_color_blocks_2
msgid "Another color block"
msgstr "Một khối màu khác"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "Another feature"
msgstr "Tính năng khác"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__append
msgid "Append"
msgstr "Append"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_add_features
#: model:ir.ui.menu,name:website.menu_website_add_features
msgid "Apps"
msgstr "Ứng dụng"

#. module: website
#: code:addons/website/controllers/main.py:0
#, python-format
msgid "Apps url"
msgstr "URL ứng dụng"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__arch
msgid "Arch"
msgstr "Arch"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_db
msgid "Arch Blob"
msgstr "Arch Blob"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_fs
msgid "Arch Filename"
msgstr "Tên tệp Arch"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__arch_fs
msgid "Arch Fs"
msgstr "Arch Fs"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Archived"
msgstr "Đã lưu"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__specific_user_account
msgid "Are newly created user accounts website specific"
msgstr ""
"Có phải tài khoản người dùng mới được tạo chỉ áp dụng cho website cụ thể"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "Are you sure you want to delete this page ?"
msgstr "Bạn có chắc chắn muốn xóa trang này không?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Arrows"
msgstr "Mũi tên"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
#, python-format
msgid "As promised, we will offer 4 free tickets to our next summit."
msgstr ""
"Như đã hứa, chúng tôi sẽ tặng 4 vé miễn phí dự hội nghị tiếp theo của chúng "
"tôi."

#. module: website
#: model:ir.model,name:website.model_ir_asset
msgid "Asset"
msgstr "Tài sản"

#. module: website
#: model:ir.model,name:website.model_web_editor_assets
msgid "Assets Utils"
msgstr "Assets Utils"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__copy_ids
msgid "Assets using a copy of me"
msgstr "Asset sử dụng bản sao của tôi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "At The End"
msgstr "Khi kết thúc"

#. module: website
#: model:ir.model,name:website.model_ir_attachment
msgid "Attachment"
msgstr "Đính kèm"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__copy_ids
msgid "Attachment using a copy of me"
msgstr "Bản đính kèm sử dụng bản sao của tôi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Authenticate users, protect user data and allow the website to deliver the services users expects,\n"
"                                                such as maintaining the content of their cart, or allowing file uploads."
msgstr ""
"Xác thực người dùng, bảo vệ dữ liệu người dùng và cho phép trang web mang lại dịch vụ mà người dùng mong muốn, \n"
"                                                ví dụ như lưu giữ nội dung giỏ hàng hay cho phép tải lên tệp. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_search
msgid "Author"
msgstr "Tác giả"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Auto"
msgstr "Tự động"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid ""
"Automatically opens the pop-up if the user stays on a page longer than the "
"specified time."
msgstr ""
"Tự động mở pop-up nếu người dùng ở lại trang lâu hơn thời gian chỉ định. "

#. module: website
#: model:ir.model.fields,field_description:website.field_website__auto_redirect_lang
msgid "Autoredirect Language"
msgstr "Tự động chuyển hướng ngôn ngữ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Autosizing"
msgstr "Tự động định cỡ"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_actions_server__website_published
#: model:ir.model.fields,field_description:website.field_ir_cron__website_published
msgid "Available on the Website"
msgstr "Có trên trang web"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "BSgzTvR5L1GB9jriT451iTN4huVPxHmltG6T6eo"
msgstr "BSgzTvR5L1GB9jriT451iTN4huVPxHmltG6T6eo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "BTS Base Colors"
msgstr "Màu cơ sở BTS"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Backdrop"
msgstr "Backdrop"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Background"
msgstr "Nền"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/configurator_tour.js:0
#: code:addons/website/static/src/js/tours/configurator_tour.js:0
#, python-format
msgid "Background Shape"
msgstr "Hình dạng nền"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Badge"
msgstr "Huy hiệu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Bags"
msgstr "Túi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Bar Horizontal"
msgstr "Cột ngang"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Bar Vertical"
msgstr "Cột dọc"

#. module: website
#: model:ir.model,name:website.model_base
msgid "Base"
msgstr "Cơ sở"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_base
msgid "Base View Architecture"
msgstr "Xem kiến trúc cơ sở"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_ui_view__mode__primary
msgid "Base view"
msgstr "Kiểu xem cơ sở"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Basic sales &amp; marketing for up to 2 users"
msgstr "Bán hàng cơ bản & amp; tiếp thị cho tối đa 2 người dùng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "Beautiful snippets"
msgstr "Snippet đẹp mắt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Beef Carpaccio"
msgstr "Thịt bò Carpaccio"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__before
msgid "Before"
msgstr "Trước"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Beginner"
msgstr "Sơ cấp"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Below Each Other"
msgstr "Dòng dưới"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
msgid "Big"
msgstr "Lớn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Big Icons Subtitles"
msgstr "Chú thích icon lớn"

#. module: website
#: model:ir.model.fields,help:website.field_ir_model_fields__website_form_blacklisted
msgid "Blacklist this field for web forms"
msgstr "Đưa trường này vào danh sách đen của biểu mẫu web "

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model_fields__website_form_blacklisted
msgid "Blacklisted in web forms"
msgstr "Đã đưa vào danh sách đen của biểu mẫu web "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Blazers"
msgstr "Áo khoác blazer"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Block"
msgstr "Khối"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Blockquote"
msgstr "Trích dẫn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Blog"
msgstr "Blog"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Blog Post"
msgstr "Bài blog"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_news
msgid "Blogging and posting relevant content"
msgstr "Viết và đăng nội dung blog phù hợp"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Books"
msgstr "Sách"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Bootstrap-based templates"
msgstr "Mẫu dựa trên Bootstrap"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_widgets
#, python-format
msgid "Border"
msgstr "Viền"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Border Bottom"
msgstr "Viền dưới"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
msgid "Border Color"
msgstr "Màu viền"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Border Radius"
msgstr "Bán kính viền"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Border Width"
msgstr "Độ rộng viền"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bordered"
msgstr "Viền"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Bottom"
msgstr "Dưới"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Bottom to Top"
msgstr "Từ dưới lên trên"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bounce In"
msgstr "Bounce In"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bounce In-Down"
msgstr "Bounce In-Down"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bounce In-Left"
msgstr "Bounce In-Left"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bounce In-Right"
msgstr "Bounce In-Right"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Boxed"
msgstr "Hộp"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Boxes"
msgstr "Hộp"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Breadcrumb"
msgstr "Breadcrumb"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Build my website"
msgstr "Dựng website của tôi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Building blocks system"
msgstr "Hệ thống khối dựng"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Building your %s"
msgstr "Đang tạo %s"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Building your website..."
msgstr "Đang dựng website của bạn..."

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__bundle
msgid "Bundle"
msgstr "Nhóm"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Button"
msgstr "Nút"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Button Position"
msgstr "Vị trí nút"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Buttons"
msgstr "Nút"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#, python-format
msgid "By clicking on this banner, you give us permission to collect data."
msgstr ""
"Bằng cách nhấp vào biểu ngữ này, bạn cho phép chúng tôi thu thập dữ liệu."

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__cdn_url
#: model:ir.model.fields,field_description:website.field_website__cdn_url
msgid "CDN Base URL"
msgstr "URL cơ sở CDN"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__cdn_filters
#: model:ir.model.fields,field_description:website.field_website__cdn_filters
msgid "CDN Filters"
msgstr "Bộ lọc CDN"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "CTA"
msgstr "CTA"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__cache_key_expr
msgid "Cache Key Expr"
msgstr "Biểu thức khóa cache"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__cache_time
msgid "Cache Time"
msgstr "Thời gian lưu cache"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Call to Action"
msgstr "Nút hành động"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "Call us"
msgstr "Gọi cho chúng tôi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Call-to-action"
msgstr "Nút CTA"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Camera"
msgstr "Camera"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_partner__can_publish
#: model:ir.model.fields,field_description:website.field_res_users__can_publish
#: model:ir.model.fields,field_description:website.field_website_page__can_publish
#: model:ir.model.fields,field_description:website.field_website_published_mixin__can_publish
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__can_publish
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__can_publish
msgid "Can Publish"
msgstr "Có thể đăng"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/dashboard.js:0
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: code:addons/website/static/src/js/menu/new_content.js:0
#: code:addons/website/static/src/js/utils.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/xml/website.xml:0
#: model_terms:ir.ui.view,arch_db:website.qweb_500
#: model_terms:ir.ui.view,arch_db:website.view_edit_robots
#: model_terms:ir.ui.view,arch_db:website.view_website_form_view_themes_modal
#, python-format
msgid "Cancel"
msgstr "Hủy"

#. module: website
#: code:addons/website/models/res_lang.py:0
#, python-format
msgid "Cannot deactivate a language that is currently used on a website."
msgstr ""
"Không thể hủy kích hoạt ngôn ngữ hiện đang được sử dụng trên trang web."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/content/website_root.js:0
#, python-format
msgid "Cannot load google map."
msgstr "Không thể tải bản đồ Google."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Card"
msgstr "Thẻ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Card Body"
msgstr "Thân thẻ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Card Footer"
msgstr "Chân thẻ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Card Header"
msgstr "Đầu thẻ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
msgid "Card Style"
msgstr "Kiểu thẻ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Cards"
msgstr "Thẻ"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_career
msgid "Career"
msgstr "Công việc"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Case Studies"
msgstr "Case study"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_search
msgid "Category"
msgstr "Danh mục"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Category of Cookie"
msgstr "Danh mục Cookie"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Center"
msgstr "Giữa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Centered"
msgstr "Căn giữa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Centered Logo"
msgstr "Logo căn giữa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Change Icons"
msgstr "Đổi Icon"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Change theme in a few clicks, and browse through Odoo's catalog of\n"
"                            ready-to-use themes available in our app store."
msgstr ""
"Thay đổi chủ đề bằng vài lần bấm chuột và duyệt qua danh mục của Odoo về\n"
"                            các chủ đề tạo sẵn hiện có trong cửa hàng ứng dụng. "

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"Changing the color palette will reset all your color customizations, are you"
" sure you want to proceed?"
msgstr ""
"Thay đổi bảng màu sẽ đặt lại tất cả các tùy chỉnh màu của bạn, bạn có chắc "
"chắn muốn tiếp tục không?"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"Changing theme requires to leave the editor. This will save all your "
"changes, are you sure you want to proceed? Be careful that changing the "
"theme will reset all your color customizations."
msgstr ""
"Thay đổi chủ đề yêu cầu rời khỏi trình chỉnh sửa. Thao tác này sẽ lưu tất cả"
" các thay đổi của bạn, bạn có chắc chắn muốn tiếp tục không? Hãy cẩn thận "
"rằng việc thay đổi chủ đề sẽ đặt lại tất cả các tùy chỉnh màu sắc của bạn."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#, python-format
msgid "Chart"
msgstr "Biểu đồ"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_live_chat
msgid "Chat with visitors to improve traction"
msgstr "Trò chuyện để giữ chân khách truy cập"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup
msgid "Check out now and get $20 off your first order."
msgstr "Thanh toán ngay và được giảm 400,000đ cho đơn hàng đầu tiên."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/content/website_root.js:0
#, python-format
msgid "Check your configuration."
msgstr "Kiểm tra cấu hình của bạn."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Check your connection and try again"
msgstr "Kiểm tra kết nối và thử lại"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Checkbox"
msgstr "Hộp kiểm"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Cheese Onion Rings"
msgstr "Vòng hành tây phô mai"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Chefs Fresh Soup of the Day"
msgstr "Súp theo ngày"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__field_parent
msgid "Child Field"
msgstr "Trường con"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__child_id
#: model_terms:ir.ui.view,arch_db:website.website_menus_form_view
msgid "Child Menus"
msgstr "Menu con"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Children"
msgstr "Trẻ em"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Choose"
msgstr "Chọn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_picture
msgid ""
"Choose a vibrant image and write an inspiring paragraph about it.<br/> It "
"does not have to be long, but it should reinforce your image."
msgstr ""
"Chọn một bức ảnh sống động và viết một đoạn văn miêu tả thật giàu cảm "
"hứng.<br/>Đoạn văn không nhất thiết phải dài nhưng phải củng cố cho hình ảnh"
" này. "

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Choose an anchor name"
msgstr "Chọn một tên neo"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#, python-format
msgid "Choose another theme"
msgstr "Chọn một chủ đề khác"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Choose your favorite"
msgstr "Chọn một"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
msgid "Circle"
msgstr "Tròn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Classic"
msgstr "Cổ điển"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Clean"
msgstr "Trống"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "Clever Slogan"
msgstr "Slogan thông minh"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Click and change content directly from the front-end: no complex back\n"
"                            end to deal with."
msgstr ""
"Bấm và thay đổi nội dung trực tiếp từ front-end: không cần xử lý\n"
"                            back-end phức tạp."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "Click here to go back to block tab."
msgstr "Bấm vào đây để quay lại tab khối."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Click on"
msgstr "Bấm vào"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Click on the icon to adapt it <br/>to your purpose."
msgstr "Bấm vào biểu tượng để điều chỉnh <br/>cho phù hợp mục đích. "

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Click to choose more images"
msgstr "Bấm vào đây để chọn thêm hình ảnh"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Click to select"
msgstr "Click để chọn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Client ID"
msgstr "ID Khách hàng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Client Secret"
msgstr "Bảo mật khách "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Clone this page"
msgstr "Sao chép trang này"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/xml/website.xml:0
#: model_terms:ir.ui.view,arch_db:website.qweb_500
#: model_terms:ir.ui.view,arch_db:website.s_popup
#: model_terms:ir.ui.view,arch_db:website.show_website_info
#, python-format
msgid "Close"
msgstr "Đóng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Close Button Color"
msgstr "Màu nút đóng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Clothes"
msgstr "Quần áo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
msgid "Code"
msgstr "Mã"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Code Injection"
msgstr "Chèn mã"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
msgid "Collapse Icon"
msgstr "Icon thu gọn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_badge_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Color"
msgstr "Màu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_color_blocks_2
msgid ""
"Color blocks are a simple and effective way to <b>present and highlight your"
" content</b>. Choose an image or a color for the background. You can even "
"resize and duplicate the blocks to create your own layout. Add images or "
"icons to customize the blocks."
msgstr ""
"Khối màu là cách đơn giản và hiểu quả để <b>trình bày và làm nổi bật nội "
"dung</b>. Chọn một hình ảnh hoặc một màu làm nền. Bạn có thể thay đổi kích "
"cỡ và sao chép các khối để tạo bố cục riêng. Hãy thêm hình ảnh hoặc biểu "
"tượng để tạo khối tuỳ ý. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Color filter"
msgstr "Bộ lọc màu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Colors"
msgstr "Màu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Columns"
msgstr "Cột"

#. module: website
#: model:ir.model.fields,help:website.field_website_configurator_feature__website_config_preselection
msgid ""
"Comma-separated list of website type/purpose for which this feature should "
"be pre-selected"
msgstr ""
"Danh sách ngăn cách bởi dấu phẩy về loại website hoặc mục đích mà tính năng "
"này được chọn trước "

#. module: website
#: model:ir.model,name:website.model_res_company
msgid "Companies"
msgstr "Công ty"

#. module: website
#: code:addons/website/models/website.py:0
#: model:ir.model.fields,field_description:website.field_website__company_id
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#, python-format
msgid "Company"
msgstr "Công ty"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Complete CRM for any size team"
msgstr "CRM hoàn chỉnh cho mọi quy mô"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Components"
msgstr "Thành phần"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Computers"
msgstr "Máy tính"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Computers &amp; Devices"
msgstr "Máy tính &amp; Thiết bị"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Conditionally"
msgstr "Có điều kiện"

#. module: website
#: model:ir.model,name:website.model_res_config_settings
msgid "Config Settings"
msgstr "Thiết lập cấu hình"

#. module: website
#: model:ir.ui.menu,name:website.menu_website_global_configuration
msgid "Configuration"
msgstr "Cấu hình"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__configurator_done
msgid "Configurator Done"
msgstr "Định cấu hình hoàn tất"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_social_network
msgid "Configure Social Network"
msgstr "Cấu hình mạng xã hội"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/dashboard.js:0
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Connect Google Analytics"
msgstr "Kết nối Google Analytics"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Connect with us"
msgstr "Kết nối với chúng tôi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Connected"
msgstr "Đã kết nối"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_google_search_console
msgid "Console Google Search"
msgstr "Điều khiển Google Search"

#. module: website
#: model:ir.model,name:website.model_res_partner
#: model:ir.model.fields,field_description:website.field_website_visitor__partner_id
#: model_terms:ir.ui.view,arch_db:website.s_tabs
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Contact"
msgstr "Liên hệ"

#. module: website
#: code:addons/website/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website.header_call_to_action
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.s_banner
#, python-format
msgid "Contact Us"
msgstr "Liên hệ"

#. module: website
#: code:addons/website/models/website_visitor.py:0
#, python-format
msgid "Contact Visitor"
msgstr "Liên hệ với khách"

#. module: website
#: model:website.menu,name:website.menu_contactus
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
#: model_terms:ir.ui.view,arch_db:website.s_cover
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#: model_terms:website.page,arch_db:website.contactus_page
msgid "Contact us"
msgstr "Liên hệ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid ""
"Contact us about anything related to our company or services.<br/>\n"
"                                    We'll do our best to get back to you as soon as possible."
msgstr ""
"Hãy liên hệ khi bạn gặp bất cứ vấn đề nào liên quan đến công ty hoặc dịch vụ của chúng tôi.<br/>\n"
"                                    Chúng tôi sẽ cố gắng phản hồi trong thời gian sớm nhất. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "Contact us anytime"
msgstr "Liên hệ với chúng tôi bất cứ lúc nào"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Contact us for any issue or question"
msgstr "Liên hệ khi gặp sự cố hoặc có câu hỏi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Contacts"
msgstr "Liên hệ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Contains"
msgstr "Chứa"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_robots__content
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Content"
msgstr "Nội dung"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__cdn_activated
#: model:ir.model.fields,field_description:website.field_website__cdn_activated
msgid "Content Delivery Network (CDN)"
msgstr "Mạng phân phối nội dung (CDN)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Content Width"
msgstr "Độ rộng nội dung"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/translator.xml:0
#, python-format
msgid "Content to translate"
msgstr "Nội dung để dịch"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Continue"
msgstr "Tiếp tục"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Continue reading <i class=\"fa fa-long-arrow-right align-middle ml-1\"/>"
msgstr "Tiếp tục đọc <i class=\"fa fa-long-arrow-right align-middle ml-1\"/>"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
#: model_terms:ir.ui.view,arch_db:website.cookies_bar
#, python-format
msgid "Cookie Policy"
msgstr "Chính sách cookie"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.res_config_settings.xml:0
#, python-format
msgid "Cookie bars may significantly impair the experience"
msgstr "Thanh cookie có thể làm giảm đáng kể trải nghiệm"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_cookies_bar
#: model:ir.model.fields,field_description:website.field_website__cookies_bar
msgid "Cookies Bar"
msgstr "Thanh Cookies"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Cookies are small bits of text sent by our servers to your computer or device when you access our services.\n"
"                            They are stored in your browser and later sent back to our servers so that we can provide contextual content.\n"
"                            Without cookies, using the web would be a much more frustrating experience.\n"
"                            We use them to support your activities on our website. For example, your session (so you don't have to login again) or your shopping cart.\n"
"                            <br/>\n"
"                            Cookies are also used to help us understand your preferences based on previous or current activity on our website (the pages you have\n"
"                            visited), your language and country, which enables us to provide you with improved services.\n"
"                            We also use cookies to help us compile aggregate data about site traffic and site interaction so that we can offer\n"
"                            better site experiences and tools in the future."
msgstr ""
"Cookie là các đoạn văn bản nhỏ gửi từ máy chủ của chúng tôi tới máy tính hoặc thiết bị của bạn mỗi khi bạn truy cập dịch vụ của chúng tôi.\n"
"                            Chúng được lưu trong trình duyệt của bạn và sau đó được gửi lại máy chủ của chúng tôi để chúng tôi có thể cung cấp các nội dung phù hợp hơn. \n"
"                            Không có cookie, sử dụng web sẽ trở thành một trải nghiệm hết sức khó chịu. \n"
"                            Chúng tôi dùng cookie để hỗ trợ các hoạt động trên website của bạn. Ví dụ như các phiên (để bạn không phải đăng nhập lại) hoặc giỏ hàng của bạn. \n"
"                            <br/>\n"
"                            Cookie cũng được dùng để giúp chúng tôi hiểu lựa chọn của bạn hơn dựa vào hoạt động trước đây hoặc hiện tại trên website của chúng tôi (các trang bạn đã\n"
"                            truy cập), ngôn ngữ và quốc gia, điều này giúp chúng tôi cung cấp dịch vụ tốt hơn nhiều. \n"
"                            Chúng tôi cũng dùng cookie để giúp thu thập tài liệu tổng hợp về lượng truy cập trang và tương tác với trang, nhằm mục đích mang lại\n"
"                            trải nghiệm và công cụ trang tốt hơn trong tương lai. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Copyright"
msgstr "Bản quyền"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#, python-format
msgid "Countdown ends in"
msgstr "Đếm ngược kết thúc sau"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
#, python-format
msgid "Countdown is over - Firework"
msgstr "Đếm ngược kết thúc - Pháo hoa"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__country_id
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Country"
msgstr "Quốc gia"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__country_flag
msgid "Country Flag"
msgstr "Quốc kỳ"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_country_group_ids
#: model:ir.model.fields,field_description:website.field_website__country_group_ids
msgid "Country Groups"
msgstr "Nhóm quốc gia"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Course"
msgstr "Khóa học"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.record_cover
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
msgid "Cover"
msgstr "Bìa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
msgid "Cover Photo"
msgstr "Ảnh bìa"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_cover_properties_mixin__cover_properties
msgid "Cover Properties"
msgstr "Thuộc tính bìa"

#. module: website
#: model:ir.model,name:website.model_website_cover_properties_mixin
msgid "Cover Properties Website Mixin"
msgstr "Mixin thuộc tính bìa website"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/utils.js:0
#: model_terms:ir.ui.view,arch_db:website.view_website_form_view_themes_modal
#, python-format
msgid "Create"
msgstr "Tạo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid "Create Page"
msgstr "Tạo trang"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.brand_promotion
msgid "Create a"
msgstr "Tạo"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Create a Google Project and Get a Key"
msgstr "Tạo dự án Google và nhận khóa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Create a New Website"
msgstr "Tạo website mới"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Create a link to target this section"
msgstr "Tạo link nhắm tới mục này"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Create new"
msgstr "Tạo mới"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Create your page from scratch by dragging and dropping pre-made,\n"
"                            fully customizable building blocks."
msgstr ""
"Tạo trang của bạn từ đầu bằng cách kéo và thả,\n"
"                            các khối dựng tùy chỉnh được tạo sẵn."

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__create_uid
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__create_uid
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__create_uid
#: model:ir.model.fields,field_description:website.field_theme_website_menu__create_uid
#: model:ir.model.fields,field_description:website.field_theme_website_page__create_uid
#: model:ir.model.fields,field_description:website.field_website__create_uid
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__create_uid
#: model:ir.model.fields,field_description:website.field_website_menu__create_uid
#: model:ir.model.fields,field_description:website.field_website_page__create_uid
#: model:ir.model.fields,field_description:website.field_website_rewrite__create_uid
#: model:ir.model.fields,field_description:website.field_website_robots__create_uid
#: model:ir.model.fields,field_description:website.field_website_route__create_uid
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__create_uid
#: model:ir.model.fields,field_description:website.field_website_visitor__create_uid
msgid "Created by"
msgstr "Tạo bởi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Created in 2021, the company is young and dynamic. Discover the composition "
"of the team and their skills."
msgstr ""
"Được thành lập năm 2021, chúng tôi là một công ty trẻ trung và năng động. "
"Khám phá thành viên các nhóm và kỹ năng của họ."

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__create_date
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__create_date
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__create_date
#: model:ir.model.fields,field_description:website.field_theme_website_menu__create_date
#: model:ir.model.fields,field_description:website.field_theme_website_page__create_date
#: model:ir.model.fields,field_description:website.field_website__create_date
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__create_date
#: model:ir.model.fields,field_description:website.field_website_menu__create_date
#: model:ir.model.fields,field_description:website.field_website_page__create_date
#: model:ir.model.fields,field_description:website.field_website_rewrite__create_date
#: model:ir.model.fields,field_description:website.field_website_robots__create_date
#: model:ir.model.fields,field_description:website.field_website_route__create_date
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__create_date
msgid "Created on"
msgstr "Thời điểm tạo"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#, python-format
msgid "Custom"
msgstr "Tùy chỉnh"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__custom_code_head
msgid "Custom <head> code"
msgstr "Mã <head> tùy chỉnh"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "Custom Code"
msgstr "Mã tùy chỉnh"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Custom Key"
msgstr "Khóa tùy chỉnh"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Custom Url"
msgstr "URL tùy chỉnh"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__custom_code_footer
msgid "Custom end of <body> code"
msgstr " Mã <body> cuối tùy chỉnh"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Custom end of body code"
msgstr "Mã phần cuối tùy chỉnh"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Custom field"
msgstr "Trường tuỳ chỉnh"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Custom head code"
msgstr "Mã phần đầu tùy chỉnh"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__auth_signup_uninvited
#: model:ir.model.fields,field_description:website.field_website__auth_signup_uninvited
msgid "Customer Account"
msgstr "Tài khoản khách hàng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Customers"
msgstr "Khách hàng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Customization tool"
msgstr "Công cụ tùy chỉnh"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Customize"
msgstr "Tuỳ chỉnh"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__customize_show
msgid "Customize Show"
msgstr "Tùy chỉnh trình chiếu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "D - H - M"
msgstr "N - G - P"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "D - H - M - S"
msgstr "N - G - P - Gi"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "DRAG BUILDING BLOCKS HERE"
msgstr "KÉO KHỐI DỰNG TẠI ĐÂY"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
msgid "Danger"
msgstr "Nguy hiểm"

#. module: website
#: model:ir.ui.menu,name:website.menu_dashboard
msgid "Dashboard"
msgstr "Dashboard"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_line_widgets
msgid "Dashed"
msgstr "Nét đứt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Data"
msgstr "Dữ liệu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Data Border"
msgstr "Viền dữ liệu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Data Color"
msgstr "Màu dữ liệu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Dataset Border"
msgstr "Viền tập dữ liệu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Dataset Color"
msgstr "Màu tập dữ liệu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
msgid "Date"
msgstr "Ngày"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Date &amp; Time"
msgstr "Ngày &amp; Giờ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#, python-format
msgid "Days"
msgstr "Ngày"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Decimal Number"
msgstr "Số thập phân"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Default"
msgstr "Mặc định"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Default Access Rights"
msgstr "Quyền truy cập mặc định"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__default_lang_id
msgid "Default Language"
msgstr "Ngôn ngữ mặc định"

#. module: website
#: model:website.menu,name:website.main_menu
msgid "Default Main Menu"
msgstr "Menu chính mặc định"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Default Reversed"
msgstr "Đảo mặc định"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_default_image
#: model:ir.model.fields,field_description:website.field_website__social_default_image
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Default Social Share Image"
msgstr "Ảnh chia sẻ mạng xã hội mặc định"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Default Value"
msgstr "Giá trị mặc định"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_default_lang_id
msgid "Default language"
msgstr "Ngôn ngữ mặc định"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_default_lang_code
msgid "Default language code"
msgstr "Mã ngôn ngữ mặc định"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Delay"
msgstr "Trễ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Delete Blocks"
msgstr "Xóa khối"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Delete Menu Item"
msgstr "Xóa mục menu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Delete Page"
msgstr "Xóa trang"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"Delete the above image or replace it with a picture that illustrates your "
"message. Click on the picture to change its <em>rounded corner</em> style."
msgstr ""
"Xóa hình trên hoặc thay thế bằng hình ảnh minh họa thông điệp của bạn. Nhấp "
"vào ảnh để thay đổi kiểu <em> bo tròn </em> của ảnh này. "

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Delete this font"
msgstr "Xóa phông chữ này"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Delete this page"
msgstr "Xóa trang này"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"Deleting a font requires a reload of the page. This will save all your "
"changes and reload the page, are you sure you want to proceed?"
msgstr ""
"Xóa phông chữ yêu cầu tải lại trang. Thao tác này sẽ lưu tất cả các thay đổi"
" của bạn và tải lại trang, bạn có chắc chắn muốn tiếp tục không?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "Deliveries"
msgstr "Giao hàng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "Departments"
msgstr "Phòng/Ban"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Dependencies"
msgstr "Phụ thuộc"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "Describe your field here."
msgstr "Mô tả trường tại đây. "

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__description
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
#, python-format
msgid "Description"
msgstr "Mô tả"

#. module: website
#: model:website.configurator.feature,description:website.feature_page_our_services
msgid "Description of your services offer"
msgstr "Mô tả các gói dịch vụ của bạn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog_options
msgid "Descriptions"
msgstr "Mô tả"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Descriptive"
msgstr "Mô tả"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Design"
msgstr "Thiết kế"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Design features"
msgstr "Tính năng thiết kế"

#. module: website
#: model:website.configurator.feature,description:website.feature_page_pricing
msgid "Designed to drive conversion"
msgstr "Được thiết kế để tăng chuyển đổi"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#, python-format
msgid "Desktop"
msgstr "Máy tính bàn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Desktop computers"
msgstr "Máy tính để bàn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Detail"
msgstr "Chi tiết"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Details"
msgstr "Chi tiết"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Detect"
msgstr "Phát hiện"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Direction"
msgstr "Hướng"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__directive
msgid "Directive"
msgstr "Chỉ dẫn"

#. module: website
#: model:ir.actions.server,name:website.website_disable_unused_snippets_assets_ir_actions_server
#: model:ir.cron,cron_name:website.website_disable_unused_snippets_assets
#: model:ir.cron,name:website.website_disable_unused_snippets_assets
msgid "Disable unused snippets assets"
msgstr "Tắt các snippets assets không sử dụng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Disabled"
msgstr "Đã vô hiệu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Disappearing"
msgstr "Biến mất"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Disappears"
msgstr "Biến mất"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/js/menu/content.js:0
#: code:addons/website/static/src/js/menu/seo.js:0
#: code:addons/website/static/src/snippets/s_embed_code/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Discard"
msgstr "Huỷ bỏ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Discard & Edit in backend"
msgstr "Hủy & sửa trong backend"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Discover"
msgstr "Khám phá"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "Discover all the features"
msgstr "Khám phá tất cả tính năng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
msgid "Discover more"
msgstr "Khám phá thêm"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Discover our culture and our values"
msgstr "Khám phá văn hoá và giá trị "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Discover our legal notice"
msgstr "Khám phá thông báo pháp lý"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Discover our realisations"
msgstr "Thành công của chúng tôi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid "Discover our team"
msgstr "Khám phá đội ngũ của chúng tôi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Discrete"
msgstr "Rời rạc"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Discussion Group"
msgstr "Nhóm thảo luận"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_share_options
msgid "Disk"
msgstr "Đĩa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Display"
msgstr "Hiển thị"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "Display Inline"
msgstr "Hiển thị cùng dòng"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__display_name
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__display_name
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__display_name
#: model:ir.model.fields,field_description:website.field_theme_website_menu__display_name
#: model:ir.model.fields,field_description:website.field_theme_website_page__display_name
#: model:ir.model.fields,field_description:website.field_website__display_name
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__display_name
#: model:ir.model.fields,field_description:website.field_website_menu__display_name
#: model:ir.model.fields,field_description:website.field_website_page__display_name
#: model:ir.model.fields,field_description:website.field_website_rewrite__display_name
#: model:ir.model.fields,field_description:website.field_website_robots__display_name
#: model:ir.model.fields,field_description:website.field_website_route__display_name
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__display_name
#: model:ir.model.fields,field_description:website.field_website_track__display_name
#: model:ir.model.fields,field_description:website.field_website_visitor__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_cookies_bar
#: model:ir.model.fields,help:website.field_website__cookies_bar
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Display a customizable cookies bar on your website."
msgstr "Hiển thị thanh cookie có thể tùy chỉnh trên website. "

#. module: website
#: code:addons/website/models/ir_qweb_fields.py:0
#, python-format
msgid "Display the badges"
msgstr "Hiện huy hiệu"

#. module: website
#: code:addons/website/models/ir_qweb_fields.py:0
#, python-format
msgid "Display the biography"
msgstr "Hiện tiểu sử"

#. module: website
#: code:addons/website/models/ir_qweb_fields.py:0
#, python-format
msgid "Display the website description"
msgstr "Hiện miêu tả trang web"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_logo
#: model:ir.model.fields,help:website.field_website__logo
msgid "Display this logo on the website."
msgstr "Hiển thị logo này trên trang web."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Display this website when users visit this domain"
msgstr "Hiện website này khi người dùng truy cập tên miền này"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/res_config_settings.js:0
#, python-format
msgid "Do not activate"
msgstr "Không kích hoạt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Do you need specific information? Our specialists will help you with "
"pleasure."
msgstr ""
"Bạn cần tìm thông tin cụ thể? Chuyên gia của chúng tôi sẵn lòng giúp đỡ bạn."
" "

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Do you want to edit the company data ?"
msgstr "Bạn có muốn sửa dữ liệu công ty?"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Do you want to install the \"%s\" App?"
msgstr "Bạn có muốn cài đặt ứng dụng \"%s\" không?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Documentation"
msgstr "Tài liệu hướng dẫn "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Doesn't contain"
msgstr "Không chứa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Domain"
msgstr "Miền"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "Don't forget to update all links referring to this page."
msgstr "Đừng quên cập nhật tất cả liên kết liên quan đến trang này."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/theme_preview_kanban.js:0
#, python-format
msgid "Don't worry, you can switch later."
msgstr "Đừng lo lắng, bạn có thể chuyển đổi sau."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Donation"
msgstr "Quyên góp"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Donation Button"
msgstr "Nút quyên góp"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Dots"
msgstr "Chấm"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_line_widgets
msgid "Dotted"
msgstr "Chấm"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_line_widgets
msgid "Double"
msgstr "Đôi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Double click an icon to replace it with one of your choice."
msgstr "Bấm đúp vào một biểu tượng để thay thế bằng một lựa chọn khác. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Doughnut"
msgstr "Bánh vòng"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid ""
"Drag the <b>%s</b> building block and drop it at the bottom of the page."
msgstr "Kéo khối dựng <b>%s</b> và thả vào cuối trang. "

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Drag to the right to get a submenu"
msgstr "Kéo sang phải để tạo menu phụ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Dresses"
msgstr "Váy"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Dropdown"
msgstr "Thả xuống"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Dropdown menu"
msgstr "Menu thả xuống"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Due Date"
msgstr "Hạn chót"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Duplicate"
msgstr "Nhân bản"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Duplicate Page"
msgstr "Sao chép trang"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Duplicate blocks <br/>to add more steps."
msgstr "Sao chép khối <br/>để thêm bước."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Duplicate blocks and columns to add more features."
msgstr "Sao chép khối và cột để thêm nhiều tính năng."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Dynamic Content"
msgstr "Nội dung động"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_domain
#: model:ir.model.fields,help:website.field_website__domain
msgid "E.g. https://www.mydomain.com"
msgstr "VD:  https://www.mydomain.com"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Easily design your own Odoo templates thanks to clean HTML\n"
"                            structure and bootstrap CSS."
msgstr ""
"Dễ dàng thiết kế các mẫu Odoo của riêng bạn nhờ cấu trúc HTML\n"
"                            rõ ràng và bootstrap CSS."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#, python-format
msgid "Edit"
msgstr "Sửa"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#: code:addons/website/static/src/js/widgets/link_popover_widget.js:0
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Edit Menu"
msgstr "Sửa menu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Edit Menu Item"
msgstr "Sửa mục menu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Edit Message"
msgstr "Sửa tin nhắn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Edit Styles"
msgstr "Sửa kiểu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Edit Top Menu"
msgstr "Sửa menu trên"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Edit code in backend"
msgstr "Sửa mã trong backend"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_embed_code/options.js:0
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#, python-format
msgid "Edit embedded code"
msgstr "Sửa mã nhúng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Edit in backend"
msgstr "Sửa ở backend"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Edit my Analytics Client ID"
msgstr "Chỉnh sửa ID khách hàng Analytics"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Edit robots.txt"
msgstr "Sửa robots.txt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Edit video"
msgstr "Sửa video"

#. module: website
#: model:res.groups,name:website.group_website_designer
msgid "Editor and Designer"
msgstr "Biên tập và Thiết kế"

#. module: website
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "Either action_server_id or filter_id must be provided."
msgstr "action_server_id hoặc filter_id phải được cung cấp."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Electronics"
msgstr "Thiết bị điện tử"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__email
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
msgid "Email"
msgstr "Email"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Email address"
msgstr "Địa chỉ email"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Email support"
msgstr "Hỗ trợ qua email"

#. module: website
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "Empty field name in %r"
msgstr "Tên trường trống trong %r"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Enable billing on your Google Project"
msgstr "Bật thanh toán trên Google Project "

#. module: website
#: model:ir.model.fields,help:website.field_ir_model__website_form_access
msgid "Enable the form builder feature for this model."
msgstr "Bật tính năng tạo biểu mẫu cho model này."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Enable the right google map APIs in your google account"
msgstr "Kích hoạt đúng API Google Map trong tài khoản Google của bạn"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Enter an API Key"
msgstr "Nhập khóa API"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"Enter code that will be added before the </body> of every page of your site."
msgstr "Nhập mã sẽ được thêm vào trước </body> của mỗi trang trên trang web. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Enter code that will be added into every page of your site"
msgstr "Nhập mã sẽ được thêm vào mọi trang của website"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"Enter code that will be added into the <head> of every page of your site."
msgstr "Nhập mã sẽ được thêm vào <head> của mỗi trang trên trang web. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Enter email"
msgstr "Nhập email"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Equal Widths"
msgstr "Rộng đều"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form.xml:0
#: code:addons/website/static/src/xml/website_form.xml:0
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Error"
msgstr "Lỗi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Essential oils"
msgstr "Tinh dầu thơm"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Event"
msgstr "Sự kiện"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Event heading"
msgstr "Tiêu đề sự kiện"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_event
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Events"
msgstr "Sự kiện"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Every Time"
msgstr "Mọi lần"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Everything"
msgstr "Tất cả"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Examples"
msgstr "Ví dụ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Expert"
msgstr "Chuyên gia"

#. module: website
#: model:website.configurator.feature,description:website.feature_page_privacy_policy
msgid "Explain how you protect privacy"
msgstr "Giải thích cách bạn bảo vệ quyền riêng tư"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert
msgid ""
"Explain the benefits you offer. <br/>Don't write about products or services "
"here, write about solutions."
msgstr ""
"Giải thích những lợi ích bạn cung cấp. <br/> Tránh viết về sản phẩm hay dịch"
" vụ ở đây, hãy viết về giải pháp."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Explore"
msgstr "Khám phá"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__cache_key_expr
msgid ""
"Expression (tuple) to evaluate the cached key. \n"
"E.g.: \"(request.params.get(\"currency\"), )\""
msgstr ""
"Biểu thức (tuple) để đánh giá khóa đã được lưu vào cache. \n"
"VD: \"(request.params.get(\"currency\"), )\""

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_ui_view__mode__extension
msgid "Extension View"
msgstr "Xem mở rộng"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_actions_server__xml_id
#: model:ir.model.fields,field_description:website.field_ir_cron__xml_id
#: model:ir.model.fields,field_description:website.field_website_page__xml_id
msgid "External ID"
msgstr "ID bên ngoài"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Extra Large"
msgstr "Cực lớn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Extra link"
msgstr "Link thêm"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Extra-Large"
msgstr "Cực lớn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Extra-Small"
msgstr "Cực nhỏ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "F.A.Q."
msgstr "Hỏi - Đáp"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Facebook"
msgstr "Facebook"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_facebook
#: model:ir.model.fields,field_description:website.field_website__social_facebook
msgid "Facebook Account"
msgstr "Tài khoản Facebook"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade"
msgstr "Fade"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade In"
msgstr "Fade In"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade In-Down"
msgstr "Fade In-Down"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade In-Left"
msgstr "Fade In-Left"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade In-Right"
msgstr "Fade In-Right"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade In-Up"
msgstr "Fade In-Up"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade Out"
msgstr "Fade Out"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Failed to install \"%s\""
msgstr "Cài đặt \"%s\" đã thất bại"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Farm Friendly Chicken Supreme"
msgstr "Ức gà sốt kem"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__favicon
msgid "Favicon"
msgstr "Favicon"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature One"
msgstr "Tính năng một"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature Three"
msgstr "Tính năng ba"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card
msgid "Feature Title"
msgstr "Tiêu đề tính năng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature Two"
msgstr "Tính năng hai"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__feature_url
msgid "Feature Url"
msgstr "Url nổi bật"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.snippets
#, python-format
msgid "Features"
msgstr "Tính năng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
msgid "Fetched elements"
msgstr "Yếu tố được nạp"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__field_names
msgid "Field Names"
msgstr "Tên trường"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model__website_form_default_field_id
msgid "Field for custom form data"
msgstr "Trường cho dữ liệu biểu mẫu tùy chỉnh"

#. module: website
#: model:ir.model,name:website.model_ir_model_fields
msgid "Fields"
msgstr "Trường "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "File Upload"
msgstr "Tệp tải lên"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch_fs
msgid ""
"File from where the view originates.\n"
"                                                          Useful to (hard) reset broken views or to read arch from file in dev-xml mode."
msgstr ""
"Tệp gốc của dạng xem.\n"
"                                                          Giúp đặt lại (đặt về cài đặt gốc) dạng xem bị hỏng hoặc đọc kiến trúc từ tệp ở chế độ dev-xml. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Filet Mignon 8oz"
msgstr "Filet Mignon 226g"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fill"
msgstr "Phủ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Fill and justify"
msgstr "Phủ và căn chỉnh"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__filter_id
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
msgid "Filter"
msgstr "Bộ lọc"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Filter Intensity"
msgstr "Cường độ lọc"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Find a store near you"
msgstr "Tìm cửa hàng ở gần bạn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Find all information about our deliveries, express deliveries and all you "
"need to know to return a product."
msgstr ""
"Tìm mọi thông tin về dịch vụ giao hàng, giao hàng nhanh và thủ tục hoàn trả "
"sản phẩm. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Find out how we were able helping them and set in place solutions adapted to"
" their needs."
msgstr ""
"Tìm hiểu cách chúng tôi giúp đỡ khách hàng và đưa ra giải pháp phù hợp với "
"nhu cầu của họ. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Find the perfect solution for you"
msgstr "Tìm giải pháp hoàn hảo cho bạn"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__create_date
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "First Connection"
msgstr "Kết nối lần đầu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "First Feature"
msgstr "Tính năng đầu tiên"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "First Menu"
msgstr "Menu đầu tiên"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "First Time Only"
msgstr "Chỉ lần đầu tiên"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "First feature"
msgstr "Tính năng đầu tiên"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "First list of Features"
msgstr "Danh sách tính năng đầu tiên"

#. module: website
#: model:ir.model.fields,help:website.field_ir_ui_view__first_page_id
#: model:ir.model.fields,help:website.field_website_page__first_page_id
msgid "First page linked to this view"
msgstr "Trang đầu tiên được liên kết với chế độ xem này"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fit content"
msgstr "Vừa với nội dung"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fit text"
msgstr "Vừa với văn bản"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Fixed"
msgstr "Cố định"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flag"
msgstr "Cờ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flag and Text"
msgstr "Cờ và văn bản"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flash"
msgstr "Flash"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "Flat"
msgstr "Phẳng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flip-In-X"
msgstr "Flip-In-X"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flip-In-Y"
msgstr "Flip-In-Y"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Float"
msgstr "Nổi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
#: model_terms:ir.ui.view,arch_db:website.template_header_boxed_oe_structure_header_boxed_1
#: model_terms:ir.ui.view,arch_db:website.template_header_hamburger_full_oe_structure_header_hamburger_full_1
#: model_terms:ir.ui.view,arch_db:website.template_header_hamburger_oe_structure_header_hamburger_3
#: model_terms:ir.ui.view,arch_db:website.template_header_sidebar_oe_structure_header_sidebar_1
#: model_terms:ir.ui.view,arch_db:website.template_header_vertical_oe_structure_header_vertical_1
msgid "Follow us"
msgstr "Theo dõi chúng tôi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Follow your website traffic in Odoo."
msgstr "Theo dõi lượng truy cập vào website trong Odoo. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Font"
msgstr "Font"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Font Size"
msgstr "Cỡ chữ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Font family"
msgstr "Nhóm font"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Font size"
msgstr "Cỡ chữ"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__footer_visible
msgid "Footer Visible"
msgstr "Hiện chân trang"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.res_config_settings.xml:0
#, python-format
msgid "For session cookies, authentification and analytics,"
msgstr "Đối với cookie phiên, xác thực và phân tích, "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Force your user to create an account per website"
msgstr "Buộc người dùng tạo tài khoản trên mỗi website"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Form"
msgstr "Biểu mẫu"

#. module: website
#: model:ir.model.fields,help:website.field_ir_model__website_form_label
msgid ""
"Form action label. Ex: crm.lead could be 'Send an e-mail' and project.issue "
"could be 'Create an Issue'."
msgstr ""
"Nhãn tác vụ trong biểu mẫu. Ví dụ: crm.lead có thể là 'Gửi email' và "
"project.issue là 'Tạo sự cố.'"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_forum
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Forum"
msgstr "Diễn đàn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Founder and chief visionary, Tony is the driving force behind the company. He loves\n"
"                                to keep his hands full by participating in the development of the software,\n"
"                                marketing, and customer experience strategies."
msgstr ""
"Là nhà sáng lập và có tầm nhìn xa trông rộng, Tony là động lực phát triển của công ty. Anh thích\n"
"                                giữ bản thân bận rộn bằng cách tham gia phát triển phần mềm,\n"
"                                các chiến lược tiếp thị và trải nghiệm khách hàng. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Framed"
msgstr "Khung"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website__auth_signup_uninvited__b2c
msgid "Free sign up"
msgstr "Đăng ký tự do"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
msgid "Friends' Faces"
msgstr "Khuôn mặt bạn bè"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"From seminars to team building activities, we offer a wide choice of events "
"to organize."
msgstr ""
"Từ hội thảo tới hoạt động nhóm, chúng tôi tổ chức đa dạng các sự kiện. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Full"
msgstr "Đầy đủ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Full Screen"
msgstr "Toàn màn hình"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Full Width"
msgstr "Trải hết chiều rộng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Full screen"
msgstr "Toàn màn hình"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Full-Width"
msgstr "Trải hết chiều rộng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Furniture"
msgstr "Đồ nội thất"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#, python-format
msgid "G-XXXXXXXXXX"
msgstr "G-XXXXXXXXXX"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "GPS &amp; navigation"
msgstr "GPS &amp; điều hướng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Gaming"
msgstr "Thiết bị game"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Get Delivered"
msgstr "Nhận giao hàng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Get access to all modules"
msgstr "Truy cập tất cả phân hệ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Get access to all modules and features"
msgstr "Truy cập tất cả phân hệ và tính năng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Get in touch"
msgstr "Liên lạc"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "GitHub"
msgstr "GitHub"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_github
#: model:ir.model.fields,field_description:website.field_website__social_github
msgid "GitHub Account"
msgstr "Tài khoản Github"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_forum
msgid "Give visitors the information they need"
msgstr "Cung cấp thông tin cần thiết cho khách truy cập"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Glasses"
msgstr "Kính"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Go To Page"
msgstr "Đến trang"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Go to"
msgstr "Đến"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_form_extend
msgid "Go to Page Manager"
msgstr "Đi đến Quản lý trang"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#: code:addons/website/static/src/xml/website.backend.xml:0
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#, python-format
msgid "Go to Website"
msgstr "Đi tới website"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "Go to the Theme tab"
msgstr "Đi tới tab Chủ đề"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"Good copy starts with understanding how your product or service helps your "
"customers. Simple words communicate better than big words and pompous "
"language."
msgstr ""
"Bài viết tốt bắt đầu với việc hiểu cách sản phẩm hoặc dịch vụ của bạn giúp "
"khách hàng. Từ ngữ đơn giản truyền thông điệp tốt hơn là những từ to tát và "
"sáo rỗng. "

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "Good job! It's time to <b>Save</b> your work."
msgstr "Rất tốt! Hãy <b>Lưu</b> công việc của bạn. "

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_google_analytics
msgid "Google Analytics"
msgstr "Google Analytics"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_google_analytics_dashboard
msgid "Google Analytics Dashboard"
msgstr "Google Analytics Dashboard"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_analytics_key
#: model:ir.model.fields,field_description:website.field_website__google_analytics_key
msgid "Google Analytics Key"
msgstr "Khóa Google Analytics"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid ""
"Google Analytics initialization failed. Maybe this domain is not whitelisted"
" in your Google Analytics project for this client ID."
msgstr ""
"Khởi tạo Google Analytics không thành công. Có thể tên miền này không nằm "
"trong danh sách được hỗ trợ trong dự án Google Analytics của bạn dành cho ID"
" khách hàng này."

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_management_client_id
#: model:ir.model.fields,field_description:website.field_website__google_management_client_id
msgid "Google Client ID"
msgstr "ID khách hàng Google"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_management_client_secret
#: model:ir.model.fields,field_description:website.field_website__google_management_client_secret
msgid "Google Client Secret"
msgstr "Google Client Secret"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Google Font address"
msgstr "Địa chỉ Google Font"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Google Map"
msgstr "Google Map"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Google Map API Key"
msgstr "Khóa API Google Map"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_google_maps
msgid "Google Maps"
msgstr "Google Maps"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_maps_api_key
#: model:ir.model.fields,field_description:website.field_website__google_maps_api_key
msgid "Google Maps API Key"
msgstr "Khóa API của Google Maps"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_search_console
#: model:ir.model.fields,field_description:website.field_website__google_search_console
msgid "Google Search Console"
msgstr "Google Search Console"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid ""
"Google deprecated both its \"Universal Analytics\" and \"Google Sign-In\" "
"API. It means that only accounts and keys created before 2020 will be able "
"to integrate their Analytics dashboard in Odoo (or any other website). This "
"will be possible only up to mid 2023. After that, those services won't work "
"anymore, at all."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__google_search_console
#: model:ir.model.fields,help:website.field_website__google_search_console
msgid "Google key, or Enable to access first reply"
msgstr "Khóa Google hoặc Bật để truy cập câu trả lời đầu tiên"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Gray #{grayCode}"
msgstr "Xám #{grayCode}"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Grays"
msgstr "Xám"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Great Value"
msgstr "Giá trị lớn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid ""
"Great stories are <b>for everyone</b> even when only written <b>for just one"
" person</b>. If you try to write with a wide, general audience in mind, your"
" story will sound fake and lack emotion. No one will be interested. Write "
"for one person. If it’s genuine for the one, it’s genuine for the rest."
msgstr ""
"Những câu chuyện hay là <b>dành cho mọi người</b> ngay cả khi được sáng tác "
"<b>cho một người duy nhất</b>. Nếu bạn cố viết cho đối tượng khán giả quá "
"rộng, câu chuyện của bạn sẽ trở nên sáo rỗng, thiếu cảm xúc và không thu hút"
" người đọc. Hãy sáng tác cho một người. Câu chuyện chân thực với một người "
"chắc chắn sẽ chân thực với những người khác. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid ""
"Great stories have a <b>personality</b>. Consider telling a great story that"
" provides personality. Writing a story with personality for potential "
"clients will assist with making a relationship connection. This shows up in "
"small quirks like word choices or phrases. Write from your point of view, "
"not from someone else's experience."
msgstr ""
"Những câu chuyện hay đều mang <b>bản sắc riêng</b>. Hãy cân nhắc kể những "
"câu chuyện có cá tính như vậy. Sáng tác những câu chuyện có màu sắc riêng "
"cho khách hàng tương lai sẽ giúp tạo mối quan hệ sâu sắc. Điều này được thể "
"hiện qua cả những chi tiết nhỏ nhất như lựa chọn từ ngữ. Bạn cũng nên viết "
"từ quan điểm của mình chứ không phải từ trải nghiệm của người khác. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Grid"
msgstr "Lưới"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Group By"
msgstr "Nhóm theo"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__groups_id
msgid "Groups"
msgstr "Nhóm"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "H1"
msgstr "H1"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "H2"
msgstr "H2"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "H4 Card title"
msgstr "Tiêu đề thẻ H4"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "H5 Card subtitle"
msgstr "Tiêu đề phụ thẻ H5"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "HTML/CSS/JS Editor"
msgstr "Chỉnh HTML/CSS/JS"

#. module: website
#: model:ir.model,name:website.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP Routing"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Half Screen"
msgstr "Nửa màn hình"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Half screen"
msgstr "Nửa màn hình"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Hamburger Full"
msgstr "Nút hamburger kiểu đầy đủ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Hamburger Type"
msgstr "Kiểu hamburger"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Hamburger menu"
msgstr "Menu hamburger"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
#, python-format
msgid "Happy Odoo Anniversary!"
msgstr "Chúc mừng kỷ niệm Odoo!"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__has_social_default_image
msgid "Has Social Default Image"
msgstr "Có hình ảnh mặc định trên mạng xã hội"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Header"
msgstr "Đầu trang"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__header_color
msgid "Header Color"
msgstr "Màu tiêu đề"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__header_overlay
msgid "Header Overlay"
msgstr "Lớp phủ tiêu đề"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Header Position"
msgstr "Vị trí tiêu đề"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__header_visible
msgid "Header Visible"
msgstr "Hiển thị đầu trang "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 1"
msgstr "Tiêu đề 1"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 2"
msgstr "Tiêu đề 2"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 3"
msgstr "Tiêu đề 3"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 4"
msgstr "Tiêu đề 4"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 5"
msgstr "Tiêu đề 5"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 6"
msgstr "Tiêu đề 6"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Headline"
msgstr "Tiêu đề"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Height"
msgstr "Chiều cao"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Height (Scrolled)"
msgstr "Độ cao (được cuộn)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Help center"
msgstr "Trung tâm hỗ trợ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/translator.xml:0
#, python-format
msgid "Here are the visuals used to help you translate efficiently:"
msgstr "Đây là một số hình ảnh được dùng để giúp bạn dịch hiệu quả: "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Here is an overview of the cookies that may be stored on your device when "
"you visit our website:"
msgstr ""
"Dưới đây là tổng quan về các cookie có thể được lưu trữ trên thiết bị của "
"bạn khi bạn truy cập trang web của chúng tôi:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Hidden"
msgstr "Ẩn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_conditional_visibility
msgid "Hidden for"
msgstr "Ẩn cho"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Hidden on mobile"
msgstr "Ẩn trên di động"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Hide"
msgstr "Ẩn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Hide For"
msgstr "Ẩn với"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Hide this page from search results"
msgstr "Ẩn trang này khỏi kết quả tìm kiếm"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "High"
msgstr "Cao"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"Hint: How to use Google Map on your website (Contact Us page and as a "
"snippet)"
msgstr ""
"Gợi ý: Cách sử dụng Google Map trên website của bạn (Trang Liên hệ và dưới "
"dạng đoạn trích)"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"Hint: Type '/' to search an existing page and '#' to link to an anchor."
msgstr ""
"Gợi ý: bấm '/' để tìm ở các trang hiện có và '#' để liên kết tới một neo. "

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#: model:website.menu,name:website.menu_home
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_tabs
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Home"
msgstr "Trang chủ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Home <span class=\"sr-only\">(current)</span>"
msgstr "Trang chủ <span class=\"sr-only\">(hiện tại)</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Home audio"
msgstr "Thiết bị âm thanh gia đình"

#. module: website
#. openerp-web
#: code:addons/website/models/website.py:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model:ir.model.fields,field_description:website.field_website__homepage_id
#: model:ir.model.fields,field_description:website.field_website_page__is_homepage
#: model_terms:ir.ui.view,arch_db:website.one_page_line
#, python-format
msgid "Homepage"
msgstr "Trang chủ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Hoodies"
msgstr "Áo hoodie"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Horizontal"
msgstr "Ngang"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#, python-format
msgid "Hours"
msgstr "Giờ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "How can we help?"
msgstr "Tôi có thể giúp gì cho bạn?"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "How to get my Client ID"
msgstr "Làm sao lấy được Client ID"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "How to get my Measurement ID"
msgstr "Cách lấy Measurement ID "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "Hybrid"
msgstr "Hỗn hợp"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: model_terms:ir.ui.view,arch_db:website.cookies_bar
#, python-format
msgid "I agree"
msgstr "Tôi đồng ý"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "I want"
msgstr "Tôi muốn"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__id
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__id
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__id
#: model:ir.model.fields,field_description:website.field_theme_website_menu__id
#: model:ir.model.fields,field_description:website.field_theme_website_page__id
#: model:ir.model.fields,field_description:website.field_website__id
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__id
#: model:ir.model.fields,field_description:website.field_website_menu__id
#: model:ir.model.fields,field_description:website.field_website_page__id
#: model:ir.model.fields,field_description:website.field_website_rewrite__id
#: model:ir.model.fields,field_description:website.field_website_robots__id
#: model:ir.model.fields,field_description:website.field_website_route__id
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__id
#: model:ir.model.fields,field_description:website.field_website_track__id
#: model:ir.model.fields,field_description:website.field_website_visitor__id
msgid "ID"
msgstr "ID"

#. module: website
#: model:ir.model.fields,help:website.field_ir_actions_server__xml_id
#: model:ir.model.fields,help:website.field_ir_cron__xml_id
msgid "ID of the action if defined in a XML file"
msgstr "ID của tác vụ nếu được xác định trong tệp XML"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__xml_id
msgid "ID of the view defined in xml file"
msgstr "ID của dạng xem được xác định trong file xml"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__iap_page_code
msgid "Iap Page Code"
msgstr "Mã trang Iap"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__icon
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "Icon"
msgstr "Biểu tượng"

#. module: website
#: model:ir.model.fields,help:website.field_website__specific_user_account
msgid "If True, new accounts will be associated to the current website"
msgstr "Nếu đúng, tài khoản mới sẽ được liên kết với website hiện tại"

#. module: website
#: model:ir.model.fields,help:website.field_website_configurator_feature__menu_sequence
msgid "If set, a website menu will be created for the feature."
msgstr "Nếu đặt, một menu trên website sẽ được tạo cho tính năng này. "

#. module: website
#: model:ir.model.fields,help:website.field_website_configurator_feature__menu_company
msgid ""
"If set, add the menu as a second level menu, as a child of \"Company\" menu."
msgstr "Nếu được đặt, thêm menu ở dạng thứ cấp, menu con của của \"Company\""

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__social_default_image
#: model:ir.model.fields,help:website.field_website__social_default_image
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "If set, replaces the website logo as the default social share image."
msgstr ""
"Nếu được cài, ảnh này sẽ thay thế logo website thành ảnh chia sẻ mạng xã hội"
" mặc định. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid ""
"If this error is caused by a change of yours in the templates, you have the "
"possibility to reset the template to its <strong>factory settings</strong>."
msgstr ""
"Nếu lỗi này do thay đổi của bạn trong các mẫu, bạn có khả năng cài lại mẫu "
"về <strong>thiết lập xuất xưởng</strong>."

#. module: website
#: model:ir.model.fields,help:website.field_website_page__groups_id
msgid ""
"If this field is empty, the view applies to all users. Otherwise, the view "
"applies to the users of those groups only."
msgstr ""
"Nếu trường này trống, chế độ xem sẽ áp dụng cho tất cả người dùng. Nếu "
"không, chế độ xem chỉ áp dụng cho người dùng của các nhóm đó."

#. module: website
#: model:ir.model.fields,help:website.field_website_page__active
msgid ""
"If this view is inherited,\n"
"* if True, the view always extends its parent\n"
"* if False, the view currently does not extend its parent but can be enabled\n"
"         "
msgstr ""
"Nếu dạng xem này được kế thừa,\n"
"* nếu True, dạng xem luôn mở rộng dạng xem chính\n"
"* nếu False, dạng xem hiện không mở rộng dạng xem chính nhưng có thể bật tính năng này\n"
"         "

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid ""
"If you discard the current edits, all unsaved changes will be lost. You can "
"cancel to return to edit mode."
msgstr ""
"Nếu bạn hủy các chỉnh sửa hiện tại, tất cả các thay đổi chưa được lưu sẽ bị "
"mất. Bạn có thể hủy để quay lại chế độ chỉnh sửa."

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_image_gallery/options.js:0
#: model:ir.model.fields,field_description:website.field_website_visitor__partner_image
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_header_brand
#, python-format
msgid "Image"
msgstr "Hình ảnh"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Image Cover"
msgstr "Ảnh bìa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Image Menu"
msgstr "Menu ảnh"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "Image Size"
msgstr "Cỡ hình ảnh"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Image Text Image"
msgstr "Ảnh chữ ảnh"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Images"
msgstr "Hình ảnh"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Images Spacing"
msgstr "Khoảng cách ảnh"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Images Subtitles"
msgstr "Tiêu đề ảnh"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "In main menu"
msgstr "Trên menu chính"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "In the meantime we invite you to visit our"
msgstr "Trong khi chờ đợi, mời bạn ghé thăm "

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/translator.xml:0
#, python-format
msgid ""
"In this mode, you can only translate texts. To change the structure of the page, you must edit the master page.\n"
"        Each modification on the master page is automatically applied to all translated versions."
msgstr ""
"Ở chế độ này, bạn chỉ có thể dịch văn bản. Để thay đổi cấu trúc trang, bạn phải chỉnh sửa trang gốc. \n"
"        Mọi thay đổi ở trang gốc sẽ được tự động áp dụng cho tất cả các phiên bản dịch. "

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__include
msgid "Include"
msgstr "Bao gồm"

#. module: website
#: code:addons/website/controllers/backend.py:0
#, python-format
msgid "Incorrect Client ID / Key"
msgstr "ID/khóa khách hàng không chính xác"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model_terms:ir.ui.view,arch_db:website.index_management
#: model_terms:ir.ui.view,arch_db:website.one_page_line
#, python-format
msgid "Indexed"
msgstr "Được lập chỉ mục"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Indicators"
msgstr "Chỉ báo"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Info"
msgstr "Thông tin"

#. module: website
#: model:website.configurator.feature,description:website.feature_page_about_us
msgid "Info and stats about your company"
msgstr "Thông tin và số liệu về công ty chúng tôi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Information about the"
msgstr "Thông tin về"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__inherit_id
msgid "Inherit"
msgstr "Kế thừa"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__inherit_id
msgid "Inherited View"
msgstr "Dạng xem kế thừa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Inline"
msgstr "Cùng dòng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Inner"
msgstr "Bên trong"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Inner content"
msgstr "Nội dung bên trong"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Input Aligned"
msgstr "Đã căn đầu vào"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Input Type"
msgstr "Loại đầu vào"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Inputs"
msgstr "Đầu vào"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a badge snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a blockquote snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a card snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a chart snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a progress bar snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a rating snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a share snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a text Highlight snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert an alert snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert an horizontal separator sippet."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Insert text styles like headers, bold, italic, lists, and fonts with\n"
"                            a simple WYSIWYG editor. Flexible and easy to use."
msgstr ""
"Chèn các kiểu văn bản như tiêu đề, in đậm, in nghiêng, danh sách và phông chữ bằng\n"
"                            trình sửa WYSIWYG đơn giản. Rất linh hoạt và dễ sử dụng. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Inset"
msgstr "Inset"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Instagram"
msgstr "Instagram"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_instagram
#: model:ir.model.fields,field_description:website.field_website__social_instagram
msgid "Instagram Account"
msgstr "Tài khoản Instagram"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Install"
msgstr "Cài đặt"

#. module: website
#: model:ir.model,name:website.model_base_language_install
msgid "Install Language"
msgstr "Cài đặt ngôn ngữ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Install new language"
msgstr "Cài ngôn ngữ mới"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Installed Applications"
msgstr "Cài ứng dụng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Installed Localizations / Account Charts"
msgstr "Đã cài đặt bản nội địa / Hệ thống tài khoản"

#. module: website
#: model:ir.model.fields,help:website.field_website__theme_id
msgid "Installed theme"
msgstr "Chủ đề đã cài"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Installing \"%s\""
msgstr "Đang cài \"%s\""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Interaction History"
msgstr "Lịch sử tương tác"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Intuitive system"
msgstr "Hệ thống trực quan"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Invalid API Key. The following error was returned by Google:"
msgstr "Khóa API không hợp lệ. Google đã trả lại lỗi sau: "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Iris Joe, CFO"
msgstr "Iris Joe, CFO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Iris, with her international experience, helps us easily understand the "
"numbers and improves them. She is determined to drive success and delivers "
"her professional acumen to bring the company to the next level."
msgstr ""
"Với kinh nghiệm quốc tế của mình,  Iris giúp chúng tôi dễ dàng hiểu các chỉ "
"số và cải thiện chúng. Cô luôn quyết tâm đạt được thành công và thể hiện sự "
"nhạy bén trong nghề nghiệp của mình để đưa công ty phát triển lên một tầm "
"cao mới."

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__website_indexed
msgid "Is Indexed"
msgstr "Được lập chỉ mục "

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_module_module__is_installed_on_current_website
msgid "Is Installed On Current Website"
msgstr "Được cài đặt trên website hiện tại"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__is_mega_menu
msgid "Is Mega Menu"
msgstr "Là Mega menu"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_partner__is_published
#: model:ir.model.fields,field_description:website.field_res_users__is_published
#: model:ir.model.fields,field_description:website.field_website_page__is_published
#: model:ir.model.fields,field_description:website.field_website_published_mixin__is_published
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__is_published
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__is_published
#: model_terms:ir.ui.view,arch_db:website.website_pages_tree_view
msgid "Is Published"
msgstr "Được đăng"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__is_visible
#: model:ir.model.fields,field_description:website.field_website_page__is_visible
msgid "Is Visible"
msgstr "Hiện"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is after"
msgstr "Sau"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is after or equal to"
msgstr "Sau hoặc bằng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is before"
msgstr "Trước"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is before or equal to"
msgstr "Trước hoặc bằng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is between (included)"
msgstr "Giữa (đã bao gồm)"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__is_connected
msgid "Is connected ?"
msgstr "Đã kết nối ?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is equal to"
msgstr "Bằng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is greater than"
msgstr "Lớn hơn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is greater than or equal to"
msgstr "Lớn hơn hoặc bằng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is less than"
msgstr "Nhỏ hơn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is less than or equal to"
msgstr "Nhỏ hơn hoặc bằng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is not between (excluded)"
msgstr "Không ở giữa (loại trừ)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is not equal to"
msgstr "Không bằng"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#, python-format
msgid "Is not set"
msgstr "Không được đặt"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#, python-format
msgid "Is set"
msgstr "Được đặt"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"It appears you are in debug=assets mode, all theme customization options "
"require a page reload in this mode."
msgstr ""
"Có vẻ như bạn đang ở chế độ debug = asset, tất cả các tùy chọn tùy chỉnh chủ"
" đề đều yêu cầu tải lại trang ở chế độ này."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid ""
"It appears your website is still using the old color system of\n"
"            Odoo 13.0 in some places. We made sure it is still working but\n"
"            we recommend you to try to use the new color system, which is\n"
"            still customizable."
msgstr ""
"Có vẻ một số chỗ trong website của bạn vẫn đang dùng hệ màu cũ\n"
"            của Odoo 13.0. Chúng tôi đảm bảo hệ thống này vẫn hoạt động nhưng\n"
"            chúng tôi khuyên bạn thử dùng hệ thống màu mới vẫn có tính năng\n"
"            tùy chỉnh."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "It looks like your file is being called by"
msgstr "Có vẻ như tệp của bạn đang được gọi bởi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Item"
msgstr "Mục"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Item 1"
msgstr "Mục 1"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Item 2"
msgstr "Mục 2"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options
msgid "Items per row"
msgstr "Số mục mỗi hàng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.dynamic_snippet_carousel_options_template
msgid "Items per slide"
msgstr "Mục trên mỗi slide"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Jacket"
msgstr "Áo khoác"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Jeans"
msgstr "Quần bò"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Job Offer"
msgstr "Thông tin việc làm"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
msgid "Join us and make your company a better place."
msgstr "Hãy để chúng tôi cùng bạn tạo dựng công ty tốt đẹp hơn. "

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Keep empty to use default value"
msgstr "Giữ trống để sử dụng giá trị mặc định"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_asset__key
#: model:ir.model.fields,field_description:website.field_ir_attachment__key
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__key
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__key
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__key
#: model:ir.model.fields,field_description:website.field_website_page__key
msgid "Key"
msgstr "Khóa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Keyboards"
msgstr "Bàn phím"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Keyword"
msgstr "Từ khóa"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Keywords"
msgstr " Từ khóa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Label"
msgstr "Nhãn"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model__website_form_label
msgid "Label for form action"
msgstr "Nhãn cho tác vụ trong mẫu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Labels Width"
msgstr "Độ rộng nhãn"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__lang_id
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Language"
msgstr "Ngôn ngữ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Language Selector"
msgstr "Bộ chọn ngôn ngữ"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__lang_id
msgid "Language from the website when visitor has been created"
msgstr "Ngôn ngữ từ trang web khi khách truy cập đã được tạo"

#. module: website
#: model:ir.model,name:website.model_res_lang
#: model:ir.model.fields,field_description:website.field_res_config_settings__language_ids
#: model:ir.model.fields,field_description:website.field_website__language_ids
msgid "Languages"
msgstr "Ngôn ngữ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Languages available on your website"
msgstr "Ngôn ngữ có trên website"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Laptops"
msgstr "Laptop"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Large"
msgstr "Rộng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Last 7 Days"
msgstr "7 ngày qua"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Last Action"
msgstr "Tác vụ cuối"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__last_connection_datetime
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Last Connection"
msgstr "Kết nối mới nhất"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "Last Feature"
msgstr "Tính năng cuối"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Last Menu"
msgstr "Menu cuối"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset____last_update
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment____last_update
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view____last_update
#: model:ir.model.fields,field_description:website.field_theme_website_menu____last_update
#: model:ir.model.fields,field_description:website.field_theme_website_page____last_update
#: model:ir.model.fields,field_description:website.field_website____last_update
#: model:ir.model.fields,field_description:website.field_website_configurator_feature____last_update
#: model:ir.model.fields,field_description:website.field_website_menu____last_update
#: model:ir.model.fields,field_description:website.field_website_page____last_update
#: model:ir.model.fields,field_description:website.field_website_rewrite____last_update
#: model:ir.model.fields,field_description:website.field_website_robots____last_update
#: model:ir.model.fields,field_description:website.field_website_route____last_update
#: model:ir.model.fields,field_description:website.field_website_snippet_filter____last_update
#: model:ir.model.fields,field_description:website.field_website_track____last_update
#: model:ir.model.fields,field_description:website.field_website_visitor____last_update
msgid "Last Modified on"
msgstr "Sửa lần cuối vào"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Last Month"
msgstr "Tháng trước"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
msgid "Last Page"
msgstr "Trang cuối"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__write_uid
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__write_uid
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__write_uid
#: model:ir.model.fields,field_description:website.field_theme_website_menu__write_uid
#: model:ir.model.fields,field_description:website.field_theme_website_page__write_uid
#: model:ir.model.fields,field_description:website.field_website__write_uid
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__write_uid
#: model:ir.model.fields,field_description:website.field_website_menu__write_uid
#: model:ir.model.fields,field_description:website.field_website_page__write_uid
#: model:ir.model.fields,field_description:website.field_website_rewrite__write_uid
#: model:ir.model.fields,field_description:website.field_website_robots__write_uid
#: model:ir.model.fields,field_description:website.field_website_route__write_uid
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__write_uid
#: model:ir.model.fields,field_description:website.field_website_visitor__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__write_date
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__write_date
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__write_date
#: model:ir.model.fields,field_description:website.field_theme_website_menu__write_date
#: model:ir.model.fields,field_description:website.field_theme_website_page__write_date
#: model:ir.model.fields,field_description:website.field_website__write_date
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__write_date
#: model:ir.model.fields,field_description:website.field_website_menu__write_date
#: model:ir.model.fields,field_description:website.field_website_page__write_date
#: model:ir.model.fields,field_description:website.field_website_rewrite__write_date
#: model:ir.model.fields,field_description:website.field_website_robots__write_date
#: model:ir.model.fields,field_description:website.field_website_route__write_date
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__write_date
#: model:ir.model.fields,field_description:website.field_website_visitor__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__last_visited_page_id
msgid "Last Visited Page"
msgstr "Trang truy cập lần cuối"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Last Week"
msgstr "Tuần trước"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Last Year"
msgstr "Năm ngoái"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__time_since_last_action
msgid "Last action"
msgstr "Tác vụ cuối"

#. module: website
#: code:addons/website/controllers/main.py:0
#, python-format
msgid "Last modified pages"
msgstr "Trang sửa đổi lần cuối"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__last_connection_datetime
msgid "Last page view date"
msgstr "Ngày cuối cùng xem trang"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Latests news and case studies"
msgstr "Tin tức mới nhất và case study"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Layout"
msgstr "Bố cục"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Layout Background"
msgstr "Nền bố cục"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Layout Background Color"
msgstr "Màu nền bố cục"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "Learn more"
msgstr "Tìm hiểu thêm"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Left"
msgstr "Trái"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menu_image_menu
msgid "Left Menu"
msgstr "Menu bên trái"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Legal"
msgstr "Pháp lý"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Legal Notice"
msgstr "Thông báo pháp lý"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Legend"
msgstr "Chú giải"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Let your customers follow <br/>and understand your process."
msgstr "Hãy để khách hàng theo dõi <br/> và hiểu quy trình của bạn."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Let your customers log in to see their documents"
msgstr "Cho phép khách hàng đăng nhập để xem các tài liệu của họ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Let's do it"
msgstr "Cùng làm thôi"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Let's go!"
msgstr "Đi thôi nào!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Library"
msgstr "Thư viện"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
msgid "Light"
msgstr "Sáng"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__limit
msgid "Limit"
msgstr "Giới hạn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Limited customization"
msgstr "Tuỳ chỉnh hạn chế"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Line"
msgstr "Đường"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline_options
msgid "Line Color"
msgstr "Màu dòng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Link"
msgstr "Liên kết"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Link Anchor"
msgstr "Liên kết neo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Link Style"
msgstr "Kiểu link"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Link button"
msgstr "Nút liên kết"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Link text"
msgstr "Nội dung link"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_linkedin
#: model:ir.model.fields,field_description:website.field_website__social_linkedin
msgid "LinkedIn Account"
msgstr "Tài khoản LinkedIn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Linkedin"
msgstr "Linkedin"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Links"
msgstr "Liên kết"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Links Style"
msgstr "Kiểu link"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Links to other Websites"
msgstr "Liên kết đến các websites khác"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Little Icons"
msgstr "Icon nhỏ"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_live_chat
msgid "Live Chat"
msgstr "Live Chat"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_kanban
msgid "Live Preview"
msgstr "Xem trước trực tiếp"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Livechat Widget"
msgstr "Tiện ích Livechat"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#: code:addons/website/static/src/xml/website.background.video.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Loading..."
msgstr "Đang tải..."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Logo"
msgstr "Logo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Logo Type"
msgstr "Kiểu logo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Logo of MyCompany"
msgstr "Logo của MyCompany"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Logos"
msgstr "Logo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Long Text"
msgstr "Văn bản dài"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Low"
msgstr "Thấp"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Magazine"
msgstr "Tạp chí"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Main Course"
msgstr "Món chính"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__menu_id
msgid "Main Menu"
msgstr "Menu chính"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#, python-format
msgid "Main actions"
msgstr "Tác vụ chính"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Make sure billing is enabled"
msgstr "Đảm bảo tính năng lập hoá đơn được bật"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"Make sure to wait if errors keep being shown: sometimes enabling an API "
"allows to use it immediately but Google keeps triggering errors for a while"
msgstr ""
"Hãy chờ đợi nếu lỗi tiếp tục hiển thị: đôi khi việc bật API cho phép sử dụng"
" API ngay lập tức nhưng Google vẫn tiếp tục báo lỗi trong một thời gian"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Make sure your settings are properly configured:"
msgstr "Đảm bảo cài đặt của bạn được định cấu hình chính xác:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Manage Pages"
msgstr "Quản lý trang"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "Manage Your Pages"
msgstr "Quản lý trang của bạn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Manage Your Website Pages"
msgstr "Quản lý các trang trong website"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Manage this page"
msgstr "Quản lý trang này"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Maps JavaScript API"
msgstr "Maps JavaScript API"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Maps Static API"
msgstr "Maps Static API"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Mark Text"
msgstr "Kiểu đánh dấu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Marked Fields"
msgstr "Trường đánh dấu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "Marker style"
msgstr "Kiểu đánh dấu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Marketplace"
msgstr "Sàn giao dịch"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Masonry"
msgstr "Gạch lát"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Measurement ID"
msgstr "Measurement ID"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "Media"
msgstr "Phương tiện"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Media heading"
msgstr "Tiêu đề phương tiện"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Medium"
msgstr "Trung bình"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Mega Menu"
msgstr "Mega Menu"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__mega_menu_classes
msgid "Mega Menu Classes"
msgstr "Các lớp Mega menu"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__mega_menu_content
msgid "Mega Menu Content"
msgstr "Nội dung Mega Menu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Men"
msgstr "Nam"

#. module: website
#: code:addons/website/models/website.py:0
#: model:ir.model.fields,field_description:website.field_website_menu__name
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Menu"
msgstr "Menu"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__menu_company
msgid "Menu Company"
msgstr "Menu công ty"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menu_image_menu
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Menu Item %s"
msgstr "Mục menu %s"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Menu Label"
msgstr "Nhãn menu"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__menu_sequence
msgid "Menu Sequence"
msgstr "Thứ tự menu"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_menu__copy_ids
msgid "Menu using a copy of me"
msgstr "Menu sử dụng bản sao của tôi"

#. module: website
#: code:addons/website/models/website.py:0
#: model:ir.ui.menu,name:website.menu_website_menu_list
#, python-format
msgid "Menus"
msgstr "Menu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
msgid "Messages"
msgstr "Tin nhắn"

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "Metadata"
msgstr "Metadata"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Mich Stark, COO"
msgstr "Mich Stark, COO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Mich loves taking on challenges. With his multi-year experience as "
"Commercial Director in the software industry, Mich has helped the company to"
" get where it is today. Mich is among the best minds."
msgstr ""
"Mich luôn chấp nhận mọi thử thách. Với nhiều năm kinh nghiệm làm Giám đốc "
"thương mại trong lĩnh vực phần mềm, Mich đã giúp công ty có được vị trí như "
"ngày hôm nay. Mich cũng là một người hết sức uyên bác. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Middle"
msgstr "Giữa"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Min-Height"
msgstr "Chiều cao tối thiểu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Minimalist"
msgstr "Tối giản"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#, python-format
msgid "Minutes"
msgstr "Phút"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#: model:ir.model.fields,field_description:website.field_website_visitor__mobile
#, python-format
msgid "Mobile"
msgstr "Di động"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Mobile Alignment"
msgstr "Căn chỉnh trên điện thoại"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Mobile menu"
msgstr "Menu di động"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/mobile_view.js:0
#, python-format
msgid "Mobile preview"
msgstr "Xem bản di động"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__mode
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Mode"
msgstr "Chế độ"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__model
msgid "Model"
msgstr "Mô hình"

#. module: website
#: model:ir.model,name:website.model_ir_model_data
#: model:ir.model.fields,field_description:website.field_website_page__model_data_id
msgid "Model Data"
msgstr "Dữ liệu mô hình"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__model_name
msgid "Model name"
msgstr "Tên mô hình"

#. module: website
#: model:ir.model,name:website.model_ir_model
msgid "Models"
msgstr "Mô hình"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_updated
msgid "Modified Architecture"
msgstr "Kiến trúc đã sửa đổi"

#. module: website
#: model:ir.model,name:website.model_ir_module_module
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__module_id
msgid "Module"
msgstr "Mô đun"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Monitor Google Search results data"
msgstr "Theo dõi dữ liệu kết quả của Google Search"

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_0
msgid ""
"Monitor your visitors while they are browsing your website with the Odoo "
"Social app. Engage with them in just a click using a live chat request or a "
"push notification. If they have completed one of your forms, you can send "
"them an SMS, or call them right away while they are browsing your website."
msgstr ""
"Giám sát khách truy cập đang duyệt trang web của bạn bằng ứng dụng Odoo Xã "
"hội. Tương tác với họ chỉ bằng một cú nhấp chuột bằng cách sử dụng yêu cầu "
"trò chuyện trực tuyến hoặc thông báo đẩy. Nếu họ đã điền một trong các biểu "
"mẫu của bạn, bạn có thể gửi SMS hoặc gọi cho họ ngay trong khi họ đang duyệt"
" trang web của bạn."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Monitors"
msgstr "Màn hình"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_color_blocks_2
msgid "More Details"
msgstr "Thêm chi tiết"

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_4
msgid ""
"More than 90 shapes exist and their colors are picked to match your Theme."
msgstr ""
"Có hơn 90 hình dạng và màu sắc được chọn cho phù hợp với Chủ đề của bạn. "

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "More than one group has been set on the view."
msgstr "Nhiều nhóm đã được đặt cho chế độ xem. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Mosaic"
msgstr "Mảnh ghép"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Most searched topics related to your keyword, ordered by importance"
msgstr ""
"Các chủ đề được tìm kiếm nhiều nhất có liên quan đến từ khóa, sắp xếp theo "
"tầm quan trọng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Mouse"
msgstr "Chuột"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move Backward"
msgstr "Tiến về sau"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move Forward"
msgstr "Tiến về trước"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Move to first"
msgstr "Chuyển lên đầu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Move to last"
msgstr "Chuyển xuống cuối"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Move to next"
msgstr "Chuyển về sau"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Move to previous"
msgstr "Chuyển lên trước"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Multi Menus"
msgstr "Nhiều menu"

#. module: website
#: model:ir.model,name:website.model_website_multi_mixin
msgid "Multi Website Mixin"
msgstr "Mixin đa trang web"

#. module: website
#: model:ir.model,name:website.model_website_published_multi_mixin
msgid "Multi Website Published Mixin"
msgstr "Mixin được đăng trên nhiều website"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__group_multi_website
#: model:res.groups,name:website.group_multi_website
msgid "Multi-website"
msgstr "Nhiều website"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Multimedia"
msgstr "Đa phương tiện"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Multiple Checkboxes"
msgstr "Nhiều hộp kiểm"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_qweb
msgid "Multiple tree exists for this view"
msgstr "Nhiều cây cùng tồn tại cho dạng xem này"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_page
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid "My Company"
msgstr "My Company"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.option_header_brand_name
#: model_terms:ir.ui.view,arch_db:website.option_header_off_canvas
msgid "My Website"
msgstr "Website của tôi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "MyCompany"
msgstr "MyCompany"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__name
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__name
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__name
#: model:ir.model.fields,field_description:website.field_theme_website_menu__name
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__name
#: model:ir.model.fields,field_description:website.field_website_rewrite__name
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__name
#: model:ir.model.fields,field_description:website.field_website_visitor__name
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
#: model_terms:ir.ui.view,arch_db:website.menu_search
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
#, python-format
msgid "Name"
msgstr "Tên"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Name (A-Z)"
msgstr "Tên (A-Z)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Name (Z-A)"
msgstr "Tên (Z-A)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Name and favicon of your website"
msgstr "Tên và biểu tượng website của bạn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_qweb
msgid "Name, id or key"
msgstr "Tên, ID hoặc khóa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Narrow"
msgstr "Hẹp"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Navbar"
msgstr "Navbar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Need to pick up your order at one of our stores? Discover the nearest to "
"you."
msgstr "Muốn nhận hàng ở cửa hàng? Khám phá cửa hàng gần bạn nhất. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Network Advertising Initiative opt-out page"
msgstr "Trang hủy đăng ký quảng cáo Network Advertising Initiative "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Networks"
msgstr "Mạng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "New"
msgstr "Mới"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid ""
"New Google Analytics accounts and keys are now using Google Analytics 4 "
"which, for now, can't be integrated/embed in external websites."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "New Page"
msgstr "Trang mới"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_menu__new_window
#: model:ir.model.fields,field_description:website.field_website_menu__new_window
msgid "New Window"
msgstr "Cửa sổ mới"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "New collection"
msgstr "Bộ sưu tập mới"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup
msgid "New customer"
msgstr "Khách hàng mới"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "New page"
msgstr "Trang mới"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_news
msgid "News"
msgstr "Tin tức"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Newsletter"
msgstr "Bản tin"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Newsletter Popup"
msgstr "Bản tin popup"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Next"
msgstr "Tiếp"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "No Animation"
msgstr "Không hiệu ứng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "No Slide Effect"
msgstr "Không hiệu ứng Slide"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_widget.xml:0
#, python-format
msgid "No Url"
msgstr "Không có Url"

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitors_action
msgid "No Visitors yet!"
msgstr "Chưa có khách truy cập!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "No customization"
msgstr "Không có tùy chỉnh"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "No matching record !"
msgstr "Không có hồ sơ phù hợp !"

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitor_page_action
msgid "No page views yet for this visitor"
msgstr "Khách truy cập này chưa có lượt xem trang nào"

#. module: website
#: model_terms:ir.actions.act_window,help:website.visitor_partner_action
msgid "No partner linked for this visitor"
msgstr "Không có đối tác được liên kết với khách truy cập này"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "No result found, broaden your search."
msgstr "Không có kết quả, hãy mở rộng tìm kiếm. "

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_searchbar/000.xml:0
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
#, python-format
msgid "No results found for '"
msgstr "Không có kết quả cho '"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_searchbar/000.xml:0
#, python-format
msgid "No results found. Please try another search."
msgstr "Không tìm thấy kết quả. Vui lòng tìm bằng từ khóa khác. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "No support"
msgstr "Không hỗ trợ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_header_brand
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "None"
msgstr "Không "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Normal"
msgstr "Thông thường"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Not SEO optimized"
msgstr "Không được tối ưu SEO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Not in main menu"
msgstr "Không có trong menu chính"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Not indexed"
msgstr "Không được lập chỉ mục"

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_3
msgid ""
"Not only can you search for royalty-free illustrations, their colors are "
"also converted so that they always fit your Theme."
msgstr ""
"Bạn không chỉ tìm thấy nhiều hình minh họa miễn phí bản quyền mà màu sắc các"
" hình đó cũng được chuyển đổi cho phù hợp với chủ đề của bạn. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Not published"
msgstr "Chưa đăng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Not tracked"
msgstr "Không theo dõi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Note that some third-party services may install additional cookies on your "
"browser in order to identify you."
msgstr ""
"Lưu ý rằng một số dịch vụ của bên thứ ba có thể cài đặt thêm cookie trên "
"trình duyệt của bạn để nhận dạng bạn."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Note: To hide this page, uncheck it from the top Customize menu."
msgstr "Chú ý: Để ẩn trang này, bỏ chọn từ menu Tùy chỉnh ở phía trên."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Nothing"
msgstr "Không"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Number"
msgstr "Số"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_language_count
msgid "Number of languages"
msgstr "Số lượng ngôn ngữ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "OR"
msgstr "HOẶC"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Odoo Logo"
msgstr "Odoo Logo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Odoo Menu"
msgstr "Odoo Menu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Odoo Version"
msgstr "Phiên bản Odoo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Off-Canvas"
msgstr "Off-Canvas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Office audio"
msgstr "Thiết bị âm thanh văn phòng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Office screens"
msgstr "Màn hình văn phòng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Offline"
msgstr "Offline"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "Ok"
msgstr "Ok"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "Ok, never show me this again"
msgstr "Ok, không bao giờ hiển thị lại"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "On Click"
msgstr "Khi bấm"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "On Exit"
msgstr "Khi thoát"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "On Hover"
msgstr "Khi di chuột"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "On Success"
msgstr "Điền xong"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "On Website"
msgstr "Trên website"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website__auth_signup_uninvited__b2b
msgid "On invitation"
msgstr "Khi mời"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"Once the selection of available websites by domain is done, you can filter "
"by country group."
msgstr ""
"Khi lựa chọn các trang web có sẵn theo tên miền được thực hiện, bạn có thể "
"lọc theo nhóm quốc gia."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid ""
"Once the user closes the popup, it won't be shown again for that period of "
"time."
msgstr ""
"Khi người dùng đóng popup, popup sẽ không hiển thị lại trong khoảng thời "
"gian đó. "

#. module: website
#: code:addons/website/models/website_configurator_feature.py:0
#, python-format
msgid ""
"One and only one of the two fields 'page_view_id' and 'module_id' should be "
"set"
msgstr ""
"Một và chỉ một trong hai trường 'page_view_id' và 'module_id' phải được đặt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Online"
msgstr "Online"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__mode
msgid ""
"Only applies if this view inherits from an other one (inherit_id is not False/Null).\n"
"\n"
"* if extension (default), if this view is requested the closest primary view\n"
"is looked up (via inherit_id), then all views inheriting from it with this\n"
"view's model are applied\n"
"* if primary, the closest primary view is fully resolved (even if it uses a\n"
"different model than this one), then this view's inheritance specs\n"
"(<xpath/>) are applied, and the result is used as if it were this view's\n"
"actual arch.\n"
msgstr ""
"Chỉ áp dụng nếu dạng xem này kế thừa từ một dạng xem khác (inherit_id không phải là False/Null).\n"
"\n"
"* nếu mở rộng (mặc định), nếu dạng xem này yêu cầu dạng xem chính gần nhất\n"
"được tra cứu (qua inherit_id), thì tất cả dạng xem kế thừa từ đó với \n"
"mô hình của dạng xem này được áp dụng\n"
"* nếu chính, dạng xem chính gần nhất được hiển thị hoàn toàn (thậm chí nếu sử dụng\n"
"mô hình khác dạng xem này), thì thông số kế thừa của dạng xem này\n"
"(<xpath/>) được áp dụng, và kết quả được sử dụng như thể là kiến trúc\n"
"thực của dạng xem này. \n"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Open Source ERP"
msgstr "hệ thống ERP mã nguồn mở"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Optimize SEO"
msgstr "Tối ưu hoá SEO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Optimize SEO of this page"
msgstr "Tối ưu SEO của trang này"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Option 1"
msgstr "Lựa chọn 1"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Option 2"
msgstr "Lựa chọn 2"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Option 3"
msgstr "Lựa chọn 3"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Optional"
msgstr "Tuỳ chọn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Order by"
msgstr "Sắp xếp theo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Order now"
msgstr "Đặt hàng ngay"

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "Other Information:"
msgstr "Thông tin khác: "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Our Company"
msgstr "Công ty của chúng tôi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid "Our References"
msgstr "Khách hàng tham khảo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Our seminars and trainings for you"
msgstr "Hội thảo và tập huấn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "Our team"
msgstr "Đội ngũ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "Our team will message you back as soon as possible."
msgstr "Chúng tôi sẽ trả lời bạn trong thời gian sớm nhất. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Outline"
msgstr "Đường viền"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Outset"
msgstr "Outset"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "Outstanding images"
msgstr "Hình ảnh nổi bật"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Over The Content"
msgstr "Trên nội dung"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Paddings"
msgstr "Đệm"

#. module: website
#: code:addons/website/models/website.py:0
#: code:addons/website/models/website.py:0
#: model:ir.model,name:website.model_website_page
#: model:ir.model.fields,field_description:website.field_ir_ui_view__page_ids
#: model:ir.model.fields,field_description:website.field_theme_website_menu__page_id
#: model:ir.model.fields,field_description:website.field_website_page__page_ids
#: model:ir.model.fields,field_description:website.field_website_track__page_id
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#, python-format
msgid "Page"
msgstr "Trang"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "Page <b>%s</b> contains a link to this page"
msgstr "Trang <b>%s</b> chứa link đến trang này"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "Page <b>%s</b> is calling this file"
msgstr "Trang <b>%s</b> đang gọi tệp"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_page__website_indexed
msgid "Page Indexed"
msgstr "Trang được lập chỉ mục"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Page Layout"
msgstr "Bố cục trang"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "Page Name"
msgstr "Tên trang"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Page Properties"
msgstr "Thuộc tính trang"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Page Title"
msgstr "Tiêu đề trang"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields,field_description:website.field_website_page__url
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
#, python-format
msgid "Page URL"
msgstr "URL trang"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__page_view_id
msgid "Page View"
msgstr "Xem trang"

#. module: website
#: model:ir.actions.act_window,name:website.website_visitor_view_action
#: model:ir.model.fields,field_description:website.field_website_visitor__visitor_page_count
#: model:ir.ui.menu,name:website.menu_visitor_view_menu
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Page Views"
msgstr "Lượt xem trang"

#. module: website
#: model:ir.actions.act_window,name:website.website_visitor_page_action
msgid "Page Views History"
msgstr "Lịch sử xem trang"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Page Visibility"
msgstr "Hiển thị trang"

#. module: website
#: model:ir.model.fields,help:website.field_website_configurator_feature__iap_page_code
msgid ""
"Page code used to tell IAP website_service for which page a snippet list "
"should be generated"
msgstr ""
"Mã trang được sử dụng để cho IAP website_service biết danh sách snippet được"
" tạo cho trang nào"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_page__copy_ids
msgid "Page using a copy of me"
msgstr "Trang sử dụng bản sao của tôi"

#. module: website
#. openerp-web
#: code:addons/website/models/website.py:0
#: code:addons/website/models/website.py:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: model:ir.ui.menu,name:website.menu_website_pages_list
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
#, python-format
msgid "Pages"
msgstr "Trang"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Pagination"
msgstr "Phân trang"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Pants"
msgstr "Quần"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid ""
"Paragraph text. Lorem <b>ipsum dolor sit amet</b>, consectetur adipiscing "
"elit. <i>Integer posuere erat a ante</i>."
msgstr ""
"Đoạn văn. Lorem <b>ipsum dolor sit amet</b>, consectetur adipiscing elit. "
"<i>Integer posuere erat a ante</i>."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid ""
"Paragraph with <strong>bold</strong>, <span class=\"text-"
"muted\">muted</span> and <em>italic</em> texts"
msgstr ""
"Đoạn văn với văn bản <strong>in đậm</strong>, <span class=\"text-muted\">in "
"mờ</span> và <em>in nghiêng</em>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Paragraph."
msgstr "Đoạn văn."

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_menu__parent_id
msgid "Parent"
msgstr "Chính"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__parent_id
msgid "Parent Menu"
msgstr "Menu chính"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__parent_path
msgid "Parent Path"
msgstr "Đường dẫn chính"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__partner_id
msgid "Partner of the last logged in user."
msgstr "Đối tác của người dùng đăng nhập cuối cùng."

#. module: website
#: model:ir.model.fields,help:website.field_website__partner_id
msgid "Partner-related data of the user"
msgstr "Dữ liệu liên quan tới đối tác của người dùng"

#. module: website
#: model:ir.actions.act_window,name:website.visitor_partner_action
msgid "Partners"
msgstr "Đối tác"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Password"
msgstr "Mật khẩu"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__path
msgid "Path"
msgstr "Đường dẫn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Pattern"
msgstr "Hoa văn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Pay"
msgstr "Thanh toán"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Phone Number"
msgstr "Số Điện thoại"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Phones"
msgstr "Điện thoại"

#. module: website
#: model:ir.actions.act_window,name:website.theme_install_kanban_action
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Pick a Theme"
msgstr "Chọn một chủ đề"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Pie"
msgstr "Tròn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Pill"
msgstr "Viên"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Pills"
msgstr "Viên "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Placeholder"
msgstr "Trình giữ chỗ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Places API"
msgstr "API địa điểm"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Plain"
msgstr "Trơn"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/res_config_settings.js:0
#, python-format
msgid "Please confirm"
msgstr "Vui lòng xác nhận"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "Please fill in the form correctly."
msgstr "Vui lòng điền biểu mẫu chính xác. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "Points of sale"
msgstr "Điểm bán hàng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Popup"
msgstr "Popup"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Portfolio"
msgstr "Hồ sơ công ty"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
msgid "Position"
msgstr "Vị trí"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Post heading"
msgstr "Tiêu đề bài đăng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Postcard"
msgstr "Bưu thiếp"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Preferences"
msgstr "Tùy chỉnh cá nhân"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__prepend
msgid "Prepend"
msgstr "Prepend"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Preset"
msgstr "Kiểu định trước "

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "Press"
msgstr "Nhấn"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/view_hierarchy.js:0
#, python-format
msgid "Press %s for next %s"
msgstr "Nhấn %s để xem %s tiếp theo"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Preview"
msgstr "Xem trước"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Previous"
msgstr "Trước "

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_prev
msgid "Previous View Architecture"
msgstr "Chế độ xem kiến trúc trước đó"

#. module: website
#: model:website.configurator.feature,name:website.feature_page_pricing
#: model_terms:ir.ui.view,arch_db:website.pricing
msgid "Pricing"
msgstr "Chi tiết giá"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Primary"
msgstr "Chính"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Primary Style"
msgstr "Kiểu chính"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Printers"
msgstr "Máy In"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__priority
msgid "Priority"
msgstr "Độ ưu tiên"

#. module: website
#: code:addons/website/models/website.py:0
#: model:website.configurator.feature,name:website.feature_page_privacy_policy
#: model_terms:ir.ui.view,arch_db:website.privacy_policy
#, python-format
msgid "Privacy Policy"
msgstr "Chính sách quyền riêng tư"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog_add_product_widget
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Product"
msgstr "Sản phẩm"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Products"
msgstr "Sản phẩm"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Professional"
msgstr "Chuyên nghiệp"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Professional themes"
msgstr "Chủ đề chuyên nghiệp"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "Profile"
msgstr "Hồ sơ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Progress Bar"
msgstr "Thanh tiến trình"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Progress Bar Color"
msgstr "Màu thanh tiến độ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Progress Bar Style"
msgstr "Kiểu thanh tiến độ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Progress Bar Weight"
msgstr "Độ dày thanh tiến độ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Projectors"
msgstr "Máy chiếu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Promote"
msgstr "Quảng bá"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Promote page on the web"
msgstr "Quảng bá trang trên web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Promotions"
msgstr "Khuyến mãi"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Public"
msgstr "Công khai"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__partner_id
msgid "Public Partner"
msgstr "Đối tác công cộng"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__user_id
msgid "Public User"
msgstr "Người dùng công cộng"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/button.js:0
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Publish"
msgstr "Đăng"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_career
msgid "Publish job offers and let people apply"
msgstr "Đăng thông tin việc làm và cho mọi người ứng tuyển"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_event
msgid "Publish on-site and online events"
msgstr "Đăng sự kiện tại chỗ và trực tuyến"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/button.js:0
#: code:addons/website/static/src/js/backend/button.js:0
#: model_terms:ir.ui.view,arch_db:website.one_page_line
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.publish_short
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
#, python-format
msgid "Published"
msgstr "Đã đăng"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields,field_description:website.field_website_page__date_publish
#, python-format
msgid "Publishing Date"
msgstr "Ngày đăng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Pulse"
msgstr "Pulse"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Purpose"
msgstr "Mục đích"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_highlight
msgid "Put the focus on what you have to say!"
msgstr "Hãy tập trung vào những gì bạn cần nói!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating
msgid "Quality"
msgstr "Chất lượng"

#. module: website
#: model:ir.model,name:website.model_ir_qweb
msgid "Qweb"
msgstr "Qweb"

#. module: website
#: model:ir.model,name:website.model_ir_qweb_field_contact
msgid "Qweb Field Contact"
msgstr "Trường liên hệ Qweb"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Radar"
msgstr "Radar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Radio Buttons"
msgstr "Nút tròn"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Rating"
msgstr "Đánh giá"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Re-order"
msgstr "Sắp xếp lại"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Ready to build the"
msgstr "Bạn đã sẵn sàng dựng "

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Recipient Email"
msgstr "Email người nhận"

#. module: website
#: model:ir.model,name:website.model_ir_rule
msgid "Record Rule"
msgstr "Quy tắc lưu hồ sơ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Redirect"
msgstr "Chuyển hướng"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Redirect Old URL"
msgstr "Chuyển hướng URL cũ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Redirect to URL in a new tab"
msgstr "Chuyển hướng đến URL trong tab mới"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
msgid "Redirection Type"
msgstr "Loại chuyển hướng"

#. module: website
#: model:ir.ui.menu,name:website.menu_website_rewrite
msgid "Redirects"
msgstr "Chuyển hướng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_rewrite_form
msgid "Refresh route's list"
msgstr "Làm mới danh sách tuyến"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Regular"
msgstr "Thông thường"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_form_view
msgid "Related Menu Items"
msgstr "Mục menu liên quan"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__menu_ids
msgid "Related Menus"
msgstr "Menu liên quan"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__page_id
msgid "Related Page"
msgstr "Trang liên quan"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Related keywords"
msgstr "Các từ khóa liên quan"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Remember information about the preferred look or behavior of the website, "
"such as your preferred language or region."
msgstr ""
"Ghi nhớ thông tin về giao diện hoặc hành vi ưa thích của trang web, chẳng "
"hạn như ngôn ngữ hoặc khu vực ưa thích của bạn."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__remove
#, python-format
msgid "Remove"
msgstr "Gỡ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Remove Row"
msgstr "Gỡ dòng"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Remove Serie"
msgstr "Xóa cột"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Remove Slide"
msgstr "Gỡ slide"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Remove Tab"
msgstr "Gỡ tab"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Remove all"
msgstr "Gỡ tất cả"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_kanban
msgid "Remove theme"
msgstr "Gỡ chủ đề"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "Rename Page To:"
msgstr "Đổi tên trang thành: "

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__replace
msgid "Replace"
msgstr "Thay thế"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_embed_code
msgid "Replace this with your own HTML code"
msgstr "Thay thế bằng mã HTML của bạn "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Required"
msgstr "Bắt buộc"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Reset templates"
msgstr "Đặt lại mẫu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Reset to initial version (hard reset)."
msgstr "Đặt lại phiên bản đầu (đặt lại bằng nút bấm)."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Resources"
msgstr "Nguồn lực"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#, python-format
msgid "Respecting your privacy is our priority."
msgstr "Tôn trọng quyền riêng tư của bạn là ưu tiên của chúng tôi."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Restore previous version (soft reset)."
msgstr "Khôi phục phiên bản trước (khởi động lại). "

#. module: website
#: model:ir.model.fields,help:website.field_res_partner__website_id
#: model:ir.model.fields,help:website.field_res_users__website_id
#: model:ir.model.fields,help:website.field_website_multi_mixin__website_id
#: model:ir.model.fields,help:website.field_website_page__website_id
#: model:ir.model.fields,help:website.field_website_published_multi_mixin__website_id
#: model:ir.model.fields,help:website.field_website_snippet_filter__website_id
msgid "Restrict publishing to this website."
msgstr "Hạn chế đăng trên website này."

#. module: website
#: model:res.groups,name:website.group_website_publisher
msgid "Restricted Editor"
msgstr "Bộ chỉnh sửa hạn chế"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__ir_ui_view__visibility__restricted_group
msgid "Restricted Group"
msgstr "Nhóm bị hạn chế"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_rewrite_list
msgid "Rewrite"
msgstr "Viết lại"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Right"
msgstr "Phải"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menu_image_menu
msgid "Right Menu"
msgstr "Menu bên phải"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Ripple Effect"
msgstr "Gợn sóng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Road"
msgstr "Đường"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "RoadMap"
msgstr "RoadMap"

#. module: website
#: code:addons/website/models/res_config_settings.py:0
#: model:ir.model.fields,field_description:website.field_website__robots_txt
#, python-format
msgid "Robots.txt"
msgstr "Robots.txt"

#. module: website
#: model:ir.model,name:website.model_website_robots
msgid "Robots.txt Editor"
msgstr "Trình chỉnh sửa Robots.txt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"Robots.txt: This file tells to search engine crawlers which pages or files "
"they can or can't request from your site.<br/>"
msgstr ""
"Robots.txt: Tệp này cho trình thu thập thông tin của công cụ tìm kiếm biết "
"những trang hoặc tệp nào họ có thể hoặc không thể yêu cầu từ trang web của "
"bạn.<br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Rotate In"
msgstr "Rotate In"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Rotate In-Down-Left"
msgstr "Rotate In-Down-Left"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Rotate In-Down-Right"
msgstr "Rotate In-Down-Right"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_widgets
msgid "Round Corners"
msgstr "Góc tròn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Rounded"
msgstr "Bo tròn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Rounded Miniatures"
msgstr "Mẫu nhỏ hình tròn"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__route_id
#: model:ir.model.fields,field_description:website.field_website_route__path
msgid "Route"
msgstr "Tuyến "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "SEO"
msgstr "SEO"

#. module: website
#: model:ir.model,name:website.model_website_seo_metadata
msgid "SEO metadata"
msgstr "Siêu dữ liệu SEO"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__is_seo_optimized
#: model:ir.model.fields,field_description:website.field_website_page__is_seo_optimized
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__is_seo_optimized
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "SEO optimized"
msgstr "SEO đã tối ưu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Same as desktop"
msgstr "Giống máy tính bàn"

#. module: website
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "Sample %s"
msgstr "Mẫu %s"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Sample Icons"
msgstr "Biểu tượng mẫu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Satellite"
msgstr "Vệ tinh"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/dashboard.js:0
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/js/menu/content.js:0
#: code:addons/website/static/src/js/menu/seo.js:0
#: code:addons/website/static/src/snippets/s_embed_code/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: model_terms:ir.ui.view,arch_db:website.view_edit_robots
#, python-format
msgid "Save"
msgstr "Lưu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Save & Reload"
msgstr "Lưu & tải lại"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Save & copy"
msgstr "Lưu & sao chép"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Save the block to use it elsewhere"
msgstr "Lưu khối để sử dụng ở nơi khác"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "Score"
msgstr "Điểm"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Screens"
msgstr "Màn hình"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_module_module__image_ids
msgid "Screenshots"
msgstr "Ảnh chụp màn hình"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll"
msgstr "Cuộn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll Effect"
msgstr "Hiệu ứng cuộn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.option_footer_scrolltop
msgid "Scroll To Top"
msgstr "Cuộn lên đầu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll Top Button"
msgstr "Nút cuộn lên đầu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll down button"
msgstr "Nút cuộn xuống"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Scroll down to next section"
msgstr "Cuộn xuống phần tiếp theo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.view_view_qweb
#: model_terms:ir.ui.view,arch_db:website.website_search_box
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Search"
msgstr "Tìm kiếm"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
msgid "Search Menus"
msgstr "Tìm trong menu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
msgid "Search Redirect"
msgstr "Chuyển hướng tìm kiếm"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
msgid "Search Results"
msgstr "Kết quả tìm kiếm"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Search Visitor"
msgstr "Tìm kiếm khách truy cập"

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_1
msgid ""
"Search in the media dialogue when you need photos to illustrate your "
"website. Odoo's integration with Unsplash, featuring millions of royalty "
"free and high quality photos, makes it possible for you to get the perfect "
"picture, in just a few clicks."
msgstr ""
"Tìm kiếm trong cửa sổ phương tiện khi bạn cần ảnh để minh họa trang web của "
"mình. Nhờ tích hợp của Odoo với Unsplash, bao gồm hàng triệu bức ảnh chất "
"lượng cao và miễn phí bản quyền, bạn có thể nhận được bức ảnh hoàn hảo chỉ "
"trong vài cú nhấp chuột."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_searchbar
msgid "Search on our website"
msgstr "Tìm kiếm trên trang web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Search within"
msgstr "Tìm kiếm trong"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_search_box
msgid "Search..."
msgstr "Tìm kiếm..."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Second Feature"
msgstr "Tính năng thứ hai"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Second Menu"
msgstr "Menu thứ hai"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "Second feature"
msgstr "Tính năng thứ hai"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Second list of Features"
msgstr "Danh sách tính năng thứ hai"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Secondary"
msgstr "Phụ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Secondary Style"
msgstr "Kiểu phụ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#, python-format
msgid "Seconds"
msgstr "Giây"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
msgid "Section Subtitle"
msgstr "Tiêu đề mục"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Select a Menu"
msgstr "Chọn một menu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Select a website to load its settings."
msgstr "Chọn một website để tải cài đặt của trang đó. "

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Select an image for social share"
msgstr "Chọn một hình ảnh để chia sẻ trên mạng xã hội"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Select and delete blocks <br/>to remove some steps."
msgstr "Chọn và xóa khối <br/> để xóa một số bước."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Select and delete blocks to remove features."
msgstr "Chọn và xóa các khối để xóa các tính năng."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Select one font on"
msgstr "Chọn một phông chữ trên"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Select the Website to Configure"
msgstr "Chọn website để đặt cấu hình"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Selection"
msgstr "Lựa chọn"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_shop
msgid "Sell more with an eCommerce"
msgstr "Kinh doanh tốt hơn nhờ eCommerce"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Send Email"
msgstr "Gửi email"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "Send us a message"
msgstr "Gửi tin nhắn cho chúng tôi"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__seo_name
#: model:ir.model.fields,field_description:website.field_website_page__seo_name
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__seo_name
msgid "Seo name"
msgstr "Tên Seo"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "Separate email addresses with a comma."
msgstr "Phân cách địa chỉ email bằng dấu phẩy. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Separated link"
msgstr "Link riêng biệt"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Separator"
msgstr "Phần ngăn cách"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__sequence
#: model:ir.model.fields,field_description:website.field_theme_website_menu__sequence
#: model:ir.model.fields,field_description:website.field_website__sequence
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__sequence
#: model:ir.model.fields,field_description:website.field_website_menu__sequence
#: model:ir.model.fields,field_description:website.field_website_page__priority
#: model:ir.model.fields,field_description:website.field_website_rewrite__sequence
msgid "Sequence"
msgstr "Trình tự"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Serve font from Google servers"
msgstr "Cung cấp phông chữ từ máy chủ của Google"

#. module: website
#: model:ir.model,name:website.model_ir_actions_server
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__action_server_id
msgid "Server Action"
msgstr "Tác vụ máy chủ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Service"
msgstr "Dịch vụ"

#. module: website
#: model:website.configurator.feature,name:website.feature_page_our_services
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.our_services
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Services"
msgstr "Dịch vụ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Session &amp; Security"
msgstr "Phiên &amp; Bảo mật"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_configuration
#: model:ir.ui.menu,name:website.menu_website_website_settings
msgid "Settings"
msgstr "Cài đặt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Settings on this page will apply to this website"
msgstr "Cài đặt trên trang này sẽ áp dụng website này"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Shadow"
msgstr "Đổ bóng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Shadows"
msgstr "Đổ bóng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Shake"
msgstr "Shake"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: model_terms:ir.ui.view,arch_db:website.s_share
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#, python-format
msgid "Share"
msgstr "Chia sẻ"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_elearning
msgid "Share knowledge publicly or for a fee"
msgstr "Chia sẻ kiến thức miễn phí hoặc thu phí"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_success_stories
msgid "Share your best case studies"
msgstr "Chia sẻ những trường hợp điển hình"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Shoes"
msgstr "Giày"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_shop
msgid "Shop"
msgstr "Cửa hàng"

#. module: website
#: model:ir.model.fields,help:website.field_website__auto_redirect_lang
msgid "Should users be redirected to their browser's language"
msgstr "Người dùng được chuyển hướng tới ngôn ngữ của trình duyệt của họ"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__customize_show
msgid "Show As Optional Inherit"
msgstr "Hiển thị như kế thừa tùy chọn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Show Header"
msgstr "Hiển thị tiêu đề"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Show Message"
msgstr "Hiện tin nhắn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Show Message and hide countdown"
msgstr "Hiện thông điệp và ẩn đếm ngược "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Show Message and keep countdown"
msgstr "Hiện thông điệp và tiếp tục đếm ngược"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Show Sign In"
msgstr "Hiện Đăng nhập"

#. module: website
#: model:ir.actions.act_window,name:website.action_show_viewhierarchy
msgid "Show View Hierarchy"
msgstr "Hiện dạng xem phân cấp"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Show in Top Menu"
msgstr "Hiển thị trong menu phía trên"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_qweb
msgid "Show inactive views"
msgstr "Hiện dạng xem không hoạt động"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Show on"
msgstr "Hiển thị trên"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Show reCaptcha Policy"
msgstr "Hiện chính sách reCaptcha"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Sidebar"
msgstr "Thanh bên"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Sign in"
msgstr "Đăng nhập"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields.selection,name:website.selection__ir_ui_view__visibility__connected
#, python-format
msgid "Signed In"
msgstr "Đã đăng nhập"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"Sitemap.xml: Help search engine crawlers to find out what pages are present "
"and which have recently changed, and to crawl your site accordingly. This "
"file is automatically generated by Odoo."
msgstr ""
"Sitemap.xml: Giúp trình thu thập của công cụ tìm kiếm tìm ra những trang "
"hiện có và những trang mới thay đổi và dựa vào đó để duyệt các trang. Tệp "
"này được tự động tạo bởi Odoo. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Size"
msgstr "Kích thước"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Skip and start from scratch"
msgstr "Bỏ qua và bắt đầu từ đầu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slide"
msgstr "Slide"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Slide Down"
msgstr "Trượt xuống"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slide Hover"
msgstr "Trượt khi trỏ chuột"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Slide Left"
msgstr "Trượt trái"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Slide Right"
msgstr "Trượt phải"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Slide Up"
msgstr "Trượt lên"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slideout Effect"
msgstr "Hiệu ứng trượt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.dynamic_snippet_carousel_options_template
msgid "Slider Speed"
msgstr "Tốc độ slide"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Slideshow"
msgstr "Trình chiếu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slogan"
msgstr "Slogan"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Small"
msgstr "Nhỏ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
msgid "Small Header"
msgstr "Tiêu đề nhỏ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid ""
"Small text. Lorem <b>ipsum dolor sit amet</b>, consectetur adipiscing elit. "
"<i>Integer posuere erat a ante</i>."
msgstr ""
"Chữ nhỏ. Lorem <b>ipsum dolor sit amet</b>, consectetur adipiscing elit. "
"<i>Integer posuere erat a ante</i>."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Smartphones"
msgstr "Điện thoại thông minh"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Social Media"
msgstr "Mạng xã hội"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Social Preview"
msgstr "Xem trước mạng xã hội"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_line_widgets
msgid "Solid"
msgstr "Đặc "

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Some Users"
msgstr "Một số người dùng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Something else here"
msgstr "Nội dung khác"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Something went wrong."
msgstr "Đã xảy ra lỗi. "

#. module: website
#: code:addons/website/controllers/main.py:0
#, python-format
msgid "Sort by Name"
msgstr "Sắp xếp theo tên"

#. module: website
#: code:addons/website/controllers/main.py:0
#, python-format
msgid "Sort by Url"
msgstr "Sắp xếp theo Url"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Sound"
msgstr "Âm thanh"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid ""
"Speakers from all over the world will join our experts to give inspiring "
"talks on various topics. Stay on top of the latest business management "
"trends &amp; technologies"
msgstr ""
"Các diễn giả từ khắp nơi trên thế giới sẽ cùng các chuyên gia của chúng tôi "
"thực hiện các bài diễn thuyết đầy cảm hứng về các chủ đề khác nhau. Luôn cập"
" nhật các xu hướng quản lý kinh doanh &amp; công nghệ mới nhất. "

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__specific_user_account
#: model:ir.model.fields,field_description:website.field_website__specific_user_account
msgid "Specific User Account"
msgstr "Tài khoản người dùng cụ thể"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
msgid "Specify a search term."
msgstr "Chỉ rõ cụm từ cần tìm kiếm. "

#. module: website
#: model:ir.model.fields,help:website.field_ir_model__website_form_default_field_id
msgid ""
"Specify the field which will contain meta and custom form fields datas."
msgstr ""
"Chỉ định trường chứa siêu dữ liệu và dữ liệu trường biểu mẫu tùy chỉnh"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Speed"
msgstr "Tốc độ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Spring collection has arrived !"
msgstr "Bộ sưu tập mùa xuân đã về!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_share_options
msgid "Square"
msgstr "Vuông"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Squared Miniatures"
msgstr "Mẫu nhỏ hình vuông"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Stacked"
msgstr "Xếp chồng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Standard"
msgstr "Tiêu chuẩn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
msgid "Start Button"
msgstr "Nút bắt đầu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#, python-format
msgid "Start Now"
msgstr "Bắt đầu ngay"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Start now"
msgstr "Bắt đầu ngay"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
#: model_terms:ir.ui.view,arch_db:website.s_tabs
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "Start with the customer – find out what they want and give it to them."
msgstr "Bắt đầu từ khách hàng – tìm hiểu và mang lại điều họ muốn. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "Start your journey"
msgstr "Bắt đầu hành trình "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Starter"
msgstr "Khai vị"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Status Colors"
msgstr "Màu trạng thái"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Stay informed of our latest news and discover what will happen in the next "
"weeks."
msgstr ""
"Nhận thông tin mới nhất và khám phá sự kiện sẽ diễn ra trong các tuần tới."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
msgid "Sticky"
msgstr "Dính"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Storage"
msgstr "Lưu trữ"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_stores_locator
msgid "Stores Locator"
msgstr "Tìm cửa hàng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "Storytelling is powerful.<br/> It draws readers in and engages them."
msgstr ""
"Kể chuyện là một công cụ mạnh mẽ.<br/> Giúp thu hút và lôi cuốn khán giả. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Stretch to Equal Height"
msgstr "Độ cao như nhau"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Striped"
msgstr "Sọc"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Structure"
msgstr "Cấu trúc "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Style"
msgstr "Kiểu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Styling"
msgstr "Kiểu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Sub Menus"
msgstr "Menu phụ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Subject"
msgstr "Tiêu đề"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.s_website_form
#: model_terms:website.page,arch_db:website.contactus_page
msgid "Submit"
msgstr "Gửi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Submit sitemap to Google"
msgstr "Gửi sitemap cho Google"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form.xml:0
#: code:addons/website/static/src/xml/website_form.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Success"
msgstr "Thành công"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_success_stories
msgid "Success Stories"
msgstr "Câu chuyện thành công"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Suggestions"
msgstr "Gợi ý"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Surrounded"
msgstr "Vòng quanh"

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "Suspicious activity detected by Google reCaptcha."
msgstr "Hoạt động đáng ngờ được Google reCaptcha phát hiện"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Switch Theme"
msgstr "Đổi chủ đề"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "T-shirts"
msgstr "Áo phông"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#, python-format
msgid "TIP: Once loaded, follow the"
msgstr "MẸO: Khi tải xong, hãy đi theo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid ""
"TIP: Once loaded, follow the\n"
"                    <span class=\"o_tooltip o_tooltip_visible bottom o_animated position-relative\"/>\n"
"                    <br/>pointer to build the perfect page in 7 steps."
msgstr ""
"MẸO: Khi tải xong, đi theo\n"
"                    <span class=\"o_tooltip o_tooltip_visible bottom o_animated position-relative\"/>\n"
"                    <br/>con trỏ để dựng website hoàn hảo trong 7 bước."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "TRANSLATE"
msgstr "DỊCH"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Tablets"
msgstr "Máy tính bảng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Tabs"
msgstr "Tab"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Tabs color"
msgstr "Màu tab "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Tada"
msgstr "Tada"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__target
msgid "Target"
msgstr "Mục tiêu"

#. module: website
#: model:ir.model.fields,help:website.field_ir_asset__key
msgid ""
"Technical field used to resolve multiple assets in a multi-website "
"environment."
msgstr ""
"Trường kỹ thuật được dùng để giải quyết nhiều asset trong môi trường đa "
"website. "

#. module: website
#: model:ir.model.fields,help:website.field_ir_attachment__key
msgid ""
"Technical field used to resolve multiple attachments in a multi-website "
"environment."
msgstr ""
"Trường kỹ thuật được sử dụng để giải quyết nhiều tệp đính kèm trong môi "
"trường nhiều website. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Technical name:"
msgstr "Tên kỹ thuật:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Telephone"
msgstr "Điện thoại"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Televisions"
msgstr "Ti-vi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Tell what's the value for the <br/>customer for this feature."
msgstr "Cho biết giá trị của tính năng này <br/> đối với khách hàng."

#. module: website
#: code:addons/website/models/website.py:0
#: code:addons/website/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Template"
msgstr "Mẫu"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "Template <b>%s (id:%s)</b> contains a link to this page"
msgstr "Mẫu <b>%s (id:%s)</b> chứa liên kết đến trang này"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "Template <b>%s (id:%s)</b> is calling this file"
msgstr "Mẫu <b>%s (id:%s)</b> đang gọi file này"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Template fallback"
msgstr "Mẫu dự phòng"

#. module: website
#: code:addons/website/models/website.py:0
#: code:addons/website/models/website.py:0
#, python-format
msgid "Templates"
msgstr "Mẫu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "Terms of Services"
msgstr "Điều khoản dịch vụ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Terms of service"
msgstr "Điều khoản dịch vụ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "Terrain"
msgstr "Địa hình"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_edit_robots
msgid "Test your robots.txt with Google Search Console"
msgstr "Kiểm tra tệp robots.txt bằng Google Search Console"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_header_brand
msgid "Text"
msgstr "Văn bản"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Text Alignment"
msgstr "Căn văn bản"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Text Color"
msgstr "Màu chữ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: model_terms:ir.ui.view,arch_db:website.s_text_highlight
#, python-format
msgid "Text Highlight"
msgstr "Văn bản nổi bật"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Text Image Text"
msgstr "Chữ ảnh chữ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Text Inline"
msgstr "Văn bản cùng dòng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "Text Position"
msgstr "Vị trí văn bản"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Text muted. Lorem <b>ipsum dolor sit amet</b>, consectetur."
msgstr "Chữ mờ. Lorem <b>ipsum dolor sit amet</b>, consectetur."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "Thank You For Your Feedback"
msgstr "Cảm ơn phản hồi của bạn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid "Thank You!"
msgstr "Cảm ơn bạn!"

#. module: website
#: code:addons/website/controllers/backend.py:0
#, python-format
msgid "The Google Analytics Client ID or Key you entered seems incorrect."
msgstr ""
"ID khách hàng Google Analytics hoặc khóa bạn đã nhập có vẻ không chính xác."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "The chosen name already exists"
msgstr "Tên được chọn đã tồn tại"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "The company this website belongs to"
msgstr "Công ty của website này"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid ""
"The current text selection cannot be animated. Try clearing the format and "
"try again."
msgstr ""
"Không thể tạo hình động cho phần văn bản được chọn. Thử xóa định dạng và thử"
" lại. "

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid ""
"The description will be generated by search engines based on page content "
"unless you specify one."
msgstr ""
"Mô tả sẽ được tạo bởi các công cụ tìm kiếm dựa trên nội dung trang trừ khi "
"bạn chỉ định cụ thể."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid ""
"The description will be generated by social media based on page content "
"unless you specify one."
msgstr ""
"Mô tả sẽ được tạo bởi phương tiện truyền thông xã hội dựa trên nội dung "
"trang trừ khi bạn chỉ định cụ thể."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form.xml:0
#, python-format
msgid "The form has been sent successfully."
msgstr "Biểu mẫu đã được gửi thành công"

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "The form's specified model does not exist"
msgstr "Mô hình được chỉ định của biểu mẫu này không tồn tại "

#. module: website
#: model:ir.model.fields,help:website.field_res_partner__website_url
#: model:ir.model.fields,help:website.field_res_users__website_url
#: model:ir.model.fields,help:website.field_website_page__website_url
#: model:ir.model.fields,help:website.field_website_published_mixin__website_url
#: model:ir.model.fields,help:website.field_website_published_multi_mixin__website_url
#: model:ir.model.fields,help:website.field_website_snippet_filter__website_url
msgid "The full URL to access the document through the website."
msgstr "URL đầy đủ để truy cập tài liệu thông qua website. "

#. module: website
#: model:ir.model.fields,help:website.field_ir_actions_server__website_url
#: model:ir.model.fields,help:website.field_ir_cron__website_url
msgid "The full URL to access the server action through the website."
msgstr "URL đầy đủ để truy cập tác vụ máy chủ thông qua website. "

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "The installation of an App is already in progress."
msgstr "Quá trình cài đặt ứng dụng đang được tiến hành."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "The language of the keyword and related keywords."
msgstr "Ngôn ngữ của từ khóa và các từ khóa liên quan."

#. module: website
#: model:ir.model.fields,help:website.field_website_snippet_filter__limit
msgid "The limit is the maximum number of records retrieved"
msgstr "Giới hạn là số lượng dữ liệu tối đa được truy xuất"

#. module: website
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "The limit must be between 1 and 16."
msgstr "Giới hạn phải từ 1 đến 16."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "The message will be visible once the countdown ends"
msgstr "Thông điệp sẽ hiện lên khi đếm ngược kết thúc"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "The selected templates will be reset to their factory settings."
msgstr "Các mẫu được chọn sẽ được đặt lại về các cài đặt khi xuất xưởng. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid "The team"
msgstr "Đội ngũ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "The title will take a default value unless you specify one."
msgstr "Tiêu đề sẽ lấy một giá trị mặc định trừ khi bạn chỉ định cụ thể."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"The website will not work properly if you reject or discard those cookies."
msgstr ""
"Trang web sẽ không hoạt động bình thường nếu bạn từ chối hoặc loại bỏ các "
"cookie đó."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "The website will still work if you reject or discard those cookies."
msgstr ""
"Trang web sẽ vẫn hoạt động nếu bạn từ chối hoặc loại bỏ các cookie đó."

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: model:ir.model.fields,field_description:website.field_website__theme_id
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.theme_view_search
#, python-format
msgid "Theme"
msgstr "Chủ đề"

#. module: website
#: model:ir.model,name:website.model_theme_ir_asset
msgid "Theme Asset"
msgstr "Asset chủ đề"

#. module: website
#: model:ir.model,name:website.model_theme_ir_attachment
msgid "Theme Attachments"
msgstr "Chủ đề đính kèm"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
#, python-format
msgid "Theme Colors"
msgstr "Màu chủ đề"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Theme Options"
msgstr "Tùy chọn chủ đề"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_asset__theme_template_id
#: model:ir.model.fields,field_description:website.field_ir_attachment__theme_template_id
#: model:ir.model.fields,field_description:website.field_ir_ui_view__theme_template_id
#: model:ir.model.fields,field_description:website.field_website_menu__theme_template_id
#: model:ir.model.fields,field_description:website.field_website_page__theme_template_id
msgid "Theme Template"
msgstr "Mẫu chủ đề"

#. module: website
#: model:ir.model,name:website.model_theme_ir_ui_view
msgid "Theme UI View"
msgstr "Dạng xem UI chủ đề"

#. module: website
#: model:ir.model,name:website.model_theme_utils
msgid "Theme Utils"
msgstr "Theme Utils"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
msgid "There are currently no pages for this website."
msgstr "Hiện tại website này không có trang nào. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "There are currently no pages for your website."
msgstr "Hiện tại website của bạn không có trang nào. "

#. module: website
#: code:addons/website/models/website_visitor.py:0
#, python-format
msgid "There are no contact and/or no email linked to this visitor."
msgstr ""
"Không có liên hệ và/hoặc email nào được liên kết với khách truy cập này."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "There is no data currently available."
msgstr "Hiện chưa có dữ liệu nào."

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "There is no field available for this option."
msgstr "Không có trường nào có sẵn cho tùy chọn này."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"There is no website available for this company. You could create a new one."
msgstr ""
"Không có website nào cho công ty này. Bạn có thể tạo một website mới. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"These terms of service (\"Terms\", \"Agreement\") are an agreement between "
"the website (\"Website operator\", \"us\", \"we\" or \"our\") and you "
"(\"User\", \"you\" or \"your\"). This Agreement sets forth the general terms"
" and conditions of your use of this website and any of its products or "
"services (collectively, \"Website\" or \"Services\")."
msgstr ""
"Các điều khoản dịch vụ này (\"Điều khoản\", \"Thỏa thuận\") là một thỏa "
"thuận giữa trang web (\"Nhà điều hành trang web\", \"chúng tôi\" hoặc \"của "
"chúng tôi\") và bạn (\"Người dùng\", \"bạn\" hoặc \"của bạn\"). Thỏa thuận "
"này nêu ra các điều khoản và điều kiện chung về việc bạn sử dụng trang web "
"này và bất kỳ sản phẩm hoặc dịch vụ nào trên trang web (gọi chung là \"Trang"
" web\" hoặc \"Dịch vụ\")."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "They trust us since years"
msgstr "Từ rất lâu họ đã tin tưởng chúng tôi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Thick"
msgstr "Dày"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Thin"
msgstr "Mỏng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Third Feature"
msgstr "Tính năng thứ ba"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Third Menu"
msgstr "Menu thứ ba"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__favicon
#: model:ir.model.fields,help:website.field_website__favicon
msgid "This field holds the image used to display a favicon on the website."
msgstr ""
"Trường này chứa hình ảnh được sử dụng để hiển thị một biểu tượng favicon "
"trên website. "

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch_base
msgid "This field is the same as `arch` field without translations"
msgstr "Trường này giống như trường `arch` không có bản dịch"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_default_lang_code
msgid "This field is used to set/get locales for user"
msgstr ""
"Trường này được sử dụng để cài/lấy địa điểm và ngôn ngữ cho người dùng"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch
msgid ""
"This field should be used when accessing view arch. It will use translation.\n"
"                               Note that it will read `arch_db` or `arch_fs` if in dev-xml mode."
msgstr ""
"Trường này sẽ được dùng khi truy cập xem kiến trúc. Bản dịch sẽ được sử dụng.\n"
"                               Lưu ý rằng trường này sẽ đọc `arch_db` hoặc `arch_fs` nếu ở chế độ dev-xml. "

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch_db
msgid "This field stores the view arch."
msgstr "Trường này lưu trữ dạng xem kiến trúc "

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch_prev
msgid ""
"This field will save the current `arch_db` before writing on it.\n"
"                                                                         Useful to (soft) reset a broken view."
msgstr ""
"Trường này sẽ lưu `arch_db` hiện tại trước khi ghi lên đó.\n"
"                                                                         Giúp đặt lại (khởi động lại) bản xem bị hỏng. "

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"This font already exists, you can only add it as a local font to replace the"
" server version."
msgstr ""
"Phông chữ này đã tồn tại, bạn chỉ có thể thêm nó làm phông chữ cục bộ để "
"thay thế phiên bản máy chủ."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "This font is hosted and served to your visitors by Google servers"
msgstr ""
"Phông chữ này được lưu trữ và phục vụ cho khách truy cập của bạn bởi các máy"
" chủ của Google"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "This is a \""
msgstr "Đây là một cảnh báo \""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_banner
msgid ""
"This is a simple hero unit, a simple jumbotron-style component for calling "
"extra attention to featured content or information."
msgstr ""
"Đây là mục toàn màn hình, là thành phần kiểu màn hình lớn giúp thu hút sự "
"chú ý tới nội dung và thông tin nổi bật.  "

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "This message has been posted on your website!"
msgstr "Thông điệp này đã được đăng trên website!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "This page"
msgstr "Trang này"

#. module: website
#: code:addons/website/models/website_visitor.py:0
#, python-format
msgid "This operator is not supported"
msgstr "This operator is not supported"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid ""
"This page does not exist, but you can create it as you are editor of this "
"site."
msgstr ""
"Trang không tồn tại nhưng bạn có thể tạo trang này với tư cách là biên tập "
"trang web. "

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "This page is in the menu <b>%s</b>"
msgstr "Trang này nằm trong menu <b>%s</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "This page will be published on {{ date_formatted }}"
msgstr "Trang này sẽ được đăng vào {{ date_formatted }}"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "This translation is not editable."
msgstr "Không thể chỉnh sửa bản dịch này."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid ""
"This value will be escaped to be compliant with all major browsers and used "
"in url. Keep it empty to use the default name of the record."
msgstr ""
"Giá trị này sẽ được thoát để phù hợp với tất cả các trình duyệt chính và "
"được sử dụng trong url. Giữ trống để sử dụng tên mặc định của bản ghi."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.report_viewhierarchy_children
msgid "This view arch has been modified"
msgstr "Dạng xem kiến trúc này đã được sửa đổi"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid ""
"Those accounts should now check their Analytics dashboard in the Google "
"platform directly."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Thumbnails"
msgstr "Hình thu nhỏ"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__time_since_last_action
msgid "Time since last page view. E.g.: 2 minutes ago"
msgstr "Thời gian kể từ lần xem trang cuối cùng. Ví dụ: 2 phút trước"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__cache_time
msgid "Time to cache the page. (0 = no cache)"
msgstr "Thời gian để lưu trang vào bộ nhớ cache. (0 = không cache)"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
#, python-format
msgid "Time's up! You can now visit"
msgstr "Hết giờ rồi! Bây giờ bạn có thể ghé thăm"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
msgid "Timeline"
msgstr "Dòng thời gian"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__timezone
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Timezone"
msgstr "Múi giờ"

#. module: website
#: model:digest.tip,name:website.digest_tip_website_4
#: model_terms:digest.tip,tip_description:website.digest_tip_website_4
msgid "Tip: Add shapes to energize your Website"
msgstr "Mẹo: Thêm hình dạng để tạo website đầy năng lượng"

#. module: website
#: model:digest.tip,name:website.digest_tip_website_0
#: model_terms:digest.tip,tip_description:website.digest_tip_website_0
msgid "Tip: Engage with visitors to convert them into leads"
msgstr ""
"Mẹo: Tương tác với khách truy cập để chuyển đổi họ thành khách hàng tiềm "
"năng"

#. module: website
#: model:digest.tip,name:website.digest_tip_website_2
#: model_terms:digest.tip,tip_description:website.digest_tip_website_2
msgid "Tip: Search Engine Optimization (SEO)"
msgstr "Mẹo: Tối ưu hóa công cụ tìm kiếm (SEO)"

#. module: website
#: model:digest.tip,name:website.digest_tip_website_3
#: model_terms:digest.tip,tip_description:website.digest_tip_website_3
msgid "Tip: Use illustrations to spice up your website"
msgstr "Mẹo: Dùng hình minh họa để website thú vị hơn"

#. module: website
#: model:digest.tip,name:website.digest_tip_website_1
#: model_terms:digest.tip,tip_description:website.digest_tip_website_1
msgid "Tip: Use royalty-free photos"
msgstr "Mẹo: Sử dụng ảnh miễn phí bản quyền"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Title"
msgstr "Tiêu đề"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_share_options
msgid "Title Position"
msgstr "Vị trí tiêu đề"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"To add a fourth column, reduce the size of these three columns using the "
"right icon of each block. Then, duplicate one of the columns to create a new"
" one as a copy."
msgstr ""
"Để thêm cột thứ tư, hãy giảm kích thước của ba cột này bằng cách sử dụng "
"biểu tượng bên phải của mỗi khối. Sau đó, sao chép một trong các cột để tạo "
"một cột mới dưới dạng bản sao."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "To be successful your content needs to be useful to your readers."
msgstr "Để thành công, nội dung cần phải có ích cho người đọc. "

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_2
msgid ""
"To get more visitors, you should target keywords that are often searched in "
"Google. With the built-in SEO tool, once you define a few keywords, Odoo "
"will recommend you the best keywords to target. Then adapt your title and "
"description accordingly to boost your traffic."
msgstr ""
"Để có nhiều khách truy cập, bạn nên nhắm mục tiêu các từ khóa thường được "
"tìm kiếm trên Google. Với công cụ SEO tích hợp sẵn, khi bạn xác định một vài"
" từ khóa, Odoo sẽ giới thiệu cho bạn những từ khóa tốt nhất để nhắm mục "
"tiêu. Sau đó, điều chỉnh tiêu đề và mô tả của bạn cho phù hợp để tăng lưu "
"lượng truy cập."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"To send invitations in B2B mode, open a contact or select several ones in "
"list view and click on 'Portal Access Management' option in the dropdown "
"menu *Action*."
msgstr ""
"Để gửi lời mời trong chế độ B2B, hãy mở một liên lạc hoặc chọn một số liên "
"lạc trong chế độ xem danh sách và bấm vào tùy chọn 'Quản lý truy cập cổng "
"thông tin' trong menu thả xuống  *Tác vụ*."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Toggle"
msgstr "Chuyển đổi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.option_header_off_canvas
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Toggle navigation"
msgstr "Chuyển đổi điều hướng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Tony Fred, CEO"
msgstr "Tony Fred, CEO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Tooltip"
msgstr "Tooltip"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Top"
msgstr "Trên"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Top Menu"
msgstr "Menu trên"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "Top Menu for Website %s"
msgstr "Menu trên của website %s"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Top to Bottom"
msgstr "Từ trên xuống "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Tops"
msgstr "Áo "

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__page_count
msgid "Total number of tracked page visited"
msgstr "Tổng số trang được theo dõi đã truy cập"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__visitor_page_count
msgid "Total number of visits on tracked pages"
msgstr "Tổng số lượt truy cập trên các trang được theo dõi"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__track
#: model:ir.model.fields,field_description:website.field_website_page__track
msgid "Track"
msgstr "Theo dõi"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/track_page.xml:0
#, python-format
msgid "Track Visitor"
msgstr "Theo dõi khách truy cập"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Track visits in Google Analytics"
msgstr "Theo dõi lượt truy cập trong Google Analytics"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Tracked"
msgstr "Được theo dõi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Transition"
msgstr "Chuyển tiếp"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "Translate Attribute"
msgstr "Dịch thuộc tính"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "Translate Selection Option"
msgstr "Tùy chọn lựa chọn dịch"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "Translate header in the text. Menu is generated automatically."
msgstr "Dịch lề trên trong văn bản. Menu được tạo tự động."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/translator.xml:0
#, python-format
msgid "Translated content"
msgstr "Nội dung được dịch"

#. module: website
#: model:ir.model,name:website.model_ir_translation
msgid "Translation"
msgstr "Bản dịch"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "Translation Info"
msgstr "Thông tin bản dịch"

#. module: website
#: model:ir.model.fields,help:website.field_website__configurator_done
msgid "True if configurator has been completed or ignored"
msgstr "Đúng nếu bộ định cấu hình được hoàn thành hoặc bỏ qua"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Tuna and Salmon Burger"
msgstr "Burger cá ngừ và cá hồi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Turn every feature into a benefit for your reader."
msgstr "Khiến mọi tính năng đều có lợi cho độc giả. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Twitter"
msgstr "Twitter"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_twitter
#: model:ir.model.fields,field_description:website.field_website__social_twitter
msgid "Twitter Account"
msgstr "Tài khoản Twitter"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Twitter Scroller"
msgstr "Twitter Scroller"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__type
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Type"
msgstr "Loại"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Type '"
msgstr "Gõ '"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid ""
"Type '<i class=\"confirm_word\">yes</i>' in the box below if you want to "
"confirm."
msgstr ""
"Gõ '<i class=\"confirm_word\">có</i>' trong hộp bên dưới nếu bạn muốn xác "
"nhận."

#. module: website
#: model:ir.model.fields,help:website.field_website_rewrite__redirect_type
msgid ""
"Type of redirect/Rewrite:\n"
"\n"
"        301 Moved permanently: The browser will keep in cache the new url.\n"
"        302 Moved temporarily: The browser will not keep in cache the new url and ask again the next time the new url.\n"
"        404 Not Found: If you want remove a specific page/controller (e.g. Ecommerce is installed, but you don't want /shop on a specific website)\n"
"        308 Redirect / Rewrite: If you want rename a controller with a new url. (Eg: /shop -> /garden - Both url will be accessible but /shop will automatically be redirected to /garden)\n"
"    "
msgstr ""
"Loại chuyển hướng/Viết lại:\n"
"\n"
"        301 Đã chuyển vĩnh viễn: Trình duyệt sẽ lưu trong bộ nhớ cache url mới.\n"
"        302 Đã chuyển tạm thời: Trình duyệt sẽ không lưu trong bộ nhớ cache url mới và hỏi lại vào lần url mới tiếp theo.\n"
"        404 Không tìm thấy: Nếu bạn muốn xóa một trang/bộ điều khiển cụ thể (ví dụ: eCommerce đã được cài đặt, nhưng bạn không muốn /shop trên một trang web cụ thể)\n"
"        308 Chuyển hướng / Viết lại: Nếu bạn muốn đổi tên bộ điều khiển bằng url mới. (Ví dụ: /shop -> /garden - Cả hai url đều có thể truy cập được nhưng /shop sẽ tự động được chuyển hướng đến /garden)\n"
"    "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "URL"
msgstr "Liên kết"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__url_from
#: model_terms:ir.ui.view,arch_db:website.view_website_rewrite_form
msgid "URL from"
msgstr "URL từ"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__cdn_filters
#: model:ir.model.fields,help:website.field_website__cdn_filters
msgid "URL matching those filters will be rewritten using the CDN Base URL"
msgstr "URL khớp với các bộ lọc đó sẽ được viết lại bằng URL cơ sở CDN"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__url_to
msgid "URL to"
msgstr "URL tới"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Unalterable unique identifier"
msgstr "Mã định danh duy nhất không thể thay đổi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Underline On Hover"
msgstr "Gạch chân khi di chuột"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Understand how visitors engage with our website, via Google Analytics.\n"
"                                                Learn more about"
msgstr ""
"Hiểu cách khách truy cập tương tác với trang web của chúng ta, thông qua Google Analytics.\n"
"                                                Tìm hiểu thêm về"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.index_management
msgid "Unindexed"
msgstr "Không lập chỉ mục"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Unlimited CRM power and support"
msgstr "CRM mạnh mẽ và hỗ trợ không giới hạn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Unlimited customization"
msgstr "Tuỳ biến không hạn chế"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/button.js:0
#, python-format
msgid "Unpublish"
msgstr "Bỏ đăng"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/button.js:0
#: code:addons/website/static/src/js/backend/button.js:0
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.publish_short
#, python-format
msgid "Unpublished"
msgstr "Không đăng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Unregistered"
msgstr "Chưa đăng ký"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_kanban
msgid "Update theme"
msgstr "Cập nhật chủ đề"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Upload"
msgstr "Tải lên"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "Uploaded file is too large."
msgstr "Tệp tải lên quá lớn. "

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__url
#: model:ir.model.fields,field_description:website.field_theme_website_menu__url
#: model:ir.model.fields,field_description:website.field_theme_website_page__url
#: model:ir.model.fields,field_description:website.field_website_menu__url
#: model:ir.model.fields,field_description:website.field_website_track__url
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
#: model_terms:ir.ui.view,arch_db:website.menu_search
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
msgid "Url"
msgstr "Url"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__country_flag
msgid "Url of static flag image"
msgstr "Url của hình ảnh cờ tĩnh"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
msgid "Urls & Pages"
msgstr "Url & Trang"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Use Google Map on your website ("
msgstr "Dùng Google Map cho trang web của bạn ("

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Use Google Map on your website (Contact Us page, snippets, etc)."
msgstr ""
"Dùng Google Map trên website của bạn (trang Liên hệ, các đoạn trích, v.v.)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Use a CDN to optimize the availability of your website's content"
msgstr "Dùng CDN để tối ưu hóa hiển thị nội dung website"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_default_share_image
msgid "Use a image by default for sharing"
msgstr "Dùng hình ảnh theo mặc định để chia sẻ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Use as Homepage"
msgstr "Dùng như Trang chủ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Use of Cookies"
msgstr "Sử dụng cookie"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid ""
"Use this component for creating a list of featured elements to which you "
"want to bring attention."
msgstr ""
"Dùng thành phần này để tạo danh sách các yếu tố nổi bật mà bạn muốn thu hút "
"sự chú ý."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid ""
"Use this snippet to build various types of components that feature a left- "
"or right-aligned image alongside textual content. Duplicate the element to "
"create a list that fits your needs."
msgstr ""
"Sử dụng đoạn trích này để xây dựng các loại thành phần có chứa ảnh căn trái "
"hoặc phải cùng với nội dung phù hợp. Nhân đôi các yếu tố để tạo danh sách "
"phù hợp với nội dung. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"Use this snippet to presents your content in a slideshow-like format. Don't "
"write about products or services here, write about solutions."
msgstr ""
"Sử dụng đoạn trích này để trình bày nội dung ở dạng bản trình chiếu. Đừng "
"viết về sản phẩm hoặc dịch vụ ở đây, hãy viết về giải pháp."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_kanban
msgid "Use this theme"
msgstr "Dùng chủ đề này"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid ""
"Use this timeline as a part of your resume, to show your visitors what "
"you've done in the past."
msgstr ""
"Thêm dòng thời gian này vào sơ yếu lý lịch của bạn để cho khách truy cập "
"thấy những gì bạn đã đạt được. "

#. module: website
#: model:ir.model.fields,help:website.field_ir_model__website_form_key
msgid "Used in FormBuilder Registry"
msgstr "Dùng trong Đăng ký trình tạo biểu mẫu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Used in page content"
msgstr "Sử dụng trong nội dung trang"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Used in page description"
msgstr "Sử dụng trong miêu tả trang"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Used in page first level heading"
msgstr "Sử dụng trong dòng tiêu đề đầu tiên của trang"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Used in page second level heading"
msgstr "Sử dụng trong dòng tiêu đề thứ hai của trang"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Used in page title"
msgstr "Sử dụng trong tiêu đề trang"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Used to collect information about your interactions with the website, the pages you've seen,\n"
"                                                and any specific marketing campaign that brought you to the website."
msgstr ""
"Được sử dụng để thu thập thông tin về tương tác của bạn với trang web, các trang bạn đã xem,\n"
"                                               và bất kỳ chiến dịch tiếp thị cụ thể nào do trang web này mang đến. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Used to make advertising more engaging to users and more valuable to publishers and advertisers,\n"
"                                                such as providing more relevant ads when you visit other websites that display ads or to improve reporting on ad campaign performance."
msgstr ""
"Được sử dụng để giúp quảng cáo thu hút người dùng và hữu dụng hơn cho người đăng quảng cáo và nhà quảng cáo,\n"
"                                                ví dụ như cung cấp quảng cáo phù hợp khi bạn ghé thăm các website có đặt quảng cáo và để cải thiện báo cáo về hiệu suất của chiến dịch quảng cáo. "

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_country_group_ids
#: model:ir.model.fields,help:website.field_website__country_group_ids
msgid "Used when multiple websites have the same domain."
msgstr "Được dùng khi nhiều website có cùng một tên miền."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Useful Links"
msgstr "Liên kết hữu ích"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "Useful options"
msgstr "Tùy chọn hữu ích"

#. module: website
#: model:ir.model.fields,help:website.field_website_menu__group_ids
msgid "User need to be at least in one of these groups to see the menu"
msgstr "Người dùng ít nhất phải thuộc một trong các nhóm này để xem menu"

#. module: website
#: model:ir.model,name:website.model_res_users
msgid "Users"
msgstr "Người dùng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Utilities &amp; Typography"
msgstr "Tiện ích &amp; Kiểu chữ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Value"
msgstr "Giá trị"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Vert. Alignment"
msgstr "Căn dọc"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Vertical"
msgstr "Dọc"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Vertical Alignment"
msgstr "Căn chỉnh dọc"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Video"
msgstr "Video"

#. module: website
#: model:ir.model,name:website.model_ir_ui_view
#: model:ir.model.fields,field_description:website.field_theme_website_page__view_id
#: model:ir.model.fields,field_description:website.field_website_page__view_id
msgid "View"
msgstr "Hiển thị"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch
msgid "View Architecture"
msgstr "Xem kiến trúc"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__name
msgid "View Name"
msgstr "Tên hiển thị"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__type
msgid "View Type"
msgstr "Dạng hiển thị"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__mode
msgid "View inheritance mode"
msgstr "Chế độ dạng xem kế thừa"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__copy_ids
msgid "Views using a copy of me"
msgstr "Dạng xem sử dụng bản sao của tôi"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__inherit_children_ids
msgid "Views which inherit from this one"
msgstr "Dạng xem kế thừa từ dạng xem này"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields,field_description:website.field_ir_ui_view__visibility
#: model:ir.model.fields,field_description:website.field_website_page__visibility
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Visibility"
msgstr "Hiển thị"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__visibility_password
#: model:ir.model.fields,field_description:website.field_website_page__visibility_password
#: model_terms:ir.ui.view,arch_db:website.view_view_form_extend
msgid "Visibility Password"
msgstr "Mật khẩu hiển thị"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__visibility_password_display
#: model:ir.model.fields,field_description:website.field_website_page__visibility_password_display
msgid "Visibility Password Display"
msgstr "Hiện mật khẩu hiển thị"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__group_ids
msgid "Visible Groups"
msgstr "Nhóm hiển thị"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_conditional_visibility
msgid "Visible for"
msgstr "Hiện cho"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Visible for Everyone"
msgstr "Hiện cho mọi người"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Visible for Logged In"
msgstr "Hiện cho đăng nhập"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Visible for Logged Out"
msgstr "Hiện cho đăng xuất"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_partner__website_published
#: model:ir.model.fields,field_description:website.field_res_users__website_published
#: model:ir.model.fields,field_description:website.field_website_page__website_published
#: model:ir.model.fields,field_description:website.field_website_published_mixin__website_published
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__website_published
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__website_published
msgid "Visible on current website"
msgstr "Hiển thị ở website hiện tại"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Visible on mobile"
msgstr "Hiện trên di động"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Visible only if"
msgstr "Chỉ hiện khi"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_track__visit_datetime
msgid "Visit Date"
msgstr "Ngày truy cập"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
#, python-format
msgid "Visit our Facebook page to know if you are one of the lucky winners."
msgstr ""
"Hãy truy cập trang Facebook của chúng tôi để biết bạn có phải là một trong "
"những người may mắn trúng thưởng hay không."

#. module: website
#: model:ir.model,name:website.model_website_track
#: model:ir.model.fields,field_description:website.field_website_visitor__page_ids
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Visited Pages"
msgstr "Trang đã xem"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__website_track_ids
msgid "Visited Pages History"
msgstr "Lịch sử các trang đã truy cập"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_track__visitor_id
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Visitor"
msgstr "Khách"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_graph
msgid "Visitor Page Views"
msgstr "Lượt xem trang của khách truy cập"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_tree
msgid "Visitor Page Views History"
msgstr "Lịch sử lượt xem trang của khách"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_track_view_graph
msgid "Visitor Views"
msgstr "Lượt xem của khách truy cập"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_track_view_tree
msgid "Visitor Views History"
msgstr "Lịch sử lượt xem của khách truy cập"

#. module: website
#: model:ir.actions.act_window,name:website.website_visitors_action
#: model:ir.model.fields,field_description:website.field_res_partner__visitor_ids
#: model:ir.model.fields,field_description:website.field_res_users__visitor_ids
#: model:ir.ui.menu,name:website.menu_visitor_sub_menu
#: model:ir.ui.menu,name:website.website_visitor_menu
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_graph
msgid "Visitors"
msgstr "Khách truy cập"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#, python-format
msgid "Visits"
msgstr "Lượt truy cập"

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitor_view_action
msgid ""
"Wait for visitors to come to your website to see the pages they viewed."
msgstr "Chờ khách truy cập vào website của bạn để xem các trang họ đã xem."

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitors_action
msgid ""
"Wait for visitors to come to your website to see their history and engage "
"with them."
msgstr "Chờ khách hàng truy cập website để xem lịch sử và tương tác với họ. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Warning"
msgstr "Cảnh báo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Watches"
msgstr "Đồng hồ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid ""
"We are a team of passionate people whose goal is to improve everyone's life through disruptive products. We build great products to solve your business problems.\n"
"                            <br/><br/>Our products are designed for small to medium size companies willing to optimize their performance."
msgstr ""
"Chúng tôi là một nhóm những người đam mê với mục tiêu cải thiện cuộc sống của mọi người thông qua các sản phẩm đột phá. Chúng tôi xây dựng các sản phẩm tuyệt vời để giải quyết các vấn đề kinh doanh của bạn.\n"
"                            <br/><br/>Sản phẩm của chúng tôi được thiết kế cho các công ty vừa và nhỏ sẵn sàng để tối ưu hóa hiệu suất của họ."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
msgid ""
"We are a team of passionate people whose goal is to improve everyone's life "
"through disruptive products. We build great products to solve your business "
"problems. Our products are designed for small to medium size companies "
"willing to optimize their performance."
msgstr ""
"Chúng tôi là một nhóm những người đam mê với mục tiêu cải thiện cuộc sống "
"của mọi người thông qua các sản phẩm đột phá. Chúng tôi xây dựng các sản "
"phẩm tuyệt vời để giải quyết các vấn đề kinh doanh của bạn. Sản phẩm của "
"chúng tôi được thiết kế cho các công ty vừa và nhỏ sẵn sàng tối ưu hóa hiệu "
"suất của họ."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
msgid ""
"We are a team of passionate people whose goal is to improve everyone's "
"life.<br/>Our services are designed for small to medium size companies."
msgstr ""
"Chúng tôi là một đội gồm những người đầy nhiệt huyết với mục tiêu là cải "
"thiện cuộc sống của mọi người.<br/>Dịch vụ của chúng tôi được thiết kế cho "
"các công ty vừa và nhỏ."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar
msgid "We are almost done!"
msgstr "Chúng ta sắp hoàn tất!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid "We are in good company."
msgstr "Đồng hành cùng chúng tôi. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"We do not currently support Do Not Track signals, as there is no industry "
"standard for compliance."
msgstr ""
"Chúng tôi hiện không hỗ trợ các tín hiệu Không theo dõi, vì không có tiêu "
"chuẩn ngành nào về việc tuân thủ."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "We found these ones:"
msgstr "Những gì chúng tôi đã tìm thấy: "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_header_slogan_oe_structure_header_slogan_1
msgid "We help <b>you</b> grow your business"
msgstr "Chúng tôi giúp <b>bạn</b> phát triển kinh doanh"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"We may not be able to provide the best service to you if you reject those "
"cookies, but the website will work."
msgstr ""
"Có thể chúng tôi không thể cung cấp dịch vụ tốt nhất cho bạn nếu bạn từ chối"
" các cookie đó, nhưng trang web sẽ hoạt động."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "We offer tailor-made products according to your needs and your budget."
msgstr ""
"Chúng tôi điều chỉnh các sản phẩm dựa theo nhu cầu và ngân sách của bạn. "

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: model_terms:ir.ui.view,arch_db:website.cookies_bar
#, python-format
msgid "We use cookies to provide you a better user experience."
msgstr ""
"Chúng tôi sử dụng cookie để cung cấp cho bạn trải nghiệm người dùng tốt hơn."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#, python-format
msgid ""
"We use them to store info about your habits on our website. It will helps us"
" to provide you the very best experience and customize what you see."
msgstr ""
"Chúng tôi sử dụng chúng để lưu trữ thông tin về thói quen của bạn trên trang"
" web của chúng tôi. Nó sẽ giúp chúng tôi cung cấp cho bạn trải nghiệm tốt "
"nhất và tùy chỉnh những gì bạn thấy."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid "We will get back to you shortly."
msgstr "Chúng tôi sẽ phản hồi bạn sớm."

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "We'll set you up and running in"
msgstr "Chúng tôi sẽ giúp bạn thiết lập và hoạt động trong"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
msgid "Web Visitors"
msgstr "Khách truy cập web"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/xml/website.backend.xml:0
#: model:ir.actions.act_url,name:website.action_website
#: model:ir.model,name:website.model_website
#: model:ir.model.fields,field_description:website.field_ir_asset__website_id
#: model:ir.model.fields,field_description:website.field_ir_attachment__website_id
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_id
#: model:ir.model.fields,field_description:website.field_res_company__website_id
#: model:ir.model.fields,field_description:website.field_res_partner__website_id
#: model:ir.model.fields,field_description:website.field_res_users__website_id
#: model:ir.model.fields,field_description:website.field_website_menu__website_id
#: model:ir.model.fields,field_description:website.field_website_multi_mixin__website_id
#: model:ir.model.fields,field_description:website.field_website_page__website_id
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__website_id
#: model:ir.model.fields,field_description:website.field_website_rewrite__website_id
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__website_id
#: model:ir.model.fields,field_description:website.field_website_visitor__website_id
#: model:ir.ui.menu,name:website.menu_website_configuration
#: model_terms:ir.ui.view,arch_db:website.menu_search
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.view_server_action_search_website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
#, python-format
msgid "Website"
msgstr "Website"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_company_id
msgid "Website Company"
msgstr "Website của công ty"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__website_config_preselection
msgid "Website Config Preselection"
msgstr "Chọn trước bộ định cấu hình website"

#. module: website
#: model:ir.actions.act_url,name:website.start_configurator_act_url
msgid "Website Configurator"
msgstr "Bộ định cấu hình website"

#. module: website
#: model:ir.model,name:website.model_website_configurator_feature
msgid "Website Configurator Feature"
msgstr "Tính năng bộ định cấu hình website"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_domain
#: model:ir.model.fields,field_description:website.field_website__domain
msgid "Website Domain"
msgstr "Tên miền website"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__favicon
msgid "Website Favicon"
msgstr "Favicon website"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model__website_form_key
msgid "Website Form Key"
msgstr "Khóa biểu mẫu website"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.ir_model_view
msgid "Website Forms"
msgstr "Biểu mẫu website"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_logo
#: model:ir.model.fields,field_description:website.field_website__logo
#, python-format
msgid "Website Logo"
msgstr "Logo website"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_menu
#: model:ir.model,name:website.model_website_menu
msgid "Website Menu"
msgstr "Menu website"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_menus_form_view
msgid "Website Menus Settings"
msgstr "Thiết lập menu website"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_name
#: model:ir.model.fields,field_description:website.field_website__name
msgid "Website Name"
msgstr "Tên Website"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__first_page_id
#: model:ir.model.fields,field_description:website.field_website_page__first_page_id
msgid "Website Page"
msgstr "Trang web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_form_view
msgid "Website Page Settings"
msgstr "Thiết lập trang của website"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_pages_list
#: model_terms:ir.ui.view,arch_db:website.website_pages_tree_view
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Website Pages"
msgstr "Trang Website"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_actions_server__website_path
#: model:ir.model.fields,field_description:website.field_ir_cron__website_path
msgid "Website Path"
msgstr "Đường dẫn website"

#. module: website
#: model:ir.model,name:website.model_website_published_mixin
msgid "Website Published Mixin"
msgstr "Mixin được đăng trên website"

#. module: website
#: model:ir.model,name:website.model_website_searchable_mixin
msgid "Website Searchable Mixin"
msgstr "Mixin có thể tìm kiếm website"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: model_terms:ir.ui.view,arch_db:website.view_website_form
#, python-format
msgid "Website Settings"
msgstr "Cài đặt website"

#. module: website
#: model:ir.model,name:website.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "Bộ lọc snippet website"

#. module: website
#: model:ir.model,name:website.model_theme_website_menu
msgid "Website Theme Menu"
msgstr "Menu chủ đề Website"

#. module: website
#: model:ir.model,name:website.model_theme_website_page
msgid "Website Theme Page"
msgstr "Trang chủ đề website"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Website Title"
msgstr "Tiêu đề Website"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_attachment__website_url
#: model:ir.model.fields,field_description:website.field_res_partner__website_url
#: model:ir.model.fields,field_description:website.field_res_users__website_url
#: model:ir.model.fields,field_description:website.field_website_page__website_url
#: model:ir.model.fields,field_description:website.field_website_published_mixin__website_url
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__website_url
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__website_url
msgid "Website URL"
msgstr "URL website"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_actions_server__website_url
#: model:ir.model.fields,field_description:website.field_ir_cron__website_url
msgid "Website Url"
msgstr "Url Website"

#. module: website
#: model:ir.model,name:website.model_website_visitor
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Website Visitor"
msgstr "Khách truy cập website"

#. module: website
#: code:addons/website/models/website_visitor.py:0
#, python-format
msgid "Website Visitor #%s"
msgstr "Khách truy cập web #%s"

#. module: website
#: model:ir.actions.server,name:website.website_visitor_cron_ir_actions_server
#: model:ir.cron,cron_name:website.website_visitor_cron
#: model:ir.cron,name:website.website_visitor_cron
msgid "Website Visitor : Archive old visitors"
msgstr "Khách truy cập web: Lưu trữ khách truy cập cũ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"Website may use cookies to personalize and facilitate maximum navigation of "
"the User by this site. The User may configure his / her browser to notify "
"and reject the installation of the cookies sent by us."
msgstr ""
"Trang web có thể sử dụng cookie để cá nhân hóa và hỗ trợ điều hướng tối đa "
"cho người dùng bởi trang web này. Người dùng có thể định cấu hình trình "
"duyệt của mình để thông báo và từ chối cài đặt cookie do chúng tôi gửi."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_tree
msgid "Website menu"
msgstr "Menu website"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_description
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_description
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_description
msgid "Website meta description"
msgstr "Mô tả meta website"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_keywords
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_keywords
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_keywords
msgid "Website meta keywords"
msgstr "Từ khóa meta website"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_title
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_title
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_title
msgid "Website meta title"
msgstr "Tiêu đề meta website"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_og_img
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_og_img
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_og_img
msgid "Website opengraph image"
msgstr "Hình ảnh opengraph website"

#. module: website
#: model:ir.model,name:website.model_website_rewrite
msgid "Website rewrite"
msgstr "Viết lại website"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_rewrite_form
msgid "Website rewrite Settings"
msgstr "Cài đặt viết lại website"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.action_website_rewrite_tree
msgid "Website rewrites"
msgstr "Website viết lại"

#. module: website
#: model:ir.actions.server,name:website.ir_actions_server_website_google_analytics
msgid "Website: Analytics"
msgstr "Website: Phân tích"

#. module: website
#: model:ir.actions.server,name:website.ir_actions_server_website_dashboard
msgid "Website: Dashboard"
msgstr "Website: Dashboard"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_list
#: model:ir.ui.menu,name:website.menu_website_websites_list
#: model_terms:ir.ui.view,arch_db:website.view_website_tree
msgid "Websites"
msgstr "Website"

#. module: website
#: model:ir.model.fields,field_description:website.field_base_language_install__website_ids
msgid "Websites to translate"
msgstr "Website cần dịch"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Websites-shared page"
msgstr "Trang website được chia sẻ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Welcome to your"
msgstr "Chào mừng bạn đến với"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "What you see is what you get"
msgstr "Những gì bạn thấy là những gì bạn nhận được"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Width"
msgstr "Độ rộng"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__ir_ui_view__visibility__password
msgid "With Password"
msgstr "Kèm mật khẩu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Women"
msgstr "Nữ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid ""
"Would you like to save before being redirected? Unsaved changes will be "
"discarded."
msgstr ""
"Bạn có muốn lưu trước khi chuyển hướng không? Thay đổi chưa được lưu sẽ bị "
"huỷ bỏ. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"Write a quote here from one of your customers. Quotes are a great way to "
"build confidence in your products or services."
msgstr ""
"Trích dẫn nhận xét của khách hàng tại đây. Dùng trích dẫn là cách tuyệt vời "
"để xây dựng niềm tin vào sản phẩm hoặc dịch vụ của bạn."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "Write one or two paragraphs describing your product or services."
msgstr "Viết một hoặc hai đoạn văn miêu tả sản phẩm hoặc dịch vụ của bạn. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid ""
"Write one or two paragraphs describing your product or services. To be "
"successful your content needs to be useful to your readers."
msgstr ""
"Viết một hoặc hai đoạn văn mô tả sản phẩm hoặc dịch vụ của bạn. Để thành "
"công, nội dung bạn viết cần phải hữu ích cho người đọc."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cover
msgid ""
"Write one or two paragraphs describing your product, services or a specific "
"feature.<br/> To be successful your content needs to be useful to your "
"readers."
msgstr ""
"Viết một hoặc hai đoạn văn mô tả sản phẩm, dịch vụ hoặc một tính năng cụ "
"thể.<br/> Để thành công, nội dung bạn viết cần phải hữu ích cho người đọc."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid ""
"Write what the customer would like to know, <br/>not what you want to show."
msgstr ""
"Viết những gì khách hàng muốn biết <br/>chứ không phải những gì bạn muốn thể"
" hiện."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline_options
msgid "Year"
msgstr "Năm"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/translator.xml:0
#, python-format
msgid "You are about to enter the translation mode."
msgstr "Bạn chuẩn bị vào chế độ dịch thuật."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"You can choose to have your computer warn you each time a cookie is being sent, or you can choose to turn off all cookies.\n"
"                            Each browser is a little different, so look at your browser's Help menu to learn the correct way to modify your cookies."
msgstr ""
"Bạn có thể chọn cách máy tính cảnh báo mỗi lần cookie được gửi đi hoặc chọn tắt tất cả cookie.\n"
"                            Vì mỗi trình duyệt có thiết kế khác nhau nên bạn cần đọc phần Trợ giúp để học cách chỉnh cookie đúng cách."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "You can edit colors and backgrounds to highlight features."
msgstr "Bạn có thể chỉnh màu và hình nền để làm nổi bật các tính năng."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "You can edit, duplicate..."
msgstr "Bạn có thể chỉnh sửa, sao chép ..."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"You can have 2 websites with same domain AND a condition on country group to"
" select wich website use."
msgstr ""
"Bạn có thể có 2 website cùng tên miền VÀ một điều kiện trong nhóm quốc gia "
"để chọn sử dụng website. "

#. module: website
#: code:addons/website/models/res_users.py:0
#: model:ir.model.constraint,message:website.constraint_res_users_login_key
#, python-format
msgid "You can not have two users with the same login!"
msgstr "Bạn không thể có hai người dùng có cùng thông tin đăng nhập!"

#. module: website
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "You can only use template prefixed by dynamic_filter_template_ "
msgstr "Bạn chỉ có thể sử dụng mẫu có tiền tố là dynamic_filter_template_ "

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "You can't duplicate a model field."
msgstr "Bạn không thể nhân bản trường mẫu. "

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "You can't duplicate the submit button of the form."
msgstr "Bạn không thể nhân bản nút nộp đơn. "

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "You can't remove a field that is required by the model itself."
msgstr "Bạn không thể gỡ bỏ trường bắt buộc của bản mẫu. "

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "You can't remove the submit button of the form"
msgstr "Bạn không thể bỏ nút nộp đơn"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid ""
"You cannot delete default website %s. Try to change its settings instead"
msgstr ""
"Bạn không thể xóa trang web mặc định %s. Thay vào đó hãy thử thay đổi cài "
"đặt trang web"

#. module: website
#: code:addons/website/controllers/backend.py:0
#, python-format
msgid "You do not have sufficient rights to perform that action."
msgstr "Bạn không có đủ quyền để thực hiện tác vụ đó. "

#. module: website
#: code:addons/website/models/mixins.py:0
#, python-format
msgid "You do not have the rights to publish/unpublish"
msgstr "Bạn không có quyền đăng/hủy đăng"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "You do not seem to have access to this Analytics Account."
msgstr "Dường như bạn không có quyền truy cập vào Tài khoản Analytics này."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid ""
"You have hidden this page from search results. It won't be indexed by search"
" engines."
msgstr ""
"Bạn đã ẩn trang này khỏi kết quả tìm kiếm. Các công cụ tìm kiếm sẽ không lập"
" chỉ mục trang này. "

#. module: website
#: code:addons/website/models/res_config_settings.py:0
#, python-format
msgid "You haven't defined your domain"
msgstr "Bạn chưa xác định tên miền "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "You may opt-out of a third-party's use of cookies by visiting the"
msgstr "Bạn có thể từ chối sử dụng cookie của bên thứ ba bằng cách truy cập"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "You must keep at least one website."
msgstr "Bạn phải giữ ít nhất một website."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "You need to log in to your Google Account before:"
msgstr "Bạn cần phải đăng nhập vào Tài khoản Google trước khi:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"You should carefully review the legal statements and other conditions of use"
" of any website which you access through a link from this Website. Your "
"linking to any other off-site pages or other websites is at your own risk."
msgstr ""
"Bạn nên xem xét cẩn thận các tuyên bố pháp lý và các điều kiện sử dụng khác "
"của bất kỳ trang web nào bạn truy cập thông qua một liên kết từ trang web "
"này. Bạn phải chịu trách nhiệm khi liên kết với bất kỳ trang nào ngoài trang"
" web hoặc các website khác. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_searchbar
msgid "You will get results from blog posts, products, etc"
msgstr "Bạn sẽ nhận được kết quả từ blog posts, sản phẩm, v.v"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "You'll be able to create your pages later on."
msgstr "Bạn có thể tạo các trang sau. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "YouTube"
msgstr "YouTube"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Your Client ID:"
msgstr "ID khách hàng: "

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Your Company"
msgstr "Công ty của bạn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_template
msgid ""
"Your Dynamic Snippet will be displayed here... This message is displayed "
"because you did not provided both a filter and a template to use.<br/>"
msgstr ""
"Đoạn trích sống động sẽ được hiển thị tại đây... Thông báo này xuất hiện vì "
"bạn đã không cung cấp cả bộ lọc và mẫu để sử dụng. <br/>"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Your Email"
msgstr "Email"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Your Measurement ID:"
msgstr "Measurement ID của bạn: "

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Your Name"
msgstr "Tên"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Your Question"
msgstr "Câu hỏi của bạn"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid "Your description looks too long."
msgstr "Mô tả quá dài."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid "Your description looks too short."
msgstr "Mô tả quá ngắn."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Your experience may be degraded if you discard those cookies, but the "
"website will still work."
msgstr ""
"Trải nghiệm của bạn có thể bị giảm sút nếu bạn hủy các cookie đó, nhưng "
"trang web sẽ vẫn hoạt động."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
msgid "Your search '"
msgstr "Tìm kiếm '"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "Your title"
msgstr "Tiêu đề"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_youtube
#: model:ir.model.fields,field_description:website.field_website__social_youtube
msgid "Youtube Account"
msgstr "Tài khoản Youtube"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Zoom"
msgstr "Zoom"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Zoom In"
msgstr "Zoom In"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Zoom In-Down"
msgstr "Zoom In-Down"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Zoom In-Left"
msgstr "Zoom In-Left"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Zoom In-Right"
msgstr "Zoom In-Right"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Zoom Out"
msgstr "Zoom Out"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"__gads (Google)<br/>\n"
"                                            __gac (Google)"
msgstr ""
"__gads (Google)<br/>\n"
"                                            __gac (Google)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"_ga (Google)<br/>\n"
"                                            _gat (Google)<br/>\n"
"                                            _gid (Google)<br/>\n"
"                                            _gac_* (Google)"
msgstr ""
"_ga (Google)<br/>\n"
"                                            _gat (Google)<br/>\n"
"                                            _gid (Google)<br/>\n"
"                                            _gac_* (Google)"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "a blog"
msgstr "một trang blog"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "a business website"
msgstr "website kinh doanh"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "a new image"
msgstr "một hình ảnh mới"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "a pre-made Palette"
msgstr "một bảng màu tạo sẵn"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "an elearning platform"
msgstr "một nền tảng elearning"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "an event website"
msgstr "một website sự kiện"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "an online store"
msgstr "một cửa hàng online"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "and"
msgstr "và"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "and copy paste the address of the font page here."
msgstr "và sao chép và dán địa chỉ của trang phông chữ vào đây."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "big"
msgstr "lớn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "breadcrumb"
msgstr "breadcrumb"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "btn-outline-primary"
msgstr "nút-viền-chính"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "btn-outline-secondary"
msgstr "nút-viền-phụ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "btn-primary"
msgstr "nút-chính"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "btn-secondary"
msgstr "nút-phụ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "business"
msgstr "của tôi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "celebration, launch"
msgstr "lễ kỷ niệm, ra mắt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "chart, table, diagram, pie"
msgstr "biểu đồ, bảng, sơ đồ, hình tròn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "cite"
msgstr "dẫn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "columns, description"
msgstr "cột, mô tả"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "common answers, common questions"
msgstr "câu trả lời phổ biến, câu hỏi phổ biến"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "content"
msgstr "nội dung"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "customers, clients"
msgstr "khách hàng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "days"
msgstr "ngày"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "develop the brand"
msgstr "phát triển thương hiệu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "e.g. /my-awesome-page"
msgstr "e.g. /my-awesome-page"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "e.g. About Us"
msgstr "vd: Về chúng tôi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "e.g. De Brouckere, Brussels, Belgium"
msgstr "e.g. Quận 12, Thành phố Hồ Chí Minh, Việt Nam"

#. module: website
#: model:ir.ui.menu,name:website.menu_website_dashboard
msgid "eCommerce Dashboard"
msgstr "eCommerce dashboard"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_elearning
msgid "eLearning"
msgstr "eLearning"

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "email"
msgstr "email"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "esc"
msgstr "esc"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "evolution, growth"
msgstr "tiến hóa, tăng trưởng"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "fonts.google.com"
msgstr "fonts.google.com"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "for my"
msgstr "cho"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "found(s)"
msgstr "được tìm thấy"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.brand_promotion
msgid "free website"
msgstr "website miễn phí"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "from Logo"
msgstr "từ Logo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "frontend_lang (Odoo)"
msgstr "frontend_lang (Odoo)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "gallery, carousel"
msgstr "phòng trưng bày, băng chuyền"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "get leads"
msgstr "tạo lead"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "google1234567890123456.html"
msgstr "google1234567890123456.html"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "heading, h1"
msgstr "tiêu đề, h1"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "hero, jumbotron"
msgstr "toàn màn hình, màn hình lớn"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "https://fonts.google.com/specimen/Roboto"
msgstr "https://fonts.google.com/specimen/Roboto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "https://www.odoo.com"
msgstr "https://www.odoo.com"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "iPhone"
msgstr "iPhone"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"im_livechat_previous_operator_pid (Odoo)<br/>\n"
"                                            utm_campaign (Odoo)<br/>\n"
"                                            utm_source (Odoo)<br/>\n"
"                                            utm_medium (Odoo)"
msgstr ""
"im_livechat_previous_operator_pid (Odoo)<br/>\n"
"                                            utm_campaign (Odoo)<br/>\n"
"                                            utm_source (Odoo)<br/>\n"
"                                            utm_medium (Odoo)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "image, media, illustration"
msgstr "hình ảnh, phương tiện, minh họa"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "in the top right corner to start designing."
msgstr "ở góc trên cùng bên phải để bắt đầu thiết kế."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#: model_terms:ir.ui.view,arch_db:website.template_header_contact_oe_structure_header_contact_1
#: model_terms:ir.ui.view,arch_db:website.template_header_hamburger_oe_structure_header_hamburger_3
#: model_terms:ir.ui.view,arch_db:website.template_header_sidebar_oe_structure_header_sidebar_1
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "inform customers"
msgstr "thông báo cho khách hàng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "instance of Odoo, the"
msgstr "một ứng dụng của Odoo, "

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_ui_view__inherit_id__ir_ui_view
msgid "ir.ui.view"
msgstr "ir.ui.view"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "link"
msgstr "link"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "masonry, grid"
msgstr "gạch lát, lưới"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "menu, pricing"
msgstr "menu, giá cả"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "no value"
msgstr "không có giá trị"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "o-color-"
msgstr "o-color-"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.res_config_settings.xml:0
#, python-format
msgid ""
"of\n"
"            your visitors. We recommend you avoid them unless you have\n"
"            verified with a legal advisor that you absolutely need cookie\n"
"            consent in your country."
msgstr ""
"của\n"
"            khách truy cập. Chúng tôi khuyên bạn nên tránh sử dụng trừ khi bạn đã xác \n"
"            nhận với cố vấn pháp lý rằng bạn nhất định cần sự đồng ý sử dụng cookie\n"
"            tại quốc gia của bạn. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "or Edit Master"
msgstr "hoặc Sửa bản gốc"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "organization, structure"
msgstr "cấu trúc, tổ chức"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "page, snippets, ...)"
msgstr ", snippet, ...)"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "perfect website?"
msgstr "một website hoàn hảo chưa?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_form_preview
msgid "phone"
msgstr "điện thoại"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "placeholder"
msgstr "trình giữ chỗ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#, python-format
msgid "pointer to build the perfect page in 7 steps."
msgstr "con trỏ để tạo trang hoàn hảo trong 7 bước. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "pricing"
msgstr "giá"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "promotion, characteristic, quality"
msgstr "khuyến mãi, đặc tính, chất lượng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "results"
msgstr "kết quả"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "rows"
msgstr "dòng"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "schedule appointments"
msgstr "lên lịch hẹn"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "sell more"
msgstr "bán nhiều hàng hơn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "separator, divider"
msgstr "dấu phân cách, dải phân cách"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "session_id (Odoo)<br/>"
msgstr "session_id (Odoo)<br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "statistics, stats, KPI"
msgstr "thống kê, chỉ số thống kê, KPI"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "testimonials"
msgstr "lời chứng thực"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "text link"
msgstr "văn bản link"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_ui_view__inherit_id__theme_ir_ui_view
msgid "theme.ir.ui.view"
msgstr "theme.ir.ui.view"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
#, python-format
msgid "this page"
msgstr "trang này"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "to exit full screen"
msgstr "để thoát chế độ toàn màn hình"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "true"
msgstr "đúng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "valuation, rank"
msgstr "định giá, xếp hạng"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_id
#, python-format
msgid "website"
msgstr "website"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "with the main objective to"
msgstr "với mục đích chính là"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "yes"
msgstr "có"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.res_config_settings.xml:0
#, python-format
msgid "you do not need to ask for the consent"
msgstr "bạn không cần phải yêu cầu sự đồng ý"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "⌙ Active"
msgstr "⌙ Hoạt động"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Background"
msgstr "⌙ Nền"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "⌙ Blur"
msgstr "⌙ Mờ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Buttons"
msgstr "⌙ Nút"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "⌙ Color"
msgstr "⌙ Màu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Colors"
msgstr "⌙ Màu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Country"
msgstr "⌙ Quốc gia"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "⌙ Delay"
msgstr "⌙ Độ trễ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
msgid "⌙ Desktop"
msgstr "⌙ Máy tính bàn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "⌙ Display"
msgstr "⌙ Hiển thị"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Headings"
msgstr "⌙ Tiêu đề"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "⌙ Height"
msgstr "⌙ Chiều cao"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Hue"
msgstr "⌙ Hue"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "⌙ Inactive"
msgstr "⌙ Không h.động"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "⌙ Intensity"
msgstr "⌙ Cường độ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Label"
msgstr "⌙ Nhãn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Languages"
msgstr "⌙ Ngôn ngữ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Large"
msgstr "⌙ Lớn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
msgid "⌙ Mobile"
msgstr "⌙ Di động"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Off-Canvas Logo"
msgstr "⌙ Logo Off-Canvas "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "⌙ Offset (X, Y)"
msgstr "⌙ Offset (X, Y)"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "⌙ Page Anchor"
msgstr "⌙ Neo trang"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "⌙ Parallax"
msgstr "⌙ Parallax"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "⌙ Position"
msgstr "⌙ Vị trí "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Saturation"
msgstr "⌙ Độ rực màu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog_options
msgid "⌙ Separator"
msgstr "⌙ Phân cách"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Small"
msgstr "⌙ Nhỏ"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Spacing"
msgstr "⌙ Khoảng cách"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "⌙ Spread"
msgstr "⌙ Dàn trải"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "⌙ Style"
msgstr "⌙ Kiểu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ UTM Campaign"
msgstr "⌙ UTM Campaign"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ UTM Medium"
msgstr "⌙ UTM Medium"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ UTM Source"
msgstr "⌙ UTM Source"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Users"
msgstr "⌙ Người dùng"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "⌙ Video"
msgstr "⌙ Video"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Width"
msgstr "⌙ Độ rộng"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "└ Height"
msgstr ""
