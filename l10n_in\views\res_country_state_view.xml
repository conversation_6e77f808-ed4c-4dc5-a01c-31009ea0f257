<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="l10n_in_view_country_state_form_inherit" model="ir.ui.view">
        <field name="name">l10n.in.res.country.state.form.inhert</field>
        <field name="model">res.country.state</field>
        <field name="inherit_id" ref="base.view_country_state_form"/>
        <field name="arch" type="xml">
            <field name="code" position="after">
                <field name="l10n_in_tin" attrs="{'invisible': [('country_id', '!=', %(base.in)d)]}"/>
            </field>
        </field>
    </record>

</odoo>
