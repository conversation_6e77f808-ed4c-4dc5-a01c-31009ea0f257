# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_presence
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: Ra<PERSON><PERSON><PERSON>iam, 2023\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_presence
#: model:mail.template,body_html:hr_presence.mail_template_presence
msgid ""
"<div>\n"
"                    Dear <t t-out=\"object.name or ''\"><PERSON></t>,<br/><br/>\n"
"                    Exception made if there was a mistake of ours, it seems that you are not at your office and there is not request of time off from you.<br/>\n"
"                    Please, take appropriate measures in order to carry out this work absence.<br/>\n"
"                    Do not hesitate to contact your manager or the human resource department.\n"
"                    <br/>Best Regards,<br/><br/>\n"
"                </div>\n"
"            "
msgstr ""
"<div>\n"
"                    เรียน <t t-out=\"object.name or ''\">Abigail Peterson</t>,<br/><br/>\n"
"                    มีข้อยกเว้นหากเป็นข้อผิดพลาดของเรา ดูเหมือนว่าคุณไม่ได้อยู่ที่สำนักงานและไม่มีการลาจากคุณ<br/>\n"
"                    โปรดใช้มาตรการที่เหมาะสมเพื่อดำเนินการขาดงานนี้<br/>\n"
"                    อย่าลังเลที่จะติดต่อผู้จัดการของคุณหรือฝ่ายทรัพยากรบุคคล\n"
"                    <br/>ขอแสดงความนับถืออย่างสูง<br/><br/>\n"
"                </div>\n"
"            "

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_presence_search
msgid "Absence/Presence"
msgstr "การขาดงาน/การเข้างาน"

#. module: hr_presence
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee__hr_presence_state_display__absent
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_base__hr_presence_state_display__absent
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_public__hr_presence_state_display__absent
msgid "Absent"
msgstr "ขาด"

#. module: hr_presence
#: model:ir.model,name:hr_presence.model_hr_employee_base
msgid "Basic Employee"
msgstr "พนักงานทั่วไป"

#. module: hr_presence
#: model:ir.model,name:hr_presence.model_res_company
msgid "Companies"
msgstr "บริษัท"

#. module: hr_presence
#: code:addons/hr_presence/models/hr_employee.py:0
#, python-format
msgid "Compose Email"
msgstr "เขียนอีเมล"

#. module: hr_presence
#: model:ir.actions.server,name:hr_presence.ir_actions_server_action_open_presence_view
msgid "Compute presence and open presence view"
msgstr "การมีอยู่ของคอมพิวเตอร์และมุมมองการมีอยู่แบบเปิด"

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_res_users_log__create_uid
msgid "Create Uid"
msgstr "สร้าง Uid"

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee__email_sent
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_base__email_sent
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_public__email_sent
msgid "Email Sent"
msgstr "ส่งอีเมลแล้ว"

#. module: hr_presence
#: model:ir.ui.menu,name:hr_presence.menu_hr_presence_view
msgid "Employee Presence"
msgstr "การเข้างานของพนักงาน"

#. module: hr_presence
#: model:mail.template,name:hr_presence.mail_template_presence
msgid "Employee: Absence email"
msgstr "พนักงาน: อีเมลการขาด"

#. module: hr_presence
#: model:sms.template,name:hr_presence.sms_template_data_hr_presence
msgid "Employee: Presence Reminder"
msgstr "พนักงาน: แจ้งเตือนการเข้างาน"

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_presence_search
msgid "Employees"
msgstr "พนักงาน"

#. module: hr_presence
#: code:addons/hr_presence/models/hr_employee.py:0
#: model:sms.template,body:hr_presence.sms_template_data_hr_presence
#, python-format
msgid ""
"Exception made if there was a mistake of ours, it seems that you are not at your office and there is not request of time off from you.\n"
"Please, take appropriate measures in order to carry out this work absence.\n"
"Do not hesitate to contact your manager or the human resource department."
msgstr ""
"มีข้อยกเว้นหากมีข้อผิดพลาดของเรา ดูเหมือนว่าคุณไม่ได้อยู่ที่สำนักงานและไม่มีการขอเวลาจากคุณ\n"
"โปรดใช้มาตรการที่เหมาะสมเพื่อดำเนินการขาดงานนี้\n"
"อย่าลังเลที่จะติดต่อผู้จัดการของคุณหรือฝ่ายทรัพยากรบุคคล"

#. module: hr_presence
#: model:ir.actions.server,name:hr_presence.ir_cron_presence_control_ir_actions_server
#: model:ir.cron,cron_name:hr_presence.ir_cron_presence_control
#: model:ir.cron,name:hr_presence.ir_cron_presence_control
msgid "HR Presence: cron"
msgstr "การเข้างานของ HR: cron"

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_res_company__hr_presence_last_compute_date
msgid "Hr Presence Last Compute Date"
msgstr "การคำนวณวันที่เข้างานของ Hr"

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee__hr_presence_state_display
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_base__hr_presence_state_display
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_public__hr_presence_state_display
msgid "Hr Presence State Display"
msgstr "แสดงสถานะการเข้างานของ Hr"

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_res_users_log__ip
msgid "IP Address"
msgstr "ที่อยู่ IP"

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee__ip_connected
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_base__ip_connected
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_public__ip_connected
msgid "Ip Connected"
msgstr "เชื่อมต่อ Ip"

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_kanban
msgid "Log"
msgstr "ล็อก"

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee__manually_set_present
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_base__manually_set_present
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_public__manually_set_present
msgid "Manually Set Present"
msgstr "ตั้งค่าปัจจุบันด้วยตนเอง"

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_presence_search
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_search
msgid "Presence"
msgstr "การมา"

#. module: hr_presence
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee__hr_presence_state_display__present
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_base__hr_presence_state_display__present
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_public__hr_presence_state_display__present
msgid "Present"
msgstr "การมา"

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_kanban
msgid "SMS"
msgstr "SMS"

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_kanban
msgid "Set as absent"
msgstr "ตั้งค่าว่าขาด"

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_kanban
msgid "Set as present"
msgstr "ตั้งค่าว่ามา "

#. module: hr_presence
#: code:addons/hr_presence/models/hr_employee.py:0
#, python-format
msgid "There is no professional email address for this employee."
msgstr "ไม่มีที่อยู่อีเมลแบบมืออาชีพสำหรับพนักงานรายนี้"

#. module: hr_presence
#: code:addons/hr_presence/models/hr_employee.py:0
#, python-format
msgid "There is no professional mobile for this employee."
msgstr "ไม่มีมือถือระดับมืออาชีพสำหรับพนักงานคนนี้"

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_kanban
msgid "Time Off"
msgstr "การลา"

#. module: hr_presence
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee__hr_presence_state_display__to_define
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_base__hr_presence_state_display__to_define
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_public__hr_presence_state_display__to_define
msgid "To Define"
msgstr "ระบุ"

#. module: hr_presence
#: model:mail.template,subject:hr_presence.mail_template_presence
msgid "Unexpected Absence"
msgstr "การขาดโดยไม่คาดคิด"

#. module: hr_presence
#: model:ir.model,name:hr_presence.model_res_users_log
msgid "Users Log"
msgstr "ลงชื่อผู้ใช้"

#. module: hr_presence
#: code:addons/hr_presence/models/hr_employee.py:0
#: code:addons/hr_presence/models/hr_employee.py:0
#: code:addons/hr_presence/models/hr_employee.py:0
#, python-format
msgid "You don't have the right to do this. Please contact an Administrator."
msgstr "คุณไม่มีสิทธิ์ทำเช่นนี้ โปรดติดต่อผู้ดูแลระบบ"

#. module: hr_presence
#: code:addons/hr_presence/models/hr_employee.py:0
#, python-format
msgid "Employee's Presence to Define"
msgstr "การแสดงตนของพนักงานเพื่อกำหนด"
