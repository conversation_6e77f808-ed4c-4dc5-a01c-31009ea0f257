<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="loangrant_sukuk_details_form" model="ir.ui.view">
        <field name="name">loangrant.sukuk.wizard.report.form</field>
        <field name="model">loangrant.sukuk.wizard.report</field>
        <field name="arch" type="xml">
            <form string="صكوك سلف/عهد">
                <group>
                    <group>
                        <field name="type" required="1"/>
                        <field name="loan_id" attrs="{'required':[('type','=','loan')], 'invisible':[('type','=','grants')]}" options="{'no_create': True, 'no_create_edit':True}"/>
                        <field name="grant_id" attrs="{'required':[('type','=','grants')], 'invisible':[('type','=','loan')]}" options="{'no_create': True, 'no_create_edit':True}"/>
<!--                        <field name="loan_partner_id" readonly="1" options="{'no_open':True}"  attrs="{'invisible':[('type','!=','loan')]}"/>-->
<!--                        <field name="grant_partner_id" options="{'no_open':True}" attrs="{'invisible':[('type','!=','grants')]}"/>-->
                        <field name="partner_paid_to"/>
                        <field name="amount"/>
                    </group>
                    <group>
                        <field name="account_no_id" options="{'no_create': True, 'no_create_edit':True}"/>
                        <field name="bank_id" options="{'no_create': True, 'no_create_edit':True}"/>
                        <field name="branch_id" options="{'no_create': True, 'no_create_edit':True}"/>
                        <field name="dialog_box" readonly="1" nolabel="1"/>
                    </group>
                    <group>
                        <field name="user_id" readonly="1"/>
                        <field name="sukuk_computed" invisible="1" readonly="1"/>
                    </group>
                </group>
                <h3>
                    في حالة تعديل البيانات يدوياً يرجى التأكد من صحة الرقم التسلسلي ورقم الدفتر
                </h3>
                <notebook>
                    <page string="الصكوك">
                        <field name="sukuk_page_ids" readonly="0">
                            <tree editable="top" create="false">
                                <field name="sukuk_book_id" string="دفتر الصكوك" options="{'no_create': True,'no_open': True, 'no_create_edit':True}" invisible="0"/>
                                <field name="bank_id" invisible="0" readonly="1"/>
                                <field name="branch_id" invisible="0" readonly="1"/>
                                <field name="partner_paid_to" readonly="1"/>
                                <field name="person_signature" readonly="1"/>
                                <field name="suke_book_number" readonly="0"/>
                                <field name="serial_no" readonly="0"/>
                                <field name="amount" readonly="1"/>
                                <field name="note" readonly="1"/>
                            </tree>
                        </field>
                    </page>
                </notebook>
                <footer>
                    <button name="generate_payslips_sukuk"
                            type="object"
                            string="انشاء صك"
                            attrs="{'invisible':[('sukuk_computed','=',True)]}"
                            class="btn-primary"/>
                    <button name="get_confirm"
                            type="object"
                            string="تأكيد"
                            class="btn-primary"
                            attrs="{'invisible':[('sukuk_computed','=',False)]}"
                            confirm="في حالة التأكيد فسيتم اصدار الصك وتخزينه في المنظومة !"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>


    <record id="action_report_loangrant_sukuk_details" model="ir.actions.act_window">
        <field name="name">اصدار صكوك سلف/عهد</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">loangrant.sukuk.wizard.report</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="loangrant_sukuk_details_form"/>
        <field name="target">new</field>
    </record>

    <menuitem id="د"
              name="اصدار صكوك سلف/عهد"
              parent="account.menu_finance_entries"
              sequence="83"
              action="action_report_loangrant_sukuk_details"/>

</odoo>