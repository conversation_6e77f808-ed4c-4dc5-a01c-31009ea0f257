# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_de
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-22 09:50+0000\n"
"PO-Revision-Date: 2021-07-22 09:50+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_de
#: model_terms:ir.ui.view,arch_db:l10n_de.din5008_css
msgid ""
"&amp;.din_page {\n"
"                        &amp;.header {\n"
"                            .company_header {\n"
"                                .name_container {\n"
"                                    color:"
msgstr ""

#. module: l10n_de
#: model:ir.model.fields,help:l10n_de.field_account_tax__l10n_de_datev_code
msgid "2 digits code use by Datev"
msgstr ""

#. module: l10n_de
#: model_terms:ir.ui.view,arch_db:l10n_de.din5008_css
msgid ""
";\n"
"                                    }\n"
"                                }\n"
"                            }\n"
"                            h2 {\n"
"                                color:"
msgstr ""

#. module: l10n_de
#: model_terms:ir.ui.view,arch_db:l10n_de.din5008_css
msgid ""
";\n"
"                                }\n"
"                            }\n"
"                        }\n"
"                        &amp;.invoice_note {\n"
"                            td {\n"
"                                .address {\n"
"                                    &gt; span {\n"
"                                        color:"
msgstr ""

#. module: l10n_de
#: model_terms:ir.ui.view,arch_db:l10n_de.din5008_css
msgid ""
";\n"
"                            }\n"
"                            .page {\n"
"                                [name=invoice_line_table], [name=stock_move_table], .o_main_table {\n"
"                                    th {\n"
"                                        color:"
msgstr ""

#. module: l10n_de
#: code:addons/l10n_de/models/datev.py:0
#, python-format
msgid ""
"Account %s does not authorize to have tax %s specified on the line."
"                                 Change the tax used in this invoice or "
"remove all taxes from the account"
msgstr ""

#. module: l10n_de
#: model:ir.model,name:l10n_de.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_de
#: model_terms:ir.ui.view,arch_db:l10n_de.external_layout_din5008
msgid "BIC:"
msgstr ""

#. module: l10n_de
#: model:ir.model.fields,field_description:l10n_de.field_base_document_layout__bank_ids
msgid "Banks"
msgstr ""

#. module: l10n_de
#: code:addons/l10n_de/models/account_move.py:0
#, python-format
msgid "Cancelled Invoice"
msgstr ""

#. module: l10n_de
#: model:ir.model.fields,field_description:l10n_de.field_base_document_layout__city
msgid "City"
msgstr ""

#. module: l10n_de
#: model:ir.model,name:l10n_de.model_base_document_layout
msgid "Company Document Layout"
msgstr ""

#. module: l10n_de
#: model:ir.model.fields,field_description:l10n_de.field_base_document_layout__company_registry
msgid "Company Registry"
msgstr ""

#. module: l10n_de
#: code:addons/l10n_de/models/account_move.py:0
#, python-format
msgid "Credit Note"
msgstr ""

#. module: l10n_de
#: model:ir.model.fields,field_description:l10n_de.field_account_chart_template__display_name
#: model:ir.model.fields,field_description:l10n_de.field_account_move__display_name
#: model:ir.model.fields,field_description:l10n_de.field_account_tax__display_name
#: model:ir.model.fields,field_description:l10n_de.field_account_tax_template__display_name
#: model:ir.model.fields,field_description:l10n_de.field_base_document_layout__display_name
#: model:ir.model.fields,field_description:l10n_de.field_ir_actions_report__display_name
#: model:ir.model.fields,field_description:l10n_de.field_product_template__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_de
#: code:addons/l10n_de/models/account_move.py:0
#, python-format
msgid "Draft Invoice"
msgstr ""

#. module: l10n_de
#: code:addons/l10n_de/models/account_move.py:0
#: code:addons/l10n_de/models/base_document_layout.py:0
#, python-format
msgid "Due Date"
msgstr ""

#. module: l10n_de
#: model:ir.ui.menu,name:l10n_de.account_reports_de_statements_menu
msgid "Germany"
msgstr ""

#. module: l10n_de
#: model_terms:ir.ui.view,arch_db:l10n_de.external_layout_din5008
msgid "HRB Nr:"
msgstr ""

#. module: l10n_de
#: model_terms:ir.ui.view,arch_db:l10n_de.external_layout_din5008
msgid "IBAN:"
msgstr ""

#. module: l10n_de
#: model:ir.model.fields,field_description:l10n_de.field_account_chart_template__id
#: model:ir.model.fields,field_description:l10n_de.field_account_move__id
#: model:ir.model.fields,field_description:l10n_de.field_account_tax__id
#: model:ir.model.fields,field_description:l10n_de.field_account_tax_template__id
#: model:ir.model.fields,field_description:l10n_de.field_base_document_layout__id
#: model:ir.model.fields,field_description:l10n_de.field_ir_actions_report__id
#: model:ir.model.fields,field_description:l10n_de.field_product_template__id
msgid "ID"
msgstr ""

#. module: l10n_de
#: code:addons/l10n_de/models/account_move.py:0
#: code:addons/l10n_de/models/base_document_layout.py:0
#, python-format
msgid "Invoice"
msgstr ""

#. module: l10n_de
#: code:addons/l10n_de/models/account_move.py:0
#, python-format
msgid "Invoicing Address:"
msgstr ""

#. module: l10n_de
#: code:addons/l10n_de/models/account_move.py:0
#, python-format
msgid "Invoicing and Shipping Address:"
msgstr ""

#. module: l10n_de
#: code:addons/l10n_de/models/account_move.py:0
#: code:addons/l10n_de/models/base_document_layout.py:0
#, python-format
msgid "Invoice Date"
msgstr ""

#. module: l10n_de
#: code:addons/l10n_de/models/account_move.py:0
#: code:addons/l10n_de/models/base_document_layout.py:0
#, python-format
msgid "Invoice No."
msgstr ""

#. module: l10n_de
#: model:ir.model,name:l10n_de.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_de
#: model:ir.model.fields,field_description:l10n_de.field_account_bank_statement_line__l10n_de_addresses
#: model:ir.model.fields,field_description:l10n_de.field_account_move__l10n_de_addresses
#: model:ir.model.fields,field_description:l10n_de.field_account_payment__l10n_de_addresses
#: model:ir.model.fields,field_description:l10n_de.field_base_document_layout__l10n_de_addresses
msgid "L10N Din5008 Addresses"
msgstr ""

#. module: l10n_de
#: model:ir.model.fields,field_description:l10n_de.field_account_tax__l10n_de_datev_code
#: model:ir.model.fields,field_description:l10n_de.field_account_tax_template__l10n_de_datev_code
msgid "L10N De Datev Code"
msgstr ""

#. module: l10n_de
#: model:ir.model.fields,field_description:l10n_de.field_account_bank_statement_line__l10n_de_document_title
#: model:ir.model.fields,field_description:l10n_de.field_account_move__l10n_de_document_title
#: model:ir.model.fields,field_description:l10n_de.field_account_payment__l10n_de_document_title
#: model:ir.model.fields,field_description:l10n_de.field_base_document_layout__l10n_de_document_title
msgid "L10N De Document Title"
msgstr ""

#. module: l10n_de
#: model:ir.model.fields,field_description:l10n_de.field_account_bank_statement_line__l10n_de_template_data
#: model:ir.model.fields,field_description:l10n_de.field_account_move__l10n_de_template_data
#: model:ir.model.fields,field_description:l10n_de.field_account_payment__l10n_de_template_data
#: model:ir.model.fields,field_description:l10n_de.field_base_document_layout__l10n_de_template_data
msgid "L10N De Template Data"
msgstr ""

#. module: l10n_de
#: model:ir.model.fields,field_description:l10n_de.field_account_chart_template____last_update
#: model:ir.model.fields,field_description:l10n_de.field_account_move____last_update
#: model:ir.model.fields,field_description:l10n_de.field_account_tax____last_update
#: model:ir.model.fields,field_description:l10n_de.field_account_tax_template____last_update
#: model:ir.model.fields,field_description:l10n_de.field_base_document_layout____last_update
#: model:ir.model.fields,field_description:l10n_de.field_ir_actions_report____last_update
#: model:ir.model.fields,field_description:l10n_de.field_product_template____last_update
msgid "Last Modified on"
msgstr ""

#. module: l10n_de
#: model_terms:ir.ui.view,arch_db:l10n_de.external_layout_din5008
msgid "Page: <span class=\"page\"/> of <span class=\"topage\"/>"
msgstr ""

#. module: l10n_de
#: model:ir.model,name:l10n_de.model_product_template
msgid "Product Template"
msgstr ""

#. module: l10n_de
#: code:addons/l10n_de/models/account_move.py:0
#: code:addons/l10n_de/models/base_document_layout.py:0
#, python-format
msgid "Reference"
msgstr ""

#. module: l10n_de
#: model:ir.model,name:l10n_de.model_ir_actions_report
msgid "Report Action"
msgstr ""

#. module: l10n_de
#: code:addons/l10n_de/models/account_move.py:0
#, python-format
msgid "Shipping Address:"
msgstr ""

#. module: l10n_de
#: code:addons/l10n_de/models/account_move.py:0
#, python-format
msgid "Source"
msgstr ""

#. module: l10n_de
#: model:ir.model.fields,field_description:l10n_de.field_base_document_layout__street
msgid "Street"
msgstr ""

#. module: l10n_de
#: model:ir.model.fields,field_description:l10n_de.field_base_document_layout__street2
msgid "Street2"
msgstr ""

#. module: l10n_de
#: model:ir.model,name:l10n_de.model_account_tax
msgid "Tax"
msgstr ""

#. module: l10n_de
#: model:ir.model,name:l10n_de.model_account_tax_template
msgid "Templates for Taxes"
msgstr ""

#. module: l10n_de
#: code:addons/l10n_de/models/account_move.py:0
#, python-format
msgid "Vendor Bill"
msgstr ""

#. module: l10n_de
#: code:addons/l10n_de/models/account_move.py:0
#, python-format
msgid "Vendor Credit Note"
msgstr ""

#. module: l10n_de
#: model:ir.model.fields,field_description:l10n_de.field_base_document_layout__zip
msgid "Zip"
msgstr ""
