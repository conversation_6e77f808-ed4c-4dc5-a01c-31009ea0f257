# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* repair
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# david <PERSON>, 2021
# NoaFarkash, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# MichaelHadar, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:18+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: MichaelHadar, 2023\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

#. module: repair
#: model:ir.actions.report,print_report_name:repair.action_report_repair_order
msgid ""
"(\n"
"                object.state == 'draft' and 'Repair Quotation - %s' % (object.name) or\n"
"                'Repair Order - %s' % (object.name))"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "(<i>Remove</i>)"
msgstr "(<i>הסר</i>)"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "(update)"
msgstr "(עדכן)"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__state
msgid ""
"* The 'Draft' status is used when a user is encoding a new and unconfirmed repair order.\n"
"* The 'Confirmed' status is used when a user confirms the repair order.\n"
"* The 'Ready to Repair' status is used to start to repairing, user can start repairing only after repair order is confirmed.\n"
"* The 'Under Repair' status is used when the repair is ongoing.\n"
"* The 'To be Invoiced' status is used to generate the invoice before or after repairing done.\n"
"* The 'Done' status is set when repairing is completed.\n"
"* The 'Cancelled' status is used when user cancel repair order."
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid ": Insufficient Quantity To Repair"
msgstr ""

#. module: repair
#: model:mail.template,body_html:repair.mail_template_repair_quotation
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px;font-size: 13px;\">\n"
"        Hello <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br/>\n"
"        Here is your repair order <strong t-out=\"object.name or ''\">RO/00004</strong>\n"
"        <t t-if=\"object.invoice_method != 'none'\">\n"
"            amounting in <strong><t t-out=\"format_amount(object.amount_total, object.pricelist_id.currency_id) or ''\">$ 100.00</t>.</strong><br/>\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            .<br/>\n"
"        </t>\n"
"        You can reply to this email if you have any questions.\n"
"        <br/><br/>\n"
"        Thank you,\n"
"        <t t-if=\"user.signature\">\n"
"            <br/>\n"
"            <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<i>(Add)</i>"
msgstr "<i>(הוסף)</i>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid ""
"<span class=\"o_stat_text\">1</span>\n"
"                                <span class=\"o_stat_text\">Invoices</span>"
msgstr ""
"<span class=\"o_stat_text\">1</span>\n"
"                                <span class=\"o_stat_text\">חשבוניות</span>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">Repairs</span>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Lot/Serial Number:</strong>"
msgstr "<strong>אצווה/ מספר סידורי:</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Operations</strong>"
msgstr "<strong>פעולות</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Parts</strong>"
msgstr "<strong>חלקים</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Printing Date:</strong>"
msgstr "<strong>תאריך הדפסה:</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Product to Repair:</strong>"
msgstr "<strong>מוצר לתיקון:</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Shipping address :</strong>"
msgstr "<strong>כתובת למשלוח :</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Total Without Taxes</strong>"
msgstr "<strong>סה\"כ ללא מע\"מ</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Total</strong>"
msgstr "<strong>סה\"כ</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Warranty:</strong>"
msgstr "<strong>אחריות:</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_warn_insufficient_qty_repair_form_view
msgid "? This may lead to inconsistencies in your inventory."
msgstr "? זה עלול להוביל לחוסר עקביות במלאי שלך."

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_needaction
msgid "Action Needed"
msgstr "נדרשת פעולה"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_ids
msgid "Activities"
msgstr "פעילויות"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "סימון פעילות חריגה"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_state
msgid "Activity State"
msgstr "מצב פעילות"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_type_icon
msgid "Activity Type Icon"
msgstr "סוג פעילות"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_line__type__add
msgid "Add"
msgstr "הוסף"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Add internal notes."
msgstr "הוסף הערות פנימיות."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Add quotation notes."
msgstr "הוסף הערות הצעת מחיר..."

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__invoice_method__after_repair
msgid "After Repair"
msgstr "לאחר תיקון"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_attachment_count
msgid "Attachment Count"
msgstr "כמות קבצים מצורפים"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__invoice_method__b4repair
msgid "Before Repair"
msgstr "לפני תיקון"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_make_invoice
msgid "Cancel"
msgstr "בטל"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Cancel Repair"
msgstr "בטל תיקון"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_line__state__cancel
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__cancel
msgid "Cancelled"
msgstr "בוטל"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__product_uom_category_id
#: model:ir.model.fields,field_description:repair.field_repair_line__product_uom_category_id
#: model:ir.model.fields,field_description:repair.field_repair_order__product_uom_category_id
msgid "Category"
msgstr "קטגוריה"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__partner_id
msgid ""
"Choose partner for whom the order will be invoiced and delivered. You can "
"find a partner by its Name, TIN, Email or Internal Reference."
msgstr ""
"בחר לקוח/ספק שעבורו תחויב ההזמנה ותמסר. אתה יכול למצוא לקוח/ספק לפי שם, "
"דוא\"ל או מזהה פנימי."

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_tags__color
msgid "Color Index"
msgstr "אינדקס צבעים"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__company_id
#: model:ir.model.fields,field_description:repair.field_repair_line__company_id
#: model:ir.model.fields,field_description:repair.field_repair_order__company_id
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Company"
msgstr "חברה"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_menu_config
msgid "Configuration"
msgstr "תצורה"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Confirm Repair"
msgstr "אשר תיקון"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_line__state__confirmed
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__confirmed
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Confirmed"
msgstr "מאושר"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_fee__product_uom_category_id
#: model:ir.model.fields,help:repair.field_repair_line__product_uom_category_id
#: model:ir.model.fields,help:repair.field_repair_order__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"המרה בין יחידות מידה יכולה להתרחש רק אם הן שייכות לאותה קטגוריה. ההמרה תיעשה"
" על בסיס היחס בניהן."

#. module: repair
#: code:addons/repair/models/repair.py:0 code:addons/repair/models/repair.py:0
#, python-format
msgid ""
"Couldn't find a pricelist line matching this product and quantity.\n"
"You have to change either the product, the quantity or the pricelist."
msgstr ""
"לא ניתן למצוא שורת מחירון המתאימה למוצר ולכמות זו.\n"
"אתה צריך לשנות את המוצר, את הכמות או את המחירון."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_make_invoice
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Create Invoice"
msgstr "צור חשבונית"

#. module: repair
#: model:ir.model,name:repair.model_repair_order_make_invoice
msgid "Create Mass Invoice (repair)"
msgstr "יצירת חשבוניות המונית (תיקון)"

#. module: repair
#: model_terms:ir.actions.act_window,help:repair.action_repair_order_tag
msgid "Create a new tag"
msgstr "צור תגית חדשה"

#. module: repair
#: model:ir.actions.act_window,name:repair.act_repair_invoice
#: model_terms:ir.ui.view,arch_db:repair.view_make_invoice
msgid "Create invoices"
msgstr "צור חשבוניות"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__create_uid
#: model:ir.model.fields,field_description:repair.field_repair_line__create_uid
#: model:ir.model.fields,field_description:repair.field_repair_order__create_uid
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__create_uid
#: model:ir.model.fields,field_description:repair.field_repair_tags__create_uid
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__create_date
#: model:ir.model.fields,field_description:repair.field_repair_line__create_date
#: model:ir.model.fields,field_description:repair.field_repair_order__create_date
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__create_date
#: model:ir.model.fields,field_description:repair.field_repair_tags__create_date
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__currency_id
#: model:ir.model.fields,field_description:repair.field_repair_line__currency_id
#: model:ir.model.fields,field_description:repair.field_repair_order__currency_id
msgid "Currency"
msgstr "מטבע"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__partner_id
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Customer"
msgstr "לקוח"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__default_address_id
msgid "Default Address"
msgstr "כתובת ברירת מחדל"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__address_id
msgid "Delivery Address"
msgstr "כתובת לאספקה"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__name
#: model:ir.model.fields,field_description:repair.field_repair_line__name
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Description"
msgstr "תיאור"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__location_dest_id
msgid "Dest. Location"
msgstr "מיקום יעד"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__display_name
#: model:ir.model.fields,field_description:repair.field_repair_line__display_name
#: model:ir.model.fields,field_description:repair.field_repair_order__display_name
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__display_name
#: model:ir.model.fields,field_description:repair.field_repair_tags__display_name
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_warn_insufficient_qty_repair_form_view
msgid "Do you confirm you want to repair"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_make_invoice
msgid "Do you really want to create the invoice(s)?"
msgstr "בטוח שברצונך ליצור חשבוניות?"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_line__state__done
msgid "Done"
msgstr "בוצע"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_line__state__draft
msgid "Draft"
msgstr "טיוטה"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid ""
"Draft invoices for this order will be cancelled. Do you confirm the action?"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "End Repair"
msgstr "סיים תיקון"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_line__tracking
#: model:ir.model.fields,help:repair.field_repair_order__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "וודא מעקב אחר מוצר מנוהל מלאי במחסן שלך."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Extra Info"
msgstr "מידע נוסף"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Fees"
msgstr "עמלות"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_follower_ids
msgid "Followers"
msgstr "עוקבים"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_partner_ids
msgid "Followers (Partners)"
msgstr "עוקבים (לקוחות/ספקים)"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "פונט מדהים למשל עבור משימות fa-tasks"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Future Activities"
msgstr "פעילויות עתידיות"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Group By"
msgstr "קבץ לפי"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__group
msgid "Group by partner invoice address"
msgstr "קבץ לפי כתובת חשבונית של לקוח/ספק"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__has_message
msgid "Has Message"
msgstr "יש הודעה"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "History"
msgstr "היסטוריה"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__id
#: model:ir.model.fields,field_description:repair.field_repair_line__id
#: model:ir.model.fields,field_description:repair.field_repair_order__id
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__id
#: model:ir.model.fields,field_description:repair.field_repair_tags__id
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__id
msgid "ID"
msgstr "תעודה מזהה"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_exception_icon
msgid "Icon"
msgstr "סמל"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "סמל לציון פעילות חריגה."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_needaction
#: model:ir.model.fields,help:repair.field_repair_order__message_unread
msgid "If checked, new messages require your attention."
msgstr "אם מסומן, הודעות חדשות דורשות את תשומת לבך."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_has_error
#: model:ir.model.fields,help:repair.field_repair_order__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "אם מסומן, בחלק מההודעות קיימת שגיאת משלוח."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__priority
msgid "Important repair order"
msgstr ""

#. module: repair
#: model_terms:ir.actions.act_window,help:repair.action_repair_order_tree
msgid ""
"In a repair order, you can detail the components you remove,\n"
"                add or replace and record the time you spent on the different\n"
"                operations."
msgstr ""
"בהזמנת תיקון, תוכל לפרט את הרכיבים שאתה מסיר,\n"
"               מוסיף או מחליף ולתעד את זמן העבודה עבור הפעולות\n"
"                השונות."

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__internal_notes
msgid "Internal Notes"
msgstr "הערות פנימיות"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__move_id
msgid "Inventory Move"
msgstr "תנועת מלאי"

#. module: repair
#: model:ir.actions.act_window,name:repair.action_repair_move_lines
msgid "Inventory Moves"
msgstr "תנועות מלאי"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__invoice_id
msgid "Invoice"
msgstr "חשבונית מס"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__invoice_line_id
#: model:ir.model.fields,field_description:repair.field_repair_line__invoice_line_id
msgid "Invoice Line"
msgstr "שורת חשבונית"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__invoice_method
msgid "Invoice Method"
msgstr "שיטת חיוב"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__invoice_state
msgid "Invoice State"
msgstr "מצב חשבונית"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Invoice address:"
msgstr "כתובת חשבונית:"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Invoice and shipping address:"
msgstr "כתובת חשבונית ואספקה:"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Invoice created"
msgstr "נוצרה חשבונית"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__invoiced
#: model:ir.model.fields,field_description:repair.field_repair_line__invoiced
#: model:ir.model.fields,field_description:repair.field_repair_order__invoiced
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Invoiced"
msgstr "חויב"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__partner_invoice_id
msgid "Invoicing Address"
msgstr "כתובת חשבונית"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_is_follower
msgid "Is Follower"
msgstr "עוקב"

#. module: repair
#: model:ir.model,name:repair.model_account_move
msgid "Journal Entry"
msgstr "פקודת יומן"

#. module: repair
#: model:ir.model,name:repair.model_account_move_line
msgid "Journal Item"
msgstr "פקודת יומן"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee____last_update
#: model:ir.model.fields,field_description:repair.field_repair_line____last_update
#: model:ir.model.fields,field_description:repair.field_repair_order____last_update
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice____last_update
#: model:ir.model.fields,field_description:repair.field_repair_tags____last_update
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair____last_update
msgid "Last Modified on"
msgstr "שינוי אחרון ב"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__write_uid
#: model:ir.model.fields,field_description:repair.field_repair_line__write_uid
#: model:ir.model.fields,field_description:repair.field_repair_order__write_uid
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__write_uid
#: model:ir.model.fields,field_description:repair.field_repair_tags__write_uid
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__write_date
#: model:ir.model.fields,field_description:repair.field_repair_line__write_date
#: model:ir.model.fields,field_description:repair.field_repair_order__write_date
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__write_date
#: model:ir.model.fields,field_description:repair.field_repair_tags__write_date
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Late Activities"
msgstr "פעילויות באיחור"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__location_id
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__location_id
msgid "Location"
msgstr "מיקום"

#. module: repair
#: model:ir.model,name:repair.model_stock_production_lot
#: model:ir.model.fields,field_description:repair.field_repair_line__lot_id
#: model:ir.model.fields,field_description:repair.field_repair_order__lot_id
msgid "Lot/Serial"
msgstr "מספר סידורי/אצווה"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_main_attachment_id
msgid "Main Attachment"
msgstr "קובץ ראשי מצורף "

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_has_error
msgid "Message Delivery error"
msgstr "הודעת שגיאת שליחה"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_ids
msgid "Messages"
msgstr "הודעות"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__move_id
msgid "Move"
msgstr "תנועה"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__move_id
msgid "Move created by the repair order"
msgstr "תנועה נוצרה ע\"י הזמנת התיקון"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "מועד אחרון לפעילות שלי"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "הפעילות הבאה ביומן"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "מועד אחרון לפעילות הבאה"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_summary
msgid "Next Activity Summary"
msgstr "תיאור הפעילות הבאה "

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_type_id
msgid "Next Activity Type"
msgstr "סוג הפעילות הבאה"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__invoice_method__none
msgid "No Invoice"
msgstr "ללא חיוב"

#. module: repair
#: code:addons/repair/models/repair.py:0 code:addons/repair/models/repair.py:0
#, python-format
msgid "No account defined for product \"%s\"."
msgstr "לא הוגדר חשבון עבור מוצר \"%s\"."

#. module: repair
#: code:addons/repair/models/repair.py:0 code:addons/repair/models/repair.py:0
#, python-format
msgid "No pricelist found."
msgstr "לא נמצא מחירון"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "No product defined on fees."
msgstr "אין מוצר מוגדר בעמלות."

#. module: repair
#: model_terms:ir.actions.act_window,help:repair.action_repair_order_tree
msgid "No repair order found. Let's create one!"
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:0 code:addons/repair/models/repair.py:0
#, python-format
msgid "No valid pricelist line found."
msgstr "לא נמצאה שורת מחירון תקפה."

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__priority__0
msgid "Normal"
msgstr "נורמלי "

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_needaction_counter
msgid "Number of Actions"
msgstr "מספר פעולות"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_has_error_counter
msgid "Number of errors"
msgstr "מספר השגיאות"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "מספר הודעות המחייבות פעולה"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "מספר הודעות עם שגיאת משלוח"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_unread_counter
msgid "Number of unread messages"
msgstr "מספר ההודעות שלא נקראו"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Only draft repairs can be confirmed."
msgstr "ניתן לאשר רק תיקוני טיוטה."

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__fees_lines
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Operations"
msgstr "פעולות"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__operations
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Parts"
msgstr "חלקים"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Please define an accounting sales journal for the company %s (%s)."
msgstr "אנא הגדר יומן מכירות חשבונאי לחברה %s (%s)."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Price"
msgstr "מחיר"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__pricelist_id
msgid "Pricelist"
msgstr "מחירון"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__pricelist_id
msgid "Pricelist of the selected partner."
msgstr "מחירון הלקוח/ספק שנבחר."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Print Quotation"
msgstr "הדפס הצעת מחיר"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__priority
msgid "Priority"
msgstr "קְדִימוּת"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__product_id
#: model:ir.model.fields,field_description:repair.field_repair_line__product_id
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__product_id
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Product"
msgstr "מוצר"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Product Moves"
msgstr "תנועות מוצר"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_qty
msgid "Product Quantity"
msgstr "כמות מוצרים"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__tracking
#: model:ir.model.fields,field_description:repair.field_repair_order__tracking
msgid "Product Tracking"
msgstr "מעקב אחר מוצר"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__product_uom
#: model:ir.model.fields,field_description:repair.field_repair_line__product_uom
#: model:ir.model.fields,field_description:repair.field_repair_order__product_uom
msgid "Product Unit of Measure"
msgstr "יחידת מידה של מוצר"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_id
msgid "Product to Repair"
msgstr "מוצר לתיקון"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__lot_id
msgid "Products repaired are all belonging to this lot"
msgstr "מוצרים שתוקנו שייכים לאצווה זו"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__quant_ids
msgid "Quant"
msgstr "מנה"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__product_uom_qty
#: model:ir.model.fields,field_description:repair.field_repair_line__product_uom_qty
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__quantity
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_tree
msgid "Quantity"
msgstr "כמות"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__draft
msgid "Quotation"
msgstr "הצעת מחיר"

#. module: repair
#: model:ir.actions.report,name:repair.action_report_repair_order
msgid "Quotation / Order"
msgstr "הצעת מחיר/הזמנה"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__quotation_notes
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Quotation Notes"
msgstr "הערות הצעת מחיר"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Quotations"
msgstr "הצעות מחיר"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Ready To Repair"
msgstr "מוכן לתיקון"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__ready
msgid "Ready to Repair"
msgstr "מוכן לתיקון"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_line__type__remove
msgid "Remove"
msgstr "הסר"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_account_bank_statement_line__repair_ids
#: model:ir.model.fields,field_description:repair.field_account_move__repair_ids
#: model:ir.model.fields,field_description:repair.field_account_payment__repair_ids
#: model:ir.model.fields,field_description:repair.field_stock_move__repair_id
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__repair_id
msgid "Repair"
msgstr "תיקון "

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__description
msgid "Repair Description"
msgstr "תיאור התיקון"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_account_move_line__repair_fee_ids
msgid "Repair Fee"
msgstr "עמלת תיקון"

#. module: repair
#: model:ir.model,name:repair.model_repair_fee
msgid "Repair Fees"
msgstr "עמלות תיקון"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_account_move_line__repair_line_ids
msgid "Repair Line"
msgstr "שורת תיקון"

#. module: repair
#: model:ir.model,name:repair.model_repair_line
msgid "Repair Line (parts)"
msgstr "שורת תיקון (חלקים)"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Repair Notes"
msgstr ""

#. module: repair
#: model:ir.model,name:repair.model_repair_order
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Repair Order"
msgstr "הזמנת תיקון"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Repair Order #:"
msgstr "הזמנת תיקון מס':"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__repair_id
#: model:ir.model.fields,field_description:repair.field_repair_line__repair_id
msgid "Repair Order Reference"
msgstr "מזהה הזמנת תיקון"

#. module: repair
#: model:ir.actions.act_window,name:repair.action_repair_order_graph
#: model:ir.actions.act_window,name:repair.action_repair_order_tree
#: model:ir.model.fields,field_description:repair.field_stock_production_lot__repair_order_ids
#: model_terms:ir.ui.view,arch_db:repair.stock_production_lot_view_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_graph
#: model_terms:ir.ui.view,arch_db:repair.view_repair_pivot
msgid "Repair Orders"
msgstr "הזמנות תיקונים"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_menu_tag
msgid "Repair Orders Tags"
msgstr "תגיות הזמנות תיקונים"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Repair Quotation #:"
msgstr "הצעת מחיר לתיקון מס':"

#. module: repair
#: model:mail.template,name:repair.mail_template_repair_quotation
msgid "Repair Quotation: Send by email"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__name
msgid "Repair Reference"
msgstr "מזהה תיקון"

#. module: repair
#: model:product.product,name:repair.product_service_order_repair
#: model:product.template,name:repair.product_service_order_repair_product_template
msgid "Repair Services"
msgstr "שירותי תיקון"

#. module: repair
#: model:ir.model,name:repair.model_repair_tags
#: model_terms:ir.ui.view,arch_db:repair.view_repair_tag_form
msgid "Repair Tags"
msgstr "תגיות תיקון"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Repair must be canceled in order to reset it to draft."
msgstr "יש לבטל את התיקון כדי להחזיר אותו למצב טיוטה."

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Repair must be confirmed before starting reparation."
msgstr "יש לאשר את התיקון לפני תחילתו."

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Repair must be repaired in order to make the product moves."
msgstr "התיקון חייב להתבצע כדי לבצע את תנועות מוצר."

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Repair must be under repair in order to end reparation."
msgstr "תיקון חייב להיות בטיפול כדי לסיים אותו."

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_production_lot__repair_order_count
msgid "Repair order count"
msgstr ""

#. module: repair
#: code:addons/repair/models/stock_production_lot.py:0
#, python-format
msgid "Repair orders of %s"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__repaired
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__done
msgid "Repaired"
msgstr "תוקן"

#. module: repair
#: model:ir.ui.menu,name:repair.menu_repair_order
#: model:ir.ui.menu,name:repair.repair_menu
msgid "Repairs"
msgstr "תיקונים"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_tree
msgid "Repairs order"
msgstr "הזמנת תיקון"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_menu_reporting
msgid "Reporting"
msgstr "דו\"חות"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__user_id
msgid "Responsible"
msgstr "אחראי"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_user_id
msgid "Responsible User"
msgstr "משתמש אחראי"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_has_sms_error
msgid "SMS Delivery error"
msgstr "שגיאה בשליחת SMS"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__sale_order_id
msgid "Sale Order"
msgstr "הזמנת לקוח"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__sale_order_id
msgid "Sale Order from which the product to be repaired comes from."
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__schedule_date
msgid "Scheduled Date"
msgstr "תאריך מתוזמן"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Search Repair Orders"
msgstr "חפש הזמנות תיקונים"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__invoice_method
msgid ""
"Selecting 'Before Repair' or 'After Repair' will allow you to generate "
"invoice before or after the repair is done respectively. 'No invoice' means "
"you don't want to generate invoice for this repair order."
msgstr ""
"בחירה 'לפני תיקון' או 'לאחר תיקון' תאפשר לך ליצור חשבונית לפני או לאחר ביצוע"
" התיקון בהתאמה. 'אין חשבונית' פירושו שאינך רוצה להפיק חשבונית עבור הזמנת "
"תיקון זו."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Send Quotation"
msgstr "שלח הצעת מחיר"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Serial number is required for operation lines with products: %s"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Set to Draft"
msgstr "הגדר כטיוטה"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Show all records which has next action date is before today"
msgstr "הצג את כל הרשומות שתאריך הפעולה הבא שלהן הוא עד היום"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__location_id
msgid "Source Location"
msgstr "מיקום מוצא"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Start Repair"
msgstr "התחל תיקון"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__state
#: model:ir.model.fields,field_description:repair.field_repair_order__state
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Status"
msgstr "סטטוס"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"סטטוס על בסיס פעילויות\n"
"איחור: תאריך היעד כבר חלף\n"
"היום: תאריך הפעילות הוא היום\n"
"מתוכנן: פעילויות עתידיות."

#. module: repair
#: model:ir.model,name:repair.model_stock_move
msgid "Stock Move"
msgstr "תנועת מלאי"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__price_subtotal
#: model:ir.model.fields,field_description:repair.field_repair_line__price_subtotal
msgid "Subtotal"
msgstr "סיכום ביניים"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_tags__name
msgid "Tag Name"
msgstr "שם תגית"

#. module: repair
#: model:ir.model.constraint,message:repair.constraint_repair_tags_name_uniq
msgid "Tag name already exists!"
msgstr "שם התגית כבר קיים!"

#. module: repair
#: model:ir.actions.act_window,name:repair.action_repair_order_tag
#: model:ir.model.fields,field_description:repair.field_repair_order__tag_ids
#: model_terms:ir.ui.view,arch_db:repair.view_repair_tag_search
#: model_terms:ir.ui.view,arch_db:repair.view_repair_tag_tree
msgid "Tags"
msgstr "תגיות"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Tax"
msgstr "מס"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__tax_id
#: model:ir.model.fields,field_description:repair.field_repair_line__tax_id
#: model:ir.model.fields,field_description:repair.field_repair_order__amount_tax
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Taxes"
msgstr "מיסים"

#. module: repair
#: model:ir.model.constraint,message:repair.constraint_repair_order_name
msgid "The name of the Repair Order must be unique!"
msgstr "שם הזמנת התיקון חייב להיות ייחודי!"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid ""
"The product unit of measure you chose has a different category than the "
"product unit of measure."
msgstr ""
"יחידת המידה של המוצר שבחרת כוללת קטגוריה שונה מזו של יחידת המידה של המוצר."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_line__state
msgid ""
"The status of a repair line is set automatically to the one of the linked "
"repair order."
msgstr "הסטטוס של שורת תיקון מוגדר אוטומטית להזמנת התיקון המקושרת."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__location_id
msgid "This is the location where the product to repair is located."
msgstr "זה המיקום בו נמצא המוצר לתיקון."

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__2binvoiced
msgid "To be Invoiced"
msgstr "לחיוב"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Today Activities"
msgstr "פעילויות היום"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__price_total
#: model:ir.model.fields,field_description:repair.field_repair_line__price_total
#: model:ir.model.fields,field_description:repair.field_repair_order__amount_total
msgid "Total"
msgstr "סה\"כ"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Total amount"
msgstr "סה\"כ"

#. module: repair
#: model:ir.model,name:repair.model_stock_traceability_report
msgid "Traceability Report"
msgstr "דוח מעקב"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__type
msgid "Type"
msgstr "סוג"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "סוג הפעילות החריגה ברשומה."

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__under_repair
msgid "Under Repair"
msgstr "בתיקון"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__price_unit
#: model:ir.model.fields,field_description:repair.field_repair_line__price_unit
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Unit Price"
msgstr "מחיר יחידה"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__product_uom_name
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_tree
msgid "Unit of Measure"
msgstr "יחידת מידה"

#. module: repair
#: model:product.product,uom_name:repair.product_service_order_repair
#: model:product.template,uom_name:repair.product_service_order_repair_product_template
msgid "Units"
msgstr "יחידה"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_unread
msgid "Unread Messages"
msgstr "הודעות שלא נקראו"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_unread_counter
msgid "Unread Messages Counter"
msgstr "מספר ההודעות שלא נקראו"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__amount_untaxed
msgid "Untaxed Amount"
msgstr "סכום ללא מע\"מ"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Untaxed amount"
msgstr "סכום ללא מע\"מ"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "UoM"
msgstr "יחידת מידה"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__priority__1
msgid "Urgent"
msgstr "דחוף"

#. module: repair
#: model:ir.model,name:repair.model_stock_warn_insufficient_qty_repair
msgid "Warn Insufficient Repair Quantity"
msgstr "הזהר כי כמות התיקון אינה מספיקה"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Warning"
msgstr "אזהרה"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__guarantee_limit
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Warranty Expiration"
msgstr "תוקף אחריות"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__website_message_ids
msgid "Website Messages"
msgstr "הודעות מאתר האינטרנט"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__website_message_ids
msgid "Website communication history"
msgstr "היסטורית התקשרויות מאתר האינטרנט"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid ""
"You can not delete a repair order once it has been confirmed. You must first"
" cancel it."
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid ""
"You can not delete a repair order which is linked to an invoice which has "
"been posted once."
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "You can not enter negative quantities."
msgstr "לא ניתן להזין כמויות שליליות."

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "You cannot cancel a completed repair order."
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "You cannot delete a completed repair order."
msgstr "אינך יכול למחוק תיקון שבוצע."

#. module: repair
#: code:addons/repair/models/repair.py:0 code:addons/repair/models/repair.py:0
#, python-format
msgid ""
"You have to select a pricelist in the Repair form !\n"
" Please set one before choosing a product."
msgstr ""
"עליך לבחור מחירון בטופס תיקון !\n"
"אנא הגדר אחד לפני שתבחר במוצר."

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "You have to select an invoice address in the repair form."
msgstr "עליך לבחור כתובת חשבונית בטופס התיקון."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_warn_insufficient_qty_repair_form_view
msgid "from location"
msgstr "מהמיקום "

#. module: repair
#: model:mail.template,report_name:repair.mail_template_repair_quotation
msgid "{{ (object.name or '').replace('/','_') }}"
msgstr ""

#. module: repair
#: model:mail.template,subject:repair.mail_template_repair_quotation
msgid ""
"{{ object.partner_id.name }} Repair Orders (Ref {{ object.name or 'n/a' }})"
msgstr ""
