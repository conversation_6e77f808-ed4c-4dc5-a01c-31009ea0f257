# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* note
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON> <robert.fry<PERSON><PERSON>@linserv.se>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# 3eec91a23d05c632ffac786ac42b81b8_b6fff7b <8985b7bc57db860af29969457dbb51b3_1018915>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:49+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: Mikael Åkerberg <<EMAIL>>, 2022\n"
"Language-Team: Swedish (https://app.transifex.com/odoo/teams/41243/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_kanban
msgid "<i class=\"fa fa-check\" role=\"img\" aria-label=\"Opened\" title=\"Opened\"/>"
msgstr "<i class=\"fa fa-check\" role=\"img\" aria-label=\"Opened\" title=\"Opened\"/>"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_kanban
msgid "<i class=\"fa fa-undo\" role=\"img\" aria-label=\"Closed\" title=\"Closed\"/>"
msgstr "<i class=\"fa fa-undo\" role=\"img\" aria-label=\"Closed\" title=\"Closed\"/>"

#. module: note
#: model:ir.model.fields,field_description:note.field_mail_activity_type__category
msgid "Action"
msgstr "Åtgärd"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_needaction
msgid "Action Needed"
msgstr "Åtgärd krävs"

#. module: note
#: model:ir.model.fields,help:note.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"Åtgärder kan utlösa specifika beteende såsom att öppna kalendervy eller "
"automatiskt markeras som utfört när ett dokument laddas upp"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__open
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Active"
msgstr "Aktiv"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_ids
msgid "Activities"
msgstr "Aktiviteter"

#. module: note
#: model:ir.model,name:note.model_mail_activity
msgid "Activity"
msgstr "Aktivitet"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Dekoration för aktivitetsundantag"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_state
msgid "Activity State"
msgstr "Aktivitetstillstånd"

#. module: note
#: model:ir.model,name:note.model_mail_activity_type
msgid "Activity Type"
msgstr "Aktivitetstyp"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikon för aktivitetstyp"

#. module: note
#: model_terms:ir.actions.act_window,help:note.action_note_note
msgid "Add a new personal note"
msgstr "Lägg till en ny personlig notering"

#. module: note
#: model_terms:ir.actions.act_window,help:note.note_tag_action
msgid "Add a new tag"
msgstr "Lätt till ny etikett"

#. module: note
#. openerp-web
#: code:addons/note/static/src/xml/systray.xml:0
#, python-format
msgid "Add a note"
msgstr "Lägg till notering"

#. module: note
#. openerp-web
#: code:addons/note/static/src/xml/systray.xml:0
#, python-format
msgid "Add new note"
msgstr "Lägg till ny anteckning"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Archive"
msgstr "Arkiv"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_attachment_count
msgid "Attachment Count"
msgstr "Antal Bilagor"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "By sticky note Category"
msgstr "Per Klisterlappskatergori"

#. module: note
#. openerp-web
#: code:addons/note/static/src/xml/systray.xml:0
#, python-format
msgid "Channel"
msgstr "Säljkanal"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__color
#: model:ir.model.fields,field_description:note.field_note_tag__color
msgid "Color Index"
msgstr "Färgindex"

#. module: note
#: model:ir.ui.menu,name:note.menu_note_configuration
msgid "Configuration"
msgstr "Konfiguration"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__create_uid
#: model:ir.model.fields,field_description:note.field_note_stage__create_uid
#: model:ir.model.fields,field_description:note.field_note_tag__create_uid
msgid "Created by"
msgstr "Skapad av"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__create_date
#: model:ir.model.fields,field_description:note.field_note_stage__create_date
#: model:ir.model.fields,field_description:note.field_note_tag__create_date
msgid "Created on"
msgstr "Skapad den"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__date_done
msgid "Date done"
msgstr "Date done"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_kanban
msgid "Delete"
msgstr "Ta bort"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__display_name
#: model:ir.model.fields,field_description:note.field_note_stage__display_name
#: model:ir.model.fields,field_description:note.field_note_tag__display_name
msgid "Display Name"
msgstr "Visningsnamn"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_kanban
msgid "Dropdown menu"
msgstr "Rullgardinsmeny"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_stage__fold
msgid "Folded by Default"
msgstr "Nedvikt som standard"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_kanban
msgid "Follower"
msgstr "Följare"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_follower_ids
msgid "Followers"
msgstr "Följare"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_partner_ids
msgid "Followers (Partners)"
msgstr "Följare (kontakter)"

#. module: note
#: model:ir.model.fields,help:note.field_note_note__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesomeikon t.ex fa-tasks"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Future Activities"
msgstr "Framtida aktiviteter"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Group By"
msgstr "Gruppera efter"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__has_message
msgid "Has Message"
msgstr "Har Meddelande"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__id
#: model:ir.model.fields,field_description:note.field_note_stage__id
#: model:ir.model.fields,field_description:note.field_note_tag__id
msgid "ID"
msgstr "ID"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_exception_icon
msgid "Icon"
msgstr "Ikon"

#. module: note
#: model:ir.model.fields,help:note.field_note_note__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikon för att indikera en undantagsaktivitet."

#. module: note
#: model:ir.model.fields,help:note.field_note_note__message_needaction
#: model:ir.model.fields,help:note.field_note_note__message_unread
msgid "If checked, new messages require your attention."
msgstr ""
"Om den är markerad så finns det meddelanden som kräver din uppmärksamhet."

#. module: note
#: model:ir.model.fields,help:note.field_note_note__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Om markerad så har det blivit ett leveransfel för några meddelanden."

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_is_follower
msgid "Is Follower"
msgstr "Är följare"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note____last_update
#: model:ir.model.fields,field_description:note.field_note_stage____last_update
#: model:ir.model.fields,field_description:note.field_note_tag____last_update
msgid "Last Modified on"
msgstr "Senast redigerad"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__write_uid
#: model:ir.model.fields,field_description:note.field_note_stage__write_uid
#: model:ir.model.fields,field_description:note.field_note_tag__write_uid
msgid "Last Updated by"
msgstr "Senast uppdaterad av"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__write_date
#: model:ir.model.fields,field_description:note.field_note_stage__write_date
#: model:ir.model.fields,field_description:note.field_note_tag__write_date
msgid "Last Updated on"
msgstr "Senast uppdaterad"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Late Activities"
msgstr "Försenade aktiviteter"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_main_attachment_id
msgid "Main Attachment"
msgstr "Huvudbilaga"

#. module: note
#: model:note.stage,name:note.note_stage_01
msgid "Meeting Minutes"
msgstr "Mötesprotokoll"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_has_error
msgid "Message Delivery error"
msgstr "Fel vid leverans av meddelande"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_ids
msgid "Messages"
msgstr "Meddelanden"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Min Aktivitets Deadline"

#. module: note
#: model:note.stage,name:note.note_stage_00
msgid "New"
msgstr "Ny"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Nästa Aktivitets Kalender Händelse"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Nästa slutdatum för aktivitet"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_summary
msgid "Next Activity Summary"
msgstr "Nästa aktivitetssummering"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_type_id
msgid "Next Activity Type"
msgstr "Nästa aktivitetstyp"

#. module: note
#: model:ir.model,name:note.model_note_note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
#: model_terms:ir.ui.view,arch_db:note.view_note_note_form
msgid "Note"
msgstr "Anteckning"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__memo
msgid "Note Content"
msgstr "Anteckningsinnehåll"

#. module: note
#: model:ir.model,name:note.model_note_stage
msgid "Note Stage"
msgstr "Anteckningsetapp"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__name
msgid "Note Summary"
msgstr "Anteckningssammandrag"

#. module: note
#: model:ir.model,name:note.model_note_tag
msgid "Note Tag"
msgstr "Anteckningsetikett"

#. module: note
#: code:addons/note/models/res_users.py:0
#: model:ir.actions.act_window,name:note.action_note_note
#: model:ir.ui.menu,name:note.menu_note_notes
#: model:note.stage,name:note.note_stage_02
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
#, python-format
msgid "Notes"
msgstr "Anteckningar"

#. module: note
#: model_terms:ir.actions.act_window,help:note.action_note_note
msgid ""
"Notes are private, unless you share them by inviting follower on a note.\n"
"            (Useful for meeting minutes)."
msgstr ""
"Anteckningar är privata såvida du inte delar dem genom att bjuda in följare till en anteckning.\n"
"            (Användbart för mötesprotokoll)."

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_needaction_counter
msgid "Number of Actions"
msgstr "Antal åtgärder"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_has_error_counter
msgid "Number of errors"
msgstr "Antal fel"

#. module: note
#: model:ir.model.fields,help:note.field_note_note__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Antal meddelanden som kräver en åtgärd"

#. module: note
#: model:ir.model.fields,help:note.field_note_note__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Antal meddelanden med leveransfel"

#. module: note
#: model:ir.model.fields,help:note.field_note_note__message_unread_counter
msgid "Number of unread messages"
msgstr "Antal olästa meddelanden"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__user_id
#: model:ir.model.fields,field_description:note.field_note_stage__user_id
msgid "Owner"
msgstr "Ägare"

#. module: note
#: model:ir.model.fields,help:note.field_note_stage__user_id
msgid "Owner of the note stage"
msgstr "Ägare av anteckningsetappen"

#. module: note
#: model:ir.model.fields,field_description:note.field_mail_activity__note_id
msgid "Related Note"
msgstr "Relaterad anteckning"

#. module: note
#. openerp-web
#: code:addons/note/static/src/xml/systray.xml:0
#, python-format
msgid "Remember..."
msgstr "Kom ihåg…"

#. module: note
#: model:ir.model.fields.selection,name:note.selection__mail_activity_type__category__reminder
#: model:mail.activity.type,name:note.mail_activity_data_reminder
msgid "Reminder"
msgstr "Påminnelse"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__activity_user_id
msgid "Responsible User"
msgstr "Ansvarig användare"

#. module: note
#. openerp-web
#: code:addons/note/static/src/xml/systray.xml:0
#, python-format
msgid "SAVE"
msgstr "SPARA"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__sequence
#: model:ir.model.fields,field_description:note.field_note_stage__sequence
msgid "Sequence"
msgstr "Sekvens"

#. module: note
#. openerp-web
#: code:addons/note/static/src/xml/systray.xml:0
#: code:addons/note/static/src/xml/systray.xml:0
#, python-format
msgid "Set date and time"
msgstr "Ange datum och tid"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Show all records which has next action date is before today"
msgstr "Visa alla poster som har nästa händelse före idag"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__stage_id
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Stage"
msgstr "Etapp"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_stage__name
msgid "Stage Name"
msgstr "Etappnamn"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_stage_form
msgid "Stage of Notes"
msgstr "Anteckningsetapp"

#. module: note
#: model:ir.actions.act_window,name:note.action_note_stage
#: model:ir.ui.menu,name:note.menu_notes_stage
#: model_terms:ir.ui.view,arch_db:note.view_note_note_tree
msgid "Stages"
msgstr "Etapper"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_stage_tree
msgid "Stages of Notes"
msgstr "Anteckningsetapper"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__stage_ids
msgid "Stages of Users"
msgstr "Användares etapper"

#. module: note
#: model:ir.model.fields,help:note.field_note_note__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status baserad på aktiviteter\n"
"Försenade: Leveranstidpunkten har passerat\n"
"Idag: Aktivitetsdatum är idag\n"
"Kommande: Framtida aktiviteter."

#. module: note
#: model:ir.model.fields,field_description:note.field_note_tag__name
msgid "Tag Name"
msgstr "Etikettnamn"

#. module: note
#: model:ir.model.constraint,message:note.constraint_note_tag_name_uniq
msgid "Tag name already exists !"
msgstr "Etikettnamn existerar redan !"

#. module: note
#: model:ir.actions.act_window,name:note.note_tag_action
#: model:ir.model.fields,field_description:note.field_note_note__tag_ids
#: model:ir.ui.menu,name:note.notes_tag_menu
#: model_terms:ir.ui.view,arch_db:note.note_tag_view_form
#: model_terms:ir.ui.view,arch_db:note.note_tag_view_tree
#: model_terms:ir.ui.view,arch_db:note.view_note_note_form
msgid "Tags"
msgstr "Etiketter"

#. module: note
#. openerp-web
#: code:addons/note/static/src/js/systray_activity_menu.js:0
#, python-format
msgid "Today"
msgstr "Idag"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Today Activities"
msgstr "Dagens aktiviteter"

#. module: note
#: model:note.stage,name:note.note_stage_03
msgid "Todo"
msgstr "Att göra"

#. module: note
#: model:ir.model.fields,help:note.field_note_note__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Typ av undantagsaktivitet i posten."

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_unread
msgid "Unread Messages"
msgstr "Olästa meddelanden"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Räknare olästa meddelanden"

#. module: note
#: model:ir.model.fields,help:note.field_note_stage__sequence
msgid "Used to order the note stages"
msgstr "Används för att ordna noteringsetapper"

#. module: note
#: model:ir.model,name:note.model_res_users
msgid "Users"
msgstr "Användare"
