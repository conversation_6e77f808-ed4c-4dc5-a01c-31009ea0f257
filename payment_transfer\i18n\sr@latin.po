# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * payment_transfer
# 
# Translators:
# <PERSON><PERSON><PERSON> <nemanja<PERSON><PERSON><EMAIL>>, 2017
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: payment_transfer
#: code:addons/payment_transfer/models/payment.py:70
#, python-format
msgid "; multiple order found"
msgstr ""

#. module: payment_transfer
#: code:addons/payment_transfer/models/payment.py:68
#, python-format
msgid "; no order found"
msgstr ""

#. module: payment_transfer
#: code:addons/payment_transfer/models/payment.py:28
#, python-format
msgid ""
"<div>\n"
"<h3>Please use the following transfer details</h3>\n"
"<h4>%(bank_title)s</h4>\n"
"%(bank_accounts)s\n"
"<h4>Communication</h4>\n"
"<p>Please use the order name as communication reference.</p>\n"
"</div>"
msgstr ""

#. module: payment_transfer
#: code:addons/payment_transfer/models/payment.py:26
#, python-format
msgid "Bank Account"
msgstr "Bankovni račun"

#. module: payment_transfer
#: code:addons/payment_transfer/models/payment.py:26
#, python-format
msgid "Bank Accounts"
msgstr "Bankovni računi"

#. module: payment_transfer
#: model:ir.model,name:payment_transfer.model_payment_acquirer
msgid "Payment Acquirer"
msgstr ""

#. module: payment_transfer
#: model:ir.model,name:payment_transfer.model_payment_transaction
msgid "Payment Transaction"
msgstr ""

#. module: payment_transfer
#: code:addons/payment_transfer/models/payment.py:66
#, python-format
msgid "received data for reference %s"
msgstr ""
