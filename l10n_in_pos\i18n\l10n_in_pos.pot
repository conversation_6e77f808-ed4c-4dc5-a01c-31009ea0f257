# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_in_pos
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-06-12 07:24+0000\n"
"PO-Revision-Date: 2023-06-12 07:24+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_in_pos
#: code:addons/l10n_in_pos/models/pos_config.py:0
#, python-format
msgid "Go to Company configuration"
msgstr ""

#. module: l10n_in_pos
#. openerp-web
#: code:addons/l10n_in_pos/static/src/xml/pos_receipt.xml:0
#, python-format
msgid "HSN Code:"
msgstr ""

#. module: l10n_in_pos
#. openerp-web
#: code:addons/l10n_in_pos/static/src/xml/pos_receipt.xml:0
#, python-format
msgid "Phone:"
msgstr ""

#. module: l10n_in_pos
#: model:ir.model,name:l10n_in_pos.model_pos_config
msgid "Point of Sale Configuration"
msgstr ""

#. module: l10n_in_pos
#: model:ir.model,name:l10n_in_pos.model_pos_order
msgid "Point of Sale Orders"
msgstr ""

#. module: l10n_in_pos
#: code:addons/l10n_in_pos/models/pos_config.py:0
#, python-format
msgid ""
"Your company %s needs to have a correct address in order to open the session.\n"
"Set the address of your company (Don't forget the State field)"
msgstr ""
