<?xml version="1.0"?>
<odoo><data>
    <!-- Update Preferences form !-->
    <record id="res_users_view_form_preferences" model="ir.ui.view">
        <field name="name">res.users.view.form.preferences.mail_bot</field>
        <field name="model">res.users</field>
        <field name="inherit_id" ref="mail.view_users_form_simple_modif_mail"/>
        <field name="arch" type="xml">
            <data>
                <field name="notification_type" position="after">
                    <field name="odoobot_state" readonly="0" groups="base.group_no_one"/>
                </field>
            </data>
        </field>
    </record>

    <!-- Update user form !-->
    <record id="res_users_view_form" model="ir.ui.view">
        <field name="name">res.users.view.form.mail_bot</field>
        <field name="model">res.users</field>
        <field name="inherit_id" ref="mail.view_users_form_mail"/>
        <field name="arch" type="xml">
            <data>
                <field name="signature" position="before">
                    <field name="odoobot_state" readonly="0" groups="base.group_no_one"/>
                </field>
            </data>
        </field>
    </record>
</data></odoo>
