<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
    
        <!-- Fiscal Position Templates -->

        <record id="fiscal_position_template_1" model="account.fiscal.position.template">
            <field name="sequence">1</field>
            <field name="name">Obchody v CZ</field>
            <field name="chart_template_id" ref="cz_chart_template"/>
            <field name="auto_apply" eval="True"/>
            <field name="vat_required" eval="True"/>
            <field name="country_id" ref="base.cz"/>
        </record>

        <record id="fp_intra_private" model="account.fiscal.position.template">
            <field name="sequence">2</field>
            <field name="name">Obchody s EU konzument</field>
            <field name="chart_template_id" ref="cz_chart_template"/>
            <field name="auto_apply" eval="True"/>
            <field name="country_group_id" ref="base.europe"/>
        </record>
      
        <record id="fiscal_position_template_2" model="account.fiscal.position.template">
            <field name="sequence">3</field>
            <field name="name">Obchody s EU</field>
            <field name="chart_template_id" ref="cz_chart_template"/>
            <field name="auto_apply" eval="True"/>
            <field name="vat_required" eval="True"/>
            <field name="country_group_id" ref="base.europe"/>
        </record>

    <!-- Fiscal Position Tax Templates -->

        <!-- European Union -->
        <!-- Sales -->
        <record id="fiscal_position_tax_template_2a" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_2"  />
            <field name="tax_src_id" ref="vyc_tuz_21" />
            <field name="tax_dest_id" ref="vyc_dod_eu" />
        </record>

        <record id="fiscal_position_tax_template_2b" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_2"  />
            <field name="tax_src_id" ref="vyc_tuz_15" />
            <field name="tax_dest_id" ref="vyc_dod_eu" />
        </record>

        <record id="fiscal_position_tax_template_2c" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_2"  />
            <field name="tax_src_id" ref="vyc_tuz_0" />
            <field name="tax_dest_id" ref="vyc_dod_eu" />
        </record>

        <record id="fiscal_position_tax_template_2d" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_2"  />
            <field name="tax_src_id" ref="vyc_tuz_12" />
            <field name="tax_dest_id" ref="vyc_dod_eu" />
        </record>

        <!-- European Union -->
        <!-- Purchase -->

        <record id="fiscal_position_tax_template_21a" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_2"  />
            <field name="tax_src_id" ref="vsc_tuz_21" />
            <field name="tax_dest_id" ref="vsc_nad_eu" />
        </record>

        <record id="fiscal_position_tax_template_21b" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_2"  />
            <field name="tax_src_id" ref="vsc_tuz_15" />
            <field name="tax_dest_id" ref="vsc_nad_eu" />
        </record>

        <record id="fiscal_position_tax_template_21c" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_2"  />
            <field name="tax_src_id" ref="vsc_tuz_12" />
            <field name="tax_dest_id" ref="vsc_nad_eu" />
        </record>
        
    </data>
</odoo>
