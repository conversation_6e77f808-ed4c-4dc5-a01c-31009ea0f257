# -*- coding: utf-8 -*-
from odoo import tools, api, fields, models
from datetime import datetime, timedelta, date
from odoo.exceptions import ValidationError


class HrEmployeeReward(models.Model):
    _name = "hr.masarat.reward"
    _inherit = ['mail.thread', 'mail.activity.mixin']

    def _get_nested_employee(self,employee_list):
        old_len = len(employee_list)
        new_list = set()
        for id in employee_list:
            new_list.add(id)
            new_employee = self.env['hr.employee'].search([('parent_id','=',id)])
            for new_id in new_employee:
                new_list.add(new_id.id)
        if len(new_list) == old_len:
            return new_list
        else:
            return self._get_nested_employee(new_list)

    @api.model
    def _get_employee_id_domain(self):
        me = self.env['hr.employee'].search([('user_id','=',self.env.user.id)])
        my_list = list(self._get_nested_employee(set([me.id])))
        res = [('id','in',my_list)]
        return res

    name = fields.Char(compute='get_reward_name', store=True)

    state = fields.Selection(selection=[('draft', 'مسودة'),
                                        ('gm_approval', 'موافقة المدير العام'),
                                        ('gm_refuse', 'رفض'),
                                        ('fm_approval', 'موافقة مدير الشؤون المالية')], default='draft', string="الحالة")

    request_date = fields.Date(string="تاريخ الطلب", readonly=True, default=lambda self: fields.Date.to_string(date.today()))
    employee_id = fields.Many2one('hr.employee', required=True, string="الموظف", domain=lambda self: self._get_employee_id_domain())
    user_id = fields.Many2one('res.users', readonly=True, string="تم الطلب بواسطة", default=lambda self: self.env.user)

    department_id = fields.Many2one('hr.department',related='employee_id.department_id', string='القسم')
    reward_reason = fields.Selection([
        ('a','الموظف انجز العمل قبل الوقت المحدد'),
        ('b','القيام بإضافات مهمة ساهمت في زيادة جودة العمل'),
        ('c','إنجاز العمل بإتقان وحرفية'),
        ('d','أسباب أخرى'),
    ],string='سبب المكافأة', required=True)
    reward_reason_other = fields.Text(string="شرح الأسباب الأخرى")

    reward_amount = fields.Float('قيمة المكافأة', required=True)
    is_paid = fields.Boolean(string='تم الدفع', readonly=True)

    employee_assigned_task = fields.Text('الأعمال المكلف بها الموظف', required=True)
    non_employee_assigned_task = fields.Text('الأعمال الغير مكلف بها الموظف (المبادرات والإبداعات)', required=True)

    reject_reason = fields.Text('أسباب الرفض')

    paid_date = fields.Date('تاريخ الصرف')

    def set_gm_approval(self):
        self.state = 'gm_approval'
    def set_gm_refuse(self):
        self.state = 'gm_refuse'
    def set_fm_approval(self):
        self.state = 'fm_approval'

    @api.constrains('reward_amount')
    def reward_amount_constrain(self):
        for elem in self:
            if (elem.reward_amount > 1000.0) or (elem.reward_amount <= 0.0):
                raise ValidationError('يجب أن تكون قيمة المكافأة مابين 0 و 1000 دينار')

    def get_reward_name(self):
        for elem in self:
            if elem.employee_id:
                elem.name = 'مكافأة'+'-'+elem.employee_id.name


    def action_send_notification_to_maneger(self,recorde_id):
        gm =  self.env['res.users'].search([("groups_id", "=", self.env.ref("hr_approvales_masarat.group_gm_reward_masarat").id)], limit=1)
        if gm:
            email_to = gm.login
        else:
            email_to = False
        user_id = self._context.get('uid')
        email_from = self.env['hr.employee'].search([('user_id', '=', user_id)])
        web_base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        web_base_url += '/web#id=%d&view_type=form&model=%s' % (recorde_id, self._name)

        body = """
        <div dir="rtl">
        <p><font style="font-size: 14px;"> لديك طلب موافقة على مكافأة </font></p>
                  <p><font style="font-size: 14px;"> مقدم الطلب """+str(email_from.name)+"""</font></p>
                  <a href="%s">Request Link</a>
        </div>""" % (web_base_url)
        template_id = self.env['mail.mail'].create({
            'subject':'طلب مكافأة',
            'email_from':email_from.work_email,
            'email_to': email_to,
            'body_html':body
        })
        #### freaa
        template_id.send()

    @api.model
    def create(self,vals):
        res = super(HrEmployeeReward, self).create(vals)
        recode_id = res.id
        self.sudo().action_send_notification_to_maneger(recode_id)
        return res


class HrPayslipX(models.Model):
    _inherit = 'hr.payslip'

    masarat_reward = fields.Float(compute="get_masarat_reward")

    def get_masarat_reward(self):
        for elem in self:
            elem.masarat_reward = 0
            search_masarat_reward = self.sudo().env['hr.masarat.reward'].search([('is_paid','=',False),('employee_id','=',elem.employee_id.id),('state','in',('gm_approval','fm_approval'))])
            for line in search_masarat_reward:
                elem.masarat_reward+=line.reward_amount

    def action_payslip_done(self):
        if self.masarat_reward:
            search_masarat_reward = self.sudo().env['hr.masarat.reward'].search([('is_paid', '=', False), ('employee_id', '=', self.employee_id.id),('state', 'in', ('gm_approval', 'fm_approval'))])
            for line in search_masarat_reward:
                line.is_paid = True
        return super(HrPayslipX, self).action_payslip_done()

