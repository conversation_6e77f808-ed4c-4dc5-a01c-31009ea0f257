{% extends "layout.html" %}
{% from "loading.html" import loading_block_ui %}
{% block head %}
    <script>
        var _onQueryDone = function () {
            $('.message-status').html('Updated configuration <br> Refreshing page');
            setTimeout(function () {
                location.reload();
            }, 30000);
        }
        $(document).ready(function () {
            $('#list-credential').submit(function(e){
                e.preventDefault();
                $('.loading-block').removeClass('o_hide');
                $.ajax({
                    url: '/save_credential',
                    type: 'post',
                    data: $('#list-credential').serialize(),
                }).always(_onQueryDone);
            });
        $('#credential-clear').submit(function(e){
            e.preventDefault();
            $('.loading-block').removeClass('o_hide');
            $.ajax({
                url: '/clear_credential',
                type: 'get',
            }).always(_onQueryDone);
        });
        });
    </script>
{% endblock %}
{% block content %}
    <h2 class="text-center">List Credential</h2>
    <p>
        Set the DB UUID and your Contract Number you want to use.
    </p>
    <form id="list-credential" action='/save_credential' method='POST'>
        <table align="center">
            <tr>
                <td>DB uuid</td>
                <td><input type="text" name="db_uuid" value="{{ db_uuid }}"></td>
            </tr>
            <tr>
                <td>Contract Number</td>
                <td><input type="text" name="enterprise_code" value="{{ enterprise_code }}"></td>
            </tr>
            <tr>
                <td/>
                <td><input class="btn" type="submit" value="Save"/></td>
            </tr>
        </table>
        {{ loading_block_ui(loading_message) }}
    </form>
    {% if db_uuid or enterprise_code %}
        <p class="text-center font-small">
            Current DB uuid: <strong>{{ db_uuid }}</strong>
        </p>
        <p class="text-center font-small">
            Current Contract Number: <strong>{{ enterprise_code }}</strong>
        </p>
        <div class="text-center font-small" style="margin: 10px auto;">
            You can clear the credential configuration
            <form id="credential-clear" style="display: inline-block;margin-left: 4px;">
                <input class="btn btn-sm" type="submit" value="Clear"/>
            </form>
        </div>
    {% endif %}
{% endblock %}
