# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_sale
#
# Translators:
# <PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:06+0000\n"
"PO-Revision-Date: 2016-06-17 19:19+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Peru) (http://www.transifex.com/odoo/odoo-9/language/"
"es_PE/)\n"
"Language: es_PE\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "(extra fees apply)"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ", go to the 'Sales' tab"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ", go to the 'Variants' tab"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "-- Create a new address --"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid ""
"30-day money-back guarantee<br/>\n"
"                    Free Shipping in U.S.<br/>\n"
"                    Buy now, get in 2 days"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<br/>\n"
"                                <small>(don't forget to apply the changes)</"
"small>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<br/>\n"
"                                <small>(you'll have to subscribe directly on "
"each of the payment companies' websites)</small>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.report_shop_saleorder_document
msgid ""
"<br/>\n"
"                                <strong>Payment Status:</strong>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.order_state_message
msgid "<i class=\"fa fa-cog\"/> Configure Transfer Details"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<i class=\"fa fa-comment-o\"/> Website Live Chat on"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<i class=\"fa fa-envelope-o\"/> Email Our Website Expert"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<i class=\"fa fa-print\"/> Print"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.header
msgid ""
"<i class=\"fa fa-shopping-cart\"/>\n"
"              My cart"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "<span class=\"col-xs-6 text-right h4\">Total:</span>"
msgstr "<span class=\"col-xs-6 text-right h4\">Total:</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid ""
"<span class=\"col-xs-6 text-right text-muted\" title=\"Taxes may be updated "
"after providing shipping address\"> Taxes:</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "<span class=\"col-xs-6 text-right text-muted\">Subtotal:</span>"
msgstr "<span class=\"col-xs-6 text-right text-muted\">Subtotal:</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "<span class=\"fa fa-arrow-right\"/> Change Address"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "<span class=\"fa fa-arrow-right\"/> change"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<span class=\"fa fa-comment-o\"/> Website Live Chat on"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"fa fa-lightbulb-o fa-2x\"/>\n"
"                        At cost price is a good option for heavy or "
"oversized packages."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"fa fa-lightbulb-o fa-2x\"/>\n"
"                        Offering free delivery with a minimum amount or "
"minimum number of items should drive up your average order value and help to "
"compensate for the delivery costs."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"fa fa-lightbulb-o fa-2x\"/>\n"
"                        You can also create different rates based on order "
"amount ranges, for example 10€ up to a 50€ order, then 5€ after."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.continue_shopping
msgid ""
"<span class=\"fa fa-long-arrow-left\"/> <span class=\"hidden-xs\">Continue "
"Shopping</span><span class=\"visible-xs-inline\">Continue</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "<span class=\"fa fa-long-arrow-left\"/> Return to Cart"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"<span class=\"hidden-xs\">Process Checkout</span><span class=\"visible-xs-"
"inline\">Checkout</span> <span class=\"fa fa-long-arrow-right\"/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\">\n"
"                            <span class=\"fa fa-shopping-cart\"/>\n"
"                            <strong>2. On Add to Cart window:</strong> Show "
"accessories, services\n"
"                        </span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\">\n"
"                            <span class=\"fa fa-shopping-cart\"/>\n"
"                            <strong>3. On Check-out page:</strong> Show "
"optional products\n"
"                        </span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\">\n"
"                            <span class=\"fa fa-tag\"/>\n"
"                            <strong> 1. On Product pages:</strong> Show "
"suggested products\n"
"                        </span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\"><span class=\"fa fa-cc-paypal\"/><strong> "
"Paypal</strong> (Recommended for starters)</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\"><span class=\"fa fa-credit-card\"/><strong> "
"Ogone, Adyen, Authorize.net, Buckaroo...</strong></span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\"><span class=\"fa fa-lock\"/><strong>Wire "
"transfer</strong> (Slow and inefficient)</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\"><span class=\"fa fa-pencil-square-o\"/><strong> "
"Web-Services</strong><br/>scripts development</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\"><span class=\"fa fa-shopping-cart\"/><strong> At "
"cost price</strong> (customer pay what you pay)</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\"><span class=\"fa fa-sitemap\"/><strong> "
"Importation</strong><br/>by using a CSV file</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\"><span class=\"fa fa-smile-o\"/><strong> Free "
"delivery</strong> (risky, but has best potential)</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<span class=\"panel-title\"><span class=\"fa fa-table\"/><strong> Flat "
"rates</strong> (everybody pays the same)</span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"text-danger\">* </span>Field 2"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.suggested_products_list
msgid "<strong>Add to Cart</strong>"
msgstr "<strong>Agregar al Carrito</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Bonuses:</strong> what you get on top of the offer"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Call to action</strong> short and clear: (Add to Cart, Ask for "
"quote,...)"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Cons:</strong>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Cons:</strong> The delivery cost may be discouraging for your "
"cheapest items."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Cons:</strong> customers have to wait until checkout to find out the "
"delivery price."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Cons:</strong> will require you to either absorb the cost or "
"slightly increase your prices to cover it."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Contact us now:</strong><br/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Customers review:</strong> what do the customers think of the product"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Features and benefits:</strong> what the product does and why that "
"is good"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>High-quality picture</strong>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Key features, emotional and commercial content</strong><br/>\n"
"                            Recommended for at least for your top products, "
"because it can have a big impact on your sales and conversion rates."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Mandatory content</strong><br/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Name</strong> of your product"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Need help to import your products?</strong>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<strong>Order Details:</strong>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
#: model_terms:ir.ui.view,arch_db:website_sale.report_shop_saleorder_document
msgid "<strong>Payment Method:</strong>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<strong>Payment information:</strong>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Pictures gallery of the product:</strong> all angles, detailed view, "
"package,etc."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Price</strong> with currency"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Product variants</strong><br/>\n"
"                            Product variants are used to offer variations of "
"the same product to your customers on the products page.<br/>\n"
"                            For example, the customer choose a T-shirt and "
"then select its size or color."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Pros:</strong>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Pros:</strong> being transparent about your charges can win you the "
"trust of your customers."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Pros:</strong> gives you a significant advantage over any "
"competitors that don't offer the same perk."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Pros:</strong> simple for your customers to understand."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Reassurance arguments</strong><br/>\n"
"                            Anticipate your customers questions &amp; "
"worries on practical details like Shipping rates &amp; policies, Return "
"&amp; replacement policies, Payment methods &amp; security, General "
"Conditions, etc."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Recommended action:</strong>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Recommended actions:</strong>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>See it in action</strong><br/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Short description</strong> of the product or service"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Technical information:</strong> what do you get and how does it work?"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<strong>Total:</strong>"
msgstr "<strong>Total:</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"<strong>Value proposition:</strong> what’s the end-benefit of this product "
"and who is it for?"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "<strong>Variants</strong> of the product like size or color (see below)"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_accessory_product_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template_accessory_product_ids
msgid "Accessory Products"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Activate 'Suggested products' from the 'Customize' menu."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Activate the 'Support multiple variants per products' option in"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Activate the payment options you want to use"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Add as many variants as you need from 3 different types: radio buttons, drop-"
"down menu or color buttons."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "Add to Cart"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.option_collapse_products_categories
#: model_terms:ir.ui.view,arch_db:website_sale.products_categories
msgid "All Products"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website_pricelist_selectable
msgid "Allow the end user to choose this price list"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product_alternative_product_ids
#: model:ir.model.fields,help:website_sale.field_product_template_alternative_product_ids
msgid "Appear on the product page"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product_accessory_product_ids
#: model:ir.model.fields,help:website_sale.field_product_template_accessory_product_ids
msgid "Appear on the shopping cart"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.reduction_code
msgid "Apply"
msgstr "Aplicar"

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_benelux
msgid "Benelux Pricelist"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Bill To:"
msgstr "Facturar a:"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Billing Information"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "Billing<span class=\"chevron\"/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Can take up to several days for you to receive the money"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_cart_quantity
msgid "Cart Quantity"
msgstr ""

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_public_category_action
msgid ""
"Categories are used to browse your products through the\n"
"            touchscreen interface."
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:59
#, python-format
msgid "Change the price"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_child_id
msgid "Children Categories"
msgstr "Categorías Hijo"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:72
#, python-format
msgid "Choose an image"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:73
#, python-format
msgid "Choose an image from the library."
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:39
#, python-format
msgid "Choose name"
msgstr ""

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_christmas
msgid "Christmas Pricelist"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "City"
msgstr "Provincia"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:25
#, python-format
msgid "Click here to add a new product."
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:67
#, python-format
msgid "Click here to set an image describing your product."
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:47
#, python-format
msgid "Click on <em>Continue</em> to create the product."
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:106
#, python-format
msgid "Click on <em>Publish</em> your product so your customers can see it."
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:84
#, python-format
msgid "Click on <em>Save</em> to add the image to the product description."
msgstr ""

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_public_category_action
msgid "Click to define a new category."
msgstr "Haga click para definir una nueva categoría."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:112
#, python-format
msgid "Close Tutorial"
msgstr ""

#. module: website_sale
#: selection:product.attribute,type:0
msgid "Color"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Company Name"
msgstr "Nombre de la Compañía"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Configure your bank account(s)"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Configure your delivery prices"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Confirm <span class=\"fa fa-long-arrow-right\"/>"
msgstr "Confirmar <span class=\"fa fa-long-arrow-right\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Confirm Order <span class=\"fa fa-long-arrow-right\"/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "Confirmation<span class=\"chevron\"/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Confirmed"
msgstr "Confirmado"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:110
#, python-format
msgid "Congratulations"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:111
#, python-format
msgid "Congratulations! You just created and published your first product."
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:53
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
#, python-format
msgid "Continue"
msgstr "Continuar"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Country"
msgstr "País"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_country_group
msgid "Country Group"
msgstr "Grupo de Paises"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_country_group_ids
msgid "Country Groups"
msgstr "Grupos de Países"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Country..."
msgstr "País..."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.reduction_code
msgid "Coupon Code"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:46
#, python-format
msgid "Create Product"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:31
#, python-format
msgid "Create a new product"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:14
#, python-format
msgid "Create a product"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:24
#, python-format
msgid "Create your first product"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_create_uid
#: model:ir.model.fields,field_description:website_sale.field_product_style_create_uid
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_create_date
#: model:ir.model.fields,field_description:website_sale.field_product_style_create_date
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_create_date
msgid "Created on"
msgstr "Creado en"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Customize your Payment message"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_config_settings_module_delivery_dhl
msgid "DHL integration"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_currency_id
msgid "Default Currency"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_id
msgid "Default Pricelist"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_config_settings_view_form
msgid "Default Sales Team"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_config_settings_view_form
msgid "Default Salesperson"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Defining a good delivery strategy is difficult: you don't want to cut into "
"your margins, but you want to be attractive to customers."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Delivery Strategy"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_quote_description
msgid "Description for the quote"
msgstr "Descripción para la cotización"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_description
msgid "Description for the website"
msgstr "Descripción para el sitio web"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product_website_sequence
#: model:ir.model.fields,help:website_sale.field_product_template_website_sequence
msgid "Determine the display order in the Website E-commerce"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line_discounted_price
msgid "Discounted price"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_display_name
#: model:ir.model.fields,field_description:website_sale.field_product_style_display_name
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_display_name
msgid "Display Name"
msgstr "Nombre a Mostrar"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:90
#, python-format
msgid "Drag & Drop a block"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:91
#, python-format
msgid "Drag this website block and drop it in your page."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist_code
msgid "E-commerce Promotional Code"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:60
#, python-format
msgid "Edit the price of this product by clicking on the amount."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Electronic payments have the advantage of being integrated into the buying "
"process. This means your customers will be less likely to drop out before "
"the payment, resulting in a higher conversion rate at the end."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Email"
msgstr "Email"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:40
#, python-format
msgid "Enter a name for your new product"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Enter their identification credentials"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Enter your existing products into this CSV file, respecting its structure."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product_split_method
msgid ""
"Equal : Cost will be equally divided.\n"
"By Quantity : Cost will be divided according to product's quantity.\n"
"By Current cost : Cost will be divided according to product's current cost.\n"
"By Weight : Cost will be divided depending on its weight.\n"
"By Volume : Cost will be divided depending on its volume."
msgstr ""

#. module: website_sale
#: constraint:product.public.category:0
msgid "Error ! You cannot create recursive categories."
msgstr "¡Error! No puede crear categorías recursivas"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Example of Good Product Page"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Example: you sell T-shirts in the US only. You can offer free delivery "
"because your items are medium priced and the delivery costs are limited and "
"well defined."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Example: you sell cheap specialized electronic components. You choose flat "
"rates because the price of an item is sometimes lower than the delivery "
"costs."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Example: you sell custom-made wood sculptures, and because your customers "
"are all over the world, each delivery is different and at cost price."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Export the 3 products you have already created by checking them and choosing "
"'Export' from the 'Action' menu"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info_option
msgid "Extra Info<span class=\"chevron\"/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Extra Step"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_config_settings_module_delivery_fedex
msgid "Fedex integration"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Field 1"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Field 3"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Field not custom"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Field not required"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Field required"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Focus on adding content and improving the pages for your best-selling "
"products: don't try to create complete pages for all your products at first!"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Free and easy to setup"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "From a"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_public_category_sequence
msgid "Gives the sequence order when displaying a list of product categories."
msgstr ""
"Indica el orden de secuencia cuando se muestra una lista de categorías de "
"producto."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Go to the"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_style_html_class
msgid "HTML Classes"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_attribute_value_html_color
msgid "HTML Color Index"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_ir_http
msgid "HTTP routing"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.reduction_code
msgid "Have a coupon code? Fill in this field and apply."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Here are <strong>some pros and cons</strong> to help you decide:"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_attribute_value_html_color
msgid ""
"Here you can set a specific HTML color index (e.g. #ff0000) to display the "
"color on the website if the attibute type is 'Color'."
msgstr ""

#. module: website_sale
#: selection:product.attribute,type:0
msgid "Hidden"
msgstr "Oculto"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_id
#: model:ir.model.fields,field_description:website_sale.field_product_style_id
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_id_11000
msgid "ID"
msgstr "ID"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"If you have an eCommerce, one of your objectives is of course to grow your "
"revenues by\n"
"                    selling more and pricier products. Luckily for you, Odoo "
"integrates three powerful\n"
"                    customizations for that."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_image
msgid "Image"
msgstr "Imagen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Imagine a new customer who comes to your website, finds the product they "
"want and add it to their cart.<br/>\n"
"                        Then they get to the checkout page and are hit with "
"the delivery and handling charges.<br/>\n"
"                        Suddenly, a product that looked like it was a fair "
"price seems little expensive, and the customer leaves your website "
"disappointed."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Import Your Products"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"In order to take money from customers, you need a way of accepting payments."
"<br/>\n"
"                        That's what a payment gateway is for: it helps you "
"make money, but that does cost money.<br/>\n"
"                        That why it's important to choose the right provider "
"for your online payments."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Increase your average cart amount by proposing complementary products to "
"your visitors."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Increase your chances to make a sale by displaying suggested products."
msgstr ""

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:567
#, python-format
msgid "Invalid Email! Please enter a valid email address."
msgstr "¡Email Inválido! Por favor introducir una dirección de email válida."

#. module: website_sale
#: code:addons/website_sale/models/sale_order.py:86
#, python-format
msgid "It is forbidden to modify a sale order which is not in draft status"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"It's difficult to recommend one over the others. So, simply pick the one "
"that is more popular in your country!"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_landed_cost_ok
msgid "Landed Costs"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category___last_update
#: model:ir.model.fields,field_description:website_sale.field_product_style___last_update
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist___last_update
msgid "Last Modified on"
msgstr "Ultima Modificación en"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_partner_last_website_so_id
msgid "Last Online Sale Order"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_write_uid
#: model:ir.model.fields,field_description:website_sale.field_product_style_write_uid
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_write_uid
msgid "Last Updated by"
msgstr "Actualizado última vez por"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_write_date
#: model:ir.model.fields,field_description:website_sale.field_product_style_write_date
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_write_date
msgid "Last Updated on"
msgstr "Ultima Actualización"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_image_medium
msgid "Medium-sized image"
msgstr "Imagen de tamaño mediano"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_public_category_image_medium
msgid ""
"Medium-sized image of the category. It is automatically resized as a "
"128x128px image, with aspect ratio preserved. Use this field in form views "
"or some kanban views."
msgstr ""
"Imagen de tamaño mediano de la categoría. Se redimensiona de forma "
"automática como una imagen 128x128px, se mantiene el ratio de aspecto. Use "
"este campo en vista de formulario y kanban."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_config_settings_view_form
msgid "Merchant Connectors"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_purchase_line_warn_msg
msgid "Message for Purchase Order Line"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_sale_line_warn_msg
msgid "Message for Sales Order Line"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale.js:15
#, python-format
msgid "My Cart"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_complete_name
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_name
msgid "Name"
msgstr "Nombre"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Name (Shipping)"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/controllers/main.py:976
#: code:addons/website_sale/static/src/js/website_sale.editor.js:18
#: model_terms:ir.ui.view,arch_db:website_sale.content_new_product
#, python-format
msgid "New Product"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:51
#, python-format
msgid "New product created"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "No monthly fees for standard offer"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No product defined."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Now you can also <strong>import your existing products:</strong>"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale.xml:12
#, python-format
msgid "OK"
msgstr "OK"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Odoo offers an importation service to handle the whole process for you!"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Odoo's web-services allows developers to create scripts that will load data "
"automatically into the system."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"On your website, go to the product page where you want to add suggested "
"products."
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:98
#, python-format
msgid "Once you click on <em>Save</em>, your product is updated."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_only_services
msgid "Only Services"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Order"
msgstr "Pedido"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_website_order_line
msgid "Order Lines displayed on Website"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_sale_order_website_order_line
msgid ""
"Order Lines to be displayed on the website. They should not be used for "
"computation purpose."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_parent_id
msgid "Parent Category"
msgstr "Categoria Padre"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_partner
msgid "Partner"
msgstr "Partner"

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:779
#: code:addons/website_sale/controllers/main.py:859
#, python-format
msgid "Pay Now"
msgstr "Pagar Ahora"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Pay Now <span class=\"fa fa-long-arrow-right\"/>"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_payment_acquirer_id
msgid "Payment Acquirer"
msgstr "Pago del Adquiriente"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.report_shop_saleorder_document
msgid "Payment Information"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Payment Method:"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Payment Methods"
msgstr "Métodos de Pago"

#. module: website_sale
#: model:ir.model,name:website_sale.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transacción de Pago"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info_option
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "Payment<span class=\"chevron\"/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Phone"
msgstr "Teléfono"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Policies"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Price"
msgstr "Precio"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_website_pricelist_ids
msgid "Price list available for this Ecommerce/Website"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_pricelist
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_pricelist_id
msgid "Pricelist"
msgstr "Lista de Precios"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_name
msgid "Pricelist Name"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_product
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Product"
msgstr "Producto"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_attribute
msgid "Product Attribute"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "Product Name"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Product Pages"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_public_category_tree_view
#, fuzzy
msgid "Product Public Categories"
msgstr "Categorias de Producto de Producto"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_template
msgid "Product Template"
msgstr "Plantilla de Producto"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Product detail form"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
#: model_terms:ir.ui.view,arch_db:website_sale.product_price
msgid "Product not available"
msgstr "Producto no disponible"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.404
msgid "Product not found!"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "Products"
msgstr "Productos"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Products list"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Products list view"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Promote"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:105
#, python-format
msgid "Publish your product"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_rating_rating_website_published
msgid "Published"
msgstr "Publicado"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_purchase_line_warn
msgid "Purchase Order Line"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Push down"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Push to bottom"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Push to top"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Push up"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Put the practical details (shipping, payment options,...) as links in the "
"footer; that way, they will be accessible from all your product pages."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
msgid "Qty:"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Quantity"
msgstr "Cantidad"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Quick and easy to set up"
msgstr ""

#. module: website_sale
#: selection:product.attribute,type:0
msgid "Radio"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_rating_rating
#: model:ir.model.fields,field_description:website_sale.field_product_product_rating_ids
msgid "Rating"
msgstr "Clasificación"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Read the"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.404
msgid "Return to the product list."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "Review Order<span class=\"chevron\"/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_item
msgid "Sale"
msgstr "Venta"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Sales / Settings"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_order
msgid "Sales Order"
msgstr "Pedidos de Venta"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_order_line
#: model:ir.model.fields,field_description:website_sale.field_product_product_sale_line_warn
msgid "Sales Order Line"
msgstr "Línea de Pedido de Venta"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_config_settings_salesteam_id
#: model:ir.model.fields,field_description:website_sale.field_website_salesteam_id
msgid "Sales Team"
msgstr "Equipo de Ventas"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_config_settings_salesperson_id
#: model:ir.model.fields,field_description:website_sale.field_website_salesperson_id
msgid "Salesperson"
msgstr "Vendedor"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:83
#, python-format
msgid "Save"
msgstr "Guardar"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Save and import the modified CSV file from the 'More' menu of the"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:97
#, python-format
msgid "Save your modifications"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Secure Payment"
msgstr ""

#. module: website_sale
#: selection:product.attribute,type:0
msgid "Select"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:32
#, python-format
msgid ""
"Select <em>New Product</em> to create it and manage its properties to boost "
"your sales."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Select a product from the"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_selectable
msgid "Selectable"
msgstr "Seleccionable"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product_purchase_line_warn
#: model:ir.model.fields,help:website_sale.field_product_product_sale_line_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Sell"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Sell More"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_sequence
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_sequence
#: model:ir.model.fields,field_description:website_sale.field_product_template_website_sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Ship To:"
msgstr "Enviar a:"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Ship to the same address"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Shipping"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "Shipping &amp;"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_config_settings_view_form
msgid "Shipping Connectors"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Shipping Information"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
#: model:website.menu,name:website_sale.menu_shop
msgid "Shop"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Shop - Checkout"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Shop - Confirmed"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Shop - Select Payment Method"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Shopping Cart"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Sign in"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Simply add one or more products as an <strong>'Accessory Product'</strong>."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Simply add the product you want as an <strong>'Optional Product'</strong>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Size"
msgstr "Tamaño"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_size_x
#: model:ir.model.fields,field_description:website_sale.field_product_template_website_size_x
msgid "Size X"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_size_y
#: model:ir.model.fields,field_description:website_sale.field_product_template_website_size_y
msgid "Size Y"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:19
#, python-format
msgid "Skip It"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category_image_small
msgid "Small-sized image"
msgstr "Imagen de tamaño pequeño"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_public_category_image_small
msgid ""
"Small-sized image of the category. It is automatically resized as a 64x64px "
"image, with aspect ratio preserved. Use this field anywhere a small image is "
"required."
msgstr ""
"Imagen de tamaño pequeño de la categoría. Se redimensiona de forma "
"automática como una imagen de 64x64px, con un ratio de aspecto preservado. "
"Use este campo donde quiera que una imagen pequeña se requiera."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Some customers prefer to pay this way"
msgstr ""

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:589
#, python-format
msgid "Some required fields are empty."
msgstr "Algunos campos requeridos están vacios."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.404
msgid "Sorry, this product is not available anymore."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_split_method
msgid "Split Method"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:19
#, python-format
msgid "Start Tutorial"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "State / Province"
msgstr "Estado / Provincia"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "State / Province..."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Street"
msgstr "Calle"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_style_name
msgid "Style Name"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_style_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template_website_style_ids
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale
msgid "Styles"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Subtotal"
msgstr "Subtotal"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_alternative_product_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template_alternative_product_ids
msgid "Suggested Products"
msgstr "Productos Sugeridos"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.recommended_products
msgid "Suggested alternatives:"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.suggested_products_list
msgid "Suggested products:"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Taxes"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_config_settings_module_delivery_temando
msgid "Temando integration"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Thank you for your order."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"The best way to start your online shop is by creating 3 products pages "
"directly in the website.<br/>\n"
"                        To help you, here are some guidelines that will "
"convert customers:"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product_website_url
msgid "The full URL to access the document through the website."
msgstr "La URL completa para acceder al documento a través del sitio web"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.order_state_message
msgid "The payment seems to have been canceled."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.order_state_message
msgid "There seems to be an error with your request."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_public_category_image
msgid ""
"This field holds the image used as image for the category, limited to "
"1024x1024px."
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:52
#, python-format
msgid "This page contains all the information related to the new product."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.reduction_code
msgid "This promo code is not available"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product_public_categ_ids
#: model:ir.model.fields,help:website_sale.field_product_template_public_categ_ids
msgid "Those categories are used to group similar products for e-commerce."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "To use them:"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.report_shop_saleorder_document
msgid "Total"
msgstr "Total"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_payment_tx_id
msgid "Transaction"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Try to apply what you've learned above by manually creating three Product "
"pages from the Content menu."
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_attribute_type
msgid "Type"
msgstr "Tipo"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_config_settings_module_delivery_ups
msgid "UPS integration"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_config_settings_module_delivery_usps
msgid "USPS integration"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Unit Price"
msgstr "Precio Unitario"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:66
#, python-format
msgid "Update image"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "Use the <i>'Content'</i> top menu to create a new product."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"Users will buy more accessories and services if they can add them to their "
"cart in one click."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "VAT Number"
msgstr "Número de RUC"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Validate Order"
msgstr "Validar Pedido"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
msgid "View Cart ("
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_published
msgid "Visible in Website"
msgstr "Visible en Sitio Web"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_rating_rating_website_published
msgid "Visible on the website as a comment"
msgstr "Visible en Sitio Web como un comentario"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "Web Service technical documentation."
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_website
#: model:ir.model.fields,field_description:website_sale.field_website_pricelist_website_id
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Website"
msgstr "Sitio Web"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Website Categories"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_country_group_website_pricelist_ids
msgid "Website Price Lists"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_pricelist_form_view
#: model_terms:ir.ui.view,arch_db:website_sale.website_pricelist_tree_view
msgid "Website PriceLists"
msgstr ""

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.website_sale_pricelists_by_website
#: model:ir.model,name:website_sale.model_website_pricelist
#: model:ir.ui.menu,name:website_sale.menu_website_sale_pricelists
msgid "Website Pricelist"
msgstr ""

#. module: website_sale
#: code:addons/website_sale/models/sale_order.py:427
#, python-format
msgid "Website Pricelist for %s"
msgstr ""

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_public_category_action
#: model:ir.ui.menu,name:website_sale.menu_product_public_category
msgid "Website Product Categories"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_public_category
#: model:ir.model.fields,field_description:website_sale.field_product_product_public_categ_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template_public_categ_ids
msgid "Website Product Category"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_public_category_form_view
msgid "Website Public Categories"
msgstr ""

#. module: website_sale
#: model:ir.actions.act_url,name:website_sale.action_open_website
msgid "Website Shop"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_url
msgid "Website URL"
msgstr "URL del Sitio Web"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_meta_description
msgid "Website meta description"
msgstr "Descripción meta del sitio web"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_meta_keywords
msgid "Website meta keywords"
msgstr "Palabras clave meta del sitio web"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product_website_meta_title
msgid "Website meta title"
msgstr "Título meta del sitio web"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:17
#, python-format
msgid "Welcome to your shop"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid ""
"You can also define different prices for the variants you created by "
"activating the 'Use pricelists to adapt your price per customers' option in"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "You can setup 3 types of <strong>payment methods in Odoo:</strong>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "You have to reconcile the payment manually"
msgstr ""

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_tour_shop.js:18
#, python-format
msgid ""
"You successfully installed the e-commerce. This guide will help you to "
"create your product and promote your sales."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Your Address"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Your Name"
msgstr "Su Nombre"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Your Order"
msgstr "Su Pedido"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
msgid "Your cart is empty!"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.order_state_message
msgid "Your order has been confirmed, thank you for your loyalty."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.order_state_message
msgid "Your payment has been received."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.order_state_message
msgid "Your transaction is waiting a manual confirmation."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.order_state_message
msgid "Your transaction is waiting confirmation."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Zip / Postal Code"
msgstr "Código Postal / Zip"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "and fill in one or more <strong>'Suggested Products'</strong>."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.reduction_code
msgid "code..."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_comment
msgid "comment"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_comment
msgid "comments"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_config_settings_module_sale_ebay
msgid "eBay connector"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_config_settings_view_form
msgid "eCommerce"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
msgid "items)"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "of the Sales module"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "or"
msgstr "o"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "pagination form-inline"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_attribute_value
msgid "product.attribute.value"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_style
msgid "product.style"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "select..."
msgstr "seleccionar..."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_planner
msgid "using one or several of the strategies above"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_config_settings
msgid "website.config.settings"
msgstr "website.config.settings"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "☑ 256 bit encryption"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "☑ 30-days money-back guarantee"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "☑ Invoice sent by e-Mail"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "☑ Processed by Ogone"
msgstr ""

#~ msgid "Available in the Point of Sale"
#~ msgstr "Disponible en el Punto de Venta"

#~ msgid ""
#~ "Check if the product should be weighted using the hardware scale "
#~ "integration"
#~ msgstr ""
#~ "Marque si el producto debe ser pesado usando la integración hardware de "
#~ "la balanza"

#~ msgid "Check if you want this product to appear in the Point of Sale"
#~ msgstr "Marque esta casilla si quiere que este producto aparezca en el TPV"

#~ msgid "Extra Info"
#~ msgstr "Información Extra"

#~ msgid "Payment"
#~ msgstr "Pago"

#~ msgid "Point of Sale Category"
#~ msgstr "Categoría de Punto de Venta"

#~ msgid "Procurement"
#~ msgstr "Requerimiento"

#~ msgid "Project"
#~ msgstr "Proyecto"

#~ msgid ""
#~ "Those categories are used to group similar products for point of sale."
#~ msgstr ""
#~ "Esas categorías son usadas para grupos similares de productos para el "
#~ "punto de venta."

#~ msgid "To Weigh With Scale"
#~ msgstr "Para Pesar con Balanza"
