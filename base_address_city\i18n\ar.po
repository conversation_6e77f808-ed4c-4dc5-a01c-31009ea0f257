# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_address_city
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:49+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: base_address_city
#: model:ir.model.fields,help:base_address_city.field_res_country__enforce_cities
#: model:ir.model.fields,help:base_address_city.field_res_partner__country_enforce_cities
#: model:ir.model.fields,help:base_address_city.field_res_users__country_enforce_cities
msgid ""
"Check this box to ensure every address created in that country has a 'City' "
"chosen in the list of the country's cities."
msgstr ""
"قم بتحديد هذا الخيار للتأكد من أن كل عنوان تم إنشاؤه في تلك الدولة به "
"'مدينة' مختارة في قائمة مدن الدولة. "

#. module: base_address_city
#: model:ir.actions.act_window,name:base_address_city.action_res_city_tree
#: model_terms:ir.ui.view,arch_db:base_address_city.view_res_country_city_extended_form
msgid "Cities"
msgstr "المدن"

#. module: base_address_city
#: code:addons/base_address_city/models/res_partner.py:0
#: model:ir.model,name:base_address_city.model_res_city
#: model_terms:ir.ui.view,arch_db:base_address_city.view_city_filter
#: model_terms:ir.ui.view,arch_db:base_address_city.view_city_tree
#, python-format
msgid "City"
msgstr "المدينة"

#. module: base_address_city
#: model:ir.model.fields,field_description:base_address_city.field_res_partner__city_id
#: model:ir.model.fields,field_description:base_address_city.field_res_users__city_id
msgid "City of Address"
msgstr "المدينة المذكورة في العنوان "

#. module: base_address_city
#: model:ir.model,name:base_address_city.model_res_partner
msgid "Contact"
msgstr "جهة الاتصال"

#. module: base_address_city
#: model:ir.model,name:base_address_city.model_res_country
#: model:ir.model.fields,field_description:base_address_city.field_res_city__country_id
msgid "Country"
msgstr "الدولة"

#. module: base_address_city
#: model:ir.model.fields,field_description:base_address_city.field_res_city__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: base_address_city
#: model:ir.model.fields,field_description:base_address_city.field_res_city__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: base_address_city
#: model:ir.model.fields,field_description:base_address_city.field_res_city__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: base_address_city
#: model_terms:ir.actions.act_window,help:base_address_city.action_res_city_tree
msgid ""
"Display and manage the list of all cities that can be assigned to\n"
"                your partner records. Note that an option can be set on each country separately\n"
"                to enforce any address of it to have a city in this list."
msgstr ""
"قم بعرض وإدارة قائمة كافة المدن التي يمكن تعيينها\n"
"                في سجلات شريكك. يرجى العلم بأنه من الممكن اختيار خيار مختلف لكل دولة\n"
"                لإلزام كل عنوان يُسجل بها على أن يحتوي مدينة من هذه القائمة. "

#. module: base_address_city
#: model:ir.model.fields,field_description:base_address_city.field_res_country__enforce_cities
#: model:ir.model.fields,field_description:base_address_city.field_res_partner__country_enforce_cities
#: model:ir.model.fields,field_description:base_address_city.field_res_users__country_enforce_cities
msgid "Enforce Cities"
msgstr "جعل خانة المدن إلزامية"

#. module: base_address_city
#: model:ir.model.fields,field_description:base_address_city.field_res_city__id
msgid "ID"
msgstr "المُعرف"

#. module: base_address_city
#: model:ir.model.fields,field_description:base_address_city.field_res_city____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: base_address_city
#: model:ir.model.fields,field_description:base_address_city.field_res_city__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: base_address_city
#: model:ir.model.fields,field_description:base_address_city.field_res_city__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: base_address_city
#: model:ir.model.fields,field_description:base_address_city.field_res_city__name
msgid "Name"
msgstr "الاسم"

#. module: base_address_city
#: model_terms:ir.ui.view,arch_db:base_address_city.view_city_filter
msgid "Search City"
msgstr "البحث عن مدينة"

#. module: base_address_city
#: model:ir.model.fields,field_description:base_address_city.field_res_city__state_id
msgid "State"
msgstr "الولاية "

#. module: base_address_city
#: model:ir.model.fields,field_description:base_address_city.field_res_city__zipcode
msgid "Zip"
msgstr "الرمز البريدي"
