<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="payment_refund_wizard_view_form" model="ir.ui.view">
        <field name="name">payment.refund.wizard.form</field>
        <field name="model">payment.refund.wizard</field>
        <field name="arch" type="xml">
            <form string="Refund">
                <field name="has_pending_refund" invisible="1"/>
                <div class="alert alert-warning"
                     id="alert_draft_refund_tx"
                     role="alert"
                     attrs="{'invisible': [('has_pending_refund', '=', False)]}">
                    <p>
                        <strong>Warning!</strong> There is a refund pending for this payment.
                        Wait a moment for it to be processed. If the refund is still pending in a
                        few minutes, please check your payment acquirer configuration.
                    </p>
                </div>
                <group>
                    <group>
                        <field name="payment_id" invisible="1"/>
                        <field name="transaction_id" invisible="1"/>
                        <field name="currency_id" invisible="1"/>
                        <field name="support_refund" invisible="1"/>
                        <field name="payment_amount"/>
                        <field name="refunded_amount"
                               attrs="{'invisible': [('refunded_amount', '&lt;=', 0)]}"/>
                        <field name="amount_available_for_refund"/>
                        <field name="amount_to_refund"
                               attrs="{'readonly': [('support_refund', '=', 'full_only')]}"/>
                    </group>
                </group>
                <footer>
                    <button string="Refund" type="object" name="action_refund" class="btn-primary"/>
                    <button string="Close" special="cancel" class="btn-secondary"/>
                </footer>
            </form>
        </field>
    </record>

</odoo>
