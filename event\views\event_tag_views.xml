<?xml version="1.0"?>
<odoo>
    <data>
        <!-- EVENT.TAG.CATEGORY VIEWS -->
        <record id="event_tag_category_view_tree" model="ir.ui.view">
            <field name="name">event.tag.category.view.tree</field>
            <field name="model">event.tag.category</field>
            <field name="arch" type="xml">
                <tree string="Event Category">
                    <field name="sequence" widget="handle"/>
                    <field name="name"/>
                    <field name="tag_ids" widget="many2many_tags" options="{'color_field': 'color'}"/>
                </tree>
            </field>
        </record>

        <record id="event_tag_category_view_form" model="ir.ui.view">
            <field name="name">event.tag.category.view.form</field>
            <field name="model">event.tag.category</field>
            <field name="arch" type="xml">
                <form string="Event Category">
                    <sheet>
                        <div class="oe_title">
                            <h1><field nolabel="1" name="name"/></h1>
                        </div>
                        <group>
                            <field name="tag_ids" context="{'default_category_id': active_id}">
                                <tree string="Tags" editable="bottom">
                                    <field name="sequence" widget="handle"/>
                                    <field name="name"/>
                                    <field name="color" widget="color_picker"/>
                                </tree>
                            </field>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="event_tag_category_action_tree" model="ir.actions.act_window" >
            <field name="name">Event Tags Categories</field>
            <field name="res_model">event.tag.category</field>
            <field name="view_mode">tree,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create an Event Tag Category
                </p><p>
                    Use Event Tag Categories to classify and organize your event tags.
                </p>
            </field>
        </record>

        <!-- EVENT.TAG VIEWS -->
        <record id="event_tag_view_tree" model="ir.ui.view">
            <field name="name">event.tag.view.tree</field>
            <field name="model">event.tag</field>
            <field name="arch" type="xml">
                <tree string="Event Tags Categories">
                    <field name="sequence" widget="handle"/>
                    <field name="name"/>
                    <field name="category_id"/>
                    <field name="color" widget="color_picker"/>
                </tree>
            </field>
        </record>

        <record id="event_tag_view_form" model="ir.ui.view">
            <field name="name">event.tag.view.form</field>
            <field name="model">event.tag</field>
            <field name="arch" type="xml">
                <form string="Event Category Tag">
                    <sheet>
                        <group>
                            <field name="name"/>
                            <field name="category_id" widget="many2one"/>
                            <field name="color" widget="color_picker"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="menu_event_category" model="ir.ui.menu">
            <field name="action" ref="event.event_tag_category_action_tree"/>
        </record>

    </data>
</odoo>
