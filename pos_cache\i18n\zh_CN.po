# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_cache
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: 山西清水欧度(QQ:54773801) <<EMAIL>>, 2022\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_config__cache_ids
msgid "Cache"
msgstr "缓存"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__compute_user_id
msgid "Cache compute user"
msgstr "缓存计算用户"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__config_id
msgid "Config"
msgstr "配置"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__create_uid
msgid "Created by"
msgstr "创建人"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__create_date
msgid "Created on"
msgstr "创建时间"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__id
msgid "ID"
msgstr "ID"

#. module: pos_cache
#: model_terms:ir.ui.view,arch_db:pos_cache.pos_config_view_form_inherit_pos_cache
msgid "Invalidate cache"
msgstr "失效缓存"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache____last_update
msgid "Last Modified on"
msgstr "最后修改时间"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__write_date
msgid "Last Updated on"
msgstr "最后更新时间"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_config__limit_products_per_request
msgid "Limit Products Per Request"
msgstr "每个请求的产品限制"

#. module: pos_cache
#. openerp-web
#: code:addons/pos_cache/static/src/js/pos_cache.js:0
#, python-format
msgid "Loading"
msgstr "正在加载"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_config__oldest_cache_time
msgid "Oldest cache time"
msgstr "最早缓存时间"

#. module: pos_cache
#: model:ir.actions.server,name:pos_cache.refresh_pos_cache_cron_ir_actions_server
#: model:ir.cron,cron_name:pos_cache.refresh_pos_cache_cron
#: model:ir.cron,name:pos_cache.refresh_pos_cache_cron
msgid "PoS: refresh cache"
msgstr "PoS: 刷新缓存"

#. module: pos_cache
#: model:ir.model,name:pos_cache.model_pos_cache
msgid "Point of Sale Cache"
msgstr "POS缓存"

#. module: pos_cache
#: model:ir.model,name:pos_cache.model_pos_config
msgid "Point of Sale Configuration"
msgstr "POS配置"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__product_domain
msgid "Product Domain"
msgstr "产品域"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache__product_fields
msgid "Product Fields"
msgstr "产品字段"
