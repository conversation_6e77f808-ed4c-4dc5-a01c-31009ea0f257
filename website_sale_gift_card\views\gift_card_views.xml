<?xml version="1.0"?>
<odoo>
    <record id="gift_card_view_form" model="ir.ui.view">
        <field name="name">gift.card.form Website</field>
        <field name="model">gift.card</field>
        <field name="inherit_id" ref="gift_card.gift_card_view_form" />
        <field name="arch" type="xml">
            <field name="partner_id">
                <field name="website_id" groups="website.group_multi_website" options="{'no_create': True}"/>
            </field>
        </field>
    </record>

    <!-- Searching -->
    <record id="gift_card_view_search" model="ir.ui.view">
        <field name="name">gift.card.search</field>
        <field name="model">gift.card</field>
        <field name="arch" type="xml">
            <search string="Gift Card">
                <field name="code"/>
                <filter name="valid" string="Valid" domain="[('state', '=', 'valid')]" />
            </search>
        </field>
    </record>
</odoo>
