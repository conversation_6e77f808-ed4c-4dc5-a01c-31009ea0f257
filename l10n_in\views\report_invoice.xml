<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="l10n_in_report_invoice_document_inherit" inherit_id="account.report_invoice_document">

        <xpath expr="//span[@t-field='o.partner_id.vat']" position="attributes">
            <attribute name="t-if">o.company_id.account_fiscal_country_id.code != 'IN'</attribute>
        </xpath>
        <xpath expr="//span[@t-field='o.partner_id.vat']" position="after">
            <span t-field="o.l10n_in_gstin" t-if="o.company_id.account_fiscal_country_id.code == 'IN'"/>
        </xpath>
        <xpath expr="//t[@t-set='address']" position="inside">
            <t t-if="o.company_id.account_fiscal_country_id.code == 'IN' and o.l10n_in_state_id" class="mt16">
                <t t-if="o.move_type in ('in_invoice', 'in_refund')">
                    Destination of supply: <span t-esc="o.l10n_in_state_id.name"/>
                </t>
                <t t-if="o.move_type in ('out_invoice', 'out_refund')">
                    Place of supply: <span t-esc="o.l10n_in_state_id.name"/>
                </t>
            </t>
        </xpath>
        <xpath expr="//div[@t-if='not is_html_empty(o.narration)']" position="before">
            <t t-if="o.company_id.account_fiscal_country_id.code == 'IN'">
                <p id="total_in_words" class="mb16">
                    <strong>Total (In Words): </strong>
                    <span t-field="o.amount_total_words"/>
                </p>
            </t>
        </xpath>

        <xpath expr="//table[@name='invoice_line_table']/thead/tr/th[1]" position="after">
            <t t-if="o.company_id.account_fiscal_country_id.code == 'IN'">
                <th>HSN/SAC</th>
            </t>
        </xpath>

        <xpath expr="//t[@name='account_invoice_line_accountable']/td[1]" position="after">
            <td t-if="o.company_id.account_fiscal_country_id.code == 'IN'">
              <span t-if="line.product_id.l10n_in_hsn_code" t-field="line.product_id.l10n_in_hsn_code"></span>
            </td>
        </xpath>

        <xpath expr="//h2" position="replace">
            <h2>
                <span t-if="o.move_type == 'out_invoice' and o.state == 'posted'" t-field="o.journal_id.name"/>
                <span t-if="o.move_type == 'out_invoice' and o.state == 'draft'">Draft <span t-field="o.journal_id.name"/></span>
                <span t-if="o.move_type == 'out_invoice' and o.state == 'cancel'">Cancelled <span t-field="o.journal_id.name"/></span>
                <span t-if="o.move_type == 'out_refund'">Credit Note</span>
                <span t-if="o.move_type == 'in_refund'">Vendor Credit Note</span>
                <span t-if="o.move_type == 'in_invoice'">Vendor Bill</span>
                <span t-field="o.name"/>
            </h2>
        </xpath>

    </template>

</odoo>
