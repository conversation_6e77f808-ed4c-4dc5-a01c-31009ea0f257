# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * stock_account
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:06+0000\n"
"PO-Revision-Date: 2016-05-15 18:50+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Spanish (Venezuela) (http://www.transifex.com/odoo/odoo-9/"
"language/es_VE/)\n"
"Language: es_VE\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_history_report_tree
msgid "# of Products"
msgstr "Nº de productos"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form
msgid "Account Stock Properties"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_config_settings_inherit
msgid "Accounting"
msgstr "Contabilidad"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_location_form_inherit
msgid "Accounting Information"
msgstr ""

#. module: stock_account
#: code:addons/stock_account/wizard/stock_change_standard_price.py:62
#, python-format
msgid "Active ID is not set in Context."
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_config_settings_group_stock_inventory_valuation
msgid ""
"Allows to configure inventory valuations on products and product categories."
msgstr ""

#. module: stock_account
#: selection:product.category,property_cost_method:0
#: selection:product.template,property_cost_method:0
msgid "Average Price"
msgstr "Precio medio"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product_can_be_expensed
msgid "Can be expensed"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_change_standard_price
#: model_terms:ir.ui.view,arch_db:stock_account.view_wizard_valuation_history
msgid "Cancel"
msgstr "Cancelar"

#. module: stock_account
#: code:addons/stock_account/stock_account.py:278
#, python-format
msgid ""
"Cannot find a stock input account for the product %s. You must define one on "
"the product category, or on the location, before processing this operation."
msgstr ""

#. module: stock_account
#: code:addons/stock_account/stock_account.py:280
#, python-format
msgid ""
"Cannot find a stock output account for the product %s. You must define one "
"on the product category, or on the location, before processing this "
"operation."
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_change_standard_price
msgid "Change Price"
msgstr ""

#. module: stock_account
#: model:ir.actions.act_window,name:stock_account.action_view_change_standard_price
#: model:ir.model,name:stock_account.model_stock_change_standard_price
#: model_terms:ir.ui.view,arch_db:stock_account.view_change_standard_price
msgid "Change Standard Price"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_wizard_valuation_history
msgid "Choose a date in the past to get the inventory at that date."
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_inventory_accounting_date
msgid ""
"Choose the accounting date at which you want to value the stock moves "
"created by the inventory instead of the default one (the inventory end date)"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_wizard_valuation_history
msgid "Choose your date"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product_uos_coeff
msgid ""
"Coefficient to convert default Unit of Measure to Unit of Sale uos = uom * "
"coeff"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_history_company_id
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_history_report_search
msgid "Company"
msgstr "Compañía"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.product_variant_easy_edit_view_inherit
#: model_terms:ir.ui.view,arch_db:stock_account.view_template_property_form
msgid "Compute from average price"
msgstr ""

#. module: stock_account
#: code:addons/stock_account/stock_account.py:351
#, python-format
msgid ""
"Configuration error. Please configure the price difference account on the "
"product or its category to process this operation."
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_change_standard_price
msgid "Cost"
msgstr "Coste"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category_property_cost_method
#: model:ir.model.fields,field_description:stock_account.field_product_product_property_cost_method
#: model:ir.model.fields,field_description:stock_account.field_product_template_property_cost_method
msgid "Costing Method"
msgstr "Método de coste"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price_counterpart_account_id
msgid "Counter-Part Account"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price_create_uid
#: model:ir.model.fields,field_description:stock_account.field_wizard_valuation_history_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price_create_date
#: model:ir.model.fields,field_description:stock_account.field_wizard_valuation_history_create_date
msgid "Created on"
msgstr "Creado en"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_wizard_valuation_history_date
msgid "Date"
msgstr "Fecha"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price_display_name
#: model:ir.model.fields,field_description:stock_account.field_stock_history_display_name
#: model:ir.model.fields,field_description:stock_account.field_wizard_valuation_history_display_name
msgid "Display Name"
msgstr "Mostrar nombre"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_inventory_accounting_date
msgid "Force Accounting Date"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_history_report_search
msgid "Group By"
msgstr "Agrupar por"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price_id
#: model:ir.model.fields,field_description:stock_account.field_stock_history_id
#: model:ir.model.fields,field_description:stock_account.field_wizard_valuation_history_id
msgid "ID"
msgstr "ID"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_change_standard_price_new_price
msgid ""
"If cost price is increased, stock variation account will be debited and "
"stock output account will be credited with the value = (difference of amount "
"* quantity available).\n"
"If cost price is decreased, stock variation account will be creadited and "
"stock input account will be debited."
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category_property_valuation
msgid ""
"If perpetual valuation is enabled for a product, the system will "
"automatically create journal entries corresponding to stock moves, with "
"product price as specified by the 'Costing Method'. The inventory variation "
"account set on the product category will represent the current inventory "
"value, and the stock input and stock output account will hold the "
"counterpart moves for incoming and outgoing products."
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product_property_valuation
#: model:ir.model.fields,help:stock_account.field_product_template_property_valuation
msgid ""
"If perpetual valuation is enabled for a product, the system will "
"automatically create journal entries corresponding to stock moves, with "
"product price as specified by the 'Costing Method'The inventory variation "
"account set on the product category will represent the current inventory "
"value, and the stock input and stock output account will hold the "
"counterpart moves for incoming and outgoing products."
msgstr ""

#. module: stock_account
#: selection:stock.config.settings,module_stock_landed_costs:0
msgid "Include landed costs in product costing computation"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_config_settings_module_stock_landed_costs
msgid ""
"Install the module that allows to affect landed costs on pickings, and split "
"them onto the different products."
msgstr ""

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_inventory
msgid "Inventory"
msgstr ""

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_location
msgid "Inventory Locations"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category_property_valuation
#: model:ir.model.fields,field_description:stock_account.field_product_product_property_valuation
#: model:ir.model.fields,field_description:stock_account.field_product_template_property_valuation
#: model:ir.model.fields,field_description:stock_account.field_stock_config_settings_group_stock_inventory_valuation
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form
msgid "Inventory Valuation"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_history_inventory_value
msgid "Inventory Value"
msgstr ""

#. module: stock_account
#: model:ir.actions.act_window,name:stock_account.action_wizard_stock_valuation_history
#: model:ir.model.fields,field_description:stock_account.field_wizard_valuation_history_choose_date
#: model:ir.ui.menu,name:stock_account.menu_action_wizard_valuation_history
msgid "Inventory at Date"
msgstr ""

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_invoice
msgid "Invoice"
msgstr "Factura"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_invoice_line
msgid "Invoice Line"
msgstr "Línea factura"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_config_settings_module_stock_landed_costs
msgid "Landed Costs"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price___last_update
#: model:ir.model.fields,field_description:stock_account.field_stock_history___last_update
#: model:ir.model.fields,field_description:stock_account.field_wizard_valuation_history___last_update
msgid "Last Modified on"
msgstr "Modificada por última vez"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price_write_uid
#: model:ir.model.fields,field_description:stock_account.field_wizard_valuation_history_write_uid
msgid "Last Updated by"
msgstr "Última actualización realizada por"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price_write_date
#: model:ir.model.fields,field_description:stock_account.field_wizard_valuation_history_write_date
msgid "Last Updated on"
msgstr "Ultima actualizacion en"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_history_location_id
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_history_report_search
msgid "Location"
msgstr "Lugar"

#. module: stock_account
#: model:res.groups,name:stock_account.group_inventory_valuation
msgid "Manage Inventory Valuation and Costing Methods"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_history_report_search
msgid "Move"
msgstr "Movimiento"

#. module: stock_account
#: code:addons/stock_account/product.py:125
#: code:addons/stock_account/product.py:187
#, python-format
msgid "No difference between standard price and new price!"
msgstr ""

#. module: stock_account
#: selection:stock.config.settings,module_stock_landed_costs:0
msgid "No landed costs"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_history_date
msgid "Operation Date"
msgstr ""

#. module: stock_account
#: selection:product.category,property_valuation:0
#: selection:product.template,property_valuation:0
msgid "Periodic (manual)"
msgstr ""

#. module: stock_account
#: selection:stock.config.settings,group_stock_inventory_valuation:0
msgid "Periodic inventory valuation (recommended)"
msgstr ""

#. module: stock_account
#: selection:product.category,property_valuation:0
#: selection:product.template,property_valuation:0
msgid "Perpetual (automated)"
msgstr ""

#. module: stock_account
#: selection:stock.config.settings,group_stock_inventory_valuation:0
msgid "Perpetual inventory valuation (stock move generates accounting entries)"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price_new_price
msgid "Price"
msgstr "Precio"

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_product
#: model:ir.model.fields,field_description:stock_account.field_stock_history_product_id
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_history_report_search
msgid "Product"
msgstr "Producto"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product_alert_time
msgid "Product Alert Time"
msgstr "Tiempo de alerta producto"

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_category
#: model:ir.model.fields,field_description:stock_account.field_stock_history_product_categ_id
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_history_report_search
msgid "Product Category"
msgstr "Categoría de producto"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product_life_time
msgid "Product Life Time"
msgstr "Tiempo de vida producto"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_history_quantity
msgid "Product Quantity"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product_removal_time
msgid "Product Removal Time"
msgstr "Tiempo eliminación producto"

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_template
#: model:ir.model.fields,field_description:stock_account.field_stock_history_product_template_id
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_history_report_search
msgid "Product Template"
msgstr "Plantilla de producto"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product_use_time
msgid "Product Use Time"
msgstr "Tiempo de uso producto"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_quant
msgid "Quants"
msgstr ""

#. module: stock_account
#: selection:product.category,property_cost_method:0
#: selection:product.template,property_cost_method:0
msgid "Real Price"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_wizard_valuation_history
msgid "Retrieve the Inventory Value"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_wizard_valuation_history
msgid "Retrieve the curent stock valuation."
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_history_serial_number
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_history_report_search
msgid "Serial Number"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.product_variant_easy_edit_view_inherit
#: model_terms:ir.ui.view,arch_db:stock_account.view_template_property_form
#, fuzzy
msgid "Set standard price"
msgstr "Precio estándar"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_history_source
msgid "Source"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product_uos_id
msgid ""
"Specify a unit of measure here if invoicing is made in another unit of "
"measure than inventory. Keep empty to use the default unit of measure."
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product_can_be_expensed
msgid "Specify whether the product can be selected in an HR expense."
msgstr ""

#. module: stock_account
#: selection:product.category,property_cost_method:0
#: selection:product.template,property_cost_method:0
msgid "Standard Price"
msgstr "Precio estándar"

#. module: stock_account
#: code:addons/stock_account/product.py:138
#: code:addons/stock_account/product.py:145
#: code:addons/stock_account/product.py:199
#: code:addons/stock_account/product.py:205
#, python-format
msgid "Standard Price changed"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product_property_cost_method
#: model:ir.model.fields,help:stock_account.field_product_template_property_cost_method
msgid ""
"Standard Price: The cost price is manually updated at the end of a specific "
"period (usually once a year).\n"
"                    Average Price: The cost price is recomputed at each "
"incoming shipment and used for the product valuation.\n"
"                    Real Price: The cost price displayed is the price of the "
"last outgoing product (will be use in case of inventory loss for example)."
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category_property_cost_method
msgid ""
"Standard Price: The cost price is manually updated at the end of a specific "
"period (usually once a year).\n"
"Average Price: The cost price is recomputed at each incoming shipment and "
"used for the product valuation.\n"
"Real Price: The cost price displayed is the price of the last outgoing "
"product (will be used in case of inventory loss for example)."
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category_property_stock_account_input_categ_id
#: model:ir.model.fields,field_description:stock_account.field_product_product_property_stock_account_input
#: model:ir.model.fields,field_description:stock_account.field_product_template_property_stock_account_input
msgid "Stock Input Account"
msgstr ""

#. module: stock_account
#: code:addons/stock_account/stock_account.py:464
#: model:ir.model.fields,field_description:stock_account.field_product_category_property_stock_journal
#, python-format
msgid "Stock Journal"
msgstr ""

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_move
#: model:ir.model.fields,field_description:stock_account.field_stock_history_move_id
msgid "Stock Move"
msgstr "Movimiento stock"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category_property_stock_account_output_categ_id
#: model:ir.model.fields,field_description:stock_account.field_product_product_property_stock_account_output
#: model:ir.model.fields,field_description:stock_account.field_product_template_property_stock_account_output
msgid "Stock Output Account"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category_property_stock_valuation_account_id
msgid "Stock Valuation Account"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_location_valuation_in_account_id
msgid "Stock Valuation Account (Incoming)"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_location_valuation_out_account_id
msgid "Stock Valuation Account (Outgoing)"
msgstr ""

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_history.py:31
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_history_report_graph
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_history_report_pivot
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_history_report_search
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_history_report_tree
#, python-format
msgid "Stock Value At Date"
msgstr ""

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_chart_template
msgid "Templates for Account Chart"
msgstr ""

#. module: stock_account
#: code:addons/stock_account/stock_account.py:306
#, python-format
msgid ""
"The found valuation amount for product %s is zero. Which means there is "
"probably a configuration error. Check the costing method and the standard "
"price"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_history_report_tree
msgid "Total Value"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product_uos_coeff
msgid "Unit of Measure -> UOS Coeff"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product_uos_id
msgid "Unit of Sale"
msgstr "Unidad de venta"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_location_valuation_in_account_id
msgid ""
"Used for real-time inventory valuation. When set on a virtual location (non "
"internal type), this account will be used to hold the value of products "
"being moved from an internal location into this location, instead of the "
"generic Stock Output Account set on the product. This has no effect for "
"internal locations."
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_location_valuation_out_account_id
msgid ""
"Used for real-time inventory valuation. When set on a virtual location (non "
"internal type), this account will be used to hold the value of products "
"being moved out of this location and into an internal location, instead of "
"the generic Stock Output Account set on the product. This has no effect for "
"internal locations."
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_history_price_unit_on_quant
msgid "Value"
msgstr "Valor"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product_alert_time
msgid ""
"When a new a Serial Number is issued, this is the number of days before an "
"alert should be notified."
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product_life_time
msgid ""
"When a new a Serial Number is issued, this is the number of days before the "
"goods may become dangerous and must not be consumed."
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product_removal_time
msgid ""
"When a new a Serial Number is issued, this is the number of days before the "
"goods should be removed from the stock."
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product_use_time
msgid ""
"When a new a Serial Number is issued, this is the number of days before the "
"goods starts deteriorating, without being dangerous yet."
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category_property_stock_account_input_categ_id
msgid ""
"When doing real-time inventory valuation, counterpart journal items for all "
"incoming stock moves will be posted in this account, unless there is a "
"specific valuation account set on the source location. This is the default "
"value for all products in this category. It can also directly be set on each "
"product"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product_property_stock_account_input
#: model:ir.model.fields,help:stock_account.field_product_template_property_stock_account_input
msgid ""
"When doing real-time inventory valuation, counterpart journal items for all "
"incoming stock moves will be posted in this account, unless there is a "
"specific valuation account set on the source location. When not set on the "
"product, the one from the product category is used."
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category_property_stock_account_output_categ_id
msgid ""
"When doing real-time inventory valuation, counterpart journal items for all "
"outgoing stock moves will be posted in this account, unless there is a "
"specific valuation account set on the destination location. This is the "
"default value for all products in this category. It can also directly be set "
"on each product"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product_property_stock_account_output
#: model:ir.model.fields,help:stock_account.field_product_template_property_stock_account_output
msgid ""
"When doing real-time inventory valuation, counterpart journal items for all "
"outgoing stock moves will be posted in this account, unless there is a "
"specific valuation account set on the destination location. When not set on "
"the product, the one from the product category is used."
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category_property_stock_journal
msgid ""
"When doing real-time inventory valuation, this is the Accounting Journal in "
"which entries will be automatically posted when stock moves are processed."
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category_property_stock_valuation_account_id
msgid ""
"When real-time inventory valuation is enabled on a product, this account "
"will hold the current value of the products."
msgstr ""

#. module: stock_account
#: model:ir.model,name:stock_account.model_wizard_valuation_history
msgid "Wizard that opens the stock valuation history table"
msgstr ""

#. module: stock_account
#: code:addons/stock_account/stock_account.py:276
#, python-format
msgid ""
"You don't have any stock journal defined on your product category, check if "
"you have installed a chart of accounts"
msgstr ""

#. module: stock_account
#: code:addons/stock_account/stock_account.py:282
#, python-format
msgid ""
"You don't have any stock valuation account defined on your product category. "
"You must define one before processing this operation."
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_change_standard_price
msgid "_Apply"
msgstr ""

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_config_settings
msgid "stock.config.settings"
msgstr ""

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_history
msgid "stock.history"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product_cost_method
#: model:ir.model.fields,field_description:stock_account.field_product_product_valuation
#: model:ir.model.fields,field_description:stock_account.field_product_template_cost_method
#: model:ir.model.fields,field_description:stock_account.field_product_template_valuation
msgid "unknown"
msgstr "desconocido"
