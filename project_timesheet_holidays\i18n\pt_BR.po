# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_timesheet_holidays
# 
# Translators:
# <PERSON>, 2021
# <PERSON>, 2021
# <PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: Layna Nascimento, 2023\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_account_analytic_line
msgid "Analytic Line"
msgstr "Linha Analítica"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave__timesheet_ids
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_resource_calendar_leaves__timesheet_ids
msgid "Analytic Lines"
msgstr "Linhas Analíticas"

#. module: project_timesheet_holidays
#: code:addons/project_timesheet_holidays/models/hr_holidays.py:0
#, python-format
msgid ""
"Both the internal project and task are required to generate a timesheet for "
"the time off %s. If you don't want a timesheet, you should leave the "
"internal project and task empty."
msgstr ""
"Tanto o projeto interno quanto a tarefa são necessários para gerar uma "
"planilha de horas para o tempo de folga %s. Se você não quiser uma planilha "
"de horas, você deve deixar o projeto interno e a tarefa vazios."

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_res_config_settings
msgid "Config Settings"
msgstr "Definições de Configuração"

#. module: project_timesheet_holidays
#: model:ir.model.fields,help:project_timesheet_holidays.field_res_config_settings__internal_project_id
msgid "Default project value for timesheet generated from time off type."
msgstr ""
"Valor padrão do projeto para a folha de tempos gerada a partir do tipo tempo"
" livre."

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_hr_employee
msgid "Employee"
msgstr "Funcionário"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave_type__timesheet_generate
msgid "Generate Timesheet"
msgstr "Gerar planilha de horas"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_account_analytic_line__global_leave_id
msgid "Global Time Off"
msgstr "Folga Global"

#. module: project_timesheet_holidays
#: model:ir.model.fields,help:project_timesheet_holidays.field_hr_leave_type__timesheet_generate
msgid ""
"If checked, when validating a time off, timesheet will be generated in the "
"Vacation Project of the company."
msgstr ""
"Se marcado, ao validar um tempo de folga, será gerada uma planilha de horas "
"no projeto de Férias da empresa."

#. module: project_timesheet_holidays
#: code:addons/project_timesheet_holidays/models/res_company.py:0
#: code:addons/project_timesheet_holidays/models/res_company.py:0
#, python-format
msgid "Internal"
msgstr "Interno"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_res_config_settings__internal_project_id
msgid "Internal Project"
msgstr "Projeto Interno"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_account_analytic_line__holiday_id
msgid "Leave Request"
msgstr "Pedido de Folga"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave_type__timesheet_project_id
#: model_terms:ir.ui.view,arch_db:project_timesheet_holidays.res_config_settings_view_form
msgid "Project"
msgstr "Projeto"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_resource_calendar_leaves
msgid "Resource Time Off Detail"
msgstr "Detalhe de Folga de Recurso"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave_type__timesheet_task_id
#: model_terms:ir.ui.view,arch_db:project_timesheet_holidays.res_config_settings_view_form
msgid "Task"
msgstr "Tarefa"

#. module: project_timesheet_holidays
#: model:ir.model.fields,help:project_timesheet_holidays.field_hr_leave_type__timesheet_project_id
msgid ""
"The project will contain the timesheet generated when a time off is "
"validated."
msgstr ""
"O projeto conterá a folha de tempos gerada quando um tempo livre for "
"validado."

#. module: project_timesheet_holidays
#: code:addons/project_timesheet_holidays/models/res_company.py:0
#: code:addons/project_timesheet_holidays/models/res_company.py:0
#: model:ir.model,name:project_timesheet_holidays.model_hr_leave
#, python-format
msgid "Time Off"
msgstr "Folga"

#. module: project_timesheet_holidays
#: code:addons/project_timesheet_holidays/models/hr_holidays.py:0
#: code:addons/project_timesheet_holidays/models/resource_calendar_leaves.py:0
#, python-format
msgid "Time Off (%s/%s)"
msgstr "Folga (%s/%s)"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_res_company__leave_timesheet_task_id
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_res_config_settings__leave_timesheet_task_id
msgid "Time Off Task"
msgstr "Tempo fora de serviço"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_hr_leave_type
msgid "Time Off Type"
msgstr "Tipo de Folga"

#. module: project_timesheet_holidays
#: model_terms:ir.ui.view,arch_db:project_timesheet_holidays.hr_holiday_status_view_form_inherit
msgid "Timesheets"
msgstr "Planilhas de Horas"

#. module: project_timesheet_holidays
#: code:addons/project_timesheet_holidays/models/account_analytic.py:0
#, python-format
msgid ""
"You cannot delete timesheets linked to time off. Please, cancel the time off"
" instead."
msgstr ""
"Não é possível excluir planilhas de horas vinculadas a folgas. Em vez disso,"
" cancele a folga."
