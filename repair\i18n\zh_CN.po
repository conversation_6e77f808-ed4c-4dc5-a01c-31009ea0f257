# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* repair
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# xu aaron, 2021
# <AUTHOR> <EMAIL>, 2022
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> CHEN <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:18+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON><PERSON> CHEN <<EMAIL>>, 2023\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: repair
#: model:ir.actions.report,print_report_name:repair.action_report_repair_order
msgid ""
"(\n"
"                object.state == 'draft' and 'Repair Quotation - %s' % (object.name) or\n"
"                'Repair Order - %s' % (object.name))"
msgstr ""
"(\n"
"                object.state == 'draft' and '维修报价 - %s' % (object.name) or\n"
"                '维修订单 - %s' % (object.name))"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "(<i>Remove</i>)"
msgstr "(<i>移除</i>)"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "(update)"
msgstr "（更新）"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__state
msgid ""
"* The 'Draft' status is used when a user is encoding a new and unconfirmed repair order.\n"
"* The 'Confirmed' status is used when a user confirms the repair order.\n"
"* The 'Ready to Repair' status is used to start to repairing, user can start repairing only after repair order is confirmed.\n"
"* The 'Under Repair' status is used when the repair is ongoing.\n"
"* The 'To be Invoiced' status is used to generate the invoice before or after repairing done.\n"
"* The 'Done' status is set when repairing is completed.\n"
"* The 'Cancelled' status is used when user cancel repair order."
msgstr ""
"* 草稿 \"状态是在用户对新的和未确认的维修单进行编码时使用。\n"
"* 已确认 \"状态是在用户确认维修单时使用的。\n"
"* Ready to Repair \"状态用于开始维修，用户只有在维修单被确认后才能开始维修。\n"
"* 维修中 \"状态用于维修正在进行中。\n"
"* 待开结算单 \"状态用于在维修完成之前或之后生成结算单。\n"
"* 完成 \"状态是在维修完成后设置的。\n"
"* 取消 \"状态在用户取消维修订单时使用。"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid ": Insufficient Quantity To Repair"
msgstr ": 维修数量不足"

#. module: repair
#: model:mail.template,body_html:repair.mail_template_repair_quotation
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px;font-size: 13px;\">\n"
"        Hello <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br/>\n"
"        Here is your repair order <strong t-out=\"object.name or ''\">RO/00004</strong>\n"
"        <t t-if=\"object.invoice_method != 'none'\">\n"
"            amounting in <strong><t t-out=\"format_amount(object.amount_total, object.pricelist_id.currency_id) or ''\">$ 100.00</t>.</strong><br/>\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            .<br/>\n"
"        </t>\n"
"        You can reply to this email if you have any questions.\n"
"        <br/><br/>\n"
"        Thank you,\n"
"        <t t-if=\"user.signature\">\n"
"            <br/>\n"
"            <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>"
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px;font-size: 13px;\">\n"
"        您好，<t t-out=\"object.partner_id.name or ''\">布兰登-弗里曼</t>。<br/>\n"
"        这是您的维修单<strong t-out=\"object.name or ''\">RO/00004</strong>\n"
"       <t t-if=\"object.invoice_method != 'none'\">\n"
"            金额为<strong><t t-out=\"format_amount(object.amount_total, object.pricelist_id.currency_id) or ''\">100.00美元</t></strong>。<br/>\n"
"       </t>\n"
"       <t t-else=\"\">\n"
"            .<br/>\n"
"       </t>\n"
"        如果您有任何问题，可以回复此邮件。\n"
"       <br/><br/>\n"
"        谢谢您。\n"
"        <t t-if=\"user.signature\">\n"
"            <br/>\n"
"            <t t-out=\"user.signature or ''\">--<br/>Mitchell 管理</t>\n"
"        </t>\n"
"    </p>\n"
"</div>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<i>(Add)</i>"
msgstr "<i>(添加)</i>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid ""
"<span class=\"o_stat_text\">1</span>\n"
"                                <span class=\"o_stat_text\">Invoices</span>"
msgstr ""
"<span class=\"o_stat_text\">1</span>\n"
"                                <span class=\"o_stat_text\">结算单</span>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">Repairs</span>"
msgstr "<span class=\"o_stat_text\">维修</span>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Lot/Serial Number:</strong>"
msgstr "<strong>批次/序列号:</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Operations</strong>"
msgstr "<strong>操作</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Parts</strong>"
msgstr "<strong>零件</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Printing Date:</strong>"
msgstr "<strong>打印日期:</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Product to Repair:</strong>"
msgstr "<strong>待维修产品:</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Shipping address :</strong>"
msgstr "<strong>送货地址 :</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Total Without Taxes</strong>"
msgstr "<strong>不含税总计</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Total</strong>"
msgstr "<strong>总计</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Warranty:</strong>"
msgstr "<strong>保证:</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_warn_insufficient_qty_repair_form_view
msgid "? This may lead to inconsistencies in your inventory."
msgstr "？这可能会导致库存不一致。"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_needaction
msgid "Action Needed"
msgstr "需要采取动作"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_ids
msgid "Activities"
msgstr "活动"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "活动异常勋章"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_state
msgid "Activity State"
msgstr "活动状态"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_type_icon
msgid "Activity Type Icon"
msgstr "活动类型图表"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_line__type__add
msgid "Add"
msgstr "添加"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Add internal notes."
msgstr "添加内部备注。"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Add quotation notes."
msgstr "添加报价备注。"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__invoice_method__after_repair
msgid "After Repair"
msgstr "维修后"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_attachment_count
msgid "Attachment Count"
msgstr "附件数量"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__invoice_method__b4repair
msgid "Before Repair"
msgstr "维修前"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_make_invoice
msgid "Cancel"
msgstr "取消"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Cancel Repair"
msgstr "取消维修"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_line__state__cancel
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__cancel
msgid "Cancelled"
msgstr "已取消"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__product_uom_category_id
#: model:ir.model.fields,field_description:repair.field_repair_line__product_uom_category_id
#: model:ir.model.fields,field_description:repair.field_repair_order__product_uom_category_id
msgid "Category"
msgstr "类别"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__partner_id
msgid ""
"Choose partner for whom the order will be invoiced and delivered. You can "
"find a partner by its Name, TIN, Email or Internal Reference."
msgstr "选择将为其开具结算单和交付订单的合作伙伴。您可以通过名称，TIN，EMail或内部参考找到合作伙伴。"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_tags__color
msgid "Color Index"
msgstr "颜色索引"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__company_id
#: model:ir.model.fields,field_description:repair.field_repair_line__company_id
#: model:ir.model.fields,field_description:repair.field_repair_order__company_id
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Company"
msgstr "公司"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_menu_config
msgid "Configuration"
msgstr "配置"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Confirm Repair"
msgstr "确认维修"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_line__state__confirmed
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__confirmed
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Confirmed"
msgstr "已确认"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_fee__product_uom_category_id
#: model:ir.model.fields,help:repair.field_repair_line__product_uom_category_id
#: model:ir.model.fields,help:repair.field_repair_order__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr "计量单位转换只能基于转换比例对同一个类别进行转换。"

#. module: repair
#: code:addons/repair/models/repair.py:0 code:addons/repair/models/repair.py:0
#, python-format
msgid ""
"Couldn't find a pricelist line matching this product and quantity.\n"
"You have to change either the product, the quantity or the pricelist."
msgstr ""
"找不到与此产品和数额匹配的价格表。\n"
"您必须修改产品、数量或者价格表。"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_make_invoice
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Create Invoice"
msgstr "创建结算单"

#. module: repair
#: model:ir.model,name:repair.model_repair_order_make_invoice
msgid "Create Mass Invoice (repair)"
msgstr "创建批量结算单 (修理)"

#. module: repair
#: model_terms:ir.actions.act_window,help:repair.action_repair_order_tag
msgid "Create a new tag"
msgstr "创建一个新标签"

#. module: repair
#: model:ir.actions.act_window,name:repair.act_repair_invoice
#: model_terms:ir.ui.view,arch_db:repair.view_make_invoice
msgid "Create invoices"
msgstr "创建结算单"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__create_uid
#: model:ir.model.fields,field_description:repair.field_repair_line__create_uid
#: model:ir.model.fields,field_description:repair.field_repair_order__create_uid
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__create_uid
#: model:ir.model.fields,field_description:repair.field_repair_tags__create_uid
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__create_uid
msgid "Created by"
msgstr "创建人"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__create_date
#: model:ir.model.fields,field_description:repair.field_repair_line__create_date
#: model:ir.model.fields,field_description:repair.field_repair_order__create_date
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__create_date
#: model:ir.model.fields,field_description:repair.field_repair_tags__create_date
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__create_date
msgid "Created on"
msgstr "创建时间"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__currency_id
#: model:ir.model.fields,field_description:repair.field_repair_line__currency_id
#: model:ir.model.fields,field_description:repair.field_repair_order__currency_id
msgid "Currency"
msgstr "币种"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__partner_id
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Customer"
msgstr "客户"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__default_address_id
msgid "Default Address"
msgstr "默认地址"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__address_id
msgid "Delivery Address"
msgstr "送货地址"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__name
#: model:ir.model.fields,field_description:repair.field_repair_line__name
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Description"
msgstr "说明"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__location_dest_id
msgid "Dest. Location"
msgstr "目的地的位置"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__display_name
#: model:ir.model.fields,field_description:repair.field_repair_line__display_name
#: model:ir.model.fields,field_description:repair.field_repair_order__display_name
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__display_name
#: model:ir.model.fields,field_description:repair.field_repair_tags__display_name
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_warn_insufficient_qty_repair_form_view
msgid "Do you confirm you want to repair"
msgstr "您确认要维修吗"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_make_invoice
msgid "Do you really want to create the invoice(s)?"
msgstr "您真的要创建结算单吗 ？"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_line__state__done
msgid "Done"
msgstr "完成"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_line__state__draft
msgid "Draft"
msgstr "草稿"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid ""
"Draft invoices for this order will be cancelled. Do you confirm the action?"
msgstr "该订单的结算单草稿将被取消。 您确认吗？"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "End Repair"
msgstr "结束维修"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_line__tracking
#: model:ir.model.fields,help:repair.field_repair_order__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "确保可追溯到仓库中的产品。"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Extra Info"
msgstr "额外信息"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Fees"
msgstr "费用"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_follower_ids
msgid "Followers"
msgstr "关注者"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_partner_ids
msgid "Followers (Partners)"
msgstr "关注者(业务伙伴)"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "完美的图标，例如FA任务"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Future Activities"
msgstr "未来活动"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Group By"
msgstr "分组"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__group
msgid "Group by partner invoice address"
msgstr "分组，按业务伙伴结算单地址"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__has_message
msgid "Has Message"
msgstr "有信息"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "History"
msgstr "历史"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__id
#: model:ir.model.fields,field_description:repair.field_repair_line__id
#: model:ir.model.fields,field_description:repair.field_repair_order__id
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__id
#: model:ir.model.fields,field_description:repair.field_repair_tags__id
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__id
msgid "ID"
msgstr "ID"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_exception_icon
msgid "Icon"
msgstr "图标"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "表示异常活动的图标。"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_needaction
#: model:ir.model.fields,help:repair.field_repair_order__message_unread
msgid "If checked, new messages require your attention."
msgstr "确认后, 出现提示消息."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_has_error
#: model:ir.model.fields,help:repair.field_repair_order__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "如果勾选此项， 某些消息将会产生传递错误。"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__priority
msgid "Important repair order"
msgstr "重要的维修顺序"

#. module: repair
#: model_terms:ir.actions.act_window,help:repair.action_repair_order_tree
msgid ""
"In a repair order, you can detail the components you remove,\n"
"                add or replace and record the time you spent on the different\n"
"                operations."
msgstr ""
"在维修订单中，您可以非常详细定义您要移除和添加或者替代的组件\n"
"并且可以记录您在不同操作上花费的时间"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__internal_notes
msgid "Internal Notes"
msgstr "内部备注"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__move_id
msgid "Inventory Move"
msgstr "库存移动"

#. module: repair
#: model:ir.actions.act_window,name:repair.action_repair_move_lines
msgid "Inventory Moves"
msgstr "库存移动"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__invoice_id
msgid "Invoice"
msgstr "结算单"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__invoice_line_id
#: model:ir.model.fields,field_description:repair.field_repair_line__invoice_line_id
msgid "Invoice Line"
msgstr "结算单明细"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__invoice_method
msgid "Invoice Method"
msgstr "开票方式"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__invoice_state
msgid "Invoice State"
msgstr "结算单状态"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Invoice address:"
msgstr "结算单寄送地址:"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Invoice and shipping address:"
msgstr "结算单和送货地址:"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Invoice created"
msgstr "开票日期。"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__invoiced
#: model:ir.model.fields,field_description:repair.field_repair_line__invoiced
#: model:ir.model.fields,field_description:repair.field_repair_order__invoiced
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Invoiced"
msgstr "已结算"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__partner_invoice_id
msgid "Invoicing Address"
msgstr "开票地址"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_is_follower
msgid "Is Follower"
msgstr "关注者"

#. module: repair
#: model:ir.model,name:repair.model_account_move
msgid "Journal Entry"
msgstr "会计凭证"

#. module: repair
#: model:ir.model,name:repair.model_account_move_line
msgid "Journal Item"
msgstr "会计分录"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee____last_update
#: model:ir.model.fields,field_description:repair.field_repair_line____last_update
#: model:ir.model.fields,field_description:repair.field_repair_order____last_update
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice____last_update
#: model:ir.model.fields,field_description:repair.field_repair_tags____last_update
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair____last_update
msgid "Last Modified on"
msgstr "最后修改时间"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__write_uid
#: model:ir.model.fields,field_description:repair.field_repair_line__write_uid
#: model:ir.model.fields,field_description:repair.field_repair_order__write_uid
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__write_uid
#: model:ir.model.fields,field_description:repair.field_repair_tags__write_uid
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__write_date
#: model:ir.model.fields,field_description:repair.field_repair_line__write_date
#: model:ir.model.fields,field_description:repair.field_repair_order__write_date
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__write_date
#: model:ir.model.fields,field_description:repair.field_repair_tags__write_date
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__write_date
msgid "Last Updated on"
msgstr "最后更新时间"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Late Activities"
msgstr "最近的活动"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__location_id
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__location_id
msgid "Location"
msgstr "位置"

#. module: repair
#: model:ir.model,name:repair.model_stock_production_lot
#: model:ir.model.fields,field_description:repair.field_repair_line__lot_id
#: model:ir.model.fields,field_description:repair.field_repair_order__lot_id
msgid "Lot/Serial"
msgstr "批次/序列号"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_main_attachment_id
msgid "Main Attachment"
msgstr "主要附件"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_has_error
msgid "Message Delivery error"
msgstr "消息传递错误"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_ids
msgid "Messages"
msgstr "消息"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__move_id
msgid "Move"
msgstr "凭证"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__move_id
msgid "Move created by the repair order"
msgstr "根据维修订单创建的库存移动"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "我的活动截止时间"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "下一个活动日历事件"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "下一活动截止日期"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_summary
msgid "Next Activity Summary"
msgstr "下一活动摘要"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_type_id
msgid "Next Activity Type"
msgstr "下一活动类型"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__invoice_method__none
msgid "No Invoice"
msgstr "不开票"

#. module: repair
#: code:addons/repair/models/repair.py:0 code:addons/repair/models/repair.py:0
#, python-format
msgid "No account defined for product \"%s\"."
msgstr "产品 \"%s\" 没有指定科目"

#. module: repair
#: code:addons/repair/models/repair.py:0 code:addons/repair/models/repair.py:0
#, python-format
msgid "No pricelist found."
msgstr "没有找到价格表."

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "No product defined on fees."
msgstr "没有定义费用产品."

#. module: repair
#: model_terms:ir.actions.act_window,help:repair.action_repair_order_tree
msgid "No repair order found. Let's create one!"
msgstr "还没有维修订单， 我们先创建一个"

#. module: repair
#: code:addons/repair/models/repair.py:0 code:addons/repair/models/repair.py:0
#, python-format
msgid "No valid pricelist line found."
msgstr "没有找到有效的价格表。"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__priority__0
msgid "Normal"
msgstr "正常"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_needaction_counter
msgid "Number of Actions"
msgstr "动作数量"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_has_error_counter
msgid "Number of errors"
msgstr "错误数"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "需要采取动作消息数量"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "发送错误的消息数量"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_unread_counter
msgid "Number of unread messages"
msgstr "未读消息数量"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Only draft repairs can be confirmed."
msgstr "只能确认修理草案。"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__fees_lines
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Operations"
msgstr "作业"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__operations
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Parts"
msgstr "零件"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Please define an accounting sales journal for the company %s (%s)."
msgstr "请为公司%s（%s）定义会计销售日记账。"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Price"
msgstr "价格"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__pricelist_id
msgid "Pricelist"
msgstr "价格表"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__pricelist_id
msgid "Pricelist of the selected partner."
msgstr "所选伙伴的价格列表"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Print Quotation"
msgstr "打印报价"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__priority
msgid "Priority"
msgstr "优先级"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__product_id
#: model:ir.model.fields,field_description:repair.field_repair_line__product_id
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__product_id
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Product"
msgstr "产品"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Product Moves"
msgstr "产品移动"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_qty
msgid "Product Quantity"
msgstr "产品数量"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__tracking
#: model:ir.model.fields,field_description:repair.field_repair_order__tracking
msgid "Product Tracking"
msgstr "产品追溯"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__product_uom
#: model:ir.model.fields,field_description:repair.field_repair_line__product_uom
#: model:ir.model.fields,field_description:repair.field_repair_order__product_uom
msgid "Product Unit of Measure"
msgstr "产品计量单位"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_id
msgid "Product to Repair"
msgstr "待维修产品"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__lot_id
msgid "Products repaired are all belonging to this lot"
msgstr "维修的产品全属于此批次"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__quant_ids
msgid "Quant"
msgstr "量化"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__product_uom_qty
#: model:ir.model.fields,field_description:repair.field_repair_line__product_uom_qty
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__quantity
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_tree
msgid "Quantity"
msgstr "数量"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__draft
msgid "Quotation"
msgstr "报价"

#. module: repair
#: model:ir.actions.report,name:repair.action_report_repair_order
msgid "Quotation / Order"
msgstr "报价/订单"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__quotation_notes
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Quotation Notes"
msgstr "报价说明"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Quotations"
msgstr "报价"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Ready To Repair"
msgstr "准备维修"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__ready
msgid "Ready to Repair"
msgstr "准备维修"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_line__type__remove
msgid "Remove"
msgstr "移除"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_account_bank_statement_line__repair_ids
#: model:ir.model.fields,field_description:repair.field_account_move__repair_ids
#: model:ir.model.fields,field_description:repair.field_account_payment__repair_ids
#: model:ir.model.fields,field_description:repair.field_stock_move__repair_id
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__repair_id
msgid "Repair"
msgstr "维修"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__description
msgid "Repair Description"
msgstr "修复说明"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_account_move_line__repair_fee_ids
msgid "Repair Fee"
msgstr "维修费用"

#. module: repair
#: model:ir.model,name:repair.model_repair_fee
msgid "Repair Fees"
msgstr "修理费"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_account_move_line__repair_line_ids
msgid "Repair Line"
msgstr "维修明细"

#. module: repair
#: model:ir.model,name:repair.model_repair_line
msgid "Repair Line (parts)"
msgstr "修理明细行(零件)"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Repair Notes"
msgstr "维修说明"

#. module: repair
#: model:ir.model,name:repair.model_repair_order
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Repair Order"
msgstr "维修单"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Repair Order #:"
msgstr "维修订单行"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__repair_id
#: model:ir.model.fields,field_description:repair.field_repair_line__repair_id
msgid "Repair Order Reference"
msgstr "维修单编＃"

#. module: repair
#: model:ir.actions.act_window,name:repair.action_repair_order_graph
#: model:ir.actions.act_window,name:repair.action_repair_order_tree
#: model:ir.model.fields,field_description:repair.field_stock_production_lot__repair_order_ids
#: model_terms:ir.ui.view,arch_db:repair.stock_production_lot_view_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_graph
#: model_terms:ir.ui.view,arch_db:repair.view_repair_pivot
msgid "Repair Orders"
msgstr "维修单"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_menu_tag
msgid "Repair Orders Tags"
msgstr "维修订单标签"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Repair Quotation #:"
msgstr "维修报价＃"

#. module: repair
#: model:mail.template,name:repair.mail_template_repair_quotation
msgid "Repair Quotation: Send by email"
msgstr "维修报价:通过电子邮件发送"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__name
msgid "Repair Reference"
msgstr "维修参照"

#. module: repair
#: model:product.product,name:repair.product_service_order_repair
#: model:product.template,name:repair.product_service_order_repair_product_template
msgid "Repair Services"
msgstr "维修服务"

#. module: repair
#: model:ir.model,name:repair.model_repair_tags
#: model_terms:ir.ui.view,arch_db:repair.view_repair_tag_form
msgid "Repair Tags"
msgstr "维修标签"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Repair must be canceled in order to reset it to draft."
msgstr "必须取消维修单，以便将其重置为草稿。"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Repair must be confirmed before starting reparation."
msgstr "维修单必须确认后才能进行维修。"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Repair must be repaired in order to make the product moves."
msgstr "维修单必须维修后，才能移动"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Repair must be under repair in order to end reparation."
msgstr "维修单必须维修，以便结束赔偿。"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_production_lot__repair_order_count
msgid "Repair order count"
msgstr "维修订单数量"

#. module: repair
#: code:addons/repair/models/stock_production_lot.py:0
#, python-format
msgid "Repair orders of %s"
msgstr "%s的维修订单"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__repaired
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__done
msgid "Repaired"
msgstr "已维修"

#. module: repair
#: model:ir.ui.menu,name:repair.menu_repair_order
#: model:ir.ui.menu,name:repair.repair_menu
msgid "Repairs"
msgstr "维修"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_tree
msgid "Repairs order"
msgstr "维修单"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_menu_reporting
msgid "Reporting"
msgstr "报告"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__user_id
msgid "Responsible"
msgstr "负责人"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_user_id
msgid "Responsible User"
msgstr "负责用户"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_has_sms_error
msgid "SMS Delivery error"
msgstr "短信发送错误"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__sale_order_id
msgid "Sale Order"
msgstr "销售订单"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__sale_order_id
msgid "Sale Order from which the product to be repaired comes from."
msgstr "来自的销售订单要维修的产品。"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__schedule_date
msgid "Scheduled Date"
msgstr "安排的日期"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Search Repair Orders"
msgstr "搜索维修单"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__invoice_method
msgid ""
"Selecting 'Before Repair' or 'After Repair' will allow you to generate "
"invoice before or after the repair is done respectively. 'No invoice' means "
"you don't want to generate invoice for this repair order."
msgstr "选择“维修前”或“维修后”选项来确定在维修前后生产结算单。“无结算单”则代表此维修单无需生成结算单。"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Send Quotation"
msgstr "发送报价"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Serial number is required for operation lines with products: %s"
msgstr "产品需要序列号的维修行：%s"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Set to Draft"
msgstr "设为草稿"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Show all records which has next action date is before today"
msgstr "显示所有的在今天之前的下一个动作日期的记录"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__location_id
msgid "Source Location"
msgstr "源位置"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Start Repair"
msgstr "开始维修"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__state
#: model:ir.model.fields,field_description:repair.field_repair_order__state
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Status"
msgstr "状态"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"基于活动的状态 \n"
" 逾期：已经超过截止日期 \n"
" 现今：活动日期是当天 \n"
" 计划：未来活动。"

#. module: repair
#: model:ir.model,name:repair.model_stock_move
msgid "Stock Move"
msgstr "库存移动"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__price_subtotal
#: model:ir.model.fields,field_description:repair.field_repair_line__price_subtotal
msgid "Subtotal"
msgstr "小计"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_tags__name
msgid "Tag Name"
msgstr "标签名"

#. module: repair
#: model:ir.model.constraint,message:repair.constraint_repair_tags_name_uniq
msgid "Tag name already exists!"
msgstr "标签名称已存在！"

#. module: repair
#: model:ir.actions.act_window,name:repair.action_repair_order_tag
#: model:ir.model.fields,field_description:repair.field_repair_order__tag_ids
#: model_terms:ir.ui.view,arch_db:repair.view_repair_tag_search
#: model_terms:ir.ui.view,arch_db:repair.view_repair_tag_tree
msgid "Tags"
msgstr "标签"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Tax"
msgstr "税金"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__tax_id
#: model:ir.model.fields,field_description:repair.field_repair_line__tax_id
#: model:ir.model.fields,field_description:repair.field_repair_order__amount_tax
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Taxes"
msgstr "税"

#. module: repair
#: model:ir.model.constraint,message:repair.constraint_repair_order_name
msgid "The name of the Repair Order must be unique!"
msgstr "维修单的名必须唯一！"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid ""
"The product unit of measure you chose has a different category than the "
"product unit of measure."
msgstr "您选择的产品计量单位跟产品的计量单位类别不同。"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_line__state
msgid ""
"The status of a repair line is set automatically to the one of the linked "
"repair order."
msgstr "修复线的状态自动设置为链接修复顺序之一。"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__location_id
msgid "This is the location where the product to repair is located."
msgstr "这是待维修的产品存放的位置或位置"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__2binvoiced
msgid "To be Invoiced"
msgstr "要开结算单的"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Today Activities"
msgstr "今天的活动"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__price_total
#: model:ir.model.fields,field_description:repair.field_repair_line__price_total
#: model:ir.model.fields,field_description:repair.field_repair_order__amount_total
msgid "Total"
msgstr "合计"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Total amount"
msgstr "合计金额"

#. module: repair
#: model:ir.model,name:repair.model_stock_traceability_report
msgid "Traceability Report"
msgstr "追溯报告"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__type
msgid "Type"
msgstr "类型"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "记录的异常活动类型。"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__under_repair
msgid "Under Repair"
msgstr "维修中"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__price_unit
#: model:ir.model.fields,field_description:repair.field_repair_line__price_unit
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Unit Price"
msgstr "单价"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__product_uom_name
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_tree
msgid "Unit of Measure"
msgstr "计量单位"

#. module: repair
#: model:product.product,uom_name:repair.product_service_order_repair
#: model:product.template,uom_name:repair.product_service_order_repair_product_template
msgid "Units"
msgstr "件"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_unread
msgid "Unread Messages"
msgstr "未读消息"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_unread_counter
msgid "Unread Messages Counter"
msgstr "未读消息数"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__amount_untaxed
msgid "Untaxed Amount"
msgstr "未税金额"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Untaxed amount"
msgstr "未税金额"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "UoM"
msgstr "计量单位"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__priority__1
msgid "Urgent"
msgstr "紧急"

#. module: repair
#: model:ir.model,name:repair.model_stock_warn_insufficient_qty_repair
msgid "Warn Insufficient Repair Quantity"
msgstr "警告维修数量不足"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Warning"
msgstr "警告"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__guarantee_limit
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Warranty Expiration"
msgstr "质保到期"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__website_message_ids
msgid "Website Messages"
msgstr "网站消息"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__website_message_ids
msgid "Website communication history"
msgstr "网上沟通记录"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid ""
"You can not delete a repair order once it has been confirmed. You must first"
" cancel it."
msgstr "确认维修订单后，您将无法删除它。 您必须先取消它。"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid ""
"You can not delete a repair order which is linked to an invoice which has "
"been posted once."
msgstr "您不能删除链接到已过帐结算单的维修单。"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "You can not enter negative quantities."
msgstr "不能输入负数数量"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "You cannot cancel a completed repair order."
msgstr "不能删除已完成的维修订单。"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "You cannot delete a completed repair order."
msgstr "不能删除已完成的维修订单。"

#. module: repair
#: code:addons/repair/models/repair.py:0 code:addons/repair/models/repair.py:0
#, python-format
msgid ""
"You have to select a pricelist in the Repair form !\n"
" Please set one before choosing a product."
msgstr "您必须在维修表单中选择一个价目表！ 在选择产品之前请设置一个。"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "You have to select an invoice address in the repair form."
msgstr "您必须在维修单上选择结算单地址。"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_warn_insufficient_qty_repair_form_view
msgid "from location"
msgstr "来自位置"

#. module: repair
#: model:mail.template,report_name:repair.mail_template_repair_quotation
msgid "{{ (object.name or '').replace('/','_') }}"
msgstr "{{ (object.name or '').replace('/','_') }}"

#. module: repair
#: model:mail.template,subject:repair.mail_template_repair_quotation
msgid ""
"{{ object.partner_id.name }} Repair Orders (Ref {{ object.name or 'n/a' }})"
msgstr ""
"{{ object.partner_id.name }} 维修单（参考 {{ object.name 或 'n/a' }}）。维修订单（参考 {{ "
"object.name or 'n/a' }})"
