# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* coupon
# 
# Translators:
# danimaribe<PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# mariana rod<PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:21+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: coupon
#: code:addons/coupon/models/coupon_reward.py:0
#, python-format
msgid "%(amount)s %(currency)s discount on total amount"
msgstr "%(amount)s %(currency)s desconto no montante total"

#. module: coupon
#: code:addons/coupon/models/coupon_reward.py:0
#, python-format
msgid "%(percentage)s%% discount on %(product_name)s"
msgstr "%(percentage)s%% desconto em %(product_name)s"

#. module: coupon
#: code:addons/coupon/models/coupon_reward.py:0
#, python-format
msgid "%s%% discount on cheapest product"
msgstr "%s%% desconto no produto mais barato"

#. module: coupon
#: code:addons/coupon/models/coupon_reward.py:0
#, python-format
msgid "%s%% discount on products"
msgstr "%s%% desconto em produtos"

#. module: coupon
#: code:addons/coupon/models/coupon_reward.py:0
#, python-format
msgid "%s%% discount on total amount"
msgstr "%s%% desconto no montante total"

#. module: coupon
#: code:addons/coupon/wizard/coupon_generate.py:0
#, python-format
msgid "%s, a coupon has been generated for you"
msgstr "%s, um cupom foi gerado para você"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "*Valid for following products:"
msgstr "* Válido para os seguintes produtos:"

#. module: coupon
#: model:coupon.program,name:coupon.10_percent_coupon
msgid "10% Discount"
msgstr "10% Desconto"

#. module: coupon
#: model:product.product,name:coupon.product_product_10_percent_discount
#: model:product.template,name:coupon.product_product_10_percent_discount_product_template
msgid "10.0% discount on total amount"
msgstr "10% de desconto no montante total"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid ""
"<span attrs=\"{'invisible': [('discount_type', '!=', "
"'percentage')],'required': [('discount_type', '=', 'percentage')]}\" "
"class=\"oe_inline\">%</span>"
msgstr ""

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_coupon_program_form
msgid ""
"<span class=\"o_form_label oe_inline\"> Days</span> <span "
"class=\"oe_grey\">if 0, infinite use</span>"
msgstr ""
"<span class=\"o_form_label oe_inline\"> Dias</span> <span "
"class=\"oe_grey\">se 0, uso infinito</span>"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "<span class=\"oe_grey\"> if 0, no limit</span>"
msgstr ""

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_promo_program_form
msgid ""
"<span> Days</span>\n"
"                    <span class=\"oe_grey\"> if 0, coupon doesn't expire</span>"
msgstr ""

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_promo_program_form
msgid ""
"<span> Orders</span>\n"
"                    <span class=\"oe_grey\"> if 0, infinite use</span>"
msgstr ""
"<span>Pedidos</span>\n"
"<span class=\"oe_grey\">se 0, uso infinito</span>"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "<span>Minimum purchase of</span>"
msgstr "<span>Compra mínima de</span>"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "<span>Valid for purchase above</span>"
msgstr "<span>Válido para compra acima de</span>"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "<span>products</span>"
msgstr "<span>produtos</span>"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid ""
"<strong style=\"font-size: 55px; color: #875A7B\">get free shipping</strong>"
msgstr ""
"<strong style=\"font-size: 55px; color: #875A7B\">obter frete "
"gratis</strong>"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.view_coupon_program_kanban
msgid "<strong>Active</strong>"
msgstr "<strong>Ativo</strong>"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.view_coupon_program_kanban
msgid "<strong>Coupons</strong>"
msgstr "<strong>Cupons</strong>"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__active
msgid "A program is available for the customers when active"
msgstr "Um programa está disponível para os clientes quando ativo"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__promo_code
msgid ""
"A promotion code is a code that is associated with a marketing discount. For"
" example, a retailer might tell frequent customers to enter the promotion "
"code 'THX001' to receive a 10%% discount on their whole order."
msgstr ""
"Um código de promoção é um código que está associado a um desconto de "
"marketing. Por exemplo, um varejista pode dizer aos clientes freqüentes que "
"digitem o código de promoção 'THX001' para receber um desconto de 10%% em "
"toda a ordem."

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__program_type
msgid ""
"A promotional program can be either a limited promotional offer without code (applied automatically)\n"
"                or with a code (displayed on a magazine for example) that may generate a discount on the current\n"
"                order or create a coupon for a next order.\n"
"\n"
"                A coupon program generates coupons with a code that can be used to generate a discount on the current\n"
"                order or create a coupon for a next order."
msgstr ""
"Um programa promocional pode ser uma oferta promocional limitada sem código (aplicada automaticamente)\n"
"ou com um código (exibido em uma revista, por exemplo) que pode gerar um desconto na corrente\n"
"ordene ou crie um cupom para um próximo pedido.\n"
"\n"
"Um programa de cupom gera cupons com um código que pode ser usado para gerar um desconto na corrente\n"
"ordene ou crie um cupom para um próximo pedido."

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__active
msgid "Active"
msgstr "Ativo"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__promo_applicability
msgid "Applicability"
msgstr "Aplicações"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Apply Discount"
msgstr "Aplicar Desconto"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_program__promo_applicability__on_current_order
msgid "Apply On Current Order"
msgstr "Aplicar na ordem atual"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_promo_program_form
msgid "Apply on First"
msgstr "Aplicar em Primeiro"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_promo_program_search
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_search
msgid "Archived"
msgstr "Arquivado"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_program__promo_code_usage__no_code_needed
msgid "Automatically Applied"
msgstr "Aplicado automaticamente"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__promo_code_usage
msgid ""
"Automatically Applied - No code is required, if the program rules are met, the reward is applied (Except the global discount or the free shipping rewards which are not cumulative)\n"
"Use a code - If the program rules are met, a valid code is mandatory for the reward to be applied\n"
msgstr ""
"Aplicado automaticamente - Nenhum código é necessário, se as regras do programa forem cumpridas, a recompensa é aplicada (exceto o desconto global ou as recompensas de frete grátis que não são cumulativas)\n"
"Use um código - Se as regras do programa forem atendidas, um código válido é obrigatório para que a recompensa seja aplicada\n"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__rule_partners_domain
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__rule_partners_domain
msgid "Based on Customers"
msgstr "Baseado em Clientes"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__rule_products_domain
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__rule_products_domain
msgid "Based on Products"
msgstr "Baseado em Produtos"

#. module: coupon
#: model_terms:ir.actions.act_window,help:coupon.coupon_program_action_promo_program
msgid ""
"Build up promotion programs to attract more customers with discounts, free products, free delivery, etc.\n"
"                You can share promotion codes or grant the promotions automatically if some conditions are met."
msgstr ""
"Crie programas de promoção para atrair mais clientes com descontos, produtos gratuitos, entrega grátis, etc.\n"
"                Você pode compartilhar códigos de promoção ou conceder as promoções automaticamente se algumas condições forem atendidas."

#. module: coupon
#: model:coupon.program,name:coupon.3_cabinets_plus_1_free
msgid "Buy 3 large cabinets, get one for free"
msgstr "Compre 3 armários grandes e ganhe um de graça"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_generate_view_form
#: model_terms:ir.ui.view,arch_db:coupon.coupon_view_form
#: model_terms:ir.ui.view,arch_db:coupon.coupon_view_tree
msgid "Cancel"
msgstr "Cancelar"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_coupon__state__cancel
msgid "Cancelled"
msgstr "Cancelado"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__code
msgid "Code"
msgstr "Código"

#. module: coupon
#: model:coupon.program,name:coupon.10_percent_auto_applied
msgid "Code for 10% on orders"
msgstr "Código para 10% em pedidos"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__company_id
msgid "Company"
msgstr "Empresa"

#. module: coupon
#: code:addons/coupon/models/coupon.py:0
#, python-format
msgid "Compose Email"
msgstr "Escrever E-mail"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Conditions"
msgstr "Condições"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "Congratulations"
msgstr "Parabéns"

#. module: coupon
#: model:ir.model,name:coupon.model_coupon_coupon
msgid "Coupon"
msgstr "Cupom"

#. module: coupon
#: model:ir.actions.report,name:coupon.report_coupon_code
msgid "Coupon Code"
msgstr "Código cupom"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__coupon_count
msgid "Coupon Count"
msgstr "Contagem de cupom"

#. module: coupon
#: model:ir.model,name:coupon.model_coupon_program
#: model:ir.model.fields.selection,name:coupon.selection__coupon_program__program_type__coupon_program
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Coupon Program"
msgstr "Programa de cupom"

#. module: coupon
#: model:ir.actions.act_window,name:coupon.coupon_program_action_coupon_program
msgid "Coupon Programs"
msgstr "Programas de cupom"

#. module: coupon
#: model:ir.model,name:coupon.model_coupon_reward
msgid "Coupon Reward"
msgstr "Recompensa de Cupom"

#. module: coupon
#: model:ir.model,name:coupon.model_coupon_rule
#: model:ir.model.fields,field_description:coupon.field_coupon_program__rule_id
msgid "Coupon Rule"
msgstr "Regra do Cupom"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_promo_program_form
msgid "Coupon Validity"
msgstr ""

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__rule_date_to
#: model:ir.model.fields,help:coupon.field_coupon_rule__rule_date_to
msgid "Coupon program end date"
msgstr "Data final do programa de cupom"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__rule_date_from
#: model:ir.model.fields,help:coupon.field_coupon_rule__rule_date_from
msgid "Coupon program start date"
msgstr "Data de início do programa de cupom"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__sequence
msgid ""
"Coupon program will be applied based on given sequence if multiple programs "
"are defined on same condition(For minimum amount)"
msgstr ""
"O programa de cupom será aplicado com base na sequência dada se vários "
"programas forem definidos na mesma condição (para quantidade mínima)"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__rule_partners_domain
#: model:ir.model.fields,help:coupon.field_coupon_rule__rule_partners_domain
msgid "Coupon program will work for selected customers only"
msgstr "O programa Coupon funcionará apenas para clientes selecionados"

#. module: coupon
#: model:ir.actions.server,name:coupon.expire_coupon_cron_ir_actions_server
#: model:ir.cron,cron_name:coupon.expire_coupon_cron
#: model:ir.cron,name:coupon.expire_coupon_cron
msgid "Coupon: expire coupon based on date"
msgstr "Cupom: expirar cupom com base na data"

#. module: coupon
#: model:ir.actions.act_window,name:coupon.coupon_action
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_coupon_program_form
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_promo_program_form
#: model_terms:ir.ui.view,arch_db:coupon.coupon_view_form
#: model_terms:ir.ui.view,arch_db:coupon.coupon_view_tree
msgid "Coupons"
msgstr "Cupons"

#. module: coupon
#: model_terms:ir.actions.act_window,help:coupon.coupon_program_action_coupon_program
msgid "Create a new coupon program"
msgstr "Crie um novo programa de cupom"

#. module: coupon
#: model_terms:ir.actions.act_window,help:coupon.coupon_program_action_promo_program
msgid "Create a new promotion program"
msgstr "Crie um novo programa de promoção"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__create_uid
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard__create_uid
#: model:ir.model.fields,field_description:coupon.field_coupon_program__create_uid
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__create_uid
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__create_date
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard__create_date
#: model:ir.model.fields,field_description:coupon.field_coupon_program__create_date
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__create_date
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__create_date
msgid "Created on"
msgstr "Criado em"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__currency_id
msgid "Currency"
msgstr "Moeda"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard__partners_domain
msgid "Customer"
msgstr "Cliente"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__reward_product_uom_id
#: model:ir.model.fields,help:coupon.field_coupon_reward__reward_product_uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "Unidade de medida padrão usada para todas as operações de estoque."

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__discount_percentage
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__discount_percentage
#: model:ir.model.fields.selection,name:coupon.selection__coupon_reward__reward_type__discount
msgid "Discount"
msgstr "Desconto"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__reward_type
#: model:ir.model.fields,help:coupon.field_coupon_reward__reward_type
msgid ""
"Discount - Reward will be provided as discount.\n"
"Free Product - Free product will be provide as reward \n"
"Free Shipping - Free shipping will be provided as reward (Need delivery module)"
msgstr ""
"Desconto - A recompensa será fornecida como desconto.\n"
"Produto grátis - O produto gratuito será fornecido como recompensa\n"
"Frete Grátis - O envio gratuito será fornecido como recompensa (Precisa de um módulo de entrega)"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__discount_apply_on
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__discount_apply_on
msgid "Discount Apply On"
msgstr "Desconto Aplicar em"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__discount_max_amount
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__discount_max_amount
msgid "Discount Max Amount"
msgstr "Valor máximo de desconto"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__discount_type
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__discount_type
msgid "Discount Type"
msgstr "Tipo de desconto"

#. module: coupon
#: code:addons/coupon/models/coupon_reward.py:0
#, python-format
msgid "Discount percentage should be between 1-100"
msgstr "A porcentagem de desconto deve estar entre 1-100"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__display_name
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard__display_name
#: model:ir.model.fields,field_description:coupon.field_coupon_program__display_name
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__display_name
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__display_name
msgid "Display Name"
msgstr "Nome exibido"

#. module: coupon
#: model:ir.model,name:coupon.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Assistente de composição de E-mail"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__rule_date_to
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__rule_date_to
msgid "End Date"
msgstr "Data Final"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__expiration_date
msgid "Expiration Date"
msgstr "Data de expiração"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_coupon__state__expired
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_search
msgid "Expired"
msgstr "Expirado"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_search
msgid "Expired Programs"
msgstr "Programas expirados"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__discount_fixed_amount
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__discount_fixed_amount
#: model:ir.model.fields.selection,name:coupon.selection__coupon_reward__discount_type__fixed_amount
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Fixed Amount"
msgstr "Quantidade fixa"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__partner_id
msgid "For Customer"
msgstr "Para o cliente"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__reward_product_id
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__reward_product_id
#: model:ir.model.fields.selection,name:coupon.selection__coupon_reward__reward_type__product
msgid "Free Product"
msgstr "Produto grátis"

#. module: coupon
#: code:addons/coupon/models/coupon_reward.py:0
#, python-format
msgid "Free Product - %s"
msgstr "Produto grátis -%s"

#. module: coupon
#: model:product.product,name:coupon.product_product_free_large_cabinet
#: model:product.template,name:coupon.product_product_free_large_cabinet_product_template
msgid "Free Product - Large Cabinet"
msgstr "Produto Grátis - Armário Grande"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_generate_view_form
msgid "Generate"
msgstr "Gerar"

#. module: coupon
#: model:ir.model,name:coupon.model_coupon_generate_wizard
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_coupon_program_form
msgid "Generate Coupon"
msgstr "Gerar Cupom"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_generate_view_form
msgid "Generate Coupons"
msgstr "Gerar Cupons"

#. module: coupon
#: model_terms:ir.actions.act_window,help:coupon.coupon_program_action_coupon_program
msgid ""
"Generate and share coupon codes with your customers to get discounts or free"
" products."
msgstr ""
"Gere e compartilhe códigos de cupom com seus clientes para obter descontos "
"ou produtos grátis."

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__coupon_ids
msgid "Generated Coupons"
msgstr "Gerar Cupons"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard__generation_type
msgid "Generation Type"
msgstr "Tipo de geração"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard__has_partner_email
msgid "Has Partner Email"
msgstr "Tem E-mail de Parceiro"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "Here is your reward from"
msgstr "Aqui está sua recompensa de"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__id
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard__id
#: model:ir.model.fields,field_description:coupon.field_coupon_program__id
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__id
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__id
msgid "ID"
msgstr "ID"

#. module: coupon
#: code:addons/coupon/models/coupon.py:0
#, python-format
msgid "Invalid partner."
msgstr "Parceiro inválido."

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon____last_update
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard____last_update
#: model:ir.model.fields,field_description:coupon.field_coupon_program____last_update
#: model:ir.model.fields,field_description:coupon.field_coupon_reward____last_update
#: model:ir.model.fields,field_description:coupon.field_coupon_rule____last_update
msgid "Last Modified on"
msgstr "Última modificação em"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__write_uid
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard__write_uid
#: model:ir.model.fields,field_description:coupon.field_coupon_program__write_uid
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__write_uid
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__write_uid
msgid "Last Updated by"
msgstr "Última atualização por"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__write_date
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard__write_date
#: model:ir.model.fields,field_description:coupon.field_coupon_program__write_date
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__write_date
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__write_date
msgid "Last Updated on"
msgstr "Última atualização em"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "Logo"
msgstr "Logotipo"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Max Discount Amount"
msgstr "Valor máximo de desconto"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__maximum_use_number
msgid "Maximum Use Number"
msgstr "Número máximo de uso"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__discount_max_amount
#: model:ir.model.fields,help:coupon.field_coupon_reward__discount_max_amount
msgid "Maximum amount of discount that can be provided"
msgstr "Montante máximo de desconto que pode ser fornecido"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__maximum_use_number
msgid "Maximum number of sales orders in which reward can be provided"
msgstr ""
"Número máximo de pedidos de vendas em que recompensa pode ser fornecida"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Minimum Purchase Of"
msgstr "Compra mínima de"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__rule_min_quantity
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__rule_min_quantity
msgid "Minimum Quantity"
msgstr "Quantidade Mínima"

#. module: coupon
#: code:addons/coupon/models/coupon_rules.py:0
#, python-format
msgid "Minimum purchased amount should be greater than 0"
msgstr "O valor mínimo comprado deve ser superior a 0"

#. module: coupon
#: code:addons/coupon/models/coupon_rules.py:0
#, python-format
msgid "Minimum quantity should be greater than 0"
msgstr "A quantidade mínima deve ser maior que 0"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__rule_minimum_amount
#: model:ir.model.fields,help:coupon.field_coupon_rule__rule_minimum_amount
msgid "Minimum required amount to get the reward"
msgstr "Quantidade mínima necessária para obter a recompensa"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__rule_min_quantity
#: model:ir.model.fields,help:coupon.field_coupon_rule__rule_min_quantity
msgid "Minimum required product quantity to get the reward"
msgstr "Quantidade mínima de produto requerida para obter a recompensa"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__name
msgid "Name"
msgstr "Nome"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard__nbr_coupons
#: model:ir.model.fields.selection,name:coupon.selection__coupon_generate_wizard__generation_type__nbr_coupon
msgid "Number of Coupons"
msgstr "Número de cupons"

#. module: coupon
#: model:ir.actions.act_window,name:coupon.coupon_generate_action
msgid "Number of Coupons To Generate"
msgstr "Número de cupons para gerar"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_generate_wizard__generation_type__nbr_customer
msgid "Number of Selected Customers"
msgstr "Número de Clientes Selecionados"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_generate_wizard__nbr_coupons
msgid "Number of coupons"
msgstr "Número de cupons"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_reward__discount_apply_on__cheapest_product
msgid "On Cheapest Product"
msgstr "No produto mais barato"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_reward__discount_apply_on__on_order
msgid "On Order"
msgstr "Em ordem"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__discount_apply_on
#: model:ir.model.fields,help:coupon.field_coupon_reward__discount_apply_on
msgid ""
"On Order - Discount on whole order\n"
"Cheapest product - Discount on cheapest product of the order\n"
"Specific products - Discount on selected specific products"
msgstr ""
"No pedido - Desconto em todo o pedido\n"
"Produto mais barato - Desconto no produto mais barato do pedido\n"
"Produtos específicos - Desconto em produtos específicos selecionados"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__rule_products_domain
#: model:ir.model.fields,help:coupon.field_coupon_rule__rule_products_domain
msgid "On Purchase of selected product, reward will be given"
msgstr "Na compra do produto selecionado, recompensa será dada"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_reward__discount_apply_on__specific_products
msgid "On Specific Products"
msgstr "Em Produtos Específicos"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_coupon__state__reserved
msgid "Pending"
msgstr "Pendente"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_reward__discount_type__percentage
msgid "Percentage"
msgstr "Porcentagem"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__discount_type
#: model:ir.model.fields,help:coupon.field_coupon_reward__discount_type
msgid ""
"Percentage - Entered percentage discount will be provided\n"
"Amount - Entered fixed amount discount will be provided"
msgstr ""
"Porcentagem - O desconto percentual registrado será fornecido\n"
"Montante - O desconto de montante fixo registrado será fornecido"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_coupon__discount_line_product_id
msgid "Product used in the sales order to apply the discount."
msgstr "Produto usado na ordem do cliente para aplicar o desconto."

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__discount_line_product_id
#: model:ir.model.fields,help:coupon.field_coupon_reward__discount_line_product_id
msgid ""
"Product used in the sales order to apply the discount. Each coupon program "
"has its own reward product for reporting purpose"
msgstr ""
"Produto usado na ordem do cliente para aplicar o desconto. Cada programa de "
"cupom tem seu próprio produto de recompensa para fins de relatório"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__discount_specific_product_ids
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__discount_specific_product_ids
msgid "Products"
msgstr "Produtos"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__discount_specific_product_ids
#: model:ir.model.fields,help:coupon.field_coupon_reward__discount_specific_product_ids
msgid ""
"Products that will be discounted if the discount is applied on specific "
"products"
msgstr ""
"Produtos que terão desconto se o desconto for aplicado em produtos "
"específicos"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__program_id
msgid "Program"
msgstr "Programa"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_coupon_program_form
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_promo_program_form
msgid "Program Name"
msgstr "Nome do programa"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__program_type
msgid "Program Type"
msgstr "Tipo do Programa"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__promo_code_usage
msgid "Promo Code Usage"
msgstr "Uso do Código de Promoção"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__promo_code
msgid "Promotion Code"
msgstr "Código de promoção"

#. module: coupon
#: model:ir.actions.act_window,name:coupon.coupon_program_action_promo_program
msgid "Promotion Programs"
msgstr "Programas de Promoção"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_program__program_type__promotion_program
msgid "Promotional Program"
msgstr "Programa Promocional"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__reward_product_quantity
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__reward_product_quantity
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Quantity"
msgstr "Quantidade"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__reward_id
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Reward"
msgstr "Recompensa"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__reward_description
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__reward_description
msgid "Reward Description"
msgstr "Descrição da recompensa"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__discount_line_product_id
#: model:ir.model.fields,field_description:coupon.field_coupon_program__discount_line_product_id
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__discount_line_product_id
msgid "Reward Line Product"
msgstr "Produto de linha de recompensa"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__reward_product_id
#: model:ir.model.fields,help:coupon.field_coupon_reward__reward_product_id
msgid "Reward Product"
msgstr "Produto de recompensa"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__reward_type
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__reward_type
msgid "Reward Type"
msgstr "Tipo de Recompensa"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__reward_product_quantity
#: model:ir.model.fields,help:coupon.field_coupon_reward__reward_product_quantity
msgid "Reward product quantity"
msgstr "Recompensar a quantidade do produto"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Rewards"
msgstr "Recompensas"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__rule_minimum_amount
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__rule_minimum_amount
msgid "Rule Minimum Amount"
msgstr "Valor mínimo da regra"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__rule_minimum_amount_tax_inclusion
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__rule_minimum_amount_tax_inclusion
msgid "Rule Minimum Amount Tax Inclusion"
msgstr "Regra Quantidade mínima de Inclusão de impostos"

#. module: coupon
#: model:ir.model,name:coupon.model_report_coupon_report_coupon
msgid "Sales Coupon Report"
msgstr "Relatório de Cupom de Vendas"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Select company"
msgstr "Selecione a empresa"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_promo_program_form
msgid "Select customer"
msgstr "Selecione o cliente"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Select product"
msgstr "Selecione o produto"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Select products"
msgstr "Selecionar produtos"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Select reward product"
msgstr "Selecione o produto de recompensa"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_view_tree
msgid "Send"
msgstr "Enviar"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_program__promo_applicability__on_next_order
msgid "Send a Coupon"
msgstr "Enviar um Cupom"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_view_form
msgid "Send by Email"
msgstr "Enviar por e-mail"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_coupon__state__sent
msgid "Sent"
msgstr "Enviado"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__sequence
msgid "Sequence"
msgstr "Sequência"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_generate_view_form
msgid ""
"Some selected customers do not have an email address and will not receive "
"the coupon."
msgstr ""
"Alguns clientes selecionados não possuem um endereço de e-mail e não "
"receberão o cupom."

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_generate_view_form
msgid "Specify a mail template to send the generated coupons as email."
msgstr ""

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__rule_date_from
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__rule_date_from
msgid "Start Date"
msgstr "Data de Início"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__state
msgid "State"
msgstr "Estado"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_rule__rule_minimum_amount_tax_inclusion__tax_excluded
msgid "Tax Excluded"
msgstr "Sem impostos"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_rule__rule_minimum_amount_tax_inclusion__tax_included
msgid "Tax Included"
msgstr "Com impostos"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "Thank you,"
msgstr "Obrigado,"

#. module: coupon
#: model:ir.model.constraint,message:coupon.constraint_coupon_coupon_unique_coupon_code
msgid "The coupon code must be unique!"
msgstr "O código do cupom deve ser exclusivo!"

#. module: coupon
#: code:addons/coupon/models/coupon.py:0
#, python-format
msgid "The coupon program for %s is in draft or closed state"
msgstr "O programa de cupom %s está em estado rascunho ou fechado"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__discount_fixed_amount
#: model:ir.model.fields,help:coupon.field_coupon_reward__discount_fixed_amount
msgid "The discount in fixed amount"
msgstr "O desconto em quantidade fixa"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__discount_percentage
#: model:ir.model.fields,help:coupon.field_coupon_reward__discount_percentage
msgid "The discount in percentage, between 1 and 100"
msgstr "A desconto em porcentagem, entre 1 e 100"

#. module: coupon
#: code:addons/coupon/models/coupon_program.py:0
#, python-format
msgid "The program code must be unique!"
msgstr "O código do programa deve ser exclusivo!"

#. module: coupon
#: code:addons/coupon/models/coupon_rules.py:0
#, python-format
msgid "The start date must be before the end date"
msgstr "A data de início deve ser anterior à data final"

#. module: coupon
#: code:addons/coupon/models/coupon.py:0
#, python-format
msgid "This coupon %s exists but the origin sales order is not validated yet."
msgstr ""
"Este cupom %s existe, mas a ordem de venda de origem ainda não foi validada."

#. module: coupon
#: code:addons/coupon/models/coupon.py:0
#, python-format
msgid "This coupon has already been used (%s)."
msgstr "Este cupom já foi utilizado(%s)"

#. module: coupon
#: code:addons/coupon/models/coupon.py:0
#, python-format
msgid "This coupon has been cancelled (%s)."
msgstr "Este cupom foi cancelado (%s)"

#. module: coupon
#: code:addons/coupon/models/coupon.py:0
#, python-format
msgid "This coupon is expired (%s)."
msgstr "Este cupom expirou (%s)"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__total_order_count
msgid "Total Order Count"
msgstr "Total Order Count"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__reward_product_uom_id
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__reward_product_uom_id
msgid "Unit of Measure"
msgstr "Unidade de Medida"

#. module: coupon
#: model:product.product,uom_name:coupon.product_product_10_percent_discount
#: model:product.product,uom_name:coupon.product_product_free_large_cabinet
#: model:product.template,uom_name:coupon.product_product_10_percent_discount_product_template
#: model:product.template,uom_name:coupon.product_product_free_large_cabinet_product_template
msgid "Units"
msgstr "Unidades"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_program__promo_code_usage__code_needed
msgid "Use a code"
msgstr "Use um código"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard__template_id
msgid "Use template"
msgstr "Usar modelo"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "Use this promo code before"
msgstr "Use este código promocional antes"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_coupon__state__used
msgid "Used"
msgstr "Usado"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_coupon__state__new
msgid "Valid"
msgstr "Válido"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Validity"
msgstr "Validade"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__validity_duration
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_coupon_program_form
msgid "Validity Duration"
msgstr "Duração da Validade"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__validity_duration
msgid "Validity duration for a coupon after its generation"
msgstr "Duração da validade de um cupom após sua geração"

#. module: coupon
#: code:addons/coupon/models/coupon_program.py:0
#, python-format
msgid "You can not delete a program in active state"
msgstr "Você não pode excluir um programa no estado ativo"

#. module: coupon
#: code:addons/coupon/models/product_product.py:0
#, python-format
msgid ""
"You cannot delete a product that is linked with Coupon or Promotion program."
" Archive it instead."
msgstr ""

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_promo_program_form
msgid "e.g. 10% Discount"
msgstr ""

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "off %s"
msgstr "desconto %s"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "off on %s"
msgstr "desconto em %s"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "off on some products*"
msgstr "desconto em alguns produtos*"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "off on the cheapest product"
msgstr "desconto no produto mais barato"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "on your next order"
msgstr "seu próximo pedido"
