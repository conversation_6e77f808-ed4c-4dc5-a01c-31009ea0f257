<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="payslip_sukuk_details_form" model="ir.ui.view">
        <field name="name">payslip.sukuk.wizard.report2.form</field>
        <field name="model">payslip.sukuk.wizard.report2</field>
        <field name="arch" type="xml">
            <form string="Details Payslip Report">
                <group>
                    <group>
                        <field name="dateFrom" string="تاريخ من" attrs="{'readonly':[('sukuk_computed','=',True)]}"/>
                        <field name="dateTo" string="تاريخ الى" attrs="{'readonly':[('sukuk_computed','=',True)]}"/>
                    </group>
                    <group>
<!--                        <field name="sukuk_book_id" attrs="{'readonly':[('sukuk_computed','=',True)]}"/>-->
                        <field name="account_no_id" attrs="{'readonly':[('sukuk_computed','=',True)]}"/>
                        <field name="bank_id" attrs="{'readonly':[('sukuk_computed','=',True)]}"/>
                        <field name="branch_id" attrs="{'readonly':[('sukuk_computed','=',True)]}"/>
                        <field name="dialog_box" readonly="1" nolabel="1"/>
                    </group>
                    <group>
                        <field name="user_id" readonly="1"/>
                        <field name="sukuk_computed" invisible="1"/>
                    </group>
                </group>
                <h3>
                    في حالة تعديل البيانات يدوياً يرجى التأكد من صحة الرقم التسلسلي ورقم الدفتر
                </h3>
                <notebook>
                    <page string="الحوافظ">
                        <field name="sukuk_page_ids" readonly="0">
                            <tree editable="top" create="false">
                                <field name="sukuk_book_id" string="دفتر الصكوك" options="{'no_create': True,'no_open': True, 'no_create_edit':True}" invisible="0"/>
                                <field name="bank_id" invisible="0" readonly="1"/>
                                <field name="branch_id" invisible="0" readonly="1"/>
                                <field name="paid_to_bank_id" readonly="1"/>
                                <field name="paid_to_branch_id" readonly="1"/>
                                <field name="person_signature" readonly="1"/>
                                <field name="suke_book_number" readonly="0"/>
                                <field name="serial_no" readonly="0"/>
                                <field name="amount" readonly="1"/>
                                <field name="note" readonly="1"/>
                            </tree>
                        </field>
                    </page>
                </notebook>
                <footer>
                    <button name="generate_payslips_sukuk"
                            type="object"
                            string="انشاء صكوك الحوافظ"
                            attrs="{'invisible':[('sukuk_computed','=',True)]}"
                            class="btn-primary"/>
                    <button name="get_report_action"
                            type="object"
                            string="طباعة الحوافظ"
                            class="btn-primary"
                            attrs="{'invisible':[('sukuk_computed','=',False)]}"
                            confirm="في حالة طباعة الحوافظ فسيتم اصدار الصكوك وتخزينها في المنظومة !"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>


    <record id="action_report_payslip_sukuk_details" model="ir.actions.act_window">
        <field name="name">تقرير الحوافظ مرفق بالصكوك</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">payslip.sukuk.wizard.report2</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="payslip_sukuk_details_form"/>
        <field name="target">new</field>
    </record>

    <menuitem id="payslip_sukuk_menu"
              name="اصدار صكوك حوافظ المصارف"
              parent="account.menu_finance_entries"
              sequence="81"
              action="action_report_payslip_sukuk_details"/>

</odoo>