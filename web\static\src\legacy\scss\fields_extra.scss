//
// This file regroups all the rules which apply to field widgets wherever they
// are in the DOM, in the community version.
//

// Required
.o_required_modifier {
    &.o_input, .o_input {
        background-color: #D2D2FF!important;
    }
}

//------------------------------------------------------------------------------
// Fields
//------------------------------------------------------------------------------

.o_field_widget {
    &.o_field_many2manytags {
        .o_dropdown_button {
            top: 0;
            right: 0;
        }

        &.avatar .o_dropdown_button {
            top: $o-input-padding-y;
        }
    }

    // Many2one
    &.o_field_many2one .o_external_button {
        flex: 0 0 auto;
        padding: 0;
        margin-left: 2px;
        font-size: 19px;
        color: #7C7BAD;
        border: none;
        &:hover {
            background-color: transparent;
        }
    }

    // Percent pie field
    &.o_field_percent_pie {
        $pie-dimension: 34px;
        $pie-ring-width: 4px;

        .o_pie {
            width: $pie-dimension;
            height: $pie-dimension;
            margin-left: 5px;

            &:after { // Outside pie border to go over border-radius irregularities
                border: 1px solid $o-brand-odoo;
            }

            .o_pie_value {
                @include o-position-absolute($pie-ring-width, $pie-ring-width);
                width: $pie-dimension - 2 * $pie-ring-width;
                height: $pie-dimension - 2 * $pie-ring-width;
                border: 1px solid white;
                background-color: white;
                font-size: 10px;
            }
        }
    }
}
