# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON><PERSON> Jovev <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-11-16 08:08+0000\n"
"PO-Revision-Date: 2017-11-16 08:08+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> Jovev <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: hr
#: code:addons/hr/models/hr.py:75
#, python-format
msgid "%s (copy)"
msgstr "%s (kopija)"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_kanban_view_employees
msgid ""
"<span class=\"o_unfollow\">Unfollow</span>\n"
"                                    <span class=\"o_following\">Following</span>"
msgstr ""
"<span class=\"o_unfollow\">Prestani pratiti</span>\n"
"                                    <span class=\"o_following\">Pratioci</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "<span>Reporting</span>"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "<span>To Approve</span>"
msgstr "<span>Čeka odobrenje</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "<span>To Do</span>"
msgstr "<span>Za uraditi</span>"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department_active
#: model:ir.model.fields,field_description:hr.field_hr_employee_active
msgid "Active"
msgstr "Aktivan"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Archived"
msgstr "Arhivirani"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_bank_account_id
msgid "Bank Account Number"
msgstr "Broj bankovnog računa"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Birth"
msgstr "Datum rodjenja"

#. module: hr
#: model:hr.job,name:hr.job_ceo
msgid "Chief Executive Officer"
msgstr "Izvršni direktor"

#. module: hr
#: model:hr.job,name:hr.job_cto
msgid "Chief Technical Officer"
msgstr "Tehnički direktor"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department_child_ids
msgid "Child Departments"
msgstr "Pod sektori"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Citizenship & Other Information"
msgstr "Gradjanske i ostale informacije"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.act_employee_from_department
#: model_terms:ir.actions.act_window,help:hr.open_view_employee_list_my
msgid "Click to add a new employee."
msgstr "Klikni da dodaš novog zaposlenog."

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.open_module_tree_department
msgid "Click to create a department."
msgstr "Klikni da kreiraš novi sektor."

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.action_hr_job
msgid "Click to define a new job position."
msgstr "Klikni da napraviš novo radno mjesto."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_coach_id
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Coach"
msgstr "Mentor"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department_color
#: model:ir.model.fields,field_description:hr.field_hr_employee_category_color
#: model:ir.model.fields,field_description:hr.field_hr_employee_color
msgid "Color Index"
msgstr "Indeks boje"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_department_tree
msgid "Companies"
msgstr "Preduzeća"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department_company_id
#: model:ir.model.fields,field_description:hr.field_hr_job_company_id
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Company"
msgstr "Preduzeće"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings_resource_calendar_id
msgid "Company Working Hours"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department_complete_name
msgid "Complete Name"
msgstr "Puno Ime"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_human_resources_configuration
msgid "Configuration"
msgstr "Postavka"

#. module: hr
#: model:hr.job,name:hr.job_consultant
msgid "Consultant"
msgstr "Konsultant"

#. module: hr
#: model:ir.model,name:hr.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Contact Information"
msgstr "Kontakt Info"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department_create_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee_category_create_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee_create_uid
#: model:ir.model.fields,field_description:hr.field_hr_job_create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department_create_date
#: model:ir.model.fields,field_description:hr.field_hr_employee_category_create_date
#: model:ir.model.fields,field_description:hr.field_hr_employee_create_date
#: model:ir.model.fields,field_description:hr.field_hr_job_create_date
msgid "Created on"
msgstr "Datum kreiranja"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job_no_of_employee
msgid "Current Number of Employees"
msgstr "Trenutni broj zaposlenih"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_birthday
msgid "Date of Birth"
msgstr "Datum Rođenja"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_department_id
#: model:ir.model.fields,field_description:hr.field_hr_job_department_id
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Department"
msgstr "Sektor"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department_name
msgid "Department Name"
msgstr "Naziv sektora"

#. module: hr
#: model:ir.actions.act_window,name:hr.open_module_tree_department
#: model:ir.ui.menu,name:hr.menu_hr_department_tree
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
msgid "Departments"
msgstr "Sektori"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department_display_name
#: model:ir.model.fields,field_description:hr.field_hr_employee_category_display_name
#: model:ir.model.fields,field_description:hr.field_hr_employee_display_name
#: model:ir.model.fields,field_description:hr.field_hr_job_display_name
msgid "Display Name"
msgstr "Naziv za prikaz"

#. module: hr
#: selection:hr.employee,marital:0
msgid "Divorced"
msgstr "Razvedeni"

#. module: hr
#: model:ir.model,name:hr.model_mail_alias
msgid "Email Aliases"
msgstr ""

#. module: hr
#: model:ir.model,name:hr.model_hr_employee
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Employee"
msgstr "Zaposleni"

#. module: hr
#: model:ir.model,name:hr.model_hr_employee_category
msgid "Employee Category"
msgstr "Kategorija Zaposlenog"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_category_name
msgid "Employee Tag"
msgstr "Oznaka zaposlenog"

#. module: hr
#: model:ir.actions.act_window,name:hr.open_view_categ_form
#: model:ir.ui.menu,name:hr.menu_view_employee_category_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_category_form
msgid "Employee Tags"
msgstr "Oznake zaposlenog"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee_bank_account_id
msgid "Employee bank salary account"
msgstr "Bankovni račun zaposlenog"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Employee's Name"
msgstr "Ime zaposlenog"

#. module: hr
#: model:ir.actions.act_window,name:hr.act_employee_from_department
#: model:ir.actions.act_window,name:hr.open_view_employee_list
#: model:ir.actions.act_window,name:hr.open_view_employee_list_my
#: model:ir.model.fields,field_description:hr.field_hr_employee_category_employee_ids
#: model:ir.model.fields,field_description:hr.field_hr_job_employee_ids
#: model:ir.ui.menu,name:hr.menu_hr_root
#: model:ir.ui.menu,name:hr.menu_open_view_employee_list_my
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_tree
#: model_terms:ir.ui.view,arch_db:hr.view_partner_tree2
msgid "Employees"
msgstr "Zaposlenici"

#. module: hr
#: model:ir.actions.act_window,name:hr.open_view_employee_tree
msgid "Employees Structure"
msgstr "Struktura zaposlenih"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_category_list
msgid "Employees Tags"
msgstr "Oznake zaposlenih"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee_address_home_id
msgid ""
"Enter here the private address of the employee, not the one linked to your "
"company."
msgstr ""

#. module: hr
#: code:addons/hr/models/hr.py:303
#, python-format
msgid "Error! You cannot create recursive departments."
msgstr "Greška! Nije moguće napraviti rekurzivne sektore."

#. module: hr
#: code:addons/hr/models/hr.py:184
#, python-format
msgid "Error! You cannot create recursive hierarchy of Employee(s)."
msgstr "Greška! Nije moguće napraviti rekurzivnu hijerarhiju zaposlenog(ih)."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job_no_of_recruitment
msgid "Expected New Employees"
msgstr "Očekivan broj novih radnika"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job_expected_employees
msgid ""
"Expected number of employees for this job position after new recruitment."
msgstr ""
"Očekivan broj novih radnika na ovom radnom mjestu nakon novog konkursa"

#. module: hr
#: model:hr.job,name:hr.job_developer
msgid "Experienced Developer"
msgstr "Iskusni developer"

#. module: hr
#: selection:hr.employee,gender:0
msgid "Female"
msgstr "Žensko"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_kanban_view_employees
msgid "Follow"
msgstr "Prati"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_kanban_view_employees
msgid "Followers"
msgstr "Pratioci"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_gender
msgid "Gender"
msgstr "Pol"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Group By"
msgstr "Grupiši po"

#. module: hr
#: model:ir.model,name:hr.model_hr_department
msgid "HR Department"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "HR Settings"
msgstr "Dodatne informacije"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job_no_of_hired_employee
msgid "Hired Employees"
msgstr "Zaposlenih radnika"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_hr_main
msgid "Human Resources"
msgstr "Ljudski resursi"

#. module: hr
#: model:hr.job,name:hr.job_hrm
msgid "Human Resources Manager"
msgstr "Menadžer ljudskih resursa"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_category_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_id
#: model:ir.model.fields,field_description:hr.field_hr_job_id
msgid "ID"
msgstr "ID"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_identification_id
msgid "Identification No"
msgstr "Identifikacioni broj"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee_active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"Ako je ovo polje postavljeno na NE, možete sakriti resurs bez da ga "
"uklonite."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "In Position"
msgstr "Na poziciji"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "In Recruitment"
msgstr "U zapošljavanju"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_tree
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Job"
msgstr "Radno mjesto"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job_description
msgid "Job Description"
msgstr "Opis radnog mjesta"

#. module: hr
#: model:ir.model,name:hr.model_hr_job
#: model:ir.model.fields,field_description:hr.field_hr_employee_job_id
#: model:ir.model.fields,field_description:hr.field_hr_job_name
msgid "Job Position"
msgstr "Radno mjesto"

#. module: hr
#: model:ir.actions.act_window,name:hr.action_hr_job
msgid "Job Positions"
msgstr "Radna mjesta"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.action_hr_job
msgid ""
"Job Positions are used to define jobs and their requirements.\n"
"                You can keep track of the number of employees you have per job\n"
"                position and follow the evolution according to what you planned\n"
"                for the future."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department_jobs_ids
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Jobs"
msgstr "Radna mjesta"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department___last_update
#: model:ir.model.fields,field_description:hr.field_hr_employee___last_update
#: model:ir.model.fields,field_description:hr.field_hr_employee_category___last_update
#: model:ir.model.fields,field_description:hr.field_hr_job___last_update
msgid "Last Modified on"
msgstr "Zadnja promena"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department_write_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee_category_write_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee_write_uid
#: model:ir.model.fields,field_description:hr.field_hr_job_write_uid
msgid "Last Updated by"
msgstr "Promenio"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department_write_date
#: model:ir.model.fields,field_description:hr.field_hr_employee_category_write_date
#: model:ir.model.fields,field_description:hr.field_hr_employee_write_date
#: model:ir.model.fields,field_description:hr.field_hr_job_write_date
msgid "Last Updated on"
msgstr "Vreme promene"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "Launch Recruitment"
msgstr "Raspiši konkurs"

#. module: hr
#: selection:hr.employee,gender:0
msgid "Male"
msgstr "Muškarac"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department_manager_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_parent_id
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model:res.groups,name:hr.group_hr_manager
msgid "Manager"
msgstr "Nadzor"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_marital
msgid "Marital Status"
msgstr "Bračno Stanje"

#. module: hr
#: model:hr.job,name:hr.job_marketing
msgid "Marketing and Community Manager"
msgstr ""

#. module: hr
#: selection:hr.employee,marital:0
msgid "Married (or similar)"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_image_medium
msgid "Medium-sized photo"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee_image_medium
msgid ""
"Medium-sized photo of the employee. It is automatically resized as a "
"128x128px image, with aspect ratio preserved. Use this field in form views "
"or some kanban views."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department_member_ids
msgid "Members"
msgstr "Članovi"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_kanban_view_employees
msgid "Messages"
msgstr "Poruke"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_name
msgid "Name"
msgstr "Naziv"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_country_id
msgid "Nationality (Country)"
msgstr "Nacionalnos (Država)"

#. module: hr
#: selection:hr.job,state:0
msgid "Not Recruiting"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department_note
msgid "Note"
msgstr "Zabilješka"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_notes
msgid "Notes"
msgstr "Zabilješke"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job_no_of_employee
msgid "Number of employees currently occupying this job position."
msgstr "Broj zaposlenih koji trenutno zauzimaju ovo radno mjesto"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job_no_of_hired_employee
msgid ""
"Number of hired employees for this job position during recruitment phase."
msgstr "Broj zaposlenih radnika na ovo radno mjesto tokom faze zapošljavanja."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job_no_of_recruitment
msgid "Number of new employees you expect to recruit."
msgstr "Broj novih radnika koje očekuješ da ćeš zaposliti"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.open_module_tree_department
msgid ""
"Odoo's department structure is used to manage all documents\n"
"                related to employees by departments: expenses, timesheets,\n"
"                leaves, recruitments, etc."
msgstr ""

#. module: hr
#: model:res.groups,name:hr.group_hr_user
msgid "Officer"
msgstr ""

#. module: hr
#: selection:hr.employee,gender:0
msgid "Other"
msgstr "Ostalo"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Other Information ..."
msgstr "Ostale informacije ..."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department_parent_id
msgid "Parent Department"
msgstr "Nadsektor"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_passport_id
msgid "Passport No"
msgstr "Br. pasoša"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_image
msgid "Photo"
msgstr "Fotografija"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Position"
msgstr "Pozicija"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_address_home_id
msgid "Private Address"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Private Information"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "Recruitment"
msgstr "Zapošljavanje"

#. module: hr
#: selection:hr.job,state:0
msgid "Recruitment in Progress"
msgstr "Konkurs u toku"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Related User"
msgstr "Odnosni korisnički nalog"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users_employee_ids
msgid "Related employees"
msgstr "Odnosni zaposleni"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee_user_id
msgid "Related user name for the resource to manage its access."
msgstr "Korisničko ime povezano je sa pristupom i upravljanjem modulima"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_hr_reporting_timesheet
msgid "Reporting"
msgstr "Izvještavanje"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job_requirements
msgid "Requirements"
msgstr "Zahtjevi"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_sinid
msgid "SIN No"
msgstr "Broj polise osiguranja"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_ssnid
msgid "SSN No"
msgstr "JMBG"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid ""
"Set default calendar used to compute time allocation for leaves, timesheets,"
" ..."
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job_state
msgid ""
"Set whether the recruitment process is open or closed for this job position."
msgstr ""

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_config_settings_action
#: model:ir.ui.menu,name:hr.hr_menu_configuration
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "Settings"
msgstr "Podešavanja"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings_module_hr_org_chart
msgid "Show Organizational Chart"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Show organizational chart on employee form"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Show organizational chart on employee form."
msgstr ""

#. module: hr
#: selection:hr.employee,marital:0
msgid "Single"
msgstr "Sam/Sama"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_image_small
msgid "Small-sized photo"
msgstr "Manja fotografija"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee_image_small
msgid ""
"Small-sized photo of the employee. It is automatically resized as a 64x64px "
"image, with aspect ratio preserved. Use this field anywhere a small image is"
" required."
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee_sinid
msgid "Social Insurance Number"
msgstr "Broj Socijalnog Osiguranja"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee_ssnid
msgid "Social Security Number"
msgstr "Jedinstveni matični broj građana"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job_state
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Status"
msgstr "Status"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "Stop Recruitment"
msgstr "Zatvori konkurs"

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_employee_action_subordinate_hierachy
msgid "Subordinate Hierarchy"
msgstr "Hijerarhija zaposlenih"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_child_ids
msgid "Subordinates"
msgstr "Podređeni"

#. module: hr
#: sql_constraint:hr.employee.category:0
msgid "Tag name already exists !"
msgstr "Naziv oznake već postoji !"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_category_ids
msgid "Tags"
msgstr "Oznake"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_is_address_home_a_company
msgid "The employee adress has a company linked"
msgstr ""

#. module: hr
#: sql_constraint:hr.job:0
msgid "The name of the job position must be unique per department in company!"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee_image
msgid ""
"This field holds the image used as photo for the employee, limited to "
"1024x1024px."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job_expected_employees
msgid "Total Forecasted Employees"
msgstr "Predviđen ukupan broj zaposlenih"

#. module: hr
#: model:hr.job,name:hr.job_trainee
msgid "Trainee"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_kanban_view_employees
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Unread Messages"
msgstr "Nepročitane poruke"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid ""
"Use here the home address of the employee.\n"
"                                            This private address is used in the expense report reimbursement document.\n"
"                                            It should be different from the work address."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_user_id
msgid "User"
msgstr "Korisnik"

#. module: hr
#: model:ir.model,name:hr.model_res_users
msgid "Users"
msgstr "Korisnici"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_job_view_kanban
msgid "Vacancies :"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_visa_expire
msgid "Visa Expire Date"
msgstr "Datum isteka Visa kartice"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_visa_no
msgid "Visa No"
msgstr "Broj Visa kartice"

#. module: hr
#: selection:hr.employee,marital:0
msgid "Widower"
msgstr "Udovac"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.act_employee_from_department
#: model_terms:ir.actions.act_window,help:hr.open_view_employee_list_my
msgid ""
"With just a quick glance on the Odoo employee screen, you\n"
"                can easily find all the information you need for each person;\n"
"                contact data, job position, availability, etc."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_address_id
msgid "Work Address"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_work_email
msgid "Work Email"
msgstr "Poslovni email"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Work Information"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_work_location
msgid "Work Location"
msgstr "Lokacija posla"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_mobile_phone
msgid "Work Mobile"
msgstr "Poslovni mobilni"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Work Organization"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Work Permit"
msgstr "Radna dozvola"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_permit_no
msgid "Work Permit No"
msgstr "Broj radne dozvole"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_work_phone
msgid "Work Phone"
msgstr "Poslovni telefon"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.action_hr_job
msgid ""
"You can attach a survey to a job position. It will be used in\n"
"                the recruitment process to evaluate the applicants for this job\n"
"                position."
msgstr ""

#. module: hr
#: model:mail.template,subject:hr.mail_template_data_unknown_employee_email_address
msgid "Your document has not been created"
msgstr ""

#. module: hr
#: model:mail.template,body_html:hr.mail_template_data_unknown_employee_email_address
msgid ""
"Your document has not been created because your email address is not "
"recognized. Please send emails with the email address recorded on your "
"employee information, or contact your HR manager."
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_department_form
msgid "department"
msgstr "sektor"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "e.g. Part Time"
msgstr "npr. povremeno"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "e.g. Sales Manager"
msgstr ""

#. module: hr
#: model:ir.model,name:hr.model_mail_alias_mixin
msgid "mail.alias.mixin"
msgstr ""

#. module: hr
#: model:ir.model,name:hr.model_res_config_settings
msgid "res.config.settings"
msgstr ""

#. module: hr
#: model:res.groups,comment:hr.group_hr_user
msgid "the user will be able to approve document created by employees."
msgstr ""

#. module: hr
#: model:res.groups,comment:hr.group_hr_manager
msgid ""
"the user will have an access to the human resources configuration as well as"
" statistic reports."
msgstr ""
