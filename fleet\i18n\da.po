# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* fleet
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.res_config_settings_view_form
msgid "<span class=\"o_form_label\">End Date Contract Alert</span>"
msgstr "<span class=\"o_form_label\">Slutdato kontrakt advarsel</span>"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.res_config_settings_view_form
msgid "<span> days before the end date</span>"
msgstr "<span> dage før slutdatoen</span>"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.res_config_settings_view_form
msgid "<span>Send an alert </span>"
msgstr "<span>Send en advarsel </span>"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "<span>cm</span>"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "<span>g/km</span>"
msgstr "<span>g/km</span>"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "<span>kW</span>"
msgstr "<span>kW</span>"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_1
msgid "A/C Compressor Replacement"
msgstr "A/C Kompressor Udskiftning"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_2
msgid "A/C Condenser Replacement"
msgstr "A/C Kondensator Udskiftning"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_3
msgid "A/C Diagnosis"
msgstr "A/C Diagnose"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_4
msgid "A/C Evaporator Replacement"
msgstr "A/C fordamper udskiftning"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_5
msgid "A/C Recharge"
msgstr "A/C genoplad"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_needaction
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_needaction
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_needaction
msgid "Action Needed"
msgstr "Handling påkrævet"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Activation Cost"
msgstr "Aktiverings omkostning"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__active
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__active
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__active
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__active
msgid "Active"
msgstr "Aktiv"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_ids
msgid "Activities"
msgstr "Aktiviteter"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_exception_decoration
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_exception_decoration
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Aktivitet undtagelse markering"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_state
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_state
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_state
msgid "Activity State"
msgstr "Aktivitetstilstand"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_type_icon
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_type_icon
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_type_icon
msgid "Activity Type Icon"
msgstr "Aktivitets Type Ikon"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.mail_activity_type_action_config_fleet
#: model:ir.ui.menu,name:fleet.fleet_menu_config_activity_type
msgid "Activity Types"
msgstr "Aktivitetstyper"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_tag_action
msgid "Add a new tag"
msgstr "Tilføj et nyt tag"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__description
msgid "Add a note about this vehicle"
msgstr ""

#. module: fleet
#: model:res.groups,name:fleet.fleet_group_manager
msgid "Administrator"
msgstr "Administrator"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_6
msgid "Air Filter Replacement"
msgstr "Udskiftning af Luftfilter"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "All vehicles"
msgstr "Alle køretøjer"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_7
msgid "Alternator Replacement"
msgstr "Generator placering"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Apply New Driver"
msgstr ""

#. module: fleet
#. openerp-web
#: code:addons/fleet/static/src/js/fleet_form.js:0
#, python-format
msgid "Archive"
msgstr "Arkivér"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Archived"
msgstr "Arkiveret"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__next_assignation_date
msgid "Assignment Date"
msgstr "Tildelingsdato"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__log_drivers
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_assignation_log_view_list
msgid "Assignment Logs"
msgstr "Tildelings Logs"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_9
msgid "Assistance"
msgstr "Assistance"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_attachment_count
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_attachment_count
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_attachment_count
msgid "Attachment Count"
msgstr "Antal vedhæftninger"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_kanban
msgid "Attention: renewal overdue"
msgstr "OBS: Fornyelse overskredet"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__transmission__automatic
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__transmission__automatic
msgid "Automatic"
msgstr "Automatisk"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Available"
msgstr "Til rådighed"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_8
msgid "Ball Joint Replacement"
msgstr "Kuglelejre udskiftning"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_9
msgid "Battery Inspection"
msgstr "Batteri eftersyn"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_10
msgid "Battery Replacement"
msgstr "Udskiftning af batteri"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_cost_report__vehicle_type__bike
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__vehicle_type__bike
msgid "Bike"
msgstr "Cykel"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Bikes"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_11
msgid "Brake Caliper Replacement"
msgstr "Bremsekalibre udskiftning"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_12
msgid "Brake Inspection"
msgstr "Eftersyn af bremser"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_13
msgid "Brake Pad(s) Replacement"
msgstr "Bremsklods(er) udskiftning"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__brand_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Brand"
msgstr "Mærke"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_model_brand
msgid "Brand of the vehicle"
msgstr "Køretøjets mærke"

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_break
msgid "Break"
msgstr "Bremse"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__cng
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__cng
msgid "CNG"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__co2
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__default_co2
msgid "CO2 Emissions"
msgstr "CO2 Udledninger"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_tree
msgid "CO2 Emissions g/km"
msgstr "CO2 Udledning g/km"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__co2_standard
msgid "CO2 Standard"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__co2
msgid "CO2 emissions of the vehicle"
msgstr "Køretøjets CO2 udledninger"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_1
msgid "Calculation Benefit In Kind"
msgstr "Beregningsfordel in natura"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Cancel"
msgstr "Annullér"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_services__state__cancelled
msgid "Cancelled"
msgstr "Annulleret"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_cost_report__vehicle_type__car
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__vehicle_type__car
msgid "Car"
msgstr "Bil"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_14
msgid "Car Wash"
msgstr "Bilvask"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Cars"
msgstr "Biler"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__car_value
msgid "Catalog Value (VAT Incl.)"
msgstr "Katalog værdi (inkl. moms)"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_15
msgid "Catalytic Converter Replacement"
msgstr "Katalysator udskiftning"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__category
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__category_id
msgid "Category"
msgstr "Kategori"

#. module: fleet
#: model:ir.model.constraint,message:fleet.constraint_fleet_vehicle_model_category_name_uniq
msgid "Category name must be unique"
msgstr ""

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_model_category
msgid "Category of the model"
msgstr ""

#. module: fleet
#: model:mail.message.subtype,description:fleet.mt_fleet_driver_updated
#: model:mail.message.subtype,name:fleet.mt_fleet_driver_updated
msgid "Changed Driver"
msgstr "Skiftede fører"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_16
msgid "Charging System Diagnosis"
msgstr "Oplader system diagnostik"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__vin_sn
msgid "Chassis Number"
msgstr "Chassisnummer"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__state
msgid "Choose whether the contract is still valid or not"
msgstr "Vælg om kontrakten fortsat er gyldig eller ej"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_service_type__category
msgid ""
"Choose whether the service refer to contracts, vehicle services or both"
msgstr ""
"Vælg hvorvidt servicen referere til kontrakter, køretøj service, eller begge"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Close Contract"
msgstr "Luk kontrakt"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__contract_state__closed
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__state__closed
msgid "Closed"
msgstr "Lukket"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__co2_standard
msgid "Co2 Standard"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__color
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__color
msgid "Color"
msgstr "Farve"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__color
msgid "Color Index"
msgstr "Farve index"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__color
msgid "Color of the vehicle"
msgstr "Køretøjets farve"

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_compact
msgid "Compact"
msgstr "Kompakt"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__company_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__company_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__company_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__company_id
msgid "Company"
msgstr "Virksomhed"

#. module: fleet
#: model:ir.model,name:fleet.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurer opsætning"

#. module: fleet
#: model:ir.ui.menu,name:fleet.fleet_configuration
msgid "Configuration"
msgstr "Konfiguration"

#. module: fleet
#: model:ir.model,name:fleet.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_search
msgid "Contains Vehicles"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_service_type__category__contract
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_cost_report__cost_type__contract
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Contract"
msgstr "Kontrakt"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_graph
msgid "Contract Costs Per Month"
msgstr "Kontrakt omkostninger per måned"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__contract_count
msgid "Contract Count"
msgstr "Kontrakt antal"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__expiration_date
msgid "Contract Expiration Date"
msgstr "Kontrakt udløbsdato"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Contract Information"
msgstr "Kontrakt Information"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__start_date
msgid "Contract Start Date"
msgstr "Kontrakt startdato"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_tree
msgid "Contract logs"
msgstr "Kontrakt logs"

#. module: fleet
#: model:mail.activity.type,name:fleet.mail_act_fleet_contract_to_renew
msgid "Contract to Renew"
msgstr "Kontrakt der skal fornys"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_kanban
msgid "Contract(s)"
msgstr "Kontrakt(er)"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_log_contract_action
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__log_contracts
#: model:ir.ui.menu,name:fleet.fleet_vehicle_log_contract_menu
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Contracts"
msgstr "Kontrakter"

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_convertible
msgid "Convertible"
msgstr "Cabriolet"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__cost
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__amount
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__amount
msgid "Cost"
msgstr "Kostpris"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__cost_type
msgid "Cost Type"
msgstr "Udgiftstype"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Cost that is paid only once at the creation of the contract"
msgstr "Omkostning der er betalt kun én gang ved oprettelse af kontrakten"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__cost_subtype_id
msgid "Cost type purchased with this cost"
msgstr "Omkostningstype købt med denne pris"

#. module: fleet
#: model:ir.ui.menu,name:fleet.menu_fleet_reporting_costs
msgid "Costs"
msgstr "Omkostninger"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_costs_reporting_action
msgid "Costs Analysis"
msgstr "Omkostninger analyse"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__country_id
msgid "Country"
msgstr "Land"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__country_code
msgid "Country Code"
msgstr "Lande kode"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_model_category_action
msgid "Create a new category"
msgstr "Opret en ny kategori"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_contract_action
msgid "Create a new contract"
msgstr "Opret en ny kontrakt"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_model_brand_action
msgid "Create a new manufacturer"
msgstr "Opret en ny producent"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_model_action
msgid "Create a new model"
msgstr "Opret en ny model"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_odometer_action
msgid "Create a new odometer log"
msgstr "Opret en ny triptæller log"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_services_action
msgid "Create a new service entry"
msgstr "Opret en ny service indskrivning"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_service_types_action
msgid "Create a new type of service"
msgstr "Opret en ny type serice"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_state_action
msgid "Create a new vehicle status"
msgstr "Opret en ny køretøj status"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__create_uid
msgid "Created by"
msgstr "Oprettet af"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__create_date
msgid "Created on"
msgstr "Oprettet den"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__currency_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__currency_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Current Driver"
msgstr "Nuværende fører"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__state_id
msgid "Current state of the vehicle"
msgstr "Nuværende tilstand på køretøjet"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__cost_frequency__daily
msgid "Daily"
msgstr "Dagligt"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__date_start
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__date
msgid "Date"
msgstr "Dato"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__date
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__date
msgid "Date when the cost has been executed"
msgstr "Dato hvor omkostningen er blevet eksekveret"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__start_date
msgid "Date when the coverage of the contract begins"
msgstr "Dato når dækningen på kontrakten påbegynder"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__expiration_date
msgid ""
"Date when the coverage of the contract expirates (by default, one year after"
" begin date)"
msgstr ""
"Dato når dækningen af kontrakten udløber (per standard, et år efter "
"påbegyndelses datoen)"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__acquisition_date
msgid "Date when the vehicle has been immatriculated"
msgstr "Dato hvor køretøjet er blevet umatrikuleret"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_res_config_settings__delay_alert_contract
msgid "Delay alert contract outdated"
msgstr "Forsink advarsel kontrakt udløbet"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_2
msgid "Depreciation and Interests"
msgstr "Afskrivning og renter"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__description
msgid "Description"
msgstr "Beskrivelse"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__frame_type__diamant
msgid "Diamant"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__diesel
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__diesel
msgid "Diesel"
msgstr "Diesel"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__display_name
msgid "Display Name"
msgstr "Vis navn"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_services__state__done
msgid "Done"
msgstr "Udført"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_17
msgid "Door Window Motor/Regulator Replacement"
msgstr "Dør vindue motor/regulator udskiftning"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__doors
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__doors
msgid "Doors Number"
msgstr "Døre antal"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_downgraded
msgid "Downgraded"
msgstr "Nedgraderet"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__driver_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__driver_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__driver_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__purchaser_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__purchaser_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__driver_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Driver"
msgstr "Fører"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__driver_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__purchaser_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_odometer__driver_id
msgid "Driver address of the vehicle"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Drivers"
msgstr "Førere"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Drivers History"
msgstr "Førere historik"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__history_count
msgid "Drivers History Count"
msgstr "Førere historik antal"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_assignation_log
msgid "Drivers history on a vehicle"
msgstr "Førere historik for køretøj"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_kanban
msgid "Dropdown menu"
msgstr "Dropdown menu"

#. module: fleet
#. openerp-web
#: code:addons/fleet/static/src/js/fleet_form.js:0
#, python-format
msgid ""
"Each Services and contracts of this vehicle will be considered as Archived. "
"Are you sure that you want to archive this record?"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_contract_action
msgid ""
"Each contract (e.g.: leasing) may include several services\n"
"            (reparation, insurances, periodic maintenance)."
msgstr ""
"Hver kontrakt (f.eks.: leasing) kan inkludere flere tjenester\n"
"              (reparation, forsikringer, periodisk vedligeholdelse)."

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_service_types_action
msgid "Each service can used in contracts, as a standalone service or both."
msgstr ""
"Hver tjeneste kan bruges i kontrakter, som en enestående tjeneste, eller "
"begge."

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__electric
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__electric
msgid "Electric"
msgstr "Elektrisk"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__electric_assistance
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__electric_assistance
msgid "Electric Assistance"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_17
msgid "Emissions"
msgstr "Udledninger"

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_leasing
msgid "Employee Car"
msgstr "Medarbejderbil"

#. module: fleet
#: code:addons/fleet/models/fleet_vehicle_log_services.py:0
#, python-format
msgid "Emptying the odometer value of a vehicle is not allowed."
msgstr "Nulstilling af køretøjets kilometertæller er ikke tilladt."

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__date_end
msgid "End Date"
msgstr "Slut dato"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Engine"
msgstr "Motor"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_18
msgid "Engine Belt Inspection"
msgstr "Motor rem inspektion"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_19
msgid "Engine Coolant Replacement"
msgstr "Motor kølevæske udskiftning"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_20
msgid "Engine/Drive Belt(s) Replacement"
msgstr "Engine/Drivrem(me) udskiftning"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_13
msgid "Entry into service tax"
msgstr "Indskrivning ind i tjeneste afgift"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_21
msgid "Exhaust Manifold Replacement"
msgstr "Udstødningsmanifold udskiftning"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__contract_state__expired
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__state__expired
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
msgid "Expired"
msgstr "Udløbet"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__first_contract_date
msgid "First Contract Date"
msgstr "Første kontrakt dato"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Fiscality"
msgstr ""

#. module: fleet
#: model:ir.module.category,name:fleet.module_fleet_category
#: model:ir.ui.menu,name:fleet.fleet_vehicle_menu
#: model:ir.ui.menu,name:fleet.fleet_vehicles
#: model:ir.ui.menu,name:fleet.menu_root
#: model_terms:ir.ui.view,arch_db:fleet.res_config_settings_view_form
msgid "Fleet"
msgstr "Flåde"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_cost_report
msgid "Fleet Analysis Report"
msgstr "Flåde Analyse Rapport"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_graph
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vechicle_costs_report_view_tree
msgid "Fleet Costs Analysis"
msgstr "Flåde Omkostnings Analyse"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.res_config_settings_view_form
msgid "Fleet Management"
msgstr "Bilpark håndtering"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__manager_id
msgid "Fleet Manager"
msgstr "Flåde leder"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_service_type
msgid "Fleet Service Type"
msgstr "Flåde tjeneste type"

#. module: fleet
#: model:ir.actions.server,name:fleet.ir_cron_contract_costs_generator_ir_actions_server
#: model:ir.cron,cron_name:fleet.ir_cron_contract_costs_generator
#: model:ir.cron,name:fleet.ir_cron_contract_costs_generator
msgid "Fleet: Generate contracts costs based on costs frequency"
msgstr "Flåde: Generer kontrakt omkostninger baseret på omkostnings frekvens"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_follower_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_follower_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_follower_ids
msgid "Followers"
msgstr "Følgere"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_partner_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_partner_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_partner_ids
msgid "Followers (Partners)"
msgstr "Følgere (partnere)"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__activity_type_icon
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__activity_type_icon
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Skrifttype awesome icon f.eks. fa-opgaver"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__frame_size
msgid "Frame Size"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__frame_type
msgid "Frame Type"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__frame_type
msgid "Frame type of the bike"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__cost_frequency
msgid "Frequency of the recuring cost"
msgstr "Frekvens på gentagende omkostning"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__fuel_type
msgid "Fuel"
msgstr "Brændstof"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_22
msgid "Fuel Injector Replacement"
msgstr "Brændstofindsprøjterdyse udskiftning"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_23
msgid "Fuel Pump Replacement"
msgstr "Udskiftning af brændstofpumpe"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__fuel_type
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__default_fuel_type
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Fuel Type"
msgstr "Brændstoftype"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__fuel_type
msgid "Fuel Used by the vehicle"
msgstr "Brændstof anvendt af køretøjet"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Future Activities"
msgstr "Fremtidige aktiviteter"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__future_driver_id
msgid "Future Driver"
msgstr "Fremtidig føre"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_kanban
msgid "Future Driver :"
msgstr "Fremtidig føre :"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__gasoline
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__gasoline
msgid "Gasoline"
msgstr "Benzin"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Group By"
msgstr "Sortér efter"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__contract_renewal_overdue
msgid "Has Contracts Overdue"
msgstr "Forfaldne kontrakter"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__contract_renewal_due_soon
msgid "Has Contracts to renew"
msgstr "Har kontrakter til fornyelse"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__has_message
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__has_message
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__has_message
msgid "Has Message"
msgstr "Har besked"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_24
msgid "Head Gasket(s) Replacement"
msgstr "Toppakning(er) udskiftning"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_25
msgid "Heater Blower Motor Replacement"
msgstr "Varmeblæser motor udskiftning"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_26
msgid "Heater Control Valve Replacement"
msgstr "Varmeblæser kontrolventil udskiftning"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_27
msgid "Heater Core Replacement"
msgstr "Varme element udskiftning"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_28
msgid "Heater Hose Replacement"
msgstr "Varme slange udskiftning"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__horsepower
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__horsepower
msgid "Horsepower"
msgstr "Hestekræfter"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__horsepower_tax
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__horsepower_tax
msgid "Horsepower Taxation"
msgstr "Hestekræft afgift"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__hybrid
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__hybrid
msgid "Hybrid Diesel"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__full_hybrid_gasoline
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__full_hybrid_gasoline
msgid "Hybrid Gasoline"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__hydrogen
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__hydrogen
msgid "Hydrogen"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__id
msgid "ID"
msgstr "ID"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_exception_icon
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_exception_icon
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_exception_icon
msgid "Icon"
msgstr "Ikon"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__activity_exception_icon
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__activity_exception_icon
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikon for uventet aktivitet."

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__message_needaction
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__message_unread
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__message_needaction
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__message_unread
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__message_needaction
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__message_unread
msgid "If checked, new messages require your attention."
msgstr "Hvis afkrydset, kræver nye beskeder din opmærksomhed "

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__message_has_error
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__message_has_error
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Hvis afkrydset har nogle beskeder en leveringsfejl"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_29
msgid "Ignition Coil Replacement"
msgstr "Udskiftning af tændspole"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__acquisition_date
msgid "Immatriculation Date"
msgstr "Registreringsdato"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__contract_state__open
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__state__open
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
msgid "In Progress"
msgstr "I gang"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__service_ids
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Included Services"
msgstr "Inkluderede services"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__contract_state__futur
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__state__futur
msgid "Incoming"
msgstr "Indkommende"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
msgid "Information"
msgstr "Information"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_30
msgid "Intake Manifold Gasket Replacement"
msgstr "Indsugningsmanifoldpakning udskiftning"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Invoice Date"
msgstr "Fakturadato"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_is_follower
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_is_follower
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_is_follower
msgid "Is Follower"
msgstr "Er følger"

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_junior
msgid "Junior"
msgstr "Junior"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_kanban
msgid "Km"
msgstr "Km"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__lpg
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__lpg
msgid "LPG"
msgstr "LPG"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__contract_state
msgid "Last Contract State"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag____last_update
msgid "Last Modified on"
msgstr "Sidst ændret den"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__odometer
msgid "Last Odometer"
msgstr "Sidste kilometertæller"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__write_uid
msgid "Last Updated by"
msgstr "Sidst opdateret af"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__write_date
msgid "Last Updated on"
msgstr "Sidst opdateret den"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Late Activities"
msgstr "Overskredet aktiviteter"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_contract_leasing
msgid "Leasing"
msgstr "Lokation"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_action
msgid "Let's create your first vehicle."
msgstr "Lad os oprette dit første køretøj."

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__license_plate
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "License Plate"
msgstr "Nummerplade"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__license_plate
msgid "License plate number of the vehicle (i = plate number for a car)"
msgstr "Køretøjets nummerplade (i  = bil nummerplade)"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__location
msgid "Location"
msgstr "Adresse"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__location
msgid "Location of the vehicle (garage, ...)"
msgstr "Køretøjets lokation (garage, ...)"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__image_128
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__image_128
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__image_128
msgid "Logo"
msgstr "Logo"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_kanban
msgid "MODELS"
msgstr "MODELLER"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_main_attachment_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_main_attachment_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_main_attachment_id
msgid "Main Attachment"
msgstr "Vedhæftning"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__name
msgid "Make"
msgstr "Mærke"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_contract_action
msgid ""
"Manage all your contracts (leasing, insurances, etc.) with\n"
"            their related services, costs. Odoo will automatically warn\n"
"            you when some contracts have to be renewed."
msgstr ""
"Administrer alle dine kontrakter (leasing, forsikringer, etc.) med\n"
"             deres relaterede tjenester, omkostninger. Odoo vil automatisk advare\n"
"             dig når en kontrakt skal fornyes."

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_costs_reporting_action
msgid "Manage efficiently your different effective vehicles Costs with Odoo."
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_11
msgid "Management Fee"
msgstr "Administrations gebyr"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__transmission__manual
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__transmission__manual
msgid "Manual"
msgstr "Manuel"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__brand_id
msgid "Manufacturer"
msgstr "Producent"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__brand_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_model__brand_id
msgid "Manufacturer of the vehicle"
msgstr "Køretøjets producent"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_model_brand_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_model_brand_menu
msgid "Manufacturers"
msgstr "Producenter"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_has_error
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_has_error
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_has_error
msgid "Message Delivery error"
msgstr "Besked ved leveringsfejl"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_ids
msgid "Messages"
msgstr "Beskeder"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__model_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__model_ids
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Model"
msgstr "Model"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_model_category_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_model_category_menu
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_category_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_category_view_tree
msgid "Model Category"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__model_count
msgid "Model Count"
msgstr "Model Antal"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_tree
msgid "Model Make"
msgstr "Model mærke"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__model_year
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__model_year
msgid "Model Year"
msgstr "Model årgang"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__name
msgid "Model name"
msgstr "Model"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_model
msgid "Model of a vehicle"
msgstr "Køretøjets model"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__model_id
msgid "Model of the vehicle"
msgstr "Køretøjets model"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_kanban
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_tree
msgid "Models"
msgstr "Modeller"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__cost_frequency__monthly
msgid "Monthly"
msgstr "Månedlig"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__my_activity_date_deadline
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__my_activity_date_deadline
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Mine Aktiviteter Deadline"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__name
msgid "Name"
msgstr "Navn"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__contract_renewal_name
msgid "Name of contract to renew soon"
msgstr "Navn på kontrakt til fornyelse snart"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Need Action"
msgstr "Kræver handling"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_services__state__new
msgid "New"
msgstr "Ny"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_new_request
msgid "New Request"
msgstr "Ny forespørgsel"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_calendar_event_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_calendar_event_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Næste Aktivitet Kalender Arrangement"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_date_deadline
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_date_deadline
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Deadline for næste aktivitet"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_summary
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_summary
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_summary
msgid "Next Activity Summary"
msgstr "Oversigt over næste aktivitet"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_type_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_type_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_type_id
msgid "Next Activity Type"
msgstr "Næste aktivitetstype"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__future_driver_id
msgid "Next Driver Address of the vehicle"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__cost_frequency__no
msgid "No"
msgstr "Nej"

#. module: fleet
#: code:addons/fleet/models/fleet_vehicle.py:0
#, python-format
msgid "No Plate"
msgstr "Ingen nummerplade"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_costs_reporting_action
msgid "No data for analysis"
msgstr "Ingen data at analysere"

#. module: fleet
#: code:addons/fleet/models/fleet_vehicle.py:0
#, python-format
msgid "No plate"
msgstr "Ingen nummerplade"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Note"
msgstr "Notat"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__notes
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
msgid "Notes"
msgstr "Notater"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_needaction_counter
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_needaction_counter
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_needaction_counter
msgid "Number of Actions"
msgstr "Antal handlinger"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__doors
msgid "Number of doors of the vehicle"
msgstr "Antal af døre på køretøjet"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_has_error_counter
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_has_error_counter
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_has_error_counter
msgid "Number of errors"
msgstr "Antal fejl"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__message_needaction_counter
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__message_needaction_counter
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Antal meddelser der kræver handling"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__message_has_error_counter
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__message_has_error_counter
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Antal beskeder med leveringsfejl"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__seats
msgid "Number of seats of the vehicle"
msgstr "Antal siddepladser i køretøjet"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__message_unread_counter
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__message_unread_counter
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__message_unread_counter
msgid "Number of unread messages"
msgstr "Antal ulæste beskeder"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__odometer_count
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__odometer_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Odometer"
msgstr "Kilometertæller"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_tree
msgid "Odometer Logs"
msgstr "Kilometertæller logs"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__odometer_unit
msgid "Odometer Unit"
msgstr "Kilometertæller enhed"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__odometer
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__value
msgid "Odometer Value"
msgstr "Kilometertæller værdi"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_graph
msgid "Odometer Values Per Vehicle"
msgstr "Kilometertæller værdier per køretøj"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_odometer
msgid "Odometer log for a vehicle"
msgstr "Kilometertæller log for et køretøj"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__odometer
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__odometer
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__odometer_id
msgid "Odometer measure of the vehicle at the moment of this log"
msgstr "Kilometertæller mål ved denne log indskrivning"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_odometer_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_odometer_menu
msgid "Odometers"
msgstr "Kilometertæller"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_31
msgid "Oil Change"
msgstr "Udskiftning af olie"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_32
msgid "Oil Pump Replacement"
msgstr "Udskiftning af oliepumpe"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_contract_omnium
msgid "Omnium"
msgstr "Alle"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_16
msgid "Options"
msgstr "Valgmuligheder"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_ordered
msgid "Ordered"
msgstr "Bestilt"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_33
msgid "Other Maintenance"
msgstr "Anden vedligeholdelse"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_34
msgid "Oxygen Sensor Replacement"
msgstr "Ilt sensor udskiftning"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__plan_to_change_bike
#: model:ir.model.fields,field_description:fleet.field_res_partner__plan_to_change_bike
#: model:ir.model.fields,field_description:fleet.field_res_users__plan_to_change_bike
msgid "Plan To Change Bike"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__plan_to_change_car
#: model:ir.model.fields,field_description:fleet.field_res_partner__plan_to_change_car
#: model:ir.model.fields,field_description:fleet.field_res_users__plan_to_change_car
msgid "Plan To Change Car"
msgstr "Plan om at udskifte bil"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Planned for Change"
msgstr "Planlagt til udskiftning"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__plug_in_hybrid_diesel
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__plug_in_hybrid_diesel
msgid "Plug-in Hybrid Diesel"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__plug_in_hybrid_gasoline
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__plug_in_hybrid_gasoline
msgid "Plug-in Hybrid Gasoline"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__power
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__power
msgid "Power"
msgstr "Servo"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_35
msgid "Power Steering Hose Replacement"
msgstr "Servostyring slange udskiftning"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_36
msgid "Power Steering Pump Replacement"
msgstr "Servostyring pumpe udskiftning"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__power
msgid "Power in kW of the vehicle"
msgstr "Køretøjets kraft i kW"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_kanban
msgid "Properties"
msgstr "Egenskaber"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__net_car_value
msgid "Purchase Value"
msgstr "Købsværdi"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__net_car_value
msgid "Purchase value of the vehicle"
msgstr "Køretøjets købsværdi"

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_purchased
msgid "Purchased"
msgstr "Indkøbt"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_37
msgid "Radiator Repair"
msgstr "Radiator reaparation"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_action
msgid "Ready to manage your fleet more efficiently ?"
msgstr "Klar til at administrere din flåde mere effektivt ?"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__cost_generated
msgid "Recurring Cost"
msgstr "Gentagende Omkostning"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__cost_frequency
msgid "Recurring Cost Frequency"
msgstr "Gentagende omkostning frekvens"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__ins_ref
msgid "Reference"
msgstr "Reference"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_refueling
msgid "Refueling"
msgstr "Genoptankning"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_registered
msgid "Registered"
msgstr "Registreret"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_12
msgid "Rent (Excluding VAT)"
msgstr "Leje (Eksklusiv moms)"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_8
msgid "Repair and maintenance"
msgstr "Reparation og vedligeholdelse"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_contract_repairing
msgid "Repairing"
msgstr "Reparation"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_10
msgid "Replacement Vehicle"
msgstr "Erstatningskøretøj"

#. module: fleet
#: model:ir.ui.menu,name:fleet.menu_fleet_reporting
msgid "Reporting"
msgstr "Rapportering"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_reserve
msgid "Reserve"
msgstr "Reserver"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Reset To Draft"
msgstr "Nulstil til kladde"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__residual_value
msgid "Residual Value"
msgstr "Nedskrevet værdi"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_15
msgid "Residual value (Excluding VAT)"
msgstr "Restværdi (Eksklusiv moms)"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_19
msgid "Residual value in %"
msgstr "Restværdi i %"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__user_id
msgid "Responsible"
msgstr "Ansvarlig"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_user_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_user_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_user_id
msgid "Responsible User"
msgstr "Ansvarlig bruger"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_38
msgid "Resurface Rotors"
msgstr "Overfladebehandling af skiver"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_39
msgid "Rotate Tires"
msgstr "Rotér dæk"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_40
msgid "Rotor Replacement"
msgstr "Skive udskiftning"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_services__state__running
msgid "Running"
msgstr "Kører"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__seats
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__seats
msgid "Seats Number"
msgstr "Sæde antal"

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_sedan
msgid "Sedan"
msgstr "Sedan"

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_senior
msgid "Senior"
msgstr "Senior"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__sequence
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__sequence
msgid "Sequence"
msgstr "Sekvens"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_service_type__category__service
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_cost_report__cost_type__service
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_search
msgid "Service"
msgstr "Ydelse"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__service_type_id
msgid "Service Type"
msgstr "Service type"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_service_types_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_service_types_menu
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_service_types_view_tree
msgid "Service Types"
msgstr "Tjeneste typer"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_log_services_action
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__service_count
#: model:ir.ui.menu,name:fleet.fleet_vehicle_log_services_menu
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Services"
msgstr "Serviceydelser"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_graph
msgid "Services Costs Per Month"
msgstr "Service omkostninger per måned"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__log_services
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_tree
msgid "Services Logs"
msgstr "Service logs"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_log_services
msgid "Services for vehicles"
msgstr "Service på køretøjer"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_config_settings_action
#: model:ir.ui.menu,name:fleet.fleet_config_settings_menu
msgid "Settings"
msgstr "Opsætning"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Show all records which has next action date is before today"
msgstr "Vis alle poster, hvor den næste aktivitetsdato er før i dag"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_6
msgid "Snow tires"
msgstr "Vinterdæk"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_41
msgid "Spark Plug Replacement"
msgstr "Tændrør udskiftning"

#. module: fleet
#: code:addons/fleet/models/fleet_vehicle.py:0
#, python-format
msgid "Specify the End date of %s"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__state
msgid "Stage"
msgstr "Fase"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Start Contract"
msgstr "Påbegynd Kontrakt"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__date_start
msgid "Start Date"
msgstr "Start dato"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_42
msgid "Starter Replacement"
msgstr "Starter udskiftning"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__state_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_state_view_tree
msgid "State"
msgstr "Status"

#. module: fleet
#: model:ir.model.constraint,message:fleet.constraint_fleet_vehicle_state_fleet_state_name_unique
msgid "State name already exists"
msgstr "Navn på fase eksisterer allerede"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__state
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Status"
msgstr "Status"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__activity_state
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__activity_state
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status baseret på aktiviteter\n"
"Forfaldne: Forfaldsdato er allerede overskredet\n"
"I dag: Aktivitetsdato er i dag\n"
"Planlagt: Fremtidige aktiviteter."

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vechicle_costs_report_view_tree
msgid "Sum of Cost"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_5
#: model:fleet.service.type,name:fleet.type_service_service_7
msgid "Summer tires"
msgstr "Sommerdæk"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__name
msgid "Tag Name"
msgstr "Tag-navn"

#. module: fleet
#: model:ir.model.constraint,message:fleet.constraint_fleet_vehicle_tag_name_uniq
msgid "Tag name already exists !"
msgstr "Tag navn eksisterer allerede!"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__tag_ids
msgid "Tags"
msgstr "Tags"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Tax Info"
msgstr "Moms info"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_3
msgid "Tax roll"
msgstr "Skatterulle"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__notes
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Terms and Conditions"
msgstr "Salgs- og leveringsbetingelser"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"ISO-landekoden med to tegn. \n"
"Dette felt kan bruges til hurtig søgning."

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_43
msgid "Thermostat Replacement"
msgstr "Udskiftning af termostat"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__next_assignation_date
msgid ""
"This is the date at which the car will be available, if not set it means "
"available instantly"
msgstr ""
"Dette er den dato hvor bilen vil være tilgængelig, hvis ikke angivet, "
"betyder det at den er tilgængelig med det samme"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_44
msgid "Tie Rod End Replacement"
msgstr "Forbindelsesstang ende udskiftning"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_45
msgid "Tire Replacement"
msgstr "Udskiftning af dæk"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_46
msgid "Tire Service"
msgstr "Dækservice"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_to_order
msgid "To Order"
msgstr "At bestille"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Today Activities"
msgstr "Dagens aktiviteter"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_tree
msgid "Total"
msgstr "I alt"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_14
msgid "Total expenses (Excluding VAT)"
msgstr "Total udgifter (eksklusiv moms)"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__contract_renewal_total
msgid "Total of contracts due or overdue minus one"
msgstr "Samlet beløb for forfaldende som overskredne kontrakter, minus en"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_18
msgid "Touring Assistance"
msgstr "Tour assistance"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_services_action
msgid ""
"Track all the services done on your vehicle.\n"
"            Services can be of many types: occasional repair, fixed maintenance, etc."
msgstr ""
"Spor alle services udført på dit køretøj.\n"
"              Service kan være mange typer: lejlighedsvis reparation, fastsat vedligeholdelse, osv."

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__trailer_hook
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__trailer_hook
msgid "Trailer Hitch"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Trailer Hook"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__transmission
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__transmission
msgid "Transmission"
msgstr "Gearkasse"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_47
msgid "Transmission Filter Replacement"
msgstr "Gearkasse filter udskiftning"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_48
msgid "Transmission Fluid Replacement"
msgstr "Gearkasse væske udskiftning"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_49
msgid "Transmission Replacement"
msgstr "Gearkasse udskiftning"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__transmission
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_model__transmission
msgid "Transmission Used by the vehicle"
msgstr "Gearkasse anvendt af køretøjet"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__frame_type__trapez
msgid "Trapez"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__cost_subtype_id
msgid "Type"
msgstr "Type"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__activity_exception_decoration
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__activity_exception_decoration
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type af undtagelsesaktivitet registreret "

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__vin_sn
msgid "Unique number written on the vehicle motor (VIN/SN number)"
msgstr "Unik nummer skrevet på køretøjets motor (VIN/SN nummer)"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__odometer_unit
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__unit
msgid "Unit"
msgstr "Enhed"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__odometer_unit
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__odometer_unit
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_odometer__unit
msgid "Unit of the odometer "
msgstr "Enhed på kilometertæller"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_unread
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_unread
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_unread
msgid "Unread Messages"
msgstr "Ulæste beskeder"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_unread_counter
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_unread_counter
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Ulæste beskedtæller"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_state__sequence
msgid "Used to order the note stages"
msgstr "Brugt til at rangere notat stadierne"

#. module: fleet
#: model:res.groups,name:fleet.fleet_group_user
msgid "User"
msgstr "Bruger"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__car_value
msgid "Value of the bought vehicle"
msgstr "Værdi af købt køretøj"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__vehicle_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__vehicle_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__vehicle_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__vehicle_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__vehicle_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_tree
msgid "Vehicle"
msgstr "Køretøj"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_log_contract
msgid "Vehicle Contract"
msgstr "Køretøj Kontrakt"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__vehicle_count
msgid "Vehicle Count"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__description
msgid "Vehicle Description"
msgstr "Køretøj Beskrivelse"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
msgid "Vehicle Information"
msgstr "Køretøj Information"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_model_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_model_menu
msgid "Vehicle Models"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__name
msgid "Vehicle Name"
msgstr "Køretøj Navn"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_state_action
#: model:ir.model,name:fleet.model_fleet_vehicle_state
#: model:ir.ui.menu,name:fleet.fleet_vehicle_state_menu
msgid "Vehicle Status"
msgstr "Køretøj status"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_tag
msgid "Vehicle Tag"
msgstr "Køretøj tag"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_tag_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_tag_menu
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_tag_view_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_tag_view_view_tree
msgid "Vehicle Tags"
msgstr "Køretøj tags"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__vehicle_type
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__vehicle_type
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__vehicle_type
msgid "Vehicle Type"
msgstr "Køretøj Type"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__vehicle_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__vehicle_id
msgid "Vehicle concerned by this log"
msgstr "Køretøj denne log omhandler"

#. module: fleet
#: code:addons/fleet/models/fleet_vehicle_model.py:0
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_action
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_tree
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_activity
#, python-format
msgid "Vehicles"
msgstr "Køretøjer"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_activity
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
msgid "Vehicles Contracts"
msgstr "Køretøjer kontrakter"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_search
msgid "Vehicles costs"
msgstr "Køretøjer omkostninger"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_search
msgid "Vehicles odometers"
msgstr "Køretøjer kilometertællere"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__insurer_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__vendor_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
msgid "Vendor"
msgstr "Leverandør"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__inv_ref
msgid "Vendor Reference"
msgstr "Leverandør reference/fakturanr."

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__vendors
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
msgid "Vendors"
msgstr "Leverandører"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_waiting_list
msgid "Waiting List"
msgstr "Venteliste"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__days_left
msgid "Warning Date"
msgstr "Advarsels dato"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_kanban
msgid "Warning: renewal due soon"
msgstr "Advarsel: Fornyelse forfalder snart"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_50
msgid "Water Pump Replacement"
msgstr "Udskiftning af vandpumpe"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__frame_type__wave
msgid "Wave"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__cost_frequency__weekly
msgid "Weekly"
msgstr "Ugentlig"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_51
msgid "Wheel Alignment"
msgstr "Hjulsudmåling"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_52
msgid "Wheel Bearing Replacement"
msgstr "Udskiftning af hjulleje"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_53
msgid "Windshield Wiper(s) Replacement"
msgstr "Udskiftning af viduesvisker(e)"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_search
msgid "With Models"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Write here all other information relative to this contract"
msgstr "Skriv her al anden information vedrørende denne kontrakt"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__notes
msgid "Write here all supplementary information relative to this contract"
msgstr "Skriv her alle uddybende information vedrørende denne kontrakt"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
msgid "Write here any other information related to the service completed."
msgstr "Skriv her enhver anden information vedrørende den udførte tjeneste."

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Write here any other information related to this vehicle"
msgstr ""
"Her kan du skrive enhver anden information relateret til dette køretøj."

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__model_year
msgid "Year of the model"
msgstr "År på model"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__cost_frequency__yearly
msgid "Yearly"
msgstr "Årligt"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_odometer_action
msgid "You can add various odometer entries for all vehicles."
msgstr ""
"Du kan tilføje diverse kilometertæller indskrivninger for alle køretøjer."

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_state_action
msgid ""
"You can customize available status to track the evolution of\n"
"            each vehicle. Example: active, being repaired, sold."
msgstr ""
"Du kan tilpasse tilgængelig status for at spore udvikling på\n"
"            hvert køretøj. Eksempel: aktiv, under reparation, solgt."

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_model_action
msgid "You can define several models (e.g. A3, A4) for each make (Audi)."
msgstr ""
"Du kan definere flere modeller (f.eks. A3, A4) for hvert mærke (Audi)."

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "e.g. Model S"
msgstr "fx Model S"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "e.g. PAE 326"
msgstr "fx PAE 326"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
msgid "e.g. Tesla"
msgstr "fx Tesla"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_kanban
msgid "img"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__odometer_unit__kilometers
msgid "km"
msgstr "km"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__odometer_unit__miles
msgid "mi"
msgstr "mil"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "show the contract for this vehicle"
msgstr "vis kontrakter for dette køretøj"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "show the odometer logs for this vehicle"
msgstr "vis kilometertæller logs for dette køretøj"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "show the services logs for this vehicle"
msgstr "vis service logs for dette køretøj"
