# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_expense
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 06:48+0000\n"
"PO-Revision-Date: 2018-10-08 06:48+0000\n"
"Last-Translator: Bole <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid ""
"<i class=\"text-muted oe_edit_only\">Use [Reference] as a subject prefix for"
" incoming receipts</i>"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tour.js:49
#, python-format
msgid ""
"<p>Approve the report here.</p><p>Tip: if you refuse, don’t forget to give "
"the reason thanks to the hereunder message tool</p>"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tour.js:41
#, python-format
msgid ""
"<p>Click on <b> Action Create Report </b> to submit selected expenses to "
"your manager</p>"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tour.js:32
#, python-format
msgid "<p>Click on <b> Create Report </b> to create the report.</p>"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tour.js:23
#, python-format
msgid "<p>Once your <b> Expense </b> is ready, you can save it.</p>"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tour.js:36
#, python-format
msgid "<p>Select expenses to submit them to your manager</p>"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tour.js:53
#, python-format
msgid ""
"<p>The accountant receive approved expense reports.</p><p>He can post "
"journal entries in one click if taxes and accounts are right.</p>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Date:</strong>"
msgstr "<strong>Datum:</strong>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Description:</strong>"
msgstr "<strong>Opis:</strong>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Employee:</strong>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Payment By:</strong>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Total</strong>"
msgstr "<strong>Ukupno</strong>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Validated By:</strong>"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/wizard/hr_expense_sheet_register_payment.py:104
#, python-format
msgid ""
"A payment of %s %s with the reference <a href='/mail/view?%s'>%s</a> related"
" to your expense %s has been made."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__account_id
msgid "Account"
msgstr "Konto"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Accounting"
msgstr "Računovodstvo"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_needaction
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_needaction
msgid "Action Needed"
msgstr "Potrebna akcija"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_ids
msgid "Activities"
msgstr "Aktivnosti"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_state
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_state
msgid "Activity State"
msgstr "Status aktivnosti"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.mail_activity_type_action_config_hr_expense
#: model:ir.ui.menu,name:hr_expense.hr_expense_menu_config_activity_type
msgid "Activity Types"
msgstr "Tipovi aktivnosti"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "Actual expense sheets, not the refused ones"
msgstr "Trenutna lista troškova, ne odbijeni"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:158
#, python-format
msgid "Add a new expense,"
msgstr ""

#. module: hr_expense
#: model:product.product,name:hr_expense.air_ticket
#: model:product.template,name:hr_expense.air_ticket_product_template
msgid "Air Flight"
msgstr "Let"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all_all
msgid "All Expense Reports"
msgstr "Izvještaj svih troškova"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_actions_all
msgid "All Expenses"
msgstr "Svi troškovi"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__account_id
msgid "An expense account is expected"
msgstr "Očekivan je konto troška"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__analytic_account_id
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "Analytic Account"
msgstr "Analitički konto"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__analytic_tag_ids
msgid "Analytic Tags"
msgstr "Analitičke oznake"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Approve"
msgstr "Odobri"

#. module: hr_expense
#: selection:hr.expense,state:0 selection:hr.expense.sheet,state:0
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
#: model:mail.message.subtype,name:hr_expense.mt_expense_approved
msgid "Approved"
msgstr "Odobren"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "Approved Expenses"
msgstr "Odobreni troškovi"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Attach Document"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tour.js:27
#, python-format
msgid "Attach your receipt here."
msgstr "Ovdje zakačite račun"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_attachment_count
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_attachment_count
msgid "Attachment Count"
msgstr "Broj zakački"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__bank_journal_id
msgid "Bank Journal"
msgstr "Izvod"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__reference
msgid "Bill Reference"
msgstr "Referenca računa"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__can_reset
msgid "Can Reset"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_product_template__can_be_expensed
msgid "Can be Expensed"
msgstr "Može se staviti u trošak"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_register_payment_view_form
msgid "Cancel"
msgstr "Otkaži"

#. module: hr_expense
#: model:product.product,name:hr_expense.car_travel
#: model:product.template,name:hr_expense.car_travel_product_template
msgid "Car Travel Expenses"
msgstr "Troškovi vozila za putovanje"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Certified honest and conform,<br/>(Date and signature).<br/><br/>"
msgstr ""

#. module: hr_expense
#: selection:hr.expense,payment_mode:0
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__company_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__company_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_register_payment_wizard__company_id
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "Company"
msgstr "Kompanija"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet_register_payment_wizard__company_id
msgid "Company related to this journal"
msgstr "Kompanija za koju se vodi ovaj dnevnik"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_configuration
msgid "Configuration"
msgstr "Konfiguracija"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "Confirmed Expenses"
msgstr "Potvrđeni Troškovi"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Create Report"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_request_approve_expense_sheet
#: model_terms:ir.actions.act_window,help:hr_expense.action_request_to_pay_expense_sheet
#: model_terms:ir.actions.act_window,help:hr_expense.action_request_to_post_expense_sheet
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_all
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_unsubmitted
msgid "Create a new expense"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_product
msgid "Create a new expense category"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_all
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_pay
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_post
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_my_all
msgid "Create a new expense report"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_register_payment_wizard__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_register_payment_wizard__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__currency_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__currency_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_register_payment_wizard__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__accounting_date
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "Date"
msgstr "Datum"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__expense_alias_prefix
msgid "Default Alias Name for Expenses"
msgstr "Zadani nadimak za trošak"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__department_id
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "Department"
msgstr "Odjeljenje"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__name
msgid "Description"
msgstr "Opis"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_register_payment_wizard__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Documents"
msgstr "Dokumenti"

#. module: hr_expense
#: selection:hr.expense.sheet,state:0
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "Draft"
msgstr "U pripremi"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_register_payment_view_form
msgid "Draft Payment"
msgstr "Plaćanje u pripremi"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Email Alias"
msgstr "Email nadimak"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Emails"
msgstr "E-mail"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_employee
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__employee_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__employee_id
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "Employee"
msgstr "Zaposleni"

#. module: hr_expense
#: selection:hr.expense,payment_mode:0
msgid "Employee (to reimburse)"
msgstr "Zaposleni (za nadoknadu)"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_approved_expense
msgid "Employee Expenses"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__address_id
msgid "Employee Home Address"
msgstr "Kućna adresa zaposlenog"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_account_move_line__expense_id
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Expense"
msgstr "Trošak"

#. module: hr_expense
#: model:mail.activity.type,name:hr_expense.mail_act_expense_approval
msgid "Expense Approval"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "Expense Date"
msgstr "Datum troška"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__journal_id
msgid "Expense Journal"
msgstr "Dnevnik troška"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__expense_line_ids
msgid "Expense Lines"
msgstr "Stavke troškova"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_product
#: model:ir.ui.menu,name:hr_expense.menu_hr_product
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Expense Products"
msgstr "Proizvod troška"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_refuse_wizard
msgid "Expense Refuse Reason Wizard"
msgstr ""

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_sheet_register_payment_wizard
msgid "Expense Register Payment Wizard"
msgstr ""

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_sheet
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__sheet_id
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Expense Report"
msgstr "Izvještaj troška"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__state
msgid "Expense Report State"
msgstr "Izvještaj statusa troška"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__name
msgid "Expense Report Summary"
msgstr "Sumarni izvještaj troška"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_report
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Expense Reports"
msgstr "Izvještaji troška"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_department_filtered
msgid "Expense Reports Analysis"
msgstr "Analiza izvještaja troška"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all_to_pay
msgid "Expense Reports To Pay"
msgstr "Izvještaji troška za plaćanje"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all_to_post
msgid "Expense Reports To Post"
msgstr "Izvještaji troška za knjiženje"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all_to_approve
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_department_to_approve
msgid "Expense Reports to Approve"
msgstr "Izvještaji troška za odobrenje"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_employee__expense_manager_id
msgid "Expense Responsible"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
msgid "Expense refuse reason"
msgstr "Razlog odbijanja troška"

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_approved
msgid "Expense report approved"
msgstr "Odobren izvještaj troška"

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_paid
msgid "Expense report paid"
msgstr "Plaćen izvještaj troška"

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_refused
msgid "Expense report refused"
msgstr "Odbijen izvještaj troška"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_account_move_line__expense_id
msgid "Expense where the move line come from"
msgstr "Trošak sa kojeg potiče stavka kretanja"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_root
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_employee_view_form_inherit_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_expenses_tree
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
#: model:product.product,name:hr_expense.product_product_fixed_cost
#: model:product.template,name:hr_expense.product_product_fixed_cost_product_template
msgid "Expenses"
msgstr "Troškovi"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_action
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_all_expenses
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_graph
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_graph
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_pivot
msgid "Expenses Analysis"
msgstr "Analiza troška"

#. module: hr_expense
#: model:ir.actions.report,name:hr_expense.action_report_hr_expense_sheet
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Expenses Report"
msgstr "Izvještaj troška"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_department__expense_sheets_to_approve_count
msgid "Expenses Reports to Approve"
msgstr "Izvještaji troška za odobrenje"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "Expenses by Date"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "Expenses in Draft"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:568
#, python-format
msgid "Expenses must be paid by the same entity (Company or employee)."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:623
#, python-format
msgid ""
"Expenses must have an expense journal specified to generate accounting "
"entries."
msgstr ""
"Troškovi moraju imati specifiran dnevnik troška kako bi se generisali "
"računovodstveni zapisi."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "Expenses of Your Team Member"
msgstr "Troškovi vašeg člana tima"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "Expenses to Invoice"
msgstr "Troškovi za fakturisati"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.account_journal_dashboard_kanban_view_inherit_hr_expense
msgid "Expenses to Process"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__is_refused
msgid "Explicitely Refused by manager or acccountant"
msgstr "Eksplicitno odbijeno od strane upravnika ili računovođe"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_follower_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_follower_ids
msgid "Followers"
msgstr "Pratioci"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_channel_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_channel_ids
msgid "Followers (Channels)"
msgstr "Pratioci (Kanali)"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_partner_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_partner_ids
msgid "Followers (Partners)"
msgstr "Pratioci (Partneri)"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "Former Employees"
msgstr "Bivši zaposleni"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_approved_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_unsubmitted_expense
msgid ""
"From here the accountant will be able to approve as well as refuse the "
"expenses which are verified by the HR Manager."
msgstr ""
"Odavde računovođa će biti u mogućnosti da odobri kao i odbije troškove koji "
"su verifikovani od strane upravnika zaposlenih."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "Future Activities"
msgstr "Buduće aktivnosti"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "General Information"
msgstr "Opšte informacije"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "Group By"
msgstr "Grupiši po"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_department
msgid "HR Department"
msgstr "Odjeljenje ljudskih resursa"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__is_multiple_currency
msgid "Handle lines with different currencies"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_register_payment_wizard__hide_payment_method
msgid "Hide Payment Method"
msgstr "Sakrij način plaćanja"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__hr_expense_ids
msgid "Hr Expense"
msgstr "Troškovi ljudskih resursa"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__hr_expense_sheet_id
msgid "Hr Expense Sheet"
msgstr "Lista troškova ljudskih resursa"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_register_payment_wizard__id
msgid "ID"
msgstr "ID"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_unread
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_unread
msgid "If checked new messages require your attention."
msgstr "Ako je označeno nove poruke će zahtjevati vašu pažnju."

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_needaction
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Ako je zakačeno, nove poruke će zahtjevati vašu pažnju"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_has_error
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_is_follower
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_is_follower
msgid "Is Follower"
msgstr "Je pratilac"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_journal
msgid "Journal"
msgstr "Dnevnik knjiženja"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__account_move_id
msgid "Journal Entry"
msgstr "Dnevnički zapis"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_move_line
msgid "Journal Item"
msgstr "Stavka dnevnika"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense____last_update
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard____last_update
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet____last_update
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_register_payment_wizard____last_update
msgid "Last Modified on"
msgstr "Zadnje mijenjano"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_register_payment_wizard__write_uid
msgid "Last Updated by"
msgstr "Zadnji ažurirao"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_register_payment_wizard__write_date
msgid "Last Updated on"
msgstr "Zadnje ažurirano"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "Late Activities"
msgstr "Aktivnosti u kašnjenju"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__use_mailgateway
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Let your employees record expenses by email"
msgstr "Omogućava vašim zaposlenima evidenciju troškova putem email-a"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_main_attachment_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_main_attachment_id
msgid "Main Attachment"
msgstr "Glavna zakačka"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__user_id
#: model:res.groups,name:hr_expense.group_hr_expense_manager
msgid "Manager"
msgstr "Upravitelj"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tour.js:61
#, python-format
msgid "Managers can get all reports to approve from this menu."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_register_payment_wizard__communication
msgid "Memo"
msgstr "Zabilješka"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_has_error
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_ids
msgid "Messages"
msgstr "Poruke"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_my_all
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_sheet_my_reports
msgid "My Expense Reports"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_actions_my_unsubmitted
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_my_expenses
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_my_expenses_to_submit
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "My Expenses"
msgstr "Moji Troškovi"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "My Reports"
msgstr "Moji izvještaji"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "My Team Expenses"
msgstr "Troškovi mog tima"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "My Team Reports"
msgstr "Izvještaji troška mog tima"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Name"
msgstr "Naziv:"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:186
#, python-format
msgid "New Expense Report"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_date_deadline
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Krajnji rok za sljedeću aktivnost"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_summary
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_summary
msgid "Next Activity Summary"
msgstr "Pregled sljedeće aktivnosti"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_type_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_type_id
msgid "Next Activity Type"
msgstr "Tip sljedeće aktivnosti"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:244
#, python-format
msgid ""
"No Expense account found for the product %s (or for its category), please "
"configure one."
msgstr ""
"Nije pronađen konto troška za proizvod %s (ili njegovu kategoriju), molimo "
"vas da podesite jedan."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:261
#, python-format
msgid "No Home Address found for the employee %s, please configure one."
msgstr "Nije pronađena kućna adresa zaposlenog %s, molimo da unesete neku."

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_approved_expense
msgid "No approved employee expenses"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:257
#: code:addons/hr_expense/models/hr_expense.py:368
#, python-format
msgid "No credit account found for the %s journal, please configure one."
msgstr ""
"Nije definisan potražni konto za dnevnik %s , molimo konfigurišite jedan."

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_approve
msgid "No expense reports to approve"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_unsubmitted_expense
msgid "No unreported employee expenses"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__description
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Notes..."
msgstr "Zabilješke..."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_needaction_counter
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_needaction_counter
msgid "Number of Actions"
msgstr "Broj akcija"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__attachment_number
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__attachment_number
msgid "Number of Attachments"
msgstr "Broj zakački"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_has_error_counter
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_has_error_counter
msgid "Number of error"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_needaction_counter
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Broj poruka koje zahtjevaju neku akciju"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_has_error_counter
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_unread_counter
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_unread_counter
msgid "Number of unread messages"
msgstr "Broj nepročitanih poruka"

#. module: hr_expense
#: model:res.groups,name:hr_expense.group_hr_expense_user
msgid "Officer"
msgstr "Oficir"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_all
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_pay
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_post
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_my_all
#: model_terms:ir.actions.act_window,help:hr_expense.action_request_approve_expense_sheet
#: model_terms:ir.actions.act_window,help:hr_expense.action_request_to_pay_expense_sheet
#: model_terms:ir.actions.act_window,help:hr_expense.action_request_to_post_expense_sheet
msgid ""
"Once you have created your expense, submit it to your manager who will "
"validate it."
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tour.js:45
#, python-format
msgid ""
"Once your <b>Expense report</b> is ready, you can submit it to your manager "
"and wait for the approval from your manager."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:706
#, python-format
msgid "Only HR Officers or the concerned employee can reset to draft."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:667
#: code:addons/hr_expense/models/hr_expense.py:688
#, python-format
msgid "Only Managers and HR Officers can approve expenses"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Other Info"
msgstr "Ostale informacije"

#. module: hr_expense
#: selection:hr.expense,activity_state:0
#: selection:hr.expense.sheet,activity_state:0
msgid "Overdue"
msgstr "Dospjele"

#. module: hr_expense
#: selection:hr.expense,state:0 selection:hr.expense.sheet,state:0
#: model:mail.message.subtype,name:hr_expense.mt_expense_paid
msgid "Paid"
msgstr "Plaćeno"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__payment_mode
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__payment_mode
msgid "Paid By"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_register_payment_wizard__partner_id
msgid "Partner"
msgstr "Partner"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_register_payment_wizard__amount
msgid "Payment Amount"
msgstr "Iznos plaćanja"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_register_payment_wizard__payment_date
msgid "Payment Date"
msgstr "Datum plaćanja"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_register_payment_wizard__journal_id
msgid "Payment Method"
msgstr "Metoda plaćanja"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_register_payment_wizard__payment_method_id
msgid "Payment Type"
msgstr "Tip plaćanja"

#. module: hr_expense
#: selection:hr.expense,activity_state:0
#: selection:hr.expense.sheet,activity_state:0
msgid "Planned"
msgstr "Planiran"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:248
#, python-format
msgid ""
"Please configure Default Expense account for Product expense: "
"`property_account_expense_categ_id`."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Post Journal Entries"
msgstr "Proknjiži zapise dnevnika"

#. module: hr_expense
#: selection:hr.expense.sheet,state:0
msgid "Posted"
msgstr "Proknjižen"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Price"
msgstr "Cijena"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Price in Company Currency"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_id
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "Product"
msgstr "Proizvod"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Product Name"
msgstr "Naziv proizvoda"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_product_template
msgid "Product Template"
msgstr "Predlog proizvoda"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Qty"
msgstr "Količina"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__quantity
msgid "Quantity"
msgstr "Količina"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__reason
msgid "Reason"
msgstr "Razlog"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "Reason :"
msgstr "Razlog:"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
msgid "Reason to refuse Expense"
msgstr "Razlog za odbijanje troška"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_register_payment_wizard__partner_bank_account_id
msgid "Recipient Bank Account"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Recording"
msgstr "Zabilježavanje"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Ref."
msgstr "Ref."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Refuse"
msgstr "Odbij"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_refuse_wizard_action
msgid "Refuse Expense"
msgstr "Odbi trošak"

#. module: hr_expense
#: selection:hr.expense,state:0 selection:hr.expense.sheet,state:0
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
#: model:mail.message.subtype,name:hr_expense.mt_expense_refused
msgid "Refused"
msgstr "Odbijeno"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "Refused Expenses"
msgstr "Odbijeni troškovi"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_sheet_register_payment_wizard_action
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_register_payment_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Register Payment"
msgstr "Registracija uplate"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "Report"
msgstr "Izvještaj"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__company_currency_id
msgid "Report Company Currency"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "Reported"
msgstr "Prijavljen"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_reports
msgid "Reporting"
msgstr "Izvještavanje"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_request_approve_expense_sheet
msgid "Reports to Approve"
msgstr "Izvještaji za odobrenje"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_request_to_pay_expense_sheet
msgid "Reports to Pay"
msgstr "Izvještaji za plaćanje"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_request_to_post_expense_sheet
msgid "Reports to Post"
msgstr "Izvještaji za knjiženje"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Reset to Draft"
msgstr "Vrati u pripremu"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_user_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_user_id
msgid "Responsible User"
msgstr "Odgovorni korisnik"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:135
#, python-format
msgid ""
"Selected Unit of Measure does not belong to the same category as the product"
" Unit of Measure."
msgstr ""
"Odabrana jedinica mjere ne pripada istoj kategoriji kao jedinica mjere "
"proizvoda"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_configuration
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_global_settings
msgid "Settings"
msgstr "Postavke"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Setup your domain alias"
msgstr "Postavite Vaš nadimak domene"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet_register_payment_wizard__show_partner_bank_account
msgid "Show Partner Bank Account"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "Show all records which has next action date is before today"
msgstr "Prikaži sve zapise koji imaju datum sljedeće akcije prije danas"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_product_template__can_be_expensed
msgid "Specify whether the product can be selected in an expense."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "State"
msgstr "Rep./Fed."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__state
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__state
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "Status"
msgstr "Status"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__activity_state
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__state
msgid "Status of the expense."
msgstr "Status troška."

#. module: hr_expense
#: model:ir.actions.server,name:hr_expense.hr_expense_submit_action_server
msgid "Submit Report"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Submit to Manager"
msgstr "Podnesi upravitelju"

#. module: hr_expense
#: selection:hr.expense,state:0 selection:hr.expense.sheet,state:0
msgid "Submitted"
msgstr "Podneseno"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__untaxed_amount
msgid "Subtotal"
msgstr "Podukupno"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__tax_ids
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Taxes"
msgstr "Porezi"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet_register_payment_wizard__hide_payment_method
msgid ""
"Technical field used to hide the payment method if the selected journal has "
"only one available which is 'manual'"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet_register_payment_wizard__show_partner_bank_account
msgid ""
"Technical field used to know whether the field `partner_bank_account_id` "
"needs to be displayed or not in the payments form views"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tour.js:57
#, python-format
msgid ""
"The accountant can register a payment to reimburse the employee directly."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__journal_id
msgid "The journal used when the expense is done."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/wizard/hr_expense_sheet_register_payment.py:48
#, python-format
msgid "The payment amount must be strictly positive."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__bank_journal_id
msgid "The payment method used when the expense is paid by the company."
msgstr ""

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_sheet_all_to_approve
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "To Approve"
msgstr "Za odobriti"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_sheet_all_to_pay
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "To Pay"
msgstr "Za platiti"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_sheet_all_to_post
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "To Post"
msgstr "Za knjiženje"

#. module: hr_expense
#: selection:hr.expense,state:0
msgid "To Submit"
msgstr "Za podnošenje"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
msgid "To report"
msgstr ""

#. module: hr_expense
#: selection:hr.expense,activity_state:0
#: selection:hr.expense.sheet,activity_state:0
msgid "Today"
msgstr "Danas"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_filter
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_filter
msgid "Today Activities"
msgstr "Današnje aktivnosti"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__total_amount
msgid "Total"
msgstr "Ukupno"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__total_amount_company
msgid "Total (Company Currency)"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__total_amount
#: model_terms:ir.ui.view,arch_db:hr_expense.view_expenses_tree
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Total Amount"
msgstr "Ukupan iznos"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__unit_amount
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Unit Price"
msgstr "Jedinična cijena"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_uom_id
msgid "Unit of Measure"
msgstr "Jedinica mjere"

#. module: hr_expense
#: model:product.product,uom_name:hr_expense.air_ticket
#: model:product.product,uom_name:hr_expense.product_product_fixed_cost
#: model:product.template,uom_name:hr_expense.air_ticket_product_template
#: model:product.template,uom_name:hr_expense.product_product_fixed_cost_product_template
msgid "Unit(s)"
msgstr "kom (komad)"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_unread
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_unread
msgid "Unread Messages"
msgstr "Nepročitane poruke"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_unread_counter
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Brojač nepročitanih poruka"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_unsubmitted_expense
msgid "Unreported Expenses"
msgstr "Neprijavljeni troškovi"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_employee__expense_manager_id
msgid "User responsible of expense approval. Should be Expense Manager."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_register_payment_view_form
msgid "Validate"
msgstr "Odobri"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_expenses_tree
msgid "View Attached Documents"
msgstr "Pogledaj zakačene dokumente"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "View Attachments"
msgstr "Pogledaj zakačke"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "View Report"
msgstr "Pogledaj izvještaj"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tour.js:13
#: code:addons/hr_expense/static/src/js/tour.js:18
#, python-format
msgid "Want to manage your expenses? It starts here."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__website_message_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__website_message_ids
msgid "Website Messages"
msgstr "Poruke sa website-a"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__website_message_ids
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:675
#, python-format
msgid "You can only approve your department expenses"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:620
#, python-format
msgid "You can only generate accounting entry for approved expense(s)."
msgstr "Možete samo generisati računovodstvene zapise za odobrene troškove."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:696
#, python-format
msgid "You can only refuse your department expenses"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid ""
"You can setup a generic email alias to create incoming expenses easily. "
"Write an email with the receipt in attachment to create an expense line in "
"one click. If the mail subject contains the product's internal reference "
"between brackets, the product will be set automatically. Type the expense "
"amount in the mail subject to set it on the expense too."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:575
#, python-format
msgid "You cannot add expenses of another employee."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:672
#, python-format
msgid "You cannot approve your own expenses"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:145
#, python-format
msgid "You cannot delete a posted or approved expense."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:587
#, python-format
msgid "You cannot delete a posted or paid expense."
msgstr "Ne možete obrisati knjižene ili plaćene troškove."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:693
#, python-format
msgid "You cannot refuse your own expenses"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:182
#, python-format
msgid "You cannot report expenses for different employees in the same report."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:180
#, python-format
msgid "You cannot report twice the same line!"
msgstr "Ne možete dva puta prijaviti istu stavku!"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "Your Expense"
msgstr "Vaš trošak"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "e.g. Lunch"
msgstr "npr.: Ručak"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "e.g. Lunch with Customer"
msgstr "npr.: Ručak sa kupcem"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "e.g. Trip to NY"
msgstr "npr.: Putovanje u Banja Luku"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "has been refused"
msgstr "je odbijen"

#. module: hr_expense
#: model:product.product,weight_uom_name:hr_expense.air_ticket
#: model:product.product,weight_uom_name:hr_expense.car_travel
#: model:product.product,weight_uom_name:hr_expense.product_product_fixed_cost
#: model:product.template,weight_uom_name:hr_expense.air_ticket_product_template
#: model:product.template,weight_uom_name:hr_expense.car_travel_product_template
#: model:product.template,weight_uom_name:hr_expense.product_product_fixed_cost_product_template
msgid "kg"
msgstr "kg"

#. module: hr_expense
#: model:product.product,uom_name:hr_expense.car_travel
#: model:product.template,uom_name:hr_expense.car_travel_product_template
msgid "km"
msgstr "km"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:159
#, python-format
msgid "or send receipts by email to %s."
msgstr ""
