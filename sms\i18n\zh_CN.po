# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sms
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# xu aaron, 2021
# <PERSON> <<PERSON>.<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> CHEN <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:54+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON><PERSON> CHEN <<EMAIL>>, 2022\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_invalid_count
msgid "# Invalid recipients"
msgstr "# 无效的收件人"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_valid_count
msgid "# Valid recipients"
msgstr "#有效的收件人"

#. module: sms
#: code:addons/sms/models/sms_template.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (副本)"

#. module: sms
#. openerp-web
#: code:addons/sms/static/src/js/fields_sms_widget.js:0
#: code:addons/sms/static/src/js/fields_sms_widget.js:0
#, python-format
msgid "%s characters, fits in %s SMS (%s) "
msgstr "%s 字符， 适合 %s 短信 (%s) "

#. module: sms
#: code:addons/sms/wizard/sms_composer.py:0
#, python-format
msgid "%s invalid recipients"
msgstr "%s无效的收件人"

#. module: sms
#: code:addons/sms/models/sms_sms.py:0
#, python-format
msgid ""
"%s out of the %s selected SMS Text Messages have successfully been resent."
msgstr "所选的%s条短信中的%s条已成功重发。"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid ""
"<span class=\"fa fa-info-circle\"/> Caution: It won't be possible to send "
"this SMS again to the recipients you did not select."
msgstr "<span class=\"fa fa-info-circle\"/> 提示:这不会将邮件发送给未选中的收件者。"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid ""
"<span class=\"o_stat_text\">Add</span>\n"
"                                <span class=\"o_stat_text\">Context Action</span>"
msgstr ""
"<span class=\"o_stat_text\">添加</span>\n"
"                                <span class=\"o_stat_text\">上下文操作</span>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid ""
"<span class=\"o_stat_text\">Remove</span>\n"
"                                <span class=\"o_stat_text\">Context Action</span>"
msgstr ""
"<span class=\"o_stat_text\">移除</span>\n"
"                                <span class=\"o_stat_text\">上下文操作</span>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid ""
"<span class=\"text-warning\" attrs=\"{'invisible': [('no_record', '=', "
"False)]}\">No records</span>"
msgstr ""
"<span class=\"text-warning\" attrs=\"{'invisible': [('no_record', '=', "
"False)]}\">无记录</span>"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_needaction
msgid "Action Needed"
msgstr "需要采取动作"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_base_automation__state
#: model:ir.model.fields,field_description:sms.field_ir_actions_server__state
#: model:ir.model.fields,field_description:sms.field_ir_cron__state
msgid "Action To Do"
msgstr "待办的动作"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__active_domain
msgid "Active domain"
msgstr "启用域名"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__active_domain_count
msgid "Active records count"
msgstr "启用记录计数"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid ""
"Add a contextual action on the related model to open a sms composer with "
"this template"
msgstr "在相关的模型上添加上下文操作以使用此模板打开短信作者"

#. module: sms
#. openerp-web
#: code:addons/sms/static/src/components/notification_group/notification_group.xml:0
#, python-format
msgid "An error occurred when sending an SMS."
msgstr "发送短信时发生错误。"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__model_id
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__model_id
msgid "Applies to"
msgstr "应用于"

#. module: sms
#: code:addons/sms/wizard/sms_cancel.py:0
#, python-format
msgid ""
"Are you sure you want to discard %s SMS delivery failures? You won't be able"
" to re-send these SMS later!"
msgstr "您确定要丢弃%s短信发送失败？您以后就不能再重新发送这些短信了!"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_attachment_count
msgid "Attachment Count"
msgstr "附件数量"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_blacklist
msgid "Blacklisted"
msgstr "列入黑名单"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__mobile_blacklisted
msgid "Blacklisted Phone Is Mobile"
msgstr "列入黑名单的手机是移动的"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__phone_blacklisted
msgid "Blacklisted Phone is Phone"
msgstr "列入黑名单的电话是电话"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__body
#: model:ir.model.fields,field_description:sms.field_sms_template__body
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__body
msgid "Body"
msgstr "正文"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Buy credits"
msgstr "购买信用"

#. module: sms
#: code:addons/sms/models/sms_api.py:0
#, python-format
msgid "Buy credits."
msgstr "购买信用。"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_cancel
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Cancel"
msgstr "取消"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_cancel
msgid "Cancel notification in failure"
msgstr "取消失败通知"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__canceled
msgid "Canceled"
msgstr "已取消"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Check"
msgstr "检查"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "Choose a language:"
msgstr "选择一种语言："

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "Choose an example"
msgstr "选择一个示例"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Close"
msgstr "关闭"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__composition_mode
msgid "Composition Mode"
msgstr "合成模式"

#. module: sms
#: model:ir.model,name:sms.model_res_partner
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Contact"
msgstr "联系人"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "Content"
msgstr "内容"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_cancel__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_composer__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_resend__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_sms__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_template__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__create_uid
msgid "Created by"
msgstr "创建人"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_cancel__create_date
#: model:ir.model.fields,field_description:sms.field_sms_composer__create_date
#: model:ir.model.fields,field_description:sms.field_sms_resend__create_date
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__create_date
#: model:ir.model.fields,field_description:sms.field_sms_sms__create_date
#: model:ir.model.fields,field_description:sms.field_sms_template__create_date
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__create_date
msgid "Created on"
msgstr "创建时间"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__partner_id
msgid "Customer"
msgstr "客户"

#. module: sms
#: model:sms.template,name:sms.sms_template_demo_0
msgid "Customer: automated SMS"
msgstr "客户：自动短信"

#. module: sms
#: model:sms.template,body:sms.sms_template_demo_0
msgid "Dear {{ object.display_name }} this is an automated SMS."
msgstr "亲爱的 {{ object.display_name }} 这是一条自动短信。"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__null_value
msgid "Default Value"
msgstr "默认值"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "Discard"
msgstr "丢弃"

#. module: sms
#: model:ir.actions.act_window,name:sms.sms_cancel_action
msgid "Discard SMS delivery failures"
msgstr "丢弃短信发送失败"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_cancel
msgid "Discard delivery failures"
msgstr "放弃发送失败"

#. module: sms
#: model:ir.model,name:sms.model_sms_cancel
msgid "Dismiss notification for resend by model"
msgstr "重发请求被拒绝"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_cancel__display_name
#: model:ir.model.fields,field_description:sms.field_sms_composer__display_name
#: model:ir.model.fields,field_description:sms.field_sms_resend__display_name
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__display_name
#: model:ir.model.fields,field_description:sms.field_sms_sms__display_name
#: model:ir.model.fields,field_description:sms.field_sms_template__display_name
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: sms
#: model:ir.model,name:sms.model_mail_followers
msgid "Document Followers"
msgstr "单据关注者"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_id
msgid "Document ID"
msgstr "文档ID"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_ids
msgid "Document IDs"
msgstr "文档 IDs"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_model
msgid "Document Model Name"
msgstr "文档模型名称"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_duplicate
msgid "Duplicate"
msgstr "复制"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "Dynamic Placeholder Generator"
msgstr "动态定位符生成器"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Edit Partners"
msgstr "编辑伙伴"

#. module: sms
#: model:ir.model,name:sms.model_mail_thread
msgid "Email Thread"
msgstr "邮件主题"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__error
msgid "Error"
msgstr "错误"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__failure_type
msgid "Failure Type"
msgstr "失败类型"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_notification__failure_type
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__failure_type
msgid "Failure type"
msgstr "失败类型"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__model_object_field
msgid "Field"
msgstr "字段"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__phone_sanitized
msgid ""
"Field used to store sanitized phone number. Helps speeding up searches and "
"comparisons."
msgstr "用于存储已消毒的电话号码的字段。有助于加快搜索和比较。"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__copyvalue
msgid ""
"Final placeholder expression, to be copy-pasted in the desired template "
"field."
msgstr "最终的占位符表达式，可以复制粘贴到目标模版字段."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_follower_ids
msgid "Followers"
msgstr "关注者"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_partner_ids
msgid "Followers (Partners)"
msgstr "关注者(业务伙伴)"

#. module: sms
#: code:addons/sms/wizard/sms_composer.py:0
#, python-format
msgid "Following numbers are not correctly encoded: %s"
msgstr "以下数字未正确编码：%s"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__has_cancel
msgid "Has Cancel"
msgstr "已取消"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__has_insufficient_credit
msgid "Has Insufficient Credit"
msgstr "信用不足"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__has_message
msgid "Has Message"
msgstr "有信息"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_mail__has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_message__has_sms_error
msgid "Has SMS error"
msgstr "有短信错误"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__has_unregistered_account
msgid "Has Unregistered Account"
msgstr "拥有未注册的账户"

#. module: sms
#: model:ir.model.fields,help:sms.field_mail_mail__has_sms_error
#: model:ir.model.fields,help:sms.field_mail_message__has_sms_error
msgid "Has error"
msgstr "有误差"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_cancel__help_message
msgid "Help message"
msgstr "帮助消息"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_cancel__id
#: model:ir.model.fields,field_description:sms.field_sms_composer__id
#: model:ir.model.fields,field_description:sms.field_sms_resend__id
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__id
#: model:ir.model.fields,field_description:sms.field_sms_sms__id
#: model:ir.model.fields,field_description:sms.field_sms_template__id
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__id
msgid "ID"
msgstr "ID"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__message_needaction
#: model:ir.model.fields,help:sms.field_res_partner__message_unread
msgid "If checked, new messages require your attention."
msgstr "确认后, 出现提示消息."

#. module: sms
#: model:ir.model.fields,help:sms.field_account_analytic_account__message_has_sms_error
#: model:ir.model.fields,help:sms.field_calendar_event__message_has_sms_error
#: model:ir.model.fields,help:sms.field_crm_team__message_has_sms_error
#: model:ir.model.fields,help:sms.field_crm_team_member__message_has_sms_error
#: model:ir.model.fields,help:sms.field_fleet_vehicle__message_has_sms_error
#: model:ir.model.fields,help:sms.field_fleet_vehicle_log_contract__message_has_sms_error
#: model:ir.model.fields,help:sms.field_fleet_vehicle_log_services__message_has_sms_error
#: model:ir.model.fields,help:sms.field_gamification_badge__message_has_sms_error
#: model:ir.model.fields,help:sms.field_gamification_challenge__message_has_sms_error
#: model:ir.model.fields,help:sms.field_hr_contract__message_has_sms_error
#: model:ir.model.fields,help:sms.field_hr_department__message_has_sms_error
#: model:ir.model.fields,help:sms.field_hr_employee__message_has_sms_error
#: model:ir.model.fields,help:sms.field_hr_job__message_has_sms_error
#: model:ir.model.fields,help:sms.field_hr_leave__message_has_sms_error
#: model:ir.model.fields,help:sms.field_hr_leave_allocation__message_has_sms_error
#: model:ir.model.fields,help:sms.field_lunch_supplier__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_blacklist__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_channel__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread_blacklist__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread_cc__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread_phone__message_has_sms_error
#: model:ir.model.fields,help:sms.field_maintenance_equipment__message_has_sms_error
#: model:ir.model.fields,help:sms.field_maintenance_equipment_category__message_has_sms_error
#: model:ir.model.fields,help:sms.field_maintenance_request__message_has_sms_error
#: model:ir.model.fields,help:sms.field_note_note__message_has_sms_error
#: model:ir.model.fields,help:sms.field_phone_blacklist__message_has_sms_error
#: model:ir.model.fields,help:sms.field_product_product__message_has_sms_error
#: model:ir.model.fields,help:sms.field_product_template__message_has_sms_error
#: model:ir.model.fields,help:sms.field_res_partner__message_has_error
#: model:ir.model.fields,help:sms.field_res_partner__message_has_sms_error
#: model:ir.model.fields,help:sms.field_res_users__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "如果勾选此项， 某些消息将会产生传递错误。"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__phone_sanitized_blacklisted
msgid ""
"If the sanitized phone number is on the blacklist, the contact won't receive"
" mass mailing sms anymore, from any list"
msgstr "如果已被列入的电话号码在黑名单中，则该联系人将不会再收到来自任何列表的群发短信"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_cancel
msgid ""
"If you want to re-send them, click Cancel now, then click on the "
"notification and review them one by one by clicking on the red icon next to "
"each message."
msgstr "如果您想重新发送，请点击取消，然后点击每条消息旁边的红色信封提示逐一审查。"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Ignore all"
msgstr "忽略所有"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__outgoing
msgid "In Queue"
msgstr "排队"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__mobile_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a mobile number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr "指示列入黑名单的已消毒电话号码是否为手机号码。帮助区分在模型中同时存在手机和手机字段时列入黑名单的号码。"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__phone_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a phone number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr "指示列入黑名单的已消毒电话号码是否为电话号码。帮助区分在模型中同时存在手机和手机字段时列入黑名单的号码。"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_composer__comment_single_recipient
msgid "Indicates if the SMS composer targets a single specific recipient"
msgstr "表明短信合成器是否针对单一的特定收件人"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_credit
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_credit
msgid "Insufficient Credit"
msgstr "信用不足"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Invalid phone number"
msgstr "无效的电话号码"

#. module: sms
#: code:addons/sms/wizard/sms_composer.py:0
#, python-format
msgid "Invalid recipient number. Please update it."
msgstr "无效的收件人号码。请更新它。"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_is_follower
msgid "Is Follower"
msgstr "关注者"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_single_valid
msgid "Is valid"
msgstr "是有效的"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__mass_keep_log
msgid "Keep a note on document"
msgstr "记下文件"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__lang
msgid "Language"
msgstr "语言"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_cancel____last_update
#: model:ir.model.fields,field_description:sms.field_sms_composer____last_update
#: model:ir.model.fields,field_description:sms.field_sms_resend____last_update
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient____last_update
#: model:ir.model.fields,field_description:sms.field_sms_sms____last_update
#: model:ir.model.fields,field_description:sms.field_sms_template____last_update
#: model:ir.model.fields,field_description:sms.field_sms_template_preview____last_update
msgid "Last Modified on"
msgstr "最后修改时间"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_cancel__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_composer__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_resend__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_sms__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_template__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_cancel__write_date
#: model:ir.model.fields,field_description:sms.field_sms_composer__write_date
#: model:ir.model.fields,field_description:sms.field_sms_resend__write_date
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__write_date
#: model:ir.model.fields,field_description:sms.field_sms_sms__write_date
#: model:ir.model.fields,field_description:sms.field_sms_template__write_date
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__write_date
msgid "Last Updated on"
msgstr "最后更新时间"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_base_automation__sms_mass_keep_log
#: model:ir.model.fields,field_description:sms.field_ir_actions_server__sms_mass_keep_log
#: model:ir.model.fields,field_description:sms.field_ir_cron__sms_mass_keep_log
msgid "Log as Note"
msgstr "记录为备注"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__mail_message_id
msgid "Mail Message"
msgstr "邮件消息"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_ir_model__is_mail_thread_sms
msgid "Mail Thread SMS"
msgstr "邮件线程短信"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_main_attachment_id
msgid "Main Attachment"
msgstr "主要附件"

#. module: sms
#: model:ir.model,name:sms.model_mail_message
#: model:ir.model.fields,field_description:sms.field_sms_composer__body
#: model:ir.model.fields,field_description:sms.field_sms_resend__mail_message_id
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Message"
msgstr "消息"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_has_error
msgid "Message Delivery error"
msgstr "消息传递错误"

#. module: sms
#: model:ir.model,name:sms.model_mail_notification
msgid "Message Notifications"
msgstr "消息通知"

#. module: sms
#: model:ir.model.fields,help:sms.field_mail_mail__message_type
#: model:ir.model.fields,help:sms.field_mail_message__message_type
msgid ""
"Message type: email for email message, notification for system message, "
"comment for other messages such as user replies"
msgstr "消息类型：电子邮件用于邮件消息， 通知用户系统消息，评论用于其他消息，例如用户回复"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_ids
msgid "Messages"
msgstr "消息"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_number_missing
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_number_missing
msgid "Missing Number"
msgstr "遗失号码"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_cancel__model
msgid "Model"
msgstr "模型"

#. module: sms
#: model:ir.model,name:sms.model_ir_model
msgid "Models"
msgstr "模型"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__name
msgid "Name"
msgstr "名称"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__no_record
msgid "No Record"
msgstr "没有记录"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__notification_id
msgid "Notification"
msgstr "通知"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_notification__notification_type
msgid "Notification Type"
msgstr "通知类型"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__sms_number
#: model:ir.model.fields,field_description:sms.field_sms_sms__number
msgid "Number"
msgstr "号码"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__number_field_name
msgid "Number Field"
msgstr "数值字段"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_needaction_counter
msgid "Number of Actions"
msgstr "动作数量"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_has_error_counter
msgid "Number of errors"
msgstr "错误数"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "需要作业消息数量"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "发送错误的消息数量"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_composer__res_ids_count
msgid ""
"Number of recipients that will receive the SMS if sent in mass mode, without"
" applying the Active Domain value"
msgstr "如果以群发模式发送，将收到短信的收件人数量，不应用活动域值"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_composer__active_domain_count
msgid "Number of records found when searching with the value in Active Domain"
msgstr "用活跃条件中的值搜索时发现的记录数量"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__message_unread_counter
msgid "Number of unread messages"
msgstr "未读消息数量"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_optout
msgid "Opted Out"
msgstr "选择退出"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"在发送邮件时可选择的语言代码(ISO 代码)。如果没有设置，会使用英文版本。一般用占位符来确定合适的语言，例如: {{ "
"object.partner_id.lang }}."

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__null_value
msgid "Optional value to use if the target field is empty"
msgstr "如果目标字段为空则使用此可选值"

#. module: sms
#: model:ir.model,name:sms.model_sms_sms
msgid "Outgoing SMS"
msgstr "外发短信"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__partner_id
msgid "Partner"
msgstr "业务伙伴"

#. module: sms
#: model:ir.model,name:sms.model_mail_thread_phone
msgid "Phone Blacklist Mixin"
msgstr "手机黑名单Mixin"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__phone_sanitized_blacklisted
msgid "Phone Blacklisted"
msgstr "电话加黑"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__phone_mobile_search
msgid "Phone/Mobile"
msgstr "电话/手机"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__copyvalue
msgid "Placeholder Expression"
msgstr "占位符表达式"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_composer__composition_mode__comment
msgid "Post on a document"
msgstr "提交在文件上"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "Preview"
msgstr "预览"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "Preview of"
msgstr "预览"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Put in queue"
msgstr "排队"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Reason"
msgstr "原因"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__partner_name
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Recipient"
msgstr "收件人"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_single_number_itf
msgid "Recipient Number"
msgstr "收货人号码"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__recipient_ids
msgid "Recipients"
msgstr "收件人"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__numbers
msgid "Recipients (Numbers)"
msgstr "收件人（数字）"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_single_description
msgid "Recipients (Partners)"
msgstr "收件人（合作伙伴）"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__resource_ref
msgid "Record reference"
msgstr "参照记录"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__model
msgid "Related Document Model"
msgstr "相关的单据模型"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "Remove the contextual action of the related model"
msgstr "删除相关模型的上下文操作"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__render_model
msgid "Rendering Model"
msgstr "呈现模型"

#. module: sms
#: model:ir.actions.server,name:sms.ir_actions_server_sms_sms_resend
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__resend
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Resend"
msgstr "重发"

#. module: sms
#: model:ir.model,name:sms.model_sms_resend_recipient
msgid "Resend Notification"
msgstr "重新发送通知"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Retry"
msgstr "重试"

#. module: sms
#. openerp-web
#: code:addons/sms/static/src/components/message/message.xml:0
#: code:addons/sms/static/src/components/message/message.xml:0
#: model:ir.actions.act_window,name:sms.sms_sms_action
#: model:ir.model.fields,field_description:sms.field_mail_notification__sms_id
#: model:ir.model.fields.selection,name:sms.selection__mail_message__message_type__sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__notification_type__sms
#: model:ir.ui.menu,name:sms.sms_sms_menu
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
#, python-format
msgid "SMS"
msgstr "短信"

#. module: sms
#: model:ir.model,name:sms.model_sms_api
msgid "SMS API"
msgstr "短信API"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_account_analytic_account__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_calendar_event__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_crm_team__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_crm_team_member__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_fleet_vehicle__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_fleet_vehicle_log_contract__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_fleet_vehicle_log_services__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_gamification_badge__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_gamification_challenge__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_hr_contract__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_hr_department__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_hr_employee__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_hr_job__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_hr_leave__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_hr_leave_allocation__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_lunch_supplier__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_blacklist__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_channel__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread_blacklist__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread_cc__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread_phone__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_maintenance_equipment__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_maintenance_equipment_category__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_maintenance_request__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_note_note__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_phone_blacklist__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_product_product__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_product_template__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_res_partner__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_res_users__message_has_sms_error
msgid "SMS Delivery error"
msgstr "短信发送错误"

#. module: sms
#. openerp-web
#: code:addons/sms/static/src/models/notification_group/notification_group.js:0
#, python-format
msgid "SMS Failures"
msgstr "短信失败"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_notification__sms_number
msgid "SMS Number"
msgstr "短信号码"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "SMS Preview"
msgstr "短信预览"

#. module: sms
#. openerp-web
#: code:addons/sms/static/src/js/fields_sms_widget.js:0
#: code:addons/sms/static/src/js/fields_sms_widget.js:0
#, python-format
msgid "SMS Pricing"
msgstr "短信价格"

#. module: sms
#: model:ir.model,name:sms.model_sms_resend
msgid "SMS Resend"
msgstr "短信重新发送"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__state
msgid "SMS Status"
msgstr "短信状态"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_base_automation__sms_template_id
#: model:ir.model.fields,field_description:sms.field_ir_actions_server__sms_template_id
#: model:ir.model.fields,field_description:sms.field_ir_cron__sms_template_id
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "SMS Template"
msgstr "短信模板"

#. module: sms
#: model:ir.model,name:sms.model_sms_template_preview
msgid "SMS Template Preview"
msgstr "短信模板预览"

#. module: sms
#: model:ir.model,name:sms.model_sms_template
#: model:ir.ui.menu,name:sms.sms_template_menu
#: model_terms:ir.ui.view,arch_db:sms.sms_sms_view_tree
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_tree
msgid "SMS Templates"
msgstr "短信模板"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "SMS content"
msgstr "短信内容"

#. module: sms
#: model:ir.actions.server,name:sms.ir_cron_sms_scheduler_action_ir_actions_server
#: model:ir.cron,cron_name:sms.ir_cron_sms_scheduler_action
#: model:ir.cron,name:sms.ir_cron_sms_scheduler_action
msgid "SMS: SMS Queue Manager"
msgstr "内容: SMS队列管理员"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__phone_sanitized
#: model:ir.model.fields,field_description:sms.field_sms_composer__sanitized_numbers
msgid "Sanitized Number"
msgstr "消毒数量"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_sms_view_search
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_search
msgid "Search SMS Templates"
msgstr "搜索短信模板"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__model_object_field
msgid ""
"Select target field from the related document model.\n"
"If it is a relationship field you will be able to select a target field at the destination of the relationship."
msgstr ""
"从相关单据模型中选择目标字段。\n"
"如果这是个关系型字段，您可以选择关系型字段的目标字段。"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Send Now"
msgstr "立即发送"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Send SMS"
msgstr "发送短信"

#. module: sms
#: code:addons/sms/models/sms_template.py:0
#, python-format
msgid "Send SMS (%s)"
msgstr "发送短信 (%s)"

#. module: sms
#. openerp-web
#: code:addons/sms/static/src/js/fields_phone_widget.js:0
#: code:addons/sms/static/src/js/fields_phone_widget.js:0
#: model:ir.actions.act_window,name:sms.res_partner_act_window_sms_composer_multi
#: model:ir.actions.act_window,name:sms.res_partner_act_window_sms_composer_single
#: model:ir.actions.act_window,name:sms.sms_composer_action_form
#: model:ir.model.fields.selection,name:sms.selection__ir_actions_server__state__sms
#, python-format
msgid "Send SMS Text Message"
msgstr "发送文本短信"

#. module: sms
#: model:ir.model,name:sms.model_sms_composer
msgid "Send SMS Wizard"
msgstr "发送短信向导"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_composer__composition_mode__mass
msgid "Send SMS in batch"
msgstr "批处理发送短信"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Send an SMS"
msgstr "发送一条短信"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__mass_force_send
msgid "Send directly"
msgstr "直接发送"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_composer__composition_mode__numbers
msgid "Send to numbers"
msgstr "发送到数字"

#. module: sms
#: model:ir.actions.act_window,name:sms.sms_resend_action
msgid "Sending Failures"
msgstr "发送失败"

#. module: sms
#: code:addons/sms/models/ir_actions.py:0
#, python-format
msgid "Sending SMS can only be done on a mail.thread model"
msgstr "发送短信只能在mail.thread模型上完成"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__sent
msgid "Sent"
msgstr "已发送"

#. module: sms
#: model:ir.model,name:sms.model_ir_actions_server
msgid "Server Action"
msgstr "服务器动作"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_server
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_server
msgid "Server Error"
msgstr "服务器错误"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Set up an account"
msgstr "建立一个账户"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__sidebar_action_id
msgid "Sidebar action"
msgstr "边栏动作"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__sidebar_action_id
msgid ""
"Sidebar action to make this template available on records of the related "
"document model"
msgstr "用于在相关单据上调用此模版的边栏按钮"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__comment_single_recipient
msgid "Single Mode"
msgstr "单篇模式"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__sms_resend_id
msgid "Sms Resend"
msgstr "短信重新发送"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__sms_template_id
msgid "Sms Template"
msgstr "短信模板"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_single_number
msgid "Stored Recipient Number"
msgstr "存储的收件人号码"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__sub_model_object_field
msgid "Sub-field"
msgstr "子字段"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__sub_object
msgid "Sub-model"
msgstr "子模型"

#. module: sms
#: code:addons/sms/models/sms_sms.py:0
#, python-format
msgid "Success"
msgstr "成功"

#. module: sms
#: model:ir.actions.act_window,name:sms.sms_template_preview_action
msgid "Template Preview"
msgstr "模板预览"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__lang
msgid "Template Preview Language"
msgstr "模板预览语言"

#. module: sms
#: model:ir.actions.act_window,name:sms.sms_template_action
msgid "Templates"
msgstr "模板"

#. module: sms
#: code:addons/sms/models/sms_sms.py:0
#, python-format
msgid "The SMS Text Messages could not be resent."
msgstr "短信文本无法重新发送。"

#. module: sms
#: code:addons/sms/models/sms_api.py:0
#, python-format
msgid "The number you're trying to reach is not correctly formatted."
msgstr "您想联系的号码格式不正确。"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__model_id
#: model:ir.model.fields,help:sms.field_sms_template_preview__model_id
msgid "The type of document this template can be used with"
msgstr "该模板可使用的单据类型"

#. module: sms
#: code:addons/sms/models/sms_sms.py:0
#, python-format
msgid "There are no SMS Text Messages to resend."
msgstr "不存在需要重新发送的SMS短信。"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.res_partner_view_form
msgid ""
"This phone number is blacklisted for SMS Marketing. Click to unblacklist."
msgstr "此电话号码被列入短信营销黑名单。单击以取消黑名单。"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_mail__message_type
#: model:ir.model.fields,field_description:sms.field_mail_message__message_type
msgid "Type"
msgstr "类型"

#. module: sms
#: model:ir.model.fields,help:sms.field_base_automation__state
#: model:ir.model.fields,help:sms.field_ir_actions_server__state
#: model:ir.model.fields,help:sms.field_ir_cron__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Execute Python Code': a block of python code that will be executed\n"
"- 'Create': create a new record with new values\n"
"- 'Update a Record': update the values of a record\n"
"- 'Execute several actions': define an action that triggers several other server actions\n"
"- 'Send Email': automatically send an email (Discuss)\n"
"- 'Add Followers': add followers to a record (Discuss)\n"
"- 'Create Next Activity': create an activity (Discuss)"
msgstr ""
"服务器操作的类型。 提供以下值：\n"
"-“执行Python代码”：将要执行的python代码块\n"
"-'创建'：使用新值创建新记录\n"
"-“更新记录”：更新记录的值\n"
"-“执行多个动作”：定义一个触发其他几个服务器动作的动作\n"
"-“发送电子邮件”：自动发送电子邮件（讨论）\n"
"-“添加关注者”：将关注者添加到记录中（讨论）\n"
"-“创建下一个活动”：创建活动（讨论）"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_composer__recipient_single_number_itf
msgid ""
"UX field allowing to edit the recipient number. If changed it will be stored"
" onto the recipient."
msgstr "允许编辑收件人号码的用户体验字段。如果改变，它将被存储到收件人身上。"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_unread
msgid "Unread Messages"
msgstr "未读消息"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_unread_counter
msgid "Unread Messages Counter"
msgstr "未读消息数"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_acc
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_acc
msgid "Unregistered Account"
msgstr "未注册的账户"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__template_id
msgid "Use Template"
msgstr "使用模版"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__use_active_domain
msgid "Use active domain"
msgstr "使用启用域名"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__mass_use_blacklist
msgid "Use blacklist"
msgstr "使用黑名单"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_ids_count
msgid "Visible records count"
msgstr "可见记录数"

#. module: sms
#: code:addons/sms/models/sms_sms.py:0
#, python-format
msgid "Warning"
msgstr "警告"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__website_message_ids
msgid "Website Messages"
msgstr "网站消息"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__website_message_ids
msgid "Website communication history"
msgstr "网上沟通记录"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__sub_model_object_field
msgid ""
"When a relationship field is selected as first field, this field lets you "
"select the target field within the destination document model (sub-model)."
msgstr "如果首先选择了一个关系型字段，这个字段可用于选择目标单据模型的目标字段。"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__sub_object
msgid ""
"When a relationship field is selected as first field, this field shows the "
"document model the relationship goes to."
msgstr "如果关系型字段被选为第一个字段，这个字段显示这个关系指向的单据模型。"

#. module: sms
#: model:ir.model.fields,help:sms.field_ir_model__is_mail_thread_sms
msgid "Whether this model supports messages and notifications through SMS"
msgstr "该模型是否支持通过短信发送消息和通知"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_number_format
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_number_format
msgid "Wrong Number Format"
msgstr "错误的数字格式"

#. module: sms
#: code:addons/sms/wizard/sms_resend.py:0
#, python-format
msgid "You do not have access to the message and/or related document."
msgstr "您无权访问消息和/或相关的文档。"

#. module: sms
#: code:addons/sms/models/sms_api.py:0
#, python-format
msgid "You don't have an eligible IAP account."
msgstr "您没有一个合格的IAP账户。"

#. module: sms
#: code:addons/sms/models/sms_api.py:0
#, python-format
msgid "You don't have enough credits on your IAP account."
msgstr "您的IAP账户上没有足够的点数。"

#. module: sms
#. openerp-web
#: code:addons/sms/static/src/js/fields_sms_widget.js:0
#, python-format
msgid ""
"Your SMS Text Message must include at least one non-whitespace character"
msgstr "您的SMS短信必须至少包括一个非空格字符"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "are invalid."
msgstr "无效。"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "e.g. +1 415 555 0100"
msgstr "例如+1 415 555 0100"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "e.g. Calendar Reminder"
msgstr "例如：日历提醒"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "e.g. Contact"
msgstr "例如：联系"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "e.g. en_US or {{ object.partner_id.lang }}"
msgstr "例如，en_US或{{ object.partner_id.lang }}。"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid ""
"recipients are valid\n"
"                                and"
msgstr ""
"收件人有效\n"
"                                和"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "record:"
msgstr "记录："

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "records instead. <br/>"
msgstr "而是记录。<br/>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "records selected."
msgstr "所选记录。"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "to send to all"
msgstr "发送给所有人"
