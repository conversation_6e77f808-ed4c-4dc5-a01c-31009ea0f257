# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * product_extended
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2015-09-08 07:21+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: <PERSON>by<PERSON> (http://www.transifex.com/odoo/odoo-9/language/kab/)\n"
"Language: kab\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: product_extended
#: code:addons/product_extended/wizard/wizard_price.py:24
#: code:addons/product_extended/wizard/wizard_price.py:40
#, python-format
msgid "Active ID is not set in Context."
msgstr "Asulay urmid ulacit deg umnaḍ"

#. module: product_extended
#: model:ir.model,name:product_extended.model_mrp_bom
msgid "Bill of Material"
msgstr "Tagrawalt"

#. module: product_extended
#: model_terms:ir.ui.view,arch_db:product_extended.view_compute_price_wizard
msgid "Cancel"
msgstr "Sefsex"

#. module: product_extended
#: model_terms:ir.ui.view,arch_db:product_extended.view_compute_price_wizard
msgid "Change Price"
msgstr "Snifel ssuma"

#. module: product_extended
#: model_terms:ir.ui.view,arch_db:product_extended.view_compute_price_wizard
msgid "Change Standard Price"
msgstr "Snifel ssuma "

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_wizard_price_recursive
msgid "Change prices of child BoMs too"
msgstr ""

#. module: product_extended
#: model:ir.actions.act_window,name:product_extended.action_view_compute_price_wizard
#: model:ir.model,name:product_extended.model_wizard_price
msgid "Compute Price Wizard"
msgstr "Amarag n usiḍen ssuma"

#. module: product_extended
#: model_terms:ir.ui.view,arch_db:product_extended.product_product_ext_form_view2
msgid "Compute from BOM"
msgstr ""

#. module: product_extended
#: model_terms:ir.ui.view,arch_db:product_extended.product_product_ext_form_view2
msgid ""
"Compute the price of the product using products and operations of related "
"bill of materials, for manufactured products only."
msgstr ""

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_wizard_price_create_uid
msgid "Created by"
msgstr "Yerna-t"

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_wizard_price_create_date
msgid "Created on"
msgstr "Yerna di"

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_wizard_price_display_name
msgid "Display Name"
msgstr ""

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_wizard_price_real_time_accounting
msgid "Generate accounting entries when real-time"
msgstr ""

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_wizard_price_id
msgid "ID"
msgstr "Asulay"

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_wizard_price_info_field
msgid "Info"
msgstr "Information"

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_wizard_price___last_update
msgid "Last Modified on"
msgstr "Aleqqem aneggaru di"

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_wizard_price_write_uid
msgid "Last Updated by"
msgstr "Aleqqem aneggaru sɣuṛ"

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_wizard_price_write_date
msgid "Last Updated on"
msgstr "Aleqqem aneggaru di"

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_mrp_bom_get_variant_count
msgid "Number of variant for the product"
msgstr ""

#. module: product_extended
#: model:ir.model,name:product_extended.model_product_template
msgid "Product Template"
msgstr "Taneɣruft n ufaris"

#. module: product_extended
#: model_terms:ir.ui.view,arch_db:product_extended.view_compute_price_wizard
msgid "Set price on BoM"
msgstr "Sers ssuma ɣef  tegrawalt"

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_mrp_bom_standard_price
msgid "Standard Price"
msgstr ""

#. module: product_extended
#: model_terms:ir.ui.view,arch_db:product_extended.view_compute_price_wizard
msgid ""
"The price is computed from the bill of material lines which are not variant "
"specific"
msgstr ""

#. module: product_extended
#: code:addons/product_extended/wizard/wizard_price.py:38
#, python-format
msgid ""
"This wizard is built for product templates, while you are currently running "
"it from a product variant."
msgstr ""

#~ msgid "Compute price wizard"
#~ msgstr "Amarag n usiḍen n ssuma"
