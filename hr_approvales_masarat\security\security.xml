<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record model="ir.module.category" id="module_hr_approvales_masarat">
        <field name="name">Masarat Approvals Forms</field>
        <field name="description">Approvals Request Of Masarat</field>
        <field name="sequence">45</field>
    </record>

    <record id="group_hr_approvales_masarat" model="res.groups">
        <field name="name">HR</field>
        <field name="category_id" ref="module_hr_approvales_masarat"/>
    </record>

    <record id="group_employee_approvales_masarat" model="res.groups">
        <field name="name">Employee</field>
        <field name="category_id" ref="module_hr_approvales_masarat"/>
    </record>

    <record model="ir.rule" id="masarat_approvales_latency_records_rule">
        <field name="name">Masarat Latency Approvals Rules</field>
        <field name="model_id" ref="model_hr_masarat_latency"/>
        <field name="domain_force">["|", ("manager_id.user_id","=",user.id), ('employee_id.user_id','=',user.id) ]</field>
        <field name="groups" eval="[(4, ref('group_employee_approvales_masarat'))]"/>
    </record>
    <record model="ir.rule" id="masarat_approvales_assignment_records_rule">
        <field name="name">Masarat Work Assignment Approvals Rules</field>
        <field name="model_id" ref="model_hr_masarat_work_assignment"/>
        <field name="domain_force">["|", ("manager_id.user_id","=",user.id), ('employee_id.user_id','=',user.id) ]</field>
        <field name="groups" eval="[(4, ref('group_employee_approvales_masarat'))]"/>
    </record>

    <record model="ir.rule" id="masarat_approvales_car_records_rule">
        <field name="name">Masarat Car Approvals Rules</field>
        <field name="model_id" ref="model_hr_masarat_car"/>
        <field name="domain_force">["|", ("manager_id.user_id","=",user.id), ('employee_id.user_id','=',user.id) ]</field>
        <field name="groups" eval="[(4, ref('group_employee_approvales_masarat'))]"/>
    </record>

    <record model="ir.rule" id="masarat_approvales_absence_records_rule">
        <field name="name">Masarat Absence Approvals Rules</field>
        <field name="model_id" ref="model_hr_masarat_absence"/>
        <field name="domain_force">["|", ("manager_id.user_id","=",user.id), ('employee_id.user_id','=',user.id) ]</field>
        <field name="groups" eval="[(4, ref('group_employee_approvales_masarat'))]"/>
    </record>

    <record model="ir.rule" id="masarat_approvales_overtime_records_rule">
        <field name="name">Masarat overtime Approvals Rules</field>
        <field name="model_id" ref="model_hr_masarat_overtime"/>
        <field name="domain_force">["|", ("manager_id.user_id","=",user.id), ('employee_id.user_id','=',user.id) ]</field>
        <field name="groups" eval="[(4, ref('group_employee_approvales_masarat'))]"/>
    </record>
    <record model="ir.rule" id="masarat_approvales_exit_p_records_rule">
        <field name="name">Masarat Exit Permission Approvals Rules</field>
        <field name="model_id" ref="model_hr_masarat_exit_permission"/>
        <field name="domain_force">["|", ("manager_id.user_id","=",user.id), ('employee_id.user_id','=',user.id) ]</field>
        <field name="groups" eval="[(4, ref('group_employee_approvales_masarat'))]"/>
    </record>

<!--    masarar reward-->

    <record model="ir.module.category" id="module_hr_reward_masarat">
        <field name="name">مكافأة مسارات</field>
        <field name="sequence">46</field>
    </record>

    <record id="group_hr_reward_masarat" model="res.groups">
        <field name="name">موارد بشرية</field>
        <field name="category_id" ref="module_hr_reward_masarat"/>
    </record>

    <record id="group_manager_reward_masarat" model="res.groups">
        <field name="name">مدراء</field>
        <field name="category_id" ref="module_hr_reward_masarat"/>
    </record>

    <record id="group_gm_reward_masarat" model="res.groups">
        <field name="name">المدير العام</field>
        <field name="category_id" ref="module_hr_reward_masarat"/>
    </record>
    <record id="group_fm_reward_masarat" model="res.groups">
        <field name="name">مدير الشؤون المالية</field>
        <field name="category_id" ref="module_hr_reward_masarat"/>
    </record>

    <record model="ir.rule" id="masarat_manager_reward_rule">
        <field name="name">Masarat Manager Reward Rules</field>
        <field name="model_id" ref="model_hr_masarat_reward"/>
        <field name="domain_force">[("user_id","=",user.id)]</field>
        <field name="groups" eval="[(4, ref('group_manager_reward_masarat'))]"/>
    </record>

    <record model="ir.rule" id="masarat_gm_reward_rule">
        <field name="name">Masarat General Manager Reward Rules</field>
        <field name="model_id" ref="model_hr_masarat_reward"/>
        <field name="domain_force">[(1,"=",1)]</field>
        <field name="groups" eval="[(4, ref('group_gm_reward_masarat'))]"/>
    </record>

    <record model="ir.rule" id="masarat_fm_reward_rule">
        <field name="name">Masarat Financial Manager Reward Rules</field>
        <field name="model_id" ref="model_hr_masarat_reward"/>
        <field name="domain_force">[(1,"=",1)]</field>
        <field name="groups" eval="[(4, ref('group_fm_reward_masarat'))]"/>
    </record>

</odoo>