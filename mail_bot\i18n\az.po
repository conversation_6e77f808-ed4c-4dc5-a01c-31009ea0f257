# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail_bot
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: erpgo translator <<EMAIL>>, 2022\n"
"Language-Team: Azerbaijani (https://app.transifex.com/odoo/teams/41243/az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Aaaaaw that's really cute but, you know, bots don't work that way. You're "
"too human for me! Let's keep it professional ❤️"
msgstr ""

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__disabled
msgid "Disabled"
msgstr "Deaktiv edildi"

#. module: mail_bot
#: model:ir.model,name:mail_bot.model_mail_channel
msgid "Discussion Channel"
msgstr "Müzakirə Kanalı"

#. module: mail_bot
#: model:ir.model,name:mail_bot.model_mail_thread
msgid "Email Thread"
msgstr "Email Zənciri"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Great! 👍<br/>To access special commands, <b>start your sentence with</b> "
"<span class=\"o_odoobot_command\">/</span>. Try getting help."
msgstr ""

#. module: mail_bot
#: model:ir.model,name:mail_bot.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP Marşrutizasiyası"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_channel.py:0
#, python-format
msgid ""
"Hello,<br/>Odoo's chat helps employees collaborate efficiently. I'm here to "
"help you discover its features.<br/><b>Try to send me an emoji</b> <span "
"class=\"o_odoobot_command\">:)</span>"
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "Hmmm..."
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"I am a simple bot, but if that's a dog, he is the cutest 😊 "
"<br/>Congratulations, you finished this tour. You can now <b>close this chat"
" window</b>. Enjoy discovering Odoo."
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "I'm afraid I don't understand. Sorry!"
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"I'm not smart enough to answer your question.<br/>To follow my guide, ask: "
"<span class=\"o_odoobot_command\">start the tour</span>."
msgstr ""

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__idle
msgid "Idle"
msgstr ""

#. module: mail_bot
#: model:ir.model,name:mail_bot.model_mail_bot
msgid "Mail Bot"
msgstr "Poçt Botu"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Not exactly. To continue the tour, send an emoji: <b>type</b> <span "
"class=\"o_odoobot_command\">:)</span> and press enter."
msgstr ""

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__not_initialized
msgid "Not initialized"
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Not sure what you are doing. Please, type <span "
"class=\"o_odoobot_command\">/</span> and wait for the propositions. Select "
"<span class=\"o_odoobot_command\">help</span> and press enter"
msgstr ""

#. module: mail_bot
#: model:ir.model.fields,field_description:mail_bot.field_res_users__odoobot_state
msgid "OdooBot Status"
msgstr "OdooBot Statusu"

#. module: mail_bot
#: model:ir.model.fields,field_description:mail_bot.field_res_users__odoobot_failed
msgid "Odoobot Failed"
msgstr ""

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__onboarding_attachement
msgid "Onboarding attachement"
msgstr ""

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__onboarding_command
msgid "Onboarding command"
msgstr ""

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__onboarding_emoji
msgid "Onboarding emoji"
msgstr ""

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__onboarding_ping
msgid "Onboarding ping"
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Sorry I'm sleepy. Or not! Maybe I'm just trying to hide my unawareness of "
"human language...<br/>I can show you features if you write: <span "
"class=\"o_odoobot_command\">start the tour</span>."
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Sorry, I am not listening. To get someone's attention, <b>ping him</b>. "
"Write <span class=\"o_odoobot_command\">@OdooBot</span> and select me."
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "That's not nice! I'm a bot but I have feelings... 💔"
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"To <b>send an attachment</b>, click on the <i class=\"fa fa-paperclip\" "
"aria-hidden=\"true\"></i> icon and select a file."
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "To start, try to send me an emoji :)"
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Unfortunately, I'm just a bot 😞 I don't understand! If you need help "
"discovering our product, please check <a "
"href=\"https://www.odoo.com/documentation\" target=\"_blank\">our "
"documentation</a> or <a href=\"https://www.odoo.com/slides\" "
"target=\"_blank\">our videos</a>."
msgstr ""

#. module: mail_bot
#: model:ir.model,name:mail_bot.model_res_users
msgid "Users"
msgstr "İstifadəçilər"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Wow you are a natural!<br/>Ping someone with @username to grab their "
"attention. <b>Try to ping me using</b> <span "
"class=\"o_odoobot_command\">@OdooBot</span> in a sentence."
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Yep, I am here! 🎉 <br/>Now, try <b>sending an attachment</b>, like a picture"
" of your cute dog..."
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "fuck"
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "help"
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "i love you"
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "love"
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "start the tour"
msgstr ""
