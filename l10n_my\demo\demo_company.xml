<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_my" model="res.partner">
        <field name="name">MY Company</field>
        <field name="vat">MY647828492048102</field>
        <field name="street">1 Wisma Dato Dagang</field>
        <field name="street2">Jln Raja Alang Kampung Bahru Mala</field>
        <field name="city">Kuala Lumpur</field>
        <field name="country_id" ref="base.my"/>
        <field name="state_id" ref="base.state_my_kul"/>
        <field name="zip">50300</field>
        <field name="phone">+60 0326921917</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.myexample.com</field>
    </record>

    <record id="demo_company_my" model="res.company">
        <field name="name">MY Company</field>
        <field name="partner_id" ref="partner_demo_company_my"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_my')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_my.demo_company_my'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_my.l10n_my_chart_template')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_my.demo_company_my')"/>
    </function>
</odoo>
