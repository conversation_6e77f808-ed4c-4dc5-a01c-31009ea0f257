# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_mercury
# 
# Translators:
# Silvija <PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# UAB "Draugi<PERSON><PERSON> sprendimai" <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> ViaLaurea <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:18+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.res_config_settings_view_form_inherit_pos_mercury
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>Buy a card reader"
msgstr "<i class=\"fa fa-fw fa-arrow-right\"/>Pirkti kortelių skaitytuvą"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"<i>Vantiv Configurations</i> define what Vantiv account will be used when\n"
"                                processing credit card transactions in the Point Of Sale. Setting up a Vantiv\n"
"                                configuration will enable you to allow payments with various credit cards\n"
"                                (eg. Visa, MasterCard, Discovery, American Express, ...). After setting up this\n"
"                                configuration you should associate it with a Point Of Sale payment method."
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "ABOVE AMOUNT PURSUANT"
msgstr "VIRŠ SUMOS"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "APPROVAL CODE:"
msgstr "PATVIRTINIMO KODAS:"

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_barcode_rule
msgid "Barcode Rule"
msgstr "Brūkšninio kodo taisyklė"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "CARDHOLDER WILL PAY CARD ISSUER"
msgstr "KORTELĖS TURĖTOJAS SUMOKĖS KORTELĖS IŠDAVĖJUI"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentTransactionPopup.js:0
#, python-format
msgid "Cancel"
msgstr "Atšaukti"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_card_brand
msgid "Card Brand"
msgstr "Kortelės rūšis"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_card_number
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_order
msgid "Card Number"
msgstr "Kortelės numeris"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_prefixed_card_number
msgid "Card Number Prefix"
msgstr "Kortelės numerio priešdėlis"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_card_owner_name
msgid "Card Owner Name"
msgstr "Kortelės savininko numeris"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_tree
msgid "Card Reader"
msgstr "Kortelės skaitytuvas"

#. module: pos_mercury
#: model_terms:ir.actions.act_window,help:pos_mercury.action_configuration_form
msgid "Configure your card reader"
msgstr "Sukonfigūruokite savo kortelių skaitytuvą"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Could not read card"
msgstr "Nepavyko nuskaityti kortelės"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__create_uid
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__create_uid
msgid "Created by"
msgstr "Sukūrė"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__create_date
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__create_date
msgid "Created on"
msgstr "Sukurta"

#. module: pos_mercury
#: model:ir.model.fields.selection,name:pos_mercury.selection__barcode_rule__type__credit
msgid "Credit Card"
msgstr "Kreditinė kortelė"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid ""
"Credit card refunds are not supported. Instead select your credit card "
"payment method, click 'Validate' and refund the original charge manually "
"through the Vantiv backend."
msgstr ""
"Kredito kortelės grąžinimai nepalaikomi. Vietoje to, pasirinkite savo "
"kortelės mokėjimo būdą, paspauskite \"Patvirtinti\" ir grąžinkite sumą "
"rankiniu būdu per Vantiv programinį kodą."

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__display_name
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__display_name
msgid "Display Name"
msgstr "Rodomas pavadinimas"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"For quickly handling orders: just swiping a credit card when on the payment screen\n"
"                                (without having pressed anything else) will charge the full amount of the order to\n"
"                                the card."
msgstr ""
"Greitam užsakymų tvarkymui: kortelės perbraukimas mokėjimo lange\n"
"(nespaudžiant nieko kito) nuskaitys pilną užsakymo sumą nuo kortelės."

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/ProductScreen.js:0
#, python-format
msgid "Go to payment screen to use cards"
msgstr "Norėdami naudoti korteles, eikite į mokėjimo langą"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Handling transaction..."
msgstr "Tvarkoma operacija..."

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__id
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__id
msgid "ID"
msgstr "ID"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_mercury_configuration__merchant_id
msgid "ID of the merchant to authenticate him on the payment provider server"
msgstr "Pardavėjo ID jo identifikavimui tiekėjo serveryje"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"If you don't already have a Vantiv account, contact Vantiv at +****************\n"
"                                to create one."
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_invoice_no
msgid "Invoice number from Vantiv Pay"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration____last_update
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction____last_update
msgid "Last Modified on"
msgstr "Paskutinį kartą keista"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__write_uid
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__write_uid
msgid "Last Updated by"
msgstr "Paskutinį kartą atnaujino"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__write_date
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__write_date
msgid "Last Updated on"
msgstr "Paskutinį kartą atnaujinta"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__merchant_id
msgid "Merchant ID"
msgstr "Pardavėjo ID"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__merchant_pwd
msgid "Merchant Password"
msgstr "Pardavėjo slaptažodis"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__name
msgid "Name"
msgstr "Pavadinimas"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_mercury_configuration__name
msgid "Name of this Vantiv configuration"
msgstr ""

#. module: pos_mercury
#: code:addons/pos_mercury/models/pos_mercury_transaction.py:0
#, python-format
msgid "No Vantiv configuration associated with the payment method."
msgstr ""

#. module: pos_mercury
#: code:addons/pos_mercury/models/pos_mercury_transaction.py:0
#, python-format
msgid "No opened point of sale session for user %s found."
msgstr "Vartotojui %s nerasta atidarytų pardavimo taško sesijų."

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "No response from Vantiv (Vantiv down?)"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "No response from server (connected to network?)"
msgstr "Jokio atsakymo iš serverio (prisijungta prie tinklo?)"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Odoo error while processing transaction."
msgstr "Apdorojant operaciją įvyko \"Odoo\" klaida."

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentTransactionPopup.js:0
#, python-format
msgid "Ok"
msgstr "Gerai"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentTransactionPopup.js:0
#, python-format
msgid "Online Payment"
msgstr "Internetinis mokėjimas"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Partially approved"
msgstr "Dalinai patvirtintas"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_mercury_configuration__merchant_pwd
msgid ""
"Password of the merchant to authenticate him on the payment provider server"
msgstr "Pardavėjo slaptažodis jo patvirtinimui mokėjimo tiekėjo serveryje"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Pay with: "
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_record_no
msgid "Payment record number from Vantiv Pay"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_ref_no
msgid "Payment reference number from Vantiv Pay"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Please setup your Vantiv merchant account."
msgstr ""

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_order
msgid "Point of Sale Orders"
msgstr "PT užsakymai"

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "Pardavimo taško mokėjimo būdai"

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_payment
msgid "Point of Sale Payments"
msgstr "Pardavimų Taško Mokėjimai"

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_mercury_configuration
msgid "Point of Sale Vantiv Configuration"
msgstr ""

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_mercury_mercury_transaction
msgid "Point of Sale Vantiv Transaction"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Refunds not supported"
msgstr "Grąžinimai nepalaikomi"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Reversal failed, sending VoidSale..."
msgstr "Grąžinimas nepavyko, siunčiamas pardavimo anuliavimas..."

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Reversal succeeded"
msgstr "Grąžinimas pavyko"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Sending reversal..."
msgstr "Siunčiamas grąžinimas..."

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "TO CARDHOLDER AGREEMENT"
msgstr "SU KORTELĖS SAVININKO SUTIKIMU"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_card_brand
msgid "The brand of the payment card (e.g. Visa, AMEX, ...)"
msgstr "Mokėjimo kortelės rūšies pavadinimas (pvz., Visa, AMEX, ...)"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_prefixed_card_number
msgid "The card number used for the payment."
msgstr "Kortelės numeris, naudojamas mokėjimui."

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment_method__pos_mercury_config_id
msgid "The configuration of Vantiv used for this journal"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_card_number
msgid "The last 4 numbers of the card used to pay"
msgstr "Paskutiniai 4 mokėjimui naudotos kortelės skaitmenys"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_card_owner_name
msgid "The name of the card owner"
msgstr "Kortelės savininko vardas"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid ""
"This can be caused by a badly executed swipe or by not having your keyboard "
"layout set to US QWERTY (not US International)."
msgstr ""
"Tai galėjo sukelti netinkamas perbraukimas arba klaviatūros išdėstymas, nustatytas ne į US\n"
"QWERTY (neturi būti US International)."

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Transaction approved"
msgstr "Operacija patvirtinta"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_barcode_rule__type
msgid "Type"
msgstr "Tipas"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"Using the Vantiv integration in the Point Of Sale is easy: just press the\n"
"                                associated payment method. After that the amount can be adjusted (eg. for cashback)\n"
"                                just like on any other payment line. Whenever the payment line is set up, a card\n"
"                                can be swiped through the card reader device."
msgstr ""

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.res_config_settings_view_form_inherit_pos_mercury
msgid "Vantiv Accounts"
msgstr "Vantiv sąskaitos"

#. module: pos_mercury
#: model:ir.actions.act_window,name:pos_mercury.action_configuration_form
#: model:ir.ui.menu,name:pos_mercury.menu_pos_pos_mercury_config
msgid "Vantiv Configurations"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment_method__pos_mercury_config_id
msgid "Vantiv Credentials"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_invoice_no
msgid "Vantiv invoice number"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_record_no
msgid "Vantiv record number"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_ref_no
msgid "Vantiv reference number"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "VoidSale succeeded"
msgstr "Pardavimo anuliavimas pavyko"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/PaymentScreenPaymentLines.xml:0
#, python-format
msgid "WAITING FOR SWIPE"
msgstr "LAUKIA PERBRAUKIMO"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"We currently support the MagTek Dynamag card reader device. It can be connected\n"
"                                directly to the Point Of Sale device or it can be connected to the IoTBox."
msgstr ""
"Šiuo metu mes palaikome MagTek Dynamag kortelių skaitymo įrenginį. Jis gali būti\n"
"prijungiamas tiesiogiai prie pardavimo taško paslaugos arba prie IoTBox."

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "X______________________________"
msgstr "X______________________________"
