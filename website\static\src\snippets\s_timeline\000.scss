.s_timeline {
    .s_timeline_line {
        position: relative;
        border-color: gray('800'); // For inheritance, not actual border

        &:before {
            content: '';
            display: block !important; // override portal '#wrap .container' value
            position: absolute;
            width: 1px;
            top: 0px;
            bottom: 0px;
            left: 50%;
            border-left: 1px solid;
            border-color: inherit;
        }
    }
    .s_timeline_row {
        align-items: center;
        .s_timeline_content {
            align-items: center;
            justify-content: flex-end;
            width: 100%;
            ~ .s_timeline_content {
                justify-content: flex-start;
            }
        }
        &.flex-row-reverse {
            .s_timeline_content {
                flex-direction: row-reverse;
            }
        }
        @include media-breakpoint-up(md) {
            &.flex-row-reverse {
                .s_timeline_content {
                    flex-direction: row-reverse;
                    &:not(:last-child) {
                        margin-left: 10%;
                    }
                }
            }
            &:not(.flex-row-reverse) {
                .s_timeline_content:last-child {
                    margin-left: 10%;
                }
            }
        }
    }
    .s_timeline_date {
        @include media-breakpoint-up(md) {
            position: absolute;
            left: 0%;
            right: 0%;
        }
        @include media-breakpoint-down(sm) {
            position: relative;
            margin: 20px 0px;
        }
        span:not(.fa) {
            display: inline-block;
            padding: 5px;
        }
        .fa {
            margin: 0 $grid-gutter-width/2;
        }
        text-align: center;
    }
    .s_timeline_icon {
        flex: 0 0 auto;
        margin: $grid-gutter-width/2;
        z-index: 1;
    }
}
