# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery
# 
# Translators:
# <PERSON> <amunif<PERSON><PERSON>@gmail.com>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> Useful <<EMAIL>>, 2022
# whenwesober, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> Syuhada <<EMAIL>>, 2023
# Abe <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:21+0000\n"
"Last-Translator: <PERSON> Manyo, 2023\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: delivery
#: code:addons/delivery/models/sale_order.py:0
#, python-format
msgid " (Estimated Cost: %s )"
msgstr " (Estimasi Biaya: %s )"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_quant_package_weight_form
msgid "(computed:"
msgstr "(dihitung:"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_carrier_view_form
msgid "<i class=\"fa fa-arrow-right mr-1\"/>Get rate"
msgstr ""

#. module: delivery
#: code:addons/delivery/models/delivery_carrier.py:0
#, python-format
msgid ""
"<p class=\"o_view_nocontent\">\n"
"                    Buy Odoo Enterprise now to get more providers.\n"
"                </p>"
msgstr ""
"<p class=\"o_view_nocontent\">\n"
"                    Beli Odoo Enterprise sekarang untuk mendapatkan lebih banyak penyedia.\n"
"                </p>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid ""
"<span class=\"o_warning_text\">Test</span>\n"
"                                    <span class=\"o_stat_text\">Environment</span>"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "<span class=\"text-danger\">No debug</span>"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "<span class=\"text-success\">Debug requests</span>"
msgstr "<span class=\"text-success\">Permintaan debug</span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid ""
"<span class=\"text-success\">Production</span>\n"
"                                    <span class=\"o_stat_text\">Environment</span>"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.stock_report_delivery_package_section_line_inherit_delivery
msgid "<span> - Weight (estimated): </span>"
msgstr "<span> - Berat (estimasi): </span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.delivery_stock_report_delivery_no_package_section_line
#: model_terms:ir.ui.view,arch_db:delivery.stock_report_delivery_package_section_line_inherit_delivery
msgid "<span> - Weight: </span>"
msgstr "<span> - Berat: </span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_package_barcode_small_delivery
msgid "<span>Shipping Weight: </span>"
msgstr "<span>Berat Pengiriman: </span>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_delivery_document2
#: model_terms:ir.ui.view,arch_db:delivery.report_shipping2
msgid "<strong>Carrier:</strong>"
msgstr "<strong>Carrier:</strong>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_delivery_document2
msgid "<strong>HS Code</strong>"
msgstr "<strong>Kode HS</strong>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_package_barcode_delivery
msgid ""
"<strong>Shipping Weight:</strong>\n"
"                <br/>"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_delivery_document2
msgid ""
"<strong>Total Weight:</strong>\n"
"                <br/>"
msgstr ""
"<strong>Berat Total:</strong>\n"
"                <br/>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_delivery_document2
msgid "<strong>Tracking Number:</strong>"
msgstr "<strong>Nomor Pelacakan:</strong>"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.report_shipping2
msgid ""
"<strong>Weight:</strong>\n"
"                <br/>"
msgstr ""
"<strong>Berat:</strong>\n"
"                <br/>"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__integration_level
msgid "Action while validating Delivery Orders"
msgstr "Action saat memvalidasi Order Pengiriman"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__active
msgid "Active"
msgstr "Aktif"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_carrier_view_form
msgid "Add"
msgstr "Tambah"

#. module: delivery
#: code:addons/delivery/models/sale_order.py:0
#: code:addons/delivery/wizard/choose_delivery_carrier.py:0
#, python-format
msgid "Add a shipping method"
msgstr "Tambahkan metode pengiriman"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_order_form_with_carrier
msgid "Add shipping"
msgstr "Tambah pengiriman"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__amount
msgid "Amount"
msgstr "Jumlah"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__amount
msgid ""
"Amount of the order to benefit from a free shipping, expressed in the "
"company currency"
msgstr ""
"Jumlah order untuk mendapatkan pengiriman gratis, ditunjukkan dalam mata "
"uang perusahaan"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Archived"
msgstr "Diarsipkan"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__available_carrier_ids
msgid "Available Carriers"
msgstr "Carrier yang Tersedia"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__delivery_type__base_on_rule
msgid "Based on Rules"
msgstr "Berdasarkan Peraturan"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking__weight_bulk
msgid "Bulk Weight"
msgstr "Berat Massal"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__can_generate_return
msgid "Can Generate Return"
msgstr "Dapat Membuat Pengembalian"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Cancel"
msgstr "Batal"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__carrier_id
#: model:ir.model.fields,field_description:delivery.field_stock_move_line__carrier_id
#: model:ir.model.fields,field_description:delivery.field_stock_package_type__package_carrier_type
#: model:ir.model.fields,field_description:delivery.field_stock_picking__carrier_id
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_tree
msgid "Carrier"
msgstr "Pembawa"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_package_type__shipper_package_code
msgid "Carrier Code"
msgstr "Kode Carrier"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_choose_delivery_carrier__carrier_id
msgid "Choose the method to deliver your goods"
msgstr "Pilih metode pengiriman untuk barang Anda"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__company_id
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__company_id
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__company_id
msgid "Company"
msgstr "Perusahaan"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_form
msgid "Condition"
msgstr "Syarat"

#. module: delivery
#: model:ir.model,name:delivery.model_res_partner
msgid "Contact"
msgstr "Kontak"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__display_price
msgid "Cost"
msgstr "Modal"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__country_ids
msgid "Countries"
msgstr "Negara"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__create_uid
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__create_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__create_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__create_uid
msgid "Created by"
msgstr "Dibuat oleh"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__create_date
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__create_date
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__create_date
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__create_date
msgid "Created on"
msgstr "Dibuat pada"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__currency_id
msgid "Currency"
msgstr "Mata Uang"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__partner_id
msgid "Customer"
msgstr "Pelanggan"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__debug_logging
msgid "Debug logging"
msgstr "Logging debug"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_res_partner__property_delivery_carrier_id
#: model:ir.model.fields,help:delivery.field_res_users__property_delivery_carrier_id
msgid "Default delivery method used in sales orders."
msgstr "Metode pengiriman standar yang digunakan di order penjualan."

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_carrier_form
msgid "Define a new delivery method"
msgstr "Tentukan metode pengiriman baru"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Delivery Carrier"
msgstr "Carrier Pengiriman"

#. module: delivery
#: model:ir.model,name:delivery.model_choose_delivery_carrier
msgid "Delivery Carrier Selection Wizard"
msgstr "Wizard Pemilihan Carrier Pengiriman"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_form
msgid "Delivery Cost"
msgstr "Biaya pengiriman"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__delivery_message
#: model:ir.model.fields,field_description:delivery.field_sale_order__delivery_message
msgid "Delivery Message"
msgstr "Pesan Pengiriman"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__name
#: model:ir.model.fields,field_description:delivery.field_res_partner__property_delivery_carrier_id
#: model:ir.model.fields,field_description:delivery.field_res_users__property_delivery_carrier_id
#: model:ir.model.fields,field_description:delivery.field_sale_order__carrier_id
msgid "Delivery Method"
msgstr "Metode Pengiriman"

#. module: delivery
#: model:ir.model,name:delivery.model_choose_delivery_package
msgid "Delivery Package Selection Wizard"
msgstr "Wizard Pengiriman Paket Pengiriman"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__delivery_package_type_id
msgid "Delivery Package Type"
msgstr "Tipe Paket Pengiriman"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__delivery_price
msgid "Delivery Price"
msgstr "Harga Pengiriman"

#. module: delivery
#: model:ir.model,name:delivery.model_delivery_price_rule
msgid "Delivery Price Rules"
msgstr "Peraturan Harga Pengiriman"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__product_id
msgid "Delivery Product"
msgstr "Produk Pengiriman"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__delivery_rating_success
msgid "Delivery Rating Success"
msgstr "Rating Kesuksesan Pengiriman"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__delivery_set
msgid "Delivery Set"
msgstr "Delivery Set"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__recompute_delivery_price
#: model:ir.model.fields,field_description:delivery.field_sale_order_line__recompute_delivery_price
msgid "Delivery cost should be recomputed"
msgstr "Biaya pengiriman harus dihitung kembali"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.vpicktree_view_tree
msgid "Destination"
msgstr "Tujuan"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Destination Availability"
msgstr "Ketersediaan Tujuan"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_move_line__destination_country_code
#: model:ir.model.fields,field_description:delivery.field_stock_picking__destination_country_code
msgid "Destination Country"
msgstr "Negara Tujuan"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__sequence
msgid "Determine the display order"
msgstr "Menentukan urutan tampilan"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_carrier_view_form
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_package_view_form
msgid "Discard"
msgstr "Abaikan"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__display_name
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__display_name
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__display_name
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__display_name
msgid "Display Name"
msgstr "Nama Tampilan"

#. module: delivery
#: model:ir.actions.act_window,name:delivery.act_delivery_trackers_url
msgid "Display tracking links"
msgstr "Tampilkan link pelacakan"

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_carrier_form
msgid ""
"Each carrier (e.g. UPS) can have several delivery methods (e.g.\n"
"                UPS Express, UPS Standard) with a set of pricing rules attached\n"
"                to each method."
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__prod_environment
msgid "Environment"
msgstr "Lingkungan"

#. module: delivery
#: code:addons/delivery/models/delivery_carrier.py:0
#: code:addons/delivery/models/delivery_grid.py:0
#, python-format
msgid "Error: this delivery method is not available for this address."
msgstr "Kesalahan: metode pengiriman ini tidak tersedia untuk alamat ini."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__invoice_policy
msgid ""
"Estimated Cost: the customer will be invoiced the estimated cost of the shipping.\n"
"Real Cost: the customer will be invoiced the real cost of the shipping, the cost of the shipping will be updated on the SO after the delivery."
msgstr ""

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__invoice_policy__estimated
msgid "Estimated cost"
msgstr "Estimasi Biaya"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_sale_order__carrier_id
msgid "Fill this field if you plan to invoice the shipping based on picking."
msgstr ""
"Isi field ini bila Anda berencana untuk memfakturkan pengiriman berdasarkan "
"picking."

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid ""
"Filling this form allows you to filter delivery carriers according to the "
"delivery address of your customer."
msgstr ""
"Mengisi formulir ini memungkinkan Anda untuk memfilter carrier pengiriman "
"berdasarkan alamat pengiriman pelanggan Anda."

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__fixed_price
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__delivery_type__fixed
msgid "Fixed Price"
msgstr "Harga Tetap"

#. module: delivery
#: code:addons/delivery/models/sale_order.py:0
#, python-format
msgid "Free Shipping"
msgstr "Bebas Biaya Kirim"

#. module: delivery
#: model:delivery.carrier,name:delivery.free_delivery_carrier
#: model:product.product,name:delivery.product_product_delivery
#: model:product.template,name:delivery.product_product_delivery_product_template
msgid "Free delivery charges"
msgstr "Gratis ongkos kirim"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__free_over
msgid "Free if order amount is above"
msgstr "Gratis jika pesanan melebihi"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__return_label_on_delivery
msgid "Generate Return Label"
msgstr "Buat Label Pengembalian"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__integration_level__rate
msgid "Get Rate"
msgstr "Dapatkan Harga"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__integration_level__rate_and_ship
msgid "Get Rate and Create Shipment"
msgstr "Dapatkan Harga dan Buat Pengiriman"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Group By"
msgstr "Dikelompokkan berdasarkan"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_product_product__hs_code
#: model:ir.model.fields,field_description:delivery.field_product_template__hs_code
msgid "HS Code"
msgstr "Kode HS"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__id
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__id
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__id
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__id
msgid "ID"
msgstr "ID"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__free_over
msgid ""
"If the order total amount (shipping excluded) is above or equal to this "
"value, the customer benefits from a free shipping"
msgstr ""
"Bila jumlah total order (tidak termasuk pengiriman) diatas atau sama dengan "
"value ini, pelanggan mendapatkan pengiriman gratis"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Install more Providers"
msgstr "Instal lebih banyak Penyedia"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__integration_level
msgid "Integration Level"
msgstr "Level Integrasi"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__invoicing_message
msgid "Invoicing Message"
msgstr "Pesan Faktur"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__invoice_policy
msgid "Invoicing Policy"
msgstr "Kebijakan Penagihan"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking__is_return_picking
msgid "Is Return Picking"
msgstr "Adalah Pengembalian"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order_line__is_delivery
msgid "Is a Delivery"
msgstr "Adalah Pengiriman"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier____last_update
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package____last_update
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier____last_update
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule____last_update
msgid "Last Modified on"
msgstr "Terakhir diubah pada"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__write_uid
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__write_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__write_uid
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__write_uid
msgid "Last Updated by"
msgstr "Terakhir diperbarui oleh"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__write_date
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__write_date
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__write_date
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__write_date
msgid "Last Updated on"
msgstr "Terakhir diperbarui pada"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__debug_logging
msgid "Log requests in order to ease debugging"
msgstr "Catat permintaan untuk memudahkan debugging"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__margin
msgid "Margin"
msgstr "Margin"

#. module: delivery
#: model:ir.model.constraint,message:delivery.constraint_delivery_carrier_margin_not_under_100_percent
msgid "Margin cannot be lower than -100%"
msgstr "Margin tidak boleh di bawah -100%"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Margin on Rate"
msgstr "Margin pada Harga"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__max_value
msgid "Maximum Value"
msgstr "Value Maksimum"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__name
msgid "Name"
msgstr "Nama"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__stock_package_type__package_carrier_type__none
msgid "No carrier integration"
msgstr "Tidak ada integrasi carrier"

#. module: delivery
#: code:addons/delivery/models/delivery_grid.py:0
#, python-format
msgid "No price rule matching this order; delivery cost cannot be computed."
msgstr ""

#. module: delivery
#: model:delivery.carrier,name:delivery.normal_delivery_carrier
#: model:product.product,name:delivery.product_product_delivery_normal
#: model:product.template,name:delivery.product_product_delivery_normal_product_template
msgid "Normal Delivery Charges"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.delivery_tracking_url_warning_form
msgid "OK"
msgstr "OK"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__operator
msgid "Operator"
msgstr "Operator"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__order_id
msgid "Order"
msgstr "Order"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_package_view_form
msgid "Package"
msgstr "Kemasan"

#. module: delivery
#: code:addons/delivery/models/stock_picking.py:0
#, python-format
msgid "Package Details"
msgstr "Detail Paket"

#. module: delivery
#: code:addons/delivery/wizard/choose_delivery_package.py:0
#, python-format
msgid "Package too heavy!"
msgstr "Paket terlalu berat!"

#. module: delivery
#: model:ir.model,name:delivery.model_stock_quant_package
#: model:ir.model.fields,field_description:delivery.field_stock_picking__package_ids
msgid "Packages"
msgstr "Kemasan"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__picking_id
msgid "Picking"
msgstr "Pengambilan"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__price
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__price
msgid "Price"
msgstr "Harga"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_form
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_price_rule_tree
msgid "Price Rules"
msgstr "Peraturan Harga"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Pricing"
msgstr "Harga"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__price_rule_ids
msgid "Pricing Rules"
msgstr "Peraturan-Peraturan Harga"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.sale_order_portal_content_inherit_sale_stock_inherit_website_sale_delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Print Return Label"
msgstr "Cetak Label Pengembalian"

#. module: delivery
#: model:ir.model,name:delivery.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Pergerakan Produk (Baris Pergerakan Stok)"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order_line__product_qty
msgid "Product Qty"
msgstr "Produk Qty"

#. module: delivery
#: model:ir.model,name:delivery.model_product_template
msgid "Product Template"
msgstr "Templete Produk"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__delivery_type
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__delivery_type
#: model:ir.model.fields,field_description:delivery.field_stock_picking__delivery_type
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_search
msgid "Provider"
msgstr "Pemberi"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__quantity
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__quantity
msgid "Quantity"
msgstr "Kuantitas"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_carrier__invoice_policy__real
msgid "Real cost"
msgstr "Biaya sebenarnya"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking__return_label_ids
msgid "Return Label"
msgstr "Label Pengembalian"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__get_return_label_from_portal
msgid "Return Label Accessible from Customer Portal"
msgstr "Label Pengembalian Dapat Diakses dari Portal Pelanggan"

#. module: delivery
#: model:ir.model,name:delivery.model_stock_return_picking
msgid "Return Picking"
msgstr "Pengembalian Pengambilan"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__list_base_price
msgid "Sale Base Price"
msgstr "Harga Dasar Sale"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__list_price
#: model:ir.model.fields,field_description:delivery.field_stock_move_line__sale_price
msgid "Sale Price"
msgstr "Harga Jual"

#. module: delivery
#: model:ir.model,name:delivery.model_sale_order
msgid "Sales Order"
msgstr "Order Penjualan"

#. module: delivery
#: model:ir.model,name:delivery.model_sale_order_line
msgid "Sales Order Line"
msgstr "Detail Order Penjualan"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_package_view_form
msgid "Save"
msgstr "Simpan"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Send to Shipper"
msgstr "Kirim ke Pengirim"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__sequence
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__sequence
msgid "Sequence"
msgstr "Urutan"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_sale_order__is_all_service
msgid "Service Product"
msgstr "Produk Layanan"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__prod_environment
msgid "Set to True if your credentials are certified for production."
msgstr ""
"Tetapkan sebagai True bila kredensial Anda disertifikasi untuk produksi."

#. module: delivery
#: code:addons/delivery/models/stock_picking.py:0
#, python-format
msgid ""
"Shipment sent to carrier %(carrier_name)s for shipping with tracking number "
"%(ref)s<br/>Cost: %(price).2f %(currency)s"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking__carrier_price
msgid "Shipping Cost"
msgstr "Biaya Pengiriman"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Shipping Information"
msgstr "Informasi Pengiriman"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_carrier__carrier_id
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "Shipping Method"
msgstr "Metode Pengiriman"

#. module: delivery
#: model:ir.actions.act_window,name:delivery.action_delivery_carrier_form
#: model:ir.model,name:delivery.model_delivery_carrier
#: model:ir.ui.menu,name:delivery.menu_action_delivery_carrier_form
#: model:ir.ui.menu,name:delivery.sale_menu_action_delivery_carrier_form
#: model_terms:ir.ui.view,arch_db:delivery.res_config_settings_view_form
msgid "Shipping Methods"
msgstr "Metode Pengiriman"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__shipping_weight
#: model:ir.model.fields,field_description:delivery.field_stock_quant_package__shipping_weight
msgid "Shipping Weight"
msgstr "Berat Pengiriman"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_product_product__hs_code
#: model:ir.model.fields,help:delivery.field_product_template__hs_code
msgid ""
"Standardized code for international shipping and goods declaration. At the "
"moment, only used for the FedEx shipping provider."
msgstr ""
"Kode standar untuk pengiriman internasional dan deklarasi barang. Pada saat "
"ini, hanya digunakan untuk penyedia pengiriman FedEx."

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__state_ids
msgid "States"
msgstr "Status"

#. module: delivery
#: model:ir.model,name:delivery.model_stock_move
msgid "Stock Move"
msgstr "Pergerakan Stok"

#. module: delivery
#: model:ir.model,name:delivery.model_stock_package_type
msgid "Stock package type"
msgstr "Tipe paket stok"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_stock_move_line__destination_country_code
#: model:ir.model.fields,help:delivery.field_stock_picking__destination_country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"Kode ISO negara dalam dua karakter.\n"
"Anda dapat menggunakan kolom ini untuk pencarian cepat."

#. module: delivery
#: model:delivery.carrier,name:delivery.delivery_carrier
#: model:product.product,name:delivery.product_product_delivery_poste
#: model:product.template,name:delivery.product_product_delivery_poste_product_template
msgid "The Poste"
msgstr "The Poste"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__get_return_label_from_portal
msgid ""
"The return label can be downloaded by the customer from the customer portal."
msgstr ""
"Label pengembalian dapat diunduh oleh pelanggan dari portal pelanggan."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__return_label_on_delivery
msgid "The return label is automatically generated at the delivery."
msgstr "Label pengembalian secara otomatis dibuat pada pengiriman."

#. module: delivery
#: code:addons/delivery/models/delivery_carrier.py:0
#, python-format
msgid "The shipping is free since the order amount exceeds %.2f."
msgstr "Pengiriman bebas ongkir karena jumlah order melebihi %.2f."

#. module: delivery
#: code:addons/delivery/wizard/choose_delivery_carrier.py:0
#, python-format
msgid "The shipping price will be set once the delivery is done."
msgstr "Harga pengiriman akan diatur ketika pengiriman selesai."

#. module: delivery
#: code:addons/delivery/wizard/choose_delivery_package.py:0
#, python-format
msgid ""
"The weight of your package is higher than the maximum weight authorized for "
"this package type. Please choose another package type."
msgstr ""
"Berat paket Anda lebih tinggi dari berat maksimum yang diizinkan untuk tipe "
"pkaet ini. Mohon pilih tipe paket lain."

#. module: delivery
#: code:addons/delivery/models/delivery_grid.py:0
#, python-format
msgid "There is no matching delivery rule."
msgstr "Tidak ada peraturan pengiriman yang cocok."

#. module: delivery
#: model_terms:ir.actions.act_window,help:delivery.action_delivery_carrier_form
msgid ""
"These methods allow to automatically compute the delivery price\n"
"                according to your settings; on the sales order (based on the\n"
"                quotation) or the invoice (based on the delivery orders)."
msgstr ""

#. module: delivery
#: model:ir.model.fields,help:delivery.field_delivery_carrier__margin
msgid "This percentage will be added to the shipping price."
msgstr "Persentase ini akan ditambahkan pada harga pengiriman."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_stock_quant_package__weight
msgid "Total weight of all the products contained in the package."
msgstr "Total berat semua produk di dalam kemasan."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_stock_picking__shipping_weight
msgid ""
"Total weight of packages and products not in a package. Packages with no "
"shipping weight specified will default to their products' total weight. This"
" is the weight used to compute the cost of the shipping."
msgstr ""
"Total berat kemasan dan produk yang tidak didalam kemasan. Paket tanpa berat"
" pengiriman akan secara default menggunakan total berat produk mereka. Ini "
"adalah berat yang digunakan untuk menghitung biaya pengiriman."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_stock_picking__weight_bulk
msgid "Total weight of products which are not in a package."
msgstr "Total berat produk yang tidak di dalam kemasan."

#. module: delivery
#: model:ir.model.fields,help:delivery.field_stock_quant_package__shipping_weight
msgid "Total weight of the package."
msgstr "Total berat dari paket"

#. module: delivery
#: model:ir.model.fields,help:delivery.field_stock_picking__weight
msgid "Total weight of the products in the picking."
msgstr "Total berat produk di picking."

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.delivery_tracking_url_warning_form
msgid "Trackers URL"
msgstr "URL Pelacak"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Tracking"
msgstr "Pelacakan"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking__carrier_tracking_ref
msgid "Tracking Reference"
msgstr "Referensi Pelacak"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking__carrier_tracking_url
msgid "Tracking URL"
msgstr "URL Pelacakan"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.sale_order_portal_content_inherit_sale_stock_inherit_website_sale_delivery
msgid "Tracking:"
msgstr "Melacak:"

#. module: delivery
#: model:ir.model,name:delivery.model_stock_picking
msgid "Transfer"
msgstr "Transfer"

#. module: delivery
#: model:product.product,uom_name:delivery.product_product_delivery
#: model:product.product,uom_name:delivery.product_product_delivery_normal
#: model:product.product,uom_name:delivery.product_product_delivery_poste
#: model:product.template,uom_name:delivery.product_product_delivery_normal_product_template
#: model:product.template,uom_name:delivery.product_product_delivery_poste_product_template
#: model:product.template,uom_name:delivery.product_product_delivery_product_template
msgid "Units"
msgstr "Unit"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.choose_delivery_carrier_view_form
msgid "Update"
msgstr "Perbaharui"

#. module: delivery
#: code:addons/delivery/models/sale_order.py:0
#: model_terms:ir.ui.view,arch_db:delivery.view_order_form_with_carrier
#, python-format
msgid "Update shipping cost"
msgstr "Perbarui biaya pengiriman"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__variable
msgid "Variable"
msgstr "Variabel"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_price_rule__variable_factor
msgid "Variable Factor"
msgstr "Faktor Variasi"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__volume
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__volume
msgid "Volume"
msgstr "Volume"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_move__weight
#: model:ir.model.fields,field_description:delivery.field_stock_picking__weight
#: model:ir.model.fields,field_description:delivery.field_stock_quant_package__weight
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__weight
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__weight
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Weight"
msgstr "Berat"

#. module: delivery
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable__wv
#: model:ir.model.fields.selection,name:delivery.selection__delivery_price_rule__variable_factor__wv
msgid "Weight * Volume"
msgstr "Berat * Volume"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_stock_picking__shipping_weight
msgid "Weight for Shipping"
msgstr "Berat untuk Pengiriman"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_picking_withcarrier_out_form
msgid "Weight for shipping"
msgstr "Berat untuk pengiriman"

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_choose_delivery_package__weight_uom_name
#: model:ir.model.fields,field_description:delivery.field_stock_picking__weight_uom_name
#: model:ir.model.fields,field_description:delivery.field_stock_quant_package__weight_uom_name
msgid "Weight unit of measure label"
msgstr "Satuan berat dari label ukuran"

#. module: delivery
#: code:addons/delivery/models/sale_order.py:0
#, python-format
msgid ""
"You can not update the shipping costs on an order where it was already invoiced!\n"
"\n"
"The following delivery lines (product, invoiced quantity and price) have already been processed:\n"
"\n"
msgstr ""
"Anda tidak dapat mengupdate biaya pengiriman pada order yang sudah difaktur!\n"
"\n"
"Baris pengiriman berikut (produk, kuantitas difaktur dan harga) sudah diproses:\n"
"\n"

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.delivery_tracking_url_warning_form
msgid "You have multiple tracker links, they are available in the chatter."
msgstr ""
"Anda memiliki lebih dari satu link pelacak, mereka tersedia di chatter."

#. module: delivery
#: code:addons/delivery/models/stock_picking.py:0
#, python-format
msgid ""
"Your delivery method has no redirect on courier provider's website to track "
"this order."
msgstr ""
"Metode pengiriman Anda tidak memiliki pengalihan ulang pada website penyedia"
" kurir untuk melacak order ini."

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__zip_from
msgid "Zip From"
msgstr ""

#. module: delivery
#: model:ir.model.fields,field_description:delivery.field_delivery_carrier__zip_to
msgid "Zip To"
msgstr ""

#. module: delivery
#: model_terms:ir.ui.view,arch_db:delivery.view_delivery_carrier_form
msgid "e.g. UPS Express"
msgstr "contoh UPS Express"
