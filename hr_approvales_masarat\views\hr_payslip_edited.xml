<?xml version="1.0" encoding="utf-8"?>

<odoo>
    <record id="view_hr_payslip_form_x1" model="ir.ui.view">
        <field name="name">hr.payslip.form.inherit.x1</field>
        <field name="model">hr.payslip</field>
        <field name="inherit_id" ref="hr_payroll_community.view_hr_payslip_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='credit_note']" position="after">
                <field name="unapproved_latency_minutes"/>
                <field name="approved_absence_days"/>
                <field name="non_approved_absence_days"/>
                <field name="work_hour_day"/>
                <field name="over_time_hours_at_home" invisible="1"/>
                <field name="over_time_hours_at_work" invisible="1"/>
                <field name="over_time_hours_at_holiday" invisible="1"/>
                <field name="car_allawance_amount" invisible="1"/>
                <field name="masarat_reward" invisible="1"/>
                <field name="work_assighnment_deduction" invisible="1"/>
            </xpath>
        </field>
    </record>

    <record id="view_hr_payslip_by_employees_xx1" model="ir.ui.view">
        <field name="name">hr.payslip.employees.form.inherit.x1</field>
        <field name="model">hr.payslip.employees</field>
        <field name="inherit_id" ref="hr_payroll_community.view_hr_payslip_by_employees"/>
        <field name="arch" type="xml">
            <xpath expr="//button[@name='compute_sheet']" position="attributes">
                <attribute name="confirm">يرجى التأكد من أن الموظف ليس في إجازة وضع , اصابة عمل , اجازة مرضية !</attribute>
            </xpath>
        </field>
    </record>
</odoo>