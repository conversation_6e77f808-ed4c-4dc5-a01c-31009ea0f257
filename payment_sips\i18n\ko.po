# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_sips
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:49+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: payment_sips
#: code:addons/payment_sips/models/payment_transaction.py:0
#, python-format
msgid "Incorrect amount: received %(received).2f, expected %(expected).2f"
msgstr ""

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_version
msgid "Interface Version"
msgstr "인터페이스 버전"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_merchant_id
msgid "Merchant ID"
msgstr "판매자 ID"

#. module: payment_sips
#: code:addons/payment_sips/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "%s 참조와 일치하는 거래 항목이 없습니다."

#. module: payment_sips
#: model:ir.model,name:payment_sips.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "결제 매입사"

#. module: payment_sips
#: model:ir.model,name:payment_sips.model_account_payment_method
msgid "Payment Methods"
msgstr "지급 방법"

#. module: payment_sips
#: model:ir.model,name:payment_sips.model_payment_transaction
msgid "Payment Transaction"
msgstr "결제 내역"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_prod_url
msgid "Production URL"
msgstr "생산 URL"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__provider
msgid "Provider"
msgstr "공급업체"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_secret
msgid "SIPS Secret Key"
msgstr "SIPS 보안 키"

#. module: payment_sips
#: model_terms:ir.ui.view,arch_db:payment_sips.payment_acquirer_form
msgid "Secret Key"
msgstr "비밀 키"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_key_version
msgid "Secret Key Version"
msgstr "보안 키 버전"

#. module: payment_sips
#: model:account.payment.method,name:payment_sips.payment_method_sips
#: model:ir.model.fields.selection,name:payment_sips.selection__payment_acquirer__provider__sips
msgid "Sips"
msgstr "Sips"

#. module: payment_sips
#: model:ir.model.fields,field_description:payment_sips.field_payment_acquirer__sips_test_url
msgid "Test URL"
msgstr "테스트 URL"

#. module: payment_sips
#: model:ir.model.fields,help:payment_sips.field_payment_acquirer__sips_merchant_id
msgid "The ID solely used to identify the merchant account with Sips"
msgstr "Sips에서 판매자 계정을 식별하는 데 사용되는 ID입니다."

#. module: payment_sips
#: model:ir.model.fields,help:payment_sips.field_payment_acquirer__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr ""

#. module: payment_sips
#: code:addons/payment_sips/models/payment_transaction.py:0
#, python-format
msgid "This currency is not supported: %s."
msgstr ""

#. module: payment_sips
#: code:addons/payment_sips/models/payment_transaction.py:0
#, python-format
msgid "Unrecognized response received from the payment provider."
msgstr "결제대행업체로부터 받은 응답을 인식할 수 없습니다."
