// ------------------------------------------------------------------
// Layout
// ------------------------------------------------------------------

.o_DiscussSidebarMailbox {
    display: flex;
    align-items: center;
    padding: map-get($spacers, 1) 0;
}


.o_DiscussSidebarMailbox_item {
    margin-left: $o-mail-discuss-sidebar-category-item-margin;
    margin-right: $o-mail-discuss-sidebar-category-item-margin;

    &:first-child {
        margin-left: $o-mail-discuss-sidebar-category-item-avatar-left-margin;
    }

    &:last-child {
        margin-right: $o-mail-discuss-sidebar-scrollbar-width;
    }
}


.o_DiscussSidebarMailbox_name {
    @include text-truncate();
}

// ------------------------------------------------------------------
// Style
// ------------------------------------------------------------------

.o_DiscussSidebarMailbox {
    cursor: pointer;

    &.o-active {
        background-color: gray('200')
    }

    &:hover {
        background-color: gray('300');
    }

    &.o-starred-box {
        .o_DiscussSidebarMailbox_counter {
            border-color: gray('400');
            background-color: gray('400');
            color: gray('300');
        }
    }
}

.o_DiscussSidebarMailbox_counter {
    background-color: $o-brand-primary;
    color: gray('300');
}
