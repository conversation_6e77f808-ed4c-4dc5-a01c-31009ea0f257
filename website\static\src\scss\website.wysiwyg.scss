
@each $font-name, $font-config in $o-theme-font-configs {
    $url: map-get($font-config, 'url');
    @if $url {
        @import url("https://fonts.googleapis.com/css?family=#{unquote($url)}&display=swap");
    } @else {
        $name: map-get($font-config, 'name');
        $attachment: map-get($font-config, 'attachment');
        @if $attachment {
            @import url("/web/content/#{$attachment}/google-font-#{unquote($name)}");
        }
    }
}

:root {
    // Override css variables to influence the default style of the editor
    // without duplicating the css.
    @include print-variable('o-we-toolbar-height', $o-navbar-height);

    // Need the colors, name, menu background color and footer background color of each color palette
    $o-palette-names: ();
    $-default-cc: ();
    $-bg-attrs: 'menu', 'footer';
    @each $attr in $-bg-attrs {
        // Since the variables built here are currently only used for theme previews we
        // decided to keep things simple and we deliberately ignore potential overrides
        // from #{$-bg-attrs}-custom and #{$-bg-attrs}-gradient.
        $-default-cc: map-merge($-default-cc, ($attr: map-get($o-base-color-palette, $attr)))
    }

    @each $-selected-palette-name in $o-selected-color-palettes-names {
        $-palette: map-get($o-color-palettes, $-selected-palette-name);

        @if $-selected-palette-name != 'user-palette' {
            @each $key, $value in $-palette {
                @include print-variable('o-palette-#{$-selected-palette-name}-#{$key}', $value);
            }

            @each $attr-name, $default-cc-idx in $-default-cc {
                $-cc-idx: $default-cc-idx;
                @if map-has-key($-palette, $attr-name) {
                    $-cc-idx: map-get($-palette, $attr-name);
                }
                $-bg: $body-bg;
                @if $-cc-idx != null {
                    $-cc: nth($o-color-combinations, $-cc-idx);
                    $-bg: o-safe-get($-palette, 'o-cc#{$-cc-idx}-bg', map-get($-cc, 'bg'));
                    @if type-of($-bg) != color {
                        $-bg: map-get($-palette, $-bg);
                    }
                }
                @include print-variable('o-palette-#{$-selected-palette-name}-#{$attr-name}-bg', $-bg);
            }
        }
    }

    // Need the palette colors used as default menu and footer background color
    @each $attr-name, $default-cc-idx in $-default-cc {
        $-cc: nth($o-color-combinations, $default-cc-idx);
        $-default-bg: map-get($-cc, 'bg');
        @include print-variable('o-default-#{$attr-name}-bg', $-default-bg);
    }

    @include print-variable('palette-names', $o-selected-color-palettes-names);

    // Need info about the base grays which are used to compute the final grays
    @each $name, $color in $o-base-gray-color-palette {
        @include print-variable('base-#{$name}', $color);
    }
}

#oe_snippets {
    top: 0;
    .oe-toolbar {
        .color-indicator {
            padding: 0 2px 2px 2px;
        }
    }
}

// TRANSLATIONS
.oe_translate_examples li {
    margin: 10px;
    padding: 4px;
}

html[lang] > body.editor_enable [data-oe-translation-state] {
    &, .o_translation_select_option {
        background: rgba($o-we-content-to-translate-color, 0.5) !important;
    }

    &[data-oe-translation-state="translated"] {
        &, .o_translation_select_option {
            background: rgba($o-we-translated-content-color, 0.5) !important;
        }
    }

    &.o_dirty, &.oe_translated, .oe_translated {
        background: rgba($o-we-translated-content-color, 0.25) !important;
    }
}

html[data-edit_translations="1"] {
    .o_translate_mode_hidden {
        display: none !important;
    }
}

// SNIPPET PANEL
$i: 1;
@each $font-name, $font-config in $o-theme-font-configs {
    we-toggler.o_we_option_font_#{$i}, we-button.o_we_option_font_#{$i} > div {
        font-family: o-safe-get($font-config, 'family', $font-family-base);

        &::before {
            // Must prevail against the '/' for missing values.
            content: $font-name !important;
        }
    }
    $i: $i + 1;
}
.o_we_add_google_font_btn {
    border-top: 1px solid currentColor !important;
}

#oe_snippets > .o_we_customize_panel {
    .o_we_user_value_widget.o_palette_color_preview_button {
        display: inline-flex;
        margin: 1% 0;
        padding-right: 0.3rem;
        width: 50%;
        background: transparent;

        &.active, &:hover {
            background: transparent;

            > div {
                box-shadow: 0 0 0 2px $o-we-bg-darkest, 0 0 0 3px $o-we-accent;
            }
        }

        > div {
            display: flex;
            flex: 1 1 auto;
            align-items: stretch;
            justify-content: flex-end;
            margin: 3px;
            min-height: $o-we-sidebar-content-field-dropdown-grid-item-height * .5;
            border-radius: $o-we-sidebar-content-field-dropdown-grid-item-height;
            box-shadow: 0 0 0 1px $o-we-bg-darkest;
        }

        .o_palette_color_preview {
            flex: 1 0 0;
        }
    }

    we-select.o_scroll_effects_selector we-button {
        padding-top: $o-we-item-spacing;
        padding-bottom: $o-we-item-spacing;

        img {
            max-height: 80px;
            width: auto;
            margin-right: $o-we-item-spacing;
            margin-left: $o-we-item-spacing * .5;
        }
    }

    we-button.o_we_mobile {
        > div {
            // FIXME maybe there is some refactoring to do so that all SVG icons
            // can be properly aligned with extra CSS rules...
            display: flex;
            align-items: center;

            svg {
                width: 15px;
                fill: $o-we-color-danger;
                margin-bottom: 0;
                margin-left: -3px;

                &:hover {
                    fill: darken($o-we-color-danger, 7.5%);
                }
            }
        }
        &.active > div > svg {
            fill: $o-we-sidebar-content-field-color;

            &:hover {
                fill: $o-we-sidebar-content-field-pressed-color;
            }
        }
    }

    //----------------------------------------------------------------------
    // 'Options' Tab Specific Components
    //----------------------------------------------------------------------

    // Theme Colors Editor
    .o_we_theme_colors_selector {

        > we-title {
            display: none
        }
        .o_we_so_color_palette.o_we_user_value_widget {

            + .o_we_so_color_palette {
                margin-left: $o-we-item-spacing * .5;

                &:nth-child(4) {
                    margin-left: $o-we-item-spacing * 3;
                }
            }
            .o_we_color_preview {
                width: $o-we-sidebar-content-field-colorpicker-size-large;
                height: $o-we-sidebar-content-field-colorpicker-size-large;
            }
        }
        > div, we-select.o_we_theme_colors_select, we-toggler {
            display: flex;
        }
        > div {
            align-items: stretch;
            width: 100%;
        }
        we-select.o_we_theme_colors_select {
            justify-content: flex-end;
            margin-left: auto;

            > div, we-toggler {
                height: 100%;
            }

            we-selection-items {
                padding-top: $o-we-dropdown-item-height * .5;
                padding-bottom: $o-we-dropdown-item-height * .5;
                background: $o-we-dropdown-item-active-bg;
            }
        }
        we-toggler {
            align-items: center;
            padding: 0 0.4rem;
            font-size: 1.5em;

            &:after {
                content: none;
            }
        }
    }

    // Palettes Dropdown
    .o_palette_color_preview_button > div {
        min-height: 24px;
    }

    // CC Edition
    .o_we_cc_preview_wrapper {
        // Use box-shadow rather than border-bottom in order to
        // avoid misalignments in the 'Options' tab.
        border: 1px solid;
        border-color: rgba($o-we-item-standup-color-light, .2) $o-we-sidebar-content-field-dropdown-border-color transparent;
        box-shadow: 0 1px 0 $o-we-item-standup-color-dark;

        + .o_we_collapse_toggler {
            height: 35px; // FIXME hardcoded...
        }
    }
}

// SNIPPET OPTIONS
.o_we_border_preview {
    display: inline-block;
    width: 999px;
    max-width: 100%;
    margin-bottom: 2px;
    border-width: 4px;
    border-bottom: none !important;
}

.pac-container { // google map autosuggestion
    z-index: $zindex-modal-backdrop; // > $o-we-zindex
    width: $o-we-sidebar-width !important;
    font-size: $o-we-sidebar-font-size;
    margin-left: -$o-we-sidebar-width/2;
    border-top: none;
    background-color: $o-we-sidebar-content-field-dropdown-bg;
    box-shadow: $o-we-sidebar-content-field-dropdown-shadow;
    &:after {
        display: none;
    }
    .pac-item {
        border-top: $o-we-sidebar-content-field-border-width solid lighten($o-we-sidebar-content-field-dropdown-border-color, 15%);
        border-radius: $o-we-sidebar-content-field-border-radius;
        background-color: $o-we-sidebar-content-field-clickable-bg;
        color: $o-we-sidebar-content-field-clickable-color;
        &:hover {
            background-color: $o-we-sidebar-content-field-dropdown-item-bg-hover;
            cursor: pointer;
        }
    }
    .pac-item-query {
        color: $o-we-sidebar-content-field-clickable-color;
    }
}
