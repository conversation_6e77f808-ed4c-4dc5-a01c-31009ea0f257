.product-info-popup {
    max-width: 800px !important;
}

.product-info-popup .body {
    max-height: 600px;
    overflow: auto;
}

.product-info-popup .section-product-info-title {
    display: flex;
    justify-content: space-between;
}

.product-info-popup .section-product-info-title .global-info-title {
    font-size: 32px;
    font-weight: bold;
}

.product-info-popup .section-product-info-title  div:first-child{
    max-width: 60%;
}

.product-info-popup .section-product-info-title .global-info-title.product-name {
    max-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.product-info-popup .subsection-list {
    display: flex;
}

.product-info-popup .subsection-list .subsection-list-value {
    margin-left: 30px;
}

.product-info-popup .column {
    display: flex;
    flex-direction: column;
}

.product-info-popup .flex-start {
    align-items: flex-start;
}

.product-info-popup .flex-end {
    align-items: flex-end;
}

.product-info-popup .section-title {
    font-size: 24px;
    font-weight: bold;
    margin-top: 30px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    white-space: nowrap;
}

.product-info-popup .section-title-line {
    width: 100%;
    border-top: solid black 3px;
    opacity: 25%;
    margin: 15px 10px 0 10px;
}

.product-info-popup table {
    text-align: left;
}
.product-info-popup .section-financials-body {
    display: flex;
    justify-content: space-between;
}

.product-info-popup .section-financials-body table {
    width: 45%;
}

.product-info-popup .section-financials-body table td {
    width: 50%;
}

.product-info-popup .section-inventory-body table td {
    padding-right: 15px;
}

.product-info-popup .section-supplier-body table {
    width: 50%;
}

.product-info-popup .section-supplier-body table div{
    display: flex;
    justify-content: space-between;
}

.product-info-popup .table-value {
    padding-left: 15px;
}

.product-info-popup .searchable {
    color: blue;
    cursor: pointer;
}

.product-info-popup .searchable:hover {
    font-weight: bold;
}

.product-info-popup .searchable:active {
    color: darkblue;
}

.product-info-popup .section-order-body table {
    text-align: left;
}

@media screen and (max-width: 768px) {
    .product-info-popup .body {
        max-height: 70vh;
    }

    .product-info-popup .section-product-info-title  div:first-child{
        max-width: 100%;
    }

    .product-info-popup .section-product-info-title .global-info-title {
        font-size: 28px;
    }

    .product-info-popup .section-product-info-title {
        flex-direction: column;
    }

    .product-info-popup .flex-end {
        align-items: unset;
    }

    .product-info-popup .section-financials-body {
        flex-direction: column;
    }

    .product-info-popup .section-financials-body table {
        width: unset;
    }

    .product-info-popup .mobile-table tr {
        display: flex;
        flex-direction: column;
        text-align: left;
    }

    .product-info-popup .mobile-table .table-name:before {
        content: "- ";
    }

    .product-info-popup .mobile-table td:not(:nth-child(1)) {
        margin-left: 15px;
    }

    .product-info-popup .mobile-line {
        display: flex;
    }

    .product-info-popup .mobile-line td {
        margin: 0 15px;
    }

    .product-info-popup .section-supplier-body table {
        width: 100%;
    }

    .product-info-popup .section-supplier-body table tr {
        align-items: start;
    }

    .product-info-popup .section-variants-body .table-value {
        padding-left: 0;
    }

    .product-info-popup .button.cancel {
        float: unset;
        margin: 10px auto;
    }
}
