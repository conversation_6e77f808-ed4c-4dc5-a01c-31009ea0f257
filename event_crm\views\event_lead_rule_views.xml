<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data>
    <record id="event_lead_rule_view_search" model="ir.ui.view">
        <field name="name">event.lead.rule.view.search</field>
        <field name="model">event.lead.rule</field>
        <field name="arch" type="xml">
            <search string="Search Lead Generation Rules">
                <field name="name" string="Name"/>
                <separator/>
                <filter string="Archived" name="filter_inactive" domain="[('active', '=', False)]"/>
                <filter string="Creation Type" name="filter_lead_creation_basis" context="{'group_by': 'lead_creation_basis'}"/>
                <filter string="Trigger Type" name="filter_lead_creation_trigger" context="{'group_by': 'lead_creation_trigger'}"/>
            </search>
        </field>
    </record>

    <record id="event_lead_rule_view_tree" model="ir.ui.view">
        <field name="name">event.lead.rule.view.tree</field>
        <field name="model">event.lead.rule</field>
        <field name="arch" type="xml">
            <tree string="Lead Generation Rules">
                <field name="name"/>
                <field name="lead_creation_basis" string="Lead Creation Type" invisible="1"/>
                <field name="lead_creation_trigger"/>
                <field name="event_type_ids" widget="many2many_tags"/>
                <field name="event_id"/>
                <field name="company_id" groups="base.group_multi_company"/>
            </tree>
        </field>
    </record>

    <record id="event_lead_rule_view_form" model="ir.ui.view">
        <field name="name">event.lead.rule.view.form</field>
        <field name="model">event.lead.rule</field>
        <field name="arch" type="xml">
            <form string="Lead Generation Rule">
                <sheet>
                    <widget name="web_ribbon" title="Archived" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                    <div class="oe_title">
                        <label for="name"/>
                        <h1><field name="name" placeholder="e.g. B2B Fairs"/></h1>
                    </div>
                    <field name="active" invisible="1"/>
                    <group name="lead_creation_configuration">
                        <group name="lead_creation_basis" invisible="1">
                            <field name="lead_creation_basis" widget="radio"/>
                        </group>
                        <group>
                            <field name="lead_creation_trigger" widget="radio"/>
                        </group>
                    </group>
                    <group string="For any of these Events">
                        <group>
                            <field name="event_type_ids" widget="many2many_tags"/>
                            <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                        </group>
                        <group>
                            <field name="event_id" options="{'no_create': True}"/>
                        </group>
                    </group>
                    <group string="If the Attendees meet these Conditions">
                        <field name="event_registration_filter" widget="domain" options="{'model': 'event.registration'}" nolabel="1"/>
                    </group>
                    <group string="Lead Default Values">
                        <group class="col">
                            <field name="lead_type" groups="crm.group_use_lead"/>
                            <field name="lead_sales_team_id"/>
                            <field name="lead_user_id"/>
                        </group>
                        <group class="col">
                            <field name="lead_tag_ids" widget="many2many_tags" options="{'color_field': 'color'}"/>
                        </group>                         
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="event_lead_rule_action" model="ir.actions.act_window">
        <field name="name">Lead Generation Rule</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">event.lead.rule</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">Create a Lead Generation Rule</p>
            <p>Those automatically create leads when attendees register.</p>
        </field>
    </record>

    <menuitem name="Lead Generation"
        id="event_lead_rule_menu" 
        action="event_lead_rule_action"
        parent="event.menu_event_configuration"
        sequence="10"
        groups="event.group_event_manager"/>
</data>
</odoo>
