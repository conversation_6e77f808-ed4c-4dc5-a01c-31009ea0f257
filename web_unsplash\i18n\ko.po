# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_unsplash
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: web_unsplash
#: model_terms:ir.ui.view,arch_db:web_unsplash.res_config_settings_view_form
msgid "<i class=\"fa fa-arrow-right\"/> Generate an Access Key"
msgstr "<i class=\"fa fa-arrow-right\"/> 액세스 키 생성"

#. module: web_unsplash
#: model:ir.model.fields,field_description:web_unsplash.field_res_config_settings__unsplash_access_key
msgid "Access Key"
msgstr "액세스 키"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Application ID"
msgstr "애플리케이션 ID"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Apply"
msgstr "적용"

#. module: web_unsplash
#: model:ir.model,name:web_unsplash.model_res_config_settings
msgid "Config Settings"
msgstr "설정 구성"

#. module: web_unsplash
#: code:addons/web_unsplash/controllers/main.py:0
#, python-format
msgid "ERROR: Unknown Unsplash URL!"
msgstr "오류 : 알 수 없는 Unsplash URL!"

#. module: web_unsplash
#: code:addons/web_unsplash/controllers/main.py:0
#, python-format
msgid "ERROR: Unknown Unsplash notify URL!"
msgstr "오류 : 알 수 없는 Unsplash 알림 URL!"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Get an Access key"
msgstr "액세스 키 얻기"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Paste your access key here"
msgstr "여기에 액세스 키 붙여넣기"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Paste your application ID here"
msgstr "여기에 응용 프로그램 ID 붙여넣기"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Photos (via Unsplash)"
msgstr "사진 (Unsplash를 통해 제공)"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Please check your Unsplash access key and application ID."
msgstr "Unsplash 액세스 키 및 응용 프로그램 ID를 확인하십시오."

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Please check your internet connection or contact administrator."
msgstr "인터넷 연결을 확인하거나 관리자에게 문의하십시오."

#. module: web_unsplash
#: model:ir.model,name:web_unsplash.model_ir_qweb_field_image
msgid "Qweb Field Image"
msgstr "Qweb 이미지 필드"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Search is temporarily unavailable"
msgstr "검색을 일시적으로 사용할 수 없습니다"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Setup Unsplash to access royalty free photos."
msgstr "저작권이 없는 사진에 액세스하려면 Unsplash를 설정하세요."

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Something went wrong"
msgstr "문제가 발생했습니다"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid ""
"The max number of searches is exceeded. Please retry in an hour or extend to"
" a better account."
msgstr "최대 검색 횟수를 초과했습니다. 한 시간 후에 다시 시도하거나 더 나은 계정으로 확장하십시오."

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Unauthorized Key"
msgstr "승인되지 않은 키"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/js/unsplash_image_widget.js:0
#, python-format
msgid "Uploading %s '%s' images."
msgstr "%s '%s' 이미지 파일을 업로드하는 중입니다."

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/js/unsplash_image_widget.js:0
#, python-format
msgid "Uploading '%s' image."
msgstr "'%s' 이미지 파일을 업로드하는 중입니다."

#. module: web_unsplash
#: model:ir.model,name:web_unsplash.model_res_users
msgid "Users"
msgstr "사용자"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "and paste"
msgstr "및 붙여넣기"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "and paste it here:"
msgstr "및 여기에 붙여넣기:"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "here:"
msgstr "여기:"
