.o_form_sheet {
    $o-hrs-timeline-entry-padding: .5rem;
    $o-hrs-timeline-dot-size: .6rem;

    // Overall design
    // =========================================
    .o_hr_skills_group {
        // Overwrite '.o_list_table' default design
        .o_list_view {
            .o_list_table {
                cursor: auto;
            }

            tbody:first-of-type > .o_resume_group_header:first-child {
                box-shadow: none;
            }
        }

        &.o_hr_skills_editable {
            .o_data_row {
                cursor: pointer;
            }
        }

        .o_data_row {
            cursor: default;
        }

        // Deny user interaction to headers but keep access to buttons
        .o_group_header, .o_resume_group_header {
            &, &:hover {
                background: none !important;
                cursor: initial;
                pointer-events: none;
            }
            .o_field_x2many_list_row_add, .o_field_x2many_list_row_add:hover {
                cursor: pointer;
                pointer-events: initial;
            }
        }

        // Resumé design
        // =========================================
        &.o_group_resume {
            .o_data_row td {
                padding: $o-hrs-timeline-entry-padding;

                &.o_resume_timeline_cell {
                    div {
                        @include size($o-hrs-timeline-dot-size);
                    }

                    &:before {
                        @include o-position-absolute(0, $left: ($o-hrs-timeline-dot-size * .5 + $o-hrs-timeline-entry-padding));
                        @include size(1px, 100%);
                        margin-left: -.01rem;
                        background-color: $border-color;
                        content: "";
                    }
                }
            }

            .o_resume_line_title, .o_resume_line_desc {
                white-space: normal;
            }

            .o_resume_line_title, .o_resume_line_dates {
                line-height: 1;
            }

            .o_resume_group_header + .o_data_row .o_resume_timeline_cell:before {
                top: $o-hrs-timeline-entry-padding;
            }

            .o_data_row.o_data_row_last {
                .o_resume_line_desc {
                    margin-bottom: $headings-margin-bottom;
                }

                .o_resume_timeline_cell:before {
                    height: $o-hrs-timeline-entry-padding;
                }
            }
        }

        // Skills design
        // =========================================
        &.o_group_skills {
            .o_resume_empty_helper {
                display: none;
            }

            .o_group_header {
                > .o_group_name {
                    padding: .8rem .5rem 0 0;
                }

                &:first-child > .o_group_name {
                    padding-top: 0;
                }
            }

            .o_skill_cell {
                padding-left: 0;
                white-space: normal !important;

                .o_progressbar {
                    display: inline-flex;
                    align-items: center;
                }

                .o_progress {
                    border: 0;
                    background: $gray-300;
                    height: 5px;
                }

                .o_progressbar_value {
                    width: auto;
                    font-size: $font-size-sm;
                    font-weight: bold;
                }
            }
        }
    }
}

// Editing mode
// =========================================
.o_form_view.o_form_editable .o_form_sheet .o_hr_skills_group {
    .o_group_name {
        background-color: gray('200');
    }

    .o_resume_group_header .btn {
        margin-top: .25rem;
        margin-right: .4rem;
    }

    &.o_group_skills .o_group_name {
        padding-top: .4em;
        padding-bottom: .4rem;
    }

    .o_group_name, .o_skill_cell {
        padding-left: .5rem;
    }

    &.o_group_skills .o_group_name > b, .o_horizontal_separator {
        color: color-yiq(gray('200'));
        font-style: italic;
    }
}
