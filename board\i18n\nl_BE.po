# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * board
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Dutch (Belgium) (https://www.transifex.com/odoo/teams/41243/nl_BE/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl_BE\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:25
#, python-format
msgid "&nbsp;"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/js/favorite_menu.js:97
#, python-format
msgid "'%s' added to dashboard"
msgstr ""

#. module: board
#: model_terms:ir.actions.act_window,help:board.open_board_my_dash_action
msgid "<b>Your personal dashboard is empty.</b>"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:69
#, python-format
msgid "Add"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:63
#, python-format
msgid "Add to my Dashboard"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/js/dashboard.js:335
#, python-format
msgid "Are you sure you want to remove this item?"
msgstr ""

#. module: board
#: model:ir.model,name:board.model_board_board
msgid "Board"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:7
#, python-format
msgid "Change Layout"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:5
#, python-format
msgid "Change Layout.."
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/xml/board.xml:36
#, python-format
msgid "Choose dashboard layout"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/js/favorite_menu.js:99
#, python-format
msgid "Could not add filter to dashboard"
msgstr ""

#. module: board
#: model:ir.model.fields,field_description:board.field_board_board_display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: board
#. openerp-web
#: code:addons/board/static/src/js/dashboard.js:85
#, python-format
msgid "Edit Layout"
msgstr ""

#. module: board
#: model:ir.model.fields,field_description:board.field_board_board_id
msgid "ID"
msgstr "ID"

#. module: board
#: model:ir.model.fields,field_description:board.field_board_board___last_update
msgid "Last Modified on"
msgstr "Laatst gewijzigd op"

#. module: board
#. openerp-web
#: code:addons/board/static/src/js/dashboard.js:48
#: model:ir.actions.act_window,name:board.open_board_my_dash_action
#: model:ir.ui.menu,name:board.menu_board_my_dash
#: model_terms:ir.ui.view,arch_db:board.board_my_dash_view
#, python-format
msgid "My Dashboard"
msgstr ""

#. module: board
#: model_terms:ir.actions.act_window,help:board.open_board_my_dash_action
msgid ""
"To add your first report into this dashboard, go to any\n"
"                    menu, switch to list or graph view, and click <i>'Add to\n"
"                    Dashboard'</i> in the extended search options."
msgstr ""

#. module: board
#: model_terms:ir.actions.act_window,help:board.open_board_my_dash_action
msgid ""
"You can filter and group data before inserting into the\n"
"                    dashboard using the search options."
msgstr ""
