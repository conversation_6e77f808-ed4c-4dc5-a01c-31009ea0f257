# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* microsoft_calendar
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <ye<PERSON><PERSON><PERSON><PERSON>@itpp.dev>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# Серге<PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2022
# alenafairy, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: alenafairy, 2023\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: microsoft_calendar
#: code:addons/microsoft_calendar/models/calendar.py:0
#, python-format
msgid "%(reminder_type)s - %(duration)s Days"
msgstr "%(reminder_type)s - %(duration)s дней"

#. module: microsoft_calendar
#: code:addons/microsoft_calendar/models/calendar.py:0
#, python-format
msgid "%(reminder_type)s - %(duration)s Hours"
msgstr "%(reminder_type)s - %(duration)s часов"

#. module: microsoft_calendar
#: code:addons/microsoft_calendar/models/calendar.py:0
#, python-format
msgid "%(reminder_type)s - %(duration)s Minutes"
msgstr "%(reminder_type)s - %(duration)s минут"

#. module: microsoft_calendar
#: code:addons/microsoft_calendar/models/calendar.py:0
#, python-format
msgid "%s - At time of event"
msgstr ""

#. module: microsoft_calendar
#. openerp-web
#: code:addons/microsoft_calendar/static/src/xml/base_calendar.xml:0
#, python-format
msgid "&nbsp;Outlook"
msgstr ""

#. module: microsoft_calendar
#: code:addons/microsoft_calendar/models/calendar.py:0
#, python-format
msgid "(No title)"
msgstr "(Нет названия)"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_calendar_event__active
#: model:ir.model.fields,field_description:microsoft_calendar.field_calendar_recurrence__active
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_sync__active
msgid "Active"
msgstr "Активно"

#. module: microsoft_calendar
#. openerp-web
#: code:addons/microsoft_calendar/static/src/js/microsoft_calendar.js:0
#, python-format
msgid ""
"An administrator needs to configure Outlook Synchronization before you can "
"use it!"
msgstr ""

#. module: microsoft_calendar
#: code:addons/microsoft_calendar/models/res_users.py:0
#, python-format
msgid ""
"An error occurred while generating the token. Your authorization code may be"
" invalid or has already expired [%s]. You should check your Client ID and "
"secret on the Microsoft Azure portal or try to stop and restart your "
"calendar synchronisation."
msgstr ""

#. module: microsoft_calendar
#. openerp-web
#: code:addons/microsoft_calendar/static/src/js/microsoft_calendar.js:0
#, python-format
msgid "Are you sure you want to delete this record ?"
msgstr "Вы действительно хотите удалить эту запись?"

#. module: microsoft_calendar
#: model:ir.model,name:microsoft_calendar.model_calendar_attendee
msgid "Calendar Attendee Information"
msgstr "Информация календаря участника"

#. module: microsoft_calendar
#: model:ir.model,name:microsoft_calendar.model_calendar_event
msgid "Calendar Event"
msgstr "Событие в календаре"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.microsoft_calendar_reset_account_view_form
msgid "Cancel"
msgstr "Отмена"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.res_config_settings_view_form
msgid "Client ID"
msgstr "ID клиента"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.res_config_settings_view_form
msgid "Client Secret"
msgstr "Секретный ключ клиента"

#. module: microsoft_calendar
#: model:ir.model,name:microsoft_calendar.model_res_config_settings
msgid "Config Settings"
msgstr "Конфигурационные настройки"

#. module: microsoft_calendar
#. openerp-web
#: code:addons/microsoft_calendar/static/src/js/microsoft_calendar.js:0
#: code:addons/microsoft_calendar/static/src/js/microsoft_calendar.js:0
#, python-format
msgid "Configuration"
msgstr "Настройки"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.microsoft_calendar_reset_account_view_form
msgid "Confirm"
msgstr "Подтвердить"

#. module: microsoft_calendar
#. openerp-web
#: code:addons/microsoft_calendar/static/src/js/microsoft_calendar.js:0
#, python-format
msgid "Confirmation"
msgstr "Подтверждение"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__create_uid
msgid "Created by"
msgstr "Создан"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__create_date
msgid "Created on"
msgstr "Создан"

#. module: microsoft_calendar
#. openerp-web
#: code:addons/microsoft_calendar/static/src/xml/microsoft_calendar_popover.xml:0
#, python-format
msgid "Delete"
msgstr "Удалить"

#. module: microsoft_calendar
#: model:ir.model.fields.selection,name:microsoft_calendar.selection__microsoft_calendar_account_reset__delete_policy__delete_odoo
msgid "Delete from Odoo"
msgstr "Удалить из системы"

#. module: microsoft_calendar
#: model:ir.model.fields.selection,name:microsoft_calendar.selection__microsoft_calendar_account_reset__delete_policy__delete_both
msgid "Delete from both"
msgstr "Удалить"

#. module: microsoft_calendar
#: model:ir.model.fields.selection,name:microsoft_calendar.selection__microsoft_calendar_account_reset__delete_policy__delete_microsoft
msgid "Delete from the current Microsoft Calendar account"
msgstr ""

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: microsoft_calendar
#: model:ir.model,name:microsoft_calendar.model_calendar_recurrence
msgid "Event Recurrence Rule"
msgstr "Правило повторения встречи"

#. module: microsoft_calendar
#: code:addons/microsoft_calendar/models/calendar.py:0
#, python-format
msgid ""
"For a correct synchronization between Odoo and Outlook Calendar, all attendees must have an email address. However, some events do not respect this condition. As long as the events are incorrect, the calendars will not be synchronized.\n"
"Either update the events/attendees or archive these events %s:\n"
"%s"
msgstr ""

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__id
msgid "ID"
msgstr "Идентификатор"

#. module: microsoft_calendar
#: model:ir.model.fields,help:microsoft_calendar.field_calendar_event__active
msgid ""
"If the active field is set to false, it will allow you to hide the event "
"alarm information without removing it."
msgstr ""
"Если поле «Активен» имеет значение Истина, можно будет скрыть информацию об "
"уведомлении о мероприятии, не удаляя его."

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset____last_update
msgid "Last Modified on"
msgstr "Последнее изменение"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__write_uid
msgid "Last Updated by"
msgstr "Последний раз обновил"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: microsoft_calendar
#: model:ir.model.fields.selection,name:microsoft_calendar.selection__microsoft_calendar_account_reset__delete_policy__dont_delete
msgid "Leave them untouched"
msgstr "Оставьте их нетронутыми"

#. module: microsoft_calendar
#: model:ir.actions.act_window,name:microsoft_calendar.microsoft_calendar_reset_account_action
#: model:ir.model,name:microsoft_calendar.model_microsoft_calendar_account_reset
msgid "Microsoft Calendar Account Reset"
msgstr ""

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_calendar_event__microsoft_id
msgid "Microsoft Calendar Event Id"
msgstr ""

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_sync__microsoft_id
msgid "Microsoft Calendar Id"
msgstr ""

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_calendar_recurrence__microsoft_id
msgid "Microsoft Calendar Recurrence Id"
msgstr ""

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_res_config_settings__cal_microsoft_client_id
msgid "Microsoft Client_id"
msgstr ""

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_res_config_settings__cal_microsoft_client_secret
msgid "Microsoft Client_key"
msgstr ""

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_res_users__microsoft_calendar_sync_token
msgid "Microsoft Next Sync Token"
msgstr ""

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_calendar_event__microsoft_recurrence_master_id
msgid "Microsoft Recurrence Master Id"
msgstr ""

#. module: microsoft_calendar
#: code:addons/microsoft_calendar/models/calendar.py:0
#, python-format
msgid "Modified occurrence is crossing or overlapping adjacent occurrence."
msgstr ""

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_calendar_event__need_sync_m
#: model:ir.model.fields,field_description:microsoft_calendar.field_calendar_recurrence__need_sync_m
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_sync__need_sync_m
msgid "Need Sync M"
msgstr ""

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.view_users_form
msgid "Next Sync Token"
msgstr "Следующий токен синхронизации"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__sync_policy
msgid "Next Synchronization"
msgstr "Следующая синхронизация"

#. module: microsoft_calendar
#: code:addons/microsoft_calendar/models/calendar.py:0
#, python-format
msgid "Notification"
msgstr "Уведомления"

#. module: microsoft_calendar
#. openerp-web
#: code:addons/microsoft_calendar/static/src/xml/base_calendar.xml:0
#, python-format
msgid "Outlook"
msgstr "Outlook"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.view_users_form
msgid "Outlook Calendar"
msgstr "Календарь Outlook"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_res_users__microsoft_synchronization_stopped
msgid "Outlook Synchronization stopped"
msgstr ""

#. module: microsoft_calendar
#: model:ir.actions.server,name:microsoft_calendar.ir_cron_sync_all_cals_ir_actions_server
#: model:ir.cron,cron_name:microsoft_calendar.ir_cron_sync_all_cals
#: model:ir.cron,name:microsoft_calendar.ir_cron_sync_all_cals
msgid "Outlook: synchronization"
msgstr ""

#. module: microsoft_calendar
#. openerp-web
#: code:addons/microsoft_calendar/static/src/js/microsoft_calendar.js:0
#, python-format
msgid "Redirection"
msgstr "Перенаправление"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.view_users_form
msgid "Refresh Token"
msgstr "Обновить токен"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.view_users_form
msgid "Reset Account"
msgstr "Переустановка аккаунта"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.microsoft_calendar_reset_account_view_form
msgid "Reset Outlook Calendar Account"
msgstr ""

#. module: microsoft_calendar
#. openerp-web
#: code:addons/microsoft_calendar/static/src/js/microsoft_calendar.js:0
#, python-format
msgid "Success"
msgstr "Успех"

#. module: microsoft_calendar
#: model:ir.model,name:microsoft_calendar.model_microsoft_calendar_sync
msgid "Synchronize a record with Microsoft Calendar"
msgstr ""

#. module: microsoft_calendar
#: model:ir.model.fields.selection,name:microsoft_calendar.selection__microsoft_calendar_account_reset__sync_policy__all
msgid "Synchronize all existing events"
msgstr "Синхронизировать все существующие события"

#. module: microsoft_calendar
#: model:ir.model.fields.selection,name:microsoft_calendar.selection__microsoft_calendar_account_reset__sync_policy__new
msgid "Synchronize only new events"
msgstr "Синхронизировать только новые события"

#. module: microsoft_calendar
#. openerp-web
#: code:addons/microsoft_calendar/static/src/js/microsoft_calendar.js:0
#, python-format
msgid ""
"The Outlook Synchronization needs to be configured before you can use it, do"
" you want to do it now?"
msgstr ""

#. module: microsoft_calendar
#: code:addons/microsoft_calendar/models/res_users.py:0
#, python-format
msgid "The account for the Outlook Calendar service is not configured."
msgstr ""

#. module: microsoft_calendar
#. openerp-web
#: code:addons/microsoft_calendar/static/src/js/microsoft_calendar.js:0
#, python-format
msgid "The synchronization with Outlook calendar was successfully stopped."
msgstr ""

#. module: microsoft_calendar
#: model:ir.model.fields,help:microsoft_calendar.field_microsoft_calendar_account_reset__delete_policy
msgid "This will only affect events for which the user is the owner"
msgstr ""
"Это будет влиять только на события в Календаре, которые пользователь создал "
"сам"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.view_users_form
msgid "Token Validity"
msgstr "Срок действия токена"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__user_id
msgid "User"
msgstr "Пользователь"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.view_users_form
msgid "User Token"
msgstr ""

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__delete_policy
msgid "User's Existing Events"
msgstr "Существующие события в Календаре пользователя"

#. module: microsoft_calendar
#: model:ir.model,name:microsoft_calendar.model_res_users
msgid "Users"
msgstr "Пользователи"

#. module: microsoft_calendar
#. openerp-web
#: code:addons/microsoft_calendar/static/src/js/microsoft_calendar.js:0
#, python-format
msgid ""
"You are about to stop the synchronization of your calendar with Outlook. Are"
" you sure you want to continue?"
msgstr ""

#. module: microsoft_calendar
#. openerp-web
#: code:addons/microsoft_calendar/static/src/js/microsoft_calendar.js:0
#, python-format
msgid ""
"You will be redirected to Outlook to authorize the access to your calendar."
msgstr ""
