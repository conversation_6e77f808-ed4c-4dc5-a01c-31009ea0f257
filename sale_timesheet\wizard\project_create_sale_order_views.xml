<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <record id="project_create_sale_order_view_form" model="ir.ui.view">
        <field name="name">project.create.sale.order.wizard.form</field>
        <field name="model">project.create.sale.order</field>
        <field name="arch" type="xml">
            <form string="Create a Sales Order">
                <group>
                    <group>
                        <field name="project_id" readonly="1"/>
                        <field name="company_id" invisible="1"/>
                        <field name="partner_id" domain="['|', ('company_id', '=', False), ('company_id', '=', company_id)]"/>
                    </group>
                </group>
                <group>
                    <field name="line_ids" nolabel="1">
                        <tree editable="bottom">
                            <field name="employee_id" options="{'no_create_edit': True, 'no_create': True}"/>
                            <field name="product_id" options="{'no_create_edit': True, 'no_create': True}"/>
                            <field name="price_unit" widget='monetary' options="{'currency_field': 'currency_id', 'field_digits': True}"/>
                            <field name="currency_id" invisible="1"/>
                        </tree>
                    </field>
                </group>
                <group attrs="{'invisible': [('info_invoice', '=', False)]}">
                    <field name="info_invoice" nolabel="1"/>
                </group>
                <footer>
                    <button string="Create Sales Order" type="object" name="action_create_sale_order" class="oe_highlight" data-hotkey="q"/>
                    <button string="Cancel" special="cancel" data-hotkey="z" type="object" class="btn btn-secondary oe_inline"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="project_project_action_multi_create_sale_order" model="ir.actions.act_window">
        <field name="name">Create a Sales Order</field>
        <field name="res_model">project.create.sale.order</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="project_create_sale_order_view_form"/>
        <field name="target">new</field>
    </record>

</odoo>
