# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_forum
# 
# Translators:
# <PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON>essel<PERSON>ch, 2022
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_answers
msgid "# Answers"
msgstr "# Antworten"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_favorites
msgid "# Favorites"
msgstr "# Favoriten"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_posts
msgid "# Posts"
msgstr "# Beiträge"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_views
msgid "# Views"
msgstr "# Ansichten"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to accept or refuse an answer."
msgstr ""
"%d Karma-Punkte-Punkte benötigt, um eine Antwort zu akzeptieren oder "
"abzulehnen."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to answer a question."
msgstr "%d Karma-Punkte benötigt, um eine Frage zu beanworten."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to close or reopen a post."
msgstr ""
"%d Karma-Punkte benötigt, um einen Beitrag zu schließen oder wieder zu "
"öffnen."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to comment."
msgstr "%d Karma-Punkte benötigt, um kommentieren zu können."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to convert a comment to an answer."
msgstr ""
"%d Karma-Punkte benötigt, um einen Kommentar in eine Antwort umzuwandeln."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to convert an answer to a comment."
msgstr ""
"%d Karma-Punkte benötigt, um eine Antwort in einen Kommentar umzuwandeln."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to convert your comment to an answer."
msgstr ""
"%d Karma-Punkte benötigt, um Ihren Kommentar in eine Antwort zu verwandeln."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to create a new Tag."
msgstr "%d Karma-Punkte benötigt, um ein neues Stichwort zu erstellen."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to create a new question."
msgstr "%d Karma-Punkte benötigt, um eine neue Frage zu erstellen."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to delete or reactivate a post."
msgstr ""
"%d Karma-Punkte benötigt, um einen Beitrag zu löschen oder zu reaktivieren."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to downvote."
msgstr "%d Karma-Punkte benötigt, um negative Stimme abzugeben."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to edit a post."
msgstr "%d Karma-Punkte benötigt, um einen Beitrag zu posten."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to flag a post."
msgstr "%d Karma-Punkte benötigt, um einen Beitrag zu kennzeichnen."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to mark a post as offensive."
msgstr "%d Karma-Punkte benötigt, um Beitrag als anstößig zu kennzeichnen."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to post an image or link."
msgstr "%d Karma-Punkte benötigt, um ein Bild oder einen Link zu posten."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to refuse a post."
msgstr "%d Karma-Punkte benötigt, um einen Beitrag abzulehnen."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to retag."
msgstr "%d Karma-Punkte benötigt, um neues Stichwort hinzuzufügen."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to unlink a comment."
msgstr ""
"%d Karma-Punkte benötigt, um Verlinkungen für Kommentare zu entfernen."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to unlink a post."
msgstr "%d Karma-Punkte benötigt, um Verlinkungen für Beiträge zu entfernen."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to upvote."
msgstr "%d Karma-Punkte benötigt, eine positive Stimme abzugeben."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to validate a post."
msgstr "%d Karma-Punkte benötigt, um einen Beitrag zu validieren."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "&amp;nbsp;and&amp;nbsp;"
msgstr "&amp;nbsp;and&amp;nbsp;"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "(The above section was adapted from Stackoverflow’s FAQ.)"
msgstr ""
"(Der vorstehende Abschnitt wurde übernommen von den FAQ von Stackoverflow.)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "(votes - 1) **"
msgstr "(Stimmen - 1)**"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid ", by"
msgstr ", von"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ", consider <b>adding an example</b>."
msgstr ", versuchen Sie, gegebenenfalls <b>ein Beispiel hinzuzufügen</b>."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "/ (days + 2) **"
msgstr "/(Tage + 2)**"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "0 Answers"
msgstr "0 Antworten"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid "45% of questions shared"
msgstr "45 % der Fragen"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid ""
"65% more chance to get an\n"
"        answer"
msgstr ""
"65 % höhere Chance, eine Antwort\n"
"        zu erhalten"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid ""
"<b class=\"d-block\">You have a pending post</b>\n"
"                        Please wait for a moderator to validate your previous post to be allowed replying questions."
msgstr ""
"<b class=\"d-block\">Sie haben einen ausstehenden Beitrag</b>\n"
"                       Sie müssen auf die Freigabe Ihres vorherigen Beitrages durch einen Moderator warten, bevor Sie weitere Inhalte veröffentlichen können."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
msgid "<b> [Offensive]</b>"
msgstr "<b> [Anstößig]</b>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not add or expand questions</b>. Instead either edit the "
"question or add a question comment."
msgstr ""
"<b>Antworten sollten keine Fragen hinzufügen oder erweitern</b>. Ändern Sie "
"Ihre Frage stattdessen oder fügen Sie der Frage einen Kommentar hinzu."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not add or expand questions</b>. Instead, either edit the "
"question or add a comment."
msgstr ""
"<b>Antworten sollten keine Fragen hinzufügen oder erweitern</b>. Ändern Sie "
"Ihre Frage oder fügen sie einen Kommentar hinzu."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not comment other answers</b>. Instead add a comment on "
"the other answers."
msgstr ""
"<b>Antworten sollten andere Antworten nicht kommentieren</b>. Fügen Sie "
"stattdessen einen Kommentar zu anderen Antworten hinzu."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not start debates</b> This community Q&amp;A is not a "
"discussion group. Please avoid holding debates in your answers as they tend "
"to dilute the essence of questions and answers. For brief discussions please"
" use commenting facility."
msgstr ""
"<b>Antworten sollten keine Diskussion beginnen.</b> Diese F&amp;A der "
"Community ist keine Diskussionsgruppe. Bitte vermeiden Sie Diskussionen in "
"Ihren Antworten, denn sie machen Fragen und Antworten unübersichtlich. "
"Nutzen Sie die Kommentierfunktion für kurze Diskussionen."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers shouldn't just point to other Questions</b>. Instead add a "
"question comment indication \"Possible duplicate of...\". However, it's ok "
"to include links to other questions or answers providing relevant additional"
" information."
msgstr ""
"<b>Antworten sollten nicht auf andere Fragen verweisen</b>. Stattdessen "
"fügen Sie einen Kommentar hinzu mit dem Verweis „Mögliches Duplikat von "
"...“. Links zu anderen Fragen oder Antworten sind jedoch erlaubt, wenn sie "
"wichtige zusätzliche Informationen enthalten."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers shouldn't just point to other questions</b>.Instead add a comment"
" indicating <i>\"Possible duplicate of...\"</i>. However, it's fine to "
"include links to other questions or answers providing relevant additional "
"information."
msgstr ""
"<b>Antworten sollen nicht auf andere Fragen verweisen</b>. Stattdessen fügen"
" Sie einen Kommentar hinzu mit dem Verweis <i>„Mögliches Duplikat von ...“. "
"</i>Links zu anderen Fragen oder Antworten sind jedoch erlaubt, wenn sie "
"wichtige zusätzliche Informationen enthalten."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers shouldn't just provide a link a solution</b>. Instead provide the"
" solution description text in your answer, even if it's just a copy/paste. "
"Links are welcome, but should be complementary to answer, referring sources "
"or additional reading."
msgstr ""
"<b>Antworten sollten nicht nur einen Link zur Lösung enthalten.</b> Bieten "
"Sie stattdessen die Lösung als beschreibenden Text in Ihrer Antwort an, "
"selbst wenn Sie diesen nur per Kopieren und Einfügen bereitstellen. Links "
"sind willkommen, sollten jedoch nur als Ergänzung zur Antwort bereitgestellt"
" werden, in denen auf Quellen oder weitere Informationen verwiesen wird."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Before you ask - please make sure to search for a similar question.</b> "
"You can search questions by their title or tags. It’s also OK to answer your"
" own question."
msgstr ""
"<b>Haben Sie schon nach ähnlichen Fragen gesucht?</b> Sie können Fragen nach"
" Titeln oder Stichwörtern durchsuchen. Sie dürfen auch Ihre eigene Frage "
"beantworten."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Please avoid asking questions that are too subjective and "
"argumentative</b> or not relevant to this community."
msgstr ""
"<b>Bitte vermeiden Sie es, Fragen zu stellen, die zu subjektiv und "
"argumentativ sind</b> oder für diese Community nicht relevant sind."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid ""
"<b>Please try to give a substantial answer.</b> If you wanted to comment on the question or answer, just\n"
"            <b>use the commenting tool.</b> Please remember that you can always <b>revise your answers</b>\n"
"            - no need to answer the same question twice. Also, please <b>don't forget to vote</b>\n"
"            - it really helps to select the best questions and answers!"
msgstr ""
"<b>Bitte versuchen Sie, eine aussagekräftige Antwort zu geben. </b>Wenn Sie die Frage oder die Antwort kommentieren möchten, benutzen Sie einfach\n"
"            <b>das Kommentarwerkzeug</b>. Bitte denken Sie daran, dass Sie Ihre <b>Antworten jederzeit überarbeiten</b> können\n"
"            - Sie brauchen dieselbe Frage nicht zweimal zu beantworten. Vergessen Sie bitte auch nicht, <b>Ihre Stimme</b> abzugeben\n"
"            - Es hilft wirklich dabei, die besten Fragen und Antworten auszuwählen!"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "<b>Tags</b> I Follow"
msgstr "<b>Stichwörtern</b>, denen ich folge"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid ""
"<b>This forum is empty.</b><br/>\n"
"                    Be the first one asking a question"
msgstr ""
"<b>Dieses Forum ist leer.</b><br/>\n"
"                    Seien Sie der erste, der eine Frage beantwortet."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "<b>What kinds of questions can I ask here?</b>"
msgstr "<b>Welche Fragen kann ich hier stellen?</b>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "<b>What should I avoid in my answers?</b>"
msgstr "<b>Was sollte ich bei meinen Antworten vermeiden?</b>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "<b>What should I avoid in my questions?</b>"
msgstr "<b>Was sollte ich in meinen Fragen vermeiden?</b>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "<b>Why can other people edit my questions/answers?</b>"
msgstr ""
"<b>Warum können andere Personen meine Fragen/Antworten bearbeiten?</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "<b>You already have a pending post.</b><br/>"
msgstr "<b>Sie haben bereits einen ausstehenden Beitrag.</b><br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
msgid "<b>[Answer]</b>"
msgstr "<b>[Antwort]</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.contact
msgid ""
"<b>|</b>\n"
"                    <span class=\"fa fa-trophy badge-gold ml-2\" role=\"img\" aria-label=\"Gold badge\" title=\"Gold badge\"/>"
msgstr ""
"<b>|</b>\n"
"                    <span class=\"fa fa-trophy badge-gold ml-2\" role=\"img\" aria-label=\"Gold badge\" title=\"Gold-Anzeigen\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "<em class=\"d-block mb-2\">or</em>"
msgstr "<em class=\"d-block mb-2\">oder</em>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\" fa fa-comment text-muted mr-1\"/>Comment"
msgstr "<i class=\" fa fa-comment text-muted mr-1\"/>Kommentar"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-bell fa-fw\"/> Followed Posts"
msgstr "<i class=\"fa fa-bell fa-fw\"/> Verfolgte Beiträge"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-bug\"/> Filter Tool"
msgstr "<i class=\"fa fa-bug\"/> Filterwerkzeug"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\"fa fa-check fa-fw mr-1\"/>Accept"
msgstr "<i class=\"fa fa-check fa-fw mr-1\"/>Akzeptieren"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid ""
"<i class=\"fa fa-check text-success d-block display-2\"/>\n"
"            <b>You've Completely Caught Up!</b><br/>"
msgstr ""
"<i class=\"fa fa-check text-success d-block display-2\"/>\n"
"            <b>Sie sind wieder auf dem neuesten Stand!</b><br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "<i class=\"fa fa-check\"/> How to configure TPS and TVQ's canadian taxes?"
msgstr ""
"<i class=\"fa fa-check\"/> Wie konfiguriert man kanadische Steuern (TPS und "
"TVQ)?"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-check-square-o fa-fw\"/> To Validate"
msgstr "<i class=\"fa fa-check-square-o fa-fw\"/> Zu validieren"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "<i class=\"fa fa-chevron-left mr-1\"/>Back"
msgstr "<i class=\"fa fa-chevron-left mr-1\"/>Zurück"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "<i class=\"fa fa-chevron-left mr-2\"/>Back to All Topics"
msgstr "<i class=\"fa fa-chevron-left mr-2\"/> Zurück zu allen Themen"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_sub_nav
msgid "<i class=\"fa fa-chevron-left small\"/> Back"
msgstr "<i class=\"fa fa-chevron-left small\"/> Zurück"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-flag fa-fw\"/> Flagged"
msgstr "<i class=\"fa fa-flag fa-fw\"/> Gekennzeichnet"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid ""
"<i class=\"fa fa-flag ml-4 mr4\"/>\n"
"                                    Flagged"
msgstr ""
"<i class=\"fa fa-flag ml-4 mr4\"/>\n"
"                                    Markiert"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-flag\"/> Country"
msgstr "<i class=\"fa fa-flag\"/> Land"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid ""
"<i class=\"fa fa-pencil\"/>\n"
"                                Edit<span class=\"d-none d-lg-inline\"> your answer</span>"
msgstr ""
"<i class=\"fa fa-pencil\"/>\n"
"                              <span class=\"d-none d-lg-inline\">Ihre Antwort</span> bearbeiten"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-question-circle-o fa-fw\"/> My Posts"
msgstr "<i class=\"fa fa-question-circle-o fa-fw\"/> Meine Beiträge"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\"fa fa-reply mr-1\"/>Answer"
msgstr "<i class=\"fa fa-reply mr-1\"/>Antwort"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\"fa fa-share-alt text-muted mr-1\"/>Share"
msgstr "<i class=\"fa fa-share-alt text-muted mr-1\"/>Teilen"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid ""
"<i class=\"fa fa-share-alt text-muted\"/>\n"
"                                Share"
msgstr ""
"<i class=\"fa fa-share-alt text-muted\"/>\n"
"                                Teilen"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-star fa-fw\"/> Favourites"
msgstr "<i class=\"fa fa-star fa-fw\"/> Favoriten"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-tags fa-fw\"/> Followed Tags"
msgstr "<i class=\"fa fa-tags fa-fw\"/> Verfolgte Stichwörter"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid ""
"<i class=\"fa fa-thumbs-down text-danger ml-3\" role=\"img\" aria-"
"label=\"Negative votes\" title=\"Negative votes\"/>"
msgstr ""
"<i class=\"fa fa-thumbs-down text-danger ml-3\" role=\"img\" aria-"
"label=\"Negative votes\" title=\"Negative Stimmen\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid ""
"<i class=\"fa fa-thumbs-up text-success\" role=\"img\" aria-label=\"Positive"
" votes\" title=\"Positive votes\"/>"
msgstr ""
"<i class=\"fa fa-thumbs-up text-success\" role=\"img\" aria-label=\"Positive"
" votes\" title=\"Positive votes\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\"fa fa-times fa-fw mr-1\"/>Reject"
msgstr "<i class=\"fa fa-times fa-fw mr-1\"/>Ablehnen"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"<i class=\"fa fa-times\"/> Good morning to all! Please, can someone help "
"solve my tax computation problem in Canada? Thanks!"
msgstr ""
"<i class=\"fa fa-times\"/> Guten Morgen allerseits! Könnte mir bitte jemand "
"mit meinem Steuerberechnungsproblem in Kanada helfen? Danke!"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-user\"/> User"
msgstr "<i class=\"fa fa-user\"/> Benutzer"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "<small class=\"font-weight-bold\">Votes</small>"
msgstr "<small class=\"font-weight-bold\">Stimmen</small>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
msgid ""
"<small class=\"text-muted\">\n"
"                    Flagged\n"
"                </small>"
msgstr ""
"<small class=\"text-muted\">\n"
"                    Gekennzeichnet\n"
"                </small>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<span aria-label=\"Close\">×</span>"
msgstr "<span aria-label=\"Schließen\">×</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<span class=\"badge badge-info\">Closed</span>"
msgstr "<span class=\"badge badge-info\">Abgeschlossen</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.contact
msgid ""
"<span class=\"fa fa-trophy badge-bronze ml-2\" role=\"img\" aria-"
"label=\"Bronze badge\" title=\"Bronze badge\"/>"
msgstr ""
"<span class=\"fa fa-trophy badge-bronze ml-2\" role=\"img\" aria-"
"label=\"Bronze badge\" title=\"Bronze Abzeichen\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.contact
msgid ""
"<span class=\"fa fa-trophy badge-silver ml-2\" role=\"img\" aria-"
"label=\"Silver badge\" title=\"Silver badge\"/>"
msgstr ""
"<span class=\"fa fa-trophy badge-silver ml-2\" role=\"img\" aria-"
"label=\"Silver badge\" title=\"Silbernes Abzeichen\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "<span class=\"font-weight-bold\">No answer posted yet.</span>"
msgstr ""
"<span class=\"font-weight-bold\">Es wurde noch keine Antwort "
"gepostet.</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "<span class=\"font-weight-bold\">No question posted yet.</span>"
msgstr "<span class=\"font-weight-bold\">Noch keine Frage veröffentlich.</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "<span class=\"mx-1 d-none d-sm-inline\">&amp;nbsp;|</span>"
msgstr "<span class=\"mx-1 d-none d-sm-inline\">&amp;nbsp;|</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "<span class=\"mx-1 text-400 d-none d-lg-block\">|</span>"
msgstr "<span class=\"mx-1 text-400 d-none d-lg-block\">|</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "<span class=\"mx-1\">|</span>"
msgstr "<span class=\"mx-1\">|</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid ""
"<span class=\"mx-1\">|</span>\n"
"                    <i class=\"fa fa-star\"/>"
msgstr ""
"<span class=\"mx-1\">|</span>\n"
"                    <i class=\"fa fa-star\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "<span class=\"mx-3  mx-lg-2 text-400 d-none d-md-inline\">|</span>"
msgstr "<span class=\"mx-3  mx-lg-2 text-400 d-none d-md-inline\">|</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "<span class=\"navbar-text mr-1\">Go to:</span>"
msgstr "<span class=\"navbar-text mr-1\">Gehen Sie zu:</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "<span class=\"navbar-text mr-3\">Show Tags Starting By</span>"
msgstr "<span class=\"navbar-text mr-3\">Tags anzeigen beginnend mit</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "<span class=\"o_stat_text\">Favorites</span>"
msgstr "<span class=\"o_stat_text\">Favoriten</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
msgid "<span class=\"o_stat_text\">Go to <br/>Website</span>"
msgstr "<span class=\"o_stat_text\">Zur <br/>Website</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "<span class=\"o_stat_text\">Posts</span>"
msgstr "<span class=\"o_stat_text\">Beiträge</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid ""
"<span class=\"o_wforum_answer_correct_badge border small border-success rounded-pill font-weight-bold text-success ml-2 px-2\">\n"
"                            Best Answer\n"
"                        </span>"
msgstr ""
"<span class=\"o_wforum_answer_correct_badge border small border-success rounded-pill font-weight-bold text-success ml-2 px-2\">\n"
"                            Beste Antwort\n"
"                        </span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid "<span class=\"text-muted mx-3\">or</span>"
msgstr "<span class=\"text-muted mx-3\">oder</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<span>By </span>"
msgstr "<span>Von </span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "A clear, explicit and concise title"
msgstr "Ein klarer, eindeutiger und prägnanter Titel"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "A new answer on"
msgstr "Eine neue Antwort zu"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid "A new question"
msgstr "Eine neue Frage"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "About"
msgstr "Über"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_answer_accept_own
msgid "Accept an answer on own questions"
msgstr "Antwort auf eigene Fragen akzeptieren"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_answer_accept_all
msgid "Accept an answer to all questions"
msgstr "Antwort auf alle Fragen akzeptieren"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Accepted Answer"
msgstr "Akzeptierte Antwort"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_accept
msgid "Accepting an answer"
msgstr "Eine Antwort akzeptieren"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#: code:addons/website_forum/static/src/js/website_forum.js:0
#: code:addons/website_forum/static/src/js/website_forum.js:0
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Access Denied"
msgstr "Zugriff verweigert"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_needaction
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_needaction
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_needaction
msgid "Action Needed"
msgstr "Aktion notwendig"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__active
#: model:ir.model.fields,field_description:website_forum.field_forum_post__active
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__active
msgid "Active"
msgstr "Aktiv"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Activities"
msgstr "Aktivitäten"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Activity"
msgstr "Aktivität"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#, python-format
msgid "Add to menu"
msgstr "Zum Menü Hinzufügen"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__allow_share
msgid ""
"After posting the user will be proposed to share its question or answer on "
"social networks, enabling social network propagation of the forum content."
msgstr ""
"Nach der Veröffentlichung wird dem Benutzer vorgeschlagen, seine Frage oder "
"Antwort in den sozialen Netzwerken zu teilen, damit der Inhalt des Forums in"
" den sozialen Netzwerken bekannt gemacht wird."

#. module: website_forum
#: code:addons/website_forum/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#, python-format
msgid "All"
msgstr "Alle"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "All Forums"
msgstr "Alle Foren"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "All Tags"
msgstr "Alle Stichwörter"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "All Topics"
msgstr "Alle Themen"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "All forums"
msgstr "Alle Foren"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__allow_bump
msgid "Allow Bump"
msgstr "Schubs erlauben"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Amazing! There are no unanswered questions left!"
msgstr "Fantastisch! Es gibt keine unbeantworteten Fragen!"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Answer"
msgstr "Antworten"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#: model:mail.message.subtype,description:website_forum.mt_answer_edit
#: model:mail.message.subtype,name:website_forum.mt_answer_edit
#, python-format
msgid "Answer Edited"
msgstr "Antwort bearbeitet"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_accepted
msgid "Answer accepted"
msgstr "Antwort akzeptiert"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_6
msgid "Answer accepted with 15 or more votes"
msgstr "Antwort mit 15 oder mehr Stimmen akzeptiert"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_downvote
msgid "Answer downvoted"
msgstr "Antwort negativ bewertet"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_flagged
msgid "Answer flagged"
msgstr "Antwort gekennzeichnet"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_answer
msgid "Answer questions"
msgstr "Fragen beantworten"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_upvote
msgid "Answer upvoted"
msgstr "Antwort positiv bewertet"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_4
msgid "Answer voted up 15 times"
msgstr "Antwort erhielt mehr als 15 Stimmen"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_2
msgid "Answer voted up 4 times"
msgstr "Antwort erhielt mehr als 4 Stimmen"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_3
msgid "Answer voted up 6 times"
msgstr "Antwort erhielt mehr als 6 Stimmen"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_5
msgid "Answer was accepted with 3 or more votes"
msgstr "Antwort akzeptiert mit 3 oder mehr Stimmen"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__child_count_desc
msgid "Answered"
msgstr "Beantwortet"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Answered Posts"
msgstr "Beantwortete Beiträge"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
msgid "Answered by"
msgstr "Beantwortet von"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_8
msgid "Answered own question with at least 4 up votes"
msgstr "Eigene Frage beantwortet mit 4 oder mehr Stimmen"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__child_count
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Answers"
msgstr "Antworten"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_gamification_challenge__challenge_category
msgid "Appears in"
msgstr "Erscheint in"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Archived"
msgstr "Archiviert"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_ask
msgid "Ask questions"
msgstr "Fragen stellen"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_post
msgid "Ask questions without validation"
msgstr "Fragen stellen ohne Validierung"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_26
msgid "Asked a question and accepted an answer"
msgstr "Hat eine Frage gestellt und eine Antwort akzeptiert"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_1
msgid "Asked a question with at least 150 views"
msgstr "Hat eine Frage gestellt mit mindestens 150 Ansichten"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_2
msgid "Asked a question with at least 250 views"
msgstr "Hat eine Frage gestellt mit mindestens 250 Ansichten"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_3
msgid "Asked a question with at least 500 views"
msgstr "Hat eine Frage gestellt mit mindestens 500 Ansichten"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_7
msgid "Asked first question with at least one up vote"
msgstr "Hat eine erste Frage gestellt mit mindestens einer Stimme"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__create_date
msgid "Asked on"
msgstr "Gefragt am"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_question_new
msgid "Asking a question"
msgstr "Eine Frage stellen"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_attachment_count
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_attachment_count
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_attachment_count
msgid "Attachment Count"
msgstr "Anzahl Anhänge"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Author"
msgstr "Autor"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__authorized_group_id
msgid "Authorized Group"
msgstr "Autorisierte Gruppe"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_1
msgid "Autobiographer"
msgstr "Autobiograph"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.author_box
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "Avatar"
msgstr "Avatar"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Back"
msgstr "Zurück"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.header
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Back to Question"
msgstr "Zurück zur Frage"

#. module: website_forum
#: code:addons/website_forum/controllers/main.py:0
#: code:addons/website_forum/controllers/main.py:0
#, python-format
msgid "Bad Request"
msgstr "Ungültiger Aufruf"

#. module: website_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_badges
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Badges"
msgstr "Abzeichen"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post_reason__reason_type__basic
msgid "Basic"
msgstr "Basic"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Be less specific in your wording for a wider search result"
msgstr ""
"Seien Sie weniger spezifisch in Ihrer Formulierung, um ein breiteres "
"Suchergebnis zu erzielen"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Browse All"
msgstr "Alle durchsuchen"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__bump_date
msgid "Bumped on"
msgstr "Nach oben geschubst am"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid "By sharing you answer, you will get additional"
msgstr "Wenn Sie Ihre Antwort teilen, erhalten Sie zusätzliche"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_accept
msgid "Can Accept"
msgstr "Darf akzeptieren"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_answer
msgid "Can Answer"
msgstr "Darf beantworten"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_ask
msgid "Can Ask"
msgstr "Darf Fragen stellen"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_post
msgid "Can Automatically be Validated"
msgstr "Darf automatisch validiert werden"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_close
msgid "Can Close"
msgstr "Darf schließen"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_comment
msgid "Can Comment"
msgstr "Darf kommentieren"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_comment_convert
msgid "Can Convert to Comment"
msgstr "Darf in Kommentar umwandeln"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_downvote
msgid "Can Downvote"
msgstr "Darf negativ bewerten"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_edit
msgid "Can Edit"
msgstr "Darf bearbeiten"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_flag
msgid "Can Flag"
msgstr "Darf kennzeichnen"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_moderate
msgid "Can Moderate"
msgstr "Darf moderieren"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_unlink
msgid "Can Unlink"
msgstr "Darf Link entfernen"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_upvote
msgid "Can Upvote"
msgstr "Darf positiv bewerten"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_view
msgid "Can View"
msgstr "Darf sehen"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_edit_retag
msgid "Change question tags"
msgstr "Fragenstichwörter ändern"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__allow_bump
msgid ""
"Check this box to display a popup for posts older than 10 days without any "
"given answer. The popup will offer to share it on social networks. When "
"shared, a question is bumped at the top of the forum."
msgstr ""
"Markieren Sie dieses Kästchen, damit sich ein Fenster mit unbeantworteten "
"Fragen, die älter als 10 Tage sind, öffnet. Dort können Sie Fragen in den "
"sozialen Netzwerken teilen. Geteilte Fragen springen im Forum nach ganz "
"oben."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Check your spelling and try again"
msgstr "Prüfung Sie Ihre Schreibweise und versuchen Sie es erneut"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_4
#: model:gamification.challenge,name:website_forum.challenge_chief_commentator
msgid "Chief Commentator"
msgstr "Chefkommentator"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Click here to accept this answer."
msgstr "Hier klicken, um die Antwort zu akzeptieren."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid ""
"Click here to send a verification email allowing you to participate in the "
"forum."
msgstr ""
"Klicken Sie hier um eine Bestätigungs-E-Mail auszulösen und am Forum "
"teilnehmen zu können."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Click to answer."
msgstr "Zum Antworten klicken."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Click to post your answer."
msgstr "Klicken, um Ihre Antwort zu veröffentlichen."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Click to post your question."
msgstr "Klicken, um Ihre Frage zu veröffentlichen."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
#, python-format
msgid "Close"
msgstr "Schließen"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Close Post"
msgstr "Beitrag schließen"

#. module: website_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_post_reasons
msgid "Close Reasons"
msgstr "Gründe für Schließung"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_close_all
msgid "Close all posts"
msgstr "Alle Beiträge schließen"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_close_own
msgid "Close own posts"
msgstr "Eigene Beiträge schließen"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid "Close post"
msgstr "Beitrag schließen"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__close
msgid "Closed"
msgstr "Geschlossen"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__closed_uid
msgid "Closed by"
msgstr "Geschlossen durch"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__closed_date
msgid "Closed on"
msgstr "Geschlossen am"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid "Closing"
msgstr "Schließen"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__name
msgid "Closing Reason"
msgstr "Grund für Schließung"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Comment"
msgstr "Kommentar"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_all
msgid "Comment all posts"
msgstr "Alle Beiträge kommentieren"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_own
msgid "Comment own posts"
msgstr "Eigene Beiträge kommentieren"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Comment this post..."
msgstr "Diesen Beitrag kommentieren ..."

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_2
#: model:gamification.challenge,name:website_forum.challenge_commentator
#: model:gamification.challenge.line,name:website_forum.line_chief_commentator
#: model:gamification.challenge.line,name:website_forum.line_commentator
#: model:gamification.goal.definition,name:website_forum.definition_commentator
msgid "Commentator"
msgstr "Kommentator"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Comments"
msgstr "Kommentare"

#. module: website_forum
#: model:gamification.challenge,name:website_forum.challenge_configure_profile
msgid "Complete own biography"
msgstr "Vervollständigen Sie Ihre eigene Biographie"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_configure_profile
#: model:gamification.goal.definition,name:website_forum.definition_configure_profile
#: model_terms:gamification.badge,description:website_forum.badge_p_1
msgid "Completed own biography"
msgstr "Eigene Biographie vervollständigt"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_7
msgid "Contains offensive or malicious remarks"
msgstr "Enthält anstößige oder bösartige Äußerungen"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__content
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Content"
msgstr "Inhalt"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_convert_all
msgid "Convert all answers to comments and vice versa"
msgstr "Alle Antworten in Kommentare umwandeln und umgekehrt"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Convert as a answer"
msgstr "In Antwort umwandeln"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "Convert as a comment"
msgstr "In Kommentar umwandeln"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_accept
msgid "Convert comment to answer"
msgstr "Kommentar in eine Antwort umwandeln"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_convert_own
msgid "Convert own answers to comments and vice versa"
msgstr "Eigene Antworten in Kommentare umwandeln und umgekehrt"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__is_correct
msgid "Correct"
msgstr "Korrekt"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_post__is_correct
msgid "Correct answer or answer accepted"
msgstr "Korrekte oder akzeptierte Antwort"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.editor.js:0
#, python-format
msgid "Create"
msgstr "Anlegen"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__create_date
#: model:ir.model.fields,field_description:website_forum.field_res_users__create_date
msgid "Create Date"
msgstr "Erstellt am"

#. module: website_forum
#: model_terms:ir.actions.act_window,help:website_forum.action_forum_post
msgid "Create a new forum post"
msgstr "Einen neuen Forumsbeitrag erstellen"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Create a new post in this forum by clicking on the button."
msgstr ""
"Erstellen Sie einen neuen Beitrag in diesem Forum, indem Sie auf die "
"Schaltfläche klicken."

#. module: website_forum
#: model_terms:ir.actions.act_window,help:website_forum.forum_tag_action
msgid "Create a new tag"
msgstr "Neues Stichwort erstellen"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_tag_create
msgid "Create new tags"
msgstr "Neue Stichwörter erstellen"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_32
msgid "Created a tag used by 15 questions"
msgstr ""
"Hat ein Stichwort erstelllt, das für 15 oder mehr Fragen benutzt wurde"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__create_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__create_date
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_4
#: model:gamification.challenge,name:website_forum.challenge_favorite_question_1
msgid "Credible Question"
msgstr "Zu belohnende Frage"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_5
#: model:gamification.challenge,name:website_forum.challenge_critic
#: model:gamification.challenge.line,name:website_forum.line_critic
#: model:gamification.goal.definition,name:website_forum.definition_critic
msgid "Critic"
msgstr "Kritik"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
msgid "Date"
msgstr "Datum"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
msgid "Date (high to low)"
msgstr "Datum (absteigend)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
msgid "Date (low to high)"
msgstr "Datum (aufsteigend)"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__default_order
msgid "Default"
msgstr "Standard"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Default Sort"
msgstr "Standardsortierung"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_gamification_challenge__challenge_category
msgid "Define the visibility of the challenge through menus"
msgstr ""
"Legen Sie fest, in welchen Menüs die Herausforderung sichtbar sein soll"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Delete"
msgstr "Löschen"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_unlink_all
msgid "Delete all posts"
msgstr "Alle Beiträge löschen"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_unlink_own
msgid "Delete own posts"
msgstr "Eigene Beiträge löschen"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Deleted"
msgstr "Gelöscht"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_23
msgid "Deleted own post with 3 or more downvotes"
msgstr "Eigenen Beitrag mit 3 oder mehr negativen Stimmen gelöscht"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_6
msgid "Deleted own post with 3 or more upvotes"
msgstr "Eigenen Beitrag mit 3 oder mehr positiven Stimmen gelöscht"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__description
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_all_entries
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Description"
msgstr "Beschreibung"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Description visible on website"
msgstr "Beschreibung sichtbar auf Website"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.editor.js:0
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#, python-format
msgid "Discard"
msgstr "Verwerfen"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_6
#: model:gamification.challenge,name:website_forum.challenge_disciplined
#: model:gamification.challenge.line,name:website_forum.line_disciplined
#: model:gamification.goal.definition,name:website_forum.definition_disciplined
msgid "Disciplined"
msgstr "Diszipliniert"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#, python-format
msgid "Discussions"
msgstr "Diskussionen"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__mode__discussions
msgid "Discussions (multiple answers)"
msgstr "Diskussion (mehrere Antworten)"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_user_bio
msgid "Display detailed user biography"
msgstr "Detaillierte Benutzer-Biographie anzeigen"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_downvote
msgid "Downvote"
msgstr "Negativ bewerten"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_1
msgid "Duplicate post"
msgstr "Beitrag duplizieren"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Edit"
msgstr "Bearbeiten"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Edit Answer"
msgstr "Antwort bearbeiten"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Edit Forum in Backend"
msgstr "Forum im Backend bearbeiten"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Edit Question"
msgstr "Frage bearbeiten"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_edit_all
msgid "Edit all posts"
msgstr "Alle Beiträge bearbeiten"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_edit_own
msgid "Edit own posts"
msgstr "Eigene Beiträge bearbeiten"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Edit your Post"
msgstr "Eigenen Beitrag bearbeiten"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_7
#: model:gamification.challenge,name:website_forum.challenge_editor
#: model:gamification.challenge.line,name:website_forum.line_editor
#: model:gamification.goal.definition,name:website_forum.definition_editor
msgid "Editor"
msgstr "Editor"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_editor
msgid "Editor Features: image and links"
msgstr "Editor-Funktionen: Bild und Links"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_5
#: model:gamification.challenge,name:website_forum.challenge_enlightened
#: model:gamification.challenge.line,name:website_forum.line_enlightened
#: model:gamification.goal.definition,name:website_forum.definition_enlightened
msgid "Enlightened"
msgstr "Hilfreich"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"Example\n"
"                        <i class=\"fa fa-question-circle\"/>"
msgstr ""
"Beispiel\n"
"                        <i class=\"fa fa-question-circle\"/>"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_3
#: model:gamification.challenge,name:website_forum.challenge_famous_question
msgid "Famous Question"
msgstr "Erstklassige Frage"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__favourite_count
msgid "Favorite"
msgstr "Favorit"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_5
#: model:gamification.challenge,name:website_forum.challenge_favorite_question_5
msgid "Favorite Question"
msgstr "Favorisierte Antwort"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__favourite_ids
msgid "Favourite"
msgstr "Favorit"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_favorite_question_1
#: model:gamification.goal.definition,name:website_forum.definition_favorite_question_1
msgid "Favourite Question (1)"
msgstr "Favorisierte Frage (1)"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_stellar_question_25
#: model:gamification.goal.definition,name:website_forum.definition_stellar_question_25
msgid "Favourite Question (25)"
msgstr "Favorisierte Frage (25)"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_favorite_question_5
#: model:gamification.goal.definition,name:website_forum.definition_favorite_question_5
msgid "Favourite Question (5)"
msgstr "Favorisierte Frage (5)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Favourite Questions"
msgstr "Favorisierte Fragen"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Favourites"
msgstr "Favoriten"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Filter by:"
msgstr "Filtern nach:"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__relevancy_post_vote
msgid "First Relevance Parameter"
msgstr "Erster Parameter für die Relevanz"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_5
msgid "First downvote"
msgstr "Erste negative Stimme"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_7
msgid "First edit"
msgstr "Erste Bearbeitung"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_31
msgid "First upvote"
msgstr "Erste positive Stimme"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Flag"
msgstr "Kennzeichnen"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_flag
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_flag
msgid "Flag a post as offensive"
msgstr "Beitrag als anstößig kennzeichnen"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__flagged
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Flagged"
msgstr "Gekennzeichnet"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__flag_user_id
msgid "Flagged by"
msgstr "Gekennzeichnet von"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Followed Questions"
msgstr "Verfolgte Fragen"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_follower_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_follower_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_follower_ids
msgid "Followers"
msgstr "Follower"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_partner_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_partner_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_partner_ids
msgid "Followers (Partners)"
msgstr "Follower (Partner)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Following"
msgstr "Folgt"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"For example, if you ask an interesting question or give a helpful answer, "
"your input will be upvoted. On the other hand if the answer is misleading - "
"it will be downvoted. Each vote in favor will generate 10 points, each vote "
"against will subtract 10 points. There is a limit of 200 points that can be "
"accumulated for a question or answer per day. The table given at the end "
"explains reputation point requirements for each type of moderation task."
msgstr ""
"Stellen Sie beispielsweise eine interessante Frage oder geben Sie eine "
"hilfreiche Antwort, so erhalten Sie eine positive Stimme. Geben Sie eine "
"irreführende Antwort, so erhalten Sie eine negative Stimme. Jede positive "
"Stimme ergibt 10 Punkte, jede negative Stimme -10 Punkte. Eine Frage oder "
"Antwort kann insgesamt maximal 200 Punkte pro Tag erhalten. Die nachfolgende"
" Tabelle stellt die verschiedenen Moderationsaufgaben und die dafür "
"notwendigen Punkte dar."

#. module: website_forum
#: code:addons/website_forum/models/website.py:0
#: code:addons/website_forum/models/website.py:0
#: model:ir.actions.act_url,name:website_forum.action_open_forum
#: model:ir.model,name:website_forum.model_forum_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__forum_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__forum_id
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__forum_id
#: model:ir.ui.menu,name:website_forum.menu_website_forum
#: model:ir.ui.menu,name:website_forum.menu_website_forum_global
#: model:website.menu,name:website_forum.menu_website_forums
#: model_terms:ir.ui.view,arch_db:website_forum.forum_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
#, python-format
msgid "Forum"
msgstr "Forum"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#, python-format
msgid "Forum Mode"
msgstr "Modus des Forums"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__name
#, python-format
msgid "Forum Name"
msgstr "Name des Forums"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
msgid "Forum Post"
msgstr "Forumsbeitrag"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.action_forum_post
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_list
msgid "Forum Posts"
msgstr "Forumsbeiträge"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_tag
msgid "Forum Tag"
msgstr "Forumsstichwörter"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.action_forum_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_global
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_list
msgid "Forums"
msgstr "Foren"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_website__forums_count
msgid "Forums Count"
msgstr "Anzahl Foren"

#. module: website_forum
#: model:ir.model,name:website_forum.model_gamification_challenge
msgid "Gamification Challenge"
msgstr "Gamification-Herausforderung"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Give your post title."
msgstr "Geben Sie den Titel Ihres Beiträge an."

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_3
#: model:gamification.challenge,name:website_forum.challenge_good_answer
msgid "Good Answer"
msgstr "Gute Antwort"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_good_answer
#: model:gamification.goal.definition,name:website_forum.definition_good_answer
msgid "Good Answer (6)"
msgstr "Gute Antwort (6)"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_9
#: model:gamification.challenge,name:website_forum.challenge_good_question
msgid "Good Question"
msgstr "Gute Frage"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_graph
msgid "Graph of Posts"
msgstr "Beitragsgrafik"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_4
#: model:gamification.challenge,name:website_forum.challenge_great_answer
msgid "Great Answer"
msgstr "Sehr gute Antwort"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_great_answer
#: model:gamification.goal.definition,name:website_forum.definition_great_answer
msgid "Great Answer (15)"
msgstr "Sehr gute Antwort (15)"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_10
#: model:gamification.challenge,name:website_forum.challenge_great_question
msgid "Great Question"
msgstr "Sehr gute Frage"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Group By"
msgstr "Gruppieren nach"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__faq
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Guidelines"
msgstr "Leitfaden"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_6
#: model:gamification.challenge,name:website_forum.challenge_guru
msgid "Guru"
msgstr "Guru"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_guru
#: model:gamification.goal.definition,name:website_forum.definition_guru
msgid "Guru (15)"
msgstr "Guru (15)"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__uid_has_answered
msgid "Has Answered"
msgstr "Hat beantwortet"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__has_message
#: model:ir.model.fields,field_description:website_forum.field_forum_post__has_message
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__has_message
msgid "Has Message"
msgstr "Hat eine Nachricht"

#. module: website_forum
#: model:forum.forum,name:website_forum.forum_help
msgid "Help"
msgstr "Hilfe"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "Here a table with the privileges and the karma level"
msgstr "Hier ist eine Tabelle mit den Privilegien und dem Karma-Level"

#. module: website_forum
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid "Hide Intro"
msgstr "Intro ausblenden"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "I'm <b>Following</b>"
msgstr "Ich <b>Folge</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "I'm Following"
msgstr "Ich folge"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__id
#: model:ir.model.fields,field_description:website_forum.field_forum_post__id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__id
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__id
msgid "ID"
msgstr "ID"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_needaction
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_unread
#: model:ir.model.fields,help:website_forum.field_forum_post__message_needaction
#: model:ir.model.fields,help:website_forum.field_forum_post__message_unread
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_needaction
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_unread
msgid "If checked, new messages require your attention."
msgstr "Falls markiert, erfordern neue Nachrichten Ihre Aufmerksamkeit."

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_has_error
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_has_sms_error
#: model:ir.model.fields,help:website_forum.field_forum_post__message_has_error
#: model:ir.model.fields,help:website_forum.field_forum_post__message_has_sms_error
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_has_error
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""
"Falls markiert, weisen einige Nachrichten einen Zustellungsfehler auf."

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__karma_dofollow
msgid ""
"If the author has not enough karma, a nofollow attribute is added to links"
msgstr ""
"Hat der Verfasser zu wenig Karma, wird das „nofollow“-Attribut zu Links "
"hinzugefügt."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "If this approach is not for you, please respect the community."
msgstr ""
"Bitte respektieren Sie die Community, auch wenn Ihnen dieser Ansatz nicht "
"zusagt."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid ""
"If you close this post, it will be hidden for most users. Only\n"
"            users having a high karma can see closed posts to moderate\n"
"            them."
msgstr ""
"Wenn Sie diesen Beitrag schließen, ist er für die meisten Benutzer verborgen. Nur\n"
"            Benutzer mit ausreichenden Karma-Punkten können geschlossene Beiträge sehen\n"
"            und moderieren."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"If you fit in one of these example or if your motivation for asking the "
"question is “I would like to participate in a discussion about ______”, then"
" you should not be asking here but on our mailing lists. However, if your "
"motivation is “I would like others to explain ______ to me”, then you are "
"probably OK."
msgstr ""
"Wenn auf Sie eines dieser Beispiele zutrifft oder wenn Ihre Motivation für "
"die Frage lautet „Ich möchte mich an einer Diskussion über ______ "
"beteiligen“, dann sollten Sie nicht hier, sondern über unsere Mailinglisten "
"fragen. Wenn Ihre Motivation jedoch lautet: „Ich möchte, dass andere mir "
"______ erklären“, dann ist das wahrscheinlich in Ordnung."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid ""
"If you mark this post as offensive, it will be hidden for most users. Only\n"
"            users having a high karma can see offensive posts to moderate\n"
"            them."
msgstr ""
"Wenn Sie einen Beitrag als anstößig markieren, dann ist er für die meisten Benutzer nicht mehr sichtbar.\n"
"            Nur Benutzer mit ausreichend Karma können anstößige\n"
"Beiträge sehen oder\n"
"            moderieren."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_1920
msgid "Image"
msgstr "Bild"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_1024
msgid "Image 1024"
msgstr "Bild 1024"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_128
msgid "Image 128"
msgstr "Bild 128"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_256
msgid "Image 256"
msgstr "Bild 256"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_512
msgid "Image 512"
msgstr "Bild 512"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_13
msgid "Inappropriate and unacceptable statements"
msgstr "Unangebrachte oder inakzeptable Äußerungen"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Insert tags related to your question."
msgstr "Fügen Sie Stichwörter passend zur Frage ein."

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_11
msgid "Insulting and offensive language"
msgstr "Beleidigende und anstößige Sprache"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__user_favourite
msgid "Is Favourite"
msgstr "Ist Favorit"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_is_follower
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_is_follower
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_is_follower
msgid "Is Follower"
msgstr "Ist Follower"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__has_validated_answer
msgid "Is answered"
msgstr "Ist beantwortet"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_display_biography
msgid "Is the author's biography visible from his post"
msgstr "Ist die Biografie des Verfassers aus seinem Beitrag ersichtlich"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "It is not allowed to modify someone else's vote."
msgstr "Es ist nicht erlaubt, die Bewertung einer anderen Person zu ändern."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "It is not allowed to vote for its own post."
msgstr "Es ist nicht erlaubt, für seinen eigenen Beitrag zu stimmen."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Karma"
msgstr "Karma"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Karma Error"
msgstr "Karma-Fehler"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Karma Gains"
msgstr "Karma-Gewinne"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Karma Related Rights"
msgstr "Karmabezogene Rechte"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_close
msgid "Karma to close"
msgstr "Karma für die Schließung"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_comment
msgid "Karma to comment"
msgstr "Karma für einen Kommentar"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_comment_convert
msgid "Karma to convert comment to answer"
msgstr "Karma für die Umwandlung eines Kommentars in eine Antwort"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_edit
msgid "Karma to edit"
msgstr "Karma für die Bearbeitung"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_unlink
msgid "Karma to unlink"
msgstr "Karma zur Aufhebung der Verlinkung"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum____last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_post____last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason____last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote____last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_tag____last_update
msgid "Last Modified on"
msgstr "Zuletzt geändert am"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__last_post_id
msgid "Last Post"
msgstr "Letzter Beitrag"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_options
msgid "Last Post:"
msgstr "Letzter Beitrag:"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__write_date_desc
msgid "Last Updated"
msgstr "Zuletzt aktualisiert"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Last activity date"
msgstr "Datum der letzten Aktivität"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_25
msgid "Left 10 answers with score of 10 or more"
msgstr "Hat 10 Antworten mit 10 oder mehr Stimmen gegeben"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_main_attachment_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_main_attachment_id
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_main_attachment_id
msgid "Main Attachment"
msgstr "Hauptanhang"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "Mark as Best Answer"
msgstr "Als beste Antwort markieren"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Mark as offensive"
msgstr "Als anstößig markieren"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Mark as spam"
msgstr "Als Spam markieren"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__menu_id
msgid "Menu"
msgstr "Menü"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_has_error
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_has_error
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_has_error
msgid "Message Delivery error"
msgstr "Nachricht mit Zustellungsfehler"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_ids
msgid "Messages"
msgstr "Nachrichten"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__mode
msgid "Mode"
msgstr "Modus"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_moderate
msgid "Moderate posts"
msgstr "Beiträge moderieren"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Moderation"
msgstr "Moderation"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "Moderation tools"
msgstr "Moderationstools"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "More"
msgstr "Mehr"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "More over:"
msgstr "Darüber hinaus:"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__vote_count_desc
msgid "Most Voted"
msgstr "Am häufigsten zugestimmt"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Most answered"
msgstr "Am häufigsten beantwortet"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Most voted"
msgstr "Am häufigsten bewertet"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid ""
"Move this question to the top of the list by sharing it on social networks."
msgstr ""
"Verschieben Sie diese Frage an den Anfang der Liste, indem Sie sie über "
"soziale Netzwerke teilen."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "My <b>Favourites</b>"
msgstr "Meine <b>Favoriten</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "My <b>Posts</b>"
msgstr "Meine <b>Beiträge</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "My Favourites"
msgstr "Meine Favoriten"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "My Posts"
msgstr "Meine Beiträge"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__user_vote
msgid "My Vote"
msgstr "Meine Bewertung"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "My profile"
msgstr "Mein Profil"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__name
msgid "Name"
msgstr "Name"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
#: model_terms:ir.ui.view,arch_db:website_forum.vote
msgid "Negative vote"
msgstr "Negative Stimme"

#. module: website_forum
#: model:mail.message.subtype,description:website_forum.mt_answer_new
#: model:mail.message.subtype,name:website_forum.mt_answer_new
#: model:mail.message.subtype,name:website_forum.mt_forum_answer_new
msgid "New Answer"
msgstr "Neue Antwort"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.editor.js:0
#, python-format
msgid "New Forum"
msgstr "Neues Forum"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "New Post"
msgstr "Neuer Beitrag"

#. module: website_forum
#: model:mail.message.subtype,description:website_forum.mt_question_new
#: model:mail.message.subtype,name:website_forum.mt_forum_question_new
#: model:mail.message.subtype,name:website_forum.mt_question_new
msgid "New Question"
msgstr "Neue Frage"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__create_date_desc
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Newest"
msgstr "Neueste"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_2
#: model:gamification.challenge,name:website_forum.challenge_nice_answer
msgid "Nice Answer"
msgstr "Schöne Antwort"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_nice_answer
#: model:gamification.goal.definition,name:website_forum.definition_nice_answer
msgid "Nice Answer (4)"
msgstr "Schöne Antwort (4)"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_8
#: model:gamification.challenge,name:website_forum.challenge_nice_question
msgid "Nice Question"
msgstr "Schöne Frage"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_activities
msgid "No activities yet!"
msgstr "Noch keine Aktivitäten!"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "No favourite questions in this forum (yet).<br/>"
msgstr "Noch keine Lieblingsfragen in diesem Forum.<br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "No flagged posts"
msgstr "Keine gekennzeichneten Beiträge"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all
msgid "No forum is available yet."
msgstr "Es ist noch kein Forum verfügbar."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "No post to be validated"
msgstr "Kein zu validierender Beitrag"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "No tags"
msgstr "Keine Stichwörter"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
msgid "No vote given by you yet!"
msgstr "Sie haben noch keine Stimme abgegeben!"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_dofollow
msgid "Nofollow links"
msgstr "„Nofollow“-Links"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_4
msgid "Not a real post"
msgstr "Kein echter Beitrag"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_6
msgid "Not relevant or out dated"
msgstr "Nicht relevant oder veraltet"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_2
#: model:gamification.challenge,name:website_forum.challenge_notable_question
msgid "Notable Question"
msgstr "Bemerkenswerte Antwort"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_needaction_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_needaction_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_needaction_counter
msgid "Number of Actions"
msgstr "Anzahl der Aktionen"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__posts_count
msgid "Number of Posts"
msgstr "Anzahl der Beiträge"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_has_error_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_has_error_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_has_error_counter
msgid "Number of errors"
msgstr "Anzahl der Fehler"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__count_flagged_posts
msgid "Number of flagged posts"
msgstr "Anzahl gekennzeichneter Beiträge"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_needaction_counter
#: model:ir.model.fields,help:website_forum.field_forum_post__message_needaction_counter
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Anzahl der Nachrichten, die eine Aktion erfordern"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_has_error_counter
#: model:ir.model.fields,help:website_forum.field_forum_post__message_has_error_counter
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Anzahl der Nachrichten mit Zustellungsfehler."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__count_posts_waiting_validation
msgid "Number of posts waiting for validation"
msgstr "Anzahl der Beiträge, die auf Validierung warten"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_unread_counter
#: model:ir.model.fields,help:website_forum.field_forum_post__message_unread_counter
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_unread_counter
msgid "Number of unread messages"
msgstr "Anzahl ungelesener Nachrichten"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_2
msgid "Off-topic or not relevant"
msgstr "Nicht themenrelevant oder irrelevant"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__offensive
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post_reason__reason_type__offensive
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
#: model_terms:ir.ui.view,arch_db:website_forum.header
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Offensive"
msgstr "Anstößig"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Offensive Post"
msgstr "Anstößiger Beitrag"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid "On average,"
msgstr "Im Durchschnitt werden"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "Only one answer per question is allowed"
msgstr "Pro Frage ist nur eine Antwort erlaubt"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Options"
msgstr "Optionen"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Order and Visibility"
msgstr "Reihenfolge und Sichtbarkeit"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Order by"
msgstr "Anordnen nach"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_oe_structure_forum_all_top
msgid "Our forums"
msgstr "Unsere Foren"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_23
#: model:gamification.challenge,name:website_forum.challenge_peer_pressure
#: model:gamification.challenge.line,name:website_forum.line_peer_pressure
#: model:gamification.goal.definition,name:website_forum.definition_peer_pressure
msgid "Peer Pressure"
msgstr "Gruppenzwang"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "People"
msgstr "Personen"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__plain_content
msgid "Plain Content"
msgstr "Unformatierter Inhalt"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#, python-format
msgid "Please fill in this field"
msgstr "Bitte dieses Feld ausfüllen"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"Please wait for a moderator to validate your previous post before "
"continuing."
msgstr ""
"Bitte warten Sie, bis ein Moderator Ihren vorherigen Beitrag validiert hat, "
"bevor Sie fortfahren."

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_1
#: model:gamification.challenge,name:website_forum.challenge_popular_question
msgid "Popular Question"
msgstr "Beliebte Frage"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_popular_question
#: model:gamification.goal.definition,name:website_forum.definition_popular_question
msgid "Popular Question (150)"
msgstr "Beliebte Frage (150)"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_notable_question
#: model:gamification.goal.definition,name:website_forum.definition_notable_question
msgid "Popular Question (250)"
msgstr "Beliebte Frage (200)"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_famous_question
#: model:gamification.goal.definition,name:website_forum.definition_famous_question
msgid "Popular Question (500)"
msgstr "Beliebte Frage (500)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
#: model_terms:ir.ui.view,arch_db:website_forum.vote
msgid "Positive vote"
msgstr "Positive Stimme"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__post_id
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Post"
msgstr "Beitrag"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_reply
msgid "Post Answer"
msgstr "Antwort veröffentlichen"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__child_ids
msgid "Post Answers"
msgstr "Antworten veröffentlichen"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_post_reasons_action
msgid "Post Close Reasons"
msgstr "Gründe für Beendigung von Beiträgen"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post_reason
msgid "Post Closing Reason"
msgstr "Grund für Beendigung des Beitrags"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Post Comment"
msgstr "Kommentar veröffentlichen"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post_vote
msgid "Post Vote"
msgstr "Beitrag bewerten"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Post Your Question"
msgstr "Ihre Frage veröffentlichen"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid "Post:"
msgstr "Beitrag:"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_p_2
msgid "Posted 10 comments"
msgstr "Hat 10 Kommentare veröffentlicht"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_p_4
msgid "Posted 100 comments"
msgstr "Hat 100 Kommentare veröffentlicht"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "Posting answer on a [Deleted] or [Closed] question is not possible."
msgstr ""
"Das Posten einer Antwort auf eine [Gelöschte] oder [Geschlossene] Frage ist "
"nicht möglich."

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.action_forum_posts
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__post_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__post_ids
#: model:ir.ui.menu,name:website_forum.menu_forum_posts
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Posts"
msgstr "Beiträge"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_options
msgid "Posts:"
msgstr "Beiträge:"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__privacy
#, python-format
msgid "Privacy"
msgstr "Privatsphäre"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__privacy__public
#, python-format
msgid "Public"
msgstr "Öffentlich"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__privacy
msgid ""
"Public: Forum is public\n"
"Signed In: Forum is visible for signed in users\n"
"Some users: Forum and their content are hidden for non members of selected group"
msgstr ""
"Öffentlich: Das Forum ist öffentlich\n"
"Angemeldet: Das Forum ist für angemeldete Benutzer sichtbar\n"
"Einige Benutzer: Das Forum und seine Inhalte sind nur für Mitglieder der ausgewählten Gruppe sichtbar"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#, python-format
msgid ""
"Public: Forum is public\\nSigned In: Forum is visible for signed in "
"users\\nSome users: Forum and their content are hidden for non members of "
"selected group"
msgstr ""
"Öffentlich: Das Forum ist öffentlich \\nAngemeldet: Das Forum ist für "
"angemeldete Benutzer sichtbar\\nAusgewählte Benutzer: Das Forum und sein "
"Inhalt sind für Nichtmitglieder der ausgewählten Gruppe verborgen."

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_25
#: model:gamification.challenge,name:website_forum.challenge_pundit
#: model:gamification.challenge.line,name:website_forum.line_pundit
#: model:gamification.goal.definition,name:website_forum.definition_pundit
msgid "Pundit"
msgstr "Experte"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Put your answer here."
msgstr "Platzieren Sie Ihre Antwort hier."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Put your question here."
msgstr "Platzieren Sie Ihre Frage hier."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__parent_id
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Question"
msgstr "Frage"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#: model:mail.message.subtype,description:website_forum.mt_question_edit
#: model:mail.message.subtype,name:website_forum.mt_question_edit
#, python-format
msgid "Question Edited"
msgstr "Bearbeitete Frage"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Question by"
msgstr "Frage von"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_question_downvote
msgid "Question downvoted"
msgstr "Frage negativ bewertet"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid "Question not found!"
msgstr "Frage nicht gefunden!"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_4
msgid "Question set as favorite by 1 user"
msgstr "Frage wurde von 1 Benutzer als Favorit markiert"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_6
msgid "Question set as favorite by 25 users"
msgstr "Frage wurde von 25 Benutzern als Favorit markiert"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_5
msgid "Question set as favorite by 5 users"
msgstr "Frage wurde von 5 Benutzern als Favorit markiert"

#. module: website_forum
#: code:addons/website_forum/controllers/main.py:0
#, python-format
msgid "Question should not be empty."
msgstr "Frage sollte nicht leer sein."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_question_upvote
msgid "Question upvoted"
msgstr "Frage positiv bewertet"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_10
msgid "Question voted up 15 times"
msgstr "Frage erhielt 15 Stimmen"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_8
msgid "Question voted up 4 times"
msgstr "Frage erhielt 4 Stimmen"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_9
msgid "Question voted up 6 times"
msgstr "Frage erhielt 6 Stimmen"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Questions"
msgstr "Fragen"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__mode__questions
msgid "Questions (1 answer)"
msgstr "Fragen (1 Antwort)"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#, python-format
msgid "Questions and Answers"
msgstr "Fragen und Antworten"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#, python-format
msgid ""
"Questions and Answers mode: only one answer allowed\\n Discussions mode: "
"multiple answers allowed"
msgstr ""
"Modus \"Fragen und Antworten\": nur eine Antwort zulässig\\Modus "
"\"Diskussionen\": mehrere Antworten zulässig"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__mode
msgid ""
"Questions mode: only one answer allowed\n"
" Discussions mode: multiple answers allowed"
msgstr ""
"Fragen-Modus: nur eine Antwort erlaubt\n"
"Diskussionsmodus: mehrere Antworten erlaubt"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_15
msgid "Racist and hate speech"
msgstr "Rassismus und Hassrede"

#. module: website_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_rank_global
msgid "Ranks"
msgstr "Ränge"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "Re: %s"
msgstr "Re: %s"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Read the guidelines to know how to gain karma."
msgstr ""
"Lesen Sie den Leitfaden, um zu erfahren, wie sie Karma-Punkte gewinnen."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "Read: #{question.name}"
msgstr "Lesen: #{question.name}"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__closed_reason_id
msgid "Reason"
msgstr "Grund"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__reason_type
msgid "Reason Type"
msgstr "Art des Grundes"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid "Reason:"
msgstr "Grund:"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_reason_view_list
msgid "Reasons"
msgstr "Gründe"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_1
msgid "Received at least 3 upvote for an answer for the first time"
msgstr "Erstmalig mindestens 3 positive Stimmen für eine Antwort erhalten"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Refuse"
msgstr "Ablehnen"

#. module: website_forum
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid "Register"
msgstr "Anmelden"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__relevancy
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__relevancy_desc
msgid "Relevance"
msgstr "Relevanz"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Relevance Computation"
msgstr "Berechnung der Relevanz"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Reopen"
msgstr "Erneut öffnen"

#. module: website_forum
#: code:addons/website_forum/controllers/main.py:0
#, python-format
msgid "Reply should not be empty."
msgstr "Antwort sollte nicht leer sein."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__self_reply
msgid "Reply to own question"
msgstr "Auf die eigene Frage antworten"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__website_id
#: model:ir.model.fields,help:website_forum.field_forum_post__website_id
msgid "Restrict publishing to this website."
msgstr "Beschränken Sie die Veröffentlichung auf dieser Website."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.private_profile
msgid "Return to the forum."
msgstr "Zurück zum Forum."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid "Return to the question list."
msgstr "Zurück zur Fragenliste."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__moderator_id
msgid "Reviewed by"
msgstr "Überprüft durch"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__is_seo_optimized
#: model:ir.model.fields,field_description:website_forum.field_forum_post__is_seo_optimized
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO-optimiert"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_has_sms_error
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_has_sms_error
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS-Zustellungsfehler"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Save Changes"
msgstr "Änderungen speichern"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_26
#: model:gamification.challenge,name:website_forum.challenge_scholar
#: model:gamification.challenge.line,name:website_forum.line_scholar
#: model:gamification.goal.definition,name:website_forum.definition_scholar
msgid "Scholar"
msgstr "Azubi"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Search Tips"
msgstr "Tipps für die Suche"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Search in Post"
msgstr "Suche in Beitrag"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Search..."
msgstr "Suchen ..."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__relevancy_time_decay
msgid "Second Relevance Parameter"
msgstr "Zweiter Parameter für die Relevanz"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "See"
msgstr "Ansehen"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "See post"
msgstr "Beitrag ansehen"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
msgid "See question"
msgstr "Frage ansehen"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Select All"
msgstr "Alles auswählen"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#, python-format
msgid "Select Authorized Group"
msgstr "Authorisierte Gruppe auswählen"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_8
#: model:gamification.challenge,name:website_forum.challenge_self_learner
#: model:gamification.challenge.line,name:website_forum.line_self_learner
#: model:gamification.goal.definition,name:website_forum.definition_self_learner
msgid "Self-Learner"
msgstr "Selbstlerner"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__seo_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post__seo_name
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__seo_name
msgid "Seo name"
msgstr "Seo-Name"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__sequence
msgid "Sequence"
msgstr "Sequenz"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid ""
"Share this content to increase your chances to be featured on the front page"
" and attract more visitors."
msgstr ""
"Teilen Sie diesen Inhalt und erhalten Sie eine größere Chance, auf der "
"Homepage zu erscheinen und mehr Besucher anzuziehen."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__allow_share
msgid "Sharing Options"
msgstr "Optionen zur Freigabe"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Show"
msgstr "Zeigen"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "Show Tags Starting By"
msgstr "Stichwörter anzeigen beginnend mit"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Showing results for"
msgstr "Ergebnisse werden angezeigt für"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Sign in"
msgstr "Anmelden"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__privacy__connected
#, python-format
msgid "Signed In"
msgstr "Angemeldet"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Solved"
msgstr "Gelöst"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#, python-format
msgid "Some Users"
msgstr "Einige Benutzer"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__privacy__private
msgid "Some users"
msgstr "Einige Benutzer"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Sorry you must be logged in to perform this action"
msgstr "Tut uns leid, Sie müssen angemeldet sein, um diese Aktion auszuführen"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Sorry you must be logged to flag a post"
msgstr ""
"Tut uns leid, Sie müssen angemeldet sein, um einen Beitrag zu kennzeichnen"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Sorry you must be logged to vote"
msgstr "Tut uns leid, Sie müssen angemeldet sein, um abzustimmen"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Sorry, anonymous users cannot choose correct answer."
msgstr ""
"Tut uns leid, Sie müssen angemeldet sein, um eine Antwort als korrekt zu "
"markieren."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid "Sorry, this question is not available anymore."
msgstr "Tut uns leid, diese Frage ist nicht mehr verfügbar."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Sorry, we could not find any <b>%s</b> result <b>%s</b> %s%s%s."
msgstr ""
"Es tut uns leid, wir konnten kein <b>%s</b> Ergebnis finden <b>%s</b>%s%s%s."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Sorry, you cannot vote for your own posts"
msgstr ""
"Es tut uns leid, Sie dürfen nicht über Ihren eigenen Beiträge abstimmen"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Spam all post"
msgstr "Alle Beiträge spammen"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_8
msgid "Spam or advertising"
msgstr "Spam oder Werbung"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__state
msgid "Status"
msgstr "Status"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_6
#: model:gamification.challenge,name:website_forum.challenge_stellar_question_25
msgid "Stellar Question"
msgstr "Exzellente Frage"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_7
#: model:gamification.challenge,name:website_forum.challenge_student
msgid "Student"
msgstr "Student"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_31
#: model:gamification.challenge,name:website_forum.challenge_supporter
#: model:gamification.challenge.line,name:website_forum.line_supporter
#: model:gamification.goal.definition,name:website_forum.definition_supporter
msgid "Supporter"
msgstr "Unterstützer"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_tag_view_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Tag"
msgstr "Stichwort"

#. module: website_forum
#: model:ir.model.constraint,message:website_forum.constraint_forum_tag_name_uniq
msgid "Tag name already exists !"
msgstr "Stichwortbezeichnung existiert bereits!"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_tag_action
#: model:ir.model.fields,field_description:website_forum.field_forum_post__tag_ids
#: model:ir.ui.menu,name:website_forum.menu_forum_tag_global
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
#: model_terms:ir.ui.view,arch_db:website_forum.forum_tag_view_list
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Tags"
msgstr "Stichwörter"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Tags I Follow"
msgstr "Stichwörter I Folgen"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_32
#: model:gamification.challenge,name:website_forum.challenge_taxonomist
#: model:gamification.challenge.line,name:website_forum.line_taxonomist
#: model:gamification.goal.definition,name:website_forum.definition_taxonomist
msgid "Taxonomist"
msgstr "Profi"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_1
#: model:gamification.challenge,name:website_forum.challenge_teacher
#: model:gamification.challenge.line,name:website_forum.line_teacher
#: model:gamification.goal.definition,name:website_forum.definition_teacher
msgid "Teacher"
msgstr "Lehrer"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__teaser
msgid "Teaser"
msgstr "Teaser"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_post__bump_date
msgid ""
"Technical field allowing to bump a question. Writing on this field will "
"trigger a write on write_date and therefore bump the post. Directly writing "
"on write_date is currently not supported and this field is a workaround."
msgstr ""
"Technisches Feld, um eine Frage anzuschubsen. Schreiben auf dieses Feld löst"
" ein „write“ auf „write_date“ aus und schiebt damit den Beitrag nach oben. "
"Direktes Schreiben auf „write_date“ wird zurzeit nicht unterstützt, daher "
"ist dieses Feld eine Hilfslösung."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid "Thanks for posting!"
msgstr "Danke für Ihren Beitrag!"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid ""
"The goal of this site is create a relevant knowledge base that would answer "
"questions related to Odoo."
msgstr ""
"Das Ziel dieser Seite ist es, eine Wissensdatenbank zu schaffen, die Fragen "
"zu Odoo beantwortet."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "The question has been closed"
msgstr "Die Frage wurde geschlossen"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid ""
"Therefore questions and answers can be edited like wiki pages by experienced"
" users of this site in order to improve the overall quality of the knowledge"
" base content. Such privileges are granted based on user karma level: you "
"will be able to do the same once your karma gets high enough."
msgstr ""
"Daher können Fragen und Antworten wie Wiki-Seiten von erfahrenen Benutzern "
"dieser Website bearbeitet werden, um die Gesamtqualität des Inhalts der "
"Wissensdatenbank zu verbessern. Solche Privilegien werden auf der Grundlage "
"des Karma-Levels des Benutzers gewährt: Sie werden dasselbe tun können, "
"sobald Ihr Karma hoch genug ist."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_oe_structure_forum_all_top
msgid ""
"This community is for professional and enthusiast users, partners and "
"programmers."
msgstr ""
"Diese Gemeinschaft ist für professionelle und enthusiastische Benutzer, "
"Partner und Programmierer gedacht."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"This community is for professional and enthusiast users, partners and "
"programmers. You can ask questions about:"
msgstr ""
"Diese Community ist für Profis und begeisterte Benutzer, Partner und "
"Programmierer. Stellen Sie Fragen dazu:"

#. module: website_forum
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid ""
"This community is for professionals and enthusiasts of our products and services.\n"
"                                        <br>Share and discuss the best content and new marketing ideas, build your professional profile and become a better marketer together."
msgstr ""
"Diese Community richtet sich an Fachleute und Enthusiasten unserer Produkte und Dienstleistungen.\n"
"                                        <br>Teilen und diskutieren Sie die besten Inhalte und neuen Marketingideen, bauen Sie Ihr professionelles Profil auf und werden Sie gemeinsam ein besserer Vermarkter."

#. module: website_forum
#: model:forum.forum,description:website_forum.forum_help
msgid ""
"This community is for professionals and enthusiasts of our products and "
"services. Share and discuss the best content and new marketing ideas, build "
"your professional profile and become a better marketer together."
msgstr ""
"Diese Community richtet sich an Fachleute und Enthusiasten unserer Produkte "
"und Dienstleistungen. Teilen und diskutieren Sie die besten Inhalte und "
"neuen Marketingideen, bauen Sie Ihr professionelles Profil auf und werden "
"Sie gemeinsam ein besserer Vermarkter."

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__relevancy_post_vote
msgid ""
"This formula is used in order to sort by relevance. The variable 'votes' "
"represents number of votes for a post, and 'days' is number of days since "
"the post creation"
msgstr ""
"Diese Formel wird verwendet für die Sortierung nach Relevanz. Die Variable "
"„votes“ beinhaltet die Anzahl Stimmen für einen Beitrag und „days“ die "
"Anzahl Tage seit Erstellung des Beitrags"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "This forum has been archived."
msgstr "Dieses Forum wurde archiviert."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "This post can not be flagged"
msgstr "Dieser Beitrag kann nicht gekennzeichnet werden."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "This post is already flagged"
msgstr "Dieser Beitrag ist bereits gekennzeichnet"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid ""
"This post is currently awaiting moderation and it's not published yet.<br/>\n"
"                Do you want <b>Accept</b> or <b>Reject</b> this post ?"
msgstr ""
"Der Beitrag wartet auf Moderation und wurde noch nicht veröffentlicht.<br/>\n"
"                Möchten Sie den Post <b>Freigeben</b> oder <b>Ablehnen</b>?"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_14
msgid "Threatening language"
msgstr "Bedrohende Ausdrucksweise"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__name
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Title"
msgstr "Titel"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Title must not be empty"
msgstr "Der Titel darf nicht leer sein"

#. module: website_forum
#: code:addons/website_forum/controllers/main.py:0
#, python-format
msgid "Title should not be empty."
msgstr "Der Titel sollte nicht leer sein."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__recipient_id
msgid "To"
msgstr "An"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "To Validate"
msgstr "Zu validieren"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"To prevent your question from being flagged and possibly removed, avoid "
"asking subjective questions where …"
msgstr ""
"Um zu vermeiden, dass Ihre Frage markiert oder gar entfernt wird, vermeiden "
"Sie subjektive Fragen, in denen ..."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Toggle favorite status"
msgstr "Favoritenstatus ein/aus"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_9
msgid "Too localized"
msgstr "Zu stark eingeschränkt"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_3
msgid "Too subjective and argumentative"
msgstr "Zu subjektiv und argumentativ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "Toolbar with button groups"
msgstr "Symbolleiste mit Schaltflächengruppen"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Topics"
msgstr "Themen"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_list
msgid "Total Answers"
msgstr "Antworten gesamt"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_list
msgid "Total Favorites"
msgstr "Favoriten Gesamt"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_list
msgid "Total Views"
msgstr "Ansichten insgesamt"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__vote_count
msgid "Total Votes"
msgstr "Stimmen gesamt"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Trending"
msgstr "Im Trend"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Try searching for one or two words"
msgstr "Versuchen Sie, nach einem oder zwei Wörtern zu suchen"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Unanswered"
msgstr "Nicht beantwortet"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Undelete"
msgstr "Wiederherstellen"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_unlink_all
msgid "Unlink all comments"
msgstr "Alle Kommentare entfernen"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_unlink_own
msgid "Unlink own comments"
msgstr "Eigene Kommentare entfernen"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "Unmark as Best Answer"
msgstr "Markierung als beste Antwort aufheben"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_unread
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_unread
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_unread
msgid "Unread Messages"
msgstr "Ungelesene Nachrichten"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_unread_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_unread_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Anzahl ungelesener Nachrichten"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Unsolved"
msgstr "Ungelöst"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__write_uid
msgid "Updated by"
msgstr "Aktualisiert von"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__write_date
msgid "Updated on"
msgstr "Aktualisiert am"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_upvote
msgid "Upvote"
msgstr "Positiv bewerten"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_student
#: model:gamification.goal.definition,name:website_forum.definition_student
msgid "Upvoted question (1)"
msgstr "Aufgewertete Frage (1)"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_great_question
#: model:gamification.goal.definition,name:website_forum.definition_great_question
msgid "Upvoted question (15)"
msgstr "Aufgewertete Frage (15)"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_nice_question
#: model:gamification.goal.definition,name:website_forum.definition_nice_question
msgid "Upvoted question (4)"
msgstr "Aufgewertete Frage (4)"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_good_question
#: model:gamification.goal.definition,name:website_forum.definition_good_question
msgid "Upvoted question (6)"
msgstr "Aufgewertete Frage (6)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Use a clear, explicit and concise title"
msgstr "Nutzen Sie einen klaren, eindeutigen und knappen Titel"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__user_id
msgid "User"
msgstr "Benutzer"

#. module: website_forum
#: model:ir.model,name:website_forum.model_res_users
msgid "Users"
msgstr "Benutzer"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.action_forum_favorites
msgid "Users favorite posts"
msgstr "Beliebteste Beiträge der Benutzer"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Validate"
msgstr "Validieren"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid "Validate question"
msgstr "Frage validieren"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "View"
msgstr "Ansicht"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__views
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "Views"
msgstr "Ansichten"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_12
msgid "Violent language"
msgstr "Gewalttätige Sprache"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__vote
msgid "Vote"
msgstr "Bewertung"

#. module: website_forum
#: model:ir.model.constraint,message:website_forum.constraint_forum_post_vote_vote_uniq
msgid "Vote already exists !"
msgstr "Bewertung existiert bereits!"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__vote_ids
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Votes"
msgstr "Bewertungen"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__pending
msgid "Waiting Validation"
msgstr "Wartende Validierung"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Waiting for validation"
msgstr "Wartet auf Validierung"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_res_users__forum_waiting_posts_count
msgid "Waiting post"
msgstr "Wartender Beitrag"

#. module: website_forum
#: model:ir.model,name:website_forum.model_website
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_id
msgid "Website"
msgstr "Website"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__gamification_challenge__challenge_category__forum
msgid "Website / Forum"
msgstr "Website / Forum"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_message_ids
msgid "Website Messages"
msgstr "Website-Nachrichten"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__website_message_ids
#: model:ir.model.fields,help:website_forum.field_forum_post__website_message_ids
#: model:ir.model.fields,help:website_forum.field_forum_tag__website_message_ids
msgid "Website communication history"
msgstr "Website-Kommunikationshistorie"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_description
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_description
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_description
msgid "Website meta description"
msgstr "Website-Meta-Beschreibung"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_keywords
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_keywords
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_keywords
msgid "Website meta keywords"
msgstr "Website-Meta-Schlagwörter"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_title
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_title
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_title
msgid "Website meta title"
msgstr "Website-Meta-Titel"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_og_img
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_og_img
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_og_img
msgid "Website opengraph image"
msgstr "Opengraph-Bild der Website"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__welcome_message
msgid "Welcome Message"
msgstr "Willkommensnachricht"

#. module: website_forum
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid "Welcome!"
msgstr "Willkommen!"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"When a question or answer is upvoted, the user who posted them will gain "
"some points, which are called \"karma points\". These points serve as a "
"rough measure of the community trust to him/her. Various moderation tasks "
"are gradually assigned to the users based on those points."
msgstr ""
"Wird eine Frage aufgewertet, so gewinnt der Benutzer, der sie gestellt hat, "
"entsprechende Punkte, Karma-Punkte genannt. Diese Punkte dienen als grobe "
"Maßeinheit für die Vertrauenswürdigkeit des Benutzers. Verschiedene "
"Moderationstätigkeiten werden Benutzern entsprechend der Höhe ihres Karmas "
"freigegeben."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "You already have a pending post"
msgstr "Sie haben bereits einen ausstehenden Beitrag."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid "You can share your question once it has been validated"
msgstr "Sie können Ihre Frage teilen, sobald Sie validiert wurde"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "You cannot create recursive forum posts."
msgstr "Sie können keine rekursiven Forumsbeiträge erstellen."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid "You cannot post an empty answer"
msgstr "Sie können keine leere Antwort posten"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "You have no posts in this forum (yet)."
msgstr "Sie haben (noch) keine Beiträge in diesem Forum."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "You may now participate in our forums."
msgstr "Sie dürfen jetzt in unseren Foren aktiv werden."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "You need to have sufficient karma to edit tags"
msgstr "Sie benötigen ausreichend Karma, um Stichwörter zu bearbeiten"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"You should only ask practical, answerable questions based on actual problems"
" that you face. Chatty, open-ended questions diminish the usefulness of this"
" site and push other questions off the front page."
msgstr ""
"Sie sollten praxisbezogene, beantwortbare Fragen stellen, die auf Ihren "
"konkreten Problemen beruhen. Schwatzhafte und nicht abgegrenzte Fragen "
"vermindern die Nützlichkeit dieser Seite und verdrängen andere Fragen von "
"der Startseite."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "You're not following any topic in this forum (yet).<br/>"
msgstr "Sie verfolgen (noch) kein Thema in diesem Forum.<br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid "Your Answer"
msgstr "Ihre Antwort"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_reply
msgid "Your Reply"
msgstr "Ihre Antwort"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "Your favourite"
msgstr "Ihr Favorit"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "[Closed]"
msgstr "[Geschlossen]"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "[Deleted]"
msgstr "[Gelöscht]"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "[Offensive]"
msgstr "[Anstößig]"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "accept any answer"
msgstr "beliebige Antworten akzeptieren"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "activity date"
msgstr "Datum der Aktivität"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "breadcrumb"
msgstr "Brotkrümel"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "by"
msgstr "von"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "close any posts"
msgstr "beliebige Beiträge schließen"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "delete any comment"
msgstr "beliebige Kommentare löschen"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "delete any question or answer"
msgstr "beliebige Fragen oder Antworten löschen"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "delete own comment"
msgstr "eigenen Kommentar löschen"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "downvote"
msgstr "abwerten"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "e.g. Help"
msgstr "z. B. Hilfe"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
msgid "e.g. Who to do this particular thing?"
msgstr "es. Wer soll diese bestimmte Aufgabe übernehmen?"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "edit any post, view offensive flags"
msgstr "beliebige Beiträge bearbeiten, als anstößig gekennzeichnete ansehen"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "every answer is equally valid: “What’s your favorite ______?”"
msgstr "jede Antwort gleichwertig ist: „Was ist Ihre Lieblings______?“"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "flag offensive, close own questions"
msgstr "anstößige Fragen kennzeichnen, eigene Fragen schließen"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "for reason:"
msgstr "aus dem folgenden Grund:"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid ""
"has been posted and require your validation. Click here to access the "
"question :"
msgstr ""
"wurde gesendet und erfordert Ihre Validierung. Klicken Sie hier, um auf die "
"Frage zuzugreifen:"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "has been posted. Click here to access the post :"
msgstr "wurde gepostet. Klicken Sie hier, um auf den Beitrag zuzugreifen:"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
msgid "has been posted. Click here to access the question :"
msgstr "wurde gepostet. Klicken Sie hier, um auf die Frage zuzugreifen:"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid "here"
msgstr "hier"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "how to configure or customize Odoo to specific business needs,"
msgstr ""
"wie kann man Odoo konfigurieren oder an spezifische Geschäftsanforderungen "
"anpassen kann,"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "how to develop modules for your own need,"
msgstr "wie man Module für die eigenen Anforderungen entwickelt,"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "how to install Odoo on a specific infrastructure,"
msgstr "wie man Odoo in bestimmten Umgebungen installiert,"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid ""
"if your\n"
"        answer is selected as the right one. See what you can do with karma"
msgstr ""
", wenn Ihre\n"
"        Frage als korrekt gekennzeichnet wurde. Erfahren Sie, was Sie mit Karma-Punkten tun können, und zwar"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "in your favourites"
msgstr "in Ihren Favoriten"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "in your followed list"
msgstr "in Ihrer Favoritenliste"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "in your posts"
msgstr "in Ihren Beiträgen"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "insert text link, upload files"
msgstr "Textlinks einfügen, Dateien hochladen"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "instead."
msgstr "stattdessen."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "it is a rant disguised as a question: “______ sucks, am I right?”"
msgstr ""
"eine Tirade maskiert als Frage enthalten ist: „_______ ist doch Scheiße, "
"oder etwa nicht?“"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid "karma points"
msgstr "Karma-Punkte"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "matching \""
msgstr "für „"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "most answered"
msgstr "am häufigsten beantwortet"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "most voted"
msgstr "am häufigsten bewertet"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "newest"
msgstr "neueste"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "on"
msgstr "zu"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid ""
"on social networks get an answer within\n"
"        5 hours. Questions shared on two social networks have"
msgstr ""
", die in sozialen Netzwerken geteilt werden, innerhalb von 5 Stunden beantwortet.\n"
"        Fragen, die in zwei sozialen Netzwerken geteilt werden, haben eine"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "post"
msgstr "Beitrag"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "solved"
msgstr "gelöst"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "specific questions about Odoo service offers, etc."
msgstr "spezifische Fragen zu Odoo-Dienstleistungsangeboten etc."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "tag"
msgstr "Stichwort"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"there is no actual problem to be solved: “I’m curious if other people feel "
"like I do.”"
msgstr ""
"kein aktuelles Problem zu lösen ist: „Ich wollte nur einmal wissen, ob "
"andere Leute dasselbe denken.“"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "to partecipate"
msgstr ", um teilzunehmen"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "trending"
msgstr "im Trend"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "unanswered"
msgstr "nicht beantwortet"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "unsolved"
msgstr "ungelöst"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "upvote, add comments"
msgstr "aufwerten, Kommentare hinzufügen"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "using the"
msgstr "durch Anwendung von"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"we are being asked an open-ended, hypothetical question: “What if ______ "
"happened?”"
msgstr ""
"uns eine weit offene, hypothetische Frage gestellt wird: „Was wäre, wenn "
"______geschehen würde?“"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "what's the best way to use Odoo for a specific business need,"
msgstr ""
"wie man Odoo am besten für eine spezifische Geschäftsanforderung nutzt,"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.author_box
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "xp"
msgstr "xp"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"your answer is provided along with the question, and you expect more "
"answers: “I use ______ for ______, what do you use?”"
msgstr ""
"Ihre Antwort zusammen mit der Frage gegeben wird und Sie weitere Antworten "
"erwarten: „Ich verwende ______ für _______. Was brauchen Sie?“"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "your biography can be seen as tooltip"
msgstr "Ihre Biographie kann in einem Hilfefenster gesehen werden"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "| Flagged"
msgstr "| Gekennzeichnet"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "圾 Text"
msgstr "圾 Text"
