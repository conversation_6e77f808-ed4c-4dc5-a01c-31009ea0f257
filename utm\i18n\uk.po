# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* utm
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <alina.lis<PERSON><PERSON>@erp.co.ua>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_medium__active
msgid "Active"
msgstr "Активно"

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_campaign__is_auto_campaign
msgid "Allows us to filter relevant Campaigns"
msgstr "Дозволяє нам фільтрувати релевантні кампанії"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Approval-based Flow"
msgstr "Процес на основі схвалення"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Approved"
msgstr "Затверджено"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_search
msgid "Archived"
msgstr "Заархівовано"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.action_view_utm_tag
msgid "Assign tags to your campaigns to organize, filter and track them."
msgstr ""
"Призначайте теги своїм кампаніям, щоб упорядковувати, фільтрувати та "
"відстежувати їх."

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Audience-driven Flow"
msgstr "Процес, орієнтований на аудиторію"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__is_auto_campaign
msgid "Automatically Generated Campaign"
msgstr "Автоматично створена кампанія"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_mixin__campaign_id
msgid "Campaign"
msgstr "Кампанія"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__name
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form_quick_create
msgid "Campaign Name"
msgstr "Назва кампанії"

#. module: utm
#: model:ir.model,name:utm.model_utm_stage
msgid "Campaign Stage"
msgstr "Етап кампанії"

#. module: utm
#: model:ir.actions.act_window,name:utm.action_view_utm_tag
#: model_terms:ir.ui.view,arch_db:utm.utm_tag_view_tree
msgid "Campaign Tags"
msgstr "Теги кампанії"

#. module: utm
#: model:ir.actions.act_window,name:utm.utm_campaign_action
#: model:ir.ui.menu,name:utm.menu_utm_campaign_act
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Campaigns"
msgstr "Кампанії"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_campaign_action
msgid ""
"Campaigns are used to centralize your marketing efforts and track their "
"results."
msgstr ""
"Кампанії використовуються для централізації ваших маркетингових зусиль і "
"відстеження їх результатів."

#. module: utm
#: model:utm.campaign,name:utm.utm_campaign_christmas_special
msgid "Christmas Special"
msgstr "Спеціальні різдвяні"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Collect ideas, design creative content and publish it once reviewed."
msgstr ""
"Збирайте ідеї, створюйте креативний контент і публікуйте його після "
"перевірки."

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__color
#: model:ir.model.fields,field_description:utm.field_utm_tag__color
msgid "Color Index"
msgstr "Індекс кольору"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Copywriting"
msgstr "Копірайтинг"

#. module: utm
#: model:utm.source,name:utm.utm_source_craigslist
msgid "Craigslist"
msgstr "Craigslist"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_medium_action
msgid "Create a Medium"
msgstr "Створити канал"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.action_view_utm_tag
msgid "Create a Tag"
msgstr "Створити тег"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_campaign_action
msgid "Create a campaign"
msgstr "Створити кампанію"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.action_view_utm_stage
msgid "Create a stage for your campaigns"
msgstr "Створіть етап вашої кампанії"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__create_uid
#: model:ir.model.fields,field_description:utm.field_utm_medium__create_uid
#: model:ir.model.fields,field_description:utm.field_utm_source__create_uid
#: model:ir.model.fields,field_description:utm.field_utm_stage__create_uid
#: model:ir.model.fields,field_description:utm.field_utm_tag__create_uid
msgid "Created by"
msgstr "Створив"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__create_date
#: model:ir.model.fields,field_description:utm.field_utm_medium__create_date
#: model:ir.model.fields,field_description:utm.field_utm_source__create_date
#: model:ir.model.fields,field_description:utm.field_utm_stage__create_date
#: model:ir.model.fields,field_description:utm.field_utm_tag__create_date
msgid "Created on"
msgstr "Створено на"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Creative Flow"
msgstr "Процес створення"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_kanban
msgid "Delete"
msgstr "Видалити"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Deploy"
msgstr "Розгорнути"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Deployed"
msgstr "Завантажено"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: model:utm.stage,name:utm.campaign_stage_2
#, python-format
msgid "Design"
msgstr "Дизайн"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__display_name
#: model:ir.model.fields,field_description:utm.field_utm_medium__display_name
#: model:ir.model.fields,field_description:utm.field_utm_source__display_name
#: model:ir.model.fields,field_description:utm.field_utm_stage__display_name
#: model:ir.model.fields,field_description:utm.field_utm_tag__display_name
msgid "Display Name"
msgstr "Відобразити назву"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Done"
msgstr "Виконано"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_kanban
msgid "Dropdown menu"
msgstr "Спадне меню"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_kanban
msgid "Edit"
msgstr "Редагувати"

#. module: utm
#: model:utm.campaign,name:utm.utm_campaign_email_campaign_products
msgid "Email Campaign - Products"
msgstr "Кампанія електронної пошти - Товари"

#. module: utm
#: model:utm.campaign,name:utm.utm_campaign_email_campaign_services
msgid "Email Campaign - Services"
msgstr "Електронна пошта - послуги"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Event-driven Flow"
msgstr "Процес на основі подій"

#. module: utm
#: model:utm.source,name:utm.utm_source_facebook
msgid "Facebook"
msgstr "Facebook"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Gather Data"
msgstr "Збирайте дані"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid ""
"Gather data, build a recipient list and write content based on your "
"Marketing target."
msgstr ""
"Збирайте дані, створюйте список одержувачів і пишіть вміст на основі вашої "
"маркетингової цілі."

#. module: utm
#: model:utm.source,name:utm.utm_source_glassdoor
msgid "Glassdoor"
msgstr "Glassdoor"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Group By"
msgstr "Групувати за"

#. module: utm
#: model:ir.model,name:utm.model_ir_http
msgid "HTTP Routing"
msgstr "Маршрутизація HTTP"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__id
#: model:ir.model.fields,field_description:utm.field_utm_medium__id
#: model:ir.model.fields,field_description:utm.field_utm_source__id
#: model:ir.model.fields,field_description:utm.field_utm_stage__id
#: model:ir.model.fields,field_description:utm.field_utm_tag__id
msgid "ID"
msgstr "ID"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Ideas"
msgstr "Ідеї"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign____last_update
#: model:ir.model.fields,field_description:utm.field_utm_medium____last_update
#: model:ir.model.fields,field_description:utm.field_utm_source____last_update
#: model:ir.model.fields,field_description:utm.field_utm_stage____last_update
#: model:ir.model.fields,field_description:utm.field_utm_tag____last_update
msgid "Last Modified on"
msgstr "Останні зміни на"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__write_uid
#: model:ir.model.fields,field_description:utm.field_utm_medium__write_uid
#: model:ir.model.fields,field_description:utm.field_utm_source__write_uid
#: model:ir.model.fields,field_description:utm.field_utm_stage__write_uid
#: model:ir.model.fields,field_description:utm.field_utm_tag__write_uid
msgid "Last Updated by"
msgstr "Востаннє оновив"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__write_date
#: model:ir.model.fields,field_description:utm.field_utm_medium__write_date
#: model:ir.model.fields,field_description:utm.field_utm_source__write_date
#: model:ir.model.fields,field_description:utm.field_utm_stage__write_date
#: model:ir.model.fields,field_description:utm.field_utm_tag__write_date
msgid "Last Updated on"
msgstr "Останнє оновлення"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Later"
msgstr "Пізніше"

#. module: utm
#: model:utm.source,name:utm.utm_source_mailing
msgid "Lead Recall"
msgstr "Нагадування ліда"

#. module: utm
#: model:ir.ui.menu,name:utm.menu_link_tracker_root
msgid "Link Tracker"
msgstr "Відстежувач посилань"

#. module: utm
#: model:utm.source,name:utm.utm_source_linkedin
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "List-Building"
msgstr "Створення списку"

#. module: utm
#: model:utm.tag,name:utm.utm_tag_1
msgid "Marketing"
msgstr "Маркетинг"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_mixin__medium_id
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_form
msgid "Medium"
msgstr "Канал"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_medium__name
msgid "Medium Name"
msgstr "Назва каналу"

#. module: utm
#: model:ir.actions.act_window,name:utm.utm_medium_action
#: model:ir.ui.menu,name:utm.menu_utm_medium
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_tree
msgid "Mediums"
msgstr "Канали"

#. module: utm
#: model:utm.source,name:utm.utm_source_monster
msgid "Monster"
msgstr "Монстр"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_stage__name
#: model:ir.model.fields,field_description:utm.field_utm_tag__name
msgid "Name"
msgstr "Назва"

#. module: utm
#: model:utm.stage,name:utm.default_utm_stage
msgid "New"
msgstr "Новий"

#. module: utm
#: model:utm.source,name:utm.utm_source_newsletter
msgid "Newsletter"
msgstr "Інформаційний бюлетень"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_source_action
msgid "No Sources yet!"
msgstr "Ще немає джерел!"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Pre-Launch"
msgstr "Попередній запуск"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Prepare Campaigns and get them approved before making them go live."
msgstr "Підготуйте кампанії та отримайте їх схвалення, перш ніж запускати їх."

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid ""
"Prepare your Campaign, test it with part of your audience and deploy it "
"fully afterwards."
msgstr ""
"Підготуйте свою кампанію, протестуйте її з частиною своєї аудиторії та "
"повністю розгорніть її після цього."

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Report"
msgstr "Звіт"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__user_id
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Responsible"
msgstr "Відповідальний"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Review"
msgstr "Відгук"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Running"
msgstr "Діючий"

#. module: utm
#: model:utm.campaign,name:utm.utm_campaign_fall_drive
msgid "Sale"
msgstr "Розпродаж"

#. module: utm
#: model:utm.stage,name:utm.campaign_stage_1
msgid "Schedule"
msgstr "Розклад"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_search
msgid "Search UTM Medium"
msgstr "Пошук каналу UTM"

#. module: utm
#: model:utm.source,name:utm.utm_source_search_engine
msgid "Search engine"
msgstr "Пошукова система"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Send"
msgstr "Надіслати"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: model:utm.stage,name:utm.campaign_stage_3
#, python-format
msgid "Sent"
msgstr "Надіслано"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_stage__sequence
msgid "Sequence"
msgstr "Послідовність"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Soft-Launch"
msgstr "Легкий запуск"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Soft-Launch Flow"
msgstr "Процес легкого запуску"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_mixin__source_id
#: model_terms:ir.ui.view,arch_db:utm.utm_source_view_form
#: model_terms:ir.ui.view,arch_db:utm.utm_source_view_tree
msgid "Source"
msgstr "Джерело"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_source__name
msgid "Source Name"
msgstr "Назва джерела"

#. module: utm
#: model:ir.actions.act_window,name:utm.utm_source_action
#: model:ir.ui.menu,name:utm.menu_utm_source
msgid "Sources"
msgstr "Джерела"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__stage_id
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Stage"
msgstr "Етап"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_stage_view_search
#: model_terms:ir.ui.view,arch_db:utm.utm_stage_view_tree
msgid "Stages"
msgstr "Етапи"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.action_view_utm_stage
msgid ""
"Stages allow you to organize your workflow  (e.g. : plan, design, in "
"progress,  done, …)."
msgstr ""
"Етапи дозволяють вам організувати ваш робочий процес (напр. : план, дизайн, "
"в процесі, виконано, …)."

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_tag__color
msgid ""
"Tag color. No color means no display in kanban to distinguish internal tags "
"from public categorization tags."
msgstr ""
"Колір тегу. Відсутність кольору означає відсутність відображення в канбані, "
"щоб відрізнити внутрішні теги від загальнодоступних тегів категоризації."

#. module: utm
#: model:ir.model.constraint,message:utm.constraint_utm_tag_name_uniq
msgid "Tag name already exists !"
msgstr "Така мітка вже існує!"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__tag_ids
msgid "Tags"
msgstr "Теги"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "This Month"
msgstr "Цього місяця"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "This Week"
msgstr "Цього тижня"

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_mixin__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""
"Це ім'я, яке допомагає вам стежити за різними зусиллями кампанії, напр. "
"Fall_Drive, Christmas_Special"

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_mixin__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr ""
"Це спосіб доставки, наприклад, листівка, електронна пошта або банерна "
"реклама"

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_mixin__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr ""
"Це джерело посилання, наприклад, пошукова система, інший домен або назва "
"списку листів"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "To be Approved"
msgstr "Буде затверджено"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid ""
"Track incoming events (e.g. : Christmas, Black Friday, ...) and publish "
"timely content."
msgstr ""
"Відстежуйте вхідні події (наприклад, Різдво, Чорну п’ятницю, ...) і "
"публікуйте своєчасний контент."

#. module: utm
#: model:utm.source,name:utm.utm_source_twitter
msgid "Twitter"
msgstr "Twitter"

#. module: utm
#: model:ir.model,name:utm.model_utm_campaign
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form
msgid "UTM Campaign"
msgstr "Кампанія UTM"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_tree
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "UTM Campaigns"
msgstr "Кампанії UTM"

#. module: utm
#: model:ir.model,name:utm.model_utm_medium
msgid "UTM Medium"
msgstr "Канал UTM "

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_medium_action
msgid ""
"UTM Mediums track the mean that was used to attract traffic (e.g. "
"\"Website\", \"Twitter\", ...)."
msgstr ""
"Канали UTM відстежують значення, яке було використано для залучення трафіку "
"(наприклад, «Веб-сайт», «Твіттер», ...)."

#. module: utm
#: model:ir.model,name:utm.model_utm_mixin
msgid "UTM Mixin"
msgstr "Мікс UTM "

#. module: utm
#: model:ir.model,name:utm.model_utm_source
msgid "UTM Source"
msgstr "Джерело UTM "

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_source_action
msgid ""
"UTM Sources track where traffic comes from  (e.g. \"May Newsletter\", \"\", "
"...)."
msgstr ""
"Джерела UTM відстежують, звідки надходить трафік (наприклад, \"Травневий "
"інформаційний бюлетень\", \"\", ...)."

#. module: utm
#: model:ir.actions.act_window,name:utm.action_view_utm_stage
msgid "UTM Stages"
msgstr "Етапи UTM"

#. module: utm
#: model:ir.model,name:utm.model_utm_tag
msgid "UTM Tag"
msgstr "Тег UTM"

#. module: utm
#: model:ir.ui.menu,name:utm.marketing_utm
msgid "UTMs"
msgstr "UTM"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Use This For My Campaigns"
msgstr "Використовуйте це для Моєї кампанії"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form_quick_create
msgid "e.g. Black Friday"
msgstr "напр., Чорна П'ятниця"
