# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* microsoft_outlook
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-03-30 11:00+0000\n"
"PO-Revision-Date: 2022-05-17 12:42+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Hungarian (https://app.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.ir_mail_server_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                        Connect your Outlook account"
msgstr ""

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.ir_mail_server_view_form
msgid ""
"<i class=\"fa fa-cog\"/>\n"
"                        Edit Settings"
msgstr ""

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.ir_mail_server_view_form
msgid ""
"<span attrs=\"{'invisible': ['|', ('use_microsoft_outlook_service', '=', False), ('microsoft_outlook_refresh_token', '=', False)]}\" class=\"badge badge-success\">\n"
"                        Outlook Token Valid\n"
"                    </span>"
msgstr ""

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Outlook Credentials</span>"
msgstr ""

#. module: microsoft_outlook
#: code:addons/microsoft_outlook/models/microsoft_outlook_mixin.py:0
#, python-format
msgid "An error occurred when fetching the access token. %s"
msgstr ""

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__microsoft_outlook_uri
#: model:ir.model.fields,field_description:microsoft_outlook.field_microsoft_outlook_mixin__microsoft_outlook_uri
msgid "Authentication URI"
msgstr ""

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.res_config_settings_view_form
msgid "Client ID"
msgstr "Ügyfél azonosító"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.res_config_settings_view_form
msgid "Client Secret"
msgstr "Ügyfél titok"

#. module: microsoft_outlook
#: model:ir.model,name:microsoft_outlook.model_res_config_settings
msgid "Config Settings"
msgstr "Beállítások módosítása"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.microsoft_outlook_oauth_error
msgid "Go back to your mail server"
msgstr ""

#. module: microsoft_outlook
#: code:addons/microsoft_outlook/models/ir_mail_server.py:0
#, python-format
msgid ""
"Incorrect Connection Security for Outlook mail server %r. Please set it to "
"\"TLS (STARTTLS)\"."
msgstr ""

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__is_microsoft_outlook_configured
#: model:ir.model.fields,field_description:microsoft_outlook.field_microsoft_outlook_mixin__is_microsoft_outlook_configured
msgid "Is Outlook Credential Configured"
msgstr ""

#. module: microsoft_outlook
#: model:ir.model,name:microsoft_outlook.model_ir_mail_server
msgid "Mail Server"
msgstr "Levelezőszerver"

#. module: microsoft_outlook
#: model:ir.model,name:microsoft_outlook.model_microsoft_outlook_mixin
msgid "Microsoft Outlook Mixin"
msgstr ""

#. module: microsoft_outlook
#: code:addons/microsoft_outlook/models/microsoft_outlook_mixin.py:0
#, python-format
msgid "Only the administrator can link an Outlook mail server."
msgstr ""

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.ir_mail_server_view_form
msgid "Outlook"
msgstr "Outlook"

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__microsoft_outlook_access_token
#: model:ir.model.fields,field_description:microsoft_outlook.field_microsoft_outlook_mixin__microsoft_outlook_access_token
msgid "Outlook Access Token"
msgstr ""

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__microsoft_outlook_access_token_expiration
#: model:ir.model.fields,field_description:microsoft_outlook.field_microsoft_outlook_mixin__microsoft_outlook_access_token_expiration
msgid "Outlook Access Token Expiration Timestamp"
msgstr ""

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__use_microsoft_outlook_service
#: model:ir.model.fields,field_description:microsoft_outlook.field_microsoft_outlook_mixin__use_microsoft_outlook_service
msgid "Outlook Authentication"
msgstr ""

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_res_config_settings__microsoft_outlook_client_identifier
msgid "Outlook Client Id"
msgstr ""

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_res_config_settings__microsoft_outlook_client_secret
msgid "Outlook Client Secret"
msgstr ""

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__microsoft_outlook_refresh_token
#: model:ir.model.fields,field_description:microsoft_outlook.field_microsoft_outlook_mixin__microsoft_outlook_refresh_token
msgid "Outlook Refresh Token"
msgstr ""

#. module: microsoft_outlook
#: code:addons/microsoft_outlook/models/microsoft_outlook_mixin.py:0
#, python-format
msgid "Please configure your Outlook credentials."
msgstr ""

#. module: microsoft_outlook
#: code:addons/microsoft_outlook/models/ir_mail_server.py:0
#, python-format
msgid ""
"Please leave the password field empty for Outlook mail server %r. The OAuth "
"process does not require it"
msgstr ""

#. module: microsoft_outlook
#: code:addons/microsoft_outlook/models/microsoft_outlook_mixin.py:0
#, python-format
msgid "Please login your Outlook mail server before using it."
msgstr ""

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.res_config_settings_view_form
msgid "Send and receive email with your Outlook account."
msgstr ""

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.ir_mail_server_view_form
msgid ""
"Setup your Outlook API credentials in the general settings to link a Outlook"
" account."
msgstr ""

#. module: microsoft_outlook
#: model:ir.model.fields,help:microsoft_outlook.field_ir_mail_server__microsoft_outlook_uri
#: model:ir.model.fields,help:microsoft_outlook.field_microsoft_outlook_mixin__microsoft_outlook_uri
msgid "The URL to generate the authorization code from Outlook"
msgstr ""

#. module: microsoft_outlook
#: code:addons/microsoft_outlook/models/microsoft_outlook_mixin.py:0
#, python-format
msgid "Unknown error."
msgstr ""
