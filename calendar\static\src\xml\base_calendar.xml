<?xml version="1.0" encoding="UTF-8"?>
<template>

    <t t-name="calendar.CalendarView" t-extend="CalendarView">
        <t t-jquery="div.o_calendar_sidebar" t-operation="append">
            <div id="calendar_sync" class="o_calendar_sync container inline btn-group"/>
        </t>
    </t>

    <div t-name="calendar.RecurrentEventUpdate">
        <div class="form-check o_radio_item">
            <input name="recurrence-update" type="radio" class="form-check-input o_radio_input" checked="true" value="self_only" id="self_only"/>
            <label class="form-check-label o_form_label" for="self_only">This event</label>
        </div>

        <div class="form-check o_radio_item">
            <input name="recurrence-update" type="radio" class="form-check-input o_radio_input" value="future_events" id="future_events"/>
            <label class="form-check-label o_form_label" for="future_events">This and following events</label>
        </div>

        <div class="form-check o_radio_item">
            <input name="recurrence-update" type="radio" class="form-check-input o_radio_input" value="all_events" id="all_events"/>
            <label class="form-check-label o_form_label" for="all_events">All events</label>
        </div>

    </div>

    <t t-extend="mail.systray.ActivityMenu.Previews">
        <t t-jquery="div.o_preview_title" t-operation="after">
            <div t-if="activity and activity.type == 'meeting'">
                <t t-set="is_next_meeting" t-value="true"/>
                <t t-foreach="activity.meetings" t-as="meeting">
                    <div>
                        <span t-att-class="!meeting.allday and is_next_meeting ? 'o_meeting_filter o_meeting_bold' : 'o_meeting_filter'" t-att-data-res_model="activity.model" t-att-data-res_id="meeting.id" t-att-data-model_name="activity.name" t-att-title="meeting.name">
                            <span><t t-esc="meeting.name"/></span>
                        </span>
                        <span t-if="meeting.start" class="float-right">
                            <t t-if="meeting.allday">All Day</t>
                            <t t-else=''>
                                <t t-set="is_next_meeting" t-value="false"/>
                                <t t-esc="moment(meeting.start).local().format(Time.strftime_to_moment_format(_t.database.parameters.time_format))"/>
                            </t>
                        </span>
                    </div>
                </t>
            </div>
        </t>
    </t>

    <t t-name="Calendar.attendee.status.popover" t-extend="CalendarView.event.popover">
        <t t-jquery=".o_cw_popover_delete" t-operation="after">
            <div t-if="widget.displayAttendeeAnswerChoice()" class="btn-group o-calendar-attendee-status ml-2">
                <a href="#" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i t-attf-class="fa fa-circle o-calendar-attendee-status-icon #{widget.selectedStatusInfo.color}"/> <span class="o-calendar-attendee-status-text" t-esc="widget.selectedStatusInfo.text"></span>
                </a>
                <div class="dropdown-menu overflow-hidden">
                    <a class="dropdown-item" href="#" data-action="accepted"><i class="fa fa-circle text-success"/> Accept</a>
                    <a class="dropdown-item" href="#" data-action="declined"><i class="fa fa-circle text-danger"/> Decline</a>
                    <a class="dropdown-item" href="#" data-action="tentative"><i class="fa fa-circle text-muted"/> Uncertain</a>
                </div>
            </div>
        </t>
    </t>

     <t t-name="Calendar.calendar_add_buttons">
        <span class="o_calendar_create_buttons">
            <button class="btn btn-primary o-calendar-button-new" aria-label="Add" title="Add">Add</button>
        </span>
    </t>

    <t t-name="Calendar.calendar-box">
        <div t-attf-class="#{record.is_highlighted ? 'o_event_hightlight' : ''} #{typeof color === 'number' ? _.str.sprintf('o_calendar_color_%s', color) : 'o_calendar_color_1'} fc-event o_event o_attendee_status_#{record.is_alone ? 'alone' : record.attendee_status} py-0" >
            <div class="o_event_title mr-2">
                <span t-if="record.is_alone" class="fa fa-exclamation-circle"/>
                <t t-esc="record.display_name"/>
            </div>
            <span class="fc-time"/>
        </div>
    </t>

</template>
