# تعليمات التثبيت - HR Error Handler

## المتطلبات المسبقة

1. **Odoo 15.0** - تأكد من أن لديك Odoo 15 مثبت ويعمل
2. **مديول hr_employees_masarat** - يجب أن يكون مثبتاً ومفعلاً
3. **صلاحيات المدير** - تحتاج صلاحيات مدير النظام لتثبيت المديولات

## خطوات التثبيت

### 1. نسخ المديول

انسخ مجلد `hr_error_handler` إلى مجلد addons في Odoo:

```bash
cp -r hr_error_handler /path/to/odoo/addons/
```

أو إذا كنت تستخدم مجلد addons مخصص:

```bash
cp -r hr_error_handler /path/to/custom/addons/
```

### 2. إعادة تشغيل Odoo

أعد تشغيل خدمة Odoo لتحميل المديول الجديد:

```bash
sudo systemctl restart odoo
```

أو إذا كنت تشغل Odoo يدوياً:

```bash
./odoo-bin -c /path/to/config.conf --stop-after-init
./odoo-bin -c /path/to/config.conf
```

### 3. تحديث قائمة التطبيقات

1. سجل دخولك إلى Odoo كمدير نظام
2. اذهب إلى **التطبيقات** (Apps)
3. انقر على **تحديث قائمة التطبيقات** (Update Apps List)
4. أكد العملية

### 4. تثبيت المديول

1. في قائمة التطبيقات، ابحث عن "HR Error Handler"
2. انقر على **تثبيت** (Install)
3. انتظر حتى اكتمال التثبيت

## التحقق من التثبيت

للتأكد من أن المديول يعمل بشكل صحيح:

1. اذهب إلى **الموظفون** > **الموظفون**
2. افتح أي موظف
3. تأكد من عدم ظهور أخطاء في السجلات (logs)

## استكشاف الأخطاء

### خطأ في التبعيات

إذا ظهر خطأ يتعلق بالتبعيات:

```
Module hr_employees_masarat not found
```

تأكد من أن مديول `hr_employees_masarat` مثبت ومفعل.

### خطأ في الصلاحيات

إذا ظهر خطأ في الصلاحيات:

```bash
chmod -R 755 hr_error_handler/
chown -R odoo:odoo hr_error_handler/
```

### مشاكل في قاعدة البيانات

إذا واجهت مشاكل في قاعدة البيانات، جرب:

```bash
./odoo-bin -c config.conf -d database_name -u hr_error_handler
```

## إلغاء التثبيت

لإلغاء تثبيت المديول:

1. اذهب إلى **التطبيقات**
2. ابحث عن "HR Error Handler"
3. انقر على **إلغاء التثبيت** (Uninstall)

## الدعم

إذا واجهت أي مشاكل، تحقق من:

1. سجلات Odoo (`/var/log/odoo/odoo.log`)
2. تأكد من أن جميع التبعيات مثبتة
3. تأكد من صحة صلاحيات الملفات
