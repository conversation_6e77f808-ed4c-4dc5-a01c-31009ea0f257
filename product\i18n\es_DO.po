# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * product
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2016-03-21 01:06+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Dominican Republic) (http://www.transifex.com/odoo/"
"odoo-9/language/es_DO/)\n"
"Language: es_DO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: product
#: selection:product.pricelist.item,applied_on:0
msgid " Product Category"
msgstr "Categoría de producto"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_product_variant_count
#: model:ir.model.fields,field_description:product.field_product_template_product_variant_count
msgid "# of Product Variants"
msgstr "Variantes de productos"

#. module: product
#: code:addons/product/pricelist.py:372
#, python-format
msgid "%s %% discount"
msgstr "%s %% de descuento"

#. module: product
#: code:addons/product/pricelist.py:374
#, python-format
msgid "%s %% discount and %s surcharge"
msgstr "%s %% de descuento y %s de recargo"

#. module: product
#: code:addons/product/product.py:716
#, python-format
msgid "%s (copy)"
msgstr "%s (copia)"

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_1
msgid "16 GB"
msgstr "16 GB"

#. module: product
#: model:product.product,description_sale:product.product_product_3
#: model:product.template,description_sale:product.product_product_3_product_template
msgid ""
"17\" LCD Monitor\n"
"Processor AMD 8-Core\n"
"512MB RAM\n"
"HDD SH-1"
msgstr ""
"Monitor LCD 17\"\n"
"Procesador AMD 8-Core\n"
"512 MB RAM\n"
"HDD SH-1"

#. module: product
#: model:product.product,description:product.product_product_25
#: model:product.template,description:product.product_product_25_product_template
msgid ""
"17\" Monitor\n"
"4GB RAM\n"
"Standard-1294P Processor\n"
"QWERTY keyboard"
msgstr ""
"Monitor 17\"\n"
"4 GB RAM\n"
"Procesador estándar 1294P\n"
"Teclado QWERTY"

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_5
msgid "2.4 GHz"
msgstr "2.4 GHz"

#. module: product
#: model:product.product,description_sale:product.product_product_8
#: model:product.template,description_sale:product.product_product_8_product_template
msgid ""
"2.7GHz quad-core Intel Core i5\n"
"                Turbo Boost up to 3.2GHz\n"
"                8GB (two 4GB) memory\n"
"                1TB hard drive\n"
"                Intel Iris Pro graphics\n"
"            "
msgstr ""
"2,7GHz quad-core Intel Core i5\n"
"Turbo Boost hasta 3.2GHz\n"
"8GB (2x4GB) de memoria\n"
"Disco duro de 1TB\n"
"Gráficos Intel Iris Pro"

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_2
msgid "32 GB"
msgstr "32 GB"

#. module: product
#: model:product.product,description_sale:product.product_product_4
#: model:product.product,description_sale:product.product_product_4b
#: model:product.product,description_sale:product.product_product_4c
#: model:product.product,description_sale:product.product_product_4d
#: model:product.template,description_sale:product.product_product_4_product_template
#: model:product.template,description_sale:product.product_product_4b_product_template
#: model:product.template,description_sale:product.product_product_4c_product_template
#: model:product.template,description_sale:product.product_product_4d_product_template
msgid ""
"7.9‑inch (diagonal) LED-backlit, 128Gb\n"
"Dual-core A5 with quad-core graphics\n"
"FaceTime HD Camera, 1.2 MP Photos"
msgstr ""
"Pantalla con retroiluminación LED de 7.9\", 128Gb\n"
"Dual-core A5 con gráficos quad-core\n"
"Cámara FaceTime HD, Fotos 1.2 MP"

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "75 percent less reflection."
msgstr "75% menos reflejos."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
#, fuzzy
msgid ""
"<span attrs=\"{'invisible':[('base', '!=', 'list_price')]}\">Public Price  "
"-  </span>\n"
"                            <span attrs=\"{'invisible':[('base', '!=', "
"'standard_price')]}\">Cost  -  </span>\n"
"                            <span attrs=\"{'invisible':[('base', '!=', "
"'pricelist')]}\">Other Pricelist  -  </span>"
msgstr ""
"<span attrs=\"{'invisible':[('base', 'not in', "
"('list_price','standard_price'))]}\">Precio público  -  </span>\n"
"                            <span attrs=\"{'invisible':[('base', '!=', "
"'pricelist')]}\">Otras listas de Precio  -  </span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "<span>kg</span>"
msgstr "<span>kg</span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist
msgid "<strong>Currency</strong>:<br/>"
msgstr "<strong>Moneda</strong>:<br/>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist
msgid "<strong>Description</strong>"
msgstr "<strong>Descripción</strong>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist
msgid "<strong>Price List Name</strong>:<br/>"
msgstr "<strong>Nombre de tarifa</strong>:<br/>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist
msgid "<strong>Print date</strong>:<br/>"
msgstr "<strong>Fecha de impresión</strong>:<br/>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_only_form_view
msgid ""
"<strong>Warning</strong>: adding or deleting attributes\n"
"                            will delete and recreate existing variants and "
"lead\n"
"                            to the loss of their possible customizations."
msgstr ""
"<strong>Advertencia</strong>: agregar o eliminación de atributos\n"
"                            borrará y volver a crear variantes existentes\n"
"                            y la pérdida de sus posibles personalizaciones."

#. module: product
#: code:addons/product/product.py:1011 sql_constraint:product.product:0
#, python-format
msgid "A barcode can only be assigned to one product !"
msgstr "¡Un código de barras solo puede ser asignado a un único producto!"

#. module: product
#: model:ir.model.fields,help:product.field_product_category_type
msgid ""
"A category of the view type is a virtual category that can be used as the "
"parent of another category to create a hierarchical structure."
msgstr ""
"Una categoría con tipo 'Vista' es una categoría que puede ser usada como "
"padre de otra categoría, para crear una estructura jerárquica."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_type
#: model:ir.model.fields,help:product.field_product_template_type
msgid ""
"A consumable is a product for which you don't manage stock, a service is a "
"non-material product provided by a company or an individual."
msgstr ""
"Un consumible es un producto para el que no se gestiona stock; un servicio "
"es un producto inmaterial suministrado por una compañía o un individuo."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_description_sale
#: model:ir.model.fields,help:product.field_product_template_description_sale
msgid ""
"A description of the Product that you want to communicate to your customers. "
"This description will be copied to every Sale Order, Delivery Order and "
"Customer Invoice/Refund"
msgstr ""
"Una descripción del producto que quiera comunicar a sus clientes. Esta "
"descripción se copiará a cada pedido de venta, albarán de salida y factura "
"de cliente."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_description_purchase
#: model:ir.model.fields,help:product.field_product_template_description_purchase
msgid ""
"A description of the Product that you want to communicate to your vendors. "
"This description will be copied to every Purchase Order, Receipt and Vendor "
"Bill/Refund."
msgstr ""
"Una descripción del producto que usted quiere comunicar a sus proveedores. "
"Esta descripción se copiará en todas las órdenes de venta y facturas de "
"proveedor o rectificativas."

#. module: product
#: model:product.product,website_description:product.product_product_9
#: model:product.template,website_description:product.product_product_9_product_template
msgid "A great Keyboard. Cordless."
msgstr "Un gran teclado. Inalámbrico."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_description
#: model:ir.model.fields,help:product.field_product_template_description
msgid ""
"A precise description of the Product, used only for internal information "
"purposes."
msgstr ""
"Una descripción precisa del producto, usada sólo para propósitos de "
"información interna."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_pricelist_action2
msgid ""
"A price list contains rules to be evaluated in order to compute\n"
"                the sales price of the products."
msgstr ""
"Una tarifa contiene reglas a ser evaluadas para calcular los\n"
"precios de venta de cada producto."

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid "A screen worthy of iPad."
msgstr "Una pantalla digna de un iPad."

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid ""
"About the size of a credit card — and just 5.4 mm thin — iPod nano is the "
"thinnest iPod ever made.\n"
"                                    The 2.5-inch Multi-Touch display is "
"nearly twice as big as the display on the previous iPod nano,\n"
"                                    so you can see more of the music, "
"photos, and videos you love."
msgstr ""
"Del tamaño de una tarjeta de crédito — y sólo 5,4 mm de espesor — el iPod "
"nano es el iPod más delgado que jamás se ha hecho.\n"
"                                    La pantalla Multi-Touch de 2.5 pulgadas "
"es casi dos veces tan grande como la pantalla en el iPod nano anterior, para "
"que pueda ver más de la música, fotos y videos de que amas."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_active
#: model:ir.model.fields,field_description:product.field_product_product_active
#: model:ir.model.fields,field_description:product.field_product_template_active
#: model:ir.model.fields,field_description:product.field_product_uom_active
msgid "Active"
msgstr "Activo"

#. module: product
#: model:product.category,name:product.product_category_all
msgid "All"
msgstr "Todos"

#. module: product
#: code:addons/product/pricelist.py:367
#, python-format
msgid "All Products"
msgstr "Todos los productos"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "All general settings about this product are managed on"
msgstr "Todos los ajustes generales acerca de este producto se gestionan en"

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid ""
"And at the Apple Online Store, you can configure your iMac with an even more "
"powerful Intel Core i7 processor, up to 3.5GHz."
msgstr ""
"Y en la Tienda en la Linea de Apple, usted puede configurar su iMac con un "
"procesador más potente como un Intel Core i7, hasta 3.5 GHz."

#. module: product
#: model:product.product,website_description:product.product_product_4
#: model:product.product,website_description:product.product_product_4b
#: model:product.product,website_description:product.product_product_4c
#: model:product.product,website_description:product.product_product_4d
#: model:product.template,website_description:product.product_product_4_product_template
#: model:product.template,website_description:product.product_product_4b_product_template
#: model:product.template,website_description:product.product_product_4c_product_template
#: model:product.template,website_description:product.product_product_4d_product_template
msgid "And because it’s so easy to use, it’s easy to love."
msgstr "Y porque es tan fácil de usar, es fácil amar."

#. module: product
#: model:product.product,name:product.product_product_7
#: model:product.template,name:product.product_product_7_product_template
msgid "Apple In-Ear Headphones"
msgstr "Auriculares de tapón Apple"

#. module: product
#: model:product.product,name:product.product_product_9
#: model:product.template,name:product.product_product_9_product_template
msgid "Apple Wireless Keyboard"
msgstr "Teclado inalámbrico Apple"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_normal_form_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "Applicable On"
msgstr "Aplicable en"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_applied_on
msgid "Apply On"
msgstr "Aplicar a"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_search
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Archived"
msgstr "Archivado"

#. module: product
#: model:product.product,name:product.product_assembly
#: model:product.template,name:product.product_assembly_product_template
msgid "Assembly Service Cost"
msgstr "Coste servicio ensamblaje"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_sequence
msgid "Assigns the priority to the list of product vendor."
msgstr "Asigna la prioridad a la lista de proveedores de producto."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_line_attribute_id
#: model:ir.model.fields,field_description:product.field_product_attribute_value_attribute_id
msgid "Attribute"
msgstr "Atributo"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value_price_extra
msgid "Attribute Price Extra"
msgstr "Precio extra del atributo"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value_price_ids
msgid "Attribute Prices"
msgstr "Precios de atributo"

#. module: product
#: model:ir.actions.act_window,name:product.variants_action
#: model:ir.model.fields,field_description:product.field_product_attribute_line_value_ids
#: model:ir.ui.menu,name:product.menu_variants_action
msgid "Attribute Values"
msgstr "Valores de atributo"

#. module: product
#: model:ir.actions.act_window,name:product.attribute_action
#: model:ir.model.fields,field_description:product.field_product_product_attribute_value_ids
#: model:ir.ui.menu,name:product.menu_attribute_action
#: model_terms:ir.ui.view,arch_db:product.product_attribute_value_view_tree
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Attributes"
msgstr "Atributos"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Auxiliary input for portable devices"
msgstr "Entrada auxiliar para dispositivos portátiles"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Auxiliary port lets you connect other audio sources, like an MP3 player"
msgstr ""
"Puerto auxiliar le permite conectar otras fuentes de audio como un "
"reproductor de MP3"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_barcode
#: model:ir.model.fields,field_description:product.field_product_template_barcode
msgid "Barcode"
msgstr "Código de barras"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_base
msgid ""
"Base price for computation. \n"
" Public Price: The base price will be the Sale/public Price. \n"
" Cost Price : The base price will be the cost price. \n"
" Other Pricelist : Computation of the base price based on another Pricelist."
msgstr ""
"Precio base para el cómputo. \n"
" Precio público: El precio base será el precio de venta público. \n"
" Precio de Coste: El precio base será el precio de costo. \n"
" Otros precios: Cálculo del precio base partiendo de otra lista de precios."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_list_price
#: model:ir.model.fields,help:product.field_product_template_list_price
msgid ""
"Base price to compute the customer price. Sometimes called the catalog price."
msgstr ""
"Precio base para calcular el precio de los clientes. A veces llamado precio "
"de catalogo."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_base
msgid "Based on"
msgstr "Basado en"

#. module: product
#: model:product.product,name:product.consu_delivery_03
#: model:product.template,name:product.consu_delivery_03_product_template
msgid "Basic Computer"
msgstr "Computador Basico"

#. module: product
#: model:product.product,name:product.membership_2
#: model:product.template,name:product.membership_2_product_template
#, fuzzy
msgid "Basic Membership"
msgstr "Computador Basico"

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid "Beautiful 7.9‑inch display."
msgstr "Pantalla  hermosa de 7.9\" ."

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "Beautiful widescreen display."
msgstr "Pantalla panorámica hermosa ."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_image
msgid "Big-sized image"
msgstr "Imagen grande"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_uom_factor_inv
msgid "Bigger Ratio"
msgstr "Mayor ratio"

#. module: product
#: selection:product.uom,uom_type:0
msgid "Bigger than the reference Unit of Measure"
msgstr "Más grande que la unidad de medida de referencia"

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_4
msgid "Black"
msgstr "Negro"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Bluetooth connectivity"
msgstr "Conectividad Bluetooth"

#. module: product
#: model:product.product,name:product.product_product_5b
#: model:product.template,name:product.product_product_5b_product_template
msgid "Bose Mini Bluetooth Speaker"
msgstr "Altavoz Bose mini bluetooth"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Bose Mini Bluetooth Speaker."
msgstr "Mini Altavoz Bluetooth Bose"

#. module: product
#: model:product.product,description_sale:product.product_product_5b
#: model:product.template,description_sale:product.product_product_5b_product_template
msgid "Bose's smallest portable Bluetooth speaker"
msgstr "El altavoz bluetooth portátil de Bose más pequeño"

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "Brilliance onscreen. And behind it."
msgstr "Maravillas en la pantalla. Y detrás de él."

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid ""
"Buttons let you quickly play, pause, change songs, or adjust the volume.\n"
"                                    The smooth anodized aluminum design "
"makes iPod nano feel as good as it sounds.\n"
"                                    And iPod nano wouldn’t be iPod nano "
"without gorgeous, hard-to-choose-from color."
msgstr ""
"Botones le permiten rápidamente reproducir, pausar, cambiar de canciones o "
"ajustar el volumen.\n"
"                                    El diseño liso de aluminio anodizado "
"hace el iPod nano tan bueno como parece.\n"
"                                    Y no sería iPod nano iPod nano sin su "
"colección de colores difícil de elegir.."

#. module: product
#: model:ir.model.fields,help:product.field_product_uom_active
msgid ""
"By unchecking the active field you can disable a unit of measure without "
"deleting it."
msgstr ""
"Si el campo activo se desmarca, permite ocultar una unidad de medida sin "
"eliminarla."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_product_price_list
msgid "Calculate Product Price per Unit Based on Pricelist Version."
msgstr ""
"Calcular los precios del producto según unidades para una versión de tarifa."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_rental
#: model:ir.model.fields,field_description:product.field_product_template_rental
msgid "Can be Rent"
msgstr "Puede ser alquilado"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_sale_ok
#: model:ir.model.fields,field_description:product.field_product_template_sale_ok
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Can be Sold"
msgstr "Puede ser vendido"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_product_price_list
msgid "Cancel"
msgstr "Cancelar"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category_type
#: model_terms:ir.ui.view,arch_db:product.product_category_form_view
msgid "Category Type"
msgstr "Tipo categoría"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_category_form_view
msgid "Category name"
msgstr "Nombre de categoría"

#. module: product
#: code:addons/product/pricelist.py:361
#, python-format
msgid "Category: %s"
msgstr "Categoría: %s"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Characteristics"
msgstr "Características"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Charges iPod/iPhone"
msgstr "Carga iPod/iPhone"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid ""
"Charging cradle recharges the battery and serves as a convenient\n"
"                                    home base for your speaker, and it lets "
"you play while it charges."
msgstr ""
"Base de carga recarga la batería y sirve como una base conveniente para el "
"altavoz, y le permite reproducir mientras se carga."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category_child_id
msgid "Child Categories"
msgstr "Categorías hijas"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_uom_categ_form_action
msgid "Click to add a new unit of measure category."
msgstr "Haga click para añadir una nueva categoría de unidades de medida"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_uom_form_action
msgid "Click to add a new unit of measure."
msgstr "Haga click para añadir una nueva unidad de medida."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_pricelist_action2
msgid "Click to create a pricelist."
msgstr "Haga click para crear una tarifa."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_normal_action
#: model_terms:ir.actions.act_window,help:product.product_normal_action_sell
#: model_terms:ir.actions.act_window,help:product.product_template_action
#: model_terms:ir.actions.act_window,help:product.product_template_action_product
#: model_terms:ir.actions.act_window,help:product.product_variant_action
msgid "Click to define a new product."
msgstr "Haga click para definir un nuevo producto."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Codes"
msgstr "Códigos"

#. module: product
#: model:product.attribute,name:product.product_attribute_2
msgid "Color"
msgstr "Color"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_color
#: model:ir.model.fields,field_description:product.field_product_template_color
msgid "Color Index"
msgstr "Índice de colores"

#. module: product
#: model:product.product,description_sale:product.product_product_6
#: model:product.template,description_sale:product.product_product_6_product_template
msgid ""
"Color: White\n"
"Capacity: 16GB\n"
"Connectivity: Wifi\n"
"Beautiful 7.9-inch display\n"
"Over 375,000 apps\n"
"Ultrafast wireless\n"
"iOS7\n"
"            "
msgstr ""
"Color: Blanco\n"
"Capacidad: 16\n"
" Conectividad: Wifi\n"
" Hermosa pantalla de 7.9\"\n"
" Mas de 375.000 apps \n"
"Inalámbrico ultrarrápido\n"
" iOS7"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_company_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_company_id
#: model:ir.model.fields,field_description:product.field_product_product_company_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_company_id
#: model:ir.model.fields,field_description:product.field_product_template_company_id
msgid "Company"
msgstr "Compañía"

#. module: product
#: model:product.public.category,name:product.Components
msgid "Components"
msgstr "Componentes"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Compute Price"
msgstr "Calcular precio"

#. module: product
#: model:product.product,name:product.product_product_16
#: model:product.template,name:product.product_product_16_product_template
msgid "Computer Case"
msgstr "Carcasa de ordenador"

#. module: product
#: model:product.product,name:product.product_product_3
#: model:product.template,name:product.product_product_3_product_template
msgid "Computer SC234"
msgstr "Computador SC234"

#. module: product
#: model:product.public.category,name:product.Computer_all_in_one
msgid "Computer all-in-one"
msgstr "Ordenador todo-en-uno"

#. module: product
#: model:product.public.category,name:product.sub_computers
msgid "Computers"
msgstr "Ordenadores"

#. module: product
#: code:addons/product/product.py:474
#, python-format
msgid "Consumable"
msgstr "Consumible"

#. module: product
#: model:ir.model.fields,help:product.field_product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"La conversión entre las unidades de medidas sólo pueden ocurrir si "
"pertenecen a la misma categoría. La conversión se basará en los ratios "
"establecidos."

#. module: product
#: code:addons/product/product.py:122
#, python-format
msgid ""
"Conversion from Product UoM %s to Default UoM %s is not possible as they "
"both belong to different Category!."
msgstr ""
"¡La conversión de la UdM %s del producto a UdM por defecto %s no es posible "
"debido a que no pertenecen a la misma categoría!"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_history_cost
#: model:ir.model.fields,field_description:product.field_product_product_standard_price
#: model:ir.model.fields,field_description:product.field_product_template_standard_price
#: selection:product.pricelist.item,base:0
msgid "Cost"
msgstr "Coste"

#. module: product
#: model:ir.model.fields,help:product.field_product_product_standard_price
msgid ""
"Cost of the product template used for standard stock valuation in accounting "
"and used as a base price on purchase orders. Expressed in the default unit "
"of measure of the product."
msgstr ""
"Coste para la plantilla de producto usada para la valoración de existencias "
"estándar en contabilidad, y utilizada como precio de referencia en órdenes "
"de compra. Se expresa en la unidad de medida por defecto del producto."

#. module: product
#: model:ir.model.fields,help:product.field_product_template_standard_price
msgid "Cost of the product, in the default unit of measure of the product."
msgstr "Coste del producto, en la unidad de medida por defecto."

#. module: product
#: model:product.product,name:product.service_delivery
#: model:product.template,name:product.service_delivery_product_template
msgid "Cost-plus Contract"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_create_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_line_create_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_price_create_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_value_create_uid
#: model:ir.model.fields,field_description:product.field_product_category_create_uid
#: model:ir.model.fields,field_description:product.field_product_packaging_create_uid
#: model:ir.model.fields,field_description:product.field_product_price_history_create_uid
#: model:ir.model.fields,field_description:product.field_product_price_list_create_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist_create_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_create_uid
#: model:ir.model.fields,field_description:product.field_product_product_create_uid
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_create_uid
#: model:ir.model.fields,field_description:product.field_product_template_create_uid
#: model:ir.model.fields,field_description:product.field_product_uom_categ_create_uid
#: model:ir.model.fields,field_description:product.field_product_uom_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_create_date
#: model:ir.model.fields,field_description:product.field_product_attribute_line_create_date
#: model:ir.model.fields,field_description:product.field_product_attribute_price_create_date
#: model:ir.model.fields,field_description:product.field_product_attribute_value_create_date
#: model:ir.model.fields,field_description:product.field_product_category_create_date
#: model:ir.model.fields,field_description:product.field_product_packaging_create_date
#: model:ir.model.fields,field_description:product.field_product_price_history_create_date
#: model:ir.model.fields,field_description:product.field_product_price_list_create_date
#: model:ir.model.fields,field_description:product.field_product_pricelist_create_date
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_create_date
#: model:ir.model.fields,field_description:product.field_product_product_create_date
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_create_date
#: model:ir.model.fields,field_description:product.field_product_template_create_date
#: model:ir.model.fields,field_description:product.field_product_uom_categ_create_date
#: model:ir.model.fields,field_description:product.field_product_uom_create_date
msgid "Created on"
msgstr "Creado en"

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid ""
"Creating such a stunningly thin design took some equally stunning feats of "
"technological innovation. We refined,re-imagined,or re-engineered everything "
"about iMac from the inside out. The result is an advanced, elegant all-in-"
"one computer that’s as much a work of art as it is state of the art."
msgstr ""
"Crear un diseño increíblemente delgado tomó algunas igualmente "
"impresionantes hazañas de la innovación tecnológica. Hemos refinado, re-"
"imaginado, y re-diseñado todo sobre el iMac desde el interior al exterior. "
"El resultado es un ordenador todo en uno avanzado, elegante que es tanto una "
"obra de arte como lo es el estado del arte."

#. module: product
#: model:ir.model,name:product.model_res_currency
#: model:ir.model.fields,field_description:product.field_product_pricelist_currency_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_currency_id
#: model:ir.model.fields,field_description:product.field_product_product_currency_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_currency_id
#: model:ir.model.fields,field_description:product.field_product_template_currency_id
msgid "Currency"
msgstr "Moneda"

#. module: product
#: model:product.product,name:product.product_product_5
#: model:product.template,name:product.product_product_5_product_template
msgid "Custom Computer (kit)"
msgstr "Equipo Personalizado (kit)"

#. module: product
#: model:product.product,description:product.product_product_27
#: model:product.template,description:product.product_product_27_product_template
msgid "Custom Laptop based on customer's requirement."
msgstr "Portátil personalizado basado en los requisitos del cliente."

#. module: product
#: model:product.product,description:product.product_product_5
#: model:product.template,description:product.product_product_5_product_template
msgid "Custom computer shipped in kit."
msgstr "Equipo personalizado enviado en kit."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_partner_ref
msgid "Customer ref"
msgstr "Ref. cliente"

#. module: product
#: model:product.product,name:product.product_delivery_02
#: model:product.template,name:product.product_delivery_02_product_template
msgid "Datacard"
msgstr "Tarjeta de datos"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_history_datetime
msgid "Date"
msgstr "Fecha"

#. module: product
#: model:product.uom,name:product.product_uom_day
msgid "Day(s)"
msgstr "Día(s)"

#. module: product
#: model:ir.model.fields,help:product.field_product_product_uom_id
#: model:ir.model.fields,help:product.field_product_template_uom_id
msgid "Default Unit of Measure used for all stock operation."
msgstr ""
"Unidad de medida por defecto utilizada para todas las operaciones de stock."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_uom_po_id
#: model:ir.model.fields,help:product.field_product_template_uom_po_id
msgid ""
"Default Unit of Measure used for purchase orders. It must be in the same "
"category than the default unit of measure."
msgstr ""
"Unidad de medida por defecto utilizada para los pedidos de compra. Debe "
"estar en la misma categoría que la unidad de medida por defecto."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_delay
msgid "Delivery Lead Time"
msgstr "Tiempo inicial entrega"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_description
#: model:ir.model.fields,field_description:product.field_product_template_description
msgid "Description"
msgstr "Descripción"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Description for Quotations"
msgstr "Descripción para las ofertas"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Description for Vendors"
msgstr "Descripción para proveedores"

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid "Design. The thinnest iPod ever."
msgstr "Diseño. El iPod más delgado."

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute_sequence
#: model:ir.model.fields,help:product.field_product_attribute_value_sequence
msgid "Determine the display order"
msgstr "Determina el orden de visualización"

#. module: product
#: model:product.public.category,name:product.devices
msgid "Devices"
msgstr "Dispositivos"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_display_name
#: model:ir.model.fields,field_description:product.field_product_attribute_line_display_name
#: model:ir.model.fields,field_description:product.field_product_attribute_price_display_name
#: model:ir.model.fields,field_description:product.field_product_attribute_value_display_name
#: model:ir.model.fields,field_description:product.field_product_category_display_name
#: model:ir.model.fields,field_description:product.field_product_packaging_display_name
#: model:ir.model.fields,field_description:product.field_product_price_history_display_name
#: model:ir.model.fields,field_description:product.field_product_price_list_display_name
#: model:ir.model.fields,field_description:product.field_product_pricelist_display_name
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_display_name
#: model:ir.model.fields,field_description:product.field_product_product_display_name
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_display_name
#: model:ir.model.fields,field_description:product.field_product_template_display_name
#: model:ir.model.fields,field_description:product.field_product_uom_categ_display_name
#: model:ir.model.fields,field_description:product.field_product_uom_display_name
#: model:ir.model.fields,field_description:product.field_report_product_report_pricelist_display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: product
#: model:product.uom,name:product.product_uom_dozen
msgid "Dozen(s)"
msgstr "Docena(s)"

#. module: product
#: model:product.product,description_sale:product.consu_delivery_03
#: model:product.template,description_sale:product.consu_delivery_03_product_template
msgid ""
"Dvorak keyboard \n"
"            left-handed mouse"
msgstr ""
"Teclado Dvorak \n"
"              Ratón para zurdos"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_pricelist_action2
msgid ""
"Each rule include a set of applicability criteria (date range,\n"
"                product category...) and a computation that easily helps to "
"achieve\n"
"                any kind of pricing."
msgstr ""
"Cada regla incluyen un conjunto de criterios de aplicabilidad (rango de "
"fecha,\n"
"                  categoría de producto...) y un cómputo que fácilmente "
"ayuda a conseguir\n"
"                  cualquier tipo de fijación de precios."

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Efficient, high-quality audio"
msgstr "Audio eficiente, de alta calidad"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_date_end
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_date_end
msgid "End Date"
msgstr "Fecha final"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_date_end
msgid "End date for this vendor price"
msgstr "Fecha de finalización para este precio de proveedor"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_date_end
msgid "Ending valid for the pricelist item validation"
msgstr "Conclusión válida para la validación de elemento de lista de precios"

#. module: product
#: constraint:product.category:0
msgid "Error ! You cannot create recursive categories."
msgstr "¡Error! No puede crear categorías recursivas"

#. module: product
#: constraint:product.attribute.line:0
msgid "Error ! You cannot use this attribute with the following value."
msgstr ""

#. module: product
#: constraint:product.product:0
msgid ""
"Error! It is not allowed to choose more than one value for a given attribute."
msgstr ""

#. module: product
#: constraint:product.pricelist.item:0
msgid "Error! The minimum margin should be lower than the maximum margin."
msgstr "¡Error! El margen mínimo debe ser menor que el margen máximo."

#. module: product
#: constraint:product.pricelist.item:0
msgid ""
"Error! You cannot assign the Main Pricelist as Other Pricelist in PriceList "
"Item!"
msgstr ""
"¡Error! ¡No puede asignar la tarifa principal como otra tarifa en el "
"elemento de tarifa!"

#. module: product
#: constraint:res.currency:0
msgid ""
"Error! You cannot define a rounding factor for the company's main currency "
"that is smaller than the decimal precision of 'Account'."
msgstr ""
"¡Error! No puede definir una precisión decimal para la moneda principal de "
"la compañía que sea menor que la precisión decimal de las cuentas."

#. module: product
#: constraint:decimal.precision:0
msgid ""
"Error! You cannot define the decimal precision of 'Account' as greater than "
"the rounding factor of the company's main currency"
msgstr ""
"¡Error! No puede definir una precisión decimal de las cuentas que sea mayor "
"que el factor de redondeo de la moneda actual de la compañía."

#. module: product
#: constraint:product.template:0
msgid ""
"Error: The default Unit of Measure and the purchase Unit of Measure must be "
"in the same category."
msgstr ""
"Error: La unidad de medida por defecto y la unidad de compra deben ser de la "
"misma categoría."

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid ""
"Everything you love about iPad — the beautiful\n"
"                                    screen, fast Colors are vivid and text "
"is sharp on the iPad mini display.\n"
"                                    But what really makes it stand out is "
"its size. At 7.9 inches,\n"
"                                    it’s perfectly sized to deliver an "
"experience every bit as big as iPad."
msgstr ""
"Todo lo que te gusta de iPad, — la hermosa\n"
"                                    pantalla, colores rápidos son vívidos y "
"el texto es fino en la pantalla de iPad mini.\n"
"                                    Pero lo que realmente la hace destacar "
"es su tamaño. 7.9\", \n"
"                                    tiene el tamaño perfecto para ofrecer "
"una experiencia tan grande como el iPad."

#. module: product
#: model:product.product,website_description:product.product_product_4
#: model:product.product,website_description:product.product_product_4b
#: model:product.product,website_description:product.product_product_4c
#: model:product.product,website_description:product.product_product_4d
#: model:product.template,website_description:product.product_product_4_product_template
#: model:product.template,website_description:product.product_product_4b_product_template
#: model:product.template,website_description:product.product_product_4c_product_template
#: model:product.template,website_description:product.product_product_4d_product_template
msgid ""
"Everything you love about iPad — the beautiful\n"
"                                    screen, fast and fluid performance, "
"FaceTime and\n"
"                                    iSight cameras, thousands of amazing "
"apps, 10-hour\n"
"                                    battery life* — is everything you’ll "
"love about\n"
"                                    iPad mini, too. And you can hold it in "
"one hand."
msgstr ""
"Todo lo que te gusta de iPad, la hermosa\n"
"                                  pantalla, rendimiento rápido y fluido, "
"FaceTime y cámaras iSight, miles de aplicaciones sorprendentes, vida de "
"batería\n"
"                                  de 10 horas * — es todo lo que te va a "
"encantar sobre el iPad mini, tambien. Y usted lo puede sostener en una mano."

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid ""
"Everything you love about iPad — the beautiful screen,\n"
"                                        fast and fluid performance, FaceTime "
"and iSight cameras, \n"
"                                        thousands of amazing apps, 10-hour "
"battery life* — is everything\n"
"                                        you’ll love about iPad mini, too. "
"And you can hold it in one hand."
msgstr ""
"Todo lo que te gusta de iPad, la hermosa pantalla, \n"
"                                   rendimiento rápido y fluido, FaceTime y "
"cámaras iSight,\n"
"                                   miles de aplicaciones sorprendentes, vida "
"de batería de 10 horas * — es todo \n"
"                                   lo que te va a encantar sobre el iPad "
"mini, también. Y usted lo puede sostener en una mano."

#. module: product
#: model:product.product,description:product.product_product_2
#: model:product.template,description:product.product_product_2_product_template
msgid "Example of product to invoice based on delivery."
msgstr "Ejemplo de producto a facturar en base a entrega."

#. module: product
#: model:product.product,description:product.service_order_01
#: model:product.template,description:product.service_order_01_product_template
msgid "Example of product to invoice on order."
msgstr "Ejemplo de producto a facturar al pedido."

#. module: product
#: model:product.product,description:product.service_cost_01
#: model:product.template,description:product.service_cost_01_product_template
msgid "Example of products to invoice based on cost."
msgstr "Ejemplo de productos a facturar basado en el coste."

#. module: product
#: model:product.product,description:product.product_product_1
#: model:product.template,description:product.product_product_1_product_template
msgid "Example of products to invoice based on delivery."
msgstr "Ejemplo de productos a facturar se basa en la entrega."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_name
#: model:ir.model.fields,help:product.field_product_pricelist_item_price
msgid "Explicit rule name for this pricelist line."
msgstr "Nombre de regla explícita para esta línea de tarifa."

#. module: product
#: model:product.product,name:product.service_cost_01
#: model:product.template,name:product.service_cost_01_product_template
msgid "External Audit"
msgstr "Auditoría externa"

#. module: product
#: model:product.public.category,name:product.External_Hard_Drive
msgid "External Hard Drive"
msgstr "Disco duro externo"

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid "Fast connections.The world over."
msgstr "Conexiones rápidas. Todo el mundo."

#. module: product
#: selection:product.pricelist.item,compute_price:0
msgid "Fix Price"
msgstr "Precio fijo"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_fixed_price
msgid "Fixed Price"
msgstr "Precio fijo"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_min_quantity
msgid ""
"For the rule to apply, bought/sold quantity must be greater than or equal to "
"the minimum quantity specified in this field.\n"
"Expressed in the default unit of measure of the product."
msgstr ""
"Para que la regla se aplique, la cantidad comprada/vendida debe ser mayor o "
"igual a la cantidad mínima especificada en este campo.\n"
"Expresada en la unidad predeterminada de medida del producto."

#. module: product
#: selection:product.pricelist.item,compute_price:0
msgid "Formula"
msgstr "Fórmula"

#. module: product
#: model:product.product,description_sale:product.product_product_7
#: model:product.template,description_sale:product.product_product_7_product_template
msgid ""
"Frequency: 5Hz to 21kHz\n"
"Impedance: 23 ohms\n"
"Sensitivity: 109 dB SPL/mW\n"
"Drivers: two-way balanced armature\n"
"Cable length: 1065 mm\n"
"Weight: 10.2 grams\n"
"            "
msgstr ""
"Frecuencia: 5Hz a 21kHz \n"
"Impedancia: 23 ohmios \n"
"Sensibilidad: 109 dB SPL/mW\n"
"Controladores: armadura equilibrada bidireccional\n"
"Longitud de Cable: 1065 mm\n"
"Peso: 10.2 gramos"

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "Friendly to the environment."
msgstr "Amigable con el medio ambiente."

#. module: product
#: model:product.product,name:product.product_product_1
#: model:product.template,name:product.product_product_1_product_template
msgid "GAP Analysis Service"
msgstr "Servicio de Análisis GAP"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "General Information"
msgstr "Información general"

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid "Genius. Your own personal DJ."
msgstr "Genius. Tu propio DJ personal."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_packaging_ids
#: model:ir.model.fields,help:product.field_product_template_packaging_ids
msgid ""
"Gives the different ways to package the same product. This has no impact on "
"the picking order and is mainly used if you use the EDI module."
msgstr ""
"Indica las diferentes formas de empaquetar el mismo producto. Esto no tiene "
"ningún impacto en la preparación de albaranes y se utiliza principalmente si "
"utiliza el módulo EDI."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_sequence
msgid ""
"Gives the order in which the pricelist items will be checked. The evaluation "
"gives highest priority to lowest sequence and stops as soon as a matching "
"item is found."
msgstr ""
"Indica el orden en que los elementos de la tarifa serán comprobados. En la "
"evaluación se da máxima prioridad a la secuencia más baja y se detiene tan "
"pronto como se encuentra un elemento coincidente."

#. module: product
#: model:ir.model.fields,help:product.field_product_category_sequence
msgid "Gives the sequence order when displaying a list of product categories."
msgstr ""
"Indica el orden de secuencia cuando se muestra una lista de categorías de "
"producto."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_sequence
#: model:ir.model.fields,help:product.field_product_template_sequence
msgid "Gives the sequence order when displaying a product list"
msgstr "Da la orden de secuencia para mostrar una lista de productos"

#. module: product
#: selection:product.pricelist.item,applied_on:0
msgid "Global"
msgstr "Global"

#. module: product
#: model:product.product,name:product.membership_0
#: model:product.template,name:product.membership_0_product_template
msgid "Gold Membership"
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_24
#: model:product.template,name:product.product_product_24_product_template
msgid "Graphics Card"
msgstr "Tarjeta gráfica"

#. module: product
#: model:product.product,name:product.product_product_17
#: model:product.template,name:product.product_product_17_product_template
msgid "HDD SH-1"
msgstr "HDD SH-1"

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid "Have Genius call the tunes."
msgstr "Haz que Genius reproduzca la música."

#. module: product
#: model:product.public.category,name:product.Headset
msgid "Headset"
msgstr "Auriculares"

#. module: product
#: model:product.product,website_description:product.product_product_7
#: model:product.template,website_description:product.product_product_7_product_template
msgid "Hear, hear."
msgstr "Oye, Oye."

#. module: product
#: model:product.product,description_sale:product.product_product_11
#: model:product.product,description_sale:product.product_product_11b
#: model:product.template,description_sale:product.product_product_11_product_template
#: model:product.template,description_sale:product.product_product_11b_product_template
msgid ""
"Height: 76.5 mm\n"
"Width:  39.6 mm\n"
"Depth:  5.4 mm\n"
"Weight: 31 grams"
msgstr ""
"Altura: 76,5 mm\n"
"Ancho: 39,6 mm\n"
"Profundidad: 5,4 mm\n"
"Peso: 31 gramos"

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "Highly rated designs."
msgstr "Diseños altamente calificados."

#. module: product
#: model:product.uom,name:product.product_uom_hour
msgid "Hour(s)"
msgstr "Hora(s)"

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid ""
"How did we make an already gorgeous widescreen display even better? By "
"making it 75 percent less reflective. And by re-architecting the LCD and "
"moving it right up against the cover glass. So you see your photos, games, "
"movies, and everything else in vivid, lifelike detail."
msgstr ""
"¿Como hicimos una pantalla panorámica ya preciosa aún mejor? Haciéndola 75 "
"por ciento menos reflexiva. Y por volver a diseñar la arquitectura de la "
"pantalla y moverla a la derecha contra el vidrio de la cubierta. Para que "
"ver tus fotos, juegos, películas y todo lo demás en detalle mas vivo y "
"realista."

#. module: product
#: model:ir.model.fields,help:product.field_product_uom_factor_inv
msgid ""
"How many times this Unit of Measure is bigger than the reference Unit of "
"Measure in this category:\n"
"1 * (this unit) = ratio * (reference unit)"
msgstr ""
"Cómo de grande o de pequeña es esta unidad comparada con la unidad de medida "
"de referencia de esta categoría:\n"
"1 * (esta unidad) = ratio * (unidad de referencia)"

#. module: product
#: model:ir.model.fields,help:product.field_product_uom_factor
msgid ""
"How much bigger or smaller this unit is compared to the reference Unit of "
"Measure for this category:\n"
"1 * (reference unit) = ratio * (this unit)"
msgstr ""
"Cómo de grande o de pequeña es esta unidad comparada con la unidad de medida "
"de referencia de esta categoría:\n"
"1 * (unidad de referencia) = ratio * (esta unidad)"

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid "How to get your groove on."
msgstr "Como activar tu chispa."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_id
#: model:ir.model.fields,field_description:product.field_product_attribute_line_id
#: model:ir.model.fields,field_description:product.field_product_attribute_price_id
#: model:ir.model.fields,field_description:product.field_product_attribute_value_id
#: model:ir.model.fields,field_description:product.field_product_category_id
#: model:ir.model.fields,field_description:product.field_product_packaging_id
#: model:ir.model.fields,field_description:product.field_product_price_history_id
#: model:ir.model.fields,field_description:product.field_product_price_list_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_id
#: model:ir.model.fields,field_description:product.field_product_product_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_id
#: model:ir.model.fields,field_description:product.field_product_template_id
#: model:ir.model.fields,field_description:product.field_product_uom_categ_id
#: model:ir.model.fields,field_description:product.field_product_uom_id
#: model:ir.model.fields,field_description:product.field_report_product_report_pricelist_id
msgid "ID"
msgstr "ID (identificación)"

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid "If it's made for iPad, it's made for iPad mini."
msgstr "Si está hecho para el iPad, está hecho para iPad mini."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_active
msgid ""
"If unchecked, it will allow you to hide the pricelist without removing it."
msgstr "Si no está marcado, la tarifa podrá ocultarse sin eliminarla."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_active
#: model:ir.model.fields,help:product.field_product_template_active
msgid ""
"If unchecked, it will allow you to hide the product without removing it."
msgstr "Si no está marcado, permitirá ocultar el producto sin eliminarlo."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_image
msgid "Image"
msgstr "Imagen"

#. module: product
#: model:ir.model.fields,help:product.field_product_product_image
msgid ""
"Image of the product variant (Big-sized image of product template if false). "
"It is automatically resized as a 1024x1024px image, with aspect ratio "
"preserved."
msgstr ""
"Imagen de la variante del producto (Imagen grande de la plantilla del "
"producto en caso de que esté vacía). Se redimensionará automáticamente como "
"una imagen 1024x1024px, manteniendo la proporción."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_image_medium
msgid ""
"Image of the product variant (Medium-sized image of product template if "
"false)."
msgstr ""
"Imagen de la variante del producto (Imagen mediana de la plantilla del "
"producto en caso de que esté vacía)."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_image_small
msgid ""
"Image of the product variant (Small-sized image of product template if "
"false)."
msgstr ""
"Imagen de la variante del producto (Imagen pequeña de la plantilla del "
"producto en caso de que esté vacía)."

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "Individually calibrated for true-to-life color."
msgstr "Calibrado individualmente para color true-to-life."

#. module: product
#: model:ir.model,name:product.model_product_supplierinfo
msgid "Information about a product vendor"
msgstr "Información acerca del proveedor de un producto"

#. module: product
#: model:product.product,website_description:product.product_product_7
#: model:product.template,website_description:product.product_product_7_product_template
msgid ""
"Inside each earpiece is a stainless steel mesh cap that protects the "
"precision acoustic\n"
"                                    components from dust and debris. You can "
"remove the caps for cleaning or replace\n"
"                                    them with an extra set that’s included "
"in the box."
msgstr ""
"Dentro de cada auricular hay una tapa de malla de acero inoxidable que "
"protege los componentes acústicos\n"
"                                        de precisión del polvo y la "
"suciedad. Puede quitar las tapas para la limpieza o reemplazar\n"
"                                         con un juego extra que se incluye "
"en la caja."

#. module: product
#: model:product.category,name:product.product_category_2
msgid "Internal"
msgstr "Interno"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_categ_id
#: model:ir.model.fields,field_description:product.field_product_template_categ_id
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Internal Category"
msgstr "Categoría interna"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_code
#: model:ir.model.fields,field_description:product.field_product_product_default_code
#: model:ir.model.fields,field_description:product.field_product_template_default_code
msgid "Internal Reference"
msgstr "Referencia interna"

#. module: product
#: model:ir.model.fields,help:product.field_product_product_barcode
msgid "International Article Number used for product identification."
msgstr ""
"Número de artículo internacional usado para la identificación de producto."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Inventory"
msgstr "Inventario"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_is_product_variant
#: model:ir.model.fields,field_description:product.field_product_template_is_product_variant
msgid "Is a product variant"
msgstr "Es una variante de producto"

#. module: product
#: model:product.product,website_description:product.product_product_7
#: model:product.template,website_description:product.product_product_7_product_template
msgid "Keep it clean."
msgstr "Mantenerla limpia."

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "Key Features"
msgstr "Características principales"

#. module: product
#: model:product.public.category,name:product.Keyboard_Mouse
msgid "Keyboard / Mouse"
msgstr "Teclado / Ratón"

#. module: product
#: model:product.product,name:product.product_product_27
#: model:product.template,name:product.product_product_27_product_template
msgid "Laptop Customized"
msgstr "Portátil personalizado"

#. module: product
#: model:product.product,name:product.product_product_25
#: model:product.template,name:product.product_product_25_product_template
msgid "Laptop E5023"
msgstr "Portátil E5023"

#. module: product
#: model:product.public.category,name:product.laptops
msgid "Laptops"
msgstr "Portátiles"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute___last_update
#: model:ir.model.fields,field_description:product.field_product_attribute_line___last_update
#: model:ir.model.fields,field_description:product.field_product_attribute_price___last_update
#: model:ir.model.fields,field_description:product.field_product_attribute_value___last_update
#: model:ir.model.fields,field_description:product.field_product_category___last_update
#: model:ir.model.fields,field_description:product.field_product_packaging___last_update
#: model:ir.model.fields,field_description:product.field_product_price_history___last_update
#: model:ir.model.fields,field_description:product.field_product_price_list___last_update
#: model:ir.model.fields,field_description:product.field_product_pricelist___last_update
#: model:ir.model.fields,field_description:product.field_product_pricelist_item___last_update
#: model:ir.model.fields,field_description:product.field_product_product___last_update
#: model:ir.model.fields,field_description:product.field_product_supplierinfo___last_update
#: model:ir.model.fields,field_description:product.field_product_template___last_update
#: model:ir.model.fields,field_description:product.field_product_uom___last_update
#: model:ir.model.fields,field_description:product.field_product_uom_categ___last_update
#: model:ir.model.fields,field_description:product.field_report_product_report_pricelist___last_update
msgid "Last Modified on"
msgstr "Última modificación en"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_line_write_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_price_write_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_value_write_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_write_uid
#: model:ir.model.fields,field_description:product.field_product_category_write_uid
#: model:ir.model.fields,field_description:product.field_product_packaging_write_uid
#: model:ir.model.fields,field_description:product.field_product_price_history_write_uid
#: model:ir.model.fields,field_description:product.field_product_price_list_write_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_write_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist_write_uid
#: model:ir.model.fields,field_description:product.field_product_product_write_uid
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_write_uid
#: model:ir.model.fields,field_description:product.field_product_template_write_uid
#: model:ir.model.fields,field_description:product.field_product_uom_categ_write_uid
#: model:ir.model.fields,field_description:product.field_product_uom_write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_line_write_date
#: model:ir.model.fields,field_description:product.field_product_attribute_price_write_date
#: model:ir.model.fields,field_description:product.field_product_attribute_value_write_date
#: model:ir.model.fields,field_description:product.field_product_attribute_write_date
#: model:ir.model.fields,field_description:product.field_product_category_write_date
#: model:ir.model.fields,field_description:product.field_product_packaging_write_date
#: model:ir.model.fields,field_description:product.field_product_price_history_write_date
#: model:ir.model.fields,field_description:product.field_product_price_list_write_date
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_write_date
#: model:ir.model.fields,field_description:product.field_product_pricelist_write_date
#: model:ir.model.fields,field_description:product.field_product_product_write_date
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_write_date
#: model:ir.model.fields,field_description:product.field_product_template_write_date
#: model:ir.model.fields,field_description:product.field_product_uom_categ_write_date
#: model:ir.model.fields,field_description:product.field_product_uom_write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_delay
msgid ""
"Lead time in days between the confirmation of the purchase order and the "
"receipt of the products in your warehouse. Used by the scheduler for "
"automatic computation of the purchase order planning."
msgstr ""
"Tiempo de espera en días entre la confirmación del pedido de compra y la "
"recepción de los productos en su almacén. Usado por el planificador para el "
"cálculo automático de la planificación de pedidos de compra."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category_parent_left
msgid "Left Parent"
msgstr "Padre izquierdo"

#. module: product
#: model:product.uom.categ,name:product.uom_categ_length
msgid "Length / Distance"
msgstr "Longitud / Distancia"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_attribute_line_ids
msgid "Lines"
msgstr "Líneas"

#. module: product
#: model:product.uom,name:product.product_uom_litre
msgid "Liter(s)"
msgstr "Litro(s)"

#. module: product
#: model:product.product,name:product.consu_delivery_02
#: model:product.template,name:product.consu_delivery_02_product_template
msgid "Little server"
msgstr "Pequeño servidor"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_packaging_ids
#: model:ir.model.fields,field_description:product.field_product_template_packaging_ids
msgid "Logistical Units"
msgstr "Unidades de logística"

#. module: product
#: model:res.groups,name:product.group_uom
msgid "Manage Multiple Units of Measure"
msgstr "Gestionar múltiples unidades de medida"

#. module: product
#: model:res.groups,name:product.group_pricelist_item
msgid "Manage Pricelist Items"
msgstr "Gestionar elementos de tarifa"

#. module: product
#: model:res.groups,name:product.group_stock_packaging
msgid "Manage Product Packaging"
msgstr "Administrar empaquetado del producto"

#. module: product
#: model:ir.model.fields,field_description:product.field_base_config_settings_group_product_variant
#: model:res.groups,name:product.group_product_variant
msgid "Manage Product Variants"
msgstr "Administrar Variantes de Productos"

#. module: product
#: model:res.groups,name:product.group_mrp_properties
msgid "Manage Properties of Product"
msgstr "Gestionar propiedades de los productos"

#. module: product
#: model:res.groups,name:product.group_uos
msgid "Manage Secondary Unit of Measure"
msgstr "Gestionar segunda unidad de medida"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Max. Margin"
msgstr "Margen máx."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_price_max_margin
msgid "Max. Price Margin"
msgstr "Máx. margen de precio"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_image_medium
#: model:ir.model.fields,field_description:product.field_product_template_image_medium
msgid "Medium-sized image"
msgstr "Imagen mediana"

#. module: product
#: model:ir.model.fields,help:product.field_product_template_image_medium
msgid ""
"Medium-sized image of the product. It is automatically resized as a "
"128x128px image, with aspect ratio preserved, only when the image exceeds "
"one of those sizes. Use this field in form views or some kanban views."
msgstr ""
"Imagen mediana del producto. Se redimensionará automáticamente a 128x128p "
"px, preservando el ratio de aspecto, sólo cuando la imagen exceda uno de "
"esos tamaños. Este campo se usa en las vistas de formulario y en algunas "
"vistas kanban."

#. module: product
#: model:product.attribute,name:product.product_attribute_1
msgid "Memory"
msgstr "Memoria"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Min. Margin"
msgstr "Margen mín."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_price_min_margin
msgid "Min. Price Margin"
msgstr "Mín. margen de precio"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_min_quantity
msgid "Min. Quantity"
msgstr "Cantidad mín."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_min_qty
msgid "Minimal Quantity"
msgstr "Cantidad mínima"

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "More energy efficient."
msgstr "Mas eficacia energética"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "More features."
msgstr "Más Características"

#. module: product
#: model:product.product,name:product.product_product_20
#: model:product.template,name:product.product_product_20_product_template
msgid "Motherboard I9P57"
msgstr "Placa base I9P57"

#. module: product
#: model:product.product,name:product.product_product_10
#: model:product.template,name:product.product_product_10_product_template
msgid "Mouse, Optical"
msgstr "Ratón, optico"

#. module: product
#: model:product.product,name:product.product_product_12
#: model:product.template,name:product.product_product_12_product_template
msgid "Mouse, Wireless"
msgstr "Ratón inalámbrico"

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid "Music. It's what beats inside."
msgstr "Música. Es lo que late dentro."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_name
#: model:ir.model.fields,field_description:product.field_product_category_complete_name
#: model:ir.model.fields,field_description:product.field_product_category_name
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_name
#: model:ir.model.fields,field_description:product.field_product_product_name
#: model:ir.model.fields,field_description:product.field_product_template_name
#: model:ir.model.fields,field_description:product.field_product_uom_categ_name
msgid "Name"
msgstr "Nombre"

#. module: product
#: model:product.public.category,name:product.network
msgid "Network"
msgstr "Red"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "New Price ="
msgstr "Nuevo Precio ="

#. module: product
#: selection:product.category,type:0
msgid "Normal"
msgstr "Normal"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Notes"
msgstr "Notas"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "Other Information"
msgstr "Otra información"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_base_pricelist_id
#: selection:product.pricelist.item,base:0
msgid "Other Pricelist"
msgstr "Otra tarifa"

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid "Over 375,000 apps."
msgstr "Más de 375.000 apps."

#. module: product
#: model:ir.model,name:product.model_product_packaging
#: model_terms:ir.ui.view,arch_db:product.product_packaging_form_view
#: model_terms:ir.ui.view,arch_db:product.product_packaging_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Packaging"
msgstr "Empaquetado"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_packaging_name
msgid "Packaging Type"
msgstr "Tipo de empaquetado"

#. module: product
#: model:ir.actions.act_window,name:product.action_packaging_view
msgid "Packagings"
msgstr "Empaquetados"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category_parent_id
msgid "Parent Category"
msgstr "Categoría padre"

#. module: product
#: model:ir.model,name:product.model_res_partner
msgid "Partner"
msgstr "Empresa"

#. module: product
#: model:product.public.category,name:product.Pen_Drive
msgid "Pen Drive"
msgstr "Pen drive"

#. module: product
#: selection:product.pricelist.item,compute_price:0
msgid "Percentage (discount)"
msgstr "Porcentaje (descuento)"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_percent_price
msgid "Percentage Price"
msgstr "Precio Porcentaje "

#. module: product
#: model:product.category,name:product.product_category_5
msgid "Physical"
msgstr "Físico"

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid "Playlists. The perfect mix for every mood."
msgstr "Listas de reproducción. La mezcla perfecta para cada estado de ánimo."

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Plays where you play"
msgstr "Reproduce donde juegas"

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid ""
"Powered by fourth-generation Intel Core processors, this iMac is the fastest "
"yet. Every model in the lineup comes standard with a quad-core Intel Core i5 "
"processor, starting at 2.7GHz and topping out at 3.4GHz."
msgstr ""
"Impulsada por procesadores Intel Core de cuarta generación, este iMac es el "
"más rápido. Cada modelo en la alineación viene estándar con un procesador "
"quad-core Intel Core i5, a partir de 2,7 GHz y llega a 3.4 GHz."

#. module: product
#: model:product.product,name:product.service_order_01
#: model:product.template,name:product.service_order_01_product_template
msgid "Prepaid Consulting"
msgstr "Consulta Prepago"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_price
#: model:ir.model.fields,field_description:product.field_product_product_price
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_price
#: model:ir.model.fields,field_description:product.field_product_template_price
#: model_terms:ir.ui.view,arch_db:product.product_normal_form_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Price"
msgstr "Precio"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Price Computation"
msgstr "Cálculo del precio"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_price_discount
msgid "Price Discount"
msgstr "Descuento precio"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_price_price_extra
msgid "Price Extra"
msgstr "Precio extra"

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute_value_price_extra
msgid ""
"Price Extra: Extra price for the variant with this attribute value on sale "
"price. eg. 200 price extra, 1000 + 200 = 1200."
msgstr ""
"Precio extra: Precio extra para la variante con este valor de atributo en el "
"precio de venta. Por ejemplo, precio extra 200. Precio final variante: 1000 "
"+ 200 = 1200."

#. module: product
#: model:ir.actions.act_window,name:product.action_product_price_list
#: model:ir.model,name:product.model_product_price_list
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.report_pricelist
#: model_terms:ir.ui.view,arch_db:product.view_product_price_list
msgid "Price List"
msgstr "Lista de precios"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_price_round
msgid "Price Rounding"
msgstr "Redondeo precio"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_price_surcharge
msgid "Price Surcharge"
msgstr "Recargo precio"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_template_kanban_view
msgid "Price:"
msgstr "Precio:"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list_price_list
msgid "PriceList"
msgstr "Tarifa"

#. module: product
#: model:ir.actions.report.xml,name:product.action_report_pricelist
#: model:ir.model,name:product.model_product_pricelist
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_pricelist_id
#: model:ir.model.fields,field_description:product.field_product_product_pricelist_id
#: model:ir.model.fields,field_description:product.field_product_template_pricelist_id
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Pricelist"
msgstr "Tarifa"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_applied_on
msgid "Pricelist Item applicable on selected option"
msgstr " Artículo de la lista de precios aplicable en la opción seleccionada"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_ids
#: model:ir.model.fields,field_description:product.field_product_product_item_ids
#: model:ir.model.fields,field_description:product.field_product_template_item_ids
#: model_terms:ir.ui.view,arch_db:product.product_normal_form_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Pricelist Items"
msgstr "Elementos de tarifa"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_name
msgid "Pricelist Name"
msgstr "Nombre tarifa"

#. module: product
#: model:ir.model,name:product.model_product_pricelist_item
msgid "Pricelist item"
msgstr "Elemento de la tarifa"

#. module: product
#: model:ir.actions.act_window,name:product.product_pricelist_action2
#: model:ir.ui.menu,name:product.menu_product_pricelist_action2
#: model:ir.ui.menu,name:product.menu_product_pricelist_main
msgid "Pricelists"
msgstr "Tarifas"

#. module: product
#: model:res.groups,name:product.group_product_pricelist
msgid "Pricelists On Product"
msgstr "Tarifa por producto"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_partner_property_form
msgid "Pricelists are managed on"
msgstr "Las tarifas de precios son gestionadas en"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Pricing"
msgstr "Precio"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_product_price_list
msgid "Print"
msgstr "Imprimir"

#. module: product
#: model:product.public.category,name:product.printer
msgid "Printer"
msgstr "Impresora"

#. module: product
#: model:product.product,name:product.product_product_22
#: model:product.template,name:product.product_product_22_product_template
msgid "Processor Core i5 2.70 Ghz"
msgstr "Procesador core i5 2.70 GHz"

#. module: product
#: model:ir.actions.act_window,name:product.product_normal_action
#: model:ir.model,name:product.model_product_product
#: model:ir.model.fields,field_description:product.field_product_packaging_product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_price_history_product_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_product_id
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_tree_view
#: selection:product.pricelist.item,applied_on:0
#: model:res.request.link,name:product.req_link_product
msgid "Product"
msgstr "Producto"

#. module: product
#: model:ir.model,name:product.model_product_attribute
msgid "Product Attribute"
msgstr "Atributo de producto"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_price_value_id
msgid "Product Attribute Value"
msgstr "Valor del atributo de producto"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_attribute_line_ids
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line_ids
msgid "Product Attributes"
msgstr "Atributos del producto"

#. module: product
#: model:ir.actions.act_window,name:product.product_category_action_form
#: model:ir.ui.menu,name:product.menu_product_category_action_form
#: model_terms:ir.ui.view,arch_db:product.product_category_list_view
#: model_terms:ir.ui.view,arch_db:product.product_category_search_view
msgid "Product Categories"
msgstr "Categorías de productos"

#. module: product
#: model:ir.model,name:product.model_product_category
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_categ_id
msgid "Product Category"
msgstr "Categoría de producto"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_product_manager
#: model:ir.model.fields,field_description:product.field_product_template_product_manager
msgid "Product Manager"
msgstr "Responsable de producto"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Product Name"
msgstr "Nombre del producto"

#. module: product
#: model:ir.model,name:product.model_product_template
#: model:ir.model.fields,field_description:product.field_product_attribute_line_product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_attribute_price_product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_product_product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_product_tmpl_id
#: model_terms:ir.ui.view,arch_db:product.product_search_form_view
msgid "Product Template"
msgstr "Plantilla producto"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_type
#: model:ir.model.fields,field_description:product.field_product_template_type
msgid "Product Type"
msgstr "Tipo de producto"

#. module: product
#: model:ir.model,name:product.model_product_uom
msgid "Product Unit of Measure"
msgstr "Unidad de medida del producto"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_product_id
#: model_terms:ir.ui.view,arch_db:product.product_normal_form_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
#: selection:product.pricelist.item,applied_on:0
msgid "Product Variant"
msgstr "Variantes de producto"

#. module: product
#: model:ir.actions.act_window,name:product.product_normal_action_sell
#: model:ir.actions.act_window,name:product.product_variant_action
#: model:ir.ui.menu,name:product.menu_products
#: model_terms:ir.ui.view,arch_db:product.product_product_tree_view
msgid "Product Variants"
msgstr "Variantes de Producto"

#. module: product
#: model:ir.model,name:product.model_product_uom_categ
msgid "Product uom categ"
msgstr "Categ. UdM de producto"

#. module: product
#: model:ir.actions.act_window,name:product.product_template_action
#: model:ir.actions.act_window,name:product.product_template_action_product
#: model:ir.model.fields,field_description:product.field_product_product_product_variant_ids
#: model:ir.model.fields,field_description:product.field_product_template_product_variant_ids
#: model:ir.ui.menu,name:product.menu_product_template_action
#: model:ir.ui.menu,name:product.prod_config_main
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Products"
msgstr "Productos"

#. module: product
#: model:ir.actions.report.xml,name:product.report_product_label
msgid "Products Labels"
msgstr "Etiquetas de productos"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_search
msgid "Products Price"
msgstr "Precio de los productos"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_tree
msgid "Products Price List"
msgstr "Tarifa de productos"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_search
msgid "Products Price Search"
msgstr "Buscar precio productos"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Products your store in the inventory"
msgstr "Productos que almacena en su inventario"

#. module: product
#: code:addons/product/product.py:824
#, python-format
msgid "Products: "
msgstr "Productos: "

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_lst_price
#: selection:product.pricelist.item,base:0
msgid "Public Price"
msgstr "Precio al público"

#. module: product
#: model:product.pricelist,name:product.list0
msgid "Public Pricelist"
msgstr "Tarifa pública"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_description_purchase
#: model:ir.model.fields,field_description:product.field_product_template_description_purchase
msgid "Purchase Description"
msgstr "Descripción de compra"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_uom_po_id
#: model:ir.model.fields,field_description:product.field_product_template_uom_po_id
msgid "Purchase Unit of Measure"
msgstr "Unidad de medida compra"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_qty
msgid "Quantity"
msgstr "Cantidad"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_packaging_qty
msgid "Quantity by Package"
msgstr "Cantidad por paquete"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list_qty1
msgid "Quantity-1"
msgstr "Cantidad-1"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list_qty2
msgid "Quantity-2"
msgstr "Cantidad-2"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list_qty3
msgid "Quantity-3"
msgstr "Cantidad-3"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list_qty4
msgid "Quantity-4"
msgstr "Cantidad-4"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list_qty5
msgid "Quantity-5"
msgstr "Cantidad-5"

#. module: product
#: model:product.product,name:product.product_product_13
#: model:product.template,name:product.product_product_13_product_template
msgid "RAM SR5"
msgstr "RAM SR5"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_uom_factor
msgid "Ratio"
msgstr "Ratio"

#. module: product
#: selection:product.uom,uom_type:0
msgid "Reference Unit of Measure for this category"
msgstr "Unidad de medida de referencia para esta categoría"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Remote control for power, volume, track seek"
msgstr "Control remoto para encendido, volumen, búsqueda de pista"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category_parent_right
msgid "Right Parent"
msgstr "Padre derecho"

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid ""
"Right from the start, apps made for iPad also work with iPad mini.\n"
"                                    They’re immersive, full-screen apps that "
"let you do almost anything\n"
"                                    you can imagine. And with automatic "
"updates,\n"
"                                    you're always getting the best "
"experience possible."
msgstr ""
"Desde el principio, apps para iPad también trabajan con el iPad mini.\n"
"                                    Son inmersión, apps a pantalla completa "
"que le permiten hacen casi cualquier cosa que puedas imaginar. Y con "
"actualizaciones automáticas, siempre obtendrá la mejor experiencia posible."

#. module: product
#: model:product.product,website_description:product.product_product_4
#: model:product.product,website_description:product.product_product_4b
#: model:product.product,website_description:product.product_product_4c
#: model:product.product,website_description:product.product_product_4d
#: model:product.template,website_description:product.product_product_4_product_template
#: model:product.template,website_description:product.product_product_4b_product_template
#: model:product.template,website_description:product.product_product_4c_product_template
#: model:product.template,website_description:product.product_product_4d_product_template
msgid ""
"Right from the start, there’s a lot to love about\n"
"                                    iPad. It’s simple yet powerful. Thin and "
"light yet\n"
"                                    full-featured. It can do just about "
"everything and\n"
"                                    be just about anything."
msgstr ""
"Desde el principio, hay mucho que gusta de iPad. Es simple pero poderoso. "
"Fina y ligera pero completa. Puede hacer casi todo y ser casi cualquier cosa."

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid ""
"Right from the start, there’s a lot to love about iPad.\n"
"                                   It’s simple yet powerful. Thin and light "
"yet full-\n"
"                                   featured. It can do just about everything "
"and be just\n"
"                                   about anything.And because it’s so easy "
"to use, it’s\n"
"                                   easy to love."
msgstr ""
"Desde el principio, hay mucho que gusta de iPad.\n"
"                                   Es simple pero poderoso. Fino y ligero "
"pero completo todas las funciones. Puede hacer casi todo y ser casi "
"cualquier cosa. Y porque es tan fácil de usar, es fácil amar."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Rounding Method"
msgstr "Método redondeo"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_uom_rounding
msgid "Rounding Precision"
msgstr "Precisión de redondeo"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Sale Conditions"
msgstr "Condiciones de venta"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_description_sale
#: model:ir.model.fields,field_description:product.field_product_template_description_sale
msgid "Sale Description"
msgstr "Descripción de venta"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_list_price
#: model:ir.model.fields,field_description:product.field_product_product_lst_price
#: model:ir.model.fields,field_description:product.field_product_template_list_price
msgid "Sale Price"
msgstr "Precio de venta"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_partner_property_product_pricelist
msgid "Sale Pricelist"
msgstr "Tarifa de venta"

#. module: product
#: model:product.category,name:product.product_category_1
msgid "Saleable"
msgstr "Se puede vender"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Sales"
msgstr "Ventas"

#. module: product
#: model:res.groups,name:product.group_sale_pricelist
msgid "Sales Pricelists"
msgstr "Tarifas de venta"

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid ""
"Say you’re listening to a song you love and you want to stay in the mood.\n"
"                                        Just tap Genius. It finds other "
"songs on iPod nano that go great together\n"
"                                        and makes a Genius playlist for you. "
"For more song combinations\n"
"                                        you wouldn’t have thought of "
"yourself, create Genius Mixes in iTunes\n"
"                                        and sync the ones you like to iPod "
"nano. Then tap Genius Mixes and\n"
"                                        rediscover songs you haven’t heard "
"in a while — or find music you forgot you even had."
msgstr ""
"Digamos que estás escuchando una canción que te gusta y que desea permanecer "
"en el estado de ánimo. \n"
"                                       Sólo tienes que pulsar Genius. "
"Encuentra otras canciones en el iPod nano que van bien juntas \n"
"                                        y crea una lista de reproducción "
"Genius para ti. Para combinaciones de canciones \n"
"                                        que no tendría que pensar en ti "
"mismo, crear mezclas Genius en iTunes \n"
"                                         y sincronizar las que le gusten con "
"el iPod nano. A continuación, toque mezclas Genius y \n"
"                                         redescubrir canciones que no han "
"oído en mucho tiempo - o encontrar la música que usted olvidado que aún "
"tenía."

#. module: product
#: model:product.public.category,name:product.Screen
msgid "Screen"
msgstr "Pantalla"

#. module: product
#: model:ir.model.fields,help:product.field_product_product_categ_id
#: model:ir.model.fields,help:product.field_product_template_categ_id
msgid "Select category for the current product"
msgstr "Seleccione la categoría para el producto actual."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_sequence
#: model:ir.model.fields,field_description:product.field_product_attribute_value_sequence
#: model:ir.model.fields,field_description:product.field_product_category_sequence
#: model:ir.model.fields,field_description:product.field_product_packaging_sequence
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_sequence
#: model:ir.model.fields,field_description:product.field_product_product_sequence
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_sequence
#: model:ir.model.fields,field_description:product.field_product_template_sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: product
#: model:product.product,name:product.consu_delivery_01
#: model:product.public.category,name:product.server
#: model:product.template,name:product.consu_delivery_01_product_template
msgid "Server"
msgstr "Servidor"

#. module: product
#: code:addons/product/product.py:474
#, python-format
msgid "Service"
msgstr "Servicio"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model:product.category,name:product.product_category_3
#: model:product.public.category,name:product.services
msgid "Services"
msgstr "Servicios"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_price_round
msgid ""
"Sets the price so that it is a multiple of this value.\n"
"Rounding is applied after the discount and before the surcharge.\n"
"To have prices that end in 9.99, set rounding 10, surcharge -0.01"
msgstr ""
"Calcula el precio de modo que sea un múltiplo de este valor.\n"
"El redondeo se aplica después del descuento y antes del incremento.\n"
"Para que los precios terminen en 9,99, redondeo 10, incremento -0,01."

#. module: product
#: model:ir.model.fields,field_description:product.field_base_config_settings_company_share_product
msgid "Share product to all companies"
msgstr "Compartir producto a todas las empresas"

#. module: product
#: model:ir.model.fields,help:product.field_base_config_settings_company_share_product
msgid ""
"Share your product to all companies defined in your instance.\n"
" * Checked : Product are visible for every company, even if a company is "
"defined on the partner.\n"
" * Unchecked : Each company can see only its product (product where company "
"is defined). Product not related to a company are visible for all companies."
msgstr ""
"Comparte su producto a todas las empresas definidas en su instance.\n"
"* Marcada: Producto son visibles para todas las empresas , incluso si una "
"empresa se ​​define en el partner.\n"
"* Desactivada: Cada empresa sólo puede ver su producto ( producto donde la "
"empresa se ​​define ). Producto no relacionado con una empresa son visibles "
"para todas las empresas."

#. module: product
#: model:product.product,name:product.membership_1
#: model:product.template,name:product.membership_1_product_template
msgid "Silver Membership"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Sleek, compact design"
msgstr "Diseño elegante y compacto"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_image_small
#: model:ir.model.fields,field_description:product.field_product_template_image_small
msgid "Small-sized image"
msgstr "Imagen pequeña"

#. module: product
#: model:ir.model.fields,help:product.field_product_template_image_small
msgid ""
"Small-sized image of the product. It is automatically resized as a 64x64px "
"image, with aspect ratio preserved. Use this field anywhere a small image is "
"required."
msgstr ""
"Imagen pequeña del producto. Se redimensiona automáticamente a 64x64 px, "
"preservando el ratio de aspecto. Use este campo siempre que se requiera una "
"imagen pequeña."

#. module: product
#: selection:product.uom,uom_type:0
msgid "Smaller than the reference Unit of Measure"
msgstr "Más pequeña que la unidad de medida de referencia"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid ""
"Soft covers are available separately in blue, green or orange. Pick a color "
"to match your style."
msgstr ""
"Cubiertas suaves están disponibles por separado en azul, verde o naranja. "
"Elegir un color para que coincida con tu estilo."

#. module: product
#: model:product.category,name:product.product_category_4
#: model:product.public.category,name:product.Software
msgid "Software"
msgstr "Software"

#. module: product
#: model:product.public.category,name:product.Speakers
msgid "Speakers"
msgstr "Ponentes"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_categ_id
msgid ""
"Specify a product category if this rule only applies to products belonging "
"to this category or its children categories. Keep empty otherwise."
msgstr ""
"Especifique una categoría de producto si esta regla sólo se aplica a los "
"productos pertenecientes a esa categoría o a sus categorías hijas. Déjelo en "
"blanco en caso contrario."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_product_id
msgid ""
"Specify a product if this rule only applies to one product. Keep empty "
"otherwise."
msgstr ""
"Especifique un producto si esta regla sólo se aplica a un producto, Déjelo "
"vacío en otro caso."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_product_tmpl_id
msgid ""
"Specify a template if this rule only applies to one product template. Keep "
"empty otherwise."
msgstr ""
"Especifique una plantilla si esta regla sólo se aplica a una plantilla de "
"producto. Déjelo vacío en caso contrario."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_sale_ok
#: model:ir.model.fields,help:product.field_product_template_sale_ok
msgid "Specify if the product can be selected in a sales order line."
msgstr ""
"Especifique si un producto puede ser seleccionado en un pedido de venta."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_price_surcharge
msgid ""
"Specify the fixed amount to add or substract(if negative) to the amount "
"calculated with the discount."
msgstr ""
"Especifica el importe fijo a añadir o a quitar (si es negativo) al importe "
"calculado con el descuento."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_price_max_margin
msgid "Specify the maximum amount of margin over the base price."
msgstr "Especifique el importe máximo de margen sobre el precio base."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_price_min_margin
msgid "Specify the minimum amount of margin over the base price."
msgstr "Especifique el importe mínimo del margen sobre el precio base."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_date_start
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_date_start
msgid "Start Date"
msgstr "Fecha inicial"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_date_start
msgid "Start date for this vendor price"
msgstr "Fecha inicial para este precio de proveedor"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_date_start
msgid "Starting date for the pricelist item validation"
msgstr "Fecha inicial para la validación del elemento de tarifa"

#. module: product
#: model:ir.actions.act_window,name:product.product_supplierinfo_type_action
msgid "Supplier Pricelist"
msgstr "Tarifa de proveedor"

#. module: product
#: model:product.product,name:product.product_product_2
#: model:product.template,name:product.product_product_2_product_template
msgid "Support Contract (on timesheet)"
msgstr "Contrato de Soporte ( Segun Hoja de Asistencia)"

#. module: product
#: model:product.product,name:product.product_delivery_01
#: model:product.template,name:product.product_delivery_01_product_template
msgid "Switch, 24 ports"
msgstr "Switch de 24 puertos"

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid "Sync to your heart’s content."
msgstr "Sincronizar el contenido de su corazón."

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid ""
"Tap to play your favorite songs. Or entire albums.\n"
"                                    Or everything by one artist. You can "
"even browse by genres or composers.\n"
"                                    Flip through your music: Album art looks "
"great on the bigger screen.\n"
"                                    Or to keep things fresh, give iPod nano "
"a shake and it shuffles to a different song in your music library."
msgstr ""
"Toque para reproducir tus canciones favoritas. O álbumes enteros.\n"
"                                    O todo un artista. Puedes incluso buscar "
"por géneros o compositores.\n"
"                                    Flip a través de su música: Arte del "
"álbum se ve muy bien en la pantalla más grande.\n"
"                                    O mantener las cosas frescas, dando iPod "
"nano un batido y se mezcla a una canción diferente en su biblioteca de "
"música."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_name_template
msgid "Template Name"
msgstr "Nombre de la plantilla"

#. module: product
#: model:product.product,website_description:product.product_product_7
#: model:product.template,website_description:product.product_product_7_product_template
msgid ""
"The Apple In-Ear Headphones deliver a truly immersive sound experience by "
"drastically\n"
"                                    reducing unwanted outside noises. The "
"soft, silicone ear tips fit snugly and comfortably\n"
"                                    in your ear, creating a seal that "
"isolates your music from your surroundings.\n"
"                                    Three different sizes of ear tips are "
"included so you can find a perfect fit for each ear.\n"
"                                    Also included are a convenient carrying "
"case for the ear tips and a cable-control case\n"
"                                    for the headphones themselves."
msgstr ""
"Los Auriculares Apple In-Ear ofrecen una experiencia de sonido realmente "
"envolvente por drásticamente\n"
"                                     la reducción de los ruidos externos no "
"deseados. Los, suaves almohadillas de silicona que se sienta cómodo\n"
"                                     en el oído, la creación de un sello que "
"aísla a su música desde su entorno.\n"
"                                     Tres tamaños diferentes de puntas se "
"incluyen para que pueda encontrar un ajuste perfecto para cada oído.\n"
"                                     También se incluye una funda de "
"transporte para las puntas de las orejas y un cable de casos y controles\n"
"                                     para los propios auriculares."

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid ""
"The Bose® SoundLink® mini is Bose's smallest portable Bluetooth speaker. Its "
"ultra-compact size fits in the \n"
"                                    palm of your hand, yet gives you full, "
"natural sound wirelessly from your iPhone, iPad, or iPod. Grab it and go \n"
"                                    full-featured. It can do just about "
"everything and\n"
"                                    experience music just about anywhere."
msgstr ""
"Bose SoundLink® mini es más pequeño Altavoz portátil de Bluetooth de Bose. "
"Su tamaño ultra compacto cabe en la palma de su mano, pero le da un sonido "
"completo y natural sin cables desde tu iPhone, iPad o iPod. Agarrarlo y vaya "
"completa. Puede hacer casi todo y vivir la música casi en cualquier lugar."

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid ""
"The SoundLink® Mini speaker is small and light enough\n"
"                                        to tuck into your bag. It weighs in "
"at just 1.5 pounds.\n"
"                                        Its low profile lets you place it "
"almost anywhere and\n"
"                                        provides a low center of gravity "
"that makes it nearly\n"
"                                        impossible to tip over."
msgstr ""
"El SoundLink® Mini altavoz es pequeño y ligero meter en su bolso. Pesa sólo "
"1,5 libras.\n"
"                                        Su bajo perfil le permite colocar "
"casi en cualquier lugar y ofrece un bajo centro de gravedad que hace que sea "
"casi imposible de volcar."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid ""
"The computed price is expressed in the default Unit of Measure of the "
"product."
msgstr ""
"El precio calculado se expresa en la unidad de medida por defecto del "
"producto."

#. module: product
#: model:ir.model.fields,help:product.field_product_uom_rounding
msgid ""
"The computed quantity will be a multiple of this value. Use 1.0 for a Unit "
"of Measure that cannot be further split, such as a piece."
msgstr ""
"La cantidad calculada será un múltiplo de este valor. Use 1.0 para una "
"unidad de medida que no puede ser dividida, como una pieza."

#. module: product
#: sql_constraint:product.uom:0
msgid "The conversion ratio for a unit of measure cannot be 0!"
msgstr "¡El ratio de conversión para una unidad de medida no puede ser 0!"

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "The desktop. In its most advanced form ever"
msgstr "El escritorio. En su forma más avanzada jamás"

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging_sequence
msgid "The first in the sequence is the default one."
msgstr "El primero en la secuencia es el aplicado por defecto."

#. module: product
#: model:product.product,website_description:product.product_product_4
#: model:product.product,website_description:product.product_product_4b
#: model:product.product,website_description:product.product_product_4c
#: model:product.product,website_description:product.product_product_4d
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_4_product_template
#: model:product.template,website_description:product.product_product_4b_product_template
#: model:product.template,website_description:product.product_product_4c_product_template
#: model:product.template,website_description:product.product_product_4d_product_template
#: model:product.template,website_description:product.product_product_6_product_template
msgid "The full iPad experience."
msgstr "La experiencia iPad completo."

#. module: product
#: model:product.product,website_description:product.product_product_9
#: model:product.template,website_description:product.product_product_9_product_template
msgid ""
"The incredibly thin Apple Wireless Keyboard uses Bluetooth technology,\n"
"                                    which makes it compatible with iPad. And "
"you’re free to type wherever\n"
"                                    you like — with the keyboard in front of "
"your iPad or on your lap."
msgstr ""
"El increíblemente delgado teclado inalámbrico de Apple utiliza tecnología "
"Bluetooth, que lo hace compatible con el iPad. Y eres libre escribir donde "
"desee, con el teclado delante de tu iPad o en tu regazo."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_min_qty
msgid ""
"The minimal quantity to purchase from this vendor, expressed in the vendor "
"Product Unit of Measure if not any, in the default unit of measure of the "
"product otherwise."
msgstr ""
"Cantidad mínima a comprar de este proveedor, expresada en la unidad de "
"producto del proveedor si existe o, en otro caso, en la unidad de medida por "
"defecto del producto."

#. module: product
#: code:addons/product/product.py:332
#, python-format
msgid ""
"The operation cannot be completed:\n"
"You are trying to delete an attribute value with a reference on a product "
"variant."
msgstr ""
"La operación no se puede completar:\n"
"Está intentando eliminar un valor de atributo que hace referencia a una "
"variante de producto."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_price
msgid "The price to purchase a product"
msgstr "El precio al que se compra un producto"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_normal_action
#: model_terms:ir.actions.act_window,help:product.product_normal_action_sell
#: model_terms:ir.actions.act_window,help:product.product_variant_action
msgid ""
"The product form contains information to simplify the sale\n"
"                process: price, notes in the quotation, accounting data,\n"
"                procurement methods, etc."
msgstr ""
"El formulario de producto contiene información para simplificar el proceso "
"de venta: precio, notas del presupuesto, información contable, métodos de "
"aprovisionamiento, etc..."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_template_action
msgid ""
"The product form contains information to simplify the sale process: price, "
"notes in the quotation, accounting data, procurement methods, etc."
msgstr ""
"El formulario de producto contiene información para simplificar el proceso "
"de venta: precio, notas del presupuesto, información contable, métodos de "
"aprovisionamiento, etc..."

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid ""
"The rechargeable lithium-ion battery delivers up to seven hours of "
"playtime.\n"
"                                    And at home, you can listen even longer—"
"the charging cradle lets\n"
"                                    you listen while it charges."
msgstr ""
"La batería recargable de iones de litio proporciona hasta siete horas de "
"recreo.\n"
"                                    Y en casa, puedes escuchar más, la carga "
"base te permite escuchar mientras se carga."

#. module: product
#: model:product.product,description_sale:product.product_product_9
#: model:product.template,description_sale:product.product_product_9_product_template
msgid ""
"The sleek aluminium Apple Wireless Keyboard.\n"
"            "
msgstr ""
"El teclado inalámbrico de aluminio de Apple.\n"
"            "

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid ""
"The speaker has a range of about 30 feet, so you can enjoy\n"
"                                    the sound you want without wires. It "
"pairs easily with your\n"
"                                    smartphone, iPad® or other Bluetooth "
"device.\n"
"                                    And it remembers the most recent six "
"devices you've used,\n"
"                                    so reconnecting is even simpler."
msgstr ""
"El altavoz tiene un rango de unos 30 pies, para que pueda disfrutar el "
"sonido que desee sin necesidad de cables. Combina fácilmente con el teléfono "
"inteligente, iPad® o dispositivo Bluetooth.\n"
"                                    Y recuerda los seis dispositivos más "
"recientes que has usado, para volver a conectar es incluso más simple."

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging_qty
msgid "The total number of products you can put by pallet or box."
msgstr "El número total de productos que puede poner por palet o caja."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_volume
#: model:ir.model.fields,help:product.field_product_template_volume
msgid "The volume in m3."
msgstr "El volumen en m3."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_weight
#: model:ir.model.fields,help:product.field_product_template_weight
msgid "The weight of the contents in Kg, not including any packaging, etc."
msgstr "El peso del contenido en Kg, sin incluir empaquetado, etc..."

#. module: product
#: model:product.product,website_description:product.product_product_4
#: model:product.product,website_description:product.product_product_4b
#: model:product.product,website_description:product.product_product_4c
#: model:product.product,website_description:product.product_product_4d
#: model:product.template,website_description:product.product_product_4_product_template
#: model:product.template,website_description:product.product_product_4b_product_template
#: model:product.template,website_description:product.product_product_4c_product_template
#: model:product.template,website_description:product.product_product_4d_product_template
msgid "There is less of it, but no less to it."
msgstr "Hay menos, pero no menos a él."

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid "There's less of it, but no less to it."
msgstr "Hay menos, pero no menos a él."

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid ""
"There’s another way to get a good mix of music on iPod: Let Genius do the "
"work.\n"
"                                        Activate Genius in iTunes on your "
"computer, and it automatically finds songs that sound\n"
"                                        great together. Then it creates "
"Genius Mixes, which you can easily sync to your iPod.\n"
"                                        It’s the perfect way to rediscover "
"songs you haven’t listened to in forever."
msgstr ""
"Hay otra manera para obtener una buena mezcla de música en el iPod: genio "
"que haga el trabajo.\n"
"                                        Activar Genius en iTunes en su "
"computadora, y automáticamente encuentra canciones que suenan grandes "
"juntos. Luego crea mezclas Genius, que fácilmente puede sincronizar a tu "
"iPod.\n"
"                                        Es la manera perfecta para "
"redescubrir canciones que no has escuchado en forever."

#. module: product
#: sql_constraint:product.attribute.value:0
msgid "This attribute value already exists !"
msgstr "Este valor de atributo ya existe"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_product_uom
msgid "This comes from the product form."
msgstr "Esto proviene del formulario del producto"

#. module: product
#: model:ir.model.fields,help:product.field_product_product_image_variant
msgid ""
"This field holds the image used as image for the product variant, limited to "
"1024x1024px."
msgstr ""
"Este campo contiene la imagen usada como imagen de la variante del producto, "
"limitada a 1024x1024px."

#. module: product
#: model:ir.model.fields,help:product.field_product_template_image
msgid ""
"This field holds the image used as image for the product, limited to "
"1024x1024px."
msgstr ""
"Este campo contiene la imagen usada para el producto, limitada a 1024x1024 "
"px."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_qty
msgid "This is a quantity which is converted into Default Unit of Measure."
msgstr ""
"Ésta es una cantidad que se convierte en la unidad de medida por defecto."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_price_extra
msgid "This is the sum of the extra price of all attributes"
msgstr "Ésta es la suma de los precios extra de todos los atributos"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "This note will be displayed on requests for quotation."
msgstr "Esta nota se mostrará en las peticiones de presupuesto."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "This note will be displayed on the quotations."
msgstr "Esta nota se mostrará en los presupuestos."

#. module: product
#: model:ir.model.fields,help:product.field_res_partner_property_product_pricelist
msgid ""
"This pricelist will be used, instead of the default one, for sales to the "
"current partner"
msgstr ""
"Esta tarifa se utilizará, en lugar de la por defecto, para las ventas de la "
"empresa actual."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_product_code
msgid ""
"This vendor's product code will be used when printing a request for "
"quotation. Keep empty to use the internal one."
msgstr ""
"El código de producto de proveedor se usará al imprimir una solicitud de "
"presupuesto. Déjelo vacío para usar el código interno."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_product_name
msgid ""
"This vendor's product name will be used when printing a request for "
"quotation. Keep empty to use the internal one."
msgstr ""
"El nombre de producto del proveedor se usará al imprimir una solicitud de "
"presupuesto. Déjelo vacío para usar el nombre interno."

#. module: product
#: model:product.product,website_description:product.product_product_7
#: model:product.template,website_description:product.product_product_7_product_template
msgid "Two is better than one."
msgstr "Dos mejor que uno."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_uom_uom_type
msgid "Type"
msgstr "Tipo"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid ""
"USB port allows for software update to ensure ongoing Bluetooth device "
"compatibility"
msgstr ""
"Puerto USB permite la actualización de software asegurar la continua "
"compatibilidad con dispositivos Bluetooth"

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid "Ultrafast wireless."
msgstr "WIFI ultrarrápida."

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "Ultrathin design"
msgstr "Diseño ultrafino"

#. module: product
#: model:product.uom.categ,name:product.product_uom_categ_unit
msgid "Unit"
msgstr "Unidad"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_uom_id
#: model:ir.model.fields,field_description:product.field_product_template_uom_id
#: model:ir.model.fields,field_description:product.field_product_uom_name
msgid "Unit of Measure"
msgstr "Unidad de medida"

#. module: product
#: model:ir.actions.act_window,name:product.product_uom_categ_form_action
#: model:ir.ui.menu,name:product.menu_product_uom_categ_form_action
msgid "Unit of Measure Categories"
msgstr "Categorías de las unidades de medida"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_uom_category_id
msgid "Unit of Measure Category"
msgstr "Categoría de unidades de medida"

#. module: product
#: model:product.uom,name:product.product_uom_unit
msgid "Unit(s)"
msgstr "Unidad(es)"

#. module: product
#: model:ir.actions.act_window,name:product.product_uom_form_action
#: model:ir.ui.menu,name:product.menu_product_uom_form_action
#: model:ir.ui.menu,name:product.next_id_16
#: model_terms:ir.ui.view,arch_db:product.product_uom_form_view
#: model_terms:ir.ui.view,arch_db:product.product_uom_tree_view
msgid "Units of Measure"
msgstr "Unidades de medida"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_uom_categ_form_view
msgid "Units of Measure categories"
msgstr "Categorías de unidades de medida"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_uom_categ_form_action
msgid ""
"Units of measure belonging to the same category can be\n"
"                converted between each others. For example, in the category\n"
"                <i>'Time'</i>, you will have the following units of "
"measure:\n"
"                Hours, Days."
msgstr ""
"Pueden convertir unidades de medida pertenecientes a la misma categoría "
"entre unos y otros. Por ejemplo, en la categoría <i>'Tiempo'</i>, tendrá las "
"siguientes unidades de medida: horas, días."

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Universal iPod docking station fits most iPod/iPhone models"
msgstr ""
"Estación de conexión iPod universal adapta a mayoría de los modelos de iPod"

#. module: product
#: model:product.product,website_description:product.product_product_7
#: model:product.template,website_description:product.product_product_7_product_template
msgid ""
"Unlike many small headphones, each earpiece of the Apple In-Ear Headphones\n"
"                                    contains two separate high-performance "
"drivers — a woofer to handle bass and\n"
"                                    mid-range sounds and a tweeter for high-"
"frequency audio. These dedicated\n"
"                                    drivers help ensure accurate, detailed "
"sound across the entire sonic spectrum.\n"
"                                    The result: you’re immersed in the music "
"and hear details you never knew existed.\n"
"                                    Even when listening to an old favorite, "
"you may feel like you’re hearing it for the first time."
msgstr ""
"A diferencia de muchos auriculares pequeños, cada auricular del Apple In-Ear "
"contiene dos conductores de alto desempeño separadas - un woofer de manejar "
"los sonidos graves y de gama media y un altavoz de agudos para audio de alta "
"frecuencia. Estos controladores dedicados ayudan a garantizar un sonido "
"precisa y detallada a través de todo el espectro sonoro. El resultado: estás "
"inmerso en la música y escuchar detalles que no sabía que existían. Incluso "
"cuando se escucha a un viejo favorito, se puede sentir como si estuviera "
"oyendo por primera vez."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "Validity"
msgstr "Validez"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value_name
msgid "Value"
msgstr "Valor"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value_ids
msgid "Values"
msgstr "Valores"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_price_extra
msgid "Variant Extra Price"
msgstr "Precio extra de la variante"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_image_variant
msgid "Variant Image"
msgstr "Imagen de la variante"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Variant Information"
msgstr "Información variante"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Variant Prices"
msgstr "Precios de las variantes"

#. module: product
#: model:ir.actions.act_window,name:product.product_attribute_value_action
#: model_terms:ir.ui.view,arch_db:product.attribute_tree_view
#: model_terms:ir.ui.view,arch_db:product.variants_tree_view
msgid "Variant Values"
msgstr "Valores de las variantes"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value_product_ids
#: model_terms:ir.ui.view,arch_db:product.product_template_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_template_only_form_view
msgid "Variants"
msgstr "Variantes"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_name
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "Vendor"
msgstr "Vendedor"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_tree_view
msgid "Vendor Information"
msgstr "Información del proveedor"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_product_code
msgid "Vendor Product Code"
msgstr "Código de producto del proveedor"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_product_name
msgid "Vendor Product Name"
msgstr "Nombre del producto del proveedor"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_product_uom
msgid "Vendor Unit of Measure"
msgstr "Unidad de medida del proveedor"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_name
msgid "Vendor of this product"
msgstr "Proveedor para este producto"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_seller_ids
#: model:ir.model.fields,field_description:product.field_product_template_seller_ids
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Vendors"
msgstr "Vendedores"

#. module: product
#: selection:product.category,type:0
msgid "View"
msgstr "Vista"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_volume
#: model:ir.model.fields,field_description:product.field_product_template_volume
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model:product.uom.categ,name:product.product_uom_categ_vol
msgid "Volume"
msgstr "Volumen"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Volume control on main system"
msgstr "Control de volumen de sistema principal"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid ""
"Wall charger can be plugged into the cradle or directly into the speaker"
msgstr ""
"Cargador de pared puede ser enchufado en la cuna o directamente en el altavoz"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_warranty
#: model:ir.model.fields,field_description:product.field_product_template_warranty
msgid "Warranty"
msgstr "Garantía"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_weight
#: model:ir.model.fields,field_description:product.field_product_template_weight
#: model:product.uom.categ,name:product.product_uom_categ_kgm
msgid "Weight"
msgstr "Peso"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Weights"
msgstr "Pesos"

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid "When one playlist isn’t enough."
msgstr "Cuando una lista de reproducción no es suficiente."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_product_id
msgid ""
"When this field is filled in, the vendor data will only apply to the variant."
msgstr ""
"Cuando llena este campo, los datos del proveedor aplicaran sólo a la "
"variante."

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_3
msgid "White"
msgstr "Blanco"

#. module: product
#: model:product.product,website_description:product.product_product_4
#: model:product.product,website_description:product.product_product_4b
#: model:product.product,website_description:product.product_product_4c
#: model:product.product,website_description:product.product_product_4d
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_4_product_template
#: model:product.template,website_description:product.product_product_4b_product_template
#: model:product.template,website_description:product.product_product_4c_product_template
#: model:product.template,website_description:product.product_product_4d_product_template
#: model:product.template,website_description:product.product_product_6_product_template
msgid "Why you'll love an iPad."
msgstr "Por qué te va a encantar un iPad."

#. module: product
#: model:product.attribute,name:product.product_attribute_3
msgid "Wi-Fi"
msgstr "Red inalámbrica"

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid ""
"With advanced Wi‑Fi that’s up to twice as fast as\n"
"                                   any previous-generation iPad and access "
"to fast\n"
"                                   cellular data networks around the world, "
"iPad mini\n"
"                                   lets you download content, stream video,\n"
"                                   and browse the web at amazing speeds."
msgstr ""
"Con avanzadas Wi-Fi que es hasta dos veces tan rápido como cualquier "
"generación anterior iPad y acceso a redes de datos móviles rápidamente "
"alrededor del mundo, iPad mini le permite descargar contenido, stream de "
"video y navegar por la web a velocidades increíbles."

#. module: product
#: model:ir.model.fields,help:product.field_base_config_settings_group_product_variant
msgid ""
"Work with product variant allows you to define some variant of the same "
"products, an ease the product management in the ecommerce for example"
msgstr ""
"Trabaja con variante de producto le permite definir alguna variante de los "
"mismos productos , una facilidad de la gestión de productos en el comercio "
"electrónico , por ejemplo."

#. module: product
#: model:product.uom.categ,name:product.uom_categ_wtime
msgid "Working Time"
msgstr "Horario de trabajo"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_uom_form_action
msgid ""
"You must define a conversion rate between several Units of\n"
"                Measure within the same category."
msgstr ""
"Debe definir un ratio de conversión entre varias unidades de\n"
"medida dentro de la misma categoría."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_normal_action
#: model_terms:ir.actions.act_window,help:product.product_normal_action_sell
#: model_terms:ir.actions.act_window,help:product.product_variant_action
msgid ""
"You must define a product for everything you sell, whether it's\n"
"                a physical product, a consumable or a service you offer to\n"
"                customers."
msgstr ""
"Debe definir un producto para todo lo que venda, ya sea un producto\n"
"físico, un consumible o servicios que usted subcontrate."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_template_action
#, fuzzy
msgid ""
"You must define a product for everything you sell, whether it's a physical "
"product, a consumable or a service you offer to customers."
msgstr ""
"Debe definir un producto para todo lo que venda, ya sea un producto físico, "
"un consumible o servicios que usted subcontrate."

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid ""
"You probably have multiple playlists in iTunes on your computer.\n"
"                                        One for your commute. One for the "
"gym. Sync those playlists\n"
"                                        to iPod, and you can play the "
"perfect mix for whatever\n"
"                                        mood strikes you. VoiceOver tells "
"you the name of each playlist,\n"
"                                        so it’s easy to switch between them "
"and find the one you want without looking."
msgstr ""
"Probablemente tienes varias listas de reproducción en iTunes en su "
"computadora.\n"
"                                        Uno para su viaje. Uno para el "
"gimnasio. Sincronizar las listas de reproducción iPod, y puede jugar la "
"mezcla perfecta para cualquier estado de ánimo te golpea. VoiceOver te dice "
"el nombre de cada lista de reproducción, por lo que es fácil de cambiar "
"entre ellas y encontrar el que quieras sin mirar."

#. module: product
#: model:product.product,name:product.product_order_01
#: model:product.template,name:product.product_order_01_product_template
msgid "Zed+ Antivirus"
msgstr "Antivirus Zed+"

#. module: product
#: model:ir.model,name:product.model_base_config_settings
msgid "base.config.settings"
msgstr "base.config.settings"

#. module: product
#: model:product.uom,name:product.product_uom_cm
msgid "cm"
msgstr "cm"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "days"
msgstr "días"

#. module: product
#: model:ir.model,name:product.model_decimal_precision
msgid "decimal.precision"
msgstr "Precision decimal"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_category_form_view
msgid "e.g. Lamps"
msgstr "Ej. Lámparas "

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "e.g. Odoo Enterprise Susbcription"
msgstr "Ej. Suscripción Odoo  Enterprise "

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "e.g. USD Retailers"
msgstr "Ej. Minoristas USD"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_uom_form_view
msgid "e.g: 1 * (reference unit) = ratio * (this unit)"
msgstr "Por ejemplo: 1* (unidad de referencia) = ratio * (esta unidad)"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_uom_form_view
msgid "e.g: 1 * (this unit) = ratio * (reference unit)"
msgstr "Por ejemplo: 1 * (esta unidad) = ratio * (unidad de referencia)"

#. module: product
#: model:product.uom,name:product.product_uom_floz
msgid "fl oz"
msgstr "fl oz"

#. module: product
#: model:product.uom,name:product.product_uom_foot
msgid "foot(ft)"
msgstr "pie(s)"

#. module: product
#: model:product.uom,name:product.product_uom_gal
msgid "gal(s)"
msgstr "galón(es)"

#. module: product
#: model:product.product,name:product.product_product_8
#: model:product.template,name:product.product_product_8_product_template
msgid "iMac"
msgstr "iMac"

#. module: product
#: model:product.product,name:product.product_product_6
#: model:product.template,name:product.product_product_6_product_template
msgid "iPad Mini"
msgstr "iPad Mini"

#. module: product
#: model:product.product,name:product.product_product_4
#: model:product.product,name:product.product_product_4b
#: model:product.product,name:product.product_product_4c
#: model:product.product,name:product.product_product_4d
#: model:product.template,name:product.product_product_4_product_template
#: model:product.template,name:product.product_product_4b_product_template
#: model:product.template,name:product.product_product_4c_product_template
#: model:product.template,name:product.product_product_4d_product_template
msgid "iPad Retina Display"
msgstr "iPad con pantalla Retina"

#. module: product
#: model:product.product,name:product.product_product_11
#: model:product.product,name:product.product_product_11b
#: model:product.template,name:product.product_product_11_product_template
#: model:product.template,name:product.product_product_11b_product_template
msgid "iPod"
msgstr "iPod"

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid ""
"iTunes on your Mac or PC makes it easy to load up\n"
"                                        your iPod. Just choose the "
"playlists, audiobooks,\n"
"                                        podcasts, and other audio files you "
"want, then sync."
msgstr ""
"iTunes en tu Mac o PC es fácil de cargar\n"
"                                  tu iPod. Sólo tienes que elegir las listas "
"de reproducción, audiolibros, \n"
"                                  podcasts y otros archivos de audio que "
"quieras, luego sincronizar."

#. module: product
#: model:product.uom,name:product.product_uom_inch
msgid "inch(es)"
msgstr "pulgada(s)"

#. module: product
#: model:product.uom,name:product.product_uom_kgm
msgid "kg"
msgstr "kg"

#. module: product
#: model:product.uom,name:product.product_uom_km
msgid "km"
msgstr "km"

#. module: product
#: model:product.uom,name:product.product_uom_lb
msgid "lb(s)"
msgstr "libra(s)"

#. module: product
#: model:product.uom,name:product.product_uom_mile
msgid "mile(s)"
msgstr "milla(s)"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "months"
msgstr "meses"

#. module: product
#: model:product.uom,name:product.product_uom_oz
msgid "oz(s)"
msgstr "onza(s)"

#. module: product
#: model:ir.model,name:product.model_product_attribute_line
msgid "product.attribute.line"
msgstr "product.attribute.line"

#. module: product
#: model:ir.model,name:product.model_product_attribute_price
msgid "product.attribute.price"
msgstr "product.attribute.price"

#. module: product
#: model:ir.model,name:product.model_product_attribute_value
msgid "product.attribute.value"
msgstr "product.attribute.value"

#. module: product
#: model:ir.model,name:product.model_product_price_history
msgid "product.price.history"
msgstr "product.price.history"

#. module: product
#: model:product.uom,name:product.product_uom_qt
msgid "qt"
msgstr "cuarto(s) de galón"

#. module: product
#: model:product.product,description_sale:product.consu_delivery_02
#: model:product.template,description_sale:product.consu_delivery_02_product_template
msgid ""
"raid 1 \n"
"            512ECC ram"
msgstr ""
"raid 1 \n"
"            512ECC ram"

#. module: product
#: model:product.product,description_sale:product.consu_delivery_01
#: model:product.template,description_sale:product.consu_delivery_01_product_template
msgid ""
"raid 10 \n"
"            2048ECC ram"
msgstr ""
"raid 10 \n"
"            2048ECC ram"

#. module: product
#: model:ir.model,name:product.model_report_product_report_pricelist
msgid "report.product.report_pricelist"
msgstr "report.product.report_pricelist"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_partner_property_form
msgid "the parent company"
msgstr "La empresa matriz"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "the product template"
msgstr "la plantilla de producto"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "to"
msgstr "a"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_history_company_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_compute_price
msgid "unknown"
msgstr "desconocido"

#~ msgid ""
#~ "17\" Monitor\n"
#~ "6GB RAM\n"
#~ "Hi-Speed 234Q Processor\n"
#~ "QWERTY keyboard"
#~ msgstr ""
#~ "Monitor 17\"\n"
#~ "6 GB RAM\n"
#~ "Procesador 234Q de alta velocidad\n"
#~ "Teclado QWERTY"

#~ msgid "30m RJ45 wire"
#~ msgstr "Cable RJ45 30m"

#~ msgid "8-port Switch"
#~ msgstr "Conmutador de 8 puertos"

#~ msgid "Action Needed"
#~ msgstr "Necesaria acción"

#~ msgid "All in one hi-speed printer with fax and scanner."
#~ msgstr "Impresora multi-función de alta velocidad con fax y escáner."

#~ msgid "Blank CD"
#~ msgstr "CD virgen"

#~ msgid "Blank DVD-RW"
#~ msgstr "DVD-RW virgen"

#~ msgid "Date of the last message posted on the record."
#~ msgstr "Fecha del último mensaje publicado en el registro."

#~ msgid "Dell Inspiron Laptop without OS"
#~ msgstr "Laptop Dell Inspiron sin Sistema Operativo"

#~ msgid "Desktop Computer Table"
#~ msgstr "Mesa de ordenador de sobremesa"

#~ msgid "Desktop Lamp"
#~ msgstr "Lámpara de escritorio"

#~ msgid "End of Lifecycle"
#~ msgstr "Fin del ciclo de vida"

#~ msgid "Ergonomic Mouse"
#~ msgstr "Ratón ergonómico"

#~ msgid "External Hard disk"
#~ msgstr "Disco duro externo"

#~ msgid "Followers"
#~ msgstr "Seguidores"

#~ msgid "Followers (Channels)"
#~ msgstr "Seguidores (Canales)"

#~ msgid "Followers (Partners)"
#~ msgstr "Seguidores (Empresas)"

#~ msgid "Full featured image editing software."
#~ msgstr "Software de edición de imágenes completo"

#~ msgid "GrapWorks Software"
#~ msgstr "Software GrapWorks"

#~ msgid "Gross Weight"
#~ msgstr "Peso bruto"

#~ msgid "HDD SH-2 (mto)"
#~ msgstr "Disco duro SH-2 (mto)"

#~ msgid "HDD on Demand"
#~ msgstr "Disco duro (HDD) bajo demanda"

#~ msgid ""
#~ "Hands free headset for laptop PC with in-line microphone and headphone "
#~ "plug."
#~ msgstr ""
#~ "Auriculares manos libres para portátil con conexión para micrófono y "
#~ "auricular."

#~ msgid "Headset USB"
#~ msgstr "Auriculares USB"

#~ msgid "Headset for laptop PC with USB connector."
#~ msgstr "Auriculares para portátil con conector USB."

#~ msgid "Headset standard"
#~ msgstr "Auriculares estándar"

#~ msgid "If checked new messages require your attention."
#~ msgstr "Si está marcado, hay nuevos mensajes que requieren su atención"

#~ msgid "If checked, new messages require your attention."
#~ msgstr "Si está marcado, hay nuevos mensajes que requieren su atención."

#~ msgid "In Development"
#~ msgstr "En desarrollo"

#~ msgid "Ink Cartridge"
#~ msgstr "Cartucho de tinta"

#~ msgid "Is Follower"
#~ msgstr "Es un seguidor"

#~ msgid "Laptop S3450"
#~ msgstr "Portátil S3450"

#~ msgid "Last Message Date"
#~ msgstr "Fecha del último mensaje"

#~ msgid "Linutop"
#~ msgstr "Linutop"

#~ msgid "Messages"
#~ msgstr "Mensajes"

#~ msgid "Messages and communication history"
#~ msgstr "Mensajes e historial de comunicación"

#~ msgid "Motherboard A20Z7"
#~ msgstr "Placa base A20Z7"

#~ msgid "Multimedia Speakers"
#~ msgstr "Altavoces multimedia"

#~ msgid "Number of Actions"
#~ msgstr "Número de acciones"

#~ msgid "Number of messages which requires an action"
#~ msgstr "Número de mensajes que requieren una acción"

#~ msgid "Number of unread messages"
#~ msgstr "Número de mensajes no leidos"

#~ msgid "Obsolete"
#~ msgstr "Obsoleto"

#~ msgid ""
#~ "Office Editing Software with word processing, spreadsheets, "
#~ "presentations, graphics, and databases..."
#~ msgstr ""
#~ "Suite ofimática con procesador de textos, hoja de cálculo, "
#~ "presentaciones, gráficos, y bases de datos..."

#~ msgid "Office Suite"
#~ msgstr "Suite ofimática"

#~ msgid "On demand hard-disk having capacity based on requirement."
#~ msgstr ""
#~ "Disco duro bajo demanda con la capacidad basada en los requisitos dados."

#~ msgid "Pen drive, SP-2"
#~ msgstr "Pen drive, SP-2"

#~ msgid "Pen drive, SP-4"
#~ msgstr "Pen drive, SP-4"

#~ msgid "Printer, All-in-one"
#~ msgstr "Impresora multi-función"

#~ msgid "Processor AMD 8-Core"
#~ msgstr "Procesador AMD 8-Core"

#~ msgid "RAM SR2 (kit)"
#~ msgstr "RAM SR2 (kit)"

#~ msgid "RAM SR3"
#~ msgstr "RAM SR3"

#~ msgid "Router R430"
#~ msgstr "Router R430"

#~ msgid "Status"
#~ msgstr "Estado"

#~ msgid "Toner Cartridge"
#~ msgstr "Cartucho de tóner"

#~ msgid "TypeMatrix Dvorak Keyboard"
#~ msgstr "Teclado TypeMatrix Dvorak"

#~ msgid "USB Adapter"
#~ msgstr "Adaptador USB"

#~ msgid "Unread Messages"
#~ msgstr "Mensajes sin leer"

#~ msgid "Unread Messages Counter"
#~ msgstr "Contador de mensajes no leidos"

#~ msgid "Webcam"
#~ msgstr "Webcam"

#~ msgid "Windows 7 Professional"
#~ msgstr "Windows 7 Professional"

#~ msgid "Windows Home Server 2011"
#~ msgstr "Windows Home Server 2011"
