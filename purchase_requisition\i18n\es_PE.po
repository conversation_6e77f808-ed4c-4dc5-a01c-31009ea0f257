# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * purchase_requisition
#
# Translators:
# <PERSON> <<EMAIL>>, 2016
# <AUTHOR> <EMAIL>, 2015
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2016-06-16 20:06+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Peru) (http://www.transifex.com/odoo/odoo-9/language/"
"es_PE/)\n"
"Language: es_PE\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Call for Tender Reference:</strong><br/>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Date</strong>"
msgstr "<strong>Fecha</strong>"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Description</strong>"
msgstr "<strong>Descripción</strong>"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Product UoM</strong>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Qty</strong>"
msgstr "<strong>Cant</strong>"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Reference </strong>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Scheduled Date</strong>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Scheduled Ordering Date:</strong><br/>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Selection Type:</strong><br/>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Source:</strong><br/>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Vendor </strong>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.actions.act_window,help:purchase_requisition.action_purchase_requisition
msgid ""
"A Call for Tenders is a procedure for generating competing offers from\n"
"            different bidders. In the call for tenders, you can record the\n"
"            products you need to buy and generate the creation of RfQs to\n"
"            vendors. Once the tenders have been registered, you can review "
"and\n"
"            compare them and you can validate some and cancel others."
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_account_analytic_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_account_analytic_id
msgid "Analytic Account"
msgstr "Cuenta Analítica"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Approved by Vendor"
msgstr ""

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.action_bid_line_qty
msgid "Bid Line Qty"
msgstr ""

#. module: purchase_requisition
#: selection:purchase.requisition,state:0
msgid "Bid Selection"
msgstr ""

#. module: purchase_requisition
#: model:ir.actions.report.xml,name:purchase_requisition.action_report_purchase_requisitions
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_requisition_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_requisition_id
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_tree
msgid "Call for Tenders"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_name
msgid "Call for Tenders Reference"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Call for Tenders in negotiation"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Call for Tenders where tenders are closed"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_bid_line_qty
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_partner
msgid "Cancel"
msgstr "Cancelar"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Cancel Call"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_tree_tender
msgid "Cancel Choice"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Cancel Purchase Order"
msgstr ""

#. module: purchase_requisition
#: selection:purchase.requisition,state:0
msgid "Cancelled"
msgstr "Cancelado"

#. module: purchase_requisition
#: code:addons/purchase_requisition/purchase_requisition.py:302
#, python-format
msgid ""
"Cancelled by the call for tenders associated to this request for quotation."
msgstr ""

#. module: purchase_requisition
#: code:addons/purchase_requisition/purchase_requisition.py:67
#, python-format
msgid "Cancelled by the tender associated to this quotation."
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_bid_line_qty
msgid "Change Bid line quantity"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_tree_tender
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_bid_line_qty
msgid "Change Quantity"
msgstr ""

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.action_purchase_requisition_partner
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_partner
msgid "Choose Vendor"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Choose product lines"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.actions.act_window,help:purchase_requisition.action_purchase_requisition
msgid "Click to start a new Call for Tenders process."
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Close Call for Tenders"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Closed Tenders"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_company_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_company_id
msgid "Company"
msgstr "Compañia"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Confirm Call"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_tree_tender
msgid "Confirm Order"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Confirm Purchase Order"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
#: selection:purchase.requisition,state:0
msgid "Confirmed"
msgstr "Confirmado"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_partner
msgid "Create Request for Quotation"
msgstr ""

#. module: purchase_requisition
#: selection:product.template,purchase_requisition:0
msgid "Create a draft purchase order"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_bid_line_qty_create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_partner_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_bid_line_qty_create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_partner_create_date
msgid "Created on"
msgstr "Creado en"

#. module: purchase_requisition
#: code:addons/purchase_requisition/wizard/purchase_requisition_partner.py:22
#, python-format
msgid "Define product(s) you want to include in the call for tenders."
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_description
msgid "Description"
msgstr "Descripción"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_bid_line_qty_display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_partner_display_name
msgid "Display Name"
msgstr "Nombre a Mostrar"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Done"
msgstr "Realizado"

#. module: purchase_requisition
#: selection:purchase.requisition,state:0
msgid "Draft"
msgstr "Borrador"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "End Month"
msgstr ""

#. module: purchase_requisition
#. openerp-web
#: code:addons/purchase_requisition/static/src/xml/purchase_requisition.xml:5
#, python-format
msgid "Generate PO"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Group By"
msgstr "Agrupado por"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_bid_line_qty_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_partner_id
msgid "ID"
msgstr "ID"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_bid_line_qty___last_update
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition___last_update
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line___last_update
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_partner___last_update
msgid "Last Modified on"
msgstr "Ultima Modificación en"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_bid_line_qty_write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_partner_write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_write_uid
msgid "Last Updated by"
msgstr "Actualizado última vez por"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_bid_line_qty_write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_partner_write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_write_date
msgid "Last Updated on"
msgstr "Ultima Actualización"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_procurement_order_requisition_id
msgid "Latest Requisition"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_multiple_rfq_per_supplier
msgid "Multiple RFQ per vendor"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "Multiple Requisitions"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "New"
msgstr "Nuevo"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "New Call for Tenders"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Order Date"
msgstr "Fecha de Pedido"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Ordering Date"
msgstr ""

#. module: purchase_requisition
#: selection:purchase.requisition,state:0
msgid "PO Created"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_picking_type_id
msgid "Picking Type"
msgstr "Tipo de Picking"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_procurement_order
#: model:ir.model.fields,field_description:purchase_requisition.field_product_template_purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_procurement_id
msgid "Procurement"
msgstr "Requerimiento"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_product_id
msgid "Product"
msgstr "Producto"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Product Lines"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_product_template
msgid "Product Template"
msgstr "Plantilla de Producto"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_product_uom_id
msgid "Product Unit of Measure"
msgstr "Unidad de Medida del Producto"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Products"
msgstr "Productos"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_po_line_ids
msgid "Products by vendor"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_ids
msgid "Products to Purchase"
msgstr ""

#. module: purchase_requisition
#: selection:product.template,purchase_requisition:0
msgid "Propose a call for tenders"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_order
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Purchase Order"
msgstr "Orden de Compra"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_order_line
msgid "Purchase Order Line"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_tree_tender
msgid "Purchase Order Lines"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_purchase_ids
msgid "Purchase Orders"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_search_inherit
msgid "Purchase Orders with requisition"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition
msgid "Purchase Requisition"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition_line
msgid "Purchase Requisition Line"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition_partner
msgid "Purchase Requisition Partner"
msgstr ""

#. module: purchase_requisition
#: code:addons/purchase_requisition/purchase_requisition.py:435
#, python-format
msgid "Purchase Requisition created"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "Purchase Requisitions (exclusive)"
msgstr ""

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.action_purchase_requisition
#: model:ir.ui.menu,name:purchase_requisition.menu_purchase_requisition_pro_mgt
msgid "Purchase Tenders"
msgstr ""

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.act_res_partner_2_purchase_order
msgid "Purchase orders"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_bid_line_qty_qty
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_product_qty
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_bid_line_qty
msgid "Quantity"
msgstr "Cantidad"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_line_quantity_tendered
msgid "Quantity Tendered"
msgstr ""

#. module: purchase_requisition
#: code:addons/purchase_requisition/purchase_requisition.py:201
#, python-format
msgid "RFQ created"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "RFQs/Bids"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Reference"
msgstr "Referencia"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Request a Quotation"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Requests for Quotation"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "Requests for Quotation Details"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_search_inherit
msgid "Requisition"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Reset to Draft"
msgstr "Establecer como Borrador"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_user_id
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Responsible"
msgstr "Responsable"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_schedule_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_schedule_date
msgid "Scheduled Date"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_ordering_date
msgid "Scheduled Ordering Date"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Search Call for Tenders"
msgstr ""

#. module: purchase_requisition
#: selection:purchase.requisition,exclusive:0
msgid "Select multiple RFQ"
msgstr ""

#. module: purchase_requisition
#: selection:purchase.requisition,exclusive:0
msgid "Select only one RFQ (exclusive)"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_exclusive
msgid ""
"Select only one RFQ (exclusive):  On the confirmation of a purchase order, "
"it cancels the remaining purchase order.\n"
"Select multiple RFQ:  It allows to have multiple purchase orders.On "
"confirmation of a purchase order it does not cancel the remaining orders"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Send RFQ by Email"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Source"
msgstr "Fuente"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_origin
msgid "Source Document"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_state
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Status"
msgstr "Estado"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_order_line_quantity_tendered
msgid ""
"Technical field for not loosing the initial information about the quantity "
"proposed in the tender"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_date_end
msgid "Tender Closing Deadline"
msgstr ""

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.purchase_line_tree
msgid "Tender Lines"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_exclusive
msgid "Tender Selection Type"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Terms and Conditions"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_schedule_date
msgid ""
"The expected and scheduled delivery date where all the products are received"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Unassigned"
msgstr "No asignado"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Unassigned  Requisition"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_tree_tender
msgid "Vendor"
msgstr "Proveedor"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_partner_partner_ids
msgid "Vendors"
msgstr "Proveedores"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_warehouse_id
msgid "Warehouse"
msgstr "Almacén"

#. module: purchase_requisition
#: code:addons/purchase_requisition/purchase_requisition.py:72
#, python-format
msgid "You can not confirm call because there is no product line."
msgstr ""

#. module: purchase_requisition
#: code:addons/purchase_requisition/purchase_requisition.py:249
#, python-format
msgid "You have already generate the purchase order(s)."
msgstr ""

#. module: purchase_requisition
#: code:addons/purchase_requisition/purchase_requisition.py:198
#, python-format
msgid ""
"You have already one %s purchase order for this partner, you must cancel "
"this purchase order to create a new quotation."
msgstr ""

#. module: purchase_requisition
#: code:addons/purchase_requisition/purchase_requisition.py:258
#, python-format
msgid "You have no line selected for buying."
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "e.g. PO0025"
msgstr ""

#~ msgid "Action Needed"
#~ msgstr "Se Necesita Acción"

#~ msgid "Date of the last message posted on the record."
#~ msgstr "Fecha del ultimo mensaje actualizado en el registro."

#~ msgid "Followers"
#~ msgstr "Seguidores"

#~ msgid "Followers (Channels)"
#~ msgstr "Seguidores (Canales)"

#~ msgid "Followers (Partners)"
#~ msgstr "Seguidores (Partners)"

#~ msgid "If checked new messages require your attention."
#~ msgstr "Si está marcado nuevos mensajes requieren su atención."

#~ msgid "If checked, new messages require your attention."
#~ msgstr "Si está marcado, nuevos mensajes requieren su atención."

#~ msgid "Is Follower"
#~ msgstr "Es Seguidor"

#~ msgid "Last Message Date"
#~ msgstr "Fecha del último mensaje"

#~ msgid "Messages"
#~ msgstr "Mensajes"

#~ msgid "Messages and communication history"
#~ msgstr "Historial de mensajes y comunicación"

#~ msgid "Number of Actions"
#~ msgstr "Número de Acciones"

#~ msgid "Number of messages which requires an action"
#~ msgstr "Número de mensajes que requieren acción"

#~ msgid "Number of unread messages"
#~ msgstr "Número de mensajes no leídos"

#~ msgid "Unread Messages"
#~ msgstr "Mensajes no leidos"

#~ msgid "Unread Messages Counter"
#~ msgstr "Contador de Mensajes sin Leer"

#~ msgid "Website Messages"
#~ msgstr "Mensajes del Sitio Web"

#~ msgid "Website communication history"
#~ msgstr "Historial de comunicación del sitio web"
