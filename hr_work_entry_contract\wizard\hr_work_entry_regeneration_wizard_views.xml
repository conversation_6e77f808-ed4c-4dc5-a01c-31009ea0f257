<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="hr_work_entry_regeneration_wizard" model="ir.ui.view">
        <field name="name">hr_work_entry_regeneration_wizard</field>
        <field name="model">hr.work.entry.regeneration.wizard</field>
        <field name="arch" type="xml">
            <form string="Regenerate Employee Work Entries">
                <group>
                    <group>
                        <field name="employee_id"/>
                        <label for="date_from"></label>
                        <div name="date_from">
                            <div class="text-info" attrs="{'invisible': [('earliest_available_date_message', '=', '')]}">
                                <i class="fa fa-info-circle mr-1" title="Hint"/>
                                <field name="earliest_available_date_message" nolabel="1"/>
                            </div >
                            <field name="date_from"/>
                        </div>
                        <label for="date_to"></label>
                        <div name="date_to">
                            <div class="text-info" attrs="{'invisible': [('latest_available_date_message', '=', '')]}">
                                <i class="fa fa-info-circle mr-1" title="Hint"/>
                                <field name="latest_available_date_message" nolabel="1"/>
                            </div>
                            <field name="date_to"/>
                        </div>
                    </group>
                </group>
                <field name="search_criteria_completed" invisible="1"/>
                <field name="valid" invisible="1"/>
                <div attrs="{'invisible': ['|', ('search_criteria_completed', '=', False), ('valid', '=', True)]}">
                    <div class="text-danger"><i class="fa fa-exclamation-triangle mr-1" title="Warning"/>You are not allowed to regenerate validated work entries</div>
                    <field name="validated_work_entry_ids" widget="many2many" nolabel="1">
                        <tree string="Work Entries"
                              default_order = "date_start"

                              editable="bottom"
                              no_open="1" decoration-danger="state == 'validated'">
                            <field name="state" invisible="1"/>
                            <field name="date_start"/>
                            <field name="date_stop"/>
                            <field name="work_entry_type_id"/>
                            <field name="state"/>
                        </tree>
                    </field>
                </div>
                <footer>
                    <button name="regenerate_work_entries"
                            string="Regenerate Work Entries" data-hotkey="q"
                            type="object" class="oe_highlight"
                            attrs="{'invisible': ['|', ('search_criteria_completed', '=', False), ('valid', '=', False)]}"/>
                    <button name="regenerate_work_entries_disabled"
                            string="Regenerate Work Entries"
                            disabled="1"
                            attrs="{'invisible': [('search_criteria_completed', '=', True), ('valid', '=', True)]}"/>
                </footer>
            </form>
        </field>
    </record>
    <record id="hr_work_entry_regeneration_wizard_action" model="ir.actions.act_window">
        <field name="name">Work Entry Regeneration</field>
        <field name="res_model">hr.work.entry.regeneration.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>
</odoo>
