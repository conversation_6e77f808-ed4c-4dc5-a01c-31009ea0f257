# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_stripe
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-27 15:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: Rasaree<PERSON> Lappiam, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.payment_acquirer_form
msgid "Connect Stripe"
msgstr "เชื่อมต่อ Stripe"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid "Could not establish the connection to the API."
msgstr "ไม่สามารถสร้างการเชื่อมต่อกับ API ได้"

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.payment_acquirer_form
msgid "Generate your webhook"
msgstr "สร้างเว็บฮุคของคุณ"

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.payment_acquirer_form
msgid "Get your Secret and Publishable keys"
msgstr "รับคีย์ลับและคีย์ที่เผยแพร่ได้ของคุณ"

#. module: payment_stripe
#: model:ir.model.fields,help:payment_stripe.field_payment_acquirer__stripe_webhook_secret
msgid ""
"If a webhook is enabled on your Stripe account, this signing secret must be "
"set to authenticate the messages sent from Stripe to Odoo."
msgstr ""
"หากเปิดใช้งานเว็บฮุคในบัญชี Stripe ของคุณ "
"ต้องตั้งค่าความลับในการลงนามนี้เพื่อตรวจสอบสิทธิ์ข้อความที่ส่งจาก Stripe "
"ไปยัง Odoo"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "ไม่พบธุรกรรมที่ตรงกับการอ้างอิง %s"

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "ผู้รับชำระ"

#. module: payment_stripe
#: model:ir.actions.act_window,name:payment_stripe.action_payment_acquirer_onboarding
msgid "Payment Acquirers"
msgstr "ผู้ให้บริการชำระเงิน"

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_account_payment_method
msgid "Payment Methods"
msgstr "วิธีการชำระเงิน"

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_token
msgid "Payment Token"
msgstr "โทเคนการชำระเงิน"

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_transaction
msgid "Payment Transaction"
msgstr "ธุรกรรมการชำระเงิน"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__provider
msgid "Provider"
msgstr "ผู้ให้บริการ"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__stripe_publishable_key
msgid "Publishable Key"
msgstr "คีย์ที่เผยแพร่ได้"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "Received data with invalid intent status: %s"
msgstr "ได้รับข้อมูลที่มีสถานะเจตนาที่ไม่ถูกต้อง: %s"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing intent status."
msgstr "ได้รับข้อมูลโดยไม่มีมีสถานะเจตนา"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing merchant reference"
msgstr "ได้รับข้อมูลโดยไม่มีการอ้างอิงผู้ขาย"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__stripe_secret_key
msgid "Secret Key"
msgstr "คีย์ลับ"

#. module: payment_stripe
#: model:account.payment.method,name:payment_stripe.payment_method_stripe
#: model:ir.model.fields.selection,name:payment_stripe.selection__payment_acquirer__provider__stripe
msgid "Stripe"
msgstr "Stripe"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_transaction__stripe_payment_intent
msgid "Stripe Payment Intent ID"
msgstr ""

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_token__stripe_payment_method
msgid "Stripe Payment Method ID"
msgstr "ไอดีวิธีการชำระเงิน Stripe"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid "Stripe Proxy error: %(error)s"
msgstr "ข้อผิดพลาด Stripe พร็อกซี: %(error)s"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid "Stripe Proxy: An error occurred when communicating with the proxy."
msgstr "Stripe Proxy: เกิดข้อผิดพลาดขณะสื่อสารกับพร็อกซี"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid "Stripe Proxy: Could not establish the connection."
msgstr "Stripe Proxy: ไม่สามารถสร้างการเชื่อมต่อได้"

#. module: payment_stripe
#: model:ir.model.fields,help:payment_stripe.field_payment_acquirer__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr ""

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid ""
"The communication with the API failed.\n"
"Stripe gave us the following info about the problem:\n"
"'%s'"
msgstr ""
"การสื่อสารกับ API ล้มเหลว\n"
"Stripe ให้ข้อมูลเกี่ยวกับปัญหาดังต่อไปนี้:\n"
"'%s'"

#. module: payment_stripe
#: model:ir.model.fields,help:payment_stripe.field_payment_acquirer__stripe_publishable_key
msgid "The key solely used to identify the account with Stripe"
msgstr "คีย์ที่ใช้เพื่อระบุบัญชีกับ Stripe เท่านั้น"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "The transaction is not linked to a token."
msgstr "ธุรกรรมไม่ได้เชื่อมโยงกับโทเค็น"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_token.py:0
#, python-format
msgid "Unable to convert payment token to new API."
msgstr "ไม่สามารถแปลงโทเค็นการชำระเงินเป็น API ใหม่ได้"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__stripe_webhook_secret
msgid "Webhook Signing Secret"
msgstr "ความลับในการลงนามเว็บฮุค"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid "You Stripe Webhook was successfully set up!"
msgstr "เว็บฮุค Stripe ของคุณถูกตั้งค่าเรียบร้อยแล้ว!"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid ""
"You cannot create a Stripe Webhook if your Stripe Secret Key is not set."
msgstr ""
"คุณไม่สามารถสร้างเว็บฮุค Stripe ได้ หากไม่ได้ตั้งค่าคีย์ลับ Stripe ของคุณ"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid ""
"You cannot set the acquirer state to Enabled until your onboarding to Stripe"
" is completed."
msgstr ""

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid ""
"You cannot set the acquirer to Test Mode while it is linked with your Stripe"
" account."
msgstr ""

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid "Your Stripe Webhook is already set up."
msgstr "เว็บฮุค Stripe ของคุณได้รับการตั้งค่าแล้ว"
