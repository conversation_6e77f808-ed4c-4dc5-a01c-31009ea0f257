# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* bus
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:21+0000\n"
"Last-Translator: Ha Ketem <<EMAIL>>, 2022\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

#. module: bus
#: model:ir.model.constraint,message:bus.constraint_bus_presence_bus_user_presence_unique
msgid "A user can only have one IM status."
msgstr "למשתמש יכול להיות רק סטאטוס IM אחד "

#. module: bus
#: model:ir.model.fields.selection,name:bus.selection__bus_presence__status__away
msgid "Away"
msgstr "רחוק"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__channel
msgid "Channel"
msgstr "ערוץ"

#. module: bus
#: model:ir.model,name:bus.model_bus_bus
msgid "Communication Bus"
msgstr "אפיק תקשורת"

#. module: bus
#: model:ir.model,name:bus.model_res_partner
msgid "Contact"
msgstr "צור קשר"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__display_name
#: model:ir.model.fields,field_description:bus.field_bus_presence__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__id
#: model:ir.model.fields,field_description:bus.field_bus_presence__id
msgid "ID"
msgstr "תעודה מזהה"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__status
#: model:ir.model.fields,field_description:bus.field_res_partner__im_status
#: model:ir.model.fields,field_description:bus.field_res_users__im_status
msgid "IM Status"
msgstr "סטטוס IM"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus____last_update
#: model:ir.model.fields,field_description:bus.field_bus_presence____last_update
msgid "Last Modified on"
msgstr "שינוי אחרון ב"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__last_poll
msgid "Last Poll"
msgstr "סקר אחרון"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__last_presence
msgid "Last Presence"
msgstr "נוכחות אחרונה"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__message
msgid "Message"
msgstr "הודעה"

#. module: bus
#: model:ir.model.fields.selection,name:bus.selection__bus_presence__status__offline
msgid "Offline"
msgstr "לא מקוון"

#. module: bus
#: model:ir.model.fields.selection,name:bus.selection__bus_presence__status__online
msgid "Online"
msgstr "מקוון"

#. module: bus
#. openerp-web
#: code:addons/bus/static/src/js/web_client_bus.js:0
#: code:addons/bus/static/src/js/web_client_bus.js:0
#, python-format
msgid "Refresh"
msgstr "רענן"

#. module: bus
#. openerp-web
#: code:addons/bus/static/src/js/web_client_bus.js:0
#, python-format
msgid "The page appears to be out of date."
msgstr "נראה שהדף הינו מיושן."

#. module: bus
#: model:ir.model,name:bus.model_bus_presence
msgid "User Presence"
msgstr "נוכחות משתמש"

#. module: bus
#: model:ir.model,name:bus.model_res_users
#: model:ir.model.fields,field_description:bus.field_bus_presence__user_id
msgid "Users"
msgstr "משתמשים"

#. module: bus
#: code:addons/bus/controllers/main.py:0
#, python-format
msgid "bus.Bus not available in test mode"
msgstr "bus.Bus לא זמין במצב נסיון"
