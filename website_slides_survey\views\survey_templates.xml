<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <template id="survey_fill_form_done_inherit_website_slides" inherit_id="survey.survey_fill_form_done">
            <xpath expr="//div[hasclass('o_survey_finished')]//a[hasclass('btn-primary')][1]" position="after">
                <t t-if="channel_id">
                    <a role="button" class="mr-2 mt-2 mt-md-0 btn btn-secondary btn-lg" href="#" data-toggle="modal" t-attf-data-target="#slideChannelShareModal_{{channel_id.id}}">
                        <i class="fa fa-share-alt" aria-label="Share certification" title="Share certification"/>
                        Share your certification
                    </a>
                    <t t-call="website_slides.slide_share_modal">
                        <t t-set="record" t-value="channel_id"/>
                    </t>
                </t>
            </xpath>
            <xpath expr="//div[hasclass('o_survey_finished')]/div[last()]" position="after">
                <div t-if="channel_id" class="mt16">
                    <a role="button"
                        class="btn btn-primary btn-lg"
                        t-att-href="channel_id.website_url">
                        Go back to course
                    </a>
                </div>
            </xpath>
        </template>

        <template id="o_wss_certification_icon">
            <t t-set="icon_url" t-value="icon_url if icon_url else '/website_slides_survey/static/src/img/certification.svg'"/>
            <t t-set="icon_classes" t-value="icon_classes if icon_classes else 'o_wss_certification_icon'"/>
            <img t-att-class="icon_classes" t-att-src="icon_url" alt="Certification icon"/>
        </template>
    </data>
</odoo>
