<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
    <rect id="path-1" width="21.12" height="2" x="0" y="0"/>
    <filter id="filter-2" width="104.7%" height="200%" x="-2.4%" y="-25%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.292012675 0"/>
    </filter>
    <path id="path-3" d="M19 20v1H8v-1h11zm2-2v1H8v-1h13zm-5-9v1H8V9h8zm5-2v1H8V7h13z"/>
    <filter id="filter-4" width="107.7%" height="114.3%" x="-3.8%" y="-3.6%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
    <linearGradient id="linearGradient-5" x1="50%" x2="50%" y1="0%" y2="100%">
      <stop offset="0%" stop-color="#00A09D"/>
      <stop offset="100%" stop-color="#00E2FF"/>
    </linearGradient>
    <rect id="path-6" width="24.038" height="2" x="0" y="0"/>
    <filter id="filter-7" width="104.2%" height="200%" x="-2.1%" y="-25%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.292012675 0"/>
    </filter>
    <path id="path-8" d="M19 20v1H8v-1h11zm3-2v1H8v-1h14zm-3-9v1H8V9h11zm-1-2v1H8V7h10z"/>
    <filter id="filter-9" width="107.1%" height="114.3%" x="-3.6%" y="-3.6%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_features_grid">
      <rect width="82" height="60" class="bg"/>
      <g class="group_3" transform="translate(15 18)">
        <g class="group_2">
          <g class="group">
            <g class="rectangle">
              <use fill="#000" filter="url(#filter-2)" xlink:href="#path-1"/>
              <use fill="#FFF" fill-opacity=".78" xlink:href="#path-1"/>
            </g>
            <g class="combined_shape">
              <use fill="#000" filter="url(#filter-4)" xlink:href="#path-3"/>
              <use fill="#FFF" fill-opacity=".348" xlink:href="#path-3"/>
            </g>
          </g>
          <path fill="url(#linearGradient-5)" d="M3 18a3 3 0 1 1 0 6 3 3 0 0 1 0-6zM3 7a3 3 0 1 1 0 6 3 3 0 0 1 0-6z" class="combined_shape"/>
        </g>
        <g class="group_2" transform="translate(28)">
          <g class="group">
            <g class="rectangle">
              <use fill="#000" filter="url(#filter-7)" xlink:href="#path-6"/>
              <use fill="#FFF" fill-opacity=".78" xlink:href="#path-6"/>
            </g>
            <g class="combined_shape">
              <use fill="#000" filter="url(#filter-9)" xlink:href="#path-8"/>
              <use fill="#FFF" fill-opacity=".348" xlink:href="#path-8"/>
            </g>
          </g>
          <path fill="url(#linearGradient-5)" d="M3 18a3 3 0 1 1 0 6 3 3 0 0 1 0-6zM3 7a3 3 0 1 1 0 6 3 3 0 0 1 0-6z" class="combined_shape"/>
        </g>
      </g>
    </g>
  </g>
</svg>
