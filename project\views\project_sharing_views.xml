<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="project_sharing_quick_create_task_form" model="ir.ui.view">
        <field name="name">project.task.form.quick_create</field>
        <field name="model">project.task</field>
        <field name="priority">999</field>
        <field name="groups_id" eval="[(4, ref('base.group_portal')), (4, ref('base.group_user'))]"/>
        <field name="arch" type="xml">
            <form>
                <group>
                    <field name="name" string="Task Title" placeholder="e.g. New Design"/>
                </group>
            </form>
        </field>
    </record>

    <record id="project_sharing_project_task_view_kanban" model="ir.ui.view">
        <field name="name">project.sharing.project.task.view.kanban</field>
        <field name="model">project.task</field>
        <field name="priority">999</field>
        <field name="groups_id" eval="[(4, ref('base.group_portal')), (4, ref('base.group_user'))]"/>
        <field name="arch" type="xml">
            <kanban
                class="o_kanban_small_column o_kanban_project_tasks"
                default_group_by="stage_id"
                on_create="quick_create"
                quick_create_view="project.project_sharing_quick_create_task_form"
                archivable="0"
                import="0"
            >
                <field name="color"/>
                <field name="priority"/>
                <field name="stage_id" options='{"group_by_tooltip": {"description": "Description"}}'/>
                <!-- TODO: [XBO] remove me in master -->
                <field name="user_ids" invisible="1"/>
                <field name="portal_user_names"/>
                <field name="partner_id"/>
                <field name="sequence"/>
                <field name="is_closed"/>
                <field name="partner_is_company"/>
                <field name="displayed_image_id"/>
                <field name="active"/>
                <field name="allow_subtasks"/>
                <field name="child_text"/>
                <field name="legend_blocked" invisible="1"/>
                <field name="legend_normal" invisible="1"/>
                <field name="legend_done" invisible="1"/>
                <progressbar field="kanban_state" colors='{"done": "success", "blocked": "danger", "normal": "muted"}'/>
                <templates>
                <t t-name="kanban-box">
                    <div t-attf-class="{{!selection_mode ? 'oe_kanban_color_' + kanban_getcolor(record.color.raw_value) : ''}} oe_kanban_card oe_kanban_global_click">
                        <div class="oe_kanban_content">
                            <div class="o_kanban_record_top">
                                <div class="o_kanban_record_headings">
                                    <strong class="o_kanban_record_title">
                                        <s t-if="!record.active.raw_value"><field name="name" widget="name_with_subtask_count"/></s>
                                        <t t-else=""><field name="name" widget="name_with_subtask_count"/></t>
                                    </strong>
                                    <span invisible="context.get('default_project_id', False)"><br/><field name="project_id" required="1"/></span>
                                    <br />
                                    <t t-if="record.partner_id.value">
                                        <span t-if="!record.partner_is_company.raw_value">
                                            <field name="commercial_partner_id"/>
                                        </span>
                                        <span t-else="">
                                            <field name="partner_id"/>
                                        </span>
                                    </t>
                                    <t t-else="record.email_from.raw_value"><span><field name="email_from"/></span></t>
                                </div>
                                <div class="o_dropdown_kanban dropdown" t-if="!selection_mode">
                                    <a role="button" class="dropdown-toggle o-no-caret btn" data-toggle="dropdown" data-display="static" href="#" aria-label="Dropdown menu" title="Dropdown menu">
                                        <span class="fa fa-ellipsis-v"/>
                                    </a>
                                    <div class="dropdown-menu" role="menu">
                                        <!-- [XBO] TODO: remove me in master -->
                                        <a t-if="widget.editable" role="menuitem" type="set_cover" class="dropdown-item d-none" data-field="displayed_image_id">Set Cover Image</a>
                                        <a t-if="widget.editable" role="menuitem" type="edit" class="dropdown-item">Edit</a>
                                        <div role="separator" class="dropdown-divider"></div>
                                        <ul class="oe_kanban_colorpicker" data-field="color"/>
                                    </div>
                                </div>
                            </div>
                            <div class="o_kanban_record_body">
                                <field name="tag_ids" widget="many2many_tags" options="{'color_field': 'color'}"/>
                                <div t-if="record.date_deadline.raw_value" name="date_deadline" attrs="{'invisible': [('is_closed', '=', True)]}">
                                    <field name="date_deadline" widget="remaining_days"/>
                                </div>
                                <div t-if="record.displayed_image_id.value">
                                    <field name="displayed_image_id" widget="attachment_image"/>
                                </div>
                            </div>
                            <div class="o_kanban_record_bottom" t-if="!selection_mode">
                                <div class="oe_kanban_bottom_left">
                                    <field name="priority" widget="priority"/>
                                </div>
                                <div class="oe_kanban_bottom_right" t-if="!selection_mode">
                                    <span t-if="record.portal_user_names.raw_value.length > 0" class="pr-2" t-att-title="record.portal_user_names.raw_value">
                                        <t t-set="user_count" t-value="record.portal_user_names.raw_value.split(',').length"/>
                                        <t t-out="user_count"/>
                                        <t t-if="user_count > 1"> assignees</t>
                                        <t t-else=""> assignee</t>
                                        <t t-if="1 == 0" t-set="display_nb_assignees" t-value="user_count + ' assignee' + (user_count > 1 ? 's' : '')"/>
                                        <t t-if="1 == 0" t-out="display_nb_assignees"/>
                                    </span>
                                    <field name="kanban_state" widget="state_selection"/>
                                    <!-- TODO: [XBO] remove me in master -->
                                    <field name="user_ids" string="Assignees" widget="many2many_avatar_user" options="{'no_open_chat': True}" invisible="1"/>
                                </div>
                            </div>
                        </div>
                        <div class="oe_clear"></div>
                    </div>
                </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="project_sharing_project_task_view_tree" model="ir.ui.view">
        <field name="name">project.sharing.project.task.tree</field>
        <field name="model">project.task</field>
        <field name="priority">999</field>
        <field name="arch" type="xml">
            <tree string="Tasks" sample="1" delete="0" import="0">
                <field name="is_closed" invisible="1" />
                <field name="allow_subtasks" invisible="1" />
                <field name="sequence" invisible="1" readonly="1"/>
                <field name="priority" widget="priority" optional="show" nolabel="1"/>
                <field name="name" widget="name_with_subtask_count"/>
                <field name="child_text" invisible="1"/>
                <field name="company_id" invisible="1"/>
                <field name="partner_id" optional="hide"/>
                <field name="portal_user_names" string="Assignees" optional="show"/>
                <!-- TODO: [XBO] remove me in master -->
                <field name="user_ids" invisible="1" />
                <field name="date_deadline" optional="hide" widget="remaining_days" attrs="{'invisible': [('is_closed', '=', True)]}"/>
                <field name="tag_ids" widget="many2many_tags" options="{'color_field': 'color'}" optional="show"/>
                <field name="kanban_state" widget="state_selection" optional="hide"/>
                <field name="legend_blocked" invisible="1"/>
                <field name="legend_normal" invisible="1"/>
                <field name="legend_done" invisible="1"/>
                <field name="stage_id" invisible="context.get('set_visible',False)" optional="show"/>
            </tree>
        </field>
    </record>

    <record id="project_sharing_project_task_view_form" model="ir.ui.view">
        <field name="name">project.sharing.project.task.view.form</field>
        <field name="model">project.task</field>
        <field name="priority">999</field>
        <field name="groups_id" eval="[(4, ref('base.group_portal')), (4, ref('base.group_user'))]"/>
        <field name="arch" type="xml">
            <form string="Project Sharing: Task" class="o_form_project_tasks">
                <header>
                    <button name="action_assign_to_me" string="Assign to Me" type="object" class="oe_highlight"
                            attrs="{'invisible' : &quot;[('user_ids', 'in', [uid])]&quot;}" data-hotkey="q" groups="base.group_user"/>
                    <button name="action_unassign_me" string="Unassign Me" type="object" class="oe_highlight"
                            attrs="{'invisible' : &quot;[('user_ids', 'not in', [uid])]&quot;}" data-hotkey="q"/>
                    <field name="stage_id" widget="statusbar" options="{'clickable': '1', 'fold_field': 'fold'}" attrs="{'invisible': [('project_id', '=', False), ('stage_id', '=', False)]}" />
                </header>
                <sheet string="Task">
                    <div class="oe_button_box" name="button_box">
                        <button name="action_open_parent_task" type="object" special="cancel" class="oe_stat_button o_debounce_disabled" icon="fa-tasks" string="Parent Task" attrs="{'invisible': [('parent_id', '=', False)]}"/>
                    </div>
                    <widget name="web_ribbon" title="Archived" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                    <div class="oe_title pr-0">
                        <h1 class="d-flex flex-row justify-content-between">
                            <field name="priority" widget="priority" class="mr-3"/>
                            <field name="name" class="o_task_name text-truncate" placeholder="Task Title..."/>
                            <field name="kanban_state" widget="state_selection" class="ml-auto"/>
                            <field name="legend_blocked" invisible="1"/>
                            <field name="legend_normal" invisible="1"/>
                            <field name="legend_done" invisible="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="project_id" invisible="1"/>
                            <field name="display_project_id" string="Project" invisible="1"/>
                            <field name="user_ids" invisible="1" />
                            <field name="portal_user_names"
                                string="Assignees"
                                class="o_task_user_field"/>
                        </group>
                        <group>
                            <field name="active" invisible="1"/>
                            <field name="parent_id" invisible="1" />
                            <field name="company_id" invisible="1" />
                            <field name="is_closed" invisible="1" />
                            <field name="allow_subtasks" invisible="1" />
                            <field name="partner_id" options="{'no_open': True, 'no_create': True, 'no_edit': True}"/>
                            <field name="date_deadline" attrs="{'invisible': [('is_closed', '=', True)]}"/>
                            <field name="tag_ids" widget="many2many_tags" options="{'color_field': 'color', 'no_create_edit': True, 'no_edit_color': True}"/>
                        </group>
                    </group>
                    <notebook>
                        <page name="description_page" string="Description">
                            <field name="description" type="html" options="{'collaborative': true}"/>
                        </page>
                        <page name="sub_tasks_page" string="Sub-tasks" attrs="{'invisible': [('allow_subtasks', '=', False)]}">
                            <field name="child_ids" context="{'default_project_id': project_id if not parent_id or not display_project_id else display_project_id, 'default_parent_id': id, 'default_partner_id': partner_id, 'form_view_ref' : 'project.project_sharing_project_task_view_form'}">
                                <tree editable="bottom">
                                    <field name="project_id" invisible="1"/>
                                    <field name="is_closed" invisible="1"/>
                                    <field name="sequence" widget="handle"/>
                                    <field name="name"/>
                                    <field name="display_project_id" string="Project" optional="hide" invisible="1"/>
                                    <field name="company_id" invisible="1"/>
                                    <field name="partner_id" options="{'no_open': True, 'no_create': True, 'no_edit': True}" optional="hide"/>
                                    <!-- TODO: [XBO] remove me in master -->
                                    <field name="user_ids" invisible="1"/>
                                    <field name="portal_user_names" string="Assignees"/>
                                    <field name="date_deadline" attrs="{'invisible': [('is_closed', '=', True)]}" optional="show"/>
                                    <field name="tag_ids" widget="many2many_tags" options="{'color_field': 'color'}" optional="hide"/>
                                    <field name="kanban_state" widget="state_selection" optional="hide"/>
                                    <field name="stage_id" optional="show"/>
                                    <button name="action_open_task" type="object" title="View Task" string="View Task" class="btn btn-link pull-right"
                                            context="{'form_view_ref': 'project.project_sharing_project_task_view_form'}"
                                            attrs="{'invisible': &quot;[('display_project_id', '!=', False), ('display_project_id', '!=', active_id)]&quot;}"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter"/>
            </form>
        </field>
    </record>

    <record id="project_sharing_project_task_view_search" model="ir.ui.view">
        <field name="name">project.task.search.form</field>
        <field name="model">project.task</field>
        <field name="priority">999</field>
        <field name="arch" type="xml">
            <search string="Tasks">
                <field name="name" string="Task"/>
                <field name="tag_ids"/>
                <!-- TODO: [XBO] remove me in master -->
                <field name="user_ids" invisible="1"/>
                <field name="portal_user_names" string="Assignees"/>
                <field name="partner_id" operator="child_of"/>
                <field name="stage_id"/>
                <field string="Project" name="display_project_id"/>
                <filter string="Unassigned" name="unassigned" domain="[('user_ids', '=', False)]"/>
                <separator/>
                <filter string="Starred" name="starred" domain="[('priority', 'in', [1, 2])]"/>
                <separator/>
                <filter string="Late Tasks" name="late" domain="[('date_deadline', '&lt;', context_today().strftime('%Y-%m-%d')), ('is_closed', '=', False)]"/>
                <separator/>
                <filter invisible="1" string="Today Activities" name="activities_today"
                    domain="[('activity_ids.date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"/>
                <filter invisible="1" string="Future Activities" name="activities_upcoming_all"
                    domain="[('activity_ids.date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))]"/>
                <group expand="0" string="Group By">
                    <filter string="Stage" name="stage" context="{'group_by': 'stage_id'}"/>
                    <!-- TODO: [XBO] remove me in master -->
                    <filter string="Assignees" name="user" context="{'group_by': 'user_ids'}" invisible="1"/>
                    <!-- [XBO] TODO: remove me in master -->
                    <filter string="Project" name="project" context="{'group_by': 'project_id'}" invisible="1"/>
                    <filter string="Customer" name="customer" context="{'group_by': 'partner_id'}"/>
                    <filter string="Kanban State" name="kanban_state" context="{'group_by': 'kanban_state'}"/>
                    <filter string="Deadline" name="date_deadline" context="{'group_by': 'date_deadline'}"/>
                    <!-- [XBO] TODO: remove me in master -->
                    <filter string="Creation Date" name="group_create_date" context="{'group_by': 'create_date'}" invisible="1"/>
                </group>
            </search>
        </field>
    </record>

    <record id="project_sharing_project_task_action" model="ir.actions.act_window">
        <field name="name">Project Sharing</field>
        <field name="res_model">project.task</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="search_view_id" ref="project.project_sharing_project_task_view_search"/>
        <field name="domain">[('display_project_id', '=', active_id)]</field>
        <field name="context">{
            'default_project_id': active_id,
            'delete': 0,
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No tasks found. Let's create one!
            </p><p>
                To get things done, use activities and status on tasks.<br/>
                Chat in real time or by email to collaborate efficiently.
            </p>
        </field>
    </record>

    <record id="project_sharing_kanban_action_view" model="ir.actions.act_window.view">
        <field name="view_mode">kanban</field>
        <field name="act_window_id" ref="project.project_sharing_project_task_action"/>
        <field name="view_id" ref="project.project_sharing_project_task_view_kanban"/>
    </record>

    <record id="project_sharing_tree_action_view" model="ir.actions.act_window.view">
        <field name="view_mode">tree</field>
        <field name="act_window_id" ref="project.project_sharing_project_task_action"/>
        <field name="view_id" ref="project.project_sharing_project_task_view_tree"/>
    </record>

    <record id="project_sharing_form_action_view" model="ir.actions.act_window.view">
        <field name="view_mode">form</field>
        <field name="act_window_id" ref="project.project_sharing_project_task_action"/>
        <field name="view_id" ref="project.project_sharing_project_task_view_form"/>
    </record>

</odoo>
