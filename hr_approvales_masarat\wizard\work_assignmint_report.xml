<?xml version="1.0"?>
<odoo>

    <record id="hr_masarat_assignmint_report_form" model="ir.ui.view">
        <field name="name">hr.masarat.work.assignmint.wizard.form</field>
        <field name="model">hr.masarat.work.assignmint.wizard</field>
        <field name="arch" type="xml">
            <form string="تقرير تكليف بعمل">
                <group>
<!--                    <group>-->
<!--                        <field name="employee_id"-->
<!--                               attrs="{'required':[('all_employee','=',False)],'invisible':[('all_employee','=',True)]}"/>-->
<!--                        <field name="all_employee"/>-->
<!--                    </group>-->
                    <group>
                        <field name="date_start" required="1"/>
                        <field name="date_end" required="1"/>
                    </group>
                </group>
                <footer>
                    <button name="get_report_action" type="object" string="انشاء"
                            class="btn-primary"/>
                    <button string="الغاء" class="btn-secondary" special="cancel"/>
                </footer>

            </form>
        </field>
    </record>


    <record id="action_assignmint_report_form" model="ir.actions.act_window">
        <field name="name">تقرير تكليف بعمل</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">hr.masarat.work.assignmint.wizard</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="hr_masarat_assignmint_report_form"/>
        <field name="target">new</field>
    </record>


    <menuitem
            id="menu_hr_work_assignmint_report"
            name="تقرير تكليف بعمل"
            parent="hr_approvales_masarat.menu_masarat_approvale_report"
            groups="hr_approvales_masarat.group_hr_approvales_masarat"
            action="action_assignmint_report_form"
            sequence="3"/>


    <template id="work_assignmint_report_id">
        <t t-call="web.html_container">
            <t t-call="web.external_layout">
                <t t-set="index" t-value="1"/>
                <div class="page">
                    <h4 style="text-align: center;">
                        <strong>تقرير تكليف بعمل</strong>
                        <span t-esc="employee_name"/>
                    </h4>
                    <ul dir="rtl" class="nav" style="display: flex; justify-content: center;">
                        <li>
                            <h5 style="text-align: center;">للفترة من :
                                <span t-esc="start_date"/>
                            </h5>
                        </li>
                        <li>
                            <h5 style="text-align: center;">الى :
                                <span t-esc="end_date"/>
                            </h5>
                        </li>
                    </ul>
                    <br/>
                    <br/>
                    <table style="font-size:16px; width: 100%; border-collapse: collapse; border-style: solid; border: 1px solid;">
                        <thead>
                            <tr style="text-align: center; height: 20px; font-weight:bold;">
                                <th style="width: 30%; text-align: center; border: 1px solid;">عدد الساعات</th>
                                <th style="width: 20%; text-align: center; border: 1px solid; font-size:12px">عدد التكليفات/الطلبات
                                </th>
                                <th style="width: 20%; text-align: center; border: 1px solid; font-size:12px">اسم الموظف
                                </th>
                                <th style="width: 5%; text-align: center; border: 1px solid; font-size:12px">#</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr t-foreach="employees_dict" t-as="line" style="text-align: center;">
                                <td style="border: 1px solid;">
                                    <span t-esc="employees_dict[line]['total_hours']"/>
                                </td>
                                <td style="border: 1px solid;">
                                    <span t-esc="employees_dict[line]['total_count']"/>
                                </td>
                                <td style="border: 1px solid;">
                                    <span t-esc="employees_dict[line]['name']"/>
                                </td>
                                <td style="border: 1px solid;">
                                    <span t-esc="index"/>
                                    <t t-set="index" t-value="index + 1"/>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <br/>
                </div>
            </t>
        </t>
    </template>

    <report
            id="work_assignmint_report_x1"
            string="Work Assignmint Report"
            model="hr.employee"
            report_type="qweb-pdf"
            name="hr_approvales_masarat.work_assignmint_report_id"
            file="hr_approvales_masarat.work_assignmint_report_id"
            menu="False"/>


</odoo>