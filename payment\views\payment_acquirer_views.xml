<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="payment_acquirer_form" model="ir.ui.view">
        <field name="name">payment.acquirer.form</field>
        <field name="model">payment.acquirer</field>
        <field name="arch" type="xml">
            <form string="Payment Acquirer">
                <field name="support_authorization" invisible="1"/>
                <field name="support_fees_computation" invisible="1"/>
                <field name="support_tokenization" invisible="1"/>
                <field name="module_id" invisible="1"/>
                <field name="module_state" invisible="1"/>
                <field name="module_to_buy" invisible="1"/>
                <field name="show_credentials_page" invisible="1"/>
                <field name="show_allow_tokenization" invisible="1"/>
                <field name="show_payment_icon_ids" invisible="1"/>
                <field name="show_pre_msg" invisible="1"/>
                <field name="show_pending_msg" invisible="1"/>
                <field name="show_auth_msg" invisible="1"/>
                <field name="show_done_msg" invisible="1"/>
                <field name="show_cancel_msg" invisible="1"/>
                <sheet>
                    <field name="image_128" widget="image" class="oe_avatar"/>
                    <widget name="web_ribbon" title="Disabled" bg_color="bg-danger" attrs="{'invisible': [('state', '!=', 'disabled')]}"/>
                    <widget name="web_ribbon" title="Test Mode" bg_color="bg-warning" attrs="{'invisible': [('state', '!=', 'test')]}"/>
                    <div class="oe_title">
                        <h1><field name="name" placeholder="Name"/></h1>
                        <div attrs="{'invisible': ['|', ('module_state', '=', 'installed'), ('module_id', '=', False)]}">
                            <a attrs="{'invisible': [('module_to_buy', '=', False)]}" href="https://odoo.com/pricing?utm_source=db&amp;utm_medium=module" target="_blank" class="btn btn-info" role="button">Upgrade</a>
                            <button attrs="{'invisible': [('module_to_buy', '=', True)]}" type="object" class="btn btn-primary" name="button_immediate_install" string="Install"/>
                        </div>
                    </div>
                    <div attrs="{'invisible': ['|', ('module_state', '=', 'installed'), ('module_id', '=', False)]}">
                        <div class="o_payment_acquirer_desc">
                            <field name="description"/>
                        </div>
                    </div>
                    <div attrs="{'invisible': [('id', '!=', False)]}" class="alert alert-warning" role="alert">
                        <strong>Warning</strong> Creating a payment acquirer from the <em>CREATE</em> button is not supported.
                        Please use the <em>Duplicate</em> action instead.
                    </div>
                    <group>
                        <group name="payment_state">
                            <field name="provider" groups="base.group_no_one" attrs="{'readonly': [('id', '!=', False)], 'invisible': [('module_id', '!=', False), ('module_state', '!=', 'installed')]}"/>
                            <field name="state" widget="radio" attrs="{'invisible': [('module_state', '=', 'uninstalled')]}"/>
                            <field name="company_id" groups="base.group_multi_company" options='{"no_open":True}'/>
                        </group>
                    </group>
                    <notebook attrs="{'invisible': ['&amp;', ('module_id', '!=', False), ('module_state', '!=', 'installed')]}">
                        <page string="Credentials" name="acquirer_credentials" attrs="{'invisible': ['|', ('provider', '=', 'none'), ('show_credentials_page', '=', False)]}">
                            <group name="acquirer"/>
                        </page>
                        <page string="Configuration" name="configuration">
                            <group name="acquirer_config">
                                <group string="Payment Form" name="payment_form">
                                    <field name="display_as" placeholder="If not defined, the acquirer name will be used."/>
                                    <field name="payment_icon_ids" attrs="{'invisible': [('show_payment_icon_ids', '=', False)]}" widget="many2many_tags"/>
                                    <field name="allow_tokenization" attrs="{'invisible': ['|', ('support_tokenization', '=', False), ('show_allow_tokenization', '=', False)]}"/>
                                    <field name="capture_manually" attrs="{'invisible': [('support_authorization', '=', False)]}"/>
                                </group>
                                <group string="Availability" name="availability">
                                    <field name="country_ids" widget="many2many_tags" placeholder="Select countries. Leave empty to use everywhere." options="{'no_open': True, 'no_create': True}"/>
                                </group>
                                <group string="Payment Followup" name="payment_followup">
                                    <field name="journal_id" context="{'default_type': 'bank'}"
                                           attrs="{'required': [('state', '!=', 'disabled'), ('provider', 'not in', ['none', 'transfer'])]}"/>
                                </group>
                            </group>
                        </page>
                        <page string="Fees" name="fees" attrs="{'invisible': [('support_fees_computation', '=', False)]}">
                            <group name="payment_fees">
                                <field name="fees_active"/>
                                <field name="fees_dom_fixed" attrs="{'invisible': [('fees_active', '=', False)]}"/>
                                <field name="fees_dom_var" attrs="{'invisible': [('fees_active', '=', False)]}"/>
                                <field name="fees_int_fixed" attrs="{'invisible': [('fees_active', '=', False)]}"/>
                                <field name="fees_int_var" attrs="{'invisible': [('fees_active', '=', False)]}"/>
                            </group>
                        </page>
                        <page string="Messages"
                            name="messages"
                            attrs="{'invisible': [('module_id', '=', True), ('module_state', '!=', 'installed')]}">
                            <group>
                                <field name="pre_msg" attrs="{'invisible': [('show_pre_msg', '=', False)]}"/>
                                <field name="pending_msg" attrs="{'invisible': [('show_pending_msg', '=', False)]}"/>
                                <field name="auth_msg" attrs="{'invisible': ['|', ('support_authorization', '=', False), ('show_auth_msg', '=', False)]}"/>
                                <field name="done_msg" attrs="{'invisible': [('show_done_msg', '=', False)]}"/>
                                <field name="cancel_msg" attrs="{'invisible': [('show_cancel_msg', '=', False)]}"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="payment_acquirer_list" model="ir.ui.view">
        <field name="name">payment.acquirer.list</field>
        <field name="model">payment.acquirer</field>
        <field name="arch" type="xml">
            <tree string="Payment Acquirers" create="false">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="provider"/>
                <field name="state"/>
                <field name="country_ids" widget="many2many_tags" optional="hide"/>
                <field name="company_id" groups="base.group_multi_company" optional="show"/>
            </tree>
        </field>
    </record>

    <record id="payment_acquirer_kanban" model="ir.ui.view">
        <field name="name">payment.acquirer.kanban</field>
        <field name="model">payment.acquirer</field>
        <field name="arch" type="xml">
            <kanban create="false" quick_create="false" class="o_kanban_payment_acquirer o_kanban_dashboard">
                <field name="id"/>
                <field name="name"/>
                <field name="description"/>
                <field name="provider"/>
                <field name="module_id"/>
                <field name="module_state"/>
                <field name="module_to_buy"/>
                <field name="color"/>
                <templates>
                    <t t-name="kanban-box">
                        <t t-set="installed" t-value="!record.module_id.value || (record.module_id.value &amp;&amp; record.module_state.raw_value === 'installed')"/>
                        <t t-set="to_buy" t-value="record.module_to_buy.raw_value === true"/>
                        <div t-attf-class="oe_kanban_global_click #{kanban_color(record.color.raw_value)}">
                            <div class="o_payment_acquirer_desc">
                                <div class="o_kanban_image">
                                    <img type="open" t-att-src="kanban_image('payment.acquirer', 'image_128', record.id.raw_value)" alt="Acquirer"/>
                                </div>
                                <h3 class="mt4"><t t-esc="record.name.value"/></h3>
                                <t t-if="record.description.raw_value" t-out="record.description.value"/>
                            </div>
                            <div class="o_payment_acquirer_bottom">
                                <t t-if="installed">
                                    <field name="state" widget="label_selection" options="{'classes': {'enabled': 'success', 'test': 'warning', 'disabled' : 'danger'}}"/>
                                </t>
                                <button t-if="!installed and !selection_mode and !to_buy" type="object" class="btn btn-secondary float-right" name="button_immediate_install">Install</button>
                                <t t-if="!installed and to_buy">
                                    <button href="https://odoo.com/pricing?utm_source=db&amp;utm_medium=module" target="_blank" class="btn btn-info btn-sm float_right">Upgrade</button>
                                    <span class="badge badge-primary oe_inline o_enterprise_label">Enterprise</span>
                                </t>
                                <button t-if="installed and record.state.raw_value == 'disabled' and !selection_mode" type="edit" class="btn btn-primary float-right">Activate</button>
                                <button t-if="installed and record.state.raw_value in ['enabled', 'test'] and !selection_mode" type="edit" class="btn btn-primary float-right">Configure</button>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="payment_acquirer_search" model="ir.ui.view">
        <field name="name">payment.acquirer.search</field>
        <field name="model">payment.acquirer</field>
        <field name="arch" type="xml">
            <search>
                <field name="name" string="Acquirer" filter_domain="['|', ('name', 'ilike', self), ('description', 'ilike', self)]"/>
                <field name="provider"/>
                <filter name="acquirer_installed" string="Installed" domain="[('module_state', '=', 'installed')]"/>
                <group expand="0" string="Group By">
                    <filter string="Provider" name="provider" context="{'group_by': 'provider'}"/>
                    <filter string="State" name="state" context="{'group_by': 'state'}"/>
                    <filter string="Company" name="company" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                </group>
            </search>
        </field>
    </record>

    <record id="action_payment_acquirer" model="ir.actions.act_window">
        <field name="name">Payment Acquirers</field>
        <field name="res_model">payment.acquirer</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new payment acquirer
            </p>
        </field>
    </record>

    <menuitem action="action_payment_acquirer"
              id="payment_acquirer_menu"
              parent="account.root_payment_menu"
              sequence="10"/>

</odoo>
