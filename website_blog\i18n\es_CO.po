# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_blog
#
# Translators:
# <AUTHOR> <EMAIL>, 2016
# <PERSON> <<EMAIL>>, 2016
# <PERSON> <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2016-02-18 13:46+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Colombia) (http://www.transifex.com/odoo/odoo-9/"
"language/es_CO/)\n"
"Language: es_CO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:117
#, python-format
msgid " Click on this button to send your blog post online."
msgstr "Clic en este botón para publicar su artículo en línea."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"\"Finally, the leading edge is being brought to the masses.\n"
"                    It will now be the turn of the big players to catch up "
"to\n"
"                    the superior technologies of the SME.\""
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"\"Odoo now competes on many fronts, with no real\n"
"                competition out there to knock them off the top spot.\n"
"                With the launch of their integrated CMS and Ecommerce\n"
"                systems,it only elevates their position as one of the "
"leading\n"
"                lights in the open source revolution. It will be at least 5\n"
"                years before another ERP or CMS provider will be able to\n"
"                compete at this level due to the technology currently\n"
"                employed by most industry providers.\""
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"\"Odoo's latest launch will allow a business to go from\n"
"                zero to trading online quicker than ever before,” Stuart\n"
"                Mackintosh, MD of Open Source specialist and Odoo\n"
"                integration partner, OpusVL, explains. “The investment\n"
"                required to have a fully automated business system is\n"
"                dramatically reduced, enabling the small and medium\n"
"                enterprise to compete at a level of functionality and\n"
"                performance previously reserved for the big IT investors.\""
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"\"This is another clever and highly disruptive move by\n"
"                Odoo,which will force other technology providers to\n"
"                take another look at the value they are providing to ensure\n"
"                that their 'solutions' can still compete.\""
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid "&amp;times;"
msgstr "&amp;times;"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_feed
msgid "&lt;?xml version=\"1.0\" encoding=\"utf-8\"?&gt;"
msgstr "&lt;?xml version=\"1.0\" encoding=\"utf-8\"?&gt;"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"A mix of push &amp; pull: Today, people\n"
"                    are victims of what others decide to push to them.\n"
"                    Odoo differentiates:"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "A new post"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_about_us
msgid "About us"
msgstr "Sobre Nosotros"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "Access post"
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:24
#, python-format
msgid "Add Content"
msgstr "Añadir Contenido"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Adding to industry leading technology"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_history
msgid "Archives"
msgstr "Archivos"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"As it comes, there is a default website based on Bootstrap\n"
"                3, the latest industry standard for rapid development of\n"
"                multi-device websites backed by Twitter, so can be directly\n"
"                integrated with many web tools and works across all devices\n"
"                by default."
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"At Odoo, we build tools to bring productivity to\n"
"                enterprises. As emails and information flows are one of\n"
"                the biggest wastes of time in companies, we have to fix\n"
"                this."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Atom Feed"
msgstr "Atom Feed"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post_author_id
#: model:ir.model.fields,field_description:website_blog.field_blog_post_create_uid
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Author"
msgstr "Autor"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post_author_avatar
msgid "Avatar"
msgstr "Avatar"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post_blog_id
#: model:ir.ui.menu,name:website_blog.menu_website_blog_root
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
#: model:website.menu,name:website_blog.menu_news
msgid "Blog"
msgstr "Blog"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog_name
msgid "Blog Name"
msgstr "Nombre del Blog"

#. module: website_blog
#: code:addons/website_blog/models/website_blog.py:283
#: model:ir.model,name:website_blog.model_blog_post
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
#, python-format
msgid "Blog Post"
msgstr "Artículo"

#. module: website_blog
#: code:addons/website_blog/models/website_blog.py:287
#, python-format
msgid "Blog Post <b>%s</b> seems to have a link to this page !"
msgstr "El artículo <b>%s</b> parece tener un enlace a esta página!"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:37
#, python-format
msgid "Blog Post Created"
msgstr "Artículo Creado"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Blog Post Title"
msgstr "Título del Artículo"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_blog_post
#: model:ir.ui.menu,name:website_blog.menu_page
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_list
msgid "Blog Posts"
msgstr "Artículos"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog_subtitle
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Blog Subtitle"
msgstr "Subtítulo del Blog"

#. module: website_blog
#: model:ir.model,name:website_blog.model_blog_tag
msgid "Blog Tag"
msgstr "Etiqueta del Blog"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_tags
#: model:ir.ui.menu,name:website_blog.menu_blog_tag
msgid "Blog Tags"
msgstr "Etiquetas del Blog"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_blog_blog
#: model:ir.model,name:website_blog.model_blog_blog
#: model:ir.ui.menu,name:website_blog.menu_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_list
msgid "Blogs"
msgstr "Blogs"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Blue"
msgstr "Azul"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_2
msgid "Building your company's website and selling your products online easy."
msgstr ""
"Construyendo el sitio web de su compañía y vendiendo sus productos en línea "
"fácilmente."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:58
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
#, python-format
msgid "Change Cover"
msgstr "Cambiar Portada"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:54
#, python-format
msgid "Change and customize your blog post cover."
msgstr "Cambiar y personalizar la portada del artículo."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:108
#, python-format
msgid "Check Mobile Preview"
msgstr "Ver la Previsualización Móvil"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:68
#, python-format
msgid "Choose an image"
msgstr "Escoja una imagen"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:69
#, python-format
msgid "Choose an image from the library."
msgstr "Escoja una imagen de la colección."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Click on \"Content\" on the top menu to write your first blog post."
msgstr ""
"Clic en \"Contenido\" en el menú superior para escribir su primer artículo."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:94
#, python-format
msgid "Click on '<em>Save</em>' button to record changes on the page."
msgstr ""
"Haga clic en el botón '<em>Guardar</em>' para grabar los cambios en la "
"página."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:77
#, python-format
msgid "Click on '<em>Save</em>' to set the picture as cover."
msgstr "Clic en '<em>Guardar</em>' para fijar la imagen como portada."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:102
#, python-format
msgid ""
"Click on the mobile icon to preview how your blog post will be displayed on "
"a mobile device."
msgstr ""
"Clic en el ícono del móvil para previsualizar cómo se vería su artículo en "
"un dispositivo móvil."

#. module: website_blog
#: model_terms:ir.actions.act_window,help:website_blog.action_blog_post
msgid "Click to create a new blog post."
msgstr "Clic para crear un nuevo artículo."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:124
#, python-format
msgid "Close Tutorial"
msgstr "Cerrar Tutorial"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Color"
msgstr "Color"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_about_us
msgid "Contact us"
msgstr "Contáctenos"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:84
#: model:ir.model.fields,field_description:website_blog.field_blog_post_content
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
#, python-format
msgid "Content"
msgstr "Contenido"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:39
#, python-format
msgid "Continue"
msgstr "Siguiente"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:60
#, python-format
msgid "Cover"
msgstr "Portada"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post_cover_properties
msgid "Cover Properties"
msgstr "Propiedades de la Portada"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:14
#, python-format
msgid "Create a blog post"
msgstr "Crear un artículo"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog_create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog_create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_post_create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_create_date
msgid "Created on"
msgstr "Creado"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:53
#, python-format
msgid "Customize Cover"
msgstr "Personalizar Portada"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_mail_compose_message_path
#: model:ir.model.fields,field_description:website_blog.field_mail_message_path
#: model:ir.model.fields,field_description:website_blog.field_survey_mail_compose_message_path
msgid "Discussion Path"
msgstr "Ruta de Charla"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog_display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_post_display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_display_name
msgid "Display Name"
msgstr "Nombre Público"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
msgid "Duplicate"
msgstr "Duplicar"

#. module: website_blog
#: model:ir.model,name:website_blog.model_mail_compose_message
msgid "Email composition wizard"
msgstr ""

#. module: website_blog
#: model:ir.model,name:website_blog.model_survey_mail_compose_message
msgid "Email composition wizard for Survey"
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid "Emails are broken."
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Emails make me waste my time. But I need them.\n"
"                Given the importance that emails have in our lives,\n"
"                it's incredible it's still one of the only software\n"
"                areas that did not evolve in the past 20 years!"
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Focus on the Content: Everything is\n"
"                    stripped to emphasize on the real message. No more\n"
"                    welcome introductions, greetings, signatures and legal\n"
"                    notes.We standardize the layout of each message.\n"
"                    (signatures are on the profile of a contact, not in\n"
"                    every message)"
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Folders and mailing lists are great tools but too\n"
"                    complex in traditional email clients. In Odoo, a\n"
"                    group of contacts that share a discussion can be\n"
"                    created with one click. Every group should have it's\n"
"                    own email address."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_follow_us
msgid "Follow us"
msgstr "Síganos"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Full Screen"
msgstr "Pantalla Completa"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Get Things Done: your inbox is a\n"
"                    todo list. You should be able to process (not only\n"
"                    read) the inbox and easily mark messages for future\n"
"                    actions. Every inbox should be empty after having\n"
"                    been processed; no more overload of information."
msgstr ""

#. module: website_blog
#: model:blog.blog,subtitle:website_blog.blog_blog_1
msgid "Get in touch with us"
msgstr "Póngase en contacto con nosotros"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Green"
msgstr "Verde"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Group By"
msgstr "Agrupar por"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
#, fuzzy
msgid "Here are the ideas behind the Odoo communication tools:"
msgstr "Ideas detrás de las herramientas de comunicación de Odoo."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "High"
msgstr "Alta"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"However, unlike other web content management systems, it\n"
"                fully integrates into the back-end database. This means\n"
"                that when you edit a product description, image or price,\n"
"                it updates the product database in real time, providing a\n"
"                true self-service window into the business."
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog_id
#: model:ir.model.fields,field_description:website_blog.field_blog_post_id
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_id
msgid "ID"
msgstr "ID"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_1
msgid "Ideas behind the Odoo communication tools."
msgstr "Ideas detrás de las herramientas de comunicación de Odoo."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid "In"
msgstr "En"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_2
msgid "Integrating your CMS and E-Commerce"
msgstr "Integrando su CMS y el Comercio Electrónico"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Keep control of what you want to receive or don't want\n"
"                    to receive. People should never receive spam. You\n"
"                    should follow/unfollow any kind of information in one\n"
"                    click."
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post_write_uid
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Last Contributor"
msgstr "Último Colaborador"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog___last_update
#: model:ir.model.fields,field_description:website_blog.field_blog_post___last_update
#: model:ir.model.fields,field_description:website_blog.field_blog_post_write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag___last_update
msgid "Last Modified on"
msgstr "Última Modificación el"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog_write_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_write_uid
msgid "Last Updated by"
msgstr "Actualizado por"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog_write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_write_date
msgid "Last Updated on"
msgstr "Actualizado"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid "Latest Posts"
msgstr "Últimos Artículos"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:18
#, python-format
msgid "Let's go through the first steps to write beautiful blog posts."
msgstr ""
"Vayamos a través de los primeros pasos para escribir artículos impactantes."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Like many modern website editors, with Odoo you can edit\n"
"                content in-line, enabling you to see exactly what you are\n"
"                changing and ensure your changes suit the context."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Low"
msgstr "Baja"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Medium"
msgstr "Media"

#. module: website_blog
#: model:ir.model,name:website_blog.model_mail_message
msgid "Message"
msgstr "Mensaje"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Messages \"for action\": they\n"
"                            require your immediate attention and you need\n"
"                            to process them all. This accounts for 10%\n"
"                            of your daily emails. Use the \"To: me\" menu\n"
"                            for these."
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Messages \"for information\":\n"
"                            you can pull them when you need some specific\n"
"                            information; they are not required to be read\n"
"                            every day.You receive only what you decided\n"
"                            to follow.This accounts for 90% of your daily\n"
"                            emails.Use the \"Inbox\" menu for these."
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:101
#, python-format
msgid "Mobile Preview"
msgstr "Previsualización Móvil"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_name
msgid "Name"
msgstr "Nombre"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Narrow"
msgstr "Estrecho"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:17
#: code:addons/website_blog/static/src/js/website.tour.blog.js:31
#: code:addons/website_blog/static/src/js/website_blog.editor.js:21
#: model_terms:ir.ui.view,arch_db:website_blog.content_new_blogpost
#, python-format
msgid "New Blog Post"
msgstr "Nuevo Artículo"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "New Features Launched"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.header_footer_custom
msgid "News"
msgstr "Noticias"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "No Cover"
msgstr "Sin Portada"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "No blog post yet."
msgstr "Todavía no hay artículos."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid "No keywords defined!"
msgstr "No se han definido palabras clave!"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post_visits
msgid "No of Views"
msgstr "Nº de Visitas"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "None"
msgstr "Ninguno"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/xml/website_blog.inline.discussion.xml:9
#, python-format
msgid "Not Published"
msgstr "No Publicado"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Odoo claims to be 'the Open Source software that makes\n"
"                building your company's website and selling your products\n"
"                online easy'. So how true is this statement?"
msgstr ""

#. module: website_blog
#: model:blog.post,website_meta_keywords:website_blog.blog_post_1
msgid "Odoo, email"
msgstr "Odoo, correo"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Opacity"
msgstr "Opacidad"

#. module: website_blog
#: model:blog.blog,name:website_blog.blog_blog_1
msgid "Our Blog"
msgstr "Nuestro Blog"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_blogs
msgid "Our Blogs"
msgstr "Nuestros Blogs"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_follow_us
msgid "Participate on our social stream."
msgstr "Participar en nuestro flujo social."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/xml/website_blog.inline.discussion.xml:28
#, python-format
msgid "Please"
msgstr "Por Favor"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/xml/website_blog.inline.discussion.xml:31
#, python-format
msgid "Post"
msgstr "Artículo"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:44
#, python-format
msgid "Post Headline"
msgstr "Publicar Encabezado"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_post_ids
msgid "Posts"
msgstr "Artículos"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Productivity is key: our smart user\n"
"                    interface does not require you to click on every mail\n"
"                    to read a thread. Reading a full thread, replying,\n"
"                    attaching documents is super fast."
msgstr ""

#. module: website_blog
#: code:addons/website_blog/controllers/main.py:268
#, python-format
msgid "Public user cannot post comments on blog post."
msgstr "Un usuario público no puede comentar los artículos del blog."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/xml/website_blog.inline.discussion.xml:10
#, python-format
msgid "Published"
msgstr "Publicado"

#. module: website_blog
#: model:mail.message.subtype,description:website_blog.mt_blog_blog_published
#: model:mail.message.subtype,name:website_blog.mt_blog_blog_published
msgid "Published Post"
msgstr "Artículo Publicado"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:116
#, python-format
msgid "Publishing status"
msgstr "Estado de publicación"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Purple"
msgstr "Morado"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post_ranking
msgid "Ranking"
msgstr "Clasificación"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
msgid "Read Next <span class=\"fa fa-long-arrow-right\"/>"
msgstr "Leer Siguiente <span class=\"fa fa-long-arrow-right\"/>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Reading my inbox is the most unproductive task I do\n"
"                on a daily basis. I have to spend one full hour a\n"
"                day to process my emails. All the junk flows in the\n"
"                same inbox; spams, information that doesn't matter,\n"
"                quoted answers of quoted answers, etc. At the end\n"
"                of the hour, only 10 emails actually requested an\n"
"                answer from me. With a good tool, I could have done\n"
"                my job in 10 minutes!"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Red"
msgstr "Rojo"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:76
#, python-format
msgid "Save"
msgstr "Guardar"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:93
#, python-format
msgid "Save your modifications once you are done"
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:109
#, python-format
msgid "Scroll to check rendering and then close the mobile preview."
msgstr ""
"Desplace para comprobar el dibujado y cierre entonces la previsualización "
"móvil."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:61
#, python-format
msgid "Select this menu item to change blog cover."
msgstr "Seleccione esta opción para cambiar la portada del blog."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:32
#, python-format
msgid "Select this menu item to create a new blog post."
msgstr "Seleccione esta opción para crear un nuevo artículo."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/xml/website_blog.inline.discussion.xml:29
#, python-format
msgid "Sign in"
msgstr "Iniciar sesión"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Size"
msgstr "Tamaño"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:19
#, python-format
msgid "Skip"
msgstr "Omitir"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:19
#, python-format
msgid "Start Tutorial"
msgstr "Comenzar Tutorial"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/models/website_blog.py:91
#: code:addons/website_blog/static/src/js/website.tour.blog.js:90
#, python-format
msgid "Start writing here..."
msgstr "Empieza a escribir aquí..."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:85
#, fuzzy, python-format
msgid "Start writing your story here."
msgstr "Empieza a escribir aquí..."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post_subtitle
msgid "Sub Title"
msgstr "Subtítulo"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
msgid "Subtitle"
msgstr "Subtítulo"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_form
msgid "Tag Form"
msgstr "Formulario de Etiqueta"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_tree
msgid "Tag List"
msgstr "Lista de Etiquetas"

#. module: website_blog
#: sql_constraint:blog.tag:0
msgid "Tag name already exists !"
msgstr "Ese nombre de etiqueta ya existe!"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post_tag_ids
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_tags
msgid "Tags"
msgstr "Etiquetas"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Technical"
msgstr "Técnico"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid "The Communication Mechanism of Odoo"
msgstr ""

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_1
#: model:blog.post,website_meta_description:website_blog.blog_post_1
msgid "The Future of Emails"
msgstr "El Futuro de los Correos Electrónicos"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:38
#, python-format
msgid "This is your new blog post. Let's edit it."
msgstr "Este es su nuevo artículo. Editémoslo."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid ""
"This page is great to improve your <strong>Search Engine Optimization</"
"strong>;\n"
"                   You can review titles, keywords and descriptions of all "
"blogs at once."
msgstr ""
"Esta página es genial para mejorar su <strong>Optimización para Motores de "
"Búsqueda</strong>;\n"
"Usted revisar los títulos, palabras clave y descripciones de todos los blogs "
"a la vez."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"This provides a single source of data for your company and\n"
"                removes the need to create offline synchronisation between\n"
"                website and product database."
msgstr ""

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:123
#, fuzzy, python-format
msgid ""
"This tutorial is over. To discover more features and improve the content of "
"this page, go to the upper left customize menu. You can also add some cool "
"content with your text in the edit mode with the upper right button."
msgstr ""
"Este tutorial ha terminado. Para descubrir más características y mejorar el "
"contenido de esta página, vaya al menú de personalización  en la parte "
"superior derecha. También puede añadir un poco de contenido actual en su "
"texto haciendo clic en \"Inserción de bloques\" en el modo de edición."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post_name
msgid "Title"
msgstr "Título"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"To add to an already comprehensive set of Odoo\n"
"                    features, a website content management system (CMS\n"
"                    or WMS) has been developed and a beta release is\n"
"                    available from today, 31st January 2014."
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"To disrupt emails, you need more than just another user\n"
"                interface. We need to rethink the whole communication flow."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Untitled Post"
msgstr "Artículo sin Título"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:25
#, fuzzy, python-format
msgid ""
"Use this button to create a new blog post like any other document (page, "
"menu, products, event, ...)."
msgstr ""
"Utilice este menú <em>'Contenido'</em> para crear un nuevo artículo como "
"cualquier otro documento (página, menú, productos, eventos, ...)."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_form
msgid "Used in:"
msgstr "Usado en:"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_mail_compose_message_path
#: model:ir.model.fields,help:website_blog.field_mail_message_path
#: model:ir.model.fields,help:website_blog.field_survey_mail_compose_message_path
msgid ""
"Used to display messages in a paragraph-based chatter using a unique path;"
msgstr ""
"Usado para mostrar mensajes en una sala de conversación basada en párrafos "
"usando una única ruta;"

#. module: website_blog
#: code:addons/website_blog/models/website_blog.py:262
#, python-format
msgid "View Blog Post"
msgstr "Ver Artículo"

#. module: website_blog
#: model:ir.model,name:website_blog.model_website
msgid "Website"
msgstr "Sitio Web"

#. module: website_blog
#: model:ir.actions.act_url,name:website_blog.action_open_website
msgid "Website Blogs"
msgstr "Blogs del Sitio Web"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post_website_message_ids
msgid "Website Messages"
msgstr "Mensajes del Sitio Web"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_post_website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicación del sitio web"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/xml/website_blog.inline.discussion.xml:26
#, python-format
msgid "Write a comment..."
msgstr "Escribir un comentario..."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_rc_about_us
msgid ""
"Write a small text here for when <b>new visitors</b> find your website\n"
"            through your <b>blog entries</b>, referenced in Google."
msgstr ""
"Escriba un texto corto aquí para que cuando <b>nuevos visitantes</b> "
"encuentren su sitio web\n"
"a través de sus <b>artículos</b>, referenciados en Google."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website.tour.blog.js:46
#, python-format
msgid "Write a title, the subtitle is optional."
msgstr "Escribe un título, el subtitulo es opcional."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Yellow"
msgstr "Amarillo"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid ""
"You should <strong>add a banner on the top</strong> as it is a frequent "
"landing page for new visitors.\n"
"                   <span class=\"text-muted\">This box will not be visible "
"to your visitors.</span>"
msgstr ""
"Usted puede <strong> añadir un banner en la parte superior </strong>, ya que "
"es una página de destino frecuente para los nuevos visitantes. <span class = "
"\"text-silenciado\"> Esta caja no será visible a sus visitantes. </span>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
#, fuzzy
msgid "blog. Click here to access the blog :"
msgstr "Pulse aquí para acceder a la entrada."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/xml/website_blog.inline.discussion.xml:14
#, python-format
msgid "by"
msgstr "por"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid "comment"
msgstr "comentario"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid "comments"
msgstr "comentarios"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
#, fuzzy
msgid "has been published on the"
msgstr "Se ha publicado un nuevo artículo %s en el blog %s."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "not published"
msgstr "no publicado"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
#: model_terms:ir.ui.view,arch_db:website_blog.latest_blogs
msgid "pull-right"
msgstr "pull-right"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/xml/website_blog.inline.discussion.xml:29
#, python-format
msgid "to comment."
msgstr "a comentar."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "view"
msgstr "vista"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "views"
msgstr "vistas"

#~ msgid ""
#~ "<div class=\"container\">\n"
#~ "        <section class=\"mt16 mb16 readable\">\n"
#~ "            <iframe width=\"100%\" height=\"250\" src=\"http://www."
#~ "youtube.com/embed/EkbBFmIWoTE\" frameborder=\"0\" allowfullscreen></"
#~ "iframe>\n"
#~ "            <p data-chatter-id=\"counter_52500/div/section\">\n"
#~ "                Emails are broken.\n"
#~ "            </p><p data-chatter-id=\"counter_15339/div/section\">\n"
#~ "                Emails make me waste my time. But I need them.\n"
#~ "                Given the importance that emails have in our lives,\n"
#~ "                it's incredible it's still one of the only software\n"
#~ "                areas that did not evolve in the past 20 years!\n"
#~ "            </p><p data-chatter-id=\"counter_98391/div/section\">\n"
#~ "                Reading my inbox is the most unproductive task I do\n"
#~ "                on a daily basis. I have to spend one full hour a\n"
#~ "                day to process my emails. All the junk flows in the\n"
#~ "                same inbox; spams, information that doesn't matter,\n"
#~ "                quoted answers of quoted answers, etc. At the end\n"
#~ "                of the hour, only 10 emails actually requested an\n"
#~ "                answer from me. With a good tool, I could have done\n"
#~ "                my job in 10 minutes!\n"
#~ "            </p>\n"
#~ "        </section>\n"
#~ "        <section class=\"mt16 mb16 readable\">\n"
#~ "            <p data-chatter-id=\"counter_99844/div/section\">\n"
#~ "                At Odoo, we build tools to bring productivity to\n"
#~ "                enterprises. As emails and information flows are one of\n"
#~ "                the biggest wastes of time in companies, we have to fix\n"
#~ "                this.\n"
#~ "            </p><p data-chatter-id=\"counter_72514/div/section\">\n"
#~ "                To disrupt emails, you need more than just another user\n"
#~ "                interface. We need to rethink the whole communication "
#~ "flow.\n"
#~ "            </p>\n"
#~ "            <h3>The Communication Mechanism of Odoo</h3>\n"
#~ "            <p data-chatter-id=\"counter_44333/div/section\">\n"
#~ "                Here are the ideas behind the Odoo communication tools:\n"
#~ "            </p>\n"
#~ "            <ul>\n"
#~ "                <li>\n"
#~ "                    Get Things Done: your inbox is a\n"
#~ "                    todo list. You should be able to process (not only\n"
#~ "                    read) the inbox and easily mark messages for future\n"
#~ "                    actions. Every inbox should be empty after having\n"
#~ "                    been processed; no more overload of information.\n"
#~ "                    <img class=\"img-responsive\" src=\"/website_blog/"
#~ "static/src/img/mail-sc-00.png\">\n"
#~ "                </li><li>\n"
#~ "                    Keep control of what you want to receive or don't "
#~ "want\n"
#~ "                    to receive. People should never receive spam. You\n"
#~ "                    should follow/unfollow any kind of information in "
#~ "one\n"
#~ "                    click.\n"
#~ "                </li><li>\n"
#~ "                    Productivity is key: our smart user\n"
#~ "                    interface does not require you to click on every "
#~ "mail\n"
#~ "                    to read a thread. Reading a full thread, replying,\n"
#~ "                    attaching documents is super fast.\n"
#~ "                    <img class=\"img-responsive\" src=\"/website_blog/"
#~ "static/src/img/mail-sc-03.png\">\n"
#~ "                </li><li>\n"
#~ "                    A mix of push &amp; pull: Today, people\n"
#~ "                    are victims of what others decide to push to them.\n"
#~ "                    Odoo differentiates:\n"
#~ "                    <ul>\n"
#~ "                        <li>\n"
#~ "                            Messages \"for information\":\n"
#~ "                            you can pull them when you need some "
#~ "specific\n"
#~ "                            information; they are not required to be "
#~ "read\n"
#~ "                            every day.You receive only what you decided\n"
#~ "                            to follow.This accounts for 90% of your "
#~ "daily\n"
#~ "                            emails.Use the \"Inbox\" menu for these.\n"
#~ "                        </li><li>\n"
#~ "                            Messages \"for action\": they\n"
#~ "                            require your immediate attention and you "
#~ "need\n"
#~ "                            to process them all. This accounts for 10%\n"
#~ "                            of your daily emails. Use the \"To: me\" "
#~ "menu\n"
#~ "                            for these.\n"
#~ "                        </li>\n"
#~ "                    </ul>\n"
#~ "                </li><li>\n"
#~ "                    Focus on the Content: Everything is\n"
#~ "                    stripped to emphasize on the real message. No more\n"
#~ "                    welcome introductions, greetings, signatures and "
#~ "legal\n"
#~ "                    notes.We standardize the layout of each message.\n"
#~ "                    (signatures are on the profile of a contact, not in\n"
#~ "                    every message)\n"
#~ "                </li><li>\n"
#~ "                    Folders and mailing lists are great tools but too\n"
#~ "                    complex in traditional email clients. In Odoo, a\n"
#~ "                    group of contacts that share a discussion can be\n"
#~ "                    created with one click. Every group should have it's\n"
#~ "                    own email address.\n"
#~ "                </li>\n"
#~ "            </ul>\n"
#~ "        </section>\n"
#~ "    </div>\n"
#~ "\n"
#~ msgstr ""
#~ "<div class=\"container\">\n"
#~ "<section class=\"mt16 mb16 readable\">\n"
#~ "<iframe width=\"100%\" height=\"250\" src=\"http://www.youtube.com/embed/"
#~ "EkbBFmIWoTE\" frameborder=\"0\" allowfullscreen></iframe>\n"
#~ "<p data-chatter-id=\"counter_52500/div/section\">\n"
#~ "Emails are broken.\n"
#~ "</p><p data-chatter-id=\"counter_15339/div/section\">\n"
#~ "Los correos electrónicos hacen perder el tiempo. Pero se los necesita.\n"
#~ "Dada la importancia que los correos electrónicos tienen en nuestras "
#~ "vidas,\n"
#~ "es increíble que sigue siendo uno de los únicas áreas de programación\n"
#~ "¡que no evolucionaron en los últimos 20 años!\n"
#~ "</p><p data-chatter-id=\"counter_98391/div/section\">\n"
#~ "La lectura de mi bandeja de entrada es la tarea más productiva que hago\n"
#~ "diariamente. Tengo que pasar una hora cada día para procesar \n"
#~ "mis correos electrónicos. Todos los flujos de correo chatarra están en "
#~ "la\n"
#~ "misma bandeja de entrada; spams, información que no importa, \n"
#~ "respuestas de respuestas, etc. Al final de la hora, sólo 10 correos\n"
#~ "electrónicos solicitados en realidad sirven para mí. \n"
#~ "Con una buena herramienta, que podría haber hecho mi trabajo en 10 "
#~ "minutos!\n"
#~ "</p>\n"
#~ "</section>\n"
#~ "<section class=\"mt16 mb16 readable\">\n"
#~ "<p data-chatter-id=\"counter_99844/div/section\">\n"
#~ "En Odoo, construimos herramientas para llevar la productividad a\n"
#~ "empresas. Como los correos electrónicos y los flujos de información son "
#~ "uno de\n"
#~ "las mayores pérdidas de tiempo en las empresas, que tienen que arreglar\n"
#~ "esta.\n"
#~ "</p><p data-chatter-id=\"counter_72514/div/section\">\n"
#~ "Para interrumpir correos electrónicos, es necesario algo más que otra "
#~ "interfaz de usuario. \n"
#~ "Tenemos que repensar todo el flujo de comunicación.\n"
#~ "</p>\n"
#~ "<h3>El mecanismo de comunicación de Odoo</h3>\n"
#~ "<p data-chatter-id=\"counter_44333/div/section\">\n"
#~ "Estas son las ideas detrás de las herramientas de comunicación Odoo:\n"
#~ "</p>\n"
#~ "<ul>\n"
#~ "<li>\n"
#~ "Hacer las cosas: la bandeja de entrada es un\n"
#~ "lista de pendientes. Usted debe ser capaz de procesar (no sólo\n"
#~ "leer) la bandeja de entrada y marcar fácilmente los mensajes para el "
#~ "futuro\n"
#~ "comportamiento. Cada bandeja de entrada debe estar vacío después de "
#~ "haber\n"
#~ "ha procesado; no más la sobrecarga de información.\n"
#~ "<img class=\"img-responsive\" src=\"/website_blog/static/src/img/mail-"
#~ "sc-00.png\">\n"
#~ "</li><li>\n"
#~ "Mantenga el control de lo que desea recibir o no quieren\n"
#~ "para recibir. La gente nunca deben recibir spam. usted\n"
#~ "debe seguir / dejar de seguir cualquier tipo de información en un solo\n"
#~ "hacer clic.\n"
#~ "</li><li>\n"
#~ "La productividad es clave: nuestro interfaz inteligente\n"
#~ "no requiere que usted haga clic en cada correo para\n"
#~ "leer un hilo. Lectura completa y acelerada respuesta,\n"
#~ "adjuntando los documentos fácilmente.\n"
#~ "<img class=\"img-responsive\" src=\"/website_blog/static/src/img/mail-"
#~ "sc-03.png\">\n"
#~ "</li><li>\n"
#~ "Una mezcla de enviar y recibir: Hoy en día, la gente\n"
#~ "son víctimas de lo que otros deciden enviar a ellos.\n"
#~ "Odoo diferencia:\n"
#~ "<ul>\n"
#~ "<li>\n"
#~ "Mensajes \"para información\":\n"
#~ "usted puede tirar de ellos cuando se necesita alguna información \n"
#~ "específica; que no están obligados a ser leído diariamente\n"
#~ "Usted solo recibe lo que decidiste seguir.\n"
#~ "Esto representa el 90% de su correo diario.\n"
#~ "Use el menú \"Bandeja de entrada\" para estos.\n"
#~ "</li><li>\n"
#~ "Mensajes \"para la acción\": se\n"
#~ "requieren su atención inmediata y necesita\n"
#~ "para procesar. Esto representa el 10%\n"
#~ "de sus correos electrónicos diarios. \n"
#~ "Utilice la opción de menú \"Para: mi\" \n"
#~ "para éstos.\n"
#~ "</li>\n"
#~ "</ul>\n"
#~ "</li><li>\n"
#~ "Centrarse en el contenido: Todo es\n"
#~ "revisado profundamente para enfatizar el contenido real \n"
#~ "en el mensaje. No más introducciones de bienvenida, \n"
#~ "saludos, firmas y notas legales.\n"
#~ "Nosotros estandarizamos la distribución de cada mensaje.\n"
#~ "(las firmas están en el perfil de un contacto, no en\n"
#~ "cada mensaje)\n"
#~ "</li><li>\n"
#~ "Carpetas y listas de correo son una gran herramienta, pero también\n"
#~ "se tornan complejas en los clientes de correo tradicionales. \n"
#~ "En Odoo, un grupo de contactos que comparten en una discusión que \n"
#~ "puede ser creado con un solo clic. Cada grupo debe tener su\n"
#~ "dirección de correo electrónico propia.\n"
#~ "</li>\n"
#~ "</ul>\n"
#~ "</section>\n"
#~ "</div>\n"

#~ msgid ""
#~ "<section><div class=\"container\">\n"
#~ "            <div class=\"row\">\n"
#~ "                <div class=\"col-md-6 mt16\"><img class=\"img img-"
#~ "responsive mb16\" src=\"/website_blog/static/src/img/CMS_WMS_screens.jpg"
#~ "\" style=\"\"></div>\n"
#~ "\n"
#~ "                <div class=\"col-md-6 mt16\">\n"
#~ "                    <h3>New Features Launched</h3>\n"
#~ "\n"
#~ "                    <p data-chatter-id=\"counter_0/section/div/div/div"
#~ "\">To add to an already comprehensive set of Odoo\n"
#~ "                    features, a website content management system (CMS\n"
#~ "                    or WMS) has been developed and a beta release is\n"
#~ "                    available from today, 31st January 2014.</p>\n"
#~ "                </div>\n"
#~ "            </div>\n"
#~ "        \n"
#~ "        <section class=\"readable\">\n"
#~ "            <p data-chatter-id=\"counter_75695/section/div/section\">\n"
#~ "                Odoo claims to be 'the Open Source software that makes\n"
#~ "                building your company's website and selling your "
#~ "products\n"
#~ "                online easy'. So how true is this statement?\n"
#~ "            </p><p data-chatter-id=\"counter_63354/section/div/section"
#~ "\">\n"
#~ "                \"Odoo's latest launch will allow a business to go from\n"
#~ "                zero to trading online quicker than ever before,&#8221; "
#~ "Stuart\n"
#~ "                Mackintosh, MD of Open Source specialist and Odoo\n"
#~ "                integration partner, OpusVL, explains. &#8220;The "
#~ "investment\n"
#~ "                required to have a fully automated business system is\n"
#~ "                dramatically reduced, enabling the small and medium\n"
#~ "                enterprise to compete at a level of functionality and\n"
#~ "                performance previously reserved for the big IT investors."
#~ "\"\n"
#~ "            </p>\n"
#~ "            <blockquote>\n"
#~ "                <p data-chatter-id=\"counter_92702/section/div/section/"
#~ "blockquote\">\n"
#~ "                    \"Finally, the leading edge is being brought to the "
#~ "masses.\n"
#~ "                    It will now be the turn of the big players to catch "
#~ "up to\n"
#~ "                    the superior technologies of the SME.\"\n"
#~ "                </p>\n"
#~ "            </blockquote>\n"
#~ "            <p data-chatter-id=\"counter_70446/section/div/section\">\n"
#~ "                \"This is another clever and highly disruptive move by\n"
#~ "                Odoo,which will force other technology providers to\n"
#~ "                take another look at the value they are providing to "
#~ "ensure\n"
#~ "                that their 'solutions' can still compete.\"\n"
#~ "            </p><p data-chatter-id=\"counter_44493/section/div/section"
#~ "\">\n"
#~ "                \"Odoo now competes on many fronts, with no real\n"
#~ "                competition out there to knock them off the top spot.\n"
#~ "                With the launch of their integrated CMS and Ecommerce\n"
#~ "                systems,it only elevates their position as one of the "
#~ "leading\n"
#~ "                lights in the open source revolution. It will be at least "
#~ "5\n"
#~ "                years before another ERP or CMS provider will be able to\n"
#~ "                compete at this level due to the technology currently\n"
#~ "                employed by most industry providers.\"\n"
#~ "            </p>\n"
#~ "            <h4>Adding to industry leading technology</h4>\n"
#~ "            <p data-chatter-id=\"counter_41774/section/div/section\">\n"
#~ "                Like many modern website editors, with Odoo you can edit\n"
#~ "                content in-line, enabling you to see exactly what you "
#~ "are\n"
#~ "                changing and ensure your changes suit the context.\n"
#~ "            </p><p data-chatter-id=\"counter_58203/section/div/section"
#~ "\">\n"
#~ "                However, unlike other web content management systems, it\n"
#~ "                fully integrates into the back-end database. This means\n"
#~ "                that when you edit a product description, image or "
#~ "price,\n"
#~ "                it updates the product database in real time, providing "
#~ "a\n"
#~ "                true self-service window into the business.\n"
#~ "            </p><p data-chatter-id=\"counter_39750/section/div/section"
#~ "\">\n"
#~ "                This provides a single source of data for your company "
#~ "and\n"
#~ "                removes the need to create offline synchronisation "
#~ "between\n"
#~ "                website and product database.\n"
#~ "            </p><p data-chatter-id=\"counter_74064/section/div/section"
#~ "\">\n"
#~ "                As it comes, there is a default website based on "
#~ "Bootstrap\n"
#~ "                3, the latest industry standard for rapid development of\n"
#~ "                multi-device websites backed by Twitter, so can be "
#~ "directly\n"
#~ "                integrated with many web tools and works across all "
#~ "devices\n"
#~ "                by default.\n"
#~ "            </p>\n"
#~ "        </section>\n"
#~ "\n"
#~ "</div></section>"
#~ msgstr ""
#~ "<section><div class=\"container\">\n"
#~ "<div class=\"row\">\n"
#~ "<div class=\"col-md-6 mt16\"><img class=\"img img-responsive mb16\" src="
#~ "\"/website_blog/static/src/img/CMS_WMS_screens.jpg\" style=\"\"></div>\n"
#~ "<div class=\"col-md-6 mt16\">\n"
#~ "<h3>Nuevas características lanzadas</h3>\n"
#~ "<p data-chatter-id=\"counter_0/section/div/div/div\">\n"
#~ "Se han añadido a un conjunto de características Odoo,\n"
#~ "como un sistema de gestión de contenidos web (CMS\n"
#~ "o WMS) que se han desarrollado y que tendrá una versión \n"
#~ "beta disponible a partir de hoy, 31 de enero de 2014.\n"
#~ "</p>\n"
#~ "</div>\n"
#~ "</div>\n"
#~ "<section class=\"readable\">\n"
#~ "<p data-chatter-id=\"counter_75695/section/div/section\">\n"
#~ "Odoo dice ser 'el software de código abierto que hace\n"
#~ "la construcción de la página web de su empresa y la venta de sus "
#~ "productos\n"
#~ "línea de una forma fácil'. ¿Cómo es verdadera esta afirmación?\n"
#~ "</p><p data-chatter-id=\"counter_63354/section/div/section\">\n"
#~ "\"El último lanzamiento  de Odoo permitirá a una empresa ir desde\n"
#~ "cero a una negociación en línea más rápido que nunca antes \", Stuart\n"
#~ "Mackintosh, director general de especialista de código abierto y \n"
#~ "socio de integración Odoo, OpusVL, explica. \"La inversión que\n"
#~ "requiere tener un sistema de negocio es totalmente automatizado es\n"
#~ "drásticamente reducida, permitiendo a la pequeña y mediana\n"
#~ "la empresa para competir a un nivel de funcionalidad y\n"
#~ "rendimiento hasta ahora reservado para los grandes inversionistas de TI"
#~ "\".\n"
#~ "</p>\n"
#~ "<blockquote>\n"
#~ "<p data-chatter-id=\"counter_92702/section/div/section/blockquote\">\n"
#~ "\"Por último, el líder está atrayendo a las masas.\n"
#~ "Ahora será el turno de los grandes jugadores para ponerse al día con\n"
#~ "las tecnologías superiores de para la pequeña y mediana Empresa\".\n"
#~ "</p>\n"
#~ "</blockquote>\n"
#~ "<p data-chatter-id=\"counter_70446/section/div/section\">\n"
#~ "\"Este es otro movimiento inteligente y altamente competitivo para\n"
#~ "Odoo, lo que obligará a otros proveedores de tecnología a\n"
#~ "echar otro vistazo a el valor que están aportando para garantizar\n"
#~ "que sus \"soluciones\" todavía pueden competir \".\n"
#~ "</p><p data-chatter-id=\"counter_44493/section/div/section\">\n"
#~ "\"Odoo ahora compite en muchos frentes, sin verdadera\n"
#~ "competencia existente para eliminarlos de la primera posición.\n"
#~ "Con el lanzamiento de su CMS integrado y sistema de comercio \n"
#~ "electrónico, sólo eleva su posición como uno de los principales\n"
#~ "luces en la revolución de código abierto. Será por lo menos 5\n"
#~ "años antes de que otro ERP o proveedor de CMS podrán\n"
#~ "competir a este nivel debido a la tecnología actualmente\n"
#~ "empleado por la mayoría de los proveedores de la industria \".\n"
#~ "</p>\n"
#~ "<h4>Agregando a la tecnología líder en la industria</h4>\n"
#~ "<p data-chatter-id=\"counter_41774/section/div/section\">\n"
#~ "Al igual que muchos editores de sitios web modernos, con Odoo \n"
#~ "puede editar contenido en línea, lo que le permite ver exactamente \n"
#~ "lo que es el cambio y asegurarse de que se adaptan al contexto.\n"
#~ "</p><p data-chatter-id=\"counter_58203/section/div/section\">\n"
#~ "Sin embargo, a diferencia de otros sistemas de gestión de contenidos "
#~ "web,\n"
#~ "integra plenamente en la base de datos back-end. Esto significa\n"
#~ "que cuando se edita una descripción del producto, la imagen o el precio,\n"
#~ "se actualiza la base de datos de productos en tiempo real, proporcionando "
#~ "una\n"
#~ "verdadera ventana de auto-servicio en el negocio.\n"
#~ "</p><p data-chatter-id=\"counter_39750/section/div/section\">\n"
#~ "Esto proporciona una única fuente de datos para su empresa y\n"
#~ "elimina la necesidad de crear la sincronización sin conexión entre\n"
#~ "página web y base de datos de productos.\n"
#~ "</p><p data-chatter-id=\"counter_74064/section/div/section\">\n"
#~ "Como se trata, hay un sitio web predeterminado basado en Bootstrap\n"
#~ "3, el último estándar de la industria para el desarrollo rápido de\n"
#~ "sitios web multi-dispositivo respaldados por Twitter, por lo que puede \n"
#~ "ser directamente integrado con muchas herramientas web y funciona en \n"
#~ "todos los dispositivos por defecto.\n"
#~ "</p>\n"
#~ "</section>\n"
#~ "</div></section>"

#~ msgid "Action Needed"
#~ msgstr "Acción Requerida"

#~ msgid "Date of the last message posted on the record."
#~ msgstr "Fecha del último mensaje publicado en el registro."

#~ msgid "Followers"
#~ msgstr "Seguidores"

#~ msgid "Followers (Channels)"
#~ msgstr "Seguidores (Canales)"

#~ msgid "Followers (Partners)"
#~ msgstr "Seguidores (Asociados)"

#~ msgid "If checked new messages require your attention."
#~ msgstr "Si está marcado, hay nuevos mensajes que requieren su atención"

#~ msgid "If checked, new messages require your attention."
#~ msgstr "Si está marcado, los nuevos mensajes requerirán su atención."

#~ msgid "Is Follower"
#~ msgstr "Es Seguidor"

#~ msgid "Last Message Date"
#~ msgstr "Fecha del Último Mensaje"

#~ msgid "Messages"
#~ msgstr "Mensajes"

#~ msgid "Messages and communication history"
#~ msgstr "Historial de mensajes y de comunicación"

#~ msgid "Number of Actions"
#~ msgstr "Número de Acciones"

#~ msgid "Number of messages which requires an action"
#~ msgstr "Número de mensajes que requieren una acción"

#~ msgid "Number of unread messages"
#~ msgstr "Número de mensajes no leidos"

#~ msgid "Save Your Blog"
#~ msgstr "Guardar Su Blog"

#~ msgid ""
#~ "Start writing your story here. Click on save in the upper left corner "
#~ "when you are done."
#~ msgstr ""
#~ "Comience a escribir su historia aquí. Haga clic en Guardar en la esquina "
#~ "superior izquierda cuando haya terminado."

#~ msgid "The full URL to access the document through the website."
#~ msgstr "La URL completa para acceder al documento a través del sitio web."

#~ msgid "Unread Messages"
#~ msgstr "Mensajes sin Leer"

#~ msgid "Unread Messages Counter"
#~ msgstr "Contador de Mensajes no Leídos"

#~ msgid "Visible in Website"
#~ msgstr "Visible en el Sitio Web"

#~ msgid "Website URL"
#~ msgstr "URL del Sitio Web"

#~ msgid "Website meta description"
#~ msgstr "Meta descripción del sitio web"

#~ msgid "Website meta keywords"
#~ msgstr "Meta palabras clave del sitio web"

#~ msgid "Website meta title"
#~ msgstr "Meta título del sitio web"

#~ msgid "on"
#~ msgstr "en"
