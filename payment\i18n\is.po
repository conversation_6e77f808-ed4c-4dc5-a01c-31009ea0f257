# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * payment
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:17+0000\n"
"PO-Revision-Date: 2018-08-24 09:21+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Icelandic (https://www.transifex.com/odoo/teams/41243/is/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: is\n"
"Plural-Forms: nplurals=2; plural=(n % 10 != 1 || n % 100 == 11);\n"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__invoice_ids_nbr
msgid "# of Invoices"
msgstr "# reikninga"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_confirmation_status
msgid "&amp;times;"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__html_3ds
msgid "3D Secure HTML"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<b>Amount:</b>"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_confirmation_status
msgid "<b>Communication: </b>"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<b>Reference:</b>"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "<i class=\"fa fa-arrow-circle-right\"/> Back to My Account"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_tokens_list
msgid "<i class=\"fa fa-lock\"> Pay</i>"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_tokens_list
msgid "<i class=\"fa fa-plus-circle\"> Add new card</i>"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_tokens_list
msgid "<i class=\"fa fa-trash\"/> Delete"
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:124
#, python-format
msgid "<i>Cancel,</i> Your payment has been cancelled."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:120
#, python-format
msgid ""
"<i>Done,</i> Your online payment has been successfully processed. Thank you "
"for your order."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:128
#, python-format
msgid ""
"<i>Error,</i> Please be aware that an error occurred during the transaction."
" The order has been confirmed but will not be paid. Do not hesitate to "
"contact us if you have any questions on the status of your order."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:116
#, python-format
msgid ""
"<i>Pending,</i> Your online payment has been successfully processed. But "
"your order is not validated yet."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:352
#, python-format
msgid "<p>This card is currently linked to the following records:<p/>"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "<span class=\"fa fa-arrow-right\"> Create a PayPal account</span>"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "<span class=\"fa fa-arrow-right\"> Get my Stripe keys</span>"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.view_partners_form_payment_defaultcreditcard
msgid "<span class=\"o_stat_text\">Credit card(s)</span>"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid ""
"<span class=\"o_warning_text\">Test</span>\n"
"                                    <span class=\"o_stat_text\">Environment</span>"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid ""
"<span class=\"text-success\">Production</span>\n"
"                                    <span class=\"o_stat_text\">Environment</span>"
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_custom
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_ogone
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_payu
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_stripe
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_transfer
msgid "<span><i>Cancel,</i> Your payment has been cancelled.</span>"
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_custom
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_ogone
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_payu
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_stripe
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_transfer
msgid ""
"<span><i>Done,</i> Your online payment has been successfully processed. "
"Thank you for your order.</span>"
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,error_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,error_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,error_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,error_msg:payment.payment_acquirer_custom
#: model_terms:payment.acquirer,error_msg:payment.payment_acquirer_ogone
#: model_terms:payment.acquirer,error_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,error_msg:payment.payment_acquirer_payu
#: model_terms:payment.acquirer,error_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,error_msg:payment.payment_acquirer_stripe
#: model_terms:payment.acquirer,error_msg:payment.payment_acquirer_transfer
msgid ""
"<span><i>Error,</i> Please be aware that an error occurred during the "
"transaction. The order has been confirmed but will not be paid. Do not "
"hesitate to contact us if you have any questions on the status of your "
"order.</span>"
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_custom
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_ogone
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_payu
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_stripe
msgid ""
"<span><i>Pending,</i> Your online payment has been successfully processed. "
"But your order is not validated yet.</span>"
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_transfer
msgid ""
"<span><i>Pending</i>... The order will be validated after the "
"payment.</span>"
msgstr ""

#. module: payment
#: code:addons/payment/models/account_invoice.py:72
#, python-format
msgid "A journal must be specified of the acquirer %s."
msgstr ""

#. module: payment
#: code:addons/payment/models/account_invoice.py:65
#, python-format
msgid "A payment acquirer is required to create a transaction."
msgstr ""

#. module: payment
#: code:addons/payment/models/account_payment.py:55
#, python-format
msgid "A payment transaction already exists."
msgstr ""

#. module: payment
#: code:addons/payment/models/account_payment.py:57
#, python-format
msgid "A token is required to create a new payment transaction."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:657
#, python-format
msgid "A transaction %s with %s initiated using %s credit card."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:663
#, python-format
msgid "A transaction %s with %s initiated."
msgstr ""

#. module: payment
#: code:addons/payment/models/account_invoice.py:36
#, python-format
msgid "A transaction can't be linked to invoices having different currencies."
msgstr ""

#. module: payment
#: code:addons/payment/models/account_invoice.py:41
#, python-format
msgid "A transaction can't be linked to invoices having different partners."
msgstr ""

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:34
#, python-format
msgid "Account"
msgstr "Bókhaldslykill"

#. module: payment
#: model:ir.model,name:payment.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__acc_number
msgid "Account Number"
msgstr "Account Number"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__acquirer_id
#: model_terms:ir.ui.view,arch_db:payment.acquirer_kanban
msgid "Acquirer"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__acquirer_id
msgid "Acquirer Account"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__acquirer_ref
msgid "Acquirer Ref."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__acquirer_reference
msgid "Acquirer Reference"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_icon__acquirer_ids
msgid "Acquirers"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_icon_form_view
msgid "Acquirers list"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_kanban
msgid "Activate"
msgstr "Activate"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__active
msgid "Active"
msgstr "Virkur"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_active
msgid "Add Extra Fees"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_address
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "Address"
msgstr "Heimilisfang"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_adyen
#: selection:payment.acquirer,provider:0
msgid "Adyen"
msgstr ""

#. module: payment
#: selection:payment.acquirer,save_token:0
msgid "Always"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__amount
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Amount"
msgstr "Upphæð"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:17
#, python-format
msgid "An error occured during the processing of this payment."
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "Apply"
msgstr "Virkja"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.account_invoice_view_form_inherit_payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid ""
"Are you sure you want to void the authorized transaction? This action can't "
"be undone."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__authorize_implemented
msgid "Authorize Mechanism Supported"
msgstr ""

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_authorize
#: selection:payment.acquirer,provider:0
msgid "Authorize.Net"
msgstr ""

#. module: payment
#: selection:payment.transaction,state:0
msgid "Authorized"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_invoice__authorized_transaction_ids
msgid "Authorized Transactions"
msgstr ""

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:34
#, python-format
msgid "Bank"
msgstr "Banki"

#. module: payment
#: model_terms:payment.acquirer,post_msg:payment.payment_acquirer_custom
#: model_terms:payment.acquirer,post_msg:payment.payment_acquirer_transfer
msgid "Bank Account"
msgstr "Bankareikningur"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__journal_name
msgid "Bank Name"
msgstr "Nafn banka"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_buckaroo
#: selection:payment.acquirer,provider:0
msgid "Buckaroo"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_res_id
msgid "Callback Document ID"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_model_id
msgid "Callback Document Model"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_hash
msgid "Callback Hash"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_method
msgid "Callback Method"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:360
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
#, python-format
msgid "Cancel"
msgstr "Hætta við"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__cancel_msg
msgid "Cancel Message"
msgstr ""

#. module: payment
#: selection:payment.transaction,state:0
msgid "Canceled"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:109
#, python-format
msgid "Cancelled payments"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:190
#, python-format
msgid "Cannot set-up the payment"
msgstr ""

#. module: payment
#: code:addons/payment/controllers/portal.py:225
#, python-format
msgid "Cannot setup the payment."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__capture_manually
msgid "Capture Amount Manually"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.account_invoice_view_form_inherit_payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "Capture Transaction"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__capture_manually
msgid "Capture the amount from Odoo, when the delivery is completed."
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "Check here"
msgstr ""

#. module: payment
#: model:ir.actions.act_window,name:payment.action_open_payment_onboarding_payment_acquirer_wizard
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "Choose a payment method"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_city
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "City"
msgstr "Staður"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:40
#, python-format
msgid "Click here to be redirected to the confirmation page."
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_confirmation_status
msgid "Close"
msgstr "Loka"

#. module: payment
#: model_terms:payment.acquirer,post_msg:payment.payment_acquirer_custom
#: model_terms:payment.acquirer,post_msg:payment.payment_acquirer_transfer
msgid "Communication"
msgstr "Communication"

#. module: payment
#: model:ir.model,name:payment.model_res_company
msgid "Companies"
msgstr "Fyrirtæki"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__company_id
msgid "Company"
msgstr "Fyrirtæki"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "Configuration"
msgstr "Uppsetning"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_kanban
msgid "Configure"
msgstr "Configure"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.onboarding_payment_acquirer_step
msgid "Configure your payment methods."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:359
#, python-format
msgid "Confirm Deletion"
msgstr ""

#. module: payment
#: model:ir.model,name:payment.model_res_partner
msgid "Contact"
msgstr "Tengiliður"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__module_id
msgid "Corresponding Module"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_partner__payment_token_count
#: model:ir.model.fields,field_description:payment.field_res_users__payment_token_count
msgid "Count Payment Token"
msgstr "Count Payment Token"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__country_ids
msgid "Countries"
msgstr "Lönd"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_country_id
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "Country"
msgstr "Land"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_acquirer
msgid "Create a new payment acquirer"
msgstr ""

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_transaction
msgid "Create a new payment transaction"
msgstr ""

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_icon
msgid "Create a payment icon"
msgstr ""

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.payment_token_action
msgid "Create a saved payment data"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_icon__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_token__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_transaction__create_uid
msgid "Created by"
msgstr "Búið til af"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__create_date
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_icon__create_date
#: model:ir.model.fields,field_description:payment.field_payment_token__create_date
#: model:ir.model.fields,field_description:payment.field_payment_transaction__create_date
msgid "Created on"
msgstr "Stofnað þann"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "Credentials"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__currency_id
msgid "Currency"
msgstr "Gjaldmiðill"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_custom
msgid "Custom"
msgstr "Custom"

#. module: payment
#: selection:payment.acquirer.onboarding.wizard,payment_method:0
msgid "Custom payment instructions"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_id
msgid "Customer"
msgstr "Viðskiptavinur"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__description
msgid "Description"
msgstr "Lýsing"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__sequence
msgid "Determine the display order"
msgstr "Determine the display order"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_confirmation_status
msgid "Dismiss"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__display_name
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_icon__display_name
#: model:ir.model.fields,field_description:payment.field_payment_token__display_name
#: model:ir.model.fields,field_description:payment.field_payment_transaction__display_name
msgid "Display Name"
msgstr "Nafn"

#. module: payment
#: selection:payment.transaction,state:0
#: selection:res.company,payment_acquirer_onboarding_state:0
msgid "Done"
msgstr "Lokið"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__done_msg
msgid "Done Message"
msgstr ""

#. module: payment
#: selection:payment.transaction,state:0
msgid "Draft"
msgstr "Tillaga"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "E-mail"
msgstr ""

#. module: payment
#: model:account.payment.method,name:payment.account_payment_method_electronic_in
msgid "Electronic"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_email
msgid "Email"
msgstr "Tölvupóstur"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__environment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_search
msgid "Environment"
msgstr "Environment"

#. module: payment
#: selection:payment.transaction,state:0
msgid "Error"
msgstr "Villa!"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__error_msg
msgid "Error Message"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:430
#, python-format
msgid "Error: "
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__fees
msgid "Fees"
msgstr "Fees"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_implemented
msgid "Fees Computation Supported"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__fees
msgid "Fees amount; set by the system because depends on the acquirer"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__state_message
msgid "Field used to store error and/or validation messages for information"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_dom_fixed
msgid "Fixed domestic fees"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_int_fixed
msgid "Fixed international fees"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__inbound_payment_method_ids
msgid "For Incoming Payments"
msgstr ""

#. module: payment
#: selection:payment.transaction,type:0
msgid "Form"
msgstr "Form"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__view_template_id
msgid "Form Button Template"
msgstr ""

#. module: payment
#: selection:payment.transaction,type:0
msgid "Form with tokenization"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "From"
msgstr "From"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_search
msgid "Group By"
msgstr "Hópa eftir"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__is_processed
msgid "Has the payment been post processed"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__pre_msg
msgid "Help Message"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__id
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_icon__id
#: model:ir.model.fields,field_description:payment.field_payment_token__id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__id
msgid "ID"
msgstr "Auðkenni"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__specific_countries
msgid ""
"If you leave it empty, the payment acquirer will be available for all the "
"countries."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:132
#, python-format
msgid "If, the payment hasn't been confirmed you can contact us."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__image
#: model:ir.model.fields,field_description:payment.field_payment_icon__image
msgid "Image"
msgstr "Mynd"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_icon__image_payment_form
msgid "Image displayed on the payment form"
msgstr ""

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_ogone
msgid "Ingenico"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
#: model_terms:ir.ui.view,arch_db:payment.acquirer_kanban
msgid "Install"
msgstr "Install"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__module_state
msgid "Installation State"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__reference
msgid "Internal reference of the TX"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:145
#, python-format
msgid "Internal server error"
msgstr ""

#. module: payment
#: code:addons/payment/models/account_invoice.py:55
#, python-format
msgid "Invalid token found! Token acquirer %s != %s"
msgstr ""

#. module: payment
#: code:addons/payment/models/account_invoice.py:58
#, python-format
msgid "Invalid token found! Token partner %s != %s"
msgstr ""

#. module: payment
#: model:ir.model,name:payment.model_account_invoice
msgid "Invoice"
msgstr "Reikningur"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "Invoice(s)"
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:851
#: model:ir.model.fields,field_description:payment.field_payment_transaction__invoice_ids
#, python-format
msgid "Invoices"
msgstr "Reikningar"

#. module: payment
#: selection:res.company,payment_acquirer_onboarding_state:0
msgid "Just done"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_lang
msgid "Language"
msgstr "Tungumál"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer____last_update
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard____last_update
#: model:ir.model.fields,field_description:payment.field_payment_icon____last_update
#: model:ir.model.fields,field_description:payment.field_payment_token____last_update
#: model:ir.model.fields,field_description:payment.field_payment_transaction____last_update
msgid "Last Modified on"
msgstr "Síðast breytt þann"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_icon__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_token__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_transaction__write_uid
msgid "Last Updated by"
msgstr "Síðast uppfært af"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__write_date
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_icon__write_date
#: model:ir.model.fields,field_description:payment.field_payment_token__write_date
#: model:ir.model.fields,field_description:payment.field_payment_transaction__write_date
msgid "Last Updated on"
msgstr "Síðast uppfært þann"

#. module: payment
#: selection:payment.acquirer,save_token:0
msgid "Let the customer decide"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_icon__acquirer_ids
msgid "List of Acquirers supporting this payment icon."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__website_published
msgid "Make this payment acquirer available (Customer invoices, etc.)"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay_methods
msgid "Manage your Payment Methods"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay_meth_link
msgid "Manage your payment methods"
msgstr ""

#. module: payment
#: selection:res.company,payment_onboarding_payment_method:0
msgid "Manual"
msgstr "Manual"

#. module: payment
#: selection:payment.acquirer,provider:0
msgid "Manual Configuration"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__inbound_payment_method_ids
msgid ""
"Manual: Get paid by cash, check or any other method outside of Odoo.\n"
"Electronic: Get paid automatically through a payment acquirer by requesting a transaction on a card saved by the customer when buying or subscribing online (payment token).\n"
"Batch Deposit: Encase several customer checks at once by generating a batch deposit to submit to your bank. When encoding the bank statement in Odoo,you are suggested to reconcile the transaction with the batch deposit. Enable this option from the settings."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__image_medium
msgid "Medium-sized image"
msgstr "Mynd í miðstærð"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__image_medium
msgid ""
"Medium-sized image of this provider. It is automatically resized as a "
"128x128px image, with aspect ratio preserved. Use this field in form views "
"or some kanban views."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__state_message
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "Message"
msgstr "Skilaboð"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__post_msg
msgid "Message displayed after having done the payment process."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__pre_msg
msgid "Message displayed to explain and help the payment process."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__error_msg
msgid "Message displayed, if error is occur during the payment process."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__cancel_msg
msgid "Message displayed, if order is cancel during the payment process."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__done_msg
msgid ""
"Message displayed, if order is done successfully after having done the "
"payment process."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__pending_msg
msgid ""
"Message displayed, if order is in pending state after having done the "
"payment process."
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "Messages"
msgstr "Skilaboð"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__manual_name
msgid "Method"
msgstr "Greiðslumáti"

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:469
#, python-format
msgid "Missing partner reference when trying to create a new payment token"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__name
#: model:ir.model.fields,field_description:payment.field_payment_icon__name
#: model:ir.model.fields,field_description:payment.field_payment_token__name
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
#: model_terms:ir.ui.view,arch_db:payment.payment_icon_form_view
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "Name"
msgstr "Nafn"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_token__name
msgid "Name of the payment token"
msgstr ""

#. module: payment
#: selection:payment.acquirer,save_token:0
msgid "Never"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "No payment acquirer found."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:118
#, python-format
msgid "No payment has been processed."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:201
#: code:addons/payment/static/src/js/payment_form.js:310
#, python-format
msgid "No payment method selected"
msgstr ""

#. module: payment
#: selection:res.company,payment_acquirer_onboarding_state:0
msgid "Not done"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_account_payment__payment_token_id
msgid ""
"Note that tokens from acquirers set to only authorize transactions (instead "
"of capturing the amount) are not available."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__payment_flow
msgid ""
"Note: Subscriptions does not take this field in account, it uses server to "
"server by default."
msgstr ""

#. module: payment
#: selection:payment.acquirer,provider:0
msgid "Ogone"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:434
#, python-format
msgid "Ok"
msgstr "Ok"

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:48
#, python-format
msgid "Only administrators can access this data."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:723
#, python-format
msgid "Only draft transaction can be authorized."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:714
#: code:addons/payment/models/payment_acquirer.py:775
#, python-format
msgid "Only draft transaction can be processed."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:763
#, python-format
msgid "Only draft/authorized transaction can be cancelled."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:732
#, python-format
msgid "Only draft/authorized transaction can be posted."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:1018
#, python-format
msgid "Only transactions having the capture status can be captured."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:1025
#, python-format
msgid "Only transactions having the capture status can be voided."
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
#: model_terms:ir.ui.view,arch_db:payment.payment_confirmation_status
msgid "Or scan me with your banking app."
msgstr ""

#. module: payment
#: selection:res.company,payment_onboarding_payment_method:0
msgid "Other"
msgstr "Annað"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__partner_id
msgid "Partner"
msgstr "Viðskipta aðili"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_name
msgid "Partner Name"
msgstr ""

#. module: payment
#: selection:payment.acquirer.onboarding.wizard,payment_method:0
msgid "Pay with PayPal"
msgstr ""

#. module: payment
#: selection:payment.acquirer.onboarding.wizard,payment_method:0
msgid "Pay with another payment acquirer"
msgstr ""

#. module: payment
#: selection:payment.acquirer.onboarding.wizard,payment_method:0
msgid "Pay with credit card (via Stripe)"
msgstr ""

#. module: payment
#: selection:res.company,payment_onboarding_payment_method:0
msgid "PayPal"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__paypal_email_account
msgid "PayPal Email ID"
msgstr ""

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_payu
#: selection:payment.acquirer,provider:0
msgid "PayUmoney"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__payment_id
#: model_terms:ir.ui.view,arch_db:payment.confirm
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "Payment"
msgstr "Greiðsla"

#. module: payment
#: model:ir.model,name:payment.model_payment_acquirer
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "Payment Acquirer"
msgstr "Payment Acquirer"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_acquirer
#: model:ir.ui.menu,name:payment.payment_acquirer_menu
#: model_terms:ir.ui.view,arch_db:payment.acquirer_list
msgid "Payment Acquirers"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__payment_flow
msgid "Payment Flow"
msgstr ""

#. module: payment
#: model:ir.model,name:payment.model_payment_icon
#: model_terms:ir.ui.view,arch_db:payment.payment_icon_form_view
msgid "Payment Icon"
msgstr ""

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_icon
#: model:ir.ui.menu,name:payment.payment_icon_menu
msgid "Payment Icons"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__manual_post_msg
msgid "Payment Instructions"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__journal_id
msgid "Payment Journal"
msgstr "Payment Journal"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__payment_method
#: model_terms:ir.ui.view,arch_db:payment.onboarding_payment_acquirer_step
msgid "Payment Method"
msgstr "Payment Method"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay_methods
msgid "Payment Methods"
msgstr "Greiðslumátar"

#. module: payment
#: model:ir.model,name:payment.model_payment_token
#: model:ir.model.fields,field_description:payment.field_payment_transaction__payment_token_id
msgid "Payment Token"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_partner__payment_token_ids
#: model:ir.model.fields,field_description:payment.field_res_users__payment_token_ids
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form_view
#: model_terms:ir.ui.view,arch_db:payment.payment_token_tree_view
#: model_terms:ir.ui.view,arch_db:payment.payment_token_view_search
msgid "Payment Tokens"
msgstr "Payment Tokens"

#. module: payment
#: model:ir.model,name:payment.model_payment_transaction
#: model:ir.model.fields,field_description:payment.field_account_payment__payment_transaction_id
msgid "Payment Transaction"
msgstr ""

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_transaction
#: model:ir.actions.act_window,name:payment.action_payment_tx_ids
#: model:ir.model.fields,field_description:payment.field_payment_token__payment_ids
#: model:ir.ui.menu,name:payment.payment_transaction_menu
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
#: model_terms:ir.ui.view,arch_db:payment.transaction_list
msgid "Payment Transactions"
msgstr ""

#. module: payment
#: model:ir.model,name:payment.model_payment_acquirer_onboarding_wizard
msgid "Payment acquire onboarding wizard"
msgstr ""

#. module: payment
#: selection:payment.acquirer,payment_flow:0
msgid "Payment from Odoo"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.onboarding_payment_acquirer_step
msgid "Payment method set!"
msgstr ""

#. module: payment
#: code:addons/payment/controllers/portal.py:252
#, python-format
msgid "Payment transaction failed."
msgstr ""

#. module: payment
#: model:ir.model,name:payment.model_account_payment
#: model:ir.ui.menu,name:payment.root_payment_menu
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form_view
msgid "Payments"
msgstr "Greiðslur"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:9
#, python-format
msgid "Payments failed"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:26
#, python-format
msgid "Payments received"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__journal_id
msgid ""
"Payments will be registered into this journal. If you get paid straight on your bank account,\n"
"                select your bank account. If you get paid in batch for several transactions, create a specific\n"
"                payment journal for this payment acquirer to easily manage the bank reconciliation. You hold\n"
"                the amount in a temporary transfer account of your books (created automatically when you create\n"
"                the payment journal). Then when you get paid on your bank account by the payment acquirer, you\n"
"                reconcile the bank statement line with this temporary transfer account. Use reconciliation\n"
"                templates to do it in one-click."
msgstr ""

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_paypal
#: selection:payment.acquirer,provider:0
msgid "Paypal"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__paypal_seller_account
msgid "Paypal Merchant ID"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__paypal_pdt_token
msgid "Paypal PDT Token"
msgstr ""

#. module: payment
#: selection:payment.transaction,state:0
msgid "Pending"
msgstr "Í bið"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__pending_msg
msgid "Pending Message"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_phone
msgid "Phone"
msgstr "Sími"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "Please configure a payment acquirer."
msgstr ""

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:33
#, python-format
msgid ""
"Please make a payment to <ul><li>Bank: %s</li><li>Account Number: "
"%s</li><li>Account Holder: %s</li></ul>"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:202
#, python-format
msgid "Please select a payment method."
msgstr "Veldu greiðslumáta."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:311
#, python-format
msgid "Please select the option to add a new payment method."
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,post_msg:payment.payment_acquirer_custom
#: model_terms:payment.acquirer,post_msg:payment.payment_acquirer_transfer
msgid "Please use the following transfer details"
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,post_msg:payment.payment_acquirer_custom
#: model_terms:payment.acquirer,post_msg:payment.payment_acquirer_transfer
msgid "Please use the order name as communication reference."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:139
#, python-format
msgid "Please wait ..."
msgstr ""

#. module: payment
#: model:ir.actions.server,name:payment.cron_post_process_payment_tx_ir_actions_server
#: model:ir.cron,cron_name:payment.cron_post_process_payment_tx
#: model:ir.cron,name:payment.cron_post_process_payment_tx
msgid "Post process payment transactions"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Processed by"
msgstr ""

#. module: payment
#: selection:payment.acquirer,environment:0
msgid "Production"
msgstr "Framleiðsla"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__provider
#: model:ir.model.fields,field_description:payment.field_payment_transaction__provider
#: model_terms:ir.ui.view,arch_db:payment.acquirer_search
msgid "Provider"
msgstr "Provider"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:19
#, python-format
msgid "Reason:"
msgstr "Ástæða:"

#. module: payment
#: selection:payment.acquirer,payment_flow:0
msgid "Redirection to the acquirer website"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__reference
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Reference"
msgstr "Tilvísun"

#. module: payment
#: sql_constraint:payment.transaction:0
msgid "Reference must be unique!"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__acquirer_reference
msgid "Reference of the TX as stored in the acquirer database"
msgstr ""

#. module: payment
#: constraint:payment.acquirer:0
msgid "Required fields not filled"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__return_url
msgid "Return URL after payment"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__registration_view_template_id
msgid "S2S Form Template"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__save_token
msgid "Save Cards"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_tokens_list
msgid "Save my payment data"
msgstr ""

#. module: payment
#: model:ir.actions.act_window,name:payment.payment_token_action
#: model:ir.ui.menu,name:payment.payment_token_menu
msgid "Saved Payment Data"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment__payment_token_id
msgid "Saved payment token"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__token_implemented
msgid "Saving Card Data supported"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_company__payment_onboarding_payment_method
msgid "Selected onboarding payment method"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__sequence
msgid "Sequence"
msgstr "Runa"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:120
#: code:addons/payment/static/src/js/payment_form.js:135
#: code:addons/payment/static/src/js/payment_form.js:175
#: code:addons/payment/static/src/js/payment_form.js:181
#: code:addons/payment/static/src/js/payment_form.js:287
#: code:addons/payment/static/src/js/payment_form.js:334
#: code:addons/payment/static/src/js/payment_form.js:369
#, python-format
msgid "Server Error"
msgstr "Villa í netþjóni"

#. module: payment
#: selection:payment.transaction,type:0
msgid "Server To Server"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:302
#, python-format
msgid "Server error"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:138
#, python-format
msgid "Server error:"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.onboarding_payment_acquirer_step
msgid "Set payments"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__short_name
msgid "Short name"
msgstr ""

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_sips
#: selection:payment.acquirer,provider:0
msgid "Sips"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__image_small
msgid "Small-sized image"
msgstr "Lítil mynd"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__image_small
msgid ""
"Small-sized image of this provider. It is automatically resized as a 64x64px"
" image, with aspect ratio preserved. Use this field anywhere a small image "
"is required."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__specific_countries
msgid "Specific Countries"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_company__payment_acquirer_onboarding_state
msgid "State of the onboarding payment acquirer step"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__state
msgid "Status"
msgstr "Staða"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_stripe
#: selection:payment.acquirer,provider:0
#: selection:res.company,payment_onboarding_payment_method:0
msgid "Stripe"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__stripe_publishable_key
msgid "Stripe Publishable Key"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__stripe_secret_key
msgid "Stripe Secret Key"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__payment_icon_ids
msgid "Supported Payment Icons"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__registration_view_template_id
msgid "Template for method registration"
msgstr ""

#. module: payment
#: selection:payment.acquirer,environment:0
msgid "Test"
msgstr "Test"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__post_msg
msgid "Thanks Message"
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:871
#, python-format
msgid "The %s payment acquirers are not allowed to manual capture mode!"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
#: model_terms:ir.ui.view,arch_db:payment.payment_confirmation_status
msgid "The SEPA QR Code informations are not set correctly."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:660
#, python-format
msgid "The customer has selected %s to pay this document."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:677
#, python-format
msgid ""
"The transaction %s with %s for %s has been authorized. Waiting for "
"capture..."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:682
#, python-format
msgid ""
"The transaction %s with %s for %s has been cancelled with the following "
"message: %s"
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:685
#, python-format
msgid "The transaction %s with %s for %s has been cancelled."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:679
#, python-format
msgid ""
"The transaction %s with %s for %s has been confirmed. The related payment is"
" posted: %s"
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:675
#, python-format
msgid "The transaction %s with %s for %s is pending."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_icon__image
msgid ""
"This field holds the image used for this payment icon, limited to "
"1024x1024px"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__image
msgid ""
"This field holds the image used for this provider, limited to 1024x1024px"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__save_token
msgid ""
"This option allows customers to save their credit card as a payment token "
"and to reuse it for a later purchase. If you manage subscriptions (recurring"
" invoicing), you need it to automatically charge the customer when you issue"
" an invoice."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__country_ids
msgid ""
"This payment gateway is available for selected countries. If none is "
"selected it is available for all countries."
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid ""
"This template renders the acquirer button with all necessary values.\n"
"                                            It is rendered with qWeb with the following evaluation context:"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:117
#, python-format
msgid "This transaction has been cancelled."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_invoice__transaction_ids
msgid "Transactions"
msgstr "Transactions"

#. module: payment
#: model_terms:payment.acquirer,pre_msg:payment.payment_acquirer_transfer
msgid ""
"Transfer information will be provided after choosing the payment aquirer."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__type
msgid "Type"
msgstr "Gerð"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:138
#, python-format
msgid "Unable to contact the Odoo server."
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__qr_code
msgid "Use SEPA QR Code"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__date
msgid "Validation Date"
msgstr ""

#. module: payment
#: selection:payment.transaction,type:0
msgid "Validation of the bank card"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_dom_var
msgid "Variable domestic fees (in percents)"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_int_var
msgid "Variable international fees (in percents)"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__verified
msgid "Verified"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__website_published
msgid "Visible in Portal / Website"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.account_invoice_view_form_inherit_payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "Void Transaction"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:87
#, python-format
msgid "Waiting for payment"
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:666
#, python-format
msgid "Waiting for payment confirmation..."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:355
#, python-format
msgid "Warning!"
msgstr "Aðvörun!"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:136
#, python-format
msgid "We are not able to add your payment method at the moment."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:303
#, python-format
msgid "We are not able to add your payment method at the moment.</p>"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:335
#: code:addons/payment/static/src/js/payment_form.js:370
#, python-format
msgid "We are not able to delete your payment method at the moment."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:130
#, python-format
msgid "We are not able to find your payment, but don't worry."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:176
#, python-format
msgid "We are not able to redirect you to the payment form."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:182
#, python-format
msgid "We are not able to redirect you to the payment form. "
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_processing.js:117
#, python-format
msgid "We are processing your payments, please wait ..."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:99
#, python-format
msgid "We are waiting for the payment acquirer to confirm the payment."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:191
#, python-format
msgid "We're unable to process your payment."
msgstr ""

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_transfer
#: selection:payment.acquirer,provider:0
msgid "Wire Transfer"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:59
#, python-format
msgid "You can click here to be redirected to the confirmation page."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:131
#, python-format
msgid "You should receive an email confirming your payment in a few minutes."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:77
#, python-format
msgid "You will be notified when the payment is confirmed."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:58
#, python-format
msgid "You will be notified when the payment is fully confirmed."
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,pre_msg:payment.payment_acquirer_stripe
msgid "You will be prompted with Stripe Payment page for payment information."
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,pre_msg:payment.payment_acquirer_adyen
msgid ""
"You will be redirected to the Adyen website after clicking on the payment "
"button."
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,pre_msg:payment.payment_acquirer_authorize
msgid ""
"You will be redirected to the Authorize website after clicking on the "
"payment button."
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,pre_msg:payment.payment_acquirer_buckaroo
msgid ""
"You will be redirected to the Buckaroo website after clicking on the payment"
" button."
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,pre_msg:payment.payment_acquirer_ogone
msgid ""
"You will be redirected to the Ingenico website after clicking on the payment"
" button."
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,pre_msg:payment.payment_acquirer_payu
msgid ""
"You will be redirected to the PayUmoney website after clicking on the "
"payment button."
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,pre_msg:payment.payment_acquirer_paypal
msgid ""
"You will be redirected to the Paypal website after clicking on the payment "
"button."
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,pre_msg:payment.payment_acquirer_sips
msgid ""
"You will be redirected to the Sips website after clicking on payment button."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:39
#, python-format
msgid "Your order has been processed."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:36
#, python-format
msgid "Your order is being processed, please wait ..."
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_confirmation_status
msgid "Your payment has been authorized."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:76
#, python-format
msgid "Your payment has been received but need to be confirmed manually."
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_processing.xml:57
#, python-format
msgid "Your payment is in pending state."
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_form
msgid "ZIP"
msgstr "Póstnúmer"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_zip
msgid "Zip"
msgstr "Zip"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "acquirer: payment.acquirer browse record"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "amount: the transaction amount, a float"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_tokens_list
msgid "and more"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "context: the current context dictionary"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "currency: the transaction currency browse record"
msgstr ""

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form.js:121
#: code:addons/payment/static/src/js/payment_form.js:288
#, python-format
msgid "e.g. Your credit card details are wrong. Please verify."
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "partner: the buyer partner browse record, not necessarily set"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid ""
"partner_values: specific values about the buyer, for example coming from a "
"shipping form"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "reference: the transaction reference number"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "to choose another payment method."
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "tx_url: transaction URL to post the form"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "tx_values: transaction values"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.acquirer_form
msgid "user: current user browse record"
msgstr ""
