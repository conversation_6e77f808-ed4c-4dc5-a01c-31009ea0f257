# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_twitter
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:29+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                Show me how to obtain the Twitter API key and Twitter API secret"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                أرني كيفية الحصول على مفتاح الواجهة البرمجية لـ Twitter وسر Twitter "

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Twitter Roller</span>\n"
"                        <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"o_form_label\">شريط تمرير Twitter</span>\n"
"                        <span class=\"fa fa-lg fa-globe\" title=\"القيم التي تعين هنا تحدد حسب الموقع.\" groups=\"website.group_multi_website\"/>"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "<strong>Callback URL: </strong>Leave blank"
msgstr "<strong>رابط الاستدعاء: </strong>اتركه فارغاً "

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "<strong>Description: </strong> Odoo Twitter Integration"
msgstr "<strong>الوصف: </strong> تكامل أودو مع Twitter "

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "<strong>Name: </strong> Odoo Twitter Integration"
msgstr "<strong>الاسم: </strong> تكامل أودو مع Twitter "

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "<strong>Website: </strong>"
msgstr "<strong>الموقع الإلكتروني: </strong>"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_res_config_settings__twitter_api_key
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "API Key"
msgstr "مفتاح الواجهة البرمجية للتطبيق "

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_res_config_settings__twitter_api_secret
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "API secret"
msgstr "سر الواجهة البرمجية للتطبيق "

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid ""
"Accept terms of use and click on the Create your Twitter application button "
"at the bottom"
msgstr "قم بقبول شروط الاستخدام واضغط على زر إنشاء تطبيقك على Twitter أدناه "

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"Authentication credentials were missing or incorrect. Maybe screen name "
"tweets are protected."
msgstr ""
"بيانات الاعتماد المطلوبة للمصادقة مفقودة أو غير صحيحة. قد تكون التغريدات "
"محمية."

#. module: website_twitter
#: model:ir.model,name:website_twitter.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid ""
"Copy/Paste Consumer Key (API Key) and Consumer Secret (API Secret) keys "
"below."
msgstr ""
"قم بنسخ ولصق مفتاح المستهلك (مفتاح الواجهة البرمجية للتطبيق) ومفتاح سر "
"المستهلك (سر الواجهة البرمجية للتطبيق) أدناه. "

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Create a new Twitter application on"
msgstr "إنشاء تطبيق Twitter جديد على "

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_res_config_settings__twitter_screen_name
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Favorites From"
msgstr "المفضلات من"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website__twitter_screen_name
msgid "Get favorites from this screen name"
msgstr "الحصول على مفضلات اسم الظهور هذا"

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid "HTTP Error: Something is misconfigured"
msgstr "خطأ HTTP: هناك مشكلة في التهيئة "

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "How to configure the Twitter API access"
msgstr "كيفية تهيئة وصول مفتاح الواجهة البرمجية لـ Twitter "

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__id
msgid "ID"
msgstr "المُعرف"

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid "Internet connection refused: We failed to reach a twitter server."
msgstr "تم رفض الاتصال بالإنترنت: لم نتمكن من من الوصول إلى خادم Twitter. "

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: website_twitter
#: code:addons/website_twitter/controllers/main.py:0
#, python-format
msgid ""
"Please set a Twitter screen name to load favorites from, in the Website "
"Settings (it does not have to be yours)"
msgstr ""
"الرجاء وضع اسم ظهور لتحميل المفضلات منه في إعدادات الموقع الإلكتروني (ليس "
"بالضرورة أن يكون لك) "

#. module: website_twitter
#: code:addons/website_twitter/controllers/main.py:0
#, python-format
msgid "Please set the Twitter API Key and Secret in the Website Settings."
msgstr ""
"يرجى وضع مفتاح الواجهة البرمجية لـ Twitter وسره في إعدادات الموقع "
"الإلكتروني. "

#. module: website_twitter
#. openerp-web
#: code:addons/website_twitter/static/src/js/website.twitter.editor.js:0
#, python-format
msgid "Reload"
msgstr "إعادة تحميل"

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"Request cannot be served due to the applications rate limit having been "
"exhausted for the resource."
msgstr "لا يمكن تنفيذ الطلب نظرًا لاستنفاد حد معدل التطبيقات للمورد."

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__screen_name
msgid "Screen Name"
msgstr "اسم الظهور"

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_res_config_settings__twitter_screen_name
msgid ""
"Screen Name of the Twitter Account from which you want to load favorites.It "
"does not have to match the API Key/Secret."
msgstr ""
"اسم ظهور حساب Twitter الذي ترغب في تحميل المفضلات منه لا يتطابق مع مفتاح "
"الواجهة البرمجية/سر Twitter. "

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Switch to the API Keys tab: <br/>"
msgstr "الانتقال إلى علامة تبويب مفاتيح الواجهة البرمجية للتطبيق: <br/>"

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"The Twitter servers are up, but overloaded with requests. Try again later."
msgstr ""
"خوادم Twitter تعمل، لكنها محملة بالكثير من الطلبات. حاول مرة أخرى لاحقاً. "

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"The Twitter servers are up, but the request could not be serviced due to "
"some failure within our stack. Try again later."
msgstr ""
"خوادم Twitter تعمل، لكن تعذّر تنفيذ الطلب بسبب مشكلة في بياناتنا. حاول مرة "
"أخرى لاحقاً. "

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"The request is understood, but it has been refused or access is not allowed."
" Please check your Twitter API Key and Secret."
msgstr ""
"تم فهم الطلب، لكن تم رفضه أو قد يكون الوصول ممنوع. يرجى التحقق من مفتاح "
"الواجهة البرمجية لـ Twitter ورمزها السري. "

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"The request was invalid or cannot be otherwise served. Requests without "
"authentication are considered invalid and will yield this response."
msgstr ""
"الطلب غير صالح أو مستحيل التنفيذ. تعتبر الطلبات غير صالحة إذا لم تتم "
"المُصادقة عليها وينتج عنها هذه الرسالة."

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid "There was no new data to return."
msgstr "لا توجد بيانات حديثة لإعادتها."

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__tweet_id
msgid "Tweet ID"
msgstr "معرف التغريدة"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__tweet
msgid "Tweets"
msgstr "التغريدات "

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Twitter API Credentials"
msgstr "بيانات اعتماد الواجهة البرمجية لـ Twitter "

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_website__twitter_api_key
msgid "Twitter API Key"
msgstr "مفتاح الواجهة البرمجية لـ Twitter "

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_website__twitter_api_secret
msgid "Twitter API Secret"
msgstr "سر الواجهة البرمجية لـ Twitter "

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website__twitter_api_key
msgid "Twitter API key"
msgstr "مفتاح الواجهة البرمجية لـ Twitter "

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_res_config_settings__twitter_api_key
msgid "Twitter API key you can get it from https://apps.twitter.com/"
msgstr ""
"يمكنك الحصول على مفتاح الواجهة البرمجية لـ Twitter من "
"https://apps.twitter.com/ "

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website__twitter_api_secret
msgid "Twitter API secret"
msgstr "سر الواجهة البرمجية لـ Twitter "

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_res_config_settings__twitter_api_secret
msgid "Twitter API secret you can get it from https://apps.twitter.com/"
msgstr ""
"يمكنك الحصول على سر الواجهة البرمجية لـ Twitter من https://apps.twitter.com/"
" "

#. module: website_twitter
#. openerp-web
#: code:addons/website_twitter/static/src/xml/website.twitter.xml:0
#, python-format
msgid "Twitter Configuration"
msgstr "تهيئة Twitter "

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"Twitter authorization error! Please double-check your Twitter API Key and "
"Secret!"
msgstr ""
"خطأ في تفويض Twitter! يرجى التحقق مجدداً من مفتاح وسر الواجهة البرمجية لـ "
"Twitter! "

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid "Twitter is down or being upgraded."
msgstr "Twitter معطل أو قيد التحديث. "

#. module: website_twitter
#: code:addons/website_twitter/models/res_config_settings.py:0
#, python-format
msgid ""
"Twitter seems broken. Please retry later. You may consider posting an issue "
"on Twitter forums to get help."
msgstr ""
"يبدو أن Twitter معطل. يرجى المحاولة مرة أخرى لاحقاً. ربما عليك كتابة منشور "
"عن مشكلتك على منتديات Twitter للحصول على المساعدة. "

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_res_config_settings__twitter_server_uri
msgid "Twitter server uri"
msgstr "معرف الموارد الموحد لخادم Twitter "

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "Twitter tutorial"
msgstr "درس تدريبي عن Twitter "

#. module: website_twitter
#: code:addons/website_twitter/controllers/main.py:0
#, python-format
msgid ""
"Twitter user @%(username)s has less than 12 favorite tweets. Please add more"
" or choose a different screen name."
msgstr ""
"مستخدم تويتر @%(username)s لديه أقل من 12 تغريدة مفضلة. الرجاء إضافة المزيد "
"أو اختيار اسم ظهور مختلف. "

#. module: website_twitter
#. openerp-web
#: code:addons/website_twitter/static/src/xml/website.twitter.xml:0
#, python-format
msgid "Twitter's user"
msgstr "مستخدم Twitter "

#. module: website_twitter
#: model:ir.actions.server,name:website_twitter.ir_cron_twitter_actions_ir_actions_server
#: model:ir.cron,cron_name:website_twitter.ir_cron_twitter_actions
#: model:ir.cron,name:website_twitter.ir_cron_twitter_actions
msgid "Twitter: Fetch new favorites"
msgstr "Twitter: البحث عن مفضلات جديدة "

#. module: website_twitter
#: model:ir.model,name:website_twitter.model_website
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet__website_id
msgid "Website"
msgstr "الموقع الإلكتروني"

#. module: website_twitter
#: model:ir.model,name:website_twitter.model_website_twitter_tweet
msgid "Website Twitter"
msgstr "Twitter الموقع الإلكتروني "

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "https://apps.twitter.com/app/new"
msgstr "https://apps.twitter.com/app/new"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.res_config_settings_view_form
msgid "with the following values:"
msgstr "بالقيم التالية:"
