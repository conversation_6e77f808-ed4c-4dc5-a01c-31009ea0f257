<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">
    <record id="website_slides_forum_public" model="ir.rule">
        <field name="name">Website forum: User can only access to forum related to followed courses</field>
        <field name="model_id" ref="website_forum.model_forum_forum"/>
        <field name="domain_force">['&amp;', ('slide_channel_ids.website_published', '=', True), '|', ('slide_channel_ids.visibility', '=', 'public'), ('slide_channel_ids.partner_ids', 'in', user.partner_id.id)]</field>
        <field name="groups" eval="[(4, ref('base.group_public')), (4, ref('base.group_portal')), (4, ref('base.group_user'))]"/>
    </record>
    <record id="website_slides_forum_website_slides_officer" model="ir.rule">
        <field name="name">Website forum: website slides officer can access all forum</field>
        <field name="model_id" ref="website_forum.model_forum_forum"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('website_slides.group_website_slides_officer'))]"/>
    </record>

    <record id="website_slides_forum_public_post" model="ir.rule">
        <field name="name">Website forum post: User can only access to post linked to forum related to followed courses</field>
        <field name="model_id" ref="website_forum.model_forum_post"/>
        <field name="domain_force">['&amp;', ('forum_id.slide_channel_ids.website_published', '=', True), '|', ('forum_id.slide_channel_ids.visibility', '=', 'public'), ('forum_id.slide_channel_ids.partner_ids', 'in', user.partner_id.id)]</field>
        <field name="groups" eval="[(4, ref('base.group_public')), (4, ref('base.group_portal')), (4, ref('base.group_user'))]"/>
    </record>
    <record id="website_slides_forum_website_slides_officer_post" model="ir.rule">
        <field name="name">Website forum post: website slides officer can access all post</field>
        <field name="model_id" ref="website_forum.model_forum_post"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('website_slides.group_website_slides_officer'))]"/>
    </record>

    <record id="website_slides_forum_public_tag" model="ir.rule">
        <field name="name">Website slides forum tag: User can only access to tag linked to forum related to followed courses</field>
        <field name="model_id" ref="website_forum.model_forum_tag"/>
        <field name="domain_force">['&amp;', ('forum_id.slide_channel_ids.website_published', '=', True), '|', ('forum_id.slide_channel_ids.visibility', '=', 'public'), ('forum_id.slide_channel_ids.partner_ids', 'in', user.partner_id.id)]</field>
        <field name="groups" eval="[(4, ref('base.group_public')), (4, ref('base.group_portal')), (4, ref('base.group_user'))]"/>
    </record>
    <record id="website_slides_forum_website_slides_officer_tag" model="ir.rule">
        <field name="name">Website slides forum tag: website slides officer can access all tag</field>
        <field name="model_id" ref="website_forum.model_forum_tag"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('website_slides.group_website_slides_officer'))]"/>
    </record>
</odoo>
