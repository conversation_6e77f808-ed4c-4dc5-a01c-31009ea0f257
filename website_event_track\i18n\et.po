# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_track
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <armaged<PERSON><EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# Leaanika Randmets, 2022
# JanaAvalah, 2023
# <PERSON><PERSON>, 2023
# Anna, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:54+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: Anna, 2023\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_event_track
#: code:addons/website_event_track/models/website.py:0
#, python-format
msgid "\"Events App Name\" field is required."
msgstr "„Sündmuste rakenduse nimi” väli on kohustuslik."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__wishlist_visitor_count
#: model:ir.model.fields,field_description:website_event_track.field_website_visitor__event_track_wishlisted_count
msgid "# Wishlisted"
msgstr "# Soovide nimekirjas"

#. module: website_event_track
#: code:addons/website_event_track/models/event_track.py:0
#, python-format
msgid "%(name)s from %(company)s"
msgstr "%(name)s ettevõttest %(company)s"

#. module: website_event_track
#: code:addons/website_event_track/models/event_track.py:0
#, python-format
msgid "%(name)s, %(function)s at %(company)s"
msgstr "%(name)s, %(function)s ettevõttes %(company)s"

#. module: website_event_track
#: code:addons/website_event_track/models/website.py:0
#, python-format
msgid "%s Events"
msgstr "%s Sündmused"

#. module: website_event_track
#: code:addons/website_event_track/controllers/webmanifest.py:0
#, python-format
msgid "%s Online Events Application"
msgstr "%s Online sündmuste rakendus"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "&amp;bull;"
msgstr "&amp;bull;"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_17
msgid "10 DIY Furniture Ideas For Absolute Beginners"
msgstr "10 mööbli ideed algajatele"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_18
msgid "6 Woodworking tips and tricks for beginners"
msgstr "6 puidutöötlemise nippi algajatele"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid "<b>Contact Information</b>"
msgstr "<b>Kontaktinformatsioon</b>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Mail</b>:"
msgstr "<b>E-post</b>:"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Phone</b>:"
msgstr "<b>Telefon</b>:"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Proposed By</b>:"
msgstr "<b>Pakkuja</b>:"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Speaker Biography</b>:"
msgstr "<b>Kõneleja elulugu</b>:"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "<b>Speaker Profile</b>"
msgstr "<b>Esitleja profiil</b>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "<b>Talk Intro</b>"
msgstr "<b>Jutu sissejuhatus</b>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Talk Introduction</b>:"
msgstr "<b>Kõne sissejuhatus</b>:"

#. module: website_event_track
#: model:mail.template,body_html:website_event_track.mail_template_data_track_confirmation
msgid ""
"<div>\n"
"    Dear <t t-out=\"object.partner_id.name or object.partner_name or ''\">Brandon Freeman</t><br/>\n"
"    We are pleased to inform you that your proposal <t t-out=\"object.name or ''\">What This Event Is All About</t> has been accepted and confirmed for the event <t t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</t>.\n"
"    <br/>\n"
"    You will find more details here:\n"
"    <div style=\"margin: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/event/{{ object.event_id.id }}/track/{{ object.id }}\" style=\"padding: 8px 16px 8px 16px; font-size: 14px; color: #FFFFFF; text-decoration: none !important; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"            View Talk\n"
"        </a>\n"
"    </div>\n"
"    <br/><br/>\n"
"    Thank you,\n"
"    <t t-if=\"user.signature\">\n"
"        <br/>\n"
"        <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>"
msgstr ""
"<div>\n"
"    Dear <t t-out=\"object.partner_id.name or object.partner_name or ''\">Brandon Freeman</t><br/>\n"
"    We are pleased to inform you that your proposal <t t-out=\"object.name or ''\">What This Event Is All About</t> has been accepted and confirmed for the event <t t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</t>.\n"
"    <br/>\n"
"    You will find more details here:\n"
"    <div style=\"margin: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/event/{{ object.event_id.id }}/track/{{ object.id }}\" style=\"padding: 8px 16px 8px 16px; font-size: 14px; color: #FFFFFF; text-decoration: none !important; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"            View Talk\n"
"        </a>\n"
"    </div>\n"
"    <br/><br/>\n"
"    Thank you,\n"
"    <t t-if=\"user.signature\">\n"
"        <br/>\n"
"        <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_card
msgid "<i class=\"fa fa-ban mr-2\"/>Unpublished"
msgstr "<i class=\"fa fa-ban mr-2\"/>Avaldamata"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_search
msgid "<i class=\"fa fa-bell mr-2 text-muted\"/> Favorite Talks"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.session_topbar
msgid "<i class=\"fa fa-folder-open\"/> Favorites"
msgstr "<i class=\"fa fa-folder-open\"/> Lemmikud"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "<span class=\"d-none d-md-block ml-2\">&amp;bull;</span>"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<span class=\"o_wetrack_proposal_error_section text-danger d-none ml8\">\n"
"                                        <i class=\"fa fa-close mr4\" role=\"img\" aria-label=\"Error\" title=\"Error\"/>\n"
"                                        <span class=\"o_wetrack_proposal_error_message\"/>\n"
"                                    </span>"
msgstr ""
"<span class=\"o_wetrack_proposal_error_section text-danger d-none ml8\">\n"
"                                        <i class=\"fa fa-close mr4\" role=\"img\" aria-label=\"Error\" title=\"Error\"/>\n"
"                                        <span class=\"o_wetrack_proposal_error_message\"/>\n"
"                                    </span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<span class=\"o_wetrack_proposal_label_content\">Talk Introduction</span>\n"
"                                            <span>*</span>"
msgstr ""
"<span class=\"o_wetrack_proposal_label_content\">Sissejuhatus</span>\n"
"                                            <span>*</span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<span class=\"o_wetrack_proposal_label_content\">Talk Title</span>\n"
"                                            <span>*</span>"
msgstr ""
"<span class=\"o_wetrack_proposal_label_content\">Pealkiri</span>\n"
"                                            <span>*</span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid ""
"<span for=\"contact_name\">Name</span>\n"
"                    <span>*</span>"
msgstr ""
"<span for=\"contact_name\">Nimi</span>\n"
"                    <span>*</span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_topbar
msgid "<span id=\"search_number\" class=\"mr-1\">0</span>Results"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "<span> - </span>"
msgstr "<span> - </span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "<span>&amp;nbsp;at&amp;nbsp;</span>"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<span>New track proposal</span>"
msgstr "<span>Uus jälgimise ettepanek</span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "<span>hours</span>"
msgstr "<span>tundi</span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<strong>Lightning Talks</strong>. These are 30 minutes talks on many\n"
"                                    different topics. Most topics are accepted in lightning talks."
msgstr ""
"<strong>Välkkõned</strong>. Need 30-minutilised kõned on mõned\n"
"                                    paljudest erinevatest teemades. Enamik teemad on välkkõnede puhul lubatud."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "<strong>Location:</strong>"
msgstr "<strong>Asukoht:</strong>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<strong>Regular Talks</strong>. These are standard talks with slides,\n"
"                                    alocated in slots of 60 minutes."
msgstr ""
"<<strong>Regular Talks</strong>. These are standard talks with slides,\n"
"                                    alocated in slots of 60 minutes."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track7
msgid "A technical explanation of how to use computer design apps"
msgstr "Tehniline selgitus arvutidisaini rakenduste kasutamise kohta"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__kanban_state
msgid ""
"A track's kanban state indicates special situations affecting it:\n"
" * Grey is the default situation\n"
" * Red indicates something is preventing the progress of this track\n"
" * Green indicates the track is ready to be pulled to the next stage"
msgstr ""
"A track's kanban state indicates special situations affecting it:\n"
" * Grey is the default situation\n"
" * Red indicates something is preventing the progress of this track\n"
" * Green indicates the track is ready to be pulled to the next stage"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_needaction
msgid "Action Needed"
msgstr "Vajalik toiming"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__active
msgid "Active"
msgstr "Tegev"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_ids
msgid "Activities"
msgstr "Tegevused"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Tegevuse erandlik kohendus"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_state
msgid "Activity State"
msgstr "Tegevuse seis"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_type_icon
msgid "Activity Type Icon"
msgstr "Tegevuse liigi sümbol"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid ""
"Add a description to help your coworkers understand the meaning and purpose "
"of the stage."
msgstr ""
"Siia saad lisada kirjelduse teistele töötajatele, et nad saaksid aru selle "
"etapi mõttest ja eesmärgist."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "Add a description..."
msgstr "Lisa kirjeldus ..."

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_stage_action
msgid "Add a new stage in the task pipeline"
msgstr "Lisage ülesannete torusse uus etapp"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_tag
msgid ""
"Add tags to your tracks to help your attendees browse your event web pages."
msgstr ""
"Lisage oma lugudele silte, et aidata osalejatel teie sündmuste veebilehti "
"sirvida."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track19
msgid "Advanced lead management : tips and tricks from the fields"
msgstr "Täiustatud müügivihjete haldamine: väljade näpunäited ja nipid"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track13
msgid "Advanced reporting"
msgstr "Täpsem aruandlus"

#. module: website_event_track
#: code:addons/website_event_track/models/event_event.py:0
#, python-format
msgid "Agenda"
msgstr "Päevakava"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.session_topbar
msgid "All Talks"
msgstr "Kõik jutud"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_event_view_form
msgid "Allow Track Proposals"
msgstr "Lubage ettepanekuid jälgida"

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#, python-format
msgid "Allow push notifications?"
msgstr "Kas lubada Push-märguanded?"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"Allow video and audio recording of their\n"
"                                    presentation, for publishing on our website."
msgstr ""
"Lubage nende video- ja helisalvestuse\n"
"                                     esitlemine meie veebisaidil."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__wishlisted_by_default
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Always Wishlisted"
msgstr "Alati soovinimekirjas"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage2
msgid "Announced"
msgstr "Väljakuulutatud"

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/xml/event_track_proposal_templates.xml:0
#, python-format
msgid "Application"
msgstr "Kandideerimine"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Archived"
msgstr "Arhiveeritud"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_visitor__is_blacklisted
msgid ""
"As key track cannot be un-favorited, this field store the partner choice to "
"remove the reminder for key tracks."
msgstr ""
"As key track cannot be un-favorited, this field store the partner choice to "
"remove the reminder for key tracks."

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_17
msgid ""
"As you may have heard before, making your own furniture is actually not as difficult or as complicated as you think.\n"
"    In fact, some projects are so easy anyone could successfully complete them. For example, making a cute stool out of\n"
"    a old tire is a real piece of cake and if you’re in need of a coffee table you can easily put one together using\n"
"    wood crates."
msgstr ""
"As you may have heard before, making your own furniture is actually not as difficult or as complicated as you think.\n"
"    In fact, some projects are so easy anyone could successfully complete them. For example, making a cute stool out of\n"
"    a old tire is a real piece of cake and if you’re in need of a coffee table you can easily put one together using\n"
"    wood crates."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_attachment_count
msgid "Attachment Count"
msgstr "Manuste arv"

#. module: website_event_track
#: model:event.track.tag.category,name:website_event_track.event_track_tag_category_1
msgid "Audience"
msgstr "Audients"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__allowed_track_tag_ids
msgid "Available Track Tags"
msgstr "Saadaolevad jälgimise sildid"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Bandy clamp hack"
msgstr "Bandy clamp hack"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_biography
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Biography"
msgstr "Biograafia"

#. module: website_event_track
#: code:addons/website_event_track/models/event_track_stage.py:0
#: model:event.track,legend_blocked:website_event_track.event_7_track_1
#: model:event.track,legend_blocked:website_event_track.event_7_track_10
#: model:event.track,legend_blocked:website_event_track.event_7_track_11
#: model:event.track,legend_blocked:website_event_track.event_7_track_12
#: model:event.track,legend_blocked:website_event_track.event_7_track_13
#: model:event.track,legend_blocked:website_event_track.event_7_track_14
#: model:event.track,legend_blocked:website_event_track.event_7_track_15
#: model:event.track,legend_blocked:website_event_track.event_7_track_16
#: model:event.track,legend_blocked:website_event_track.event_7_track_17
#: model:event.track,legend_blocked:website_event_track.event_7_track_18
#: model:event.track,legend_blocked:website_event_track.event_7_track_19
#: model:event.track,legend_blocked:website_event_track.event_7_track_2
#: model:event.track,legend_blocked:website_event_track.event_7_track_20
#: model:event.track,legend_blocked:website_event_track.event_7_track_21
#: model:event.track,legend_blocked:website_event_track.event_7_track_22
#: model:event.track,legend_blocked:website_event_track.event_7_track_23
#: model:event.track,legend_blocked:website_event_track.event_7_track_24
#: model:event.track,legend_blocked:website_event_track.event_7_track_25
#: model:event.track,legend_blocked:website_event_track.event_7_track_26
#: model:event.track,legend_blocked:website_event_track.event_7_track_3
#: model:event.track,legend_blocked:website_event_track.event_7_track_4
#: model:event.track,legend_blocked:website_event_track.event_7_track_5
#: model:event.track,legend_blocked:website_event_track.event_7_track_6
#: model:event.track,legend_blocked:website_event_track.event_7_track_7
#: model:event.track,legend_blocked:website_event_track.event_7_track_8
#: model:event.track,legend_blocked:website_event_track.event_7_track_9
#: model:event.track,legend_blocked:website_event_track.event_7_track_l3_1
#: model:event.track,legend_blocked:website_event_track.event_7_track_l3_10
#: model:event.track,legend_blocked:website_event_track.event_7_track_l3_11
#: model:event.track,legend_blocked:website_event_track.event_7_track_l3_2
#: model:event.track,legend_blocked:website_event_track.event_track1
#: model:event.track,legend_blocked:website_event_track.event_track10
#: model:event.track,legend_blocked:website_event_track.event_track11
#: model:event.track,legend_blocked:website_event_track.event_track12
#: model:event.track,legend_blocked:website_event_track.event_track13
#: model:event.track,legend_blocked:website_event_track.event_track14
#: model:event.track,legend_blocked:website_event_track.event_track15
#: model:event.track,legend_blocked:website_event_track.event_track16
#: model:event.track,legend_blocked:website_event_track.event_track17
#: model:event.track,legend_blocked:website_event_track.event_track18
#: model:event.track,legend_blocked:website_event_track.event_track19
#: model:event.track,legend_blocked:website_event_track.event_track2
#: model:event.track,legend_blocked:website_event_track.event_track20
#: model:event.track,legend_blocked:website_event_track.event_track21
#: model:event.track,legend_blocked:website_event_track.event_track22
#: model:event.track,legend_blocked:website_event_track.event_track23
#: model:event.track,legend_blocked:website_event_track.event_track24
#: model:event.track,legend_blocked:website_event_track.event_track25
#: model:event.track,legend_blocked:website_event_track.event_track27
#: model:event.track,legend_blocked:website_event_track.event_track28
#: model:event.track,legend_blocked:website_event_track.event_track29
#: model:event.track,legend_blocked:website_event_track.event_track3
#: model:event.track,legend_blocked:website_event_track.event_track30
#: model:event.track,legend_blocked:website_event_track.event_track31
#: model:event.track,legend_blocked:website_event_track.event_track4
#: model:event.track,legend_blocked:website_event_track.event_track5
#: model:event.track,legend_blocked:website_event_track.event_track6
#: model:event.track,legend_blocked:website_event_track.event_track7
#: model:event.track,legend_blocked:website_event_track.event_track8
#: model:event.track,legend_blocked:website_event_track.event_track9
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage0
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage1
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage2
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage3
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage4
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage5
#, python-format
msgid "Blocked"
msgstr "Blokeeritud"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.registration_complete
msgid "Book your seats to the best talks"
msgstr "Broneerige kohad parimatele vestlustele"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Book your talks"
msgstr "Broneerige vestlused"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_14
msgid "Building a DIY cabin from the ground up"
msgstr "Building a DIY cabin from the ground up"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta_url
msgid "Button Target URL"
msgstr "Button Target URL"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta_title
msgid "Button Title"
msgstr "Button Title"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta_delay
msgid "Button appears"
msgstr "Button appears"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__is_website_cta_live
msgid "CTA button is available"
msgstr "CTA nupp on saadaval"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Call for Proposals"
msgstr "Call for Proposals"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__can_publish
msgid "Can Publish"
msgstr "Võib avalikustada"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__is_cancel
msgid "Canceled Stage"
msgstr "Tühistatud etapp"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage5
msgid "Cancelled"
msgstr "Tühistatud"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Categories"
msgstr "Kategooriad"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__category_id
msgid "Category"
msgstr "Kategooria"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_12
msgid "Climate positive"
msgstr "Kliima positiivne"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__color
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__color
msgid "Color"
msgstr "Värv"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__color
msgid "Color Index"
msgstr "Värvikood"

#. module: website_event_track
#: code:addons/website_event_track/controllers/event_track.py:0
#, python-format
msgid "Coming soon"
msgstr "Tulekul"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_cards
msgid "Coming soon ..."
msgstr "Tulekul..."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__company_id
msgid "Company"
msgstr "Ettevõte"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_company_name
msgid "Company Name"
msgstr "Ettevõtte nimi"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_res_config_settings
msgid "Config Settings"
msgstr "Seadistused"

#. module: website_event_track
#: model:mail.template,subject:website_event_track.mail_template_data_track_confirmation
msgid "Confirmation of {{ object.name }}"
msgstr "{{ object.name }} kinnitus"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage1
msgid "Confirmed"
msgstr "Kinnitatud"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_13
#: model_terms:event.track,description:website_event_track.event_7_track_3
msgid ""
"Considering to build a wooden house? Watch this video to find out more "
"information about a construction process and final result. Step by step "
"simple explanation! Interested?"
msgstr ""
"Kas kaalute puitmaja ehitamist? Ehitusprotsessi ja lõpptulemuse kohta "
"lisateabe saamiseks vaadake seda videot. Lihtne selgitus samm-sammult! Kas "
"olete huvitatud?"

#. module: website_event_track
#: code:addons/website_event_track/models/event_track.py:0
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_id
#, python-format
msgid "Contact"
msgstr "Kontakt"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Contact Details"
msgstr "Kontakti andmed"

#. module: website_event_track
#: code:addons/website_event_track/models/event_track.py:0
#: model:ir.model.fields,field_description:website_event_track.field_event_track__contact_email
#, python-format
msgid "Contact Email"
msgstr "Kontakt e-posti aadress"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__contact_phone
msgid "Contact Phone"
msgstr "Kontakttelefon"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__contact_email
msgid "Contact email is private and used internally"
msgstr ""
"Kontakte-posti aadress on privaatne ja seda kasutatakse ettevõttesiseselt"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid "Contact me through a different email/phone"
msgstr "Võtke minuga ühendust mõne muu e-posti/telefoni kaudu"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__partner_id
msgid "Contact of the track, may be different from speaker."
msgstr "Jälgitav kontakt võib esinejast erineda. "

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__contact_phone
msgid "Contact phone is private and used internally"
msgstr "Kontakttelefon on privaatne ja kasutatakse ettevõttesiseselt"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_from_event
msgid "Create a Track"
msgstr "Looge jälgimine"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_location
msgid "Create a Track Location"
msgstr "Looge jälgimise asukoht"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_tag
msgid "Create a Track Tag"
msgstr "Looge jälgimise silt"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__create_uid
msgid "Created by"
msgstr "Loonud"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__create_date
msgid "Created on"
msgstr "Loomise kuupäev"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
msgid "Customer"
msgstr "Klient"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_19
msgid "DIY Timber Cladding Project"
msgstr "DIY puitvoodri projekt"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Date"
msgstr "Date"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_11
msgid "Day 2 Wrapup"
msgstr "2. päeva kokkuvõte"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_8
msgid "Dealing with OpenWood Furniture"
msgstr "OpenWood mööbliga tegelemine"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "Define labels explaining kanban state management."
msgstr "Määratlege kanbani oleku haldamist selgitavad sildid."

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_stage_action
msgid ""
"Define the steps that will be used in the event from the\n"
"            creation of the track, up to the closing of the track.\n"
"            You will use these stages in order to track the progress in\n"
"            solving an event track."
msgstr ""
"Define the steps that will be used in the event from the\n"
"            creation of the track, up to the closing of the track.\n"
"            You will use these stages in order to track the progress in\n"
"            solving an event track."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "Delete"
msgstr "Kustuta"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__description
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__description
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Description"
msgstr "Kirjeldus"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__partner_tag_line
msgid "Description of the partner (name, function and company name)"
msgstr "Partneri kirjeldus (nimi, funktsioon ja ettevõtte nimi)"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track25
msgid "Design contest (entire afternoon)"
msgstr "Disainikonkurss (terve pärastlõuna)"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track24
msgid "Design contest (entire day)"
msgstr "Ideekonkurss (terve päev)"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track6
msgid "Detailed roadmap of our new products"
msgstr "Meie uute toodete üksikasjalik tegevuskava"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track11
msgid "Discover our new design team"
msgstr "Avastage meie uus disainimeeskond"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__display_name
msgid "Display Name"
msgstr "Näidatav nimi"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__website_cta
msgid ""
"Display a Call to Action button to your Attendees while they watch your "
"Track."
msgstr ""
"Display a Call to Action button to your Attendees while they watch your "
"Track."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_aside_other_track
msgid "Done"
msgstr "Tehtud"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Dowel Hack"
msgstr "Dowel Hack"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "Dropdown menu"
msgstr "Rippmenüü"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__duration
msgid "Duration"
msgstr "Kestus"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_3
msgid "Easy Way To Build a Wooden House"
msgstr "Lihtne viis puitmaja ehitamiseks"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "Edit Track"
msgstr "Redigeeri jälgimist"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_email
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid "Email"
msgstr "E-post"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__mail_template_id
msgid "Email Template"
msgstr "E-kirja näidis"

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#, python-format
msgid "Error"
msgstr "Viga"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_event
#: model:ir.model.fields,field_description:website_event_track.field_event_track__event_id
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Event"
msgstr "Sündmus"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_location_tree
msgid "Event Location"
msgstr "Sündmuse asukoht"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track_location
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_location_form
msgid "Event Locations"
msgstr "Sündmuse asukohad"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__track_proposal_menu_ids
#: model:ir.model.fields.selection,name:website_event_track.selection__website_event_menu__menu_type__track_proposal
msgid "Event Proposals Menus"
msgstr "Ürituse ettepanekute menüüd"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_type
msgid "Event Template"
msgstr "Sündmuse mall"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_tree
msgid "Event Track"
msgstr "Sündmuste jälgimine"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_location
msgid "Event Track Location"
msgstr "Sündmuste jälgimise asukoht"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_stage
msgid "Event Track Stage"
msgstr "Sündmuste jälgimise etapp"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_tag
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_tag_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_tag_tree
msgid "Event Track Tag"
msgstr "Sündmuste jälgimise silt"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_tag_category
msgid "Event Track Tag Category"
msgstr "Sündmuste jälgimise sildi kategooria"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track_from_event
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_calendar
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Event Tracks"
msgstr "Sündmuste jälgimisesed"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__track_menu_ids
#: model:ir.model.fields.selection,name:website_event_track.selection__website_event_menu__menu_type__track
msgid "Event Tracks Menus"
msgstr "Sündmuste jälgimise menüüd"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_21
msgid "Event Wrapup"
msgstr "Sündmuse kokkuvõte"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_res_config_settings__events_app_name
#: model:ir.model.fields,field_description:website_event_track.field_website__events_app_name
msgid "Events App Name"
msgstr "Sündmuse rakenduse nimi"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.res_config_settings_view_form
msgid "Events PWA"
msgstr "Sündmused PWA"

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#: model_terms:ir.ui.view,arch_db:website_event_track.track_widget_reminder
#, python-format
msgid "Favorite On"
msgstr "Favorite On"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.session_topbar
msgid "Favorites"
msgstr "Lemmikud"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_topbar
msgid "Filter Tracks..."
msgstr "Filtreeri jälgimisi..."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Finished"
msgstr "Lõpetatud"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_2
msgid "First Day Wrapup"
msgstr "Esimese päeva kokkuvõte"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__fold
msgid "Folded in Kanban"
msgstr "Kanbanis volditud"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_follower_ids
msgid "Followers"
msgstr "Jälgijad"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_partner_ids
msgid "Followers (Partners)"
msgstr "Jälgijad (partnerid)"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome icon nt. fa-tasks"

#. module: website_event_track
#: model:event.track.tag.category,name:website_event_track.event_track_tag_category_2
msgid "Format"
msgstr "Formaat"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__is_fully_accessible
msgid "Fully accessible"
msgstr "Täielikult ligipääsetav"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Future Activities"
msgstr "Tulevased tegevused"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.registration_complete
msgid "Get prepared and"
msgstr "Valmita ette ja"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Glue tip"
msgstr "Glue tip"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__kanban_state__done
msgid "Green"
msgstr "Roheline"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__legend_done
msgid "Green Kanban Label"
msgstr "Roheline Kanban´i silt."

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__kanban_state__normal
msgid "Grey"
msgstr "Hall"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__legend_normal
msgid "Grey Kanban Label"
msgstr "Hall Kanbani silt"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Group By"
msgstr "Grupeeri"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_l3_11
msgid "Happy with OpenWood"
msgstr "Happy with OpenWood"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__has_message
msgid "Has Message"
msgstr "On sõnum"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__priority__2
msgid "High"
msgstr "Kõrge"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__priority__3
msgid "Highest"
msgstr "Kõrgeim"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.pwa_offline
msgid "Home page"
msgstr "Avaleht"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track21
msgid "House of World Cultures"
msgstr "House of World Cultures"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid "How can our team get in touch with you?"
msgstr "Kuidas saab meie meeskond Teiega ühendust võtta?"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track18
msgid "How to build your marketing strategy within a competitive environment"
msgstr "Kuidas luua oma turundusstrateegiat konkurentsikeskkonnas"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track15
msgid "How to communicate with your community"
msgstr "Kuidas oma kogukonnaga suhelda"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track1
msgid "How to design a new piece of furniture"
msgstr "Kuidas kujundada uut mööblit"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track4
msgid "How to develop automated processes"
msgstr "Kuidas arendada automatiseeritud protsesse"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track16
msgid "How to follow us on the social media"
msgstr "Kuidas meid sotsiaalmeedias jälgida"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track9
msgid "How to improve your quality processes"
msgstr "Kuidas parandada oma kvaliteediprotsesse"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track2
msgid "How to integrate hardware materials in your pieces of furniture"
msgstr "Kuidas integreerida riistvara materjale oma mööbliesemetesse"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track8
msgid "How to optimize your sales, from leads to sales orders"
msgstr "Kuidas optimeerida müüki alates müügivihjetest kuni müügitellimusteni"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__id
msgid "ID"
msgstr "ID"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_exception_icon
msgid "Icon"
msgstr "Ikoon"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikoon, mis näitab erandi tegevust"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_stage__is_fully_accessible
msgid ""
"If checked, automatically publish tracks so that access links to customers "
"are provided."
msgstr ""
"Kui märgitud, avaldate lood automaatselt, et klientidele oleks "
"juurdepääsulingid."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__message_needaction
#: model:ir.model.fields,help:website_event_track.field_event_track__message_unread
msgid "If checked, new messages require your attention."
msgstr "Kui märgitud, siis uued sõnumid nõuavad Su tähelepanu."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__message_has_error
#: model:ir.model.fields,help:website_event_track.field_event_track__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Kui valitud, on mõningatel sõnumitel saatmiserror."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_stage__is_visible_in_agenda
msgid "If checked, the related tracks will be visible in the frontend."
msgstr "If checked, the related tracks will be visible in the frontend."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_stage__mail_template_id
msgid ""
"If set an email will be sent to the customer when the track reaches this "
"step."
msgstr ""
"If set an email will be sent to the customer when the track reaches this "
"step."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__wishlisted_by_default
msgid ""
"If set, the talk will be set as favorite for each attendee registered to the"
" event."
msgstr ""
"If set, the talk will be set as favorite for each attendee registered to the"
" event."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_image_url
msgid "Image URL"
msgstr "Pildi URL"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_card
msgid "In"
msgstr "Sisse"

#. module: website_event_track
#: code:addons/website_event_track/models/event_track_stage.py:0
#: model:event.track,legend_normal:website_event_track.event_7_track_1
#: model:event.track,legend_normal:website_event_track.event_7_track_10
#: model:event.track,legend_normal:website_event_track.event_7_track_11
#: model:event.track,legend_normal:website_event_track.event_7_track_12
#: model:event.track,legend_normal:website_event_track.event_7_track_13
#: model:event.track,legend_normal:website_event_track.event_7_track_14
#: model:event.track,legend_normal:website_event_track.event_7_track_15
#: model:event.track,legend_normal:website_event_track.event_7_track_16
#: model:event.track,legend_normal:website_event_track.event_7_track_17
#: model:event.track,legend_normal:website_event_track.event_7_track_18
#: model:event.track,legend_normal:website_event_track.event_7_track_19
#: model:event.track,legend_normal:website_event_track.event_7_track_2
#: model:event.track,legend_normal:website_event_track.event_7_track_20
#: model:event.track,legend_normal:website_event_track.event_7_track_21
#: model:event.track,legend_normal:website_event_track.event_7_track_22
#: model:event.track,legend_normal:website_event_track.event_7_track_23
#: model:event.track,legend_normal:website_event_track.event_7_track_24
#: model:event.track,legend_normal:website_event_track.event_7_track_25
#: model:event.track,legend_normal:website_event_track.event_7_track_26
#: model:event.track,legend_normal:website_event_track.event_7_track_3
#: model:event.track,legend_normal:website_event_track.event_7_track_4
#: model:event.track,legend_normal:website_event_track.event_7_track_5
#: model:event.track,legend_normal:website_event_track.event_7_track_6
#: model:event.track,legend_normal:website_event_track.event_7_track_7
#: model:event.track,legend_normal:website_event_track.event_7_track_8
#: model:event.track,legend_normal:website_event_track.event_7_track_9
#: model:event.track,legend_normal:website_event_track.event_7_track_l3_1
#: model:event.track,legend_normal:website_event_track.event_7_track_l3_10
#: model:event.track,legend_normal:website_event_track.event_7_track_l3_11
#: model:event.track,legend_normal:website_event_track.event_7_track_l3_2
#: model:event.track,legend_normal:website_event_track.event_track1
#: model:event.track,legend_normal:website_event_track.event_track10
#: model:event.track,legend_normal:website_event_track.event_track11
#: model:event.track,legend_normal:website_event_track.event_track12
#: model:event.track,legend_normal:website_event_track.event_track13
#: model:event.track,legend_normal:website_event_track.event_track14
#: model:event.track,legend_normal:website_event_track.event_track15
#: model:event.track,legend_normal:website_event_track.event_track16
#: model:event.track,legend_normal:website_event_track.event_track17
#: model:event.track,legend_normal:website_event_track.event_track18
#: model:event.track,legend_normal:website_event_track.event_track19
#: model:event.track,legend_normal:website_event_track.event_track2
#: model:event.track,legend_normal:website_event_track.event_track20
#: model:event.track,legend_normal:website_event_track.event_track21
#: model:event.track,legend_normal:website_event_track.event_track22
#: model:event.track,legend_normal:website_event_track.event_track23
#: model:event.track,legend_normal:website_event_track.event_track24
#: model:event.track,legend_normal:website_event_track.event_track25
#: model:event.track,legend_normal:website_event_track.event_track27
#: model:event.track,legend_normal:website_event_track.event_track28
#: model:event.track,legend_normal:website_event_track.event_track29
#: model:event.track,legend_normal:website_event_track.event_track3
#: model:event.track,legend_normal:website_event_track.event_track30
#: model:event.track,legend_normal:website_event_track.event_track31
#: model:event.track,legend_normal:website_event_track.event_track4
#: model:event.track,legend_normal:website_event_track.event_track5
#: model:event.track,legend_normal:website_event_track.event_track6
#: model:event.track,legend_normal:website_event_track.event_track7
#: model:event.track,legend_normal:website_event_track.event_track8
#: model:event.track,legend_normal:website_event_track.event_track9
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage0
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage1
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage2
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage3
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage4
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage5
#, python-format
msgid "In Progress"
msgstr "Töös"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_15
msgid "In this video we will see how lumber is made in a sawmill factory."
msgstr ""
"Selles videos näeme, kuidas saeveskitehases saematerjali valmistatakse."

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "In this video, I covered 6 tips and tricks to help out beginners:"
msgstr "Selles videos käsitlesin 6 nõuannet ja nippi, mis aitavad algajaid:"

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/xml/website_event_pwa.xml:0
#, python-format
msgid "Install"
msgstr "Paigalda"

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/xml/website_event_pwa.xml:0
#, python-format
msgid "Install Application"
msgstr "Installige rakendus"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Interactivity"
msgstr "Interaktiivsus"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Introduction"
msgstr "Esitlemine"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_website_cta_live
msgid "Is CTA Live"
msgstr "Is CTA Live"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_is_follower
msgid "Is Follower"
msgstr "Jälgija"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_published
msgid "Is Published"
msgstr "On avalikustatud"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_reminder_on
msgid "Is Reminder On"
msgstr "Kas meeldetuletus on sisse lülitatud"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_done
msgid "Is Track Done"
msgstr "Is Track Done"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_live
msgid "Is Track Live"
msgstr "Is Track Live"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_soon
msgid "Is Track Soon"
msgstr "Is Track Soon"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_today
msgid "Is Track Today"
msgstr "Is Track Today"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_upcoming
msgid "Is Track Upcoming"
msgstr "Is Track Upcoming"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__is_wishlisted
msgid "Is Wishlisted"
msgstr "On soovinimekirjas"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__is_blacklisted
msgid "Is reminder off"
msgstr "Kas meeldetuletus on väljas"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_function
msgid "Job Position"
msgstr "Ametikoht"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Job Title"
msgstr "Ametinimetus"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__legend_blocked
msgid "Kanban Blocked Explanation"
msgstr "Blokeeritud kanbani selgitus"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__legend_normal
msgid "Kanban Ongoing Explanation"
msgstr "Kanbani jooksev selgitus"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__kanban_state
msgid "Kanban State"
msgstr "Kanbani seisund"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__kanban_state_label
msgid "Kanban State Label"
msgstr "Kanban´i oleku silt"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__legend_done
msgid "Kanban Valid Explanation"
msgstr "Kanban´i kehtiv seletus"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track23
msgid "Key Success factors selling our furniture"
msgstr "Meie mööbli müügi peamised edutegurid"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_9
msgid "Kitchens for the Future"
msgstr "Köögid tulevikuks"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track____last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location____last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage____last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag____last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category____last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor____last_update
msgid "Last Modified on"
msgstr "Viimati muudetud"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__write_uid
msgid "Last Updated by"
msgstr "Viimati uuendas"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__write_date
msgid "Last Updated on"
msgstr "Viimati uuendatud"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Late Activities"
msgstr "Hilinenud tegevused"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track12
msgid "Latest trends"
msgstr "Viimased trendid"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_26
msgid "Less Furniture is More Furniture"
msgstr "Vähem mööblit on rohkem mööblit"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_4
msgid "Life at Home Around the World: William’s Story"
msgstr "Life at Home Around the World: William’s Story"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_19
msgid ""
"Link to Q&amp;A here! The time has come to hide those old block walls. Love "
"simple and transformation type projects like this! :)-"
msgstr ""
"Link to Q&amp;A here! The time has come to hide those old block walls. Love "
"simple and transformation type projects like this! :)-"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_aside_other_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Live"
msgstr "Online"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_cards
msgid "Live Now"
msgstr "Live Now"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_l3_10
msgid "Live Testimonial"
msgstr "Otseülekanne"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_25
msgid "Live Testimonials"
msgstr "Otseülekanded"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__location_id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__name
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Location"
msgstr "Asukoht"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_13
msgid "Log House Building"
msgstr "Palkmaja ehitus"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_15
msgid "Logs to lumber"
msgstr "Palgid saematerjaliks"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__priority__0
msgid "Low"
msgstr "Madal"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track31
msgid "Lunch"
msgstr "Lõuna"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta
msgid "Magic Button"
msgstr "Maagiline nupp"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_main_attachment_id
msgid "Main Attachment"
msgstr "Peamine manus"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Making a center marking gauge"
msgstr "Making a center marking gauge"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_location
msgid ""
"Manage from here the places where you organize your tracks (e.g. Rooms, "
"Channels, ...)."
msgstr ""
"Manage from here the places where you organize your tracks (e.g. Rooms, "
"Channels, ...)."

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__priority__1
msgid "Medium"
msgstr "Levitamise vahend"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr "Menüü tüüp"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_has_error
msgid "Message Delivery error"
msgstr "Sõnumi edastamise veateade"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_ids
msgid "Messages"
msgstr "Sõnumid"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track22
msgid "Minimal but efficient design"
msgstr "Minimaalne, kuid tõhus disain"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta_start_remaining
msgid "Minutes before CTA starts"
msgstr "Minutid enne CTA algust"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__track_start_remaining
msgid "Minutes before track starts"
msgstr "Minutid enne loo algust"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__track_start_relative
msgid "Minutes compare to track start"
msgstr "Minutes compare to track start"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Miter saw tip"
msgstr "Miter saw tip"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track30
msgid "Morning break"
msgstr "Hommikupaus"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Minu tegevuse tähtaeg"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track27
msgid "My Company global presentation"
msgstr "Minu ettevõtte ülemaailmne esitlus"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "My Tracks"
msgstr "My Lood"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__name
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
#: model_terms:ir.ui.view,arch_db:website_event_track.res_config_settings_view_form
msgid "Name"
msgstr "Nimi"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.res_config_settings_view_form
msgid "Name of your website's Events Progressive Web Application"
msgstr "Teie veebisaidi sündmuste progressiivse veebirakenduse nimi"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track20
msgid "New Certification Program"
msgstr "Uus sertifitseerimise programm"

#. module: website_event_track
#: model:mail.message.subtype,name:website_event_track.mt_event_track
msgid "New Track"
msgstr "Uus lugu"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Järgmine tegevus kalendris"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Järgmise tegevuse kuupäev"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_summary
msgid "Next Activity Summary"
msgstr "Järgmise tegevuse kokkuvõte"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_type_id
msgid "Next Activity Type"
msgstr "Järgmise tegevuse tüüp"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_visitor_action
msgid "No Track Visitors yet!"
msgstr "Loo külastajaid veel pole!"

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/website_event_track_proposal_form_tags.js:0
#, python-format
msgid "No results found"
msgstr "Tulemusi ei leitud"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_action_from_visitor
msgid "No track favorited by this visitor"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_main
msgid "No track found."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_tag__color
msgid "Note that colorless tags won't be available on the website."
msgstr "Värvitud sildid pole veebilehel saadaval."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_needaction_counter
msgid "Number of Actions"
msgstr "Toimingute arv"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_has_error_counter
msgid "Number of errors"
msgstr "Veateadete arv"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Tegutsemist nõudvate sõnumite arv"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Kohaletoimetamise veateatega sõnumite arv"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__message_unread_counter
msgid "Number of unread messages"
msgstr "Lugemata sõnumite arv"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_24
msgid "Old is New"
msgstr "Vana on uus"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_20
msgid "Our Last Day Together !"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__partner_id
msgid "Partner"
msgstr "Partner"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track14
msgid "Partnership programs"
msgstr "Partnerlusprogrammid"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_phone
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid "Phone"
msgstr "Telefon"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Picture"
msgstr "Pilt"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Plan your experience by adding your favorites talks to your wishlist"
msgstr "Saa uusi teadmisi, lisades oma lemmikvestlused soovinimekirja."

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/website_event_track_proposal_form.js:0
#, python-format
msgid "Please enter either a contact email address or a contact phone number."
msgstr "Palun sisestage kontakti e-posti aadress või kontakttelefon."

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/website_event_track_proposal_form.js:0
#, python-format
msgid "Please fill out the form correctly."
msgstr "Palun täida vorm õigesti."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track3
msgid "Portfolio presentation"
msgstr "Portfoolio esitlus"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_16
msgid "Pretty. Ugly. Lovely."
msgstr "Ilus. Kole. Armas."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.pwa_offline
msgid "Previous page"
msgstr "Eelmine lehekülg"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__priority
msgid "Priority"
msgstr "Prioriteet"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_7
msgid ""
"Probably one of the most asked questions I've gotten is how I got started "
"woodworking! In this video I share with you how/why I started building "
"furniture!"
msgstr ""

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage0
msgid "Proposal"
msgstr "Proposal"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Proposals are closed!"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__website_track_proposal
msgid "Proposals on Website"
msgstr ""

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage3
msgid "Published"
msgstr "Avalikustatud"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track10
msgid "Raising qualitive insights from your customers"
msgstr "Tagasiside klientide poolt"

#. module: website_event_track
#: code:addons/website_event_track/models/event_track_stage.py:0
#: model:event.track,legend_done:website_event_track.event_7_track_1
#: model:event.track,legend_done:website_event_track.event_7_track_10
#: model:event.track,legend_done:website_event_track.event_7_track_11
#: model:event.track,legend_done:website_event_track.event_7_track_12
#: model:event.track,legend_done:website_event_track.event_7_track_13
#: model:event.track,legend_done:website_event_track.event_7_track_14
#: model:event.track,legend_done:website_event_track.event_7_track_15
#: model:event.track,legend_done:website_event_track.event_7_track_16
#: model:event.track,legend_done:website_event_track.event_7_track_17
#: model:event.track,legend_done:website_event_track.event_7_track_18
#: model:event.track,legend_done:website_event_track.event_7_track_19
#: model:event.track,legend_done:website_event_track.event_7_track_2
#: model:event.track,legend_done:website_event_track.event_7_track_20
#: model:event.track,legend_done:website_event_track.event_7_track_21
#: model:event.track,legend_done:website_event_track.event_7_track_22
#: model:event.track,legend_done:website_event_track.event_7_track_23
#: model:event.track,legend_done:website_event_track.event_7_track_24
#: model:event.track,legend_done:website_event_track.event_7_track_25
#: model:event.track,legend_done:website_event_track.event_7_track_26
#: model:event.track,legend_done:website_event_track.event_7_track_3
#: model:event.track,legend_done:website_event_track.event_7_track_4
#: model:event.track,legend_done:website_event_track.event_7_track_5
#: model:event.track,legend_done:website_event_track.event_7_track_6
#: model:event.track,legend_done:website_event_track.event_7_track_7
#: model:event.track,legend_done:website_event_track.event_7_track_8
#: model:event.track,legend_done:website_event_track.event_7_track_9
#: model:event.track,legend_done:website_event_track.event_7_track_l3_1
#: model:event.track,legend_done:website_event_track.event_7_track_l3_10
#: model:event.track,legend_done:website_event_track.event_7_track_l3_11
#: model:event.track,legend_done:website_event_track.event_7_track_l3_2
#: model:event.track,legend_done:website_event_track.event_track1
#: model:event.track,legend_done:website_event_track.event_track10
#: model:event.track,legend_done:website_event_track.event_track11
#: model:event.track,legend_done:website_event_track.event_track12
#: model:event.track,legend_done:website_event_track.event_track13
#: model:event.track,legend_done:website_event_track.event_track14
#: model:event.track,legend_done:website_event_track.event_track15
#: model:event.track,legend_done:website_event_track.event_track16
#: model:event.track,legend_done:website_event_track.event_track17
#: model:event.track,legend_done:website_event_track.event_track18
#: model:event.track,legend_done:website_event_track.event_track19
#: model:event.track,legend_done:website_event_track.event_track2
#: model:event.track,legend_done:website_event_track.event_track20
#: model:event.track,legend_done:website_event_track.event_track21
#: model:event.track,legend_done:website_event_track.event_track22
#: model:event.track,legend_done:website_event_track.event_track23
#: model:event.track,legend_done:website_event_track.event_track24
#: model:event.track,legend_done:website_event_track.event_track25
#: model:event.track,legend_done:website_event_track.event_track27
#: model:event.track,legend_done:website_event_track.event_track28
#: model:event.track,legend_done:website_event_track.event_track29
#: model:event.track,legend_done:website_event_track.event_track3
#: model:event.track,legend_done:website_event_track.event_track30
#: model:event.track,legend_done:website_event_track.event_track31
#: model:event.track,legend_done:website_event_track.event_track4
#: model:event.track,legend_done:website_event_track.event_track5
#: model:event.track,legend_done:website_event_track.event_track6
#: model:event.track,legend_done:website_event_track.event_track7
#: model:event.track,legend_done:website_event_track.event_track8
#: model:event.track,legend_done:website_event_track.event_track9
#: model:event.track.stage,legend_done:website_event_track.event_track_stage0
#: model:event.track.stage,legend_done:website_event_track.event_track_stage1
#: model:event.track.stage,legend_done:website_event_track.event_track_stage2
#: model:event.track.stage,legend_done:website_event_track.event_track_stage3
#: model:event.track.stage,legend_done:website_event_track.event_track_stage4
#: model:event.track.stage,legend_done:website_event_track.event_track_stage5
#, python-format
msgid "Ready for Next Stage"
msgstr "Järgmise etapi jaoks valmis"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__kanban_state__blocked
msgid "Red"
msgstr "Punane"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__legend_blocked
msgid "Red Kanban Label"
msgstr "Punane Kanban´i silt"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage4
msgid "Refused"
msgstr "Tagasi lükatud"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__track_start_relative
msgid "Relative time compared to track start (seconds)"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__website_cta_start_remaining
msgid "Remaining time before CTA starts (seconds)"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__track_start_remaining
msgid "Remaining time before track starts (seconds)"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__user_id
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Responsible"
msgstr "Vastutaja"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_user_id
msgid "Responsible User"
msgstr "Vastutav kasutaja"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_23
msgid "Restoring Old Woodworking Tools"
msgstr ""

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_23
msgid "Restoring old woodworking tools"
msgstr ""

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Right angle clamp jig"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO optimeeritud"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Sõnumi kohaletoimetamise veateade"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_main
msgid "Schedule some tracks to get started"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_6
msgid "Securing your Lumber during transport"
msgstr "Saematerjali kindlustamine transpordi ajal"

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/website_event_track_proposal_form_tags.js:0
#, python-format
msgid "Select categories"
msgstr "Vali kategooriad"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__seo_name
msgid "Seo name"
msgstr "Seo nimi"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__sequence
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__sequence
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__sequence
msgid "Sequence"
msgstr "Järjestus"

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#: model_terms:ir.ui.view,arch_db:website_event_track.track_widget_reminder
#, python-format
msgid "Set Favorite"
msgstr "Määra lemmikuks"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Näita kõiki andmeid, mille järgmise tegevuse kuupäev on enne tänast kuupäeva"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_event_view_form
msgid "Showcase Tracks"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Speaker"
msgstr "Esineja"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Speaker Bio"
msgstr "Esineja biograafia"

#. module: website_event_track
#: code:addons/website_event_track/models/event_track.py:0
#, python-format
msgid "Speaker Email"
msgstr "Esinaja e-post"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__image
msgid "Speaker Photo"
msgstr "Esinaja foto"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__partner_email
msgid ""
"Speaker email is used for public display and may vary from contact email"
msgstr ""
"Esineja e-posti aadressi avalikustatakse ja see võib kontakt e-posti "
"aadressist erineda"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__partner_name
msgid "Speaker name is used for public display and may vary from contact name"
msgstr "Esineja nime avalikustatakse ja see võib pärisnimest erineda"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__partner_phone
msgid ""
"Speaker phone is used for public display and may vary from contact phone"
msgstr ""
"Esineja telefoninumbrit avalikustatakse ja see võib kontakttelefonist "
"erineda"

#. module: website_event_track
#: code:addons/website_event_track/models/event_track.py:0
#, python-format
msgid "Speakers"
msgstr "Esinajad"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__stage_id
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Stage"
msgstr "Etapp"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "Stage Description and Tooltips"
msgstr "Etappide kirjeldused ja õpetused"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__name
msgid "Stage Name"
msgstr "Etapi nimi"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track28
msgid "Status & Strategy"
msgstr "Staatus & Strateegia"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Tegevuspõhised staatused\n"
"Üle aja: Tähtaeg on juba möödas\n"
"Täna: Tegevuse tähtaeg on täna\n"
"Planeeritud: Tulevased tegevused"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Submission Agreement"
msgstr "Leping"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Submit Proposal"
msgstr "Saada ettepanek"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_tag_line
msgid "Tag Line"
msgstr "Sildi rida"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__name
msgid "Tag Name"
msgstr "Sildi nimi"

#. module: website_event_track
#: model:ir.model.constraint,message:website_event_track.constraint_event_track_tag_name_uniq
msgid "Tag name already exists !"
msgstr "Sildi nimi on juba olemas!"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__tag_ids
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__tag_ids
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_tag_category_view_form
msgid "Tags"
msgstr "Sildid"

#. module: website_event_track
#: code:addons/website_event_track/models/event_event.py:0
#, python-format
msgid "Talk Proposals"
msgstr "Ettepanekud"

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#, python-format
msgid "Talk added to your Favorites"
msgstr "Vestlus on lisatud sinu lemmikutesse"

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#, python-format
msgid "Talk already in your Favorites"
msgstr "Vestlus on juba sinu lemmikutes"

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#, python-format
msgid "Talk removed from your Favorites"
msgstr "Vestlus on eemaldatud sinu lemmikutest"

#. module: website_event_track
#: code:addons/website_event_track/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_aside
#, python-format
msgid "Talks"
msgstr "Vestlused"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Talks Types"
msgstr "Vestluse tüübid"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "Task in progress. Click to block or set as done."
msgstr "Töös ülesanded. Klõpsa peale, et neid blokeerida või märkida tehtuks."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "Task is blocked. Click to unblock or set as done."
msgstr ""
"Ülesanne on blokeeritud. Kliki, et blokeering tühistada või märkida "
"\"Tehtud\"."

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/xml/event_track_proposal_templates.xml:0
#, python-format
msgid "Thank you for your proposal."
msgstr "Aitäh ettepaneku eest!"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__website_url
msgid "The full URL to access the document through the website."
msgstr "Dokumendile veebisaidi kaudu juurdepääsuks täielik URL."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track17
#: model:event.track,name:website_event_track.event_track29
msgid "The new marketing strategy"
msgstr "Uus turundusstrateegia"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track5
msgid "The new way to promote your creations"
msgstr "Uus viis omaloomingu reklaamimiseks"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_17
msgid ""
"There are a lot of ideas worth exploring so start with the 10 DIY furniture "
"ideas for absolute beginners."
msgstr ""

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_5
msgid ""
"There are several variants of wood is available in the world but we are talking about most expensive\n"
"    ones in the world and keeping to the point we have arranged ten most expensive wood."
msgstr ""

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_visitor_action
msgid ""
"They will be created automatically once attendees start browsing your "
"events."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "This event does not accept proposals."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_website__app_icon
msgid ""
"This field holds the image used as mobile app icon on the website (PNG "
"format)."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_res_config_settings__events_app_name
#: model:ir.model.fields,help:website_event_track.field_website__events_app_name
msgid "This fields holds the Event's Progressive Web App name."
msgstr "See väli sisaldab sündmuse progressiivse veebirakenduse nime."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.pwa_offline
msgid ""
"This page hasn't been saved for offline reading yet.<br/>Please check your "
"network connection."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_stage__fold
msgid ""
"This stage is folded in the kanban view when there are no records in that "
"stage to display."
msgstr ""
"See etapp on kanban vaates volditud, kui puuduvad etapikirjed, mida kuvada."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "This step is done. Click to block or set in progress."
msgstr "See etapp on tehtud. Klõpsa peale, et blokeerida või töösse määrata."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"Timely release of presentation material (slides),\n"
"                                    for publishing on our website."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__name
msgid "Title"
msgstr "Tiitel"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Today Activities"
msgstr "Tänased tegevused"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_22
msgid "Tools for the Woodworking Beginner"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_5
msgid "Top 10 Most Expensive Wood in the World"
msgstr ""

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_5
msgid ""
"Top most expensive wood in the world is quite interesting topic and several people may be surprised\n"
"    that there are hundreds of wood types exist around the globe following different properties and use."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__track_id
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Track"
msgstr "Jälgi"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_visitor
msgid "Track / Visitor Link"
msgstr "Jälgi / Külastaja link"

#. module: website_event_track
#: model:mail.message.subtype,name:website_event_track.mt_track_blocked
msgid "Track Blocked"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__track_count
msgid "Track Count"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__date
msgid "Track Date"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__date_end
msgid "Track End Date"
msgstr ""

#. module: website_event_track
#: model:ir.ui.menu,name:website_event_track.menu_event_track_location
msgid "Track Locations"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_type_view_form_inherit_track
msgid "Track Proposals Menu Item"
msgstr ""

#. module: website_event_track
#: model:mail.message.subtype,name:website_event_track.mt_track_ready
msgid "Track Ready"
msgstr ""

#. module: website_event_track
#: model:mail.message.subtype,description:website_event_track.mt_track_ready
msgid "Track Ready for Next Stage"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_tree
msgid "Track Stage"
msgstr ""

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.event_track_stage_action
#: model:ir.ui.menu,name:website_event_track.event_track_stage_menu
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_search
msgid "Track Stages"
msgstr ""

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.event_track_tag_category_action
#: model:ir.ui.menu,name:website_event_track.event_track_tag_category_menu
msgid "Track Tag Categories"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_tag_category_view_form
msgid "Track Tag Category"
msgstr ""

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track_tag
#: model:ir.model.fields,field_description:website_event_track.field_event_event__tracks_tag_ids
#: model:ir.ui.menu,name:website_event_track.menu_event_track_tag
msgid "Track Tags"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_tag_category_view_list
msgid "Track Tags Category"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_form
msgid "Track Visitor"
msgstr "Jälgi külastajat"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.event_track_visitor_action
#: model:ir.model.fields,field_description:website_event_track.field_event_track__event_track_visitor_ids
#: model:ir.model.fields,field_description:website_event_track.field_website_visitor__event_track_visitor_ids
#: model:ir.ui.menu,name:website_event_track.event_track_visitor_menu
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_list
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
msgid "Track Visitors"
msgstr ""

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_visitor_action
msgid ""
"Track Visitors store statistics on your events, including how many times "
"tracks have been wishlisted."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__is_track_soon
msgid "Track begins soon"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__is_track_today
msgid "Track begins today"
msgstr ""

#. module: website_event_track
#: model:mail.message.subtype,description:website_event_track.mt_track_blocked
msgid "Track blocked"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__duration
msgid "Track duration in hours."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__is_track_live
msgid "Track has started and is ongoing"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__is_track_done
msgid "Track is finished"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__is_track_upcoming
msgid "Track is not yet started"
msgstr ""

#. module: website_event_track
#: model:mail.template,name:website_event_track.mail_template_data_track_confirmation
msgid "Track: Confirmation"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__track_ids
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__track_ids
#: model:ir.ui.menu,name:website_event_track.menu_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_graph
#: model_terms:ir.ui.view,arch_db:website_event_track.website_visitor_view_form
msgid "Tracks"
msgstr "Jälgimine"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_type_view_form_inherit_track
msgid "Tracks Menu Item"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_type__website_track_proposal
msgid "Tracks Proposals on Website"
msgstr ""

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_from_event
msgid ""
"Tracks define your event schedule. They can be talks, workshops or any "
"similar activity."
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__website_track
#: model:ir.model.fields,field_description:website_event_track.field_event_type__website_track
msgid "Tracks on Website"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Kirjel oleva erandtegevuse tüüp."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_aside_other_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Unpublished"
msgstr "Avalikustamata"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_unread
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Unread Messages"
msgstr "Lugemata sõnumid"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Lugemata sõnumite loendur"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_6
msgid ""
"Use these simple steps to easily haul LONG lumber in a short box pickup truck.  A dose of carpenter's\n"
"    ingenuity along with a couple boards, a sturdy strap and a few screws are all I use to easily haul\n"
"    long boards from the lumberyard to the Next Level Carpentry shop or jobsite."
msgstr ""

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_6
msgid ""
"Using a unique wrapping method for a tie down strap (NOT Bungee cords!!!) allows lumber to be\n"
"    cinched securely WITHOUT the need to tie and untie tricky or complicated knots."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "View Track"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__is_visible_in_agenda
msgid "Visible in agenda"
msgstr "Nähtav päevakorras"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_published
msgid "Visible on current website"
msgstr "Nähtav praegusel veebilehel"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__visitor_id
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
msgid "Visitor"
msgstr "Külastaja"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__wishlist_visitor_ids
msgid "Visitor Wishlist"
msgstr "Külastaja soovinimekiri"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.website_visitor_action_from_track
msgid "Visitors Wishlist"
msgstr "Külastajate soovinimekiri"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_l3_1
msgid "Voice from Customer"
msgstr "Kliendi hääl"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.website_visitor_action_from_track
msgid "Wait for visitors to add this track to their list of favorites"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_main
msgid "We did not find any track matching your"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "We require speakers to accept an agreement in which they commit to:"
msgstr "Me nõuame, et esinejad nõustuksid lepinguga, milles nad kohustuvad:"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"We will accept a broad range of\n"
"                            presentations, from reports on academic and\n"
"                            commercial projects to tutorials and case\n"
"                            studies. As long as the presentation is\n"
"                            interesting and potentially useful to the\n"
"                            audience, it will be considered for\n"
"                            inclusion in the programme."
msgstr ""

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/xml/event_track_proposal_templates.xml:0
#, python-format
msgid "We will evaluate your proposition and get back to you shortly."
msgstr ""

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_website
msgid "Website"
msgstr "Veebileht"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_website__app_icon
msgid "Website App Icon"
msgstr "Veebilehe rakenduse ikoon"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_website_event_menu
msgid "Website Event Menu"
msgstr "Veebilehe sündmuste menüü"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_image
msgid "Website Image"
msgstr "Veebilehe pilt"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_website_menu
msgid "Website Menu"
msgstr "Veebisaidi menüü"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_message_ids
msgid "Website Messages"
msgstr "Veebilehe sõnumid"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_url
msgid "Website URL"
msgstr "Veebilehe URL"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_website_visitor
msgid "Website Visitor"
msgstr "Veebilehe Külastaja"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__website_message_ids
msgid "Website communication history"
msgstr "Veebilehe suhtluse ajalugu"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_meta_description
msgid "Website meta description"
msgstr "Veebilehe metakirjeldus"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_meta_keywords
msgid "Website meta keywords"
msgstr "Veebilehe meta võtmesõnad"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_meta_title
msgid "Website meta title"
msgstr "Veebilehe metapealkiri"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_meta_og_img
msgid "Website opengraph image"
msgstr "Veebilehe opengraph pilt"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_10
msgid "Welcome to Day 2"
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_1
msgid "What This Event Is All About"
msgstr "Mida see sündmus endast kujutab"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "What is your talk about?"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"Who will give this talk? We will show this to attendees to showcase your "
"talk."
msgstr ""

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_l3_2
msgid "Who's OpenWood anyway ?"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Wishlisted By"
msgstr "Soovinimekirja lisatud"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.event_track_action_from_visitor
#: model:ir.model.fields,field_description:website_event_track.field_website_visitor__event_track_wishlisted_ids
msgid "Wishlisted Tracks"
msgstr "Soovinimekirjas vestlused"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_7
msgid "Woodworking: How I got started!"
msgstr ""

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/website_event_track_proposal_form.js:0
#, python-format
msgid "You cannot access this page."
msgstr ""

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#, python-format
msgid ""
"You have to enable push notifications to get reminders for your favorite "
"tracks."
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.pwa_offline
msgid "You're offline!"
msgstr "Oled offline!"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_card
msgid "ago"
msgstr "tagasi"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "e.g. Get Yours Now !"
msgstr "e.g. Hangi kohe!"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "e.g. Inspiring Business Talk"
msgstr "nt. Inspireeriv ärivestlus"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "e.g. http://www.example.com"
msgstr "nt http://www.example.com"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "hours"
msgstr "tundi"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "minutes after track starts"
msgstr "minutit enne loo algust"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.registration_complete
msgid "register to your favorites talks now."
msgstr "registreeru oma lemmik vestlustele nüüd!"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_main
msgid "search."
msgstr "otsi."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "starts in"
msgstr "algab"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "starts on"
msgstr "algab"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main
msgid "tracks"
msgstr "jälgimine"
