# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>fur A Banter <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# Lilach <PERSON> <<EMAIL>>, 2021
# da<PERSON>, 2021
# דוד<PERSON> מלכ<PERSON> <Du<PERSON><EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON><PERSON><PERSON>r, 2021
# <PERSON><PERSON>DER <<EMAIL>>, 2022
# <PERSON><PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# NoaFarkash, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# Sagi Ahiel, 2022
# Roy Sayag, 2022
# yael terner, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:18+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: yael terner, 2023\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"\n"
"\n"
"%s --> Product UoM is %s (%s) - Move UoM is %s (%s)"
msgstr ""
"\n"
"\n"
"%s --> יחידת המידה של המוצר היא %s (%s) - תנועת יחידת המידה היא  %s (%s)"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"\n"
"\n"
"Blocking: %s"
msgstr ""
"\n"
"\n"
"חסימה %s"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid ""
"\n"
"\n"
"Transfers %s: You cannot validate these transfers if no quantities are reserved nor done. To force these transfers, switch in edit more and encode the done quantities."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid ""
"\n"
"\n"
"Transfers %s: You need to supply a Lot/Serial number for products %s."
msgstr ""
"\n"
"\n"
"העברות%s: עליך לספק מגש/מספר סידורי עבור מוצרים%s."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"\n"
"(%s) exists in location %s"
msgstr ""
"\n"
"%sקיים במקום (%s)"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__state
msgid ""
" * Draft: The transfer is not confirmed yet. Reservation doesn't apply.\n"
" * Waiting another operation: This transfer is waiting for another operation before being ready.\n"
" * Waiting: The transfer is waiting for the availability of some products.\n"
"(a) The shipping policy is \"As soon as possible\": no product could be reserved.\n"
"(b) The shipping policy is \"When all products are ready\": not all the products could be reserved.\n"
" * Ready: The transfer is ready to be processed.\n"
"(a) The shipping policy is \"As soon as possible\": at least one product has been reserved.\n"
"(b) The shipping policy is \"When all products are ready\": all product have been reserved.\n"
" * Done: The transfer has been processed.\n"
" * Cancelled: The transfer has been cancelled."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_production_lot.py:0
#, python-format
msgid " - Product: %s, Serial Number: %s"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__cyclic_inventory_frequency
msgid ""
" When different than 0, inventory count date for products stored at this "
"location will be automatically set at the defined frequency."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "%(warehouse)s: Supply Product from %(supplier)s"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_package_type.py:0
#: code:addons/stock/models/stock_storage_category.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (העתק)"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid ""
"%s use default source or destination locations from warehouse %s that will "
"be archived."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
msgid "&gt;"
msgstr "&gt;"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_inventory
msgid "'Count Sheet'"
msgstr ""

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_delivery
msgid ""
"'Delivery Slip - %s - %s' % (object.partner_id.name or '', object.name)"
msgstr "'תעודת משלוח - %s - %s' % (object.partner_id.name or '', object.name)"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_location_barcode
msgid "'Location - %s' % object.name"
msgstr "'מיקום- %s' % object.name"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_lot_label
msgid "'Lot-Serial - %s' % object.name"
msgstr "'אצווה-מס' סידורי - %s' % object.name"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_picking_type_label
msgid "'Operation-type - %s' % object.name"
msgstr "'סוג-פעולה - %s' % object.name"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_picking
msgid ""
"'Picking Operations - %s - %s' % (object.partner_id.name or '', object.name)"
msgstr ""
"'פעולות ליקוט - %s - %s' % (object.partner_id.name or '', object.name)"

#. module: stock
#: code:addons/stock/models/stock_production_lot.py:0
#, python-format
msgid "(copy of) %s"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__state
#: model:ir.model.fields,help:stock.field_stock_move_line__state
msgid ""
"* New: When the stock move is created and not yet confirmed.\n"
"* Waiting Another Move: This state can be seen when a move is waiting for another one, for example in a chained flow.\n"
"* Waiting Availability: This state is reached when the procurement resolution is not straight forward. It may need the scheduler to run, a component to be manufactured...\n"
"* Available: When products are reserved, it is set to 'Available'.\n"
"* Done: When the shipment is processed, the state is 'Done'."
msgstr ""
"* חדש: כאשר נוצרת תנועת מלאי והיא עדיין לא מאושרת.\n"
"* ממתין לתנועה נוספת: ניתן לראות מצב זה כאשר תנועה ממתינה, למשל בתהליך קשור.\n"
"* ממתין לזמינות: מצב זה מגיע כאשר החלטת הרכש אינה ברורה מאליה. הוא עשוי להזדקק להרצת המתזמן, רכיב שנדרש לייצר ...\n"
"* זמין: כאשר המוצרים שמורים, הוא מוגדר כ- 'זמין'.\n"
"* בוצע: כאשר המשלוח מעובד, המצב הוא 'בוצע'."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__usage
msgid ""
"* Vendor Location: Virtual location representing the source location for products coming from your vendors\n"
"* View: Virtual location used to create a hierarchical structures for your warehouse, aggregating its child locations ; can't directly contain products\n"
"* Internal Location: Physical locations inside your own warehouses,\n"
"* Customer Location: Virtual location representing the destination location for products sent to your customers\n"
"* Inventory Loss: Virtual location serving as counterpart for inventory operations used to correct stock levels (Physical inventories)\n"
"* Production: Virtual counterpart location for production operations: this location consumes the components and produces finished products\n"
"* Transit Location: Counterpart location that should be used in inter-company or inter-warehouses operations"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid "+ %d day(s)"
msgstr "+%d יום(ימים)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid ", max:"
msgstr ", מקסימום:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "-&gt;"
msgstr "-&gt;"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid ""
".\n"
"            Manual actions may be needed."
msgstr ""
".\n"
"            ייתכן שיהיה צורך בפעולות ידניות."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__day
msgid "1 Day"
msgstr "יום 1"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__month
msgid "1 Month"
msgstr "חודש אחד"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__week
msgid "1 Week"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_scrap.py:0
#, python-format
msgid ": Insufficient Quantity To Scrap"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid ""
"<br/>\n"
"                    <strong>Current Inventory: </strong>"
msgstr ""
"<br/>\n"
"<strong>מלאי עדכני: </strong>"

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid ""
"<br>A need is created in <b>%s</b> and a rule will be triggered to fulfill "
"it."
msgstr "<br>נוצר צורך ב  <b>%s</b> וכלל יופעל כדי למלא אותו."

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid ""
"<br>If the products are not available in <b>%s</b>, a rule will be triggered"
" to bring products in this location."
msgstr "<br>אם המוצרים זמינים ב <b>%s</b>, יופעל כלל שמביא מוצרים למיקום זה."

#. module: stock
#: model:mail.template,body_html:stock.mail_template_data_delivery_confirmation
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hello <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"        We are glad to inform you that your order has been shipped.\n"
"        <t t-if=\"hasattr(object, 'carrier_tracking_ref') and object.carrier_tracking_ref\">\n"
"            Your tracking reference is\n"
"            <strong>\n"
"            <t t-if=\"object.carrier_tracking_url\">\n"
"                <t t-set=\"multiple_carrier_tracking\" t-value=\"object.get_multiple_carrier_tracking()\"/>\n"
"                <t t-if=\"multiple_carrier_tracking\">\n"
"                    <t t-foreach=\"multiple_carrier_tracking\" t-as=\"line\">\n"
"                        <br/><a t-att-href=\"line[1]\" target=\"_blank\" t-out=\"line[0] or ''\"/>\n"
"                    </t>\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    <a t-attf-href=\"{{ object.carrier_tracking_url }}\" target=\"_blank\" t-out=\"object.carrier_tracking_ref or ''\"/>.\n"
"                </t>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                <t t-out=\"object.carrier_tracking_ref or ''\"/>.\n"
"            </t>\n"
"            </strong>\n"
"        </t>\n"
"        <br/><br/>\n"
"        Please find your delivery order attached for more details.<br/><br/>\n"
"        Thank you,\n"
"        <t t-if=\"user.signature\">\n"
"            <br/>\n"
"            <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>\n"
"        "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        שלום <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"        אנחנו שמחים ליידע אותך שההזמנה שלך נשלחה.\n"
"        <t t-if=\"hasattr(object, 'carrier_tracking_ref') and object.carrier_tracking_ref\">\n"
"            מזהה המשלוח שלך הוא\n"
"            <strong>\n"
"            <t t-if=\"object.carrier_tracking_url\">\n"
"                <t t-set=\"multiple_carrier_tracking\" t-value=\"object.get_multiple_carrier_tracking()\"/>\n"
"                <t t-if=\"multiple_carrier_tracking\">\n"
"                    <t t-foreach=\"multiple_carrier_tracking\" t-as=\"line\">\n"
"                        <br/><a t-att-href=\"line[1]\" target=\"_blank\" t-out=\"line[0] or ''\"/>\n"
"                    </t>\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    <a t-attf-href=\"{{ object.carrier_tracking_url }}\" target=\"_blank\" t-out=\"object.carrier_tracking_ref or ''\"/>.\n"
"                </t>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                <t t-out=\"object.carrier_tracking_ref or ''\"/>.\n"
"            </t>\n"
"            </strong>\n"
"        </t>\n"
"        <br/><br/>\n"
"        תעודת המשלוח שלך מצורפת להודעה זאת.<br/><br/>\n"
"        תודה,\n"
"        <t t-if=\"user.signature\">\n"
"            <br/>\n"
"            <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>\n"
"        "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_view_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"
msgstr "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/>\n"
"                                All products could not be reserved. Click on the \"Check Availability\" button to try to reserve products."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/>\n"
"לא ניתן היה לשמור את כל המוצרים. לחץ על כפתור \"בדוק זמינות\" כדי לנסות לשמור מוצרים."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_mrp_line
msgid ""
"<i class=\"fa fa-fw fa-caret-right\" role=\"img\" aria-label=\"Unfold\" "
"title=\"Unfold\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-caret-right\" role=\"img\" aria-label=\"Unfold\" "
"title=\"Unfold\"/>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
msgid ""
"<span attrs=\"{'invisible': ['|', ('state', '=', 'done'), "
"('from_immediate_transfer', '=', True)]}\"> / </span>"
msgstr ""
"<span attrs=\"{'invisible': ['|', ('state', '=', 'done'), "
"('from_immediate_transfer', '=', True)]}\">/</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid ""
"<span class=\"d-none d-sm-block o_print_label_text\">Print Label</span>\n"
"                                        <span class=\"d-block d-sm-none fa fa-print\"/>"
msgstr ""
"<span class=\"d-none d-sm-block o_print_label_text\">הדפס תוית</span>\n"
"<span class=\"d-block d-sm-none fa fa-print\"/>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid ""
"<span class=\"d-none d-sm-block o_print_label_text\">Print Labels</span>\n"
"                                        <span class=\"d-block d-sm-none fa fa-print\"/>"
msgstr ""
"<span class=\"d-none d-sm-block o_print_label_text\">הדפס חוית</span>\n"
"<span class=\"d-block d-sm-none fa fa-print\"/>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Forecasted</span>"
msgstr "<span class=\"o_stat_text\">חזוי</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid ""
"<span class=\"o_stat_text\">In:</span>\n"
"                                <span class=\"o_stat_text\">Out:</span>"
msgstr ""
"<span class=\"o_stat_text\">נכנס</span>\n"
"<span class=\"o_stat_text\">יוצא:</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid ""
"<span class=\"o_stat_text\">Min:</span>\n"
"                                <span class=\"o_stat_text\">Max:</span>"
msgstr ""
"<span class=\"o_stat_text\">מינימום :</span>\n"
"                                <span class=\"o_stat_text\">מקסימום:</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">On Hand</span>"
msgstr "<span class=\"o_stat_text\">במלאי</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "<span class=\"o_stat_text\">Operations</span>"
msgstr "<span class=\"o_stat_text\">פעולות</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "<span class=\"o_stat_text\">Transfers</span>"
msgstr "<span class=\"o_stat_text\">העברות</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_mrp_line
msgid ""
"<span role=\"img\" class=\"o_stock_reports_stream\" title=\"Traceability "
"Report\" aria-label=\"Traceability Report\"><i class=\"fa fa-fw fa-level-up "
"fa-rotate-270\"/></span>"
msgstr ""
"<span role=\"img\" class=\"o_stock_reports_stream\" title=\"Traceability "
"Report\" aria-label=\"Traceability Report\"><i class=\"fa fa-fw fa-level-up "
"fa-rotate-270\"/></span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Customer Address:</strong></span>"
msgstr "<span><strong>כתובת לקוח:</strong></span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Delivery Address:</strong></span>"
msgstr "<span><strong>כתובת לאספקה:</strong></span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Vendor Address:</strong></span>"
msgstr "<span><strong>כתובת ספק:</strong></span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Warehouse Address:</strong></span>"
msgstr "<span><strong>כתובת מחסן:</strong></span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
msgid "<span>Assign Serial Numbers</span>"
msgstr "<span>שייך מספרים סידוריים</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
msgid "<span>Clear All</span>"
msgstr "<span>נקה הכל</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_lot_label
msgid "<span>LN/SN:</span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<span>New</span>"
msgstr "<span>חדש</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "<span>Package Type: </span>"
msgstr "<span>סוג האריזה: </span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_report_delivery_no_package_section_line
msgid "<span>Products with no package assigned</span>"
msgstr "<span>מוצרים ללא אריזה מוקצית</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<span>Remaining quantities not yet delivered:</span>"
msgstr "<span>הכמויות הנותרות טרם נמסרו:</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "<span>kg</span>"
msgstr "<span>ק\"ג</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "<span>×</span>"
msgstr "<span>*</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_head
msgid ""
"<strong>\n"
"                The done move line has been corrected.\n"
"            </strong>"
msgstr ""
"<strong>\n"
"                שורת התנועה שבוצעה תוקנה ...\n"
"            </strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Available Quantity</strong>"
msgstr "כמות זמינה"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Counted Quantity</strong>"
msgstr "<strong>כמות שנספרה</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<strong>Delivered</strong>"
msgstr "<strong>נשלח</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid ""
"<strong>Due to some stock moves done between your initial update of the "
"quantity and now, the difference of quantity is not consistent "
"anymore.</strong>"
msgstr ""
"<strong>עקב כמה תנועות מלאי שנעשו בין העדכון הראשוני של הכמות לעכשיו, ההבדל "
"בכמות אינו עקבי יותר.</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>From</strong>"
msgstr "<strong>מקור</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Location</strong>"
msgstr "<strong>מיקום</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Lot/Serial Number</strong>"
msgstr "<strong>אצווה/מספר סידורי</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_warehouse_orderpoint_kanban
msgid "<strong>Max qty :</strong>"
msgstr "<strong>כמות מקסימלית :</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_warehouse_orderpoint_kanban
msgid "<strong>Min qty :</strong>"
msgstr "<strong>כמות מינימלית :</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>On hand Quantity</strong>"
msgstr "<strong>כמות בהישג יד</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Order:</strong>"
msgstr "<strong>הזמנה:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<strong>Ordered</strong>"
msgstr "<strong>הוזמן</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "<strong>Package Type:</strong>"
msgstr "<strong>סוג אריזה:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Package</strong>"
msgstr "<strong>חבילה</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Product Barcode</strong>"
msgstr "<strong>ברקוד מוצר </strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Product</strong>"
msgstr "<strong>מוצר</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Quantity</strong>"
msgstr "<strong>כמות</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Scheduled Date:</strong>"
msgstr "<strong>תאריך מתוכנן:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<strong>Shipping Date:</strong>"
msgstr "<strong>תאריך משלוח:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<strong>Signature</strong>"
msgstr "<strong>חתימה</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Status:</strong>"
msgstr "<strong>סטטוס:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_head
msgid "<strong>The initial demand has been updated.</strong>"
msgstr "<strong>הביקוש הראשוני עודכן.</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>To</strong>"
msgstr "<strong>יעד</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "<strong>Tracked product(s):</strong>"
msgstr "<strong>מוצר(ים) במעקב:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
msgid "<strong>Where do you want to send the products ?</strong>"
msgstr "<strong>לאיפה אתה רוצה לשלוח את המוצרים ?</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
msgid "? This may lead to inconsistencies in your inventory."
msgstr "? זה עלול להוביל לחוסר עקביות במלאי שלך."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_barcode_uniq
msgid "A barcode can only be assigned to one package type !"
msgstr "ברקוד יכול להיות משויך לסוג חבילה אחד!"

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid "A done move line should never have a reserved quantity."
msgstr "בשורת תנועה שבוצעה לא צריכה להיות כמות שמורה."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__detailed_type
#: model:ir.model.fields,help:stock.field_product_template__detailed_type
#: model:ir.model.fields,help:stock.field_stock_move__product_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""
"מוצר מנוהל מלאי הוא מוצר שעבורו מתבצע ניהול מלאי במערכת. יש להתקין את יישום "
"המלאי.מוצר לא מנוהל מלאי הוא מוצר אשר לא מתבצע עבורו ניהול מלאי במערכת.שירות"
" הוא מוצר לא חומרי שאתה מספק."

#. module: stock
#: model:res.groups,name:stock.group_warning_stock
msgid "A warning can be set on a partner (Stock)"
msgstr "ניתן להגדיר אזהרה ללקוח/ספק (מלאי)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__action
msgid "Action"
msgstr "פעולה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_needaction
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_needaction
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_needaction
msgid "Action Needed"
msgstr "נדרשת פעולה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__active
#: model:ir.model.fields,field_description:stock.field_stock_location_route__active
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__active
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__active
#: model:ir.model.fields,field_description:stock.field_stock_rule__active
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__active
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__active
msgid "Active"
msgstr "פעיל"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_ids
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_ids
msgid "Activities"
msgstr "פעילויות"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_exception_decoration
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "סימון פעילות חריגה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_state
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_state
msgid "Activity State"
msgstr "מצב פעילות"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_type_icon
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_type_icon
msgid "Activity Type Icon"
msgstr "סוג פעילות"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Add a Product"
msgstr "הוסף מוצר"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_production_lot_form
msgid "Add a lot/serial number"
msgstr "הוסף אצווה/מספר סידורי"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid "Add a new location"
msgstr "הוסף מיקום חדש"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_routes_form
msgid "Add a new route"
msgstr "הוסף מסלול חדש "

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_storage_category
msgid "Add a new storage category"
msgstr "הוסף קטגורית אחסון חדשה"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid ""
"Add an internal note that will be printed on the Picking Operations sheet"
msgstr "הוסף הערה פנימית שתודפס על גיליון פעולות הליקוט"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__group_stock_adv_location
msgid ""
"Add and customize route operations to process product moves in your warehouse(s): e.g. unload > quality control > stock for incoming products, pick > pack > ship for outgoing products. \n"
" You can also set putaway strategies on warehouse locations in order to send incoming products into specific child locations straight away (e.g. specific bins, racks)."
msgstr ""
"הוסף והתאם אישית פעולות מסלול כדי לעבד תנועות מוצרים במחסנים שלך, למשל: פרוק> בצע בקרת איכות> מלאי עבור מוצרים נכנסים, לקט> ארוז> משלוח עבור מוצרים יוצאים.\n"
"ניתן גם להגדיר את אסטרטגיות הפיזור באיזורי האחסון כדי לשלוח מוצרים נכנסים לתתי איזורים מסוימים (למשל, ארגזים ספציפיים, מדפים)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Add and customize route operations to process product moves in your "
"warehouse(s): e.g. unload > quality control > stock for incoming products, "
"pick > pack > ship for outgoing products. You can also set putaway "
"strategies on warehouse locations in order to send incoming products into "
"specific child locations straight away (e.g. specific bins, racks)."
msgstr ""
"הוסף והתאם אישית פעולות מסלול כדי לעבד תנועות מוצרים במחסנים שלך, למשל: פרוק> בצע בקרת איכות> מלאי עבור מוצרים נכנסים, לקט> ארוז> משלוח עבור מוצרים יוצאים.\n"
"ניתן גם להגדיר את אסטרטגיות הפיזור במקומי אחסון כדי לשלוח מוצרים נכנסים לתתי איזורים מסוימים (למשל, ארגזים ספציפיים, מדפים)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Add quality checks to your transfer operations"
msgstr "הוסף בדיקות איכות לפעולות ההעברה שלך"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Additional Info"
msgstr "מידע נוסף"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__comment
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Additional Information"
msgstr "מידע נוסף"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__partner_id
msgid "Address"
msgstr "כתובת"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__partner_address_id
msgid "Address where goods should be delivered. Optional."
msgstr "כתובת אליה יש לשלוח את הסחורה. אופציונלי."

#. module: stock
#: model:res.groups,name:stock.group_stock_manager
msgid "Administrator"
msgstr "מנהל מערכת"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Advanced Scheduling"
msgstr "תזמון מתקדם"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__procure_method__make_to_order
msgid "Advanced: Apply Procurement Rules"
msgstr "מתקדם: החל כללי רכש"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "All"
msgstr "הכל"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_picking_action_picking_type
msgid "All Transfers"
msgstr "כל ההעברות"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__procurement_group__move_type__one
msgid "All at once"
msgstr "הכל בבת אחת"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"All our contractual relations will be governed exclusively by United States "
"law."
msgstr "כל היחסים החוזיים שלנו יהיו כפופים באופן בלעדי לחוק ארצות הברית."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__returned_move_ids
msgid "All returned moves"
msgstr "כל תנועות ההחזרה"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Allocation"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__allow_new_product
msgid "Allow New Product"
msgstr "אפשר מוצר חדש"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_storage_category__allow_new_product__mixed
msgid "Allow mixed products"
msgstr "אפשר מוצרים מעורבים"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__allowed_location_ids
msgid "Allowed Location"
msgstr "מיקום מורשה"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Annual Inventory Day and Month"
msgstr "מלאי שנתי חודשי ויומי"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__annual_inventory_month
#: model:ir.model.fields,field_description:stock.field_res_config_settings__annual_inventory_month
msgid "Annual Inventory Month"
msgstr "מלאי שנתי חודשי"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company__annual_inventory_month
#: model:ir.model.fields,help:stock.field_res_config_settings__annual_inventory_month
msgid ""
"Annual inventory month for products not in a location with a cyclic "
"inventory date. Set to no month if no automatic annual inventory."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Applicability"
msgstr "החל על"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Applicable On"
msgstr "החל על"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__packaging_selectable
msgid "Applicable on Packaging"
msgstr "חל על אריזה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__product_selectable
msgid "Applicable on Product"
msgstr "חל על מוצר"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__product_categ_selectable
msgid "Applicable on Product Category"
msgstr "חל על קטגורית מוצר"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__warehouse_selectable
msgid "Applicable on Warehouse"
msgstr "חל על מחסן "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_adjustment_name_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Apply"
msgstr "החל"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__route_ids
msgid ""
"Apply specific route(s) for the replenishment instead of product's default "
"routes."
msgstr ""
"החל מסלול(ים) מסוימים עבור חידוש מלאי במקום מסלולי ברירת המחדל של המוצר."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__4
msgid "April"
msgstr "אפריל"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_warehouse_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Archived"
msgstr "בארכיון"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__move_type__direct
msgid "As soon as possible"
msgstr "בהקדם האפשרי"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/js/report_stock_reception.js:0
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
#, python-format
msgid "Assign"
msgstr "שייך"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/report_stock_reception.xml:0
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
#, python-format
msgid "Assign All"
msgstr "שייך כל "

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__owner_id
msgid "Assign Owner"
msgstr "שייך בעלים"

#. module: stock
#: model:ir.actions.act_window,name:stock.act_assign_serial_numbers
#: model_terms:ir.ui.view,arch_db:stock.view_assign_serial_numbers
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
msgid "Assign Serial Numbers"
msgstr "שייך מספרים סידוריים"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Assigned Moves"
msgstr "תנועות ששויכו"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__user_id
msgid "Assigned To"
msgstr "משויכת ל"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__reservation_method__at_confirm
msgid "At Confirmation"
msgstr "באישור"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_attachment_count
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_attachment_count
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_attachment_count
msgid "Attachment Count"
msgstr "כמות קבצים מצורפים"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_attribute_action
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Attributes"
msgstr "תכונות"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__8
msgid "August"
msgstr "אוגוסט"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse_orderpoint__trigger__auto
msgid "Auto"
msgstr "אוטומטי"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Automate Orders"
msgstr "הזמנות אוטומטיות"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__auto
msgid "Automatic Move"
msgstr "תנועה אוטומטית"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__auto__transparent
msgid "Automatic No Step Added"
msgstr "לא נוסף שלב אוטומטי"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Automatically open reception report when a receipt is validated."
msgstr "פתח אוטומטית את דוח הקבלה כאשר קבלה מאומתת."

#. module: stock
#. openerp-web
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/static/src/xml/forecast_widget.xml:0
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__assigned
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__products_availability_state__available
#, python-format
msgid "Available"
msgstr "זמין"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_search_form_view_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_product_search_form_view
msgid "Available Products"
msgstr "מוצרים זמינים "

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__available_quantity
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Available Quantity"
msgstr "כמות זמינה"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Available quantity should be set to zero before changing type"
msgstr "יש להגדיר את הכמות הזמינה לאפס לפני שינוי סוג"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__backorder_id
msgid "Back Order of"
msgstr "הזמנת חוזרת של"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__backorder_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Back Orders"
msgstr "הזמנות חוזרות"

#. module: stock
#: model:ir.model,name:stock.model_stock_backorder_confirmation
msgid "Backorder Confirmation"
msgstr "אישור הזמנה חוזרת"

#. module: stock
#: model:ir.model,name:stock.model_stock_backorder_confirmation_line
msgid "Backorder Confirmation Line"
msgstr "שורת הזמנה מאושרת (Backorder)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__backorder_confirmation_line_ids
msgid "Backorder Confirmation Lines"
msgstr "שורות הזמנה מאושרת (Backorder)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "Backorder creation"
msgstr "יצירת הזמנה חוזרת"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_backorder
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Backorders"
msgstr "הזמנות חוזרות"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__barcode
#: model:ir.model.fields,field_description:stock.field_stock_package_type__barcode
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__barcode
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Barcode"
msgstr "ברקוד"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_wms_barcode_nomenclature_all
msgid "Barcode Nomenclatures"
msgstr "מונחי ברקוד"

#. module: stock
#: model:ir.model,name:stock.model_barcode_rule
msgid "Barcode Rule"
msgstr "כלל ברקוד"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_barcode
msgid "Barcode Scanner"
msgstr "סורק ברקוד"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_picking_batch
msgid "Batch Transfers"
msgstr "העברות אצווה"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__reservation_method__by_date
msgid "Before scheduled date"
msgstr "לפני התאריך המתוכנן"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"Below text serves as a suggestion and doesn’t engage Odoo S.A. "
"responsibility."
msgstr "הטקסט למטה משמש כהצעה ואינו כרוך באחריות Odoo S.A."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_partner__picking_warn__block
msgid "Blocking Message"
msgstr "הודעת חסימה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__quant_ids
msgid "Bulk Content"
msgstr "תוכן בתפזורת "

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_template__tracking__lot
msgid "By Lots"
msgstr "לפי אצוות "

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_template__tracking__serial
msgid "By Unique Serial Number"
msgstr "לפי מספר סידורי ייחודי"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__procure_method
msgid ""
"By default, the system will take from the stock in the source location and "
"passively wait for availability. The other possibility allows you to "
"directly create a procurement on the source location (and thus ignore its "
"current stock) to gather products. If we want to chain moves and have this "
"one to wait for the previous, this second option should be chosen."
msgstr ""
"כברירת מחדל, המערכת תיקח מלאי ממיקום המקור ותמתין באופן פסיבי לזמינות. "
"האפשרות השנייה מאפשרת לרכוש ישירות ממיקום המקור (ובכך מתעלמת מהמלאי הנוכחי) "
"בכדי לאסוף מוצרים. אם אנחנו רוצים לשרשר תנועות וגם לחכות לתנועה הקודמת, יש "
"לבחור את האפשרות השנייה הזו."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__active
msgid ""
"By unchecking the active field, you may hide a location without deleting it."
msgstr "על ידי ביטול הסימון בשדה פעיל, תוכל להסתיר מיקום מבלי למחוק אותו."

#. module: stock
#: model:product.product,name:stock.product_cable_management_box
#: model:product.template,name:stock.product_cable_management_box_product_template
msgid "Cable Management Box"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_calendar
msgid "Calendar View"
msgstr "תצוגת לוח שנה"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Can't find any customer or supplier location."
msgstr "לא ניתן למצוא מיקום לקוח או ספק."

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Can't find any generic route %s."
msgstr "לא ניתן למצוא מסלול כללי %s."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view2
#: model_terms:ir.ui.view,arch_db:stock.view_assign_serial_numbers
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_compute_wizard
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quantity_history
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rules_report
msgid "Cancel"
msgstr "בטל"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__propagate_cancel
msgid "Cancel Next Move"
msgstr "בטל תנועה הבאה"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__cancel
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__cancel
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__cancel
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Cancelled"
msgstr "בוטל"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Cancelled Moves"
msgstr "תנועות שבוטלו "

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"Cannot set the done quantity from this stock move, work directly with the "
"move lines."
msgstr ""
"לא ניתן להגדיר את הכמות שבוצעה מתנועת מלאי זו, עבוד ישירות עם שורות התנועה."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__capacity_ids
msgid "Capacity"
msgstr "קיבולת"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "Capacity by Package"
msgstr "קיבולת לפי חבילה"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "Capacity by Product"
msgstr "קיבולת לפי מוצר"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Categorize your locations for smarter putaway rules"
msgstr "סיווג את המיקומים שלך עבור חוקים חכמים יותר למיון"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_uom_category_id
#: model:ir.model.fields,field_description:stock.field_stock_move__product_uom_category_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_uom_category_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__product_uom_category_id
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Category"
msgstr "קטגוריה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__route_from_categ_ids
#: model:ir.model.fields,field_description:stock.field_product_template__route_from_categ_ids
msgid "Category Routes"
msgstr "מסלולי קטגוריה"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"Certain countries apply withholding at source on the amount of invoices, in "
"accordance with their internal legislation. Any withholding at source will "
"be paid by the client to the tax authorities. Under no circumstances can My "
"Company (Chicago) become involved in costs related to a country's "
"legislation. The amount of the invoice will therefore be due to My Company "
"(Chicago) in its entirety and does not include any costs relating to the "
"legislation of the country in which the client is located."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__move_dest_exists
msgid "Chained Move Exists"
msgstr "תנועה קשורה קיימת"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_change_product_quantity
#: model:ir.model,name:stock.model_stock_change_product_qty
msgid "Change Product Quantity"
msgstr "שנה את כמות המוצר "

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__move_id
msgid "Change to a better name"
msgstr "שנה לשם טוב יותר "

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Changing %s is restricted, you can't do this operation."
msgstr ""

#. module: stock
#: code:addons/stock/models/product_strategy.py:0
#: code:addons/stock/models/stock_location.py:0
#: code:addons/stock/models/stock_orderpoint.py:0
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/models/stock_production_lot.py:0
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid ""
"Changing the company of this record is forbidden at this point, you should "
"rather archive it and create a new one."
msgstr ""
"החלפת חברת רשומה הזו אסורה בשלב זה, עליכם להעביר אותה לארכיון וליצור רשומה "
"חדשה."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Changing the operation type of this record is forbidden at this point."
msgstr "שינוי סוג הפעולה של רשומה זו אסור בשלב זה."

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid "Changing the product is only allowed in 'Draft' state."
msgstr "שינוי המוצר מותר רק במצב 'טיוטה'."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Check Availability"
msgstr "בדוק זמינות "

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__has_packages
msgid "Check the existence of destination packages on move lines"
msgstr "בדוק את קיומן של חבילות יעד בשורות תנועה"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__move_line_exist
msgid "Check the existence of pack operation on the picking"
msgstr "בדוק את קיום פעולת האריזה בליקוט"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__return_location
msgid "Check this box to allow using this location as a return location."
msgstr "סמן תיבה זו כדי לאפשר שימוש במיקום זה כמיקום החזרה."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__scrap_location
#: model:ir.model.fields,help:stock.field_stock_move__scrapped
msgid ""
"Check this box to allow using this location to put scrapped/damaged goods."
msgstr "סמן תיבה זו כדי לאפשר שימוש במיקום זה לשים בו פסולים / פגומים."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Choose Labels Layout"
msgstr "בחר תבנית תוויות"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quantity_history__inventory_datetime
#: model:ir.model.fields,help:stock.field_stock_request_count__inventory_date
msgid "Choose a date to get the inventory at that date"
msgstr "בחר תאריך כדי לקבל את המלאי בתאריך זה"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Choose destination location"
msgstr "בחר מיקום יעד"

#. module: stock
#: model:ir.model,name:stock.model_product_label_layout
msgid "Choose the sheet layout to print the labels"
msgstr "בחר את פריסת הגיליון כדי להדפיס את התוויות"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quantity_history
msgid "Choose your date"
msgstr "בחר את התאריך שלך"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Clear"
msgstr "נקה"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
msgid "Clear Lines"
msgstr ""

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_traceability_report_backend.xml:0
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
#: model_terms:ir.ui.view,arch_db:stock.view_stock_replenishment_info
#, python-format
msgid "Close"
msgstr "סגור"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__sequence_code
msgid "Code"
msgstr "קוד"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__color
msgid "Color"
msgstr "צבע"

#. module: stock
#: model:ir.model,name:stock.model_res_company
msgid "Companies"
msgstr "חברות"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__company_id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__company_id
#: model:ir.model.fields,field_description:stock.field_stock_location__company_id
#: model:ir.model.fields,field_description:stock.field_stock_location_route__company_id
#: model:ir.model.fields,field_description:stock.field_stock_move__company_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__company_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__company_id
#: model:ir.model.fields,field_description:stock.field_stock_package_type__company_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__company_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__company_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__company_id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__company_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__company_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__company_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__company_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__company_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__company_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__company_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__company_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__company_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__company_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Company"
msgstr "חברה"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs"
msgstr "חשב עלויות משלוח"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr "חשב עלויות המשלוח ושלח באמצעות  DHL"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with Easypost"
msgstr "חשב עלויות המשלוח ושלח באמצעות Easypost"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr "חשב עלויות המשלוח ושלח באמצעות FedEx"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr "חשב עלויות המשלוח ושלח באמצעות UPS"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr "חשב עלויות המשלוח ושלח באמצעות USPS"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr "חשב עלויות המשלוח ושלח באמצעות bpost"

#. module: stock
#: model:ir.model,name:stock.model_res_config_settings
msgid "Config Settings"
msgstr "הגדר הגדרות"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_config_settings
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Configuration"
msgstr "תצורה"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.package_level_form_edit_view
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_request_count_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quantity_history
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "Confirm"
msgstr "אשר"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__confirmed
msgid "Confirmed"
msgstr "מאושר"

#. module: stock
#: model:ir.model,name:stock.model_stock_inventory_conflict
msgid "Conflict in Inventory"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Conflict in Inventory Adjustment"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__quant_to_fix_ids
msgid "Conflicts"
msgstr "מתנגש"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_tracking_owner
msgid "Consignment"
msgstr "מכר מותנה (קונסיגנציה)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__consume_line_ids
msgid "Consume Line"
msgstr "שורת צריכה"

#. module: stock
#: model:ir.model,name:stock.model_res_partner
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_partner_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__partner_id
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Contact"
msgstr "איש קשר"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__child_ids
msgid "Contains"
msgstr "מכיל"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Content"
msgstr "תוכן"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_reset_view
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_set_view
msgid "Continue"
msgstr "המשך"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__product_uom_category_id
#: model:ir.model.fields,help:stock.field_stock_move__product_uom_category_id
#: model:ir.model.fields,help:stock.field_stock_move_line__product_uom_category_id
#: model:ir.model.fields,help:stock.field_stock_scrap__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"המרה בין יחידות מידה יכולה להתרחש רק אם הן שייכות לאותה קטגוריה. ההמרה תיעשה"
" על בסיס היחס בניהן."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__posx
msgid "Corridor (X)"
msgstr "רחוב (X)"

#. module: stock
#: code:addons/stock/wizard/stock_immediate_transfer.py:0
#, python-format
msgid ""
"Could not reserve all requested products. Please use the 'Mark as Todo' "
"button to handle the reservation manually."
msgstr ""
"לא ניתן היה לשמור את כל המוצרים המבוקשים. השתמש בכפתור 'סמן לביצוע' כדי לטפל"
" בשמירה ידנית."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_request_count__set_count
msgid "Count"
msgstr "כמות"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking
msgid "Count Picking"
msgstr "כמות ליקוטים"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_backorders
msgid "Count Picking Backorders"
msgstr "כמות ליקוטים של הזמנות חוזרות"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_draft
msgid "Count Picking Draft"
msgstr "כמות טיוטות ליקוט"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_late
msgid "Count Picking Late"
msgstr "כמות ליקוטים באיחור"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_ready
msgid "Count Picking Ready"
msgstr "כמות ליקוטים מוכנים"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_waiting
msgid "Count Picking Waiting"
msgstr "כמות ליקוטים ממתינים"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_inventory
msgid "Count Sheet"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_quantity
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Counted Quantity"
msgstr "כמות שנספרה"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Counterpart Locations"
msgstr "מיקומים מקבילים"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "Create Backorder"
msgstr "צור הזמנה חוזרת"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Create Backorder?"
msgstr "ליצור הזמנה חוזרת?"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_use_create_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking__use_create_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__use_create_lots
msgid "Create New Lots/Serial Numbers"
msgstr "צור אצוות חדשות/מספרים סידוריים חדשים"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid ""
"Create a backorder if you expect to process the remaining\n"
"                        products later. Do not create a backorder if you will not\n"
"                        process the remaining products."
msgstr ""
"צור הזמנה חוזרת אם אתה מצפה לעבד את המוצרים הנותרים\n"
"מאוחר יותר. אל תיצור הזמנה חוזרת אם לא תעבד\n"
"את המוצרים הנותרים."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_picking_type_action
msgid "Create a new operation type"
msgstr "צור סוג פעולה חדש"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_package_view
msgid "Create a new package"
msgstr "צור חבילה חדשה"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Create customizable worksheets for your quality checks"
msgstr "צור דפי עבודה הניתנים להתאמה אישית עבור בדיקות האיכות שלך"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_putaway_tree
msgid ""
"Create new putaway rules to dispatch automatically specific products to "
"their appropriate destination location upon receptions."
msgstr ""
"צור כללים חדשים לביטול שיגור אוטומטי של מוצרים ספציפיים למיקום היעד המתאים "
"להם עם קבלתם."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__create_uid
#: model:ir.model.fields,field_description:stock.field_product_removal__create_uid
#: model:ir.model.fields,field_description:stock.field_product_replenish__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_location__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_location_route__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_move__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_level__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_type__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_request_count__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_rule__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_scrap__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__create_date
#: model:ir.model.fields,field_description:stock.field_product_removal__create_date
#: model:ir.model.fields,field_description:stock.field_product_replenish__create_date
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__create_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__create_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__create_date
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__create_date
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__create_date
#: model:ir.model.fields,field_description:stock.field_stock_location__create_date
#: model:ir.model.fields,field_description:stock.field_stock_location_route__create_date
#: model:ir.model.fields,field_description:stock.field_stock_move__create_date
#: model:ir.model.fields,field_description:stock.field_stock_move_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__create_date
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__create_date
#: model:ir.model.fields,field_description:stock.field_stock_package_level__create_date
#: model:ir.model.fields,field_description:stock.field_stock_package_type__create_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__create_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__create_date
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__create_date
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quant__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__create_date
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__create_date
#: model:ir.model.fields,field_description:stock.field_stock_request_count__create_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__create_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_rule__create_date
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__create_date
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute__create_date
#: model:ir.model.fields,field_description:stock.field_stock_scrap__create_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__create_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__create_date
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__create_date
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__create_date
#: model:ir.model.fields,field_description:stock.field_stock_track_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__create_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__create_date
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid ""
"Creating a new warehouse will automatically activate the Storage Locations "
"setting"
msgstr "יצירת מחסן חדש תפעיל אוטומטית את הגדרת מיקומי האחסו"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__date
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Creation Date"
msgstr "תאריך יצירה"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__date
msgid "Creation Date, usually the time of the order"
msgstr "תאריך יצירה, בדרך כלל זמן יצירת ההזמנה"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Cross-Dock"
msgstr "Cross-Dock"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__crossdock_route_id
msgid "Crossdock Route"
msgstr "מסלול Crossdock"

#. module: stock
#: model:ir.actions.act_window,name:stock.location_open_quants
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Current Stock"
msgstr "מלאי נוכחי"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__qty_available
msgid ""
"Current quantity of products.\n"
"In a context with a single Stock Location, this includes goods stored at this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"stored in the Stock Location of the Warehouse of this Shop, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"כמות מוצרים נוכחית.\n"
"בהקשר עם מיקום מלאי יחיד, זה כולל סחורות המאוחסנות במיקום זה, או כל אחד מהילדים שלו.\n"
"בהקשר של מחסן יחיד, זה כולל סחורות המאוחסנות במיקום המלאי של מחסן זה, או כל אחד מהילדים שלו.\n"
"מאוחסן במיקום המלאי של המחסן של חנות זו, או כל אחד מילדיה.\n"
"אחרת, זה כולל מוצרים המאוחסנים בכל מיקום מלאי עם סוג 'פנימי'."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__picking_quantity__custom
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__custom
msgid "Custom"
msgstr "מותאם"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Customer"
msgstr "לקוח"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__sale_delay
#: model:ir.model.fields,field_description:stock.field_product_template__sale_delay
msgid "Customer Lead Time"
msgstr "זמן אספקה ממוצע"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__property_stock_customer
#: model:ir.model.fields,field_description:stock.field_res_users__property_stock_customer
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__customer
msgid "Customer Location"
msgstr "מיקום לקוח"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Customer Locations"
msgstr "מיקומי לקוח"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Cyclic Inventory"
msgstr "מלאי מחזורי"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_dhl
msgid "DHL Express Connector"
msgstr "DHL Express-חיבור ל"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__date
#: model:ir.model.fields,field_description:stock.field_stock_move_line__date
#: model:ir.model.fields,field_description:stock.field_stock_scrap__date_done
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Date"
msgstr "תאריך"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Date Processing"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__date_deadline
#: model:ir.model.fields,help:stock.field_stock_picking__date_deadline
msgid "Date Promise to the customer on the top level document (SO/PO)"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__date
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Date Scheduled"
msgstr "תאריך מתוכנן"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__date_planned
msgid "Date at which the replenishment should take place."
msgstr "תאריך שבו יש לחדש את המלאי."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__date_done
msgid "Date at which the transfer has been processed or cancelled."
msgstr "תאריך שבו התרחשה או בוטלה ההעברה."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__next_inventory_date
msgid "Date for next planned inventory based on cyclic schedule."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__date_done
msgid "Date of Transfer"
msgstr "תאריך ההעברה "

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__last_inventory_date
msgid "Date of the last inventory at this location."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__reservation_date
msgid "Date to Reserve"
msgstr "תאריך להזמנה"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Day and month that annual inventory counts should occur."
msgstr "יום וחודש שבהם אמורות להתרחש ספירת מלאי שנתית"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__annual_inventory_day
#: model:ir.model.fields,field_description:stock.field_res_config_settings__annual_inventory_day
msgid "Day of the month"
msgstr "יום בחודש"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company__annual_inventory_day
#: model:ir.model.fields,help:stock.field_res_config_settings__annual_inventory_day
msgid ""
"Day of the month when the annual inventory should occur. If zero or negative, then the first day of the month will be selected instead.\n"
"        If greater than the last day of a month, then the last day of the month will be selected instead."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__reservation_days_before
msgid "Days"
msgstr "ימים"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__reservation_days_before_priority
msgid "Days when starred"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__date_deadline
#: model:ir.model.fields,field_description:stock.field_stock_picking__date_deadline
msgid "Deadline"
msgstr "תאריך יעד"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Deadline exceed or/and by the scheduled"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid "Deadline updated due to delay on %s"
msgstr ""

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__12
msgid "December"
msgstr "דצמבר"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__default_location_dest_id
msgid "Default Destination Location"
msgstr "מיקום יעד ברירת מחדל"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__default_location_src_id
msgid "Default Source Location"
msgstr "ברירת מחדל של מיקום מוצא "

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__reception_steps
msgid "Default incoming route to follow"
msgstr "מסלול כניסה ברירת מחדל למעקב"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__delivery_steps
msgid "Default outgoing route to follow"
msgstr "מסלול יוצא ברירת המחדל למעקב"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_production_lot__product_uom_id
#: model:ir.model.fields,help:stock.field_stock_quant__product_uom_id
#: model:ir.model.fields,help:stock.field_stock_return_picking_line__uom_id
#: model:ir.model.fields,help:stock.field_stock_storage_category_capacity__product_uom_id
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__product_uom
msgid "Default unit of measure used for all stock operations."
msgstr "יחידת מידה ברירת מחדל המשמשת לביצוע כל פעולות המלאי."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__procure_method__make_to_stock
msgid "Default: Take From Stock"
msgstr "ברירת מחדל: קח מהמלאי"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__route_ids
msgid "Defaults routes through the warehouse"
msgstr "מסלולי ברירת מחדל של המחסן"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint
msgid ""
"Define a minimum stock rule so that Odoo creates automatically requests for "
"quotations or confirmed manufacturing orders to resupply your stock."
msgstr ""
"הגדר כלל מלאי מינימום כך ש-Odoo תיצור אוטומטית בקשות להצעות מחיר או הזמנות "
"ייצור מאושרות כדי לספק מחדש את המלאי שלך."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_warehouse_form
msgid "Define a new warehouse"
msgstr "הגדר מחסן חדש"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid ""
"Define your locations to reflect your warehouse structure and\n"
"            organization. Odoo is able to manage physical locations\n"
"            (warehouses, shelves, bin, etc), partner locations (customers,\n"
"            vendors) and virtual locations which are the counterpart of\n"
"            the stock operations like the manufacturing orders\n"
"            consumptions, inventories, etc."
msgstr ""
"הגדר את המיקומים שלך כך שישקפו את מבנה המחסן \n"
"והארגון. Odoo מסוגל לנהל מיקומים פיזיים\n"
"(מחסנים, מדפים וכו), מיקומי לקוחות/ספקים\n"
"וגם מיקומים וירטואליים שמקבילים לפעולות המלאי \n"
"כמו הוראות ייצור\n"
",צריכה, מלאים וכו'."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__removal_strategy_id
msgid ""
"Defines the default method used for suggesting the exact location (shelf) where to take the products from, which lot etc. for this location. This method can be enforced at the product category level, and a fallback is made on the parent locations if none is set here.\n"
"\n"
"FIFO: products/lots that were stocked first will be moved out first.\n"
"LIFO: products/lots that were stocked last will be moved out first.\n"
"Closet location: products/lots closest to the target location will be moved out first.\n"
"FEFO: products/lots with the closest removal date will be moved out first (the availability of this method depends on the \"Expiration Dates\" setting)."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__delay_alert_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__delay_alert_date
msgid "Delay Alert Date"
msgstr "עיכוב תאריך התראה"

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid "Delay on %s"
msgstr ""

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__delivery_steps__ship_only
msgid "Deliver goods directly (1 step)"
msgstr "שלח סחורה ישירות ללקוח (צעד 1)"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Deliver in 1 step (ship)"
msgstr "ספק בצעד 1 (שלח)"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Deliver in 2 steps (pick + ship)"
msgstr "ספק ב 2 צעדים (לקט+שלח)"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Deliver in 3 steps (pick + pack + ship)"
msgstr "ספק ב 3 צעדים (לקט+ארוז+שלח)"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Delivered Qty"
msgstr "כמות שנשלחה"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__code__outgoing
#: model:ir.ui.menu,name:stock.menu_delivery
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Delivery"
msgstr "משלוח"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Delivery Address"
msgstr "כתובת לאספקה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery
msgid "Delivery Methods"
msgstr "שיטות משלוח"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#: model:stock.picking.type,name:stock.chi_picking_type_out
#: model:stock.picking.type,name:stock.picking_type_out
#, python-format
msgid "Delivery Orders"
msgstr "הזמנות משלוח"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__delivery_route_id
msgid "Delivery Route"
msgstr "מסלול משלוח"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_delivery
msgid "Delivery Slip"
msgstr "תעודת משלוח"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__move_type
msgid "Delivery Type"
msgstr "סוג משלוח"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__sale_delay
#: model:ir.model.fields,help:stock.field_product_template__sale_delay
msgid ""
"Delivery lead time, in days. It's the number of days, promised to the "
"customer, between the confirmation of the sales order and the delivery."
msgstr ""
"זמן אספקה, בימים. זה מספר הימים, המובטחים ללקוח, מרגע אישור ההזמנה ועד "
"למשלוח."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__delivery_count
msgid "Delivery order count"
msgstr "מספר הזמנות משלוח"

#. module: stock
#: code:addons/stock/models/stock_production_lot.py:0
#, python-format
msgid "Delivery orders of %s"
msgstr ""

#. module: stock
#: model:mail.template,name:stock.mail_template_data_delivery_confirmation
msgid "Delivery: Send by Email"
msgstr "משלוח: שליחה באימייל"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_uom_qty
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
msgid "Demand"
msgstr "דרישה"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_packaging__route_ids
msgid ""
"Depending on the modules installed, this will allow you to define the route "
"of the product in this packaging: whether it will be bought, manufactured, "
"replenished on order, etc."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__route_ids
#: model:ir.model.fields,help:stock.field_product_template__route_ids
msgid ""
"Depending on the modules installed, this will allow you to define the route "
"of the product: whether it will be bought, manufactured, replenished on "
"order, etc."
msgstr ""
"בהתאם למודולים המותקנים, הדבר יאפשר לכם להגדיר את מסלול המוצר: האם הוא "
"יירכש, ייוצר, יתחדש בהזמנה וכו '."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__name
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__note
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "Description"
msgstr "תיאור"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Delivery Orders"
msgstr "תיאור להזמנות משלוח"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Internal Transfers"
msgstr "תיאור להעברות פנימיות"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Receipts"
msgstr "תיאור לקבלות"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__description_picking
msgid "Description of Picking"
msgstr "תיאור הליקוט"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__description_pickingout
#: model:ir.model.fields,field_description:stock.field_product_template__description_pickingout
msgid "Description on Delivery Orders"
msgstr "תיאור בהזמנות משלוח"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__description_picking
#: model:ir.model.fields,field_description:stock.field_product_template__description_picking
msgid "Description on Picking"
msgstr "תיאור בליקוט"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__description_pickingin
#: model:ir.model.fields,field_description:stock.field_product_template__description_pickingin
msgid "Description on Receptions"
msgstr "תיאור בקבלות"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__description_picking
msgid "Description picking"
msgstr "תיאור הליקוט"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__partner_id
msgid "Destination Address "
msgstr "כתובת היעד "

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#: model:ir.model.fields,field_description:stock.field_stock_move__location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__location_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#, python-format
msgid "Destination Location"
msgstr "מיקום יעד"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Destination Location:"
msgstr "מיקום יעד:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_dest_ids
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Destination Moves"
msgstr "תנועות יעד"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__result_package_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
msgid "Destination Package"
msgstr "יעד חבילה"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Destination Package :"
msgstr "יעד חבילה :"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__location_dest_id
msgid "Destination location"
msgstr "מיקום יעד"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__route_ids
msgid "Destination route"
msgstr "מסלול יעד"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#, python-format
msgid "Detailed Operations"
msgstr "פעולות מפורטות"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_details_visible
msgid "Details Visible"
msgstr "פרטים גלויים"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_diff_quantity
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Difference"
msgstr "הפרש"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_reset_view
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_set_view
#: model_terms:ir.ui.view,arch_db:stock.package_level_form_edit_view
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_adjustment_name_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_request_count_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
#: model_terms:ir.ui.view,arch_db:stock.view_stock_orderpoint_snooze
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "Discard"
msgstr "בטל"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Discard and manually resolve the conflict"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__display_assign_serial
msgid "Display Assign Serial"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__display_complete
msgid "Display Complete"
msgstr "תצוגה הושלמה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_lot_on_delivery_slip
msgid "Display Lots & Serial Numbers on Delivery Slips"
msgstr "הצג אצוות ומספרים סידוריים בתעודות משלוח"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__display_name
#: model:ir.model.fields,field_description:stock.field_product_removal__display_name
#: model:ir.model.fields,field_description:stock.field_product_replenish__display_name
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__display_name
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__display_name
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__display_name
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__display_name
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__display_name
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__display_name
#: model:ir.model.fields,field_description:stock.field_stock_location__display_name
#: model:ir.model.fields,field_description:stock.field_stock_location_route__display_name
#: model:ir.model.fields,field_description:stock.field_stock_move__display_name
#: model:ir.model.fields,field_description:stock.field_stock_move_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__display_name
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__display_name
#: model:ir.model.fields,field_description:stock.field_stock_package_level__display_name
#: model:ir.model.fields,field_description:stock.field_stock_package_type__display_name
#: model:ir.model.fields,field_description:stock.field_stock_picking__display_name
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__display_name
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__display_name
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quant__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__display_name
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__display_name
#: model:ir.model.fields,field_description:stock.field_stock_request_count__display_name
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__display_name
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_rule__display_name
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__display_name
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute__display_name
#: model:ir.model.fields,field_description:stock.field_stock_scrap__display_name
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__display_name
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__display_name
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__display_name
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__display_name
#: model:ir.model.fields,field_description:stock.field_stock_track_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__display_name
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: stock
#: model:res.groups,name:stock.group_auto_reception_report
msgid "Display Reception Report at Validation"
msgstr ""

#. module: stock
#: model:res.groups,name:stock.group_lot_on_delivery_slip
msgid "Display Serial & Lot Number in Delivery Slips"
msgstr "הצג מספר אצווה ומספר סידורי בתעודות המשלוח"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.package_level_tree_view_picking
msgid "Display package content"
msgstr "הצג תוכן חבילה"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_quant_package__package_use__disposable
msgid "Disposable Box"
msgstr "קופסה חד פעמית"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
msgid "Do you confirm you want to scrap"
msgstr "אתה מאשר שאתה רוצה לפסול"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Documentation"
msgstr "תיעוד"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__qty_done
#: model:ir.model.fields,field_description:stock.field_stock_package_level__is_done
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__done
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__done
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__done
#: model:ir.model.fields.selection,name:stock.selection__stock_scrap__state__done
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view2
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
msgid "Done"
msgstr "בוצע"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__draft
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__draft
#: model:ir.model.fields.selection,name:stock.selection__stock_scrap__state__draft
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Draft"
msgstr "טיוטה"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Draft Moves"
msgstr "תנועות טיוטה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_easypost
msgid "Easypost Connector"
msgstr "חיבור ל Easypost"

#. module: stock
#: code:addons/stock/models/stock_orderpoint.py:0
#, python-format
msgid "Edit Product"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"Editing quantities in an Inventory Adjustment location is forbidden,those "
"locations are used as counterpart when correcting the quantities."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Effective Date"
msgstr "תאריך אפקטיבי"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Email Confirmation"
msgstr "אישור דוא\"ל"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__stock_move_email_validation
#: model:ir.model.fields,field_description:stock.field_res_config_settings__stock_move_email_validation
msgid "Email Confirmation picking"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Email Template"
msgstr "תבנית דוא\"ל"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__stock_mail_confirmation_template_id
#: model:ir.model.fields,field_description:stock.field_res_config_settings__stock_mail_confirmation_template_id
msgid "Email Template confirmation picking"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company__stock_mail_confirmation_template_id
#: model:ir.model.fields,help:stock.field_res_config_settings__stock_mail_confirmation_template_id
msgid "Email sent to the customer once the order is done."
msgstr "דוא\"ל שנשלח ללקוח לאחר ביצוע ההזמנה."

#. module: stock
#: model_terms:digest.tip,tip_description:stock.digest_tip_stock_0
msgid ""
"Enjoy a quick-paced experience with the Odoo barcode app. It is blazing fast"
" and works even without a stable internet connection. It supports all flows:"
" inventory adjustments, batch picking, moving lots or pallets, low inventory"
" checks, etc. Go to the \"Apps\" menu to activate the barcode interface."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__tracking
#: model:ir.model.fields,help:stock.field_product_template__tracking
#: model:ir.model.fields,help:stock.field_stock_move__has_tracking
#: model:ir.model.fields,help:stock.field_stock_move_line__tracking
#: model:ir.model.fields,help:stock.field_stock_quant__tracking
#: model:ir.model.fields,help:stock.field_stock_scrap__tracking
#: model:ir.model.fields,help:stock.field_stock_track_line__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "וודא מעקב אחר מוצר מנוהל מלאי במחסן שלך."

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_traceability_report_backend.xml:0
#, python-format
msgid "Error"
msgstr "שגיאה"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid ""
"Every stock operation in Odoo moves the products from one\n"
"            location to another one.  For instance, if you receive products\n"
"            from a vendor, Odoo will move products from the Vendor\n"
"            location to the Stock location. Each report can be performed on\n"
"            physical, partner or virtual locations."
msgstr ""
"כל פעולה המתבצעת במלאי Odoo תעביר את המוצרים ממיקום אחד\n"
"לאחר. לדוגמה, אם אתה מקבל מוצרים\n"
"מהספק, Odoo תעביר מוצרים מהספק\n"
"למיקום במלאי. ניתן לבצע  דוח\n"
"פיזית, שותף או מיקומים וירטואליים."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "Exception(s) occurred on the picking"
msgstr "חריגות שהתרחשו בזמן הליקוט"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "Exception(s):"
msgstr "חריגות:"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"Existing Serial Numbers (%s). Please correct the serial numbers encoded."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid "Existing Serial numbers. Please correct the serial numbers encoded:"
msgstr ""

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/forecast_widget.xml:0
#, python-format
msgid "Exp"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Exp %s"
msgstr ""

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__products_availability_state__expected
msgid "Expected"
msgstr "צפוי"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "Expected Delivery:"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_product_expiry
msgid "Expiration Dates"
msgstr "תאריכי תפוגה"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "External note..."
msgstr "הערה חיצונית..."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_removal__method
msgid "FIFO, LIFO..."
msgstr "FIFO, LIFO..."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__2
msgid "February"
msgstr "פברואר"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_fedex
msgid "FedEx Connector"
msgstr "חיבור ל FedEx"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__filtered_location
msgid "Filtered Location"
msgstr "מיקום מסונן"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Filters"
msgstr "מסננים"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__next_serial_number
#: model:ir.model.fields,field_description:stock.field_stock_move__next_serial
msgid "First SN"
msgstr "מספר סידורי ראשון"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__group_propagation_option__fixed
msgid "Fixed"
msgstr "קבוע"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__group_id
msgid "Fixed Procurement Group"
msgstr "קבוצת רכש קבועה"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_traceability_report_line.xml:0
#: code:addons/stock/static/src/xml/stock_traceability_report_line.xml:0
#, python-format
msgid "Fold"
msgstr "קפל"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_follower_ids
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_follower_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_follower_ids
msgid "Followers"
msgstr "עוקבים"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_partner_ids
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_partner_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_partner_ids
msgid "Followers (Partners)"
msgstr "עוקבים (לקוחות/ספקים)"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__activity_type_icon
#: model:ir.model.fields,help:stock.field_stock_production_lot__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "פונט מדהים למשל עבור משימות fa-tasks"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__removal_strategy_id
msgid "Force Removal Strategy"
msgstr "אלץ אסטרטגיית ניפוק"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_forecast
msgid "Forecast"
msgstr "תחזית"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__forecast_availability
msgid "Forecast Availability"
msgstr "זמינות תחזית"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable_config
msgid "Forecast Description"
msgstr "תיאור תחזית"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__virtual_available
msgid ""
"Forecast quantity (computed as Quantity On Hand - Outgoing + Incoming)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"כמות חיזוי (מחושב לפי : כמות זמינה  - כמות יוצאת + כמות נכנסת)\n"
"בהקשר זה  עם מיקום מלאי יחידי, זה כולל סחורות המאוחסנות במיקום זה, או כל אחד מתתי המיקומים עבור מלאי זה.\n"
"בהקשר עם מחסן יחיד, זה כולל סחורות המאוחסנים באתר המלאי של מחסן זה, או כל אחד מתתי המיקומים של המחסן.\n"
"אחרת, זה כולל סחורות המאוחסנים בכל מקום במלאי עם סוג \"פנימי\"."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__free_qty
msgid ""
"Forecast quantity (computed as Quantity On Hand - reserved quantity)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_replenishment_header
msgid "Forecasted"
msgstr "חזוי"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "Forecasted Date"
msgstr "צפי תאריך"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__report_stock_quantity__state__out
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_search
msgid "Forecasted Deliveries"
msgstr "משלוחים חזויים"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__forecast_expected_date
msgid "Forecasted Expected date"
msgstr "צפי תאריך קבלה"

#. module: stock
#: model:ir.actions.act_window,name:stock.report_stock_quantity_action
#: model:ir.actions.act_window,name:stock.report_stock_quantity_action_product
#: model:ir.ui.menu,name:stock.menu_forecast_inventory
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Forecasted Inventory"
msgstr "מלאי חזוי"

#. module: stock
#: code:addons/stock/models/product.py:0
#: model:ir.model.fields,field_description:stock.field_product_product__virtual_available
#: model:ir.model.fields,field_description:stock.field_product_template__virtual_available
#: model:ir.model.fields,field_description:stock.field_stock_move__availability
#: model_terms:ir.ui.view,arch_db:stock.view_stock_product_tree
#, python-format
msgid "Forecasted Quantity"
msgstr "כמות חזויה"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__report_stock_quantity__state__in
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_search
msgid "Forecasted Receipts"
msgstr "קבלות חזויות"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/forecast_widget.xml:0
#: model:ir.actions.client,name:stock.stock_replenishment_product_product_action
#: model:ir.actions.report,name:stock.stock_replenishment_report_product_product_action
#: model:ir.actions.report,name:stock.stock_replenishment_report_product_template_action
#, python-format
msgid "Forecasted Report"
msgstr "דוח תחזיות"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__report_stock_quantity__state__forecast
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_search
msgid "Forecasted Stock"
msgstr "מלאי חזוי"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__forecast_weight
msgid "Forecasted Weight"
msgstr "תחזית משקל"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Forecasted with Pending"
msgstr "צפי ובהמתנה"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_replenishment_header
msgid "Forecasted<br/>+ Pending"
msgstr "תחזית <br/> + בהמתנה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_label_layout__print_format
msgid "Format"
msgstr "תבנית"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Free Stock"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__free_qty
msgid "Free To Use Quantity "
msgstr "חופשי להשתמש בכמות"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__location_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__location_id
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "From"
msgstr "מ"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__owner_id
msgid "From Owner"
msgstr "מבעלים"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_reserved_availability
msgid "From Supplier"
msgstr "מהספק"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__complete_name
msgid "Full Location Name"
msgstr "שם המיקום"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Future Activities"
msgstr "פעילויות עתידיות"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Future Deliveries"
msgstr "משלוחים עתידיים"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Future P&L"
msgstr "דוח רווח הפסד עתידי"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Future Productions"
msgstr "ייצור עתידי"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Future Receipts"
msgstr "קבלות עתידיות"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Get a full traceability from vendors to customers"
msgstr "קבל מעקב מלא מספקים ללקוחות"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Get informative or blocking warnings on partners"
msgstr "קבל אזהרות אינפורמטיביות או חוסמות עבור לקוחות/ספקים"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_putaway_rule__sequence
msgid ""
"Give to the more specialized category, a higher priority to have them in top"
" of the list."
msgstr ""
"תן לקטגוריה המיוחדת יותר, עדיפות גבוהה יותר כדי להעלות אותה לראש הרשימה."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__sequence
msgid "Gives the sequence of this line when displaying the warehouses."
msgstr "נותן את רצף שורה זו בעת הצגת המחסנים."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Group By"
msgstr "קבץ לפי"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Group by..."
msgstr "קבץ לפי..."

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__group_stock_picking_wave
msgid "Group your move operations in wave transfer to process them together"
msgstr "קבץ את פעולות ההעברה שלך בהעברת גלים כדי לעבד אותן יחד"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_message
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__has_message
#: model:ir.model.fields,field_description:stock.field_stock_scrap__has_message
msgid "Has Message"
msgstr "יש הודעה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_line_exist
msgid "Has Pack Operations"
msgstr "יש פעולות אריזה "

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_packages
msgid "Has Packages"
msgstr "יש חבילות"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_scrap_move
msgid "Has Scrap Moves"
msgstr "יש לו תנועות של פסולים"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_tracking
msgid "Has Tracking"
msgstr "יש מעקב"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_has_variants
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__product_has_variants
msgid "Has variants"
msgstr "יש וריאנטים"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Having Category"
msgstr "בעל קטגוריה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__height
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Height"
msgstr "גובה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__posz
msgid "Height (Z)"
msgstr "גובה (Z)"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_height
msgid "Height must be positive"
msgstr "הגובה חייב להיות חיובי"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__snoozed_until
msgid "Hidden until next scheduler."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__hide_picking_type
msgid "Hide Picking Type"
msgstr "הסתר את סוג הבחירה"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
#, python-format
msgid "History"
msgstr "היסטוריה"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__reservation_method
msgid "How products in transfers of this operation type should be reserved."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__id
#: model:ir.model.fields,field_description:stock.field_product_removal__id
#: model:ir.model.fields,field_description:stock.field_product_replenish__id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__id
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__id
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__id
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__id
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__id
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__id
#: model:ir.model.fields,field_description:stock.field_stock_location__id
#: model:ir.model.fields,field_description:stock.field_stock_location_route__id
#: model:ir.model.fields,field_description:stock.field_stock_move__id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__id
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__id
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__id
#: model:ir.model.fields,field_description:stock.field_stock_package_type__id
#: model:ir.model.fields,field_description:stock.field_stock_picking__id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__id
#: model:ir.model.fields,field_description:stock.field_stock_quant__id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__id
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__id
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__id
#: model:ir.model.fields,field_description:stock.field_stock_request_count__id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__id
#: model:ir.model.fields,field_description:stock.field_stock_rule__id
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__id
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute__id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__id
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__id
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__id
#: model:ir.model.fields,field_description:stock.field_stock_track_line__id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__id
msgid "ID"
msgstr "תעודה מזהה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_exception_icon
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_exception_icon
msgid "Icon"
msgstr "סמל"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__activity_exception_icon
#: model:ir.model.fields,help:stock.field_stock_production_lot__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "סמל לציון פעילות חריגה."

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"If a payment is still outstanding more than sixty (60) days after the due "
"payment date, My Company (Chicago) reserves the right to call on the "
"services of a debt recovery company. All legal expenses will be payable by "
"the client."
msgstr ""

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_storage_category__allow_new_product__same
msgid "If all products are same"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__message_needaction
#: model:ir.model.fields,help:stock.field_stock_picking__message_unread
#: model:ir.model.fields,help:stock.field_stock_production_lot__message_needaction
#: model:ir.model.fields,help:stock.field_stock_production_lot__message_unread
#: model:ir.model.fields,help:stock.field_stock_scrap__message_needaction
#: model:ir.model.fields,help:stock.field_stock_scrap__message_unread
msgid "If checked, new messages require your attention."
msgstr "אם מסומן, הודעות חדשות דורשות את תשומת לבך."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__message_has_error
#: model:ir.model.fields,help:stock.field_stock_picking__message_has_sms_error
#: model:ir.model.fields,help:stock.field_stock_production_lot__message_has_error
#: model:ir.model.fields,help:stock.field_stock_production_lot__message_has_sms_error
#: model:ir.model.fields,help:stock.field_stock_scrap__message_has_error
#: model:ir.model.fields,help:stock.field_stock_scrap__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "אם מסומן, בחלק מההודעות קיימת שגיאת משלוח."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__propagate_cancel
msgid "If checked, when this move is cancelled, cancel the linked move too"
msgstr "אם מסומן,כאשר תנועה זו מבוטלת, התנועה המקושרת גם תבוטל "

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__result_package_id
msgid "If set, the operations are packed into this package"
msgstr "אם מוגדר, הפעולות נארזות בחבילה זו"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__active
msgid ""
"If the active field is set to False, it will allow you to hide the "
"orderpoint without removing it."
msgstr ""
"אם השדה הפעיל מוגדר כלא נכון, זה יאפשר לך להסתיר את נקודת הזמנה מבלי להסיר "
"אותה."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route__active
msgid ""
"If the active field is set to False, it will allow you to hide the route "
"without removing it."
msgstr ""
"אם השדה פעיל מוגדר כלא נכון, הוא יאפשר לך להסתיר את המסלול מבלי להסיר אותו."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_storage_category__allow_new_product__empty
msgid "If the location is empty"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid ""
"If the picking is unlocked you can edit initial demand (for a draft picking)"
" or done quantities (for a done picking)."
msgstr ""
"אם הליקוט לא נעול,ניתן לערוך את הביקוש הראשוני (לטיוטה ליקוט) או כמויות "
"שבוצעו (עבור ליקוט שבוצע)."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__show_reserved
#: model:ir.model.fields,help:stock.field_stock_picking_type__show_reserved
msgid ""
"If this checkbox is ticked, Odoo will automatically pre-fill the detailed "
"operations with the corresponding products, locations and lot/serial "
"numbers."
msgstr ""
"אם מסומנת תיבת סימון זו, Odoo ימלא אוטומטית מראש את הפעולות המפורטות "
"במוצרים, במיקומים ובאצוות/ מספרים הסידוריים המתאימים."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__print_label
msgid "If this checkbox is ticked, label will be print in this operation."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__show_operations
#: model:ir.model.fields,help:stock.field_stock_picking_type__show_operations
msgid ""
"If this checkbox is ticked, the pickings lines will represent detailed stock"
" operations. If not, the picking lines will represent an aggregate of "
"detailed stock operations."
msgstr ""
"אם תיבת סימון זו מסומנת, שורות הליקוט ייצגו פעולות מלאי מפורטות. אם לא, "
"שורות הליקוט ייצגו מכלול של פעולות מלאי מפורטות."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_type_use_create_lots
#: model:ir.model.fields,help:stock.field_stock_picking__use_create_lots
#: model:ir.model.fields,help:stock.field_stock_picking_type__use_create_lots
msgid ""
"If this is checked only, it will suppose you want to create new Lots/Serial "
"Numbers, so you can provide them in a text field. "
msgstr ""
"אם זה מסומן בלבד, זה מניח שאתה רוצה ליצור מספרי אצווה / מספרים סידוריים "
"חדשים, כך שתוכל לספק אותם בשדה טקסט."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_type_use_existing_lots
#: model:ir.model.fields,help:stock.field_stock_picking__use_existing_lots
#: model:ir.model.fields,help:stock.field_stock_picking_type__use_existing_lots
msgid ""
"If this is checked, you will be able to choose the Lots/Serial Numbers. You "
"can also decide to not put lots in this operation type.  This means it will "
"create stock with no lot or not put a restriction on the lot taken. "
msgstr ""
"אם זה מסומן, תוכל לבחור את מספרי האצוות / מספרים סידוריים. אתה יכול גם "
"להחליט לא לשים אצוות בסוג פעילות זה. המשמעות היא שהיא תיצור מלאי ללא אצווה "
"או לא יגביל את האצווה שנלקחת."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__backorder_id
msgid ""
"If this shipment was split, then this field links to the shipment which "
"contains the already processed part."
msgstr "אם המשלוח מפוצל, אז שדה זה מקשר למשלוח המכיל את החלק שכבר עובד ."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__picking_type_entire_packs
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_type_entire_packs
#: model:ir.model.fields,help:stock.field_stock_picking__picking_type_entire_packs
#: model:ir.model.fields,help:stock.field_stock_picking_type__show_entire_packs
msgid "If ticked, you will be able to select entire packages to move"
msgstr "אם  מסומן, תוכל לבחור חבילות שלמות לתנועה"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__active
msgid "If unchecked, it will allow you to hide the rule without removing it."
msgstr "אם לא מסומן, זה יאפשר לך להסתיר את הכלל מבלי להסיר אותו."

#. module: stock
#: model:ir.model,name:stock.model_stock_immediate_transfer
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__backorder_confirmation_id
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__immediate_transfer_id
#: model:ir.model.fields,field_description:stock.field_stock_move__from_immediate_transfer
#: model:ir.model.fields,field_description:stock.field_stock_picking__immediate_transfer
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Immediate Transfer"
msgstr "העברה מיידית"

#. module: stock
#: model:ir.model,name:stock.model_stock_immediate_transfer_line
msgid "Immediate Transfer Line"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__immediate_transfer_line_ids
msgid "Immediate Transfer Lines"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Immediate Transfer?"
msgstr "העברה מיידית?"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
msgid "Immediate transfer?"
msgstr "העברה מיידית?"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Import"
msgstr "ייבא"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__in_type_id
msgid "In Type"
msgstr "בסוג"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"In order for it to be admissible, My Company (Chicago) must be notified of "
"any claim by means of a letter sent by recorded delivery to its registered "
"office within 8 days of the delivery of the goods or the provision of the "
"services."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__incoming_qty
#: model:ir.model.fields,field_description:stock.field_product_template__incoming_qty
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Incoming"
msgstr "נכנס"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__in_date
msgid "Incoming Date"
msgstr "תאריך כניסה"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Incoming Draft Transfer"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__incoming_move_line_ids
msgid "Incoming Move Line"
msgstr "שורת תנועה נכנסת"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__reception_steps
msgid "Incoming Shipments"
msgstr "משלוחים נכנסים"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__inventory_diff_quantity
msgid ""
"Indicates the gap between the product's theoretical quantity and its counted"
" quantity."
msgstr "מציין את הפער בין הכמות התיאורטית של המוצר לכמות שלו שנספרה."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_kandan
msgid "Initial Demand"
msgstr "דרישה ראשונית"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Input"
msgstr "קלט"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_input_stock_loc_id
msgid "Input Location"
msgstr "מיקום קליטה"

#. module: stock
#: code:addons/stock/models/res_company.py:0
#, python-format
msgid "Inter-warehouse transit"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Internal"
msgstr "פנימי"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__internal
msgid "Internal Location"
msgstr "מיקום פנימי"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Internal Locations"
msgstr "מיקומים פנימיים"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__ref
msgid "Internal Reference"
msgstr "מק\"ט"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__code__internal
msgid "Internal Transfer"
msgstr "העברה פנימית"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#: model:stock.picking.type,name:stock.picking_type_internal
#, python-format
msgid "Internal Transfers"
msgstr "העברות פנימיות"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__internal_transit_location_id
msgid "Internal Transit Location"
msgstr "מיקום מעבר פנימי"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__int_type_id
msgid "Internal Type"
msgstr "סוג פנימי"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__child_internal_location_ids
msgid "Internal locations amoung descendants"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_production_lot__ref
msgid ""
"Internal reference number in case it differs from the manufacturer's "
"lot/serial number"
msgstr "מספר האסמכתא פנימי במקרה אי התאמה מספר סידורי /אצווה של היצרן "

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Invalid domain left operand %s"
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:0 code:addons/stock/models/product.py:0
#, python-format
msgid "Invalid domain operator %s"
msgstr "תחום אופרטור לא חוקי %s"

#. module: stock
#: code:addons/stock/models/product.py:0 code:addons/stock/models/product.py:0
#, python-format
msgid "Invalid domain right operand '%s'. It must be of type Integer/Float"
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"Invalid rule's configuration, the following rule causes an endless loop: %s"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_quantity_auto_apply
msgid "Inventoried Quantity"
msgstr "כמות במלאי"

#. module: stock
#: model:ir.actions.server,name:stock.action_view_inventory_tree
#: model:ir.actions.server,name:stock.action_view_quants
#: model:ir.model.fields,field_description:stock.field_stock_move__is_inventory
#: model:ir.model.fields,field_description:stock.field_stock_move_line__is_inventory
#: model:ir.ui.menu,name:stock.menu_stock_root
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_partner_stock_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_pivot
msgid "Inventory"
msgstr "מלאי"

#. module: stock
#: code:addons/stock/wizard/stock_inventory_adjustment_name.py:0
#, python-format
msgid "Inventory Adjustment"
msgstr "התאמת מלאי"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__inventory_adjustment_name
msgid "Inventory Adjustment Name"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_inventory_adjustement_name
#: model:ir.model,name:stock.model_stock_inventory_adjustment_name
msgid "Inventory Adjustment Reference / Reason"
msgstr "מזהה / סיבה להתאמת מלאי"

#. module: stock
#: model:ir.model,name:stock.model_stock_inventory_warning
msgid "Inventory Adjustment Warning"
msgstr "אזהרת התאמת מלאי"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#: model:ir.ui.menu,name:stock.menu_action_inventory_tree
#, python-format
msgid "Inventory Adjustments"
msgstr "התאמות מלאי"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "Inventory Count Sheet"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_request_count__inventory_date
msgid "Inventory Date"
msgstr "תאריך מלאי"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__cyclic_inventory_frequency
msgid "Inventory Frequency (Days)"
msgstr "תדירות מלאי (בימים)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__property_stock_inventory
#: model:ir.model.fields,field_description:stock.field_product_template__property_stock_inventory
msgid "Inventory Location"
msgstr "מקומות המלאי"

#. module: stock
#: model:ir.model,name:stock.model_stock_location
msgid "Inventory Locations"
msgstr "מקומות המלאי"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__inventory
msgid "Inventory Loss"
msgstr "הפסד מלאי"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Inventory On Hand"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_picking_type_action
msgid "Inventory Overview"
msgstr "סקירה כללית של מלאי"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_quantity_set
msgid "Inventory Quantity Set"
msgstr "הגדרת כמות מלאי"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_adjustment_name_form_view
msgid "Inventory Reference / Reason"
msgstr ""

#. module: stock
#: model:ir.ui.menu,name:stock.menu_valuation
msgid "Inventory Report"
msgstr "דוחות מלאי"

#. module: stock
#: model:ir.model,name:stock.model_stock_location_route
msgid "Inventory Routes"
msgstr "מסלולי מלאי"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree
msgid "Inventory Valuation"
msgstr "הערכת שווי המלאי"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/inventory_report.xml:0
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__inventory_datetime
#, python-format
msgid "Inventory at Date"
msgstr "מלאי בתאריך"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_is_follower
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_is_follower
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_is_follower
msgid "Is Follower"
msgstr "עוקב"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__is_fresh_package
msgid "Is Fresh Package"
msgstr "חבילה חדשה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__is_locked
#: model:ir.model.fields,field_description:stock.field_stock_move_line__is_locked
#: model:ir.model.fields,field_description:stock.field_stock_picking__is_locked
msgid "Is Locked"
msgstr "נעול"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__is_signed
msgid "Is Signed"
msgstr "נחתם "

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__return_location
msgid "Is a Return Location?"
msgstr "האם מיקום החזרה?"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__scrap_location
msgid "Is a Scrap Location?"
msgstr "האם מיקום פסולים?"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__is_initial_demand_editable
#: model:ir.model.fields,field_description:stock.field_stock_move_line__is_initial_demand_editable
msgid "Is initial demand editable"
msgstr "ביקוש ראשוני ניתן לעריכה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_deadline_issue
msgid "Is late"
msgstr "מאחר"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__has_deadline_issue
msgid "Is late or will be late depending on the deadline and scheduled date"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__is_quantity_done_editable
msgid "Is quantity done editable"
msgstr "כמות שבוצעה ניתנת לעריכה"

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid ""
"It is not allowed to import reserved quantity, you have to use the quantity "
"directly."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"It is not possible to reserve more products of %s than you have in stock."
msgstr " לא ניתן לשמור יותר מוצרים  %s מהכמות שיש לך במלאי."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"It is not possible to unreserve more products of %s than you have in stock."
msgstr "לא ניתן לא לשמור יותר מוצרים %s מהכמות שיש לך במלאי."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__move_type
msgid "It specifies goods to be deliver partially or all at once"
msgstr "הוא מציין את הסחורות שיועברו באופן חלקי או בבת אחת"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__json_popover
msgid "JSON data for the popover widget"
msgstr ""

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__1
msgid "January"
msgstr "ינואר"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__json_lead_days
msgid "Json Lead Days"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__json_replenishment_history
msgid "Json Replenishment History"
msgstr ""

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__7
msgid "July"
msgstr "יולי"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__6
msgid "June"
msgstr "יוני"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Keep Counted Quantity"
msgstr "שמור כמות שנספרה "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Keep Difference"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid ""
"Keep the <strong>Counted Quantity</strong> (the Difference will be updated)"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid ""
"Keep the <strong>Difference</strong> (the Counted Quantity will be updated "
"to reflect the same difference as when you counted)"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Last 12 Months"
msgstr "12 החודשים האחרונים"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Last 3 Months"
msgstr "3 חודשים אחרונים"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Last 30 Days"
msgstr "30 ימים אחרונים"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__last_delivery_partner_id
msgid "Last Delivery Partner"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__last_inventory_date
msgid "Last Effective Inventory"
msgstr "מלאי אפקטיבי אחרון"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group____last_update
#: model:ir.model.fields,field_description:stock.field_product_removal____last_update
#: model:ir.model.fields,field_description:stock.field_product_replenish____last_update
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity____last_update
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial____last_update
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation____last_update
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line____last_update
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty____last_update
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer____last_update
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line____last_update
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name____last_update
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict____last_update
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning____last_update
#: model:ir.model.fields,field_description:stock.field_stock_location____last_update
#: model:ir.model.fields,field_description:stock.field_stock_location_route____last_update
#: model:ir.model.fields,field_description:stock.field_stock_move____last_update
#: model:ir.model.fields,field_description:stock.field_stock_move_line____last_update
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze____last_update
#: model:ir.model.fields,field_description:stock.field_stock_package_destination____last_update
#: model:ir.model.fields,field_description:stock.field_stock_package_level____last_update
#: model:ir.model.fields,field_description:stock.field_stock_package_type____last_update
#: model:ir.model.fields,field_description:stock.field_stock_picking____last_update
#: model:ir.model.fields,field_description:stock.field_stock_picking_type____last_update
#: model:ir.model.fields,field_description:stock.field_stock_production_lot____last_update
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule____last_update
#: model:ir.model.fields,field_description:stock.field_stock_quant____last_update
#: model:ir.model.fields,field_description:stock.field_stock_quant_package____last_update
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history____last_update
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info____last_update
#: model:ir.model.fields,field_description:stock.field_stock_request_count____last_update
#: model:ir.model.fields,field_description:stock.field_stock_return_picking____last_update
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line____last_update
#: model:ir.model.fields,field_description:stock.field_stock_rule____last_update
#: model:ir.model.fields,field_description:stock.field_stock_rules_report____last_update
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute____last_update
#: model:ir.model.fields,field_description:stock.field_stock_scrap____last_update
#: model:ir.model.fields,field_description:stock.field_stock_storage_category____last_update
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity____last_update
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report____last_update
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation____last_update
#: model:ir.model.fields,field_description:stock.field_stock_track_line____last_update
#: model:ir.model.fields,field_description:stock.field_stock_warehouse____last_update
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint____last_update
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap____last_update
msgid "Last Modified on"
msgstr "שינוי אחרון ב"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__write_uid
#: model:ir.model.fields,field_description:stock.field_product_removal__write_uid
#: model:ir.model.fields,field_description:stock.field_product_replenish__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_location__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_location_route__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_move__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_level__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_type__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_request_count__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_rule__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_scrap__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__write_date
#: model:ir.model.fields,field_description:stock.field_product_removal__write_date
#: model:ir.model.fields,field_description:stock.field_product_replenish__write_date
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__write_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__write_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__write_date
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__write_date
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__write_date
#: model:ir.model.fields,field_description:stock.field_stock_location__write_date
#: model:ir.model.fields,field_description:stock.field_stock_location_route__write_date
#: model:ir.model.fields,field_description:stock.field_stock_move__write_date
#: model:ir.model.fields,field_description:stock.field_stock_move_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__write_date
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__write_date
#: model:ir.model.fields,field_description:stock.field_stock_package_level__write_date
#: model:ir.model.fields,field_description:stock.field_stock_package_type__write_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__write_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__write_date
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__write_date
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quant__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__write_date
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__write_date
#: model:ir.model.fields,field_description:stock.field_stock_request_count__write_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__write_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_rule__write_date
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__write_date
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute__write_date
#: model:ir.model.fields,field_description:stock.field_stock_scrap__write_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__write_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__write_date
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__write_date
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__write_date
#: model:ir.model.fields,field_description:stock.field_stock_track_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__write_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__write_date
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__products_availability_state__late
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Late"
msgstr "מאחר"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Late Activities"
msgstr "פעילויות באיחור"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_late
msgid "Late Transfers"
msgstr "העברות באיחור"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__products_availability
msgid "Latest product availability status of the picking"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__lead_days_date
msgid "Lead Days Date"
msgstr "תאריך ההובלה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__delay
msgid "Lead Time"
msgstr "זמן אספקה "

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "Lead Times"
msgstr ""

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_request_count__set_count__empty
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__group_propagation_option__none
msgid "Leave Empty"
msgstr "השאר ריק"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route__company_id
#: model:ir.model.fields,help:stock.field_stock_rule__route_company_id
msgid "Leave this field empty if this route is shared between all companies"
msgstr "השאר שדה זה ריק אם מסלול זה משותף בין כל החברות"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Legend"
msgstr "מקרא"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__packaging_length
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Length"
msgstr "אורך"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_length
msgid "Length must be positive"
msgstr "האורך חייב להיות חיובי"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__length_uom_name
msgid "Length unit of measure label"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__company_id
#: model:ir.model.fields,help:stock.field_stock_quant__company_id
msgid "Let this field empty if this location is shared between companies"
msgstr "השאר שדה זה ריק אם מיקום זה משותף בין חברות"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Linked Moves"
msgstr "תנועות מקושרות"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "List view of operations"
msgstr "תצוגת רשימה של פעולות"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__location_id
#: model:ir.model.fields,field_description:stock.field_product_template__location_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__location_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__location_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__location_ids
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__location_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__location_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__location_id
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__location
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_tree2
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Location"
msgstr "מיקום"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_location_barcode
msgid "Location Barcode"
msgstr "ברקוד מיקום "

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__name
msgid "Location Name"
msgstr "שם המיקום"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__lot_stock_id
msgid "Location Stock"
msgstr "מיקום מלאי"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__usage
msgid "Location Type"
msgstr "סוג המיקום"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__location_dest_id
msgid "Location where the system will stock the finished products."
msgstr "מיקום בו המערכת תאחסן מוצרים גמורים."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Location: Store to"
msgstr "מיקום: אחסן ב-"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Location: When arrives to"
msgstr "מיקום: כאשר מגיע ל"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_location_form
#: model:ir.actions.act_window,name:stock.action_prod_inv_location_form
#: model:ir.actions.act_window,name:stock.action_storage_category_locations
#: model:ir.ui.menu,name:stock.menu_action_location_form
#: model_terms:ir.ui.view,arch_db:stock.report_location_barcode
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Locations"
msgstr "מיקומים"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Lock"
msgstr "נעל"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_category_form_view_inherit
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Logistics"
msgstr "לוגיסטיקה"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__lot
msgid "Lot"
msgstr "אצווה"

#. module: stock
#: model:ir.model,name:stock.model_stock_production_lot
#: model:ir.model.fields,field_description:stock.field_stock_scrap__lot_id
msgid "Lot/Serial"
msgstr "מספר סידורי/אצווה"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
msgid "Lot/Serial #"
msgstr "מס' אצווה/ סידורי"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Lot/Serial :"
msgstr "מס' אצווה/ סידורי:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__lot_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__name
#: model:ir.model.fields,field_description:stock.field_stock_quant__lot_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Lot/Serial Number"
msgstr "מס' אצווה/ סידורי"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_lot_label
msgid "Lot/Serial Number (PDF)"
msgstr "מספר סידורי/אצווה (PDF)"

#. module: stock
#: model:ir.actions.report,name:stock.label_lot_template
msgid "Lot/Serial Number (ZPL)"
msgstr "מספר סידוריי/אצווה (ZPL)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__lot_name
msgid "Lot/Serial Number Name"
msgstr "שם אצווה/מספר סידורי "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "Lot/Serial Numbers"
msgstr "מספרים של אצווה/ סידוריים"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_production_lot
msgid "Lots & Serial Numbers"
msgstr "מספרים סידוריים ואצוות "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Lots &amp; Serial numbers will appear on the delivery slip"
msgstr "אצוות ומספרים סידוריים יופיעו בתעודות משלוח"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__lots_visible
msgid "Lots Visible"
msgstr "אצוות גלויות"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "Lots or serial numbers were not provided for tracked products"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_production_lot_form
#: model:ir.ui.menu,name:stock.menu_action_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_tree
msgid "Lots/Serial Numbers"
msgstr "מספר סידוריים/אצוות"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_production_lot_form
msgid ""
"Lots/Serial numbers help you tracking the path followed by your products.\n"
"            From their traceability report you will see the full history of their use, as well as their composition."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__mto_pull_id
msgid "MTO rule"
msgstr "כלל ייצור לפי הזמנה "

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_main_attachment_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_main_attachment_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_main_attachment_id
msgid "Main Attachment"
msgstr "קובץ ראשי מצורף "

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Make To Order"
msgstr "הכן להזמנה"

#. module: stock
#: model:res.groups,name:stock.group_tracking_owner
msgid "Manage Different Stock Owners"
msgstr "נהל אחראי מלאים שונים"

#. module: stock
#: model:res.groups,name:stock.group_production_lot
msgid "Manage Lots / Serial Numbers"
msgstr "נהל  מספרים סידוריים / אצוות "

#. module: stock
#: model:res.groups,name:stock.group_stock_multi_locations
msgid "Manage Multiple Stock Locations"
msgstr "נהל מיקומי מלאי מרובים"

#. module: stock
#: model:res.groups,name:stock.group_stock_multi_warehouses
msgid "Manage Multiple Warehouses"
msgstr "נהל מחסנים מרובים"

#. module: stock
#: model:res.groups,name:stock.group_tracking_lot
msgid "Manage Packages"
msgstr "נהל חבילות"

#. module: stock
#: model:res.groups,name:stock.group_adv_location
msgid "Manage Push and Pull inventory flows"
msgstr "נהל תהליכי משיכה ודחיפה של המלאי"

#. module: stock
#: model:res.groups,name:stock.group_stock_storage_categories
msgid "Manage Storage Categories"
msgstr "נהל קטגוריות אחסון"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Manage product packagings (e.g. pack of 6 bottles, box of 10 pieces)"
msgstr "נהל אריזות מוצרים (למשל: אריזה של 6 בקבוקים, קופסה של 10 חלקים)"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse_orderpoint__trigger__manual
msgid "Manual"
msgstr "ידני"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__auto__manual
msgid "Manual Operation"
msgstr "פעולה ידנית"

#. module: stock
#: code:addons/stock/wizard/product_replenish.py:0
#: code:addons/stock/wizard/product_replenish.py:0
#, python-format
msgid "Manual Replenishment"
msgstr "חידוש מלאי ידני"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__reservation_method__manual
msgid "Manually"
msgstr "ידני"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Manufacturing"
msgstr "ייצור"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__3
msgid "March"
msgstr "מרץ"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Mark as Todo"
msgstr "סמן לביצוע  "

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_max_qty
msgid "Max Quantity"
msgstr "כמות מקסימום"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__max_weight
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__max_weight
msgid "Max Weight"
msgstr "משקל מקסימלי"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_tree
msgid "Max Weight (kg)"
msgstr "משקל מקסימלי (Kg)"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_max_weight
msgid "Max Weight must be positive"
msgstr "משקל מקסימלי חייב להיות חיובי"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_positive_max_weight
msgid "Max weight should be a positive number."
msgstr "המשקל המקסימלי צריך להיות מספר חיובי."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__reservation_days_before_priority
msgid ""
"Maximum number of days before scheduled date that priority picking products "
"should be reserved."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__reservation_days_before
msgid ""
"Maximum number of days before scheduled date that products should be "
"reserved."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__max_weight
msgid "Maximum weight shippable in this packaging"
msgstr "משקל מקסימלי הניתן למשלוח באריזה זו"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__5
msgid "May"
msgstr "מאי"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_has_error
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_has_error
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_has_error
msgid "Message Delivery error"
msgstr "הודעת שגיאת שליחה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__picking_warn_msg
#: model:ir.model.fields,field_description:stock.field_res_users__picking_warn_msg
msgid "Message for Stock Picking"
msgstr "הודעה עבור ליקוט מלאי"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_ids
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_ids
msgid "Messages"
msgstr "הודעות"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_removal__method
msgid "Method"
msgstr "אמצעי תשלום"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_min_qty
msgid "Min Quantity"
msgstr "כמות מינימום"

#. module: stock
#: model:ir.model,name:stock.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "כלל מלאי מינימלי"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__orderpoint_ids
msgid "Minimum Stock Rules"
msgstr "כללי מלאי מינימום"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__move_ids
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__move_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__move_ids
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__move_id
msgid "Move"
msgstr "תנועה"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
msgid "Move Detail"
msgstr "פרטי תנועה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_type_entire_packs
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_entire_packs
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_type_entire_packs
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__show_entire_packs
msgid "Move Entire Packages"
msgstr "העבר חבילות שלמות "

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_label_layout__move_line_ids
#: model:ir.model.fields,field_description:stock.field_stock_move__move_line_ids
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__move_line_ids
#: model:ir.model.fields,field_description:stock.field_stock_package_level__move_line_ids
msgid "Move Line"
msgstr "שורת תנועה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_line_nosuggest_ids
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_line_nosuggest_ids
msgid "Move Line Nosuggest"
msgstr "Move Line Nosuggest"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree_detailed
msgid "Move Lines"
msgstr "שורות תנועה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_lines_count
msgid "Move Lines Count"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__origin_returned_move_id
msgid "Move that created the return move"
msgstr "תנועה שיצרה את תנועת ההחזרה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__product_return_moves
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
msgid "Moves"
msgstr "תנועות"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__group_id
msgid ""
"Moves created through this orderpoint will be put in this procurement group."
" If none is given, the moves generated by stock rules will be grouped into "
"one big picking."
msgstr ""
"תנועות שנוצרו דרך נקודת הזמנה זו יוכנסו לקבוצת רכש זו. אם לא ניתן, התנועות "
"שנוצרו על ידי כללי מלאי יקובצו לתוך ליקוט אחד גדול."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_adv_location
msgid "Multi-Step Routes"
msgstr "מסלולים מרובי צעדים"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_multiple
msgid "Multiple Quantity"
msgstr "כמות מרובה"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_capacity_unique_package_type
msgid "Multiple capacity rules for one package type."
msgstr "כללי קיבולת מרובים עבור סוג חבילה אחד."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_capacity_unique_product
msgid "Multiple capacity rules for one product."
msgstr "כללי קיבולת מרובים עבור מוצר אחד."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__my_activity_date_deadline
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "מועד אחרון לפעילות שלי"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"My Company (Chicago) undertakes to do its best to supply performant services"
" in due time in accordance with the agreed timeframes. However, none of its "
"obligations can be considered as being an obligation to achieve results. My "
"Company (Chicago) cannot under any circumstances, be required by the client "
"to appear as a third party in the context of any claim for damages filed "
"against the client by an end consumer."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "My Counts"
msgstr "הספירות שלי"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "My Transfers"
msgstr "ההעברות שלי"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_removal__name
#: model:ir.model.fields,field_description:stock.field_stock_rule__name
#: model:ir.model.fields,field_description:stock.field_stock_track_line__product_display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__name
msgid "Name"
msgstr "שם"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__nbr_moves_in
#: model:ir.model.fields,field_description:stock.field_product_template__nbr_moves_in
msgid "Nbr Moves In"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__nbr_moves_out
#: model:ir.model.fields,field_description:stock.field_product_template__nbr_moves_out
msgid "Nbr Moves Out"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_search_form_view_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_product_search_form_view
msgid "Negative Forecasted Quantity"
msgstr "כמות חזויה שלילית"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Negative Stock"
msgstr "מלאי שלילי"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__net_weight
msgid "Net Weight"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_scrap.py:0
#: code:addons/stock/models/stock_scrap.py:0
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__draft
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__new
#, python-format
msgid "New"
msgstr "חדש"

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid "New Move:"
msgstr "תנועה חדשה:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__new_quantity
msgid "New Quantity on Hand"
msgstr "כמות חדשה במלאי"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_form
msgid "New Transfer"
msgstr "העברה חדשה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_calendar_event_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "הפעילות הבאה ביומן"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_date_deadline
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "מועד אחרון לפעילות הבאה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_summary
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_summary
msgid "Next Activity Summary"
msgstr "תיאור הפעילות הבאה "

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_type_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_type_id
msgid "Next Activity Type"
msgstr "סוג הפעילות הבאה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__next_inventory_date
msgid "Next Expected Inventory"
msgstr "המלאי הצפוי הבא"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__inventory_date
msgid "Next date the On Hand Quantity should be counted."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "Next transfer(s) impacted:"
msgstr "ההעברות הבאות השפיעו:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "No Backorder"
msgstr "ללא הזמנה חוזרת"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_partner__picking_warn__no-message
msgid "No Message"
msgstr "אין הודעה"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_template__tracking__none
msgid "No Tracking"
msgstr "ללא מעקב "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "No allocation need found for incoming products."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid "No negative quantities allowed"
msgstr "כמויות שליליות אינן מורשות"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
msgid "No operation made on this lot."
msgstr "לא בוצעה פעולה באצווה זו."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_get_picking_type_operations
msgid "No operations found. Let's create a tranfer !"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.product_template_action_product
msgid "No product found. Let's create one!"
msgstr "לא נמצאו מוצרים. בואו ניצור אחד!"

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:0
#, python-format
msgid ""
"No products to return (only lines in Done state and not fully returned yet "
"can be returned)."
msgstr ""
"אין מוצרים להחזרה (רק שורות במצב בוצע ושלא הוחזרו עדיין במלואן ניתנות "
"להחזרה)."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_putaway_tree
msgid "No putaway rule found. Let's create one!"
msgstr "אין כללי פיזור באיתורי אחסון. בואו ניצור!"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint
msgid "No reordering rule found"
msgstr "לא נמצא כלל הזמנה מחדש"

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid ""
"No rule has been found to replenish \"%s\" in \"%s\".\n"
"Verify the routes configuration on the product."
msgstr ""
"לא נמצא כלל לחידוש מלאי \"%s\" עבור \"%s\".\n"
"בדוק את הגדרת המסלולים במוצר."

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid "No source location defined on stock rule: %s!"
msgstr "לא הוגדר מיקום מוצא על כלל המלאי: %s!"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_action
msgid "No stock move found"
msgstr "לא נמצאו תנועות מלאי"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "No Stock On Hand"
msgstr "אין כרגע מלאי"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_picking_form
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_all
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_backorder
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_late
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_ready
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_waiting
#: model_terms:ir.actions.act_window,help:stock.action_picking_type_list
#: model_terms:ir.actions.act_window,help:stock.stock_picking_action_picking_type
msgid "No transfer found. Let's create one!"
msgstr "לא נמצאו העברות. בואו ניצור העברה!"

#. module: stock
#: code:addons/stock/report/report_stock_reception.py:0
#, python-format
msgid "No transfers selected or a delivery order selected"
msgstr ""

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__priority__0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__priority__0
msgid "Normal"
msgstr "נורמלי "

#. module: stock
#. openerp-web
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/static/src/xml/forecast_widget.xml:0
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
#, python-format
msgid "Not Available"
msgstr "לא זמין"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Not Snoozed"
msgstr "לא במצב \"נודניק\""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Note"
msgstr "הערה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__note
msgid "Notes"
msgstr "הערות"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Nothing to check the availability for."
msgstr "אין דבר לבדוק עבורו זמינות."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__11
msgid "November"
msgstr "נובמבר"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_needaction_counter
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_needaction_counter
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_needaction_counter
msgid "Number of Actions"
msgstr "מספר פעולות"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__next_serial_count
#: model:ir.model.fields,field_description:stock.field_stock_move__next_serial_count
msgid "Number of SN"
msgstr "מספר סידורי"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_has_error_counter
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_has_error_counter
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_has_error_counter
msgid "Number of errors"
msgstr "מספר השגיאות"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__nbr_moves_in
#: model:ir.model.fields,help:stock.field_product_template__nbr_moves_in
msgid "Number of incoming stock moves in the past 12 months"
msgstr "מספר תנועות המלאי הנכנסות ב-12 החודשים האחרונים"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__message_needaction_counter
#: model:ir.model.fields,help:stock.field_stock_production_lot__message_needaction_counter
#: model:ir.model.fields,help:stock.field_stock_scrap__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "מספר הודעות המחייבות פעולה"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__message_has_error_counter
#: model:ir.model.fields,help:stock.field_stock_production_lot__message_has_error_counter
#: model:ir.model.fields,help:stock.field_stock_scrap__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "מספר הודעות עם שגיאת משלוח"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__nbr_moves_out
#: model:ir.model.fields,help:stock.field_product_template__nbr_moves_out
msgid "Number of outgoing stock moves in the past 12 months"
msgstr "מספר תנועות המלאי היוצאות ב-12 החודשים האחרונים"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__message_unread_counter
#: model:ir.model.fields,help:stock.field_stock_production_lot__message_unread_counter
#: model:ir.model.fields,help:stock.field_stock_scrap__message_unread_counter
msgid "Number of unread messages"
msgstr "מספר ההודעות שלא נקראו"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__10
msgid "October"
msgstr "אוקטובר"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__on_hand
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_on_hand
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_replenishment_header
msgid "On Hand"
msgstr "במלאי"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "On Hand Quantity"
msgstr "כמות במלאי"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__available_quantity
msgid ""
"On hand quantity which hasn't been reserved on a transfer, in the default "
"unit of measure of the product"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_kanban_stock_view
msgid "On hand:"
msgstr "במלאי"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Only a stock manager can validate an inventory adjustment."
msgstr "רק מנהל מחסן יכול לאשר התאמת מלאי."

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_type_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_type_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__name
#: model:ir.model.fields,field_description:stock.field_stock_rule__picking_type_id
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#, python-format
msgid "Operation Type"
msgstr "סוג פעולה  "

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__return_picking_type_id
msgid "Operation Type for Returns"
msgstr "סוג פעולה עבור החזרות"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking_type_label
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_tree
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Operation Types"
msgstr "סוגי פעולות"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Operation not supported"
msgstr "הפעולה אינה נתמכת"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_id
msgid "Operation type"
msgstr "סוג הפעולה"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_picking_type_label
msgid "Operation type (PDF)"
msgstr "סוג הפעולה (PDF)"

#. module: stock
#: model:ir.actions.report,name:stock.label_picking_type
msgid "Operation type (ZPL)"
msgstr "סוג פעולה (ZPL)"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_get_picking_type_operations
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_line_ids
#: model:ir.ui.menu,name:stock.menu_stock_warehouse_mgmt
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Operations"
msgstr "פעולות"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_type_list
#: model:ir.ui.menu,name:stock.menu_pickingtype
msgid "Operations Types"
msgstr "סוגי פעולות"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_line_ids_without_package
msgid "Operations without package"
msgstr "פעולות ללא אריזה"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__partner_id
msgid ""
"Optional address where goods are to be delivered, specifically used for "
"allotment"
msgstr "כתובת אופציונלית שאליה ניתן לשלוח את הסחורה, משמשת במיוחד להקצאה"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__posx
#: model:ir.model.fields,help:stock.field_stock_location__posy
#: model:ir.model.fields,help:stock.field_stock_location__posz
msgid "Optional localization details, for information purpose only"
msgstr "פרטי מיקום אופציונליים, לצורך מידע בלבד"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__returned_move_ids
msgid "Optional: all returned moves created from this move"
msgstr "אופציונלי: כל התנועות שהוחזרו נוצרו מתנועה זו"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__move_dest_ids
msgid "Optional: next stock move when chaining them"
msgstr "אופציונלי: תנועת המלאי הבאה כאשר קושרים אותן"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__move_orig_ids
msgid "Optional: previous stock move when chaining them"
msgstr "אופציונלי: תנועת המלאי הקודמת כאשר קושרים אותן"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Options"
msgstr "אפשרויות"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "Order"
msgstr "הזמנה"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Order Once"
msgstr "הזמן פעם אחת"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Order signed"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Order signed by %s"
msgstr "הזמנה נחתמה ע\"י %s"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__orderpoint_ids
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__orderpoint_id
msgid "Orderpoint"
msgstr "נקודת הזמנה"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Origin"
msgstr "מקור"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Origin Moves"
msgstr "תנועות מקור"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__origin_returned_move_id
msgid "Origin return move"
msgstr "מקור תנועת החזרה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__original_location_id
msgid "Original Location"
msgstr "מיקום מקורי"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_orig_ids
msgid "Original Move"
msgstr "תנועה מקורית "

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__orderpoint_id
msgid "Original Reordering Rule"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Other Information"
msgstr "מידע נוסף"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"Our invoices are payable within 21 working days, unless another payment "
"timeframe is indicated on either the invoice or the order. In the event of "
"non-payment by the due date, My Company (Chicago) reserves the right to "
"request a fixed interest payment amounting to 10% of the sum remaining due. "
"My Company (Chicago) will be authorized to suspend any provision of services"
" without prior warning in the event of late payment."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__out_type_id
msgid "Out Type"
msgstr "סוג יציאה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__outgoing_qty
#: model:ir.model.fields,field_description:stock.field_product_template__outgoing_qty
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Outgoing"
msgstr " יוצא"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Outgoing Draft Transfer"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__outgoing_move_line_ids
msgid "Outgoing Move Line"
msgstr "שורת תנועה יוצאת"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__delivery_steps
msgid "Outgoing Shipments"
msgstr "משלוחים יוצאים"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Output"
msgstr "פלט"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_output_stock_loc_id
msgid "Output Location"
msgstr "מיקום משלוח"

#. module: stock
#: model:ir.ui.menu,name:stock.stock_picking_type_menu
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rules_report
msgid "Overview"
msgstr "סקירה כללית"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__owner_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__owner_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__owner_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
msgid "Owner"
msgstr "אחראי"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__restrict_partner_id
msgid "Owner "
msgstr "בעלים"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Owner :"
msgstr "בעלים :"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "P&L Qty"
msgstr "כמות רווח והפסד "

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_traceability_report_backend.xml:0
#, python-format
msgid "PRINT"
msgstr "הדפס"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Pack"
msgstr "חבילה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__pack_type_id
msgid "Pack Type"
msgstr "סוג חבילה"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__delivery_steps__pick_pack_ship
msgid "Pack goods, send goods in output and then deliver (3 steps)"
msgstr "ארוז סחורה, שלח לאיזור משלוח ולאחר מכן שלח ללקוח (3 שלבים)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__package_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__package_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__package_id
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__package
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_tree
msgid "Package"
msgstr "חבילה"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_quant_package_barcode_small
msgid "Package Barcode (PDF)"
msgstr "ברקוד חבילה (PDF)"

#. module: stock
#: model:ir.actions.report,name:stock.label_package_template
msgid "Package Barcode (ZPL)"
msgstr "ברקוד חבילה (ZPL)"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_quant_package_barcode
msgid "Package Barcode with Content"
msgstr "ברקוד חבילה עם תוכן"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__package_capacity_ids
msgid "Package Capacity"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_package_level.py:0
#, python-format
msgid "Package Content"
msgstr "תוכן חבילה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__package_level_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__package_level_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__package_level_ids
msgid "Package Level"
msgstr "רמת חבילה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__package_level_ids_details
msgid "Package Level Ids Details"
msgstr "פרטים על רמת החבילה"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
msgid "Package Name"
msgstr "שם חבילה "

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__name
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Package Reference"
msgstr "מזהה חבילה"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Package Transfers"
msgstr "העברות חבילה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_packaging__package_type_id
#: model:ir.model.fields,field_description:stock.field_stock_package_type__name
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__package_type_ids
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__package_type_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__package_type_id
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Package Type"
msgstr "סוג חבילה"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_package_type_view
#: model:ir.ui.menu,name:stock.menu_packaging_types
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_tree
msgid "Package Types"
msgstr "סוגי חבילות"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__package_use
msgid "Package Use"
msgstr "שימוש בחבילה"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Package type"
msgstr "סוג חבילה"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_package_view
#: model:ir.model,name:stock.model_stock_quant_package
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_tracking_lot
#: model:ir.ui.menu,name:stock.menu_package
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Packages"
msgstr "חבילות"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_package_view
msgid ""
"Packages are usually created via transfers (during pack operation) and can contain different products.\n"
"                Once created, the whole package can be moved at once, or products can be unpacked and moved as single units again."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_packaging_id
msgid "Packaging"
msgstr "אריזה"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__height
msgid "Packaging Height"
msgstr "גובה אריזה"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__packaging_length
msgid "Packaging Length"
msgstr "אורך אריזה"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__width
msgid "Packaging Width"
msgstr "רוחב אריזה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__packaging_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Packagings"
msgstr "אריזות"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_pack_stock_loc_id
msgid "Packing Location"
msgstr "מיקום אריזה"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Packing Zone"
msgstr "אזור אריזה"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_compute_wizard
msgid "Parameters"
msgstr "פרמטרים"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__location_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__parent_location_id
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Parent Location"
msgstr "מיקום אב"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__parent_path
msgid "Parent Path"
msgstr "נתיב אב"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__procurement_group__move_type__direct
msgid "Partial"
msgstr "חלקי"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__partially_available
msgid "Partially Available"
msgstr "זמין חלקית"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__partner_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Partner"
msgstr "לקוח/ספק"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__partner_address_id
msgid "Partner Address"
msgstr "כתובת לקוח/ספק"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__pick_ids
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__pick_ids
#, python-format
msgid "Pick"
msgstr "לקט"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__pick_type_id
msgid "Pick Type"
msgstr "סוג ליקוט"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__picking_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Picking"
msgstr "ליקוט"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Picking Lists"
msgstr "רשימות ליקוט"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_picking
msgid "Picking Operations"
msgstr "פעולות ליקוט"

#. module: stock
#: model:ir.model,name:stock.model_stock_picking_type
msgid "Picking Type"
msgstr "סוג ליקוט"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__picking_type_code_domain
msgid "Picking Type Code Domain"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Picking list"
msgstr "רשימת ליקוט"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Pickings already processed"
msgstr "ליקוטים שכבר עובדו"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Planned Transfer"
msgstr "העברה מתוכננת"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/js/stock_rescheduling_popover.js:0
#, python-format
msgid "Planning Issue"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Planning Issues"
msgstr "בעיות בתכנון"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Please add 'Done' quantities to the picking to create a new pack."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Please add some items to move."
msgstr "הוסף מספר פריטים להעברה."

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:0
#, python-format
msgid "Please specify at least one non-zero quantity."
msgstr "ציין לפחות כמות אחת שאינה אפס."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_reserved
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__show_reserved
msgid "Pre-fill Detailed Operations"
msgstr "מילוי פעולות מפורטות מראש"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/popover_widget.xml:0
#, python-format
msgid "Preceding operations"
msgstr "פעולות קודמות"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__route_id
msgid "Preferred Route"
msgstr "מסלול מועדף"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__route_ids
msgid "Preferred Routes"
msgstr "מסלולים מועדפים"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__route_ids
msgid "Preferred route"
msgstr "מסלול מועדף"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"Press the CREATE button to define quantity for each product in your stock or"
" import them from a spreadsheet throughout Favorites"
msgstr ""
"לחץ על הלחצן \"צור\" כדי להגדיר כמות עבור כל מוצר במלאי שלך או לייבא אותם "
"מגיליון אלקטרוני דרך מועדפים"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Print"
msgstr "הדפס"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__print_label
msgid "Print Label"
msgstr "הדפס תווית"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/report_stock_reception.xml:0
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#, python-format
msgid "Print Labels"
msgstr "הדפס תוויות"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__printed
msgid "Printed"
msgstr "הודפס"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__priority
#: model:ir.model.fields,field_description:stock.field_stock_picking__priority
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__sequence
msgid "Priority"
msgstr "קְדִימוּת"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__delay_alert_date
msgid "Process at this date to be on time"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Process operations faster with barcodes"
msgstr "בצע פעולות מהר יותר עם ברקודים"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Process operations in wave transfers"
msgstr "תהליך פעולות בהעברות גלים"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Process transfers in batch per worker"
msgstr "בצע העברות בקבוצה לפי עובד"

#. module: stock
#: model:ir.model,name:stock.model_procurement_group
#: model:ir.model.fields,field_description:stock.field_stock_move__group_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__group_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__group_id
msgid "Procurement Group"
msgstr "קבוצת רכש"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.procurement_group_form_view
msgid "Procurement group"
msgstr "קבוצת רכש"

#. module: stock
#: model:ir.actions.server,name:stock.ir_cron_scheduler_action_ir_actions_server
#: model:ir.cron,cron_name:stock.ir_cron_scheduler_action
#: model:ir.cron,name:stock.ir_cron_scheduler_action
msgid "Procurement: run scheduler"
msgstr "רכש: הפעל מתזמן"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__produce_line_ids
msgid "Produce Line"
msgstr "שורת ייצור"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Produced Qty"
msgstr "כמות מיוצרת"

#. module: stock
#: model:ir.model,name:stock.model_product_product
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__product_id
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__product_id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_id
#: model:ir.model.fields,field_description:stock.field_stock_move__product_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__product_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__product_id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__product_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_id
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__product_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__product_id
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__product_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__product_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__product_id
#: model:ir.model.fields,field_description:stock.field_stock_track_line__product_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__product_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__product_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Product"
msgstr "מוצר"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__products_availability
msgid "Product Availability"
msgstr "זמינות המוצר"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__product_capacity_ids
msgid "Product Capacity"
msgstr "קיבולת מוצר"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__categ_ids
#: model:ir.ui.menu,name:stock.menu_product_category_config_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Product Categories"
msgstr "קטגוריות מוצרים"

#. module: stock
#: model:ir.model,name:stock.model_product_category
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__category_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_categ_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_category_id
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Product Category"
msgstr "קטגורית מוצר"

#. module: stock
#: model:ir.actions.report,name:stock.label_product_product
msgid "Product Label (ZPL)"
msgstr "תווית מוצר (ZPL)"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_label_product_product_view
msgid "Product Label Report"
msgstr "דו\"ח תווית מוצר (ZPL)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
msgid "Product Lots Filter"
msgstr "מסנן אצוות מוצרים"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_move_line_action
#: model:ir.ui.menu,name:stock.stock_move_line_menu
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
msgid "Product Moves"
msgstr "תנועות מוצר"

#. module: stock
#: model:ir.model,name:stock.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "תנועות המוצר (תנועת שורת מלאי)"

#. module: stock
#: model:ir.model,name:stock.model_product_packaging
msgid "Product Packaging"
msgstr "אריזת מוצר"

#. module: stock
#: model:ir.actions.report,name:stock.label_product_packaging
msgid "Product Packaging (ZPL)"
msgstr "אריזת המוצר (ZPL)"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_product_packagings
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Product Packagings"
msgstr "אריזות מוצר"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Product Quantity Confirmed"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Product Quantity Updated"
msgstr "כמות המוצר עודכנה"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/js/report_stock_forecasted.js:0
#: model:ir.model,name:stock.model_product_replenish
#, python-format
msgid "Product Replenish"
msgstr "רענון מלאי מוצר"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_stock_rule
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rules_report
msgid "Product Routes Report"
msgstr "דוח מסלולי מוצר"

#. module: stock
#: model:ir.model,name:stock.model_product_template
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_move__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_tmpl_id
msgid "Product Template"
msgstr "תבנית מוצר "

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__product_tmpl_id
msgid "Product Tmpl"
msgstr "תבנית מוצר"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap__tracking
msgid "Product Tracking"
msgstr "מעקב אחר מוצר"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__detailed_type
#: model:ir.model.fields,field_description:stock.field_product_template__detailed_type
#: model:ir.model.fields,field_description:stock.field_stock_move__product_type
msgid "Product Type"
msgstr "סוג מוצר"

#. module: stock
#: model:ir.model,name:stock.model_uom_uom
msgid "Product Unit of Measure"
msgstr "יחידת מידה של מוצר"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_product_normal_action
#: model:ir.ui.menu,name:stock.product_product_menu
msgid "Product Variants"
msgstr "וריאנטים של מוצר"

#. module: stock
#: code:addons/stock/report/product_label_report.py:0
#, python-format
msgid "Product model not defined, Please contact your administrator."
msgstr "דגם המוצר לא מוגדר, אנא פנה למנהל המערכת שלך."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid ""
"Product this lot/serial number contains. You cannot change it anymore if it "
"has already been moved."
msgstr ""
"מוצר זה מכיל מספר סידורי/ אצווה . לא ניתן לשנות אותו אם הוא כבר הועבר."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_uom_name
msgid "Product unit of measure label"
msgstr "תווית יחידת מידה של המוצר"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__has_tracking
msgid "Product with Tracking"
msgstr "מוצר עם מעקב"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__production
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Production"
msgstr "ייצור"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__property_stock_production
#: model:ir.model.fields,field_description:stock.field_product_template__property_stock_production
msgid "Production Location"
msgstr "מיקום הייצור"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Production Locations"
msgstr ""

#. module: stock
#: code:addons/stock/wizard/stock_quantity_history.py:0
#: model:ir.actions.act_window,name:stock.act_product_location_open
#: model:ir.actions.act_window,name:stock.product_template_action_product
#: model:ir.model.fields,field_description:stock.field_stock_location_route__product_ids
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__product_ids
#: model:ir.ui.menu,name:stock.menu_product_in_config_stock
#: model:ir.ui.menu,name:stock.menu_product_variant_config_stock
#: model:ir.ui.menu,name:stock.menu_stock_inventory_control
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#, python-format
msgid "Products"
msgstr "מוצרים"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__products_availability_state
msgid "Products Availability State"
msgstr "מצב זמינות מוצרים"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__priority
msgid ""
"Products will be reserved first for the transfers with the highest "
"priorities."
msgstr "מוצרים ישמרו תחילה להעברות עם סדר העדיפויות הגבוה ביותר."

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Products: %(location)s"
msgstr ""

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__group_propagation_option__propagate
msgid "Propagate"
msgstr "הפץ"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__propagate_cancel
msgid "Propagate cancel and split"
msgstr "הפץ ביטול ופיצול"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Propagation"
msgstr "ריבוי"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__group_propagation_option
msgid "Propagation of Procurement Group"
msgstr "ריבוי קבוצות רכש"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__propagate_carrier
msgid "Propagation of carrier"
msgstr ""

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__action__pull_push
msgid "Pull & Push"
msgstr "משוך ודחוף"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__action__pull
msgid "Pull From"
msgstr "משוך מ"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Pull Rule"
msgstr "כלל משיכה "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Push Rule"
msgstr "כלל דחיפה"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__action__push
msgid "Push To"
msgstr "דחוף ל"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Put in Pack"
msgstr "הכנס לחבילה "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Put your products in packs (e.g. parcels, boxes) and track them"
msgstr "שים את המוצרים שלך בחבילות (למשל צרורות, קופסאות) ועקוב אחריהם"

#. module: stock
#: model:ir.model,name:stock.model_stock_putaway_rule
msgid "Putaway Rule"
msgstr "כלל פיזור באיתורי אחסון"

#. module: stock
#: code:addons/stock/models/product.py:0
#: model:ir.actions.act_window,name:stock.category_open_putaway
#: model:ir.actions.act_window,name:stock.location_open_putaway
#: model:ir.model.fields,field_description:stock.field_product_category__putaway_rule_ids
#: model:ir.model.fields,field_description:stock.field_product_product__putaway_rule_ids
#: model:ir.model.fields,field_description:stock.field_stock_location__putaway_rule_ids
#: model:ir.ui.menu,name:stock.menu_putaway
#: model_terms:ir.ui.view,arch_db:stock.product_category_form_view_inherit
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_product_view_form_easy_inherit_stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
#, python-format
msgid "Putaway Rules"
msgstr "כללי פיזור באיתורי אחסון"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Putaway:"
msgstr "פיזור באיתורי אחסון:"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_putaway_tree
msgid "Putaways Rules"
msgstr "כללי פיזור באיתורי אחסון"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_orderpoint_qty_multiple_check
msgid "Qty Multiple must be greater than or equal to zero."
msgstr "כפולת כמות חייבת להיות גדולה או שווה לאפס."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_quality_control
msgid "Quality"
msgstr "איכות"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Quality Control"
msgstr "בקרת איכות"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_qc_stock_loc_id
msgid "Quality Control Location"
msgstr "נקודות בקרת איכות"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_quality_control_worksheet
msgid "Quality Worksheet"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_location__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_request_count__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__quant_ids
msgid "Quant"
msgstr "מנה"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"Quants are auto-deleted when appropriate. If you must manually delete them, "
"please ask a stock manager to do it."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Quant's creation is restricted, you can't do this operation."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Quant's editing is restricted, you can't do this operation."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Quantities Already Set"
msgstr "כמויות שהוגדרו"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Quantities To Reset"
msgstr "כמויות לאפס"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__quantity
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__product_qty
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__product_qty
#: model:ir.model.fields,field_description:stock.field_stock_quant__quantity
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__quantity
#: model:ir.model.fields,field_description:stock.field_stock_scrap__scrap_qty
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__quantity
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__quantity
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__quantity
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Quantity"
msgstr "כמות"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Quantity :"
msgstr "כמות :"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__quantity_done
#: model_terms:ir.ui.view,arch_db:stock.view_move_kandan
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree_detailed
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_kanban
msgid "Quantity Done"
msgstr "כמות שבוצעה"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "Quantity Multiple"
msgstr "כפולת כמות"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__qty_available
#: model:ir.model.fields,field_description:stock.field_product_template__qty_available
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form_editable
msgid "Quantity On Hand"
msgstr "כמות במלאי"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__reserved_availability
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form_editable
msgid "Quantity Reserved"
msgstr "כמות שמורה "

#. module: stock
#: code:addons/stock/wizard/stock_change_product_qty.py:0
#, python-format
msgid "Quantity cannot be negative."
msgstr "כמות לא יכולה להיות שלילית."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__is_outdated
msgid "Quantity has been moved since last count"
msgstr "הכמות הועברה מאז הספירה האחרונה"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__availability
msgid "Quantity in stock that can still be reserved for this move"
msgstr "כמות במלאי שעדיין ניתן לשמור עבור תנועה זו"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__product_qty
msgid "Quantity in the default UoM of the product"
msgstr "כמות ביחידת מידה ברירת מחדל של המוצר"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__incoming_qty
msgid ""
"Quantity of planned incoming products.\n"
"In a context with a single Stock Location, this includes goods arriving to this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods arriving to the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods arriving to any Stock Location with 'internal' type."
msgstr ""
"כמות מתוכננת של מוצרים נכנסים.\n"
"בהקשר עם מיקום של אכסון מלאי מסוים, זה כולל טובין המגיעים למקום זה, או לכל אחד מתתי המלאים הקשורים אליו.\n"
"בהקשר עם מחסן מסוים, זה כולל סחורות המגיעות אל מיקום המלאי של מחסן זה, או של כל אחד מתתי המיקומים הקשורים אליו.\n"
"אחרת, זה כולל סחורות המגיעות לכל מקום במלאי עם סוג 'פנימי'."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__outgoing_qty
msgid ""
"Quantity of planned outgoing products.\n"
"In a context with a single Stock Location, this includes goods leaving this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods leaving the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods leaving any Stock Location with 'internal' type."
msgstr ""
"כמות מתוכננת של מוצרים יוצאים.\n"
"בהקשר עם מיקום של אכסון מלאי מסוים, זה כולל סחורות עוזבות ממקום זה, או לכל אחד מתתי הסחורות הקשורות אליו.\n"
" בהקשר עם מחסן מסוים, זה כולל את הסחורות שעוזבות את מיקום המלאי של מחסן זה, או של כל אחד מתתי המיקומים הקשורים אליו.\n"
" אחרת, זה כולל סחורות המגיעות לכל מקום במלאי עם סוג 'פנימי'."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__quantity
msgid ""
"Quantity of products in this quant, in the default unit of measure of the "
"product"
msgstr "כמות המוצרים במנה זו,  ברירת המחדל של יחידת מידה של המוצר"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__reserved_quantity
msgid ""
"Quantity of reserved products in this quant, in the default unit of measure "
"of the product"
msgstr "כמות המוצרים השמורים במנה זו, ברירת המחדל של יחידת מידה של המוצר"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_capacity_positive_quantity
msgid "Quantity should be a positive number."
msgstr "הכמות צריכה להיות מספר חיובי."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__reserved_availability
msgid "Quantity that has already been reserved for this move"
msgstr "כמות שכבר שמורה לתנועה זו"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_label_layout__picking_quantity
msgid "Quantity to print"
msgstr "כמות להדפסה"

#. module: stock
#: model:ir.model,name:stock.model_stock_quant
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__quant_ids
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Quants"
msgstr "אנליסטים כמותיים"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Quants cannot be created for consumables or services."
msgstr "לא ניתן ליצור מנות יצור עבור חומרים מתכלים או שירותים."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__assigned
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Ready"
msgstr "מוכן"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_qty
msgid "Real Quantity"
msgstr "כמות אמיתית"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_qty
msgid "Real Reserved Quantity"
msgstr "כמות שמורה בפועל"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__code__incoming
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Receipt"
msgstr "קבלה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__reception_route_id
msgid "Receipt Route"
msgstr "מסלול קבלה"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#: model:stock.picking.type,name:stock.chi_picking_type_in
#: model:stock.picking.type,name:stock.picking_type_in
#, python-format
msgid "Receipts"
msgstr "קבלות"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Receive From"
msgstr "קבל מ"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__reception_steps__one_step
msgid "Receive goods directly (1 step)"
msgstr "קלוט סחורה ישירות (צעד 1)"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__reception_steps__two_steps
msgid "Receive goods in input and then stock (2 steps)"
msgstr "קלוט סחורה באיזור קליטה והעבר למלאי במחסן  (2 צעדים)"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__reception_steps__three_steps
msgid "Receive goods in input, then quality and then stock (3 steps)"
msgstr "קלוט סחורה באיזור קליטה, בצע בדיקת איכות, העבר למלאי במחסן (3 צעדים)"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Receive in 1 step (stock)"
msgstr "קלוט בצעד 1 (אחסון)"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Receive in 2 steps (input + stock)"
msgstr "קלוט ב 2 צעדים (קליטה + אחסון)"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Receive in 3 steps (input + quality + stock)"
msgstr "קלוט ב 3 צעדים (קליטה + בדיקת איכות + אחסון)"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Received Qty"
msgstr "כמות שהתקבלה"

#. module: stock
#: model:ir.actions.client,name:stock.stock_reception_action
#: model:ir.actions.report,name:stock.stock_reception_report_action
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_reception_report
msgid "Reception Report"
msgstr "דוח קבלה"

#. module: stock
#: model:ir.actions.report,name:stock.label_picking
msgid "Reception Report Label"
msgstr "תווית דוח קבלה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__name
#: model:ir.model.fields,field_description:stock.field_stock_move__reference
#: model:ir.model.fields,field_description:stock.field_stock_move_line__reference
#: model:ir.model.fields,field_description:stock.field_stock_picking__name
#: model:ir.model.fields,field_description:stock.field_stock_scrap__name
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
msgid "Reference"
msgstr "מזהה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__sequence_id
msgid "Reference Sequence"
msgstr "רצף מזהה"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_picking_name_uniq
msgid "Reference must be unique per company!"
msgstr "המזהה חייב להיות ייחודי לכל חברה!"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__origin
msgid "Reference of the document"
msgstr "מזהה למסמך"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
msgid "Reference:"
msgstr "מזהה:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_kandan
msgid "Register lots, packs, location"
msgstr "רשום אצווה, חבילות, מיקום "

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__stock_move_ids
msgid "Related Stock Moves"
msgstr "תנועות מלאי משויכות"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Remaining parts of picking partially processed"
msgstr "החלקים הנותרים של הליקוט מעובדים חלקית"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_removal
msgid "Removal"
msgstr "ניפוק"

#. module: stock
#: model:ir.model,name:stock.model_product_removal
#: model:ir.model.fields,field_description:stock.field_stock_location__removal_strategy_id
msgid "Removal Strategy"
msgstr "אסטרטגית ניפוק"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Removal strategy %s not implemented."
msgstr "אסטרטגית ניפוק %s לא מיושם."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__reordering_max_qty
#: model:ir.model.fields,field_description:stock.field_product_template__reordering_max_qty
msgid "Reordering Max Qty"
msgstr "כלל רענון מלאי כמות מקסימלית"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__reordering_min_qty
#: model:ir.model.fields,field_description:stock.field_product_template__reordering_min_qty
msgid "Reordering Min Qty"
msgstr "כלל רענון מלאי כמות מינימלית"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Reordering Rule"
msgstr "כלל רענון מלאי"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_orderpoint
#: model:ir.model.fields,field_description:stock.field_product_product__nbr_reordering_rules
#: model:ir.model.fields,field_description:stock.field_product_template__nbr_reordering_rules
#: model:ir.ui.menu,name:stock.menu_reordering_rules_config
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable_config
msgid "Reordering Rules"
msgstr "כללי רענון מלאי"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Reordering Rules Search"
msgstr "חיפוש כללי רענון מלאי"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/report_stock_forecasted.xml:0
#: code:addons/stock/static/src/xml/report_stock_forecasted.xml:0
#: model:ir.actions.act_window,name:stock.action_product_replenish
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_product_view_form_easy_inherit_stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
#, python-format
msgid "Replenish"
msgstr "חדש מלאי"

#. module: stock
#: model:stock.location.route,name:stock.route_warehouse0_mto
msgid "Replenish on Order (MTO)"
msgstr "חדש מלאי בהזמנה (הכן להזמנה)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
msgid "Replenish wizard"
msgstr "אשף רענון מלאי"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_orderpoint_replenish
#: model:ir.actions.server,name:stock.action_replenishment
#: model:ir.ui.menu,name:stock.menu_reordering_rules_replenish
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Replenishment"
msgstr "עיתוד מלאי"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_replenishment_info
msgid "Replenishment Information"
msgstr "מידע על עיתוד מלאי"

#. module: stock
#: code:addons/stock/models/stock_orderpoint.py:0
#, python-format
msgid "Replenishment Information for %s in %s"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_orderpoint.py:0
#, python-format
msgid "Replenishment Report"
msgstr "דו\"ח עיתוד מלאי"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Replenishment Report Search"
msgstr "חיפוש דו\"ח עיתוד מלאי"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_search
msgid "Report Quantity"
msgstr "דווח כמות"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_warehouse_report
msgid "Reporting"
msgstr "דו\"חות"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_request_count
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Request a Count"
msgstr "בקש ספירה"

#. module: stock
#: model:res.groups,name:stock.group_stock_sign_delivery
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Require a signature on your delivery orders"
msgstr "דרוש חתימה על הזמנות המשלוח שלך"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__reservation_method
msgid "Reservation Method"
msgstr "שיטת הזמנה"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Reservations"
msgstr "הזמנות"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Reserve"
msgstr "שמירה"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_category__packaging_reserve_method__full
msgid "Reserve Only Full Packagings"
msgstr "אריזות שמורות"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_category__packaging_reserve_method
msgid ""
"Reserve Only Full Packagings: will not reserve partial packagings. If customer orders 2 pallets of 1000 units each and you only have 1600 in stock, then only 1000 will be reserved\n"
"Reserve Partial Packagings: allow reserving partial packagings. If customer orders 2 pallets of 1000 units each and you only have 1600 in stock, then 1600 will be reserved"
msgstr ""
"שמור רק אריזות מלאות: לא ישמור אריזות חלקיות. אם הלקוח מזמין 2 משטחים של 1000 יחידות כל אחד ויש לך רק 1600 במלאי, אז רק 1000 יישמרו\n"
"שמירת אריזות חלקיות: לאפשר שמירת אריזות חלקיות. אם הלקוח מזמין 2 משטחים של 1000 יחידות כל אחד ויש לך רק 1600 במלאי, אז 1600 יישמרו"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__packaging_reserve_method
msgid "Reserve Packagings"
msgstr "אריזות שמורות"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_category__packaging_reserve_method__partial
msgid "Reserve Partial Packagings"
msgstr "אריזות לא שלמות "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Reserve before scheduled date"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_uom_qty
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__assigned
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
msgid "Reserved"
msgstr "שמור"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__reserved_quantity
msgid "Reserved Quantity"
msgstr "כמות שמורה"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Reserved from stock"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid "Reserving a negative quantity is not allowed."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__responsible_id
#: model:ir.model.fields,field_description:stock.field_product_template__responsible_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__user_id
msgid "Responsible"
msgstr "אחראי"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_user_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_user_id
msgid "Responsible User"
msgstr "משתמש אחראי"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Resupply"
msgstr "אספקה מחדש"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__resupply_wh_ids
msgid "Resupply From"
msgstr "אספקה מחדש מ"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__resupply_route_ids
msgid "Resupply Routes"
msgstr "מסלולי אספקה מחדש "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
msgid "Return"
msgstr "החזר"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__location_id
msgid "Return Location"
msgstr "מיקום החזרה"

#. module: stock
#: model:ir.model,name:stock.model_stock_return_picking
msgid "Return Picking"
msgstr "החזר ליקוט"

#. module: stock
#: model:ir.model,name:stock.model_stock_return_picking_line
msgid "Return Picking Line"
msgstr "החזר שורת ליקוט"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__return_type_id
msgid "Return Type"
msgstr "סוג החזרה"

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:0
#, python-format
msgid "Return of %s"
msgstr "החזרה של %s"

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:0
#, python-format
msgid "Returned Picking"
msgstr "ליקוט שחזר"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Returns"
msgstr "החזרות"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_quant_package__package_use__reusable
msgid "Reusable Box"
msgstr "קופסה לשימוש חוזר "

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_package__package_use
msgid ""
"Reusable boxes are used for batch picking and emptied afterwards to be reused. In the barcode application, scanning a reusable box will add the products in this box.\n"
"        Disposable boxes aren't reused, when scanning a disposable box in the barcode application, the contained products are added to the transfer."
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.act_stock_return_picking
msgid "Reverse Transfer"
msgstr "החזר העברה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__name
#: model:ir.model.fields,field_description:stock.field_stock_rule__route_id
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Route"
msgstr "מסלול"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__route_company_id
msgid "Route Company"
msgstr "מסלול חברה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__route_sequence
msgid "Route Sequence"
msgstr "רצף מסלול"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_routes_form
#: model:ir.actions.server,name:stock.action_open_routes
#: model:ir.model.fields,field_description:stock.field_product_category__route_ids
#: model:ir.model.fields,field_description:stock.field_product_packaging__route_ids
#: model:ir.model.fields,field_description:stock.field_product_product__route_ids
#: model:ir.model.fields,field_description:stock.field_product_template__route_ids
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__route_ids
#: model:ir.ui.menu,name:stock.menu_routes_config
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_tree
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Routes"
msgstr "מסלולים"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__has_available_route_ids
#: model:ir.model.fields,field_description:stock.field_product_template__has_available_route_ids
msgid "Routes can be selected on this product"
msgstr "ניתן לבחור מסלולים במוצר זה"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__resupply_wh_ids
msgid ""
"Routes will be created automatically to resupply this warehouse from the "
"warehouses ticked"
msgstr ""
"מסלולים ייווצרו באופן אוטומטי כדי לספק מחדש למחסן זה מהמחסנים המסומנים"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__resupply_route_ids
msgid ""
"Routes will be created for these resupply warehouses and you can select them"
" on products and product categories"
msgstr ""
"מסלולים ייווצרו עבור מחסני אספקה מחדש אלו וניתן לבחור אותם במוצרים "
"ובקטגוריות מוצרים"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__rule_message
msgid "Rule Message"
msgstr "הודעת כלל"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_rules_form
#: model:ir.model.fields,field_description:stock.field_stock_location_route__rule_ids
#: model:ir.ui.menu,name:stock.menu_action_rules_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_tree
msgid "Rules"
msgstr "כללים"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Rules on Categories"
msgstr "כללים על קטגוריות"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Rules on Products"
msgstr "כללים על מוצרים"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__rule_ids
msgid "Rules used"
msgstr "כללים בשימוש"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_procurement_compute
#: model:ir.ui.menu,name:stock.menu_procurement_compute
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_compute_wizard
msgid "Run Scheduler"
msgstr "הפעל מתזמן"

#. module: stock
#: model:ir.model,name:stock.model_stock_scheduler_compute
msgid "Run Scheduler Manually"
msgstr "הפעל מתזמן ידנית"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "Run the scheduler"
msgstr "הפעל את המתזמן"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_sms
msgid "SMS Confirmation"
msgstr "אישור SMS"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_has_sms_error
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_has_sms_error
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_has_sms_error
msgid "SMS Delivery error"
msgstr "שגיאה בשליחת SMS"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid "STANDARD TERMS AND CONDITIONS OF SALE"
msgstr "תנאים ותנאי מכירה סטנדרטיים"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "Sales History"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__date_planned
#: model:ir.model.fields,field_description:stock.field_stock_picking__scheduled_date
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_date
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Scheduled Date"
msgstr "תאריך מתוזמן"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__date
msgid "Scheduled date until move is done, then date of actual move processing"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Scheduled or processing date"
msgstr "תאריך מתוזמן או תאריך עיבוד"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__scheduled_date
msgid ""
"Scheduled time for the first part of the shipment to be processed. Setting "
"manually a value here would set it as expected date for all the stock moves."
msgstr ""
"קבע זמן עיבוד עבור החלק הראשון של המשלוח. הגדרה ידנית של ערך, תגדיר תאריך זה"
" כצפוי עבור כל המהלכים במלאי."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model,name:stock.model_stock_scrap
#: model:ir.model.fields,field_description:stock.field_stock_move__scrap_ids
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__scrap_id
#: model:ir.ui.menu,name:stock.menu_stock_scrap
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view2
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#, python-format
msgid "Scrap"
msgstr "פסול"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap__scrap_location_id
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
msgid "Scrap Location"
msgstr "מיקום פסולים"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap__move_id
msgid "Scrap Move"
msgstr "תנועת פסילה"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_scrap
msgid "Scrap Orders"
msgstr "הוראות פסילה"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_stock_scrap
msgid "Scrap products"
msgstr "מוצרים פסולים"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__scrapped
msgid "Scrapped"
msgstr "נפסלו"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_stock_scrap
msgid ""
"Scrapping a product will remove it from your stock. The product will\n"
"                end up in a scrap location that can be used for reporting purpose."
msgstr ""
"גריטה של מוצר תסיר אותו מהמלאי שלך. המוצר יהיה\n"
"להסתיים במיקום גרוטאות שניתן להשתמש בו למטרת דיווח."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Scraps"
msgstr "פסולים"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Search Procurement"
msgstr "חפש רכש"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
msgid "Search Scrap"
msgstr "חפש פסולים"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__product_categ_id
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__product_category_id
msgid "Select category for the current product"
msgstr "בחר קטגוריה למוצר הנוכחי"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Select the places where this route can be selected"
msgstr "בחר את המקומות שבהם ניתן לבחור במסלול זה"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner__picking_warn
#: model:ir.model.fields,help:stock.field_res_users__picking_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"בחירה באפשרות \"אזהרה\" תודיע למשתמש על ידי הודעה,בחירת \"הודעת חסימה\" "
"תתריע על חריגה בהודעה ותחסום את ההתקדמות בתהליך. ההודעה צריכה להיכתב בשדה "
"הבא."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Sell and purchase products in different units of measure"
msgstr "מכור ורכוש מוצרים ביחידות מידה שונות"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Send an automatic confirmation SMS Text Message when Delivery Orders are "
"done"
msgstr "שלח הודעת טקסט אוטומטית ב- SMS לאחר ביצוע הזמנות משלוח"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Send an automatic confirmation email when Delivery Orders are done"
msgstr "שלח הודעת אישור אוטומטית לאחר ביצוע הזמנות משלוח"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_lead_mass_mail
msgid "Send email"
msgstr "שלח דוא\"ל"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__delivery_steps__pick_ship
msgid "Send goods in output and then deliver (2 steps)"
msgstr "שלח פריטים לאיזור משלוח ולאחר מכן שלח ללקוח (2 צעדים)"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__9
msgid "September"
msgstr "ספטמבר"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model.fields,field_description:stock.field_stock_location_route__sequence
#: model:ir.model.fields,field_description:stock.field_stock_move__sequence
#: model:ir.model.fields,field_description:stock.field_stock_package_type__sequence
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__sequence
#: model:ir.model.fields,field_description:stock.field_stock_rule__sequence
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__sequence
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
#, python-format
msgid "Sequence"
msgstr "רצף"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Sequence in"
msgstr "רצף נכנס"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Sequence internal"
msgstr "רצף פנימי"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Sequence out"
msgstr "רצף יוצא"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Sequence packing"
msgstr "רצף אריזה"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Sequence picking"
msgstr "רצף הליקוט"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Sequence return"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__lot_ids
msgid "Serial Numbers"
msgstr "מספרים סידוריים"

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid ""
"Serial number (%s) already exists in location(s): %s. Please correct the "
"serial number encoded."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"Serial number (%s) is not located in %s, but is located in location(s): %s.\n"
"\n"
"Please correct this to prevent inconsistent data."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"Serial number (%s) is not located in %s, but is located in location(s): %s.\n"
"\n"
"Source location for this move will be changed to %s"
msgstr ""

#. module: stock
#: model:ir.actions.server,name:stock.action_view_set_quants_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Set"
msgstr "הגדר"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_request_count__set_count__set
msgid "Set Current Value"
msgstr "הגדר ערך נוכחי"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set Warehouse Routes"
msgstr "הגדר מסלולי מחסן"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_category__removal_strategy_id
msgid ""
"Set a specific removal strategy that will be used regardless of the source location for this product category.\n"
"\n"
"FIFO: products/lots that were stocked first will be moved out first.\n"
"LIFO: products/lots that were stocked last will be moved out first.\n"
"Closet location: products/lots closest to the target location will be moved out first.\n"
"FEFO: products/lots with the closest removal date will be moved out first (the availability of this method depends on the \"Expiration Dates\" setting)."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set expiration dates on lots &amp; serial numbers"
msgstr "קבע תאריכי תפוגה באצוות ובמספרים סידוריים"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set owner on stored products"
msgstr "הגדר בעלים עבור מוצרים מאוחסנים"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set product attributes (e.g. color, size) to manage variants"
msgstr "הגדר תכונות מוצר (למשל צבע, גודל) כדי לנהל וריאנטים"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Set quantities"
msgstr "הגדר כמויות"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__location_id
msgid ""
"Sets a location if you produce at a fixed location. This can be a partner "
"location if you subcontract the manufacturing operations."
msgstr ""
"מגדיר מיקום אם אתה מייצר במיקום קבוע. זה יכול להיות מיקום של ספק אם קבלן "
"משנה מבצע את פעולות הייצור."

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_config_settings
#: model:ir.ui.menu,name:stock.menu_stock_general_settings
msgid "Settings"
msgstr "הגדרות"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__posy
msgid "Shelves (Y)"
msgstr "מדפים (Y)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Shipments"
msgstr "משלוחים"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Shipping"
msgstr "משלוח"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Shipping Connectors"
msgstr "חיבורים לחברות שילוח"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_type
msgid "Shipping Policy"
msgstr "מדיניות משלוח"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Shipping connectors allow to compute accurate shipping costs, print shipping"
" labels and request carrier picking at your warehouse to ship to the "
"customer. Apply shipping connector from delivery methods."
msgstr ""
"החיבורים לחברות שילוח מאפשרים לחשב עלויות משלוח מדויקות, תוויות משלוח הדפסה "
"ובקשות לאיסוף מהמחסן שלך כדי לשלוח ללקוח. החל קישור עם חברת שילוח מתוך שיטות"
" שילוח."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__code
msgid "Short Name"
msgstr "שם קצר"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__code
msgid "Short name used to identify your warehouse"
msgstr "שם קצר המשמש לזהות את המחסן שלך"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_allocation
msgid "Show Allocation"
msgstr "הצג הקצאה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_check_availability
msgid "Show Check Availability"
msgstr "הצג בדוק זמינות"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_operations
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__show_operations
msgid "Show Detailed Operations"
msgstr "הצג פעולות מפורטות"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__show_forecasted_qty_status_button
#: model:ir.model.fields,field_description:stock.field_product_template__show_forecasted_qty_status_button
msgid "Show Forecasted Qty Status Button"
msgstr "הצג לחצן תחזית סטטוס כמות"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__show_lots_m2o
msgid "Show Lots M2O"
msgstr "הצג אצוות רבים ליחיד"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__show_lots_text
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_lots_text
msgid "Show Lots Text"
msgstr "הצג טקסט אצוות"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_mark_as_todo
msgid "Show Mark As Todo"
msgstr "הצג את סומן לביצוע"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__show_on_hand_qty_status_button
#: model:ir.model.fields,field_description:stock.field_product_template__show_on_hand_qty_status_button
msgid "Show On Hand Qty Status Button"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_operations
msgid "Show Operations"
msgstr "הצג פעולות"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_auto_reception_report
msgid "Show Reception Report at Validation"
msgstr "הצג דוח קבלה לאחר תיקוף"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__show_transfers
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__show_transfers
msgid "Show Transfers"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_validate
msgid "Show Validate"
msgstr "הצג אישור"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Show all records which has next action date is before today"
msgstr "הצג את כל הרשומות שתאריך הפעולה הבא שלהן הוא עד היום"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rules_report__warehouse_ids
msgid "Show the routes that apply on selected warehouses."
msgstr "הצג את המסלולים החלים על מחסנים נבחרים."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__show_info
msgid "Show warning"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Sign"
msgstr "חתימות"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_sign_delivery
#: model:ir.model.fields,field_description:stock.field_stock_picking__signature
#: model:ir.model.fields,help:stock.field_stock_picking__signature
msgid "Signature"
msgstr "חתימה"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Signed"
msgstr "נחתם "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Size"
msgstr "גודל"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Size: Length × Width × Height"
msgstr ""

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#: model:ir.actions.act_window,name:stock.action_orderpoint_snooze
#: model_terms:ir.ui.view,arch_db:stock.view_stock_orderpoint_snooze
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
#, python-format
msgid "Snooze"
msgstr "נמנם"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__snoozed_until
msgid "Snooze Date"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_orderpoint_snooze
msgid "Snooze Orderpoint"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__predefined_date
msgid "Snooze for"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__snoozed_until
msgid "Snoozed"
msgstr "מצב \"נודניק\""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid ""
"Some products of the inventory adjustment are tracked. Are you sure you "
"don't want to specify a serial or lot number for them?"
msgstr ""
"חלק מהמוצרים של התאמת המלאי במעקב. האם אתה בטוח שאתה לא מעוניין לציין מספר "
"סידורי או מספר אצווה עבורם?"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_set_view
msgid "Some selected lines already have quantities set, they will be ignored."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_adjustment_name_form_view
msgid ""
"Some selected lines don't have any quantities set, they will be ignored."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__origin
msgid "Source"
msgstr "מקור"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__origin
#: model:ir.model.fields,field_description:stock.field_stock_picking__origin
#: model:ir.model.fields,field_description:stock.field_stock_scrap__origin
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Source Document"
msgstr "מסמך מקור"

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#: model:ir.model.fields,field_description:stock.field_stock_move__location_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__location_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__location_src_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__location_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#, python-format
msgid "Source Location"
msgstr "מיקום מוצא"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Source Location:"
msgstr "מיקום מוצא:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__package_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
msgid "Source Package"
msgstr "חבילת מקור"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Source Package :"
msgstr "חבילת מקור :"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Starred"
msgstr "מסומן בכוכב"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__state
#: model:ir.model.fields,field_description:stock.field_stock_package_level__state
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_search
msgid "State"
msgstr "סטטוס"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__state
#: model:ir.model.fields,field_description:stock.field_stock_move_line__state
#: model:ir.model.fields,field_description:stock.field_stock_picking__state
#: model:ir.model.fields,field_description:stock.field_stock_scrap__state
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Status"
msgstr "סטטוס"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__activity_state
#: model:ir.model.fields,help:stock.field_stock_production_lot__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"סטטוס על בסיס פעילויות\n"
"איחור: תאריך היעד כבר חלף\n"
"היום: תאריך הפעילות הוא היום\n"
"מתוכנן: פעילויות עתידיות."

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Stock"
msgstr "מלאי"

#. module: stock
#: model:ir.model,name:stock.model_stock_assign_serial
msgid "Stock Assign Serial Numbers"
msgstr "שיוך מספרים סידוריים למלאי"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#: model_terms:ir.ui.view,arch_db:stock.view_location_tree2
msgid "Stock Location"
msgstr "מיקום מלאי"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Stock Locations"
msgstr "מיקום מלאי"

#. module: stock
#: model:ir.model,name:stock.model_stock_move
#: model:ir.model.fields,field_description:stock.field_product_product__stock_move_ids
#: model:ir.model.fields,field_description:stock.field_stock_move_line__move_id
msgid "Stock Move"
msgstr "תנועת מלאי"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_move_action
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_lines
#: model:ir.ui.menu,name:stock.stock_move_menu
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
msgid "Stock Moves"
msgstr "תנועות מלאי"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_graph
#: model_terms:ir.ui.view,arch_db:stock.view_move_pivot
msgid "Stock Moves Analysis"
msgstr "ניתוח תנועות מלאי"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#: model:ir.actions.act_window,name:stock.dashboard_open_quants
#, python-format
msgid "Stock On Hand"
msgstr "מלאי זמין"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
msgid "Stock Operation"
msgstr "פעולת מלאי"

#. module: stock
#: model:ir.model,name:stock.model_stock_package_destination
msgid "Stock Package Destination"
msgstr "איתור אריזת מלאי"

#. module: stock
#: model:ir.model,name:stock.model_stock_package_level
msgid "Stock Package Level"
msgstr "רמת אריזת מלאי"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__picking_warn
#: model:ir.model.fields,field_description:stock.field_res_users__picking_warn
msgid "Stock Picking"
msgstr "ליקוט מלאי"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__stock_quant_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_view_graph
msgid "Stock Quant"
msgstr "מנת מלאי"

#. module: stock
#: model:ir.model,name:stock.model_stock_quantity_history
msgid "Stock Quantity History"
msgstr "היסטורית כמות מלאי "

#. module: stock
#: model:ir.model,name:stock.model_report_stock_quantity
msgid "Stock Quantity Report"
msgstr "דוח כמות מלאי"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_report_reception
msgid "Stock Reception Report"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_report_stock_report_product_product_replenishment
#: model:ir.model,name:stock.model_report_stock_report_product_template_replenishment
msgid "Stock Replenishment Report"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_request_count
msgid "Stock Request an Inventory Count"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_rule
#: model:ir.model.fields,field_description:stock.field_stock_move__rule_id
msgid "Stock Rule"
msgstr "כלל מלאי "

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_rules_report
msgid "Stock Rules Report"
msgstr "דוח כללי מלאי"

#. module: stock
#: model:ir.model,name:stock.model_stock_rules_report
msgid "Stock Rules report"
msgstr "דוח כללי מלאי"

#. module: stock
#: model:ir.model,name:stock.model_stock_track_confirmation
msgid "Stock Track Confirmation"
msgstr "אישור מעקב מלאי"

#. module: stock
#: model:ir.model,name:stock.model_stock_track_line
msgid "Stock Track Line"
msgstr "עקוב אחרי קו המלאי"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_ids_without_package
msgid "Stock moves not in package"
msgstr "תנועות מלאי לא ארוזות"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that are Available (Ready to process)"
msgstr "תנועות מלאי זמינות (מוכן לעיבוד)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that are Confirmed, Available or Waiting"
msgstr "תנועות מלאי מאושרות, זמינות או ממתינות"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that have been processed"
msgstr "תנועות מלאי שעובדו"

#. module: stock
#: model:ir.model,name:stock.model_stock_package_type
msgid "Stock package type"
msgstr "סוג חבילת מלאי"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr "דוח כללי מלאי"

#. module: stock
#: model:ir.model,name:stock.model_stock_replenishment_info
msgid "Stock supplier replenishment information"
msgstr ""

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_template__detailed_type__product
#: model:ir.model.fields.selection,name:stock.selection__product_template__type__product
msgid "Storable Product"
msgstr "מנוהל מלאי"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"Storable products are physical items for which you manage the inventory "
"level."
msgstr "מוצרים מנוהלי מלאי הם מוצרים פיזיים שמנוהלים ברמת המלאי."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "Storage Capacities"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_storage_category
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_storage_categories
#: model:ir.ui.menu,name:stock.menu_storage_categoty_config
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_tree
msgid "Storage Categories"
msgstr "קטגוריות אחסון"

#. module: stock
#: model:ir.model,name:stock.model_stock_storage_category
#: model:ir.model.fields,field_description:stock.field_stock_location__storage_category_id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__storage_category_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__name
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__storage_category_id
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "Storage Category"
msgstr "קטגורית אחסון"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_storage_category_capacity
#: model:ir.model,name:stock.model_stock_storage_category_capacity
#: model:ir.model.fields,field_description:stock.field_product_product__storage_category_capacity_ids
#: model:ir.model.fields,field_description:stock.field_stock_package_type__storage_category_capacity_ids
#: model:ir.ui.menu,name:stock.menu_storage_categoty_capacity_config
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_capacity_tree
msgid "Storage Category Capacity"
msgstr "קטגוריות תכולת אחסון"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_multi_locations
msgid "Storage Locations"
msgstr "מיקום מלאי"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__group_stock_multi_locations
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Store products in specific locations of your warehouse (e.g. bins, racks) "
"and to track inventory accordingly."
msgstr ""
"אחסן מוצרים במיקומים ספציפיים של המחסן שלך (למשל, פחים, מתלים) ועקוב אחר "
"המלאי בהתאם."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__location_out_id
msgid "Store to sublocation"
msgstr "אחסון למקום משנה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__supplied_wh_id
msgid "Supplied Warehouse"
msgstr "מחסן אספקה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__procure_method
#: model:ir.model.fields,field_description:stock.field_stock_rule__procure_method
msgid "Supply Method"
msgstr "שיטת האספקה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__supplier_wh_id
msgid "Supplying Warehouse"
msgstr "אספקת מחסן"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__procure_method__make_to_stock
msgid "Take From Stock"
msgstr "קח מהמלאי"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__procure_method__mts_else_mto
msgid "Take From Stock, if unavailable, Trigger Another Rule"
msgstr "קח מהמלאי, אם אינו זמין, הפעל כלל אחר"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__procure_method
msgid ""
"Take From Stock: the products will be taken from the available stock of the source location.\n"
"Trigger Another Rule: the system will try to find a stock rule to bring the products in the source location. The available stock will be ignored.\n"
"Take From Stock, if Unavailable, Trigger Another Rule: the products will be taken from the available stock of the source location.If there is no stock available, the system will try to find a  rule to bring the products in the source location."
msgstr ""
"קח מהמלאי: המוצרים יילקחו מהמלאי הזמין של מיקום  המוצא.\n"
"הפעל כלל אחר: המערכת תנסה למצוא כלל מלאי בכדי להביא את המוצרים למיקום המוצא. היא תתעלם מהמלאי הזמין.\n"
"קח מהמלאי, אם לא זמין, הפעל כלל אחר: המוצרים יילקחו מהמלאי הזמין של מיקום  המוצא.אם אין מלאי זמין, המערכת תנסה למצוא כלל שמביא את המוצרים למיקום  המוצא."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__show_allocation
msgid ""
"Technical Field used to decide whether the button \"Allocation\" should be "
"displayed."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Technical Information"
msgstr "מידע טכני"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__warehouse_id
msgid ""
"Technical field depicting the warehouse to consider for the route selection "
"on the next procurement (if any)."
msgstr "שדה טכני המתאר את המחסן כדי לשקול בחירת תוואי לרכש הבא (אם בכלל)."

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company__internal_transit_location_id
msgid ""
"Technical field used for resupply routes between warehouses that belong to "
"this company"
msgstr "שדה טכני המשמש עבור נתיבי אספקה בין מחסנים השייכים לחברה זו"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__show_check_availability
msgid ""
"Technical field used to compute whether the button \"Check Availability\" "
"should be displayed."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__show_mark_as_todo
msgid ""
"Technical field used to compute whether the button \"Mark as Todo\" should "
"be displayed."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__show_validate
msgid ""
"Technical field used to decide whether the button \"Validate\" should be "
"displayed."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__restrict_partner_id
msgid ""
"Technical field used to depict a restriction on the ownership of quants to "
"consider when marking this move as 'done'"
msgstr ""
"שדה טכני המשמש לתיאור ההגבלה של הבעלות על הכמות לשקול בעת סימון מהלך זה "
"כ'בוצע '"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__price_unit
msgid ""
"Technical field used to record the product cost set by the user during a "
"picking confirmation (when costing method used is 'average price' or "
"'real'). Value given in company currency and in product uom."
msgstr ""
"שדה טכני המשמש לרישום עלות המוצר שנקבע על ידי המשתמש במהלך אישור האיסוף "
"(כאשר שיטת העלות היא \"מחיר ממוצע\" או \"אמיתי\"). ערך הניתן במטבע החברה וגם"
" ביחידת מידה של המוצר ."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__produce_line_ids
msgid "Technical link to see which line was produced with this. "
msgstr "קישור טכני כדי לראות איזו שורה מיוצרת עם זה."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__consume_line_ids
msgid "Technical link to see who consumed what. "
msgstr "קישור טכני כדי לראות מי צרך ומה צרך."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__product_tmpl_id
msgid "Technical: used in views"
msgstr "טכני: משמש לצפיות"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__stock_move_ids
#: model:ir.model.fields,help:stock.field_product_product__stock_quant_ids
msgid "Technical: used to compute quantities."
msgstr "טכני: משמש לחישוב כמויות."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__incoming_move_line_ids
#: model:ir.model.fields,help:stock.field_stock_location__outgoing_move_line_ids
msgid "Technical: used to compute weight."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_tmpl_id
msgid "Template"
msgstr "תבנית"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__auto
msgid ""
"The 'Manual Operation' value will create a stock move after the current one."
" With 'Automatic No Step Added', the location is replaced in the original "
"move."
msgstr ""
"הערך 'מבצע ידני' יצור תנועת העברת מלאי לאחר המלאי הנוכח. עם הערך 'אוטומטי "
"ללא הוספת צעד', המיקום יוחלף בתנועה המקורית."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"The Serial Number (%s) is already used in these location(s): %s.\n"
"\n"
"Is this expected? For example this can occur if a delivery operation is validated before its corresponding receipt operation is validated. In this case the issue will be solved automatically once all steps are completed. Otherwise, the serial number should be corrected to prevent inconsistent data."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid ""
"The backorder <a href=# data-oe-model=stock.picking data-oe-id=%d>%s</a> has"
" been created."
msgstr ""
"ההזמנה החוזרת <a href=# data-oe-model=stock.picking data-oe-id=%d>%s</a> "
"נוצרה."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_location_barcode_company_uniq
msgid "The barcode for a location must be unique per company !"
msgstr "הברקוד עבור מיקום חייב להיות ייחודי לכל חברה!"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"The client explicitly waives its own standard terms and conditions, even if "
"these were drawn up after these standard terms and conditions of sale. In "
"order to be valid, any derogation must be expressly agreed to in advance in "
"writing."
msgstr ""

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_orderpoint_product_location_check
msgid "The combination of product and location must be unique."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_production_lot.py:0
#, python-format
msgid ""
"The combination of serial number and product must be unique across a company.\n"
"Following combination contains duplicates:\n"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__company_id
msgid "The company is automatically set from your user preferences."
msgstr "החברה מוגדרת אוטומטית לפי העדפות המשתמש שלך"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"The deadline has been automatically updated due to a delay on <a href='#' "
"data-oe-model='%s' data-oe-id='%s'>%s</a>."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__delay
msgid ""
"The expected date of the created transfer will be computed based on this "
"lead time."
msgstr "התאריך הצפוי של ההעברה שנוצרה יחושב על סמך זמן אספקה ​​זה."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__sequence
msgid "The first in the sequence is the default one."
msgstr "הראשון ברצף הוא ברירת המחדל."

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "The forecasted stock on the"
msgstr "המלאי החזוי לתאריך"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_location_inventory_freq_nonneg
msgid "The inventory frequency (days) for a location must be non-negative"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_production_lot.py:0
#, python-format
msgid "The lot name must contain at least one digit."
msgstr ""

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_warehouse_name_uniq
msgid "The name of the warehouse must be unique per company!"
msgstr "שם המחסן חייב להיות ייחודי לכל חברה!"

#. module: stock
#: code:addons/stock/wizard/stock_assign_serial_numbers.py:0
#, python-format
msgid "The number of Serial Numbers to generate must be greater than zero."
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_picking_type_action
msgid ""
"The operation type system allows you to assign each stock\n"
"                operation a specific type which will alter its views accordingly.\n"
"                On the operation type you could e.g. specify if packing is needed by default,\n"
"                if it should show the customer."
msgstr ""
"סוג מערכת ההפעלה מאפשרת להקצות כל פעולת המלאי\n"
"סוג ספציפי אשר תשנה צפייה בהתאמה\n"
"בסוג ההפעלה ניתן לדוגמה: לציין אם האריזה נחוצה כברירת מחדל,\n"
"אם זה אמור להראות את הלקוח."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__package_id
msgid "The package containing this quant"
msgstr "החבילה מכילה כמות זו"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__location_id
msgid ""
"The parent location that includes this location. Example : The 'Dispatch "
"Zone' is the 'Gate 1' parent location."
msgstr ""
"מיקום ההורה הכולל מיקום זה. דוגמה: 'אזור הפתיחה' הוא מיקום ההורה 'שער 1'."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__qty_multiple
msgid ""
"The procurement quantity will be rounded up to this multiple.  If it is 0, "
"the exact quantity will be used."
msgstr "הכמות רכש יעוגלו עד מספר זה. אם זה 0 שישמש את הכמות המדויקת"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid "The product is not available in sufficient quantity"
msgstr "המוצר אינו זמין בכמות מספקת"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__inventory_quantity
msgid "The product's counted quantity."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid ""
"The quantity done for the product \"%s\" doesn't respect the rounding "
"precision defined on the unit of measure \"%s\". Please change the quantity "
"done or the rounding precision of your unit of measure."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid ""
"The requested operation cannot be processed because of a programming error "
"setting the `product_qty` field instead of the `product_uom_qty`."
msgstr ""
"לא ניתן לעבד את הפעולה המבוקשת בגלל שגיאת תכנות שהגדירה את השדה "
"'product_qty` במקום את' product_uom_qty`."

#. module: stock
#: code:addons/stock/models/stock_location.py:0
#, python-format
msgid ""
"The selected Inventory Frequency (Days) creates a date too far into the "
"future."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"The serial number has already been assigned: \n"
" Product: %s, Serial Number: %s"
msgstr ""

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_warehouse_code_uniq
msgid "The short name of the warehouse must be unique per company!"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner__property_stock_customer
#: model:ir.model.fields,help:stock.field_res_users__property_stock_customer
msgid ""
"The stock location used as destination when sending goods to this contact."
msgstr "מיקום המלאי משמש כיעד בעת שליחת סחורה לאיש קשר זה."

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner__property_stock_supplier
#: model:ir.model.fields,help:stock.field_res_users__property_stock_supplier
msgid ""
"The stock location used as source when receiving goods from this contact."
msgstr "מיקום המלאי משמש כמקור בעת קבלת סחורה מאיש קשר זה."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_id
msgid "The stock operation where the packing has been made"
msgstr "פעולת מלאי בה בוצעה אריזה"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__rule_id
msgid "The stock rule that created this stock move"
msgstr "כלל המלאי שיצר תנועת מלאי זו"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_compute_wizard
msgid ""
"The stock will be reserved for operations waiting for availability and the "
"reordering rules will be triggered."
msgstr "המלאי ישמר עבור פעולות הממתינות לזמינות ויופעלו כללי רענון מלאי."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__propagate_warehouse_id
msgid ""
"The warehouse to propagate on the created move/procurement, which can be "
"different of the warehouse this rule is for (e.g for resupplying rules from "
"another warehouse)"
msgstr ""
"במחסן הפצה שבו נוצר תנועה / רכש, יכול להיות ממחסן שונה הנכלל בכלל זה "
"(לדוגמה: עבור הכללי חידוש הספקה ממחסן אחר)"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_line_action
msgid "There's no product move yet"
msgstr "אין עדיין תנועת מוצר"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"This analysis gives you an overview of the current stock level of your "
"products."
msgstr "ניתוח זה נותן לך סקירה כללית של רמת המלאי הנוכחית של המוצרים שלך."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__name
msgid "This field will fill the packing origin and the name of its moves"
msgstr "שדה זה ימלא את מקור האריזה ואת שם המהלכים שלה"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__reservation_date
msgid ""
"This is a technical field for calculating when a move should be reserved"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__default_location_dest_id
msgid ""
"This is the default destination location when you create a picking manually "
"with this operation type. It is possible however to change it or that the "
"routes put another location. If it is empty, it will check for the customer "
"location on the partner. "
msgstr ""
"זהו מיקום היעד המוגדר כברירת מחדל בעת יצירת איסוף ידני עם סוג פעולה זה.  "
"ניתן לשנות זאת או לשבץ מסלולים למיקום אחר. אם הוא ריק, הוא יבדוק את מיקום "
"הלקוח על השותף."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__default_location_src_id
msgid ""
"This is the default source location when you create a picking manually with "
"this operation type. It is possible however to change it or that the routes "
"put another location. If it is empty, it will check for the supplier "
"location on the partner. "
msgstr ""
"זהו מיקום המקור המוגדר כברירת מחדל בעת יצירת איסוף ידנית עם סוג פעולה זה. "
"ניתן לשנות זאת או לשבץ מסלולים למיקום אחר. אם הוא ריק, הוא יבדוק את מיקום "
"הספק על השותף."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__owner_id
msgid "This is the owner of the quant"
msgstr "זה הבעלים של הכמות"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__product_uom_qty
msgid ""
"This is the quantity of products from an inventory point of view. For moves "
"in the state 'done', this is the quantity of products that were actually "
"moved. For other moves, this is the quantity of product that is planned to "
"be moved. Lowering this quantity does not generate a backorder. Changing "
"this quantity on assigned moves affects the product reservation, and should "
"be done with care."
msgstr ""
"זוהי כמות המוצרים מנקודת מבט של מלאי. עבור מהלכים שבמצב 'בוצע', זהו כמות של "
"מוצרים אשר הועברו בפועל. עבור מהלכים אחרים, זהו כמות המוצרים המתוכננים "
"להעברה. הפחתת כמות זו לא יוצרת הזמנה חוזרת. שינוי כמות זו במהלכים שהוקצו "
"משפיע על הזמנת המוצר, ויש לעשות זאת בזהירות."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__child_internal_location_ids
msgid ""
"This location (if it's internal) and all its descendants filtered by "
"type=Internal."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_location.py:0
#, python-format
msgid ""
"This location's usage cannot be changed to view as it contains products."
msgstr "השימוש במיקום זה אינו ניתן לשינוי כתצוגה מכיוון שהוא מכיל מוצרים."

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid ""
"This lot %(lot_name)s is incompatible with this product %(product_name)s"
msgstr "אצווה זו%(lot_name)sלא מותאמת עם המוצר%(product_name)s"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_action
msgid ""
"This menu gives you the full traceability of inventory\n"
"                operations on a specific product. You can filter on the product\n"
"                to see all the past or future movements for the product."
msgstr ""
"תפריט זה נותן לך מעקב מלא אחר פעולות המלאי \n"
"על מוצר מסוים. ניתן לבצע סינון על המוצר\n"
" כדי לראות את כל התנועות בעבר או התנועות העתידיות שלו."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_line_action
msgid ""
"This menu gives you the full traceability of inventory operations on a specific product.\n"
"                    You can filter on the product to see all the past movements for the product."
msgstr ""
"תפריט זה נותן לך את המעקב המלא של פעולות המלאי במוצר מסוים.\n"
"                    אתה יכול לסנן במוצר כדי לראות את כל תנועות העבר עבור המוצר."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "This note is added to delivery orders."
msgstr "הערה זו מתווספת להזמנות משלוח."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid ""
"This note is added to internal transfer orders (e.g. where to pick the "
"product in the warehouse)."
msgstr ""
"הערה זו מתווספת להזמנות העברה פנימיות (למשל מהיכן ללקט את המוצר במחסן)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid ""
"This note is added to receipt orders (e.g. where to store the product in the"
" warehouse)."
msgstr "הערה זו מתווספת להזמנות קבלה (למשל היכן לאחסן את המוצר במחסן)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
msgid ""
"This picking appears to be chained with another operation. Later, if you "
"receive the goods you are returning now, make sure to <b>reverse</b> the "
"returned picking in order to avoid logistic rules to be applied again (which"
" would create duplicated operations)"
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"This product has been used in at least one inventory movement. It is not "
"advised to change the Product Type since it can lead to inconsistencies. A "
"better solution could be to archive the product and create a new one "
"instead."
msgstr ""
"מוצר זה היה בשימוש לפחות בתנועת מלאי אחת. לא מומלץ לשנות את סוג המוצר מכיוון"
" שהוא עלול להוביל לחוסר עקביות. פתרון טוב יותר יכול להיות להעביר את המוצר "
"לארכיון וליצור חדש במקום זאת."

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"This product's company cannot be changed as long as there are quantities of "
"it belonging to another company."
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"This product's company cannot be changed as long as there are stock moves of"
" it belonging to another company."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_change_product_qty__new_quantity
msgid ""
"This quantity is expressed in the Default Unit of Measure of the product."
msgstr "כמות זו מבוטאת ביחידת מידה ברירת המחדל של המוצר."

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/js/inventory_singleton_list_controller.js:0
#, python-format
msgid "This record already exists."
msgstr "רשומה זו כבר קיימת."

#. module: stock
#: code:addons/stock/report/report_stock_reception.py:0
#, python-format
msgid ""
"This report cannot be used for done and not done transfers at the same time"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__property_stock_production
#: model:ir.model.fields,help:stock.field_product_template__property_stock_production
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated by manufacturing orders."
msgstr ""
"מיקום מלאי זה ישמש, במקום ברירת המחדל, כמיקום המקור עבור תנועות מלאי שנוצרו "
"על-ידי הוראות ייצור."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__property_stock_inventory
#: model:ir.model.fields,help:stock.field_product_template__property_stock_inventory
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated when you do an inventory."
msgstr ""
"מיקום מלאי זה ישמש, במקום ברירת המחדל, כמיקום המקור עבור תנועות מלאי שנוצרו "
"במהלך ניהול מלאי. "

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__responsible_id
#: model:ir.model.fields,help:stock.field_product_template__responsible_id
msgid ""
"This user will be responsible of the next activities related to logistic "
"operations for this product."
msgstr ""
"משתמש זה יהיה אחראי על הפעילויות הבאות הקשורות לפעולות לוגיסטיות עבור מוצר "
"זה."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_reset_view
msgid "This will discard all unapplied counts, do you want to proceed?"
msgstr ""

#. module: stock
#: model:digest.tip,name:stock.digest_tip_stock_0
#: model_terms:digest.tip,tip_description:stock.digest_tip_stock_0
msgid "Tip: Speed up inventory operations with barcodes"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__location_dest_id
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "To"
msgstr "ל"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "To Apply"
msgstr "ליישום"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__to_backorder
msgid "To Backorder"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "To Count"
msgstr "לספור"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_ready
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "To Do"
msgstr " לביצוע"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__qty_to_order
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_to_order
msgid "To Order"
msgstr "להזמין"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__to_immediate
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "To Process"
msgstr "לביצוע"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "To Reorder"
msgstr "להזמין"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "Today"
msgstr "היום"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Today Activities"
msgstr "פעילויות היום"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__total_route_ids
msgid "Total routes"
msgstr "סך המסלולים"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Traceability"
msgstr "מעקב"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/js/stock_traceability_report_widgets.js:0
#: model:ir.actions.client,name:stock.action_stock_report
#: model:ir.model,name:stock.model_stock_traceability_report
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#, python-format
msgid "Traceability Report"
msgstr "דוח מעקב"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__module_product_expiry
msgid ""
"Track following dates on lots & serial numbers: best before, removal, end of life, alert. \n"
" Such dates are set automatically at lot/serial number creation based on values set on the product (in days)."
msgstr ""
"עקוב אחר התאריכים הבאים באצוות ובמספרים הסידוריים: לשימוש לפני, ניפוק, סוף החיים, התראה.\n"
"תאריכים אלה נקבעים אוטומטית ביצירת מספר אצווה / סידורי המבוססת על ערכים שהוגדרו במוצר (בימים)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Track following dates on lots & serial numbers: best before, removal, end of"
" life, alert. Such dates are set automatically at lot/serial number creation"
" based on values set on the product (in days)."
msgstr ""
"עקוב אחר התאריכים הבאים באצוות ובמספרים הסידוריים: לשימוש לפני, ניפוק, סוף החיים, התראה.\n"
"תאריכים אלה נקבעים אוטומטית ביצירת מספר אצווה / סידורי המבוססת על ערכים שהוגדרו במוצר (בימים)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Track product location in your warehouse"
msgstr "עקוב אחר מיקום המוצר במחסן שלך"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.product_template_action_product
msgid "Track your stock quantities by creating storable products."
msgstr "עקוב אחר כמויות המלאי שלך על ידי יצירת מוצרים לאחסון."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Tracked Products in Inventory Adjustment"
msgstr "מוצרים במעקב בהתאמת מלאי"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__tracking
#: model:ir.model.fields,field_description:stock.field_product_template__tracking
#: model:ir.model.fields,field_description:stock.field_stock_move_line__tracking
#: model:ir.model.fields,field_description:stock.field_stock_quant__tracking
#: model:ir.model.fields,field_description:stock.field_stock_track_line__tracking
msgid "Tracking"
msgstr "מעקב"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__tracking_line_ids
msgid "Tracking Line"
msgstr "שורת מעקב"

#. module: stock
#: model:ir.model,name:stock.model_stock_picking
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Transfer"
msgstr "העברה"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__picking_quantity__picking
msgid "Transfer Quantities"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_tree
msgid "Transfer to"
msgstr "הועבר ל"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_all
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__delivery_ids
#: model:ir.ui.menu,name:stock.all_picking
#: model_terms:ir.ui.view,arch_db:stock.procurement_group_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "Transfers"
msgstr "העברות"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Transfers %s: Please add some items to move."
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_get_picking_type_operations
#: model_terms:ir.actions.act_window,help:stock.action_picking_form
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_all
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_backorder
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_late
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_ready
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_waiting
#: model_terms:ir.actions.act_window,help:stock.action_picking_type_list
#: model_terms:ir.actions.act_window,help:stock.stock_picking_action_picking_type
msgid "Transfers allow you to move products from one location to another."
msgstr "העברות מאפשרות לך להעביר מוצרים ממקום למקום."

#. module: stock
#: model:ir.actions.act_window,name:stock.do_view_pickings
msgid "Transfers for Groups"
msgstr "העברות לקבוצות"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid ""
"Transfers that are late on scheduled time or one of pickings will be late"
msgstr ""

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__transit
msgid "Transit Location"
msgstr "מיקום מעבר"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Transit Locations"
msgstr "מיקום מעבר"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__trigger
msgid "Trigger"
msgstr "הפעלה"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__procure_method__make_to_order
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Trigger Another Rule"
msgstr "הפעל כלל נוסף"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Trigger Another Rule If No Stock"
msgstr "הפעל כלל אחר אם אין מלאי"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/js/report_stock_forecasted.js:0
#, python-format
msgid "Try to add some incoming or outgoing transfers."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_barcode_rule__type
#: model:ir.model.fields,field_description:stock.field_product_product__type
#: model:ir.model.fields,field_description:stock.field_product_template__type
msgid "Type"
msgstr "סוג"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_code
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_code
#: model:ir.model.fields,field_description:stock.field_stock_package_level__picking_type_code
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_type_code
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__code
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
msgid "Type of Operation"
msgstr "סוג פעולה"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__activity_exception_decoration
#: model:ir.model.fields,help:stock.field_stock_production_lot__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "סוג הפעילות החריגה ברשומה."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_ups
msgid "UPS Connector"
msgstr "חיבור ל UPS"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_usps
msgid "USPS Connector"
msgstr "חיבור ל USPS"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/js/report_stock_reception.js:0
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
#, python-format
msgid "Unassign"
msgstr ""

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_traceability_report_line.xml:0
#: code:addons/stock/static/src/xml/stock_traceability_report_line.xml:0
#: code:addons/stock/static/src/xml/stock_traceability_report_line.xml:0
#: code:addons/stock/static/src/xml/stock_traceability_report_line.xml:0
#, python-format
msgid "Unfold"
msgstr "פרוס"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_production_lot__name
msgid "Unique Lot/Serial Number"
msgstr "מספר סידורי/אצווה ייחודי"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__price_unit
msgid "Unit Price"
msgstr "מחיר יחידה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__uom_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_uom
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__product_uom_name
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__product_uom_name
#: model_terms:ir.ui.view,arch_db:stock.package_level_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree_detailed
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "Unit of Measure"
msgstr "יחידת מידה"

#. module: stock
#: model:product.product,uom_name:stock.product_cable_management_box
#: model:product.template,uom_name:stock.product_cable_management_box_product_template
msgid "Units"
msgstr "יחידה"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Units Of Measure"
msgstr "יחידות מידה"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_unit_measure_stock
msgid "Units of Measure"
msgstr "יחידות מידה"

#. module: stock
#: model:ir.ui.menu,name:stock.product_uom_menu
msgid "Units of Measures"
msgstr "יחידות מידה "

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_uom_id
msgid "Unit of measure"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Unknown Pack"
msgstr "מארז לא ידוע"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Unknown stream."
msgstr "זרם לא ידוע."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Unlock"
msgstr "שחרר נעילה"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Unpack"
msgstr "פרק"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_unread
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_unread
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_unread
msgid "Unread Messages"
msgstr "הודעות שלא נקראו"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_unread_counter
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_unread_counter
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_unread_counter
msgid "Unread Messages Counter"
msgstr "מספר ההודעות שלא נקראו"

#. module: stock
#: model:ir.actions.server,name:stock.action_unreserve_picking
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Unreserve"
msgstr "בטל שמירה"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_uom
#: model:ir.ui.menu,name:stock.menu_stock_uom_form_action
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable_config
msgid "UoM"
msgstr "יחידת מידה"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_uom_categ_form_action
msgid "UoM Categories"
msgstr "קטגוריות יחידות מידה "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
msgid "Update Product Quantity"
msgstr "עדכן כמות מוצר"

#. module: stock
#: code:addons/stock/models/product.py:0
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_product_view_form_easy_inherit_stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
#, python-format
msgid "Update Quantity"
msgstr "עדכן כמות"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__priority__1
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__priority__1
msgid "Urgent"
msgstr "דחוף"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_use_existing_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking__use_existing_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__use_existing_lots
msgid "Use Existing Lots/Serial Numbers"
msgstr "השתמש במספרים סידוריים או אצוות קיימות"

#. module: stock
#: model:res.groups,name:stock.group_reception_report
msgid "Use Reception Report"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
msgid ""
"Use this assistant to replenish your stock.\n"
"                Depending on your product configuration, launching a replenishment may trigger a request for quotation,\n"
"                a manufacturing order or a transfer."
msgstr ""
"השתמש בעוזר זה כדי לחדש את המלאי שלך.\n"
"בהתאם לתצורת המוצר שלך, הפעלת חידוש מלאי עשויה לבצע בקשה להצעת מחיר,\n"
"הוראת ייצור או העברה."

#. module: stock
#: model:res.groups,name:stock.group_stock_picking_wave
msgid "Use wave pickings"
msgstr "שימוש בגלי ליקוט"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Use your own routes"
msgstr "השתמש במסלולים שלך"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Used by"
msgstr "היה בשימוש של"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__sequence
msgid "Used to order the 'All Operations' kanban view"
msgstr "משמש לסידור את \"כל הפעולות\" בתצוגת הקנבן"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_request_count__user_id
#: model:res.groups,name:stock.group_stock_user
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "User"
msgstr "משתמש"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__user_id
msgid "User assigned to do product count."
msgstr ""

#. module: stock
#: model:ir.actions.server,name:stock.action_validate_picking
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Validate"
msgstr "אשר"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/inventory_lines.xml:0
#, python-format
msgid "Validate Inventory"
msgstr "אשר את המלאי"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_variant_count
msgid "Variant Count"
msgstr "כמות וריאנטים"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Vendor"
msgstr "ספק"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__property_stock_supplier
#: model:ir.model.fields,field_description:stock.field_res_users__property_stock_supplier
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__supplier
msgid "Vendor Location"
msgstr "מיקום ספק "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Vendor Locations"
msgstr "מיקומי ספק "

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__view
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "View"
msgstr "תצוגה"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "View Diagram"
msgstr "צפה בתרשים"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__view_location_id
msgid "View Location"
msgstr "תצוגה של מיקום"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "View and allocate received quantities."
msgstr "הצג והקצה כמויות שהתקבלו"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__confirmed
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Waiting"
msgstr "ממתין"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__waiting
msgid "Waiting Another Move"
msgstr "ממתין לתנועה נוספת"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__waiting
msgid "Waiting Another Operation"
msgstr "ממתין לפעולה אחרת"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__confirmed
msgid "Waiting Availability"
msgstr "ממתין לזמינות"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Waiting Moves"
msgstr "תנועות בהמתנה"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_waiting
msgid "Waiting Transfers"
msgstr "העברות בהמתנה "

#. module: stock
#: model:ir.model,name:stock.model_stock_warehouse
#: model:ir.model.fields,field_description:stock.field_product_product__warehouse_id
#: model:ir.model.fields,field_description:stock.field_product_replenish__warehouse_id
#: model:ir.model.fields,field_description:stock.field_product_template__warehouse_id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_location__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_move__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__warehouse_id
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_warehouse_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_tree
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Warehouse"
msgstr "מחסן"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Warehouse Configuration"
msgstr "תצורת מחסן"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__warehouse_domain_ids
msgid "Warehouse Domain"
msgstr "תחום מחסן"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_warehouse_config
msgid "Warehouse Management"
msgstr "ניהול מחסן"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__warehouse_view_ids
msgid "Warehouse View"
msgstr "תצוגת מחסן"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__propagate_warehouse_id
msgid "Warehouse to Propagate"
msgstr "מחסן הפצה "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Warehouse view location"
msgstr "מיקום תצוגת מחסן"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Warehouse's Routes"
msgstr "מסלולי מחסן"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/report_stock_forecasted.xml:0
#, python-format
msgid "Warehouse:"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_warehouse_form
#: model:ir.model.fields,field_description:stock.field_stock_location_route__warehouse_ids
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__warehouse_ids
#: model:ir.ui.menu,name:stock.menu_action_warehouse_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Warehouses"
msgstr "מחסנים"

#. module: stock
#: model:ir.model,name:stock.model_stock_warn_insufficient_qty
msgid "Warn Insufficient Quantity"
msgstr "הזהר כמות לא מספקת"

#. module: stock
#: model:ir.model,name:stock.model_stock_warn_insufficient_qty_scrap
msgid "Warn Insufficient Scrap Quantity"
msgstr "הזהר כמות פסולים לא מספיקה"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#: code:addons/stock/models/stock_move.py:0
#: code:addons/stock/models/stock_move_line.py:0
#: code:addons/stock/models/stock_move_line.py:0
#: code:addons/stock/models/stock_quant.py:0
#: code:addons/stock/models/stock_scrap.py:0
#: code:addons/stock/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:stock.selection__res_partner__picking_warn__warning
#, python-format
msgid "Warning"
msgstr "אזהרה"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_partner_stock_warnings_form
msgid "Warning on the Picking"
msgstr "אזהרה בליקוט"

#. module: stock
#: code:addons/stock/models/product.py:0 code:addons/stock/models/product.py:0
#, python-format
msgid "Warning!"
msgstr "אזהרה!"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Warnings"
msgstr "אזהרות"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_warning_stock
msgid "Warnings for Stock"
msgstr "אזהרות עבור המלאי"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_picking_wave
msgid "Wave Transfers"
msgstr "העברות גלים"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__website_message_ids
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__website_message_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__website_message_ids
msgid "Website Messages"
msgstr "הודעות מאתר האינטרנט"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__website_message_ids
#: model:ir.model.fields,help:stock.field_stock_production_lot__website_message_ids
#: model:ir.model.fields,help:stock.field_stock_scrap__website_message_ids
msgid "Website communication history"
msgstr "היסטורית התקשרויות מאתר האינטרנט"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__weight_uom_name
msgid "Weight unit of measure label"
msgstr "תווית יחידת מידה של משקל"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__weight
msgid "Weighted Product"
msgstr "מוצר משוקלל"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route__warehouse_selectable
msgid ""
"When a warehouse is selected for this route, this route should be seen as "
"the default route when products pass through this warehouse."
msgstr ""

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__move_type__one
msgid "When all products are ready"
msgstr "כאשר כל המוצרים מוכנים"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route__product_selectable
msgid ""
"When checked, the route will be selectable in the Inventory tab of the "
"Product form."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route__product_categ_selectable
msgid "When checked, the route will be selectable on the Product Category."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route__packaging_selectable
msgid "When checked, the route will be selectable on the Product Packaging."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__location_in_id
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "When product arrives in"
msgstr "כאשר המוצר נכנס"

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid ""
"When products are needed in <b>%s</b>, <br/> <b>%s</b> are created from "
"<b>%s</b> to fulfill the need."
msgstr ""
"כאשר מוצרים נדרשים ב <b>%s</b>, <br/> <b>%s</b> נוצר מחדש מ <b>%s</b> כדי "
"למלא את הצורך. "

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid ""
"When products arrive in <b>%s</b>, <br/> <b>%s</b> are created to send them "
"in <b>%s</b>."
msgstr ""
"כאשר המוצרים מגיעים <b>%s</b>, <br/> <b>%s</b> נוצרים כדי לשלוח אותם "
"ב<b>%s</b>."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__is_locked
msgid ""
"When the picking is not done this allows changing the initial demand. When "
"the picking is done this allows changing the done quantities."
msgstr ""
"כאשר האיסוף לא בוצע זה מאפשר לשנות את הביקוש הראשוני. כאשר האיסוף בוצע  זה "
"מאפשר לשנות את הכמויות לביצוע."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__product_min_qty
msgid ""
"When the virtual stock goes below the Min Quantity specified for this field,"
" Odoo generates a procurement to bring the forecasted quantity to the Max "
"Quantity."
msgstr ""
"כאשר המלאי עובר מתחת לכמות המינימלית שצוינה עבור שדה זה, Odoo מייצרת רכש כדי"
" להביא את הכמות החזוי לכמות שצוינה כמות מקסימום."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__product_max_qty
msgid ""
"When the virtual stock goes below the Min Quantity, Odoo generates a "
"procurement to bring the forecasted quantity to the Quantity specified as "
"Max Quantity."
msgstr ""
"כאשר המלאי יורד מתחת לכמות המינימלית, Odoo מייצרת רכש כדי להביא את הכמות "
"החזוי לכמות שצוינה כמות מקסימום."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__propagate_carrier
msgid "When ticked, carrier of shipment will be propgated."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__propagate_cancel
msgid ""
"When ticked, if the move created by this rule is cancelled, the next move "
"will be cancelled too."
msgstr "כאשר מסומן, אם המהלך שנוצר על ידי כלל זה מבוטל, גם המהלך הבא יבוטל."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__owner_id
msgid ""
"When validating the transfer, the products will be assigned to this owner."
msgstr "בעת אישור ההעברה, המוצרים ישויכו לבעלים זה."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__owner_id
msgid ""
"When validating the transfer, the products will be taken from this owner."
msgstr "בעת אישור ההעברה המוצרים יילקחו מבעלים זה."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__additional
msgid "Whether the move was added after the picking's confirmation"
msgstr "אם הפעולה נוספה לאחר אישור האיסוף"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__width
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Width"
msgstr "רוחב"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_width
msgid "Width must be positive"
msgstr "הרוחב חייב להיות חיובי"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__wizard_id
#: model:ir.model.fields,field_description:stock.field_stock_track_line__wizard_id
msgid "Wizard"
msgstr "אשף"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "Write your SN/LN one by one or copy paste a list."
msgstr "כתוב מספר סידורי / מספר אצווה אחד אחד או העתק הדבק רשימה."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint_replenish
msgid "You are good, no replenishment to perform!"
msgstr "המצב טוב, אין עיתוד מלאי לבצע!"

#. module: stock
#: code:addons/stock/models/stock_production_lot.py:0
#, python-format
msgid ""
"You are not allowed to change the product linked to a serial or lot number "
"if some stock moves have already been created with that number. This would "
"lead to inconsistencies in your stock."
msgstr ""
"אתה לא רשאי לשנות את הקישור למספר סידורי / אצווה של המוצר אם בוצעו כבר "
"תנועות במלאי עם המספר הזה. זה יוביל חוסר עקביות במלאי שלך."

#. module: stock
#: code:addons/stock/models/stock_production_lot.py:0
#, python-format
msgid ""
"You are not allowed to create a lot or serial number with this operation "
"type. To change this, go on the operation type and tick the box \"Create New"
" Lots/Serial Numbers\"."
msgstr ""
"לא ניתן ליצור מספר סידורי או אצווה עם סוג פעולה זה. כדי לשנות זאת, עבור על "
"סוג הפעולה וסמן את התיבה \"צור מספרים סידוריים/אצוות חדשים\"."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
msgid ""
"You are trying to put products going to different locations into the same "
"package"
msgstr "אתה מנסה להכניס מוצרים שמיועדים ליעדים שונים באותה חבילה"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"You are using a unit of measure smaller than the one you are using in order "
"to stock your product. This can lead to rounding problem on reserved "
"quantity. You should use the smaller unit of measure possible in order to "
"valuate your stock or change its rounding precision to a smaller value "
"(example: 0.00001)."
msgstr ""
"אתה משתמש ביחידת מידה קטנה מזו שבה אתה משתמש להעריך את רמת המלאי של המוצר "
"שלך. זה יכול להוביל ל בעיה בעיגול על הכמות שמורה. עליך להשתמש ביחידת המדידה "
"הקטנה יותר כדי להעריך את מלאייך או לשנות את דיוק העיגול שלו לערך קטן יותר "
"(לדוגמה: 0.00001)."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_routes_form
msgid ""
"You can define here the main routes that run through\n"
"                your warehouses and that define the flows of your products. These\n"
"                routes can be assigned to a product, a product category or be fixed\n"
"                on procurement or sales order."
msgstr ""
"ניתן להגדיר כאן את המסלולים העיקריים העוברים דרך\n"
"             המחסנים שלך ומגדירים את זרימת המוצרים שלך. אלה\n"
"             מסלולים  הניתנים להקצות למוצר, לקטגוריית מוצרים או לתקן\n"
"             רכש או הזמנת לקוח."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "You can either :"
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"You can not change the type of a product that is currently reserved on a "
"stock move. If you need to change the type, you should first unreserve the "
"stock move."
msgstr ""
"אתה לא יכול לשנות את סוג המוצר שתנועתו במלאי נשמרה. אם אתה צריך לשנות את "
"הסוג, אתה צריך תחילה להסיר את השמירה של התנועה מהמלאי."

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "You can not change the type of a product that was already used."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid ""
"You can not delete product moves if the picking is done. You can only "
"correct the done quantities."
msgstr ""
"לא ניתן למחוק תנועות מוצר אם הליקוט בוצע. אתה יכול רק לתקן את הכמויות "
"שבוצעו."

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid "You can not enter negative quantities."
msgstr "לא ניתן להזין כמויות שליליות."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid "You can only delete draft moves."
msgstr "ניתן למחוק רק תנועות טיוטה."

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid "You can only process 1.0 %s of products with unique serial number."
msgstr "אתה יכול רק לעבד 1.0 %s של מוצרים עם מספר סידורי ייחודי."

#. module: stock
#: code:addons/stock/models/res_config_settings.py:0
#, python-format
msgid ""
"You can't desactivate the multi-location if you have more than once "
"warehouse by company"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_location.py:0
#, python-format
msgid "You cannot archive the location %s as it is used by your warehouse %s"
msgstr ""
"אינך יכול להעביר לארכיון את המיקום %s מכיוון שהוא בשימוש ע\"י המחסן שלך %s"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"You cannot cancel a stock move that has been set to 'Done'. Create a return "
"in order to reverse the moves which took place."
msgstr ""
"לא ניתן לבטל תנועת מלאי שהוגדרה כ'בוצע'. צור החזרה על מנת להפוך את המהלכים "
"שהתרחשו."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "You cannot change the Scheduled Date on a done or cancelled transfer."
msgstr "אינך יכול לשנות את התאריך המתוזמן בהעברה שבוצעה או בוטלה."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"You cannot change the UoM for a stock move that has been set to 'Done'."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_location.py:0
#, python-format
msgid ""
"You cannot change the location type or its use as a scrap location as there "
"are products reserved in this location. Please unreserve the products first."
msgstr ""
"לא ניתן לשנות את סוג המיקום או את השימוש בו כמיקום פסילה, שכן קיימים מוצרים "
"השמורים במיקום זה. נדרש להסיר את השמירה על המוצר לפני.. "

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"You cannot change the ratio of this unit of measure as some products with "
"this UoM have already been moved or are currently reserved."
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"You cannot change the unit of measure as there are already stock moves for "
"this product. If you want to change the unit of measure, you should rather "
"archive this product and create a new one."
msgstr ""
"אתה לא יכול לשנות את יחידת מידה מאחר וקיימים כבר תנועות במלאי עבור מוצר זה. "
"אם הכוונה לבצע שינויים ביחידת המידה, נדרש לחזור לארכיון  המוצר וליצור אחד "
"חדש."

#. module: stock
#: code:addons/stock/models/stock_scrap.py:0
#, python-format
msgid "You cannot delete a scrap which is done."
msgstr "אתה לא יכול למחוק פסילה שבוצעה."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "You cannot modify inventory loss quantity"
msgstr "אינך יכול לשנות את כמות הפסד המלאי"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"You cannot move the same package content more than once in the same transfer"
" or split the same package into two location."
msgstr ""
"אתה לא יכול להעביר את אותו תוכן החבילה יותר מפעם אחת באותו העברה או לפצל את "
"אותה החבילה לשני מיקום."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"You cannot perform the move because the unit of measure has a different "
"category as the product unit of measure."
msgstr ""
"לא ניתן לבצע את התנועה כי יחידת מידה שונה בקטגוריה מיחידת המידה של המוצר."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid "You cannot split a draft move. It needs to be confirmed first."
msgstr "לא ניתן לפצל תנועת טיוטה. נדרש שתהיה מאושרת תחילה."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid "You cannot split a stock move that has been set to 'Done' or 'Cancel'."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"You cannot take products from or deliver products to a location of type "
"\"view\" (%s)."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid "You cannot unreserve a stock move that has been set to 'Done'."
msgstr "אתה לא יכול לבטל שמירת תנועת מלאי שמוגדרת כ 'בוצעה'."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid "You cannot change a cancelled stock move, create a new line instead."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid ""
"You cannot use the same serial number twice. Please correct the serial "
"numbers encoded."
msgstr "לא ניתן להשתמש באותו מספר סידורי פעמיים. תקן את המספר הסידורי שהוכנס."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid ""
"You cannot validate a transfer if no quantities are reserved nor done. To "
"force the transfer, switch in edit mode and encode the done quantities."
msgstr ""
"לא ניתן לאמת העברה אם אין כמויות שמורות או כמויות שבוצעו. כדי לאלץ את "
"ההעברה, עבור למצב עריכה ורשום ידנית את הכמויות שבוצעו."

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:0
#, python-format
msgid ""
"You have manually created product lines, please delete them to proceed."
msgstr "יצרת שורות מוצרים ידנית, מחק אותן כדי להמשיך."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
msgid ""
"You have not recorded <i>done</i> quantities yet, by clicking on "
"<i>apply</i> Odoo will process all the quantities."
msgstr ""
"עדיין לא נרשמו כמויות שבוצעו, ע\"י לחיצה על \"החל\" אודו ימלא באופן אוטומטי "
"את כמויות הדרישה"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "You have processed less products than the initial demand."
msgstr "עיבדת פחות מוצרים מבביקוש הראשוני."

#. module: stock
#: code:addons/stock/models/res_config_settings.py:0
#, python-format
msgid ""
"You have product(s) in stock that have lot/serial number tracking enabled. \n"
"Switch off tracking on all the products before switching off this setting."
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"You have product(s) in stock that have no lot/serial number. You can assign "
"lot/serial numbers by doing an inventory adjustment."
msgstr ""
"יש לך מוצרים במלאי שאין להם מספר אצווה / מספר סידורי. ניתן לשייך מספרי אצוות"
" / מספרים סידוריים על ידי ביצוע התאמת מלאי."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid ""
"You have to define a groupby and sorted method and pass them as arguments."
msgstr "אתה צריך להגדיר שיטות לקיבוץ ומיון ולהעביר אותן כארגומנטים."

#. module: stock
#: code:addons/stock/models/stock_orderpoint.py:0
#, python-format
msgid ""
"You have to select a product unit of measure that is in the same category as"
" the default unit of measure of the product"
msgstr ""

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/js/inventory_singleton_list_controller.js:0
#, python-format
msgid ""
"You have tried to create a record which already exists. The existing record "
"has been modified instead."
msgstr ""

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:0
#, python-format
msgid "You may only return Done pickings."
msgstr "אתה יכול להחזיר ליקוטים שבוצעו."

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:0
#, python-format
msgid "You may only return one picking at a time."
msgstr "אתה יכול להחזיר רק ליקוט אחד בכל פעם."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid ""
"You need to activate storage locations to be able to do internal operation "
"types."
msgstr "עליך להפעיל מיקומי אחסון בכדי להיות מסוגל לבצע פעולות פנימיות."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid "You need to set a Serial Number before generating more."
msgstr "עליך להגדיר מספר סידורי לפני שתייצר עוד."

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid ""
"You need to supply a Lot/Serial Number for product: \n"
" - "
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "You need to supply a Lot/Serial number for products %s."
msgstr ""

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid "You should update this document to reflect your T&amp;C."
msgstr "עליך לעדכן מסמך זה כך שישקף את התנאים וההגבלות שלך."

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "You still have ongoing operations for picking types %s in warehouse %s"
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"You still have some active reordering rules on this product. Please archive "
"or delete them first."
msgstr ""
"עדיין יש לך כמה כללי רענון מלאי פעילים במוצר זה. העבר אותם לארכיון או מחק "
"אותם בהקדם."

#. module: stock
#: code:addons/stock/models/stock_location.py:0
#, python-format
msgid "You still have some product in locations %s"
msgstr "עדיין יש לך מוצר במיקומים %s"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint_replenish
msgid ""
"You'll find here smart replenishment propositions based on inventory forecasts.\n"
"            Choose the quantity to buy or manufacture and launch orders in a click.\n"
"            To save time in the future, set the rules as \"automated\"."
msgstr ""
"תמצא כאן הצעות חידוש מלאי חכמות המבוססות על תחזיות מלאי. בחר את הכמות לקנייה"
" או ייצור ותשגר הזמנות בקליק. כדי לחסוך זמן בעתיד, הגדר את הכללים "
"כ\"אוטומטיים\"."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Your stock is currently empty"
msgstr "המלאי שלך ריק כרגע"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__print_format__zpl
msgid "ZPL Labels"
msgstr ""

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__print_format__zplxprice
msgid "ZPL Labels with price"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "]<br/>min:"
msgstr "]<br/>מינימום:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "_Cancel"
msgstr "_בטל"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "below the inventory"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_bpost
msgid "bpost Connector"
msgstr "חיבור ל bpost"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "days"
msgstr "ימים"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "days before when starred"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "days before/"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "e.g. CW"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "e.g. Central Warehouse"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "e.g. LOT/0001/20121"
msgstr "למשל אצווה/0001/20121"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "e.g. PACK0000007"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "e.g. PO0032"
msgstr "למשל PO0032"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "e.g. Physical Locations"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "e.g. Spare Stock"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "e.g. Two-steps reception"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
msgid "from location"
msgstr "מהמיקום "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid "in"
msgstr "ב"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "is"
msgstr "הוא"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "manually to trigger the reordering rules right now."
msgstr "הפעל ידנית כדי לרענן את כללי המלאי באופן מיידי."

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "minimum of"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "of"
msgstr "של"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/popover_widget.xml:0
#, python-format
msgid "planned on"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "processed instead of"
msgstr "מעובד במקום"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_graph
msgid "report_stock_quantity_graph"
msgstr "report_stock_quantity_graph"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "should be replenished"
msgstr ""

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "to reach the maximum of"
msgstr ""

#. module: stock
#: model:mail.template,report_name:stock.mail_template_data_delivery_confirmation
msgid "{{ (object.name or '').replace('/','_') }}"
msgstr ""

#. module: stock
#: model:mail.template,subject:stock.mail_template_data_delivery_confirmation
msgid ""
"{{ object.company_id.name }} Delivery Order (Ref {{ object.name or 'n/a' }})"
msgstr ""

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid "Global Visibility Days"
msgstr ""
