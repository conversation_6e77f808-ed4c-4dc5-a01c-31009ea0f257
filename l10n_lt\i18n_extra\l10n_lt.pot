# Translation of Odoo Server.
# This file contains the translation of the following modules:
#   * l10n_lt
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-05 09:02+0000\n"
"PO-Revision-Date: 2019-09-05 09:02+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_lt
#: model:account.tax.template,description:l10n_lt.account_tax_template_purchase_0
#: model:account.tax.template,description:l10n_lt.account_tax_template_purchase_0_vat14
#: model:account.tax.template,description:l10n_lt.account_tax_template_sales_0_vat12
#: model:account.tax.template,description:l10n_lt.account_tax_template_sales_0_vat13
msgid "0% VAT"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_1_net_turnover
msgid "1. Net turnover"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_9_other_interest_similar_income
msgid "9. Other interest and similar income"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_10_impaired_fin_assets_short_investments
msgid "10. The impairment of the financial assets and short-term investments"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_11_interest_other_similar_expenses
msgid "11. Interest and other similar expenses"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_12_tax_on_profit
msgid "12. Tax on profit"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_2_cost_of_sales
msgid "2. Cost of sales"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,description:l10n_lt.account_tax_template_purchase_21
#: model:account.tax.template,description:l10n_lt.account_tax_template_purchase_not_deductible_21
#: model:account.tax.template,description:l10n_lt.account_tax_template_purchase_reversed_21
#: model:account.tax.template,description:l10n_lt.account_tax_template_purchase_reversed_negative_21
#: model:account.tax.template,description:l10n_lt.account_tax_template_purchase_reversed_positive_21
#: model:account.tax.template,description:l10n_lt.account_tax_template_sales_21
#: model:account.tax.template,description:l10n_lt.account_tax_template_sales_reversed_21
#: model:account.tax.template,description:l10n_lt.account_tax_template_sales_reversed_negative_21
#: model:account.tax.template,description:l10n_lt.account_tax_template_sales_reversed_positive_21
msgid "21% VAT"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_3_adjustments_of_biological_assets
msgid "3. Fair value adjustments of the biological assets"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,description:l10n_lt.account_tax_template_purchase_5
#: model:account.tax.template,description:l10n_lt.account_tax_template_sales_5
msgid "5% VAT"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_4_selling_expenses
msgid "4. Selling expenses"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_5_general_administrative_expenses
msgid "5. General and administrative expenses"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_6_other_operating_results
msgid "6. Other operating results"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_7_income_investments_parent
msgid "7. Income from investments in the shares of parent, subsidiaries and associated entities"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,description:l10n_lt.account_tax_template_purchase_9
#: model:account.tax.template,description:l10n_lt.account_tax_template_purchase_not_deductible_9
msgid "9% VAT"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_8_income_other_longterm_investments_loans
msgid "8. Income from other long-term investments and loans"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_1_1
msgid "A.1.1. Assets arising from development"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_1_2
msgid "A.1.2. Goodwill"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_1_3
msgid "A.1.3. Software"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_1_4
msgid "A.1.4. Concessions, patents, licenses, trade marks and similar rights"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_1_5
msgid "A.1.5. Other intangible assets"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_1_6
msgid "A.1.6. Advance payments"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_2_1
msgid "A.2.1. Land"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_2_2
msgid "A.2.2. Buildings and structures"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_2_3
msgid "A.2.3. Machinery and plant"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_2_4
msgid "A.2.4. Vehicles"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_2_5
msgid "A.2.5. Other equipment, fittings and tools"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_2_6_1
msgid "A.2.6.1. Land"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_2_6_2
msgid "A.2.6.2. Buildings"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_2_7
msgid "A.2.7. Advance payments and tangible assets under construction (production)"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_3_1
msgid "A.3.1. Shares in entities of the entities group"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_3_2
msgid "A.3.2. Loans to entities of the entities group"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_3_3
msgid "A.3.3. Amounts receivable from entities of the entities group"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_3_4
msgid "A.3.4. Shares in associated entities"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_3_5
msgid "A.3.5. Loans to associated entities"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_3_6
msgid "A.3.6. Amounts receivable from the associated entities"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_3_7
msgid "A.3.7. Long-term investments"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_3_8
msgid "A.3.8. Amounts receivable after one year"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_3_9
msgid "A.3.9. Other financial assets"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_4_1
msgid "A.4.1. Assets of the deferred tax on profit"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_4_2
msgid "A.4.2. Biological assets"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_4_3
msgid "A.4.3. Other assets"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_491
msgid "Accumulated Expenses"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_292
msgid "Accumulative Income"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_3411
msgid "Acknowledged Net Profit (Loss) of The Accounting Year in The Profit (Loss) Report"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_3421
msgid "Acknowledged Net Profit (Loss) of The Previous Accounting Year in The Profit (Loss) Report"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_1210
msgid "Acquisition Cost of Buildings"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_26200
msgid "Acquisition Cost of Equity Securities of Other Companies"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_1220
msgid "Acquisition Cost of Machinery and Equipment"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_1240
msgid "Acquisition Cost of Other Equipment, Appliances and Tools"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_166110
#: model:account.account.template,name:l10n_lt.account_account_template_262110
msgid "Acquisition Cost of Other Non-Equity Securities"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_2010
msgid "Acquisition Cost of Raw Materials, Materials and Assembly Parts"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_1230
msgid "Acquisition Cost of Vehicles"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_1680
msgid "Advance Payments for Financial Assets"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_1260
msgid "Advance Payments for Long-Term Tangible Assets"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_6307
msgid "Amortization Costs of Intangible Assets Value"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_1138
msgid "Amortization of Software Value (-)"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_6400
msgid "Asset Disposition Losses"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,description:l10n_lt.account_tax_template_purchase_assumed_21
#: model:account.tax.template,description:l10n_lt.account_tax_template_purchase_assumed_21_vat16
#: model:account.tax.template,description:l10n_lt.account_tax_template_purchase_assumed_21_vat20
#: model:account.tax.template,description:l10n_lt.account_tax_template_purchase_assumed_neg_21_vat16
#: model:account.tax.template,description:l10n_lt.account_tax_template_purchase_assumed_neg_21_vat20
#: model:account.tax.template,description:l10n_lt.account_tax_template_purchase_assumed_negative_21
#: model:account.tax.template,description:l10n_lt.account_tax_template_purchase_assumed_pos_21_vat16
#: model:account.tax.template,description:l10n_lt.account_tax_template_purchase_assumed_pos_21_vat20
#: model:account.tax.template,description:l10n_lt.account_tax_template_purchase_assumed_positive_21
msgid "Assumed 21% VAT"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,name:l10n_lt.account_tax_template_purchase_assumed_21_vat16
msgid "Assumed Purchase 21% (VAT16) from EU"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,name:l10n_lt.account_tax_template_purchase_assumed_pos_21_vat16
msgid "Assumed Purchase 21% (VAT16) from EU (+)"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,name:l10n_lt.account_tax_template_purchase_assumed_neg_21_vat16
msgid "Assumed Purchase 21% (VAT16) from EU (-)"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,name:l10n_lt.account_tax_template_purchase_assumed_21_vat20
msgid "Assumed Purchase 21% (VAT20)"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,name:l10n_lt.account_tax_template_purchase_assumed_pos_21_vat20
msgid "Assumed Purchase 21% (VAT20) (+)"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,name:l10n_lt.account_tax_template_purchase_assumed_neg_21_vat20
msgid "Assumed Purchase 21% (VAT20) (-)"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,name:l10n_lt.account_tax_template_purchase_assumed_21
msgid "Assumed Purchase 21% (VAT21) Goods/Services"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,name:l10n_lt.account_tax_template_purchase_assumed_positive_21
msgid "Assumed Purchase 21% (VAT21) Goods/Services (+)"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,name:l10n_lt.account_tax_template_purchase_assumed_negative_21
msgid "Assumed Purchase 21% (VAT21) Goods/Services (-)"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_b_1_1
msgid "B.1.1. Raw materials, materials and consumables"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_b_1_2
msgid "B.1.2. Production and work in progress"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_b_1_3
msgid "B.1.3. Finished goods"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_b_1_4
msgid "B.1.4. Goods for resale"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_b_1_5
msgid "B.1.5. Biological assets"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_b_1_6
msgid "B.1.6. Fixed tangible assets held for sale"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_b_1_7
msgid "B.1.7. Advance payments"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_b_2_1
msgid "B.2.1. Trade debtors"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_b_2_2
msgid "B.2.2. Amounts owed by entities of the entities group"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_b_2_3
msgid "B.2.3. Amounts owed by associates entities"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_b_2_4
msgid "B.2.4. Other debtors"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_b_3_1
msgid "B.3.1. Shares in entities of the entities group"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_b_3_2
msgid "B.3.2. Other investments"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_b_4
msgid "B.4. CASH AND CASH EQUIVALENTS"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_1212
msgid "Buildings being Prepared for Use"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_c_prepayments_accrued_income
msgid "C. PREPAYMENTS AND ACCRUED INCOME"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_274
msgid "Cash Equivalents"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_chart_template_lithuania_liquidity_transfer
msgid "Cash in Transit"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_1201
msgid "Change of Land Value after Revaluation"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_390
msgid "Common Summary of Accounts"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_305
msgid "Company Owner’s Capital"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_4486
msgid "Contributions of Payable Compulsory Health Insurance"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_4483
msgid "Contributions of Payable Guarantee Fund"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_4482
msgid "Contributions of Payable Social Security"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_20210
msgid "Cost of Executive Works"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_6000
msgid "Cost of Goods Sold"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_20200
msgid "Cost of Incomplete Production"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_2030
msgid "Cost of Production"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_60030
msgid "Cost of Production Sold"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_2040
msgid "Cost of Purchased Goods for Resale"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_6001
msgid "Cost of Services Sold"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_6202
msgid "Cost of Services and Goods Advertising"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_2060
msgid "Cost of Tangible Assets"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_12510
msgid "Cost of the Acquisition of Buildings as Investment Property"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_12500
msgid "Cost of the Acquisition of Land as Investment Property"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_6901
msgid "Costs (Income) of Deferred Value-Added Tax"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_6311
#: model:account.account.template,name:l10n_lt.account_account_template_6804
msgid "Costs of Fines"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_63092
msgid "Costs of Intangible Assets Value Loss"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_6806
msgid "Costs of Interests for Financial Rent"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_63091
msgid "Costs of Inventory Value Loss"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_6301
msgid "Costs of Maintenance and Exploitation"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_6802
msgid "Costs of Other Loan Interests"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_63083
msgid "Costs of Other Taxes"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_63080
msgid "Costs of Real Estate Tax"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_63090
msgid "Costs of Value Loss of Debts from Customers"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_6701
msgid "Costs of Value Loss of Long-Term Financial Assets"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_63093
msgid "Costs of Value Loss of Long-Term Tangible Assets"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_63094
msgid "Costs of Value Loss of Other Assets"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_6702
msgid "Costs of Value Loss of Short-Term Investments"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_63081
msgid "Costs of not Deductible Value-Added Tax"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_d_1_1
msgid "D.1.1. Authorized (subscribed) or primary capital"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_d_1_2
msgid "D.1.2. Subscribed capital unpaid (–)"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_d_1_3
msgid "D.1.3. Own shares (–)"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_d_2_share_premium
msgid "D.2. SHARE PREMIUM ACCOUNT"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_d_3_revaluation_reserve
msgid "D.3. REVALUATION RESERVE"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_d_4_1
msgid "D.4.1. Compulsory reserve or emergency (reserve) capital"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_d_4_2
msgid "D.4.2. Reserve for acquiring own shares"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_d_4_3
msgid "D.4.3. Other reserves"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_d_5_1
msgid "D.5.1. Profit (loss) for the reporting year "
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_d_5_2
msgid "D.5.2. Profit (loss) brought forward"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_63122
msgid "Daily Allowance"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_424
msgid "Debts to Suppliers"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_4430
msgid "Debts to Suppliers for Goods and Services"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_171
msgid "Deferred Profit Tax Asset"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_2084
msgid "Deposit"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_6306
msgid "Depreciation Costs of Long-Term Tangible Assets"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_1217
msgid "Depreciation of the Acquisition Cost of Buildings (-)"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_12517
msgid "Depreciation of the Acquisition Cost of Buildings as Investment Property (-)"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_1227
msgid "Depreciation of the Acquisition Cost of Machinery and Equipment (-)"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_1247
msgid "Depreciation of the Acquisition Cost of Other Equipment, Appliances and Tools (-)"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_1237
msgid "Depreciation of the Acquisition Cost of Vehicles (-)"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_1681
msgid "Derivative Financial Assets"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_60033
msgid "Direct Commissions"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_60034
msgid "Direct Payroll of Employees Working in Production"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_60032
msgid "Direct Transport"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_6209
msgid "Discounts Received (-)"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_509
#: model:account.account.template,name:l10n_lt.account_account_template_609
msgid "Discounts, Returns (-)"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_e_grants_subsidies
msgid "E. GRANTS, SUBSIDIES"
msgstr ""

#. module: l10n_lt
#: model:account.fiscal.position.template,name:l10n_lt.account_fiscal_position_template_eu
msgid "EU"
msgstr ""

#. module: l10n_lt
#: model:account.fiscal.position.template,name:l10n_lt.account_fiscal_position_template_b2c_eu
msgid "B2C EU"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_60043
msgid "Energy Resources"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_f_1
msgid "F.1. Provisions for pensions and similar obligations"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_f_2
msgid "F.2. Provisions for taxation"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_f_3
msgid "F.3. Other provisions"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_279
msgid "Frozen Funds (-)"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_60044
msgid "Fuel"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_6302
msgid "Fuel Costs"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_291
msgid "Future Expenses"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_1_1
msgid "G.1.1. Debenture loans"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_1_2
msgid "G.1.2. Amounts owed to credit institutions"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_1_3
msgid "G.1.3. Payments received on account"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_1_4
msgid "G.1.4. Trade creditors"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_1_5
msgid "G.1.5. Amounts payable under the bills and checks "
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_1_6
msgid "G.1.6. Amounts payable to the entities of the entities group"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_1_7
msgid "G.1.7. Amounts payable to the associated entities"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_1_8
msgid "G.1.8. Other amounts payable and long-term liabilities"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_2_1
msgid "G.2.1. Debenture loans"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_2_10
msgid "G.2.10. Other amounts payable and short-term liabilities"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_2_2
msgid "G.2.2. Amounts owed to credit institutions"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_2_3
msgid "G.2.3. Payments received on account"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_2_4
msgid "G.2.4. Trade creditors"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_2_5
msgid "G.2.5. Amounts payable under the bills and checks "
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_2_6
msgid "G.2.6. Amounts payable to the entities of the entities group"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_2_7
msgid "G.2.7. Amounts payable to the associated entities"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_2_8
msgid "G.2.8. Liabilities of tax on profit"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_2_9
msgid "G.2.9. Liabilities related to employment relations"
msgstr ""

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_h_accruals_deferred_income
msgid "H. ACCRUALS AND DEFERRED INCOME"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_5804
msgid "Income from Fines"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_5000
msgid "Income from Goods Sold"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_5802
msgid "Income from Other Loan Interests"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_5600
msgid "Income from Other Long-Term Investments and Loan Interests"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_5003
msgid "Income from Production Sold"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_5001
msgid "Income from Services Sold"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_492
msgid "Income of Future Periods"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_6005
msgid "Increase (Decrease) in Inventories"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_6303
msgid "Insurance Costs"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_999
msgid "Interim Account"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_6809
msgid "Investment Disposition Losses"
msgstr ""

#. module: l10n_lt
#: model:account.fiscal.position.template,name:l10n_lt.account_fiscal_position_template_lt
msgid "LT"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_1200
msgid "Land Acquisition Cost"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_4211
msgid "Leasing (Financial Rent) or Other Obligations"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_4485
msgid "Leave Liabilities"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_4410
msgid "Liabilities under Short-Term Loan Agreements"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_4220
msgid "Long-Term Liabilities under Loan Agreements"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_1222
msgid "Machinery and Equipment being Prepared for Use"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_60042
msgid "Maintenance of Production Transport"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_60041
msgid "Maintenance of Working Tools for Production"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_331
msgid "Mandatory or Reserve Capital"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_6803
msgid "Negative Currency Exchange Difference"
msgstr ""

#. module: l10n_lt
#: model:account.fiscal.position.template,name:l10n_lt.account_fiscal_position_template_out_eu
msgid "Not EU"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_4470
msgid "Obligations of Profit Tax"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_3011
msgid "Ordinary Shares"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_6401
msgid "Other Costs"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_63120
msgid "Other Costs and Bank Taxes"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_6208
msgid "Other Costs of Sales"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_4214
msgid "Other Debt Obligations"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_4431
msgid "Other Debts to Suppliers"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_1242
msgid "Other Equipment, Appliances and Tools being Prepared for Use"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_5401
msgid "Other Income"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_4221
#: model:account.account.template,name:l10n_lt.account_account_template_4471
msgid "Other Obligations"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_4413
msgid "Other Obligations (Executables and etc.)"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_4493
msgid "Other Payable Taxes to Budget"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_4494
msgid "Other Payables"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_428
msgid "Other Payables and Long-Term Liabilities"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_413
msgid "Other Provisions"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_2449
msgid "Other Questionable Debts (-)"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_303
msgid "Own Shares (Yawn) (-)"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_308
msgid "Owners’ Contributions"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_4411
msgid "Part of Debts to Credit Institutions in The Current Year"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_4401
msgid "Part of Leasing (Financial Rent) or Other Obligations in The Current Year"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_4403
msgid "Part of Other Long-Term Debts in The Current Year"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_4491
msgid "Payable Bonuses"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_4490
msgid "Payable Dividends"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_4481
msgid "Payable Personal Income Tax"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_4480
msgid "Payable Salary"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_4492
msgid "Payable Value-Added Tax"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_4495
msgid "Payables to the Owners of the Company"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_24472
msgid "Payed Funds for Personal Use to the Owners of the Company"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_6304
msgid "Payroll and Other Costs of Employees Working in Administration"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_6203
msgid "Payroll and Other Costs of Employees Working in Sales"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_63082
msgid "Pollution Tax Costs"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_5803
msgid "Positive Currency Exchange Difference"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_3012
msgid "Preference Shares"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_2442
msgid "Prepaid Profit Tax"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_4230
#: model:account.account.template,name:l10n_lt.account_account_template_4420
msgid "Prepayments from Customers"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_4231
#: model:account.account.template,name:l10n_lt.account_account_template_4421
msgid "Prepayments from Service Receivers"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_2080
msgid "Prepayments to Suppliers"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_2036
msgid "Production at Third Parties"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_2035
msgid "Production in Transit"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_3424
msgid "Profit (Loss) of Material Corrections of The Previous Years"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_6900
msgid "Profit and Other Similar Taxes of The Current Year"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_5400
msgid "Profit from Asset Disposition"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_5809
msgid "Profit from Disposition of Investments"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_24471
msgid "Profit paid in Advance to the Owners of the Company"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_6310
msgid "Provisions Costs"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_411
msgid "Provisions for Pensions and Similar Liabilities"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,name:l10n_lt.account_tax_template_purchase_0_vat100
msgid "Purchase 0% (VAT100) other cases"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,name:l10n_lt.account_tax_template_purchase_0_vat14
msgid "Purchase 0% (VAT14) export, transportation"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,name:l10n_lt.account_tax_template_purchase_0
msgid "Purchase 0% (VAT15)"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,name:l10n_lt.account_tax_template_purchase_0_vat42
msgid "Purchase 0% (VAT42)"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,name:l10n_lt.account_tax_template_purchase_0_vat5
msgid "Purchase 0% (VAT5) Tax Exempt LT"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,name:l10n_lt.account_tax_template_purchase_21
msgid "Purchase 21% (VAT1)"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,name:l10n_lt.account_tax_template_purchase_not_deductible_21
msgid "Purchase 21% (VAT1) not deductible"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,name:l10n_lt.account_tax_template_purchase_5
msgid "Purchase 5% (VAT3)"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,name:l10n_lt.account_tax_template_purchase_9
msgid "Purchase 9% (VAT2)"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,name:l10n_lt.account_tax_template_purchase_not_deductible_9
msgid "Purchase 9% (VAT2) not deductible"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_2046
msgid "Purchased Goods for Resale at Third Parties"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_2045
msgid "Purchased Goods for Resale in Transit"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_2012
msgid "Raw Materials, Materials and Assembly Parts at Third Parties"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_2011
msgid "Raw Materials, Materials and Assembly Parts in Transit"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_1672
msgid "Receivable Amount of Leasing (Financial Rent) after One Year"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_2441
msgid "Receivable Value-Added Tax"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_6300
msgid "Rental Costs"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_6805
msgid "Representation and other disallowed deductions"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_63121
msgid "Representative Costs"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,name:l10n_lt.account_tax_template_purchase_reversed_21
msgid "Reversed Purchase 21% (VAT25)"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,name:l10n_lt.account_tax_template_purchase_reversed_positive_21
msgid "Reversed Purchase 21% (VAT25) (+)"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,name:l10n_lt.account_tax_template_purchase_reversed_negative_21
msgid "Reversed Purchase 21% (VAT25) (-)"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,name:l10n_lt.account_tax_template_sales_reversed_21
msgid "Reversed Sale 21% (VAT25)"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,name:l10n_lt.account_tax_template_sales_reversed_positive_21
msgid "Reversed Sale 21% (VAT25) (+)"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,name:l10n_lt.account_tax_template_sales_reversed_negative_21
msgid "Reversed Sale 21% (VAT25) (-)"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,name:l10n_lt.account_tax_template_sales_0_vat12
msgid "Sale 0% (VAT12) export"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,name:l10n_lt.account_tax_template_sales_0_vat13
msgid "Sale 0% (VAT13)"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,name:l10n_lt.account_tax_template_sales_0_vat15
msgid "Sale 0% (VAT15) Service not in EU"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,name:l10n_lt.account_tax_template_sales_0_vat5
msgid "Sale 0% (VAT5) Tax Exempt in LT"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,name:l10n_lt.account_tax_template_sales_21
msgid "Sale 21% (VAT1)"
msgstr ""

#. module: l10n_lt
#: model:account.tax.template,name:l10n_lt.account_tax_template_sales_5
msgid "Sale 5% (VAT3)"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_60031
msgid "Short-Term Production Inventory"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_1130
msgid "Software Acquisition Cost"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_302
msgid "Subscribed Capital Unpaid (-)"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_63123
msgid "Support"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_2443
msgid "Tax Overpayments"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_412
msgid "Tax Provisions"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_1665
#: model:account.account.template,name:l10n_lt.account_account_template_2623
msgid "Term Deposits"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_3412
msgid "Unacknowledged Net Profit (Loss) of The Accounting Year in The Profit (Loss) Report"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_3422
msgid "Unacknowledged Net Profit (Loss) of The Previous Accounting Year in The Profit (Loss) Report"
msgstr ""

#. module: l10n_lt
#: model:account.tax.group,name:l10n_lt.tax_group_vat_0
msgid "VAT 0%"
msgstr ""

#. module: l10n_lt
#: model:account.tax.group,name:l10n_lt.tax_group_vat_5
msgid "VAT 5%"
msgstr ""

#. module: l10n_lt
#: model:account.tax.group,name:l10n_lt.tax_group_vat_9
msgid "VAT 9%"
msgstr ""

#. module: l10n_lt
#: model:account.tax.group,name:l10n_lt.tax_group_vat_21
msgid "VAT 21%"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_2444
msgid "VSDF (Lithuanian State Social Insurance Fund) Debt to the Company"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_12519
msgid "Value Loss of Buildings as Investment Property (-)"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_26209
msgid "Value Loss of Equity Securities of Other Companies (-)"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_166119
#: model:account.account.template,name:l10n_lt.account_account_template_262119
msgid "Value Loss of Other Non-Equity Securities (-)"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_2039
msgid "Value Loss of Production (-)"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_2049
msgid "Value Loss of Purchased Goods for Resale (-)"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_2019
msgid "Value Loss of Raw Materials, Materials and Assembly Parts (-)"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_12509
msgid "Value Loss of the Land as Investment Property (-)"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_2410
msgid "Value of Debts from Customers"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_16710
#: model:account.account.template,name:l10n_lt.account_account_template_24400
msgid "Value of Loans Granted"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_24460
msgid "Value of Other Receivable Debts"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_16700
msgid "Value of Receivable Debts from Customers"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_16740
msgid "Value of Receivables"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_24450
msgid "Value of Receivables from Accountable Persons"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_1232
msgid "Vehicles being Prepared for Use"
msgstr ""

#. module: l10n_lt
#: model:account.account.template,name:l10n_lt.account_account_template_60040
msgid "Working Tools for Production"
msgstr ""
