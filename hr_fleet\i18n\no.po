# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_fleet
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Language-Team: Norwegian (https://www.transifex.com/odoo/teams/41243/no/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: no\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_fleet
#: code:addons/hr_fleet/controllers/main.py:0
#, python-format
msgid "%(car_name)s (driven from: %(date_start)s to %(date_end)s)"
msgstr ""

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_view_form_inherit_hr
msgid "<span class=\"o_stat_text\">Employee</span>"
msgstr ""

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.view_employee_form
msgid "Application Settings"
msgstr ""

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_assignation_log_view_list
msgid "Attachments"
msgstr ""

#. module: hr_fleet
#: code:addons/hr_fleet/models/employee.py:0
#, python-format
msgid "Cannot remove address from employees with linked cars."
msgstr ""

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_hr_employee__employee_cars_count
#: model:ir.model.fields,field_description:hr_fleet.field_res_users__employee_cars_count
msgid "Cars"
msgstr ""

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.res_users_view_form_preferences
#: model_terms:ir.ui.view,arch_db:hr_fleet.view_employee_form
msgid "Claim Car Report"
msgstr ""

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.hr_departure_wizard_view_form
msgid "Company Car"
msgstr ""

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_view_search_inherit_hr
msgid "Current Driver (Employee)"
msgstr ""

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr ""

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_log_contract_view_form_inherit_hr
msgid "Driver"
msgstr ""

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle__driver_employee_id
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle_assignation_log__driver_employee_id
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle_log_contract__purchaser_employee_id
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle_log_services__purchaser_employee_id
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle_odometer__driver_employee_id
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_log_services_view_form_inherit_hr
msgid "Driver (Employee)"
msgstr ""

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_fleet_vehicle_assignation_log
msgid "Drivers history on a vehicle"
msgstr ""

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_hr_employee
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_log_contract_view_search_inherit_hr
msgid "Employee"
msgstr ""

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle__driver_employee_name
msgid "Employee Name"
msgstr ""

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.view_employee_form
msgid "Fleet Mobility Card"
msgstr ""

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle__future_driver_employee_id
#: model_terms:ir.ui.view,arch_db:hr_fleet.fleet_vehicle_view_search_inherit_hr
msgid "Future Driver (Employee)"
msgstr ""

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.view_employee_filter
msgid "License Plate"
msgstr ""

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle__mobility_card
#: model:ir.model.fields,field_description:hr_fleet.field_hr_employee__mobility_card
#: model:ir.model.fields,field_description:hr_fleet.field_hr_employee_public__mobility_card
msgid "Mobility Card"
msgstr ""

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_fleet_vehicle_assignation_log__attachment_number
msgid "Number of Attachments"
msgstr ""

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_fleet_vehicle_odometer
msgid "Odometer log for a vehicle"
msgstr ""

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_hr_employee_public
msgid "Public Employee"
msgstr ""

#. module: hr_fleet
#: code:addons/hr_fleet/models/fleet_vehicle.py:0
#, python-format
msgid "Related Employee"
msgstr ""

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_hr_departure_wizard__release_campany_car
msgid "Release Company Car"
msgstr ""

#. module: hr_fleet
#: model:ir.model.fields,help:hr_fleet.field_hr_departure_wizard__release_campany_car
msgid "Release the company car."
msgstr ""

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_fleet_vehicle_log_services
msgid "Services for vehicles"
msgstr ""

#. module: hr_fleet
#: model_terms:ir.ui.view,arch_db:hr_fleet.res_users_view_form_preferences
#: model_terms:ir.ui.view,arch_db:hr_fleet.view_employee_form
msgid ""
"This report will contain only PDF files. If you want all documents, please "
"go on vehicle page. Do you want to proceed?"
msgstr ""

#. module: hr_fleet
#: code:addons/hr_fleet/controllers/main.py:0
msgid "There is no pdf attached to generate a claim report."
msgstr ""

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_res_users
msgid "Users"
msgstr ""

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_fleet_vehicle
msgid "Vehicle"
msgstr ""

#. module: hr_fleet
#: model:ir.model,name:hr_fleet.model_fleet_vehicle_log_contract
msgid "Vehicle Contract"
msgstr ""

#. module: hr_fleet
#: model:ir.model.fields,field_description:hr_fleet.field_hr_employee__car_ids
msgid "Vehicles (private)"
msgstr ""
