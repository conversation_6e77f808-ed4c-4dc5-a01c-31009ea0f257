# #-#-#-#-#  bg.po (Odoo 9.0)  #-#-#-#-#
# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_payment
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2016
# #-#-#-#-#  bg.po (Odoo 9.0)  #-#-#-#-#
# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_payment
#
# Translators:
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2016-07-17 03:34+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Bulgarian (http://www.transifex.com/odoo/odoo-9/language/"
"bg/)\n"
"Language: bg\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"#-#-#-#-#  bg.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"#-#-#-#-#  bg.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.confirm
msgid "<i class=\"fa fa-arrow-circle-right\"/> Back to My Account"
msgstr "<i class=\"fa fa-arrow-circle-right\"/> Обратно към Моя Профил"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.pay_methods
msgid "Acquirer"
msgstr "Обработчик на плащането"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.pay_methods
msgid "Add a new payment method:"
msgstr "Добавяне на нов метод за плащане:"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.confirm
#: model_terms:ir.ui.view,arch_db:website_payment.pay
#, fuzzy
msgid "Amount"
msgstr ""
"#-#-#-#-#  bg.po (Odoo 9.0)  #-#-#-#-#\n"
"Сума\n"
"#-#-#-#-#  bg.po (Odoo 9.0)  #-#-#-#-#\n"
"Количество"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.website_settings_payment
msgid "Configure payment acquirers"
msgstr "Конфигуриране на обработчици за плащания"

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_account_config_settings_default_acquirer
msgid "Default Acquirer"
msgstr "Обработчик по подразбиране"

#. module: website_payment
#: model:ir.model.fields,help:website_payment.field_account_config_settings_default_acquirer
msgid ""
"Default payment acquirer for website payments; your provider needs to be "
"visible in the website."
msgstr ""
"Обработчик по подразбиране за плащания през уебсайта (провайдъра трябва да е "
"видим с сайта)."

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.pay_methods
msgid "Delete <span class=\"fa fa-long-arrow-right\"/>"
msgstr "Изтрий <span class=\"fa fa-long-arrow-right\"/>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.pay_methods
msgid "Delete a payment method:"
msgstr "Изтрий метод за плащане:"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.website_settings_payment
msgid "E-Commerce"
msgstr "Елетронна Търговия"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.confirm
msgid "From"
msgstr "От"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.pay_meth_link
msgid "Manage your payment methods"
msgstr "Управление на начини за плащане"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.pay_methods
msgid "My Account"
msgstr "Моят Профил"

#. module: website_payment
#: code:addons/website_payment/controllers/main.py:43
#, python-format
msgid "Pay Now"
msgstr "Плати Сега"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.confirm
#: model_terms:ir.ui.view,arch_db:website_payment.pay
msgid "Payment"
msgstr "Плащане"

#. module: website_payment
#: model:ir.model,name:website_payment.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Обработчик на плащането"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.header_footer_custom_payment
msgid "Payment Method"
msgstr "Начин на плащане"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.pay_methods
msgid "Payment Methods"
msgstr "Начини на плащане"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.confirm
msgid "Payment processed by"
msgstr "Плащането е обработено от"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.confirm
#: model_terms:ir.ui.view,arch_db:website_payment.pay
msgid "Reference"
msgstr "Означение"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.pay
msgid "This transaction will be processed by"
msgstr "Транзакцията ще бъде обработена от"

#. module: website_payment
#: model:ir.model,name:website_payment.model_website
msgid "Website"
msgstr "Уеб-страница"

#. module: website_payment
#: model:ir.model,name:website_payment.model_account_config_settings
msgid "account.config.settings"
msgstr "account.config.settings"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.confirm
#: model_terms:ir.ui.view,arch_db:website_payment.pay
msgid "payment_provider_logo"
msgstr "payment_provider_logo"

#~ msgid "The full URL to access the document through the website."
#~ msgstr "Пълният URL за достъп до документа през уебсайта."

#~ msgid "Website URL"
#~ msgstr "URL на Уебсайта"
