<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="ir_cron_receive_fattura_pa_invoice" model="ir.cron">
        <field name="name">FatturaPA: Receive invoices from the exchange system</field>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
        <field name="model_id" ref="account_edi.model_account_edi_format"/>
        <field name="code">model._cron_receive_fattura_pa()</field>
        <field name="doall" eval="False"/>
        <field name="state">code</field>
    </record>
</odoo>
