# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * partner_autocomplete
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-02 10:06+0000\n"
"PO-Revision-Date: 2018-10-02 10:06+0000\n"
"Last-Translator: Bole <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid "(Time Now)"
msgstr ""

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                        Buy more credits"
msgstr ""

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-exclamation-triangle text-warning\"/> &amp;nbsp; You don't "
"have credits to auto-complete companies' data anymore."
msgstr ""

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid ""
"<i class=\"fa fa-fw mr-2 fa-building text-primary\"/>\n"
"                <b>Company type:</b>"
msgstr ""

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid ""
"<i class=\"fa fa-fw mr-2 fa-calendar text-primary\"/>\n"
"                <b>Founded:</b>"
msgstr ""

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid ""
"<i class=\"fa fa-fw mr-2 fa-cube text-primary\"/>\n"
"                <b>Technology Used :</b>"
msgstr ""

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid ""
"<i class=\"fa fa-fw mr-2 fa-envelope text-primary\"/>\n"
"                <b>Email :</b>"
msgstr ""

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid ""
"<i class=\"fa fa-fw mr-2 fa-globe text-primary\"/>\n"
"                <b>Timezone :</b>"
msgstr ""

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid ""
"<i class=\"fa fa-fw mr-2 fa-industry text-primary\"/>\n"
"                <b>Sector:</b>"
msgstr ""

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid ""
"<i class=\"fa fa-fw mr-2 fa-money text-primary\"/>\n"
"                <b>Annual revenue:</b>"
msgstr ""

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid ""
"<i class=\"fa fa-fw mr-2 fa-money text-primary\"/>\n"
"                <b>Estimated annual revenue:</b>"
msgstr ""

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid ""
"<i class=\"fa fa-fw mr-2 fa-phone text-primary\"/>\n"
"                <b>Phone :</b>"
msgstr ""

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid ""
"<i class=\"fa fa-fw mr-2 fa-users text-primary\"/>\n"
"                <b>Employees:</b>"
msgstr ""

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.res_config_settings_view_form
msgid "<span>&amp;times;</span>"
msgstr ""

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner__additional_info
msgid "Additional info"
msgstr ""

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.res_config_settings_view_form
msgid "Close"
msgstr "Zatvori"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_company
msgid "Companies"
msgstr "Kompanije"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_company__partner_gid
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner__partner_gid
msgid "Company database ID"
msgstr ""

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__id
msgid "ID"
msgstr "ID"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_config_settings__partner_autocomplete_insufficient_credit
msgid "Insufficient credit"
msgstr ""

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__synched
msgid "Is synched"
msgstr ""

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync____last_update
msgid "Last Modified on"
msgstr "Zadnje mijenjano"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__write_uid
msgid "Last Updated by"
msgstr "Zadnji ažurirao"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__write_date
msgid "Last Updated on"
msgstr "Zadnje ažurirano"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__partner_id
msgid "Partner"
msgstr "Partner"

#. module: partner_autocomplete
#: model:ir.actions.server,name:partner_autocomplete.ir_cron_partner_autocomplete_ir_actions_server
#: model:ir.cron,cron_name:partner_autocomplete.ir_cron_partner_autocomplete
#: model:ir.cron,name:partner_autocomplete.ir_cron_partner_autocomplete
msgid "Partner Autocomplete : Sync with remote DB"
msgstr ""

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_partner_autocomplete_sync
msgid "Partner Autocomplete Sync"
msgstr ""

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid "Partner created by Odoo Partner Autocomplete Service"
msgstr ""

#. module: partner_autocomplete
#. openerp-web
#: code:addons/partner_autocomplete/static/src/xml/partner_autocomplete.xml:8
#, python-format
msgid "Placeholder"
msgstr ""

#. module: partner_autocomplete
#. openerp-web
#: code:addons/partner_autocomplete/static/src/js/partner_autocomplete_many2one.js:22
#, python-format
msgid "Searching Autocomplete..."
msgstr ""

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid "Twitter :"
msgstr ""

#. module: partner_autocomplete
#: model_terms:ir.ui.view,arch_db:partner_autocomplete.additional_info_template
msgid "followers)"
msgstr ""
