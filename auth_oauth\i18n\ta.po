# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * auth_oauth
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2016-02-05 10:10+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Tamil (http://www.transifex.com/odoo/odoo-9/language/ta/)\n"
"Language: ta\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.view_general_configuration
msgid ""
"<br/>\n"
"                                    - Create a new project<br/>\n"
"                                    - Go to Api Access<br/>\n"
"                                    - Create an oauth client_id<br/>\n"
"                                    - Edit settings and set both Authorized "
"Redirect URIs and Authorized JavaScript Origins to your hostname.<br/>\n"
"                                    <br/>\n"
"                                    Now copy paste the client_id here:"
msgstr ""

#. module: auth_oauth
#: code:addons/auth_oauth/controllers/main.py:98
#, python-format
msgid "Access Denied"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users_property_account_payable_id
msgid "Account Payable"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users_property_account_receivable_id
msgid "Account Receivable"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_base_config_settings_auth_oauth_google_enabled
msgid "Allow users to sign in with Google"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider_enabled
msgid "Allowed"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider_auth_endpoint
msgid "Authentication URL"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users_bank_account_count
msgid "Bank"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider_body
msgid "Body"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider_css_class
msgid "CSS class"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider_client_id
#: model:ir.model.fields,field_description:auth_oauth.field_base_config_settings_auth_oauth_google_client_id
msgid "Client ID"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users_ref_company_ids
msgid "Companies that refers to partner"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users_contract_ids
#: model:ir.model.fields,field_description:auth_oauth.field_res_users_contracts_count
msgid "Contracts"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider_create_uid
msgid "Created by"
msgstr "உருவாக்கியவர்"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider_create_date
msgid "Created on"
msgstr ""
"உருவாக்கப்பட்ட \n"
"தேதி"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users_currency_id
msgid "Currency"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users_property_payment_term_id
msgid "Customer Payment Term"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider_data_endpoint
msgid "Data URL"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users_trust
msgid "Degree of trust you have in this debtor"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider_display_name
msgid "Display Name"
msgstr "காட்சி பெயர்"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users_property_account_position_id
msgid "Fiscal Position"
msgstr ""

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.view_general_configuration
msgid "Google APIs console"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users_has_unreconciled_entries
msgid "Has unreconciled entries"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider_id
msgid "ID"
msgstr "ID"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users_invoice_ids
msgid "Invoices"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users_issued_total
#: model:ir.model.fields,field_description:auth_oauth.field_res_users_journal_item_count
msgid "Journal Items"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider___last_update
msgid "Last Modified on"
msgstr "கடைசியாக திருத்திய"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider_write_uid
msgid "Last Updated by"
msgstr "கடைசியாக புதுப்பிக்கப்பட்டது"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider_write_date
msgid "Last Updated on"
msgstr "கடைசியாக புதுப்பிக்கப்பட்டது"

#. module: auth_oauth
#: model:ir.model.fields,help:auth_oauth.field_res_users_last_time_entries_checked
msgid ""
"Last time the invoices & payments matching was performed for this partner. "
"It is set either if there's not at least an unreconciled debit and an "
"unreconciled credit or if you click the \"Done\" button."
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users_last_time_entries_checked
msgid "Latest Invoices & Payments Matching Date"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users_oauth_access_token
msgid "OAuth Access Token"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users_oauth_provider_id
msgid "OAuth Provider"
msgstr ""

#. module: auth_oauth
#: model:ir.ui.menu,name:auth_oauth.menu_oauth_providers
msgid "OAuth Providers"
msgstr ""

#. module: auth_oauth
#: sql_constraint:res.users:0
msgid "OAuth UID must be unique per provider"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users_oauth_uid
msgid "OAuth User ID"
msgstr ""

#. module: auth_oauth
#: model:ir.model,name:auth_oauth.model_auth_oauth_provider
msgid "OAuth2 provider"
msgstr ""

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.view_users_form
msgid "Oauth"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,help:auth_oauth.field_res_users_oauth_uid
msgid "Oauth Provider user_id"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users_debit_limit
msgid "Payable Limit"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider_name
msgid "Provider name"
msgstr ""

#. module: auth_oauth
#: model:ir.actions.act_window,name:auth_oauth.action_oauth_provider
msgid "Providers"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider_scope
msgid "Scope"
msgstr ""

#. module: auth_oauth
#: code:addons/auth_oauth/controllers/main.py:96
#, python-format
msgid "Sign up is not allowed on this database."
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,help:auth_oauth.field_res_users_property_account_position_id
msgid ""
"The fiscal position will determine taxes and accounts used for the partner."
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,help:auth_oauth.field_res_users_has_unreconciled_entries
msgid ""
"The partner has at least one unreconciled debit and credit since last time "
"the invoices & payments matching was performed."
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,help:auth_oauth.field_res_users_property_account_payable_id
msgid ""
"This account will be used instead of the default one as the payable account "
"for the current partner"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,help:auth_oauth.field_res_users_property_account_receivable_id
msgid ""
"This account will be used instead of the default one as the receivable "
"account for the current partner"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,help:auth_oauth.field_res_users_property_supplier_payment_term_id
msgid ""
"This payment term will be used instead of the default one for purchase "
"orders and vendor bills"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,help:auth_oauth.field_res_users_property_payment_term_id
msgid ""
"This payment term will be used instead of the default one for sale orders "
"and customer invoices"
msgstr ""

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.view_general_configuration
msgid ""
"To setup the signin process with Google, first you have to perform the "
"following steps:<br/>\n"
"                                    <br/>\n"
"                                    - Go to the"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users_total_invoiced
msgid "Total Invoiced"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users_debit
msgid "Total Payable"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users_credit
msgid "Total Receivable"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,help:auth_oauth.field_res_users_credit
msgid "Total amount this customer owes you."
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,help:auth_oauth.field_res_users_debit
msgid "Total amount you have to pay to this vendor."
msgstr ""

#. module: auth_oauth
#: model:ir.model,name:auth_oauth.model_res_users
msgid "Users"
msgstr "பயனர்கள்"

#. module: auth_oauth
#: model:ir.model.fields,help:auth_oauth.field_res_users_currency_id
msgid "Utility field to express amount currency"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider_validation_endpoint
msgid "Validation URL"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users_property_supplier_payment_term_id
msgid "Vendor Payment Term"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users_website_message_ids
msgid "Website Messages"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,help:auth_oauth.field_res_users_website_message_ids
msgid "Website communication history"
msgstr ""

#. module: auth_oauth
#: code:addons/auth_oauth/controllers/main.py:100
#, python-format
msgid ""
"You do not have access to this database or your invitation has expired. "
"Please ask for an invitation and be sure to follow the link in your "
"invitation email."
msgstr ""

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.view_oauth_provider_form
#: model_terms:ir.ui.view,arch_db:auth_oauth.view_oauth_provider_list
msgid "arch"
msgstr ""

#. module: auth_oauth
#: model:ir.model,name:auth_oauth.model_base_config_settings
msgid "base.config.settings"
msgstr ""

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.view_general_configuration
msgid "e.g. 1234-xyz.apps.googleusercontent.com"
msgstr ""

#. module: auth_oauth
#: model:ir.model,name:auth_oauth.model_ir_config_parameter
msgid "ir.config_parameter"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider_sequence
msgid "unknown"
msgstr ""
