<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
    <path id="path-1" d="M46 4.5c0-.55-.196-1.022-.587-1.413A1.926 1.926 0 0 0 44 2.5c-.55 0-1.022.196-1.413.587A1.926 1.926 0 0 0 42 4.5c0 .55.196 1.022.587 1.413.391.391.862.587 1.413.587.55 0 1.022-.196 1.413-.587.391-.391.587-.862.587-1.413zm2 3.462a.517.517 0 0 1-.16.378.517.517 0 0 1-.378.16.5.5 0 0 1-.38-.16L45.64 6.901a2.88 2.88 0 0 1-1.678.522 2.91 2.91 0 0 1-1.151-.233 2.961 2.961 0 0 1-.947-.631 2.961 2.961 0 0 1-.63-.947A2.91 2.91 0 0 1 41 4.462c0-.402.078-.785.233-1.151a2.98 2.98 0 0 1 .631-.947c.266-.265.581-.475.947-.63a2.91 2.91 0 0 1 1.15-.234c.402 0 .785.078 1.151.233.366.156.682.366.947.631.265.266.475.581.63.947.156.366.234.75.234 1.15a2.88 2.88 0 0 1-.522 1.679l1.443 1.443c.104.104.156.23.156.379z"/>
    <filter id="filter-2" width="114.3%" height="128.6%" x="-7.1%" y="-7.1%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.4 0"/>
    </filter>
    <path id="path-3" d="M37 1v8H16V1h21zm-1 1H17v6h19V2z"/>
    <filter id="filter-4" width="104.8%" height="125%" x="-2.4%" y="-6.2%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.292012675 0"/>
    </filter>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_products_searchbar_inline">
      <rect width="82" height="60" class="bg"/>
      <g class="group" transform="translate(17 25)">
        <g class="box_solid" transform="translate(0 .5)">
          <rect width="11" height="10" class="rectangle"/>
          <path fill="#FFF" fill-opacity=".95" d="M0 2.174L5.077 4.1V10L0 7.91V2.174zm11 0V7.91L5.923 10V4.1L11 2.174zM5.5 0L11 1.472 5.5 3.478 0 1.472 5.5 0z" class="combined_shape"/>
        </g>
        <g class="search">
          <use fill="#000" filter="url(#filter-2)" xlink:href="#path-1"/>
          <use fill="#FFF" fill-opacity=".95" xlink:href="#path-1"/>
        </g>
        <g class="combined_shape">
          <use fill="#000" filter="url(#filter-4)" xlink:href="#path-3"/>
          <use fill="#FFF" fill-opacity=".78" xlink:href="#path-3"/>
        </g>
      </g>
    </g>
  </g>
</svg>
