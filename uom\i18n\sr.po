# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * uom
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-18 09:49+0000\n"
"PO-Revision-Date: 2018-09-18 09:49+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Serbian (https://www.transifex.com/odoo/teams/41243/sr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__active
msgid "Active"
msgstr "Активно"

#. module: uom
#: model_terms:ir.actions.act_window,help:uom.product_uom_form_action
msgid "Add a new unit of measure"
msgstr ""

#. module: uom
#: model_terms:ir.actions.act_window,help:uom.product_uom_categ_form_action
msgid "Add a new unit of measure category"
msgstr ""

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__factor_inv
msgid "Bigger Ratio"
msgstr ""

#. module: uom
#: selection:uom.uom,uom_type:0
msgid "Bigger than the reference Unit of Measure"
msgstr ""

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__category_id
msgid "Category"
msgstr "Kategorija"

#. module: uom
#: model:ir.model.fields,help:uom.field_uom_uom__category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__create_uid
#: model:ir.model.fields,field_description:uom.field_uom_uom__create_uid
msgid "Created by"
msgstr ""

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__create_date
#: model:ir.model.fields,field_description:uom.field_uom_uom__create_date
msgid "Created on"
msgstr "Kreiran"

#. module: uom
#: model:uom.uom,name:uom.product_uom_day
msgid "Day(s)"
msgstr ""

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__display_name
#: model:ir.model.fields,field_description:uom.field_uom_uom__display_name
msgid "Display Name"
msgstr ""

#. module: uom
#: model:uom.uom,name:uom.product_uom_dozen
msgid "Dozen(s)"
msgstr ""

#. module: uom
#: model:uom.uom,name:uom.product_uom_hour
msgid "Hour(s)"
msgstr ""

#. module: uom
#: model:ir.model.fields,help:uom.field_uom_uom__factor_inv
msgid ""
"How many times this Unit of Measure is bigger than the reference Unit of "
"Measure in this category: 1 * (this unit) = ratio * (reference unit)"
msgstr ""

#. module: uom
#: model:ir.model.fields,help:uom.field_uom_uom__factor
msgid ""
"How much bigger or smaller this unit is compared to the reference Unit of "
"Measure for this category: 1 * (reference unit) = ratio * (this unit)"
msgstr ""

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__id
#: model:ir.model.fields,field_description:uom.field_uom_uom__id
msgid "ID"
msgstr "ID"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category____last_update
#: model:ir.model.fields,field_description:uom.field_uom_uom____last_update
msgid "Last Modified on"
msgstr ""

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__write_uid
#: model:ir.model.fields,field_description:uom.field_uom_uom__write_uid
msgid "Last Updated by"
msgstr ""

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__write_date
#: model:ir.model.fields,field_description:uom.field_uom_uom__write_date
msgid "Last Updated on"
msgstr ""

#. module: uom
#: selection:uom.category,measure_type:0
msgid "Length"
msgstr "Dužina"

#. module: uom
#: model:uom.category,name:uom.uom_categ_length
msgid "Length / Distance"
msgstr "Duzina / Udaljenost"

#. module: uom
#: model:uom.uom,name:uom.product_uom_litre
msgid "Liter(s)"
msgstr ""

#. module: uom
#: model:res.groups,name:uom.group_uom
msgid "Manage Multiple Units of Measure"
msgstr ""

#. module: uom
#: model:ir.model,name:uom.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Jedinica mere proizvoda"

#. module: uom
#: model:ir.model,name:uom.model_uom_category
msgid "Product UoM Categories"
msgstr ""

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__factor
msgid "Ratio"
msgstr "Odnos"

#. module: uom
#: selection:uom.uom,uom_type:0
msgid "Reference Unit of Measure for this category"
msgstr ""

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__rounding
msgid "Rounding Precision"
msgstr "Preciznost zaokruživanja"

#. module: uom
#: selection:uom.uom,uom_type:0
msgid "Smaller than the reference Unit of Measure"
msgstr ""

#. module: uom
#: model:ir.model.fields,help:uom.field_uom_uom__rounding
msgid ""
"The computed quantity will be a multiple of this value. Use 1.0 for a Unit "
"of Measure that cannot be further split, such as a piece."
msgstr ""

#. module: uom
#: sql_constraint:uom.uom:0
msgid "The conversion ratio for a unit of measure cannot be 0!"
msgstr ""

#. module: uom
#: sql_constraint:uom.uom:0
msgid "The reference unit must have a conversion factor equal to 1."
msgstr ""

#. module: uom
#: sql_constraint:uom.uom:0
msgid "The rounding precision must be strictly positive."
msgstr ""

#. module: uom
#: code:addons/uom/models/uom_uom.py:152
#, python-format
msgid ""
"The unit of measure %s defined on the order line doesn't belong to the same "
"category than the unit of measure %s defined on the product. Please correct "
"the unit of measure defined on the order line or on the product, they should"
" belong to the same category."
msgstr ""

#. module: uom
#: selection:uom.category,measure_type:0
msgid "Time"
msgstr ""

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__uom_type
msgid "Type"
msgstr "Tip"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__measure_type
msgid "Type of Measure"
msgstr ""

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__measure_type
msgid "Type of measurement category"
msgstr ""

#. module: uom
#: model:ir.model.fields,help:uom.field_uom_uom__active
msgid ""
"Uncheck the active field to disable a unit of measure without deleting it."
msgstr ""

#. module: uom
#: model:uom.category,name:uom.product_uom_categ_unit
msgid "Unit"
msgstr "Jedinica"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__name
msgid "Unit of Measure"
msgstr "Jedinica Mere"

#. module: uom
#: model:ir.actions.act_window,name:uom.product_uom_categ_form_action
msgid "Unit of Measure Categories"
msgstr ""

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__name
msgid "Unit of Measure Category"
msgstr ""

#. module: uom
#: model:uom.uom,name:uom.product_uom_unit
msgid "Unit(s)"
msgstr ""

#. module: uom
#: selection:uom.category,measure_type:0
msgid "Units"
msgstr ""

#. module: uom
#: model:ir.actions.act_window,name:uom.product_uom_form_action
#: model_terms:ir.ui.view,arch_db:uom.product_uom_form_view
#: model_terms:ir.ui.view,arch_db:uom.product_uom_tree_view
msgid "Units of Measure"
msgstr "Jedinice mere"

#. module: uom
#: model_terms:ir.ui.view,arch_db:uom.product_uom_categ_form_view
msgid "Units of Measure categories"
msgstr "Jedinica za merenje kategorija"

#. module: uom
#: model_terms:ir.actions.act_window,help:uom.product_uom_categ_form_action
msgid ""
"Units of measure belonging to the same category can be\n"
"            converted between each others. For example, in the category\n"
"            <i>'Time'</i>, you will have the following units of measure:\n"
"            Hours, Days."
msgstr ""

#. module: uom
#: code:addons/uom/models/uom_uom.py:93
#, python-format
msgid ""
"UoM category %s should have a reference unit of measure. If you just created"
" a new category, please record the 'reference' unit first."
msgstr ""

#. module: uom
#: code:addons/uom/models/uom_uom.py:95
#, python-format
msgid "UoM category %s should only have one reference unit of measure."
msgstr ""

#. module: uom
#: selection:uom.category,measure_type:0
#: model:uom.category,name:uom.product_uom_categ_vol
msgid "Volume"
msgstr "Obim"

#. module: uom
#: selection:uom.category,measure_type:0
#: model:uom.category,name:uom.product_uom_categ_kgm
msgid "Weight"
msgstr "Težina"

#. module: uom
#: model:uom.category,name:uom.uom_categ_wtime
msgid "Working Time"
msgstr "Vreme rada"

#. module: uom
#: sql_constraint:uom.category:0
msgid "You can have only one category per measurement type."
msgstr ""

#. module: uom
#: code:addons/uom/models/uom_uom.py:28
#, python-format
msgid "You cannot delete this UoM Category as it is used by the system."
msgstr ""

#. module: uom
#: code:addons/uom/models/uom_uom.py:115
#, python-format
msgid ""
"You cannot delete this UoM as it is used by the system. You should rather "
"archive it."
msgstr ""

#. module: uom
#: model_terms:ir.actions.act_window,help:uom.product_uom_form_action
msgid ""
"You must define a conversion rate between several Units of\n"
"            Measure within the same category."
msgstr ""

#. module: uom
#: model:uom.uom,name:uom.product_uom_cm
msgid "cm"
msgstr "cm"

#. module: uom
#: model_terms:ir.ui.view,arch_db:uom.product_uom_form_view
msgid "e.g: 1 * (reference unit) = ratio * (this unit)"
msgstr ""

#. module: uom
#: model_terms:ir.ui.view,arch_db:uom.product_uom_form_view
msgid "e.g: 1 * (this unit) = ratio * (reference unit)"
msgstr ""

#. module: uom
#: model:uom.uom,name:uom.product_uom_floz
msgid "fl oz"
msgstr ""

#. module: uom
#: model:uom.uom,name:uom.product_uom_foot
msgid "foot(ft)"
msgstr ""

#. module: uom
#: model:uom.uom,name:uom.product_uom_gal
msgid "gal(s)"
msgstr ""

#. module: uom
#: model:uom.uom,name:uom.product_uom_inch
msgid "inch(es)"
msgstr ""

#. module: uom
#: model:uom.uom,name:uom.product_uom_kgm
msgid "kg"
msgstr "kg"

#. module: uom
#: model:uom.uom,name:uom.product_uom_km
msgid "km"
msgstr "km"

#. module: uom
#: model:uom.uom,name:uom.product_uom_lb
msgid "lb(s)"
msgstr ""

#. module: uom
#: model:uom.uom,name:uom.product_uom_mile
msgid "mile(s)"
msgstr ""

#. module: uom
#: model:uom.uom,name:uom.product_uom_oz
msgid "oz(s)"
msgstr ""

#. module: uom
#: model:uom.uom,name:uom.product_uom_qt
msgid "qt"
msgstr ""
