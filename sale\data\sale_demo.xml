<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!-- Enable EUR currency since it's the currency on the company, pricelist and Sale Orders

            If the currency is not enabled, you cannot pay the demo SO's with a payment link bc the currency
            is disabled.
        -->
        <function model="res.currency" name="action_unarchive">
            <value model="res.currency" search="[('id', '=', obj().env.ref('product.list0').currency_id.id), ('active', '=', False)]"/>
        </function>

        <!-- We want to activate pay and sign by default for easier demoing. -->
        <record id="base.main_company" model="res.company">
            <field name="portal_confirmation_pay" eval="True"/>
        </record>

        <record id="base.user_demo" model="res.users">
            <field eval="[(4, ref('sales_team.group_sale_salesman'))]" name="groups_id"/>
        </record>

        <record model="crm.team" id="sales_team.team_sales_department">
            <field name="use_quotations" eval="True"/>
            <field name="invoiced_target">250000</field>
        </record>

        <record model="crm.team" id="sales_team.crm_team_1">
            <field name="use_quotations" eval="True"/>
            <field name="invoiced_target">40000</field>
        </record>

        <record id="utm_source_sale_order_0" model="utm.source">
            <field name="name">Sale Promotion 1</field>
        </record>

        <record id="sale_order_1" model="sale.order">
            <field name="partner_id" ref="base.res_partner_2"/>
            <field name="partner_invoice_id" ref="base.res_partner_2"/>
            <field name="partner_shipping_id" ref="base.res_partner_2"/>
            <field name="user_id" ref="base.user_demo"/>
            <field name="pricelist_id" ref="product.list0"/>
            <field name="team_id" ref="sales_team.team_sales_department"/>
            <field name="campaign_id" ref="utm.utm_campaign_email_campaign_products"/>
            <field name="medium_id" ref="utm.utm_medium_email"/>
            <field name="source_id" ref="sale.utm_source_sale_order_0"/>
            <field name="date_order" eval="(DateTime.today() - relativedelta(months=1)).strftime('%Y-%m-%d %H:%M')"/>
        </record>

        <record id="sale_order_line_1" model="sale.order.line">
            <field name="order_id" ref="sale_order_1"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_25').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_25"/>
            <field name="product_uom_qty">3</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">295.00</field>
        </record>

        <record id="sale_order_line_2" model="sale.order.line">
            <field name="order_id" ref="sale_order_1"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_delivery_02').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_delivery_02"/>
            <field name="product_uom_qty">5</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">145.00</field>
        </record>

        <record id="sale_order_line_3" model="sale.order.line">
            <field name="order_id" ref="sale_order_1"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_delivery_01').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_delivery_01"/>
            <field name="product_uom_qty">2</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">65.00</field>
        </record>

        <record id="sale_order_2" model="sale.order">
            <field name="partner_id" ref="base.res_partner_4"/>
            <field name="partner_invoice_id" ref="base.res_partner_address_13"/>
            <field name="partner_shipping_id" ref="base.res_partner_address_13"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="pricelist_id" ref="product.list0"/>
            <field name="team_id" ref="sales_team.team_sales_department"/>
            <field name="campaign_id" ref="utm.utm_campaign_email_campaign_products"/>
            <field name="medium_id" ref="utm.utm_medium_email"/>
            <field name="source_id" ref="sale.utm_source_sale_order_0"/>
            <field name="date_order" eval="(DateTime.today() - relativedelta(months=1)).strftime('%Y-%m-%d %H:%M')"/>
            <field name="tag_ids" eval="[(4, ref('sales_team.categ_oppor7'))]"/>
        </record>

        <record id="sale_order_line_4" model="sale.order.line">
            <field name="order_id" ref="sale_order_2"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_1').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_1"/>
            <field name="product_uom_qty">24</field>
            <field name="product_uom" ref="uom.product_uom_hour"/>
            <field name="price_unit">75.00</field>
        </record>

        <record id="sale_order_line_5" model="sale.order.line">
            <field name="order_id" ref="sale_order_2"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_2').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_2"/>
            <field name="product_uom_qty">30</field>
            <field name="product_uom" ref="uom.product_uom_hour"/>
            <field name="price_unit">38.25</field>
        </record>

        <record id="sale_order_3" model="sale.order">
            <field name="partner_id" ref="base.res_partner_4"/>
            <field name="partner_invoice_id" ref="base.res_partner_4"/>
            <field name="partner_shipping_id" ref="base.res_partner_4"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="pricelist_id" ref="product.list0"/>
            <field name="team_id" ref="sales_team.team_sales_department"/>
            <field name="campaign_id" ref="utm.utm_campaign_email_campaign_products"/>
            <field name="medium_id" ref="utm.utm_medium_email"/>
            <field name="source_id" ref="sale.utm_source_sale_order_0"/>
            <field name="tag_ids" eval="[(4, ref('sales_team.categ_oppor1')), (4, ref('sales_team.categ_oppor2'))]"/>
        </record>

        <record id="sale_order_line_6" model="sale.order.line">
            <field name="order_id" ref="sale_order_3"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_1').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_1"/>
            <field name="product_uom_qty">10</field>
            <field name="product_uom" ref="uom.product_uom_hour"/>
            <field name="price_unit">30.75</field>
        </record>

        <record id="sale_order_line_7" model="sale.order.line">
            <field name="order_id" ref="sale_order_3"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_delivery_01').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_delivery_01"/>
            <field name="product_uom_qty">1</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">70.00</field>
        </record>

        <record id="sale_order_4" model="sale.order">
            <field name="partner_id" ref="base.res_partner_3"/>
            <field name="partner_invoice_id" ref="base.res_partner_address_25"/>
            <field name="partner_shipping_id" ref="base.res_partner_address_25"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="pricelist_id" ref="product.list0"/>
            <field name="team_id" ref="sales_team.team_sales_department"/>
            <field name="campaign_id" ref="utm.utm_campaign_email_campaign_products"/>
            <field name="medium_id" ref="utm.utm_medium_email"/>
            <field name="source_id" ref="sale.utm_source_sale_order_0"/>
        </record>

        <record id="sale_order_line_8" model="sale.order.line">
            <field name="order_id" ref="sale_order_4"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_1').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_1"/>
            <field name="product_uom_qty">16</field>
            <field name="product_uom" ref="uom.product_uom_hour"/>
            <field name="price_unit">75.00</field>
        </record>

        <record id="sale_order_line_9" model="sale.order.line">
            <field name="order_id" ref="sale_order_4"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_delivery_02').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_delivery_02"/>
            <field name="product_uom_qty">10</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">45.00</field>
        </record>

        <record id="sale_order_line_10" model="sale.order.line">
            <field name="order_id" ref="sale_order_4"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.consu_delivery_02').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.consu_delivery_02"/>
            <field name="product_uom_qty">3</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">150.00</field>
        </record>

        <record id="sale_order_line_11" model="sale.order.line">
            <field name="order_id" ref="sale_order_4"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_delivery_01').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_delivery_01"/>
            <field name="product_uom_qty">2</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">70.00</field>
        </record>

        <record id="sale_order_5" model="sale.order">
            <field name="partner_id" ref="base.res_partner_2"/>
            <field name="partner_invoice_id" ref="base.res_partner_2"/>
            <field name="partner_shipping_id" ref="base.res_partner_2"/>
            <field name="user_id" ref="base.user_demo"/>
            <field name="pricelist_id" ref="product.list0"/>
            <field name="team_id" ref="sales_team.crm_team_1"/>
            <field name="campaign_id" ref="utm.utm_campaign_email_campaign_products"/>
            <field name="medium_id" ref="utm.utm_medium_email"/>
            <field name="source_id" ref="sale.utm_source_sale_order_0"/>
            <field name="date_order" eval="(DateTime.today() - relativedelta(months=1)).strftime('%Y-%m-%d %H:%M')"/>
        </record>

        <record id="sale_order_line_12" model="sale.order.line">
            <field name="order_id" ref="sale_order_5"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_delivery_02').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_delivery_02"/>
            <field name="product_uom_qty">1</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">405.00</field>
        </record>

        <record id="sale_order_6" model="sale.order">
            <field name="partner_id" ref="base.res_partner_18"/>
            <field name="partner_invoice_id" ref="base.res_partner_18"/>
            <field name="partner_shipping_id" ref="base.res_partner_18"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="pricelist_id" ref="product.list0"/>
            <field name="team_id" ref="sales_team.crm_team_1"/>
            <field name="campaign_id" ref="utm.utm_campaign_email_campaign_products"/>
            <field name="medium_id" ref="utm.utm_medium_email"/>
            <field name="source_id" ref="sale.utm_source_sale_order_0"/>
            <field name="tag_ids" eval="[(4, ref('sales_team.categ_oppor6'))]"/>
        </record>

        <record id="sale_order_line_15" model="sale.order.line">
            <field name="order_id" ref="sale_order_6"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_4').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_4"/>
            <field name="product_uom_qty">1</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">750.00</field>
        </record>

        <record id="sale_order_7" model="sale.order">
            <field name="partner_id" ref="base.res_partner_3"/>
            <field name="partner_invoice_id" ref="base.res_partner_address_11"/>
            <field name="partner_shipping_id" ref="base.res_partner_address_11"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="pricelist_id" ref="product.list0"/>
            <field name="team_id" ref="sales_team.team_sales_department"/>
            <field name="campaign_id" ref="utm.utm_campaign_email_campaign_products"/>
            <field name="medium_id" ref="utm.utm_medium_email"/>
            <field name="source_id" ref="sale.utm_source_sale_order_0"/>
            <field name="tag_ids" eval="[(4, ref('sales_team.categ_oppor4'))]"/>
        </record>

        <record id="sale_order_line_16" model="sale.order.line">
            <field name="order_id" ref="sale_order_7"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_25').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_25"/>
            <field name="product_uom_qty">5</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">295.00</field>
        </record>

        <record id="sale_order_line_17" model="sale.order.line">
            <field name="order_id" ref="sale_order_7"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.consu_delivery_01').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.consu_delivery_01"/>
            <field name="product_uom_qty">1</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">173.00</field>
        </record>

        <record id="sale_order_line_18" model="sale.order.line">
            <field name="order_id" ref="sale_order_7"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_delivery_02').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_delivery_02"/>
            <field name="product_uom_qty">1</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">40.00</field>
        </record>

        <record id="sale_order_line_19" model="sale.order.line">
            <field name="order_id" ref="sale_order_7"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_delivery_01').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_delivery_01"/>
            <field name="product_uom_qty">1</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">18.00</field>
        </record>

        <record id="sale_order_8" model="sale.order">
            <field name="name">Test/001</field>
            <field name="partner_id" ref="base.res_partner_3"/>
            <field name="partner_invoice_id" ref="base.res_partner_address_25"/>
            <field name="partner_shipping_id" ref="base.res_partner_address_25"/>
            <field name="user_id" ref="base.user_demo"/>
            <field name="pricelist_id" ref="product.list0"/>
            <field name="team_id" ref="sales_team.crm_team_1"/>
            <field name="campaign_id" ref="utm.utm_campaign_email_campaign_products"/>
            <field name="medium_id" ref="utm.utm_medium_email"/>
            <field name="source_id" ref="sale.utm_source_sale_order_0"/>
        </record>

        <record id="sale_order_line_20" model="sale.order.line">
            <field name="order_id" ref="sale_order_8"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_27').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_27"/>
            <field name="product_uom_qty">2</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">110.50</field>
        </record>

        <record id="sale_order_line_21" model="sale.order.line">
            <field name="order_id" ref="sale_order_8"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_12').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_12"/>
            <field name="product_uom_qty">2</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">120.50</field>
        </record>

        <!-- additional demo data for pretty graphs in sales dashboard -->

        <record id="sale_order_9" model="sale.order">
            <field name="partner_id" ref="base.res_partner_3"/>
            <field name="partner_invoice_id" ref="base.res_partner_address_25"/>
            <field name="partner_shipping_id" ref="base.res_partner_address_25"/>
            <field name="user_id" ref="base.user_demo"/>
            <field name="pricelist_id" ref="product.list0"/>
            <field name="team_id" ref="sales_team.crm_team_1"/>
            <field name="campaign_id" ref="utm.utm_campaign_email_campaign_products"/>
            <field name="medium_id" ref="utm.utm_medium_email"/>
            <field name="source_id" ref="sale.utm_source_sale_order_0"/>
            <field name="date_order" eval="(datetime.now()-relativedelta(days=7)).strftime('%Y-%m-%d %H:%M:%S')"/>
        </record>

        <record id="sale_order_line_22" model="sale.order.line">
            <field name="order_id" ref="sale_order_9"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_27').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_27"/>
            <field name="product_uom_qty">3</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">97.50</field>
        </record>

        <record id="sale_order_line_23" model="sale.order.line">
            <field name="order_id" ref="sale_order_9"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_12').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_12"/>
            <field name="product_uom_qty">3</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">120.50</field>
        </record>

        <record id="sale_order_10" model="sale.order">
            <field name="partner_id" ref="base.res_partner_3"/>
            <field name="partner_invoice_id" ref="base.res_partner_address_25"/>
            <field name="partner_shipping_id" ref="base.res_partner_address_25"/>
            <field name="user_id" ref="base.user_demo"/>
            <field name="pricelist_id" ref="product.list0"/>
            <field name="team_id" ref="sales_team.crm_team_1"/>
            <field name="campaign_id" ref="utm.utm_campaign_email_campaign_products"/>
            <field name="medium_id" ref="utm.utm_medium_email"/>
            <field name="source_id" ref="sale.utm_source_sale_order_0"/>
            <field name="date_order" eval="(datetime.now()-relativedelta(days=14)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="tag_ids" eval="[(4, ref('sales_team.categ_oppor3'))]"/>
        </record>

        <record id="sale_order_line_24" model="sale.order.line">
            <field name="order_id" ref="sale_order_10"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_25').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_25"/>
            <field name="product_uom_qty">2</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">255.00</field>
        </record>

        <record id="sale_order_line_25" model="sale.order.line">
            <field name="order_id" ref="sale_order_10"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_12').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_12"/>
            <field name="product_uom_qty">2</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">120.50</field>
        </record>


        <record id="sale_order_11" model="sale.order">
            <field name="partner_id" ref="base.res_partner_3"/>
            <field name="partner_invoice_id" ref="base.res_partner_address_25"/>
            <field name="partner_shipping_id" ref="base.res_partner_address_25"/>
            <field name="user_id" ref="base.user_demo"/>
            <field name="pricelist_id" ref="product.list0"/>
            <field name="team_id" ref="sales_team.crm_team_1"/>
            <field name="campaign_id" ref="utm.utm_campaign_email_campaign_products"/>
            <field name="medium_id" ref="utm.utm_medium_email"/>
            <field name="source_id" ref="sale.utm_source_sale_order_0"/>
            <field name="date_order" eval="(datetime.now()-relativedelta(days=21)).strftime('%Y-%m-%d %H:%M:%S')"/>
        </record>

        <record id="sale_order_line_26" model="sale.order.line">
            <field name="order_id" ref="sale_order_11"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_25').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_25"/>
            <field name="product_uom_qty">3</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">245.00</field>
        </record>

        <record id="sale_order_line_27" model="sale.order.line">
            <field name="order_id" ref="sale_order_11"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_12').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_12"/>
            <field name="product_uom_qty">3</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">120.50</field>
        </record>

        <record id="sale_order_12" model="sale.order">
            <field name="partner_id" ref="base.res_partner_3"/>
            <field name="partner_invoice_id" ref="base.res_partner_address_25"/>
            <field name="partner_shipping_id" ref="base.res_partner_address_25"/>
            <field name="user_id" ref="base.user_demo"/>
            <field name="pricelist_id" ref="product.list0"/>
            <field name="team_id" ref="sales_team.crm_team_1"/>
            <field name="campaign_id" ref="utm.utm_campaign_email_campaign_products"/>
            <field name="medium_id" ref="utm.utm_medium_email"/>
            <field name="source_id" ref="sale.utm_source_sale_order_0"/>
            <field name="date_order" eval="(datetime.now()-relativedelta(days=28)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="tag_ids" eval="[(4, ref('sales_team.categ_oppor1'))]"/>
        </record>

        <record id="sale_order_line_28" model="sale.order.line">
            <field name="order_id" ref="sale_order_12"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_25').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_25"/>
            <field name="product_uom_qty">1</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">315.00</field>
        </record>

        <record id="sale_order_line_29" model="sale.order.line">
            <field name="order_id" ref="sale_order_12"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_12').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_12"/>
            <field name="product_uom_qty">2</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">120.50</field>
        </record>

        <record id="sale_order_13" model="sale.order">
            <field name="partner_id" ref="base.res_partner_3"/>
            <field name="partner_invoice_id" ref="base.res_partner_address_25"/>
            <field name="partner_shipping_id" ref="base.res_partner_address_25"/>
            <field name="user_id" ref="base.user_demo"/>
            <field name="pricelist_id" ref="product.list0"/>
            <field name="team_id" ref="sales_team.crm_team_1"/>
            <field name="campaign_id" ref="utm.utm_campaign_email_campaign_products"/>
            <field name="medium_id" ref="utm.utm_medium_email"/>
            <field name="source_id" ref="sale.utm_source_sale_order_0"/>
            <field name="date_order" eval="(datetime.now()-relativedelta(days=35)).strftime('%Y-%m-%d %H:%M:%S')"/>
        </record>

        <record id="sale_order_line_30" model="sale.order.line">
            <field name="order_id" ref="sale_order_13"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_25').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_25"/>
            <field name="product_uom_qty">1</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">295.00</field>
        </record>

        <record id="sale_order_line_31" model="sale.order.line">
            <field name="order_id" ref="sale_order_13"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_12').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_12"/>
            <field name="product_uom_qty">1</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">120.50</field>
        </record>

        <record id="sale_order_14" model="sale.order">
            <field name="partner_id" ref="base.res_partner_3"/>
            <field name="partner_invoice_id" ref="base.res_partner_address_25"/>
            <field name="partner_shipping_id" ref="base.res_partner_address_25"/>
            <field name="user_id" ref="base.user_demo"/>
            <field name="pricelist_id" ref="product.list0"/>
            <field name="team_id" ref="sales_team.team_sales_department"/>
            <field name="campaign_id" ref="utm.utm_campaign_email_campaign_products"/>
            <field name="medium_id" ref="utm.utm_medium_email"/>
            <field name="source_id" ref="sale.utm_source_sale_order_0"/>
            <field name="date_order" eval="(datetime.now()-relativedelta(days=7)).strftime('%Y-%m-%d %H:%M:%S')"/>
        </record>

        <record id="sale_order_line_32" model="sale.order.line">
            <field name="order_id" ref="sale_order_14"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_25').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_25"/>
            <field name="product_uom_qty">4</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">275.00</field>
        </record>

        <record id="sale_order_line_33" model="sale.order.line">
            <field name="order_id" ref="sale_order_14"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_12').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_12"/>
            <field name="product_uom_qty">4</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">120.50</field>
        </record>

        <record id="sale_order_15" model="sale.order">
            <field name="partner_id" ref="base.res_partner_3"/>
            <field name="partner_invoice_id" ref="base.res_partner_address_25"/>
            <field name="partner_shipping_id" ref="base.res_partner_address_25"/>
            <field name="user_id" ref="base.user_demo"/>
            <field name="pricelist_id" ref="product.list0"/>
            <field name="team_id" ref="sales_team.team_sales_department"/>
            <field name="campaign_id" ref="utm.utm_campaign_email_campaign_products"/>
            <field name="medium_id" ref="utm.utm_medium_email"/>
            <field name="source_id" ref="sale.utm_source_sale_order_0"/>
            <field name="date_order" eval="(datetime.now()-relativedelta(days=14)).strftime('%Y-%m-%d %H:%M:%S')"/>
        </record>

        <record id="sale_order_line_34" model="sale.order.line">
            <field name="order_id" ref="sale_order_15"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_25').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_25"/>
            <field name="product_uom_qty">4</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">295.00</field>
        </record>

        <record id="sale_order_line_35" model="sale.order.line">
            <field name="order_id" ref="sale_order_15"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_12').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_12"/>
            <field name="product_uom_qty">3</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">120.50</field>
        </record>

        <record id="sale_order_16" model="sale.order">
            <field name="partner_id" ref="base.res_partner_3"/>
            <field name="partner_invoice_id" ref="base.res_partner_address_25"/>
            <field name="partner_shipping_id" ref="base.res_partner_address_25"/>
            <field name="user_id" ref="base.user_demo"/>
            <field name="pricelist_id" ref="product.list0"/>
            <field name="team_id" ref="sales_team.team_sales_department"/>
            <field name="campaign_id" ref="utm.utm_campaign_email_campaign_products"/>
            <field name="medium_id" ref="utm.utm_medium_email"/>
            <field name="source_id" ref="sale.utm_source_sale_order_0"/>
            <field name="date_order" eval="(datetime.now()-relativedelta(days=21)).strftime('%Y-%m-%d %H:%M:%S')"/>
        </record>

        <record id="sale_order_line_36" model="sale.order.line">
            <field name="order_id" ref="sale_order_16"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_25').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_25"/>
            <field name="product_uom_qty">3</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">275.00</field>
        </record>

        <record id="sale_order_line_37" model="sale.order.line">
            <field name="order_id" ref="sale_order_16"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_12').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_12"/>
            <field name="product_uom_qty">3</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">120.50</field>
        </record>

        <record id="sale_order_17" model="sale.order">
            <field name="partner_id" ref="base.res_partner_3"/>
            <field name="partner_invoice_id" ref="base.res_partner_address_25"/>
            <field name="partner_shipping_id" ref="base.res_partner_address_25"/>
            <field name="user_id" ref="base.user_demo"/>
            <field name="pricelist_id" ref="product.list0"/>
            <field name="team_id" ref="sales_team.team_sales_department"/>
            <field name="campaign_id" ref="utm.utm_campaign_email_campaign_products"/>
            <field name="medium_id" ref="utm.utm_medium_email"/>
            <field name="source_id" ref="sale.utm_source_sale_order_0"/>
            <field name="date_order" eval="(datetime.now()-relativedelta(days=28)).strftime('%Y-%m-%d %H:%M:%S')"/>
        </record>

        <record id="sale_order_line_38" model="sale.order.line">
            <field name="order_id" ref="sale_order_17"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_25').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_25"/>
            <field name="product_uom_qty">2</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">355.00</field>
        </record>

        <record id="sale_order_line_39" model="sale.order.line">
            <field name="order_id" ref="sale_order_17"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_12').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_12"/>
            <field name="product_uom_qty">2</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">120.50</field>
        </record>

        <record id="sale_order_18" model="sale.order">
            <field name="partner_id" ref="base.res_partner_3"/>
            <field name="partner_invoice_id" ref="base.res_partner_address_25"/>
            <field name="partner_shipping_id" ref="base.res_partner_address_25"/>
            <field name="user_id" ref="base.user_demo"/>
            <field name="pricelist_id" ref="product.list0"/>
            <field name="team_id" ref="sales_team.team_sales_department"/>
            <field name="campaign_id" ref="utm.utm_campaign_email_campaign_products"/>
            <field name="medium_id" ref="utm.utm_medium_email"/>
            <field name="source_id" ref="sale.utm_source_sale_order_0"/>
            <field name="date_order" eval="(datetime.now()-relativedelta(days=35)).strftime('%Y-%m-%d %H:%M:%S')"/>
        </record>

        <record id="sale_order_line_40" model="sale.order.line">
            <field name="order_id" ref="sale_order_18"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_25').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_25"/>
            <field name="product_uom_qty">2</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">295.00</field>
        </record>

        <record id="sale_order_line_41" model="sale.order.line">
            <field name="order_id" ref="sale_order_18"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_12').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_12"/>
            <field name="product_uom_qty">2</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">120.50</field>
        </record>

        <!-- Confirm some Sales Orders-->
        <function model="sale.order" name="action_confirm" eval="[[ref('sale_order_4')]]"/>
        <function model="sale.order" name="action_confirm" eval="[[ref('sale_order_6')]]"/>
        <function model="sale.order" name="action_confirm" eval="[[ref('sale_order_7')]]"/>
        <function model="sale.order" name="action_confirm" eval="[[ref('sale_order_8')]]"/>
        <function model="sale.order" name="action_confirm" eval="[[ref('sale_order_9')]]"/>
        <function model="sale.order" name="action_confirm" eval="[[ref('sale_order_10')]]"/>
        <function model="sale.order" name="action_confirm" eval="[[ref('sale_order_11')]]"/>
        <function model="sale.order" name="action_confirm" eval="[[ref('sale_order_12')]]"/>
        <function model="sale.order" name="action_confirm" eval="[[ref('sale_order_13')]]"/>
        <function model="sale.order" name="action_confirm" eval="[[ref('sale_order_14')]]"/>
        <function model="sale.order" name="action_confirm" eval="[[ref('sale_order_15')]]"/>
        <function model="sale.order" name="action_confirm" eval="[[ref('sale_order_16')]]"/>
        <function model="sale.order" name="action_confirm" eval="[[ref('sale_order_17')]]"/>
        <function model="sale.order" name="action_confirm" eval="[[ref('sale_order_18')]]"/>

        <!-- Setting date_order in the past for beautiful spread -->
        <record id="sale_order_9" model="sale.order">
            <field name="date_order" eval="datetime.now() - relativedelta(days=7)"/>
        </record>

        <record id="sale_order_11" model="sale.order">
            <field name="date_order" eval="datetime.now() - relativedelta(days=21)"/>
        </record>

        <record id="sale_order_15" model="sale.order">
            <field name="date_order" eval="datetime.now() - relativedelta(days=14)"/>
        </record>

        <record id="sale_order_17" model="sale.order">
            <field name="date_order" eval="datetime.now() - relativedelta(days=28)"/>
        </record>

        <record id="sale_order_18" model="sale.order">
            <field name="date_order" eval="datetime.now() - relativedelta(days=35)"/>
        </record>

        <record id="message_sale_1" model="mail.message">
            <field name="model">sale.order</field>
            <field name="res_id" ref="sale_order_2"/>
            <field name="body">Hi,
I have a question regarding services pricing: I heard of a possible discount for quantities exceeding 25 hours.
Could you confirm, please?</field>
            <field name="message_type">comment</field>
            <field name="author_id" ref="base.partner_demo"/>
        </record>

        <record id="message_sale_2" model="mail.message">
            <field name="model">sale.order</field>
            <field name="res_id" ref="sale_order_2"/>
            <field name="parent_id" ref="message_sale_1"/>
            <field name="body">Hello,
Unfortunately that was a temporary discount that is not available anymore.
Do you still plan to confirm the order based on the quoted prices?
Thanks!</field>
            <field name="message_type">comment</field>
            <field name="author_id" ref="base.partner_root"/>
        </record>

        <record id="message_sale_3" model="mail.message">
            <field name="model">sale.order</field>
            <field name="res_id" ref="sale_order_2"/>
            <field name="parent_id" ref="message_sale_2"/>
            <field name="body">Alright, thanks for the clarification. I will confirm the order as soon as I get my manager's approval.</field>
            <field name="message_type">comment</field>
            <field name="author_id" ref="base.partner_demo"/>
        </record>
        <!-- sale advance demo.. -->
        <!-- Demo Data for Product -->

        <record id="advance_product_0" model="product.product">
            <field name="name">Deposit</field>
            <field name="categ_id" ref="product.product_category_3"/>
            <field name="type">service</field>
            <field name="list_price">150.0</field>
            <field name="invoice_policy">order</field>
            <field name="standard_price">100.0</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="company_id" eval="[]"/>
            <field name="image_1920" type="base64" file="sale/static/img/advance_product_0-image.jpg"/>
            <field name="taxes_id" eval="[]"/>
            <field name="supplier_taxes_id" eval="[]"/>
        </record>

        <record id="base.user_demo" model="res.users">
            <field eval="[(4, ref('sales_team.group_sale_salesman'))]" name="groups_id"/>
        </record>

        <!-- records coming from website_portal_sale, now dead module -->
        <record id="portal_sale_order_1" model="sale.order">
            <field name="partner_id" ref="base.partner_demo_portal"/>
            <field name="partner_invoice_id" ref="base.partner_demo_portal"/>
            <field name="partner_shipping_id" ref="base.partner_demo_portal"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="pricelist_id" ref="product.list0"/>
            <field name="state">sent</field>
            <field name="team_id" ref="sales_team.team_sales_department"/>
            <field name="date_order" eval="(DateTime.today() - relativedelta(months=1)).strftime('%Y-%m-%d %H:%M')"/>
            <field name="message_partner_ids" eval="[(4, ref('base.partner_demo_portal'))]"/>
            <field name="tag_ids" eval="[(4, ref('sales_team.categ_oppor4'))]"/>
        </record>

        <record id="portal_sale_order_line_1" model="sale.order.line">
            <field name="order_id" ref="portal_sale_order_1"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_25').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_25"/>
            <field name="product_uom_qty">3</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">295.00</field>
        </record>

        <record id="portal_sale_order_line_2" model="sale.order.line">
            <field name="order_id" ref="portal_sale_order_1"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_delivery_02').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_delivery_02"/>
            <field name="product_uom_qty">5</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">145.00</field>
        </record>

        <record id="portal_sale_order_line_3" model="sale.order.line">
            <field name="order_id" ref="portal_sale_order_1"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_delivery_01').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_delivery_01"/>
            <field name="product_uom_qty">2</field>
            <field name="product_uom" ref="uom.product_uom_unit"/>
            <field name="price_unit">65.00</field>
        </record>

        <record id="portal_sale_order_2" model="sale.order">
            <field name="partner_id" ref="base.partner_demo_portal"/>
            <field name="partner_invoice_id" ref="base.partner_demo_portal"/>
            <field name="partner_shipping_id" ref="base.partner_demo_portal"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="pricelist_id" ref="product.list0"/>
            <field name="team_id" ref="sales_team.team_sales_department"/>
            <field name="date_order" eval="(DateTime.today() - relativedelta(months=1)).strftime('%Y-%m-%d %H:%M')"/>
            <field name="message_partner_ids" eval="[(4, ref('base.partner_demo_portal'))]"/>
        </record>

        <record id="portal_sale_order_line_4" model="sale.order.line">
            <field name="order_id" ref="portal_sale_order_2"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_1').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_1"/>
            <field name="product_uom_qty">24</field>
            <field name="product_uom" ref="uom.product_uom_hour"/>
            <field name="price_unit">75.00</field>
        </record>

        <record id="portal_sale_order_line_5" model="sale.order.line">
            <field name="order_id" ref="portal_sale_order_2"/>
            <field name="name" model="sale.order.line" eval="obj().env.ref('product.product_product_2').get_product_multiline_description_sale()"/>
            <field name="product_id" ref="product.product_product_2"/>
            <field name="product_uom_qty">30</field>
            <field name="product_uom" ref="uom.product_uom_hour"/>
            <field name="price_unit">38.25</field>
        </record>

        <record id="product.product_attribute_2" model="product.attribute">
            <field name="display_type">color</field>
        </record>
        <record id="product.product_attribute_3" model="product.attribute">
            <field name="display_type">select</field>
        </record>

        <record id="product.product_attribute_value_3" model="product.attribute.value">
            <field name="html_color">#FFFFFF</field>
        </record>
        <record id="product.product_attribute_value_4" model="product.attribute.value">
            <field name="html_color">#000000</field>
        </record>

        <!-- Confirm some Sales Orders-->
        <function model="sale.order" name="action_confirm" eval="[[ref('portal_sale_order_2')]]"/>

        <record id="portal_sale_order_2" model="sale.order">
            <field name="date_order" eval="datetime.now() - relativedelta(months=1)"/>
        </record>

        <record id="product_attribute_value_7" model="product.attribute.value">
            <field name="name">Custom</field>
            <field name="attribute_id" ref="product.product_attribute_1"/>
            <field name="is_custom">True</field>
            <field name="sequence">3</field>
        </record>

        <record id="product.product_4_attribute_1_product_template_attribute_line" model="product.template.attribute.line">
            <field name="value_ids" eval="[(4,ref('product_attribute_value_7'))]"/>
        </record>

        <!--
        Handle automatically created product.template.attribute.value.
        Check "product.product_4_attribute_1_value_2" for more information about this
        -->
        <function model="ir.model.data" name="_update_xmlids">
            <value model="base" eval="[{
                'xml_id': 'sale.product_4_attribute_1_value_3',
                'record': obj().env.ref('product.product_4_attribute_1_product_template_attribute_line').product_template_value_ids[2],
                'noupdate': True,
            }]"/>
        </function>

        <function model="ir.model.data" name="_update_xmlids">
            <value model="base" eval="[{
                'xml_id': 'sale.product_product_4e',
                'record': obj().env.ref('product.product_product_4_product_template')._get_variant_for_combination(obj().env.ref('sale.product_4_attribute_1_value_3') + obj().env.ref('product.product_4_attribute_2_value_1')),
                'noupdate': True,
            }, {
                'xml_id': 'sale.product_product_4f',
                'record': obj().env.ref('product.product_product_4_product_template')._get_variant_for_combination(obj().env.ref('sale.product_4_attribute_1_value_3') + obj().env.ref('product.product_4_attribute_2_value_2')),
                'noupdate': True,
            },]"/>
        </function>

        <record id="product_product_4e" model="product.product">
            <field name="default_code">DESK0005</field>
            <field name="weight">0.01</field>
        </record>

        <record id="product_product_4f" model="product.product">
            <field name="default_code">DESK0006</field>
            <field name="weight">0.01</field>
        </record>

        <record id="product_template_attribute_exclusion_1" model="product.template.attribute.exclusion">
            <field name="product_tmpl_id" ref="product.product_product_4_product_template" />
            <field name="value_ids" eval="[(6,0,[ref('product.product_4_attribute_2_value_2')])]"/>
        </record>

        <record id="product_template_attribute_exclusion_2" model="product.template.attribute.exclusion">
            <field name="product_tmpl_id" ref="product.product_product_11_product_template" />
            <field name="value_ids" eval="[(6,0,[ref('product.product_11_attribute_1_value_1')])]"/>
        </record>

        <!--
        The "Customizable Desk's Aluminium" attribute value will excude:
        - The "Customizable Desk's Black" attribute
        - The "Office Chair's Steel" attribute
         -->
        <record id="product.product_4_attribute_1_value_2" model="product.template.attribute.value">
            <field name="exclude_for" eval="[(6,0,[ref('sale.product_template_attribute_exclusion_1') ,ref('sale.product_template_attribute_exclusion_2')])]" />
        </record>

        <record id="product_template_attribute_exclusion_3" model="product.template.attribute.exclusion">
            <field name="product_tmpl_id" ref="product.product_product_11_product_template" />
            <field name="value_ids" eval="[(6,0,[ref('product.product_11_attribute_1_value_2')])]"/>
        </record>

        <!--
        The "Customizable Desk's Steel" attribute value will excude:
        - The "Office Chair's Aluminium" attribute
         -->
        <record id="product.product_4_attribute_1_value_1" model="product.template.attribute.value">
            <field name="exclude_for" eval="[(6,0,[ref('sale.product_template_attribute_exclusion_3')])]" />
        </record>

        <!-- Activities of sales order -->
        <record id="sale_activity_2" model="mail.activity">
            <field name="res_id" ref="sale.sale_order_3"/>
            <field name="res_model_id" ref="sale.model_sale_order"/>
            <field name="activity_type_id" ref="mail.mail_activity_data_email"/>
            <field name="date_deadline" eval="DateTime.today().strftime('%Y-%m-%d %H:%M')"/>
            <field name="summary">Answer questions</field>
            <field name="create_uid" ref="base.user_admin"/>
            <field name="user_id" ref="base.user_admin"/>
        </record>
        <record id="sale_activity_3" model="mail.activity">
            <field name="res_id" ref="sale.sale_order_4"/>
            <field name="res_model_id" ref="sale.model_sale_order"/>
            <field name="activity_type_id" ref="sale.mail_act_sale_upsell"/>
            <field name="date_deadline" eval="(DateTime.today() + relativedelta(days=5)).strftime('%Y-%m-%d %H:%M')"/>
            <field name="create_uid" ref="base.user_admin"/>
            <field name="user_id" ref="base.user_admin"/>
        </record>
        <record id="sale_activity_4" model="mail.activity">
            <field name="res_id" ref="sale.sale_order_5"/>
            <field name="res_model_id" ref="sale.model_sale_order"/>
            <field name="activity_type_id" ref="mail.mail_activity_data_email"/>
            <field name="date_deadline" eval="DateTime.today().strftime('%Y-%m-%d %H:%M')"/>
            <field name="create_uid" ref="base.user_demo"/>
            <field name="user_id" ref="base.user_demo"/>
        </record>
        <record id="sale_activity_6" model="mail.activity">
            <field name="res_id" ref="sale.sale_order_7"/>
            <field name="res_model_id" ref="sale.model_sale_order"/>
            <field name="activity_type_id" ref="mail.mail_activity_data_todo"/>
            <field name="date_deadline" eval="(DateTime.today() + relativedelta(days=5)).strftime('%Y-%m-%d %H:%M')"/>
            <field name="summary">Check delivery requirements</field>
            <field name="create_uid" ref="base.user_admin"/>
            <field name="user_id" ref="base.user_admin"/>
        </record>
        <record id="sale_activity_7" model="mail.activity">
            <field name="res_id" ref="sale.sale_order_10"/>
            <field name="res_model_id" ref="sale.model_sale_order"/>
            <field name="activity_type_id" ref="mail.mail_activity_data_todo"/>
            <field name="date_deadline" eval="(DateTime.today() + relativedelta(days=5)).strftime('%Y-%m-%d %H:%M')"/>
            <field name="summary">Confirm Delivery</field>
            <field name="create_uid" ref="base.user_demo"/>
            <field name="user_id" ref="base.user_demo"/>
        </record>
        <record id="sale_activity_8" model="mail.activity">
            <field name="res_id" ref="sale.sale_order_12"/>
            <field name="res_model_id" ref="sale.model_sale_order"/>
            <field name="activity_type_id" ref="mail.mail_activity_data_email"/>
            <field name="date_deadline" eval="(DateTime.today() + relativedelta(days=5)).strftime('%Y-%m-%d %H:%M')"/>
            <field name="create_uid" ref="base.user_demo"/>
            <field name="user_id" ref="base.user_demo"/>
        </record>
        <record id="sale_activity_9" model="mail.activity">
            <field name="res_id" ref="sale.sale_order_16"/>
            <field name="res_model_id" ref="sale.model_sale_order"/>
            <field name="activity_type_id" ref="sale.mail_act_sale_upsell"/>
            <field name="date_deadline" eval="DateTime.today().strftime('%Y-%m-%d %H:%M')"/>
            <field name="create_uid" ref="base.user_demo"/>
            <field name="user_id" ref="base.user_demo"/>
        </record>
        <record id="sale_activity_10" model="mail.activity">
            <field name="res_id" ref="sale.portal_sale_order_1"/>
            <field name="res_model_id" ref="sale.model_sale_order"/>
            <field name="activity_type_id" ref="mail.mail_activity_data_todo"/>
            <field name="date_deadline" eval="(DateTime.today() + relativedelta(days=5)).strftime('%Y-%m-%d %H:%M')"/>
            <field name="summary">Get quote confirmation</field>
            <field name="create_uid" ref="base.user_admin"/>
            <field name="user_id" ref="base.user_admin"/>
        </record>

    </data>
</odoo>
