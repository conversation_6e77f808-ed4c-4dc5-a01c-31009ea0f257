# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_payumoney
# 
# Translators:
# <PERSON>, 2021
# Jonata<PERSON> G<PERSON>, 2022
# ma<PERSON><PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:49+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: ma<PERSON><PERSON>, 2022\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: payment_payumoney
#: code:addons/payment_payumoney/models/payment_transaction.py:0
#, python-format
msgid "Invalid shasign: received %(sign)s, computed %(computed)s."
msgstr "SHASIGN inválido: %(sign)s recibido, %(computed)s calculado."

#. module: payment_payumoney
#: model:ir.model.fields,field_description:payment_payumoney.field_payment_acquirer__payumoney_merchant_key
msgid "Merchant Key"
msgstr "Clave del comerciante"

#. module: payment_payumoney
#: model:ir.model.fields,field_description:payment_payumoney.field_payment_acquirer__payumoney_merchant_salt
msgid "Merchant Salt"
msgstr "Merchant Salt"

#. module: payment_payumoney
#: code:addons/payment_payumoney/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr ""
"No se ha encontrado ninguna transacción que coincida con la referencia %s."

#. module: payment_payumoney
#: model:account.payment.method,name:payment_payumoney.payment_method_payumoney
#: model:ir.model.fields.selection,name:payment_payumoney.selection__payment_acquirer__provider__payumoney
msgid "PayUmoney"
msgstr "PayUmoney"

#. module: payment_payumoney
#: model:ir.model,name:payment_payumoney.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Pago del comprador"

#. module: payment_payumoney
#: model:ir.model,name:payment_payumoney.model_account_payment_method
msgid "Payment Methods"
msgstr "Métodos de pago"

#. module: payment_payumoney
#: model:ir.model,name:payment_payumoney.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transacción de pago"

#. module: payment_payumoney
#: model:ir.model.fields,field_description:payment_payumoney.field_payment_acquirer__provider
msgid "Provider"
msgstr "Proveedor"

#. module: payment_payumoney
#: code:addons/payment_payumoney/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing reference (%(ref)s) or shasign (%(sign)s)"
msgstr "Datos recibidos sin referencia (%(ref)s) o SHASIGN (%(sign)s)"

#. module: payment_payumoney
#: model:ir.model.fields,help:payment_payumoney.field_payment_acquirer__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr "El proveedor de servicios de pago a utilizar con este adquirente"

#. module: payment_payumoney
#: model:ir.model.fields,help:payment_payumoney.field_payment_acquirer__payumoney_merchant_key
msgid "The key solely used to identify the account with PayU money"
msgstr "La clave utilizada únicamente para identificar la cuenta con PayU"

#. module: payment_payumoney
#: code:addons/payment_payumoney/models/payment_transaction.py:0
#, python-format
msgid "The payment encountered an error with code %s"
msgstr "El pago tuvo un error con el código %s  "
