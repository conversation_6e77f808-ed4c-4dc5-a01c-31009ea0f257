# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_presence
# 
# Translators:
# <PERSON><PERSON><PERSON> <karol<PERSON>.ton<PERSON>@storm.hr>, 2022
# <PERSON> <k<PERSON><PERSON><PERSON>.<EMAIL>>, 2022
# 0ba0ac30481a756f36528ba6f9a4317e_6443a87 <52eefe24349934c364624ef40611b7a3_1010754>, 2022
# <PERSON><PERSON><PERSON><PERSON> <durdi<PERSON>.<PERSON><PERSON><PERSON><PERSON>@storm.hr>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Croatian (https://app.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: hr_presence
#: model:mail.template,body_html:hr_presence.mail_template_presence
msgid ""
"<div>\n"
"                    Dear <t t-out=\"object.name or ''\">Abigail Peterson</t>,<br/><br/>\n"
"                    Exception made if there was a mistake of ours, it seems that you are not at your office and there is not request of time off from you.<br/>\n"
"                    Please, take appropriate measures in order to carry out this work absence.<br/>\n"
"                    Do not hesitate to contact your manager or the human resource department.\n"
"                    <br/>Best Regards,<br/><br/>\n"
"                </div>\n"
"            "
msgstr ""

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_presence_search
msgid "Absence/Presence"
msgstr "Odsustvo/Prisustvo"

#. module: hr_presence
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee__hr_presence_state_display__absent
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_base__hr_presence_state_display__absent
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_public__hr_presence_state_display__absent
msgid "Absent"
msgstr "Odsutan"

#. module: hr_presence
#: model:ir.model,name:hr_presence.model_hr_employee_base
msgid "Basic Employee"
msgstr "Obični zaposlenik"

#. module: hr_presence
#: model:ir.model,name:hr_presence.model_res_company
msgid "Companies"
msgstr "Tvrtke"

#. module: hr_presence
#: code:addons/hr_presence/models/hr_employee.py:0
#, python-format
msgid "Compose Email"
msgstr "Sastavi e-poštu"

#. module: hr_presence
#: model:ir.actions.server,name:hr_presence.ir_actions_server_action_open_presence_view
msgid "Compute presence and open presence view"
msgstr "Izračunaj prisutnost i otvori pogled "

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_res_users_log__create_uid
msgid "Create Uid"
msgstr "Kreirao"

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee__email_sent
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_base__email_sent
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_public__email_sent
msgid "Email Sent"
msgstr "Poslan e-mail"

#. module: hr_presence
#: model:ir.ui.menu,name:hr_presence.menu_hr_presence_view
msgid "Employee Presence"
msgstr ""

#. module: hr_presence
#: model:mail.template,name:hr_presence.mail_template_presence
msgid "Employee: Absence email"
msgstr ""

#. module: hr_presence
#: model:sms.template,name:hr_presence.sms_template_data_hr_presence
msgid "Employee: Presence Reminder"
msgstr ""

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_presence_search
msgid "Employees"
msgstr "Zaposlenici"

#. module: hr_presence
#: code:addons/hr_presence/models/hr_employee.py:0
#: model:sms.template,body:hr_presence.sms_template_data_hr_presence
#, python-format
msgid ""
"Exception made if there was a mistake of ours, it seems that you are not at your office and there is not request of time off from you.\n"
"Please, take appropriate measures in order to carry out this work absence.\n"
"Do not hesitate to contact your manager or the human resource department."
msgstr ""

#. module: hr_presence
#: model:ir.actions.server,name:hr_presence.ir_cron_presence_control_ir_actions_server
#: model:ir.cron,cron_name:hr_presence.ir_cron_presence_control
#: model:ir.cron,name:hr_presence.ir_cron_presence_control
msgid "HR Presence: cron"
msgstr ""

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_res_company__hr_presence_last_compute_date
msgid "Hr Presence Last Compute Date"
msgstr ""

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee__hr_presence_state_display
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_base__hr_presence_state_display
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_public__hr_presence_state_display
msgid "Hr Presence State Display"
msgstr ""

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_res_users_log__ip
msgid "IP Address"
msgstr "IP adresa"

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee__ip_connected
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_base__ip_connected
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_public__ip_connected
msgid "Ip Connected"
msgstr "IP Povezan"

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_kanban
msgid "Log"
msgstr "Zapisnik"

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee__manually_set_present
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_base__manually_set_present
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_public__manually_set_present
msgid "Manually Set Present"
msgstr "Ručno postavi prisustvo"

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_presence_search
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_search
msgid "Presence"
msgstr "Prisustvo"

#. module: hr_presence
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee__hr_presence_state_display__present
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_base__hr_presence_state_display__present
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_public__hr_presence_state_display__present
msgid "Present"
msgstr "Prisutan"

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_kanban
msgid "SMS"
msgstr "SMS"

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_kanban
msgid "Set as absent"
msgstr "Postavi kao odsutan"

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_kanban
msgid "Set as present"
msgstr "Postavi kao prisutan"

#. module: hr_presence
#: code:addons/hr_presence/models/hr_employee.py:0
#, python-format
msgid "There is no professional email address for this employee."
msgstr "Nema poslovnog emaila za ovog djelatnika."

#. module: hr_presence
#: code:addons/hr_presence/models/hr_employee.py:0
#, python-format
msgid "There is no professional mobile for this employee."
msgstr "Nema poslovnog mobitela za ovog djelatnika."

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_kanban
msgid "Time Off"
msgstr "Odsustva"

#. module: hr_presence
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee__hr_presence_state_display__to_define
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_base__hr_presence_state_display__to_define
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_public__hr_presence_state_display__to_define
msgid "To Define"
msgstr "Definirati"

#. module: hr_presence
#: model:mail.template,subject:hr_presence.mail_template_presence
msgid "Unexpected Absence"
msgstr "Neočekivano odsustvo"

#. module: hr_presence
#: model:ir.model,name:hr_presence.model_res_users_log
msgid "Users Log"
msgstr "Zapisnik korisnika"

#. module: hr_presence
#: code:addons/hr_presence/models/hr_employee.py:0
#: code:addons/hr_presence/models/hr_employee.py:0
#: code:addons/hr_presence/models/hr_employee.py:0
#, python-format
msgid "You don't have the right to do this. Please contact an Administrator."
msgstr ""

#. module: hr_presence
#: code:addons/hr_presence/models/hr_employee.py:0
#, python-format
msgid "Employee's Presence to Define"
msgstr ""
