# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * resource
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <djord<PERSON><EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON><PERSON> Jo<PERSON>v <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> Jovev <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: resource
#: code:addons/resource/models/resource.py:703
#, python-format
msgid "%s (copy)"
msgstr "%s (kopija)"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource_active
msgid "Active"
msgstr "Aktivan"

#. module: resource
#: model:ir.actions.act_window,name:resource.resource_calendar_closing_days
msgid "Closing Days"
msgstr ""

#. module: resource
#: model:ir.model,name:resource.model_res_company
msgid "Companies"
msgstr "Preduzeća"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_hr_employee_company_id
#: model:ir.model.fields,field_description:resource.field_mrp_workcenter_company_id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_company_id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_company_id
#: model:ir.model.fields,field_description:resource.field_resource_mixin_company_id
#: model:ir.model.fields,field_description:resource.field_resource_resource_company_id
#: model:ir.model.fields,field_description:resource.field_resource_test_company_id
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Company"
msgstr "Preduzeće"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_create_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_create_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_create_uid
#: model:ir.model.fields,field_description:resource.field_resource_resource_create_uid
#: model:ir.model.fields,field_description:resource.field_resource_test_create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_create_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_create_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_create_date
#: model:ir.model.fields,field_description:resource.field_resource_resource_create_date
#: model:ir.model.fields,field_description:resource.field_resource_test_create_date
msgid "Created on"
msgstr "Datum kreiranja"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_dayofweek
msgid "Day of Week"
msgstr ""

#. module: resource
#: model:ir.model.fields,field_description:resource.field_res_company_resource_calendar_id
#: model:ir.model.fields,field_description:resource.field_res_users_resource_calendar_id
msgid "Default Working Hours"
msgstr ""

#. module: resource
#: model:ir.model.fields,help:resource.field_hr_employee_resource_calendar_id
#: model:ir.model.fields,help:resource.field_mrp_workcenter_resource_calendar_id
#: model:ir.model.fields,help:resource.field_res_users_resource_calendar_id
#: model:ir.model.fields,help:resource.field_resource_mixin_resource_calendar_id
#: model:ir.model.fields,help:resource.field_resource_resource_calendar_id
#: model:ir.model.fields,help:resource.field_resource_test_resource_calendar_id
msgid "Define the schedule of resource"
msgstr "Rasporediti resurse"

#. module: resource
#: model_terms:ir.actions.act_window,help:resource.action_resource_calendar_form
msgid ""
"Define working hours and time table that could be scheduled to your project "
"members"
msgstr ""

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_display_name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_display_name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_display_name
#: model:ir.model.fields,field_description:resource.field_resource_mixin_display_name
#: model:ir.model.fields,field_description:resource.field_resource_resource_display_name
#: model:ir.model.fields,field_description:resource.field_resource_test_display_name
msgid "Display Name"
msgstr "Naziv za prikaz"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource_time_efficiency
msgid "Efficiency Factor"
msgstr "Faktor efikasnosti"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_date_to
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_date_to
msgid "End Date"
msgstr "Završni datum"

#. module: resource
#: code:addons/resource/models/resource.py:734
#, python-format
msgid "Error! leave start-date must be lower then leave end-date."
msgstr ""

#. module: resource
#: selection:resource.calendar.attendance,dayofweek:0
msgid "Friday"
msgstr "Petak"

#. module: resource
#: code:addons/resource/models/resource.py:74
#, python-format
msgid "Friday Evening"
msgstr ""

#. module: resource
#: code:addons/resource/models/resource.py:73
#, python-format
msgid "Friday Morning"
msgstr ""

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_global_leave_ids
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Global Leaves"
msgstr ""

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Group By"
msgstr "Grupiši po"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_attendance_form
msgid "Hours"
msgstr "Sati"

#. module: resource
#: selection:resource.resource,resource_type:0
msgid "Human"
msgstr ""

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_id
#: model:ir.model.fields,field_description:resource.field_resource_mixin_id
#: model:ir.model.fields,field_description:resource.field_resource_resource_id
#: model:ir.model.fields,field_description:resource.field_resource_test_id
msgid "ID"
msgstr "ID"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_leaves_resource_id
msgid ""
"If empty, this is a generic holiday for the company. If a resource is set, "
"the holiday/leave is only for this resource"
msgstr ""

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource_active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"Ako je ovo polje postavljeno na NE, možete sakriti resurs bez da ga "
"uklonite."

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Inactive"
msgstr "Neaktivno"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar___last_update
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance___last_update
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves___last_update
#: model:ir.model.fields,field_description:resource.field_resource_mixin___last_update
#: model:ir.model.fields,field_description:resource.field_resource_resource___last_update
#: model:ir.model.fields,field_description:resource.field_resource_test___last_update
msgid "Last Modified on"
msgstr "Zadnja promena"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_write_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_write_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_write_uid
#: model:ir.model.fields,field_description:resource.field_resource_resource_write_uid
#: model:ir.model.fields,field_description:resource.field_resource_test_write_uid
msgid "Last Updated by"
msgstr "Promenio"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_write_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_write_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_write_date
#: model:ir.model.fields,field_description:resource.field_resource_resource_write_date
#: model:ir.model.fields,field_description:resource.field_resource_test_write_date
msgid "Last Updated on"
msgstr "Vreme promene"

#. module: resource
#: model:ir.model,name:resource.model_resource_calendar_leaves
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_form
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_tree
msgid "Leave Detail"
msgstr ""

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Leave Month"
msgstr ""

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leave_ids
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Leaves"
msgstr ""

#. module: resource
#: selection:resource.resource,resource_type:0
msgid "Material"
msgstr ""

#. module: resource
#: selection:resource.calendar.attendance,dayofweek:0
msgid "Monday"
msgstr "Ponedeljak"

#. module: resource
#: code:addons/resource/models/resource.py:66
#, python-format
msgid "Monday Evening"
msgstr ""

#. module: resource
#: code:addons/resource/models/resource.py:65
#, python-format
msgid "Monday Morning"
msgstr ""

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_name
#: model:ir.model.fields,field_description:resource.field_resource_resource_name
#: model:ir.model.fields,field_description:resource.field_resource_test_name
msgid "Name"
msgstr "Naziv"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_name
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_form
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_tree
msgid "Reason"
msgstr "Razlog"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource_user_id
msgid "Related user name for the resource to manage its access."
msgstr "Korisničko ime povezano je sa pristupom i upravljanjem modulima"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_hr_employee_resource_id
#: model:ir.model.fields,field_description:resource.field_mrp_workcenter_resource_id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_resource_id
#: model:ir.model.fields,field_description:resource.field_resource_mixin_resource_id
#: model:ir.model.fields,field_description:resource.field_resource_test_resource_id
#: model:ir.ui.menu,name:resource.menu_resource_config
#: model_terms:ir.ui.view,arch_db:resource.resource_resource_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Resource"
msgstr "Resurs"

#. module: resource
#: model:ir.model,name:resource.model_resource_calendar
msgid "Resource Calendar"
msgstr ""

#. module: resource
#: model:ir.model,name:resource.model_resource_resource
msgid "Resource Detail"
msgstr ""

#. module: resource
#: model:ir.actions.act_window,name:resource.action_resource_calendar_leave_tree
#: model:ir.actions.act_window,name:resource.resource_calendar_leaves_action_from_calendar
#: model:ir.ui.menu,name:resource.menu_view_resource_calendar_leaves_search
msgid "Resource Leaves"
msgstr "Odsustva resursa"

#. module: resource
#: model:ir.model,name:resource.model_resource_mixin
msgid "Resource Mixin"
msgstr ""

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource_resource_type
msgid "Resource Type"
msgstr "Tip resursa"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_calendar_id
msgid "Resource's Calendar"
msgstr ""

#. module: resource
#: model:ir.actions.act_window,name:resource.action_resource_resource_tree
#: model:ir.actions.act_window,name:resource.resource_resource_action_from_calendar
#: model:ir.model.fields,field_description:resource.field_res_users_resource_ids
#: model:ir.ui.menu,name:resource.menu_resource_resource
#: model_terms:ir.ui.view,arch_db:resource.resource_resource_tree
msgid "Resources"
msgstr "Resursi"

#. module: resource
#: model:ir.actions.act_window,name:resource.resource_calendar_resources_leaves
msgid "Resources Leaves"
msgstr ""

#. module: resource
#: model_terms:ir.actions.act_window,help:resource.action_resource_resource_tree
#: model_terms:ir.actions.act_window,help:resource.resource_resource_action_from_calendar
msgid ""
"Resources allow you to create and manage resources that should be involved "
"in a specific project phase. You can also set their efficiency level and "
"workload based on their weekly working hours."
msgstr ""

#. module: resource
#: selection:resource.calendar.attendance,dayofweek:0
msgid "Saturday"
msgstr "Subota"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Search Resource"
msgstr ""

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Search Working Period Leaves"
msgstr ""

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_search
msgid "Search Working Time"
msgstr ""

#. module: resource
#: code:addons/resource/models/res_company.py:18
#: code:addons/resource/models/res_company.py:23
#, python-format
msgid "Standard 40 hours/week"
msgstr ""

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_date_from
msgid "Start Date"
msgstr "Početni datum"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_attendance_hour_from
msgid "Start and End time of working."
msgstr ""

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_date_from
msgid "Starting Date"
msgstr "Pocetni Datum"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Starting Date of Leave by Month"
msgstr ""

#. module: resource
#: selection:resource.calendar.attendance,dayofweek:0
msgid "Sunday"
msgstr "Nedelja"

#. module: resource
#: model:ir.model,name:resource.model_resource_test
msgid "Test Resource Model"
msgstr ""

#. module: resource
#: code:addons/resource/models/resource.py:689
#, python-format
msgid "The efficiency factor cannot be equal to 0."
msgstr ""

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource_time_efficiency
msgid ""
"This field is used to calculate the the expected duration of a work order at"
" this work center. For example, if a work order takes one hour and the "
"efficiency factor is 100%, then the expected duration will be one hour. If "
"the efficiency factor is 200%, however the expected duration will be 30 "
"minutes."
msgstr ""

#. module: resource
#: selection:resource.calendar.attendance,dayofweek:0
msgid "Thursday"
msgstr "Četvrtak"

#. module: resource
#: code:addons/resource/models/resource.py:72
#, python-format
msgid "Thursday Evening"
msgstr ""

#. module: resource
#: code:addons/resource/models/resource.py:71
#, python-format
msgid "Thursday Morning"
msgstr ""

#. module: resource
#: sql_constraint:resource.resource:0
msgid "Time efficiency must be strictly positive"
msgstr ""

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_tz
msgid "Timezone"
msgstr "VremenskaZona"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_leaves_tz
msgid ""
"Timezone used when encoding the leave. It is used to correctlylocalize leave"
" hours when computing time intervals."
msgstr ""

#. module: resource
#: selection:resource.calendar.attendance,dayofweek:0
msgid "Tuesday"
msgstr "Utorak"

#. module: resource
#: code:addons/resource/models/resource.py:68
#, python-format
msgid "Tuesday Evening"
msgstr ""

#. module: resource
#: code:addons/resource/models/resource.py:67
#, python-format
msgid "Tuesday Morning"
msgstr ""

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Type"
msgstr "Tip"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource_user_id
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "User"
msgstr "Korisnik"

#. module: resource
#: model:ir.model,name:resource.model_res_users
msgid "Users"
msgstr "Korisnici"

#. module: resource
#: selection:resource.calendar.attendance,dayofweek:0
msgid "Wednesday"
msgstr "Sreda"

#. module: resource
#: code:addons/resource/models/resource.py:70
#, python-format
msgid "Wednesday Evening"
msgstr ""

#. module: resource
#: code:addons/resource/models/resource.py:69
#, python-format
msgid "Wednesday Morning"
msgstr ""

#. module: resource
#: model:ir.model,name:resource.model_resource_calendar_attendance
msgid "Work Detail"
msgstr ""

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Work Resources"
msgstr ""

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_hour_from
msgid "Work from"
msgstr ""

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_hour_to
msgid "Work to"
msgstr ""

#. module: resource
#: model:ir.model.fields,field_description:resource.field_hr_employee_resource_calendar_id
#: model:ir.model.fields,field_description:resource.field_mrp_workcenter_resource_calendar_id
#: model:ir.model.fields,field_description:resource.field_res_company_resource_calendar_ids
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves_calendar_id
#: model:ir.model.fields,field_description:resource.field_resource_mixin_resource_calendar_id
#: model:ir.model.fields,field_description:resource.field_resource_test_resource_calendar_id
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Working Hours"
msgstr "Radno vreme(od-do)"

#. module: resource
#: code:addons/resource/models/resource.py:60
#, python-format
msgid "Working Hours of %s"
msgstr ""

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Working Period"
msgstr ""

#. module: resource
#: model:ir.actions.act_window,name:resource.action_resource_calendar_form
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance_ids
#: model:ir.model.fields,field_description:resource.field_resource_resource_calendar_id
#: model:ir.ui.menu,name:resource.menu_resource_calendar
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_attendance_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_attendance_tree
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_tree
msgid "Working Time"
msgstr "Radno vrijeme"
