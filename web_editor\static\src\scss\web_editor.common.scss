///
/// This file regroups basic style rules for web_editor enable page edition and
/// backend utils.
/// TODO many of those rules should probably not be declared for the backend.
/// in particular the o_ccX rules, see code linked to the variable named
/// 'prevent-backend-colors-alteration'.
///

:root {
    @each $color, $value in $grays {
        @include print-variable($color, $value);
    }

    // Most of the keys of the color combination color should be null. We have
    // to indicate their fallback values.
    @for $index from 1 through length($o-color-combinations) {
        $-bg-color: o-color(color('o-cc#{$index}-bg'));

        $-text: color('o-cc#{$index}-text') or color-yiq(o-color('o-cc#{$index}-bg'));
        $-headings: color('o-cc#{$index}-headings') or $-text;
        $-h2: color('o-cc#{$index}-h2') or $-headings;
        $-h3: color('o-cc#{$index}-h3') or $-h2;
        $-h4: color('o-cc#{$index}-h4') or $-h3;
        $-h5: color('o-cc#{$index}-h5') or $-h4;
        $-h6: color('o-cc#{$index}-h6') or $-h5;

        @if not color('o-cc#{$index}-text') {
            @include print-variable('o-cc#{$index}-text', $-text);
        }
        @if not color('o-cc#{$index}-headings') {
            @include print-variable('o-cc#{$index}-headings', $-headings);
        }
        @if not color('o-cc#{$index}-h2') {
            @include print-variable('o-cc#{$index}-h2', $-h2);
        }
        @if not color('o-cc#{$index}-h3') {
            @include print-variable('o-cc#{$index}-h3', $-h3);
        }
        @if not color('o-cc#{$index}-h4') {
            @include print-variable('o-cc#{$index}-h4', $-h4);
        }
        @if not color('o-cc#{$index}-h5') {
            @include print-variable('o-cc#{$index}-h5', $-h5);
        }
        @if not color('o-cc#{$index}-h6') {
            @include print-variable('o-cc#{$index}-h6', $-h6);
        }

        $-link: color('o-cc#{$index}-link');
        $-link-color: if($-link, o-color($-link), theme-color('primary'));
        @include print-variable('o-cc#{$index}-link', auto-contrast($-link-color, $-bg-color, 'o-cc#{$index}-link'));

        $-btn-primary: color('o-cc#{$index}-btn-primary');
        @if not $-btn-primary {
            @include print-variable('o-cc#{$index}-btn-primary', theme-color('primary'));
        }
        @if not color('o-cc#{$index}-btn-primary-border') {
            @include print-variable('o-cc#{$index}-btn-primary-border', $-btn-primary or theme-color('primary'));
        }

        $-btn-secondary: color('o-cc#{$index}-btn-secondary');
        @if not $-btn-secondary {
            @include print-variable('o-cc#{$index}-btn-secondary', theme-color('secondary'));
        }
        @if not color('o-cc#{$index}-btn-secondary-border') {
            @include print-variable('o-cc#{$index}-btn-secondary-border', $-btn-secondary or theme-color('secondary'));
        }
    }

    @include print-variable('o-grid-gutter-width', $grid-gutter-width);
    @include print-variable('o-md-container-max-width', map-get($container-max-widths, md));
}

html, body {
    position: relative;
    width: 100%;
    height: 100%;
}

*[contenteditable=true] {
    outline: none;
}
[contenteditable] {
    overflow-wrap: unset !important;
}

.css_non_editable_mode_hidden {
    display: none !important;
}
.editor_enable .css_editable_mode_hidden {
    display: none !important;
}
.note-toolbar {
    margin-left: 0 !important;
}
.note-popover .popover > .arrow {
    display: none;
}

.note-popover .popover, .note-editor {
    .dropdown-menu .dropdown-item {
        > i {
            visibility: hidden;
        }
        &.checked > i {
            visibility: visible;
        }
    }
}

/* ----- GENERIC LAYOUTING HELPERS ---- */
/* table */
#wrapwrap, .o_editable {
    // Only style editor-made tables (shop/portal/... tables are not supposed to
    // use table-bordered...)
    table.table.table-bordered {
        table-layout: fixed;
        td {
            min-width: 20px;
        }
    }
    @include media-breakpoint-down(sm) {
        .table-responsive > table.table {
            table-layout: auto;
        }
    }
}

// List
ul.o_checklist {
    list-style: none;

    >li {
        list-style: none;
        position: relative;
        margin-left: $o-checklist-margin-left;
        margin-right: $o-checklist-margin-left;

        &:not(.oe-nested)::before {
            content: '';
            position: absolute;
            left: - $o-checklist-margin-left;
            display: block;
            height: $o-checklist-before-size;
            width: $o-checklist-before-size;
            top: 4px;
            border: 1px solid;
            text-align: center;
            cursor: pointer;
        }
        &.o_checked {
            text-decoration: line-through;
            &::before {
                content: "✓";
                display: flex;
                align-items: center;
                justify-content: center;
                padding-left: 1px #{"/*rtl:ignore*/"};
                padding-top: 1px;
            }
        }
    }
}
ul.o_checklist[dir="rtl"] > li:not(.oe-nested)::before {
    left: auto;
    right: - $o-checklist-margin-left;
    text-align: right;
}
ol > li.o_indent, ul > li.o_indent {
    margin-left: 0;
    list-style: none;
    &::before {
        content: none;
    }
}

// Medias
img.o_we_custom_image {
    // Images added with the editor are .img-fluid by default but should
    // still behave like inline content.
    display: inline-block;
}

img.shadow {
    box-shadow: 0px 3px 8px rgba(0, 0, 0, 0.2);
}
img.padding-small, .img.padding-small, span.fa.padding-small, iframe.padding-small {
    padding: 4px;
}
img.padding-medium, .img.padding-medium, span.fa.padding-medium, iframe.padding-medium {
    padding: 8px;
}
img.padding-large, .img.padding-large, span.fa.padding-large, iframe.padding-large {
    padding: 16px;
}
img.padding-xl, .img.padding-xl, span.fa.padding-xl, iframe.padding-xl {
    padding: 32px;
}
img.ml-auto, img.mx-auto {
    display: block;
}

.fa-6x {
    font-size: 6em;
}
.fa-7x {
    font-size: 7em;
}
.fa-8x {
    font-size: 8em;
}
.fa-9x {
    font-size: 9em;
}
.fa-10x {
    font-size: 10em;
}
.fa.mx-auto {
    display: block;
    text-align: center;
}
.fa.card-img, .fa.card-img-top, .fa.card-img-bottom {
    width: auto;
}

div.media_iframe_video {
    margin: 0 auto;
    text-align: center;
    position: relative;
    overflow: hidden;
    min-width: 100px;

    iframe {
        width: 100%;
        height: 100%;
        @include o-position-absolute($top: 0);
        margin: 0 auto;
        margin-left: -50%;
    }
    &.padding-small iframe {
        padding: 4px;
    }
    &.padding-medium iframe {
        padding: 8px;
    }
    &.padding-large iframe {
        padding: 16px;
    }
    &.padding-xl iframe {
        padding: 32px;
    }

    .media_iframe_video_size {
        padding-bottom: 66.5%;
        position: relative;
        width: 100%;
        height: 0;
    }

    .css_editable_mode_display {
        @include o-position-absolute(0,0,0,0);
        width: 100%;
        height: 100%;
        display: none;
        z-index: 2;
    }
}

html[data-browser^="msie"] div.media_iframe_video iframe {
    margin-left: 0;
}

// Fields
address {
    .fa.fa-mobile-phone {
        margin: 0 3px 0 2px;
    }
    .fa.fa-file-text-o {
        margin-right: 1px;
    }
}

span[data-oe-type="monetary"] {
    white-space: nowrap;
}

// Menus
// TODO should not be here but used by web_studio so must stay here for now
ul.oe_menu_editor {
    .oe_menu_placeholder {
        outline: 1px dashed #4183C4;
    }
    ul {
        list-style: none;
    }
    li div {
        cursor: move;
    }
}

// Generate all spacings for all sizes
@mixin o-spacing-all($factor: 1) {
    // Generate vertical margin/padding classes used by the editor
    @for $i from 0 through (256 / 8) {
        @include o-vspacing($i * 8, $factor);
    }
    @include o-vspacing(4, $factor);

    // 92px vertical margin is kept for compatibility
    @include o-vmargins(92, $factor);

    // Some horizontal margin classes defined for convenience
    // (and compatibility)
    @include o-hmargins(0, $factor);
    @include o-hmargins(4, $factor);
    @include o-hmargins(8, $factor);
    @include o-hmargins(16, $factor);
    @include o-hmargins(32, $factor);
    @include o-hmargins(64, $factor);
}

// Generate all spacings for one size, scalled by a given factor
// (0 <= factor <= 1)
@mixin o-vspacing($name, $factor: 1) {
    @include o-vmargins($name, $factor);
    @include o-vpaddings($name, $factor);
}
@mixin o-vmargins($name, $factor: 1) {
    @include o-vmargins-define($name, $factor * $name);
}
@mixin o-vpaddings($name, $factor: 1) {
    @include o-vpaddings-define($name, $factor * $name);
}
@mixin o-hspacing($name, $factor: 1) {
    @include o-hmargins($name, $factor);
    @include o-hpaddings($name, $factor);
}
@mixin o-hmargins($name, $factor: 1) {
    @include o-hmargins-define($name, $factor * $name);
}
@mixin o-hpaddings($name, $factor: 1) {
    @include o-hpaddings-define($name, $factor * $name);
}

// Generate all spacings for one size, given the name of the spacing and
// intended size
@mixin o-vmargins-define($name, $size: $name) {
    .mt#{$name} { margin-top: $size * 1px !important; }
    .mb#{$name} { margin-bottom: $size * 1px !important; }
}
@mixin o-vpaddings-define($name, $size: $name) {
    .pt#{$name} { padding-top: $size * 1px !important; }
    .pb#{$name} { padding-bottom: $size * 1px !important; }
}
@mixin o-hmargins-define($name, $size: $name) {
    .ml#{$name} { margin-left: $size * 1px !important; }
    .mr#{$name} { margin-right: $size * 1px !important; }
}
@mixin o-hpaddings-define($name, $size: $name) {
    .pl#{$name} { padding-left: $size * 1px !important; }
    .pr#{$name} { padding-right: $size * 1px !important; }
}

// Generate all margins
@include o-spacing-all;

// Underline
a.o_underline {
    text-decoration: underline;
    &:hover {
        text-decoration: underline;
    }
}

// ACE EDITOR
.o_ace_view_editor {
    background: $o-we-ace-color;
    color: white;
    display: flex;
    flex-flow: column nowrap;
    opacity: 0.97;

    .o_ace_view_editor_title {
        flex: 0 0 auto;
        display: flex;
        align-items: center;
        padding: $grid-gutter-width/4;

        >.o_ace_type_switcher>button::after {
            @include o-caret-down;
            margin-left: 4px;
        }

        >* {
            flex: 0 0 auto;
            margin: 0 $grid-gutter-width/4;

            &.o_include_option {
                display: flex;
                align-items: center;
                font-size: 11px;

                >.custom-control {
                    margin-right: $grid-gutter-width/4;
                }
            }

            &.o_res_list {
                flex: 1 1 auto;
                min-width: 60px;
            }
        }
    }

    #ace-view-id {
        flex: 0 0 auto;
        padding: $grid-gutter-width/4 $grid-gutter-width/2;
        background-color: lighten($o-we-ace-color, 10%);

        .o_ace_editor_resource_info {
            color: #ebecee;
        }
    }

    #ace-view-editor {
        @mixin ace-line-error-mixin {
            content: "";
            z-index: 1000;
            display: flex;
            background-color: $o-we-color-danger;
            color: white;
            font-size: 1.2em;
            align-items: center;
            justify-content: center;
            cursor: help;
        }

        height: 70%; // in case flex is not supported
        flex: 1 1 auto;

        .ace_gutter {
            cursor: ew-resize;

            .ace_gutter-cell.o_error {
                position: relative;

                &::before {
                    @include o-position-absolute(-100vh, 0, -100vh);
                    @include ace-line-error-mixin;
                    width: 2px;
                }
                &::after {
                    @include o-position-absolute(-100%, 0, -100%, 0);
                    @include ace-line-error-mixin;
                    font-family: FontAwesome;
                    content: "\f071";
                }
            }
        }

        .ace_resize_bar {
            @include o-position-absolute($right: 0);
            width: 25px;
            height: 100%;
            cursor: ew-resize;
        }
    }
}

.o_ace_error_popover {
    max-width: 40vw;

    .popover-body {
        background-color: lighten($o-we-ace-color, 10%);
        color: #ebecee;
    }
    &.bs-popover-left .arrow::after {
        border-left-color: lighten($o-we-ace-color, 10%);
    }
    &.bs-popover-right .arrow::after {
        border-right-color: lighten($o-we-ace-color, 10%);
    }
    code {
        display: block;
        max-width: 100%;
        overflow-x: auto;
        white-space: pre;
        color: cyan;
    }
}

.o_ace_select2_dropdown {
    width: auto !important;
    padding-top: 4px;
    font-family: monospace !important;

    >.select2-results {
        max-height: none;
        max-height: 70vh;

        .select2-result-label {
            padding-top: 1px;
            padding-bottom: 2px;

            >.o_ace_select2_result {
                padding: 0;
                font-size: 12px;
                white-space: nowrap;
            }
        }
    }
}

.o_nocontent_help {
    @include o-nocontent-empty;

    .o_empty_folder_image:before {
        @extend %o-nocontent-empty-document;
    }
}

.o_we_search_prompt {
    position: relative;
    min-height: 250px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;

    & > h2 {
        max-width: 500px;
        text-align: center;
        margin-left: 150px;
    }

    &::before {
        transform: scale(-1, 1);
        content: "";
        @include o-position-absolute($top: 0, $left: 50px);
        width: 100px;
        height: 150px;
        opacity: .5;
        background-image: url('/web_editor/static/src/img/curved_arrow.svg');
        background-size: 100%;
        background-repeat: no-repeat;
    }
}

@include media-breakpoint-down(sm) {
    odoo-wysiwyg-container {
        .panel-heading.note-toolbar {
            overflow-x: auto;
        }
        .btn-group {
            position: static;
        }
    }
    // modal select media
    .o_technical_modal.o_web_editor_dialog {
        // see template 'web_editor.FieldTextHtml.fullscreen'
        z-index: $o-we-technical-modal-zindex;

        > .o_select_media_dialog {
            max-width: inherit !important;
            z-index: $o-we-technical-modal-zindex;

            .modal-dialog, .model-content {
                height: 100%;
            }

            .modal-body {
                .nav .nav-item.search {
                    width: 100%;

                    .btn-group {
                        display: flex;
                        justify-content: space-around;
                        padding: 5px;
                    }
                }

                // center pictogram
                .font-icons-icons {
                    text-align: center;
                }

                // fix search image
                .form-control.o_we_search {
                    height: inherit;
                }

                .form-inline {
                    .btn-group {
                        width: 100%;

                        .btn.btn-primary:not(.dropdown-toggle) {
                            width: 90%;
                        }
                    }

                    > .input-group.ml-2 {
                        margin-left: 0 !important;

                        > .input-group-append {
                            width: 100%;

                            > .btn {
                                width: 100%;
                            }

                            > .ml-2 {
                                margin-left: 0 !important;
                            }
                        }
                    }
                }

                // attachment cells
                .o_we_existing_attachments > .row {
                    flex-direction: column;

                    > .o_existing_attachment_cell {
                        flex: initial;
                        max-width: 100%;

                        > .o_existing_attachment_remove {
                            opacity: inherit;
                            top: 10px;
                        }
                    }
                }

                // select media dialog unsplash error
                #editor-media-image .unsplash_img_container .unsplash_error .mx-auto {
                    width: 100%;

                    .form-group {
                        input.w-100 {
                            min-width: 100px;
                        }
                    }
                }
            }
        }
    }
}

// BS4 blockquote has no style anymore, except bloquote <footer>
blockquote {
    padding: $spacer/2 $spacer;
    border-left: 5px solid;
    border-color: gray('300');
    font-style: italic;
}

// Extend bootstrap to create background and text utilities for some colors
// outside of the $theme-colors too (but not btn-, alert-, etc).
@for $index from 1 through 5 {
    $-color-name: 'o-color-#{$index}';
    $-color: color($-color-name);
    @include bg-variant(".bg-#{$-color-name}", $-color);
    @include text-emphasis-variant(".text-#{$-color-name}", $-color);
}

// Bg/text color classes generation
.o_cc {
    #{$o-color-extras-nesting-selector} {
        // Re-force dropdown-item colors inside presets otherwise the presets
        // 'link' colors take over.
        .dropdown-menu .dropdown-item { // Need to add +1 priority thanks to
                                        // .dropdown-menu to counter a:not(.btn)
            &, h6 { // Quick fix: sometimes we use h6 in dropdowns
                color: $dropdown-link-color !important;

                @include hover-focus {
                    color: $dropdown-link-hover-color !important;
                }
            }
            &.disabled,
            &:disabled {
                &, h6 { // Quick fix: sometimes we use h6 in dropdowns
                    color: $dropdown-link-disabled-color !important;
                }
            }
            .btn-link {
                // Some search autocomplete results use btn-link for extra
                // information that can be navigated to. (e.g. products give
                // access to their categories)
                // These need to be reset in case a dark palette is applied.
                color: $link-color;

                &:hover {
                    color: $link-hover-color;
                }
                &:disabled {
                    color: $btn-link-disabled-color;
                }
            }
        }
        .dropdown-menu .dropdown-item-text .text-muted {
            // Search autocomplete messages are shown as text-muted entries.
            // These need to be reset in case a dark palette is applied.
            color: $text-muted !important;

            a {
                // Search autocomplete's fuzzy matched term is shown as a link
                // within an information message.
                color: $link-color;

                &:hover {
                    color: $link-hover-color;
                }
            }
        }
    }
}
@for $index from 1 through length($o-color-combinations) {
    $-bg: color('o-cc#{$index}-bg');
    $-text: color('o-cc#{$index}-text');
    $-headings: color('o-cc#{$index}-headings');
    $-h2: color('o-cc#{$index}-h2');
    $-h3: color('o-cc#{$index}-h3');
    $-h4: color('o-cc#{$index}-h4');
    $-h5: color('o-cc#{$index}-h5');
    $-h6: color('o-cc#{$index}-h6');
    $-link: color('o-cc#{$index}-link');
    $-btn-primary: color('o-cc#{$index}-btn-primary');
    $-btn-primary-border: color('o-cc#{$index}-btn-primary-border');
    $-btn-secondary: color('o-cc#{$index}-btn-secondary');
    $-btn-secondary-border: color('o-cc#{$index}-btn-secondary-border');

    // Those color classes color multiple elements when applied on a snippet.
    // Those rules are not important so that they can be overridden through
    // bg and text utility classes. **
    .o_cc#{$index} {
        // Background & Text
        $-bg-color: o-color($-bg);
        @include o-bg-color($-bg-color, o-color($-text), $important: false, $yiq-min-opacity-threshold: 0);

        #{$o-color-extras-nesting-selector} {
            // Headings
            h1, h2, h3, h4, h5, h6 {
                // 'inherit' comes from the o-bg-color mixin
                color: o-color($-headings);
            }
            h2, h3, h4, h5, h6 {
                color: o-color($-h2);
            }
            h3, h4, h5, h6 {
                color: o-color($-h3);
            }
            h4, h5, h6 {
                color: o-color($-h4);
            }
            h5, h6 {
                color: o-color($-h5);
            }
            h6 {
                color: o-color($-h6);
            }

            // Links
            $-link-color: if($-link, o-color($-link), theme-color('primary'));
            $-link-hover-color: darken($-link-color, 15%);
            a:not(.btn), .btn-link {
                color: auto-contrast($-link-color, $-bg-color, 'o-cc#{$index}-link');

                @include hover {
                    color: auto-contrast($-link-hover-color, $-bg-color, 'o-cc#{$index}-link');
                }
            }

            // Buttons

            // Primary
            $-btn-primary-color: if($-btn-primary, o-color($-btn-primary), theme-color('primary'));
            $-btn-primary-border-color: if($-btn-primary-border, o-color($-btn-primary-border), $-btn-primary-color);
            .btn-fill-primary {
                @include button-variant($-btn-primary-color, $-btn-primary-border-color);
            }
            .btn-outline-primary {
                @include button-outline-variant($-btn-primary-border-color);
            }

            // Secondary
            $-btn-secondary-color: if($-btn-secondary, o-color($-btn-secondary), theme-color('secondary'));
            $-btn-secondary-border-color: if($-btn-secondary-border, o-color($-btn-secondary-border), $-btn-secondary-color);
            .btn-fill-secondary {
                @include button-variant($-btn-secondary-color, $-btn-secondary-border-color);
            }
            .btn-outline-secondary {
                @include button-outline-variant($-btn-secondary-border-color);
            }

            // 'Active' states. Note: this only emulates very common components
            // used in snippets. This might need to be more complex the day we
            // can apply color combinations anywhere (page-item, ...).
            .nav-pills {
                .nav-link.active,
                .show > .nav-link {
                    background-color: $-btn-primary-color;
                    color: color-yiq($-btn-primary-color);
                }
            }
            .dropdown-menu .dropdown-item { // Need to add +1 priority thanks to
                                            // .dropdown-menu (see .o_cc).
                &.active,
                &:active {
                    &, h6 { // Quick fix: sometimes we use h6 in dropdowns
                        @include gradient-bg($-btn-primary-color);
                        color: color-yiq($-btn-primary-color) !important;

                        @include hover-focus {
                            color: color-yiq($-btn-primary-color) !important;
                        }
                    }
                }
            }
            a.list-group-item {
                color: $-btn-primary-color;

                &.active {
                    background-color: $-btn-primary-color;
                    color: color-yiq($-btn-primary-color);
                    border-color: $-btn-primary-color;
                }
            }
        }
    }
}

// Buttons with custom colors
.btn-custom:hover, .btn-fill-custom:hover {
    filter: invert(0.2);
}
.btn-outline-custom:not(:hover) {
    // Custom buttons have their fill color or gradient specified in their
    // element style. They must only be shown on hover for outline buttons.
    background-color: transparent !important;
    background-image: none !important;
}

// Base snippet rules
%o-we-background-layer-parent {
    &, & > * {
        // Allow background layers to be placed accordingly and snippet content
        // to be displayed on top. Note: we cannot just position the layers
        // with z-index: -1, otherwise it would go under the snippet own
        // background. Adding a z-index: 0 on the snippet to create its own
        // stacking context won't solve that either as, in that case, any BS
        // component inside would be using that stacking context (e.g. a
        // dropdown inside snippet 1 of the page would go under snippet 2
        // when opened since the dropdown z-index would be confined into
        // snippet 1's stacking context.
        position: relative;
    }
}
%o-we-background-layer {
    @include o-position-absolute(0, 0, 0, 0);
    position: absolute !important;
    display: block;
    overflow: hidden;
    background-repeat: no-repeat;
    pointer-events: none;
}

section, .oe_img_bg, [data-oe-shape-data] {
    @extend %o-we-background-layer-parent;
}
.o_we_bg_filter {
    @extend %o-we-background-layer;
}

.o_full_screen_height {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    min-height: 100vh !important;
}
.o_half_screen_height {
    @extend .o_full_screen_height;
    min-height: 55vh !important;
}

// TODO remove cover_full and cover_mid classes (kept for compatibility for now)
.cover_full {
    @extend .o_full_screen_height;
}
.cover_mid {
    @extend .o_half_screen_height;
}

// Smaller container
.o_container_small {
    @extend .container;
    @include media-breakpoint-up(lg) {
        max-width: map-get($container-max-widths, md);
    }
}

// Background Images
.oe_img_bg {
    background-size: cover;
    background-repeat: no-repeat;

    &.o_bg_img_opt_repeat {
        background-size: auto;
        background-repeat: repeat;
    }
    &.o_bg_img_center {
        background-position: center;
    }
}

// Gradient
// TODO should be in the editor lib since it is handled there... but could not
// find the right place for it.
.text-gradient {
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;

    // FIXME (or wait for a fix in Chrome): the code below is needed to make
    // animated text work with gradient background on Chrome. It is not based
    // on ".text-gradient *" only to avoid a side effect that makes text nodes
    // wrapped in a span (e.g. bold, italic) no longer have the "gradient
    // crossing the whole text" on them but their own gradient.
    .o_animated_text, .o_animated_text *, &.o_animated_text * {
        background-image: inherit;
        -webkit-background-clip: inherit;
        -webkit-text-fill-color: inherit;
    }

    // On Iphone (Safari & Chrome) and Mac (only Safari), the font awesome icons
    // are not rendered (invisible) when inside a gradient.
    .fa {
        display: inherit;
    }
}

/* QWEB */

.odoo-editor-editable, .o_readonly {
    t,
    [t-if],
    [t-elif],
    [t-else],
    [t-foreach] {
        background-color: rgba(0, 0, 102, 0.1) !important;
    }
    t,
    [t-esc],
    [t-out],
    [t-raw] {
        border-radius: 2px;
    }
    [t-esc],
    [t-out],
    [t-raw] {
        background-color: rgba(36, 154, 255, 0.16) !important;
    }
    [t-esc]:empty::before {
        content: attr(t-esc);
    }
    [t-raw]:empty::before {
        content: attr(t-raw);
    }
    [t-out]:empty::before {
        content: attr(t-out);
    }
    t[t-set] {
        display: none;
    }
    t[data-oe-t-inline] {
        display: inline;
    }
    t:not([data-oe-t-inline]) {
        display: block;
    }
    t[data-oe-t-inline]:not([data-oe-t-group-active]) {
        display: unset;
    }
    [data-oe-t-group]:not([data-oe-t-group-active]) {
        display: none !important;
    }
    [data-oe-t-group][data-oe-t-selectable] {
        outline: 1px dashed rgba(0, 0, 102, 0.4) !important;
    }
}

.oe-qweb-select {
    position: absolute;
    z-index: 1051;
    background-color: white;
}
