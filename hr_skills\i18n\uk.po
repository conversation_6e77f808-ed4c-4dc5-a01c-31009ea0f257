# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_skills
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <alina.lis<PERSON><PERSON>@erp.co.ua>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:49+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: hr_skills
#. openerp-web
#: code:addons/hr_skills/static/src/xml/resume_templates.xml:0
#, python-format
msgid "ADD"
msgstr "ДОДАТИ"

#. module: hr_skills
#. openerp-web
#: code:addons/hr_skills/static/src/xml/resume_templates.xml:0
#, python-format
msgid "CREATE A NEW ENTRY"
msgstr "СТВОРІТЬ НОВИЙ ЗАПИС"

#. module: hr_skills
#: model:ir.model.fields.selection,name:hr_skills.selection__hr_resume_line__display_type__classic
msgid "Classic"
msgstr "Класичний"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__create_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__create_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__create_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__create_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__create_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__create_uid
msgid "Created by"
msgstr "Створив"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__create_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__create_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__create_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__create_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__create_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__create_date
msgid "Created on"
msgstr "Створено на"

#. module: hr_skills
#. openerp-web
#: code:addons/hr_skills/static/src/js/resume_widget.js:0
#, python-format
msgid "Current"
msgstr "Поточний"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__date_end
msgid "Date End"
msgstr "Дата кінця"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__date_start
msgid "Date Start"
msgstr "Дата початку"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__description
#: model_terms:ir.ui.view,arch_db:hr_skills.resume_line_view_form
msgid "Description"
msgstr "Опис"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__display_name
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__display_name
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__display_name
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__display_name
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__display_name
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__display_name
msgid "Display Name"
msgstr "Назва для відображення"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__display_type
msgid "Display Type"
msgstr "Тип відображення"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_employee
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__employee_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__employee_id
msgid "Employee"
msgstr "Співробітник"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__id
msgid "ID"
msgstr "ID"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill____last_update
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line____last_update
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type____last_update
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill____last_update
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level____last_update
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type____last_update
msgid "Last Modified on"
msgstr "Останні зміни на"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__write_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__write_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__write_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__write_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__write_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__write_uid
msgid "Last Updated by"
msgstr "Востаннє оновив"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__write_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__write_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__write_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__write_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__write_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__write_date
msgid "Last Updated on"
msgstr "Останнє оновлення"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__skill_level_ids
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_type_view_form
msgid "Levels"
msgstr "Рівні"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__name
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__name
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__name
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__name
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__name
msgid "Name"
msgstr "Ім'я"

#. module: hr_skills
#. openerp-web
#: code:addons/hr_skills/static/src/js/resume_widget.js:0
#, python-format
msgid "Other"
msgstr "Інше"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__level_progress
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__level_progress
msgid "Progress"
msgstr "Прогрес"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.employee_skill_level_view_form
msgid "Progress (%)"
msgstr "Прогрес (%)"

#. module: hr_skills
#: model:ir.model.fields,help:hr_skills.field_hr_employee_skill__level_progress
#: model:ir.model.fields,help:hr_skills.field_hr_skill_level__level_progress
msgid "Progress from zero knowledge (0%) to fully mastered (100%)."
msgstr "Прогрес від нульових знань (0%) до повної майстерності (100%)."

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_employee_public
msgid "Public Employee"
msgstr "Зовнішній користувач"

#. module: hr_skills
#: model:ir.ui.menu,name:hr_skills.menu_human_resources_configuration_resume
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_public_view_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_skills.resume_line_view_form
msgid "Resumé"
msgstr "Резюме"

#. module: hr_skills
#: model:ir.actions.act_window,name:hr_skills.hr_resume_type_action
msgid "Resumé Line Types"
msgstr "Типи рядка резюме"

#. module: hr_skills
#. openerp-web
#: code:addons/hr_skills/static/src/xml/resume_templates.xml:0
#, python-format
msgid "Resumé empty"
msgstr "Порожнє резюме"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_resume_line
msgid "Resumé line of an employee"
msgstr "Рядок резюме співробітника"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee__resume_line_ids
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_public__resume_line_ids
#: model:ir.model.fields,field_description:hr_skills.field_res_users__resume_line_ids
msgid "Resumé lines"
msgstr "Рядки резюме"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__sequence
msgid "Sequence"
msgstr "Послідовність"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_skill
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__skill_id
msgid "Skill"
msgstr "Навички"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_skill_level
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__skill_level_id
#: model_terms:ir.ui.view,arch_db:hr_skills.employee_skill_level_view_form
msgid "Skill Level"
msgstr "Рівень навичок"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.employee_skill_level_view_tree
#: model_terms:ir.ui.view,arch_db:hr_skills.employee_skill_view_tree
msgid "Skill Levels"
msgstr "Рівні навичок"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_skill_type
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__skill_type_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__skill_type_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__skill_type_id
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_type_view_form
msgid "Skill Type"
msgstr "Тип навичок"

#. module: hr_skills
#: model:ir.actions.act_window,name:hr_skills.hr_skill_type_action
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_type_view_tree
msgid "Skill Types"
msgstr "Типи навичок"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_employee_skill
msgid "Skill level for an employee"
msgstr "Рівень навичок для співробітника"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee__employee_skill_ids
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_public__employee_skill_ids
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__skill_ids
#: model:ir.model.fields,field_description:hr_skills.field_res_users__employee_skill_ids
#: model:ir.ui.menu,name:hr_skills.hr_skill_type_menu
#: model_terms:ir.ui.view,arch_db:hr_skills.employee_skill_view_form
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_public_view_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_view_form
#: model_terms:ir.ui.view,arch_db:hr_skills.res_users_view_form
msgid "Skills"
msgstr "Навички"

#. module: hr_skills
#: code:addons/hr_skills/models/hr_skills.py:0
#, python-format
msgid "The skill %(name)s and skill type %(type)s doesn't match"
msgstr "Навичка %(name)s і тип навички %(type)s не співпадають "

#. module: hr_skills
#: code:addons/hr_skills/models/hr_skills.py:0
#, python-format
msgid "The skill level %(level)s is not valid for skill type: %(type)s"
msgstr "Рівень навички %(level)s не дійсний для типу навички: %(type)s"

#. module: hr_skills
#: model:ir.model.constraint,message:hr_skills.constraint_hr_resume_line_date_check
msgid "The start date must be anterior to the end date."
msgstr "Початкова дата повинна бути раніше за кінцеву"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.resume_line_view_form
msgid "Title"
msgstr "Заголовок"

#. module: hr_skills
#: model:ir.model.constraint,message:hr_skills.constraint_hr_employee_skill__unique_skill
msgid "Two levels for the same skill is not allowed"
msgstr "Два рівні для однієї і тієї ж навички не допускаються"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__line_type_id
msgid "Type"
msgstr "Тип"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_resume_line_type
msgid "Type of a resumé line"
msgstr "Тип рядка резюме"

#. module: hr_skills
#: model:ir.ui.menu,name:hr_skills.hr_resume_line_type_menu
msgid "Types"
msgstr "Типи"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_res_users
msgid "Users"
msgstr "Користувачі"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_type_view_form
msgid "e.g. Languages"
msgstr "напр., Мови"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.resume_line_view_form
msgid "e.g. Odoo Inc."
msgstr "напр., Odoo Inc."
