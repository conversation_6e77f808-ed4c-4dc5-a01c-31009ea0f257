<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

    <!-- VAT Taxes -->

    <record id="tax_group_iva_21" model="account.tax.group">
        <field name="name">VAT 21%</field>
        <field name="l10n_ar_vat_afip_code">5</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record id="tax_group_iva_27" model="account.tax.group">
        <field name="name">VAT 27%</field>
        <field name="l10n_ar_vat_afip_code">6</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record id="tax_group_iva_105" model="account.tax.group">
        <field name="name">VAT 10.5%</field>
        <field name="l10n_ar_vat_afip_code">4</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record id="tax_group_iva_025" model="account.tax.group">
        <field name="name">VAT 2,5%</field>
        <field name="l10n_ar_vat_afip_code">9</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record model="account.tax.group" id="tax_group_iva_no_corresponde">
        <field name="name">VAT Not Applicable</field>
        <field name="l10n_ar_vat_afip_code">0</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record model="account.tax.group" id="tax_group_iva_no_gravado">
        <field name="name">VAT Untaxed</field>
        <field name="l10n_ar_vat_afip_code">1</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record model="account.tax.group" id="tax_group_iva_exento">
        <field name="name">VAT Exempt</field>
        <field name="l10n_ar_vat_afip_code">2</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record model="account.tax.group" id="tax_group_iva_0">
        <field name="name">VAT 0%</field>
        <field name="l10n_ar_vat_afip_code">3</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record model="account.tax.group" id="tax_group_iva_5">
        <field name="name">VAT 5%</field>
        <field name="l10n_ar_vat_afip_code">8</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <!-- Others Taxes -->

    <record model="account.tax.group" id="tax_group_otros_impuestos">
        <field name="name">Otros Impuestos</field>
        <field name="sequence">20</field>
        <field name="l10n_ar_tribute_afip_code">99</field>
    </record>

    <record model="account.tax.group" id="tax_impuestos_internos">
        <field name="name">Internal Taxes</field>
        <field name="sequence">15</field>
        <field name="l10n_ar_tribute_afip_code">04</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <!-- Perceptions and Withholding -->

    <record model="account.tax.group" id="tax_group_percepcion_iva">
        <field name="name">VAT Perception</field>
        <field name="l10n_ar_tribute_afip_code">06</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record model="account.tax.group" id="tax_group_percepcion_iibb_caba">
        <field name="name">Perc IIBB CABA</field>
        <field name="l10n_ar_tribute_afip_code">07</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record model="account.tax.group" id="tax_group_percepcion_iibb_ba">
        <field name="name">Perc IIBB ARBA</field>
        <field name="l10n_ar_tribute_afip_code">07</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record model="account.tax.group" id="tax_group_percepcion_iibb_ca">
        <field name="name">Perc IIBB Catamarca</field>
        <field name="l10n_ar_tribute_afip_code">07</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record model="account.tax.group" id="tax_group_percepcion_iibb_co">
        <field name="name">Perc IIBB Córdoba</field>
        <field name="l10n_ar_tribute_afip_code">07</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record model="account.tax.group" id="tax_group_percepcion_iibb_rr">
        <field name="name">Perc IIBB Corrientes</field>
        <field name="l10n_ar_tribute_afip_code">07</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record model="account.tax.group" id="tax_group_percepcion_iibb_er">
        <field name="name">Perc IIBB Entre Ríos</field>
        <field name="l10n_ar_tribute_afip_code">07</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record model="account.tax.group" id="tax_group_percepcion_iibb_ju">
        <field name="name">Perc IIBB Jujuy</field>
        <field name="l10n_ar_tribute_afip_code">07</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record model="account.tax.group" id="tax_group_percepcion_iibb_za">
        <field name="name">Perc IIBB Mendoza</field>
        <field name="l10n_ar_tribute_afip_code">07</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record model="account.tax.group" id="tax_group_percepcion_iibb_lr">
        <field name="name">Perc IIBB La Rioja</field>
        <field name="l10n_ar_tribute_afip_code">07</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record model="account.tax.group" id="tax_group_percepcion_iibb_sa">
        <field name="name">Perc IIBB Salta</field>
        <field name="l10n_ar_tribute_afip_code">07</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record model="account.tax.group" id="tax_group_percepcion_iibb_nn">
        <field name="name">Perc IIBB San Juan</field>
        <field name="l10n_ar_tribute_afip_code">07</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record model="account.tax.group" id="tax_group_percepcion_iibb_sl">
        <field name="name">Perc IIBB San Luis</field>
        <field name="l10n_ar_tribute_afip_code">07</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record model="account.tax.group" id="tax_group_percepcion_iibb_sf">
        <field name="name">Perc IIBB Santa Fe</field>
        <field name="l10n_ar_tribute_afip_code">07</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record model="account.tax.group" id="tax_group_percepcion_iibb_se">
        <field name="name">Perc IIBB Santiago del Estero</field>
        <field name="l10n_ar_tribute_afip_code">07</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record model="account.tax.group" id="tax_group_percepcion_iibb_tn">
        <field name="name">Perc IIBB Tucumán</field>
        <field name="l10n_ar_tribute_afip_code">07</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record model="account.tax.group" id="tax_group_percepcion_iibb_ha">
        <field name="name">Perc IIBB Chaco</field>
        <field name="l10n_ar_tribute_afip_code">07</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record model="account.tax.group" id="tax_group_percepcion_iibb_ct">
        <field name="name">Perc IIBB Chubut</field>
        <field name="l10n_ar_tribute_afip_code">07</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record model="account.tax.group" id="tax_group_percepcion_iibb_fo">
        <field name="name">Perc IIBB Formosa</field>
        <field name="l10n_ar_tribute_afip_code">07</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record model="account.tax.group" id="tax_group_percepcion_iibb_mi">
        <field name="name">Perc IIBB Misiones</field>
        <field name="l10n_ar_tribute_afip_code">07</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record model="account.tax.group" id="tax_group_percepcion_iibb_ne">
        <field name="name">Perc IIBB Neuquén</field>
        <field name="l10n_ar_tribute_afip_code">07</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record model="account.tax.group" id="tax_group_percepcion_iibb_lp">
        <field name="name">Perc IIBB La Pampa</field>
        <field name="l10n_ar_tribute_afip_code">07</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record model="account.tax.group" id="tax_group_percepcion_iibb_rn">
        <field name="name">Perc IIBB Río Negro</field>
        <field name="l10n_ar_tribute_afip_code">07</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record model="account.tax.group" id="tax_group_percepcion_iibb_az">
        <field name="name">Perc IIBB Santa Cruz</field>
        <field name="l10n_ar_tribute_afip_code">07</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record model="account.tax.group" id="tax_group_percepcion_iibb_tf">
        <field name="name">Perc IIBB Tierra del Fuego</field>
        <field name="l10n_ar_tribute_afip_code">07</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record model="account.tax.group" id="tax_group_percepcion_iibb">
        <field name="name">IIBB Perceptions</field>
        <field name="sequence">25</field>
        <field name="l10n_ar_tribute_afip_code">07</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record model="account.tax.group" id="tax_group_percepcion_municipal">
        <field name="name">Municipal Taxes Perceptions</field>
        <field name="l10n_ar_tribute_afip_code">08</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record model="account.tax.group" id="tax_group_percepcion_ganancias">
        <field name="name">Profit Perceptions</field>
        <field name="l10n_ar_tribute_afip_code">09</field>
        <field name="country_id" ref="base.ar"/>
    </record>

    <record model="account.tax.group" id="tax_group_otras_percepciones">
        <field name="name">Other Perceptions</field>
        <field name="l10n_ar_tribute_afip_code">09</field>
        <field name="country_id" ref="base.ar"/>
    </record>

</odoo>
