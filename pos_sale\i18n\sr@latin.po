# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * pos_sale
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:52+0000\n"
"PO-Revision-Date: 2017-09-20 09:52+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_report_all_channels_sales
msgid "All sales orders grouped by sales channels"
msgstr ""

#. module: pos_sale
#: code:addons/pos_sale/models/crm_team.py:103
#, python-format
msgid "Dashboard"
msgstr "Kontrolna ploča"

#. module: pos_sale
#: selection:crm.team,dashboard_graph_group_pos:0
msgid "Day"
msgstr "Dan"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_crm_team_dashboard_graph_group_pos
msgid "Group by"
msgstr ""

#. module: pos_sale
#: model:ir.model.fields,help:pos_sale.field_crm_team_dashboard_graph_group_pos
msgid "How this channel's dashboard graph will group the results."
msgstr ""

#. module: pos_sale
#: selection:crm.team,dashboard_graph_group_pos:0
msgid "Month"
msgstr "Mesec"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_crm_team_pos_sessions_open_count
msgid "Open POS Sessions"
msgstr ""

#. module: pos_sale
#: model:ir.actions.act_window,name:pos_sale.pos_session_action_from_crm_team
msgid "Open Sessions"
msgstr ""

#. module: pos_sale
#: selection:crm.team,dashboard_graph_group_pos:0
#: model:crm.team,name:pos_sale.pos_sales_team
msgid "Point of Sale"
msgstr "Mesto Prodaje"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_crm_team_pos_config_ids
msgid "Point of Sales"
msgstr ""

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.crm_team_salesteams_view_kanban_inherit_pos_sale
msgid "Sales"
msgstr "Prodaja"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_crm_team
#: model:ir.model.fields,field_description:pos_sale.field_pos_config_crm_team_id
#: model:ir.model.fields,field_description:pos_sale.field_pos_session_crm_team_id
msgid "Sales Channel"
msgstr ""

#. module: pos_sale
#: code:addons/pos_sale/models/crm_team.py:115
#, python-format
msgid "Sales: Untaxed Amount"
msgstr ""

#. module: pos_sale
#: selection:crm.team,dashboard_graph_group_pos:0
msgid "Salesperson"
msgstr "Prodavač"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.crm_team_salesteams_view_kanban_inherit_pos_sale
msgid "Session Running"
msgstr ""

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_crm_team_pos_order_amount_total
msgid "Session Sale Amount"
msgstr ""

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.crm_team_salesteams_view_kanban_inherit_pos_sale
msgid "Sessions Running"
msgstr ""

#. module: pos_sale
#: model:ir.model.fields,help:pos_sale.field_pos_config_crm_team_id
#: model:ir.model.fields,help:pos_sale.field_pos_session_crm_team_id
msgid "This Point of sale's sales will be related to this Sales Channel."
msgstr ""

#. module: pos_sale
#: selection:crm.team,dashboard_graph_group_pos:0
msgid "Week"
msgstr "Nedelja"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_pos_config
msgid "pos.config"
msgstr ""

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_pos_session
msgid "pos.session"
msgstr ""
