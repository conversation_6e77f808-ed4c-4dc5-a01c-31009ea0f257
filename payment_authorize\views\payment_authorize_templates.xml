<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <template id="inline_form">
        <div t-if="acquirer.authorize_payment_method_type == 'credit_card'" t-attf-id="o_authorize_form_{{acquirer_id}}" class="o_authorize_form">
            <div class="form-group">
                <label t-attf-for="o_authorize_card_{{acquirer_id}}" class="col-form-label">Card Number</label>
                <input type="text" t-attf-id="o_authorize_card_{{acquirer_id}}" required="" maxlength="19" class="form-control"/>
            </div>
            <div class="row">
                <div class="col-sm-8 form-group">
                    <label t-attf-for="o_authorize_month_{{acquirer_id}}">Expiration</label>
                    <div class="input-group">
                        <input type="number" t-attf-id="o_authorize_month_{{acquirer_id}}" placeholder="MM" min="1" max="12" required="" class="form-control"/>
                        <input type="number" t-attf-id="o_authorize_year_{{acquirer_id}}" placeholder="YY" min="00" max="99" required="" class="form-control"/>
                    </div>
                </div>
                <div class="col-sm-4 form-group">
                    <label t-attf-for="o_authorize_code_{{acquirer_id}}">Card Code</label>
                    <input type="number" t-attf-id="o_authorize_code_{{acquirer_id}}" max="9999" class="form-control"/>
                </div>
            </div>
        </div>
        <div t-else="" t-attf-id="o_authorize_form_{{acquirer_id}}" class="o_authorize_form">
            <div class="form-group">
                <label t-attf-for="o_authorize_bank_name_{{acquirer_id}}" class="col-form-label">Bank Name</label>
                <input type="text" t-attf-id="o_authorize_bank_name_{{acquirer_id}}" required="" class="form-control"/>
            </div>
            <div class="form-group">
                <label t-attf-for="o_authorize_account_name_{{acquirer_id}}" class="col-form-label">Name On Account</label>
                <input type="text" t-attf-id="o_authorize_account_name_{{acquirer_id}}" required="" class="form-control"/>
            </div>
            <div class="form-group">
                <label t-attf-for="o_authorize_account_number_{{acquirer_id}}" class="col-form-label">Account Number</label>
                <input type="text" t-attf-id="o_authorize_account_number_{{acquirer_id}}" required="" class="form-control"/>
            </div>
            <div class="form-group">
                <label t-attf-for="o_authorize_aba_number_{{acquirer_id}}" class="col-form-label">ABA Routing Number</label>
                <input type="text" t-attf-id="o_authorize_aba_number_{{acquirer_id}}" required="" class="form-control"/>
            </div>
            <div class="form-group">
                <label t-attf-for="o_authorize_account_type_{{acquirer_id}}" class="col-form-label">Bank Account Type</label>
                <select t-attf-id="o_authorize_account_type_{{acquirer_id}}" required="" class="form-control">
                    <option value="checking">Personal Checking</option>
                    <option value="savings">Personal Savings</option>
                    <option value="businessChecking">Business Checking</option>
                </select>
            </div>
        </div>
    </template>

</odoo>
