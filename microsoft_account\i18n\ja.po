# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* microsoft_account
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: microsoft_account
#: code:addons/microsoft_account/models/microsoft_service.py:0
#, python-format
msgid "Method not supported [%s] not in [GET, POST, PUT, PATCH or DELETE]!"
msgstr "サポートされていないメソッド [%s]これらは[GET、POST、PUT、PATCH、DELETE]内にありません！"

#. module: microsoft_account
#: model:ir.model.fields,field_description:microsoft_account.field_res_users__microsoft_calendar_rtoken
msgid "Microsoft Refresh Token"
msgstr "Microsoftリフレッシュトークン"

#. module: microsoft_account
#: model:ir.model,name:microsoft_account.model_microsoft_service
msgid "Microsoft Service"
msgstr "Microsoftサービス"

#. module: microsoft_account
#: model:ir.model.fields,field_description:microsoft_account.field_res_users__microsoft_calendar_token_validity
msgid "Microsoft Token Validity"
msgstr "Microsoftトークンの有効性"

#. module: microsoft_account
#: model:ir.model.fields,field_description:microsoft_account.field_res_users__microsoft_calendar_token
msgid "Microsoft User token"
msgstr "Microsoftユーザトークン"

#. module: microsoft_account
#: code:addons/microsoft_account/models/microsoft_service.py:0
#, python-format
msgid ""
"Something went wrong during your token generation. Maybe your Authorization "
"Code is invalid"
msgstr "トークンの生成中に何か問題が発生しました。 承認コードが無効である可能性があります"

#. module: microsoft_account
#: code:addons/microsoft_account/models/microsoft_service.py:0
#, python-format
msgid ""
"Something went wrong during your token generation. Maybe your Authorization "
"Code is invalid or already expired"
msgstr "トークンの生成中に何か問題が発生しました。 承認コードが無効であるかすでに失効している可能性があります "

#. module: microsoft_account
#: model:ir.model,name:microsoft_account.model_res_users
msgid "Users"
msgstr "ユーザ"
