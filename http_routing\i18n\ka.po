# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* http_routing
# 
# Translators:
# <PERSON>, 2021
# Temur, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:20+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: Temur, 2021\n"
"Language-Team: Georgian (https://app.transifex.com/odoo/teams/41243/ka/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ka\n"
"Plural-Forms: nplurals=2; plural=(n!=1);\n"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.403
msgid "403: Forbidden"
msgstr "403: აკრძალულია"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.404
msgid ""
"<b>Don't panic.</b> If you think it's our mistake, please send us a message "
"on"
msgstr ""

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.error_message
#: model_terms:ir.ui.view,arch_db:http_routing.http_error_debug
msgid "<strong>Error message:</strong>"
msgstr ""

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.500
msgid "Back"
msgstr "უკან"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.http_error_debug
msgid "Error"
msgstr "შეცდომა"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.404
msgid "Error 404"
msgstr ""

#. module: http_routing
#: model:ir.model,name:http_routing.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.404
#: model_terms:ir.ui.view,arch_db:http_routing.500
msgid "Home"
msgstr ""

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.404
msgid "Maybe you were looking for one of these <b>popular pages?</b>"
msgstr ""

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.400
msgid "Oops! Something went wrong."
msgstr ""

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.http_error_debug
msgid "QWeb"
msgstr "QWeb"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.400
msgid "Take a look at the error message below."
msgstr ""

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.http_error_debug
msgid "The error occurred while rendering the template"
msgstr ""

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.403
msgid "The page you were looking for could not be authorized."
msgstr ""

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.http_error_debug
msgid "Traceback"
msgstr ""

#. module: http_routing
#: model:ir.model,name:http_routing.model_ir_ui_view
msgid "View"
msgstr "ხედი"

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.404
msgid "We couldn't find the page you're looking for!"
msgstr ""

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.http_error_debug
msgid "and evaluating the following expression:"
msgstr ""

#. module: http_routing
#: model_terms:ir.ui.view,arch_db:http_routing.404
msgid "this page"
msgstr ""
