# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * rating
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2015-09-24 20:02+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Spanish (Bolivia) (http://www.transifex.com/odoo/odoo-9/"
"language/es_BO/)\n"
"Language: es_BO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating_access_token
msgid "Access token to set the rating of the value"
msgstr ""

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating_message_id
msgid ""
"Associated message when posting a review. Mainly used in website addons."
msgstr ""

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating_partner_id
msgid "Author of the rating"
msgstr ""

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating_create_date
msgid "Created on"
msgstr "Creado en"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating_partner_id
#: model_terms:ir.ui.view,arch_db:rating.view_rating_rating_search
msgid "Customer"
msgstr "Cliente"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.view_rating_rating_search
msgid "Day"
msgstr ""

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin_display_name
#: model:ir.model.fields,field_description:rating.field_rating_rating_display_name
msgid "Display Name"
msgstr ""

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating_res_id
msgid "Document ID"
msgstr ""

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating_res_model
msgid "Document Model"
msgstr ""

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating_feedback
msgid "Feedback reason"
msgstr ""

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.view_rating_rating_search
msgid "Group By"
msgstr "Agrupar por"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.view_rating_rating_search
msgid "Happy"
msgstr ""

#. module: rating
#. openerp-web
#: code:addons/rating/static/src/js/rating_common.js:26
#, python-format
msgid "I don't like it"
msgstr ""

#. module: rating
#. openerp-web
#: code:addons/rating/static/src/js/rating_common.js:25
#, python-format
msgid "I hate it"
msgstr ""

#. module: rating
#. openerp-web
#: code:addons/rating/static/src/js/rating_common.js:28
#, python-format
msgid "I like it"
msgstr ""

#. module: rating
#. openerp-web
#: code:addons/rating/static/src/js/rating_common.js:29
#, python-format
msgid "I love it"
msgstr ""

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin_id
#: model:ir.model.fields,field_description:rating.field_rating_rating_id
msgid "ID"
msgstr "ID"

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating_res_id
msgid "Identifier of the rated object"
msgstr ""

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_view
msgid "It was"
msgstr ""

#. module: rating
#. openerp-web
#: code:addons/rating/static/src/js/rating_common.js:27
#, python-format
msgid "It's okay"
msgstr ""

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_mixin___last_update
#: model:ir.model.fields,field_description:rating.field_rating_rating___last_update
msgid "Last Modified on"
msgstr ""

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating_write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating_write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating_message_id
msgid "Linked message"
msgstr ""

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating_res_model
msgid "Model name of the rated object"
msgstr ""

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.view_rating_rating_search
msgid "Month"
msgstr "Mes"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.view_rating_rating_search
msgid "Okay"
msgstr ""

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating_rated_partner_id
msgid "Owner of the rated resource"
msgstr ""

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating_rated_partner_id
msgid "Rated Partner"
msgstr ""

#. module: rating
#: model:ir.actions.act_window,name:rating.action_view_rating
#: model:ir.model,name:rating.model_rating_rating
#: model:ir.model.fields,field_description:rating.field_mail_channel_rating_ids
#: model:ir.model.fields,field_description:rating.field_product_template_rating_ids
#: model:ir.model.fields,field_description:rating.field_project_issue_rating_ids
#: model:ir.model.fields,field_description:rating.field_project_task_rating_ids
#: model:ir.model.fields,field_description:rating.field_rating_mixin_rating_ids
#: model:ir.model.fields,field_description:rating.field_rating_rating_rating
#: model_terms:ir.ui.view,arch_db:rating.view_rating_rating_search
msgid "Rating"
msgstr ""

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.view_rating_rating_graph
#: model_terms:ir.ui.view,arch_db:rating.view_rating_rating_pivot
msgid "Rating Average"
msgstr ""

#. module: rating
#: model:ir.model,name:rating.model_rating_mixin
msgid "Rating Mixin"
msgstr ""

#. module: rating
#: sql_constraint:rating.rating:0
msgid "Rating should be between -1 to 10"
msgstr ""

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating_rating
msgid "Rating value"
msgstr ""

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.view_rating_rating_search
msgid "Ratings"
msgstr ""

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating_feedback
msgid "Reason of the rating"
msgstr ""

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.view_rating_rating_search
msgid "Resource"
msgstr ""

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating_res_name
msgid "Resource Name"
msgstr ""

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.view_rating_rating_search
msgid "Responsible"
msgstr "Responsable"

#. module: rating
#: model:ir.model.fields,field_description:rating.field_rating_rating_access_token
msgid "Security Token"
msgstr ""

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_view
msgid "Sorry you have already voted !"
msgstr ""

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_view
msgid "Thanks for voting !"
msgstr ""

#. module: rating
#: model:ir.model.fields,help:rating.field_rating_rating_res_name
msgid "The name of the rated resource."
msgstr ""

#. module: rating
#: model_terms:ir.actions.act_window,help:rating.action_view_rating
msgid "There is no rating for this object at the moment."
msgstr ""

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.view_rating_rating_search
msgid "Unhappy"
msgstr ""

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.view_rating_rating_search
msgid "Year"
msgstr "Año"

#. module: rating
#: model_terms:ir.ui.view,arch_db:rating.rating_external_page_view
msgid "Your vote has been taken into account."
msgstr ""

#. module: rating
#: code:addons/rating/models/rating.py:57
#, python-format
msgid "rated it"
msgstr ""
