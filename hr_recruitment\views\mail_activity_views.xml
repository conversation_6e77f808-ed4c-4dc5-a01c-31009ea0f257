<?xml version="1.0"?>
<odoo>
    <!-- Activity types config -->
    <record id="mail_activity_type_action_config_hr_applicant" model="ir.actions.act_window">
        <field name="name">Activity Types</field>
        <field name="res_model">mail.activity.type</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">['|', ('res_model', '=', False), ('res_model', '=', 'hr.applicant')]</field>
        <field name="context">{'default_res_model': 'hr.applicant'}</field>
    </record>
    <menuitem id="hr_recruitment_menu_config_activity_type"
        action="mail_activity_type_action_config_hr_applicant"
        parent="menu_hr_recruitment_configuration"/>
</odoo>