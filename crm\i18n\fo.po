# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * crm
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-11-16 08:08+0000\n"
"PO-Revision-Date: 2017-11-16 08:08+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Faroese (https://www.transifex.com/odoo/teams/41243/fo/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fo\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: crm
#: model:mail.template,body_html:crm.mail_template_data_module_install_crm
msgid ""
"\n"
"\n"
"            <div style=\"margin: 10px auto;\">\n"
"            % set last_created_team = user.env['crm.team'].search([], order=\"create_date desc\")[0]\n"
"            <table cellspacing=\"0\" cellpadding=\"0\" style=\"width:100%;\">\n"
"                <tbody>\n"
"                    <tr>\n"
"                        <td style=\"padding:2px;width:30%;\">\n"
"                            <img src=\"web/static/src/img/logo.png\"/>\n"
"                        </td>\n"
"                        <td style=\"vertical-align: top; padding: 8px 10px;text-align: left;font-size: 14px;\">\n"
"                            <a href=\"web/login\" style=\"float:right ;margin:15px auto;background: #875A7B;border-radius: 5px;color: #ffffff;font-size: 16px;padding: 10px 20px 10px 20px;text-decoration: none;\">Auto Login</a>\n"
"                        </td>\n"
"                    </tr>\n"
"                </tbody>\n"
"            </table>\n"
"            <table style=\"width:100%;text-align:justify;margin:0 auto;border-collapse:collapse;border-top:1px solid lightgray\"\">\n"
"                <tbody>\n"
"                    <tr>\n"
"                        <td style=\"padding:15px 10px;font-size:20px\">\n"
"                            <p style=\"color:#875A7B;margin:0\" >Hooray!</p><br>\n"
"                            <p dir=\"ltr\" style=\"font-size:15px;margin-top:0pt;margin-bottom:0pt;\">\n"
"                                <span>Your Odoo CRM application is up and running.</span></p><br>\n"
"                            <p dir=\"ltr\" style=\"margin-top:0pt;margin-bottom:8pt;\">\n"
"                                <span style=\"font-size:13px;font-weight:bold; \">What’s next?</span></p>\n"
"                            <ul style=\"margin-top:0pt;margin-bottom:0pt;font-size:13px;list-style-type:disc;\">\n"
"                                <li dir=\"ltr\">\n"
"                                    <p dir=\"ltr\" style=\"margin-top:0pt;margin-bottom:0pt;\">\n"
"                                        <span>Try creating a lead by sending an email to </span>\n"
"                                        <a href=\"mailto:${last_created_team.alias_id.name_get()[0][1] if last_created_team.alias_id.alias_domain else user.company_id.email}\">\n"
"                                            <span style=\"font-weight:bold; text-decoration:underline;\">${last_created_team.alias_id.name_get()[0][1] if last_created_team.alias_id.alias_domain else user.company_id.email}</span>\n"
"                                        </a>\n"
"                                    </p>\n"
"                                </li>\n"
"                                <li dir=\"ltr\">\n"
"                                    <p dir=\"ltr\" style=\"margin-top:0pt;margin-bottom:8pt;\">\n"
"                                        <span>Track your opportunities in your sale funnel by simply dragging and dropping the cards from one column to another</span>\n"
"                                    </p>\n"
"                                </li>\n"
"                            </ul> <br>\n"
"                            <p dir=\"ltr\" style=\"font-size:13px;margin-top:0pt;margin-bottom:8pt;\">\n"
"                                <span style=\"font-weight:bold;\">Discover the </span>\n"
"                                <span><a href=\"/web#view_type=list&model=crm.lead&action=crm.crm_lead_all_leads\">\n"
"                                    <span style=\"font-weight:bold; text-decoration:underline;\">CRM planner</span></a></span>\n"
"                                <span> to activate extra features</span>\n"
"                                <span style=\"color:#875A7B;margin:0;font-weight:bold\">(${user.env['web.planner'].get_planner_progress('planner_crm')}% done)</span>\n"
"                            </p>\n"
"                            <ul style=\"margin-top:0pt;margin-bottom:0pt;font-size:13px;list-style-type:disc;\">\n"
"                                <li dir=\"ltr\">\n"
"                                    <p dir=\"ltr\" style=\"margin-top:0pt;margin-bottom:0pt;\">\n"
"                                        <span>Automatically assign your leads to your salesmen with the lead scoring, </span>\n"
"                                    </p>\n"
"                                </li>\n"
"                                <li dir=\"ltr\">\n"
"                                    <p dir=\"ltr\" style=\"margin-top:0pt;margin-bottom:0pt;\">\n"
"                                        <span>Keep in touch with your potential client with efficient email campaigns,</span>\n"
"                                    </p>\n"
"                                </li>\n"
"                                <li dir=\"ltr\">\n"
"                                    <p dir=\"ltr\" style=\"margin-top:0pt;margin-bottom:0pt;\">\n"
"                                        <span>Gain high quality insight from surveys,</span>\n"
"                                    </p>\n"
"                                </li>\n"
"                                <li dir=\"ltr\">\n"
"                                    <p dir=\"ltr\" style=\"margin-top:0pt;margin-bottom:0pt;\">\n"
"                                        <span>Get smart reporting and accurate dashboards,</span>\n"
"                                    </p>\n"
"                                </li>\n"
"                                <li dir=\"ltr\">\n"
"                                    <p dir=\"ltr\" style=\"margin-top:0pt;margin-bottom:8pt;\">\n"
"                                        <span>And much more...</span>\n"
"                                    </p>\n"
"                                </li>\n"
"                            </ul>\n"
"                            <br>\n"
"                            <p dir=\"ltr\" style=\"font-size:13px;line-height:1.3;margin-top:0pt;margin-bottom:8pt;\">\n"
"                                <span style=\"font-weight:bold;\">Need Help?</span>\n"
"                                <span style=\"font-style:italic;\">You’re not alone</span>\n"
"                            </p>\n"
"                            <p dir=\"ltr\" style=\"font-size:13px;margin-top:0pt;margin-bottom:8pt;\">\n"
"                                <span>We would be delighted to assist you along the way. Contact us through our\n"
"                                <a href=\"https://www.odoo.com/help\">support form</a> if you have any question.\n"
"                                You can also discover how to get the best out of Odoo CRM with our </span>\n"
"                                <a target=\"_blank\" href=\"https://www.odoo.com/documentation/user/10.0/crm.html\">\n"
"                                <span style=\"text-decoration:underline;\">User Documentation</span></a>\n"
"                                </span><span> or with our </span>\n"
"                                <a target=\"_blank\" href=\"https://www.odoo.com/documentation\">\n"
"                                <span style=\"text-decoration:underline;\">API Documentation</span></a>\n"
"                            </p>\n"
"                            <br>\n"
"                            <p dir=\"ltr\" style=\"font-size:13px;margin-top:0pt;margin-bottom:8pt;\"><span>Enjoy your Odoo experience,</span></p>\n"
"                        </td>\n"
"                    </tr>\n"
"                </tbody>\n"
"            </table>\n"
"            <div dir=\"ltr\" style=\"font-size:13px;margin-top:0pt;margin-bottom:8pt;color:grey\">\n"
"                <span><br/>-- <br/>The Odoo Team<br/>PS: People love Odoo, check </span><a target=\"_blank\" href=\"https://twitter.com/odoo/favorites\"><span style=\"text-decoration:underline;\">what they say about it.</span></a></span>\n"
"            </div>\n"
"        </div>"
msgstr ""

#. module: crm
#: model:mail.template,body_html:crm.email_template_opportunity_reminder_mail
msgid ""
"\n"
"<p>Hello ${object.user_id and object.user_id.name or ''},</p>\n"
"<p>The opportunity <strong>${object.name}</strong> did not have any activity since at least 5 days.</p>\n"
"%if object.description:\n"
"<p>Here is the description about the opportunity : </p><p><i>${object.description}</i>\n"
"%endif\n"
"</p><p>Thank you!</p>\n"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_meeting_count
#: model:ir.model.fields,field_description:crm.field_res_partner_meeting_count
#: model:ir.model.fields,field_description:crm.field_res_users_meeting_count
msgid "# Meetings"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_opportunity_report_nbr_activities
msgid "# of Activities"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "(if leads are activated)"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "(you can change it here)"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ", if accounting or purchase is installed"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tour.js:29
#, python-format
msgid ""
"<b>Drag &amp; drop opportunities</b> between columns as you progress in your"
" sales cycle."
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tour.js:74
#, python-format
msgid ""
"<b>Invite coworkers</b> via email.<br/><i>Enter one email per line.</i>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "<i class=\"fa fa-envelope-o\"/> Ask Our Experts"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "<i class=\"fa fa-question-circle\"> view examples</i>"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tour.js:60
#, python-format
msgid ""
"<p><b>Send messages</b> to your prospect and get replies automatically "
"attached to this opportunity.</p><p class=\"mb0\">Type <i>'@'</i> to mention"
" people - it's like cc-ing on emails.</p>"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tour.js:45
#, python-format
msgid ""
"<p>You will be able to customize your followup activities. "
"Examples:</p><ol><li>introductory email</li><li>call 10 days "
"after</li><li>second call 3 days after, ...</li></ol><p "
"class='mb0'><i>Select a standard activity for now on.</i></p>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "<span class=\"fa fa-arrow-circle-o-down\"/> Install Events"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "<span class=\"fa fa-arrow-circle-o-down\"/> Install Mailing"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "<span class=\"fa fa-arrow-circle-o-down\"/> Install Slides"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "<span class=\"fa fa-arrow-circle-o-down\"/> Install Surveys"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "<span class=\"fa fa-arrow-circle-o-down\"/> Install Website Live Chat"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "<span class=\"fa fa-arrow-circle-o-down\"/> Install e-Commerce"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<span class=\"fa fa-lightbulb-o fa-2x\"/>\n"
"                        <strong>Tips:</strong>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<span class=\"fa fa-lightbulb-o fa-lg\"/>\n"
"                        <strong>Note:</strong>\n"
"                        Using templates can boost the quality of your\n"
"                        quotations and their success rate. However,\n"
"                        creating such templates can be a huge effort.\n"
"                        We recommend to start with standard quotations\n"
"                        and upgrade later."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<span class=\"fa fa-lightbulb-o fa-lg\"/>\n"
"                        <strong>Tip:</strong> For developers, you can\n"
"                        use our API to load data through scripts: read\n"
"                        the"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<span class=\"label label-default text-center odoo_purple\">Incoming "
"email</span>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "<span class=\"label label-default text-center odoo_purple\">Lead</span>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<span class=\"label label-default text-center "
"odoo_purple\">Opportunity</span>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<span class=\"label label-default text-center odoo_purple\">Quotation</span>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('meeting_count', '&lt;', 2)]}\"> Meetings</span>\n"
"                                    <span class=\"o_stat_text\" attrs=\"{'invisible': [('meeting_count', '&gt;', 1)]}\"> Meeting</span>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "<span class=\"oe_grey\"> at </span>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                            <span class=\"fa fa-laptop\"/>\n"
"                                            <strong> Screen Customization</strong>\n"
"                                        </span>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                            <span class=\"fa fa-pencil-square-o\"/>\n"
"                                            <strong> Create Custom Reports</strong>\n"
"                                        </span>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                            <span class=\"fa fa-sitemap\"/>\n"
"                                            <strong> Workflow Customization</strong>\n"
"                                        </span>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong>Import a file</strong><br/>\n"
"                                        <span class=\"small\">Recommended if &gt;50 items</span>\n"
"                                    </span>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong>Import</strong><br/>\n"
"                                        <span class=\"small\">&gt; 200 contacts</span>\n"
"                                    </span>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong>Subscriptions</strong><br/>\n"
"                                        <span class=\"small\"/>\n"
"                                    </span>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong> Create manually</strong><br/>\n"
"                                        <span class=\"small\">&lt; 200 contacts</span>\n"
"                                    </span>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong> Create manually</strong><br/>\n"
"                                        <span class=\"small\">Recommended if &lt;50 items</span>\n"
"                                    </span>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/>\n"
"                                        <strong>One-time sale</strong><br/>\n"
"                                        <span class=\"small\"/>\n"
"                                    </span>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong> Rules &amp; Formulas</strong>\n"
"                                    </span>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Specific Price per Audience</strong>\n"
"                                    </span>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>Unique Price (default)</strong>\n"
"                                    </span>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<span class=\"panel-title\"><span data-icon=\"\"/><strong> Exercise 1</strong></span>\n"
"                                    <span>(Lead Manager)</span>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<span class=\"panel-title\"><span data-icon=\"\"/><strong> Exercise 3</strong></span>\n"
"                                    <span>(Salesperson)</span>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<span class=\"panel-title\"><span data-icon=\"\"/><strong> Exercise 2</strong></span>\n"
"                                    <span>(Sales Manager)</span>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<span><strong>Contact us to customize your application:</strong><br/>\n"
"                                    We have special options for unlimited number of customizations!\n"
"                                    </span>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<strong>Click on 'Reporting' in the main menu </strong>and browse "
"statistics:"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "<strong>Events</strong>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<strong>Every business is different.</strong> Odoo allows to\n"
"                        customize every application and it's usually a good\n"
"                        advice to customize screens to fit your sales process.\n"
"                        <strong>Customizations are done by Odoo experts.</strong>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "<strong>Example: Negotiation stage</strong>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "<strong>Example: Qualified stage</strong>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<strong>Exercise: </strong>Try to analyse the average time to close a deal.\n"
"                                        Group X Axis: Creation Date\n"
"                                        Group Y Axis: Expected Closing Date\n"
"                                        Filter: Won Opportunities"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "<strong>Exercise: </strong>Try to switch the kanban to graph view"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<strong>Exercise:</strong> Try to get the number of leads per salesperson "
"and by creation date"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<strong>For complex imports</strong> (&gt;5000 items, product variants, "
"pictures, etc.):"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<strong>Forecast future revenues</strong><br/>\n"
"                                    You can assess the maturity of a sales channel by its ability to forecasts future revenues\n"
"                                    for the coming months or quarters. Train the team to forecast efficiently by having\n"
"                                    an up-to-date pipeline of opportunities.\n"
"                                    After a few month, set the real success rate according to the stages, to have\n"
"                                    accurate expected revenues by closing date."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "<strong>Mass mailing</strong>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "<strong>Negotiation:</strong>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "<strong>New:</strong>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "<strong>Next actions:</strong>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<strong>Organize weekly sales meetings</strong><br/>\n"
"                                    It's a great way to see where you can improve and keep track of the progress of your sales channel. Start the pipeline review by looking at the big picture; revenues and number of opportunities by stage, by expected closing date and by salespeople."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "<strong>Propositions:</strong>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "<strong>Qualified:</strong>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<strong>Quote template</strong> <span class=\"small\">(packaged "
"services)</span>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "<strong>Recommended actions:</strong>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<strong>Review the top 5 opportunities</strong><br/>\n"
"                                    Once the big picture is clear, focus on the best opportunities. Start from stages on the right (negotiation, proposal, etc) and review the biggest opportunities one by one with the team."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "<strong>Sales Stages</strong>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "<strong>Sales objectives</strong>:"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<strong>Sales tools</strong><br/>\n"
"                                Efficiently following up the pipeline will lead to great improvements. But to achieve excellence in your sales organization, you need  to provide your team with sales tools. For every stage of the opportunities pipeline, identify the blocking points and create  documents to train the team."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "<strong>Slides</strong>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<strong>Standard quotation</strong> <span class=\"small\">(products or "
"services)</span>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "<strong>Subscriptions</strong>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "<strong>Survey</strong>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<strong>The customer</strong> understood his pain points and proposed the "
"next step in the buying process"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "<strong>The prospect</strong> started to discuss your offer"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<strong>To boost your leads acquisition,</strong> you should have a look at "
"our marketing apps:"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<strong>Trainings</strong><br/>\n"
"                                To build a strong sales training program, you can use the following\n"
"                                apps of Odoo:"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "<strong>Website Live Chat</strong>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "<strong>You</strong> called the prospect for a follow up on your offer"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"<strong>You</strong> understand the customer's needs and pain points, and "
"you have a fairly good idea of his budget to spend"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "<strong>Your Main KPIs:</strong>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "<strong>e-Commerce</strong>"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team_alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "A good pipeline should have between 5 and 7 stages"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"A product in Odoo is something you sell or buy. It could\n"
"                    be a goods, consumable or services. Choose how you want to\n"
"                    create your products:"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "A sales manager will mostly use these two reports:"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_base_partner_merge_automatic_wizard_exclude_contact
msgid "A user associated to the contact"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "API documentation"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "Accept Emails From"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Activate the 'Online Quotations' option in"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report_active
#: model:ir.model.fields,field_description:crm.field_crm_lead_active
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason_active
#: model:ir.model.fields,field_description:crm.field_crm_opportunity_report_active
msgid "Active"
msgstr ""

#. module: crm
#: model:ir.ui.menu,name:crm.crm_activity_report_menu
msgid "Activities"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_activity_report_action
#: model:ir.actions.act_window,name:crm.crm_activity_report_action_team
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_graph
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_pivot
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_team_salesteams_view_kanban
msgid "Activities Analysis"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_users_target_sales_done
msgid "Activities Done Target"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Activities Todo"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_kanban_state
msgid "Activity State"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report_mail_activity_type_id
msgid "Activity Type"
msgstr ""

#. module: crm
#: model:ir.ui.menu,name:crm.crm_team_menu_config_activity_types
msgid "Activity Types"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Add Features"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Add a qualification step before creating an opportunity"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "Address"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team_alias_id
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Alias"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team_alias_contact
msgid "Alias Contact Security"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team_alias_name
msgid "Alias Name"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team_alias_domain
msgid "Alias domain"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team_alias_model_id
msgid "Aliased Model"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"Aligning your sales organization on a systematic\n"
"                        process will allow you to continuously improve your\n"
"                        sales performance."
msgstr ""

#. module: crm
#: code:addons/crm/wizard/base_partner_merge.py:274
#, python-format
msgid ""
"All contacts must have the same email. Only the Administrator can merge "
"contacts with different emails."
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:905
#, python-format
msgid ""
"All email incoming to %s will automatically\n"
"                    create new opportunity. Update your business card, phone book, social media,...\n"
"                    Send an email right now and see it here."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team_opportunities_amount
msgid "Amount of quotations to invoice"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
msgid "Analysis"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"Analyzing the pipeline by expected closing date will help your team "
"efficiently forecast its future revenues. The pipeline review will allow you"
" to identify sales inefficiencies or pick up on the best practices."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass_deduplicate
msgid "Apply deduplication"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Apply multi-currencies"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_kanban_view_leads
msgid "Archive"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Archived"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.base_partner_merge_automatic_wizard_form
msgid "Are you sure to execute the automatic merge of your contacts ?"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.base_partner_merge_automatic_wizard_form
msgid ""
"Are you sure to execute the list of automatic merges of your contacts ?"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.merge_opportunity_form
msgid "Assign opportunities to"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Assign these opportunities to"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
msgid "Assign this opportunity to"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_opportunity_report_opening_date
msgid "Assignation Date"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_date_open
msgid "Assigned"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"At the end of the deployment process, your expectations should be reached.\n"
"                        <br/>\n"
"                        If not, our CRM experts are available to help you achieve your KPIs."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Attach a qualification form to every house opportunity"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.base_partner_merge_automatic_wizard_form
msgid "Automatic Merge Wizard"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "Avg. of Probability"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "B2B"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "B2C"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Basic, Premium"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Better structure the sales process"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Better visibility on team activities"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:381
#, python-format
msgid "Boom! Team record for the past 30 days."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Boost your online sales with sleek product pages."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_message_bounce
msgid "Bounce"
msgstr ""

#. module: crm
#: model:ir.ui.menu,name:crm.crm_menu_root
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "CRM"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_activity_report
msgid "CRM Activity Analysis"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_opportunity_report
msgid "CRM Opportunity Analysis"
msgstr ""

#. module: crm
#: model:mail.activity.type,name:crm.mail_activity_demo_call_demo
msgid "Call for Demo"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_opportunity_report_campaign_id
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Campaign"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.base_partner_merge_automatic_wizard_form
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_lost_view_form
#: model_terms:ir.ui.view,arch_db:crm.merge_opportunity_form
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Cancel"
msgstr "Strika"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_team_act_tree
msgid "Cases by Sales Channel"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_lead_tag
msgid "Category of lead"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage_on_change
msgid "Change Probability Automatically"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Change the layout of a quotation"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_form
msgid "Channel"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team_use_leads
msgid ""
"Check this box to filter and qualify incoming requests as leads before "
"converting them into opportunities and assigning them to a salesperson."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team_use_opportunities
msgid "Check this box to manage a presales process with opportunities."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_city
#: model:ir.model.fields,field_description:crm.field_crm_opportunity_report_city
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "City"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead_tag_ids
msgid ""
"Classify and analyze your lead/opportunity categories like: Training, "
"Service"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tour.js:18
#, python-format
msgid ""
"Click here to <b>create your first opportunity</b> and add it to your "
"pipeline."
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tour.js:53
#, python-format
msgid "Click on an opportunity to zoom to it."
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:908
#, python-format
msgid "Click to add a new opportunity"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.relate_partner_opportunities
msgid "Click to create an opportunity related to this customer."
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lost_reason_action
msgid "Click to define a new lost reason."
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lead_tag_action
msgid "Click to define a new sales tag."
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_stage_action
msgid "Click to set a new stage in your opportunity pipeline."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.base_partner_merge_automatic_wizard_form
msgid "Close"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_opportunity_report_date_closed
msgid "Close Date"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/web_planner_crm.js:29
#, python-format
msgid ""
"Close opportunity if: \"pre-sales days * $500\" < \"expected revenue\" * "
"probability"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_date_closed
msgid "Closed Date"
msgstr ""

#. module: crm
#: code:addons/crm/wizard/crm_lead_to_opportunity.py:87
#, python-format
msgid "Closed/Dead leads cannot be converted into opportunities."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_color
#: model:ir.model.fields,field_description:crm.field_crm_lead_tag_color
msgid "Color Index"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Community &amp; Enterprise users:"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report_company_id
#: model:ir.model.fields,field_description:crm.field_crm_lead_company_id
#: model:ir.model.fields,field_description:crm.field_crm_opportunity_report_company_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Company"
msgstr "Fyritøka"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
msgid "Company Name"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Competition Matrix comparing prices"
msgstr ""

#. module: crm
#: model:ir.ui.menu,name:crm.crm_menu_config
msgid "Configuration"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tour.js:70
#, python-format
msgid "Configuration options are available in the Settings app."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Configure the stages of your sales pipeline"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Configure your pricelists"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Congratulations, you're done!"
msgstr ""

#. module: crm
#: model:crm.lead.tag,name:crm.categ_oppor7
msgid "Consulting"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Consumers, Retailers"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_res_partner
msgid "Contact"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "Contact Information"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_contact_name
msgid "Contact Name"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Contact Us"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_base_partner_merge_automatic_wizard_partner_ids
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Contacts"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass_name
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_name
msgid "Conversion Action"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_date_conversion
#: model:ir.model.fields,field_description:crm.field_crm_opportunity_report_date_conversion
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Conversion Date"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Conversion Date from Lead to Opportunity"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Conversion Options"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Convert to Opportunities"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Convert to Opportunity"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.action_crm_send_mass_convert
msgid "Convert to opportunities"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:1080
#: selection:crm.lead2opportunity.partner,name:0
#: selection:crm.lead2opportunity.partner.mass,name:0
#: model:ir.actions.act_window,name:crm.action_crm_lead2opportunity_partner
#, python-format
msgid "Convert to opportunity"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead_message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report_country_id
#: model:ir.model.fields,field_description:crm.field_crm_lead_country_id
#: model:ir.model.fields,field_description:crm.field_crm_opportunity_report_country_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Country"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_create_opportunity_simplified
msgid "Create"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_create_opportunity_simplified
msgid "Create & Edit"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Create Customers"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_create_date
msgid "Create Date"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
msgid "Create Opportunity"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Create Vendors"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/web_planner_crm.js:30
#, python-format
msgid "Create a Proof of Concept with consultants"
msgstr ""

#. module: crm
#: selection:crm.lead2opportunity.partner,action:0
#: selection:crm.partner.binding,action:0
msgid "Create a new customer"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.create_opportunity_simplified
#: model_terms:ir.ui.view,arch_db:crm.view_create_opportunity_simplified
msgid "Create an Opportunity"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Create and combine rules:<br/>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"Create sleek and attractive event pages. Sell online and organize on site."
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lead_tag_action
msgid ""
"Create specific tags that fit your company's activities\n"
"                to better classify and analyse your leads and opportunities.\n"
"                Such categories could for instance reflect your product\n"
"                structure or the different types of sales you do."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Create the three firsts opportunities"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Create your product items"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Create your templates of offers"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report_author_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Created By"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_base_partner_merge_automatic_wizard_create_uid
#: model:ir.model.fields,field_description:crm.field_base_partner_merge_line_create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass_create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost_create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_tag_create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason_create_uid
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity_create_uid
#: model:ir.model.fields,field_description:crm.field_crm_partner_binding_create_uid
#: model:ir.model.fields,field_description:crm.field_crm_stage_create_uid
msgid "Created by"
msgstr "Byrjað av"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_base_partner_merge_automatic_wizard_create_date
#: model:ir.model.fields,field_description:crm.field_base_partner_merge_line_create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass_create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost_create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_tag_create_date
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason_create_date
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity_create_date
#: model:ir.model.fields,field_description:crm.field_crm_partner_binding_create_date
#: model:ir.model.fields,field_description:crm.field_crm_stage_create_date
msgid "Created on"
msgstr "Byrjað tann"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_opportunity_report_create_date
msgid "Creation Date"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Creation Month"
msgstr ""

#. module: crm
#: model:ir.actions.server,name:crm.action_your_pipeline
msgid "Crm: My Pipeline"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_company_currency
msgid "Currency"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Current"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_base_partner_merge_automatic_wizard_current_line_id
msgid "Current Line"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:1135
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass_partner_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_partner_id
#: model:ir.model.fields,field_description:crm.field_crm_lead_partner_id
#: model:ir.model.fields,field_description:crm.field_crm_partner_binding_partner_id
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
#, python-format
msgid "Customer"
msgstr "Kundi"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings_module_web_clearbit
msgid "Customer Autocomplete"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:1126
#, python-format
msgid "Customer Email"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_partner_name
msgid "Customer Name"
msgstr ""

#. module: crm
#: model:ir.ui.menu,name:crm.res_partner_menu_crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Customers"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Customizations"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_team.py:121
#, python-format
msgid ""
"Dashboard graph content cannot be Pipeline if the sales channel doesn't use "
"it. (Pipeline is unchecked.)"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report_date
msgid "Date"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_day_open
msgid "Days to Assign"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_day_close
msgid "Days to Close"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.action_partner_deduplicate
msgid "Deduplicate Contacts"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.base_partner_merge_automatic_wizard_form
msgid "Deduplicate the other Contacts"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings_crm_alias_prefix
msgid "Default Alias Name for Leads"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team_alias_defaults
msgid "Default Values"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_opportunity_report_delay_open
msgid "Delay to Assign"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_opportunity_report_delay_close
msgid "Delay to Close"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_kanban_view_leads
msgid "Delete"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/web_planner_crm.js:27
#, python-format
msgid "Demonstration"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"Depending on how complex your business is, it may take between 6 months and 18 months for a new\n"
"                                    sales channel to efficiently forecasts future revenues."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Deployment"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
msgid "Describe the lead..."
msgstr ""

#. module: crm
#: model:crm.lead.tag,name:crm.categ_oppor5
msgid "Design"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Design efficient email campaigns. Send, convert and track performance."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_base_partner_merge_automatic_wizard_dst_partner_id
msgid "Destination Contact"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_create_opportunity_simplified
msgid "Discard"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_base_partner_merge_automatic_wizard_display_name
#: model:ir.model.fields,field_description:crm.field_base_partner_merge_line_display_name
#: model:ir.model.fields,field_description:crm.field_crm_activity_report_display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass_display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead_display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost_display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead_tag_display_name
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason_display_name
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity_display_name
#: model:ir.model.fields,field_description:crm.field_crm_opportunity_report_display_name
#: model:ir.model.fields,field_description:crm.field_crm_partner_binding_display_name
#: model:ir.model.fields,field_description:crm.field_crm_stage_display_name
msgid "Display Name"
msgstr "Vís navn"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Display your brochures, product sheets, showcases etc."
msgstr ""

#. module: crm
#: selection:crm.lead2opportunity.partner,action:0
#: selection:crm.lead2opportunity.partner.mass,action:0
#: selection:crm.partner.binding,action:0
msgid "Do not link to a customer"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Don't forget to change this address on:"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Don't hesitate to"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Don't use stages to qualify opportunities, use tags instead"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Download the"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"Each product has only one price.\n"
"                                        <br/>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_kanban_view_leads
msgid "Edit"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_base_partner_merge_automatic_wizard_group_by_email
#: model:ir.model.fields,field_description:crm.field_crm_lead_email_from
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "Email"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "Email Alias"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead_email_from
msgid "Email address of the contact"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid ""
"Emails received to that address generate new leads not assigned to any sales"
" channel yet. This can be made when converting them into opportunities. "
"Incoming emails can be automatically assigned to specific sales channels. To"
" do so, set an email alias on the sales channel."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "End"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Engage directly with your website visitors to convert them into leads."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_stage_requirements
msgid ""
"Enter here the internal requirements for this stage (ex: Offer sent to "
"customer). It will appear as a tooltip over the stage's name."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead_date_deadline
msgid "Estimate of the date on which the opportunity will be won."
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_calendar_event
msgid "Event"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"Every salesperson can analyze their own pipe by clicking on the 'switch view"
" to graph' icon."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"Every stage should be a step forward in the buyer's decision-making, not a "
"task to carry out"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Examples"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Examples:"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Excel template"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Exclude Opt Out"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.base_partner_merge_automatic_wizard_form
msgid "Exclude contacts having"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Existing Tools"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_date_deadline
#: model:ir.model.fields,field_description:crm.field_crm_opportunity_report_date_deadline
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Expected Closing"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Expected Closing Date"
msgstr ""

#. module: crm
#: selection:crm.team,dashboard_graph_group_pipeline:0
msgid "Expected Closing Day"
msgstr ""

#. module: crm
#: selection:crm.team,dashboard_graph_group_pipeline:0
msgid "Expected Closing Month"
msgstr ""

#. module: crm
#: selection:crm.team,dashboard_graph_group_pipeline:0
msgid "Expected Closing Week"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_planned_revenue
msgid "Expected Revenue"
msgstr ""

#. module: crm
#: model:ir.filters,name:crm.filter_opportunity_expected_revenue
msgid "Expected Revenue by Team"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "Expected Revenues"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team_dashboard_graph_period_pipeline
msgid "Expected to Close"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Expiration Closing Month"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_stage_legend_priority
msgid ""
"Explanation text to help users using the star and priority mechanism on "
"stages or issues that are in this stage."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Extended Filters"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
msgid "Extra Info"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/web_planner_crm.js:31
#, python-format
msgid "Final Proposal sent"
msgstr ""

#. module: crm
#: selection:base.partner.merge.automatic.wizard,state:0
msgid "Finished"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage_fold
msgid "Folded in Pipeline"
msgstr ""

#. module: crm
#: model:mail.activity.type,name:crm.mail_activity_demo_followup_quote
msgid "Follow-up Quote"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"For every deal, review the next action, the expected closing, the pain "
"points, the expected revenues, the buying process, etc."
msgstr ""

#. module: crm
#: code:addons/crm/wizard/base_partner_merge.py:263
#, python-format
msgid ""
"For safety reasons, you cannot merge more than 3 contacts together. You can "
"re-open the wizard several times if needed."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"For the Odoo Team,<br/>\n"
"                          Fabien Pinckaers, Founder"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass_force_assignation
msgid "Force assignation"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:577
#, python-format
msgid "From %s : %s"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Future Activities"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Future Activities (7 days)"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Future Activities (All)"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/web_planner_crm.js:30
#, python-format
msgid "GAP analysis with customer"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"Gain high quality insight from surveys, a great way to engage with prospects"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Gamification"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid ""
"Generate unassigned leads from incoming emails. Leads coming from\n"
"                                    that email will be assigned manually."
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_lead_lost
msgid "Get Lost Reason"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_email_cc
msgid "Global CC"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:379
#, python-format
msgid "Go, go, go! Congrats for your first deal."
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tour.js:79
#, python-format
msgid ""
"Good job! Your completed the tour of the CRM. You can continue with the "
"<b>implementation guide</b> to help you setup the CRM in your company."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"Great pipelines have stages <strong>aligned with the buyer's buying "
"process</strong>, not your selling process:"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Group By"
msgstr "Bólka eftir"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team_dashboard_graph_group_pipeline
msgid "Group by"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_base_partner_merge_automatic_wizard_number_group
msgid "Group of Contacts"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Grow leads acquisition"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Grow revenues"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_partner_binding
msgid "Handle partner binding or generation in CRM wizards."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Happy Selling!<br/>- The Odoo Team"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Have clear forecasts on sales"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Have fun deploying your sales strategy,"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"Here are a few examples of documents you should provide to your sales "
"channel based on the sales stage:"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Here are some of the <strong>customizations available</strong>:"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lead_action_activities
msgid ""
"Here is the list of your next activities. Those are linked to your opportunities.\n"
"                   To set a next activity, go on an opportunity and add one. It will then appear in this list."
msgstr ""

#. module: crm
#: selection:crm.lead,priority:0 selection:crm.opportunity.report,priority:0
msgid "High"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team_dashboard_graph_group_pipeline
msgid "How this channel's dashboard graph will group the results."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_base_partner_merge_automatic_wizard_id
#: model:ir.model.fields,field_description:crm.field_base_partner_merge_line_id
#: model:ir.model.fields,field_description:crm.field_crm_activity_report_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass_id
#: model:ir.model.fields,field_description:crm.field_crm_lead_id
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost_id
#: model:ir.model.fields,field_description:crm.field_crm_lead_tag_id
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason_id
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity_id
#: model:ir.model.fields,field_description:crm.field_crm_opportunity_report_id
#: model:ir.model.fields,field_description:crm.field_crm_partner_binding_id
#: model:ir.model.fields,field_description:crm.field_crm_stage_id
msgid "ID"
msgstr "ID"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team_alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_base_partner_merge_line_aggr_ids
msgid "Ids"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead_opt_out
msgid ""
"If opt-out is checked, this contact has refused to receive emails for mass "
"mailing and marketing campaign. Filter 'Available for Mass Mailing' allows "
"users to filter the leads when performing mass mailing."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead2opportunity_partner_mass_force_assignation
msgid "If unchecked, this will leave the salesman of duplicated opportunities"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"If you followed the preceding steps, you already setup\n"
"                        the foundations to a strong process leading to\n"
"                        continuous improvements of your sales channels."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"If you have less than 200 contacts, we recommend you\n"
"                                        to create them manually."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"If you sell subscriptions (whether it's invoiced annually or monthly), "
"activate the"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "If you want to do it yourself:"
msgstr ""

#. module: crm
#: model:ir.ui.menu,name:crm.menu_import_crm
msgid "Import & Synchronize"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Import using the top left button in"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Improve average revenue per salesperson"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "In Odoo, there are 3 different ways to manage your prices.<br/>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_search
msgid "Include archived"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Incoming Emails"
msgstr ""

#. module: crm
#: model:crm.lead.tag,name:crm.categ_oppor4
msgid "Information"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/web_planner_crm.js:22
#, python-format
msgid "Initial Contact"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "Internal Notes"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Invoice Analysis"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_base_partner_merge_automatic_wizard_group_by_is_company
msgid "Is Company"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_function
msgid "Job Position"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_base_partner_merge_automatic_wizard_exclude_journal_item
msgid "Journal Items associated to the contact"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "KPIs"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_date_action_last
msgid "Last Action"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_base_partner_merge_automatic_wizard___last_update
#: model:ir.model.fields,field_description:crm.field_base_partner_merge_line___last_update
#: model:ir.model.fields,field_description:crm.field_crm_activity_report___last_update
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner___last_update
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass___last_update
#: model:ir.model.fields,field_description:crm.field_crm_lead___last_update
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost___last_update
#: model:ir.model.fields,field_description:crm.field_crm_lead_tag___last_update
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason___last_update
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity___last_update
#: model:ir.model.fields,field_description:crm.field_crm_opportunity_report___last_update
#: model:ir.model.fields,field_description:crm.field_crm_partner_binding___last_update
#: model:ir.model.fields,field_description:crm.field_crm_stage___last_update
msgid "Last Modified on"
msgstr "Seinast rættað tann"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Last Month"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_date_last_stage_update
#: model:ir.model.fields,field_description:crm.field_crm_opportunity_report_date_last_stage_update
msgid "Last Stage Update"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_base_partner_merge_automatic_wizard_write_uid
#: model:ir.model.fields,field_description:crm.field_base_partner_merge_line_write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass_write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost_write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_tag_write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason_write_uid
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity_write_uid
#: model:ir.model.fields,field_description:crm.field_crm_partner_binding_write_uid
#: model:ir.model.fields,field_description:crm.field_crm_stage_write_uid
msgid "Last Updated by"
msgstr "Seinast dagført av"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_base_partner_merge_automatic_wizard_write_date
#: model:ir.model.fields,field_description:crm.field_base_partner_merge_line_write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass_write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost_write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_tag_write_date
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason_write_date
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity_write_date
#: model:ir.model.fields,field_description:crm.field_crm_partner_binding_write_date
#: model:ir.model.fields,field_description:crm.field_crm_stage_write_date
msgid "Last Updated on"
msgstr "Seinast dagført tann"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Late Activities"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:882
#: selection:crm.activity.report,lead_type:0 selection:crm.lead,type:0
#: selection:crm.opportunity.report,type:0
#: model:ir.model.fields,field_description:crm.field_crm_activity_report_lead_id
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#, python-format
msgid "Lead"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Lead / Customer"
msgstr ""

#. module: crm
#: model:mail.message.subtype,name:crm.mt_lead_create
#: model:mail.message.subtype,name:crm.mt_salesteam_lead
msgid "Lead Created"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lead_tag_action
#: model:ir.ui.menu,name:crm.menu_crm_lead_categ
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_tag_form
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_tag_tree
msgid "Lead Tags"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_lead2opportunity_partner
msgid "Lead To Opportunity Partner"
msgstr ""

#. module: crm
#: model:mail.message.subtype,description:crm.mt_lead_create
msgid "Lead created"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_lead
msgid "Lead/Opportunity"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.action_lead_mass_mail
msgid "Lead/Opportunity Mass Mail"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_case_form_view_salesteams_lead
#: model:ir.actions.act_window,name:crm.crm_lead_all_leads
#: model:ir.model.fields,field_description:crm.field_crm_team_use_leads
#: model:ir.model.fields,field_description:crm.field_res_config_settings_group_use_lead
#: model:ir.ui.menu,name:crm.crm_opportunity_report_menu_lead
#: model:ir.ui.menu,name:crm.menu_crm_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "Leads"
msgstr ""

#. module: crm
#: model:ir.ui.menu,name:crm.menu_crm_config_lead
msgid "Leads & Opportunities"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.action_report_crm_lead_salesteam
#: model:ir.actions.act_window,name:crm.crm_opportunity_report_action_lead
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_graph_lead
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_pivot_lead
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Leads Analysis"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.action_report_crm_lead_salesteam
msgid ""
"Leads Analysis allows you to check different CRM related information like "
"the treatment delays or number of leads per state. You can sort out your "
"leads analysis by different groups to get accurate grained analysis."
msgstr ""

#. module: crm
#: model:ir.filters,name:crm.filter_opportunity_salesperson
msgid "Leads By Salespersons"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
msgid "Leads Form"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_calendar_view_leads
msgid "Leads Generation"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Leads by Source"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Leads that are assigned to me"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Leads that did not ask not to be included in mass mailing campaigns"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid ""
"Leads that you selected that have duplicates. If the list is empty, it means"
" that no duplicates were found"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Leads with existing duplicates (for information)"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity_opportunity_ids
msgid "Leads/Opportunities"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Licences"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_base_partner_merge_automatic_wizard_line_ids
msgid "Lines"
msgstr ""

#. module: crm
#: selection:crm.lead.convert2task,action:0
#: selection:crm.lead2opportunity.partner,action:0
#: selection:crm.partner.binding,action:0
msgid "Link to an existing customer"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead_partner_id
msgid "Linked partner (optional). Usually created when converting the lead."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "List of possible pain points by segment/profile"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Look up company information (name, logo, etc.)"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/models/crm_lead.py:1086
#: code:addons/crm/static/src/js/web_planner_crm.js:33
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
#, python-format
msgid "Lost"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lead_lost_action
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost_lost_reason_id
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost_reason
#: model:ir.model.fields,field_description:crm.field_crm_opportunity_report_lost_reason
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_lost_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Lost Reason"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lost_reason_action
#: model:ir.ui.menu,name:crm.menu_crm_lost_reason
msgid "Lost Reasons"
msgstr ""

#. module: crm
#: selection:crm.lead,priority:0 selection:crm.opportunity.report,priority:0
msgid "Low"
msgstr ""

#. module: crm
#: model:mail.activity.type,name:crm.mail_activity_demo_make_quote
msgid "Make Quote"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings_generate_lead_from_alias
msgid "Manual Assignation of Emails"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Manually Assignation of Incoming Emails"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "Mark Lost"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "Mark Won"
msgstr ""

#. module: crm
#: model:ir.actions.server,name:crm.action_mark_as_lost
msgid "Mark as lost"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "Marketing"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_lead2opportunity_partner_mass
msgid "Mass Lead To Opportunity Partner"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_base_partner_merge_automatic_wizard_maximum_group
msgid "Maximum of Group of Contacts"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_opportunity_report_medium_id
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Medium"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:924
#, python-format
msgid "Meeting scheduled at '%s'<br> Subject: %s <br> Duration: %s hour(s)"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/web_planner_crm.js:24
#, python-format
msgid "Meeting with a demo. Set Fields: expected revenue, closing date"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.act_crm_opportunity_calendar_event_new
#: model:ir.actions.act_window,name:crm.calendar_event_partner
#: model:ir.model.fields,field_description:crm.field_res_partner_meeting_ids
#: model:ir.model.fields,field_description:crm.field_res_users_meeting_ids
#: model_terms:ir.ui.view,arch_db:crm.view_partners_form_crm1
msgid "Meetings"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.merge_opportunity_form
msgid "Merge"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.base_partner_merge_automatic_wizard_form
msgid "Merge Automatically"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.base_partner_merge_automatic_wizard_form
msgid "Merge Automatically all process"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.base_partner_merge_automatic_wizard_form
msgid "Merge Contacts"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.merge_opportunity_form
msgid "Merge Leads/Opportunities"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.action_partner_merge
msgid "Merge Selected Contacts"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.action_merge_opportunities
#: model:ir.actions.act_window,name:crm.merge_opportunity_act
msgid "Merge leads/opportunities"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_merge_opportunity
msgid "Merge opportunities"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.base_partner_merge_automatic_wizard_form
msgid "Merge the following contacts"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.base_partner_merge_automatic_wizard_form
msgid "Merge with Manual Check"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead2opportunity_partner_mass_deduplicate
msgid "Merge with existing leads/opportunities of each partner"
msgstr ""

#. module: crm
#: selection:crm.lead2opportunity.partner,name:0
#: selection:crm.lead2opportunity.partner.mass,name:0
msgid "Merge with existing opportunities"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:528
#, python-format
msgid "Merged lead"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:559
#, python-format
msgid "Merged leads"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:559
#, python-format
msgid "Merged opportunities"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:528
#, python-format
msgid "Merged opportunity"
msgstr ""

#. module: crm
#: code:addons/crm/wizard/base_partner_merge.py:295
#, python-format
msgid "Merged with the following partners:"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_base_partner_merge_line_min_id
msgid "MinID"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "Misc"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_mobile
msgid "Mobile"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Month"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Motivation letter from the management"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "My Activities"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "My Leads"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "My Opportunities"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "My Pipeline"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_base_partner_merge_automatic_wizard_group_by_name
#: model:ir.model.fields,field_description:crm.field_crm_lead_tag_name
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason_name
msgid "Name"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/web_planner_crm.js:27
#, python-format
msgid "Needs assessment"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/web_planner_crm.js:15
#: code:addons/crm/static/src/js/web_planner_crm.js:33
#, python-format
msgid "Negotiation"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/web_planner_crm.js:22
#: code:addons/crm/static/src/js/web_planner_crm.js:27
#: code:addons/crm/static/src/js/web_planner_crm.js:33
#: model:crm.stage,name:crm.stage_lead1
#, python-format
msgid "New"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.action_opportunity_form
msgid "New Opportunity"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/web_planner_crm.js:16
#, python-format
msgid "New propspect assigned to the right salesperson"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lead_action_activities
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_tree_activity
msgid "Next Activities"
msgstr ""

#. module: crm
#: selection:crm.lead,kanban_state:0
msgid "Next activity is planned"
msgstr ""

#. module: crm
#: selection:crm.lead,kanban_state:0
msgid "Next activity late"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:1146
#, python-format
msgid "No Subject"
msgstr ""

#. module: crm
#: selection:crm.lead,kanban_state:0
msgid "No next activity planned"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "No salesperson"
msgstr ""

#. module: crm
#: selection:crm.lead,priority:0 selection:crm.opportunity.report,priority:0
msgid "Normal"
msgstr ""

#. module: crm
#: model:crm.lost.reason,name:crm.lost_reason_3
msgid "Not enough stock"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Note: To use those features, you need to install the"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_description
msgid "Notes"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_opportunity_report_delay_close
msgid "Number of Days to close the case"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_opportunity_report_delay_open
msgid "Number of Days to open the case"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Number of Rooms"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team_opportunities_count
msgid "Number of open opportunities"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Odoo Default"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_case_form_view_salesteams_opportunity
#: model_terms:ir.actions.act_window,help:crm.crm_lead_opportunities
#: model_terms:ir.actions.act_window,help:crm.crm_lead_opportunities_tree_view
msgid ""
"Odoo helps you keep track of your sales pipeline to follow\n"
"                    up potential sales and better forecast your future revenues."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"Odoo is the world's only software to have a full integration of Marketing "
"Apps for your sales channel: insight sales, point of sale, ecommerce."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"Odoo will help you create polished, professional quotations and contracts in minutes.\n"
"                        Tell us how you sell and we will tell you what\n"
"                        configuration will best fit your quotation process."
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_case_form_view_salesteams_lead
#: model_terms:ir.actions.act_window,help:crm.crm_lead_all_leads
msgid ""
"Once qualified, the lead can be converted into a business\n"
"                    opportunity and/or a new customer in your address book."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"Once you’ve deployed your CRM, it’s time to grow your revenues. Start by "
"setting up a continuous improvement approach.We recommend you to organize "
"weekly sales meeting with the team during which you will do a pipeline "
"review."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Online users: we do it for you!"
msgstr ""

#. module: crm
#: code:addons/crm/wizard/base_partner_merge.py:287
#, python-format
msgid ""
"Only the destination contact may be linked to existing Journal Items. Please"
" ask the Administrator if you need to merge several contacts linked to "
"existing Journal Items."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_salesteams_view_kanban
msgid "Open Opportunities"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_salesteams_view_kanban
msgid "Open Opportunity"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_case_form_view_salesteams_opportunity
#: model:ir.actions.act_window,name:crm.crm_lead_opportunities
#: model:ir.actions.act_window,name:crm.relate_partner_opportunities
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass_opportunity_ids
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_opportunity_ids
#: model:ir.model.fields,field_description:crm.field_res_partner_opportunity_ids
#: model:ir.model.fields,field_description:crm.field_res_users_opportunity_ids
#: model:ir.ui.menu,name:crm.menu_crm_config_opportunity
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
#: model_terms:ir.ui.view,arch_db:crm.crm_case_graph_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_pivot_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
#: model_terms:ir.ui.view,arch_db:crm.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
#: model_terms:ir.ui.view,arch_db:crm.view_partners_form_crm1
msgid "Opportunities"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Opportunities Analysis"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.action_report_crm_opportunity_salesteam
msgid ""
"Opportunities Analysis gives you an instant access to your opportunities "
"with information such as the expected revenue, planned cost, missed "
"deadlines or the number of interactions per opportunity. This report is "
"mainly used by the sales manager in order to do the periodic review with the"
" channels of the sales pipeline."
msgstr ""

#. module: crm
#: model:ir.filters,name:crm.filter_opportunity_opportunities_cohort
msgid "Opportunities Cohort"
msgstr ""

#. module: crm
#: model:ir.filters,name:crm.filter_opportunity_opportunities_won_per_team
msgid "Opportunities Won Per Team"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Opportunities pipeline"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Opportunities that are assigned to me"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:427
#: code:addons/crm/models/crm_lead.py:857
#: selection:crm.activity.report,lead_type:0 selection:crm.lead,type:0
#: selection:crm.opportunity.report,type:0
#: model:ir.model.fields,field_description:crm.field_calendar_event_opportunity_id
#: model:ir.model.fields,field_description:crm.field_crm_lead_name
#: model:ir.model.fields,field_description:crm.field_res_partner_opportunity_count
#: model:ir.model.fields,field_description:crm.field_res_users_opportunity_count
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
#, python-format
msgid "Opportunity"
msgstr ""

#. module: crm
#: model:mail.message.subtype,name:crm.mt_lead_lost
#: model:mail.message.subtype,name:crm.mt_salesteam_lead_lost
msgid "Opportunity Lost"
msgstr ""

#. module: crm
#: model:mail.message.subtype,name:crm.mt_salesteam_lead_stage
msgid "Opportunity Stage Changed"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_create_opportunity_simplified
msgid "Opportunity Title"
msgstr ""

#. module: crm
#: model:mail.message.subtype,name:crm.mt_lead_won
#: model:mail.message.subtype,name:crm.mt_salesteam_lead_won
msgid "Opportunity Won"
msgstr ""

#. module: crm
#: model:mail.message.subtype,description:crm.mt_lead_lost
msgid "Opportunity lost"
msgstr ""

#. module: crm
#: model:mail.message.subtype,description:crm.mt_lead_won
msgid "Opportunity won"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_opt_out
msgid "Opt-Out"
msgstr ""

#. module: crm
#: selection:base.partner.merge.automatic.wizard,state:0
msgid "Option"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team_alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.base_partner_merge_automatic_wizard_form
msgid "Options"
msgstr ""

#. module: crm
#: model:crm.lead.tag,name:crm.categ_oppor8
msgid "Other"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Overdue Opportunities"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_opportunity_report_delay_expected
msgid "Overpassed Deadline"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team_alias_user_id
msgid "Owner"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/web_planner_crm.js:27
#, python-format
msgid "POC Sold"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/web_planner_crm.js:31
#, python-format
msgid "POC demonstration to the customer"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_base_partner_merge_automatic_wizard_group_by_parent_id
msgid "Parent Company"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team_alias_parent_model_id
msgid "Parent Model"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team_alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team_alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_opportunity_report_partner_id
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Partner"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_partner_address_email
msgid "Partner Contact Email"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_partner_address_name
msgid "Partner Contact Name"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report_partner_id
msgid "Partner/Customer"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.base_partner_merge_automatic_wizard_form
msgid "Partners"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_phone
msgid "Phone"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings_module_crm_phone_validation
msgid "Phone Validation"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/web_planner_crm.js:23
#, python-format
msgid "Phone call with following questions: ..."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Phone scripts"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_team.py:165
#: model:ir.actions.act_window,name:crm.crm_lead_opportunities_tree_view
#: model:ir.model.fields,field_description:crm.field_crm_team_use_opportunities
#: model:ir.ui.menu,name:crm.crm_menu_pipeline
#: model:ir.ui.menu,name:crm.crm_opportunity_report_menu
#: model:ir.ui.menu,name:crm.menu_crm_opportunities
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
#, python-format
msgid "Pipeline"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.action_report_crm_opportunity_salesteam
#: model:ir.actions.act_window,name:crm.crm_opportunity_report_action
#: model:ir.actions.act_window,name:crm.crm_opportunity_report_action_graph
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_graph
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_pivot
msgid "Pipeline Analysis"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_opportunity_report_action
msgid ""
"Pipeline Analysis gives you an instant access to\n"
"your opportunities with information such as the expected revenue, planned cost,\n"
"missed deadlines or the number of interactions per opportunity. This report is\n"
"mainly used by the sales manager in order to do the periodic review with the\n"
"teams of the sales pipeline."
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_opportunity_report_action_graph
msgid ""
"Pipeline Analysis gives you an instant access to\n"
"your opportunities with information such as the expected revenue, planned cost,\n"
"missed deadlines or the number of interactions per opportunity. This report is\n"
"mainly used by the sales manager in order to periodically review the pipeline\n"
"with the the sales channel."
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_team.py:231
#, python-format
msgid "Pipeline: Expected Revenue"
msgstr ""

#. module: crm
#: model:web.planner,tooltip_planner:crm.planner_crm
msgid "Plan your sales strategy: objectives, leads, KPIs, and much more!"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_web_planner
msgid "Planner"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:631
#, python-format
msgid ""
"Please select more than one element (lead or opportunity) from the list "
"view."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team_alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Pricing &amp; Discounts"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_priority
#: model:ir.model.fields,field_description:crm.field_crm_opportunity_report_priority
msgid "Priority"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage_legend_priority
msgid "Priority Management Explanation"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report_probability
#: model:ir.model.fields,field_description:crm.field_crm_lead_probability
#: model:ir.model.fields,field_description:crm.field_crm_opportunity_report_probability
msgid "Probability"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage_probability
msgid "Probability (%)"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_opportunity_report_expected_revenue
msgid "Probable Turnover"
msgstr ""

#. module: crm
#: model:crm.lead.tag,name:crm.categ_oppor1
msgid "Product"
msgstr "Vøra"

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/web_planner_crm.js:22
#, python-format
msgid "Product Demonstration"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Products"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/web_planner_crm.js:15
#: code:addons/crm/static/src/js/web_planner_crm.js:22
#: code:addons/crm/static/src/js/web_planner_crm.js:27
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
#, python-format
msgid "Proposal"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/web_planner_crm.js:33
#: model:crm.stage,name:crm.stage_lead3
#, python-format
msgid "Proposition"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Qualification form (you can use the survey app for this)"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/web_planner_crm.js:14
#: code:addons/crm/static/src/js/web_planner_crm.js:27
#: code:addons/crm/static/src/js/web_planner_crm.js:33
#: model:crm.stage,name:crm.stage_lead2
#, python-format
msgid "Qualified"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/web_planner_crm.js:14
#, python-format
msgid "Qualified Sponsor"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/web_planner_crm.js:25
#, python-format
msgid "Quotation sent"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/web_planner_crm.js:19
#, python-format
msgid "Quotation sent to customer"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/web_planner_crm.js:20
#, python-format
msgid "Quotation signed by the customer"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Read the documentation about pricing &amp; discounts"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tour.js:13
#, python-format
msgid ""
"Ready to boost your sales? Your <b>Pipeline</b> can be found here, under "
"<b>CRM</b> app."
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_lost_reason
msgid "Reason for loosing leads"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team_alias_force_thread_id
msgid "Record Thread ID"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_referred
msgid "Referred By"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_action
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass_action
#: model:ir.model.fields,field_description:crm.field_crm_lead_convert2task_action
#: model:ir.model.fields,field_description:crm.field_crm_partner_binding_action
msgid "Related Customer"
msgstr ""

#. module: crm
#: model:mail.template,subject:crm.email_template_opportunity_reminder_mail
msgid ""
"Reminder: Lead ${object.name} from ${object.partner_id != False and "
"object.partner_id.name or object.contact_name}"
msgstr ""

#. module: crm
#: model:ir.ui.menu,name:crm.crm_menu_report
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Reporting"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage_requirements
#: model_terms:ir.ui.view,arch_db:crm.crm_stage_form
msgid "Requirements"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_kanban_view_leads
msgid "Restore"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Revenue by Salesperson"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Sale Flow"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_team
#: model:ir.model.fields,field_description:crm.field_crm_activity_report_team_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass_team_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_team_id
#: model:ir.model.fields,field_description:crm.field_crm_lead_team_id
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity_team_id
#: model:ir.model.fields,field_description:crm.field_crm_opportunity_report_team_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Sales Channel"
msgstr ""

#. module: crm
#: model:ir.ui.menu,name:crm.crm_team_config
#: model:ir.ui.menu,name:crm.sales_team_menu_report_crm
msgid "Sales Channels"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Sales Forecasts (month+1)"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Sales Management module."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Sales Settings"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Sales Tools"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass_user_ids
msgid "Salesmen"
msgstr ""

#. module: crm
#: selection:crm.team,dashboard_graph_group_pipeline:0
#: model:ir.model.fields,field_description:crm.field_crm_activity_report_user_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass_user_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_user_id
#: model:ir.model.fields,field_description:crm.field_crm_lead_user_id
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity_user_id
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Salesperson"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"Save filters or add any report to your dashboard with the Favorites menu."
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tour.js:40
#, python-format
msgid "Schedule an activity by clicking here"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Search Leads"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Search Opportunities"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.base_partner_merge_automatic_wizard_form
msgid "Search duplicates based on duplicated data in"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.merge_opportunity_form
msgid "Select Leads/Opportunities"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.base_partner_merge_automatic_wizard_form
msgid ""
"Select the list of fields used to search for\n"
"                            duplicated records. If you select several fields,\n"
"                            Odoo will propose you to merge only those having\n"
"                            all these fields in common. (not one of the fields)."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Select your pricing preference and allow discounts"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.base_partner_merge_automatic_wizard_form
msgid ""
"Selected contacts will be merged together.\n"
"                                All documents linked to one of these contacts\n"
"                                will be redirected to the destination contact.\n"
"                                You can remove contacts from this list to avoid merging them."
msgstr ""

#. module: crm
#: selection:base.partner.merge.automatic.wizard,state:0
msgid "Selection"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"Send an email to all opportunities in the \"Qualified\" stage for more than "
"20 days"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage_sequence
msgid "Sequence"
msgstr ""

#. module: crm
#: model:crm.lead.tag,name:crm.categ_oppor3
msgid "Services"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/web_planner_crm.js:17
#: code:addons/crm/static/src/js/web_planner_crm.js:28
#, python-format
msgid "Set fields: Expected Revenue, Expected Closing Date, Next Action"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Set the sales manager as a follower of every opportunity above $20k"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_stage_on_change
msgid ""
"Setting this stage will change the probability automatically on the "
"opportunity."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"Setting up your sales funnel is just the first step. To achieve a\n"
"                      significant boost, we will help you get the most out of\n"
"                      Odoo CRM to transform how your salespeople work.\n"
"                      That's the purpose of this guide."
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_config_settings_action
#: model:ir.ui.menu,name:crm.crm_config_settings_menu
msgid "Settings"
msgstr ""

#. module: crm
#: model:res.groups,name:crm.group_use_lead
msgid "Show Lead Menu"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"Show advanced filters and options by clicking on the magnifying glass icon "
"next to the search bar."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Show all opportunities for which the next action date is before today"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid ""
"Show all opportunities with a deadline for which the next action date is "
"before today"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Show all records which has next action date is before today"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Show only lead"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Show only opportunity"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.base_partner_merge_automatic_wizard_form
msgid "Skip these contacts"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Slides"
msgstr ""

#. module: crm
#: model:crm.lead.tag,name:crm.categ_oppor2
msgid "Software"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Solution Selling"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lost_reason_action
msgid ""
"Some examples of lost reasons: \"We don't have people/skill\", \"Price too "
"high\""
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_opportunity_report_source_id
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Source"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_stage_team_id
msgid ""
"Specific team that uses this stage. Other teams will not be able to see or "
"use this stage."
msgstr ""

#. module: crm
#: selection:crm.team,dashboard_graph_group_pipeline:0
#: model:ir.model.fields,field_description:crm.field_crm_activity_report_stage_id
#: model:ir.model.fields,field_description:crm.field_crm_lead_stage_id
#: model:ir.model.fields,field_description:crm.field_crm_opportunity_report_stage_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_stage_form
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Stage"
msgstr ""

#. module: crm
#: model:mail.message.subtype,name:crm.mt_lead_stage
msgid "Stage Changed"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_opportunity_report_stage_name
#: model:ir.model.fields,field_description:crm.field_crm_stage_name
msgid "Stage Name"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_stage_search
msgid "Stage Search"
msgstr ""

#. module: crm
#: model:mail.message.subtype,description:crm.mt_lead_stage
msgid "Stage changed"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_stage
msgid "Stage of case"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_stage_action
#: model:ir.ui.menu,name:crm.menu_crm_lead_stage_act
#: model_terms:ir.ui.view,arch_db:crm.crm_stage_tree
msgid "Stages"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_stage_action
msgid ""
"Stages allow salespersons to easily track how a specific opportunity\n"
"            is positioned in the sales cycle."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_base_partner_merge_automatic_wizard_state
#: model:ir.model.fields,field_description:crm.field_crm_lead_state_id
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "State"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_street
msgid "Street"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "Street 2..."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "Street..."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_street2
msgid "Street2"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_lost_view_form
msgid "Submit"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Subscription Management"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report_subtype_id
msgid "Subtype"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report_subject
msgid "Summary"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"Systematic organization is what makes the difference\n"
"                        between good and great salespeople! Setup a pipeline\n"
"                        that is in line with your sales cycle."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Tag"
msgstr ""

#. module: crm
#: sql_constraint:crm.lead.tag:0
msgid "Tag name already exists !"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_tag_ids
msgid "Tags"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage_team_id
msgid "Team"
msgstr ""

#. module: crm
#: model:ir.filters,name:crm.ir_filters_crm_opportunity_report_next_action
msgid "Team Activities"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Templates of emails"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Templates of quotations (use Odoo Online Proposals for this)"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/web_planner_crm.js:14
#, python-format
msgid "Territory"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/web_planner_crm.js:19
#, python-format
msgid "The customer came back to you to discuss your quotation"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team_alias_id
msgid ""
"The email address associated with this channel. New emails received will "
"automatically create new leads assigned to the channel."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "The first stage is usually \"New\" and the last one is \"Won\""
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team_alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team_alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead_partner_name
msgid ""
"The name of the future partner company that will be created while converting"
" the lead into opportunity"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team_alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "The point of contact <strong>for all your prospects</strong>"
msgstr ""

#. module: crm
#: sql_constraint:crm.lead:0
msgid "The probability of closing the deal should be between 0% and 100%!"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"The three main practices to develop a sales force are\n"
"                        <strong>methodical organization</strong>, <strong>trainings</strong> and\n"
"                        <strong>good sales tools</strong>."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team_dashboard_graph_period_pipeline
msgid "The time period this channel's dashboard graph will consider."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"The whole process may take a few hours. But it's worth doing\n"
"                      it."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"There are several ways for your company to generate leads with Odoo CRM.\n"
"                        One of them is using your company's generic email address as a trigger\n"
"                        to create a new lead in the system. In Odoo, each one of your sales channels\n"
"                        is linked to its own email address from which prospects can reach them.\n"
"                        For example, if the personal email address of your Direct team is\n"
"                        <EMAIL>, every email sent when a new prospect\n"
"                        contacts you will automatically create a new opportunity into the sales channel.\n"
"\n"
"                        That's the easiest way to integrate Odoo with third party apps."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.base_partner_merge_automatic_wizard_form
msgid "There is no more contacts to merge for this request..."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead_email_cc
msgid ""
"These email addresses will be added to the CC field of all inbound and "
"outbound emails for this record before being sent. Separate multiple email "
"addresses with a comma"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "This Month"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"This email address has been preconfigured as the default\n"
"                                    for your sales department.<br/>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "This is the default configuration, there is nothing to setup."
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tour.js:35
#, python-format
msgid ""
"This opportunity has <b>no activity planned</b>. <i>Click to check them.</i>"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_stage_probability
msgid ""
"This percentage depicts the default/average probability of the Case for this"
" stage to be a success"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_opportunity_report_action_lead
msgid "This report analyses the source of your leads."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_stage_fold
msgid ""
"This stage is folded in the kanban view when there are no records in that "
"stage to display."
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:1052
#, python-format
msgid "This target does not exist."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Time to Close a Deal"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Time to Qualify a Lead"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_title
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "Title"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"To stimulate your customers, show them the public price and the discount "
"applied to it. This is settable in the pricelist configuration if you allow "
"discounts."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Today Activities"
msgstr ""

#. module: crm
#: model:crm.lost.reason,name:crm.lost_reason_1
msgid "Too expensive"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_opportunity_report_total_revenue
msgid "Total Revenue"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
msgid "Tracking"
msgstr ""

#. module: crm
#: model:crm.lead.tag,name:crm.categ_oppor6
msgid "Training"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report_lead_type
#: model:ir.model.fields,field_description:crm.field_crm_lead_type
#: model:ir.model.fields,field_description:crm.field_crm_opportunity_report_type
msgid "Type"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_activity_report_lead_type
#: model:ir.model.fields,help:crm.field_crm_lead_type
#: model:ir.model.fields,help:crm.field_crm_opportunity_report_type
msgid "Type is used to separate Leads and Opportunities"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "USD, EUR, GBP"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Unassigned"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_salesteams_view_kanban
msgid "Unassigned Lead"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team_unassigned_leads_count
#: model_terms:ir.ui.view,arch_db:crm.crm_team_salesteams_view_kanban
msgid "Unassigned Leads"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"Unless you are starting a new business, you probably have a list of "
"customers and vendors you'd like to import."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_kanban_view_leads
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead_kanban
msgid "Unread Messages"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_write_date
msgid "Update Date"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Upsell more to existing customers"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Use"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Use an External Email Server"
msgstr ""

#. module: crm
#: selection:crm.lead2opportunity.partner.mass,action:0
msgid "Use existing partner or create"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_case_form_view_salesteams_lead
#: model_terms:ir.actions.act_window,help:crm.crm_lead_all_leads
msgid ""
"Use leads if you need a qualification step before creating an\n"
"                    opportunity or a customer. It can be a business card you received,\n"
"                    a contact form filled in your website, or a file of unqualified\n"
"                    prospects you import, etc."
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lost_reason_action
msgid "Use lost reasons to explain why an opportunity is lost."
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.relate_partner_opportunities
msgid ""
"Use opportunities to keep track of your sales pipeline, follow\n"
"                up potential sales and better forecast your future revenues."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Use our import templates to get your catalog ready in no time!"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"Use subscription for recurring billing.\n"
"                                            Examples of subscription may include:\n"
"                                            annual support contract, monthly\n"
"                                            subscription to a service, etc."
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tour.js:83
#, python-format
msgid "Use the <b>implementation guide</b> to setup the CRM in your company."
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tour.js:65
#, python-format
msgid "Use the breadcrumbs to <b>go back to your sales pipeline</b>."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead_user_login
msgid "Used to log into the system"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_stage_sequence
msgid "Used to order stages. Lower is better."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_opportunity_report_user_id
msgid "User"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_user_email
msgid "User Email"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_user_login
msgid "User Login"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_res_users
msgid "Users"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_base_partner_merge_automatic_wizard_group_by_vat
msgid "VAT"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "VIP letter to potential sponsors"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Validate and normalize phone numbers."
msgstr ""

#. module: crm
#: selection:crm.lead,priority:0 selection:crm.opportunity.report,priority:0
msgid "Very High"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"We are here to help you. If you get stuck, do not hesitate to\n"
"                      reach our"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "We can add fields related to your business on any screen, for example"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "We can automate steps in your workflow, for example:"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"We can handle the whole import process\n"
"                                        for you: simply send your Odoo project\n"
"                                        manager a CSV file containing all your\n"
"                                        data."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"We can implement custom reports for you based on your Word templates, for "
"example:"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"We designed Odoo to help you grow your business. Odoo CRM\n"
"                      is not just a tool, we want it to have a major impact on your\n"
"                      sales performance. That's our mission, and we are serious about it."
msgstr ""

#. module: crm
#: model:crm.lost.reason,name:crm.lost_reason_2
msgid "We don't have people/skills"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "We hope this process helped you bring your sales vision to life."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_website
msgid "Website"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead_website
msgid "Website of the contact"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Weekly Meetings"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Welcome"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"What <strong>kind of proposals</strong> do you usually send to your "
"customers ?"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"What are the main KPIs you need to track for your the sales activities?"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"What are your sales objectives? What challenges are you\n"
"                        dealing with? Being clear on your expectations is the first step\n"
"                        of a successful implementation."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid ""
"When populating your address book, Odoo relies on Clearbit’s API to provide "
"you with a list of matching contacts or companies.When selecting one item, "
"the partner name, logo and website get automatically set."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead_team_id
msgid ""
"When sending mails, the default email address is taken from the sales "
"channel."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Win / Loss Ratio"
msgstr ""

#. module: crm
#: selection:crm.team,dashboard_graph_period_pipeline:0
msgid "Within a Month"
msgstr ""

#. module: crm
#: selection:crm.team,dashboard_graph_period_pipeline:0
msgid "Within a Week"
msgstr ""

#. module: crm
#: selection:crm.team,dashboard_graph_period_pipeline:0
msgid "Within a Year"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_base_partner_merge_line_wizard_id
msgid "Wizard"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/models/crm_lead.py:1085
#: code:addons/crm/static/src/js/web_planner_crm.js:15
#: code:addons/crm/static/src/js/web_planner_crm.js:22
#: code:addons/crm/static/src/js/web_planner_crm.js:27
#: code:addons/crm/static/src/js/web_planner_crm.js:33
#: model:crm.stage,name:crm.stage_lead4
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
#, python-format
msgid "Won"
msgstr ""

#. module: crm
#: model:ir.filters,name:crm.filter_opportunity_country
msgid "Won By Country"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_users_target_sales_won
msgid "Won in Opportunities Target"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:383
#, python-format
msgid "Yeah! Deal of the last 7 days for the team."
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/web_planner_crm.js:18
#, python-format
msgid ""
"You are in discussion with the decision maker and HE agreed on his pain "
"points"
msgstr ""

#. module: crm
#: code:addons/crm/wizard/base_partner_merge.py:270
#, python-format
msgid "You cannot merge a contact with one of his parent."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"You don't need to review lost opportunities at every sales meeting, but it's good\n"
"                            practice to do it at least once a month. Ask your team to set tags on opportunities to\n"
"                            identify why they were lost and use this information to generate statistics. Here are\n"
"                            some examples of tags to create: no budget, competition, no pain found, etc."
msgstr ""

#. module: crm
#: code:addons/crm/wizard/base_partner_merge.py:362
#, python-format
msgid "You have to specify a filter for your selection"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:385
#, python-format
msgid "You just beat your personal record for the past 30 days."
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:387
#, python-format
msgid "You just beat your personal record for the past 7 days."
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_case_form_view_salesteams_opportunity
msgid ""
"You will be able to plan meetings and log activities from\n"
"                    opportunities, convert them into quotations, attach related\n"
"                    documents, track all discussions, and much more."
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.relate_partner_opportunities
msgid ""
"You will be able to plan meetings and log activities from\n"
"                opportunities, convert them into quotations, attach related\n"
"                documents, track all discussions, and much more."
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lead_opportunities
#: model_terms:ir.actions.act_window,help:crm.crm_lead_opportunities_tree_view
msgid ""
"You will be able to plan meetings and phone calls from\n"
"                    opportunities, convert them into quotations, attach related\n"
"                    documents, track all discussions, and much more."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Your Expectations"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Your KPIs"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Your LinkedIn or social media pages"
msgstr ""

#. module: crm
#: model:mail.template,subject:crm.mail_template_data_module_install_crm
msgid "Your Odoo CRM application is running smoothly."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Your business cards"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Your document templates"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "Your website"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "ZIP"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_zip
msgid "Zip"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "additional 10% during summer sales"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "app."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "as an exercise"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_base_partner_merge_automatic_wizard
msgid "base.partner.merge.automatic.wizard"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_base_partner_merge_line
msgid "base.partner.merge.line"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "browse the documentation"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "e.g. 15% discount when buying more than 3 t-shirts"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_create_opportunity_simplified
msgid "e.g. Customer Deal"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "e.g. Product Pricing"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "e.g. www.odoo.com"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:912
#, python-format
msgid "opportunities"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "or"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_res_config_settings
msgid "res.config.settings"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "send us an email"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "support team"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "survey"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "the list of customers"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "to create internal challenge for your salesmen"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "to create tests and certifications."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "to describe<br/> your experience or to suggest improvements!"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"to organize all your documents like: customer presentations, product sheets,"
" comparisons with competitors, pain points sheets, training materials, etc."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid ""
"to setup your own domain\n"
"                                 name (e.g. <EMAIL>)"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:918
#, python-format
msgid "unknown"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "use Odoo API to import through scripts"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_planner
msgid "with your work above"
msgstr ""
