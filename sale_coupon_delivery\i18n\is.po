# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sale_coupon_delivery
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 14:06+0000\n"
"PO-Revision-Date: 2018-08-24 11:48+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Icelandic (https://www.transifex.com/odoo/teams/41243/is/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: is\n"
"Plural-Forms: nplurals=2; plural=(n % 10 != 1 || n % 100 == 11);\n"

#. module: sale_coupon_delivery
#: selection:sale.coupon.reward,reward_type:0
msgid "Discount"
msgstr "Afsláttur"

#. module: sale_coupon_delivery
#: model:ir.model.fields,help:sale_coupon_delivery.field_sale_coupon_reward__reward_type
msgid ""
"Discount - Reward will be provided as discount.\n"
"Free Product - Free product will be provide as reward \n"
"Free Shipping - Free shipping will be provided as reward (Need delivery module)"
msgstr ""

#. module: sale_coupon_delivery
#: code:addons/sale_coupon_delivery/models/sale_order.py:33
#, python-format
msgid "Discount: "
msgstr ""

#. module: sale_coupon_delivery
#: selection:sale.coupon.reward,reward_type:0
msgid "Free Product"
msgstr ""

#. module: sale_coupon_delivery
#: code:addons/sale_coupon_delivery/models/sale_coupon_reward.py:18
#: selection:sale.coupon.reward,reward_type:0
#, python-format
msgid "Free Shipping"
msgstr ""

#. module: sale_coupon_delivery
#: model:ir.model.fields,field_description:sale_coupon_delivery.field_sale_coupon_reward__reward_type
msgid "Reward Type"
msgstr ""

#. module: sale_coupon_delivery
#: model:ir.model,name:sale_coupon_delivery.model_sale_order
msgid "Sale Order"
msgstr ""

#. module: sale_coupon_delivery
#: model:ir.model,name:sale_coupon_delivery.model_sale_coupon_program
msgid "Sales Coupon Program"
msgstr ""

#. module: sale_coupon_delivery
#: model:ir.model,name:sale_coupon_delivery.model_sale_coupon_reward
msgid "Sales Coupon Reward"
msgstr ""

#. module: sale_coupon_delivery
#: model:ir.model,name:sale_coupon_delivery.model_sale_order_line
msgid "Sales Order Line"
msgstr "Sales Order Line"

#. module: sale_coupon_delivery
#: code:addons/sale_coupon_delivery/models/sale_coupon.py:13
#: code:addons/sale_coupon_delivery/models/sale_coupon_program.py:23
#, python-format
msgid "The shipping costs are not in the order lines."
msgstr ""
