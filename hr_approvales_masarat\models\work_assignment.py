# -*- coding: utf-8 -*-
from odoo import tools, api, fields, models
from datetime import datetime, date, timedelta
from odoo.exceptions import ValidationError
from dateutil.relativedelta import relativedelta
import math


class HrEmployeeAssignment(models.Model):

    _name = "hr.masarat.work.assignment"

    _inherit = ['mail.thread', 'mail.activity.mixin']

    _rec_name = 'assignment_name'

    assignment_name = fields.Char(compute='_get_assignment_name', store=True)

    state = fields.Selection(selection=[('draft', 'Draft'),
                                        ('manager_approval', 'Manager Approval'),
                                        ('manager_refused', 'Manager Refused'),
                                        ('hr_approval', 'HR Approval'),
                                        ('hr_refused', 'HR Refused')], default='draft', string="State")

    request_date = fields.Date(string="Date of Request", readonly=True,default=lambda self: fields.Date.to_string(date.today()))
    request_date_3days = fields.Date(string="5 days of Request", readonly=True, default=lambda self: fields.Date.to_string(date.today() - timedelta(days=100)))

    employee_id = fields.Many2one('hr.employee', string="Employee")
    manager_id = fields.Many2one('hr.employee', readonly=True, related='employee_id.parent_id', string="Manager")

    start_date_time = fields.Datetime(string='تاريخ ووقت البدأ',required = True)
    end_date_time = fields.Datetime(string='تاريخ ووقت الانتهاء',required = True)

    start_date = fields.Date(compute='_compute_total_work_hours_start_end_date',store=True)
    end_date = fields.Date(compute='_compute_total_work_hours_start_end_date',store=True)

    total_work_hours = fields.Char(string='إجمالي أيام التكليف', compute='_compute_total_work_hours_start_end_date')

    assignment_type = fields.Selection(selection=[('work_outside','تكليف بعمل'),
                                               ('work_home_privet', 'عمل من المنزل لظرف خاص'),
                                               ('work_home_company', 'عمل من المنزل بأمر من الشركة')],string="نوع التكليف", required=True)

    additional_note = fields.Text(string="Details and Reasons", required = True)

    is_manager = fields.Char(compute='call_with_sudo_is_manager')
    is_hr_group = fields.Char(compute='call_with_sudo_is_hr_group')


    def get_if_hr_group(self):
        hr_group = self.env.user.has_group('hr_approvales_masarat.group_hr_approvales_masarat')
        for rec in self:
            if hr_group:
                rec.is_hr_group = 'yes'
            else:
                rec.is_hr_group = 'no'

    def compute_button_visible(self):
        for rec in self:
            if rec.manager_id.user_id.id == self._uid:
                rec.is_manager = '1'
            else:
                rec.is_manager = '0'

    @api.model
    def call_with_sudo_is_manager(self):
        self.sudo().compute_button_visible()

    @api.depends('is_hr_group')
    def call_with_sudo_is_hr_group(self):
        self.sudo().get_if_hr_group()

    @api.depends('employee_id', 'start_date_time')
    def _get_assignment_name(self):
        for elem in self:
            elem.assignment_name = False
            if elem.employee_id and elem.start_date_time:
                elem.assignment_name = elem.employee_id.name + '-Work Assignment Approval-' + str(elem.start_date_time)[:10]

    def make_cancel_approval(self):
        self.state = 'draft'
    def make_manager_approval(self):
        self.state = 'manager_approval'
    def make_manager_refused(self):
        self.state = 'manager_refused'
    def make_hr_approval(self):
        self.state = 'hr_approval'
    def make_hr_refused(self):
        self.state = 'hr_refused'


    @api.depends('start_date_time', 'end_date_time')
    def _compute_total_work_hours_start_end_date(self):
        for elem in self:
            elem.total_work_hours=''
            elem.start_date = False
            elem.end_date = False
            if elem.start_date_time and elem.end_date_time:
                elem.total_work_hours = str(elem.end_date_time-elem.start_date_time)
                elem.start_date = elem.start_date_time.date()
                elem.end_date = elem.end_date_time.date()

    @api.model
    def default_get(self, fields):
        res = super(HrEmployeeAssignment, self).default_get(fields)
        user_id = self._context.get('uid')
        employee_id = self.env['hr.employee'].search([('user_id','=',user_id)])
        res['employee_id'] = employee_id.id
        ## Check For Hr Group
        hr_group = self.env.user.has_group('hr_approvales_masarat.group_hr_approvales_masarat')
        if hr_group:
            res['is_hr_group'] = 'yes'
        else:
            res['is_hr_group'] = 'no'
        ########################
        return res

    def unlink(self):
        for elem in self:
            if elem.state !='draft':
                raise ValidationError('You cannot delete a Latency request which is not in draft state')
            return super(HrEmployeeAssignment, self).unlink()


    @api.constrains('start_date_time','end_date_time')
    def constrains_dates(self):
        for elem in self:
            if elem.start_date_time and elem.end_date_time:
                if str(elem.end_date_time) <= str(elem.start_date_time):
                    raise ValidationError("End Date Most Be Greater Than Start Date!")

    @api.constrains('end_date_time')
    def make_sure_end_date(self):
        hr_group = self.env.user.has_group('hr_approvales_masarat.group_hr_approvales_masarat')
        for elem in self:
            days_count = (elem.request_date - elem.end_date_time.date()).days
            if days_count >= 4 and (not hr_group):
                raise ValidationError('You Exceeded 4 days for the request!')


    # def action_send_notification_to_maneger(self):
    #     template_id = self.env.ref('hr_approvales_masarat.work_assignment_approval_template').id
    #     self.env['mail.template'].browse(template_id).send_mail(self.id, force_send=True)
    #
    # @api.model
    # def create(self, vals_list):
    #     obj = super(HrEmployeeAssignment, self).create(vals_list)
    #     self.sudo().action_send_notification_to_maneger()
    #     return obj

    def action_send_notification_to_maneger(self, employee_id, recode_id):
        employee = self.env['hr.employee'].search([('id', '=', employee_id)])
        email_to = employee.parent_id.work_email
        email_from = employee.work_email
        web_base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        web_base_url += '/web#id=%d&view_type=form&model=%s' % (recode_id, self._name)
        body = """
        <div dir="rtl">
            <p><font style="font-size: 14px;">Your Employee """ + employee.name + """, requested work assignment, </font></p>
            <p><font style="font-size: 14px;">Please login to Odoo in order to proceed.</font></p>
            <a href="%s">Request Link</a>
        </div>""" % (web_base_url)
        template_id = self.env['mail.mail'].create({
            'subject': 'تكليف بعمل',
            'email_from': email_from,
            'email_to': email_to,
            'body_html': body})
        template_id.send()

    @api.model
    def create(self, vals_list):
        obj = super(HrEmployeeAssignment, self).create(vals_list)
        recode_id = obj.id
        employee_id = obj.employee_id.id
        self.sudo().action_send_notification_to_maneger(employee_id, recode_id)
        return obj



class HrPayslipX(models.Model):
    _inherit = 'hr.payslip'

    work_assighnment_deduction = fields.Integer(string='Work from home Assignment Deduction', compute='get_work_assighnment_deduction', store=True)


    @api.depends('employee_id','date_to','date_from')
    def get_work_assighnment_deduction(self):
        for payslip in self:
            payslip.work_assighnment_deduction=0
            if not payslip.employee_id.contract_id.resource_calendar_id.there_is_letancy:
                continue
            if payslip.date_from and payslip.date_to:
                fmt = '%Y-%m-%d'
                pre_date = str((datetime.strptime(str(payslip.date_from), fmt) - relativedelta(months=1)).strftime(fmt))[:8] + '01'
                if pre_date:
                    attendance_date_from = (datetime.strptime(pre_date, fmt))
                    attendance_date_to = (datetime.strptime(pre_date, fmt) + relativedelta(months=1, day=1, days=-1)).strftime(fmt)

                assignemet = self.env['hr.masarat.work.assignment'].search([('state','in',('hr_approval','manager_approval')),('assignment_type','=','work_home_privet'),('employee_id', '=', payslip.employee_id.id), ('start_date','>=',attendance_date_from), ('end_date','<=',attendance_date_to)])
                for elem in assignemet:
                    total_assignment = (elem.end_date_time - elem.start_date_time).total_seconds()/60/60
                    if total_assignment <= 24:
                        payslip.work_assighnment_deduction += 8
                    else:
                        number_of_days = math.ceil(total_assignment/24)
                        payslip.work_assighnment_deduction += number_of_days*8

                    # print((elem.end_date_time - elem.start_date_time).total_seconds()/60/60/24)
                    # payslip.work_assighnment_deduction+=int(elem.computed_latency)
