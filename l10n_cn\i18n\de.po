# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_cn
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON>Nesselbosch, 2022
# <PERSON>, 2022
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-04-05 14:50+0000\n"
"PO-Revision-Date: 2022-04-08 12:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: l10n_cn
#: model:ir.actions.report,print_report_name:l10n_cn.account_voucher_cn
msgid "'Voucher_%s' % (object.name)"
msgstr ""

#. module: l10n_cn
#: model_terms:ir.ui.view,arch_db:l10n_cn.report_voucher_document
msgid "<span>借方</span>"
msgstr ""

#. module: l10n_cn
#: model_terms:ir.ui.view,arch_db:l10n_cn.report_voucher_document
msgid "<span>合计：</span>"
msgstr ""

#. module: l10n_cn
#: model_terms:ir.ui.view,arch_db:l10n_cn.report_voucher_document
msgid "<span>摘要</span>"
msgstr ""

#. module: l10n_cn
#: model_terms:ir.ui.view,arch_db:l10n_cn.report_voucher_document
msgid "<span>科目</span>"
msgstr ""

#. module: l10n_cn
#: model_terms:ir.ui.view,arch_db:l10n_cn.report_voucher_document
msgid "<span>记账凭证</span>"
msgstr ""

#. module: l10n_cn
#: model_terms:ir.ui.view,arch_db:l10n_cn.report_voucher_document
msgid "<span>贷方</span>"
msgstr ""

#. module: l10n_cn
#: model_terms:ir.ui.view,arch_db:l10n_cn.report_voucher_document
msgid "<strong>凭证号：</strong>"
msgstr ""

#. module: l10n_cn
#: model_terms:ir.ui.view,arch_db:l10n_cn.report_voucher_document
msgid "<strong>制单：</strong>"
msgstr ""

#. module: l10n_cn
#: model_terms:ir.ui.view,arch_db:l10n_cn.report_voucher_document
msgid "<strong>审核：</strong>"
msgstr ""

#. module: l10n_cn
#: model_terms:ir.ui.view,arch_db:l10n_cn.report_voucher_document
msgid "<strong>日期：</strong>"
msgstr ""

#. module: l10n_cn
#: model_terms:ir.ui.view,arch_db:l10n_cn.report_voucher_document
msgid "<strong>过账：</strong>"
msgstr ""

#. module: l10n_cn
#: model_terms:ir.ui.view,arch_db:l10n_cn.report_voucher_document
msgid "<strong>附件数：</strong>"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_2711
#: model:account.account.template,name:l10n_cn.l10n_cn_2711
msgid "Account payable special funds"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_2202
#: model:account.account.template,name:l10n_cn.l10n_cn_2202
msgid "Accounts Payable"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1122
#: model:account.account.template,name:l10n_cn.l10n_cn_1122
msgid "Accounts Receivable"
msgstr "Forderungen"

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1124
#: model:account.account.template,name:l10n_cn.l10n_cn_1124
msgid "Accounts Receivable (PoS)"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1702
#: model:account.account.template,name:l10n_cn.l10n_cn_1702
msgid "Accumulated amortization"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1602
#: model:account.account.template,name:l10n_cn.l10n_cn_1602
msgid "Accumulated depreciation"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1123
#: model:account.account.template,name:l10n_cn.l10n_cn_1123
msgid "Advance Payment"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_6701
#: model:account.account.template,name:l10n_cn.l10n_cn_6701
msgid "Assets impairment Loss"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1503
#: model:account.account.template,name:l10n_cn.l10n_cn_1503
msgid "Available for sale financial assets"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1231
#: model:account.account.template,name:l10n_cn.l10n_cn_1231
msgid "Bad Debt Provisions"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_2201
#: model:account.account.template,name:l10n_cn.l10n_cn_2201
msgid "Bills Payable"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1121
#: model:account.account.template,name:l10n_cn.l10n_cn_1121
msgid "Bills Receivable"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_2502
#: model:account.account.template,name:l10n_cn.l10n_cn_2502
msgid "Bonds Payable"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_4002
#: model:account.account.template,name:l10n_cn.l10n_cn_4002
msgid "Capital Surplus"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1408
#: model:account.account.template,name:l10n_cn.l10n_cn_1408
msgid "Consigned processing materials"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1604
#: model:account.account.template,name:l10n_cn.l10n_cn_1604
msgid "Construction in progress"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_2901
#: model:account.account.template,name:l10n_cn.l10n_cn_2901
msgid "Deferred Tax Liability"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_2203
#: model:account.account.template,name:l10n_cn.l10n_cn_2203
msgid "Deposit Received"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1407
#: model:account.account.template,name:l10n_cn.l10n_cn_1407
msgid "Differences between purchasing and selling price"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1131
#: model:account.account.template,name:l10n_cn.l10n_cn_1131
msgid "Divident Receivable"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_2241
#: model:account.account.template,name:l10n_cn.l10n_cn_2241
msgid "Dividents payable"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1605
#: model:account.account.template,name:l10n_cn.l10n_cn_1605
msgid "Engineering materials"
msgstr ""

#. module: l10n_cn
#: model:ir.model.fields,field_description:l10n_cn.field_account_bank_statement_line__fapiao
#: model:ir.model.fields,field_description:l10n_cn.field_account_move__fapiao
#: model:ir.model.fields,field_description:l10n_cn.field_account_payment__fapiao
msgid "Fapiao Number"
msgstr ""

#. module: l10n_cn
#: code:addons/l10n_cn/models/account_move.py:0
#, python-format
msgid "Fapiao number is an 8-digit number. Please enter a correct one."
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_6603
#: model:account.account.template,name:l10n_cn.l10n_cn_6603
msgid "Financial Expenses"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1601
#: model:account.account.template,name:l10n_cn.l10n_cn_1601
msgid "Fixed assets"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1603
#: model:account.account.template,name:l10n_cn.l10n_cn_1603
msgid "Fixed assets depreciation reserves"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_6101
#: model:account.account.template,name:l10n_cn.l10n_cn_6101
msgid "Gains and Losses of fair value change"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1406
#: model:account.account.template,name:l10n_cn.l10n_cn_1406
msgid "Goods shipped in transit"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1711
#: model:account.account.template,name:l10n_cn.l10n_cn_1711
msgid "Goodwill"
msgstr "Goodwill"

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1501
#: model:account.account.template,name:l10n_cn.l10n_cn_1501
msgid "Held to maturity Investment"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1512
#: model:account.account.template,name:l10n_cn.l10n_cn_1512
msgid "Impairment provision for long-term equity investments"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_6801
#: model:account.account.template,name:l10n_cn.l10n_cn_6801
msgid "Income Tax Expense"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_6111
#: model:account.account.template,name:l10n_cn.l10n_cn_6111
msgid "Income from investment"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1701
#: model:account.account.template,name:l10n_cn.l10n_cn_1701
msgid "Intangible Assets"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1703
#: model:account.account.template,name:l10n_cn.l10n_cn_1703
msgid "Intangible Assets Depreciation Reserves"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1132
#: model:account.account.template,name:l10n_cn.l10n_cn_1132
msgid "Interest Receivable"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_2231
#: model:account.account.template,name:l10n_cn.l10n_cn_2231
msgid "Interest payable"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1471
#: model:account.account.template,name:l10n_cn.l10n_cn_1471
msgid "Inventory falling price reserves"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1521
#: model:account.account.template,name:l10n_cn.l10n_cn_1521
msgid "Investmental real estate"
msgstr ""

#. module: l10n_cn
#: model:ir.model,name:l10n_cn.model_account_move
msgid "Journal Entry"
msgstr "Buchungssatz"

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1606
#: model:account.account.template,name:l10n_cn.l10n_cn_1606
msgid "Liquidation of fixed assets"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_chart_china_small_business_liquidity_transfer
#: model:account.account.template,name:l10n_cn.l10n_chart_china_small_business_liquidity_transfer
msgid "Liquidity Transfer"
msgstr "Liquiditätstransfer"

#. module: l10n_cn
#: model_terms:ir.ui.view,arch_db:l10n_cn.external_layout_boxed
msgid "Logo"
msgstr "Logo"

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_2701
#: model:account.account.template,name:l10n_cn.l10n_cn_2701
msgid "Long Term payables"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1801
#: model:account.account.template,name:l10n_cn.l10n_cn_1801
msgid "Long-term amortized expenses"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1511
#: model:account.account.template,name:l10n_cn.l10n_cn_1511
msgid "Long-term equity investment"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1531
#: model:account.account.template,name:l10n_cn.l10n_cn_1531
msgid "Long-term receivables"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_6401
#: model:account.account.template,name:l10n_cn.l10n_cn_6401
msgid "Main Business Cost"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_6001
#: model:account.account.template,name:l10n_cn.l10n_cn_6001
msgid "Main Business Income"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_6602
#: model:account.account.template,name:l10n_cn.l10n_cn_6602
msgid "Management Expenses"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_5101
#: model:account.account.template,name:l10n_cn.l10n_cn_5101
msgid "Manufacturing Expenses"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1404
#: model:account.account.template,name:l10n_cn.l10n_cn_1404
msgid "Material Cost Variance"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1401
#: model:account.account.template,name:l10n_cn.l10n_cn_1401
msgid "Material Purchasing"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1402
#: model:account.account.template,name:l10n_cn.l10n_cn_1402
msgid "Materials in transit"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1405
#: model:account.account.template,name:l10n_cn.l10n_cn_1405
msgid "Merchandise Inventory"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_6301
#: model:account.account.template,name:l10n_cn.l10n_cn_6301
msgid "Non-operating Income"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_6711
#: model:account.account.template,name:l10n_cn.l10n_cn_6711
msgid "Non-operating expenses"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_6403
#: model:account.account.template,name:l10n_cn.l10n_cn_6403
msgid "Operating Taxes and Surcharges"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_6051
#: model:account.account.template,name:l10n_cn.l10n_cn_6051
msgid "Other Business Income"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_4003
#: model:account.account.template,name:l10n_cn.l10n_cn_4003
msgid "Other Comprehensive Income"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1012
#: model:account.account.template,name:l10n_cn.l10n_cn_1012
msgid "Other Monetary Funds"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_6402
#: model:account.account.template,name:l10n_cn.l10n_cn_6402
msgid "Other Operating Costs"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1221
#: model:account.account.template,name:l10n_cn.l10n_cn_1221
msgid "Other Receivable"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_2501
#: model:account.account.template,name:l10n_cn.l10n_cn_2501
msgid "Other payable"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_4001
#: model:account.account.template,name:l10n_cn.l10n_cn_4001
msgid "Paid in capital"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_2211
#: model:account.account.template,name:l10n_cn.l10n_cn_2211
msgid "Payroll payable"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_6901
#: model:account.account.template,name:l10n_cn.l10n_cn_6901
msgid "Prior year income adjustment"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_5001
#: model:account.account.template,name:l10n_cn.l10n_cn_5001
msgid "Production Costs"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_4104
#: model:account.account.template,name:l10n_cn.l10n_cn_4104
msgid "Profit distribution"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_4103
#: model:account.account.template,name:l10n_cn.l10n_cn_4103
msgid "Profit for the year"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_2801
#: model:account.account.template,name:l10n_cn.l10n_cn_2801
msgid "Projected liabilities"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1502
#: model:account.account.template,name:l10n_cn.l10n_cn_1502
msgid "Provision for impairment of investments held to maturity"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_5301
#: model:account.account.template,name:l10n_cn.l10n_cn_5301
msgid "R & D expenditure"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1403
#: model:account.account.template,name:l10n_cn.l10n_cn_1403
msgid "Raw Material"
msgstr "Rohmaterial"

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_6601
#: model:account.account.template,name:l10n_cn.l10n_cn_6601
msgid "Selling Expenses"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_5201
#: model:account.account.template,name:l10n_cn.l10n_cn_5201
msgid "Service Cost"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_2001
#: model:account.account.template,name:l10n_cn.l10n_cn_2001
msgid "Short-term borrowing"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_4101
#: model:account.account.template,name:l10n_cn.l10n_cn_4101
msgid "Surplus Reserve"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_2221
#: model:account.account.template,name:l10n_cn.l10n_cn_2221
msgid "Tax payable"
msgstr ""

#. module: l10n_cn
#: model:account.account,name:l10n_cn.3_l10n_cn_1101
#: model:account.account.template,name:l10n_cn.l10n_cn_1101
msgid "Transactional Financial Assets"
msgstr ""

#. module: l10n_cn
#: model:account.tax.group,name:l10n_cn.l10n_cn_tax_group_vat_13
msgid "VAT 13%"
msgstr ""

#. module: l10n_cn
#: model:account.tax.group,name:l10n_cn.l10n_cn_tax_group_vat_6
msgid "VAT 6%"
msgstr ""

#. module: l10n_cn
#: model:account.tax.group,name:l10n_cn.l10n_cn_tax_group_vat_9
msgid "VAT 9%"
msgstr ""

#. module: l10n_cn
#: model:ir.actions.report,name:l10n_cn.account_voucher_cn
msgid "Voucher"
msgstr "Zahlungsbeleg"

#. module: l10n_cn
#: model:account.chart.template,name:l10n_cn.l10n_chart_china_small_business
msgid "小企业会计科目表（财会[2011]17号《小企业会计准则》）"
msgstr "小企业会计科目表（财会[2011]17号《小企业会计准则》）"

#. module: l10n_cn
#: model:account.tax,description:l10n_cn.3_l10n_cn_purchase_excluded_13
#: model:account.tax,description:l10n_cn.3_l10n_cn_sales_excluded_13
#: model:account.tax,name:l10n_cn.3_l10n_cn_purchase_excluded_13
#: model:account.tax,name:l10n_cn.3_l10n_cn_sales_excluded_13
#: model:account.tax.template,description:l10n_cn.l10n_cn_purchase_excluded_13
#: model:account.tax.template,description:l10n_cn.l10n_cn_sales_excluded_13
#: model:account.tax.template,name:l10n_cn.l10n_cn_purchase_excluded_13
#: model:account.tax.template,name:l10n_cn.l10n_cn_sales_excluded_13
msgid "税收13%"
msgstr ""

#. module: l10n_cn
#: model:account.tax,description:l10n_cn.3_l10n_cn_sales_included_13
#: model:account.tax.template,description:l10n_cn.l10n_cn_sales_included_13
msgid "税收13％"
msgstr ""

#. module: l10n_cn
#: model:account.tax,name:l10n_cn.3_l10n_cn_sales_included_13
#: model:account.tax.template,name:l10n_cn.l10n_cn_sales_included_13
msgid "税收13％（含)"
msgstr ""

#. module: l10n_cn
#: model:account.tax,name:l10n_cn.3_l10n_cn_purchase_excluded_6
#: model:account.tax,name:l10n_cn.3_l10n_cn_sales_excluded_6
#: model:account.tax.template,name:l10n_cn.l10n_cn_purchase_excluded_6
#: model:account.tax.template,name:l10n_cn.l10n_cn_sales_excluded_6
msgid "税收6%"
msgstr ""

#. module: l10n_cn
#: model:account.tax,description:l10n_cn.3_l10n_cn_purchase_excluded_6
#: model:account.tax,description:l10n_cn.3_l10n_cn_sales_excluded_6
#: model:account.tax,description:l10n_cn.3_l10n_cn_sales_included_6
#: model:account.tax.template,description:l10n_cn.l10n_cn_purchase_excluded_6
#: model:account.tax.template,description:l10n_cn.l10n_cn_sales_excluded_6
#: model:account.tax.template,description:l10n_cn.l10n_cn_sales_included_6
msgid "税收6％"
msgstr ""

#. module: l10n_cn
#: model:account.tax,name:l10n_cn.3_l10n_cn_sales_included_6
#: model:account.tax.template,name:l10n_cn.l10n_cn_sales_included_6
msgid "税收6％（含)"
msgstr ""

#. module: l10n_cn
#: model:account.tax,description:l10n_cn.3_l10n_cn_purchase_excluded_9
#: model:account.tax,description:l10n_cn.3_l10n_cn_sales_excluded_9
#: model:account.tax,name:l10n_cn.3_l10n_cn_purchase_excluded_9
#: model:account.tax,name:l10n_cn.3_l10n_cn_sales_excluded_9
#: model:account.tax.template,description:l10n_cn.l10n_cn_purchase_excluded_9
#: model:account.tax.template,description:l10n_cn.l10n_cn_sales_excluded_9
#: model:account.tax.template,name:l10n_cn.l10n_cn_purchase_excluded_9
#: model:account.tax.template,name:l10n_cn.l10n_cn_sales_excluded_9
msgid "税收9%"
msgstr ""

#. module: l10n_cn
#: model:account.tax,description:l10n_cn.3_l10n_cn_sales_included_9
#: model:account.tax.template,description:l10n_cn.l10n_cn_sales_included_9
msgid "税收9％"
msgstr ""

#. module: l10n_cn
#: model:account.tax,name:l10n_cn.3_l10n_cn_sales_included_9
#: model:account.tax.template,name:l10n_cn.l10n_cn_sales_included_9
msgid "税收9％（含)"
msgstr ""
