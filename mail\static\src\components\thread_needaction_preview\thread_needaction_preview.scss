// ------------------------------------------------------------------
// Layout
// ------------------------------------------------------------------

.o_ThreadNeedactionPreview {
    @include o-mail-notification-list-item-layout();

    &:hover .o_ThreadNeedactionPreview_markAsRead {
        opacity: 1;
    }
}

.o_ThreadNeedactionPreview_content {
    @include o-mail-notification-list-item-content-layout();
}

.o_ThreadNeedactionPreview_core {
    @include o-mail-notification-list-item-core-layout();
}

.o_ThreadNeedactionPreview_coreItem {
    @include o-mail-notification-list-item-core-item-layout();
}

.o_ThreadNeedactionPreview_counter {
    @include o-mail-notification-list-item-counter-layout();
}

.o_ThreadNeedactionPreview_date {
    @include o-mail-notification-list-item-date-layout();
}

.o_ThreadNeedactionPreview_header {
    @include o-mail-notification-list-item-header-layout();
}

.o_ThreadNeedactionPreview_image {
    @include o-mail-notification-list-item-image-layout();
}

.o_ThreadNeedactionPreview_imageContainer {
    @include o-mail-notification-list-item-image-container-layout();
}

.o_ThreadNeedactionPreview_inlineText {
    @include o-mail-notification-list-item-inline-text-layout();
}

.o_ThreadNeedactionPreview_markAsRead {
    @include o-mail-notification-list-item-mark-as-read-layout();
}

.o_ThreadNeedactionPreview_name {
    @include o-mail-notification-list-item-name-layout();
}

.o_ThreadNeedactionPreview_partnerImStatusIcon {
    @include o-mail-notification-list-item-partner-im-status-icon-layout();
}

.o_ThreadNeedactionPreview_sidebar {
    @include o-mail-notification-list-item-sidebar-layout();
}

// ------------------------------------------------------------------
// Style
// ------------------------------------------------------------------

.o_ThreadNeedactionPreview {
    @include o-mail-notification-list-item-style();
    background-color: rgba($o-brand-primary, 0.1);

    &:hover {
        background-color: rgba($o-brand-primary, 0.2);

        .o_ThreadNeedactionPreview_partnerImStatusIcon {
            @include o-mail-notification-list-item-hover-partner-im-status-icon-style();
        }
    }
}

.o_ThreadNeedactionPreview_core {
    @include o-mail-notification-list-item-core-style();
}

.o_ThreadNeedactionPreview_counter {
    @include o-mail-notification-list-item-bold-style();
}

.o_ThreadNeedactionPreview_date {
    @include o-mail-notification-list-item-date-style();
}

.o_ThreadNeedactionPreview_image {
    @include o-mail-notification-list-item-image-style();
}

.o_ThreadNeedactionPreview_markAsRead {
    @include o-mail-notification-list-item-mark-as-read-style();
}

.o_ThreadNeedactionPreview_name {
    @include o-mail-notification-list-item-bold-style();
}

.o_ThreadNeedactionPreview_partnerImStatusIcon {
    @include o-mail-notification-list-item-partner-im-status-icon-style();
}
