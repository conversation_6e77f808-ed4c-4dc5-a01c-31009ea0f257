# -*- coding: utf-8 -*-
from odoo import tools, api, fields, models
from datetime import datetime, timedelta, date
from odoo.exceptions import ValidationError
from dateutil.relativedelta import relativedelta

class HrEmployeeOverTime(models.Model):
    _name = "hr.masarat.overtime"

    _inherit = ['mail.thread', 'mail.activity.mixin']

    _rec_name = 'overtime_name'

    overtime_name = fields.Char(compute='get_overtime_name', store=True)

    state = fields.Selection(selection=[('draft', 'Draft'),
                                        ('manager_approval', 'Manager Approval'),
                                        ('manager_refused', 'Manager Refused'),
                                        ('hr_approval', 'HR Approval'),
                                        ('hr_refused', 'HR Refused')], default='draft', string="State")

    request_date = fields.Date(string="Date of Request", readonly=True, default=lambda self: fields.Date.to_string(date.today()))
    employee_id = fields.Many2one('hr.employee', string="Employee",store=True)
    manager_id = fields.Many2one('hr.employee', readonly=True, related='employee_id.parent_id', string="Manager",store=True)
    overtime_line_ids = fields.One2many('hr.masarat.overtime.line','overtime_id', ondelete='cascade')

    # overtime_type = fields.Selection(selection=[('holidays', 'Fridays & Holidays'),('saturday', 'Overtime or Saturdays')], string="Overtime type", required=True)
    # start_at = fields.Datetime(required=True, string="Overtime Start at")
    # end_at = fields.Datetime(required=True, string="Overtime End at")
    overtime_total_hours = fields.Float(string='Total Overtime',compute='compute_overtime',store=True)
    Note = fields.Text(string="Details and Reasons for Overtime")

    is_manager = fields.Char(compute='call_with_sudo_is_manager')
    is_hr_group = fields.Char(compute='call_with_sudo_is_hr_group')

    def get_if_hr_group(self):
        hr_group = self.env.user.has_group('hr_approvales_masarat.group_hr_approvales_masarat')
        for rec in self:
            if hr_group:
                rec.is_hr_group = 'yes'
            else:
                rec.is_hr_group = 'no'

    def compute_button_visible(self):
        for rec in self:
            if rec.manager_id.user_id.id == self._uid:
                rec.is_manager = '1'
            else:
                rec.is_manager = '0'

    @api.model
    def call_with_sudo_is_manager(self):
        self.sudo().compute_button_visible()

    @api.depends('is_hr_group')
    def call_with_sudo_is_hr_group(self):
        self.sudo().get_if_hr_group()


    @api.depends('employee_id','request_date')
    def get_overtime_name(self):
        for elem in self:
            elem.overtime_name = False
            if elem.employee_id and elem.request_date:
                elem.overtime_name = elem.employee_id.name+'-Overtime Request-'+str(elem.request_date)[:10]


    @api.depends('overtime_line_ids')
    def compute_overtime(self):
        for elem in self:
            elem.overtime_total_hours = 0.0
            for ele in elem.overtime_line_ids:
                elem.overtime_total_hours+=ele.overtime_hours

    @api.constrains('overtime_line_ids')
    def overtime_date_constrains(self):
        hr_group = self.env.user.has_group('hr_approvales_masarat.group_hr_approvales_masarat')
        for elem in self.overtime_line_ids:
            date_gap = (datetime.now().date() - elem.overtime_date).days
            if ((date_gap > 4) or (date_gap < 1)) and not self.is_manager and (not hr_group):
                raise ValidationError("Overtime Date "+str(elem.overtime_date)+" is out of allowed range!")
            if ((date_gap > 4) or (date_gap < 1)) and (not hr_group):
                raise ValidationError("Overtime Date "+str(elem.overtime_date)+" is out of allowed range!")
            other_dates = self.env['hr.masarat.overtime.line'].search_count([('overtime_date','=',elem.overtime_date),('overtime_type','=',elem.overtime_type),('employee_id','=',elem.employee_id.id)])
            if other_dates > 1:
                raise ValidationError("Overtime "+str(elem.overtime_type)+" Date " + str(elem.overtime_date) + " is already requested!")

    # @api.depends('start_at','end_at')
    # def compute_overtime(self):
    #     for elem in self:
    #         elem.overtime_total_hours = False
    #         if elem.start_at and elem.end_at:
    #             if elem.end_at < elem.start_at:
    #                 raise ValidationError('End Datetime Should be Greater than Start Datetime!')
    #             string=str(elem.end_at-elem.start_at).split(':')
    #             elem.overtime_total_hours = str(string[0])+':'+str(string[1])+':'+str(string[2])

    def make_cancel_approval(self):
        self.state = 'draft'
    def make_manager_approval(self):
        self.state = 'manager_approval'
    def make_manager_refused(self):
        self.state = 'manager_refused'
    def make_hr_approval(self):
        self.state = 'hr_approval'
    def make_hr_refused(self):
        self.state = 'hr_refused'

    @api.model
    def default_get(self, fields):
        res = super(HrEmployeeOverTime, self).default_get(fields)
        user_id = self._context.get('uid')
        employee_id = self.env['hr.employee'].search([('user_id', '=', user_id)])
        res.update({'employee_id': employee_id.id})
        ## Check For Hr Group
        hr_group = self.env.user.has_group('hr_approvales_masarat.group_hr_approvales_masarat')
        if hr_group:
            res['is_hr_group'] = 'yes'
        else:
            res['is_hr_group'] = 'no'
        ########################
        return res

    def unlink(self):
        for elem in self:
            if elem.state != 'draft':
                raise ValidationError('You cannot delete a Overtime request which is not in draft state')
            return super(HrEmployeeOverTime, self).unlink()

    # def action_send_notification_to_maneger(self):
    #     template_id = self.env.ref('hr_approvales_masarat.overtime_approval_template').id
    #     self.env['mail.template'].browse(template_id).send_mail(self.id, force_send=True)
    #
    # @api.model
    # def create(self, vals_list):
    #     obj = super(HrEmployeeOverTime, self).create(vals_list)
    #     self.sudo().action_send_notification_to_maneger()
    #     return obj


    def action_send_notification_to_maneger(self, employee_id, recode_id):
        employee = self.env['hr.employee'].search([('id', '=', employee_id)])
        email_to = employee.parent_id.work_email
        email_from = employee.work_email
        web_base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        web_base_url += '/web#id=%d&view_type=form&model=%s' % (recode_id, self._name)
        body = """
        <div dir="rtl">
            <p><font style="font-size: 14px;">Your Employee """ + employee.name + """, requested overtime approval, </font></p>
            <p><font style="font-size: 14px;">Please login to Odoo in order to proceed.</font></p>
            <a href="%s">Request Link</a>
        </div>""" % (web_base_url)
        template_id = self.env['mail.mail'].create({
            'subject': 'طلب إضافي',
            'email_from': email_from,
            'email_to': email_to,
            'body_html': body})
        template_id.send()

    @api.model
    def create(self, vals_list):
        obj = super(HrEmployeeOverTime, self).create(vals_list)
        recode_id = obj.id
        employee_id = obj.employee_id.id
        self.sudo().action_send_notification_to_maneger(employee_id, recode_id)
        return obj



class HrEmployeeOverTimeLine(models.Model):
    _name = "hr.masarat.overtime.line"

    overtime_id = fields.Many2one('hr.masarat.overtime', ondelete='cascade')
    employee_id = fields.Many2one('hr.employee',related='overtime_id.employee_id')

    overtime_type = fields.Selection(
        selection=[('holidays', 'العطلة /الجمعة من العمل'), ('at_work', 'من السبت الى الخميس من العمل'), ('at_home', 'العمل من المنزل في جميع الأيام والعطلات')], string="Overtime type",
        required=True)
    # overtime_location = fields.Selection(
    #     selection=[('company', 'Company'), ('home', 'Home')], string="Overtime location",
    #     required=True)
    state = fields.Selection(selection=[('draft', 'Draft'),
                                        ('manager_approval', 'Manager Approval'),
                                        ('manager_refused', 'Manager Refused'),
                                        ('hr_approval', 'HR Approval'),
                                        ('hr_refused', 'HR Refused')], related='overtime_id.state')

    overtime_date = fields.Date(string='Date',compute='compute_overtime',store=True)
    start_hour = fields.Datetime(string='Start Hour',required=True)
    end_hour = fields.Datetime(string='End Hour',required=True)
    overtime_hours = fields.Float(string='Overtime Total Hours',default=0.0,compute='compute_overtime', required=True,store=True)

    description = fields.Text(string='Description')

    @api.depends('start_hour','end_hour')
    def compute_overtime(self):
        for elem in self:
            elem.overtime_hours = 0.0
            if elem.start_hour and elem.end_hour:
                elem.overtime_hours = (elem.end_hour - elem.start_hour).total_seconds()/float(60*60)
                elem.overtime_date = datetime.strptime(str(elem.start_hour)[:10], "%Y-%m-%d")

    @api.constrains('end_hour')
    def ensure_same_date(self):
        for elem in self:
            if elem.end_hour and elem.start_hour:
                if str(elem.end_hour.date()) != str(elem.start_hour.date()):
                    raise ValidationError('End Hour and Start Hour should be at the same Day Date')
                if str(elem.end_hour) < str(elem.start_hour):
                    raise ValidationError('End Hour Should be greater than Start Hour')


class HrPayslipX(models.Model):
    _inherit = 'hr.payslip'

    over_time_hours_at_home = fields.Float(help='Total Computed OverTime work hours',string='من السبت الى الخميس من المنزل', compute='_get_total_overtime_hours',store=True)
    over_time_hours_at_work = fields.Float(help='Total Computed OverTime work hours',string='من السبت الى الخميس من العمل', compute='_get_total_overtime_hours',store=True)
    over_time_hours_at_holiday = fields.Float(help='Total Computed OverTime work hours', string='العطلة /الجمعة',compute='_get_total_overtime_hours', store=True)

    work_hour_day = fields.Float(related='contract_id.resource_calendar_id.hours_per_day', string='Work Hour Day')

    @api.depends('employee_id', 'date_to', 'date_from')
    def _get_total_overtime_hours(self):
        for payslip in self:
            payslip.over_time_hours_at_home = 0
            payslip.over_time_hours_at_work = 0
            payslip.over_time_hours_at_holiday = 0
            if payslip.date_from and payslip.date_to:
                fmt = '%Y-%m-%d'
                pre_date = str((datetime.strptime(str(payslip.date_from), fmt) - relativedelta(months=1)).strftime(fmt))[:8] + '01'  ## to make sure there is no requists un collected from previus month
                if pre_date:
                    attendance_date_from = (datetime.strptime(pre_date, fmt)).date()
                    attendance_date_to = (datetime.strptime(pre_date, fmt).date() + relativedelta(months=1, day=1,days=-1))  ## to make sure there is no requists un collected from previus month
                    overtime_lines = self.env['hr.masarat.overtime.line'].search([('employee_id','=',payslip.employee_id.id),('overtime_date','>=',attendance_date_from),('overtime_date','<=',attendance_date_to),('state','=','hr_approval')])
                    for line in overtime_lines:
                        if line.overtime_type == 'at_home':
                            payslip.over_time_hours_at_home += line.overtime_hours
                        elif line.overtime_type == 'at_work':
                            payslip.over_time_hours_at_work += line.overtime_hours
                        elif line.overtime_type == 'holidays':
                            payslip.over_time_hours_at_holiday += line.overtime_hours



    # @api.depends('employee_id', 'date_to', 'date_from')
    # def _get_total_overtime_hours(self):
    #     for payslip in self:
    #         payslip.over_time_hours_at_home = 0
    #         payslip.over_time_hours_at_work = 0
    #         payslip.over_time_hours_at_holiday = 0
    #         if payslip.date_from and payslip.date_to:
    #             fmt = '%Y-%m-%d'
    #             pre_date = str((datetime.strptime(str(payslip.date_from), fmt) - relativedelta(months=1)).strftime(fmt))[:8] + '01'  ## to make sure there is no requists un collected from previus month
    #             if pre_date:
    #                 attendance_date_from = (datetime.strptime(pre_date, fmt)).date()
    #                 attendance_date_to = (datetime.strptime(pre_date, fmt).date() + relativedelta(months=2, day=1,days=-1))  ## to make sure there is no requists un collected from previus month
    #                 over_times_requists = self.env['hr.masarat.overtime'].search(
    #                     [('employee_id', '=', payslip.employee_id.id), ('state', '=', 'hr_approval'),
    #                      ('request_date', '>=', attendance_date_from), ('request_date', '<', attendance_date_to)])
    #                 attendance_date_to = (datetime.strptime(pre_date, fmt).date() + relativedelta(months=1, day=1, days=-1))  ## geting previus month only
    #                 for elem in over_times_requists:
    #                     # print('requist', elem.request_date)
    #                     for each_requists in elem.overtime_line_ids:
    #                         if (each_requists.overtime_date >= attendance_date_from) and (each_requists.overtime_date < attendance_date_to):
    #                             # print('intended month',each_requists.overtime_date)
    #                             if each_requists.overtime_type == 'at_home':
    #                                 payslip.over_time_hours_at_home += each_requists.overtime_hours
    #                             elif each_requists.overtime_type == 'at_work':
    #                                 payslip.over_time_hours_at_work += (each_requists.overtime_hours)
    #                             elif each_requists.overtime_type == 'holidays':
    #                                 payslip.over_time_hours_at_holiday += (each_requists.overtime_hours)
    #                         else:
    #                             pass
