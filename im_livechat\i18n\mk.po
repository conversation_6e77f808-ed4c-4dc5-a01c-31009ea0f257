# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * im_livechat
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-11-30 13:12+0000\n"
"PO-Revision-Date: 2017-11-30 13:12+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Macedonian (https://www.transifex.com/odoo/teams/41243/mk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mk\n"
"Plural-Forms: nplurals=2; plural=(n % 10 == 1 && n % 100 != 11) ? 0 : 1;\n"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator_nbr_channel
msgid "# of Sessions"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel_nbr_speaker
msgid "# of speakers"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rating_percentage_satisfaction
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "% Happy"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule_action
msgid ""
"* 'Display the button' displays the chat button on the pages.\n"
"* 'Auto popup' displays the button and automatically open the conversation pane.\n"
"* 'Hide the button' hides the chat button on the pages."
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"<span>Define rules for your live support channel. You can apply an action "
"(disable the support, automatically open your support, or just make the "
"button available) for the given URL, and per country.<br/>To identify the "
"country, GeoIP must be installed on your server, otherwise, the countries of"
" the rule will not be taken into account.</span>"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule_action
msgid "Action"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel_anonymous_name
msgid "Anonymous Name"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_are_you_inside
msgid "Are you inside the matrix?"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/js/im_livechat.js:48
#, python-format
msgid "Ask something ..."
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "Attendees"
msgstr ""

#. module: im_livechat
#: selection:im_livechat.channel.rule,action:0
msgid "Auto popup"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule_auto_popup_timer
msgid "Auto popup timer"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel_duration
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator_duration
msgid "Average duration"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel_nbr_message
msgid "Average message"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator_time_to_answer
msgid "Average time to give the first answer to the visitor"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/xml/im_livechat.xml:13
#, python-format
msgid "Bad"
msgstr ""

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_canned_response_action
#: model:ir.ui.menu,name:im_livechat.canned_responses
msgid "Canned Responses"
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_canned_response_action
msgid ""
"Canned responses allow you to insert prewritten responses in\n"
"                your messages by typing <i>:shortcut</i>. The shortcut is\n"
"                replaced directly in your message, so that you can still edit\n"
"                it before sending."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule_channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel_livechat_channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator_livechat_channel_id
#: model:ir.model.fields,field_description:im_livechat.field_mail_channel_livechat_channel_id
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Channel"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel_channel_name
msgid "Channel Name"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "Channel Rule"
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_channel_rule
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Channel Rules"
msgstr ""

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.support_channels
msgid "Channels"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_input_placeholder
msgid "Chat Input Placeholder"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/js/im_livechat.js:50
#, python-format
msgid "Chat with one of our collaborators"
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_canned_response_action
msgid "Click to create a new canned response."
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_channel_action
msgid "Click to define a new website live chat channel."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel_technical_name
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Code"
msgstr ""

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.livechat_config
msgid "Configuration"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel_channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator_channel_id
msgid "Conversation"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/js/copy_clipboard.js:32
#, python-format
msgid "Copied !"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/xml/copy_clipboard.xml:6
#: code:addons/im_livechat/static/src/xml/copy_clipboard.xml:12
#, python-format
msgid "Copy Text"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Copy and paste this code into your website, within the &lt;head&gt; tag:"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule_country_ids
msgid "Country"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_create_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule_create_uid
msgid "Created by"
msgstr "Креирано од"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_create_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule_create_date
msgid "Created on"
msgstr "Креирано на"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Creation date (day)"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Creation date (hour)"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Creation date (month)"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Creation date (week)"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Creation date (year)"
msgstr ""

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.rating_rating_action_livechat_report
#: model:ir.ui.menu,name:im_livechat.rating_rating_menu_livechat
msgid "Customer Ratings"
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.rating_rating_action_livechat_report
msgid "Customer ratings on livechat session."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_button_text
msgid "Default text displayed on the Livechat Support Button"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule_auto_popup_timer
msgid ""
"Delay (in seconds) to automatically open the conversation window. Note: the "
"selected action must be 'Auto popup' otherwise this parameter will not be "
"taken into account."
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/xml/im_livechat.xml:7
#, python-format
msgid "Did we correctly answer your question ?"
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_mail_channel
msgid "Discussion channel"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule_display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel_display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator_display_name
msgid "Display Name"
msgstr "Прикажи име"

#. module: im_livechat
#: selection:im_livechat.channel.rule,action:0
msgid "Display the button"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel_duration
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator_duration
msgid "Duration of the conversation (in seconds)"
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_channel_action
msgid ""
"Each channel has it's own URL that you can send by email to\n"
"                your customers in order to start chatting with you."
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/xml/im_livechat.xml:19
#, python-format
msgid "Explain your note"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"For website built with Odoo CMS, please install the website_livechat module."
" Then go to Website Admin &gt; Configuration &gt; Settings and select the "
"Website Live Chat Channel you want to add on your website."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule_sequence
msgid ""
"Given the order to find a matching rule. If 2 rules are matching for the "
"given url/country, the one with the lowest sequence will be chosen."
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/xml/im_livechat.xml:11
#, python-format
msgid "Good"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Group By..."
msgstr ""

#. module: im_livechat
#: selection:im_livechat.channel.rule,action:0
msgid "Hide the button"
msgstr ""

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.mail_channel_action
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "History"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel_start_date_hour
msgid "Hour of start Date of session"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/js/im_livechat.js:51
#, python-format
msgid "How may I help you?"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "How to use the Website Live Chat widget?"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/xml/im_livechat.xml:15
#, python-format
msgid "I don't want to rate this conversation"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator_id
msgid "ID"
msgstr "ID"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_image
msgid "Image"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Join"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Join Channel"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Last 24h"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "Last 7 days"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel___last_update
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule___last_update
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel___last_update
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator___last_update
msgid "Last Modified on"
msgstr "Последна промена на"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule_write_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_write_uid
msgid "Last Updated by"
msgstr "Последно ажурирање од"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule_write_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_write_date
msgid "Last Updated on"
msgstr "Последно ажурирање на"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Last Week"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Leave"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Leave Channel"
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_mail_channel_partner
msgid "Listeners of a Channel"
msgstr ""

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.menu_livechat_root
msgid "Live Chat"
msgstr ""

#. module: im_livechat
#: model:ir.module.category,name:im_livechat.module_category_im_livechat
msgid "Live Support"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/xml/im_livechat_backend.xml:9
#: code:addons/im_livechat/static/src/xml/im_livechat_backend.xml:21
#, python-format
msgid "Livechat"
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_channel
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "Livechat Channel"
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_channel_action
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_operator_action
msgid ""
"Livechat Support Channel Statistics allows you to easily check and analyse "
"your company livechat session performance. Extract information about the "
"missed sessions, the audiance, the duration of a session, etc."
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_report_channel
#: model:ir.model,name:im_livechat.model_im_livechat_report_operator
msgid "Livechat Support Report"
msgstr ""

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_channel_action
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_operator_action
msgid "Livechat Support Report Channel"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_pivot
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_pivot
msgid "Livechat Support Statistics"
msgstr ""

#. module: im_livechat
#: model:res.groups,name:im_livechat.im_livechat_group_manager
msgid "Manager"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule_sequence
msgid "Matching order"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_image_medium
msgid "Medium"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_image_medium
msgid ""
"Medium-sized photo of the group. It is automatically resized as a 128x128px "
"image, with aspect ratio preserved. Use this field in form views or some "
"kanban views."
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Missed sessions"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_name
msgid "Name"
msgstr ""

#. module: im_livechat
#: code:addons/im_livechat/models/mail_channel.py:125
#, python-format
msgid "No history found"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/js/im_livechat.js:150
#, python-format
msgid ""
"None of our collaborators seems to be available, please try again later."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_nbr_channel
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator_nbr_channel
msgid "Number of conversation"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel_nbr_speaker
msgid "Number of different speakers"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel_nbr_message
msgid "Number of message in the conversation"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/xml/im_livechat.xml:12
#, python-format
msgid "OK"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel_partner_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator_partner_id
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Operator"
msgstr ""

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat_operator
msgid "Operator Analysis"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_user_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Operators"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid ""
"Operators\n"
"                                            <br/>\n"
"                                            <i class=\"fa fa-comments\"/>"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Operators that do not show any activity In Odoo for more than 30 minutes "
"will be considered as disconnected."
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Options"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rating_percentage_satisfaction
msgid "Percentage of happy ratings over the past 7 days"
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_rating_rating
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "Rating"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/static/src/js/im_livechat.js:344
#, python-format
msgid "Rating: :rating_%d"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Rating: Bad"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Rating: Great"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Rating: Okay"
msgstr ""

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.rating_rating_action_view_livechat_rating
msgid "Ratings for livechat channel"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule_regex_url
msgid ""
"Regular expression specifying the web pages this rule will be applied on."
msgstr ""

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat
msgid "Report"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_tree
msgid "Rules"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_script_external
msgid "Script (external)"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_search
msgid "Search history"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Search report"
msgstr ""

#. module: im_livechat
#: code:addons/im_livechat/models/mail_channel.py:113
#, python-format
msgid "See 15 last visited pages"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_tree
msgid "Session Date"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.mail_channel_view_form
msgid "Session Form"
msgstr ""

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat_channel
msgid "Session Statistics"
msgstr ""

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.mail_channel_action_from_livechat_channel
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_channel_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Sessions"
msgstr ""

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.session_history
msgid "Sessions History"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_image_small
msgid ""
"Small-sized photo of the group. It is automatically resized as a 64x64px "
"image, with aspect ratio preserved. Use this field anywhere a small image is"
" required."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel_start_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator_start_date
msgid "Start Date of session"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel_start_date
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator_start_date
msgid "Start date of the conversation"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_button_text
msgid "Text of the Button"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule_channel_id
msgid "The channel of the rule"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_name
msgid "The name of the channel"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule_country_ids
msgid ""
"The rule will only be applied for these countries. Example: if you select "
"'Belgium' and 'United States' and that you set the action to 'Hide Button', "
"the chat button will be hidden on the specified URL from the visitors "
"located in these 2 countries. This feature requires GeoIP installed on your "
"server."
msgstr ""

#. module: im_livechat
#: model:res.groups,comment:im_livechat.im_livechat_group_manager
msgid "The user will be able to delete support channels."
msgstr ""

#. module: im_livechat
#: model:res.groups,comment:im_livechat.im_livechat_group_user
msgid "The user will be able to join support channels."
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.rating_rating_action_view_livechat_rating
msgid "There is no rating for this channel at the moment."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_image
msgid ""
"This field holds the image used as photo for the group, limited to "
"1024x1024px."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_default_message
msgid ""
"This is an automated 'welcome' message that your visitor will see when they "
"initiate a new conversation."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_image_small
msgid "Thumbnail"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator_time_to_answer
msgid "Time to answer"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Treated sessions"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule_regex_url
msgid "URL Regex"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_web_page
msgid ""
"URL to a static page where you client can discuss with the operator of the "
"channel."
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel_uuid
msgid "UUID"
msgstr ""

#. module: im_livechat
#: model:res.groups,name:im_livechat.im_livechat_group_user
msgid "User"
msgstr ""

#. module: im_livechat
#. openerp-web
#: code:addons/im_livechat/controllers/main.py:37
#: code:addons/im_livechat/static/src/js/im_livechat.js:49
#, python-format
msgid "Visitor"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_web_page
msgid "Web Page"
msgstr ""

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_channel_action
msgid "Website Live Chat Channels"
msgstr ""

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_default_message
msgid "Welcome Message"
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_channel_action
msgid ""
"You can create channels for each website on which you want\n"
"                to integrate the website live chat widget, allowing your website\n"
"                visitors to talk in real time with your operators."
msgstr ""

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.mail_channel_action
msgid ""
"Your chatter history is empty. Create a channel and start chatting to fill "
"up your history."
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "e.g. Hello, how may I help you?"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "e.g. YourWebsite.com"
msgstr ""

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_ir_autovacuum
msgid "ir.autovacuum"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "or copy this url and send it by email to your customers or suppliers:"
msgstr ""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "seconds"
msgstr ""
