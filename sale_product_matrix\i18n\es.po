# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_product_matrix
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: sale_product_matrix
#: model:ir.model.fields,field_description:sale_product_matrix.field_product_product__product_add_mode
#: model:ir.model.fields,field_description:sale_product_matrix.field_product_template__product_add_mode
msgid "Add product mode"
msgstr "Añadir modo de producto"

#. module: sale_product_matrix
#: model:ir.model.fields,help:sale_product_matrix.field_product_product__product_add_mode
#: model:ir.model.fields,help:sale_product_matrix.field_product_template__product_add_mode
msgid ""
"Configurator: choose attribute values to add the matching         product variant to the order.\n"
"Grid: add several variants at once from the grid of attribute values"
msgstr ""
"Configurador: seleccione valores de atributo para añadir la variante de producto correspondiente a la orden.\n"
"Cuadrícula: añade varias variantes a la vez desde la cuadrícula de valores de atributos."

#. module: sale_product_matrix
#: model:ir.model.fields,field_description:sale_product_matrix.field_sale_order__grid_product_tmpl_id
msgid "Grid Product Tmpl"
msgstr "Plantilla del Producto"

#. module: sale_product_matrix
#: model:ir.model.fields,field_description:sale_product_matrix.field_sale_order__grid_update
msgid "Grid Update"
msgstr "Actualización de la plantilla"

#. module: sale_product_matrix
#: model:ir.model.fields,help:sale_product_matrix.field_sale_order__report_grids
msgid ""
"If set, the matrix of the products configurable by matrix will be shown on "
"the report of the order."
msgstr ""
"Si está configurado, la matriz de los productos configurables por matriz se "
"mostrará en el informe del pedido."

#. module: sale_product_matrix
#: model:ir.model.fields,field_description:sale_product_matrix.field_sale_order__grid
msgid "Matrix local storage"
msgstr "Almacenamiento local de matriz"

#. module: sale_product_matrix
#: model:ir.model.fields.selection,name:sale_product_matrix.selection__product_template__product_add_mode__matrix
msgid "Order Grid Entry"
msgstr "Cuadrícula de entrada"

#. module: sale_product_matrix
#: model:ir.model.fields,field_description:sale_product_matrix.field_sale_order__report_grids
msgid "Print Variant Grids"
msgstr "Imprimir cuadrículas de variantes"

#. module: sale_product_matrix
#: model:ir.model.fields.selection,name:sale_product_matrix.selection__product_template__product_add_mode__configurator
msgid "Product Configurator"
msgstr "Configurador de productos"

#. module: sale_product_matrix
#: model:ir.model,name:sale_product_matrix.model_product_template
msgid "Product Template"
msgstr "Plantilla de producto"

#. module: sale_product_matrix
#: model:ir.model,name:sale_product_matrix.model_sale_order
msgid "Sales Order"
msgstr "Pedido de venta"

#. module: sale_product_matrix
#: model_terms:ir.ui.view,arch_db:sale_product_matrix.product_template_grid_view_form
msgid "Sales Variant Selection"
msgstr "Selección de variantes de ventas"

#. module: sale_product_matrix
#: model:ir.model.fields,help:sale_product_matrix.field_sale_order__grid_product_tmpl_id
msgid "Technical field for product_matrix functionalities."
msgstr "Campo técnico de las funcionalidades de product_matrix."

#. module: sale_product_matrix
#: model:ir.model.fields,help:sale_product_matrix.field_sale_order__grid
msgid ""
"Technical local storage of grid. \n"
"If grid_update, will be loaded on the SO. \n"
"If not, represents the matrix to open."
msgstr ""
"Almacenamiento técnico local de la rejilla.\n"
"Si grid_update, se cargará en el SO.\n"
"Si no, representa la matriz a abrir."

#. module: sale_product_matrix
#: model:ir.model.fields,help:sale_product_matrix.field_sale_order__grid_update
msgid "Whether the grid field contains a new matrix to apply or not."
msgstr ""
"Si el campo de cuadrícula contiene una nueva matriz para aplicar o no."

#. module: sale_product_matrix
#: code:addons/sale_product_matrix/models/sale_order.py:0
#, python-format
msgid ""
"You cannot change the quantity of a product present in multiple sale lines."
msgstr ""
"No puede cambiar la cantidad de un producto presente en varias líneas de "
"venta."
