<?xml version="1.0" encoding="utf-8"?>
<odoo>

<template id="index" name="Our References">
    <t t-call="website.layout">
        <div id="wrap">
            <div class="oe_structure">
                <section>
                    <h1 class="text-center">
                        Our References
                    </h1><h2 class="text-center text-muted">
                        Trusted by millions worldwide
                    </h2>
                </section>
            </div>
            <div class="container">
                <div class="row">
                    <div class="col-lg-3 mb32" id="ref_left_column">
                    </div>
                    <div class="col-lg-8 offset-lg-1" id="ref_content">
                        <div class='d-flex m-2'>
                            <t t-call="website.pager">
                               <t t-set="classname" t-value="'float-left'"/>
                            </t>
                            <form action="" method="get" class="navbar-search ml-auto pagination form-inline">
                                <div class="form-group">
                                    <input type="text" name="search" class="search-query form-control"
                                        placeholder="Search" t-att-value="post.get('search', '')"/>
                                </div>
                            </form>
                        </div>

                        <div>

                    <p t-if="not partners">No result found</p>
                    <t t-foreach="partners" t-as="partner">
                        <div class="media mt-3">
                            <a t-attf-href="/customers/#{slug(partner)}"
                               t-field="partner.avatar_128"
                               class="d-block mr-3 text-center o_width_128"
                               t-options='{"widget": "image", "qweb_img_responsive": False, "class": "o_image_128_max"}'
                            ></a>
                            <div class="media-body" style="min-height: 64px;">
                                <a t-attf-href="/customers/#{slug(partner)}">
                                    <span t-field="partner.display_name"/>
                                </a>
                                <t t-if="partner.industry_id">
                                    <a class="badge badge-secondary" t-attf-href="/customers/#{ 'industry/%s/' % slug(partner.industry_id) }#{ current_country_id and 'country/%s' % slug(current_country) or '' }" t-esc="partner.industry_id.name"/>
                                </t>
                                <div t-field="partner.website_short_description"/>
                            </div>
                        </div>
                    </t>
                        </div>
                    </div>

                </div>
            </div>
            <div class="oe_structure"/>
        </div>
    </t>
</template>

<!-- Option: left column: World Map -->
<template id="opt_country" inherit_id="website_customer.index" customize_show="True" name="Show Map">
    <xpath expr="//div[@id='ref_left_column']" position="inside">
        <t t-if="google_maps_api_key">
            <!-- modal for large map -->
            <div role="dialog" class="modal fade customer_map_modal" tabindex="-1">
              <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <header class="modal-header">
                        <h4 class="modal-title">World Map</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">×</button>
                    </header>
                    <iframe t-attf-src="/google_map/?width=898&amp;height=485&amp;partner_ids=#{ google_map_partner_ids }&amp;partner_url=/customers/"
                    style="width:898px; height:485px; border:0; padding:0; margin:0;"></iframe>
                </div>
              </div>
            </div>
            <!-- modal end -->
            <h3>World Map<button class="btn btn-link" data-toggle="modal" data-target=".customer_map_modal"><span class="fa fa-external-link" role="img" aria-label="External link" title="External link"/></button></h3>
            <ul class="nav">
                <iframe t-attf-src="/google_map?width=260&amp;height=240&amp;partner_ids=#{ google_map_partner_ids }&amp;partner_url=/customers/"
                    style="width:260px; height:240px; border:0; padding:0; margin:0;" scrolling="no"></iframe>
            </ul>
        </t>
    </xpath>
</template>

<template id="opt_industry_list" inherit_id="website_customer.index" customize_show="True" name="Filter on Industry" priority="20">
    <xpath expr="//div[@id='ref_left_column']" position="inside">
        <h3>References by Industries</h3>
        <ul class="nav nav-pills flex-column mt16 mb32">
            <t t-foreach="industries" t-as="industry_dict">
                <t t-if="industry_dict['industry_id']">
                    <li class="nav-item">
                        <a t-attf-href="/customers/#{ industry_dict['industry_id'][0] and 'industry/%s/' % slug(industry_dict['industry_id']) or '' }#{ current_country_id and 'country/%s' % current_country_id or '' }#{ search_path }"
                           t-attf-class="nav-link#{industry_dict['industry_id'][0] == current_industry_id and ' active' or ''}">
                            <span class="badge badge-pill float-right" t-esc="industry_dict['industry_id_count'] or '0'"/>
                            <t t-esc="industry_dict['industry_id'][1]"/>
                        </a>
                    </li>
                </t>
            </t>
        </ul>
    </xpath>
</template>

<template id="opt_country_list" inherit_id="website_customer.index" customize_show="True" name="Filter on Countries" priority="30">
    <xpath expr="//div[@id='ref_left_column']" position="inside">
        <h3>References by Country</h3>
        <ul class="nav nav-pills flex-column mt16 mb32">
            <t t-foreach="countries" t-as="country_dict">
                <t t-if="country_dict['country_id']">
                    <li class="nav-item">
                        <a t-attf-href="/customers/#{ current_industry_id and 'industry/%s/' % slug(current_industry) or '' }#{ country_dict['country_id'][0] and 'country/%s' % slug(country_dict['country_id']) or '' }#{ search_path }"
                           t-attf-class="nav-link#{country_dict['country_id'][0] == current_country_id and ' active' or ''}">
                            <span class="badge badge-pill float-right" t-esc="country_dict['country_id_count'] or '0'"/>
                            <t t-esc="country_dict['country_id'][1]"/>
                        </a>
                    </li>
                </t>
            </t>
        </ul>
    </xpath>
</template>


<template id="opt_tag_list" inherit_id="website_customer.index" customize_show="True" name="Filter on Tags" priority="40">
    <xpath expr="//div[@id='ref_left_column']" position="inside">

        <h3 t-if="len(tags)">References by Tag</h3>
        <ul class="nav nav-pills flex-column mt16 mb32" t-if="len(tags)">
            <li class="nav-item"><a class="nav-link mr8" t-attf-href="/customers/#{ current_industry_id and 'industry/%s/' % slug(current_industry) or '' }#{ current_country_id and 'country/%s' % slug(current_country) or '' }">
                <span class="fa fa-1x fa-tags"/> All </a></li>
            <li t-foreach="tags" t-as="o_tag" class="nav-item">
                <a t-attf-class="nav-link badge badge-#{o_tag.classname}" t-esc="o_tag.name" t-att-style="tag and tag.id==o_tag.id and 'text-decoration: underline'"
                    t-attf-href="/customers/#{ current_industry_id and 'industry/%s/' % slug(current_industry) or '' }#{ current_country_id and 'country/%s' % slug(current_country) or '' }?tag_id=#{slug(o_tag)}"/>
            </li>
        </ul>
    </xpath>
</template>

<template id="contact_edit_options" inherit_id="website.user_navbar" name="Edit Customer Options">
    <xpath expr="//div[@id='edit-page-menu']" position="after">
        <t t-if="main_object._name == 'res.partner'" t-set="action" t-value="'contacts.action_contacts'"/>
    </xpath>
</template>

<template id="details" name="Customer Detail">
  <t t-call="website.layout">
    <div id="wrap">
        <div class="oe_structure" id="oe_structure_website_customer_details_1"/>
        <div class="container mt16">
            <div class="row">
                <div class="col-lg-5">
                    <ol t-if="not edit_page" class="breadcrumb">
                        <li class="breadcrumb-item"><a href="/customers">Our References</a></li>
                        <li class="breadcrumb-item active"><span t-field="partner.display_name"/></li>
                    </ol>
                </div>
                <t t-call="website_partner.partner_detail">
                    <t t-set="left_column">
                        <div id="left_column"><t t-call="website_customer.implemented_by_block"/></div>
                    </t>
                    <t t-set="right_column">
                        <div id="right_column"><t t-call="website_customer.references_block"/></div>
                    </t>
                </t>
            </div>
        </div>
        <div class="oe_structure" id="oe_structure_website_customer_details_2"/>
    </div>
  </t>
</template>

<template id="partner_details" inherit_id="website_partner.partner_page" name="Partner Detail Columns">
 <xpath expr="//t[@t-call='website_partner.partner_detail']" position="inside">
    <t t-set="left_column"><div id="left_column"><t t-call="website_customer.implemented_by_block"/></div></t>
    <t t-set="right_column"><div id="right_column"><t t-call="website_customer.references_block"/></div></t>
 </xpath>
</template>

<template id="partner_detail" inherit_id="website_partner.partner_detail" name="Partner Details">
    <xpath expr="//address" position="inside">
        <t t-if="partner.industry_id">
            <span class="badge badge-secondary"><t t-esc="partner.industry_id.name"/></span>
        </t>
    </xpath>
</template>

<template id="implemented_by_block" name="Partner Implemented By Block">
        <t t-if="partner.assigned_partner_id and partner.assigned_partner_id.website_published">
            <div class="card">
                <div class="card-header">
                    <h4>Implemented By</h4>
                </div>
                <div class="card-body text-center">
        <h4>
            <a t-attf-href="/partners/#{slug(partner.assigned_partner_id)}">
              <span t-field="partner.assigned_partner_id"/>
              <span class="small"> (<t t-esc="len([p for p in partner.assigned_partner_id.implemented_partner_ids if p.website_published])"/> reference(s))</span>
            </a>
        </h4>
        <div><a t-attf-href="/partners/#{slug(partner.assigned_partner_id)}"
                t-field="partner.assigned_partner_id.avatar_128"
                class="d-block"
                t-options='{"widget": "image", "qweb_img_responsive": False, "class": "o_image_128_max"}'
             />
        </div>
        <address class="text-left">
             <div t-field="partner.assigned_partner_id" t-options='{
                 "widget": "contact",
                 "fields": ["address", "website", "phone", "email"]
             }'/>
        </address>
                </div>
            </div>
        </t>
</template>

<template id="references_block" name="Partner References Block">
        <t t-if="any(p.website_published for p in partner.implemented_partner_ids)">
            <h3 id="references">References</h3>
            <div t-foreach="partner.implemented_partner_ids" t-as="reference" class="media mt-3">
              <t t-if="reference.website_published">
                <a t-attf-href="/customers/#{slug(reference)}">
                    <span t-field="reference.avatar_128" class="d-block mr-3 text-center o_width_128" t-options='{"widget": "image", "qweb_img_responsive": False, "class": "o_image_128_max"}'/>
                </a>
                <div class="media-body" style="min-height: 64px;">
                    <a t-attf-href="/customers/#{slug(reference)}">
                        <span t-field="reference.self"/>
                    </a>
                    <t t-if="reference.industry_id">
                        <span class="badge badge-secondary"><t t-esc="reference.industry_id.name"/></span>
                    </t>
                    <div t-field='reference.website_short_description'/>
                </div>
              </t>
            </div>
        </t>
</template>

<template id="references_block_href" inherit_id="website_crm_partner_assign.references_block" name="Partner References Block">
    <xpath expr="//div/span" position="replace">
        <a t-attf-href="/customers/#{slug(reference)}">$0</a>
    </xpath>
    <xpath expr="//div[hasclass('media-body')]/span" position="replace">
        <a t-attf-href="/customers/#{slug(reference)}">$0</a>
    </xpath>
</template>

</odoo>
