# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * membership
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Spanish (Ecuador) (https://www.transifex.com/odoo/teams/41243/es_EC/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_EC\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership_num_invoiced
msgid "# Invoiced"
msgstr ""

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership_num_paid
msgid "# Paid"
msgstr ""

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership_num_waiting
msgid "# Waiting"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_kanban
msgid "<i class=\"fa fa-clock-o\" aria-hidden=\"true\"/><strong> From: </strong>"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_kanban
msgid "<strong> To:</strong>"
msgstr ""

#. module: membership
#: model:ir.model.fields,help:membership.field_res_partner_associate_member
#: model:ir.model.fields,help:membership.field_res_users_associate_member
msgid ""
"A member with whom you want to associate your membership. It will consider "
"the membership state of the associated member."
msgstr ""

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line_account_invoice_line
msgid "Account Invoice line"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_form
msgid "Add a description..."
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "All Members"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "All non Members"
msgstr ""

#. module: membership
#: model:ir.model.fields,help:membership.field_membership_membership_line_member_price
msgid "Amount for the membership"
msgstr ""

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership_associate_member_id
#: model:ir.model.fields,field_description:membership.field_res_partner_associate_member
#: model:ir.model.fields,field_description:membership.field_res_users_associate_member
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Associate Member"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Associated Partner"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_partner_form
msgid "Buy Membership"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_membership_invoice_view
msgid "Cancel"
msgstr "Cancelar"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_res_partner_membership_cancel
#: model:ir.model.fields,field_description:membership.field_res_users_membership_cancel
msgid "Cancel Membership Date"
msgstr ""

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line_date_cancel
msgid "Cancel date"
msgstr ""

#. module: membership
#: selection:membership.membership_line,state:0
#: selection:report.membership,membership_state:0
#: selection:res.partner,membership_state:0
msgid "Cancelled Member"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_product_search_form_view
msgid "Category"
msgstr ""

#. module: membership
#: model:ir.model.fields,help:membership.field_product_product_membership
#: model:ir.model.fields,help:membership.field_product_template_membership
msgid "Check if the product is eligible for membership."
msgstr ""

#. module: membership
#: model_terms:ir.actions.act_window,help:membership.action_membership_members
msgid "Click to add a new Member"
msgstr ""

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line_company_id
#: model:ir.model.fields,field_description:membership.field_report_membership_company_id
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Company"
msgstr "Compañía"

#. module: membership
#: model:ir.ui.menu,name:membership.menu_marketing_config_association
msgid "Configuration"
msgstr ""

#. module: membership
#: model:ir.model,name:membership.model_res_partner
msgid "Contact"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_partner_tree
msgid "Contacts"
msgstr ""

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice_create_uid
#: model:ir.model.fields,field_description:membership.field_membership_membership_line_create_uid
msgid "Created by"
msgstr "Creado por:"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice_create_date
#: model:ir.model.fields,field_description:membership.field_membership_membership_line_create_date
msgid "Created on"
msgstr "Creado"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership_membership_state
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Current Membership State"
msgstr ""

#. module: membership
#: model:ir.model.fields,field_description:membership.field_res_partner_membership_state
#: model:ir.model.fields,field_description:membership.field_res_users_membership_state
msgid "Current Membership Status"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Customers"
msgstr ""

#. module: membership
#: model:ir.model.fields,help:membership.field_product_product_membership_date_from
#: model:ir.model.fields,help:membership.field_product_template_membership_date_from
#: model:ir.model.fields,help:membership.field_res_partner_membership_start
#: model:ir.model.fields,help:membership.field_res_users_membership_start
msgid "Date from which membership becomes active."
msgstr ""

#. module: membership
#: model:ir.model.fields,help:membership.field_membership_membership_line_date
msgid "Date on which member has joined the membership"
msgstr ""

#. module: membership
#: model:ir.model.fields,help:membership.field_res_partner_membership_cancel
#: model:ir.model.fields,help:membership.field_res_users_membership_cancel
msgid "Date on which membership has been cancelled"
msgstr ""

#. module: membership
#: model:ir.model.fields,help:membership.field_product_product_membership_date_to
#: model:ir.model.fields,help:membership.field_product_template_membership_date_to
#: model:ir.model.fields,help:membership.field_res_partner_membership_stop
#: model:ir.model.fields,help:membership.field_res_users_membership_stop
msgid "Date until which membership remains active."
msgstr ""

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice_display_name
#: model:ir.model.fields,field_description:membership.field_membership_membership_line_display_name
#: model:ir.model.fields,field_description:membership.field_report_membership_display_name
msgid "Display Name"
msgstr "Nombre a Mostrar"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership_tot_earned
msgid "Earned Amount"
msgstr ""

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership_date_to
msgid "End Date"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "End Membership Date"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "End Month"
msgstr ""

#. module: membership
#: model:ir.model.fields,help:membership.field_report_membership_date_to
msgid "End membership date"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Ending Month Of Membership"
msgstr ""

#. module: membership
#: sql_constraint:product.template:0
msgid "Error ! Ending Date cannot be set before Beginning Date."
msgstr ""

#. module: membership
#: code:addons/membership/models/partner.py:159
#, python-format
msgid "Error ! You cannot create recursive associated members."
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Forecast"
msgstr ""

#. module: membership
#: model:ir.model.fields,field_description:membership.field_res_partner_free_member
#: model:ir.model.fields,field_description:membership.field_res_users_free_member
#: selection:membership.membership_line,state:0
#: selection:report.membership,membership_state:0
#: selection:res.partner,membership_state:0
msgid "Free Member"
msgstr ""

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line_date_from
msgid "From"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_product_search_form_view
msgid "From Month"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Group By"
msgstr "Agrupar por"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_product_search_form_view
msgid "Group by..."
msgstr ""

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice_id
#: model:ir.model.fields,field_description:membership.field_membership_membership_line_id
#: model:ir.model.fields,field_description:membership.field_report_membership_id
msgid "ID"
msgstr "ID"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_product_search_form_view
msgid "Inactive"
msgstr ""

#. module: membership
#: model:ir.model,name:membership.model_account_invoice
#: model:ir.model.fields,field_description:membership.field_membership_membership_line_account_invoice_id
msgid "Invoice"
msgstr "Factura"

#. module: membership
#: model:ir.model,name:membership.model_account_invoice_line
msgid "Invoice Line"
msgstr "Línea de factura"

#. module: membership
#: model:ir.actions.act_window,name:membership.action_membership_invoice_view
#: model_terms:ir.ui.view,arch_db:membership.view_membership_invoice_view
msgid "Invoice Membership"
msgstr ""

#. module: membership
#: selection:membership.membership_line,state:0
#: selection:report.membership,membership_state:0
#: selection:res.partner,membership_state:0
msgid "Invoiced Member"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Invoiced/Paid/Free"
msgstr ""

#. module: membership
#: model:ir.model.fields,help:membership.field_res_partner_membership_state
#: model:ir.model.fields,help:membership.field_res_users_membership_state
msgid ""
"It indicates the membership state.\n"
"-Non Member: A partner who has not applied for any membership.\n"
"-Cancelled Member: A member who has cancelled his membership.\n"
"-Old Member: A member whose membership date has expired.\n"
"-Waiting Member: A member who has applied for the membership and whose invoice is going to be created.\n"
"-Invoiced Member: A member whose invoice has been created.\n"
"-Paying member: A member who has paid the membership fee."
msgstr ""

#. module: membership
#: model:ir.model.fields,help:membership.field_membership_membership_line_state
msgid ""
"It indicates the membership status.\n"
"-Non Member: A member who has not applied for any membership.\n"
"-Cancelled Member: A member who has cancelled his membership.\n"
"-Old Member: A member whose membership date has expired.\n"
"-Waiting Member: A member who has applied for the membership and whose invoice is going to be created.\n"
"-Invoiced Member: A member whose invoice has been created.\n"
"-Paid Member: A member who has paid the membership amount."
msgstr ""

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line_date
msgid "Join Date"
msgstr ""

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice___last_update
#: model:ir.model.fields,field_description:membership.field_membership_membership_line___last_update
#: model:ir.model.fields,field_description:membership.field_report_membership___last_update
msgid "Last Modified on"
msgstr "Fecha de modificación"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice_write_uid
#: model:ir.model.fields,field_description:membership.field_membership_membership_line_write_uid
msgid "Last Updated by"
msgstr "Ultima Actualización por"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice_write_date
#: model:ir.model.fields,field_description:membership.field_membership_membership_line_write_date
msgid "Last Updated on"
msgstr "Actualizado en"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership_partner_id
msgid "Member"
msgstr ""

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice_member_price
msgid "Member Price"
msgstr ""

#. module: membership
#: model:ir.actions.act_window,name:membership.action_membership_members
#: model:ir.ui.menu,name:membership.menu_association
#: model:ir.ui.menu,name:membership.menu_membership
#: model_terms:ir.ui.view,arch_db:membership.membership_members_tree
msgid "Members"
msgstr ""

#. module: membership
#: model:ir.actions.act_window,name:membership.action_report_membership_tree
msgid "Members Analysis"
msgstr ""

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_invoice_product_id
#: model:ir.model.fields,field_description:membership.field_membership_membership_line_membership_id
#: model:ir.model.fields,field_description:membership.field_product_product_membership
#: model:ir.model.fields,field_description:membership.field_product_template_membership
#: model:ir.model.fields,field_description:membership.field_res_partner_member_lines
#: model:ir.model.fields,field_description:membership.field_res_users_member_lines
#: model_terms:ir.ui.view,arch_db:membership.view_partner_form
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_graph1
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_pivot
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Membership"
msgstr ""

#. module: membership
#: model:ir.model.fields,field_description:membership.field_res_partner_membership_amount
#: model:ir.model.fields,field_description:membership.field_res_users_membership_amount
msgid "Membership Amount"
msgstr ""

#. module: membership
#: model:ir.model,name:membership.model_report_membership
msgid "Membership Analysis"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_form
msgid "Membership Duration"
msgstr ""

#. module: membership
#: model:ir.model.fields,field_description:membership.field_product_product_membership_date_to
#: model:ir.model.fields,field_description:membership.field_product_template_membership_date_to
#: model:ir.model.fields,field_description:membership.field_res_partner_membership_stop
#: model:ir.model.fields,field_description:membership.field_res_users_membership_stop
msgid "Membership End Date"
msgstr ""

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line_member_price
#: model_terms:ir.ui.view,arch_db:membership.membership_products_form
#: model_terms:ir.ui.view,arch_db:membership.membership_products_tree
msgid "Membership Fee"
msgstr ""

#. module: membership
#: model:ir.model,name:membership.model_membership_invoice
#: model_terms:ir.ui.view,arch_db:membership.view_membership_invoice_view
msgid "Membership Invoice"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Membership Partners"
msgstr ""

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership_membership_id
#: model_terms:ir.ui.view,arch_db:membership.membership_product_search_form_view
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Membership Product"
msgstr ""

#. module: membership
#: model:ir.actions.act_window,name:membership.action_membership_products
#: model_terms:ir.ui.view,arch_db:membership.membership_product_search_form_view
msgid "Membership Products"
msgstr ""

#. module: membership
#: model:ir.model.fields,field_description:membership.field_product_product_membership_date_from
#: model:ir.model.fields,field_description:membership.field_product_template_membership_date_from
#: model:ir.model.fields,field_description:membership.field_res_partner_membership_start
#: model:ir.model.fields,field_description:membership.field_res_users_membership_start
msgid "Membership Start Date"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Membership State"
msgstr ""

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line_state
msgid "Membership Status"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_form
#: model_terms:ir.ui.view,arch_db:membership.membership_products_tree
msgid "Membership products"
msgstr ""

#. module: membership
#: model:ir.actions.server,name:membership.ir_cron_update_membership_ir_actions_server
#: model:ir.cron,cron_name:membership.ir_cron_update_membership
#: model:ir.cron,name:membership.ir_cron_update_membership
msgid "Membership: update memberships"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_partner_form
msgid "Memberships"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Month"
msgstr ""

#. module: membership
#: selection:membership.membership_line,state:0
#: selection:report.membership,membership_state:0
#: selection:res.partner,membership_state:0
msgid "Non Member"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "None/Canceled/Old/Waiting"
msgstr ""

#. module: membership
#: model_terms:ir.actions.act_window,help:membership.action_membership_members
msgid ""
"Odoo helps you easily track all activities related to a member: \n"
"                 Current Membership Status, Discussions and History of Membership, etc."
msgstr ""

#. module: membership
#: selection:membership.membership_line,state:0
#: selection:report.membership,membership_state:0
#: selection:res.partner,membership_state:0
msgid "Old Member"
msgstr ""

#. module: membership
#: selection:membership.membership_line,state:0
#: selection:report.membership,membership_state:0
#: selection:res.partner,membership_state:0
msgid "Paid Member"
msgstr ""

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line_partner
msgid "Partner"
msgstr "Empresa"

#. module: membership
#: code:addons/membership/models/partner.py:181
#, python-format
msgid "Partner doesn't have an address to make the invoice."
msgstr ""

#. module: membership
#: code:addons/membership/models/partner.py:179
#, python-format
msgid "Partner is a free Member."
msgstr ""

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership_tot_pending
msgid "Pending Amount"
msgstr ""

#. module: membership
#: model:ir.model,name:membership.model_product_template
msgid "Product Template"
msgstr "Plantilla de producto"

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership_quantity
msgid "Quantity"
msgstr "Cantidad"

#. module: membership
#: model:ir.ui.menu,name:membership.menu_report_membership
msgid "Reporting"
msgstr "Informe"

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "Revenue Done"
msgstr ""

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership_user_id
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Salesperson"
msgstr "Comercial"

#. module: membership
#: model:ir.model.fields,help:membership.field_res_partner_free_member
#: model:ir.model.fields,help:membership.field_res_users_free_member
msgid "Select if you want to give free membership."
msgstr ""

#. module: membership
#: model:ir.model.fields,field_description:membership.field_report_membership_start_date
msgid "Start Date"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Start Month"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Starting Month Of Membership"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_form
msgid "Taxes"
msgstr "Impuestos"

#. module: membership
#: model:ir.model.fields,help:membership.field_res_partner_membership_amount
#: model:ir.model.fields,help:membership.field_res_users_membership_amount
msgid "The price negotiated by the partner"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.membership_products_form
msgid "This note will be displayed on quotations..."
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "This will display paid, old and total earned columns"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_report_membership_search
msgid "This will display waiting, invoiced and total pending columns"
msgstr ""

#. module: membership
#: model:ir.model.fields,field_description:membership.field_membership_membership_line_date_to
msgid "To"
msgstr ""

#. module: membership
#: model_terms:ir.ui.view,arch_db:membership.view_res_partner_member_filter
msgid "Vendors"
msgstr ""

#. module: membership
#: selection:membership.membership_line,state:0
#: selection:report.membership,membership_state:0
#: selection:res.partner,membership_state:0
msgid "Waiting Member"
msgstr ""

#. module: membership
#: model:ir.model,name:membership.model_membership_membership_line
msgid "membership.membership_line"
msgstr ""
