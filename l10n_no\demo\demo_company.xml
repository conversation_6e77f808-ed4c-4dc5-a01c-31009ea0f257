<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_no" model="res.partner">
        <field name="name">NO Company</field>
        <field name="vat">NO072274687MVA</field>
        <field name="street"></field>
        <field name="city">Åmot</field>
        <field name="country_id" ref="base.no"/>
        
        <field name="zip"></field>
        <field name="phone">+47 406 12 345</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.noexample.com</field>
        <field name="l10n_no_bronnoysund_number">*********</field>
    </record>

    <record id="demo_company_no" model="res.company">
        <field name="name">NO Company</field>
        <field name="partner_id" ref="partner_demo_company_no"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_no')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_no.demo_company_no'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_no.no_chart_template')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_no.demo_company_no')"/>
    </function>
</odoo>
