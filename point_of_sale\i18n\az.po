# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* point_of_sale
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-05 14:42+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: erpgo translator <<EMAIL>>, 2023\n"
"Language-Team: Azerbaijani (https://app.transifex.com/odoo/teams/41243/az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid " REFUND"
msgstr "Geri ödəniş"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "%(pos_name)s (not used)"
msgstr "%(pos_name)s (istifadə olunmur)"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "%s POS payment of %s in %s"
msgstr "%s %s-də %s-ın POS ödənişi"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid ""
"%s has a total amount of %s, are you sure you want to delete this order ?"
msgstr "%s üçün %s məbləği var, bu sifarişi silmək istədiyinizdən əminsiniz?"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#, python-format
msgid "& invoice"
msgstr "& faktura"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#, python-format
msgid "(Both will be sent by email)"
msgstr "Hər ikisi e-poçt vasitəsilə göndəriləcək."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "(RESCUE FOR %(session)s)"
msgstr "(%(session)s üçün Bərpa )"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "(as of opening)"
msgstr "(açılışa qədər)"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "(update)"
msgstr "(yenilə)"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid ". Please contact your manager to accept the closing difference."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"
msgstr ""
"<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"İdarə et\" "
"title=\"İdarə et\"/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>How to manage tax-included prices"
msgstr ""
"<i class=\"fa fa-fw fa-arrow-right\"/>Vergi daxil qiymətləri necə idarə "
"etmək olar"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_pos_kanban
msgid ""
"<i class=\"fa fa-fw fa-shopping-bag\" role=\"img\" aria-label=\"Shopping "
"cart\" title=\"Shopping cart\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-shopping-bag\" role=\"img\" aria-label=\"Shopping "
"cart\" title=\"Shopping cart\"/>"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "<p>Dear %s,<br/>Here is your electronic ticket for the %s. </p>"
msgstr ""
"Dəyərli %s,\n"
" %s üçün olan elektron biletinizi təqdim edirik. "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/tours/point_of_sale.js:0
#, python-format
msgid ""
"<p>Ready to have a look at the <b>POS Interface</b>? Let's start our first "
"session.</p>"
msgstr ""
"<p>Gəlin birlikdə <b>POS İnterfeysinə </b>nəzər yetirmək üçün ilk "
"sessiyamızı başladaq.</p>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid ""
"<span attrs=\"{'invisible': [('is_total_cost_computed','=', "
"True)]}\">TBD</span>"
msgstr ""
"<span attrs=\"{'invisible': [('is_total_cost_computed','=', "
"True)]}\">TBD</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Burada qeyd olunan dəyərlər "
"şirkətə məxsusdur.\" aria-label=\"Burada qeyd olunan dəyərlər şirkətə "
"məxsusdur.\" groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"o_form_label\">Authorized Employees</span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"o_form_label\">Barcodes</span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"o_form_label\">Default Pricelist</span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"o_form_label\">Journal Entries</span>"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"o_form_label\">Order Reference</span>"
msgstr "<span class=\"o_form_label\">Sifarişin İstinadı</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"o_form_label\">Payment Methods</span>"
msgstr "<span class=\"o_form_label\">Ödəniş Üsulları</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"oe_inline\"><b>Skip Preview Screen</b></span>"
msgstr "<span class=\"oe_inline\"><b>Önizləmə Ekranını Atla</b></span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Last Closing Cash Balance</span>"
msgstr "<span>Son Bağlanış Nəğd Balansı</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Last Closing Date</span>"
msgstr "<span>Son Bağlanış Tarixi </span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Reporting</span>"
msgstr "<span>Mühasibat uçotu</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>View</span>"
msgstr "<span>Baxın</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid ""
"<strong> &gt; Payment Terminals</strong>\n"
"                                    in order to install a Payment Terminal and make a fully integrated payment method."
msgstr ""
"<strong> &gt; Ödəniş Terminalları</strong>\n"
"                                    bir Ödəniş Terminalı quraşdırmaq və tam təkmilləşdirilmiş bir ödəniş metodunu yaratmaq üçün."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "? Clicking \"Confirm\" will validate the payment."
msgstr "\"Təsdiq et\" düyməsinə basmaq ödənişi təsdiqləyəcəkdir."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientDetailsEdit.js:0
#, python-format
msgid "A Customer Name Is Required"
msgstr "Müştəri Adı Daxil Etmək Tələb Olunur"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__uuid
msgid ""
"A globally unique identifier for this pos configuration, used to prevent "
"conflicts in client-generated data."
msgstr ""
"Müştəri tərəfindən yaradılan datada konfliktlərin qarşısını almaq üçün "
"istifadə olunan bu pos konfiqurasiyası üçün qlobal unikal identifikator, "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__login_number
msgid ""
"A sequence number that is incremented each time a user resumes the pos "
"session"
msgstr ""
"İstifadəçi pos sessiyasını davam etdirdikdə hər dəfə artırılan ardıcıllıq "
"nömrəsi"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__sequence_number
msgid "A sequence number that is incremented with each order"
msgstr "Hər sifarişlə birlikdə artırılan bir ardıcıllıq nömrəsi"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session
msgid ""
"A session is a period of time, usually one day, during which you sell "
"through the Point of Sale."
msgstr ""
"Bir sessiya, adətən bir gün boyunca, Satış Nöqtəsi üzərindən satış edilən "
"müddətdir."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"A session is currently opened for this PoS. Some settings can only be "
"changed after the session is closed."
msgstr ""
"Bu PoS üçün hazırda bir sessiya açıqdır. Bəzi parametrlər yalnız sessiya "
"bağlandıqdan sonra dəyişdirilə bilər."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__sequence_number
msgid "A session-unique sequence number for the order"
msgstr "Sifariş üçün sessiyaya məxsus unikal ardıcıllıq nömrəsi"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__receipt_footer
msgid "A short text that will be inserted as a footer in the printed receipt."
msgstr "Çap olunan qəbzin alt hissəsinə daxil ediləcək qısa bir mətn, "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__receipt_header
msgid "A short text that will be inserted as a header in the printed receipt."
msgstr "Çap olunan qəbzin başlığı kimi daxil ediləcək qısa bir mətn, "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#, python-format
msgid "AMOUNT"
msgstr "MƏBLƏĞ"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Accept customer tips or convert their change to a tip"
msgstr ""
"Müştərilərdən bəxşiş qəbul edin və ya onların xırda pullarını bəxşişə "
"çevirin. "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Accept payments difference and post a profit/loss journal entry"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with a Six payment terminal"
msgstr "Six ödəmə terminalı ilə ödəniş qəbul edin."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with a Vantiv payment terminal"
msgstr "Vantiv ödəniş terminalı ilə ödənişləri qəbul edin."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with an Adyen payment terminal"
msgstr "Adyen ödəniş terminalı ilə ödənişləri qəbul edin."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
msgid "Account"
msgstr "Hesab"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_cash_rounding
msgid "Account Cash Rounding"
msgstr "Hesabdakı vəsaitlərin yuvarlaqlaşdırılması"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_chart_template
msgid "Account Chart Template"
msgstr "Hesablar Planı Şablonu"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__account_move_id
msgid "Account Move"
msgstr "Hesab Hərəkəti"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__bank_payment_ids
msgid "Account payments representing aggregated and bank split payments."
msgstr ""
"Hesab ödənişləri birləşdirilmiş və bank tərəfindən bölünmüş ödənişləri "
"təmsil edir."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Accounting"
msgstr "Mühasibat uçotu"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__invoice_journal_id
msgid "Accounting journal used to create invoices."
msgstr "Fakturaların yaradılmasında istifadə olunan Mühasibat jurnalı"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__journal_id
#: model:ir.model.fields,help:point_of_sale.field_pos_order__sale_journal
msgid ""
"Accounting journal used to post POS session journal entries and POS invoice "
"payments."
msgstr ""
"POS sessiya jurnal qeydlərini və POS faktura ödənişlərini daxil etmək üçün "
"istifadə olunan Muhasibat Jurnalı "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_needaction
msgid "Action Needed"
msgstr "Lazımi Hərəkət"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__active
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__active
msgid "Active"
msgstr "Aktiv"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_ids
msgid "Activities"
msgstr "Fəaliyyətlər"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Faəliyyət istisnaetmə İşarəsi"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_state
msgid "Activity State"
msgstr "Fəaliyyət Statusu"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_type_icon
msgid "Activity Type Icon"
msgstr "Fəaliyyət Növü ikonu"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductConfiguratorPopup.xml:0
#, python-format
msgid "Add"
msgstr "Əlavə edin"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/OrderlineCustomerNoteButton.js:0
#, python-format
msgid "Add Customer Note"
msgstr "Müştəri Qeydi əlavə edin"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Add Tip"
msgstr "Bəxşiş Əlavə Et"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Add a custom message to header and footer"
msgstr "Başlığa və altbaşlığa özəl bir mesaj əlavə edin."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Add a customer"
msgstr "Müştəri Əlavə Et"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_method_form
msgid "Add a new payment method"
msgstr "Yeni Ödəniş Üsulu Əlavə Et "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Add notes on order lines to be printed on receipt and invoice"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Address"
msgstr "Ünvan"

#. module: point_of_sale
#: model:res.groups,name:point_of_sale.group_pos_manager
msgid "Administrator"
msgstr "Administrator"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__cash_control
msgid "Advanced Cash Control"
msgstr "Qabaqcıl Nəğd Pul Nəzarəti "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Advanced Pricelists"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Adyen"
msgstr "Adyen"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_adyen
msgid "Adyen Payment Terminal"
msgstr "Adyen Ödəniş Terminalı"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "All active orders"
msgstr "Bütün aktiv sifarişlər"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"All available pricelists must be in the same currency as the company or as "
"the Sales Journal set on this point of sale if you use the Accounting "
"application."
msgstr ""
"Əgər Mühasibat tətbiqindən istifadə edirsinizsə bütün mövcud qiymət "
"siyahıları şirkətin valyutası ilə eyni valyutada olmalıdır  və ya bu POs "
"üçün təyin olunmuş Satış Jurnalında təyin edilməlidir."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"All payment methods must be in the same currency as the Sales Journal or the"
" company currency if that is not set."
msgstr ""
"Bütün ödəniş üsulları Satış Jurnalı ilə eyni valyutada olmalıdır və ya təyin"
" edilməmişdirsə, şirkət valyutasında olmalıdır."

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_all_sales_lines
msgid "All sales lines"
msgstr "Bütün satış sətirləri"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Allow discounts per line"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Allow global discounts on orders"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_coupon
msgid "Allow the use of coupon and promotion programs in PoS."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_gift_card
msgid "Allow the use of gift card"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_orderline_customer_notes
msgid ""
"Allow to write notes for customer on Orderlines. This will be shown in the "
"receipt."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__allowed_pricelist_ids
msgid "Allowed Pricelists"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__is_total_cost_computed
msgid ""
"Allows to know if all the total cost of the order lines have already been "
"computed"
msgstr ""
"Bütün sifariş sətirlərinin ümumi məbləği hesablanıb hesablanmadığını bilməyə"
" imkan verir."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__is_total_cost_computed
msgid "Allows to know if the total cost has already been computed or not"
msgstr "Ümumi məbləğin  hesablanıb hesablanmadığını bilməyə imkan verir"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__amount
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__amount
msgid "Amount"
msgstr "Məbləğ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__amount_authorized_diff
msgid "Amount Authorized Difference"
msgstr "icazə verilən fərq məbləği"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__amount_to_balance
msgid "Amount to balance"
msgstr "Balanslaşdırılacaq Məbləğ"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
msgid "Amount total"
msgstr "Ümumi məbləğ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#, python-format
msgid ""
"An error has occurred when trying to close the session.\n"
"You will be redirected to the back-end to manually close the session."
msgstr ""
"Sessiyanı bağlamağa çalışarkən bir xəta baş verdi\n"
"Sessiyanı əl ilə bağlamaq üçün arxa tərəfə yönləndiriləcəksiniz."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid ""
"An error occurred when loading product prices. Make sure all pricelists are "
"available in the POS."
msgstr ""
"Məhsul Qiymətləri yüklənərkən xəta baş verdi. Bütün qiymət siyahılarının POS"
" da mövcud olduğundan əmin olun "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__name
msgid "An internal identification of the point of sale."
msgstr "Satış Nöqtəsinin daxili ID si "

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Another session is already opened for this point of sale."
msgstr "Bu satış nöqtəsi üçün artıq başqa bir sessiya açılıb."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_search
msgid "Archived"
msgstr "Arxivləndi"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Are you sure that the customer wants to  pay"
msgstr "Müştərinin ödəmək istədiyindən əminsinizmi"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__picking_policy__direct
msgid "As soon as possible"
msgstr "Mümkün qədər tez"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__res_company__point_of_sale_update_stock_quantities__closing
msgid "At the session closing (recommended)"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_company__point_of_sale_update_stock_quantities
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__update_stock_quantities
msgid ""
"At the session closing: A picking is created for the entire session when it's closed\n"
" In real time: Each order sent to the server create its own picking"
msgstr ""
"Sessiyanın bağlanmasında: Bağlandıqda bütün sessiya üçün bir xaricolma yaradılır\n"
"Real vaxtda: Serverə göndərilən hər sifariş üçün ayrıca xaricolma yaradılır."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_attachment_count
msgid "Attachment Count"
msgstr "Qoşma Sayı"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#: model:ir.ui.menu,name:point_of_sale.pos_menu_products_attribute_action
#, python-format
msgid "Attributes"
msgstr "Atributlar"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Authorized Difference"
msgstr "Səlahiyyət Verilən Fərq "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__rescue
msgid "Auto-generated session for orphan orders, ignored in constraints"
msgstr ""
"Məhdudiyyətlərdə nəzərə alınmayan sifarişlər üçün avtomatik yaradılmış "
"sessiya"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_auto
msgid "Automatic Receipt Printing"
msgstr "Avtomatik Qəbz Çapı"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_cashdrawer
msgid "Automatically open the cashdrawer."
msgstr "Avtomatik olaraq kassanı açın."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_available_categ_ids
msgid "Available PoS Product Categories"
msgstr "Mövcud PoS Məhsul Kateqoriyaları"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__available_pricelist_ids
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Available Pricelists"
msgstr "Mövcud Qiymət Siyahıları"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__available_in_pos
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__available_in_pos
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_search_view_pos
msgid "Available in POS"
msgstr "POS-da Mövcud"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__average_price
msgid "Average Price"
msgstr "Ortalama Qiymət"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ControlButtonPopup.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ScaleScreen/ScaleScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/ReprintReceiptScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Back"
msgstr "Geri"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/NumberPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenNumpad.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/NumpadWidget.xml:0
#, python-format
msgid "Backspace"
msgstr "Backspace"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_payment_method__type__bank
#, python-format
msgid "Bank"
msgstr "Bank"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__bank_payment_ids
msgid "Bank Payments"
msgstr "Bank Ödənişləri"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_bank_statement
msgid "Bank Statement"
msgstr "Bank Çıxarışı"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Barcode"
msgstr "Barkod"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__barcode_nomenclature_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Barcode Nomenclature"
msgstr "Barkod Nomenklaturası"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_barcode_rule
msgid "Barcode Rule"
msgstr "Barkod Qaydası"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Barcode Scanner"
msgstr "Barkod Oxuyucu "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Barcode Scanner/Card Reader"
msgstr "Barkod Oxuyucu / Kart Oxuyucu "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Base Amount"
msgstr "Baza Məbləğ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_difference
msgid "Before Closing Difference"
msgstr "Qapanış Fərqi"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_bill_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_bill_tree
msgid "Bills"
msgstr "Fakturalar"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Bills &amp; Receipts"
msgstr "Fakturalar &amp; Qəbzlər"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Buffer:"
msgstr "Bufer:"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__limited_partners_loading
msgid ""
"By default, 100 partners are loaded.\n"
"When the session is open, we keep on loading all remaining partners in the background.\n"
"In the meantime, you can use the 'Load Customers' button to load partners from database."
msgstr ""
"Standart olaraq, 100 tərəfdaş yüklənir.\n"
"Sessiya açıldıqda, arxa planda qalan bütün tərəfdaşları yükləməyə davam edirik.\n"
"Bu arada, verilənlər bazasından tərəfdaşları yükləmək üçün 'Müştəriləri Yüklə' düyməsindən istifadə edə bilərsiniz."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_via_proxy
msgid "Bypass browser printing and prints via the hardware proxy."
msgstr "Brauzer çapını ötürün və hardware proxy vasitəsilə çap edin."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#, python-format
msgid "CASH"
msgstr "NƏĞD "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "CHANGE"
msgstr "Xırda Pul"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "CLOSING CONTROL"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Can't change customer"
msgstr "Müştəri dəyişdirilə bilmir."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Can't mix order with refund products with new products."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/CashMovePopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ConfirmPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/EditListPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorBarcodePopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorTracebackPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/NumberPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/OfflineErrorPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/OrderImportPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/SelectionPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/TextAreaPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/TextInputPopup.js:0
#: code:addons/point_of_sale/static/src/xml/Popups/EditListPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ProductConfiguratorPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_form_pos_close_session_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
#, python-format
msgid "Cancel"
msgstr "Ləğv edin"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Cancel Payment Request"
msgstr "Ödəniş Tələbini Ləğv Et"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__cancel
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__cancel
msgid "Cancelled"
msgstr "Ləğv olundu"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Cannot Invoice"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ProductInfoPopup.js:0
#, python-format
msgid "Cannot access product information screen if offline."
msgstr "Məhsul məlumat ekranına çevrimdışı daxil olmaq mümkün deyil."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#, python-format
msgid "Cannot close the session when offline."
msgstr "İnternet olmadığında sessiyanı bağlaya bilməzsiz."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Cannot invoice order from closed session."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Cannot modify a tip"
msgstr "Bəxşişi dəyişə bilməzsiz "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Cannot return change without a cash payment method"
msgstr "Nəğd ödəniş üsulu olmadan qalıq pul qaytarmaq mümkün deyil."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__cardholder_name
#, python-format
msgid "Cardholder Name"
msgstr "Kart sahibinin adı"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__is_cash_count
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_payment_method__type__cash
#, python-format
msgid "Cash"
msgstr "Nağd Pul"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_cash_box_out
msgid "Cash Box Out"
msgstr "Çıxan seyfi"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMovePopup.xml:0
#, python-format
msgid "Cash In"
msgstr "Nəğd pul daxil etmək"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/CashMovePopup.js:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/CashMoveButton.xml:0
#, python-format
msgid "Cash In/Out"
msgstr "Nəğd Daxilolma/Xaricolma"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_journal_id
msgid "Cash Journal"
msgstr "Nəğd jurnalı"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMovePopup.xml:0
#, python-format
msgid "Cash Out"
msgstr "Nəğd pul çıxarışı"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Cash Register"
msgstr "Kassa"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__cash_rounding
msgid "Cash Rounding"
msgstr "Vəsaitlərin yuvarlaqlaşdırılması"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Cash Roundings"
msgstr "Vəsaitlərin yuvarlaqlaşdırılması"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__statement_ids
msgid "Cash Statements"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/CashMoveButton.js:0
#, python-format
msgid "Cash in/out of %s is ignored."
msgstr " %s üçün Nəğd Giriş/Çıxış nəzərə alınmadı."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Cash register for %s"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__rounding_method
msgid "Cash rounding"
msgstr "Nəğd pul yuvarlaqlaşdırma"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.account_cashbox_line_view_tree
msgid "Cashbox balance"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_cashdrawer
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Cashdrawer"
msgstr "Kassa qutusu"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__cashier
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
msgid "Cashier"
msgstr "Kassir"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_pos_category_action
msgid ""
"Categories are used to browse your products through the\n"
"                touchscreen interface."
msgstr ""
"Məhsullarınıza toxunma ekran interfeysi vasitəsilə gözətmaq üçün \n"
"istifadə olunan kateqoriyalar."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/CategoryButton.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_category_kanban
#, python-format
msgid "Category"
msgstr "Kateqoriya"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__name
msgid "Category Name"
msgstr "Kateqoriyanın adı"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Category Pictures"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__pos_categ_id
#: model:ir.model.fields,help:point_of_sale.field_product_template__pos_categ_id
msgid "Category used in the Point of Sale."
msgstr "Satış Nöqtəsində istifadə olunan kateqoriya."

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_chairs
msgid "Chairs"
msgstr "Oturacaqlar"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/CustomerFacingDisplay/CustomerFacingDisplayOrder.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenStatus.xml:0
#, python-format
msgid "Change"
msgstr "Dəyiş"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientListScreen.js:0
#, python-format
msgid "Change Customer"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Change Tip"
msgstr "Bəxşişi Dəyişin "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__to_weight
#: model:ir.model.fields,help:point_of_sale.field_product_template__to_weight
msgid ""
"Check if the product should be weighted using the hardware scale "
"integration."
msgstr ""
"Məhsulun tərəzi vasitəsilə çəki integrasiyası istifadə edilərək çəkilməsi "
"üçün seçin."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__available_in_pos
#: model:ir.model.fields,help:point_of_sale.field_product_template__available_in_pos
msgid "Check if you want this product to appear in the Point of Sale."
msgstr "Məhsulun Satış Nöqtəsində görünməsini istəyirsinizsə seçin."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_uom_category__is_pos_groupable
#: model:ir.model.fields,help:point_of_sale.field_uom_uom__is_pos_groupable
msgid ""
"Check if you want to group products of this category in point of sale orders"
msgstr ""
"Bu kateqoriyadakı məhsulları POS sifarişlərində qruplaşdırmaq istəyirsinizsə"
" işarə edin."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__cash_control
msgid "Check the amount of the cashbox at opening and closing."
msgstr "Açılış və bağlanış zamanı kassadakı məbləği yoxlayın."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Check the internet connection then try again."
msgstr "İnternet bağlantısını yoxlayın və sonra yenidən cəhd edin."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid ""
"Check the internet connection then try to sync again by clicking on the red "
"wifi button (upper right of the screen)."
msgstr ""
"İnternet bağlantısını yoxlayın, sonra yenidən sinxronizasiya etmək üçün "
"ekranın yuxarı sağında olan qırmızı wifi düyməsinə klikləyin."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__child_id
msgid "Children Categories"
msgstr "Alt kateqoriyalar"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Choose a specific fiscal position at the order depending on the kind of "
"customer (tax exempt, onsite vs. takeaway, etc.)."
msgstr ""
"Müştəri növünə (vergidən azad, yerli və s.) əsasən sifarişdə müəyyən bir "
"maliyyə vəziyyəti seçin."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Choose among fiscal positions when processing an order"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "City"
msgstr "Şəhər"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Click here to close the session"
msgstr "Sessiyanı bağlamaq üçün buraya klikləyin."

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__client
msgid "Client"
msgstr "Müştəri"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ClientScreenButton.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ClientScreenButton.xml:0
#, python-format
msgid "Client Screen Connected"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ClientScreenButton.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ClientScreenButton.xml:0
#, python-format
msgid "Client Screen Disconnected"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ClientScreenButton.js:0
#, python-format
msgid "Client Screen Unsupported. Please upgrade the IoT Box"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ClientScreenButton.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ClientScreenButton.xml:0
#, python-format
msgid "Client Screen Warning"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/HeaderButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
#, python-format
msgid "Close"
msgstr "Bağlayın"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_form_pos_close_session_wizard
#, python-format
msgid "Close Session"
msgstr "Sessiyanı Bağla"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Close Session & Post Entries"
msgstr "Sessiyanı bağla & Əməliyatları Jurnala Köçür"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_close_session_wizard
msgid "Close Session Wizard"
msgstr "Sessiya Qapanış Sehrbazı"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__closed
msgid "Closed & Posted"
msgstr "Bağlandı & Köçürüldü"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Closing ..."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__closing_control
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Closing Control"
msgstr "Bağlama Nəzarəti"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__stop_at
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Closing Date"
msgstr "Bağlanma Tarixi"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Closing difference in %s (%s)"
msgstr "Qapanış fərqi %s (%s) "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#, python-format
msgid "Closing session error"
msgstr "Sessiyanın bağlanma xətası"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__value
msgid "Coin/Bill Value"
msgstr "Qəpik Pullar və Kağız Pulların Dəyəri"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/MoneyDetailsPopup.xml:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_bill
#: model:ir.model,name:point_of_sale.model_pos_bill
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__default_bill_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_bill
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#, python-format
msgid "Coins/Bills"
msgstr "Qəpiklər/Kağız Pullar"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Combine %s POS payments from %s"
msgstr "%s ilə %s-dən POS ödənişlərini birləşdirin."

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_company
msgid "Companies"
msgstr "Şirkətlər"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__company_id
msgid "Company"
msgstr "Şirkət"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__company_has_template
msgid "Company has chart of accounts"
msgstr "Şirkətin hesabatlar planı var"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_config_settings
msgid "Config Settings"
msgstr "Parametrləri Konfiqurasiya edin"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_config_product
msgid "Configuration"
msgstr "Konfiqurasiya"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Configuration for journal entries of PoS orders"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Configurations &gt; Settings"
msgstr "Konfiqurasiyalar &gt; Ayarlar"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_kanban
msgid "Configure at least one Point of Sale."
msgstr "Bir ən azı bir Satış Nöqtəsi konfiqurasiya edin."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/SelectionPopup.js:0
#: code:addons/point_of_sale/static/src/xml/Popups/CashMovePopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/MoneyDetailsPopup.xml:0
#, python-format
msgid "Confirm"
msgstr "Təsdiq edin"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ConfirmPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/NumberPopup.js:0
#, python-format
msgid "Confirm ?"
msgstr "Təsdiqlə ?"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Connect devices to your PoS directly without an IoT Box"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__other_devices
msgid "Connect devices to your PoS without an IoT Box."
msgstr "Cihazları IoT qurğusu olmadan PoS-a qoşun."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Connect devices using an IoT Box"
msgstr "Cihazları bir IoT Qurğusu ilə bağlayın."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Connected Devices"
msgstr "Qoşulmuş Cihazlar "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ClientScreenButton.js:0
#, python-format
msgid "Connected, Not Owned"
msgstr "Bağlı, Sahib Deyil"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#, python-format
msgid "Connecting to Proxy"
msgstr "Proxy ilə əlaqə yaradılır."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Connecting to the IoT Box"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Connection Error"
msgstr "Qoşulma Xətası"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Connection error"
msgstr "Qoşulma xətası"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Connection is aborted"
msgstr "Qoşulma dayandırıldı"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Connection is lost"
msgstr "Qoşulma kəsildi "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/printers.js:0
#: code:addons/point_of_sale/static/src/js/printers.js:0
#, python-format
msgid "Connection to IoT Box failed"
msgstr "IoT Qurğusuna qoşulma uğursuz oldu."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/printers.js:0
#: code:addons/point_of_sale/static/src/js/printers.js:0
#, python-format
msgid "Connection to the printer failed"
msgstr "Printerə qoşulma uğursuz oldu"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Continue Selling"
msgstr "Satmağa Davam edin"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Continue selling"
msgstr "Satmağa davam et "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__currency_rate
msgid "Conversion Rate"
msgstr "Konvertasiya Dərəcəsi "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment__currency_rate
msgid "Conversion rate from company currency to order currency."
msgstr "Şirkət valyutasından sifariş valyutasına konvertasiya dərəcəsi."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Cost:"
msgstr "Dəyər:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Counted"
msgstr "Sayılan "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Country"
msgstr "Ölkə"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_coupon
msgid "Coupon and Promotion Programs"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Coupons & Promotions"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Create"
msgstr "Yaradın"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_sale_graph
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all_filtered
msgid "Create a new POS order"
msgstr "Yeni POS sifarişi Yarat "

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_kanban
msgid "Create a new PoS"
msgstr "Yeni POS Yarat "

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_product_action
msgid "Create a new product variant"
msgstr "Yeni məhsul variantı yaradın"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__create_uid
msgid "Created by"
msgstr "Tərəfindən yaradılıb"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__create_date
msgid "Created on"
msgstr "Tarixdə yaradıldı"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__currency_id
msgid "Currency"
msgstr "Valyuta"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__currency_rate
msgid "Currency Rate"
msgstr "Valyuta Məzənnəsi"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_session_id
msgid "Current Session"
msgstr "Cari sessiya"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_user_id
msgid "Current Session Responsible"
msgstr "Cari Sessiyadan Cavabdeh"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_session_state
msgid "Current Session State"
msgstr "Cari sessiya vəziyyəti"

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.fabric_attribute_custom
msgid "Custom"
msgstr "Xüsusi"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__partner_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__partner_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__partner_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#, python-format
msgid "Customer"
msgstr "Müştəri"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/models/pos_config.py:0
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_payment_method__type__pay_later
#, python-format
msgid "Customer Account"
msgstr "Müştəri Hesabı"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Customer Display"
msgstr "Müştəri Ekranı"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display_via_proxy
msgid "Customer Facing Display"
msgstr "Müştəri Tərəfdəki Ekran"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Customer Invoice"
msgstr "Müştərinin Hesab-fakturası"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/OrderlineCustomerNoteButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderlineDetails.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderlineDetails.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__customer_note
#, python-format
msgid "Customer Note"
msgstr "Müştəri Qeydi "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_orderline_customer_notes
msgid "Customer Notes"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Customer Required"
msgstr "Müştəri seçilməsi tələb olunur."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#: code:addons/point_of_sale/wizard/pos_payment.py:0
#, python-format
msgid "Customer is required for %s payment method."
msgstr "Müştəriyə %s ödəniş üsulu tələb olunur."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Customer tips, cannot be modified directly"
msgstr "Müştəri bəxşişləri, birbaşa dəyişdirilə bilməz."

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_of_sale_customer
msgid "Customers"
msgstr "Müştərilər"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_pos_dashboard
msgid "Dashboard"
msgstr "Şəxsi kabinet"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__date_order
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_date
#, python-format
msgid "Date"
msgstr "Tarix"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Days"
msgstr "Gün"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Debug Window"
msgstr "Debug Pəncərəsi "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement__account_id
msgid "Default Account"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__account_default_pos_receivable_account_id
msgid "Default Account Receivable (PoS)"
msgstr "Standart Debitor Hesablar (PoS)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__default_fiscal_position_id
msgid "Default Fiscal Position"
msgstr "Standart Maliyyə Vəziyyəti "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default Intermediary Account"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pricelist_id
msgid "Default Pricelist"
msgstr "Standart Qiymət Siyahısı"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__sale_tax_id
msgid "Default Sale Tax"
msgstr "Defolt Satış Vergisi"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default Sales Tax"
msgstr "Standart Satış Vergisi"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default sales tax for products"
msgstr "Məhsullar üçün standart satış vergisi"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__product_uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "Bütün anbar əməliyyatları üçün istifadə olunan standart ölçü vahidi."

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_pos_category_action
msgid "Define a new category"
msgstr "Yeni bir kateqoriya təyin edin."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Define the smallest coinage of the currency used to pay"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Define the smallest coinage of the currency used to pay by cash"
msgstr ""
"Nağd pulla ödəmək üçün istifadə olunan valyutanın ən kiçik pul vahidlərini "
"müəyyənləşdirin"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__name
msgid ""
"Defines the name of the payment method that will be displayed in the Point "
"of Sale when the payments are selected."
msgstr ""
"Ödənişlər seçildikdə Satış Nöqtəsində göstəriləcək ödəniş metodunun adını "
"təyin edir."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__barcode_nomenclature_id
msgid ""
"Defines what kind of barcodes are available and how they are assigned to "
"products, customers and cashiers."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__delay_validation
msgid "Delay Validation"
msgstr "Gecikmənin təsdiq edilməsi"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Delete"
msgstr "Silin"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Delete Paid Orders"
msgstr "Ödənilmiş Sifarişləri Sil "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/DebugWidget.js:0
#, python-format
msgid "Delete Paid Orders ?"
msgstr "Ödənilmiş Sifarişləri Sil ?"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Delete Unpaid Orders"
msgstr "Ödənilməmiş Sifarişləri Sil "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/DebugWidget.js:0
#, python-format
msgid "Delete Unpaid Orders ?"
msgstr "Ödənilməmiş Sifarişləri Sil ?"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientListScreen.js:0
#, python-format
msgid "Deselect Customer"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.desk_organizer
#: model:product.template,name:point_of_sale.desk_organizer_product_template
msgid "Desk Organizer"
msgstr "Masa təşkilatçısı"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.desk_pad
#: model:product.template,name:point_of_sale.desk_pad_product_template
msgid "Desk Pad"
msgstr "Masa Üstü"

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_desks
msgid "Desks"
msgstr "Masalar"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__account_id
msgid "Destination account"
msgstr "Hədəf Hesab"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__account_readonly
msgid "Destination account is readonly"
msgstr "Hədəf hesab yalnız oxuna bilər."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_real_difference
#, python-format
msgid "Difference"
msgstr "Fərq"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Difference at closing PoS session"
msgstr "PoS sessiyasının bağlanmasında fərq"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_difference
msgid ""
"Difference between the theoretical closing balance and the real closing "
"balance."
msgstr "Teorik bağlama və həqiqi bağlama arasındakı fərq."

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_digest_digest
msgid "Digest"
msgstr "Daycest"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Direct Devices"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/NumpadWidget.xml:0
#, python-format
msgid "Disc"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Disc.%"
msgstr "Endirim %"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Disc:"
msgstr "ENDRM:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/MoneyDetailsPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Discard"
msgstr "Ləğv edin"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ClientScreenButton.js:0
#, python-format
msgid "Disconnected"
msgstr "Əlaqə Kəsildi "

#. module: point_of_sale
#: model:product.product,name:point_of_sale.product_product_consumable
#: model:product.template,name:point_of_sale.product_product_consumable_product_template
msgid "Discount"
msgstr "Endirim"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__discount
msgid "Discount (%)"
msgstr "Endirim (%)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__notice
msgid "Discount Notice"
msgstr "Endirim Qeydi "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/SaleDetailsReport.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Discount:"
msgstr "Endirim:"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__discount
msgid "Discounted Product"
msgstr "Endirimli Məhsul "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Discounts"
msgstr "Endirimlər"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Dismiss"
msgstr "İşdən çıxarmaq"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_display_categ_images
msgid "Display Category Pictures"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__display_name
msgid "Display Name"
msgstr "Ekran Adı"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Display pictures of product categories"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr ""
"Əgər giriş edə bilmirsinizsə, bu məlumatı istifadəçinin daycest emaili üçün "
"buraxın"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Do you want to open the customer list to select customer?"
msgstr "Müştəri seçmək üçün müştəri siyahısını açmaq istəyirsiniz?"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Misc/AbstractReceiptScreen.js:0
#, python-format
msgid "Do you want to print using the web printer?"
msgstr "Web printer istifadə edərək çap etmək istəyirsinizmi?"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Documentation"
msgstr "Sənədləşmə"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OfflineErrorPopup.xml:0
#, python-format
msgid "Don't show again"
msgstr "Bir daha göstərmə "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#, python-format
msgid "Done by"
msgstr "Yerinə yetirən"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Download Paid Orders"
msgstr "Ödənilmiş Sifarişləri Endir "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Download Unpaid Orders"
msgstr "Ödənilməmiş sifarişləri endir "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ErrorTracebackPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ErrorTracebackPopup.xml:0
#, python-format
msgid "Download error traceback"
msgstr "Yükləmə səhvi izləməsi"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientLine.xml:0
#, python-format
msgid "EDIT"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/OfflineErrorPopup.js:0
#, python-format
msgid "Either the server is inaccessible or browser is not connected online."
msgstr "Serverlə əlaqə yoxdur və ya brauzer internetə qoşulmamışdır."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_electronic_scale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#, python-format
msgid "Electronic Scale"
msgstr "Elektron Tərəzi "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Email"
msgstr "Elektron poçt"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#, python-format
msgid "Email Receipt"
msgstr "Email Qəbzi"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ReceiptScreen/ReceiptScreen.js:0
#, python-format
msgid "Email sent."
msgstr "Email göndərildi "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Employee"
msgstr "İşçi"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Employees can scan their badge or enter a PIN to log in to a PoS session. "
"These credentials are configurable in the *HR Settings* tab of the employee "
"form."
msgstr ""
"İşçilər bir PoS sessiyasına daxil olmaq üçün kartlarını skan edə və ya PIN "
"kodunu daxil edə bilərlər. Bu məlumatlar işçi formunun *HR Ayarları* "
"bölməsində tənzimlənə bilər."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Empty Order"
msgstr "Boş Sifariş"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_scan_via_proxy
msgid ""
"Enable barcode scanning with a remotely connected barcode scanner and card "
"swiping with a Vantiv card reader."
msgstr ""
"Uzaq bağlantılı barkod oxuyucu ilə oxumanı və Vantiv kart oxuyucu ilə kart "
"məlumatlarının qəbul edilməsini aktivləşdirin."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_electronic_scale
msgid "Enables Electronic Scale integration."
msgstr "Elektron Tərəzi inteqrasiyasını Aktivləşdirir"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__module_account
#: model:ir.model.fields,help:point_of_sale.field_pos_order__invoice_group
msgid "Enables invoice generation from the Point of Sale."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientDetailsEdit.js:0
#, python-format
msgid "Encountered error when loading image. Please try again."
msgstr "Şəkil yüklənərkən səhv baş verdi. Yenidən cəhd edin."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__end_date
msgid "End Date"
msgstr "Bitmə Tarixi"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_end_real
msgid "Ending Balance"
msgstr "Bağlanış Balansı"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ErrorBarcodePopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorPopup.js:0
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Error"
msgstr "Xəta"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_category.py:0
#, python-format
msgid "Error ! You cannot create recursive categories."
msgstr "Xəta! Rekursiv kateqoriyalar yarada bilməzsiniz."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ErrorTracebackPopup.js:0
#, python-format
msgid "Error with Traceback"
msgstr "Traceback ilə Xəta"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Error: no internet connection."
msgstr "Xəta: internetə qoşulmayıb"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "Existing orderlines"
msgstr "Mövcud sifariş sətrləri"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ErrorTracebackPopup.js:0
#, python-format
msgid "Exit Pos"
msgstr "Pos dan Çıx"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_real_expected
#, python-format
msgid "Expected"
msgstr "Ehtimal edilən"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Export Paid Orders"
msgstr "Ödənilmiş Sifarişləri Export Et"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Export Unpaid Orders"
msgstr "Ödənilməmiş sifarişləri Export et"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Extra Info"
msgstr "Əlavə Məlumat"

#. module: point_of_sale
#: model:product.attribute,name:point_of_sale.fabric_attribute
msgid "Fabric"
msgstr "Fabrik"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__failed_pickings
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__failed_pickings
msgid "Failed Pickings"
msgstr "Uğursuz Seçimlər"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Financials"
msgstr "Maliyyə"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "Finished Importing Orders"
msgstr "Sifarişlərin İmportu Bitdi"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__fiscal_position_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Fiscal Position"
msgstr "Maliyyə Vəziyyəti"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "Fiscal Position not found"
msgstr "Maliyyə vəziyyəti tapılmadı."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Fiscal Position per Order"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__fiscal_position_ids
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Fiscal Positions"
msgstr "Maliyyə Vzəiyyəti"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "Fiscal data module error"
msgstr "Maliyyə məlumat modulu xətası"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_follower_ids
msgid "Followers"
msgstr "İzləyicilər"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_partner_ids
msgid "Followers (Partners)"
msgstr "İzləyicilər (Tərəfdaşlar)"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Gözəl şriftli ikon, məsələn fa-tapşırıqlar"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Footer"
msgstr "Altbilgi"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_big_scrollbars
msgid "For imprecise industrial touchscreens."
msgstr "Qeyri-dəqiq sənaye tipli toxunma ekranlar üçün."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_form_pos_close_session_wizard
#, python-format
msgid "Force Close Session"
msgstr "Sessiyanı Məcburi Bağla"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Force Done"
msgstr "Təcili Bitir"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Force done"
msgstr "Məcburi bitir"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__force_outstanding_account_id
msgid "Forced Outstanding Account"
msgstr "Məcburi Gözləmə Hesabı"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__split_transactions
msgid ""
"Forces to set a customer when using this payment method and splits the "
"journal entries for each customer. It could slow down the closing process."
msgstr ""
"Bu ödəniş üsulunu istifadə edərkən müştəri təyin etməyə və hər bir müştəri "
"üçün jurnal qeydlərini bölüşdürməyə məcbur edir. Bağlama prosesini yavaşlada"
" bilər."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "From invoice payments"
msgstr "Faktura ödənişlərindən"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__full_product_name
msgid "Full Product Name"
msgstr "Tam Məhsul Adı"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Generation of your order references"
msgstr "Sifariş referanslarının yaradılması"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_gift_card
msgid "Gift Cards"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Gift card"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Give customer rewards, free samples, etc."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_category__sequence
msgid "Gives the sequence order when displaying a list of product categories."
msgstr "Məhsul kateqoriyalarının siyahısını göstərərkən sıra təyin edir."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_discount
msgid "Global Discounts"
msgstr "Global Endirimlər"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Go to"
msgstr "Gedin"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Greater than allowed"
msgstr "İcazə veriləndən çoxdur"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Group By"
msgstr "Aşağıdakılara görə Qrupla"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_uom_category__is_pos_groupable
#: model:ir.model.fields,field_description:point_of_sale.field_uom_uom__is_pos_groupable
msgid "Group Products in POS"
msgstr "POS-da Məhsulları Qruplaşdır"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "HTTPS connection to IoT Box failed"
msgstr "IoT Qurğusuna  HTTPS əlaqəsi  uğursuz oldu."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Hardware Events"
msgstr "Cihaz Hadisələri"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Hardware Status"
msgstr "Cihaz Statusu"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__has_active_session
msgid "Has Active Session"
msgstr "Aktiv Sessiya Var"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_control
msgid "Has Cash Control"
msgstr "Nəğd Pul İdarəsi Var"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__has_message
msgid "Has Message"
msgstr "Mesajı Var"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__has_refundable_lines
msgid "Has Refundable Lines"
msgstr "Geri qaytarılabilən sətirlər var"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Header"
msgstr "Başlıq"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_header_or_footer
msgid "Header & Footer"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__hide_use_payment_terminal
msgid "Hide Use Payment Terminal"
msgstr "Ödəniş Terminalı İstifadəsini Gizlət"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/HomeCategoryBreadcrumb.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/HomeCategoryBreadcrumb.xml:0
#, python-format
msgid "Home"
msgstr "Əsas"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#, python-format
msgid "How would you like to receive your receipt"
msgstr "Sifariş qəbzinizi necə almaq istərdiniz?"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__id
msgid "ID"
msgstr "ID"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ErrorTracebackPopup.js:0
#, python-format
msgid "IMPORTANT: Bug Report From Odoo Point Of Sale"
msgstr "Mühüm: Odoo Satış Nöqtəsindən Xəta Bildirişi"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__proxy_ip
msgid "IP Address"
msgstr "IP Ünvanı"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_exception_icon
msgid "Icon"
msgstr "Simvol"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "İstisna fəaliyyəti göstərən simvol."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__split_transactions
msgid "Identify Customer"
msgstr "Müştəriyi təyin et"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_needaction
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_unread
msgid "If checked, new messages require your attention."
msgstr "İşarələnibsə, yeni mesajlara baxmalısınız."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_has_error
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Yoxlanılıbsa, bəzi mesajların çatdırılmasında xəta var."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__refunded_orderline_id
msgid ""
"If this orderline is a refund, then the refunded orderline is specified in "
"this field."
msgstr ""
"Əgər bu sifariş sətri qaytarmadırsa, o zaman qaytarılan sifariş sətri bu "
"sahədə göstərilir."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__picking_policy
msgid ""
"If you deliver all products at once, the delivery order will be scheduled "
"based on the greatest product lead time. Otherwise, it will be based on the "
"shortest."
msgstr ""
"Bütün məhsulları birlikdə çatdırırsınızsa, çatdırılma ən gec təslim olunacaq"
" məhsulun təslim müddətinə əsasən planlaşdırılacaq. Əks halda, ən qısa "
"müddətə əsasən planlaşdırılacaq."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display
msgid "Iface Customer Facing Display"
msgstr "Iface Müştəri Tərəfdən Görünən Ekran"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__image_128
msgid "Image"
msgstr "Şəkil"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Import Orders"
msgstr "Sifarişləri import et"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Improve navigation for imprecise industrial touchscreens"
msgstr ""
"Qeyri-həssas sənaye tipli  toxunma ekranları üçün naviqasiyanı "
"yaxşılaşdırın."

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__opened
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "In Progress"
msgstr "Həyata Keçirilməkdə"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "In order to delete a sale, it must be new or cancelled."
msgstr "Satışı silmək üçün, o yeni və ya ləğv edilmiş olmalıdır."

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__res_company__point_of_sale_update_stock_quantities__real
msgid "In real time"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Incorrect address for shipping"
msgstr "Çatdırma üçün Yanlış ünvan "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Incorrect rounding"
msgstr "Səhv yuvarlaqlaşdırma"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/ProductInfoButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/ProductInfoButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/ProductInfoButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductItem.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductItem.xml:0
#, python-format
msgid "Info"
msgstr "Məlumat"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__message
msgid "Information message"
msgstr "Məlumat mesajı"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_start_categ_id
msgid "Initial Category"
msgstr "İlkin Kateqoriya"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_method_form
msgid ""
"Installing chart of accounts from the General Settings of\n"
"                Invocing/Accounting app will create Bank and Cash payment\n"
"                methods automatically."
msgstr ""
"Muhasibat vəya Qaimə tətbiqinin ayarlarından Hesablar planı \n"
"                Təyin etdiyiniz zaman Bank və Nəğd ödənişlər \n"
"                avtomatik olaraq yaranacaqdır."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_mercury
msgid "Integrated Card Payments"
msgstr "İnteqrasiya olunmuş kart ödənişləri "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__receivable_account_id
msgid "Intermediary Account"
msgstr "Vasitəçi Hesab"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_category_action
msgid "Internal Categories"
msgstr "Daxili Kateqoriyalar"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__note
msgid "Internal Notes"
msgstr "Daxili Qeydlər"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Invalid action"
msgstr "Xətalı Əməliyyat"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/CashMovePopup.js:0
#, python-format
msgid "Invalid amount"
msgstr "Xətalı Məbləğ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ReceiptScreen/ReceiptScreen.js:0
#, python-format
msgid "Invalid email."
msgstr "Xətalı email"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "Invalid product lot"
msgstr "Xətalı məhsul lotu"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#, python-format
msgid "Inventory"
msgstr "Anbar"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Inventory Management"
msgstr "Anbar İdarəetmə"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__account_move
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Invoice"
msgstr "Hesab-faktura"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__invoice_journal_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Invoice Journal"
msgstr "Faktura Jurnalı "

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_payment.py:0
#, python-format
msgid "Invoice payment for %s (%s) using %s"
msgstr " %s istifadə olunaraq  %s (%s) üçün kəsilmiş qaimə "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__invoiced
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__invoiced
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__invoiced
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Invoiced"
msgstr "Qaiməsi irəli sürülüb"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_account
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__invoice_group
msgid "Invoicing"
msgstr "Hesab-faktura göndərilməsi"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "IoT Box"
msgstr "IoT Qurğu"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "IoT Box IP Address"
msgstr "IoT Qurğu IP Adresi"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_is_follower
msgid "Is Follower"
msgstr "İzləyicidir"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_invoiced
msgid "Is Invoiced"
msgstr "Fakturalanıb"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_refunded
msgid "Is Refunded"
msgstr "Geri qaytarılıb"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_total_cost_computed
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__is_total_cost_computed
msgid "Is Total Cost Computed"
msgstr "Cəmi məbləğ hesablanıb"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__is_in_company_currency
msgid "Is Using Company Currency"
msgstr "Şirkət valyutası istifadə edir."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_restaurant
msgid "Is a Bar/Restaurant"
msgstr "Bar/Restorandır"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_installed_account_accountant
msgid "Is the Full Accounting Installed"
msgstr "Yüklənmiş tam Mühasibatlıqdır"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_tipped
msgid "Is this already tipped?"
msgstr "Bəxşiş qəbul edilibmi?"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__is_change
msgid "Is this payment change?"
msgstr "Bu ödənişin qalığıdırmı?"

#. module: point_of_sale
#: code:addons/point_of_sale/models/account_tax.py:0
#, python-format
msgid ""
"It is forbidden to modify a tax used in a POS order not posted. You must "
"close the POS sessions before modifying the tax."
msgstr ""
"Dərc olunmamıış POS sifarişlərində  istifadə olunmuş vergilərdə dəyişiklik "
"etmək qadağandır. Vergini dəyişdirməzdən əvvəl POS sessiyalarını "
"bağlamalısınız."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/debug_manager.js:0
#, python-format
msgid "JS Tests"
msgstr "JS Testlər"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_journal
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__journal_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__journal_id
msgid "Journal"
msgstr "Jurnal"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_move
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__move_id
msgid "Journal Entry"
msgstr "Jurnal Girişi"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_move_line
msgid "Journal Item"
msgstr "Jurnal Sətirləri"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Journal Items"
msgstr "Jurnal Sətirləri"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Keep Session Open"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_digest_digest__kpi_pos_total_value
msgid "Kpi Pos Total Value"
msgstr "Kpi Pos Ümumi Dəyər"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.led_lamp
#: model:product.template,name:point_of_sale.led_lamp_product_template
msgid "LED Lamp"
msgstr "LED Lampa"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__name
msgid "Label"
msgstr "Etiket"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Language"
msgstr "Dil"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_big_scrollbars
msgid "Large Scrollbars"
msgstr "Böyük sürükləmə çubuqları"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order____last_update
msgid "Last Modified on"
msgstr "Son Dəyişdirilmə tarixi"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__last_session_closing_cash
msgid "Last Session Closing Cash"
msgstr "Son sessiyanın Bağlanış Nəğdi "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__last_session_closing_date
msgid "Last Session Closing Date"
msgstr "Son sessiyanın bağlanma tarixi"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__write_uid
msgid "Last Updated by"
msgstr "Son Yeniləyən"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__write_date
msgid "Last Updated on"
msgstr "Son Yenilənmə tarixi"

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.fabric_attribute_leather
msgid "Leather"
msgstr "Dəri"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Leave empty to use the default account from the company setting"
msgstr ""
"Şirkət ayarlarındakı standart hesabları istifadə etmək üçün boş buraxın."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__outstanding_account_id
msgid ""
"Leave empty to use the default account from the company setting.\n"
"Account used as outstanding account when creating accounting payment records for bank payments."
msgstr ""
"Şirkət tənzimləməsindən istifadə edilən standart hesabı istifadə etmək üçün boş buraxın, .\n"
"Bank ödənişləri üçün hesablaşdırma ödəniş qeydləri yaradıldıqda istifadə olunan gözləmə hesabı."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__receivable_account_id
msgid ""
"Leave empty to use the default account from the company setting.\n"
"Overrides the company's receivable account (for Point of Sale) used in the journal entries."
msgstr ""
"Şirkət tənzimləməsindən istifadə edilən standart hesabı istifadə etmək üçün boş buraxın,.\n"
"Jurnal qeydlərində istifadə olunan şirkətin debitor hesabını hesabını (Satış Nöqtəsi üçün) başqa hesabla əvəz edir."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Leave empty to use the receivable account of customer"
msgstr "Müştərinin debitor hesabını istifadə etmək üçün boş buraxın."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__journal_id
msgid ""
"Leave empty to use the receivable account of customer.\n"
"Defines the journal where to book the accumulated payments (or individual payment if Identify Customer is true) after closing the session.\n"
"For cash journal, we directly write to the default account in the journal via statement lines.\n"
"For bank journal, we write to the outstanding account specified in this payment method.\n"
"Only cash and bank journals are allowed."
msgstr ""
"Müştərinin debitor hesabını istifadə etmək üçün boş buraxın.\n"
"Sessiya bağlandıqdan sonra toplanmış ödənişlərin (və ya Müştəriyi təyin olunubsa, fərdi ödənişlərin) qeydiyyat yerini təyin edir.\n"
"Nəğd jurnal üçün, biz direkt olaraq jurnalda qeydiyyat aparırıq.\n"
"Bank Jurnalı üçün, bu ödəniş üsulunda göstərilən gözləmə hesabına qeyd edirik.\n"
"Yalnız nəğd və bank jurnallarına icazə verilir."

#. module: point_of_sale
#: model:product.product,name:point_of_sale.letter_tray
#: model:product.template,name:point_of_sale.letter_tray_product_template
msgid "Letter Tray"
msgstr "Məktub Qabı"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__limited_partners_amount
msgid "Limited Partners Amount"
msgstr "Məhdud ortaqların miqdarı"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__limited_partners_loading
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Limited Partners Loading"
msgstr "Məhdud ortaqlar yüklənir"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__limited_products_loading
msgid "Limited Product Loading"
msgstr "Məhdud Məhsul Yüklənməsi"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__limited_products_amount
msgid "Limited Products Amount"
msgstr "Məhdud Məhsul Miqdarı"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Limited Products Loading"
msgstr "Məhdud Məhsullar Yüklənir"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__name
msgid "Line No"
msgstr "Sətir No"

#. module: point_of_sale
#: code:addons/point_of_sale/wizard/pos_open_statement.py:0
#, python-format
msgid "List of Cash Registers"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Load Customers"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Load all remaining partners in the background"
msgstr "Arxa planda qalan bütün tərəfdaşları yükləyin"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Load all remaining products in the background"
msgstr "Arxa planda qalan bütün məhsulları yükləyin"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Loading"
msgstr "Yükləmə"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientDetailsEdit.js:0
#, python-format
msgid "Loading Image Error"
msgstr "Şəkil Yükləmə Xətası"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display_local
msgid "Local Customer Facing Display"
msgstr "Yerli Müştəri Tərəfdən Görünən Ekran"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__login_number
msgid "Login Sequence Number"
msgstr "Giriş Sıra Nömrəsi"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Chrome.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#: code:addons/point_of_sale/static/src/xml/SaleDetailsReport.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Logo"
msgstr "Loqotip"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__lot_name
msgid "Lot Name"
msgstr "Lot Adı"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/OrderWidget.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Lot/Serial Number(s) Required"
msgstr "Lot/Seriya Nömrəsi(ləri) Tələb Olunur"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__pack_lot_ids
msgid "Lot/serial Number"
msgstr "Lot/seriya Nömrəsi"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_loyalty
msgid "Loyalty Program"
msgstr "Loyallıq Proqramı"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Loyalty program to use for this point of sale."
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.magnetic_board
#: model:product.template,name:point_of_sale.magnetic_board_product_template
msgid "Magnetic Board"
msgstr "Məgnitli Lövhə"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_main_attachment_id
msgid "Main Attachment"
msgstr "Əsas Əlavə"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
msgid "Make Payment"
msgstr "Ödəniş edin."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__available_pricelist_ids
msgid ""
"Make several pricelists available in the Point of Sale. You can also apply a"
" pricelist to specific customers from their contact form (in Sales tab). To "
"be valid, this pricelist must be listed here as an available pricelist. "
"Otherwise the default pricelist will apply."
msgstr ""
"Satış Nöqtəsi üçün  bir neçə qiymət siyahısını aktivləşdirin. Siz həmçinin "
"hər müştəri üçün onlara məxsus qiymət siyahısını Kontakt məlumatlarından "
"(Satış bölməsində) təyin edə bilərsiniz. Etibarlı olmaq üçün həmin qiymət "
"siyahısı Satıq Nöqtəsi üçün də aktiv olmalıdır. Əks təqdirdə standart qiymət"
" siyahısı tətbiq ediləcəkdir. "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid ""
"Make sure you are using IoT Box v18.12 or higher. Navigate to %s to accept "
"the certificate of your IoT Box."
msgstr ""
"İoT Qurğu v18.12 və ya daha yüksək versiyasını istifadə etdiyinizdən əmin "
"olun. İoT Qurğu sertifikatını qəbul etmək üçün %s ünvanına keçin."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Manage gift card"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Manage gift card."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Manage promotion &amp; coupon programs"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Manage promotion and coupon programs."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__manual_discount
msgid "Manual Discounts"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__margin
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__margin
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__margin
msgid "Margin"
msgstr "Marja"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__margin_percent
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__margin_percent
msgid "Margin (%)"
msgstr "Marja (%)"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Margin:"
msgstr "Marja:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "Maximum Exceeded"
msgstr "Maksimum Aşılıb"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Maximum value reached"
msgstr "Maksimum dəyərə çatıldı"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_has_error
msgid "Message Delivery error"
msgstr "Mesajın Çatdırılmasında xəta"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_ids
msgid "Messages"
msgstr "Mesajlar"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__name
msgid "Method"
msgstr "Metod"

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_miscellaneous
msgid "Miscellaneous"
msgstr "Müxtəlif"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_hr
msgid "Module Pos Hr"
msgstr "Pos Hr Modulu "

#. module: point_of_sale
#: model:product.product,name:point_of_sale.monitor_stand
#: model:product.template,name:point_of_sale.monitor_stand_product_template
msgid "Monitor Stand"
msgstr "Monitor Dayağı"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductScreen.xml:0
#, python-format
msgid "More..."
msgstr "Daha çox..."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Mənim Fəaliyyətlərimin Son Tarixi "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "My Sessions"
msgstr "Mənim Sessiyalarım"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__name
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Name"
msgstr "Ad"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Need customer to invoice"
msgstr "Fakturalama üçün müştəri tələb olunur "

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Need loss account for the following journals to post the lost amount: %s\n"
msgstr ""
"Aşağıdakı jurnallar üçün itirilmiş məbləği yerləşdirmək üçün zərər hesabı "
"lazımdır: %s\n"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Need profit account for the following journals to post the gained amount: %s"
msgstr ""
"Aşağıdakı jurnallar üçün qazanılan məbləği yerləşdirmək üçün mənfəət hesabı "
"lazımdır: %s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ProductInfoPopup.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductsWidgetControlPanel.js:0
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Network Error"
msgstr "Şəbəkə səhvi"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__draft
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__draft
msgid "New"
msgstr "Yeni"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "New Order"
msgstr "Yeni Sifariş"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "New Session"
msgstr "Yeni Seans"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.newspaper_rack
#: model:product.template,name:point_of_sale.newspaper_rack_product_template
msgid "Newspaper Rack"
msgstr "Qəzet rəfi"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Növbəti Fəaliyyət Təqvimi Tədbiri"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Növbəti Fəaliyyətin Son Tarixi"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_summary
msgid "Next Activity Summary"
msgstr "Növbəti Fəaliyyət Xülasəsi"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_type_id
msgid "Next Activity Type"
msgstr "Yeni Fəaliyyət Növü"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Next Order List"
msgstr "Növbəti Sifariş Siyahısı"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "No"
msgstr "Tapılmayanlar"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "No Taxes"
msgstr "Vergi yoxdur"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"No cash statement found for this session. Unable to record returned cash."
msgstr ""
"Bu sessiya üçün heç bir pul bəyanatı tapılmadı. Qaytarılan pulun qeydə "
"alınması mümkün deyil."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientListScreen.js:0
#, python-format
msgid "No customer found"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_sale_graph
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all_filtered
msgid "No data yet!"
msgstr "Hələ məlumat yoxdur!"

#. module: point_of_sale
#: code:addons/point_of_sale/report/pos_invoice.py:0
#, python-format
msgid "No link to an invoice for %s."
msgstr " %s Hesab üçün heç bir faktura bağlantısı yoxdur."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_form
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_pos_form
#, python-format
msgid "No orders found"
msgstr "Sifariş tapılmadı"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductsWidgetControlPanel.js:0
#, python-format
msgid "No product found"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductList.xml:0
#, python-format
msgid "No results found for \""
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/wizard/pos_open_statement.py:0
#, python-format
msgid "No sequence defined on the journal"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session
msgid "No sessions found"
msgstr "Heç bir sessiya tapılmadı."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/SetFiscalPositionButton.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "None"
msgstr "Heçbiri"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Not Invoiced"
msgstr "Fakturalandırılmamış"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashOpeningPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Notes"
msgstr "Qeydlər"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_needaction_counter
msgid "Number of Actions"
msgstr "Hərəkətlərin sayı"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__number_of_opened_session
msgid "Number of Opened Session"
msgstr "Açıq sessiyaların sayı"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Number of Partners Loaded"
msgstr "Yüklənmiş Tərəfdaşların Sayı"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__nb_print
msgid "Number of Print"
msgstr "Çapın Nömrəsi "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Number of Products Loaded"
msgstr "Yüklənən məhsulların sayı"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__refund_orders_count
msgid "Number of Refund Orders"
msgstr "Qaytarılan sifarişlərin sayı"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_has_error_counter
msgid "Number of errors"
msgstr "Xətaların sayı"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__refunded_qty
msgid "Number of items refunded in this orderline."
msgstr "Bu sifariş sətrində qaytarılan məhsulların sayı."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Əməliyyat tələb edən mesajların sayı"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Çatdırılma xətası olan mesajların sayı"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "Number of partners loaded can not be 0"
msgstr "Yüklənmiş tərəfdaşların sayı 0 ola bilməz."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "Number of product loaded can not be 0"
msgstr "Yüklənmiş məhsul sayı 0 ola bilməz."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_unread_counter
msgid "Number of unread messages"
msgstr "Oxunmamış mesajların sayı"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashOpeningPopup.xml:0
#, python-format
msgid "OPENING CASH CONTROL"
msgstr "Açılış Nəğd Yoxlanışı "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ProxyStatus.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientListScreen.js:0
#, python-format
msgid "Offline"
msgstr "Oflayn"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/OfflineErrorPopup.js:0
#, python-format
msgid "Offline Error"
msgstr "Offline Səhv"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Offline Orders"
msgstr "Offline Sifarişlər"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ConfirmPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/EditListPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorBarcodePopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorTracebackPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/NumberPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/OfflineErrorPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/OrderImportPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/TextAreaPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/TextInputPopup.js:0
#: code:addons/point_of_sale/static/src/xml/Popups/EditListPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ErrorBarcodePopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/OfflineErrorPopup.xml:0
#, python-format
msgid "Ok"
msgstr "OK"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "Ongoing"
msgstr "Davamlı"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid ""
"Only a negative quantity is allowed for this refund line. Click on +/- to "
"modify the quantity to be refunded."
msgstr ""
"Bu qaytarma sətri üçün sadəcə mənfi say daxil edə bilərsiz. Qaytarılacaq "
"miqdarı dəyişdirmək üçün +/- düyməsinə basın."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__only_round_cash_method
msgid "Only apply rounding on cash"
msgstr "Yalnızca nəğd üçün yuvarlaqlaşdırma tətbiq edin."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_payment_method.py:0
#, python-format
msgid ""
"Only journals of type 'Cash' or 'Bank' could be used with payment methods."
msgstr ""
"Yalnız \"Nəğd\" və ya \"Bank\" tipindəki jurnallar Ödəniş metodları ilə "
"istifadə edilə bilər."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Only load a limited number of customers at the opening of the PoS."
msgstr "PoS-un açılışında yalnız məhdud sayda müştəri yükləyin."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Only load most common products at the opening of the PoS."
msgstr "PoS-un açılışında yalnızca ən tanınmış məhsulları yükləyin."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Only on cash methods"
msgstr "Yalnızca nağd üsullarla."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__restrict_price_control
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Only users with Manager access rights for PoS app can modify the product "
"prices on orders."
msgstr ""
"Yalnızca PoS tətbiqinin Menecer giriş hüquqlarına malik olan istifadəçilər "
"sifarişlərdə məhsul qiymətlərini dəyişdirə bilər."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientDetailsEdit.js:0
#, python-format
msgid "Only web-compatible Image formats such as .png or .jpeg are supported."
msgstr "Yalnız .png və ya .jpeg kimi yalnız veb şəkil formatları dəstəklənir."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Open Cashbox"
msgstr "Nəğd Kassasını Aç"

#. module: point_of_sale
#: model:ir.actions.client,name:point_of_sale.action_client_pos_menu
msgid "Open POS Menu"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__open_session_ids
msgid "Open PoS sessions that are using this payment method."
msgstr "Bu ödəniş üsulunu istifadə edən PoS sessiyalarını aç"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Open Session"
msgstr "Açıq sessiya"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashOpeningPopup.xml:0
#, python-format
msgid "Open session"
msgstr "Açıq sessiya"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashOpeningPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Open the money details popup"
msgstr "Pul detalları üçün Popup Aç "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__user_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Opened By"
msgstr "Açan"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "Opened Sessions"
msgstr "Açılmış sessiyalar"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Opened by"
msgstr "Açılış edən "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Opening"
msgstr "Açılır"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__opening_control
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Opening Control"
msgstr "Açılış Yoxlaması"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__start_at
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Opening Date"
msgstr "Tarix Açılır"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__opening_notes
msgid "Opening Notes"
msgstr "Açılış Qeydləri"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashOpeningPopup.xml:0
#, python-format
msgid "Opening cash"
msgstr "Açılışdakı Nəğd "

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.account_cashbox_line_action
msgid "Opening/Closing Values"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__picking_type_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_type_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Operation Type"
msgstr "Əməliyyat növü"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Operation type used to record product pickings <br/>\n"
"                                    Products will be taken from the default source location of this operation type"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Operation types show up in the Inventory dashboard."
msgstr "İnventar panosunda görünən Əməliyyat növləri "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ScaleScreen/ScaleScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__pos_order_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__order_id
#, python-format
msgid "Order"
msgstr "Sifariş"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Order %s"
msgstr "Sifariş %s"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Order %s is not fully paid."
msgstr "Sifariş %s tam ödənilməyib."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__order_count
msgid "Order Count"
msgstr "Sifariş Sayı"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__date
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Order Date"
msgstr "Sifariş Tarixi"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__sequence_id
msgid "Order IDs Sequence"
msgstr "Sifariş İD-ləri Sırası"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__sequence_line_id
msgid "Order Line IDs Sequence"
msgstr "Sifariş Sətir İD-lərin Sırası"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__lines
msgid "Order Lines"
msgstr "Sifariş xətləri"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__order_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__order_id
msgid "Order Ref"
msgstr "Sifariş  Rəyi"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__sequence_number
msgid "Order Sequence Number"
msgstr "Sifariş Sıra Nömrəsi"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#, python-format
msgid "Order is empty"
msgstr "Sifariş boşdur."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Order is not synced. Check your internet connection"
msgstr "Sifariş sinxronlaşdırılmır. İnternet bağlantınızı yoxlayın."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Order lines"
msgstr "Sifariş sətrləri"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__refund_orderline_ids
msgid "Orderlines in this field are the lines that refunded this orderline."
msgstr "Bu sahədəki sifariş sətrləri, bu sifariş sətrini qaytaran sətrlərdir."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/TicketButton.xml:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_filtered
#: model:ir.actions.act_window,name:point_of_sale.action_pos_pos_form
#: model:ir.actions.act_window,name:point_of_sale.action_pos_sale_graph
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__order_ids
#: model:ir.ui.menu,name:point_of_sale.menu_point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_ofsale
#: model:ir.ui.menu,name:point_of_sale.menu_report_pos_order_all
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Orders"
msgstr "Sifarişlər"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_order_all
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_order_all_filtered
msgid "Orders Analysis"
msgstr "Sifarişlərin Analizi"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__other_devices
msgid "Other Devices"
msgstr "Digər Cihazlar"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Other Information"
msgstr "Əlavə Məlumat"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Others"
msgstr "Digər "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__outstanding_account_id
msgid "Outstanding Account"
msgstr "Gözləmə Hesabı"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_procurement_group__pos_order_id
msgid "POS Order"
msgstr "POS Sifariş"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "POS Order %s"
msgstr "POS Sifariş %s"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line_form
msgid "POS Order line"
msgstr "POS Sifariş sətri"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "POS Order lines"
msgstr "POS Sifariş sətrləri"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
msgid "POS Orders"
msgstr "POS Sifarişləri"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree_all_sales_lines
msgid "POS Orders lines"
msgstr "POS Sifariş sətrləri"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_payment_method_id
msgid "POS Payment Method"
msgstr "POS Ödəniş Üsulu"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_product_tree_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_search_view_pos
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_tree_view
msgid "POS Product Category"
msgstr "POS Məhsul Kateqoriyası"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_digest_digest__kpi_pos_total
msgid "POS Sales"
msgstr "POS Satışları"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_session_id
msgid "POS Session"
msgstr "POS Sessiya"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "POS error"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "POS order line %s"
msgstr "POS sifariş sətri %s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_paid
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__paid
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__paid
#, python-format
msgid "Paid"
msgstr "Ödənilib"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__parent_id
msgid "Parent Category"
msgstr "Əsas Kateqoriya"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Partner"
msgstr "Tərəfdaş"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__partner_load_background
msgid "Partner Load Background"
msgstr "Partner Yükləmə Arxa Planı"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Misc/MobileOrderWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#, python-format
msgid "Pay"
msgstr "Ödəyin"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
msgid "Pay Order"
msgstr "Ödəniş Sifarişi"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductScreen.xml:0
#: code:addons/point_of_sale/wizard/pos_payment.py:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Payment"
msgstr "Ödəniş"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_date
msgid "Payment Date"
msgstr "Ödəniş Tarixi"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_method_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_method_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#, python-format
msgid "Payment Method"
msgstr "Ödəniş Üsulu"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment_method_form
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__payment_method_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__payment_method_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_payment_method
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_tree
msgid "Payment Methods"
msgstr "Ödəniş Üsulları"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__ticket
msgid "Payment Receipt Info"
msgstr "Ödəniş Qəbzi Məlumatı"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_name
msgid "Payment Reference"
msgstr "Ödənişin İstinad Nömrəsi"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_status
msgid "Payment Status"
msgstr "Ödəniş Statusu"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Payment Successful"
msgstr "Ödəniş uğurludur."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Payment Terminals"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__transaction_id
msgid "Payment Transaction ID"
msgstr "Ödəniş Əməliyyatı İD"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Payment methods available"
msgstr "Mövcud ödəniş üsulları"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Payment request pending"
msgstr "Ödəniş tələbi gözləyir"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Payment reversed"
msgstr "Ödəniş geri qaytarıldı"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment_form
#: model:ir.model,name:point_of_sale.model_account_payment
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__payment_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_payment
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_tree
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Payments"
msgstr "Ödənişlər"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_payment_methods_tree
msgid "Payments Methods"
msgstr "Ödəniş üsulları"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Payments in"
msgstr "Ödənişlər "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/SaleDetailsReport.xml:0
#, python-format
msgid "Payments:"
msgstr "Ödənişlər:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Pending Electronic Payments"
msgstr "Gözlənilən Elektron Ödənişlər"

#. module: point_of_sale
#: model:ir.filters,name:point_of_sale.filter_orders_per_session
msgid "Per session"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__user_id
msgid ""
"Person who uses the cash register. It can be a reliever, a student or an "
"interim employee."
msgstr ""
"Kassadan  istifadə edən şəxs. Bu bir əvəzçi, tələbə və ya müvəqqəti işçi ola"
" bilər."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Phone"
msgstr "Telefon"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Pick which product categories are available"
msgstr "Mövcud olan məhsul kateqoriyalarını seçin."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__picking_ids
msgid "Picking"
msgstr "Seçim etmək"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_count
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__picking_count
msgid "Picking Count"
msgstr "Seçim Sayı"

#. module: point_of_sale
#: code:addons/point_of_sale/models/stock_warehouse.py:0
#, python-format
msgid "Picking POS"
msgstr "POSun seçilməsi "

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Pickings"
msgstr "Seçimlər"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Picture"
msgstr "Şəkil"

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.fabric_attribute_plastic
msgid "Plastic"
msgstr "Plastik"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Please Confirm Large Amount"
msgstr "Böyük məbləği təsdiqləyin."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/printers.js:0
#: code:addons/point_of_sale/static/src/js/printers.js:0
#, python-format
msgid "Please check if the IoT Box is still connected."
msgstr "Lütfən, IoT Qurğusunun  qoşulub-qoşulmadığını yoxlayın."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/printers.js:0
#, python-format
msgid "Please check if the printer is still connected."
msgstr "Zəhmət olmasa printerin qoşulu olub-olmadığını yoxlayın."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/printers.js:0
#, python-format
msgid ""
"Please check if the printer is still connected. \n"
"Some browsers don't allow HTTP calls from websites to devices in the network (for security reasons). If it is the case, you will need to follow Odoo's documentation for 'Self-signed certificate for ePOS printers' and 'Secure connection (HTTPS)' to solve the issue"
msgstr ""
"Zəhmət olmasa printerin hələ də bağlı olub-olmadığını yoxlayın. \n"
"Bəzi brauzerlər şəbəkədəki cihazlara veb saytlardan HTTP çağırışlarına icazə vermir (təhlükəsizlik səbəbindən). Bu halda, məsələni həll etmək üçün Odoo-nun 'ePOS printerlər üçün manual imzalanan sertifikat' və 'Təhlükəsiz əlaqə (HTTPS)' üçün sənədlərinə nəzər salmalısınız."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#, python-format
msgid "Please check your internet connection and try again."
msgstr "Internet bağlantınızı yoxlayın və yenidən cəhd edin."

#. module: point_of_sale
#: code:addons/point_of_sale/models/res_company.py:0
#, python-format
msgid ""
"Please close all the point of sale sessions in this period before closing "
"it. Open sessions are: %s "
msgstr ""
"Lütfən, qapanışdan əvvəl bu dövr ərzində bütün satış nöqtəsi sessiyalarını "
"bağlayın. Açıq sessiyalar: %s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Please configure a payment method in your POS."
msgstr "Zəhmət olmasa POS-unuzda bir ödəniş üsulu təyin edin "

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Please define income account for this product: \"%s\" (id:%d)."
msgstr "Xahiş olunur, bu məhsul üçün gəlir hesabını təyin edin: \"%s\" (id:%d)."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "Please print the invoice from the backend"
msgstr "Zəhmət olmasa fakturanı backenddən çap edin."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Please provide a partner for the sale."
msgstr "Zəhmət olmasa, satış üçün bir partnoy təyin edin."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenStatus.xml:0
#, python-format
msgid "Please select a payment method."
msgstr "Zəhmət olmasa bir ödəniş üsulunu seçin."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Please select the Customer"
msgstr "Zəhmət olmasa Müştəri seçin."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__pos_categ_id
msgid "PoS Category"
msgstr "PoS Kateqoriya"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "PoS Interface"
msgstr "PoS İnterfeysi"

#. module: point_of_sale
#: code:addons/point_of_sale/models/stock_warehouse.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_property_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_pivot
#, python-format
msgid "PoS Orders"
msgstr "PoS Sifarişləri"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_pos_category_action
#: model:ir.ui.menu,name:point_of_sale.menu_products_pos_category
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "PoS Product Categories"
msgstr "PoS Məhsul Kateqoriyaları"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_tree_view
msgid "PoS Product Category"
msgstr "PoS Məhsul Kateqoriyası"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_property_form
msgid "Point Of Sale"
msgstr "Satış Nöqtəsi"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_config_kanban
#: model:ir.actions.act_window,name:point_of_sale.action_pos_config_pos
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__config_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__config_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__config_id
#: model:ir.ui.menu,name:point_of_sale.menu_point_root
#: model:ir.ui.menu,name:point_of_sale.menu_pos_config_pos
#: model_terms:ir.ui.view,arch_db:point_of_sale.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_account_journal_pos_user_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
#, python-format
msgid "Point of Sale"
msgstr "Satış məntəqəsi"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_pos_order_view_tree
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_graph
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_pivot
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Point of Sale Analysis"
msgstr "Satış Nöqtəsi Analizi"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_category
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__pos_categ_id
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__pos_categ_id
msgid "Point of Sale Category"
msgstr "Satış Nöqtəsi Kateqoriyası"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_search
msgid "Point of Sale Config"
msgstr "Satış Nöqtəsi konfiqurasiyası"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_config
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__config_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_tree
msgid "Point of Sale Configuration"
msgstr "Satış Nöqtəsi Konfiqurasiyası"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__config_ids
msgid "Point of Sale Configurations"
msgstr "Satış Nöqtəsi Konfiqurasiyaları"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_point_of_sale_report_saledetails
msgid "Point of Sale Details"
msgstr "Satış Nöqtəsi Detalları"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_details_wizard
msgid "Point of Sale Details Report"
msgstr "Satış Nöqtəsi Detalları Hesabatı"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_point_of_sale_report_invoice
msgid "Point of Sale Invoice Report"
msgstr "Satış Nöqtəsi Faktura Hesabatı"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__journal_id
msgid "Point of Sale Journal"
msgstr "Satış Nöqtəsi Jurnalı"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_make_payment
msgid "Point of Sale Make Payment Wizard"
msgstr "Satış Nöqtəsi Ödəniş Sehrbazı"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__group_pos_manager_id
msgid "Point of Sale Manager Group"
msgstr "Satış Nöqtəsi Menecer Qrupu"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_stock_warehouse__pos_type_id
msgid "Point of Sale Operation Type"
msgstr "Satış Nöqtəsi Əməliyyat Növü"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "Satış Nöqtəsi Sifariş Sətrləri"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_order
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Point of Sale Orders"
msgstr "Satış Nöqtəsi Sifarişləri"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_pos_order
msgid "Point of Sale Orders Report"
msgstr "Satış Nöqtəsi Sifarişlər Hesabatı"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_payment_method
#: model:ir.model.fields,field_description:point_of_sale.field_account_journal__pos_payment_method_ids
msgid "Point of Sale Payment Methods"
msgstr "Satış Nöqtəsi Ödəniş Üsulları"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_payment
msgid "Point of Sale Payments"
msgstr "Satış Nöqtəsi Ödənişləri"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_session
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_tree
msgid "Point of Sale Session"
msgstr "Satış Nöqtəsi Sessiyası"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.qunit_suite
msgid "Point of Sale Tests"
msgstr "Satış Nöqtəsi Testləri"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__group_pos_user_id
msgid "Point of Sale User Group"
msgstr "Satış Nöqtəsi İstifadəçi Qrupu"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__pos_config_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__pos_config_ids
msgid "Pos Config"
msgstr "Pos Konfiq"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_move__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_partner__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_users__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_stock_picking__pos_order_id
msgid "Pos Order"
msgstr "Pos Sifariş"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_partner__pos_order_count
#: model:ir.model.fields,field_description:point_of_sale.field_res_users__pos_order_count
msgid "Pos Order Count"
msgstr "Pos Sifariş Sayı"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__pos_order_line_id
msgid "Pos Order Line"
msgstr "Pos Sifariş Sətri"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_payment_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_move__pos_payment_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_payment_ids
msgid "Pos Payment"
msgstr "Pos Ödəniş"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_form_view
msgid "Pos Product Categories"
msgstr "Pos Məhsul Kateqoriyaları"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_stock_picking__pos_session_id
msgid "Pos Session"
msgstr "Pos Sessiya"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_duration
msgid "Pos Session Duration"
msgstr "Pos Sessiya Müddəti"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_state
msgid "Pos Session State"
msgstr "Pos Sessiya Vəziyyəti"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_username
msgid "Pos Session Username"
msgstr "Pos Sessiya İstifadəçi adı"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__open_session_ids
msgid "Pos Sessions"
msgstr "Pos Sessiyaları"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_posbox
msgid "PosBox"
msgstr "PosBox"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Positive quantity not allowed"
msgstr "Müsbət miqdar icazə verilmir"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Postcode"
msgstr "Post kodu"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__done
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__done
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Posted"
msgstr "Göndərildi"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Previous Order List"
msgstr "Əvvəlki Sifariş Siyahısı"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/CustomerFacingDisplay/CustomerFacingDisplayOrder.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/NumpadWidget.xml:0
#, python-format
msgid "Price"
msgstr "Qiymət"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Price Control"
msgstr "Qiymət nəzarəti"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Price Unit"
msgstr "Qiymət Vahidi"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Price discount from %s -> %s"
msgstr "Qiymət endirimi %s -> %s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Price excl. VAT:"
msgstr "ƏDV-siz qiymət:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/SetPricelistButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/SetPricelistButton.xml:0
#, python-format
msgid "Price list"
msgstr "Qiymət siyahısı"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__price
msgid "Priced Product"
msgstr "Qiymətləndirilmiş məhsul"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/SetPricelistButton.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__pricelist_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__pricelist_id
#, python-format
msgid "Pricelist"
msgstr "Qiymət siyahısı"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_action_product_pricelist
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "Qiymət siyahıları"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Pricing"
msgstr "Qiymətləndirmə"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SaleDetailsButton.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
#, python-format
msgid "Print"
msgstr "Çap edin"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/ControlButtons/ReprintReceiptButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/ReprintReceiptScreen.xml:0
#, python-format
msgid "Print Receipt"
msgstr "Qəbzi Çap Et"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SaleDetailsButton.xml:0
#, python-format
msgid "Print a report with all the sales of the current PoS Session"
msgstr "Cari PoS sessiyasının bütün satışlarını bir hesabatda çap edin."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Print invoices on customer request"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Print receipts automatically once the payment is registered"
msgstr "Ödəniş qeydə alındıqdan sonra qəbzləri avtomatik olaraq çap edin."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_via_proxy
msgid "Print via Proxy"
msgstr "Proxy vasitəsilə çap et."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ProxyStatus.js:0
#, python-format
msgid "Printer"
msgstr "Printer"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Misc/AbstractReceiptScreen.js:0
#, python-format
msgid "Printing is not supported on some browsers"
msgstr "Bəzi brauzerlərdə çap etmək dəstəklənmir."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Misc/AbstractReceiptScreen.js:0
#, python-format
msgid ""
"Printing is not supported on some browsers due to no default printing "
"protocol is available. It is possible to print your tickets by making use of"
" an IoT Box."
msgstr ""
"Bəzi brauzerlər çapı dəstəkləmir, çünki standart çap etmə protokolu mövcud "
"deyil. Bir IoT Qurğusundan istifadə edərək biletinizi çap edə bilərsiniz."

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_procurement_group
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__procurement_group_id
msgid "Procurement Group"
msgstr "Satınalma qrupu"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_product
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__product_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__product_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Product"
msgstr "Məhsul"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_categ_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Product Category"
msgstr "Məhsul Kateqoriyası"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__product_configurator
msgid "Product Configurator"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__product_load_background
msgid "Product Load Background"
msgstr "Məhsul Yükləmə Arxa Planı"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Product Prices"
msgstr "Məhsul Qiymətləri"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_tree_view
msgid "Product Product Categories"
msgstr "Məhsul Məhsul Kateqoriyaları"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_qty
msgid "Product Quantity"
msgstr "Məhsulun Miqdarı"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_template
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_tmpl_id
msgid "Product Template"
msgstr "Məhsul Şablonu"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Məhsulun Ölçü Vahidi"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__product_uom_id
msgid "Product UoM"
msgstr "Məhsul UoM"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_uom_category
msgid "Product UoM Categories"
msgstr "Məhsul UoM Kateqoriyaları"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_product_action
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_action_product_product
msgid "Product Variants"
msgstr "Məhsul Çeşidləri"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Product information"
msgstr "Məhsul məlumatı"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductsWidgetControlPanel.js:0
#, python-format
msgid ""
"Product is not loaded. Tried loading the product from the server but there "
"is a network error."
msgstr ""
"Məhsul yüklənmir. Məhsulu serverdən yükləməyə çalışdım, lakin şəbəkə xətası "
"baş verdi."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Product prices on receipts"
msgstr "Qəbzlərdəki məhsul qiymətləri "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_tipproduct
msgid "Product tips"
msgstr "Məhsul məsləhətləri"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_template_action_pos_product
#: model:ir.ui.menu,name:point_of_sale.menu_pos_products
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_catalog
#: model:ir.ui.menu,name:point_of_sale.pos_menu_products_configuration
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Products"
msgstr "Məhsullar"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#, python-format
msgid "Proxy Connected"
msgstr "Proxy Qoşuldu"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#, python-format
msgid "Proxy Disconnected"
msgstr "Proxy Qırıldı"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#, python-format
msgid "Proxy Warning"
msgstr "Proxy Xəbərdarlığı"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/NumpadWidget.xml:0
#, python-format
msgid "Qty"
msgstr "Sayı"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/CustomerFacingDisplay/CustomerFacingDisplayOrder.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__qty
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Quantity"
msgstr "Miqdar"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#, python-format
msgid "REASON"
msgstr "SƏBƏB"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Read Weighing Scale"
msgstr "Tərəzini oxuyun."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/tours/point_of_sale.js:0
#: code:addons/point_of_sale/static/src/js/tours/point_of_sale.js:0
#, python-format
msgid "Ready to launch your <b>point of sale</b>?"
msgstr " <b>Satış Nöqtəsi</b>başlamağa hazırsınızmı?"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMovePopup.xml:0
#, python-format
msgid "Reason"
msgstr "Səbəb"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "Receipt"
msgstr "Qəbz"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Receipt %s"
msgstr "Qəbz %s"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__receipt_footer
msgid "Receipt Footer"
msgstr "Qəbzin Altqeydi"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__receipt_header
msgid "Receipt Header"
msgstr "Qəbzin Başlığı"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__pos_reference
#, python-format
msgid "Receipt Number"
msgstr "Qəbzi Nömrəsi"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Receipt Printer"
msgstr "Qəbz Printeri"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__use_payment_terminal
msgid "Record payments with a terminal on this journal."
msgstr "Ödənişləri bu jurnalda terminal vasitəsilə qeyd edin."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__rescue
msgid "Recovery Session"
msgstr "Bərpa Sessiyası"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Refresh Display"
msgstr "Ekranı Yenilə "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/RefundButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/RefundButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/RefundButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Refund"
msgstr "Geri Ödəmə"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__refund_orderline_ids
msgid "Refund Order Lines"
msgstr "Sifariş Sətirlərini Qaytar"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Refund Orders"
msgstr "Geri ödəniş sifarişləri"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderlineDetails.xml:0
#, python-format
msgid "Refunded"
msgstr "Qaytarıldı"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__refunded_order_ids
msgid "Refunded Order"
msgstr "Qaytarılan Sifariş"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__refunded_orderline_id
msgid "Refunded Order Line"
msgstr "Qaytarılan Sifariş Sətiri"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Refunded Orders"
msgstr "Qaytarılan sifarişlər"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__refunded_orders_count
msgid "Refunded Orders Count"
msgstr "Qaytarılan sifarişlərin sayı"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__refunded_qty
msgid "Refunded Quantity"
msgstr "Geri qaytarılan miqdar"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/OrderlineDetails.js:0
#, python-format
msgid "Refunding %s in "
msgstr " %s üçün qaytarma"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Refunds"
msgstr "Geri Ödəmələr"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenStatus.xml:0
#, python-format
msgid "Remaining"
msgstr "Qalıb"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Remaining unsynced orders"
msgstr "Qalıq sinxronlaşdırılmamış sifarişlər "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/EditListInput.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/EditListInput.xml:0
#, python-format
msgid "Remove"
msgstr "Sil"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Replenishment"
msgstr "Təchizat "

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_rep
msgid "Reporting"
msgstr "Hesabatlıq"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Reprint Invoice"
msgstr "Fakturanın yenidən çap edilməsi"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Request sent"
msgstr "Tələb Göndərildi "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Reset"
msgstr "Sıfırla"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__user_id
msgid "Responsible"
msgstr "Məsul"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_user_id
msgid "Responsible User"
msgstr "Məsul İstifadəçi"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__restrict_price_control
msgid "Restrict Price Modifications to Managers"
msgstr "Menecerlərə Qiymət Dəyişikliklərini Məhdudlaşdırın"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__limit_categories
msgid "Restrict Product Categories"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Restrict price modification to managers"
msgstr "Menecerlərə qiymət dəyişikliyinin məhdudlaşdırılması"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Retry"
msgstr "Yenidən cəhd edin"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Return Products"
msgstr "Məhsulları qaytarın."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_return
msgid "Returned"
msgstr "Qaytarıldı"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Reversal of: %s"
msgstr "%s qaytarılması"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Reversal request sent to terminal"
msgstr "Terminalə qaytarma tələbi göndərildi"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Reverse"
msgstr "Ləğv edin"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Reverse Payment"
msgstr "Geri ödəniş"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Misc/MobileOrderWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Review"
msgstr "Yenidən nəzərdən keçirin"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Rounding"
msgstr "Yuvarlaqlaşdırmaq"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Rounding Method"
msgstr "Yuvarlaqlaşdırma Metodu"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Rounding error in payment lines"
msgstr "Ödəniş sətrlərində yuvarlaqlaşdırma səhvi"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/debug_manager.js:0
#, python-format
msgid "Run Point of Sale JS Tests"
msgstr "Satış Nöqtəsi JS Testlərini İcra Et"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS-in Çatdırılmasında xəta"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "SN"
msgstr "SN"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__nbr_lines
msgid "Sale Line Count"
msgstr "Satış Sətir Sayı"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line_day
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line_form
msgid "Sale line"
msgstr "Satış sətri"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_details
#: model:ir.actions.report,name:point_of_sale.sale_details_report
#: model:ir.ui.menu,name:point_of_sale.menu_report_order_details
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
msgid "Sales Details"
msgstr "Satış Detalları"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__sale_journal
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Sales Journal"
msgstr "Satış Jurnalı"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Save"
msgstr "Yadda Saxla"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Save this page and come back here to set up the feature."
msgstr "Bu səhifəni yadda saxla və parametri quraşdırmaq üçün geri qayıt."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ProxyStatus.js:0
#, python-format
msgid "Scale"
msgstr "Tərəzi"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Scan"
msgstr "Skan"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Scan EAN-13"
msgstr "EAN-13 Skan et"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_scan_via_proxy
msgid "Scan via Proxy"
msgstr "Proxy vasitəsilə Skan"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ProxyStatus.js:0
#, python-format
msgid "Scanner"
msgstr "Oxuyucu"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Search Customers"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Search Orders..."
msgstr "Sifarişlərdə Axtar..."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductsWidgetControlPanel.xml:0
#, python-format
msgid "Search Products..."
msgstr "Məhsullarda Axtar..."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Search Sales Order"
msgstr "Satış Sifarişlərində Axtar "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/SelectionPopup.js:0
#, python-format
msgid "Select"
msgstr "Seçin"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/SetFiscalPositionButton.js:0
#, python-format
msgid "Select Fiscal Position"
msgstr "Maliyyə Vəziyyətini Seçin"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#, python-format
msgid "Select an order"
msgstr "Bir sifariş seçin"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/CashMovePopup.js:0
#, python-format
msgid "Select either Cash In or Cash Out before confirming."
msgstr "Təsdiqləmədən əvvəl Nəğd Daxilolma və ya Nəğd Xaricolmanı seçin"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Select product attributes"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/HomeCategoryBreadcrumb.js:0
#, python-format
msgid "Select the category"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/SetPricelistButton.js:0
#, python-format
msgid "Select the pricelist"
msgstr "Qiymət siyahısını seçin."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#, python-format
msgid "Select the product(s) to refund and set the quantity"
msgstr "Geri qaytarılacaq məhsulları seçin və miqdarını təyin edin."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__selectable_categ_ids
msgid "Selectable Categ"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Sell products and deliver them later."
msgstr "Məhsulları sat və sonra çatdır."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Send"
msgstr "Göndərin"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Send Payment Request"
msgstr "Ödəniş Tələbi Göndər"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ErrorTracebackPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ErrorTracebackPopup.xml:0
#, python-format
msgid "Send by email"
msgstr "Emaillə Göndər"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ReceiptScreen/ReceiptScreen.js:0
#, python-format
msgid "Sending email failed. Please try again."
msgstr "E-poçt göndərmə uğursuz oldu. Xahiş edirik yenidən cəhd edin."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__sequence
msgid "Sequence"
msgstr "Ardıcıllıq"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__sequence_number
msgid "Sequence Number"
msgstr "Sıra Nömrəsi"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/EditListInput.xml:0
#, python-format
msgid "Serial/Lot Number"
msgstr "Seriya/Lot Nömrəsi"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Served by"
msgstr "Xidmət edən"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "Server Error"
msgstr "Server Xətası "

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement__pos_session_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__session_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__session_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__session_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#, python-format
msgid "Session"
msgstr "Seans"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__name
msgid "Session ID"
msgstr "Sessiya ID"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__session_move_id
msgid "Session Journal Entry"
msgstr "Sessiya Jurnalı Qeydiyyatı"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "Session ids:"
msgstr "Sessiya İD ləri:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Session is closed"
msgstr ""

#. module: point_of_sale
#: model:mail.activity.type,name:point_of_sale.mail_activity_old_session
msgid "Session open over 7 days"
msgstr "Sessiya 7 gündən artıq açıqdır "

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_session
#: model:ir.actions.act_window,name:point_of_sale.action_pos_session_filtered
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__session_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_session_all
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Sessions"
msgstr "Seanslar"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientListScreen.js:0
#, python-format
msgid "Set Customer"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__set_maximum_difference
msgid "Set Maximum Difference"
msgstr "Maksimum Fərq Təyin Et"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Set Weight"
msgstr "Ağırlığı təyin et"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Set a maximum difference allowed between the expected and counted money "
"during the closing of the session"
msgstr ""
"Sessiyanın bağlanması zamanı gözlənilən və sayılan pul arasında olan "
"maksimum fərqi təyin etsin."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__set_maximum_difference
msgid ""
"Set a maximum difference allowed between the expected and counted money "
"during the closing of the session."
msgstr ""
"Sessiyanın bağlanması zamanı gözlənilən və sayılan pul arasında olan fərqin "
"icazə verilən maksimum məbləğini təyin edin."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/SetFiscalPositionButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/SetFiscalPositionButton.xml:0
#, python-format
msgid "Set fiscal position"
msgstr "Mali vəziyyəti təyin et"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Set multiple prices per product, automated discounts, etc."
msgstr ""
"Bir məhsul üçün müxtəlif qiymətlər, avtomatik endirimlər və s. təyin edin."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Set of coins/bills that will be used in opening and closing control"
msgstr "Açılış və qapanış zamanı istifadə olunacaq qəpik/kağız pul dəsti"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Set shop-specific prices, seasonal discounts, etc."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Set the new quantity"
msgstr "Yeni miqdarı təyin edin."

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_configuration
#: model:ir.ui.menu,name:point_of_sale.menu_pos_global_settings
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Settings"
msgstr "Parametrlər"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__ship_later
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#, python-format
msgid "Ship Later"
msgstr "Daha sonra Göndər"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__picking_policy
msgid "Shipping Policy"
msgstr "Çatdırılma Siyasəti"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/OrderWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/OrderWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Shopping cart"
msgstr "Alış-veriş Səbəti"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_customer_facing_display_via_proxy
msgid "Show checkout to customers with a remotely-connected screen."
msgstr "Müştərilərə uzaqdan bağlı ekranla ödənişi göstərin."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Show customers checkout in a pop-up window. Can be moved to a second screen."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_customer_facing_display_local
msgid ""
"Show customers checkout in a pop-up window. Recommend to be moved to a "
"second screen visible to the client."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__module_pos_hr
msgid "Show employee login screen"
msgstr "İşçi login ekranını göstərin."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Six"
msgstr "Six"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_six
msgid "Six Payment Terminal"
msgstr "Six Ödəmə Terminalı"

#. module: point_of_sale
#: model:product.attribute,name:point_of_sale.size_attribute
msgid "Size"
msgstr "Ölçü"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Chrome.xml:0
#, python-format
msgid "Skip"
msgstr "Buraxın"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_skip_screen
msgid "Skip Preview Screen"
msgstr "Önizləmə Ekranını Atla"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/CategoryBreadcrumb.xml:0
#, python-format
msgid "Slash"
msgstr "Çəp Xətt"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.small_shelf
#: model:product.template,name:point_of_sale.small_shelf_product_template
msgid "Small Shelf"
msgstr "Kiçik rəf"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Some Cash Registers are already posted. Please reset them to new in order to close the session.\n"
"Cash Registers: %r"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Some Serial/Lot Numbers are missing"
msgstr "Bəzi Seriya/Lot Nömrələri Qeyd olunmayıb"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid ""
"Some orders could not be submitted to the server due to configuration "
"errors. You can exit the Point of Sale, but do not close the session before "
"the issue has been resolved."
msgstr ""
"Bəzi sifarişlər konfiqurasiya səhvləri səbəbindən serverə göndərilə bilmədi."
" Satış nöqtəsindən çıxa bilərsiniz, lakin məsələ həll edilmədən sessiyanı "
"bağlamayın."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid ""
"Some orders could not be submitted to the server due to internet connection "
"issues. You can exit the Point of Sale, but do not close the session before "
"the issue has been resolved."
msgstr ""
"Bəzi sifarişlər internet bağlantı problemindən dolayı serverə göndərilə "
"bilmədi. Satış Nöqtəsindən çıxa bilərsiniz, lakin məsələ həll edilmədən "
"əvvəl sessiyanı bağlamayın."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Some, if not all, post-processing after syncing order failed."
msgstr "Sifarişin sinxronizasiyasında  sonra bəzi əməliyyatlar uğursuz oldu."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Specific route"
msgstr "Xüsusi marşrut"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_pack_operation_lot
msgid "Specify product lot/serial number in pos order line"
msgstr "Məhsul lot/seriya nömrəsini POS sifariş sətrində göstərin."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__route_id
msgid "Spefic route for products delivered later."
msgstr "Sonradan çatdırılan məhsullar üçün xüsusi marşrut."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__start_category
msgid "Start Category"
msgstr "Başlanğıc Kateqoriyası"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__start_date
msgid "Start Date"
msgstr "Başlanğıc Tarixi"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Start selling from a default product category"
msgstr "Başlanğıcda standart məhsul kateqoriyasından satışa başlayın."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_start
msgid "Starting Balance"
msgstr "Başlanğıc Balansı"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "State"
msgstr "Dövlət"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__state
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__state
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__state
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#, python-format
msgid "Status"
msgstr "Status"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Fəaliyyətlərə əsaslanan status\n"
"Gecikmiş: Gözlənilən tarixdən keçib\n"
"Bu gün: Fəaliyyət tarixi bu gündür\n"
"Planlaşdırılıb: Gələcək fəaliyyətlər."

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_move
msgid "Stock Move"
msgstr "Stokun Hərəkəti"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_rule
msgid "Stock Rule"
msgstr "Stok Qaydaları"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__update_stock_at_closing
msgid "Stock should be updated at closing"
msgstr "Anbar bağlanış zamanı yenilənməlidir"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Street"
msgstr "Küçə"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_subtotal_incl
#, python-format
msgid "Subtotal"
msgstr "Yekun"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_subtotal
msgid "Subtotal w/o Tax"
msgstr "Vergisiz cəm"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__price_sub_total
msgid "Subtotal w/o discount"
msgstr "Endirimsiz cəm"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "Successfully imported"
msgstr "Uğurla import edildi"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/CashMoveButton.js:0
#, python-format
msgid "Successfully made a cash %s of %s."
msgstr "Uğurla %s məbləğində bir nağd %s qəbul edildi."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_balance_end
msgid "Sum of opening balance and transactions."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "Sum of subtotals"
msgstr "Məbləğlərin cəmi"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#, python-format
msgid "Synchronisation Connected"
msgstr "Sinxronizasiya Qoşuldu"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#, python-format
msgid "Synchronisation Connecting"
msgstr "Sinxronizasiya Qoşulur"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#, python-format
msgid "Synchronisation Disconnected"
msgstr "Sinxronizasiya Qırıldı"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#, python-format
msgid "Synchronisation Error"
msgstr "Sinxronlaşdırma Səhvi"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/CustomerFacingDisplay/CustomerFacingDisplayOrder.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "TOTAL"
msgstr "ÜMUMİ "

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_box_out
msgid "Take Money In/Out"
msgstr "Pul çıxarmaq/yükləmək"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/SetFiscalPositionButton.js:0
#: model:ir.model,name:point_of_sale.model_account_tax
#, python-format
msgid "Tax"
msgstr "Vergi"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Tax Amount"
msgstr "Vergi Məbləği"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_tax_included
msgid "Tax Display"
msgstr "Vergi Ekranı"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Tax ID"
msgstr "Vergi İD-si"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__tax_regime
msgid "Tax Regime"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__tax_regime_selection
msgid "Tax Regime Selection value"
msgstr "Vergi rejimi seçiminin dəyəri"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__iface_tax_included__subtotal
msgid "Tax-Excluded Price"
msgstr "Vergi Xaric Qiymət "

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__iface_tax_included__total
msgid "Tax-Included Price"
msgstr "Vergi Daxil Qiymət"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_tax
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__tax_ids
#: model:ir.ui.menu,name:point_of_sale.menu_action_tax_form_open
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Taxes"
msgstr "Vergilər"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__tax_ids_after_fiscal_position
msgid "Taxes to Apply"
msgstr "Tətbiq olunacaq Vergilər "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/SaleDetailsReport.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/OrderSummary.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#, python-format
msgid "Taxes:"
msgstr "Vergilər:"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__hide_use_payment_terminal
msgid ""
"Technical field which is used to hide use_payment_terminal when no payment "
"interfaces are installed."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Tel:"
msgstr "Tel:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ErrorBarcodePopup.js:0
#, python-format
msgid ""
"The Point of Sale could not find any product, client, employee or action "
"associated with the scanned barcode."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_rounding_form_view_inherited
msgid ""
"The Point of Sale only supports the \"add a rounding line\" rounding "
"strategy."
msgstr ""
"Satış Nöqtəsi yalnız \"yuvarlaqlaşdırma sətri əlavə et\" yuvarlaqlaşdırma "
"strategiyasını dəstəkləyir."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"The amount cannot be higher than the due amount if you don't have a cash "
"payment method configured."
msgstr ""
"Məbləğ, əgər sizin bir nağd ödəniş üsulu tənzimlənməmişdirsə, ödənilməli "
"məbləğdən yüksək ola bilməz."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"The amount of your payment lines must be rounded to validate the "
"transaction."
msgstr ""
"Ödəniş sətrlərinin miqdarı əməliyyatı təsdiqləmək üçün "
"yuvarlaqlaşdırılmalıdır."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The cash rounding strategy of the point of sale %(pos)s must be: '%(value)s'"
msgstr ""
"Satış nöqtəsinin %(pos)s pul yuvarlaqlaşdırma strategiyası: '%(value)s' "
"olmalıdır."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "The default pricelist must be included in the available pricelists."
msgstr ""
"Standart qiymət siyahısı mövcud qiymət siyahılarının tərkibində olmalıdır."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The default pricelist must belong to no company or the company of the point "
"of sale."
msgstr ""
"Standart qiymət siyahısı şirkətə məxsus olmamalıdır və ya satış nöqtəsinin "
"şirkətinə aid olmalıdır."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid ""
"The fiscal data module encountered an error while receiving your order."
msgstr ""
"Maliyyə məlumat modulu sifarişinizi qəbul edərkən bir xəta baş verdi. "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid ""
"The fiscal position used in the original order is not loaded. Make sure it "
"is loaded by adding it in the pos configuration."
msgstr ""
"Sifarişdə istifadə olunan fiskal vəziyyət yüklənməyib. POs konfiqurasiyasına"
" həmin fiskal vəziyyəti əlavə edərək yükləndiyindən əmin olun. "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__proxy_ip
msgid ""
"The hostname or ip address of the hardware proxy, Will be autodetected if "
"left empty."
msgstr ""
"Hostun adı və ya IP ünvanı, boş buraxıldıqda avtomatik olaraq aşkarlanacaq."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The invoice journal must be in the same currency as the Sales Journal or the"
" company currency if that is not set."
msgstr ""
"Faktura jurnalı Satış Jurnalı ilə eyni valyutada olmalıdır və ya təyin "
"edilməyibsə, şirkət valyutasında olmalıdır."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The invoice journal of the point of sale %s must belong to the same company."
msgstr "Satış nöqtəsinin qaimə jurnalı %s eyni şirkətə məxsus olmalıdır."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "The maximum difference allowed is"
msgstr ""

#. module: point_of_sale
#: model:ir.model.constraint,message:point_of_sale.constraint_pos_session_uniq_name
msgid "The name of this POS Session must be unique !"
msgstr "Bu POS Sessiyasının adı təkrarlanmayan olmalıdır!"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_partner__pos_order_count
#: model:ir.model.fields,help:point_of_sale.field_res_users__pos_order_count
msgid "The number of point of sales orders related to this customer"
msgstr "Bu müştəri ilə əlaqəli satış sifarişlərinin sayı"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "The order could not be sent to the server due to an unknown error"
msgstr "Sifariş naməlum bir səhvə görə serverə göndərilə bilmədi."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid ""
"The order has been synchronized earlier. Please make the invoice from the "
"backend for the order: "
msgstr ""
"Sifariş daha əvvəl sinxronizasiya edilib. Xahiş olunur, sifariş üçün "
"backenddə faktura hazırlayın:"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_payment.py:0
#, python-format
msgid ""
"The payment method selected is not allowed in the config of the POS session."
msgstr ""
"Seçilmiş ödəniş üsuluna POS sessiyasının konfiqurasiyasında icazə verilmir."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The payment methods for the point of sale %s must belong to its company."
msgstr "Satış Nöqtəsinin Ödəniş üsulları %s onun şirkətinə məxsus olmalıdır."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__config_id
#: model:ir.model.fields,help:point_of_sale.field_pos_session__config_id
msgid "The physical point of sale you will use."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_start_categ_id
msgid ""
"The point of sale will display this product category by default. If no "
"category is specified, all available products will be shown."
msgstr ""
"Satış nöqtəsi standart olaraq bu məhsul kateqoriyasını göstərəcək. "
"Kateqoriya göstərilməzsə, bütün mövcud məhsullar göstəriləcək."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_available_categ_ids
msgid ""
"The point of sale will only display products which are within one of the "
"selected category trees. If no category is specified, all available products"
" will be shown"
msgstr ""
"Satış Nöqtəsi yalnız seçilmiş kateqoriya ağaclarından birində olan "
"məhsulları göstərəcəkdir. Heç bir kateqoriya seçilməyibsə, bütün mövcud "
"məhsullar göstəriləcəkdir."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__pricelist_id
msgid ""
"The pricelist used if no customer is selected or if the customer has no Sale"
" Pricelist configured."
msgstr ""
"Müştəri seçilmədiyində və ya müştərinin Satış Qiymət Siyahısı təyin "
"edilmədiyində istifadə olunan qiymət siyahısı."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_display_categ_images
msgid "The product categories will be displayed with pictures."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__currency_rate
msgid ""
"The rate of the currency to the currency of rate applicable at the date of "
"the order"
msgstr ""
"Məlumatların sifariş tarixində keçərli olan məzənnəsinə görə valyutanın "
"məzənnəsi"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_skip_screen
msgid ""
"The receipt screen will be skipped if the receipt can be printed "
"automatically."
msgstr ""
"Qəbz avtomatik olaraq çap edilə bilirsə qəbz ekranı göstərilməyəcəkdir. "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_auto
msgid "The receipt will automatically be printed at the end of each order."
msgstr "Hər Sifarişin sonunda qəbz avtomatik olaraq çap ediləcəkdir."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid ""
"The requested quantity to be refunded is higher than the ordered quantity. "
"%s is requested while only %s can be refunded."
msgstr ""
"Qaytarılmaq istənən miqdar Sifariş edilən miqdardan çoxdur. Yalnız %s "
"qaytarıla bilərkən %s tələb olunur."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid ""
"The requested quantity to be refunded is higher than the refundable quantity"
" of %s."
msgstr "Tələb olunan qaytarma miqdarı %s qaytarıla bilən miqdardan yüksəkdir."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "The sales journal of the point of sale %s must belong to its company."
msgstr "Satış jurnalı satış nöqtəsinin %s şirkətinə məxsus olmalıdır."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "The selected customer needs an address."
msgstr "Seçilmiş müştəri üçün ünvan tələb olunur."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The selected pricelists must belong to no company or the company of the "
"point of sale."
msgstr ""
"Seçilmiş qiymət siyahıları heç bir şirkətə məxusu olmamalıdır və ya satış "
"nöqtəsinin şirkətinə məxsus olmalıdır."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "The server encountered an error while receiving your order."
msgstr "Sifarişinizi qəbul edərkən server xətası baş verdi."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid ""
"The session has been opened for an unusually long period. Please consider "
"closing."
msgstr ""
"Seans həddindən artıq uzun bir müddət açıq qalmışdır. Xahiş edirik sessiyanı"
" bağlayın."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_adyen
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Adyen. Set your Adyen credentials on the "
"related payment method."
msgstr ""
"Əməliyyatlar Adyen tərəfindən yerinə yetirilir. Əlaqəli ödəniş üsulunda "
"Adyen məlumatlarınızı təyin edin."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_six
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Six. Set the IP address of the terminal on"
" the related payment method."
msgstr ""
"Əməliyyatlar Six tərəfindən yerinə yetirlir. Terminalın IP ünvanını əlaqəli "
"ödəniş metodunda təyin edin."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_mercury
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Vantiv. Set your Vantiv credentials on the"
" related payment method."
msgstr ""
"Əməliyyatlar Vantiv tərəfindən yerinə yetirilir. Vantiv məlumatlarınızı "
"əlaqəli ödəniş üsuluna təyin edin."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_end
msgid "Theoretical Closing Balance"
msgstr "Teorik Qapanış Balansı"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "There are"
msgstr "Burda var"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductList.xml:0
#, python-format
msgid "There are no products in this category."
msgstr "Bu kateqoriyada məhsul yoxdur."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"There are still orders in draft state in the session. Pay or cancel the following orders to validate the session:\n"
"%s"
msgstr ""
"Hələ də qaralama vəziyyətində olan sifarişlər sessiyada mövcuddur. Sessiyanı təsdiq etmək üçün aşağıdakı sifarişləri ödəyin və ya ləğv edin:\n"
"%s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "There are unsynced orders. Do you want to sync these orders?"
msgstr ""
"Sinxronlaşmamış sifarişlər var. Bu sifarişləri sinxronlaşdırmaq "
"istəyirsinizmi?"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"There is a difference between the amounts to post and the amounts of the "
"orders, it is probably caused by taxes or accounting configurations changes."
msgstr ""
"Dərc ediləcək məbləğlər ilə sifarişlərin məbləğləri arasında fərq var, bu, "
"böyük ehtimalla vergilər və ya hesablama konfiqurasiyalarının dəyişdirilməsi"
" səbəbindən əməl gəlmiş ola bilər."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "There is already an electronic payment in progress."
msgstr "Elektron ödəniş artıq başlayıb."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"There is at least one pending electronic payment.\n"
"Please finish the payment with the terminal or cancel it then remove the payment line."
msgstr ""
"Ən azı bir gözləyən elektron ödəniş var.\n"
"Ödənişi terminal vasitəsilə bitirin və ya onu ləğv edin, sonra ödəniş sətrini silin."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"There is no Chart of Accounts configured on the company. Please go to the "
"invoicing settings to install a Chart of Accounts."
msgstr ""
"Şirkət üçün Hesablar Planı təyin edilməyib. Xahiş olunur, şirkət "
"parametrlərinə keçid edərək Hesablar Planı quraşdırın."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"There is no cash payment method available in this point of sale to handle the change.\n"
"\n"
" Please add a cash payment method in the point of sale configuration."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"There is no cash payment method available in this point of sale to handle the change.\n"
"\n"
" Please pay the exact amount or add a cash payment method in the point of sale configuration"
msgstr ""
"Bu satış nöqtəsində xırda pulları idarə etmək üçün heç bir nağd ödəniş üsulu mövcud deyil.\n"
"\n"
"Zəhmət olmasa tam məbləği ödəyin və ya satış nöqtəsi konfiqurasiyasında nəğd ödəniş üsulu əlavə edin."

#. module: point_of_sale
#: code:addons/point_of_sale/wizard/pos_box.py:0
#, python-format
msgid "There is no cash register for this PoS Session"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "There is no cash register in this session."
msgstr "Bu sessiyada nəğd pul kassası yoxdur."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"There must be at least one product in your order before it can be validated "
"and invoiced."
msgstr ""
"Sifarişiniz təsdiqlənmədən və faktura düzəldilmədən əvvəl ən azı bir məhsul "
"seçilməlidir."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"This account is used as intermediary account when nothing is set in a "
"payment method."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__amount_authorized_diff
msgid ""
"This field depicts the maximum difference allowed between the ending balance"
" and the theoretical cash when closing a session, for non-POS managers. If "
"this maximum is reached, the user will have an error message at the closing "
"of his session saying that he needs to contact his manager."
msgstr ""
"Bu sahə, non-POS menecerlər üçün bir sessiyanı bağlamaq zamanı bitiş balansı"
" və teoretik nəğd arasında icazə verilən maksimum fərqi təsvir edir. Bu fərq"
" maksimuma çatıldıqda, istifadəçi sessiyanı bağlamaq üçün menecerilə əlaqə "
"saxlamaq lazım olduğunu bildirən bir xəta mesajı alacaq."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__group_pos_manager_id
msgid ""
"This field is there to pass the id of the pos manager group to the point of "
"sale client."
msgstr ""
"Bu sahə, satış nöqtəsi menecer qrupunun id-sini satış nöqtəsi müştərisinə "
"təqdim etmək üçündür."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__group_pos_user_id
msgid ""
"This field is there to pass the id of the pos user group to the point of "
"sale client."
msgstr ""
"Bu sahə, satış nöqtəsi istifadəçi qrupunun id-sini satış nöqtəsi müştərisinə"
" təqdim etmək üçündür."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"This invoice has been created from the point of sale session: <a href=# "
"data-oe-model=pos.order data-oe-id=%d>%s</a>"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__customer_note
msgid "This is a note destined to the customer"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__allowed_pricelist_ids
msgid "This is a technical field used for the domain of pricelist_id."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__fiscal_position_ids
msgid ""
"This is useful for restaurants with onsite and take-away services that imply"
" specific tax rates."
msgstr ""
"Bu, xüsusi vergi dərəcələrindən istifadə edən müxtəlif xidmətləri olan "
"restoranlar üçün faydalıdır."

#. module: point_of_sale
#: code:addons/point_of_sale/models/account_journal.py:0
#, python-format
msgid ""
"This journal is associated with a payment method. You cannot modify its type"
msgstr ""
"Bu jurnal bir ödəniş metoduna bağlıdır. Onun növünü dəyişə bilməzsiniz."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/DebugWidget.js:0
#, python-format
msgid ""
"This operation will destroy all unpaid orders in the browser. You will lose "
"all the unsaved data and exit the point of sale. This operation cannot be "
"undone."
msgstr ""
"Bu əməliyyat brauzerdəki bütün ödənilməmiş sifarişləri məhv edəcəkdir. Bütün"
" saxlanılmamış məlumatları itirəcəksiniz və satış nöqtəsindən çıxış "
"edəcəksiniz. Bu əməliyyat geri qaytarıla bilməz."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/DebugWidget.js:0
#, python-format
msgid ""
"This operation will permanently destroy all paid orders from the local "
"storage. You will lose all the data. This operation cannot be undone."
msgstr ""
"Bu əməliyyat yerli saxlama yerindən bütün ödənişli sifarişləri daimi olaraq "
"siləcəkdir. Bütün məlumatları itirəcəksiniz. Bu əməliyyat geri alına bilməz."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid ""
"This order already has refund lines for %s. We can't change the customer "
"associated to it. Create a new order for the new customer."
msgstr ""
"Bu sifarişdə artıq %s geri ödəmə sətrləri var. Ona bağlı müştərini dəyişə "
"bilmərik. Yeni müştəri üçün yeni bir sifariş yaradın."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/OrderWidget.xml:0
#, python-format
msgid "This order is empty"
msgstr "Bu sifariş boşdur "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ReceiptScreen/ReceiptScreen.js:0
#, python-format
msgid ""
"This order is not yet synced to server. Make sure it is synced then try "
"again."
msgstr ""
"Bu sifariş hələ də serverə sinxronlaşdırılmayıb. Sinxronlaşdırmadan əmin "
"olun və yenidən cəhd edin."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__tip_product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "This product is used as reference on customer receipts."
msgstr "Bu məhsul müştəri qəbzlərində istinad olaraq istifadə olunur."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__sequence_line_id
msgid ""
"This sequence is automatically created by Odoo but you can change it to "
"customize the reference numbers of your orders lines."
msgstr ""
"Bu sıra Odoo tərəfindən avtomatik olaraq yaradılır, amma sifariş "
"sətrlərinizin referans nömrələrini özəlləşdirmək üçün onu dəyişə bilərsiniz."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__sequence_id
msgid ""
"This sequence is automatically created by Odoo but you can change it to "
"customize the reference numbers of your orders."
msgstr ""
"Bu sıra Odoo tərəfindən avtomatik olaraq yaradılır, amma sifarişlərinizin "
"referans nömrələrini özəlləşdirmək üçün onu dəyişə bilərsiniz."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "This session is already closed."
msgstr "Bu sessiya artıq bağlanıb. "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "This tax is applied to any new product created in the catalog."
msgstr "Bu vergi kataloqda yaradılan hər yeni məhsula tətbiq olunur."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Tip"
msgstr "Bəxşiş"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__tip_amount
msgid "Tip Amount"
msgstr "Bəxşiş Məbləği "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__tip_product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Tip Product"
msgstr "Bəxşiş Məhsulu "

#. module: point_of_sale
#: model:product.product,name:point_of_sale.product_product_tip
#: model:product.template,name:point_of_sale.product_product_tip_product_template
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Tips"
msgstr "Bəxşişlər "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Tips:"
msgstr "Bəxşişlər:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "To Close"
msgstr "Bağlamaq üçün"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "To Pay"
msgstr "Ödəniş Etmək Üçün"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/OrderlineDetails.js:0
#, python-format
msgid "To Refund: %s"
msgstr "Qaytarılacaq: %s"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__to_weight
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__to_weight
msgid "To Weigh With Scale"
msgstr "Tərəzi ilə Çəkiləcək"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__to_invoice
msgid "To invoice"
msgstr "Fakturalanacaq"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_form
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_pos_form
msgid "To record new orders, start a new session."
msgstr "Yeni sifarişləri qeyd etmək üçün yeni bir sessiya başladın."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "To return product(s), you need to open a session in the POS %s"
msgstr "Məhsulları qaytarmaq üçün, POS %s-də bir sessiya açmağınız lazımdır."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__to_ship
msgid "To ship"
msgstr "Göndəriləcək"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/MoneyDetailsPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_total
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Total"
msgstr "Cəmi"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_total_entry_encoding
msgid "Total Cash Transaction"
msgstr "Ümumi Nəğd Əməliyyat"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Total Cost:"
msgstr "Ümumi Dəyər:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__total_discount
msgid "Total Discount"
msgstr "Ümumi Endirim"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenStatus.xml:0
#, python-format
msgid "Total Due"
msgstr "Ödəniləcək Ümumi Məbləğ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Total Margin:"
msgstr "Ümumi Marja:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Total Paid (with rounding)"
msgstr "Ümumi Ödənən (yuvarlaqlaşdırmaqla)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__total_payments_amount
msgid "Total Payments Amount"
msgstr "Ümumi Ödəniş Məbləği "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__price_total
msgid "Total Price"
msgstr "Ümumi Qiymət"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Total Price excl. VAT:"
msgstr "ƏDV-siz Ümumi Qiymət:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Total Taxes"
msgstr "Ümumi Vergilər"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment__amount
msgid "Total amount of the payment."
msgstr "Ümumi ödəniş məbləği "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__total_cost
msgid "Total cost"
msgstr "Ümumi dəyər"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_total_entry_encoding
msgid "Total of all paid sales orders"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_balance_end_real
msgid "Total of closing cash control lines."
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_balance_start
msgid "Total of opening cash control lines."
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "Total qty"
msgstr "Ümumi miqdar"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/SaleDetailsReport.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/OrderSummary.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Total:"
msgstr "Cəmi:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_real_transaction
msgid "Transaction"
msgstr "Əməliyyat"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Transaction cancelled"
msgstr "Əməliyyat ləğv edildi "

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_picking
msgid "Transfer"
msgstr "Köçürmə"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_barcode_rule__type
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__type
msgid "Type"
msgstr "Tip"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__card_type
msgid "Type of card used"
msgstr "İstifadə olunan kartın növü"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Qeyddəki istisna fəaliyyət növü."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Unable to close and validate the session.\n"
"Please set corresponding tax account in each repartition line of the following taxes: \n"
"%s"
msgstr ""
"Məlumatlarınızı təsdiqləmək və bağlamaq mümkün olmadı.\n"
"Zəhmət olmasa aşağıdakı vergilərə aid  müvafiq vergi hesabını təyin edin:\n"
"%s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Unable to download invoice."
msgstr "Fakturanı endirmək mümkün olmadı."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Unable to invoice order."
msgstr "Sifarişi fakturalamaq mümkün olmadı. "

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"Unable to modify this PoS Configuration because you can't modify %s while a "
"session is open."
msgstr ""
"Bu PoS Konfiqurasiyasını dəyişmək mümkün deyil, çünki bir sessiya açıq "
"olduğu müddətdə %s dəyişdirilə bilməz."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientListScreen.js:0
#, python-format
msgid "Unable to save changes."
msgstr "Dəyişikliklər qeyd oluna bilmədi "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Unable to show information about this error."
msgstr "Bu xəta haqqında məlumat göstərilə bilmədi "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "Unable to sync order"
msgstr "Sifarişi sinxronlaşdırmaq mümkün olmadı."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_unit
msgid "Unit Price"
msgstr "Vahid Qiymət"

#. module: point_of_sale
#: model:product.product,uom_name:point_of_sale.desk_organizer
#: model:product.product,uom_name:point_of_sale.desk_pad
#: model:product.product,uom_name:point_of_sale.led_lamp
#: model:product.product,uom_name:point_of_sale.letter_tray
#: model:product.product,uom_name:point_of_sale.magnetic_board
#: model:product.product,uom_name:point_of_sale.monitor_stand
#: model:product.product,uom_name:point_of_sale.newspaper_rack
#: model:product.product,uom_name:point_of_sale.product_product_consumable
#: model:product.product,uom_name:point_of_sale.product_product_tip
#: model:product.product,uom_name:point_of_sale.small_shelf
#: model:product.product,uom_name:point_of_sale.wall_shelf
#: model:product.product,uom_name:point_of_sale.whiteboard
#: model:product.product,uom_name:point_of_sale.whiteboard_pen
#: model:product.template,uom_name:point_of_sale.desk_organizer_product_template
#: model:product.template,uom_name:point_of_sale.desk_pad_product_template
#: model:product.template,uom_name:point_of_sale.led_lamp_product_template
#: model:product.template,uom_name:point_of_sale.letter_tray_product_template
#: model:product.template,uom_name:point_of_sale.magnetic_board_product_template
#: model:product.template,uom_name:point_of_sale.monitor_stand_product_template
#: model:product.template,uom_name:point_of_sale.newspaper_rack_product_template
#: model:product.template,uom_name:point_of_sale.product_product_consumable_product_template
#: model:product.template,uom_name:point_of_sale.product_product_tip_product_template
#: model:product.template,uom_name:point_of_sale.small_shelf_product_template
#: model:product.template,uom_name:point_of_sale.wall_shelf_product_template
#: model:product.template,uom_name:point_of_sale.whiteboard_pen_product_template
#: model:product.template,uom_name:point_of_sale.whiteboard_product_template
msgid "Units"
msgstr "Vahidlər"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ErrorBarcodePopup.xml:0
#, python-format
msgid "Unknown Barcode"
msgstr "Bilinməyən Barkod"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "Unknown Error"
msgstr "Bilinməyən Xəta "

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_unread
msgid "Unread Messages"
msgstr "Oxunmamış Mesajlar"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Oxunmamış Mesaj Hesablayıcısı"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientDetailsEdit.js:0
#, python-format
msgid "Unsupported File Format"
msgstr "Dəstəklənməyən Fayl Formatı"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ReceiptScreen/ReceiptScreen.js:0
#, python-format
msgid "Unsynced order"
msgstr "Sinxronlaşdırılmamış sifariş"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "UoM"
msgstr "Ölçü Vahidi"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_company__point_of_sale_update_stock_quantities
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__update_stock_quantities
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Update quantities in stock"
msgstr "Stokdakı miqdarları yeniləyin"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__use_payment_terminal
msgid "Use a Payment Terminal"
msgstr "Ödəniş Terminalı istifadə edin."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Use a default specific tax regime"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__use_pricelist
msgid "Use a pricelist."
msgstr "Qiymət siyahısı istifadə edin."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Use barcodes to scan products, customer cards, etc."
msgstr ""
"Məhsulları, müştəri kartlarını və s. skan etmək üçün barkodlardan istifadə "
"edin."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Use employee credentials to log in to the PoS session and switch cashier"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__user_id
#: model:res.groups,name:point_of_sale.group_pos_user
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "User"
msgstr "İstifadəçi"

#. module: point_of_sale
#: model:ir.actions.report,name:point_of_sale.report_user_label
msgid "User Labels"
msgstr "İstifadəçi Etiketləri"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__uuid
msgid "Uuid"
msgstr "Uuid"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#, python-format
msgid "VAT:"
msgstr "ƏDV:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "Valid product lot"
msgstr "Düzgün məhsul lotu"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Validate"
msgstr "Təsdiqləyin"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Vantiv (US & Canada)"
msgstr "Vantiv (US & Canada)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_mercury
msgid "Vantiv Payment Terminal"
msgstr "Vantiv Ödəniş Terminalı "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Waiting for card"
msgstr "Kart üçün Gözləmə "

#. module: point_of_sale
#: model:product.product,name:point_of_sale.wall_shelf
#: model:product.template,name:point_of_sale.wall_shelf_product_template
msgid "Wall Shelf Unit"
msgstr "Divar Şkafı"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_warehouse
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__warehouse_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Warehouse"
msgstr "Anbar"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__website_message_ids
msgid "Website Messages"
msgstr "Veb sayt Mesajları"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__website_message_ids
msgid "Website communication history"
msgstr "Veb saytın kommunikasiya tarixçəsi"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Weighing"
msgstr "Çəki"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__weight
msgid "Weighted Product"
msgstr "Çəkilmiş Məhsul"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__picking_policy__one
msgid "When all products are ready"
msgstr "Bütün məhsullar hazır olduğunda"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Whenever you close a session, one entry is generated in the following "
"accounting journal for all the orders not invoiced. Invoices are recorded in"
" accounting separately."
msgstr ""
"Hər bir sessiyanı bağladığınızda, qaimə kəsilməmiş  bütün sifarişlər üçün "
"aşağıdakı hesablama jurnalında bir qeyd yaradılır. Qaimələr  ayrı-ayrılıqda "
"qeydə alınır."

#. module: point_of_sale
#: model:product.product,name:point_of_sale.whiteboard
#: model:product.template,name:point_of_sale.whiteboard_product_template
msgid "Whiteboard"
msgstr "Yazı Taxtası"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.whiteboard_pen
#: model:product.template,name:point_of_sale.whiteboard_pen_product_template
msgid "Whiteboard Pen"
msgstr "Yazı Taxtası Qələmi "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "With a"
msgstr " "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Would you like to load demo data?"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Yes"
msgstr "Bəli"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"You are not allowed to change the cash rounding configuration while a pos "
"session using it is already opened."
msgstr ""
"Nəğd pul yuvarlaqlaşdırması konfiqurasiyasını həmin ayardan istifadə edən "
"POS açıq vəziyyətdə ikən dəyişdirə bilməzsiniz. "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "You are not allowed to change this quantity"
msgstr "Bu miqdarı dəyişdirməyə icazə verilmir "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid ""
"You are trying to sell products with serial/lot numbers, but some of them are not set.\n"
"Would you like to proceed anyway?"
msgstr ""
"Siz seriya/lot nömrələri olan məhsullar satmağa çalışırsınız, lakin onlardan bəziləri təyin edilməyib.\n"
"Yenə də davam etmək istəyirsinizmi?"

#. module: point_of_sale
#: code:addons/point_of_sale/models/account_bank_statement.py:0
#, python-format
msgid ""
"You can't validate a bank statement that is used in an opened Session of a "
"Point of Sale."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"You can't: create a pos order from the backend interface, or unset the "
"pricelist, or create a pos.order in a python test with Form tool, or edit "
"the form view in studio if no PoS order exist"
msgstr ""
"Siz: arxa tərəfdən bir POS sifarişi yarada bilməzsiniz, qiymət siyahısını "
"silə bilməzsiniz, Form alətindən istifadə edərək python testində bir POS "
"sifarişi yarada bilməzsiniz və ya PoS sifarişi mövcud deyilsə form "
"görünüşünü studioda dəyişdirə bilməzsiniz."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"You cannot close the POS when invoices are not posted.\n"
"Invoices: %s"
msgstr ""
"Qaimələr dərc edilmədən POS bağlana bilməz.\n"
"Qaimələr: %s"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You cannot close the POS when orders are still in draft"
msgstr "Sifarişlər hələ də qaralamada olduğu zaman POS-u bağlaya bilməzsiniz."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You cannot create a session before the accounting lock date."
msgstr "Mühasibat kilidlənmə tarixindən əvvəl üçün sessiya aça bilməzsiniz."

#. module: point_of_sale
#: code:addons/point_of_sale/models/account_bank_statement.py:0
#, python-format
msgid "You cannot delete a bank statement linked to Point of Sale session."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_category.py:0
#, python-format
msgid ""
"You cannot delete a point of sale category while a session is still opened."
msgstr ""
"Satış nöqtəsi kateqoriyasını hələ də açıq olan bir sessiya varkən silə "
"bilməzsiniz."

#. module: point_of_sale
#: code:addons/point_of_sale/models/product.py:0
#: code:addons/point_of_sale/models/product.py:0
#, python-format
msgid ""
"You cannot delete a product saleable in point of sale while a session is "
"still opened."
msgstr ""
"Satış nöqtəsində hələ də açıq olan bir sessiya varken satılan bir məhsulu "
"silə bilməzsiniz."

#. module: point_of_sale
#: code:addons/point_of_sale/models/res_partner.py:0
#, python-format
msgid ""
"You cannot delete contacts while there are active PoS sessions. Close the "
"session(s) %s first."
msgstr ""
"Aktiv PoS sessiyaları olduğu müddətdə əlaqələri silə bilməzsiniz. Əvvəlcə "
"sessiyaları %s bağlayın."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "You do not have any products"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"You don't have the access rights to get the point of sale closing control "
"data."
msgstr ""
"Siz satış nöqtəsinin bağlanma nəzarət məlumatlarını əldə etmək üçün giriş "
"hüquqlarına malik deyilsiniz."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You don't have the access rights to set the point of sale cash box."
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"You have enabled the \"Identify Customer\" option for %s payment method,but "
"the order %s does not contain a customer."
msgstr ""
"Siz %s ödəniş üsulu üçün 'Müştərini Təyin Et' seçimini aktivləşdirmisiniz, "
"lakin sifariş %s aid hər hansı müştəri mövcud deyil.\""

#. module: point_of_sale
#: code:addons/point_of_sale/wizard/pos_open_statement.py:0
#, python-format
msgid ""
"You have to define which payment method must be available in the point of "
"sale by reusing existing bank and cash through \"Accounting / Configuration "
"/ Journals / Journals\". Select a journal and check the field \"PoS Payment "
"Method\" from the \"Point of Sale\" tab. You can also create new payment "
"methods directly from menu \"PoS Backend / Configuration / Payment "
"Methods\"."
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "You have to round your payments lines. is not rounded."
msgstr "Ödəniş sətrlərinizi yuvarlaqlaşdırmalısınız. yuvarlaqlaşdırılmayıb."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"You have to select a pricelist in the sale form !\n"
"Please set one before choosing a product."
msgstr ""
"Satış formunda bir qiymət siyahısı seçməlisiniz!\n"
"Məhsulu seçmədən əvvəl qiyməti siyahısını təyin edin."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "You have to select a pricelist in the sale form."
msgstr "Satış formunda bir qiymət siyahısı seçməlisiniz."

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_product_action
msgid ""
"You must define a product for everything you sell through\n"
"                the point of sale interface."
msgstr ""
"Satış nöqtəsindən satdığınız hər bir əmtəə üçün \n"
"                Məhsul təyin etməlisiniz "

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"You must have at least one payment method configured to launch a session."
msgstr ""
"Sessiya başlatmaq üçün ən azı bir ödəniş metodunu təyin etmək lazımdır."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "You need a loss and profit account on your cash journal."
msgstr "Siz mənfəət və zərər hesablarını  təyin etməmisiniz."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"You need to select the customer before you can invoice or ship an order."
msgstr ""
"Sifariş üçün faktura kəsmək və ya sifarişi göndərmək üçün müştəri "
"seçməlisiniz."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You should assign a Point of Sale to your session."
msgstr "Sessiyanıza Satış Nöqtəsi təyin etməlisiniz."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Your PoS Session is open since %(date)s, we advise you to close it and to "
"create a new one."
msgstr ""
"Sizin PoS sessiyanız %(date)s tarixindən açıqdır, onu bağlamağınızı və yeni "
"sessiya açmağınızı tövsiyə edirik."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "ZIP"
msgstr "ZİP"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "at"
msgstr "də"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "available,"
msgstr "mövcuddur,"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "belong to another session:"
msgstr "başqa bir sessiyaya aid :"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "discount"
msgstr "endirim"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "e.g. Cash"
msgstr "məs. Nəğd"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "e.g. Company Address, Website"
msgstr "məs. Şirkət Ünvanı, Website"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "e.g. NYC Shop"
msgstr "məs. Bakı Mağazası"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "e.g. Return Policy, Thanks for shopping with us!"
msgstr ""
"Məsələn, qaytarma siyasəti, bizimlə alış-veriş etdiyiniz üçün təşəkkürlər!"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_form_view
msgid "e.g. Soft Drinks"
msgstr "məs. Spirtsiz İçkilər"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "for an order of"
msgstr "sifariş üçün"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "forecasted"
msgstr "proqnozlaşdırılıb"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/CashMoveButton.js:0
#, python-format
msgid "in"
msgstr "-də"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Misc/MobileOrderWidget.xml:0
#, python-format
msgid "items"
msgstr "maddələr"

#. module: point_of_sale
#: model:mail.activity.type,summary:point_of_sale.mail_activity_old_session
msgid "note"
msgstr "qeyd "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "open sessions"
msgstr "açıq sessiyalar"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "orders"
msgstr "Sifarişlər"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/CashMoveButton.js:0
#, python-format
msgid "out"
msgstr "çıxış"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "paid orders"
msgstr "ödənilmiş sifarişlər "

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "return"
msgstr "qaytar"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "unpaid orders"
msgstr "ödəniş olmayan sifarişlər "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "unpaid orders could not be imported"
msgstr "ödənilməmiş sifarişlər import edilə bilmədi"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_invoice_document
msgid "using"
msgstr "istifadə edərək"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__limited_products_loading
msgid ""
"we load all starred products (favorite), all services, recent inventory movements of products, and the most recently updated products.\n"
"When the session is open, we keep on loading all remaining products in the background.\n"
"In the meantime, you can click on the 'database icon' in the searchbar to load products from database."
msgstr ""
"bütün ulduzlu məhsulları (sevimli), bütün xidmətləri, məhsulların son inventar hərəkətlərini və ən son yenilənmiş məhsulları yükləyirik.\n"
"Sessiya açıq olduğu müddətdə, arxa planda qalan bütün qalan məhsulları yükləməyə davam edirik.\n"
"Bu arada, məhsulları verilənlər bazasından yükləmək üçün axtarış çubuğundakı 'verilənlər bazası nişanı'na klikləyə bilərsiniz."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "were duplicates of existing orders"
msgstr "mövcud sifarişlərin dublikatları idi"
