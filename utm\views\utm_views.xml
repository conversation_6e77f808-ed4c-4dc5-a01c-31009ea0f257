<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <menuitem id="menu_link_tracker_root"
        name="Link Tracker"
        sequence="270"
        web_icon="utm,static/description/icon.png"
        groups="base.group_no_one"/>

    <menuitem id="marketing_utm"
        name="UTMs"
        parent="menu_link_tracker_root"
        sequence="99"
        groups="base.group_no_one"/>

    <record id="utm_campaign_action" model="ir.actions.act_window">
        <field name="name">Campaigns</field>
        <field name="res_model">utm.campaign</field>
        <field name="view_mode">tree,kanban,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a campaign
            </p>
            <p>
                Campaigns are used to centralize your marketing efforts and track their results.
            </p>
        </field>
    </record>

    <menuitem id="menu_utm_campaign_act"
        action="utm_campaign_action"
        parent="marketing_utm"
        sequence="1"
        groups="base.group_no_one"/>

	<!-- utm.medium -->
    <record id="utm_medium_view_tree" model="ir.ui.view">
        <field name="name">utm.medium.view.tree</field>
        <field name="model">utm.medium</field>
        <field name="arch" type="xml">
            <tree string="Mediums" editable="bottom" sample="1">
                <field name="name"/>
                <field name="active" invisible="1"/>
            </tree>
        </field>
    </record>

    <record id="utm_medium_view_form" model="ir.ui.view">
        <field name="name">utm.medium.view.form</field>
        <field name="model">utm.medium</field>
        <field name="arch" type="xml">
            <form string="Medium">
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="active" widget="boolean_toggle"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="utm_medium_view_search" model="ir.ui.view">
        <field name="name">utm.medium.view.search</field>
        <field name="model">utm.medium</field>
        <field name="arch" type="xml">
            <search string="Search UTM Medium">
                <field name="name"/>
                <separator/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
            </search>
        </field>
    </record>
    
    <record id="utm_medium_action" model="ir.actions.act_window">
        <field name="name">Mediums</field>
        <field name="res_model">utm.medium</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="utm_medium_view_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a Medium
            </p><p>
                UTM Mediums track the mean that was used to attract traffic (e.g. "Website", "Twitter", ...).
            </p>
        </field>
    </record>

    <menuitem id="menu_utm_medium"
        action="utm_medium_action"
        parent="marketing_utm"
        sequence="5"
        groups="base.group_no_one"/>

    <!-- utm.source -->
    <record id="utm_source_view_tree" model="ir.ui.view">
        <field name="name">utm.source.view.tree</field>
        <field name="model">utm.source</field>
        <field name="arch" type="xml">
            <tree string="Source" editable="bottom" sample="1">
                <field name="name"/>
            </tree>
        </field>
    </record>

    <record id="utm_source_view_form" model="ir.ui.view">
        <field name="name">utm.source.view.form</field>
        <field name="model">utm.source</field>
        <field name="arch" type="xml">
            <form string="Source">
                <sheet>
                    <group>
                        <field name="name"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="utm_source_action" model="ir.actions.act_window">
        <field name="name">Sources</field>
        <field name="res_model">utm.source</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No Sources yet!
            </p><p>
                UTM Sources track where traffic comes from  (e.g. "May Newsletter", "", ...).
            </p>
        </field>
    </record>

    <menuitem id="menu_utm_source"
        action="utm_source_action"
        parent="marketing_utm"
        sequence="10"
        groups="base.group_no_one"/>

</odoo>
