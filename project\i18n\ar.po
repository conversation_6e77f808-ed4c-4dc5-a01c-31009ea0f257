# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <mustaf<PERSON>@cubexco.com>, 2021
# <PERSON>, 2021
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: project
#. openerp-web
#: code:addons/project/static/src/project_sharing/components/chatter.xml:0
#, python-format
msgid "!!widget.options.res_id && widget.get('messages') || []"
msgstr "!!widget.options.res_id && widget.get('messages') || []"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "\"Refused\""
msgstr "\"مرفوض\""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "\"This Month\""
msgstr "\"هذا الشهر\""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "\"This Week\""
msgstr "\"هذا الاسبوع\""

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "\"Today\""
msgstr "\"اليوم\""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__collaborator_count
msgid "# Collaborators"
msgstr "عدد المتعاونين "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_count
msgid "# Ratings"
msgstr "عدد التقييمات "

#. module: project
#: model:ir.model.fields,field_description:project.field_res_partner__task_count
#: model:ir.model.fields,field_description:project.field_res_users__task_count
msgid "# Tasks"
msgstr "عدد المهام"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__nb_tasks
#: model:ir.model.fields,field_description:project.field_report_project_task_user__nbr
msgid "# of Tasks"
msgstr "عدد المهام"

#. module: project
#: code:addons/project/models/project.py:0
#: code:addons/project/models/project.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (نسخة)"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "%s Use the %s icon to organize your daily activities."
msgstr "%s قم باستخدام الأيقونة %s لترتيب أنشطتك اليومية "

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "(+ %(child_count)s tasks)"
msgstr "(+ مهام %(child_count)s) "

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "(+ 1 task)"
msgstr "(+ 1 مهمة) "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.milestone_deadline
msgid "(due"
msgstr "(تاريخ الاستحقاق "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "(last project update),"
msgstr "(آخر تحديث للمشروع)، "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.milestone_deadline
msgid "- reached on"
msgstr "- وصل في "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__10
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__10
msgid "10"
msgstr "10"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__11
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__11
msgid "11"
msgstr "11"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__12
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__12
msgid "12"
msgstr "12"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__13
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__13
msgid "13"
msgstr "13"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__14
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__14
msgid "14"
msgstr "14"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__15
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__15
msgid "15"
msgstr "15"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__16
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__16
msgid "16"
msgstr "16"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__17
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__17
msgid "17"
msgstr "17"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__18
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__18
msgid "18"
msgstr "18"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__19
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__19
msgid "19"
msgstr "19"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__20
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__20
msgid "20"
msgstr "20"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__21
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__21
msgid "21"
msgstr "21"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__22
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__22
msgid "22"
msgstr "22"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__23
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__23
msgid "23"
msgstr "23"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__24
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__24
msgid "24"
msgstr "24"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__25
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__25
msgid "25"
msgstr "25"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__26
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__26
msgid "26"
msgstr "26"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__27
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__27
msgid "27"
msgstr "27"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__28
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__28
msgid "28"
msgstr "28"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__29
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__29
msgid "29"
msgstr "29"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__30
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__30
msgid "30"
msgstr "30"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_day__31
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_day__31
msgid "31"
msgstr "31"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "<b>Drag &amp; drop</b> the card to change your task from stage."
msgstr "<b>قم بسحب وإفلات</b> البطاقة لتغيير مرحلة مهمتك. "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"<b>Log notes</b> for internal communications <i>(the people following this task won't be notified \n"
"    of the note you are logging unless you specifically tag them)</i>. Use @ <b>mentions</b> to ping a colleague \n"
"    or # <b>mentions</b> to reach an entire team."
msgstr ""
"<b>قم بتسجيل الملاحظات</b> للتواصل الدااخلي <i>(لن يتم إخطار الأفراد المتابعين لهذه المهمة \n"
"    بالملاحظة التي تقوم بتسجيلها إلا إذا قمت بربطهم بشكل خاص)</i>. استخدم تذكيرات <b>@</b> لتنبيه زملاء العمل \n"
"    أو أو تذكيرات <b>#</b> لتصل إلى الفريق بأكمله. "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid ""
"<br/>\n"
"                                            <span class=\"fa fa-lock text-muted\"/><span class=\"text-muted\"> Private</span>"
msgstr ""
"<br/>\n"
"                                            <span class=\"fa fa-lock text-muted\"/><span class=\"text-muted\"> خاص</span>"

#. module: project
#: model:mail.template,body_html:project.rating_project_request_email_template
msgid ""
"<div>\n"
"    <t t-set=\"access_token\" t-value=\"object.rating_get_access_token()\"/>\n"
"    <t t-set=\"partner\" t-value=\"object.rating_get_partner_id()\"/>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"width:100%; margin:0px auto;\">\n"
"    <tbody>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            <t t-if=\"partner.name\">\n"
"                Hello <t t-out=\"partner.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Hello,<br/><br/>\n"
"            </t>\n"
"            Please take a moment to rate our services related to the task \"<strong t-out=\"object.name or ''\">Planning and budget</strong>\"\n"
"            <t t-if=\"object.rating_get_rated_partner_id().name\">\n"
"                assigned to <strong t-out=\"object.rating_get_rated_partner_id().name or ''\">Mitchell Admin</strong>.<br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                .<br/>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td style=\"text-align: center;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" summary=\"o_mail_notification\" style=\"width:100%; margin: 32px 0px 32px 0px;\">\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <strong>Tell us how you feel about our service</strong><br/>\n"
"                    <span style=\"text-color: #888888\">(click on one of these smileys)</span>\n"
"                </td></tr>\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <table style=\"width:100%;text-align:center;margin-top:2rem;\">\n"
"                        <tr>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/5\">\n"
"                                    <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Satisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/3\">\n"
"                                    <img alt=\"Okay\" src=\"/rating/static/src/img/rating_3.png\" title=\"Okay\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/1\">\n"
"                                    <img alt=\"Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Dissatisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td></tr>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            We appreciate your feedback. It helps us to improve continuously.\n"
"            <t t-if=\"object.project_id.rating_status == 'stage'\">\n"
"                <br/><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\">This customer survey has been sent because your task has been moved to the stage <b t-out=\"object.stage_id.name or ''\">In progress</b></span>\n"
"            </t>\n"
"            <t t-if=\"object.project_id.rating_status == 'periodic'\">\n"
"                <br/><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\">This customer survey is sent <b t-out=\"object.project_id.rating_status_period or ''\">Weekly</b> as long as the task is in the <b t-out=\"object.stage_id.name or ''\">In progress</b> stage.</span>\n"
"            </t>\n"
"        </td></tr>\n"
"    </tbody>\n"
"    </table>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    <t t-set=\"access_token\" t-value=\"object.rating_get_access_token()\"/>\n"
"    <t t-set=\"partner\" t-value=\"object.rating_get_partner_id()\"/>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"width:100%; margin:0px auto;\">\n"
"    <tbody>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            <t t-if=\"partner.name\">\n"
"                مرحباً <t t-out=\"partner.name or ''\">براندن فريمان</t>،<br/><br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                مرحباً،<br/><br/>\n"
"            </t>\n"
"            يرجى أخذ دقيقة من وقتك لتقييم خدماتنا ذات الصلة بالمهمة \"<strong t-out=\"object.name or ''\">التخطيط والميزانية</strong>\"\n"
"            <t t-if=\"object.rating_get_rated_partner_id().name\">\n"
"                المسندة إلى <strong t-out=\"object.rating_get_rated_partner_id().name or ''\">ميتشل آدمن </strong>.<br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                .<br/>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td style=\"text-align: center;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" summary=\"o_mail_notification\" style=\"width:100%; margin: 32px 0px 32px 0px;\">\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <strong>أخبرنا بما تشعر به تجاه خدمتنا</strong><br/>\n"
"                    <span style=\"text-color: #888888\">(اضغط على إحدى تلك الوجوه الضاحكة)</span>\n"
"                </td></tr>\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <table style=\"width:100%;text-align:center;margin-top:2rem;\">\n"
"                        <tr>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/5\">\n"
"                                    <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Satisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/3\">\n"
"                                    <img alt=\"Okay\" src=\"/rating/static/src/img/rating_3.png\" title=\"Okay\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/1\">\n"
"                                    <img alt=\"Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Dissatisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td></tr>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            نحن نقدّر ملاحظاتك، حيث إنها تتيح لنا التحسن باستمرار.\n"
"            <t t-if=\"object.project_id.rating_status == 'stage'\">\n"
"                <br/><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\">تم إرسال استطلاع العميل لأنه قد تم نقل مهمتك إلى المرحلة <b t-out=\"object.stage_id.name or ''\">قيد التنفيذ</b></span>\n"
"            </t>\n"
"            <t t-if=\"object.project_id.rating_status == 'periodic'\">\n"
"                <br/><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\">يتم إرسال استطلاع العميل <b t-out=\"object.project_id.rating_status_period or ''\">أسبوعياً</b> طالما أن المهمة في مرحلة<b t-out=\"object.stage_id.name or ''\">قيد التنفيذ</b> .</span>\n"
"            </t>\n"
"        </td></tr>\n"
"    </tbody>\n"
"    </table>\n"
"</div>\n"
"            "

#. module: project
#: model:mail.template,body_html:project.mail_template_data_project_task
msgid ""
"<div>\n"
"    Dear <t t-out=\"object.partner_id.name or 'customer'\">Brandon Freeman</t>,<br/>\n"
"    Thank you for your enquiry.<br/>\n"
"    If you have any questions, please let us know.\n"
"    <br/><br/>\n"
"    Thank you,\n"
"    <t t-if=\"user.signature\">\n"
"        <br/>\n"
"        <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"        "
msgstr ""
"<div>\n"
"    عزيزي <t t-out=\"object.partner_id.name or 'customer'\">براندن فريمان</t>،<br/>\n"
"    شكراً لاستفسارك.<br/>\n"
"    يرجى إعلامنا إذا كانت لديك أي أسئلة.\n"
"    <br/><br/>\n"
"    شكراً لك،\n"
"    <t t-if=\"user.signature\">\n"
"        <br/>\n"
"        <t t-out=\"user.signature or ''\">--<br/>ميتشل آدمن</t>\n"
"    </t>\n"
"</div>\n"
"        "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"
msgstr "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"إدارة \"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2 oe_edit_only\" aria-label=\"Arrow icon\" title=\"Arrow\"/>\n"
"                                <i class=\"fa fa-long-arrow-right mx-2 oe_read_only\" aria-label=\"Arrow icon\" title=\"Arrow\" attrs=\"{'invisible': [('date_start', '=', False), ('date', '=', False)]}\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2 oe_edit_only\" aria-label=\"Arrow icon\" title=\"سهم \"/>\n"
"                                <i class=\"fa fa-long-arrow-right mx-2 oe_read_only\" aria-label=\"Arrow icon\" title=\"سهم \" attrs=\"{'invisible': [('date_start', '=', False), ('date', '=', False)]}\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid ""
"<i class=\"fa fa-smile-o\" role=\"img\" aria-label=\"Percentage of "
"satisfaction\" title=\"Percentage of satisfaction\"/>"
msgstr ""
"<i class=\"fa fa-smile-o\" role=\"img\" aria-label=\"Percentage of "
"satisfaction\" title=\"نسبة الرضا \"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid ""
"<i class=\"fa fa-warning\" title=\"Customer disabled on projects\"/><b> "
"Customer Ratings</b> are disabled on the following project(s) : <br/>"
msgstr ""
"<i class=\"fa fa-warning\" title=\"Customer disabled on projects\"/><b> "
"تقييمات العملاء</b> معطلة في المشروع (المشاريع) التالية : <br/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid ""
"<i class=\"o_project_task_project_field text-danger\" attrs=\"{'invisible': "
"[('project_id', '!=', False)]}\">Private</i>"
msgstr ""
"<i class=\"o_project_task_project_field text-danger\" attrs=\"{'invisible': "
"[('project_id', '!=', False)]}\">خاص</i>"

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "<p><em>Number of tasks: %(tasks_count)s</em></p>"
msgstr "<p><em>عدد المهام: %(tasks_count)s</em></p>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<small class=\"text-right\">Stage:</small>"
msgstr "<small class=\"text-right\">المرحلة:</small>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid ""
"<span attrs=\"{'invisible': ['|', ('repeat_show_week', '=', False), "
"('repeat_show_month', '=', False)]}\">of</span>"
msgstr ""
"<span attrs=\"{'invisible': ['|', ('repeat_show_week', '=', False), "
"('repeat_show_month', '=', False)]}\">من</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span class=\"fa fa-clock-o mr-2\" title=\"Dates\"/>"
msgstr "<span class=\"fa fa-clock-o mr-2\" title=\"التواريخ \"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid ""
"<span class=\"fa fa-envelope-o mr-2\" aria-label=\"Domain Alias\" "
"title=\"Domain Alias\"/>"
msgstr ""
"<span class=\"fa fa-envelope-o mr-2\" aria-label=\"Domain Alias\" "
"title=\"ألقاب النطاق \"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span class=\"fa fa-user mr-2\" aria-label=\"Partner\" title=\"Partner\"/>"
msgstr "<span class=\"fa fa-user mr-2\" aria-label=\"Partner\" title=\"شريك \"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Collaborators\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                    المتعاونين\n"
"                                </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Customer Satisfaction\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                    رضا العملاء\n"
"                                </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Milestones\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                    مؤشرات التقدم\n"
"                                </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"<span class=\"o_stat_text\">\n"
"                                Burndown Chart\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                مخطط التوقف\n"
"                            </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text\">Blocking</span>"
msgstr "<span class=\"o_stat_text\">الحجب</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "<span class=\"o_stat_text\">Gross Margin</span>"
msgstr "<span class=\"o_stat_text\">الهامش الإجمالي</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text\">in Recurrence</span>"
msgstr "<span class=\"o_stat_text\">في التكرار</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"<span class=\"oe_read_only\" attrs=\"{'invisible': [('alias_name', '!=', False)]}\">Create tasks by sending an email to </span>\n"
"                                        <span class=\"font-weight-bold oe_read_only\" attrs=\"{'invisible': [('alias_name', '=', False)]}\">Create tasks by sending an email to </span>\n"
"                                        <span class=\"font-weight-bold oe_edit_only\">Create tasks by sending an email to </span>"
msgstr ""
"<span class=\"oe_read_only\" attrs=\"{'invisible': [('alias_name', '!=', False)]}\">قم بإنشاء المهام عن طريق إرسال بريد إلكتروني إلى </span>\n"
"                                        <span class=\"font-weight-bold oe_read_only\" attrs=\"{'invisible': [('alias_name', '=', False)]}\">قم بإنشاء المهام عن طريق إرسال بريد إلكتروني إلى </span>\n"
"                                        <span class=\"font-weight-bold oe_edit_only\">قم بإنشاء المهام عن طريق إرسال بريد إلكتروني إلى </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_delete_wizard_form
msgid "<span>Are you sure you want to delete this project ?</span>"
msgstr "<span>هل أنت متأكد من أنك ترغب في حذف هذا المشروع؟</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span>Reporting</span>"
msgstr "<span>إعداد التقارير</span> "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span>View</span>"
msgstr "<span>عرض</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_delete_wizard_form
msgid ""
"<span>You cannot delete a project containing tasks. You can either archive "
"it or first delete all of its tasks.</span>"
msgstr ""
"<span>لا يمكنك حذف مشروع يحتوي على مهام. يمكنك إما أرشفته أو حذف كافة "
"مهامه.</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong class=\"d-block mb-2\">Attachments</strong>"
msgstr "<strong class=\"d-block mb-2\">المرفقات</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Assignees</strong>"
msgstr "<strong>المسند إليهم</strong> "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Customer</strong>"
msgstr "<strong>العميل</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Deadline:</strong>"
msgstr "<strong>الموعد النهائي:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Description</strong>"
msgstr "<strong>الوصف:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Message and communication history</strong>"
msgstr "<strong>سجل الرسائل والاتصالات</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task_planned_hours_template
msgid "<strong>Planned Hours:</strong>"
msgstr "<strong>الساعات المخطط لها:</strong> "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Project:</strong>"
msgstr "<strong>المشروع:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "<u>Milestones</u>"
msgstr "<u>مؤشرات التقدم</u>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "=&gt;"
msgstr "=&gt;"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"قاموس بايثون الذي سيتم تقييمه لتوفير قيم افتراضية عند إنشاء سجلات جديدة لهذا"
" اللقب. "

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_collaborator_unique_collaborator
msgid ""
"A collaborator cannot be selected more than once in the project sharing "
"access. Please remove duplicate(s) and try again."
msgstr ""
"لا يمكن تحديد المتعاون أكثر من مرة في وصول مشاركة المشروع. يرجى إزالة النسخة"
" (النسخ) ثم حاول مجدداً. "

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"A personal stage cannot be linked to a project because it is only visible to"
" its corresponding user."
msgstr ""
"لا يمكن أن تقوم بربط مرحلة مستخدم بالمشروع لأنها تكون مرئية فقط للمستخدم. "

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_task_user_rel_project_personal_stage_unique
msgid "A task can only have a single personal stage per user."
msgstr "يمكن أن يكون للمهمة مرحلة شخصية واحدة فقط لكل مستخدم. "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Accept Emails From"
msgstr "قبول رسائل البريد من"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__access_mode
msgid "Access Mode"
msgstr "وضع الوصول "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_warning
#: model:ir.model.fields,field_description:project.field_project_share_wizard__access_warning
#: model:ir.model.fields,field_description:project.field_project_task__access_warning
msgid "Access warning"
msgstr "تحذير من خطأ بالوصول"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_needaction
#: model:ir.model.fields,field_description:project.field_project_project__message_needaction
#: model:ir.model.fields,field_description:project.field_project_task__message_needaction
#: model:ir.model.fields,field_description:project.field_project_update__message_needaction
msgid "Action Needed"
msgstr "يتطلب اتخاذ إجراء "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__active
#: model:ir.model.fields,field_description:project.field_project_project_stage__active
#: model:ir.model.fields,field_description:project.field_project_task__active
#: model:ir.model.fields,field_description:project.field_project_task_type__active
msgid "Active"
msgstr "نشط"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_ids
#: model:ir.model.fields,field_description:project.field_project_task__activity_ids
#: model:ir.model.fields,field_description:project.field_project_update__activity_ids
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Activities"
msgstr "الأنشطة"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_exception_decoration
#: model:ir.model.fields,field_description:project.field_project_task__activity_exception_decoration
#: model:ir.model.fields,field_description:project.field_project_update__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "زخرفة استثناء النشاط"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_state
#: model:ir.model.fields,field_description:project.field_project_task__activity_state
#: model:ir.model.fields,field_description:project.field_project_update__activity_state
msgid "Activity State"
msgstr "حالة النشاط"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_type_icon
#: model:ir.model.fields,field_description:project.field_project_task__activity_type_icon
#: model:ir.model.fields,field_description:project.field_project_update__activity_type_icon
msgid "Activity Type Icon"
msgstr "أيقونة نوع النشاط"

#. module: project
#: model:ir.actions.act_window,name:project.mail_activity_type_action_config_project_types
#: model:ir.ui.menu,name:project.project_menu_config_activity_type
msgid "Activity Types"
msgstr "أنواع الأنشطة "

#. module: project
#. openerp-web
#: code:addons/project/static/src/xml/project_templates.xml:0
#, python-format
msgid "Add Milestone"
msgstr "إضافة مؤشر تقدم "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.personal_task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "Add a description..."
msgstr "إضافة وصف..."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Add a note"
msgstr "إضافة ملاحظة"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Add columns to organize your tasks into <b>stages</b> <i>e.g. New - In "
"Progress - Done</i>."
msgstr ""
"قم بإضافة أعمدة لتنظيم مهامك إلى <b>مراحل</b> <i>مثال: جديد - قيد التنفيذ - "
"منتهي</i>."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Add contacts to share the project..."
msgstr "إضافة جهات اتصال لمشاركة المشروع... "

#. module: project
#: model:ir.model.fields,help:project.field_project_share_wizard__note
msgid "Add extra content to display in the email"
msgstr "إضافة محتوى إضافي لعرضه في البريد الإكتروني"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Add your task once it is ready."
msgstr "إضافة مهمتك عندما تكون جاهزة. "

#. module: project
#: model:res.groups,name:project.group_project_manager
msgid "Administrator"
msgstr "مدير"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Agile Scrum"
msgstr "منهجية تطوير البرمجيات (Agile Scrum) "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_id
msgid "Alias"
msgstr "لقب"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_contact
msgid "Alias Contact Security"
msgstr "لقب الاتصال الآمن"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_name
msgid "Alias Name"
msgstr "اسم اللقب"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_domain
msgid "Alias domain"
msgstr "نطاق اللقب"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_value
msgid "Alias email"
msgstr "لقب البريد الإلكتروني "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_model_id
msgid "Aliased Model"
msgstr "النموذج الملقب "

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "All"
msgstr "الكل"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__employees
msgid "All employees"
msgstr "كافة الموظفين "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__recurrence_update__all
msgid "All tasks"
msgstr "كافة المهام"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__allow_subtasks
msgid "Allow Sub-tasks"
msgstr "السماح بالمهام الفرعية "

#. module: project
#: model:ir.model,name:project.model_account_analytic_account
#: model:ir.model.fields,field_description:project.field_project_project__analytic_account_id
#: model:ir.model.fields,field_description:project.field_project_task__analytic_account_id
msgid "Analytic Account"
msgstr "الحساب التحليلي"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__analytic_tag_ids
msgid "Analytic Tag"
msgstr "علامة التصنيف التحليلية "

#. module: project
#: model:ir.model,name:project.model_account_analytic_tag
msgid "Analytic Tags"
msgstr "علامات التصنيف التحليلية "

#. module: project
#: model:ir.model.fields,help:project.field_project_project__analytic_account_id
#: model:ir.model.fields,help:project.field_project_task__project_analytic_account_id
msgid ""
"Analytic account to which this project is linked for financial management. "
"Use an analytic account to record cost and revenue on your project."
msgstr ""
"الحساب التحليلي الذي يرتبط به هذا المشروع للإدارة المالية. استخدم حساباً "
"تحليلياً لتسجيل التكلفة والإيرادات في مشروعك. "

#. module: project
#: model:ir.model.fields,help:project.field_project_task__analytic_account_id
msgid ""
"Analytic account to which this task is linked for financial management. Use "
"an analytic account to record cost and revenue on your task. If empty, the "
"analytic account of the project will be used."
msgstr ""
"الحساب التحليلي الذي ترتبط به هذه المهمة للإدارة المالية. استخدم حساباً "
"تحليلياً لتسجيل التكلفة والإيرادات في مهمتك. إذا تركته فارغاً، سوف يتم "
"استخدام الحساب التحليلي للمشروع. "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Analytics"
msgstr "التحليلات"

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_user_tree
msgid "Analyze the performance of your tasks and your workers."
msgstr "قم بتحليل أداء مهامك وعمّالك. "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__april
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__april
msgid "April"
msgstr "إبريل "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_form.js:0
#: code:addons/project/static/src/js/project_list.js:0
#, python-format
msgid "Archive"
msgstr "أرشفة"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid "Archive Stages"
msgstr "أرشفه المراحل "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_delete_wizard_form
msgid "Archive project"
msgstr "أرشفه المشروع "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.personal_task_type_edit
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_form
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_search
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Archived"
msgstr "مؤرشف"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
msgid "Are you sure you want to continue?"
msgstr "هل أنت متاكد أنك ترغب في الاستمرار؟ "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid "Are you sure you want to delete those stages ?"
msgstr "هل أنت متاكد أنك ترغب في حذف تلك المراحل؟ "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Arrow"
msgstr "سهم "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Arrow icon"
msgstr "أيقونة السهم "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Assembling"
msgstr "التركيب "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Assign to Me"
msgstr "إسنادها لي"

#. module: project
#: model_terms:ir.ui.view,help:project.project_sharing_project_task_view_kanban
msgid " assignee"
msgstr "المسند إليه "

#. module: project
#: model_terms:ir.ui.view,help:project.project_sharing_project_task_view_kanban
msgid " assignees"
msgstr "المسند إليهم "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Assigned"
msgstr "تم إسنادها "

#. module: project
#: model:ir.actions.act_window,name:project.act_res_users_2_project_task_opened
msgid "Assigned Tasks"
msgstr "المهام المسندة "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
msgid "Assigned to"
msgstr "تم إسنادها إلى "

#. module: project
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__user_ids
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__user_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__user_ids
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_kanban
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_tree
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#, python-format
msgid "Assignees"
msgstr "المسند إليهم "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__date_assign
msgid "Assigning Date"
msgstr "تاريخ الإسناد"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date_assign
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_assign
msgid "Assignment Date"
msgstr "تاريخ الإسناد "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__at_risk
#: model:ir.model.fields.selection,name:project.selection__project_update__status__at_risk
msgid "At Risk"
msgstr "في خطر "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid ""
"At each stage, employees can block tasks or mark them as ready for the next step.\n"
"                                    You can customize here the labels for each state."
msgstr ""
"في كل مرحلة، بوسع الموظفين حجب المهام أو تعيينها كجاهزة للخطوة التالية.\n"
"                                    بإمكانك تخصيص بطاقات عناوين هنا لكل حالة."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_project__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_task__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_update__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__attachment_ids
msgid "Attachments that don't come from a message."
msgstr "المرفقات التي لا تأتي من رسالة. "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__august
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__august
msgid "August"
msgstr "أغسطس"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__user_id
msgid "Author"
msgstr "الكاتب "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Auto-generate tasks for regular activities"
msgstr "إنشاء المهام تلقائياً للأنشطة العادية "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__auto_validation_kanban_state
msgid "Automatic kanban status"
msgstr "حالة كانبان التلقائية"

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__auto_validation_kanban_state
msgid ""
"Automatically modify the kanban state when the customer replies to the feedback for this stage.\n"
" * Good feedback from the customer will update the kanban state to 'ready for the new stage' (green bullet).\n"
" * Neutral or bad feedback will set the kanban state to 'blocked' (red bullet).\n"
msgstr ""
"قم بتعديل حالة كانبان تلقائياً عندما يجيب العميل على ملاحظات هذه المرحلة.\n"
" * سوف تقوم الملاحظات الإيجابية بتحديث حالة كانبان إلى 'جاهز للمرحلة الجديدة' (العلامة الخضراء).\n"
" * ستعين الملاحظات المحايدة أو السلبية حالة كانبان إلى 'محجوب' (العلامة الحمراء).\n"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Backlog"
msgstr "المتأخرات"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__analytic_account_balance
msgid "Balance"
msgstr "الرصيد"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__dependent_ids
msgid "Block"
msgstr "حجب "

#. module: project
#: code:addons/project/models/project.py:0
#: model:ir.model.fields.selection,name:project.selection__project_task__kanban_state__blocked
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__blocked
#: model:project.task,legend_blocked:project.project_task_1
#: model:project.task,legend_blocked:project.project_task_12
#: model:project.task,legend_blocked:project.project_task_2
#: model:project.task,legend_blocked:project.project_task_20
#: model:project.task,legend_blocked:project.project_task_24
#: model:project.task,legend_blocked:project.project_task_25
#: model:project.task,legend_blocked:project.project_task_26
#: model:project.task,legend_blocked:project.project_task_3
#: model:project.task,legend_blocked:project.project_task_30
#: model:project.task,legend_blocked:project.project_task_31
#: model:project.task,legend_blocked:project.project_task_32
#: model:project.task,legend_blocked:project.project_task_33
#: model:project.task,legend_blocked:project.project_task_8
#: model:project.task,legend_blocked:project.project_task_9
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_0
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_1
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_2
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_3
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_4
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_5
#: model:project.task.type,legend_blocked:project.project_personal_stage_admin_6
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_0
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_1
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_2
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_3
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_4
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_5
#: model:project.task.type,legend_blocked:project.project_personal_stage_demo_6
#: model:project.task.type,legend_blocked:project.project_stage_0
#: model:project.task.type,legend_blocked:project.project_stage_2
#: model:project.task.type,legend_blocked:project.project_stage_3
#, python-format
msgid "Blocked"
msgstr "محجوب"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__depend_on_ids
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Blocked By"
msgstr "محجوب من قِبَل "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Brainstorm"
msgstr "عصف ذهني "

#. module: project
#: code:addons/project/models/project.py:0
#: model:ir.actions.act_window,name:project.action_project_task_burndown_chart_report
#: model:ir.model,name:project.model_project_task_burndown_chart_report
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_graph
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_pivot
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#, python-format
msgid "Burndown Chart"
msgstr "مخطط التوقف "

#. module: project
#: model:project.task,legend_done:project.project_task_10
#: model:project.task,legend_done:project.project_task_11
#: model:project.task,legend_done:project.project_task_19
#: model:project.task,legend_done:project.project_task_21
#: model:project.task,legend_done:project.project_task_22
#: model:project.task,legend_done:project.project_task_27
#: model:project.task,legend_done:project.project_task_28
#: model:project.task,legend_done:project.project_task_29
#: model:project.task,legend_done:project.project_task_34
#: model:project.task,legend_done:project.project_task_35
#: model:project.task,legend_done:project.project_task_36
#: model:project.task,legend_done:project.project_task_4
#: model:project.task,legend_done:project.project_task_5
#: model:project.task,legend_done:project.project_task_6
#: model:project.task,legend_done:project.project_task_7
#: model:project.task.type,legend_done:project.project_stage_1
msgid "Buzz or set as done"
msgstr "تنبيه أو تعين كمهمة منتهية"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_delete_wizard_form
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Cancel"
msgstr "إلغاء "

#. module: project
#: code:addons/project/models/project.py:0
#: model:project.project.stage,name:project.project_project_stage_3
#: model:project.task.type,name:project.project_personal_stage_admin_6
#: model:project.task.type,name:project.project_personal_stage_demo_6
#, python-format
msgid "Canceled"
msgstr "ملغي"

#. module: project
#: model:project.task.type,name:project.project_stage_3
msgid "Cancelled"
msgstr "ملغي"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__partner_is_company
msgid "Check if the contact is a company, otherwise it is a person"
msgstr "تحقق مما إذا كانت جهة الاتصال عبارة عن شركة، وإلا فستعتبر فرداً "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__child_text
msgid "Child Text"
msgstr "النص التابع "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Choose a <b>name</b> for your project. <i>It can be anything you want: the name of a customer,\n"
"     of a product, of a team, of a construction site, etc.</i>"
msgstr ""
"اختر <b>اسماً</b> لمشروعك. <i>يمكنك اختيار ما تريد: اسم العميل،\n"
"     اسم المنتج، اسم الفريق، اسم موقع البناء، وما إلى ذلك.</i>"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Choose a task <b>name</b> <i>(e.g. Website Design, Purchase Goods...)</i>"
msgstr ""
"أعطِ مهمتك <b>اسماً</b> <i>(مثال: تصميم الموقع الإلكتروني، شراء "
"البضائع...)</i>"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__partner_city
msgid "City"
msgstr "المدينة"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Client Review"
msgstr "مراجعة العميل "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__is_closed
#: model:ir.model.fields,field_description:project.field_project_task_type__is_closed
msgid "Closing Stage"
msgstr "مرحلة الإغلاق "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__partner_id
#: model_terms:ir.ui.view,arch_db:project.project_collaborator_view_search
msgid "Collaborator"
msgstr "المتعاون"

#. module: project
#: code:addons/project/models/project.py:0
#: model:ir.model.fields,field_description:project.field_project_project__collaborator_ids
#, python-format
msgid "Collaborators"
msgstr "المتعاونون"

#. module: project
#: model:ir.model,name:project.model_project_collaborator
msgid "Collaborators in project shared"
msgstr "المتعاونون في المشروع المُشارَك "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_tags__color
#: model:ir.model.fields,field_description:project.field_project_update__color
msgid "Color"
msgstr "اللون"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__color
#: model:ir.model.fields,field_description:project.field_project_task__color
msgid "Color Index"
msgstr "مؤشر اللون "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__commercial_partner_id
#: model:ir.model.fields,field_description:project.field_project_task__commercial_partner_id
msgid "Commercial Entity"
msgstr "الكيان التجاري"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid ""
"Communicate with customers on the task using the email gateway. Attach logo "
"designs to the task, so that information flows from designers to the workers"
" who print the t-shirt. Organize priorities amongst orders using the %s "
"icon. %s"
msgstr ""
"تواصل مع العملاء حول المهمة باستخدام بوابة العملاء. قم بإرفاق تصاميم الشعار "
"بالمهمة حتى تنتقل المعلومات من المصممين إلى العمال طابعي القمصان. قم بتنظيم "
"الأولويات من بين الأوامر %s باستخدام الأيقونة. %s "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__company_id
#: model:ir.model.fields,field_description:project.field_project_task__company_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__company_id
msgid "Company"
msgstr "الشركة "

#. module: project
#: model:ir.model,name:project.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: project
#: model:ir.ui.menu,name:project.menu_project_config
msgid "Configuration"
msgstr "التهيئة "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Configure Stages"
msgstr "تهيئة المراحل "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
msgid "Confirm"
msgstr "تأكيد"

#. module: project
#. openerp-web
#: code:addons/project/models/project.py:0
#: code:addons/project/static/src/js/project_form.js:0
#: code:addons/project/static/src/js/project_list.js:0
#: code:addons/project/wizard/project_task_type_delete.py:0
#, python-format
msgid "Confirmation"
msgstr "التأكيد "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Consulting"
msgstr "استشارة"

#. module: project
#: model:ir.model,name:project.model_res_partner
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
msgid "Contact"
msgstr "جهة الاتصال"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_form.js:0
#: code:addons/project/static/src/js/project_list.js:0
#, python-format
msgid "Continue Recurrence"
msgstr "الاستمرار في التكرار "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Copywriting"
msgstr "الكتابة الإعلامية "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__displayed_image_id
msgid "Cover Image"
msgstr "صورة الغلاف"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified_footer
msgid "Create"
msgstr "إنشاء"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__create_date
msgid "Create Date"
msgstr "تاريخ الإنشاء"

#. module: project
#: model:ir.actions.act_window,name:project.open_create_project
msgid "Create a Project"
msgstr "إنشاء مشروع"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form_domain
msgid "Create a new stage in the task pipeline"
msgstr "إنشاء مرحلة جديدة في مخطط سير عمل المهمة "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "Create tasks by sending an email to"
msgstr "قم بإنشاء مهام عن طريق إرسال بريد إلكتروني إلى "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__create_date
msgid "Created On"
msgstr "أنشئ في"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__create_uid
#: model:ir.model.fields,field_description:project.field_project_delete_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_milestone__create_uid
#: model:ir.model.fields,field_description:project.field_project_project__create_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage__create_uid
#: model:ir.model.fields,field_description:project.field_project_share_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_tags__create_uid
#: model:ir.model.fields,field_description:project.field_project_task__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_type__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_update__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__create_date
#: model:ir.model.fields,field_description:project.field_project_delete_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_milestone__create_date
#: model:ir.model.fields,field_description:project.field_project_project__create_date
#: model:ir.model.fields,field_description:project.field_project_project_stage__create_date
#: model:ir.model.fields,field_description:project.field_project_share_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_tags__create_date
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__create_date
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__create_date
#: model:ir.model.fields,field_description:project.field_project_task_type__create_date
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_update__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Creation Date"
msgstr "تاريخ الإنشاء"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__currency_id
msgid "Currency"
msgstr "العملة"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "Current project of the task"
msgstr "المشروع الحالي للمهمة "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "Current stage of the task"
msgstr "المرحلة الحالية للمهمة"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "Current stage of this task"
msgstr "المرحلة الحالية لهذه المهمة"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "رسالة مرتدة مخصصة"

#. module: project
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/models/project.py:0
#: model:ir.model.fields,field_description:project.field_project_project__partner_id
#: model:ir.model.fields,field_description:project.field_project_task__partner_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__partner_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__partner_id
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#, python-format
msgid "Customer"
msgstr "العميل"

#. module: project
#: code:addons/project/models/project.py:0
#: code:addons/project/models/project.py:0
#, python-format
msgid "Customer Email"
msgstr "بريد العميل الإلكتروني "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Customer Feedback"
msgstr "ملاحظات العميل"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__access_url
#: model:ir.model.fields,help:project.field_project_task__access_url
msgid "Customer Portal URL"
msgstr "رابط بوابة العميل"

#. module: project
#: model:ir.actions.act_window,name:project.rating_rating_action_project_report
#: model:ir.model.fields,field_description:project.field_project_project__rating_active
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_rating
#: model:ir.ui.menu,name:project.rating_rating_menu_project
msgid "Customer Ratings"
msgstr "تقييمات العميل"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_status
msgid "Customer Ratings Status"
msgstr "حالة تقييمات العملاء "

#. module: project
#: model:ir.actions.act_window,name:project.rating_rating_action_task
msgid "Customer Ratings on Task"
msgstr "تقييمات العملاء للمهام "

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "Customer Satisfaction"
msgstr "رضا العميل"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid ""
"Customers propose feedbacks by email; Odoo creates tasks automatically, and "
"you can communicate on the task directly. Your managers decide which "
"feedback is accepted %s and which feedback is moved to the %s column. %s"
msgstr ""
"يقدم العملاء ملاحظاتهم عبر البريد الإلكتروني؛ ويقوم أودو بإنشاء المهام "
"تلقائياً، ويمكنك التواصل بشأن المهمة مباشرةً. يحدد مديرك أي الملاحظات مقبولة"
" %s وأي الملاحظات يتم نقلها إلى عمود %sمرفوضة. %s "

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_0
msgid ""
"Customize how tasks are named according to the project and create tailor "
"made status messages for each step of the workflow. It helps to document "
"your workflow: what should be done at which step."
msgstr ""
"قم بتخصيص كيفية تسمية المهام وفقاً للسجل ثم قم بإنشاء رسائل حالات معدّة "
"خصيصاً لكل خطوة من سير العمل. يساعدك على توثيق سير عملك: ما يجب فعله في أي "
"خطوة. "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__daily
msgid "Daily"
msgstr "يومياً "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date
#: model:ir.model.fields,field_description:project.field_project_update__date
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_graph
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.project_update_view_kanban
msgid "Date"
msgstr "التاريخ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date_group_by
msgid "Date Group By"
msgstr "تجميع التواريخ حسب "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_on_month__date
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_on_month__date
msgid "Date of the Month"
msgstr "اليوم من الشهر "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_on_year__date
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_on_year__date
msgid "Date of the Year"
msgstr "اليوم من السنة "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Dates"
msgstr "التواريخ"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_weekday
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_weekday
msgid "Day Of The Week"
msgstr "اليوم من الأسبوع "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_on_month__day
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_on_month__day
msgid "Day of the Month"
msgstr "اليوم من الشهر "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_on_year__day
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_on_year__day
msgid "Day of the Year"
msgstr "اليوم من السنة "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__day
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__day
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Days"
msgstr "أيام "

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__delay_endings_days
msgid "Days to Deadline"
msgstr "الأيام حتى الموعد النهائي "

#. module: project
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_milestone__deadline
#: model:ir.model.fields,field_description:project.field_project_task__date_deadline
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date_deadline
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_deadline
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#, python-format
msgid "Deadline"
msgstr "الموعد النهائي"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_message_user_assigned
msgid "Dear"
msgstr "عزيزنا"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__december
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__december
msgid "December"
msgstr "ديسمبر"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_defaults
msgid "Default Values"
msgstr "القيم الافتراضية"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form_domain
msgid ""
"Define the steps that will be used in the project from the\n"
"                creation of the task, up to the closing of the task or issue.\n"
"                You will use these stages in order to track the progress in\n"
"                solving a task or an issue."
msgstr ""
"قم بتحديد الخطوات التي سيتم استخدامها في المشروع بدءاً من\n"
"                إنشاء المهمة، حتى إغلاق المهمة أو المشكلة.\n"
"                سوف تستخدم هذه المراحل في تتبع مستوى التقدم\n"
"                في حل مهمة أو مشكلة."

#. module: project
#: model:ir.model.fields,help:project.field_project_project__privacy_visibility
#: model:ir.model.fields,help:project.field_project_task__project_privacy_visibility
msgid ""
"People to whom this project and its tasks will be visible.\n"
"\n"
"- Invited internal users: when following a project, internal users will get access to all of its tasks without distinction. Otherwise, they will only get access to the specific tasks they are following.\n"
" A user with the project > administrator access right level can still access this project and its tasks, even if they are not explicitly part of the followers.\n"
"\n"
"- All internal users: all internal users can access the project and all of its tasks without distinction.\n"
"\n"
"- Invited portal users and all internal users: all internal users can access the project and all of its tasks without distinction.\n"
"When following a project, portal users will get access to all of its tasks without distinction. Otherwise, they will only get access to the specific tasks they are following.\n"
"\n"
"When a project is shared in read-only, the portal user is redirected to their portal. They can view the tasks, but not edit them.\n"
"When a project is shared in edit, the portal user is redirected to the kanban and list views of the tasks. They can modify a selected number of fields on the tasks.\n"
"\n"
"In any case, an internal user with no project access rights can still access a task, provided that they are given the corresponding URL (and that they are part of the followers if the project is private)."
msgstr ""
"الأشخاص الذين سيكون هذا المشروع ومهامه مرئياً بالنسبة لهم.\n"
"\n"
"- المستخدمين الداخليين المدعوين: عند متابعة مشروع ما، سيحصل المستخدمون الداخليون على صلاحية الوصول إلى كافة مهامه دون استثناء. وإلا، فسيحصلون فقط على صلاحية الوصول إلى المهام المحددة التي يتابعونها.\n"
"بإمكان المستخدم ذو صلاحية الوصول المشروع > المدير الوصول إلى هذا المشروع ومهامه، حتى وإن لم يكن أحد المتابعين.\n"
"\n"
"- كافة المستخدمين الداخليين: بوسع كافة المستخدمين الداخليين الوصول إلى المشروع وكافة مهامه دون استثناء.\n"
"\n"
"- مستخدمي البوابة المدعوين وكافة المستخدمين الداخليين: بوسع كافة المستخدمين الداخليين الوصول إلى المشروع وكافة مهامه دون استثناء.\n"
"عند متابعة مشروع ما، سيحصل مستخدمو البوابة على صلاحية الوصول إلى كافة مهامه دون استثناء. وإلا، فسيحصلون فقط على صلاحية الوصول إلى المهام المحددة التي يتابعونها.\n"
"\n"
"عندما تتم مشاركة مشروع للقراءة فقط، تتم إعادة توجيه مستخدم البوابة إلى بوابته. بإمكانه عرض المهام ولكن لن يكون بمقدوره تحريرها.\n"
"عندما تتم مشاركة مشروع للتحرير، تتم إعادة توجيه مستخدم البوابة إلى نافذتي عرض كانبان والقائمة للمهام. بإمكانه تعديل أي عدد من الحقول في المهتم.\n"
"\n"
"بأي حال، لا يزال بوسع مستخدم داخلي بلا صلاحيات وصول إلى المشاريع الوصول إلى المهام، إذا تم منحه رابط URL المناسب (ويجب أن يكون أحد المتابعين إذا كان المشروع خاصاً). "

#. module: project
#: model:ir.actions.server,name:project.unlink_project_action
#: model:ir.actions.server,name:project.unlink_task_type_action
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid "Delete"
msgstr "حذف"

#. module: project
#. openerp-web
#: code:addons/project/static/src/xml/project_templates.xml:0
#, python-format
msgid "Delete Milestone"
msgstr "حذف مؤشر التقدم "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_delete_wizard_form
msgid "Delete Project"
msgstr "حذف المشروع "

#. module: project
#: code:addons/project/models/project.py:0
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
#, python-format
msgid "Delete Stage"
msgstr "حذف المرحلة "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Delivered"
msgstr "تم التسليم"

#. module: project
#: code:addons/project/models/project.py:0
#: model:ir.model.fields,field_description:project.field_project_task__dependent_tasks_count
#, python-format
msgid "Dependent Tasks"
msgstr "المهام المعتمدة "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__description
#: model:ir.model.fields,field_description:project.field_project_task__description
#: model:ir.model.fields,field_description:project.field_project_task_type__description
#: model:ir.model.fields,field_description:project.field_project_update__description
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Description"
msgstr "الوصف"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Design"
msgstr "تصميم"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Determine the order in which to perform tasks"
msgstr "قم بتحديد الترتيب الذي ستؤدي به مهامك "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Development"
msgstr "التّطوير"

#. module: project
#: model:ir.model,name:project.model_digest_digest
msgid "Digest"
msgstr "الموجز "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Digital Marketing"
msgstr "التسويق الرقمي"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__disabled_rating_warning
msgid "Disabled Rating Warning"
msgstr "تحذيرات التقييم المعطلة "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_form.js:0
#: code:addons/project/static/src/js/project_list.js:0
#: model_terms:ir.ui.view,arch_db:project.project_delete_wizard_form
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified_footer
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
#, python-format
msgid "Discard"
msgstr "إهمال "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__display_access_mode
msgid "Display Access Mode"
msgstr "عرض وضع الوصول "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__display_name
#: model:ir.model.fields,field_description:project.field_project_delete_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_milestone__display_name
#: model:ir.model.fields,field_description:project.field_project_project__display_name
#: model:ir.model.fields,field_description:project.field_project_project_stage__display_name
#: model:ir.model.fields,field_description:project.field_project_share_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_tags__display_name
#: model:ir.model.fields,field_description:project.field_project_task__display_name
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__display_name
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__display_name
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__display_name
#: model:ir.model.fields,field_description:project.field_project_task_type__display_name
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_update__display_name
#: model:ir.model.fields,field_description:project.field_report_project_task_user__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__display_project_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__display_project_id
msgid "Display Project"
msgstr "عرض المشروع "

#. module: project
#: code:addons/project/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr "لا تملك صلاحيات الوصول. تخط هذه البيانات لبريد المستخدم الموجز. "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Documents"
msgstr "المستندات"

#. module: project
#. openerp-web
#: code:addons/project/models/project.py:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: model:project.project.stage,name:project.project_project_stage_2
#: model:project.task.type,name:project.project_personal_stage_admin_5
#: model:project.task.type,name:project.project_personal_stage_demo_5
#: model:project.task.type,name:project.project_stage_2
#, python-format
msgid "Done"
msgstr "منتهي "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Draft"
msgstr "مسودة"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Dropdown menu"
msgstr "القائمة المنسدلة"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_form
msgid "E.g: Product Launch"
msgstr "مثال: إطلاق المنتج "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_share_wizard__access_mode__edit
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Edit"
msgstr "تحرير"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_kanban.js:0
#, python-format
msgid "Edit Personal Stage"
msgstr "تحرير المرحلة الشخصية "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Edit recurring task"
msgstr "تحرير المهمة المتكررة "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid ""
"Edit tasks' description collaboratively in real time. See each author's text"
" in a distinct color."
msgstr ""
"تمكن من تحرير وصف المهام تعاونياً وفي الوقت الفعلي. سوف ترى نص كل كاتب بلون "
"مميز. "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Editing"
msgstr "تحرير"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__partner_email
#: model:ir.model.fields,field_description:project.field_project_task__partner_email
msgid "Email"
msgstr "البريد الإلكتروني"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__email_from
msgid "Email From"
msgstr "البريد الإلكتروني من "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_stage__mail_template_id
#: model:ir.model.fields,field_description:project.field_project_task_type__mail_template_id
msgid "Email Template"
msgstr "قالب البريد الإلكتروني"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__email_cc
#: model:ir.model.fields,field_description:project.field_project_update__email_cc
msgid "Email cc"
msgstr "البريد الإلكتروني cc"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_until
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_until
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_type__until
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_type__until
#: model_terms:ir.ui.view,arch_db:project.view_project
msgid "End Date"
msgstr "تاريخ الانتهاء"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__date_end
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_end
msgid "Ending Date"
msgstr "تاريخ الانتهاء"

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_project_project_date_greater
msgid "Error! Project start date must be before project end date."
msgstr "خطأ! يجب أن يسبق تاريخ بدء المشروع تاريخ انتهائه. "

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "Error! You cannot create a recursive hierarchy of tasks."
msgstr "خطأ! لا يمكنك إنشاء تدرج هرمي متداخل للمهام. "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid ""
"Everyone can propose ideas, and the Editor marks the best ones as %s. Attach"
" all documents or links to the task directly, to have all research "
"information centralized. %s"
msgstr ""
"بإمكان الجميع طرح الأفكار، ويعين المحرر أفضلها كـ%s. قم بإرفاق كافة "
"المستندات أو الروابط بالمهمة مباشرة، لحصر كافة المعلومات المرتبطة بالبحث "
"معاً. %s "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__date
msgid "Expiration Date"
msgstr "تاريخ انتهاء الصلاحية "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Extended Filters"
msgstr "عوامل التصفية التفصيلية "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Extra Info"
msgstr "معلومات إضافية"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__february
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__february
msgid "February"
msgstr "فبراير"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid ""
"Fill your Inbox easily with the email gateway. Periodically review your "
"Inbox and schedule tasks by moving them to other columns. Every day, you "
"review the %s column to move important tasks %s. Every Monday, you review "
"the %s column. %s"
msgstr ""
"املأ صندوق الوارد بسهولة باستخدام بوابة البريد الإلكتروني. راجع صندوق بريدك "
"دورياً وقم بجدولة المهام عن طريق نقلها إلى الأعمدة الأخرى. راجع العمود %s كل"
" يوم لنقل المهام المهمة إلى %s. وكل يوم اثنين، راجع العمود %s. %s "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Final Document"
msgstr "المستند النهائي"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_week__first
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_week__first
msgid "First"
msgstr "الأول"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_stage__fold
#: model:ir.model.fields,field_description:project.field_project_task_type__fold
msgid "Folded in Kanban"
msgstr "مطوي في عرض كانبان"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"Follow this project to automatically track the events associated to tasks "
"and issues of this project."
msgstr "تابع هذا المشروع لتتبع تلقائيًا الفعاليات المرتبطة بمهامه ومشكلاته."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Followed"
msgstr "تمت متابعته "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_extended
msgid "Followed Projects"
msgstr "المشاريع المُتابَعة "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_follower_ids
msgid "Followers"
msgstr "المتابعين "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعين (الشركاء) "

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_type_icon
#: model:ir.model.fields,help:project.field_project_task__activity_type_icon
#: model:ir.model.fields,help:project.field_project_update__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "أيقونة Font awesome مثال fa-tasks "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_type__forever
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_type__forever
msgid "Forever"
msgstr "للأبد"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__fri
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__fri
msgid "Fri"
msgstr "الجمعة"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__fri
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__fri
msgid "Friday"
msgstr "الجمعة"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Future Activities"
msgstr "الأنشطة المستقبلية"

#. module: project
#: model:ir.ui.menu,name:project.menu_tasks_config
msgid "GTD"
msgstr "إنجاز الأعمال"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_update_all_action
msgid ""
"Get a snapshot of the status of your project and share its progress with key"
" stakeholders."
msgstr "احصل على لقطة لحالة مشروعك وقم بمشاركة تقدمه مع الأطراف المعنية. "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Get customer feedback"
msgstr "احصل على ملاحظات العملاء "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Getting Things Done (GTD)"
msgstr "إنجاز الأعمال"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__sequence
msgid "Gives the sequence order when displaying a list of Projects."
msgstr "يظهر ترتيب التسلسل عند عرض قائمة المشاريع."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__sequence
msgid "Gives the sequence order when displaying a list of tasks."
msgstr "يعطي النظام تسلسل عند عرض قائمة المهام."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__legend_done
msgid "Green Kanban Label"
msgstr "بطاقة عنوان كانبان خضراء"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__legend_normal
msgid "Grey Kanban Label"
msgstr "بطاقة عنوان كانبان رمادية"

#. module: project
#: code:addons/project/models/project.py:0
#: code:addons/project/models/project.py:0
#, python-format
msgid "Gross Margin"
msgstr "الهامش الإجمالي "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_collaborator_view_search
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Group By"
msgstr "التجميع حسب "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid ""
"Handle your idea gathering within Tasks of your new Project and discuss them"
" in the chatter of the tasks. Use the %s and %s to signalize what is the "
"current status of your Idea. %s"
msgstr ""
"تمكن من تجميع أفكارك ضمن مهام مشروعك الجديد وقم بمناقشتها في دردشة المهام. "
"استخدم %s و %s للإشارة إلى حالة فكرتك. %s"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Handoff"
msgstr "التسليم "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Happy face"
msgstr "وجه سعيد"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__has_message
#: model:ir.model.fields,field_description:project.field_project_project__has_message
#: model:ir.model.fields,field_description:project.field_project_task__has_message
#: model:ir.model.fields,field_description:project.field_project_update__has_message
msgid "Has Message"
msgstr "يحتوي على رسالة "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__priority__2
msgid "High"
msgstr "مرتفع"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Hours"
msgstr "ساعات "

#. module: project
#: model:ir.model.fields,help:project.field_project_project__rating_status
msgid ""
"How to get customer feedback?\n"
"- Rating when changing stage: an email will be sent when a task is pulled to another stage.\n"
"- Periodic rating: an email will be sent periodically.\n"
"\n"
"Don't forget to set up the email templates on the stages for which you want to get customer feedback."
msgstr ""
"كيف تحصل على ملاحظات العملاء؟\n"
"- التقييم عند تغيير المرحلة: سوف يتم إرسال بريد إلكتروني عندما يتم سحب مهمة إذا مرحلة أخرى.\n"
"- التقييم الدوري: سوف يتم إرسال بريد إلكتروني دورياً.\n"
"\n"
"لا تنسَ ضبط قوالب البريد الإلكتروني الخاصة بك في المراحل التي ترغب في الحصول على ملاحظات العملاء من أجلها. "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "How’s this project going?"
msgstr "ما هي أحوال مشروعك؟ "

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "I take it"
msgstr "سآخذها "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__id
#: model:ir.model.fields,field_description:project.field_project_delete_wizard__id
#: model:ir.model.fields,field_description:project.field_project_milestone__id
#: model:ir.model.fields,field_description:project.field_project_project__id
#: model:ir.model.fields,field_description:project.field_project_project_stage__id
#: model:ir.model.fields,field_description:project.field_project_share_wizard__id
#: model:ir.model.fields,field_description:project.field_project_tags__id
#: model:ir.model.fields,field_description:project.field_project_task__id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__id
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__id
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__id
#: model:ir.model.fields,field_description:project.field_project_task_type__id
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__id
#: model:ir.model.fields,field_description:project.field_project_update__id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__id
msgid "ID"
msgstr "المُعرف"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"مُعرف السجل الأصلي الذي يحتوي اللقب (مثال: 'المشروع' يحمل لقب إنشاء المهمة)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_exception_icon
#: model:ir.model.fields,field_description:project.field_project_task__activity_exception_icon
#: model:ir.model.fields,field_description:project.field_project_update__activity_exception_icon
msgid "Icon"
msgstr "الأيقونة"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_exception_icon
#: model:ir.model.fields,help:project.field_project_task__activity_exception_icon
#: model:ir.model.fields,help:project.field_project_update__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "الأيقونة للإشارة إلى استثناء النشاط"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Ideas"
msgstr "أفكار"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_needaction
#: model:ir.model.fields,help:project.field_project_milestone__message_unread
#: model:ir.model.fields,help:project.field_project_project__message_needaction
#: model:ir.model.fields,help:project.field_project_project__message_unread
#: model:ir.model.fields,help:project.field_project_task__message_needaction
#: model:ir.model.fields,help:project.field_project_task__message_unread
#: model:ir.model.fields,help:project.field_project_update__message_needaction
#: model:ir.model.fields,help:project.field_project_update__message_unread
msgid "If checked, new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة تحتاج لرؤيتها."

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_has_error
#: model:ir.model.fields,help:project.field_project_milestone__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_project__message_has_error
#: model:ir.model.fields,help:project.field_project_project__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_task__message_has_error
#: model:ir.model.fields,help:project.field_project_task__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_update__message_has_error
#: model:ir.model.fields,help:project.field_project_update__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا كان محددًا، فقد حدث خطأ في تسليم بعض الرسائل."

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__rating_template_id
msgid ""
"If set and if the project's rating configuration is 'Rating when changing "
"stage', then an email will be sent to the customer when the task reaches "
"this step."
msgstr ""
"إذا كان محددًا وإذا كانت إعدادات تقييم المشروع المحددة هي 'التقييم عند تغيير"
" المرحلة'، سيتم إرسال رسالة بريد الكتروني للعميل عند وصول المهمة لهذه "
"الخطوة."

#. module: project
#: model:ir.model.fields,help:project.field_project_project_stage__mail_template_id
msgid ""
"If set, an email will be sent to the customer when the project reaches this "
"step."
msgstr ""
"إذا كان محدداً، سيتم إرسال رسالة بريد الكتروني إلى العميل عند وصول المشروع "
"إلى هذه الخطوة. "

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__mail_template_id
msgid ""
"If set, an email will be sent to the customer when the task or issue reaches"
" this step."
msgstr ""
"إذا كان محدداً، سيتم إرسال رسالة بريد الكتروني إلى العميل عند وصول المهمة أو"
" المشكلة إلى هذه الخطوة. "

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"إذا كان محدداً، سوف يتم إرسال هذا المحتوى تلقائياً إلى المستخدمين غير المصرح"
" لهم عوضاً عن الرسالة الافتراضية. "

#. module: project
#: model:ir.model.fields,help:project.field_project_project__active
msgid ""
"If the active field is set to False, it will allow you to hide the project "
"without removing it."
msgstr ""
"إذا تم تعيين قيمة الحقل النشط إلى خطأ، سوف يكون باستطاعتك إخفاء المشروع دون "
"إزالته. "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__priority__1
msgid "Important"
msgstr "مهم"

#. module: project
#. openerp-web
#: code:addons/project/models/project.py:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: model:ir.model.fields.selection,name:project.selection__project_task__kanban_state__normal
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__normal
#: model:project.project.stage,name:project.project_project_stage_1
#: model:project.task,legend_normal:project.project_task_1
#: model:project.task,legend_normal:project.project_task_10
#: model:project.task,legend_normal:project.project_task_11
#: model:project.task,legend_normal:project.project_task_12
#: model:project.task,legend_normal:project.project_task_19
#: model:project.task,legend_normal:project.project_task_2
#: model:project.task,legend_normal:project.project_task_20
#: model:project.task,legend_normal:project.project_task_21
#: model:project.task,legend_normal:project.project_task_22
#: model:project.task,legend_normal:project.project_task_24
#: model:project.task,legend_normal:project.project_task_25
#: model:project.task,legend_normal:project.project_task_26
#: model:project.task,legend_normal:project.project_task_27
#: model:project.task,legend_normal:project.project_task_28
#: model:project.task,legend_normal:project.project_task_29
#: model:project.task,legend_normal:project.project_task_3
#: model:project.task,legend_normal:project.project_task_30
#: model:project.task,legend_normal:project.project_task_31
#: model:project.task,legend_normal:project.project_task_32
#: model:project.task,legend_normal:project.project_task_33
#: model:project.task,legend_normal:project.project_task_34
#: model:project.task,legend_normal:project.project_task_35
#: model:project.task,legend_normal:project.project_task_36
#: model:project.task,legend_normal:project.project_task_4
#: model:project.task,legend_normal:project.project_task_5
#: model:project.task,legend_normal:project.project_task_6
#: model:project.task,legend_normal:project.project_task_7
#: model:project.task,legend_normal:project.project_task_8
#: model:project.task,legend_normal:project.project_task_9
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_0
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_1
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_2
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_3
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_4
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_5
#: model:project.task.type,legend_normal:project.project_personal_stage_admin_6
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_0
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_1
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_2
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_3
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_4
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_5
#: model:project.task.type,legend_normal:project.project_personal_stage_demo_6
#: model:project.task.type,legend_normal:project.project_stage_0
#: model:project.task.type,legend_normal:project.project_stage_1
#: model:project.task.type,legend_normal:project.project_stage_2
#: model:project.task.type,legend_normal:project.project_stage_3
#: model:project.task.type,name:project.project_stage_1
#, python-format
msgid "In Progress"
msgstr "قيد التنفيذ"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "In development"
msgstr "قيد التطوير "

#. module: project
#. openerp-web
#: code:addons/project/models/project.py:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: model:project.task.type,name:project.project_personal_stage_admin_0
#: model:project.task.type,name:project.project_personal_stage_demo_0
#, python-format
msgid "Inbox"
msgstr "صندوق الوارد"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__planned_hours
msgid "Initially Planned Hours"
msgstr "ساعات العمل المبدئية المخطط لها"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_id
msgid ""
"Internal email associated with this project. Incoming emails are "
"automatically synchronized with Tasks (or optionally Issues if the Issue "
"Tracker module is installed)."
msgstr ""
"البريد الإلكتروني الداخلي المرتبط بهذا المشروع. تتم مزامنة رسائل البريد "
"الإلكتروني الواردة تلقائياً مع المهام (أو يقوم بالإصدار اختيارياً إذا كان "
"تطبيق تثبيت تتبع المشاكل مثبتاً). "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Invite People"
msgstr "دعوة الأفراد "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__followers
msgid "Invited employees"
msgstr "الموظفين المدعوين "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__portal
msgid "Invited portal users and all employees"
msgstr "مستخدمي البوابة المدعوين وكافة الموظفين "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__is_deadline_exceeded
msgid "Is Deadline Exceeded"
msgstr "تم تجاوز الموعد النهائي "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__is_deadline_future
msgid "Is Deadline Future"
msgstr "الموعد النهائي في المستقبل "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_project__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_task__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_update__message_is_follower
msgid "Is Follower"
msgstr "متابع"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__is_private
msgid "Is Private"
msgstr "خاص "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__partner_is_company
msgid "Is a Company"
msgstr "شركة "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_tags_search_view
msgid "Issue Version"
msgstr "إصدار المشكلة"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_list.js:0
#, python-format
msgid "It seems that some tasks are part of a recurrence."
msgstr "يبدو أن بعض مهامك هي جزء من تكرار. "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_form.js:0
#: code:addons/project/static/src/js/project_list.js:0
#, python-format
msgid "It seems that this task is part of a recurrence."
msgstr "يبدو أن هذه المهمة هي جزء من تكرار. "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__january
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__january
msgid "January"
msgstr "يناير"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__july
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__july
msgid "July"
msgstr "يوليو"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__june
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__june
msgid "June"
msgstr "يونيو"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__legend_blocked
msgid "Kanban Blocked Explanation"
msgstr "تفسير حالة محجوب في واجهة كانبان "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__legend_normal
msgid "Kanban Ongoing Explanation"
msgstr "تفسير حالة قيد التقدم في واجهة كانبان"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__state
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Kanban State"
msgstr "حالة كانبان"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__kanban_state_label
msgid "Kanban State Label"
msgstr "بطاقة عنوان حالة كانبان"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__legend_done
msgid "Kanban Valid Explanation"
msgstr "تفسير حالة صالح في واجهة كانبان"

#. module: project
#: model:ir.model.fields,field_description:project.field_digest_digest__kpi_project_task_opened_value
msgid "Kpi Project Task Opened Value"
msgstr "قيمة المؤشر الرئيسي المفتوحة لمهمة المشروع"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__label_tasks
msgid "Label used for the tasks of the project."
msgstr "بطاقة العنوان المستخدمة لمهام المشروع. "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_week__last
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_week__last
msgid "Last"
msgstr "آخِر "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
msgid "Last 30 Days"
msgstr "آخر 30 يوم"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator____last_update
#: model:ir.model.fields,field_description:project.field_project_delete_wizard____last_update
#: model:ir.model.fields,field_description:project.field_project_milestone____last_update
#: model:ir.model.fields,field_description:project.field_project_project____last_update
#: model:ir.model.fields,field_description:project.field_project_project_stage____last_update
#: model:ir.model.fields,field_description:project.field_project_share_wizard____last_update
#: model:ir.model.fields,field_description:project.field_project_tags____last_update
#: model:ir.model.fields,field_description:project.field_project_task____last_update
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report____last_update
#: model:ir.model.fields,field_description:project.field_project_task_recurrence____last_update
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal____last_update
#: model:ir.model.fields,field_description:project.field_project_task_type____last_update
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard____last_update
#: model:ir.model.fields,field_description:project.field_project_update____last_update
#: model:ir.model.fields,field_description:project.field_report_project_task_user____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
msgid "Last Month"
msgstr "الشهر الماضي"

#. module: project
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__date_last_stage_update
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_last_stage_update
#, python-format
msgid "Last Stage Update"
msgstr "آخر تحديث للمرحلة"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__last_update_id
msgid "Last Update"
msgstr "آخر تحديث"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__last_update_color
msgid "Last Update Color"
msgstr "لون آخر تحديث "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__last_update_status
msgid "Last Update Status"
msgstr "حالة آخر تحديث "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__write_date
msgid "Last Updated On"
msgstr "آخر تحديث في"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__write_uid
#: model:ir.model.fields,field_description:project.field_project_delete_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_milestone__write_uid
#: model:ir.model.fields,field_description:project.field_project_project__write_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage__write_uid
#: model:ir.model.fields,field_description:project.field_project_share_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_tags__write_uid
#: model:ir.model.fields,field_description:project.field_project_task__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_type__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_update__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__write_date
#: model:ir.model.fields,field_description:project.field_project_delete_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_milestone__write_date
#: model:ir.model.fields,field_description:project.field_project_project__write_date
#: model:ir.model.fields,field_description:project.field_project_project_stage__write_date
#: model:ir.model.fields,field_description:project.field_project_share_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_tags__write_date
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__write_date
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__write_date
#: model:ir.model.fields,field_description:project.field_project_task_type__write_date
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_update__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Late Activities"
msgstr "الأنشطة المتأخرة"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Late Tasks"
msgstr "المهام المتأخرة "

#. module: project
#: code:addons/project/models/project.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_4
#: model:project.task.type,name:project.project_personal_stage_demo_4
#, python-format
msgid "Later"
msgstr "لاحقاً"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Latest Rating: Dissatisfied"
msgstr "آخر تقييم: غير راضٍ "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Latest Rating: Okay"
msgstr "آخر تقييم: جيد "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Latest Rating: Satisfied"
msgstr "التقييم الأخير: راضٍ"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's create your first <b>project</b>."
msgstr "فلنقم بإنشاء <b>مشروعك</b> الأول. "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's create your first <b>task</b>."
msgstr "فلنقم بإنشاء <b>مهمتك</b> الأولى. "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Let's go back to the <b>kanban view</b> to have an overview of your next "
"tasks."
msgstr ""
"فلنعد إلى <b>طريقة عرض كانبان</b> للحصول على نظرة عامة على مهامك التالية. "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Let's start working on your task."
msgstr "فلنبدأ بالعمل على مهامك "

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_project_report
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_task
msgid "Let's wait for your customers to manifest themselves."
msgstr "فلننتظر حتى يقوم عملاؤك بإظهار أنفسهم. "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__share_link
msgid "Link"
msgstr "الرابط"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__email_cc
#: model:ir.model.fields,help:project.field_project_update__email_cc
msgid "List of cc from incoming emails."
msgstr "قائمة cc من رسائل البريد الإلكتروني الواردة."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Live"
msgstr "مباشر "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Logo Design"
msgstr "تصميم الشعار"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Long Term"
msgstr "طويل الأمد "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__priority__0
msgid "Low"
msgstr "منخفض"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_main_attachment_id
#: model:ir.model.fields,field_description:project.field_project_project__message_main_attachment_id
#: model:ir.model.fields,field_description:project.field_project_task__message_main_attachment_id
#: model:ir.model.fields,field_description:project.field_project_update__message_main_attachment_id
msgid "Main Attachment"
msgstr "المرفق الرئيسي"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__attachment_ids
msgid "Main Attachments"
msgstr "المُرفقات الرئيسية"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid ""
"Manage the lifecycle of your project using the kanban view. Add newly "
"acquired projects, assign them and use the %s and %s to define if the "
"project is ready for the next step. %s"
msgstr ""
"قم بإدارة دورة حياة مشروعك في طريقة عرض كانبان. أضف المشاريع التي تم الحصول "
"عليها حديثاً ثم عينها واستخدم %s و %s لتحديد ما إذا كان المشروع جاهزاً "
"للخطوة التالية. %s "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Manufacturing"
msgstr "التصنيع"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__march
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__march
msgid "March"
msgstr "مارس"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Material Sourcing"
msgstr "مصادر المواد "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__may
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__may
msgid "May"
msgstr "مايو"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__favorite_user_ids
msgid "Members"
msgstr "الأعضاء"

#. module: project
#: model:ir.model,name:project.model_ir_ui_menu
msgid "Menu"
msgstr "القائمة"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_error
#: model:ir.model.fields,field_description:project.field_project_project__message_has_error
#: model:ir.model.fields,field_description:project.field_project_task__message_has_error
#: model:ir.model.fields,field_description:project.field_project_update__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسائل"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/right_panel/project_utils.js:0
#: model:ir.model.fields,field_description:project.field_project_project__milestone_ids
#, python-format
msgid "Milestone"
msgstr "مؤشر التقدم "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__milestone_count
msgid "Milestone Count"
msgstr "عدد مؤشرات التقدم "

#. module: project
#. openerp-web
#: code:addons/project/static/src/xml/project_templates.xml:0
#: model:ir.actions.act_window,name:project.project_milestone_all
#, python-format
msgid "Milestones"
msgstr "مؤشرات التقدم "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Mixing"
msgstr "خلط "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__mon
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__mon
msgid "Mon"
msgstr "الإثنين "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__mon
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__mon
msgid "Monday"
msgstr "الإثنين "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__month
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__month
msgid "Months"
msgstr "شهور"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__my_activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_task__my_activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_update__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "نهاية الوقت المعين للنشاط"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_extended
msgid "My Favorite Projects"
msgstr "مشاريعي المفضلة "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "My Favorites"
msgstr "مفضلاتي"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_extended
msgid "My Projects"
msgstr "مشاريعي "

#. module: project
#: model:ir.actions.act_window,name:project.action_view_all_task
#: model:ir.ui.menu,name:project.menu_project_management
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "My Tasks"
msgstr "مهامي "

#. module: project
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_milestone__name
#: model:ir.model.fields,field_description:project.field_project_project__name
#: model:ir.model.fields,field_description:project.field_project_project_stage__name
#: model:ir.model.fields,field_description:project.field_project_tags__name
#: model:ir.model.fields,field_description:project.field_project_task_type__name
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.task_type_search
#: model_terms:ir.ui.view,arch_db:project.view_project
#, python-format
msgid "Name"
msgstr "الاسم"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__name_cropped
msgid "Name Cropped"
msgstr "الاسم المختصر "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Name of the tasks"
msgstr "أسماء المهام "

#. module: project
#: model:project.task,legend_blocked:project.project_task_10
#: model:project.task,legend_blocked:project.project_task_11
#: model:project.task,legend_blocked:project.project_task_19
#: model:project.task,legend_blocked:project.project_task_21
#: model:project.task,legend_blocked:project.project_task_22
#: model:project.task,legend_blocked:project.project_task_27
#: model:project.task,legend_blocked:project.project_task_28
#: model:project.task,legend_blocked:project.project_task_29
#: model:project.task,legend_blocked:project.project_task_34
#: model:project.task,legend_blocked:project.project_task_35
#: model:project.task,legend_blocked:project.project_task_36
#: model:project.task,legend_blocked:project.project_task_4
#: model:project.task,legend_blocked:project.project_task_5
#: model:project.task,legend_blocked:project.project_task_6
#: model:project.task,legend_blocked:project.project_task_7
#: model:project.task.type,legend_blocked:project.project_stage_1
msgid "Need functional or technical help"
msgstr "بحاجة إلى مساعدة فنية أو تقنية "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Neutral face"
msgstr "وجه محايد"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: model:project.task.type,name:project.project_stage_0
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_form
#, python-format
msgid "New"
msgstr "جديد"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/right_panel/project_utils.js:0
#, python-format
msgid "New Milestone"
msgstr "مؤشر تقدم جديد "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "New Orders"
msgstr "الطلبات الجديدة "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "New Projects"
msgstr "المشاريع الجديدة "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "New Request"
msgstr "طلب جديد"

#. module: project
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Newest"
msgstr "الأحدث"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_activity
msgid "Next Activities"
msgstr "الأنشطة التالية"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_calendar_event_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_calendar_event_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "الفعالية التالية في تقويم الأنشطة "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_task__activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_update__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "الموعد النهائي للنشاط التالي"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_summary
#: model:ir.model.fields,field_description:project.field_project_task__activity_summary
#: model:ir.model.fields,field_description:project.field_project_update__activity_summary
msgid "Next Activity Summary"
msgstr "ملخص النشاط التالي"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_type_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_type_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_type_id
msgid "Next Activity Type"
msgstr "نوع النشاط التالي"

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "Next Occurrences:"
msgstr "الوقائع التالية: "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__next_recurrence_date
msgid "Next Recurrence Date"
msgstr "تاريخ التكرار القادم "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurrence_message
msgid "Next Recurrencies"
msgstr "التكرارات التالية "

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "No Subject"
msgstr "بلا موضوع"

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_project_report
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_task
msgid "No customer ratings yet"
msgstr "لا توجد أي تقييمات عملاء بعد "

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_user_tree
msgid "No data yet!"
msgstr "لا توجد أي بيانات بعد! "

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_milestone_all
msgid "No milestones found. Let's create one!"
msgstr "لم يتم العثور على أي مؤشرات للتقدم. فلنقم بإنشاء واحد! "

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_group_stage
msgid "No projects found. Let's create one!"
msgstr "لم يتم العثور على أي مشروع. فلننشئ واحداً! "

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form
#: model_terms:ir.actions.act_window,help:project.project_project_stage_configure
msgid "No stages found. Let's create one!"
msgstr "لم يتم العثور على أي مراحل. فلنقم بإنشائها! "

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_tags_action
msgid "No tags found. Let's create one!"
msgstr "لم يتم العثور على أي علامات تصنيف. فلنقم بإنشائها! "

#. module: project
#: model_terms:ir.actions.act_window,help:project.act_project_project_2_project_task_all
#: model_terms:ir.actions.act_window,help:project.action_view_all_task
#: model_terms:ir.actions.act_window,help:project.action_view_task
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action
msgid "No tasks found. Let's create one!"
msgstr "لم يتم العثور على أي مهام. فلنقم بإنشائها! "

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_update_all_action
msgid "No updates found. Let's create one!"
msgstr "لم يتم العثور على أي تحديثات. فلنقم بإنشائها! "

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "None"
msgstr "لا شيء"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__priority__0
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__priority__1
msgid "Normal"
msgstr "عادي"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__note
msgid "Note"
msgstr "الملاحظات"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__november
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__november
msgid "November"
msgstr "نوفمبر"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_project__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_task__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_update__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_type__after
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_type__after
msgid "Number of Repetitions"
msgstr "عدد مرات التكرار"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__tasks_count
msgid "Number of Tasks"
msgstr "عدد المهام "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__recurrence_left
msgid "Number of Tasks Left to Create"
msgstr "عدد المهام المتبقية لإنشائها "

#. module: project
#: model:ir.model.fields,help:project.field_report_project_task_user__working_days_close
msgid "Number of Working Days to close the task"
msgstr "عدد أيام العمل حتى إغلاق المهمة"

#. module: project
#: model:ir.model.fields,help:project.field_report_project_task_user__working_days_open
msgid "Number of Working Days to open the task"
msgstr "عدد أيام العمل حتى فتح المهمة "

#. module: project
#: model:ir.model.fields,help:project.field_report_project_task_user__working_hours_close
msgid "Number of Working Hours to close the task"
msgstr "عدد ساعات العمل حتى إغلاق المهمة "

#. module: project
#: model:ir.model.fields,help:project.field_report_project_task_user__working_hours_open
msgid "Number of Working Hours to open the task"
msgstr "عدد ساعات العمل حتى فتح المهمة "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__doc_count
msgid "Number of documents attached"
msgstr "عدد المستندات المرفقة"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_project__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_task__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_update__message_has_error_counter
msgid "Number of errors"
msgstr "عدد الأخطاء "

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_project__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_task__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_update__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "عدد الرسائل التي تتطلب إجراء"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_project__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_task__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_update__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل الحادث بها خطأ في التسليم"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_unread_counter
#: model:ir.model.fields,help:project.field_project_project__message_unread_counter
#: model:ir.model.fields,help:project.field_project_task__message_unread_counter
#: model:ir.model.fields,help:project.field_project_update__message_unread_counter
msgid "Number of unread messages"
msgstr "عدد الرسائل غير المقروءة "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__october
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__october
msgid "October"
msgstr "أكتوبر"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__off_track
#: model:ir.model.fields.selection,name:project.selection__project_update__status__off_track
msgid "Off Track"
msgstr "خارج المسار "

#. module: project
#: model:project.project,name:project.project_project_1
msgid "Office Design"
msgstr "تصميم المكتب "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_delete_wizard_form
msgid "Ok"
msgstr "موافق"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Old Completed Sprint"
msgstr "سبرينت قديم مكتمل"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__on_hold
#: model:ir.model.fields.selection,name:project.selection__project_update__status__on_hold
msgid "On Hold"
msgstr "قيد الانتظار "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__on_track
#: model:ir.model.fields.selection,name:project.selection__project_update__status__on_track
msgid "On Track"
msgstr "في المسار "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__monthly
msgid "Once a Month"
msgstr "مرة في الشهر"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Open"
msgstr "فتح"

#. module: project
#: model:ir.model.fields,field_description:project.field_digest_digest__kpi_project_task_opened
msgid "Open Tasks"
msgstr "المهام المفتوحة"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Open tasks"
msgstr "المهام المفتوحة "

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"معرف اختياري لمناقشة (سجل) سيتم إرفاق كافة رسائل البريد الإلكتروني الواردة "
"فيه، حتى لو لم يتم الرد عليها. إذا تم تعيين قيمة له، سيعطل هذا إنشاء السجلات"
" الجديدة بالكامل. "

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Others"
msgstr "آخرون"

#. module: project
#: model:ir.actions.act_window,name:project.action_view_task_overpassed_draft
msgid "Overpassed Tasks"
msgstr "المهام الزائدة"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__legend_blocked
#: model:ir.model.fields,help:project.field_project_task_type__legend_blocked
msgid ""
"Override the default value displayed for the blocked state for kanban "
"selection when the task or issue is in that stage."
msgstr ""
"تجاوز القيمة الافتراضية المعروضة لحالة \"محجوبة\" باختيارات كانبان، عندما "
"تكون المهمة أو المشكلة في تلك المرحلة."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__legend_done
#: model:ir.model.fields,help:project.field_project_task_type__legend_done
msgid ""
"Override the default value displayed for the done state for kanban selection"
" when the task or issue is in that stage."
msgstr ""
"تجاوز القيمة الافتراضية المعروضة لحالة \"منتهي\" باختيارات كانبان، عندما "
"تكون المهمة أو المشكلة في تلك المرحلة. "

#. module: project
#: model:ir.model.fields,help:project.field_project_task__legend_normal
#: model:ir.model.fields,help:project.field_project_task_type__legend_normal
msgid ""
"Override the default value displayed for the normal state for kanban "
"selection when the task or issue is in that stage."
msgstr ""
"تجاوز القيمة الافتراضية المعروضة لحالة \"طبيعي\" باختيارات كانبان، عندما "
"تكون المهمة أو المشكلة في تلك المرحلة. "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_user_id
msgid "Owner"
msgstr "المالك"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Page Ideas"
msgstr "أفكار الصفحة"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_parent_model_id
msgid "Parent Model"
msgstr "النموذج الأصلي "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "معرف مناقشة السجل الأصلي"

#. module: project
#: code:addons/project/models/project.py:0
#: model:ir.model.fields,field_description:project.field_project_task__parent_id
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#, python-format
msgid "Parent Task"
msgstr "المهمة الأصل"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"النموذج الرئيسي الذي يحتفظ بلقب البريد الإلكتروني. ليس بالضرورة أن يكون "
"النموذج الذي يحتفظ بمرجع لقب البريد الإلكتروني هو النموذج المحدد في الحقل "
"alias_model_id (مثال: المشروع (parent_model) والمهمة (model))"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr "نسبة التقييمات السعيدة "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Percentage of happy ratings over the past 30 days."
msgstr "نسبة التقييمات السعيدة خلال الـ 30 يوماً الأخيرة. "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status__periodic
msgid "Periodic rating"
msgstr "التقييم الدوري "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__personal_stage_type_ids
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Personal Stage"
msgstr "مرحلة شخصية "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__personal_stage_id
msgid "Personal Stage State"
msgstr "حالة المرحلة الشخصية "

#. module: project
#: model:ir.model,name:project.model_project_task_stage_personal
msgid "Personal Task Stage"
msgstr "مرحلة المهمة الشخصية "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__personal_stage_type_id
msgid "Personal User Stage"
msgstr "مرحلة المستخدم الشخصية "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__partner_phone
#: model:ir.model.fields,field_description:project.field_project_task__partner_phone
msgid "Phone"
msgstr "رقم الهاتف"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid ""
"Plan resource allocation across projets and tasks, and estimate deadlines "
"more accurately"
msgstr ""
"قم بتخطيط تخصيص الموارد عبر كافة المشاريع والمهام وتقدير المواعيد النهائية "
"بدقة أكبر "

#. module: project
#: model:ir.model.fields,field_description:project.field_res_config_settings__module_project_forecast
msgid "Planning"
msgstr "التخطيط"

#. module: project
#: code:addons/project/models/analytic_account.py:0
#, python-format
msgid ""
"Please remove existing tasks in the project linked to the accounts you want "
"to delete."
msgstr ""
"يرجى إزالة المهام الموجودة في المشروع المرتبط بالحسابات التي ترغب في حذفها. "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Podcast and Video Production"
msgstr "الإذاعة وإنتاج مقاطع الفيديو "

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"سياسة لنشر رسالة في المستند باستخدام بوابة البريد الإلكتروني.\n"
"- الجميع: يمكن للجميع النشر\n"
"- الشركاء: الشركاء المعتمدون فقط\n"
"- المتابعون: فقط متابعو المستند ذي الصلة أو أعضاء القنوات التالية.\n"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_url
#: model:ir.model.fields,field_description:project.field_project_task__access_url
msgid "Portal Access URL"
msgstr "رابط الوصول إلى البوابة "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__portal_user_names
msgid "Portal User Names"
msgstr "أسماء مستخدمي البوابة "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid ""
"Prioritize Tasks by using the %s icon.%s Use the %s button to inform your "
"colleagues that a task is ready for the next stage.%s Use the %s to indicate"
" a problem or a need for discussion on a task.%s"
msgstr ""
"قم بوضع المهام كأولوية باستخدام أيقونة %s.%s استخدم زر %s لإخطار زملائك في "
"العمل بأن المهمة جاهزة للمرحلة التالية.%s استخدم %s للإشارة إلى مشكلة أو "
"الحاجة إلى النقاش في مهمة.%s "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid ""
"Prioritize Tasks by using the %s icon.%s Use the %s button to signalize to "
"your colleagues that a task is ready for the next stage.%s Use the %s to "
"signalize a problem or a need for discussion on a task.%s"
msgstr ""
"قم بوضع المهام كأولوية باستخدام أيقونة %s.%s استخدم زر %s للإشارة لزملائك في"
" العمل بأن المهمة جاهزة للمرحلة التالية.%s استخدم %s للإشارة إلى مشكلة أو "
"الحاجة إلى النقاش في مهمة.%s "

#. module: project
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_report_project_task_user__priority
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#, python-format
msgid "Priority"
msgstr "الأولوية"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Private"
msgstr "خاص"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Profitability"
msgstr "الربحية "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__progress
#: model_terms:ir.ui.view,arch_db:project.project_update_view_kanban
#: model_terms:ir.ui.view,arch_db:project.project_update_view_tree
msgid "Progress"
msgstr "مدى التقدم "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__progress_percentage
msgid "Progress Percentage"
msgstr "نسبة التقدم "

#. module: project
#. openerp-web
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/static/src/js/project_rating_graph_view.js:0
#: code:addons/project/static/src/js/project_rating_pivot_view.js:0
#: model:ir.model,name:project.model_project_project
#: model:ir.model.fields,field_description:project.field_project_milestone__project_id
#: model:ir.model.fields,field_description:project.field_project_task__project_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__project_id
#: model:ir.model.fields,field_description:project.field_project_update__project_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__project_id
#: model:ir.ui.menu,name:project.menu_main_pm
#: model_terms:ir.ui.view,arch_db:project.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_collaborator_view_search
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#, python-format
msgid "Project"
msgstr "المشروع"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__project_analytic_account_id
msgid "Project Analytic Account"
msgstr "حساب المشروع التحليلي "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_collaborator_view_form
msgid "Project Collaborator"
msgstr "المتعاون في المشروع "

#. module: project
#: model:ir.actions.act_window,name:project.project_collaborator_action
#: model_terms:ir.ui.view,arch_db:project.project_sharing_access_view_tree
msgid "Project Collaborators"
msgstr "المتعاونين في المشروع "

#. module: project
#: model:ir.model.fields,field_description:project.field_account_analytic_account__project_count
msgid "Project Count"
msgstr "عدد المشاريع"

#. module: project
#: model:ir.model,name:project.model_project_delete_wizard
msgid "Project Delete Wizard"
msgstr "معالج حذف المشاريع "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__user_id
#: model:ir.model.fields,field_description:project.field_project_task__manager_id
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Project Manager"
msgstr "مدير المشروع"

#. module: project
#: model:ir.model,name:project.model_project_milestone
msgid "Project Milestone"
msgstr "مؤشر تقدم المشروع "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
msgid "Project Name"
msgstr "اسم المشروع"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__project_id
msgid "Project Shared"
msgstr "مشروع مُتَشارَك "

#. module: project
#: model:ir.actions.act_window,name:project.project_sharing_project_task_action
#: model:ir.model,name:project.model_project_share_wizard
msgid "Project Sharing"
msgstr "مشاركة المشروع "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "Project Sharing: Task"
msgstr "مشاركة المشروع: المهمة "

#. module: project
#: model:ir.model,name:project.model_project_project_stage
msgid "Project Stage"
msgstr "مرحلة المشروع "

#. module: project
#: model:mail.message.subtype,name:project.mt_project_stage_change
msgid "Project Stage Changed"
msgstr "تغيرت مرحلة المشروع "

#. module: project
#: model:ir.model,name:project.model_project_task_type_delete_wizard
msgid "Project Stage Delete Wizard"
msgstr "معالج حذف مرحلة المشروع "

#. module: project
#: model:ir.actions.act_window,name:project.project_project_stage_configure
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_stages
#: model:ir.ui.menu,name:project.menu_project_config_project_stage
msgid "Project Stages"
msgstr "مراحل المشروع "

#. module: project
#: model:ir.model,name:project.model_project_tags
msgid "Project Tags"
msgstr "علامات تصنيف المشاريع "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_activity
msgid "Project Tasks"
msgstr "مهام المشروع"

#. module: project
#: model:ir.model,name:project.model_project_update
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
msgid "Project Update"
msgstr "تحديث المشروع "

#. module: project
#: model:ir.actions.act_window,name:project.project_update_all_action
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban_inherit_project
msgid "Project Updates"
msgstr "تحديثات المشروع "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__project_privacy_visibility
msgid "Project Visibility"
msgstr "قابلية ظهور المشروع "

#. module: project
#: model:ir.actions.act_window,name:project.dblc_proj
msgid "Project's tasks"
msgstr "مهام المشروع"

#. module: project
#: model:ir.actions.server,name:project.ir_cron_recurring_tasks_ir_actions_server
#: model:ir.cron,cron_name:project.ir_cron_recurring_tasks
#: model:ir.cron,name:project.ir_cron_recurring_tasks
msgid "Project: Create Recurring Tasks"
msgstr "المشروع: إنشاء مهام متكررة "

#. module: project
#: model:ir.actions.server,name:project.ir_cron_rating_project_ir_actions_server
#: model:ir.cron,cron_name:project.ir_cron_rating_project
#: model:ir.cron,name:project.ir_cron_rating_project
msgid "Project: Send rating"
msgstr "المشروع: إرسال تقييم"

#. module: project
#: model:ir.actions.act_window,name:project.open_view_project_all
#: model:ir.actions.act_window,name:project.open_view_project_all_config
#: model:ir.actions.act_window,name:project.open_view_project_all_group_stage
#: model:ir.model.fields,field_description:project.field_account_analytic_account__project_ids
#: model:ir.model.fields,field_description:project.field_project_delete_wizard__project_ids
#: model:ir.model.fields,field_description:project.field_project_task_type__project_ids
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__project_ids
#: model:ir.ui.menu,name:project.menu_projects
#: model:ir.ui.menu,name:project.menu_projects_config
#: model:ir.ui.menu,name:project.menu_projects_group_stage
#: model_terms:ir.ui.view,arch_db:project.account_analytic_account_view_form_inherit
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
#: model_terms:ir.ui.view,arch_db:project.portal_my_projects
#: model_terms:ir.ui.view,arch_db:project.view_project
msgid "Projects"
msgstr "المشاريع "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_delete_wizard__projects_archived
msgid "Projects Archived"
msgstr "المشاريع المؤرشفة "

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_group_stage
msgid "Projects regroup tasks on the same topic, and each has its dashboard."
msgstr ""
"تقوم المشاريع بإعادة تجميع المهام من نفس الموضوع، ولكل منها لوحة بيانات "
"خاصة. "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Published"
msgstr "تم النشر "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Publishing"
msgstr "جاري النشر "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__quarterly
msgid "Quarterly"
msgstr "ربع سنوي"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Rated Tasks"
msgstr "المهام المُقيّمة"

#. module: project
#: model:ir.actions.act_window,name:project.rating_rating_action_view_project_rating
#: model:ir.model.fields,field_description:project.field_project_task__rating_ids
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Rating"
msgstr "التقييم"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_graph
#: model_terms:ir.ui.view,arch_db:project.view_project_task_pivot
msgid "Rating (/5)"
msgstr "التقييم (/5) "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_avg
msgid "Rating Average"
msgstr "متوسط التقييم"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__rating_template_id
msgid "Rating Email Template"
msgstr "قالب البريد الإلكتروني للتقييم "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_status_period
msgid "Rating Frequency"
msgstr "تواتر التقييم "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "آخر ملاحظات التقييم"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_image
msgid "Rating Last Image"
msgstr "آخر صورة للتقييم"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_value
msgid "Rating Last Value"
msgstr "آخر قيمة للتقييم"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_request_deadline
msgid "Rating Request Deadline"
msgstr "الموعد النهائي لطلب التقييم"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "نسبة الرضا"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_rating_graph_view.js:0
#: code:addons/project/static/src/js/project_rating_pivot_view.js:0
#: model:ir.model.fields,field_description:project.field_report_project_task_user__rating_last_value
#, python-format
msgid "Rating Value (/5)"
msgstr "قيمة التقييم (/5) "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_count
msgid "Rating count"
msgstr "عدد التقييمات"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status__stage
msgid "Rating when changing stage"
msgstr "التقييم عند تغيير المرحلة"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_ids
msgid "Ratings"
msgstr "التقييمات "

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "Ratings of %s"
msgstr "تقييمات %s "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__is_reached
msgid "Reached"
msgstr "تم الوصول "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__reached_date
msgid "Reached Date"
msgstr "تاريخ الوصول "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_share_wizard__access_mode__read
msgid "Readonly"
msgstr "للقراءة فقط"

#. module: project
#: code:addons/project/models/project.py:0
#: model:ir.model.fields.selection,name:project.selection__project_task__kanban_state__done
#: model:project.task,legend_done:project.project_task_1
#: model:project.task,legend_done:project.project_task_12
#: model:project.task,legend_done:project.project_task_2
#: model:project.task,legend_done:project.project_task_20
#: model:project.task,legend_done:project.project_task_24
#: model:project.task,legend_done:project.project_task_25
#: model:project.task,legend_done:project.project_task_26
#: model:project.task,legend_done:project.project_task_3
#: model:project.task,legend_done:project.project_task_30
#: model:project.task,legend_done:project.project_task_31
#: model:project.task,legend_done:project.project_task_32
#: model:project.task,legend_done:project.project_task_33
#: model:project.task,legend_done:project.project_task_8
#: model:project.task,legend_done:project.project_task_9
#: model:project.task.type,legend_done:project.project_personal_stage_admin_0
#: model:project.task.type,legend_done:project.project_personal_stage_admin_1
#: model:project.task.type,legend_done:project.project_personal_stage_admin_2
#: model:project.task.type,legend_done:project.project_personal_stage_admin_3
#: model:project.task.type,legend_done:project.project_personal_stage_admin_4
#: model:project.task.type,legend_done:project.project_personal_stage_admin_5
#: model:project.task.type,legend_done:project.project_personal_stage_admin_6
#: model:project.task.type,legend_done:project.project_personal_stage_demo_0
#: model:project.task.type,legend_done:project.project_personal_stage_demo_1
#: model:project.task.type,legend_done:project.project_personal_stage_demo_2
#: model:project.task.type,legend_done:project.project_personal_stage_demo_3
#: model:project.task.type,legend_done:project.project_personal_stage_demo_4
#: model:project.task.type,legend_done:project.project_personal_stage_demo_5
#: model:project.task.type,legend_done:project.project_personal_stage_demo_6
#: model:project.task.type,legend_done:project.project_stage_0
#: model:project.task.type,legend_done:project.project_stage_2
#, python-format
msgid "Ready"
msgstr "جاهز"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__done
msgid "Ready for Next Stage"
msgstr "جاهزة للمرحلة المقبلة"

#. module: project
#: model:project.task.type,legend_done:project.project_stage_3
msgid "Ready to reopen"
msgstr "جاهز لإعادة الفتح"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__rating_last_feedback
msgid "Reason of the rating"
msgstr "سبب التقييم"

#. module: project
#: model:mail.template,subject:project.mail_template_data_project_task
msgid "Reception of {{ object.name }}"
msgstr "استقبال {{ object.name }} "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__partner_ids
msgid "Recipients"
msgstr "المستلمين "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_force_thread_id
msgid "Record Thread ID"
msgstr "معرف مناقشة السجل"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Recording"
msgstr "تسجيل"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurrence_id
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Recurrence"
msgstr "التكرار "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurrence_update
msgid "Recurrence Update"
msgstr "تحديث التكرار "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurring_task
msgid "Recurrent"
msgstr "متكرر "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__allow_recurring_tasks
#: model:ir.model.fields,field_description:project.field_project_task__allow_recurring_tasks
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_recurring_tasks
msgid "Recurring Tasks"
msgstr "المهام المتكررة "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__legend_blocked
msgid "Red Kanban Label"
msgstr "بطاقة عنوان كانبان حمراء"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "Ref"
msgstr "المرجع "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Refused"
msgstr "تم الرفض "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__resource_ref
msgid "Related Document"
msgstr "المستند ذو الصلة "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__res_id
msgid "Related Document ID"
msgstr "معرف المستند المرتبط"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__res_model
msgid "Related Document Model"
msgstr "نموذج المستند ذو الصلة "

#. module: project
#: model:project.project,name:project.project_project_3
msgid "Renovations"
msgstr "الترميم "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_day
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_day
msgid "Repeat Day"
msgstr "تكرار اليوم "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_interval
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_interval
msgid "Repeat Every"
msgstr "التكرار كل "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_month
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_month
msgid "Repeat Month"
msgstr "تكرار الشهر "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Repeat On"
msgstr "التكرار في "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_on_month
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_on_month
msgid "Repeat On Month"
msgstr "التكرار في الشهر "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_on_year
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_on_year
msgid "Repeat On Year"
msgstr "التكرار في السنة "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_show_day
msgid "Repeat Show Day"
msgstr "تكرار إظهار اليوم "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_show_dow
msgid "Repeat Show Dow"
msgstr "تكرار اليوم من الأسبوع "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_show_month
msgid "Repeat Show Month"
msgstr "تكرار إظهار الشهر "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_show_week
msgid "Repeat Show Week"
msgstr "تكرار إظهار الأسبوع "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_unit
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_unit
msgid "Repeat Unit"
msgstr "تكرار الوحدة "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_week
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_week
msgid "Repeat Week"
msgstr "تكرار الأسبوع "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_number
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_number
msgid "Repetitions"
msgstr "التكرار "

#. module: project
#: model:ir.ui.menu,name:project.menu_project_report
msgid "Reporting"
msgstr "إعداد التقارير "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Research"
msgstr "البحث"

#. module: project
#: model:project.project,name:project.project_project_2
msgid "Research & Development"
msgstr "البحث والتطوير "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Research Project"
msgstr "مشروع البحث"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Researching"
msgstr "البحث"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Resources Allocation"
msgstr "تخصيص الموارد"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_user_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_user_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_user_id
msgid "Responsible User"
msgstr "المستخدم المسؤول"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_project__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_task__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_update__message_has_sms_error
msgid "SMS Delivery error"
msgstr "خطأ في تسليم الرسائل القصيرة"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Sad face"
msgstr "وجه حزين"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__sat
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__sat
msgid "Sat"
msgstr "السبت"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__sat
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__sat
msgid "Saturday"
msgstr "السبت"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Script"
msgstr "نص برمجي"

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search <span class=\"nolabel\"> (in Content)</span>"
msgstr "البحث <span class=\"nolabel\"> (في المحتوى)</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Search Project"
msgstr "البحث عن مشروع"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "Search Update"
msgstr "تحديث البحث "

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in All"
msgstr "البحث في الكل"

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Assignees"
msgstr "البحث في الأفراد المسند إليهم "

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Messages"
msgstr "البحث في الرسائل"

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Priority"
msgstr "البحث في الأولوية "

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Project"
msgstr "البحث في المشاريع "

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Ref"
msgstr "البحث في المراجع "

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Stages"
msgstr "البحث في المراحل"

#. module: project
#: code:addons/project/controllers/portal.py:0
#, python-format
msgid "Search in Status"
msgstr "البحث في الحالات "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_week__second
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_week__second
msgid "Second"
msgstr "الثانية"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_token
#: model:ir.model.fields,field_description:project.field_project_task__access_token
msgid "Security Token"
msgstr "رمز الحماية"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Send"
msgstr "إرسال"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_month__september
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_month__september
msgid "September"
msgstr "سبتمبر"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__sequence
#: model:ir.model.fields,field_description:project.field_project_project_stage__sequence
#: model:ir.model.fields,field_description:project.field_project_task__sequence
#: model:ir.model.fields,field_description:project.field_project_task_type__sequence
msgid "Sequence"
msgstr "التسلسل "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Set Cover Image"
msgstr "تعيين صورة غلاف"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Set a Rating Email Template on Stages"
msgstr "تعيين قالب تقييم بريد إلكتروني للمراحل "

#. module: project
#: model:ir.actions.act_window,name:project.project_config_settings_action
#: model:ir.ui.menu,name:project.project_config_settings_menu_action
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Settings"
msgstr "الإعدادات"

#. module: project
#: model:ir.actions.act_window,name:project.portal_share_action
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Share"
msgstr "مشاركة"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Share Editable"
msgstr "مشاركة القابل للتحرير "

#. module: project
#: model:ir.actions.act_window,name:project.project_share_wizard_action
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Share Project"
msgstr "مشاركة المشروع "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Share Readonly"
msgstr "المشاركة للقراءة فقط "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__is_favorite
msgid "Show Project on Dashboard"
msgstr "إظهار المشروع في لوحة المعلومات"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Show all records which has next action date is before today"
msgstr "عرض كافة السجلات المُعين لها تاريخ إجراء تالي يسبق تاريخ اليوم الجاري"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Since"
msgstr "منذ "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Software Development"
msgstr "تطوير البرمجيات"

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "Sorry. You can't set a task as its parent task."
msgstr "عذراً، لا يمكنك تعيين مهمة كمهمة رئيسية لنفسها. "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Specifications"
msgstr "المواصفات"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Split your tasks to organize your work into sub-milestones"
msgstr "قم بتقسيم مهمامك لتنظيم عملك بوضع مؤشرات تقدم فرعية "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Sprint Backlog"
msgstr "سبرينت متأخر "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Sprint Complete"
msgstr "سبرينت مكتمل "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Sprint Summary"
msgstr "ملخص سبرينت "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Sprint in Progress"
msgstr "سبرينت قيد التنفيذ "

#. module: project
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_project__stage_id
#: model:ir.model.fields,field_description:project.field_project_task__stage_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__stage_id
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__stage_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__stage_id
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#, python-format
msgid "Stage"
msgstr "المرحلة"

#. module: project
#: model:mail.message.subtype,name:project.mt_task_stage
msgid "Stage Changed"
msgstr "تم تغيير المرحلة"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "Stage Description and Tooltips"
msgstr "وصف وتلميحات المرحلة"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__user_id
msgid "Stage Owner"
msgstr "مالك المرحلة "

#. module: project
#: model:mail.message.subtype,description:project.mt_task_stage
msgid "Stage changed"
msgstr "تم تغيير المرحلة"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__stages_active
msgid "Stages Active"
msgstr "المراحل النشطة "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__stage_ids
msgid "Stages To Delete"
msgstr "المراحل لحذفها "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__priority
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Starred"
msgstr "معلم بنجمة "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__date_start
#: model_terms:ir.ui.view,arch_db:project.view_project
msgid "Start Date"
msgstr "تاريخ البدء "

#. module: project
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__kanban_state
#: model:ir.model.fields,field_description:project.field_project_update__status
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#, python-format
msgid "Status"
msgstr "الحالة"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_state
#: model:ir.model.fields,help:project.field_project_task__activity_state
#: model:ir.model.fields,help:project.field_project_update__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"الحالة على أساس الأنشطة\n"
"المتأخرة: تاريخ الاستحقاق مر\n"
"اليوم: تاريخ النشاط هو اليوم\n"
"المخطط: الأنشطة المستقبلية."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_form.js:0
#: code:addons/project/static/src/js/project_list.js:0
#, python-format
msgid "Stop Recurrence"
msgstr "إيقاف التكرار "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__subtask_count
msgid "Sub-task Count"
msgstr "عدد المهام الفرعية "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__allow_subtasks
#: model:ir.model.fields,field_description:project.field_project_task__child_ids
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_subtask_project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Sub-tasks"
msgstr "المهام الفرعية"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__subtask_planned_hours
msgid "Sub-tasks Planned Hours"
msgstr "الساعات المخطط لها في المهام الفرعية "

#. module: project
#: model:ir.model.fields,help:project.field_project_task__subtask_planned_hours
msgid ""
"Sum of the time planned of all the sub-tasks linked to this task. Usually "
"less than or equal to the initially planned time of this task."
msgstr ""
"مجموع الوقت المخطط له لكافة المهام الفرعية المرتبطة بهذه المهمة. عادةً ما "
"يكون أقل من الوقت المبدأي المخطط له في هذه المهمة أو مساوياً له. "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__sun
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__sun
msgid "Sun"
msgstr "الأحد"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__sun
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__sun
msgid "Sunday"
msgstr "الأحد"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "T-shirt Printing"
msgstr "طباعة قميص"

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_tags_name_uniq
msgid "Tag name already exists!"
msgstr "اسم علامة التصنيف موجود بالفعل! "

#. module: project
#: model:ir.actions.act_window,name:project.project_tags_action
#: model:ir.model.fields,field_description:project.field_project_project__tag_ids
#: model:ir.model.fields,field_description:project.field_project_task__tag_ids
#: model:ir.ui.menu,name:project.menu_project_tags_act
#: model_terms:ir.ui.view,arch_db:project.project_tags_form_view
#: model_terms:ir.ui.view,arch_db:project.project_tags_tree_view
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Tags"
msgstr "علامات التصنيف "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_rating_graph_view.js:0
#: code:addons/project/static/src/js/project_rating_pivot_view.js:0
#: model:ir.model,name:project.model_project_task
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__task_ids
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__task_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__name
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#, python-format
msgid "Task"
msgstr "المهمة"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__tasks
msgid "Task Activities"
msgstr "أنشطة المهمة"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_blocked
#: model:mail.message.subtype,name:project.mt_task_blocked
msgid "Task Blocked"
msgstr "تم حجب المهمة"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_delete_wizard__task_count
#: model:ir.model.fields,field_description:project.field_project_project__task_count
msgid "Task Count"
msgstr "عدد المهام"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__task_count_with_subtasks
msgid "Task Count With Subtasks"
msgstr "عدد المهام مع المهام الفرعية "

#. module: project
#: model:mail.message.subtype,description:project.mt_task_new
#: model:mail.message.subtype,name:project.mt_project_task_new
#: model:mail.message.subtype,name:project.mt_task_new
msgid "Task Created"
msgstr "تم إنشاء المهمة"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__allow_task_dependencies
#: model:ir.model.fields,field_description:project.field_project_task__allow_task_dependencies
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_task_dependencies
msgid "Task Dependencies"
msgstr "تبعيات المهام "

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_dependency_change
#: model:mail.message.subtype,name:project.mt_task_dependency_change
msgid "Task Dependency Changes"
msgstr "تغييرات تبعيات المهام "

#. module: project
#: model:ir.model.fields,field_description:project.field_res_config_settings__module_hr_timesheet
msgid "Task Logs"
msgstr "سجلات المهمة"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_rating
#: model:mail.message.subtype,name:project.mt_task_rating
msgid "Task Rating"
msgstr "تقييم المهمة"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_ready
#: model:mail.message.subtype,name:project.mt_task_ready
msgid "Task Ready"
msgstr "المهمة جاهزة"

#. module: project
#: model:ir.model,name:project.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "تكرار المهمة "

#. module: project
#: model:ir.model,name:project.model_project_task_type
#: model_terms:ir.ui.view,arch_db:project.personal_task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_tree
msgid "Task Stage"
msgstr "مرحلة المهمة"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_stage
msgid "Task Stage Changed"
msgstr "تغيرت مرحلة المهمة"

#. module: project
#: model:ir.actions.act_window,name:project.open_task_type_form
#: model:ir.actions.act_window,name:project.open_task_type_form_domain
#: model:ir.ui.menu,name:project.menu_project_config_project
msgid "Task Stages"
msgstr "مراحل المهمة "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_quick_create_task_form
#: model_terms:ir.ui.view,arch_db:project.quick_create_task_form
msgid "Task Title"
msgstr "عنوان المهمة"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Task Title..."
msgstr "عنوان المهمة..."

#. module: project
#: model:mail.message.subtype,description:project.mt_task_blocked
msgid "Task blocked"
msgstr "تم حجب المهمة"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "Task in progress. Click to block or set as done."
msgstr "المهمة قيد التنفيذ. اضغط لحظرها أو تعيينها كمكتملة."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "Task is blocked. Click to unblock or set as done."
msgstr "المهمة محظورة. اضغط لإلغاء حظرها أو تعيينها كمكتملة."

#. module: project
#: model:mail.message.subtype,description:project.mt_task_ready
msgid "Task ready for Next Stage"
msgstr "المهمة جاهزة للمرحلة المقبلة"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_track_depending_tasks
msgid "Task:"
msgstr "المهمة: "

#. module: project
#: model:mail.template,name:project.rating_project_request_email_template
msgid "Task: Rating Request"
msgstr "المهمة: طلب تقييم "

#. module: project
#: model:mail.template,name:project.mail_template_data_project_task
msgid "Task: Reception Acknowledgment"
msgstr "المهمة: الاعتراف بالاستلام "

#. module: project
#: code:addons/project/models/project.py:0
#: model:ir.actions.act_window,name:project.act_project_project_2_project_task_all
#: model:ir.actions.act_window,name:project.action_view_task
#: model:ir.actions.act_window,name:project.project_task_action_from_partner
#: model:ir.model.fields,field_description:project.field_account_analytic_tag__task_ids
#: model:ir.model.fields,field_description:project.field_project_project__task_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__task_id
#: model:ir.model.fields,field_description:project.field_res_partner__task_ids
#: model:ir.model.fields,field_description:project.field_res_users__task_ids
#: model:project.project,label_tasks:project.project_project_1
#: model:project.project,label_tasks:project.project_project_2
#: model:project.project,label_tasks:project.project_project_3
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
#: model_terms:ir.ui.view,arch_db:project.portal_my_project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_tree
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_project_task_graph
#: model_terms:ir.ui.view,arch_db:project.view_project_task_pivot
#: model_terms:ir.ui.view,arch_db:project.view_task_calendar
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_partner_info_form
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#: model_terms:ir.ui.view,arch_db:project.view_task_tree2
#, python-format
msgid "Tasks"
msgstr "المهام"

#. module: project
#: model:ir.actions.act_window,name:project.action_project_task_user_tree
#: model:ir.model,name:project.model_report_project_task_user
#: model:ir.ui.menu,name:project.menu_project_report_task_analysis
#: model_terms:ir.ui.view,arch_db:project.report_project_task_user_view_tree
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_graph
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_pivot
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Tasks Analysis"
msgstr "تحليل المهام"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Tasks In Progress"
msgstr "المهام قيد التنفيذ "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Tasks Late"
msgstr "المهام المتأخرة "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Tasks Management"
msgstr "إدارة المهام"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__type_ids
#: model_terms:ir.ui.view,arch_db:project.task_type_search
msgid "Tasks Stages"
msgstr "مراحل المهام"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurring_count
msgid "Tasks in Recurrence"
msgstr "المهام في التكرار "

#. module: project
#: model:ir.model.fields,help:project.field_project_task__is_closed
#: model:ir.model.fields,help:project.field_project_task_type__is_closed
msgid "Tasks in this stage are considered as closed."
msgstr "المهام في هذه المرحلة تعتبر مغلقة."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Tests"
msgstr "الاختبارات"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__personal_stage_id
msgid "The current user's personal stage."
msgstr "المرحلة الشخصية للمستخدم الحالي. "

#. module: project
#: model:ir.model.fields,help:project.field_project_task__personal_stage_type_id
msgid "The current user's personal task stage."
msgstr "مرحلة المهمة الشخصية للمستخدم الحالي. "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "The following milestone has been added:"
msgstr "تمت إضافة مؤشر التقدم التالي: "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "The following milestones have been added:"
msgstr "تمت إضافة مؤشرات التقدم التالية: "

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"النموذج (مستندات أودو) الذي يقترن به هذا اللقب. أي رسالة واردة لا ترد على "
"سجل موجود ستقوم بإنشاء سجل جديد من نفس نوع هذا النموذج (مثلًا: مهمة مشروع) "

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"اسم لقب البريد الإلكتروني، مثلًا: 'وظائف' إذا كنت ترغب في جمع الرسائل "
"المرسلة لـ<<EMAIL>> "

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""
"مالك السجلات المنشأة عند استلام رسائل بريد إلكتروني على هذا اللقب. إذا لم "
"يتم تعيين قيمة لهذا الحقل، سيحاول النظام معرفة المالك الصحيح حسب عنوان "
"البريد الإلكتروني للمرسل، أو سيستخدم حساب المدير إذا لم يجد حساباً مرتبطاً "
"بعنوان البريد الإلكتروني. "

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"The project cannot be shared with the recipient(s) because the privacy of "
"the project is too restricted. Set the privacy to 'Visible by following "
"customers' in order to make it accessible by the recipient(s)."
msgstr ""
"لا يمكن مشاركة المشروع مع المستلم (المستلمين) لأن خصوصية المشروع مقيدة "
"للغاية. قم بتغيير مستوى الخصوصية لـ 'مرئي للعملاء المتابعين' لتتيح للمستلم "
"(المستلمين) الوصول إليه. "

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"The task cannot be shared with the recipient(s) because the privacy of the "
"project is too restricted. Set the privacy of the project to 'Visible by "
"following customers' in order to make it accessible by the recipient(s)."
msgstr ""
"لا يمكن مشاركة المهمة مع المستلم (المستلمين) لأن خصوصية المهمة مقيدة للغاية."
" قم بتغيير مستوى الخصوصية لـ 'مرئي للعملاء المتابعين' لتتيح للمستلم "
"(المستلمين) الوصول إليه. "

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "There are no more occurrences."
msgstr "ليس هناك المزيد من الوقائع "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_projects
msgid "There are no projects."
msgstr "لا توجد مشاريع. "

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_view_project_rating
msgid "There are no ratings for this project at the moment"
msgstr "ليس هناك تقييمات لهذا المشروع في الوقت الحالي "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks
msgid "There are no tasks."
msgstr "لا توجد مهام. "

#. module: project
#: model:ir.model.fields,help:project.field_project_task__email_from
msgid "These people will receive email."
msgstr "سوف يستلم هؤلاء الأفراد بريداً إلكترونياً. "

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_week__third
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_week__third
msgid "Third"
msgstr "ثالثًا"

#. module: project
#. openerp-web
#: code:addons/project/models/project.py:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: model:project.task.type,name:project.project_personal_stage_admin_3
#: model:project.task.type,name:project.project_personal_stage_demo_3
#, python-format
msgid "This Month"
msgstr "هذا الشهر"

#. module: project
#. openerp-web
#: code:addons/project/models/project.py:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: model:project.task.type,name:project.project_personal_stage_admin_2
#: model:project.task.type,name:project.project_personal_stage_demo_2
#, python-format
msgid "This Week"
msgstr "هذا الأسبوع"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__recurrence_update__subsequent
msgid "This and following tasks"
msgstr "هذه المهمة والمهام التالية "

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__fold
msgid ""
"This stage is folded in the kanban view when there are no records in that "
"stage to display."
msgstr ""
"يتم طي هذه المرحلة عند استخدام طريقة كانبان للعرض عندما لا توجد سجلات يمكن "
"عرضها في هذه المرحلة."

#. module: project
#: model:ir.model.fields,help:project.field_project_project_stage__fold
msgid "This stage is folded in the kanban view."
msgstr "هذه المرحلة مطوية في عرض كانبان. "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "This step is done. Click to block or set in progress."
msgstr "هذه الخطوة مكتملة. اضغط لحظرها أو تعيينها قيد التنفيذ."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__recurrence_update__this
msgid "This task"
msgstr "هذه المهمة "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
msgid ""
"This will archive the stages and all the tasks they contain from the "
"following projects:"
msgstr ""
"سيقوم ذلك بأرشفة المراحل وكافة المهام التي تحتويها من المشاريع التالية: "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__thu
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__thu
msgid "Thu"
msgstr "الخميس"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__thu
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__thu
msgid "Thursday"
msgstr "الخميس"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Time Management"
msgstr "إدارة الوقت"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__planned_hours
msgid "Time planned to achieve this task (including its sub-tasks)."
msgstr "الوقت المخطط له لإنجاز هذه المهمة (بالإضافة إلى مهامها الفرعية). "

#. module: project
#: model:digest.tip,name:project.digest_tip_project_0
#: model_terms:digest.tip,tip_description:project.digest_tip_project_0
msgid "Tip: Customize tasks and stages according to the project"
msgstr "نصيحة: قم بتخصيص المهام والمراحل وفقاً للمشروع "

#. module: project
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__name
#: model:ir.model.fields,field_description:project.field_project_update__name
#, python-format
msgid "Title"
msgstr "العنوان"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
msgid "Title of the Update"
msgstr "العنوان والتحديث "

#. module: project
#: model:project.project.stage,name:project.project_project_stage_0
msgid "To Do"
msgstr "المهام المراد تنفيذها"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "To Print"
msgstr "للطباعة"

#. module: project
#: model_terms:ir.actions.act_window,help:project.act_project_project_2_project_task_all
#: model_terms:ir.actions.act_window,help:project.action_view_all_task
#: model_terms:ir.actions.act_window,help:project.action_view_task
msgid ""
"To get things done, use activities and status on tasks.<br>\n"
"                    Chat in real-time or by email to collaborate efficiently."
msgstr ""
"لإنجاز المهام، استخدم الأنشطة والحالات في المهام.<br>\n"
"                دردش في الوقت الفعلي أو تواصل عبر البريد الإلكتروني للتواصل بشكل أكثر فعالية. "

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action
msgid ""
"To get things done, use activities and status on tasks.<br>\n"
"                Chat in real time or by email to collaborate efficiently."
msgstr ""
"لإنجاز المهام، استخدم الأنشطة والحالات في المهام.<br>\n"
"                دردش في الوقت الفعلي أو تواصل عبر البريد الإلكتروني للتواصل بشكل أكثر فعالية. "

#. module: project
#. openerp-web
#: code:addons/project/models/project.py:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: model:project.task.type,name:project.project_personal_stage_admin_1
#: model:project.task.type,name:project.project_personal_stage_demo_1
#, python-format
msgid "Today"
msgstr "اليوم"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Today Activities"
msgstr "أنشطة اليوم "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track customer satisfaction on tasks"
msgstr "تتبع نسبة رضا العميل على المهام"

#. module: project
#. openerp-web
#: code:addons/project/static/src/xml/project_templates.xml:0
#: model_terms:ir.actions.act_window,help:project.project_milestone_all
#, python-format
msgid "Track major progress points that must be reached to achieve success."
msgstr "تتبع نقاط التقدم الرئيسية التي يجب إنجازها لتحقيق النجاح. "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track the costs and revenues linked to your projects"
msgstr "تتبع التكاليف والإيرادات المرتبطة بمشاريعك "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track the progress of your projects"
msgstr "تتبع تقدم مشاريعك "

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_project_stage_configure
msgid ""
"Track the progress of your projects from their creation to their closing."
msgstr "تتبع تقدم مشاريعك منذ إنشائها وحتى انتهائها. "

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form
msgid "Track the progress of your tasks from their creation to their closing."
msgstr "تتبع تقدم مهامك منذ إنشائها وحتى انتهائها. "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track time spent on projects and tasks"
msgstr "تتبع الوقت المقضي على المشاريع والمهام "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__tue
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__tue
msgid "Tue"
msgstr "الثلاثاء"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__tue
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__tue
msgid "Tuesday"
msgstr "الثلاثاء"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__bimonthly
msgid "Twice a Month"
msgstr "مرتان شهريًا"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_exception_decoration
#: model:ir.model.fields,help:project.field_project_task__activity_exception_decoration
#: model:ir.model.fields,help:project.field_project_update__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "نوع النشاط الاستثنائي المسجل."

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_form.js:0
#: code:addons/project/static/src/js/project_list.js:0
#, python-format
msgid "Unarchive"
msgstr "إلغاء الأرشفة "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "Unassign Me"
msgstr "إلغاء إسنادي  "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Unassigned"
msgstr "غير مسند "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Unassigned Tasks"
msgstr "المهام غير المسندة "

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "Unknown Analytic Account"
msgstr "حساب تحليلي مجهول"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_unread
#: model:ir.model.fields,field_description:project.field_project_project__message_unread
#: model:ir.model.fields,field_description:project.field_project_task__message_unread
#: model:ir.model.fields,field_description:project.field_project_update__message_unread
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Unread Messages"
msgstr "الرسائل غير المقروءة "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_unread_counter
#: model:ir.model.fields,field_description:project.field_project_project__message_unread_counter
#: model:ir.model.fields,field_description:project.field_project_task__message_unread_counter
#: model:ir.model.fields,field_description:project.field_project_update__message_unread_counter
msgid "Unread Messages Counter"
msgstr "عداد الرسائل غير المقروءة "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_type
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_type
msgid "Until"
msgstr "حتى"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__update_ids
msgid "Update"
msgstr "تحديث"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Use %s and %s bullets to indicate the status of a task. %s"
msgstr "استخدم العلامات %s و %s للإشارة إلى حالة مهمة ما. %s "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid "Use <b>activities</b> to organize your daily work."
msgstr "استخدم <b>الأنشطة</b> لتنظيم عملك اليومي. "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_enabled
msgid "Use Email Alias"
msgstr "استخدام لقب البريد الإلكتروني "

#. module: project
#: model:res.groups,name:project.group_project_rating
msgid "Use Rating on Project"
msgstr "استخدام التقييمات على المشروع"

#. module: project
#: model:res.groups,name:project.group_project_recurring_tasks
msgid "Use Recurring Tasks"
msgstr "استخدام المهام المتكررة "

#. module: project
#: model:res.groups,name:project.group_project_stages
msgid "Use Stages on Project"
msgstr "استخدام المراحل في المشروع "

#. module: project
#: model:res.groups,name:project.group_subtask_project
msgid "Use Subtasks"
msgstr "استخدام المهام الفرعية"

#. module: project
#: model:res.groups,name:project.group_project_task_dependencies
msgid "Use Task Dependencies"
msgstr "استخدام تبعيات المهام "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__label_tasks
msgid "Use Tasks as"
msgstr "استخدام المهام كـ"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Use This For My Project"
msgstr "الاستخدام في مشروعي "

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_tags_action
msgid "Use tags to categorize your tasks."
msgstr "استخدم علامات التصنيف لوضع مهامك في فئات. "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Use the chatter to <b>send emails</b> and communicate efficiently with your customers. \n"
"    Add new people to the followers' list to make them aware of the main changes about this task."
msgstr ""
"استخدم الدردشة <b>لإرسال رسائل البريد الإلكتروني</b> والتواصل بكفاءة مع عملائك.\n"
"    أضف المزيد من الأشخاص إلى قائمة المتابعين لإبقائهم على علم بالتغييرات الأساسية المتعلقة بهذه المهمة. "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__user_id
#: model:res.groups,name:project.group_project_user
msgid "User"
msgstr "المستخدم"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_message_user_assigned
msgid "View"
msgstr "أداة العرض"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "View Task"
msgstr "عرض المهمة "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__privacy_visibility
msgid "Visibility"
msgstr "الظهور"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/tours/project.js:0
#: code:addons/project/static/src/js/tours/project.js:0
#, python-format
msgid ""
"Want a better way to <b>manage your projects</b>? <i>It starts here.</i>"
msgstr "أتريد طريقة أفضل لـ<b>إدارة مشاريعك</b>؟ <i>ابدأ من هنا.</i>"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__website_message_ids
#: model:ir.model.fields,field_description:project.field_project_project__website_message_ids
#: model:ir.model.fields,field_description:project.field_project_task__website_message_ids
#: model:ir.model.fields,field_description:project.field_project_update__website_message_ids
msgid "Website Messages"
msgstr "رسائل الموقع الإلكتروني "

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Website Redesign"
msgstr "إعادة تصميم الموقع الإلكتروني "

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__website_message_ids
#: model:ir.model.fields,help:project.field_project_project__website_message_ids
#: model:ir.model.fields,help:project.field_project_task__website_message_ids
#: model:ir.model.fields,help:project.field_project_update__website_message_ids
msgid "Website communication history"
msgstr "سجل تواصل الموقع الإلكتروني "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__wed
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__wed
msgid "Wed"
msgstr "الأربعاء"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_weekday__wed
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_weekday__wed
msgid "Wednesday"
msgstr "الأربعاء"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__weekly
msgid "Weekly"
msgstr "أسبوعيًا"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__week
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__week
msgid "Weeks"
msgstr "أسابيع"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__is_favorite
msgid "Whether this project should be displayed on your dashboard."
msgstr "ما إذا كان ينبغي عرض هذا المشروع في لوحة بياناتك أم لا. "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_days_open
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_days_open
msgid "Working Days to Assign"
msgstr "أيام العمل لتعيينها "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_days_close
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_days_close
msgid "Working Days to Close"
msgstr "أيام العمل لإغلاقها "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_hours_open
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_hours_open
msgid "Working Hours to Assign"
msgstr "ساعات العمل لتعيينها "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_hours_close
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_hours_close
msgid "Working Hours to Close"
msgstr "ساعات العمل لإغلاقها "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__resource_calendar_id
msgid "Working Time"
msgstr "وقت العمل "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Working Time to Assign"
msgstr "وقت العمل المتبقي حتى الإسناد"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Working Time to Close"
msgstr "وقت العمل المتبقي حتى الإغلاق"

#. module: project
#. openerp-web
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#: code:addons/project/static/src/js/project_task_kanban_examples.js:0
#, python-format
msgid "Writing"
msgstr "الكتابة"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__yearly
msgid "Yearly"
msgstr "سنويًا"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__year
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__year
msgid "Years"
msgstr "سنوات"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid ""
"You can also add a description to help your coworkers understand the meaning"
" and purpose of the stage."
msgstr ""
"بوسعك أيضاً إضافة وصف لمساعدة زملائك على فهم معنى المرحلة والغرض منها. "

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"You cannot archive recurring tasks. Please disable the recurrence first."
msgstr "لا يمكنك أرشفة المهام المتكررة. يرجى تعطيل التكرار أولاً. "

#. module: project
#: code:addons/project/models/analytic_account.py:0
#, python-format
msgid ""
"You cannot change the company of an analytic account if it is related to a "
"project."
msgstr "لا يمكنك تغيير شركة حساب تحليلي إذا كان متعلقاً بمشروع. "

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "You cannot create cyclic dependency."
msgstr "لا يمكنك إنشاء تبعيات تبدأ وتنتهي بنفس المهمة. "

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"You cannot delete a project containing tasks. You can either archive it or "
"first delete all of its tasks."
msgstr ""
"لا يمكنك حذف مشروع يحتوي على مهام. إما أن تقوم بأرشفته أو تحذف كافة مهامه "
"أولًا."

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"You cannot delete recurring tasks. Please disable the recurrence first."
msgstr "لا يمكنك حذف المهام المتكررة. يرجى تعطيل التكرار أولاً. "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid ""
"You cannot delete stages containing tasks. You can either archive them or "
"first delete all of their tasks."
msgstr ""
"لا يمكنك حذف مراحل تحتوي على مهام. بإمكانك إما أرشفتها أولاً أو حذف كافة "
"مهامها. "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid ""
"You cannot delete stages containing tasks. You should first delete all of "
"their tasks."
msgstr "لا يمكنك حذف مراحل تحتوي على مهام. عليك أولاً حذف كافة مهامها. "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "You cannot read %s fields in task."
msgstr "لا يمكنك قراءة %s حقول في المهمة. "

#. module: project
#. odoo-python
#: code:addons/project/models/project.py:0
#, python-format
msgid "You cannot write on %s fields in task."
msgstr "لا يمكنك الكتابة في %s حقول في المهمة. "

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "You have been assigned to %s"
msgstr "تم تعيينك لـ%s"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_message_user_assigned
msgid "You have been assigned to the"
msgstr "تم تعيينك لـ"

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "You have not write access of %s field."
msgstr "لا تملك حق الكتابة في حقل %s. "

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid ""
"You should at least have one personal stage. Create a new stage to which the"
" tasks can be transferred after this one is deleted."
msgstr ""
"يجب أن تكون لديك مرحة شخصية واحدة على الأقل. قم بإنشاء مرحلة جديدة يمكن أن "
"يتم تحويل المهام إليها بعد أن يتم حذف هذه. "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_quick_create_task_form
#: model_terms:ir.ui.view,arch_db:project.quick_create_task_form
msgid "e.g. New Design"
msgstr "مثال: تصميم جديد "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "e.g. Office Party"
msgstr "مثال: حفلة مكتبية "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "e.g. office-party"
msgstr "مثال: حفلة مكتبية "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "for customer:"
msgstr "للعميل: "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "for project:"
msgstr "للمشروع: "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "in priority:"
msgstr "أولوية: "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "in stage:"
msgstr "في مرحلة: "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "in status:"
msgstr "في حالة: "

#. module: project
#: code:addons/project/models/project.py:0
#, python-format
msgid "task"
msgstr "المهمة"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "the deadline of the following milestone has been updated:"
msgstr "تم تحديث الموعد النهائي لمؤشر التقدم التالي: "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "the deadline of the following milestones has been updated:"
msgstr "تم تحديث المواعيد النهائية لمؤشرات التقدم التالية: "

#. module: project
#. openerp-web
#: code:addons/project/static/src/burndown_chart/burndown_chart_view.xml:0
#, python-format
msgid "true"
msgstr "صحيح"

#. module: project
#: model:mail.template,subject:project.rating_project_request_email_template
msgid "{{ object.project_id.company_id.name }}: Satisfaction Survey"
msgstr "{{ object.project_id.company_id.name }}: استطلاع الرضا "
