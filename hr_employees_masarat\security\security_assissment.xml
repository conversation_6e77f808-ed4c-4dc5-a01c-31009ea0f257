<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record model="ir.module.category" id="module_hr_masarat_assessment_module">
        <field name="name">Masarat Monthly Assessment Forms</field>
        <field name="description">Masarat Assessment Forms</field>
        <field name="sequence">46</field>
    </record>

    <record id="group_hr_masarat_assessment" model="res.groups">
        <field name="name">HR</field>
        <field name="category_id" ref="module_hr_masarat_assessment_module"/>
    </record>

    <record id="group_employee_masarat_assessment" model="res.groups">
        <field name="name">Employee</field>
        <field name="category_id" ref="module_hr_masarat_assessment_module"/>
    </record>

    <record id="group_manager_masarat_assessment" model="res.groups">
        <field name="name">Manager</field>
        <field name="category_id" ref="module_hr_masarat_assessment_module"/>
    </record>

    <record model="ir.rule" id="module_hr_masarat_assessment_module_employee_rule">
        <field name="name">Masarat Assessment Rule Employees</field>
        <field name="model_id" ref="model_hr_contract_assessment_monthly"/>
        <field name="domain_force">[('employee_id.user_id','=',user.id)]</field>
        <field name="groups" eval="[(4, ref('group_employee_masarat_assessment'))]"/>
    </record>

    <record model="ir.rule" id="module_hr_masarat_assessment_module_manager_rule">
        <field name="name">Masarat Assessment Rule Managers</field>
        <field name="model_id" ref="model_hr_contract_assessment_monthly"/>
        <field name="domain_force">["|", ("employee_id.parent_id.user_id","=",user.id), ('employee_id.user_id','=',user.id) ]</field>
        <field name="groups" eval="[(4, ref('group_manager_masarat_assessment'))]"/>
    </record>

<!--    -->
<!--  Annually  -->
<!--    -->
    <record model="ir.module.category" id="module_hr_masarat_assessment_annual_module">
        <field name="name">Masarat Annually Assessment Forms</field>
        <field name="description">Masarat Assessment Forms</field>
        <field name="sequence">46</field>
    </record>

    <record id="group_hr_masarat_assessment_annual" model="res.groups">
        <field name="name">HR</field>
        <field name="category_id" ref="module_hr_masarat_assessment_annual_module"/>
    </record>

    <record id="group_employee_masarat_assessment_annual" model="res.groups">
        <field name="name">Employee</field>
        <field name="category_id" ref="module_hr_masarat_assessment_annual_module"/>
    </record>

    <record id="group_manager_masarat_assessment_annual" model="res.groups">
        <field name="name">Manager</field>
        <field name="category_id" ref="module_hr_masarat_assessment_annual_module"/>
    </record>

    <record model="ir.rule" id="module_hr_masarat_assessment_annual_module_employee_rule">
        <field name="name">Masarat Annual Assessment Rule Employees</field>
        <field name="model_id" ref="model_hr_contract_assessment_annual"/>
        <field name="domain_force">[('employee_id.user_id','=',user.id)]</field>
        <field name="groups" eval="[(4, ref('group_employee_masarat_assessment_annual'))]"/>
    </record>

    <record model="ir.rule" id="module_hr_masarat_assessment_annual_module_manager_rule">
        <field name="name">Masarat Annual Assessment Rule Managers</field>
        <field name="model_id" ref="model_hr_contract_assessment_annual"/>
        <field name="domain_force">["|", ("employee_id.parent_id.user_id","=",user.id), ('employee_id.user_id','=',user.id) ]</field>
        <field name="groups" eval="[(4, ref('group_manager_masarat_assessment_annual'))]"/>
    </record>

    <!-- Leave to monetry Allocation -->
    <record id="group_hr_leave_topaid" model="res.groups">
        <field name="name">صلاحية تخصيص الاجازات الى قيمة مالية</field>
    </record>

</odoo>