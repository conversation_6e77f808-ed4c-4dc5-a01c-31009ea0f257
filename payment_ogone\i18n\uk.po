# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_ogone
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <alina.lis<PERSON><PERSON>@erp.co.ua>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:49+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_acquirer__ogone_userid
msgid "API User ID"
msgstr "ID користувача API "

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_acquirer__ogone_password
msgid "API User Password"
msgstr "Пароль користувача API"

#. module: payment_ogone
#: code:addons/payment_ogone/models/payment_acquirer.py:0
#, python-format
msgid "Could not establish the connection to the API."
msgstr "Не вдалося встановити з’єднання з API."

#. module: payment_ogone
#: code:addons/payment_ogone/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "Не знайдено жодної транзакції, що відповідає референсу %s."

#. module: payment_ogone
#: model:account.payment.method,name:payment_ogone.payment_method_ogone
#: model:ir.model.fields.selection,name:payment_ogone.selection__payment_acquirer__provider__ogone
msgid "Ogone"
msgstr "Ogone"

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_acquirer__ogone_pspid
msgid "PSPID"
msgstr "PSPID"

#. module: payment_ogone
#: model:ir.model,name:payment_ogone.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Платіжний еквайєр"

#. module: payment_ogone
#: model:ir.model,name:payment_ogone.model_account_payment_method
msgid "Payment Methods"
msgstr "Способи оплати"

#. module: payment_ogone
#: model:ir.model,name:payment_ogone.model_payment_token
msgid "Payment Token"
msgstr "Токен оплати"

#. module: payment_ogone
#: model:ir.model,name:payment_ogone.model_payment_transaction
msgid "Payment Transaction"
msgstr "Платіжна операція"

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_acquirer__provider
msgid "Provider"
msgstr "Провайдер"

#. module: payment_ogone
#: code:addons/payment_ogone/models/payment_transaction.py:0
#, python-format
msgid "Received data with invalid payment status: %s"
msgstr "Отримані дані з недійсним статусом платежу: %s"

#. module: payment_ogone
#: code:addons/payment_ogone/controllers/main.py:0
#, python-format
msgid ""
"Received data with invalid signature. expected: %(exp)s ; received: %(rec)s ; data:\n"
"%(data)s"
msgstr ""
"Отримані дані з недійсним підписом. Очікувано: %(exp)s ; отримано: %(rec)s ; дані:\n"
"%(data)s"

#. module: payment_ogone
#: code:addons/payment_ogone/models/payment_transaction.py:0
#, python-format
msgid "Received feedback data with unknown type: %s"
msgstr "Отриманий фідбек даних з невідомим типом: %s"

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_acquirer__ogone_shakey_in
msgid "SHA Key IN"
msgstr "SHA Key IN"

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_acquirer__ogone_shakey_out
msgid "SHA Key OUT"
msgstr "SHA Key OUT"

#. module: payment_ogone
#: code:addons/payment_ogone/models/payment_token.py:0
#, python-format
msgid "Saved payment methods cannot be restored once they have been archived."
msgstr ""
"Збережені способи оплати не можуть бути відновлені після їх архівації."

#. module: payment_ogone
#: code:addons/payment_ogone/models/payment_transaction.py:0
#, python-format
msgid "Storing your payment details is necessary for future use."
msgstr ""
"Збереження ваших платіжних даних необхідне для подальшого використання."

#. module: payment_ogone
#: model:ir.model.fields,help:payment_ogone.field_payment_acquirer__ogone_userid
msgid "The ID solely used to identify the API user with Ogone"
msgstr ""
"Ідентифікатор використовується виключно для ідентифікації користувача API з "
"Ogone"

#. module: payment_ogone
#: model:ir.model.fields,help:payment_ogone.field_payment_acquirer__ogone_pspid
msgid "The ID solely used to identify the account with Ogone"
msgstr ""
"Ідентифікатор використовується виключно для ідентифікації облікового запису "
"в Ogone"

#. module: payment_ogone
#: model:ir.model.fields,help:payment_ogone.field_payment_acquirer__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr "Постачальник платіжних послуг для використання з цим еквайєром"

#. module: payment_ogone
#: code:addons/payment_ogone/models/payment_acquirer.py:0
#, python-format
msgid "The communication with the API failed."
msgstr "Зв'язок з API не вдався."

#. module: payment_ogone
#: code:addons/payment_ogone/models/payment_transaction.py:0
#, python-format
msgid "The payment has been declined: %s"
msgstr "Платіж відхилено: %s"

#. module: payment_ogone
#: code:addons/payment_ogone/models/payment_transaction.py:0
#, python-format
msgid "The transaction is not linked to a token."
msgstr "Транзакція не зв'язана з токеном."
