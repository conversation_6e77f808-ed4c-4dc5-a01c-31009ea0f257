# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_fr_fec
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-27 14:43+0000\n"
"PO-Revision-Date: 2021-10-27 14:43+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "# 10"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "# 11"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "# 12"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "# 13"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "# 14"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "# 15"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "# 16"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "# 17"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "Cancel"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "Column"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "Comment"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "CompAuxLib"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "CompAuxNum"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "CompteLib"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "CompteNum"
msgstr ""

#. module: l10n_fr_fec
#: model:ir.model.fields,field_description:l10n_fr_fec.field_account_fr_fec__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_fr_fec
#: model:ir.model.fields,field_description:l10n_fr_fec.field_account_fr_fec__create_date
msgid "Created on"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "Credit"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "DateLet"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "Debit"
msgstr ""

#. module: l10n_fr_fec
#: model:ir.model.fields,field_description:l10n_fr_fec.field_account_fr_fec__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "EcritureDate"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "EcritureLet"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "EcritureLib"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "EcritureNum"
msgstr ""

#. module: l10n_fr_fec
#: model:ir.model.fields,field_description:l10n_fr_fec.field_account_fr_fec__date_to
msgid "End Date"
msgstr ""

#. module: l10n_fr_fec
#: model:ir.model.fields,field_description:l10n_fr_fec.field_account_fr_fec__export_type
msgid "Export Type"
msgstr ""

#. module: l10n_fr_fec
#: model:ir.actions.act_window,name:l10n_fr_fec.account_fr_fec_action
#: model:ir.ui.menu,name:l10n_fr_fec.account_fr_fec_menu
msgid "FEC"
msgstr ""

#. module: l10n_fr_fec
#: model:ir.model.fields,field_description:l10n_fr_fec.field_account_fr_fec__fec_data
msgid "FEC File"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "FEC File Generation"
msgstr ""

#. module: l10n_fr_fec
#: model:ir.model,name:l10n_fr_fec.model_account_fr_fec
msgid "Ficher Echange Informatise"
msgstr ""

#. module: l10n_fr_fec
#: model:ir.model.fields,field_description:l10n_fr_fec.field_account_fr_fec__filename
msgid "Filename"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "Generate"
msgstr ""

#. module: l10n_fr_fec
#: model:ir.model.fields,field_description:l10n_fr_fec.field_account_fr_fec__id
msgid "ID"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "Idevise"
msgstr ""

#. module: l10n_fr_fec
#: code:addons/l10n_fr_fec/wizard/account_fr_fec.py:0
#, python-format
msgid "Invalid VAT number for company %s"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "JournalCode"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "JournalLib"
msgstr ""

#. module: l10n_fr_fec
#: model:ir.model.fields,field_description:l10n_fr_fec.field_account_fr_fec____last_update
msgid "Last Modified on"
msgstr ""

#. module: l10n_fr_fec
#: model:ir.model.fields,field_description:l10n_fr_fec.field_account_fr_fec__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_fr_fec
#: model:ir.model.fields,field_description:l10n_fr_fec.field_account_fr_fec__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_fr_fec
#: code:addons/l10n_fr_fec/wizard/account_fr_fec.py:0
#, python-format
msgid "Missing VAT number for company %s"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "Montantdevise"
msgstr ""

#. module: l10n_fr_fec
#: model:ir.model.fields.selection,name:l10n_fr_fec.selection__account_fr_fec__export_type__nonofficial
msgid "Non-official FEC report (posted and unposted entries)"
msgstr ""

#. module: l10n_fr_fec
#: model:ir.model.fields.selection,name:l10n_fr_fec.selection__account_fr_fec__export_type__official
msgid "Official FEC report (posted entries only)"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "Options"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "PieceDate"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "PieceRef"
msgstr ""

#. module: l10n_fr_fec
#: model:ir.model.fields,field_description:l10n_fr_fec.field_account_fr_fec__date_from
msgid "Start Date"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "Technical Info"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "Technical Name"
msgstr ""

#. module: l10n_fr_fec
#: model:ir.model.fields,field_description:l10n_fr_fec.field_account_fr_fec__test_file
msgid "Test File"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid ""
"The encoding of this text file is UTF-8. The structure of file is CSV "
"separated by pipe '|'."
msgstr ""

#. module: l10n_fr_fec
#: code:addons/l10n_fr_fec/wizard/account_fr_fec.py:0
#, python-format
msgid "The start date must be inferior to the end date."
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "ValidDate"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid "We use partner.id"
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid ""
"When you download a FEC file, the lock date is set to the end date.\n"
"                If you want to test the FEC file generation, please tick the test file checkbox."
msgstr ""

#. module: l10n_fr_fec
#: model_terms:ir.ui.view,arch_db:l10n_fr_fec.account_fr_fec_view
msgid ""
"You are in test mode. The FEC file generation will not set the lock date."
msgstr ""

#. module: l10n_fr_fec
#: code:addons/l10n_fr_fec/wizard/account_fr_fec.py:0
#, python-format
msgid "You could not set the start date or the end date in the future."
msgstr ""
