# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase_requisition_stock
# 
# Translators:
# <PERSON><PERSON>, 2022
# <PERSON><PERSON> (Quartile) <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: purchase_requisition_stock
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_purchase_requisition_line__move_dest_id
msgid "Downstream Move"
msgstr "下流の動き"

#. module: purchase_requisition_stock
#: model:ir.model,name:purchase_requisition_stock.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "最小在庫ルール"

#. module: purchase_requisition_stock
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_purchase_requisition__picking_type_id
msgid "Operation Type"
msgstr "オペレーションタイプ"

#. module: purchase_requisition_stock
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_purchase_requisition__procurement_group_id
msgid "Procurement Group"
msgstr "調達グループ"

#. module: purchase_requisition_stock
#: model:ir.model,name:purchase_requisition_stock.model_purchase_order
msgid "Purchase Order"
msgstr "購買オーダ"

#. module: purchase_requisition_stock
#: model:ir.model,name:purchase_requisition_stock.model_purchase_requisition
msgid "Purchase Requisition"
msgstr "購買要求"

#. module: purchase_requisition_stock
#: model:ir.model,name:purchase_requisition_stock.model_purchase_requisition_line
msgid "Purchase Requisition Line"
msgstr "購買要求明細"

#. module: purchase_requisition_stock
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_stock_move__requisition_line_ids
msgid "Requisition Line"
msgstr "要求ライン"

#. module: purchase_requisition_stock
#: model:ir.model,name:purchase_requisition_stock.model_stock_move
msgid "Stock Move"
msgstr "在庫移動"

#. module: purchase_requisition_stock
#: model:ir.model,name:purchase_requisition_stock.model_stock_rule
msgid "Stock Rule"
msgstr "在庫規則"

#. module: purchase_requisition_stock
#: model:ir.model.fields,field_description:purchase_requisition_stock.field_purchase_requisition__warehouse_id
msgid "Warehouse"
msgstr "倉庫"
