# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_mondialrelay
# 
# Translators:
# <PERSON>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:21+0000\n"
"Last-Translator: <PERSON>, 2021\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: delivery_mondialrelay
#: model:ir.model.fields,field_description:delivery_mondialrelay.field_choose_delivery_carrier__mondialrelay_brand
#: model:ir.model.fields,field_description:delivery_mondialrelay.field_delivery_carrier__mondialrelay_brand
msgid "Brand Code"
msgstr ""

#. module: delivery_mondialrelay
#: model:ir.model,name:delivery_mondialrelay.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: delivery_mondialrelay
#: model:ir.model.fields,field_description:delivery_mondialrelay.field_choose_delivery_carrier__shipping_country_code
msgid "Country Code"
msgstr "Lande kode"

#. module: delivery_mondialrelay
#: model:ir.model,name:delivery_mondialrelay.model_choose_delivery_carrier
msgid "Delivery Carrier Selection Wizard"
msgstr "Leveringsvirksomhed valg guide"

#. module: delivery_mondialrelay
#: model:ir.model.fields,field_description:delivery_mondialrelay.field_choose_delivery_carrier__is_mondialrelay
#: model:ir.model.fields,field_description:delivery_mondialrelay.field_delivery_carrier__is_mondialrelay
#: model:ir.model.fields,field_description:delivery_mondialrelay.field_res_partner__is_mondialrelay
#: model:ir.model.fields,field_description:delivery_mondialrelay.field_res_users__is_mondialrelay
msgid "Is Mondialrelay"
msgstr ""

#. module: delivery_mondialrelay
#: model:ir.model.fields,field_description:delivery_mondialrelay.field_choose_delivery_carrier__mondialrelay_last_selected
msgid "Last Relay Selected"
msgstr ""

#. module: delivery_mondialrelay
#: model:delivery.carrier,name:delivery_mondialrelay.delivery_carrier_mondialrelay_be_lu
#: model:delivery.carrier,name:delivery_mondialrelay.delivery_carrier_mondialrelay_es
#: model:delivery.carrier,name:delivery_mondialrelay.delivery_carrier_mondialrelay_fr_nl
#: model:product.product,name:delivery_mondialrelay.product_product_delivery_mondialrelay
#: model:product.template,name:delivery_mondialrelay.product_product_delivery_mondialrelay_product_template
msgid "Mondial Relay"
msgstr ""

#. module: delivery_mondialrelay
#: code:addons/delivery_mondialrelay/models/sale_order.py:0
#, python-format
msgid ""
"Mondial Relay mismatching between delivery method and shipping address."
msgstr ""

#. module: delivery_mondialrelay
#: model:ir.model.fields,field_description:delivery_mondialrelay.field_choose_delivery_carrier__mondialrelay_allowed_countries
msgid "Mondialrelay Allowed Countries"
msgstr ""

#. module: delivery_mondialrelay
#: model:ir.model.fields,field_description:delivery_mondialrelay.field_choose_delivery_carrier__mondialrelay_last_selected_id
msgid "Mondialrelay Last Selected"
msgstr ""

#. module: delivery_mondialrelay
#: model:ir.model.fields,field_description:delivery_mondialrelay.field_choose_delivery_carrier__mondialrelay_colLivMod
#: model:ir.model.fields,field_description:delivery_mondialrelay.field_delivery_carrier__mondialrelay_packagetype
msgid "Mondialrelay Packagetype"
msgstr ""

#. module: delivery_mondialrelay
#: code:addons/delivery_mondialrelay/wizard/choose_delivery_carrier.py:0
#, python-format
msgid "Please, choose a Parcel Point"
msgstr ""

#. module: delivery_mondialrelay
#: model:ir.model,name:delivery_mondialrelay.model_sale_order
msgid "Sales Order"
msgstr "Salgsordre"

#. module: delivery_mondialrelay
#: model:ir.model,name:delivery_mondialrelay.model_delivery_carrier
msgid "Shipping Methods"
msgstr "Leveringsmetoder"

#. module: delivery_mondialrelay
#: model:ir.model.fields,help:delivery_mondialrelay.field_choose_delivery_carrier__shipping_country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"ISO-landekoden med to tegn. \n"
"Dette felt kan bruges til hurtig søgning."

#. module: delivery_mondialrelay
#: model:product.product,uom_name:delivery_mondialrelay.product_product_delivery_mondialrelay
#: model:product.template,uom_name:delivery_mondialrelay.product_product_delivery_mondialrelay_product_template
msgid "Units"
msgstr "Enheder"

#. module: delivery_mondialrelay
#: model:ir.model.fields,field_description:delivery_mondialrelay.field_choose_delivery_carrier__shipping_zip
msgid "Zip"
msgstr "Postnummer"
