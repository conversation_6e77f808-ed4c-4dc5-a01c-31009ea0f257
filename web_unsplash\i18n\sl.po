# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_unsplash
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: web_unsplash
#: model_terms:ir.ui.view,arch_db:web_unsplash.res_config_settings_view_form
msgid "<i class=\"fa fa-arrow-right\"/> Generate an Access Key"
msgstr "<i class=\"fa fa-arrow-right\"/> Ustvari dostopni ključ"

#. module: web_unsplash
#: model:ir.model.fields,field_description:web_unsplash.field_res_config_settings__unsplash_access_key
msgid "Access Key"
msgstr "Ključ za dostop"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Application ID"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Apply"
msgstr "Uporabi"

#. module: web_unsplash
#: model:ir.model,name:web_unsplash.model_res_config_settings
msgid "Config Settings"
msgstr "Uredi nastavitve"

#. module: web_unsplash
#: code:addons/web_unsplash/controllers/main.py:0
#, python-format
msgid "ERROR: Unknown Unsplash URL!"
msgstr ""

#. module: web_unsplash
#: code:addons/web_unsplash/controllers/main.py:0
#, python-format
msgid "ERROR: Unknown Unsplash notify URL!"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Get an Access key"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Paste your access key here"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Paste your application ID here"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Photos (via Unsplash)"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Please check your Unsplash access key and application ID."
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Please check your internet connection or contact administrator."
msgstr ""

#. module: web_unsplash
#: model:ir.model,name:web_unsplash.model_ir_qweb_field_image
msgid "Qweb Field Image"
msgstr "Qweb polje - slika"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Search is temporarily unavailable"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Setup Unsplash to access royalty free photos."
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Something went wrong"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid ""
"The max number of searches is exceeded. Please retry in an hour or extend to"
" a better account."
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "Unauthorized Key"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/js/unsplash_image_widget.js:0
#, python-format
msgid "Uploading %s '%s' images."
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/js/unsplash_image_widget.js:0
#, python-format
msgid "Uploading '%s' image."
msgstr ""

#. module: web_unsplash
#: model:ir.model,name:web_unsplash.model_res_users
msgid "Users"
msgstr "Uporabniki"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "and paste"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "and paste it here:"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:0
#, python-format
msgid "here:"
msgstr ""
