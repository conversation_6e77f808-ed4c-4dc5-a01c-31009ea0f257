<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
    <rect id="path-1" width="6.535" height="6.535" x="0" y="0"/>
    <rect id="path-2" width="5" height="2" x="0" y="0"/>
    <path id="path-3" d="M5.6 3zm2.4-3v1H0v-1h7z"/>
    <rect id="path-4" width="10.5" height="9.535" x="0" y="0"/>
    <path id="path-5" d="M0 0L11 0 11 1 0 1M0 2L8 2 8 3 0 3M0 4L11 4 11 5 0 5"/>
    <rect id="path-6" width="8" height="1" x="34" y="3"/>
    <linearGradient id="linearGradient-7" x1="72.875%" x2="40.332%" y1="46.704%" y2="35.129%">
      <stop offset="0%" stop-color="#008374"/>
      <stop offset="100%" stop-color="#006A59"/>
    </linearGradient>
    <linearGradient id="linearGradient-8" x1="88.517%" x2="50%" y1="40.057%" y2="50%">
      <stop offset="0%" stop-color="#00AA89"/>
      <stop offset="100%" stop-color="#009989"/>
    </linearGradient>
    <linearGradient id="linearGradient-9" x1="72.875%" x2="40.332%" y1="46.704%" y2="35.129%">
      <stop offset="0%" stop-color="#008374"/>
      <stop offset="100%" stop-color="#006A59"/>
    </linearGradient>
    <linearGradient id="linearGradient-10" x1="88.517%" x2="50%" y1="40.057%" y2="50%">
      <stop offset="0%" stop-color="#00AA89"/>
      <stop offset="100%" stop-color="#009989"/>
    </linearGradient>
    <filter id="filter-11" width="107.1%" height="150%" x="-3.6%" y="-12.5%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.0995137675 0"/>
    </filter>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_mega_menu_images_subtitles">
      <rect width="82" height="60" class="bg"/>
      <g class="group" transform="translate(15 17)">
        <g class="group">
          <g class="image_1_border">
            <rect width="6" height="6" fill="#FFF" class="rectangle"/>
            <g class="oval___oval_mask" transform="translate(.25 .233)">
              <mask id="mask-8" fill="#fff">
                <use xlink:href="#path-1"/>
              </mask>
              <use fill="#79D1F2" class="mask" xlink:href="#path-1"/>
              <ellipse cx="5.625" cy="2.442" fill="#F3EC60" class="oval" mask="url(#mask-8)" rx="1.875" ry="1.744"/>
              <ellipse cx="12.625" cy="9.465" fill="url(#linearGradient-7)" class="oval" mask="url(#mask-8)" rx="5.875" ry="3.488"/>
              <ellipse cx=".125" cy="9.581" fill="url(#linearGradient-8)" class="oval" mask="url(#mask-8)" rx="9.375" ry="5.465"/>
            </g>
            <path fill="#FFF" d="M7 0V7H0V0H7ZM6 1H1V6H6V1Z" class="rectangle_2"/>
          </g>
          <g class="rectangle" transform="translate(9 1)">
            <use fill="#000" filter="url(#filter-2)" xlink:href="#path-2"/>
            <use fill="#FFF" fill-opacity=".78" xlink:href="#path-2"/>
          </g>
          <g class="combined_shape" transform="translate(9 4)">
            <use fill="#000" filter="url(#filter-11)" xlink:href="#path-3"/>
            <use fill="#FFF" fill-opacity=".348" xlink:href="#path-3"/>
          </g>
        </g>
        <g class="group" transform="translate(20)">
          <g class="image_1_border">
            <rect width="6" height="6" fill="#FFF" class="rectangle"/>
            <g class="oval___oval_mask" transform="translate(.25 .233)">
              <mask id="mask-8" fill="#fff">
                <use xlink:href="#path-1"/>
              </mask>
              <use fill="#79D1F2" class="mask" xlink:href="#path-1"/>
              <ellipse cx="5.625" cy="2.442" fill="#F3EC60" class="oval" mask="url(#mask-8)" rx="1.875" ry="1.744"/>
              <ellipse cx="12.625" cy="9.465" fill="url(#linearGradient-7)" class="oval" mask="url(#mask-8)" rx="5.875" ry="3.488"/>
              <ellipse cx=".125" cy="9.581" fill="url(#linearGradient-8)" class="oval" mask="url(#mask-8)" rx="9.375" ry="5.465"/>
            </g>
            <path fill="#FFF" d="M7 0V7H0V0H7ZM6 1H1V6H6V1Z" class="rectangle_2"/>
          </g>
          <g class="rectangle" transform="translate(9 1)">
            <use fill="#000" filter="url(#filter-2)" xlink:href="#path-2"/>
            <use fill="#FFF" fill-opacity=".78" xlink:href="#path-2"/>
          </g>
          <g class="combined_shape" transform="translate(9 4)">
            <use fill="#000" filter="url(#filter-11)" xlink:href="#path-3"/>
            <use fill="#FFF" fill-opacity=".348" xlink:href="#path-3"/>
          </g>
        </g>
        <g class="group" transform="translate(0 9)">
          <g class="image_1_border">
            <rect width="6" height="6" fill="#FFF" class="rectangle"/>
            <g class="oval___oval_mask" transform="translate(.25 .233)">
              <mask id="mask-8" fill="#fff">
                <use xlink:href="#path-1"/>
              </mask>
              <use fill="#79D1F2" class="mask" xlink:href="#path-1"/>
              <ellipse cx="5.625" cy="2.442" fill="#F3EC60" class="oval" mask="url(#mask-8)" rx="1.875" ry="1.744"/>
              <ellipse cx="12.625" cy="9.465" fill="url(#linearGradient-7)" class="oval" mask="url(#mask-8)" rx="5.875" ry="3.488"/>
              <ellipse cx=".125" cy="9.581" fill="url(#linearGradient-8)" class="oval" mask="url(#mask-8)" rx="9.375" ry="5.465"/>
            </g>
            <path fill="#FFF" d="M7 0V7H0V0H7ZM6 1H1V6H6V1Z" class="rectangle_2"/>
          </g>
          <g class="rectangle" transform="translate(9 1)">
            <use fill="#000" filter="url(#filter-2)" xlink:href="#path-2"/>
            <use fill="#FFF" fill-opacity=".78" xlink:href="#path-2"/>
          </g>
          <g class="combined_shape" transform="translate(9 4)">
            <use fill="#000" filter="url(#filter-11)" xlink:href="#path-3"/>
            <use fill="#FFF" fill-opacity=".348" xlink:href="#path-3"/>
          </g>
        </g>
        <g class="group" transform="translate(20 9)">
          <g class="image_1_border">
            <rect width="6" height="6" fill="#FFF" class="rectangle"/>
            <g class="oval___oval_mask" transform="translate(.25 .233)">
              <mask id="mask-8" fill="#fff">
                <use xlink:href="#path-1"/>
              </mask>
              <use fill="#79D1F2" class="mask" xlink:href="#path-1"/>
              <ellipse cx="5.625" cy="2.442" fill="#F3EC60" class="oval" mask="url(#mask-8)" rx="1.875" ry="1.744"/>
              <ellipse cx="12.625" cy="9.465" fill="url(#linearGradient-7)" class="oval" mask="url(#mask-8)" rx="5.875" ry="3.488"/>
              <ellipse cx=".125" cy="9.581" fill="url(#linearGradient-8)" class="oval" mask="url(#mask-8)" rx="9.375" ry="5.465"/>
            </g>
            <path fill="#FFF" d="M7 0V7H0V0H7ZM6 1H1V6H6V1Z" class="rectangle_2"/>
          </g>
          <g class="rectangle" transform="translate(9 1)">
            <use fill="#000" filter="url(#filter-2)" xlink:href="#path-2"/>
            <use fill="#FFF" fill-opacity=".78" xlink:href="#path-2"/>
          </g>
          <g class="combined_shape" transform="translate(9 4)">
            <use fill="#000" filter="url(#filter-11)" xlink:href="#path-3"/>
            <use fill="#FFF" fill-opacity=".348" xlink:href="#path-3"/>
          </g>
        </g>
        <g class="group" transform="translate(0 18)">
          <g class="image_1_border">
            <rect width="6" height="6" fill="#FFF" class="rectangle"/>
            <g class="oval___oval_mask" transform="translate(.25 .233)">
              <mask id="mask-8" fill="#fff">
                <use xlink:href="#path-1"/>
              </mask>
              <use fill="#79D1F2" class="mask" xlink:href="#path-1"/>
              <ellipse cx="5.625" cy="2.442" fill="#F3EC60" class="oval" mask="url(#mask-8)" rx="1.875" ry="1.744"/>
              <ellipse cx="12.625" cy="9.465" fill="url(#linearGradient-7)" class="oval" mask="url(#mask-8)" rx="5.875" ry="3.488"/>
              <ellipse cx=".125" cy="9.581" fill="url(#linearGradient-8)" class="oval" mask="url(#mask-8)" rx="9.375" ry="5.465"/>
            </g>
            <path fill="#FFF" d="M7 0V7H0V0H7ZM6 1H1V6H6V1Z" class="rectangle_2"/>
          </g>
          <g class="rectangle" transform="translate(9 1)">
            <use fill="#000" filter="url(#filter-2)" xlink:href="#path-2"/>
            <use fill="#FFF" fill-opacity=".78" xlink:href="#path-2"/>
          </g>
          <g class="combined_shape" transform="translate(9 4)">
            <use fill="#000" filter="url(#filter-11)" xlink:href="#path-3"/>
            <use fill="#FFF" fill-opacity=".348" xlink:href="#path-3"/>
          </g>
        </g>
        <g class="group" transform="translate(20 18)">
          <g class="image_1_border">
            <rect width="6" height="6" fill="#FFF" class="rectangle"/>
            <g class="oval___oval_mask" transform="translate(.25 .233)">
              <mask id="mask-8" fill="#fff">
                <use xlink:href="#path-1"/>
              </mask>
              <use fill="#79D1F2" class="mask" xlink:href="#path-1"/>
              <ellipse cx="5.625" cy="2.442" fill="#F3EC60" class="oval" mask="url(#mask-8)" rx="1.875" ry="1.744"/>
              <ellipse cx="12.625" cy="9.465" fill="url(#linearGradient-7)" class="oval" mask="url(#mask-8)" rx="5.875" ry="3.488"/>
              <ellipse cx=".125" cy="9.581" fill="url(#linearGradient-8)" class="oval" mask="url(#mask-8)" rx="9.375" ry="5.465"/>
            </g>
            <path fill="#FFF" d="M7 0V7H0V0H7ZM6 1H1V6H6V1Z" class="rectangle_2"/>
          </g>
          <g class="rectangle" transform="translate(9 1)">
            <use fill="#000" filter="url(#filter-2)" xlink:href="#path-2"/>
            <use fill="#FFF" fill-opacity=".78" xlink:href="#path-2"/>
          </g>
          <g class="combined_shape" transform="translate(9 4)">
            <use fill="#000" filter="url(#filter-11)" xlink:href="#path-3"/>
            <use fill="#FFF" fill-opacity=".348" xlink:href="#path-3"/>
          </g>
        </g>
        <g class="image_2_border" transform="translate(42)">
          <rect width="11" height="10" fill="#FFF" class="rectangle"/>
          <g class="oval___oval_mask" transform="translate(.25 .233)">
            <mask id="mask-9" fill="#fff">
              <use xlink:href="#path-4"/>
            </mask>
            <use fill="#79D1F2" class="mask" xlink:href="#path-4"/>
            <ellipse cx="9.625" cy="2.442" fill="#F3EC60" class="oval" mask="url(#mask-9)" rx="1.875" ry="1.744"/>
            <ellipse cx="12.625" cy="10.465" fill="url(#linearGradient-9)" class="oval" mask="url(#mask-9)" rx="5.875" ry="3.488"/>
            <ellipse cx=".125" cy="10.581" fill="url(#linearGradient-10)" class="oval" mask="url(#mask-9)" rx="9.375" ry="5.465"/>
          </g>
          <path fill="#FFF" d="M11 0v10H0V0h11zm-11 1H1v8h9V1z" class="rectangle_2"/>
        </g>
        <g class="combined_shape" transform="translate(42 16)">
          <use fill="#000" filter="url(#filter-12)" xlink:href="#path-5"/>
          <use fill="#FFF" fill-opacity=".348" xlink:href="#path-5"/>
        </g>
        <g class="rectangle_copy" transform="translate(8 10)">
          <use fill="#000" filter="url(#filter-11)" xlink:href="#path-6"/>
          <use fill="#FFF" fill-opacity=".78" xlink:href="#path-6"/>
        </g>
      </g>
    </g>
  </g>
</svg>
