# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_setup
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: Ra<PERSON><PERSON><PERSON> Lappiam, 2023\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_edition.xml:0
#, python-format
msgid "(Community Edition)"
msgstr "(Community Edition)"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "<span class=\"fa fa-lg fa-users\" aria-label=\"Number of active users\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-users\" aria-"
"label=\"จำนวนผู้ใช้งานที่ใช้งานอยู่\"/>"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\" attrs=\"{'invisible':[('active_user_count', '&gt;', '1')]}\">\n"
"                                            Active User\n"
"                                        </span>\n"
"                                        <span class=\"o_form_label\" attrs=\"{'invisible':[('active_user_count', '&lt;=', '1')]}\">\n"
"                                            Active Users\n"
"                                        </span>"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\" attrs=\"{'invisible':[('company_count', '&gt;', '1')]}\">\n"
"                                            Company\n"
"                                        </span>\n"
"                                        <span class=\"o_form_label\" attrs=\"{'invisible':[('company_count', '&lt;=', '1')]}\">\n"
"                                            Companies\n"
"                                        </span>\n"
"                                        <br/>"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\" attrs=\"{'invisible':[('language_count', '&gt;', '1')]}\">\n"
"                                                Language\n"
"                                            </span>\n"
"                                            <span class=\"o_form_label\" attrs=\"{'invisible':[('language_count', '&lt;=', '1')]}\">\n"
"                                                Languages\n"
"                                            </span>"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Document Layout</span>\n"
"                                        <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"o_form_label\">เค้าโครงเอกสาร</span>\n"
"                                        <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back here to choose your Geo "
"Provider."
msgstr ""
"<strong> บันทึก </strong> หน้านี้และกลับมาที่นี่เพื่อเลือก Geo Provider "
"ของคุณ"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back here to set up reCaptcha."
msgstr "<strong>บันทึก</strong>หน้านี้และกลับมาที่นี่เพื่อตั้งค่า reCaptcha"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back here to set up the feature."
msgstr "<strong> บันทึก </strong> หน้านี้และกลับมาที่นี่เพื่อตั้งค่าคุณลักษณะ"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "About"
msgstr "เกี่ยวกับ"

#. module: base_setup
#: code:addons/base_setup/controllers/main.py:0
#, python-format
msgid "Access Denied"
msgstr "ไม่ได้รับอนุญาตให้เข้าใช้"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_dev_tool.xml:0
#, python-format
msgid "Activate the developer mode"
msgstr "เปิดใช้งานโหมดนักพัฒนา"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_dev_tool.xml:0
#, python-format
msgid "Activate the developer mode (with assets)"
msgstr "เปิดใช้งานโหมดนักพัฒนา (พร้อมกับ assets)"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_dev_tool.xml:0
#, python-format
msgid "Activate the developer mode (with tests assets)"
msgstr "เปิดใช้งานโหมดนักพัฒนา (พร้อมกับ assets ทดสอบ)"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Add Language"
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Add fun feedback and motivate your employees"
msgstr "เพิ่มข้อเสนอแนะที่สนุกสนานและกระตุ้นพนักงานของคุณ"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_mail_plugin
msgid "Allow integration with the mail plugins"
msgstr "อนุญาตให้รวมเข้ากับปลั๊กอินเมล"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_google_calendar
msgid "Allow the users to synchronize their calendar  with Google Calendar"
msgstr "อนุญาตให้ผู้ใช้เชื่อมโยงปฏิทินด้วย Google Calendar"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_microsoft_calendar
msgid "Allow the users to synchronize their calendar with Outlook Calendar"
msgstr "อนุญาตให้ผู้ใช้ซิงโครไนซ์ปฏิทินกับปฏิทิน Outlook"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_base_import
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Allow users to import data from CSV/XLS/XLSX/ODS files"
msgstr "อนุญาตให้ผู้ใช้นำเข้าข้อมูลจากไฟล์ CSV/XLS/XLSX/ODS"

#. module: base_setup
#: model:ir.model.fields,help:base_setup.field_res_config_settings__group_multi_currency
msgid "Allows to work in a multi currency environment"
msgstr "อนุญาตให้ทำงานในหลายสกุลเงิน"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_voip
msgid "Asterisk (VoIP)"
msgstr "Asterisk (VoIP)"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_google_drive
msgid "Attach Google documents to any record"
msgstr "แนบเอกสาร Google กับรายการใดๆ"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Automatically enrich your contact base with company data"
msgstr "เพิ่มฐานข้อมูลการติดต่อของคุณโดยอัตโนมัติด้วยข้อมูล บริษัท"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"Automatically generate counterpart documents for orders/invoices between "
"companies"
msgstr ""
"สร้างเอกสารคู่สัญญาโดยอัตโนมัติสำหรับคำสั่งซื้อ / ใบแจ้งหนี้ระหว่าง บริษัท"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"By default, new users get highest access rights for all installed apps."
msgstr ""
"โดยค่าเริ่มต้นผู้ใช้ใหม่จะได้รับสิทธิ์การเข้าถึงสูงสุดสำหรับแอปที่ติดตั้งทั้งหมด"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Choose the layout of your documents"
msgstr "เลือกเค้าโครงของเอกสารของคุณ"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_pad
msgid "Collaborative Pads"
msgstr "Collaborative Pads"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Companies"
msgstr "บริษัท"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__company_id
msgid "Company"
msgstr "บริษัท"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__company_informations
msgid "Company Informations"
msgstr "ข้อมูลทั่วไปของบริษัท"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__company_name
msgid "Company Name"
msgstr "ชื่อบริษัท"

#. module: base_setup
#: model:ir.model,name:base_setup.model_res_config_settings
msgid "Config Settings"
msgstr "การตั้งค่า"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Configure Document Layout"
msgstr "กำหนดค่าเค้าโครงเอกสาร"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"Configure company rules to automatically create SO/PO when one of your "
"company sells/buys to another of your company."
msgstr ""
"กำหนดค่ากฎของ บริษัท เพื่อสร้าง SO/PO โดยอัตโนมัติเมื่อ บริษัท ของคุณขาย / "
"ซื้อให้กับ บริษัท อื่นของคุณ"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Contacts"
msgstr "รายชื่อ"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_edition.xml:0
#, python-format
msgid "Copyright © 2004"
msgstr "สงวนลิขสิทธิ์ © 2004"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Create and attach Google Drive documents to any record"
msgstr "สร้างและแนบเอกสาร Google Drive กับบันทึกใด ๆ"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__external_email_server_default
msgid "Custom Email Servers"
msgstr ""

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__report_footer
msgid "Custom Report Footer"
msgstr "ส่วนท้ายรายงานที่กำหนดเอง"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_dev_tool.xml:0
#, python-format
msgid "Deactivate the developer mode"
msgstr "ปิดใช้งานโหมดนักพัฒนา"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__user_default_rights
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Default Access Rights"
msgstr "สิทธิ์การเข้าถึงเริ่มต้น"

#. module: base_setup
#: code:addons/base_setup/models/res_config_settings.py:0
#, python-format
msgid "Default User Template not found."
msgstr "ไม่พบเทมเพลตผู้ใช้เริ่มต้น"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_dev_tool.xml:0
#, python-format
msgid "Developer Tools"
msgstr "เครื่องมือของนักพัฒนา"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__external_report_layout_id
msgid "Document Template"
msgstr "เอกสาร"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Documentation"
msgstr "เอกสารกำกับ"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Edit Layout"
msgstr "แก้ไขเค้าโครง"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/js/res_config_invite_users.js:0
#, python-format
msgid "Email addresses already existing: %s."
msgstr ""

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"Enable the profiling tool. Profiling may impact performance while being "
"active."
msgstr ""
"เปิดใช้งานเครื่องมือสร้างโปรไฟล์ "
"การทำโปรไฟล์อาจส่งผลกระทบต่อประสิทธิภาพในขณะที่ใช้งานอยู่"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_invite_users.xml:0
#, python-format
msgid "Enter e-mail address"
msgstr "ใส่ที่อยู่อีเมลล์"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Extract and analyze Odoo data from Google Spreadsheet"
msgstr "แยกและวิเคราะห์ข้อมูล Odoo จาก Google Spreadsheet"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Find free high-resolution images from Unsplash"
msgstr "ค้นหาภาพความละเอียดสูงฟรีจาก Unsplash"

#. module: base_setup
#: model:ir.model.fields,help:base_setup.field_res_config_settings__report_footer
msgid "Footer text displayed at the bottom of all reports."
msgstr "ข้อความท้ายกระดาษปรากฏอยู่ด้านล่างของรายงานทั้งหมด"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_edition.xml:0
#, python-format
msgid "GNU LGPL Licensed"
msgstr "GNU LGPL Licensed"

#. module: base_setup
#: model:ir.ui.menu,name:base_setup.menu_config
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "General Settings"
msgstr "การตั้งค่าทั่วไป"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Geo Localization"
msgstr "การแปลทางภูมิศาสตร์"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_base_geolocalize
msgid "GeoLocalize"
msgstr "GeoLocalize"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "GeoLocalize your partners"
msgstr "GeoLocalize คู่ค้าของคุณ"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_product_images
msgid "Get product pictures using barcode"
msgstr "รับรูปภาพผลิตภัณฑ์โดยใช้บาร์โค้ด"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Google Calendar"
msgstr "Google Calendar"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Google Drive"
msgstr "Google Drive"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_google_spreadsheet
msgid "Google Spreadsheet"
msgstr "Google Spreadsheet"

#. module: base_setup
#: model:ir.model,name:base_setup.model_ir_http
msgid "HTTP Routing"
msgstr "See http://openerp.com"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Import & Export"
msgstr "นำเข้า & ส่งออก"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Integrate with mail client plugins"
msgstr "ผสานรวมกับปลั๊กอินไคลเอนต์เมล"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Integrations"
msgstr "Integrations"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Inter-Company Transactions"
msgstr "ธุรกรรมระหว่าง บริษัท"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/js/res_config_invite_users.js:0
#, python-format
msgid "Invalid email addresses: %s."
msgstr ""

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_invite_users.xml:0
#, python-format
msgid "Invite"
msgstr "เชิญ"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_invite_users.xml:0
#, python-format
msgid "Invite New Users"
msgstr "เชิญผู้ใช้งานใหม่"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_auth_ldap
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "LDAP Authentication"
msgstr "LDAP Authentication"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Languages"
msgstr "ภาษา"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Layout"
msgstr "ใบกำกับสินค้า/ใบแจ้งหนี้"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_dev_tool.xml:0
#, python-format
msgid "Load demo data"
msgstr "ตัวอย่าง ข้อมูล"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Mail Plugin"
msgstr "ปลั๊กอินเมล"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Manage API Keys"
msgstr "จัดการคีย์ API"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Manage Companies"
msgstr "จัดการบริษัท"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_account_inter_company_rules
msgid "Manage Inter Company"
msgstr "จัดการบริษัทที่มีสาขา"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Manage Languages"
msgstr "จัดการภาษา"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Manage Users"
msgstr "จัดการผู้ใช้"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__group_multi_currency
msgid "Multi-Currencies"
msgstr "หลายสกุลเงิน"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__active_user_count
msgid "Number of Active Users"
msgstr "จำนวนผู้ใช้ที่ใช้งานอยู่"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__company_count
msgid "Number of Companies"
msgstr "บริษัท"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__language_count
msgid "Number of Languages"
msgstr "จำนวนภาษา"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "OAuth Authentication"
msgstr "OAuth Authentication"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_edition.xml:0
#, python-format
msgid "Odoo"
msgstr "Odoo"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_edition.xml:0
#, python-format
msgid "Odoo S.A."
msgstr "Odoo S.A."

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "On Apple Store"
msgstr "บน Apple Store"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "On Google Play"
msgstr "บน Google Play"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Outlook Calendar"
msgstr "ปฏิทิน Outlook"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_partner_autocomplete
msgid "Partner Autocomplete"
msgstr "คู่ค้า"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_invite_users.xml:0
#, python-format
msgid "Pending Invitations:"
msgstr "คำเชิญที่รอดำเนินการ:"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Performance"
msgstr "ประสิทธิภาพ"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Permissions"
msgstr "สิทธิ์"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Preview Document"
msgstr "ดูตัวอย่างเอกสาร"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__profiling_enabled_until
msgid "Profiling enabled until"
msgstr "เปิดใช้งานโปรไฟล์จนถึง"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Protect your forms from spam and abuse."
msgstr "ปกป้องแบบฟอร์มของคุณจากสแปมและการละเมิด"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Send SMS"
msgstr "ส่ง SMS"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Send texts to your contacts"
msgstr "ส่งข้อความถึงผู้ติดต่อของคุณ"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Set custom access rights for new users"
msgstr "กำหนดสิทธิ์การเข้าถึงแบบกำหนดเองสำหรับผู้ใช้ใหม่"

#. module: base_setup
#: model:ir.actions.act_window,name:base_setup.action_general_configuration
msgid "Settings"
msgstr "ตั้งค่า"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__show_effect
msgid "Show Effect"
msgstr "แสดงผลเอฟเฟค"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Synchronize your calendar with Google Calendar"
msgstr "ซิงโครไนซ์ปฏิทินของคุณกับ Google ปฏิทิน"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Synchronize your calendar with Outlook"
msgstr "ซิงโครไนซ์ปฏิทินของคุณกับ Outlook"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_base_gengo
msgid "Translate Your Website with Gengo"
msgstr "Translate Your Website with Gengo"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_web_unsplash
msgid "Unsplash Image Library"
msgstr "รูปภาพ"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Update Info"
msgstr "อัพเดตข้อมูล"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Use LDAP credentials to log in"
msgstr "ใช้ข้อมูลรับรอง LDAP เพื่อเข้าสู่ระบบ"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Use external accounts to log in (Google, Facebook, etc.)"
msgstr "ใช้บัญชีภายนอกเพื่อเข้าสู่ระบบ (Google, Facebook ฯลฯ )"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_auth_oauth
msgid "Use external authentication providers (OAuth)"
msgstr "ใช้ผู้ให้บริการการพิสูจน์ตัวตนภายนอก (OAuth)"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Use external pads in Odoo Notes"
msgstr "ใช้บันทึกภายนอกใน Odoo Notes"

#. module: base_setup
#: model:ir.model,name:base_setup.model_res_users
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Users"
msgstr "ผู้ใช้งาน"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"When populating your address book, Odoo provides a list of matching "
"companies. When selecting one item, the company data and logo are auto-"
"filled."
msgstr ""
"เมื่อใส่สมุดที่อยู่ของคุณ Odoo จะให้รายชื่อ บริษัท ที่ตรงกัน "
"เมื่อเลือกหนึ่งรายการข้อมูล บริษัท และโลโก้จะถูกเติมอัตโนมัติ"

#. module: base_setup
#. openerp-web
#: code:addons/base_setup/static/src/xml/res_config_invite_users.xml:0
#, python-format
msgid "more"
msgstr "เพิ่มเติม"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_google_recaptcha
msgid "reCAPTCHA"
msgstr "reCAPTCHA"
