# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sale_coupon
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON>le <<EMAIL>>, 2018
# <PERSON>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 07:14+0000\n"
"PO-Revision-Date: 2018-10-08 07:14+0000\n"
"Last-Translator: <PERSON>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_coupon_reward.py:75
#, python-format
msgid "%s %s discount on total amount"
msgstr ""

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_coupon_reward.py:70
#, python-format
msgid "%s%% discount on %s"
msgstr ""

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_coupon_reward.py:72
#, python-format
msgid "%s%% discount on cheapest product"
msgstr ""

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_coupon_reward.py:68
#, python-format
msgid "%s%% discount on total amount"
msgstr ""

#. module: sale_coupon
#: model:product.product,name:sale_coupon.product_product_10_percent_discount
#: model:product.template,name:sale_coupon.product_product_10_percent_discount_product_template
msgid "10.0% discount on total amount"
msgstr ""

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.report_coupon
msgid "<em>CODE :</em>"
msgstr ""

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_form
msgid ""
"<span class=\"o_form_label oe_inline\"> Days</span> <span "
"class=\"oe_grey\">if 0, infinite use</span>"
msgstr ""

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_promo_program_form
msgid ""
"<span class=\"oe_grey\">\n"
"                        <b>Apply on Current Order -</b> Reward will be applied on current order.<br/>\n"
"                        <b>Apply on Next Order -</b> Generate a coupon for a next order.\n"
"                    </span>"
msgstr ""

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_form_common
msgid "<span class=\"oe_grey\">if 0, no limit</span>"
msgstr ""

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_promo_program_form
msgid ""
"<span> Orders</span>\n"
"                    <span class=\"oe_grey\"> if 0, infinite use</span>"
msgstr ""

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.view_sale_coupon_program_kanban
msgid "<strong>Active</strong>"
msgstr ""

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.view_sale_coupon_program_kanban
msgid "<strong>Coupons</strong>"
msgstr ""

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.view_sale_coupon_program_kanban
msgid "<strong>Sales</strong>"
msgstr ""

#. module: sale_coupon
#: model:mail.template,body_html:sale_coupon.mail_template_sale_coupon
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"    <tr><td valign=\"top\" style=\"text-align: center; font-size: 14px;\">\n"
"        % if object.partner_id.name:\n"
"        Congratulations ${object.partner_id.name},<br/>\n"
"        % endif\n"
"\n"
"        Here is your reward from ${object.program_id.company_id.name}.<br/>\n"
"\n"
"        % if object.program_id.reward_type == 'discount':\n"
"            % if object.program_id.discount_type == 'fixed_amount':\n"
"            <span style=\"font-size: 50px; color: #875A7B; font-weight: bold;\">\n"
"                ${'%s' % format_amount(object.program_id.discount_fixed_amount, object.program_id.currency_id)}\n"
"            </span><br/>\n"
"            <strong style=\"font-size: 24px;\">off on your next order</strong><br/>\n"
"            %else\n"
"            <span style=\"font-size: 50px; color: #875A7B; font-weight: bold;\">\n"
"                ${object.program_id.discount_percentage} %\n"
"            </span>\n"
"            % if object.program_id.discount_apply_on == 'specific_product':\n"
"            <br/><strong style=\"font-size: 24px;\">\n"
"                ${'on %s' % object.program_id.discount_specific_product_id.name}\n"
"            </strong>\n"
"            % elif object.program_id.discount_apply_on == 'cheapest_product':\n"
"            <br/><strong style=\"font-size: 24px;\">\n"
"                off on the cheapest product\n"
"            </strong>\n"
"            % else\n"
"            <br/><strong style=\"font-size: 24px;\">\n"
"                off on your next order\n"
"            </strong>\n"
"            % endif\n"
"            <br/>\n"
"            % endif\n"
"        % elif object.program_id.reward_type == 'product':\n"
"            <span style=\"font-size: 36px; color: #875A7B; font-weight: bold;\">\n"
"                ${'get %s free %s' % (object.program_id.reward_product_quantity, object.program_id.reward_product_id.name)}\n"
"            </span><br/>\n"
"            <strong style=\"font-size: 24px;\">on your next order</strong><br/>\n"
"        % elif object.program_id.reward_type == 'free_shipping':\n"
"            <span style=\"font-size: 36px; color: #875A7B; font-weight: bold;\">\n"
"                get free shipping\n"
"            </span><br/>\n"
"            <strong style=\"font-size: 24px;\">on your next order</strong><br/>\n"
"        % endif\n"
"    </td></tr>\n"
"    <tr style=\"margin-top: 16px\"><td valign=\"top\" style=\"text-align: center; font-size: 14px;\">\n"
"        Use this promo code\n"
"        % if object.expiration_date:\n"
"            before ${object.expiration_date}\n"
"        % endif\n"
"        <p style=\"margin-top: 16px;\">\n"
"            <strong style=\"padding: 16px 8px 16px 8px; border-radius: 3px; background-color: #F1F1F1;\">\n"
"                ${object.code}\n"
"            </strong>\n"
"        </p>\n"
"        % if object.program_id.rule_min_quantity not in [0, 1]\n"
"        <span style=\"font-size: 14px;\">\n"
"            Minimum purchase of ${object.program_id.rule_min_quantity} products\n"
"        </span><br/>\n"
"        % endif\n"
"        % if object.program_id.rule_minimum_amount != 0.00\n"
"        <span style=\"font-size: 14px;\">\n"
"            Valid for purchase above ${object.program_id.company_id.currency_id.symbol}${'%0.2f' % object.program_id.rule_minimum_amount |float}\n"
"        </span><br/>\n"
"        % endif\n"
"        <br/>\n"
"        Thank you,\n"
"        <br/>\n"
"        % if object.order_id.user_id:\n"
"            ${object.order_id.user_id.signature | safe}\n"
"        % endif\n"
"    </td></tr>\n"
"</tbody></table>\n"
"            "
msgstr ""

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_coupon.py:69
#, python-format
msgid "A Coupon is already applied for the same reward"
msgstr ""

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_coupon.py:63
#: code:addons/sale_coupon/models/sale_coupon_program.py:155
#, python-format
msgid "A minimum of %s %s should be purchased to get the reward"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_program__active
msgid "A program is available for the customers when active"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_program__promo_code
msgid ""
"A promotion code is a code that is associated with a marketing discount. For"
" example, a retailer might tell frequent customers to enter the promotion "
"code 'THX001' to receive a 10%% discount on their whole order."
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_program__program_type
msgid ""
"A promotional program can be either a limited promotional offer without code (applied automatically)\n"
"                or with a code (displayed on a magazine for example) that may generate a discount on the current\n"
"                order or create a coupon for a next order.\n"
"\n"
"                A coupon program generates coupons with a code that can be used to generate a discount on the current\n"
"                order or create a coupon for a next order."
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__active
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_promo_program_search
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_search
msgid "Active"
msgstr "Aktivan"

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__promo_applicability
msgid "Applicability"
msgstr "Primjenjivost"

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_order__applied_coupon_ids
msgid "Applied Coupons"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_order__no_code_promo_program_ids
msgid "Applied Immediate Promo Programs"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_order__code_promo_program_id
msgid "Applied Promo Program"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon__sales_order_id
msgid "Applied on order"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,help:sale_coupon.field_sale_order__promo_code
msgid "Applied program code"
msgstr ""

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_apply_code_view_form
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_order_view_form
msgid "Apply Coupon"
msgstr ""

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_form_common
msgid "Apply Discount"
msgstr "Primjeni popust"

#. module: sale_coupon
#: selection:sale.coupon.program,promo_applicability:0
msgid "Apply On Current Order"
msgstr ""

#. module: sale_coupon
#: selection:sale.coupon.program,promo_applicability:0
msgid "Apply On Next Order"
msgstr ""

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_apply_code_view_form
msgid "Apply coupon"
msgstr ""

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_promo_program_form
msgid "Apply on First"
msgstr ""

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_coupon.py:81
#: code:addons/sale_coupon/models/sale_coupon_program.py:176
#, python-format
msgid "At least one of the required conditions is not met to get the reward!"
msgstr ""

#. module: sale_coupon
#: selection:sale.coupon.program,promo_code_usage:0
msgid "Automatically Applied"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_program__promo_code_usage
msgid ""
"Automatically Applied - No code is required, if the program rules are met, the reward is applied (Except the global discount or the free shipping rewards which are not cumulative)\n"
"Use a code - If the program rules are met, a valid code is mandatory for the reward to be applied\n"
msgstr ""

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.report_coupon
msgid "Barcode"
msgstr "Barkod"

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__rule_partners_domain
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_rule__rule_partners_domain
msgid "Based on Customers"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__rule_products_domain
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_rule__rule_products_domain
msgid "Based on Products"
msgstr ""

#. module: sale_coupon
#: model_terms:ir.actions.act_window,help:sale_coupon.sale_coupon_program_action_promo_program
msgid ""
"Build up promotion programs to attract more customers with discounts, free products, free delivery, etc.\n"
"                You can share promotion codes or grant the promotions automatically if some conditions are met."
msgstr ""

#. module: sale_coupon
#: model:sale.coupon.program,name:sale_coupon.3_ipad_plus_1_free
msgid "Buy 3 large cabinets, get one for free"
msgstr ""

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_apply_code_view_form
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_generate_view_form
msgid "Cancel"
msgstr "Otkaži"

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_promo_program_search
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_search
msgid "Closed"
msgstr "Zatvoreno"

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_promo_program_search
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_search
msgid "Closed Programs"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon__code
msgid "Code"
msgstr "Šifra"

#. module: sale_coupon
#: model:sale.coupon.program,name:sale_coupon.10_percent_auto_applied
msgid "Code for 10% on orders"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__company_id
msgid "Company"
msgstr "Kompanija"

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_coupon.py:101
#, python-format
msgid "Compose Email"
msgstr "Sastavi e-poštu"

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_form_common
msgid "Conditions"
msgstr "Uslovi"

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.report_coupon
msgid "Congratulations"
msgstr "Čestitamo"

#. module: sale_coupon
#: selection:sale.coupon,state:0
msgid "Consumed"
msgstr "Utrošeno"

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_apply_code__coupon_code
msgid "Coupon"
msgstr ""

#. module: sale_coupon
#: model:ir.actions.report,name:sale_coupon.report_coupon_code
msgid "Coupon Code"
msgstr "Šifra kupona"

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__coupon_count
msgid "Coupon Count"
msgstr ""

#. module: sale_coupon
#: model:ir.actions.act_window,name:sale_coupon.sale_coupon_program_action_coupon_program
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_form_common
#: selection:sale.coupon.program,program_type:0
msgid "Coupon Program"
msgstr ""

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_form
msgid "Coupon Program Name..."
msgstr ""

#. module: sale_coupon
#: model:ir.ui.menu,name:sale_coupon.menu_coupon_type_config
#: model_terms:ir.ui.view,arch_db:sale_coupon.res_config_settings_view_form
msgid "Coupon Programs"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__rule_id
msgid "Coupon Rule"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_program__rule_date_to
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_rule__rule_date_to
msgid "Coupon program end date"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_program__rule_date_from
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_rule__rule_date_from
msgid "Coupon program start date"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_program__sequence
msgid ""
"Coupon program will be applied based on given sequence if multiple programs "
"are defined on same condition(For minimum amount)"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_program__rule_partners_domain
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_rule__rule_partners_domain
msgid "Coupon program will work for selected customers only"
msgstr ""

#. module: sale_coupon
#: model:ir.actions.act_window,name:sale_coupon.sale_coupon_action
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_form
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_promo_program_form
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_view_form
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_view_tree
msgid "Coupons"
msgstr ""

#. module: sale_coupon
#: model_terms:ir.actions.act_window,help:sale_coupon.sale_coupon_program_action_coupon_program
msgid "Create a new coupon program"
msgstr ""

#. module: sale_coupon
#: model_terms:ir.actions.act_window,help:sale_coupon.sale_coupon_program_action_promo_program
msgid "Create a new promotion program"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon__create_uid
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_apply_code__create_uid
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_generate__create_uid
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__create_uid
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_reward__create_uid
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_rule__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon__create_date
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_apply_code__create_date
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_generate__create_date
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__create_date
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_reward__create_date
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_rule__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_generate__partners_domain
msgid "Customer"
msgstr "Kupac"

#. module: sale_coupon
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_program__reward_product_uom_id
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_reward__reward_product_uom_id
msgid "Default unit of measure used for all stock operations."
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__discount_percentage
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_reward__discount_percentage
#: selection:sale.coupon.reward,reward_type:0
msgid "Discount"
msgstr "Popust"

#. module: sale_coupon
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_program__reward_type
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_reward__reward_type
msgid ""
"Discount - Reward will be provided as discount.\n"
"Free Product - Free product will be provide as reward \n"
"Free Shipping - Free shipping will be provided as reward (Need delivery module)"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__discount_apply_on
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_reward__discount_apply_on
msgid "Discount Apply On"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__discount_max_amount
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_reward__discount_max_amount
msgid "Discount Max Amount"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__discount_type
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_reward__discount_type
msgid "Discount Type"
msgstr ""

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_coupon_reward.py:52
#, python-format
msgid "Discount percentage should be between 1-100"
msgstr ""

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_order.py:132
#: code:addons/sale_coupon/models/sale_order.py:151
#: code:addons/sale_coupon/models/sale_order.py:184
#, python-format
msgid "Discount: "
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_report_sale_coupon_report_coupon__display_name
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon__display_name
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_apply_code__display_name
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_generate__display_name
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__display_name
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_reward__display_name
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_rule__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__rule_date_to
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_rule__rule_date_to
msgid "End Date"
msgstr "Datum Završetka"

#. module: sale_coupon
#: model:ir.actions.act_window,name:sale_coupon.sale_coupon_apply_code_action
msgid "Enter Coupon Code"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon__expiration_date
msgid "Expiration Date"
msgstr "Datum isteka roka"

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_search
#: selection:sale.coupon,state:0
msgid "Expired"
msgstr "Istekao"

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_search
msgid "Expired Programs"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__discount_fixed_amount
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_reward__discount_fixed_amount
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_form_common
#: selection:sale.coupon.reward,discount_type:0
msgid "Fixed Amount"
msgstr "Fiksni iznos"

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon__partner_id
msgid "For Customer"
msgstr ""

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_order.py:102
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__reward_product_id
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_reward__reward_product_id
#: selection:sale.coupon.reward,reward_type:0
#, python-format
msgid "Free Product"
msgstr ""

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_coupon_reward.py:63
#, python-format
msgid "Free Product - %s"
msgstr ""

#. module: sale_coupon
#: model:product.product,name:sale_coupon.product_product_free_large_cabinet
#: model:product.template,name:sale_coupon.product_product_free_large_cabinet_product_template
msgid "Free Product - Large Cabinet"
msgstr ""

#. module: sale_coupon
#: selection:sale.coupon.reward,reward_type:0
msgid "Free Shipping"
msgstr ""

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_generate_view_form
msgid "Generate"
msgstr "Generiraj"

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_form
msgid "Generate Coupon"
msgstr ""

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_generate_view_form
msgid "Generate Coupons"
msgstr ""

#. module: sale_coupon
#: model_terms:ir.actions.act_window,help:sale_coupon.sale_coupon_program_action_coupon_program
msgid ""
"Generate and share coupon codes with your customers to get discounts or free"
" products."
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__coupon_ids
msgid "Generated Coupons"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_generate__generation_type
msgid "Generation Type"
msgstr ""

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_coupon.py:71
#, python-format
msgid "Global discounts are not cumulable."
msgstr ""

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_coupon_program.py:167
#, python-format
msgid "Global discounts are not cumulative."
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_report_sale_coupon_report_coupon__id
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon__id
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_apply_code__id
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_generate__id
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__id
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_reward__id
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_rule__id
msgid "ID"
msgstr "ID"

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_coupon.py:67
#, python-format
msgid "Invalid partner."
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_order_line__is_reward_line
msgid "Is a program reward line"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_report_sale_coupon_report_coupon____last_update
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon____last_update
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_apply_code____last_update
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_generate____last_update
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program____last_update
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_reward____last_update
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_rule____last_update
msgid "Last Modified on"
msgstr "Zadnje mijenjano"

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon__write_uid
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_apply_code__write_uid
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_generate__write_uid
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__write_uid
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_reward__write_uid
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_rule__write_uid
msgid "Last Updated by"
msgstr "Zadnji ažurirao"

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon__write_date
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_apply_code__write_date
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_generate__write_date
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__write_date
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_reward__write_date
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_rule__write_date
msgid "Last Updated on"
msgstr "Zadnje ažurirano"

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.report_coupon
msgid "Logo"
msgstr "Logo"

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_form_common
msgid "Max Discount Amount"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__maximum_use_number
msgid "Maximum Use Number"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_program__discount_max_amount
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_reward__discount_max_amount
msgid "Maximum amount of discount that can be provided"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_program__maximum_use_number
msgid "Maximum number of sales orders in which reward can be provided"
msgstr ""

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_form_common
msgid "Minimum Purchase Of"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__rule_min_quantity
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_rule__rule_min_quantity
msgid "Minimum Quantity"
msgstr "Minimalna količina"

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.report_coupon
msgid "Minimum purchase of"
msgstr ""

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_coupon_rules.py:34
#, python-format
msgid "Minimum purchased amount should be greater than 0"
msgstr ""

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_coupon_rules.py:39
#, python-format
msgid "Minimum quantity should be greater than 0"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_program__rule_minimum_amount
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_rule__rule_minimum_amount
msgid "Minimum required amount to get the reward"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_program__rule_min_quantity
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_rule__rule_min_quantity
msgid "Minimum required product quantity to get the reward"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__name
msgid "Name"
msgstr "Naziv:"

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_generate__nbr_coupons
#: selection:sale.coupon.generate,generation_type:0
msgid "Number of Coupons"
msgstr ""

#. module: sale_coupon
#: model:ir.actions.act_window,name:sale_coupon.sale_coupon_generate_action
msgid "Number of Coupons To Generate"
msgstr ""

#. module: sale_coupon
#: selection:sale.coupon.generate,generation_type:0
msgid "Number of Selected Customers"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_generate__nbr_coupons
msgid "Number of coupons"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_order__generated_coupon_ids
msgid "Offered Coupons"
msgstr ""

#. module: sale_coupon
#: selection:sale.coupon.reward,discount_apply_on:0
msgid "On Cheapest Product"
msgstr ""

#. module: sale_coupon
#: selection:sale.coupon.reward,discount_apply_on:0
msgid "On Order"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_program__discount_apply_on
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_reward__discount_apply_on
msgid ""
"On Order - Discount on whole order\n"
"Cheapest product - Discount on cheapest product of the order\n"
"Specific product - Discount on selected specific product"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_program__rule_products_domain
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_rule__rule_products_domain
msgid "On Purchase of selected product, reward will be given"
msgstr ""

#. module: sale_coupon
#: selection:sale.coupon.reward,discount_apply_on:0
msgid "On Specific Product"
msgstr ""

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_order.py:179
#, python-format
msgid "On product with following tax: "
msgstr ""

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_order.py:181
#, python-format
msgid "On product with following taxes: "
msgstr ""

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_promo_program_search
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_search
msgid "Opened Programs"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__order_count
msgid "Order Count"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__order_line_ids
msgid "Order Line"
msgstr "Stavka narudžbe"

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon__order_id
msgid "Order Reference"
msgstr "Referenca naloga"

#. module: sale_coupon
#: selection:sale.coupon.reward,discount_type:0
msgid "Percentage"
msgstr "Procenat"

#. module: sale_coupon
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_program__discount_type
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_reward__discount_type
msgid ""
"Percentage - Entered percentage discount will be provided\n"
"Amount - Entered fixed amount discount will be provided"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__discount_specific_product_id
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_reward__discount_specific_product_id
msgid "Product"
msgstr "Proizvod"

#. module: sale_coupon
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_program__discount_specific_product_id
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_reward__discount_specific_product_id
msgid ""
"Product that will be discounted if the discount is applied on a specific "
"product"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon__discount_line_product_id
msgid "Product used in the sales order to apply the discount."
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_program__discount_line_product_id
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_reward__discount_line_product_id
msgid ""
"Product used in the sales order to apply the discount. Each coupon program "
"has its own reward product for reporting purpose"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon__program_id
msgid "Program"
msgstr ""

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_form_common
msgid "Program Name"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__program_type
msgid "Program Type"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__promo_code_usage
msgid "Promo Code Usage"
msgstr ""

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_coupon_program.py:153
#, python-format
msgid "Promo code %s has been expired."
msgstr ""

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_coupon_program.py:163
#, python-format
msgid "Promo code is expired"
msgstr ""

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_coupon_program.py:161
#, python-format
msgid "Promo code is invalid"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__promo_code
#: model:ir.model.fields,field_description:sale_coupon.field_sale_order__promo_code
msgid "Promotion Code"
msgstr ""

#. module: sale_coupon
#: model:ir.actions.act_window,name:sale_coupon.sale_coupon_program_action_promo_program
msgid "Promotion Program"
msgstr ""

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_promo_program_form
msgid "Promotion Program Name..."
msgstr ""

#. module: sale_coupon
#: model:ir.ui.menu,name:sale_coupon.menu_promotion_type_config
#: model_terms:ir.ui.view,arch_db:sale_coupon.res_config_settings_view_form
msgid "Promotion Programs"
msgstr ""

#. module: sale_coupon
#: selection:sale.coupon.program,program_type:0
msgid "Promotional Program"
msgstr ""

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_coupon_program.py:165
#, python-format
msgid "Promotionals codes are not cumulative."
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__reward_product_quantity
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_reward__reward_product_quantity
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_form_common
msgid "Quantity"
msgstr "Količina"

#. module: sale_coupon
#: selection:sale.coupon,state:0
msgid "Reserved"
msgstr "Rezervisano"

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__reward_id
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_form_common
msgid "Reward"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_order__reward_amount
msgid "Reward Amount"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__reward_description
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_reward__reward_description
msgid "Reward Description"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon__discount_line_product_id
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__discount_line_product_id
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_reward__discount_line_product_id
msgid "Reward Line Product"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_program__reward_product_id
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_reward__reward_product_id
msgid "Reward Product"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__reward_type
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_reward__reward_type
msgid "Reward Type"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_program__reward_product_quantity
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_reward__reward_product_quantity
msgid "Reward product quantity"
msgstr ""

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_form_common
msgid "Rewards"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__rule_minimum_amount
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_rule__rule_minimum_amount
msgid "Rule Minimum Amount"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__rule_minimum_amount_tax_inclusion
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_rule__rule_minimum_amount_tax_inclusion
msgid "Rule Minimum Amount Tax Inclusion"
msgstr ""

#. module: sale_coupon
#: model:ir.model,name:sale_coupon.model_sale_order
msgid "Sale Order"
msgstr "Prodajni nalog"

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_form_common
msgid "Sales"
msgstr "Prodaja"

#. module: sale_coupon
#: model:ir.model,name:sale_coupon.model_sale_coupon
msgid "Sales Coupon"
msgstr ""

#. module: sale_coupon
#: model:ir.model,name:sale_coupon.model_sale_coupon_apply_code
msgid "Sales Coupon Apply Code"
msgstr ""

#. module: sale_coupon
#: model:ir.model,name:sale_coupon.model_sale_coupon_generate
msgid "Sales Coupon Generate"
msgstr ""

#. module: sale_coupon
#: model:ir.model,name:sale_coupon.model_sale_coupon_program
msgid "Sales Coupon Program"
msgstr ""

#. module: sale_coupon
#: model:ir.model,name:sale_coupon.model_report_sale_coupon_report_coupon
msgid "Sales Coupon Report"
msgstr ""

#. module: sale_coupon
#: model:ir.model,name:sale_coupon.model_sale_coupon_reward
msgid "Sales Coupon Reward"
msgstr ""

#. module: sale_coupon
#: model:ir.model,name:sale_coupon.model_sale_coupon_rule
msgid "Sales Coupon Rule"
msgstr ""

#. module: sale_coupon
#: model:ir.actions.act_window,name:sale_coupon.sale_order_action
msgid "Sales Order"
msgstr "Prodajna narudžba"

#. module: sale_coupon
#: model:ir.model,name:sale_coupon.model_sale_order_line
msgid "Sales Order Line"
msgstr "Stavka prodajne narudžbe"

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_coupon_program.py:135
#, python-format
msgid "Sales Orders"
msgstr "Prodajni nalozi"

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_form_common
msgid "Select company"
msgstr ""

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_promo_program_form
msgid "Select customer"
msgstr ""

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_form_common
msgid "Select product"
msgstr ""

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_form_common
msgid "Select reward product"
msgstr ""

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_view_form
msgid "Send by Email"
msgstr "Pošalji email-om"

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__sequence
msgid "Sequence"
msgstr "Sekvenca"

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__rule_date_from
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_rule__rule_date_from
msgid "Start Date"
msgstr "Datum početka"

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon__state
msgid "State"
msgstr "Rep./Fed."

#. module: sale_coupon
#: selection:sale.coupon.rule,rule_minimum_amount_tax_inclusion:0
msgid "Tax Excluded"
msgstr ""

#. module: sale_coupon
#: selection:sale.coupon.rule,rule_minimum_amount_tax_inclusion:0
msgid "Tax Included"
msgstr ""

#. module: sale_coupon
#: code:addons/sale_coupon/wizard/sale_coupon_apply_code.py:54
#, python-format
msgid "The code %s is invalid"
msgstr ""

#. module: sale_coupon
#: sql_constraint:sale.coupon:0
msgid "The coupon code must be unique!"
msgstr ""

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_coupon.py:65
#, python-format
msgid "The coupon program for %s is in draft or closed state"
msgstr ""

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_coupon.py:75
#: code:addons/sale_coupon/models/sale_coupon_program.py:171
#, python-format
msgid "The customer doesn't have access to this reward."
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_program__discount_fixed_amount
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_reward__discount_fixed_amount
msgid "The discount in fixed amount"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_program__discount_percentage
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_reward__discount_percentage
msgid "The discount in percentage, between 1 to 100"
msgstr ""

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_coupon_program.py:62
#, python-format
msgid "The program code must be unique!"
msgstr ""

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_coupon_program.py:157
#, python-format
msgid "The promo code is already applied on this order"
msgstr ""

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_coupon_program.py:159
#, python-format
msgid "The promotional offer is already applied on this order"
msgstr ""

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_coupon.py:73
#: code:addons/sale_coupon/models/sale_coupon_program.py:169
#, python-format
msgid ""
"The reward products should be in the sales order lines to apply the "
"discount."
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon__order_id
msgid "The sales order from which coupon is generated"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon__sales_order_id
msgid "The sales order on which the coupon is applied"
msgstr ""

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_coupon_rules.py:29
#, python-format
msgid "The start date must be before the end date"
msgstr ""

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_coupon.py:60
#, python-format
msgid "This coupon %s exists but the origin sales order is not validated yet."
msgstr ""

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_coupon.py:58
#, python-format
msgid "This coupon %s has been used or is expired."
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__reward_product_uom_id
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_reward__reward_product_uom_id
msgid "Unit of Measure"
msgstr "Jedinica mjere"

#. module: sale_coupon
#: model:product.product,uom_name:sale_coupon.product_product_10_percent_discount
#: model:product.product,uom_name:sale_coupon.product_product_free_large_cabinet
#: model:product.template,uom_name:sale_coupon.product_product_10_percent_discount_product_template
#: model:product.template,uom_name:sale_coupon.product_product_free_large_cabinet_product_template
msgid "Unit(s)"
msgstr "kom (komad)"

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_order_view_form
msgid "Update Promotions"
msgstr ""

#. module: sale_coupon
#: selection:sale.coupon.program,promo_code_usage:0
msgid "Use a code"
msgstr ""

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.report_coupon
msgid "Use this promo code before"
msgstr ""

#. module: sale_coupon
#: selection:sale.coupon,state:0
msgid "Valid"
msgstr ""

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.report_coupon
msgid "Valid for purchase above"
msgstr ""

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_form_common
msgid "Validity"
msgstr "Validnost"

#. module: sale_coupon
#: model:ir.model.fields,field_description:sale_coupon.field_sale_coupon_program__validity_duration
#: model_terms:ir.ui.view,arch_db:sale_coupon.sale_coupon_program_view_form
msgid "Validity Duration"
msgstr ""

#. module: sale_coupon
#: model:ir.model.fields,help:sale_coupon.field_sale_coupon_program__validity_duration
msgid "Validity duration for a coupon after its generation"
msgstr ""

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_coupon_program.py:121
#, python-format
msgid "You can not delete a program in active state"
msgstr ""

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_coupon.py:78
#, python-format
msgid ""
"You don't have the required product quantities on your sales order. All the "
"products should be recorded on the sales order. (Example: You need to have 3"
" T-shirts on your sales order if the promotion is 'Buy 2, Get 1 Free')."
msgstr ""

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_coupon_program.py:173
#, python-format
msgid ""
"You don't have the required product quantities on your sales order. If the "
"reward is same product quantity, please make sure that all the products are "
"recorded on the sales order (Example: You need to have 3 T-shirts on your "
"sales order if the promotion is 'Buy 2, Get 1 Free'."
msgstr ""

#. module: sale_coupon
#: model:mail.template,report_name:sale_coupon.mail_template_sale_coupon
msgid "Your Coupon Code"
msgstr ""

#. module: sale_coupon
#: model:mail.template,subject:sale_coupon.mail_template_sale_coupon
msgid "Your reward coupon from ${object.program_id.company_id.name} "
msgstr ""

#. module: sale_coupon
#: model:product.product,weight_uom_name:sale_coupon.product_product_10_percent_discount
#: model:product.product,weight_uom_name:sale_coupon.product_product_free_large_cabinet
#: model:product.template,weight_uom_name:sale_coupon.product_product_10_percent_discount_product_template
#: model:product.template,weight_uom_name:sale_coupon.product_product_free_large_cabinet_product_template
msgid "kg"
msgstr "kg"

#. module: sale_coupon
#: code:addons/sale_coupon/models/sale_order.py:202
#, python-format
msgid "limited to "
msgstr ""

#. module: sale_coupon
#: model_terms:ir.ui.view,arch_db:sale_coupon.report_coupon
msgid "products"
msgstr ""
