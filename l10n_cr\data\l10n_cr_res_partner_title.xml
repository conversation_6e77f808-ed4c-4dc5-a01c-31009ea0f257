<?xml version="1.0" encoding="utf-8"?>
<odoo>
        <!--
        Resource: res.partner.title
        Update partner titles
        -->
        <record id="res_partner_title_pvt_ltd" model="res.partner.title">
            <field name="name">Corporation</field>
            <field name="shortcut">Corp.</field>
        </record>
        <record id="res_partner_title_ltd" model="res.partner.title">
            <field name="name">Limited Company</field>
            <field name="shortcut">Ltd.</field>
        </record>
        <record id="res_partner_title_sal" model="res.partner.title">
            <field name="name">Sociedad An&#243;nima Laboral</field>
            <field name="shortcut">S.A.L.</field>
        </record>
        <record id="res_partner_title_asoc" model="res.partner.title">
            <field name="name">Asociation</field>
            <field name="shortcut">Asoc.</field>
        </record>
        <record id="res_partner_title_gov" model="res.partner.title">
            <field name="name">Government</field>
            <field name="shortcut">Gov.</field>
        </record>
        <record id="res_partner_title_edu" model="res.partner.title">
            <field name="name">Educational Institution</field>
            <field name="shortcut">Edu.</field>
        </record>
        <record id="res_partner_title_indprof" model="res.partner.title">
            <field name="name">Independant Professional</field>
            <field name="shortcut">Ind. Prof.</field>
        </record>
        <record id="res_partner_title_dra" model="res.partner.title">
            <field name="name">Doctora</field>
            <field name="shortcut">Dra.</field>
        </record>
        <record id="res_partner_title_msc" model="res.partner.title">
            <field name="name">Msc.</field>
            <field name="shortcut">Msc.</field>
        </record>
        <record id="res_partner_title_mba" model="res.partner.title">
            <field name="name">MBA</field>
            <field name="shortcut">MBA</field>
        </record>
        <record id="res_partner_title_lic" model="res.partner.title">
            <field name="name">Licenciado</field>
            <field name="shortcut">Lic.</field>
        </record>
        <record id="res_partner_title_licda" model="res.partner.title">
            <field name="name">Licenciada</field>
            <field name="shortcut">Licda.</field>
        </record>
        <record id="res_partner_title_ing" model="res.partner.title">
            <field name="name">Ingeniero/a</field>
            <field name="shortcut">Ing.</field>
        </record>
</odoo>
