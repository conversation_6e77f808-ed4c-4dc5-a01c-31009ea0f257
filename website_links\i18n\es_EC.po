# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_links
#
# Translators:
# <PERSON> <<EMAIL>>, 2015-2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2016-02-16 06:55+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Ecuador) (http://www.transifex.com/odoo/odoo-9/"
"language/es_EC/)\n"
"Language: es_EC\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.share_page_menu
msgid "<span title=\"Track this page to count clicks\">Share this Page</span>"
msgstr ""
"<span title=\"Seguimiento de esta página para contar clics\">Compartir esta "
"página</span>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Campaign</strong>"
msgstr "<strong>Campaña</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Medium</strong>"
msgstr "<strong>Medio</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Original URL</strong>"
msgstr "<strong>URL Original</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Redirected URL</strong>"
msgstr "<strong>URL Redirigida</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Source</strong>"
msgstr "<strong>Origen</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Tracked Link</strong>"
msgstr "<strong>Vínculo rastreado</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "All Time"
msgstr "Todo el tiempo"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Campaign <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-"
"placement=\"top\" title=\"Defines the context of your link. It might be an "
"event you want to promote or a special promotion.\"/>"
msgstr ""
"Campaña <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-"
"placement=\"top\" title=\"Define el contexto de su vínculo. Puede ser un "
"evento o una promoción especial que desee promocionar.\"/>"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:37
#, python-format
msgid "Copy"
msgstr "Copiar"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:350
#, python-format
msgid "Generating link..."
msgstr "Generando vínculo..."

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Get tracked link"
msgstr "Obtener vínculo rastreado"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "Last Month"
msgstr "Último Mes"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "Last Week"
msgstr "Última Semana"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "Link Tracker"
msgstr "Campañas"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Medium <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-placement="
"\"top\" title=\"Defines the medium used to share your link. It might be an "
"email, or a Facebook Ads for instance.\"/>"
msgstr ""
"Medio <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-placement="
"\"top\" title=\"Define el medio utilizado para compartir su vínculo. Puede "
"ser que sea un correo electrónico o  anuncios de Facebook, por ejemplo..\"/>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Most Clicked"
msgstr "Más cliqueado"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Newest"
msgstr "Nuevo"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Recently Used"
msgstr "Recientemente usado"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Share this page with a <strong>short link</strong> that includes "
"<strong>analytics trackers</strong>."
msgstr ""
"Comparte esta página con un <strong>vínculo corto</strong> que incluye "
"<strong> análisis de rastreo </strong>."

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Source <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-placement="
"\"top\" title=\"Defines the source from which your traffic will come from, "
"Facebook or Twitter for instance.\"/>"
msgstr ""
"Origen <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-placement="
"\"top\" title=\"Define el origen de su tráfico, por ejemplo, si provienen de "
"Facebook o Twitter.\"/>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "Statistics"
msgstr "Estadísticas"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:38
#, python-format
msgid "Stats"
msgstr "Estadísticas"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
#, fuzzy
msgid ""
"Those trackers can be used in Google Analytics to track clicks and visitors, "
"or in Odoo reports to track opportunities and related revenues."
msgstr ""
"Estos rastreadores pueden utilizarse en Google Analytics para realizar un "
"seguimiento de los clics y visitantes, o en Odoo informes para realizar un "
"seguimiento de oportunidades e ingresos relacionados."

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "URL"
msgstr "URL"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:216
#, python-format
msgid "Unable to get recent links"
msgstr "Imposible obtener los vínculos recientes"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:239
#, python-format
msgid "You don't have any recent links."
msgstr "Usted no tiene vínculos recientes."

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Your tracked links"
msgstr "Sus vínculos rastreados"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:23
#: model_terms:ir.ui.view,arch_db:website_links.graphs
#, python-format
msgid "cancel"
msgstr "cancelar"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:8
#, python-format
msgid "clicks"
msgstr "clics"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "copy"
msgstr "copiar"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:259
#, python-format
msgid "e.g. Newsletter, Social Network, .."
msgstr "ejemplo: Revista, Red Social,..."

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:256
#, python-format
msgid "e.g. Promotion of June, Winter Newsletter, .."
msgstr "ejemplo: Promoción de junio, Revista de Invierno, .."

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:262
#, python-format
msgid "e.g. Search Engine, Website page, .."
msgstr "e.g. Motor de búsqueda, página de sitio web"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "e.g. https://www.odoo.com/page/contactus"
msgstr "ejemplo. https://www.odoo.com/page/contactus"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:22
#: model_terms:ir.ui.view,arch_db:website_links.graphs
#, python-format
msgid "ok"
msgstr "Aceptar"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:22
#: model_terms:ir.ui.view,arch_db:website_links.graphs
#, python-format
msgid "or"
msgstr "o"
