<?xml version="1.0" encoding="utf-8"?>
<odoo>

        <!-- CRM lead search by Salesteams -->
        <record id="crm_case_form_view_salesteams_lead" model="ir.actions.act_window">
            <field name="name">Leads</field>
            <field name="res_model">crm.lead</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="domain">['|', ('type','=','lead'), ('type','=',False)]</field>
            <field name="view_ids"
                   eval="[(5, 0, 0),
                          (0, 0, {'view_mode': 'tree', 'view_id': ref('crm_case_tree_view_leads')}),
                          (0, 0, {'view_mode': 'kanban', 'view_id': ref('view_crm_lead_kanban')})]"/>
            <field name="search_view_id" ref="crm.view_crm_case_leads_filter"/>
            <field name="context">{
                    'search_default_team_id': [active_id],
                    'default_team_id': active_id,
                    'default_type': 'lead',
                }
            </field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a new lead
                </p><p>
                    Use leads if you need a qualification step before creating an
                    opportunity or a customer. It can be a business card you received,
                    a contact form filled in your website, or a file of unqualified
                    prospects you import, etc.
                </p>
            </field>
        </record>

        <!-- CRM opportunity search by Salesteams -->
        <record id="crm_case_form_view_salesteams_opportunity" model="ir.actions.act_window">
            <field name="name">Opportunities</field>
            <field name="res_model">crm.lead</field>
            <field name="view_mode">kanban,tree,graph,form,calendar,pivot</field>
            <field name="domain">[('type','=','opportunity')]</field>
            <field name="view_id" ref="crm.crm_case_kanban_view_leads"/>
            <field name="search_view_id" ref="crm.view_crm_case_opportunities_filter"/>
            <field name="context">{
                    'search_default_team_id': [active_id],
                    'default_team_id': active_id,
                    'default_type': 'opportunity',
                    'default_user_id': uid,
                }
            </field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a new lead
                </p><p>
                    Odoo helps you keep track of your sales pipeline to follow
                    up potential sales and better forecast your future revenues.
                </p><p>
                    You will be able to plan meetings and phone calls from
                    opportunities, convert them into quotations, attach related
                    documents, track all discussions, and much more.
                </p>
            </field>
        </record>

         <record id="crm_lead_action_team_overdue_opportunity" model="ir.actions.act_window">
            <field name="name">Overdue Opportunities</field>
            <field name="res_model">crm.lead</field>
            <field name="view_mode">kanban,tree,graph,form,calendar,pivot</field>
            <field name="domain">[('type','=','opportunity')]</field>
            <field name="view_id" ref="crm.crm_case_kanban_view_leads"/>
            <field name="search_view_id" ref="crm.view_crm_case_opportunities_filter"/>
            <field name="context">{
                    'search_default_team_id': [active_id],
                    'search_default_overdue_opp': 1,
                    'default_team_id': active_id,
                    'default_type': 'opportunity',
                    'default_user_id': uid,
                }
            </field>
        </record>

       <record id="action_report_crm_lead_salesteam" model="ir.actions.act_window">
           <field name="name">Leads Analysis</field>
           <field name="res_model">crm.lead</field>
           <field name="context">{'search_default_team_id': [active_id], 'search_default_filter_create_date': 1}}</field>
           <field name="domain">[]</field>
           <field name="view_mode">graph,pivot,tree,form</field>
           <field name="view_id" ref="crm_lead_view_graph"/>
           <field name="search_view_id" ref="crm.view_crm_case_leads_filter"/>
           <field name="help">Leads Analysis allows you to check different CRM related information like the treatment delays or number of leads per state. You can sort out your leads analysis by different groups to get accurate grained analysis.</field>
       </record>
        <record id="action_report_crm_lead_salesteam_view_graph" model="ir.actions.act_window.view">
            <field name="sequence">2</field>
            <field name="view_mode">graph</field>
            <field name="view_id" ref="crm_lead_view_graph"/>
            <field name="act_window_id" ref="action_report_crm_lead_salesteam"/>
        </record>
        <record id="action_report_crm_lead_salesteam_view_pivot" model="ir.actions.act_window.view">
            <field name="sequence">3</field>
            <field name="view_mode">pivot</field>
            <field name="view_id" ref="crm_lead_view_pivot"/>
            <field name="act_window_id" ref="action_report_crm_lead_salesteam"/>
        </record>
        <record id="action_report_crm_lead_salesteam_view_tree" model="ir.actions.act_window.view">
            <field name="sequence">4</field>
            <field name="view_mode">tree</field>
            <field name="view_id" ref="crm_case_tree_view_leads"/>
            <field name="act_window_id" ref="action_report_crm_lead_salesteam"/>
        </record>

       <record id="action_report_crm_opportunity_salesteam" model="ir.actions.act_window">
            <field name="name">Pipeline Analysis</field>
            <field name="res_model">crm.lead</field>
            <field name="view_mode">graph,pivot,tree,form</field>
            <field name="search_view_id" ref="crm.crm_opportunity_report_view_search"/>
            <field name="context">{
                'search_default_team_id': [active_id],
                'tree_view_ref': 'crm.crm_case_tree_view_oppor',
                'search_default_opportunity': True,
                'search_default_filter_create_date': 1}</field>
            <field name="domain">[]</field>
            <field name="help">Opportunities Analysis gives you an instant access to your opportunities with information such as the expected revenue, planned cost, missed deadlines or the number of interactions per opportunity. This report is mainly used by the sales manager in order to do the periodic review with the channels of the sales pipeline.</field>
        </record>

        <record id="crm_team_view_tree" model="ir.ui.view">
            <field name="name">crm.team.tree.inherit.crm</field>
            <field name="model">crm.team</field>
            <field name="inherit_id" ref="sales_team.crm_team_view_tree"/>
            <field name="arch" type="xml">
                <field name="name" position="after">
                    <field string="Alias" name="alias_id"/>
                </field>
            </field>
        </record>

        <record id="sales_team_form_view_in_crm" model="ir.ui.view">
            <field name="name">crm.team.form.inherit</field>
            <field name="model">crm.team</field>
            <field name="inherit_id" ref="sales_team.crm_team_view_form"/>
            <field name="priority">12</field>
            <field name="arch" type="xml">
                <xpath expr="//sheet" position="before">
                    <header>
                        <button name="action_assign_leads" type="object"
                            string="Assign Leads"
                            class="oe_highlight"
                            confirm="This will assign leads to all members. Do you want to proceed?"
                            attrs="{'invisible': ['|', '&amp;', ('use_leads', '=', False), ('use_opportunities', '=', False), ('assignment_enabled', '=', False)]}"/>
                    </header>
                </xpath>
                <xpath expr="//div[@name='options_active']" position="inside">
                    <div class="o_row">
                        <span name="opportunities">
                            <field name="use_opportunities"/>
                            <label for="use_opportunities"/>
                        </span>
                        <span class="o_row" groups="crm.group_use_lead">
                            <field name="use_leads"/>
                            <label for="use_leads" string="Leads"/>
                        </span>
                    </div>
                </xpath>
                <xpath expr="//field[@name='user_id']" position="after">
                    <label for="alias_name" string="Email Alias"
                        attrs="{'invisible': [('use_leads', '=', False),('use_opportunities', '=', False)]}"/>
                    <div class="oe_inline" name="alias_def"
                        attrs="{'invisible': [('use_leads', '=', False),('use_opportunities', '=', False)]}">
                        <field name="alias_id" class="oe_read_only oe_inline"
                            string="Email Alias" required="0"
                            attrs="{'invisible': [('alias_domain', '=', False)]}"/>
                        <div class="oe_inline" name="edit_alias" style="display: inline;" >
                            <div class="oe_edit_only" attrs="{'invisible': [('alias_domain', '=', False)]}">
                                <field name="alias_name" class="oe_inline"/>@<field name="alias_domain" class="oe_inline" readonly="1"/>
                            </div>
                            <button icon="fa-arrow-right" type="action" name="%(base_setup.action_general_configuration)d" string="Configure a custom domain" class="p-0 btn-link" attrs="{'invisible': [('alias_domain', '!=', False)]}"/>
                        </div>
                    </div>
                    <field name="alias_contact"
                        string="Accept Emails From"
                        attrs="{'invisible': [('use_leads', '=', False), ('use_opportunities', '=', False)]}"/>
                </xpath>
                <xpath expr="//group[@name='right']" position="attributes">
                    <attribute name="string">Assignment Rules</attribute>
                    <attribute name="attrs">{'invisible': [('assignment_enabled', '=', False)]}</attribute>
                </xpath>
                <xpath expr="//group[@name='right']" position="inside">
                    <field name="assignment_enabled" invisible="1"/>
                    <field name="assignment_auto_enabled" invisible="1"/>
                    <field name="assignment_domain" widget="domain" string="Domain"
                        options="{'model': 'crm.lead', 'in_dialog': True}"
                        attrs="{'invisible': [('assignment_enabled', '=', False)]}"/>
                    <label for="lead_all_assigned_month_count" string="Assigned Leads Count"
                        attrs="{'invisible': [('assignment_enabled', '=', False)]}"/>
                    <div attrs="{'invisible': [('assignment_enabled', '=', False)]}">
                        <field name="lead_all_assigned_month_count" class="oe_inline"/> /
                        <field name="assignment_max" class="oe_inline"/>
                    </div>
                    <field name="assignment_optout" attrs="{'invisible': [('assignment_auto_enabled', '=', False)]}"/>
                </xpath>
                <xpath expr="//field[@name='member_ids']" position="attributes">
                    <attribute name="attrs">{'invisible': [('assignment_enabled', '=', True)]}</attribute>
                </xpath>
                <xpath expr="//field[@name='crm_team_member_ids']" position="attributes">
                    <attribute name="attrs">{'invisible': [('assignment_enabled', '=', False)]}</attribute>
                </xpath>
            </field>
        </record>

        <!-- Case Teams Action -->
        <record id="action_crm_tag_kanban_view_salesteams_oppor11" model="ir.actions.act_window.view">
            <field name="sequence" eval="0"/>
            <field name="view_mode">kanban</field>
            <field name="view_id" ref="crm_case_kanban_view_leads"/>
            <field name="act_window_id" ref="crm_case_form_view_salesteams_opportunity"/>
        </record>

        <record id="action_crm_tag_tree_view_salesteams_oppor11" model="ir.actions.act_window.view">
            <field name="sequence" eval="1"/>
            <field name="view_mode">tree</field>
            <field name="view_id" ref="crm_case_tree_view_oppor"/>
            <field name="act_window_id" ref="crm_case_form_view_salesteams_opportunity"/>
        </record>

        <record id="action_opportunity_form" model="ir.actions.act_window">
            <field name="name">New Opportunity</field>
            <field name="res_model">crm.lead</field>
            <field name="type">ir.actions.act_window</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="crm_lead_view_form"/>
            <field name="domain">[('type','=','opportunity')]</field>
            <field name="context">{
                    'search_default_team_id': [active_id],
                    'default_team_id': active_id,
                    'default_type': 'opportunity',
                    'default_user_id': uid,
            }
            </field>
            <field name="search_view_id" ref="crm.view_crm_case_opportunities_filter"/>
        </record>

        <record id="sales_team.crm_team_action_pipeline" model="ir.actions.act_window">
            <field name="domain">[('use_opportunities', '=', True)]</field>
        </record>

        <record id="crm_team_view_kanban_dashboard" model="ir.ui.view">
            <field name="name">crm.team.view.kanban.dashboard.inherit.crm</field>
            <field name="model">crm.team</field>
            <field name="inherit_id" ref="sales_team.crm_team_view_kanban_dashboard"/>
            <field name="arch" type="xml">
                <data>
                    <xpath expr="//templates" position="before">
                        <field name="alias_id"/>
                        <field name="alias_name"/>
                        <field name="alias_domain"/>
                        <field name="use_opportunities"/>
                        <field name="use_leads"/>
                    </xpath>

                    <xpath expr="//div[hasclass('o_primary')]" position="after">
                        <div t-if="record.alias_name.value and record.alias_domain.value">
                            <span t-translation="off"><i class="fa fa-envelope-o" aria-label="Leads" title="Leads" role="img"></i>&amp;nbsp; <field name="alias_id"/></span>
                        </div>
                    </xpath>

                    <xpath expr="//t[@name='first_options']" position="after">
                        <div class="row" t-if="record.lead_unassigned_count.raw_value">
                            <div class="col-8">
                                <a name="%(crm_case_form_view_salesteams_lead)d" type="action" context="{'search_default_unassigned_leads': 1}">
                                    <field name="lead_unassigned_count"/>
                                    <t t-if="record.lead_unassigned_count.raw_value == 1">Unassigned Lead</t>
                                    <t t-else="">Unassigned Leads</t>
                                </a>
                            </div>
                        </div>
                        <div class="row" t-if="record.opportunities_count.raw_value">
                            <div class="col-8">
                                <a name="%(crm_case_form_view_salesteams_opportunity)d" type="action" context="{'search_default_open_opportunities': True}"> <!-- context="{'search_default_probability': NOT or < 100}" -->
                                    <field name="opportunities_count"/>
                                    <t t-if="record.opportunities_count.raw_value == 1">Open Opportunity</t>
                                    <t t-else="">Open Opportunities</t>
                                </a>
                            </div>
                            <div class="col-4 text-right text-truncate">
                                <field name="opportunities_amount" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                            </div>
                        </div>
                        <div class="row" t-if="record.opportunities_overdue_count.raw_value">
                            <div class="col-8">
                                <a name="%(crm_lead_action_team_overdue_opportunity)d" type="action">
                                    <field name="opportunities_overdue_count"/>
                                    <t t-if="record.opportunities_overdue_count.raw_value == 1">Overdue Opportunity</t>
                                    <t t-else="">Overdue Opportunities</t>
                                </a>
                            </div>
                             <div class="col-4 text-right text-truncate">
                                <field name="opportunities_overdue_amount" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                            </div>
                        </div>
                    </xpath>

                    <xpath expr="//div[hasclass('o_kanban_manage_view')]/div[hasclass('o_kanban_card_manage_title')]" position="after">
                        <div t-if="record.use_leads.raw_value" groups="crm.group_use_lead">
                            <a name="%(crm_case_form_view_salesteams_lead)d" type="action">
                                Leads
                            </a>
                        </div>
                        <div t-if="record.use_opportunities.raw_value">
                            <a name="%(crm_case_form_view_salesteams_opportunity)d" type="action">
                                Opportunities
                            </a>
                        </div>
                    </xpath>

                    <xpath expr="//div[hasclass('o_kanban_manage_new')]/div[hasclass('o_kanban_card_manage_title')]" position="after">
                        <div t-if="record.use_leads.raw_value" groups="crm.group_use_lead">
                            <a name="%(crm_lead_action_open_lead_form)d" type="action">
                                Leads
                            </a>
                        </div>
                        <div t-if="record.use_opportunities.raw_value">
                            <a  name="%(action_opportunity_form)d" type="action">
                                Opportunity
                            </a>
                        </div>
                    </xpath>

                    <xpath expr="//div[hasclass('o_kanban_manage_reports')]/div[hasclass('o_kanban_card_manage_title')]" position="after">
                        <div t-if="record.use_leads.raw_value" groups="crm.group_use_lead">
                            <a name="%(action_report_crm_lead_salesteam)d" type="action">
                                Leads
                            </a>
                        </div>
                        <div t-if="record.use_opportunities.raw_value">
                            <a name="%(action_report_crm_opportunity_salesteam)d" type="action">
                                Opportunities
                            </a>
                        </div>
                    </xpath>

                    <xpath expr="//div[hasclass('o_kanban_manage_reports')]/div[@name='o_team_kanban_report_separator']" position="after">
                        <div name="activity_report">
                            <div t-if="record.use_opportunities.raw_value">
                                <a name="%(crm.crm_activity_report_action_team)d" type="action" style="color: #444B5A;">
                                    Activities
                                </a>
                            </div>
                        </div>
                    </xpath>
                </data>
            </field>
        </record>

</odoo>
