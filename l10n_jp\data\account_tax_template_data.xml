<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="tax_in_e" model="account.tax.template">
        <field name="sequence">1</field>
        <field name="chart_template_id" ref="l10n_jp1"/>
        <field name="name">仮受消費税(外) 8%</field>
        <field name="description">仮受消費税(外) 8%</field>
        <field name="amount_type">percent</field>
        <field name="amount">8</field>
        <field name="type_tax_use">sale</field>
        <field name="price_include">False</field>
        <field name="active">True</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_jp.tax_report_comp_basis_sales_taxable_8')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('A21809'),
                'plus_report_line_ids': [ref('l10n_jp.tax_report_to_pay_temp_tx_output_8')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_jp.tax_report_comp_basis_sales_taxable_8')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('A21809'),
                'minus_report_line_ids': [ref('l10n_jp.tax_report_to_pay_temp_tx_output_8')],
            }),
        ]"/>
        <field name="tax_group_id" ref="tax_group_8"/>
    </record>
    <record id="tax_in_e_10" model="account.tax.template">
        <field name="sequence">1</field>
        <field name="chart_template_id" ref="l10n_jp1"/>
        <field name="name">仮受消費税(外) 10%</field>
        <field name="description">仮受消費税(外) 10%</field>
        <field name="amount_type">percent</field>
        <field name="amount">10</field>
        <field name="type_tax_use">sale</field>
        <field name="price_include">False</field>
        <field name="active">True</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_jp.tax_report_comp_basis_sales_taxable_10')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('A21809'),
                'plus_report_line_ids': [ref('l10n_jp.tax_report_to_pay_temp_tx_output_10')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_jp.tax_report_comp_basis_sales_taxable_10')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('A21809'),
                'minus_report_line_ids': [ref('l10n_jp.tax_report_to_pay_temp_tx_output_10')],
            }),
        ]"/>
        <field name="tax_group_id" ref="tax_group_10"/>
    </record>
    <record id="tax_in_i" model="account.tax.template">
        <field name="sequence">1</field>
        <field name="chart_template_id" ref="l10n_jp1"/>
        <field name="name">仮受消費税(内) 8%</field>
        <field name="description">仮受消費税(内) 8%</field>
        <field name="amount_type">percent</field>
        <field name="amount">8</field>
        <field name="type_tax_use">sale</field>
        <field name="price_include">TRUE</field>
        <field name="active">True</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_jp.tax_report_comp_basis_sales_taxable_8')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('A21809'),
                'plus_report_line_ids': [ref('l10n_jp.tax_report_to_pay_temp_tx_output_8')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_jp.tax_report_comp_basis_sales_taxable_8')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('A21809'),
                'minus_report_line_ids': [ref('l10n_jp.tax_report_to_pay_temp_tx_output_8')],
            }),
        ]"/>
        <field name="tax_group_id" ref="tax_group_8"/>
    </record>
    <record id="tax_in_i_10" model="account.tax.template">
        <field name="sequence">1</field>
        <field name="chart_template_id" ref="l10n_jp1"/>
        <field name="name">仮受消費税(内) 10%</field>
        <field name="description">仮受消費税(内) 10%</field>
        <field name="amount_type">percent</field>
        <field name="amount">10</field>
        <field name="type_tax_use">sale</field>
        <field name="price_include">TRUE</field>
        <field name="active">True</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_jp.tax_report_comp_basis_sales_taxable_10')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('A21809'),
                'plus_report_line_ids': [ref('l10n_jp.tax_report_to_pay_temp_tx_output_10')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_jp.tax_report_comp_basis_sales_taxable_10')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('A21809'),
                'minus_report_line_ids': [ref('l10n_jp.tax_report_to_pay_temp_tx_output_10')],
            }),
        ]"/>
        <field name="tax_group_id" ref="tax_group_10"/>
    </record>
    <record id="tax_in_x" model="account.tax.template">
        <field name="sequence">1</field>
        <field name="chart_template_id" ref="l10n_jp1"/>
        <field name="name">輸出免税</field>
        <field name="description">輸出免税</field>
        <field name="amount_type">percent</field>
        <field name="amount">0</field>
        <field name="type_tax_use">sale</field>
        <field name="price_include">False</field>
        <field name="active">True</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_jp.tax_report_comp_basis_sales_duty_free')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_jp.tax_report_comp_basis_sales_duty_free')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="tax_group_id" ref="tax_group_0"/>
    </record>
    <record id="tax_in_o" model="account.tax.template">
        <field name="sequence">1</field>
        <field name="chart_template_id" ref="l10n_jp1"/>
        <field name="name">非課税販売</field>
        <field name="description">非課税販売</field>
        <field name="amount_type">percent</field>
        <field name="amount">0</field>
        <field name="type_tax_use">sale</field>
        <field name="price_include">False</field>
        <field name="active">True</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_jp.tax_report_comp_basis_sales_tax_free')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_jp.tax_report_comp_basis_sales_tax_free')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="tax_group_id" ref="tax_group_0"/>
    </record>
    <record id="tax_out_e" model="account.tax.template">
        <field name="sequence">1</field>
        <field name="chart_template_id" ref="l10n_jp1"/>
        <field name="name">仮払消費税(外) 8%</field>
        <field name="description">仮払消費税(外) 8%</field>
        <field name="amount_type">percent</field>
        <field name="amount">8</field>
        <field name="type_tax_use">purchase</field>
        <field name="price_include">False</field>
        <field name="active">True</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_jp.tax_report_comp_basis_purchases_taxable_8')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('A11809'),
                'plus_report_line_ids': [ref('l10n_jp.tax_report_to_pay_temp_pmt_susp_cons_8')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_jp.tax_report_comp_basis_purchases_taxable_8')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('A11809'),
                'minus_report_line_ids': [ref('l10n_jp.tax_report_to_pay_temp_pmt_susp_cons_8')],
            }),
        ]"/>
        <field name="tax_group_id" ref="tax_group_8"/>
    </record>
    <record id="tax_out_e_10" model="account.tax.template">
        <field name="sequence">1</field>
        <field name="chart_template_id" ref="l10n_jp1"/>
        <field name="name">仮払消費税(外) 10%</field>
        <field name="description">仮払消費税(外) 10%</field>
        <field name="amount_type">percent</field>
        <field name="amount">10</field>
        <field name="type_tax_use">purchase</field>
        <field name="price_include">False</field>
        <field name="active">True</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_jp.tax_report_comp_basis_purchases_taxable_10')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('A11809'),
                'plus_report_line_ids': [ref('l10n_jp.tax_report_to_pay_temp_pmt_susp_cons_10')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_jp.tax_report_comp_basis_purchases_taxable_10')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('A11809'),
                'minus_report_line_ids': [ref('l10n_jp.tax_report_to_pay_temp_pmt_susp_cons_10')],
            }),
        ]"/>
        <field name="tax_group_id" ref="tax_group_10"/>
    </record>
    <record id="tax_out_i" model="account.tax.template">
        <field name="sequence">1</field>
        <field name="chart_template_id" ref="l10n_jp1"/>
        <field name="name">仮払消費税(内) 8%</field>
        <field name="description">仮払消費税(内) 8%</field>
        <field name="amount_type">percent</field>
        <field name="amount">8</field>
        <field name="type_tax_use">purchase</field>
        <field name="price_include">True</field>
        <field name="active">True</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_jp.tax_report_comp_basis_purchases_taxable_8')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('A11809'),
                'plus_report_line_ids': [ref('l10n_jp.tax_report_to_pay_temp_pmt_susp_cons_8')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_jp.tax_report_comp_basis_purchases_taxable_8')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('A11809'),
                'minus_report_line_ids': [ref('l10n_jp.tax_report_to_pay_temp_pmt_susp_cons_8')],
            }),
        ]"/>
        <field name="tax_group_id" ref="tax_group_8"/>
    </record>
    <record id="tax_out_i_10" model="account.tax.template">
        <field name="sequence">1</field>
        <field name="chart_template_id" ref="l10n_jp1"/>
        <field name="name">仮払消費税(内) 10%</field>
        <field name="description">仮払消費税(内) 10%</field>
        <field name="amount_type">percent</field>
        <field name="amount">10</field>
        <field name="type_tax_use">purchase</field>
        <field name="price_include">True</field>
        <field name="active">True</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_jp.tax_report_comp_basis_purchases_taxable_10')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('A11809'),
                'plus_report_line_ids': [ref('l10n_jp.tax_report_to_pay_temp_pmt_susp_cons_10')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_jp.tax_report_comp_basis_purchases_taxable_10')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('A11809'),
                'minus_report_line_ids': [ref('l10n_jp.tax_report_to_pay_temp_pmt_susp_cons_10')],
            }),
        ]"/>
        <field name="tax_group_id" ref="tax_group_10"/>
    </record>
    <record id="tax_out_im" model="account.tax.template">
        <field name="sequence">1</field>
        <field name="chart_template_id" ref="l10n_jp1"/>
        <field name="name">海外仕入</field>
        <field name="description">海外仕入</field>
        <field name="amount_type">percent</field>
        <field name="amount">0</field>
        <field name="type_tax_use">purchase</field>
        <field name="price_include">False</field>
        <field name="active">True</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_jp.tax_report_comp_basis_purchases_import')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_jp.tax_report_comp_basis_purchases_import')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="tax_group_id" ref="tax_group_0"/>
    </record>
    <record id="tax_out_o" model="account.tax.template">
        <field name="sequence">1</field>
        <field name="chart_template_id" ref="l10n_jp1"/>
        <field name="name">非課税購買</field>
        <field name="description">非課税購買</field>
        <field name="amount_type">percent</field>
        <field name="amount">0</field>
        <field name="type_tax_use">purchase</field>
        <field name="price_include">False</field>
        <field name="active">True</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_jp.tax_report_comp_basis_purchases_tax_free')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_jp.tax_report_comp_basis_purchases_tax_free')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="tax_group_id" ref="tax_group_0"/>
    </record>

</odoo>