<?xml version='1.0' encoding='UTF-8'?>
<CreditNote xmlns="urn:oasis:names:specification:ubl:schema:xsd:CreditNote-2" xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2" xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2">
  <cbc:CustomizationID>urn:cen.eu:en16931:2017#compliant#urn:xoev-de:kosit:standard:xrechnung_2.3#conformant#urn:xoev-de:kosit:extension:xrechnung_2.3</cbc:CustomizationID>
  <cbc:ProfileID>urn:fdc:peppol.eu:2017:poacc:billing:01:1.0</cbc:ProfileID>
  <cbc:ID>RINV/2017/01/0001</cbc:ID>
  <cbc:IssueDate>2017-01-01</cbc:IssueDate>
  <cbc:CreditNoteTypeCode>381</cbc:CreditNoteTypeCode>
  <cbc:Note>test narration</cbc:Note>
  <cbc:DocumentCurrencyCode>USD</cbc:DocumentCurrencyCode>
  <cbc:BuyerReference>ref_partner_2</cbc:BuyerReference>
  <cac:OrderReference>
    <cbc:ID>ref_move</cbc:ID>
  </cac:OrderReference>
  <cac:AccountingSupplierParty>
    <cac:Party>
      <cbc:EndpointID schemeID="9930">DE257486969</cbc:EndpointID>
      <cac:PartyName>
        <cbc:Name>partner_1</cbc:Name>
      </cac:PartyName>
      <cac:PostalAddress>
        <cbc:StreetName>Legoland-Allee 3</cbc:StreetName>
        <cbc:CityName>Günzburg</cbc:CityName>
        <cbc:PostalZone>89312</cbc:PostalZone>
        <cac:Country>
          <cbc:IdentificationCode>DE</cbc:IdentificationCode>
        </cac:Country>
      </cac:PostalAddress>
      <cac:PartyTaxScheme>
        <cbc:CompanyID>DE257486969</cbc:CompanyID>
        <cac:TaxScheme>
          <cbc:ID>VAT</cbc:ID>
        </cac:TaxScheme>
      </cac:PartyTaxScheme>
      <cac:PartyLegalEntity>
        <cbc:RegistrationName>partner_1</cbc:RegistrationName>
        <cbc:CompanyID>DE257486969</cbc:CompanyID>
      </cac:PartyLegalEntity>
      <cac:Contact>
        <cbc:Name>partner_1</cbc:Name>
        <cbc:Telephone>+49 180 6 225789</cbc:Telephone>
        <cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
      </cac:Contact>
    </cac:Party>
  </cac:AccountingSupplierParty>
  <cac:AccountingCustomerParty>
    <cac:Party>
      <cbc:EndpointID schemeID="9930">DE186775212</cbc:EndpointID>
      <cac:PartyName>
        <cbc:Name>partner_2</cbc:Name>
      </cac:PartyName>
      <cac:PostalAddress>
        <cbc:StreetName>Europa-Park-Straße 2</cbc:StreetName>
        <cbc:CityName>Rust</cbc:CityName>
        <cbc:PostalZone>77977</cbc:PostalZone>
        <cac:Country>
          <cbc:IdentificationCode>DE</cbc:IdentificationCode>
        </cac:Country>
      </cac:PostalAddress>
      <cac:PartyTaxScheme>
        <cbc:CompanyID>DE186775212</cbc:CompanyID>
        <cac:TaxScheme>
          <cbc:ID>VAT</cbc:ID>
        </cac:TaxScheme>
      </cac:PartyTaxScheme>
      <cac:PartyLegalEntity>
        <cbc:RegistrationName>partner_2</cbc:RegistrationName>
        <cbc:CompanyID>DE186775212</cbc:CompanyID>
      </cac:PartyLegalEntity>
      <cac:Contact>
        <cbc:Name>partner_2</cbc:Name>
      </cac:Contact>
    </cac:Party>
  </cac:AccountingCustomerParty>
  <cac:PaymentMeans>
    <cbc:PaymentMeansCode name="credit transfer">30</cbc:PaymentMeansCode>
    <cbc:PaymentID>RINV/2017/01/0001</cbc:PaymentID>
    <cac:PayeeFinancialAccount>
      <cbc:ID>**********************</cbc:ID>
    </cac:PayeeFinancialAccount>
  </cac:PaymentMeans>
  <cac:PaymentTerms>
    <cbc:Note>30% Advance End of Following Month</cbc:Note>
  </cac:PaymentTerms>
  <cac:TaxTotal>
    <cbc:TaxAmount currencyID="USD">401.58</cbc:TaxAmount>
    <cac:TaxSubtotal>
      <cbc:TaxableAmount currencyID="USD">1782.00</cbc:TaxableAmount>
      <cbc:TaxAmount currencyID="USD">338.58</cbc:TaxAmount>
      <cac:TaxCategory>
        <cbc:ID>S</cbc:ID>
        <cbc:Percent>19.0</cbc:Percent>
        <cac:TaxScheme>
          <cbc:ID>VAT</cbc:ID>
        </cac:TaxScheme>
      </cac:TaxCategory>
    </cac:TaxSubtotal>
    <cac:TaxSubtotal>
      <cbc:TaxableAmount currencyID="USD">900.00</cbc:TaxableAmount>
      <cbc:TaxAmount currencyID="USD">63.00</cbc:TaxAmount>
      <cac:TaxCategory>
        <cbc:ID>S</cbc:ID>
        <cbc:Percent>7.0</cbc:Percent>
        <cac:TaxScheme>
          <cbc:ID>VAT</cbc:ID>
        </cac:TaxScheme>
      </cac:TaxCategory>
    </cac:TaxSubtotal>
  </cac:TaxTotal>
  <cac:LegalMonetaryTotal>
    <cbc:LineExtensionAmount currencyID="USD">2682.00</cbc:LineExtensionAmount>
    <cbc:TaxExclusiveAmount currencyID="USD">2682.00</cbc:TaxExclusiveAmount>
    <cbc:TaxInclusiveAmount currencyID="USD">3083.58</cbc:TaxInclusiveAmount>
    <cbc:PrepaidAmount currencyID="USD">0.00</cbc:PrepaidAmount>
    <cbc:PayableAmount currencyID="USD">3083.58</cbc:PayableAmount>
  </cac:LegalMonetaryTotal>
  <cac:CreditNoteLine>
    <cbc:ID>1754</cbc:ID>
    <cbc:CreditedQuantity unitCode="DZN">2.0</cbc:CreditedQuantity>
    <cbc:LineExtensionAmount currencyID="USD">1782.00</cbc:LineExtensionAmount>
    <cac:AllowanceCharge>
      <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
      <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
      <cbc:Amount currencyID="USD">198.00</cbc:Amount>
    </cac:AllowanceCharge>
    <cac:Item>
      <cbc:Description>product_a</cbc:Description>
      <cbc:Name>product_a</cbc:Name>
      <cac:ClassifiedTaxCategory>
        <cbc:ID>S</cbc:ID>
        <cbc:Percent>19.0</cbc:Percent>
        <cac:TaxScheme>
          <cbc:ID>VAT</cbc:ID>
        </cac:TaxScheme>
      </cac:ClassifiedTaxCategory>
    </cac:Item>
    <cac:Price>
      <cbc:PriceAmount currencyID="USD">990.0</cbc:PriceAmount>
    </cac:Price>
  </cac:CreditNoteLine>
  <cac:CreditNoteLine>
    <cbc:ID>1755</cbc:ID>
    <cbc:CreditedQuantity unitCode="C62">10.0</cbc:CreditedQuantity>
    <cbc:LineExtensionAmount currencyID="USD">1000.00</cbc:LineExtensionAmount>
    <cac:Item>
      <cbc:Description>product_b</cbc:Description>
      <cbc:Name>product_b</cbc:Name>
      <cac:ClassifiedTaxCategory>
        <cbc:ID>S</cbc:ID>
        <cbc:Percent>7.0</cbc:Percent>
        <cac:TaxScheme>
          <cbc:ID>VAT</cbc:ID>
        </cac:TaxScheme>
      </cac:ClassifiedTaxCategory>
    </cac:Item>
    <cac:Price>
      <cbc:PriceAmount currencyID="USD">100.0</cbc:PriceAmount>
    </cac:Price>
  </cac:CreditNoteLine>
  <cac:CreditNoteLine>
    <cbc:ID>1756</cbc:ID>
    <cbc:CreditedQuantity unitCode="C62">-1.0</cbc:CreditedQuantity>
    <cbc:LineExtensionAmount currencyID="USD">-100.00</cbc:LineExtensionAmount>
    <cac:Item>
      <cbc:Description>product_b</cbc:Description>
      <cbc:Name>product_b</cbc:Name>
      <cac:ClassifiedTaxCategory>
        <cbc:ID>S</cbc:ID>
        <cbc:Percent>7.0</cbc:Percent>
        <cac:TaxScheme>
          <cbc:ID>VAT</cbc:ID>
        </cac:TaxScheme>
      </cac:ClassifiedTaxCategory>
    </cac:Item>
    <cac:Price>
      <cbc:PriceAmount currencyID="USD">100.0</cbc:PriceAmount>
    </cac:Price>
  </cac:CreditNoteLine>
</CreditNote>
