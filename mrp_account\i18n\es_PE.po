# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * product_extended
#
# Translators:
# <PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2016-06-16 16:00+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Peru) (http://www.transifex.com/odoo/odoo-9/language/"
"es_PE/)\n"
"Language: es_PE\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: product_extended
#: code:addons/product_extended/wizard/wizard_price.py:24
#: code:addons/product_extended/wizard/wizard_price.py:40
#, python-format
msgid "Active ID is not set in Context."
msgstr "ID Activo no establecido en este Contexto."

#. module: product_extended
#: model:ir.model,name:product_extended.model_mrp_bom
msgid "Bill of Material"
msgstr "Lista de Material"

#. module: product_extended
#: model_terms:ir.ui.view,arch_db:product_extended.view_compute_price_wizard
msgid "Cancel"
msgstr "Cancelar"

#. module: product_extended
#: model_terms:ir.ui.view,arch_db:product_extended.view_compute_price_wizard
msgid "Change Price"
msgstr "Cambiar Precio"

#. module: product_extended
#: model_terms:ir.ui.view,arch_db:product_extended.view_compute_price_wizard
msgid "Change Standard Price"
msgstr "Cambiar Precio Estándar"

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_wizard_price_recursive
msgid "Change prices of child BoMs too"
msgstr "Cambiar precios de LdMs hijas también"

#. module: product_extended
#: model:ir.actions.act_window,name:product_extended.action_view_compute_price_wizard
#: model:ir.model,name:product_extended.model_wizard_price
msgid "Compute Price Wizard"
msgstr "Asistente de Cálculo de Precio"

#. module: product_extended
#: model_terms:ir.ui.view,arch_db:product_extended.product_product_ext_form_view2
msgid "Compute from BOM"
msgstr "Calcular desde LdM"

#. module: product_extended
#: model_terms:ir.ui.view,arch_db:product_extended.product_product_ext_form_view2
msgid ""
"Compute the price of the product using products and operations of related "
"bill of materials, for manufactured products only."
msgstr ""
"Calcular el precio del producto usando productos y operaciones de las listas "
"de materiales relacionadas, solamente para productos fabricados."

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_wizard_price_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_wizard_price_create_date
msgid "Created on"
msgstr "Creado en"

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_wizard_price_display_name
msgid "Display Name"
msgstr "Nombre a Mostrar"

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_wizard_price_real_time_accounting
msgid "Generate accounting entries when real-time"
msgstr "Generar asientos contables en tiempo real"

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_wizard_price_id
msgid "ID"
msgstr "ID"

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_wizard_price_info_field
msgid "Info"
msgstr "Info"

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_wizard_price___last_update
msgid "Last Modified on"
msgstr "Ultima Modificación en"

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_wizard_price_write_uid
msgid "Last Updated by"
msgstr "Actualizado última vez por"

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_wizard_price_write_date
msgid "Last Updated on"
msgstr "Ultima Actualización"

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_mrp_bom_get_variant_count
msgid "Number of variant for the product"
msgstr ""

#. module: product_extended
#: model:ir.model,name:product_extended.model_product_template
msgid "Product Template"
msgstr "Plantilla de Producto"

#. module: product_extended
#: model_terms:ir.ui.view,arch_db:product_extended.view_compute_price_wizard
msgid "Set price on BoM"
msgstr "Establecer precio en LdM"

#. module: product_extended
#: model:ir.model.fields,field_description:product_extended.field_mrp_bom_standard_price
msgid "Standard Price"
msgstr "Precio Estándar"

#. module: product_extended
#: model_terms:ir.ui.view,arch_db:product_extended.view_compute_price_wizard
msgid ""
"The price is computed from the bill of material lines which are not variant "
"specific"
msgstr ""
"El precio es calculado a partir de las líneas de lista de materiales que no "
"son una variante específica"

#. module: product_extended
#: code:addons/product_extended/wizard/wizard_price.py:38
#, fuzzy, python-format
msgid ""
"This wizard is built for product templates, while you are currently running "
"it from a product variant."
msgstr ""
"Este asistente está diseñado para plantillas de productos, mientras lo esté "
"ejecutando desde una variante de producto."

#~ msgid "Compute price wizard"
#~ msgstr "Asistente de cálculo de crecio"
