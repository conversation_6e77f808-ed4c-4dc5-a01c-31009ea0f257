<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- User Demo -->
    <record id="base.user_demo" model="res.users">
        <field name="groups_id" eval="[(4, ref('group_hr_timesheet_user'))]"/>
    </record>

    <!-- Employee -->
    <record id="hr.employee_admin" model="hr.employee">
        <field name="timesheet_cost">100</field>
    </record>

    <record id="hr.employee_qdp" model="hr.employee">
        <field name="timesheet_cost">75</field>
        <field name="parent_id" ref="hr.employee_admin"/>
    </record>

    <!-- Projects -->
    <record id="project.project_project_1" model="project.project">
        <field name="allow_timesheets" eval="True"/>
    </record>

    <record id="project.project_project_2" model="project.project">
        <field name="allow_timesheets" eval="True"/>
    </record>

    <!-- Timesheet Lines -->
    <record id="working_hours_requirements" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="user_id" ref='base.user_admin'/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2.00</field>
        <field name="project_id" ref='project.project_project_2'/>
        <field name="amount">-60.00</field>
    </record>

    <record id="working_hours_design" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="user_id" ref='base.user_admin'/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1.00</field>
        <field name="project_id" ref='project.project_project_2'/>
        <field name="amount">-30.00</field>
    </record>

    <record id="working_hours_coding" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="user_id" ref='base.user_admin'/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3.00</field>
        <field name="project_id" ref='project.project_project_2'/>
        <field name="amount">-90.00</field>
    </record>

    <record id="working_hours_testing" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="user_id" ref='base.user_admin'/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1.00</field>
        <field name="project_id" ref='project.project_project_2'/>
        <field name="amount">-30.00</field>
    </record>

    <record id="working_hours_maintenance" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="user_id" ref='base.user_admin'/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1.00</field>
        <field name="project_id" ref='project.project_project_2'/>
        <field name="amount">-30.00</field>
    </record>

    <record id="account_analytic_line_0" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_vad"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_20"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_1" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_9"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_2" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_10"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_3" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_22"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_4" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_11"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_5" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_12"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_6" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-3)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_20"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_7" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-3)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_10"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_8" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-3)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_9"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_9" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-4)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_10"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_10" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-4)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_21"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_11" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-4)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_20"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_12" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-5)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_21"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_13" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_hne"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-5)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_26"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_14" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_jep"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-5)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_10"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_15" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-6)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_26"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_16" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-6)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_21"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_17" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-6)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_20"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_18" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_jve"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-7)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_9"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_19" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-7)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_22"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_20" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_admin"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-7)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_11"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_21" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_jep"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-8)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_22"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_22" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_niv"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-8)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_11"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_23" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-8)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_12"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_24" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-9)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_11"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_25" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-9)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_11"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_26" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-9)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_12"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_27" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-10)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_12"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_28" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_fme"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-10)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_21"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_29" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-10)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_12"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_30" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_fme"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-11)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_26"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_31" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-11)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_21"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_32" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-11)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_10"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_33" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-12)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_9"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_34" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-12)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_20"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_35" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-12)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_21"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_36" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_jve"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-13)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_10"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_37" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-13)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_21"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_38" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_jod"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-13)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_20"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_39" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-14)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_22"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_40" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-14)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_12"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_41" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-14)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_22"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_42" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-15)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_10"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_43" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-15)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_10"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_44" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-15)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_20"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_45" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-16)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_20"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_46" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-16)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_22"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_47" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_fme"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-16)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_12"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_48" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_admin"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-17)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_9"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_49" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_jve"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-17)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_21"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_50" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-17)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_20"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_51" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-18)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_21"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_52" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-18)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_10"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_53" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-18)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_11"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_54" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_fme"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-19)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_12"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_55" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-19)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_26"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_56" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-19)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_11"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_57" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-20)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_22"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_58" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-20)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_10"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_59" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_jod"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-20)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_12"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_60" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_hne"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-21)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_11"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_61" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_jve"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-21)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_22"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_62" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-21)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_10"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_63" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-22)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_22"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_64" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-22)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_9"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_65" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_jod"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-22)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_12"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_66" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_vad"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-23)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_20"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_67" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_jod"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-23)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_12"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_68" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_jep"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-23)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_21"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_69" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_admin"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-24)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_9"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_70" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_admin"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-24)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_12"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_71" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_jod"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-24)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_12"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_72" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-25)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_20"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_73" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-25)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_10"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_74" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-25)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_22"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_75" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_fme"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-26)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_20"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_76" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-26)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_21"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_77" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_jod"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-26)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_11"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_78" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-27)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_12"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_79" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-27)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_9"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_80" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-27)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_11"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_81" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-28)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_10"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_82" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-28)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_22"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_83" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-28)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_26"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_84" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-29)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_21"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_85" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-29)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_26"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_86" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_fme"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-29)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_26"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_87" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-30)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_9"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_88" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_jep"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-30)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_21"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_89" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-30)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_21"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_90" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-31)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_20"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_91" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-31)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_9"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_92" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_niv"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-31)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_22"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_93" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-32)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_11"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_94" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-32)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_26"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_95" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-32)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_10"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_96" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-33)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_21"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_97" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-33)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_9"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_98" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-33)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_21"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_99" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-34)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_20"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_100" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-34)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_10"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_101" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-34)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_22"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_102" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-35)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_9"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_103" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-35)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_20"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_104" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-35)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_26"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_105" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-36)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_20"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_106" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-36)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_12"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_107" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-36)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_26"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_108" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-37)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_12"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_109" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-37)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_20"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_110" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-37)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_9"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_111" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_admin"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-38)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_21"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_112" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-38)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_9"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_113" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-38)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_11"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_114" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-39)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_22"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_115" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-39)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_10"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_116" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_jve"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-39)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_11"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_117" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-40)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_20"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_118" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-40)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_22"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_119" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-40)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_12"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_120" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_jep"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-41)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_12"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_121" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_jep"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-41)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_22"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_122" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-41)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_22"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_123" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_admin"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-42)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_20"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_124" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-42)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_12"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_125" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_vad"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-42)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_12"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_126" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-43)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_26"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_127" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-43)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_9"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_128" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-43)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_21"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_129" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-44)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_22"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_130" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_jve"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-44)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_20"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_131" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-44)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_11"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_132" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-45)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_11"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_133" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-45)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_9"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_134" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-45)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_26"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_135" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_jve"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-46)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_12"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_136" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-46)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_21"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_137" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-46)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_9"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_138" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-47)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_11"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_139" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_hne"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-47)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_20"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_140" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-47)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_22"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_141" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-48)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_11"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_142" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_jve"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-48)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_12"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_143" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-48)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_12"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_144" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-49)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_20"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_145" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-49)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_9"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_146" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-49)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_20"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_147" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-50)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_21"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_148" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-50)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_22"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_149" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_fme"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-50)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_21"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_150" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_jod"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-51)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_26"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_151" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-51)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_22"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_152" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-51)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_9"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_153" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-52)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_21"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_154" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_fme"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-52)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_22"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_155" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-52)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_21"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_156" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-53)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_26"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_157" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-53)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_26"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_158" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_admin"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-53)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_21"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_159" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-54)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_11"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_160" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_fme"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-54)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_10"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_161" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-54)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_11"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_162" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-55)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_10"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_163" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_fme"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-55)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_10"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_164" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_jve"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-55)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_21"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_165" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-56)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_12"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_166" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-56)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_20"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_167" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_fme"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-56)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_12"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_168" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_hne"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-57)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_10"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_169" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_jep"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-57)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_9"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_170" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-57)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_21"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_171" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-58)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_22"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_172" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-58)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_10"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_173" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-58)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_10"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_174" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-59)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_11"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_175" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_admin"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-59)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_12"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_176" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-59)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_20"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_177" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_hne"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-60)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_9"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_178" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-60)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_12"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_179" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-60)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_2"/>
        <field name="task_id" ref="project.project_task_21"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_180" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_25"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_181" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_25"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_182" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-1)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_4"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_183" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_4"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_184" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_2"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_185" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-2)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_7"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_186" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-3)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_6"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_187" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-3)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_2"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_188" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-3)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_1"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_189" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-4)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_7"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_190" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-4)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_7"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_191" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-4)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_7"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_192" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-5)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_2"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_193" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-5)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_5"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_194" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-5)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_4"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_195" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_jod"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-6)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_6"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_196" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_vad"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-6)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_25"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_197" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-6)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_5"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_198" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-7)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_5"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_199" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-7)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_1"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_200" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-7)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_5"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_201" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-8)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_25"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_202" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-8)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_5"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_203" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-8)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_7"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_204" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-9)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_4"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_205" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-9)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_4"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_206" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-9)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_7"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_207" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-10)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_5"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_208" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_jve"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-10)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_6"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_209" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_hne"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-10)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_6"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_210" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-11)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_5"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_211" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_niv"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-11)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_7"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_212" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-11)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_4"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_213" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-12)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_2"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_214" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-12)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_25"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_215" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-12)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_2"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_216" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-13)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_25"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_217" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_fme"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-13)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_5"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_218" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-13)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_5"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_219" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-14)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_1"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_220" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-14)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_2"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_221" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-14)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_6"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_222" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_jep"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-15)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_4"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_223" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-15)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_25"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_224" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-15)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_7"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_225" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_vad"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-16)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_5"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_226" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-16)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_4"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_227" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-16)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_5"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_228" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-17)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_6"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_229" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-17)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_7"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_230" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_hne"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-17)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_25"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_231" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-18)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_4"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_232" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_jep"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-18)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_7"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_233" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_niv"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-18)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_1"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_234" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_jve"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-19)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_25"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_235" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-19)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_2"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_236" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-19)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_6"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_237" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-20)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_25"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_238" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-20)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_7"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_239" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-20)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_2"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_240" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-21)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_5"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_241" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-21)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_1"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_242" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-21)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_6"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_243" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-22)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_6"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_244" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-22)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_5"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_245" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-22)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_4"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_246" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-23)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_5"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_247" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_fme"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-23)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_2"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_248" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_vad"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-23)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_6"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_249" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_vad"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-24)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_25"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_250" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_niv"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-24)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_4"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_251" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-24)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_1"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_252" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-25)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_2"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_253" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-25)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_7"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_254" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-25)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_6"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_255" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_fme"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-26)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_4"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_256" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_jep"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-26)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_25"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_257" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_jep"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-26)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_5"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_258" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-27)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_7"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_259" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-27)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_7"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_260" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_fme"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-27)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_25"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_261" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_admin"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-28)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_25"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_262" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-28)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_2"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_263" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-28)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_25"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_264" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_vad"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-29)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_1"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_265" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-29)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_6"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_266" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_admin"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-29)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_5"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_267" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_jod"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-30)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_2"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_268" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-30)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_6"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_269" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_jod"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-30)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_6"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_270" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-31)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_7"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_271" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-31)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_2"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_272" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-31)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_6"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_273" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-32)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_1"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_274" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-32)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_25"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_275" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-32)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_1"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_276" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_niv"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-33)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_4"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_277" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_jve"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-33)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_1"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_278" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-33)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_4"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_279" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-34)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_25"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_280" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_jod"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-34)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_2"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_281" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-34)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_4"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_282" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-35)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_2"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_283" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-35)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_6"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_284" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_vad"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-35)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_25"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_285" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-36)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_4"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_286" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-36)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_5"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_287" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-36)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_2"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_288" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_jve"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-37)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_7"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_289" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-37)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_25"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_290" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-37)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_7"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_291" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-38)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_2"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_292" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_hne"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-38)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_25"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_293" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_jep"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-38)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_7"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_294" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_niv"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-39)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_2"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_295" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-39)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_7"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_296" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-39)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_5"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_297" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_jod"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-40)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_7"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_298" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_vad"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-40)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_5"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_299" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-40)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_7"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_300" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-41)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_5"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_301" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_fpi"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-41)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_6"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_302" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_fme"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-41)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_7"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_303" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-42)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_4"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_304" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-42)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_2"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_305" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-42)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_6"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_306" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-43)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_5"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_307" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-43)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_4"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_308" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-43)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_4"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_309" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_niv"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-44)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_6"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_310" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_jep"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-44)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_7"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_311" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-44)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_7"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_312" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-45)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_7"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_313" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_jep"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-45)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_7"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_314" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-45)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_6"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_315" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-46)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_2"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_316" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_vad"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-46)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_7"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_317" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_niv"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-46)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_4"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_318" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-47)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_7"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_319" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_stw"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-47)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_6"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_320" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-47)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_4"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_321" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-48)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_25"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_322" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_admin"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-48)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_5"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_323" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-48)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_7"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_324" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-49)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_2"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_325" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_jep"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-49)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_25"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_326" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_vad"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-49)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_1"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_327" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_admin"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-50)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_7"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_328" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-50)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_25"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_329" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-50)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_4"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_330" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-51)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_4"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_331" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-51)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_6"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_332" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-51)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_25"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_333" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_jep"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-52)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_4"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_334" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_jog"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-52)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_4"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_335" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-52)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_4"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_336" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-53)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_2"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_337" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_jod"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-53)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_1"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_338" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_jod"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-53)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_2"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_339" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_lur"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-54)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_1"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_340" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-54)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_5"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_341" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_vad"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-54)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_1"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_342" model="account.analytic.line">
        <field name="name">Design</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-55)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_25"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_343" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_jgo"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-55)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_4"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_344" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_jth"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-55)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_1"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_345" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_jve"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-56)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_4"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_346" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_ngh"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-56)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_1"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_347" model="account.analytic.line">
        <field name="name">Sprint</field>
        <field name="employee_id" ref="hr.employee_jep"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-56)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_4"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_348" model="account.analytic.line">
        <field name="name">Training</field>
        <field name="employee_id" ref="hr.employee_vad"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-57)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_4"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_349" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-57)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_6"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_350" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_fme"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-57)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_1"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_351" model="account.analytic.line">
        <field name="name">On Site Visit</field>
        <field name="employee_id" ref="hr.employee_mit"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-58)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_7"/>
        <field name="amount">-90.0</field>
    </record>

    <record id="account_analytic_line_352" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-58)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_4"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_353" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_al"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-58)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">2</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_5"/>
        <field name="amount">-60.0</field>
    </record>

    <record id="account_analytic_line_354" model="account.analytic.line">
        <field name="name">Quality analysis</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-59)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_4"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_355" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_han"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-59)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_1"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_356" model="account.analytic.line">
        <field name="name">Presentation</field>
        <field name="employee_id" ref="hr.employee_chs"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-59)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_5"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_357" model="account.analytic.line">
        <field name="name">Requirements analysis</field>
        <field name="employee_id" ref="hr.employee_qdp"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-60)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_1"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_358" model="account.analytic.line">
        <field name="name">Call</field>
        <field name="employee_id" ref="hr.employee_jod"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-60)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">1</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_2"/>
        <field name="amount">-30.0</field>
    </record>

    <record id="account_analytic_line_359" model="account.analytic.line">
        <field name="name">Delivery</field>
        <field name="employee_id" ref="hr.employee_vad"/>
        <field name="date" eval="(DateTime.now() + relativedelta(days=-60)).strftime('%Y-%m-%d')"/>
        <field name="unit_amount">3</field>
        <field name="project_id" ref="project.project_project_1"/>
        <field name="task_id" ref="project.project_task_5"/>
        <field name="amount">-90.0</field>
    </record>

    <!-- Projects -->
    <record id="project.project_milestone_2" model="project.milestone">
        <field name="deadline" eval="(DateTime.now() + relativedelta(years=1, months=3)).strftime('%Y-%m-15')"/>
    </record>

    <record id="project_update_1" model="project.update" context="{'default_project_id': ref('project.project_project_1')}">
        <field name="name">Weekly review</field>
        <field name="user_id" eval="ref('base.user_demo')"/>
        <field name="progress" eval="30"/>
        <field name="status">on_hold</field>
    </record>
    <record id="project_update_2" model="project.update" context="{'default_project_id': ref('project.project_project_2')}">
        <field name="name">Weekly review</field>
        <field name="user_id" eval="ref('base.user_admin')"/>
        <field name="progress" eval="30"/>
        <field name="status">off_track</field>
    </record>

</odoo>
