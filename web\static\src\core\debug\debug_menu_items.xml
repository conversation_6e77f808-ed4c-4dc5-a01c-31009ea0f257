<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.DebugMenu.SetDefaultFooter" owl="1">
        <button class="btn btn-secondary" t-on-click="trigger('dialog-closed')">Close</button>
        <button class="btn btn-secondary" t-on-click="saveDefault">Save default</button>
    </t>

    <t t-name="web.DebugMenu.setDefaultBody" owl="1">
        <table style="width: 100%">
            <tr>
                <td>
                    <label for="formview_default_fields"
                           class="oe_label oe_align_right">
                        Default:
                    </label>
                </td>
                <td class="oe_form_required">
                    <select id="formview_default_fields" class="o_input" t-model="state.fieldToSet">
                        <option value=""/>
                        <option t-foreach="defaultFields" t-as="field"
                                t-att-value="field.name">
                            <t t-esc="field.string"/> = <t t-esc="field.displayed"/>
                        </option>
                    </select>
                </td>
            </tr>
            <tr t-if="conditions.length">
                <td>
                    <label for="formview_default_conditions"
                        class="oe_label oe_align_right">
                        Condition:
                    </label>
                </td>
                <td>
                    <select id="formview_default_conditions" class="o_input" t-model="state.condition">
                        <option value=""/>
                        <option t-foreach="conditions" t-as="cond"
                                t-att-value="cond.name + '=' + cond.value">
                            <t t-esc="cond.string"/>=<t t-esc="cond.displayed"/>
                        </option>
                    </select>
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    <input type="radio" id="formview_default_self"
                        value="self" name="scope" t-model="state.scope"/>
                    <label for="formview_default_self" class="oe_label"
                        style="display: inline;">
                        Only you
                    </label>
                    <br/>
                    <input type="radio" id="formview_default_all"
                        value="all" name="scope" t-model="state.scope"/>
                    <label for="formview_default_all" class="oe_label"
                        style="display: inline;">
                        All users
                    </label>
                </td>
            </tr>
        </table>
    </t>

    <t t-name="web.DebugMenu.getMetadataBody" owl="1">
        <table class="table table-sm table-striped">
            <tr>
                <th>ID:</th>
                <td><t t-esc="state.id"/></td>
            </tr>
            <tr>
                <th>XML ID:</th>
                <td><t t-esc="state.xmlid or '/'"/></td>
            </tr>
            <tr>
                <th>No Update:</th>
                <td>
                    <t t-esc="state.noupdate"/>
                    <t t-if="state.xmlid">
                        <a t-on-click="toggleNoupdate"> (change)</a>
                    </t>
                </td>
            </tr>
            <tr>
                <th>Creation User:</th>
                <td><t t-esc="state.creator"/></td>
            </tr>
            <tr>
                <th>Creation Date:</th>
                <td><t t-esc="state.create_date"/></td>
            </tr>
            <tr>
                <th>Latest Modification by:</th>
                <td><t t-esc="state.lastModifiedBy"/></td>
            </tr>
            <tr>
                <th>Latest Modification Date:</th>
                <td><t t-esc="state.write_date"/></td>
            </tr>
        </table>
    </t>
</templates>
