# Translation of Odoo Server.
 # This file contains the translation of the following modules:
 # * hr_payroll_community_v13
 # 
 # Translators:
 # <PERSON> <<EMAIL>>, 2018
 # <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
 # <PERSON> <<EMAIL>>, 2018
 # <PERSON>, 2019
 # 
 msgid ""
 msgstr ""
 "Project-Id-Version: Odoo Server 13.0\n"
 "Report-Msgid-Bugs-To: \n"
 "POT-Creation-Date: 2019-01-09 10:31+0000\n"
 "PO-Revision-Date: 2018-08-24 09:19+0000\n"
 "Last-Translator: <PERSON>, 2019\n"
 "Language-Team: Dutch (https://www.transifex.com/odoo/teams/41243/nl/)\n"
 "MIME-Version: 1.0\n"
 "Content-Type: text/plain; charset=UTF-8\n"
 "Content-Transfer-Encoding: \n"
 "Language: nl\n"
 "Plural-Forms: nplurals=2; plural=(n != 1);\n"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:42
 #, python-format
 msgid "%s (copy)"
 msgstr "%s (kopie)"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip__state
 msgid ""
 "* When the payslip is created the status is 'Draft'\n"
 "                \n"
 "* If the payslip is under verification, the status is 'Waiting'.\n"
 "                \n"
 "* If the payslip is confirmed then status is set to 'Done'.\n"
 "                \n"
 "* When user cancel payslip the status is 'Rejected'."
 msgstr ""
 "* Wanneer een loonafschrift is aangemaakt is de status 'Concept'. \n"
 "\n"
 "* Wanneer een loonafschrift ter goedkeuring is ingediend, is de status 'Wachtend'. \n"
 "\n"
 "* Wanneer een loonafschrift is goedgekeurd, is de status 'Gereed'. \n"
 "\n"
 "* Wanneer een loonafschrift is geannuleerd, is de status 'Afgewezen'."
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.res_config_settings_view_form
 msgid "<span class=\"o_form_label\">Payroll Rules</span>"
 msgstr "<span class=\"o_form_label\">Loonstrook regels</span>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_by_employees
 msgid ""
 "<span colspan=\"4\" nolabel=\"1\">This wizard will generate payslips for all"
 " selected employee(s) based on the dates and credit note specified on "
 "Payslips Run.</span>"
 msgstr ""
 "<span colspan=\"4\" nolabel=\"1\">Deze wizard zal loonafschriften genereren,"
 "  voor alle geselecteerde medewerkers, gebaseerd op datum en creditfactuur "
 "gespecificeerd op de loonafschrift run</span>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Address</strong>"
 msgstr "<strong>Adres</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Authorized signature</strong>"
 msgstr "<strong>Gemachtigde handtekening</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Bank Account</strong>"
 msgstr "<strong>Rekeningnummer</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 msgid "<strong>Date From:</strong>"
 msgstr "<strong>Vanaf:</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Date From</strong>"
 msgstr "<strong>Vanaf</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 msgid "<strong>Date To:</strong>"
 msgstr "<strong>T/m:</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Date To</strong>"
 msgstr "<strong>T/m</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Designation</strong>"
 msgstr "<strong>Ontwer</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Email</strong>"
 msgstr "<strong>E-mail</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Identification No</strong>"
 msgstr "<strong>Identificatie Nr</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Name</strong>"
 msgstr "<strong>Naam</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Reference</strong>"
 msgstr "<strong>Referentie</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 msgid "<strong>Register Name:</strong>"
 msgstr "<strong>Naam looninstelling:</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 msgid "<strong>Total</strong>"
 msgstr "<strong>Totaal</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.actions.act_window,help:hr_payroll_community_v13.action_contribution_register_form
 msgid ""
 "A contribution register is a third party involved in the salary\n"
 "            payment of the employees. It can be the social security, the\n"
 "            state or anyone that collect or inject money on payslips."
 msgstr ""
 "Een bijdrage register is een derde partij die betrokken is in de uitbetaling\n"
 "van de werknemers hun loon. Het kan de sociale zekerheid, de\n"
 "staat of eender wie zijn die geld verzamelt of invoert op loonstroken."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_res_config_settings__module_account_accountant
 msgid "Account Accountant"
 msgstr "Grootboek boekhouder"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.res_config_settings_view_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Accounting"
 msgstr "Boekhouding"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Accounting Information"
 msgstr "Boekhoudinformatie"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__active
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__active
 msgid "Active"
 msgstr "Actief"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.actions.act_window,help:hr_payroll_community_v13.action_contribution_register_form
 msgid "Add a new contribution register"
 msgstr "Voeg een nieuw bijdragenregister toe"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Add an internal note..."
 msgstr "Voeg interne notitie toe..."
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_contract_advantage_template_view_form
 msgid "Advantage Name"
 msgstr "Beloning naam"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.act_children_salary_rules
 msgid "All Children Rules"
 msgstr "Alle onderliggende definities"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule.category,name:hr_payroll_community_v13.ALW
 msgid "Allowance"
 msgstr "Toelage"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip.line,condition_select:0
 #: selection:hr.salary.rule,condition_select:0
 msgid "Always True"
 msgstr "Altijd waar"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__amount
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__amount
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "Amount"
 msgstr "Bedrag"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__amount_select
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__amount_select
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_filter
 msgid "Amount Type"
 msgstr "Bedragsoort"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.contract,schedule_pay:0
 msgid "Annually"
 msgstr "Jaarlijks"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__appears_on_payslip
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__appears_on_payslip
 msgid "Appears on Payslip"
 msgstr "Verschijnt op salarisstrook"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__condition_python
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__condition_python
 msgid ""
 "Applied this rule for calculation if condition is true. You can specify "
 "condition like basic > 1000."
 msgstr ""
 "Toegepaste regel voor de berekening als de conditie waar is. U kunt een "
 "conditie opgeven, zoals basic > 1000."
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule.category,name:hr_payroll_community_v13.BASIC
 msgid "Basic"
 msgstr "Basis"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_rule_basic
 msgid "Basic Salary"
 msgstr "Basis salaris"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_res_config_settings__module_l10n_be_hr_payroll_community_v13
 msgid "Belgium Payroll"
 msgstr "Belgische loonadministratie"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.contract,schedule_pay:0
 msgid "Bi-monthly"
 msgstr "Tweemaandelijks"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.contract,schedule_pay:0
 msgid "Bi-weekly"
 msgstr "Tweewekelijks"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_form
 msgid "Calculations"
 msgstr "Berekeningen"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_payslip_lines_contribution_register
 msgid "Cancel"
 msgstr "Annuleer"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Cancel Payslip"
 msgstr "Loonafschrift annuleren"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:96
 #, python-format
 msgid "Cannot cancel a payslip that is done."
 msgstr "Kan niet een loonstrook annuleren die voltooid is."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__category_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__category_id
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_rule_filter
 msgid "Category"
 msgstr "Categorie"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 msgid "Child Rules"
 msgstr "Onderliggende definitities"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__child_ids
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__child_ids
 msgid "Child Salary Rule"
 msgstr "Onderliggende salaris regel"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__children_ids
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__children_ids
 msgid "Children"
 msgstr "Onderliggende"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 msgid "Children Definition"
 msgstr "Definitie onderliggende"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.res_config_settings_view_form
 msgid "Choose a Payroll Localization"
 msgstr "Kies een loonadministratie lokalisatie"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip.run,state:0
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_form
 msgid "Close"
 msgstr "Sluiten"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__code
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__code
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__code
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__code
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__code
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__code
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__code
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "Code"
 msgstr "Code"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payroll_community_v13_structure_view_kanban
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_view_kanban
 msgid "Code:"
 msgstr "Code:"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 msgid "Companies"
 msgstr "Bedrijven"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__company_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__company_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__company_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__company_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__company_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__company_id
 msgid "Company"
 msgstr "Bedijf"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule.category,name:hr_payroll_community_v13.COMP
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 msgid "Company Contribution"
 msgstr "Bedrijfsbijdrage"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 msgid "Computation"
 msgstr "Berekening"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Compute Sheet"
 msgstr "Bereken strook"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__condition_select
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__condition_select
 msgid "Condition Based on"
 msgstr "Voorwaarde gebaseerd op"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 msgid "Conditions"
 msgstr "Voorwaarden"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_res_config_settings
 msgid "Config Settings"
 msgstr "Configuratie instellingen"
 
 #. module: hr_payroll_community_v13
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_hr_payroll_community_v13_configuration
 msgid "Configuration"
 msgstr "Instellingen"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Confirm"
 msgstr "Bevestigen"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__contract_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__contract_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__contract_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__contract_id
 msgid "Contract"
 msgstr "Contract"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.hr_contract_advantage_template_action
 #: model:ir.ui.menu,name:hr_payroll_community_v13.hr_contract_advantage_template_menu_action
 msgid "Contract Advantage Templates"
 msgstr "Contractvoordelen sjabloon"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_contribution_register_form
 msgid "Contribution"
 msgstr "Contributie"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_contribution_register
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__register_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__register_id
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_filter
 msgid "Contribution Register"
 msgstr "Contributie registratie"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_payslip_lines_contribution_register
 msgid "Contribution Register's Payslip Lines"
 msgstr "Bijdrage register loonafschriftregels"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_contribution_register_form
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_action_hr_contribution_register_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_contribution_register_filter
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_contribution_register_tree
 msgid "Contribution Registers"
 msgstr "Contributie registraties"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_salary_rule_convanceallowance1
 msgid "Conveyance Allowance"
 msgstr "Transporttoelage"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_salary_rule_ca_gravie
 msgid "Conveyance Allowance For Gravie"
 msgstr "Transporttoelage voor Gravie"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_employees__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register__create_uid
 msgid "Created by"
 msgstr "Aangemaakt door"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_employees__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register__create_date
 msgid "Created on"
 msgstr "Aangemaakt op"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__credit_note
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__credit_note
 msgid "Credit Note"
 msgstr "Creditfactuur"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__date_from
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__date_start
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register__date_from
 msgid "Date From"
 msgstr "Datum vanaf"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__date_to
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__date_end
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register__date_to
 msgid "Date To"
 msgstr "Datum tot"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule.category,name:hr_payroll_community_v13.DED
 msgid "Deduction"
 msgstr "Aftrek"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__default_value
 msgid "Default value for this advantage"
 msgstr "Standaard waarde voor dit voordeel"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_contract__schedule_pay
 msgid "Defines the frequency of the wage payment."
 msgstr "Definieert de frequentie van de salaris betaling."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip__struct_id
 msgid ""
 "Defines the rules that have to be applied to this payslip, accordingly to "
 "the contract chosen. If you let empty the field contract, this field isn't "
 "mandatory anymore and thus the rules applied will be all the rules set on "
 "the structure of all contracts of the employee valid for the chosen period"
 msgstr ""
 "Definieert de regels die moeten worden toegepast op dit loonafschrift, op "
 "basis van het gekozen contract. Als u het contract veld  leeg laat, is dit "
 "veld veld niet meer verplicht en zijn dus de regels van toepassing zijn dan "
 "alle regels ingesteld op de structuur van alle contracten van de werknemer "
 "geldig in de gekozen periode"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__note
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__note
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__note
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__note
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__note
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_contribution_register_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 msgid "Description"
 msgstr "Omschrijving"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Details By Salary Rule Category"
 msgstr "Details per salarisregel categorie"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__details_by_salary_rule_category
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "Details by Salary Rule Category"
 msgstr "Details per salarisregel categorie"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_employees__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_report_hr_payroll_community_v13_report_contributionregister__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_report_hr_payroll_community_v13_report_payslipdetails__display_name
 msgid "Display Name"
 msgstr "Schermnaam"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip,state:0
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_filter
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 msgid "Done"
 msgstr "Verwerkt"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_filter
 msgid "Done Payslip Batches"
 msgstr "Salarisstrook batches verwerkt"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 msgid "Done Slip"
 msgstr "Salarisstrook verwerkt"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip,state:0 selection:hr.payslip.run,state:0
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_filter
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 msgid "Draft"
 msgstr "Concept"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_filter
 msgid "Draft Payslip Batches"
 msgstr "Concept salarisstrookbatch"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 msgid "Draft Slip"
 msgstr "Concept salarisstrook"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_employee
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__employee_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__employee_id
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Employee"
 msgstr "Werknemer"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_contract
 msgid "Employee Contract"
 msgstr "Arbeidsovereenkomst"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_employee_grade_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payroll_community_v13_structure_list_view
 msgid "Employee Function"
 msgstr "Functie werknemer"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_view_hr_payslip_form
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_department_tree
 msgid "Employee Payslips"
 msgstr "Werknemer loonafschrift"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_contract_advantage_template
 msgid "Employee's Advantage on Contract"
 msgstr "Werknemer zijn voordeel op contract"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_contract__resource_calendar_id
 msgid "Employee's working schedule."
 msgstr "Werknemers werkschema."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_employees__employee_ids
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_by_employees
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_filter
 msgid "Employees"
 msgstr "Werknemers"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:92
 #, python-format
 msgid "Error! You cannot create recursive hierarchy of Salary Rule Category."
 msgstr ""
 "Fout! Het is niet toegestaan om een recursieve salarisregel hiërarchie aan "
 "te maken."
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:179
 #, python-format
 msgid "Error! You cannot create recursive hierarchy of Salary Rules."
 msgstr ""
 "Fout! Het is niet toegestaan om een recursieve salarisregels hiërarchie aan "
 "te maken."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__register_id
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__register_id
 msgid "Eventual third party involved in the salary payment of the employees."
 msgstr ""
 "De eventuele externe partij die bij de salaris-betaling aan medewerkers is "
 "betrokken"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip.line,amount_select:0
 #: selection:hr.salary.rule,amount_select:0
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__amount_fix
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__amount_fix
 msgid "Fixed Amount"
 msgstr "Vast Bedrag"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__amount_percentage
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__amount_percentage
 msgid "For example, enter 50.0 to apply a percentage of 50%"
 msgstr "Tik bijvoorbeeld 50,0 in om een percentage van 50% toe te passen."
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/report/report_contribution_register.py:35
 #, python-format
 msgid "Form content is missing, this report cannot be printed."
 msgstr "Formulier inhoud ontbreekt, dit rapport kan niet afgedrukt worden."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_res_config_settings__module_l10n_fr_hr_payroll_community_v13
 msgid "French Payroll"
 msgstr "Franse loonadministratie"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 msgid "General"
 msgstr "Algemeen"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_by_employees
 msgid "Generate"
 msgstr "Genereren"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_hr_payslip_by_employees
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_form
 msgid "Generate Payslips"
 msgstr "Genereer loonafschriften"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_payslip_employees
 msgid "Generate payslips for all selected employees"
 msgstr "Genereer loonafschriften voor alle geselecteerde werknemers"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_salary_rule_sales_commission
 msgid "Get 1% of sales"
 msgstr "Krijg 1% van verkopen"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:182
 #, python-format
 msgid "Global Leaves"
 msgstr "Algemene vakantie"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_rule_taxable
 #: model:hr.salary.rule.category,name:hr_payroll_community_v13.GROSS
 msgid "Gross"
 msgstr "Bruto"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_filter
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_rule_filter
 msgid "Group By"
 msgstr "Groepeer op"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_salary_rule_houserentallowance1
 msgid "House Rent Allowance"
 msgstr "Huis huuruitkering"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_employees__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_report_hr_payroll_community_v13_report_contributionregister__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_report_hr_payroll_community_v13_report_payslipdetails__id
 msgid "ID"
 msgstr "ID"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_run__credit_note
 msgid ""
 "If its checked, indicates that all payslips generated from here are refund "
 "payslips."
 msgstr ""
 "Indien aangevinkt, geeft dit aan dat alle loonafschriften, gegenereerd vanaf"
 " hier, credits zijn."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__active
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__active
 msgid ""
 "If the active field is set to false, it will allow you to hide the salary "
 "rule without removing it."
 msgstr ""
 "Als u de actief optie uitvinkt, dan kan u de salaris regel verbergen zonder "
 "ze te verwijderen."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_res_config_settings__module_l10n_in_hr_payroll_community_v13
 msgid "Indian Payroll"
 msgstr "Indiase loonadministratie"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip__credit_note
 msgid "Indicates this payslip has a refund of another"
 msgstr "Geeft aan dat dit loonafschrift een credit heeft van een andere"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Input Data"
 msgstr "Invoergegevens"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__input_ids
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__input_ids
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 msgid "Inputs"
 msgstr "Invoer"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__note
 msgid "Internal Note"
 msgstr "Interne notitie"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_view_kanban
 msgid "Is a Blocking Reason?"
 msgstr "Is een blokkerende reden?"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__quantity
 msgid ""
 "It is used in computation for percentage and fixed amount. For e.g. A rule "
 "for Meal Voucher having fixed amount of 1€ per worked day can have its "
 "quantity defined in expression like worked_days.WORK100.number_of_days."
 msgstr ""
 "Het wordt gebruikt in berekening voor percentage en vaste totaalbedrag. Voor"
 " bijvoorbeeld een regel van een maaltijdbon, met vast bedrag van 1 € per "
 "gewerkt dag, kan de hoeveelheid gedefinieerd worden als "
 "worked_days.WORK100.number_of_days."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_input__amount
 msgid ""
 "It is used in computation. For e.g. A rule for sales having 1% commission of"
 " basic salary for per product can defined in expression like result = "
 "inputs.SALEURO.amount * contract.wage*0.01."
 msgstr ""
 "Wordt gebruikt voor berekeningen. Voor bijvoorbeeld een regel voor de "
 "verkopers dat ze 1% commissie krijgen over het basis salaris. Dit kan worden"
 " gedefinieerd als result = inputs.SALEURO.amount * contract.wage*0.01"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_employees____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_report_hr_payroll_community_v13_report_contributionregister____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_report_hr_payroll_community_v13_report_payslipdetails____last_update
 msgid "Last Modified on"
 msgstr "Laatst gewijzigd op"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_employees__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register__write_uid
 msgid "Last Updated by"
 msgstr "Laatst bijgewerkt door"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_employees__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register__write_date
 msgid "Last Updated on"
 msgstr "Laatst bijgewerkt op"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule_category__parent_id
 msgid ""
 "Linking a salary category to its parent is used only for the reporting "
 "purpose."
 msgstr ""
 "Alleen voor rapportage doeleinden wordt een salariscategorie aan een "
 "bovenliggende categorie gekoppeld."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__lower_bound
 msgid "Lower Bound"
 msgstr "Ondergrens"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_contract_advantage_template__lower_bound
 msgid "Lower bound authorized by the employer for this advantage"
 msgstr "Ondergrens toegestaan door de werkgever voor dit voordeel"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__paid
 msgid "Made Payment Order ? "
 msgstr "Betalingsopdracht uitgevoerd? "
 
 #. module: hr_payroll_community_v13
 #: model:res.groups,name:hr_payroll_community_v13.group_hr_payroll_community_v13_manager
 msgid "Manager"
 msgstr "Manager"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__condition_range_max
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__condition_range_max
 msgid "Maximum Range"
 msgstr "Maximum bereik"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_salary_rule_meal_voucher
 msgid "Meal Voucher"
 msgstr "Maaltijdcheque"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__condition_range_min
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__condition_range_min
 msgid "Minimum Range"
 msgstr "Minimum bereik"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Miscellaneous"
 msgstr "Diversen"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.contract,schedule_pay:0
 msgid "Monthly"
 msgstr "Maandelijks"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__name
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "Name"
 msgstr "Naam"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule.category,name:hr_payroll_community_v13.NET
 msgid "Net"
 msgstr "Netto"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_rule_net
 msgid "Net Salary"
 msgstr "Netto salaris"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:201
 #, python-format
 msgid "Normal Working Days paid at 100%"
 msgstr "Standaard werkdagen 100% betaald"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_category_form
 msgid "Notes"
 msgstr "Opmerkingen"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__number_of_days
 msgid "Number of Days"
 msgstr "Aantal dagen"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__number_of_hours
 msgid "Number of Hours"
 msgstr "Aantal uren"
 
 #. module: hr_payroll_community_v13
 #: model:res.groups,name:hr_payroll_community_v13.group_hr_payroll_community_v13_user
 msgid "Officer"
 msgstr "Functionaris"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Other Inputs"
 msgstr "Overige ingaves"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__parent_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__parent_id
 msgid "Parent"
 msgstr "Bovenliggend"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__parent_rule_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__parent_rule_id
 msgid "Parent Salary Rule"
 msgstr "Bovenliggende salarisdefinitie"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__partner_id
 msgid "Partner"
 msgstr "Relatie"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_payslip
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__payslip_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__slip_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__payslip_id
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "Pay Slip"
 msgstr "Loonstrook"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 msgid "PaySlip Batch"
 msgstr "Salarisstrook batch"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.report,name:hr_payroll_community_v13.payslip_details_report
 msgid "PaySlip Details"
 msgstr "Salarisstrook details"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_payslip_lines_contribution_register
 msgid "PaySlip Lines"
 msgstr "Salarisstrook-regels"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.report,name:hr_payroll_community_v13.action_contribution_register
 msgid "PaySlip Lines By Conribution Register"
 msgstr "Loonafschriftregels per bijdrage register"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 msgid "PaySlip Lines by Contribution Register"
 msgstr "Loonafschriftregels per bijdrage register"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 msgid "PaySlip Name"
 msgstr "Loonafschrift naam"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.open_payroll_modules
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_hr_payroll_community_v13_root
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.res_config_settings_view_form
 msgid "Payroll"
 msgstr "Loonlijst"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_report_hr_payroll_community_v13_report_contributionregister
 msgid "Payroll Contribution Register Report"
 msgstr "Loon contributie registratierapport"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.res_config_settings_view_form
 msgid "Payroll Entries"
 msgstr "Payroll boekingen"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payroll_community_v13_structure_filter
 msgid "Payroll Structures"
 msgstr "Salarisstructuren"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.res_config_settings_view_form
 msgid "Payroll rules that apply to your country"
 msgstr "Payroll regel dat van toepassing zijn op uw land"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.report,name:hr_payroll_community_v13.action_report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Payslip"
 msgstr "Loonafschrift"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:82
 #, python-format
 msgid "Payslip 'Date From' must be earlier 'Date To'."
 msgstr "Loonstrook \"Datum van' moet voor 'Datum t/m' liggen."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_payslip_run
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__payslip_run_id
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_filter
 msgid "Payslip Batches"
 msgstr "Loonafschrift batches"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.act_payslip_lines
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__payslip_count
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Payslip Computation Details"
 msgstr "Details salarisstrookberekening"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_employee__payslip_count
 msgid "Payslip Count"
 msgstr "Loonstrook teller"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_report_hr_payroll_community_v13_report_payslipdetails
 msgid "Payslip Details Report"
 msgstr "Salarisstrook detailsrapport"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_payslip_input
 msgid "Payslip Input"
 msgstr "Salarisstrook Input"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__input_line_ids
 msgid "Payslip Inputs"
 msgstr "Loonafschrift ingaves"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_payslip_line
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_form
 msgid "Payslip Line"
 msgstr "Loonstrook regel"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.act_contribution_reg_payslip_lines
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__line_ids
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_filter
 msgid "Payslip Lines"
 msgstr "Salarisstrook-regels"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "Payslip Lines by Contribution Register"
 msgstr "Loonafschriftregels per bijdrage register"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_payslip_lines_contribution_register
 msgid "Payslip Lines by Contribution Registers"
 msgstr "Loonafschriftregels per bijdrage register"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__name
 msgid "Payslip Name"
 msgstr "Loonafschrift naam"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_payslip_worked_days
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__worked_days_line_ids
 msgid "Payslip Worked Days"
 msgstr "Loonstrook gewerkte dagen"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.act_hr_employee_payslip_list
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_employee__slip_ids
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__slip_ids
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.payroll_hr_employee_view_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_tree
 msgid "Payslips"
 msgstr "Loonstroken"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_hr_payslip_run_tree
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_hr_payslip_run
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_tree
 msgid "Payslips Batches"
 msgstr "Loonafschrift batches"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_by_employees
 msgid "Payslips by Employees"
 msgstr "Loonafschrift per werknemer"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip.line,amount_select:0
 #: selection:hr.salary.rule,amount_select:0
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__amount_percentage
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__amount_percentage
 msgid "Percentage (%)"
 msgstr "Percentage (%)"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__amount_percentage_base
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__amount_percentage_base
 msgid "Percentage based on"
 msgstr "Percentage gebaseerd op"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Period"
 msgstr "Periode"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.res_config_settings_view_form
 msgid "Post payroll slips in accounting"
 msgstr "Boek loonstroken in boekhouding"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_payslip_lines_contribution_register
 msgid "Print"
 msgstr "Afdrukken"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_salary_rule_professionaltax1
 msgid "Professional Tax"
 msgstr "BTW"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_salary_rule_providentfund1
 msgid "Provident Fund"
 msgstr "Voorzieningsfondsen"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip.line,amount_select:0
 #: selection:hr.salary.rule,amount_select:0
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__amount_python_compute
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__amount_python_compute
 msgid "Python Code"
 msgstr "Python Code"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__condition_python
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__condition_python
 msgid "Python Condition"
 msgstr "Python voorwaarde"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip.line,condition_select:0
 #: selection:hr.salary.rule,condition_select:0
 msgid "Python Expression"
 msgstr "Python expressie"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__quantity
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__quantity
 msgid "Quantity"
 msgstr "Hoeveelheid"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 msgid "Quantity/Rate"
 msgstr "Hoeveelheid/Tarief"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "Quantity/rate"
 msgstr "Hoeveelheid/ratio"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.contract,schedule_pay:0
 msgid "Quarterly"
 msgstr "Per kwartaal"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip.line,condition_select:0
 #: selection:hr.salary.rule,condition_select:0
 msgid "Range"
 msgstr "Bereik"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__condition_range
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__condition_range
 msgid "Range Based on"
 msgstr "Bereik gebaseerd op"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__rate
 msgid "Rate (%)"
 msgstr "Tarief (%)"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__code
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__number
 msgid "Reference"
 msgstr "Referentie"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Refund"
 msgstr "Creditfacturen"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:102
 #, python-format
 msgid "Refund: "
 msgstr "Credit: "
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__register_line_ids
 msgid "Register Line"
 msgstr "Register regel"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip,state:0
 msgid "Rejected"
 msgstr "Afgewezen"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__salary_rule_id
 msgid "Rule"
 msgstr "Regel"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_category_form
 msgid "Salary Categories"
 msgstr "Salaris categorie"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Salary Computation"
 msgstr "Salarisberekening"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_salary_rule
 msgid "Salary Rule"
 msgstr "Salarisregel"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_hr_salary_rule_category
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_hr_salary_rule_category
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_category_tree
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_salary_rule_category_filter
 msgid "Salary Rule Categories"
 msgstr "Salarisregel categorieën"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_salary_rule_category
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_filter
 msgid "Salary Rule Category"
 msgstr "Salarisregel categorie"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_rule_input
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__input_id
 msgid "Salary Rule Input"
 msgstr "Salaris regelinvoer"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_salary_rule_form
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_v13_structure__rule_ids
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_action_hr_salary_rule_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_list
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_tree
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_employee_grade_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_rule_filter
 msgid "Salary Rules"
 msgstr "Salaris definities"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:403
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:453
 #, python-format
 msgid "Salary Slip of %s for %s"
 msgstr "Loonafschrift van %s voor %s"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_payroll_community_v13_structure
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract__struct_id
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payroll_community_v13_structure_tree
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_tree
 msgid "Salary Structure"
 msgstr "Salarisstructuur"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_view_hr_payroll_community_v13_structure_list_form
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_hr_payroll_community_v13_structure_view
 msgid "Salary Structures"
 msgstr "Salarisstructuur"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract__schedule_pay
 msgid "Scheduled Pay"
 msgstr "Geplande betaling"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_filter
 msgid "Search Payslip Batches"
 msgstr "Zoek naar loonafschrift  batches"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_filter
 msgid "Search Payslip Lines"
 msgstr "Zoek loonafschrift regels"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 msgid "Search Payslips"
 msgstr "Zoek loonafschriften"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_rule_filter
 msgid "Search Salary Rule"
 msgstr "Zoek salaris regel"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.contract,schedule_pay:0
 msgid "Semi-annually"
 msgstr "Halfjaarlijks"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__sequence
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__sequence
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__sequence
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__sequence
 msgid "Sequence"
 msgstr "Reeks"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Set to Draft"
 msgstr "Zet op concept"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_hr_payroll_community_v13_configuration
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_hr_payroll_community_v13_global_settings
 msgid "Settings"
 msgstr "Instellingen"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 msgid "States"
 msgstr "Provincie/Staat"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__state
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__state
 msgid "Status"
 msgstr "Status"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__struct_id
 msgid "Structure"
 msgstr "Structuur"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__code
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__code
 msgid ""
 "The code of salary rules can be used as reference in computation of other "
 "rules. In that case, it is case sensitive."
 msgstr ""
 "De code van het salaris regels kan worden gebruikt als referentie berekening"
 " voor andere regels. In dat geval is het hoofdlettergevoelig."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_input__code
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_worked_days__code
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_rule_input__code
 msgid "The code that can be used in the salary rules"
 msgstr "De code die gebruikt kan worden in de salarisregels"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__amount_select
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__amount_select
 msgid "The computation method for the rule amount."
 msgstr "De berekeningsmethode voor de regel bedrag."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_input__contract_id
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_worked_days__contract_id
 msgid "The contract for which applied this input"
 msgstr "Het contract waarvoor de input geldt"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__condition_range_max
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__condition_range_max
 msgid "The maximum amount, applied for this rule."
 msgstr "Het maximum bedrag, van toepassing voor deze regel."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__condition_range_min
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__condition_range_min
 msgid "The minimum amount, applied for this rule."
 msgstr "Het minimale bedrag, toegewezen voor deze regel"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__condition_range
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__condition_range
 msgid ""
 "This will be used to compute the % fields values; in general it is on basic,"
 " but you can also use categories code fields in lowercase as a variable "
 "names (hra, ma, lta, etc.) and the variable basic."
 msgstr ""
 "Dit wordt gebruikt om de waardes van de %fields te berekenen. Normaliter is "
 "dit standaard, maar u kunt ook categorie veldcodes in kleine letters "
 "gebruiken als variabele naam (hra, ma, lta, etc.) en de variabele basic"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__total
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "Total"
 msgstr "Totaal"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Total Working Days"
 msgstr "Totaal aantal werkdagen"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__upper_bound
 msgid "Upper Bound"
 msgstr "Bovengrens"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_contract_advantage_template__upper_bound
 msgid "Upper bound authorized by the employer for this advantage"
 msgstr "Bovengrens toegestaan door de werkgever voor dit voordeel"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__sequence
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__sequence
 msgid "Use to arrange calculation sequence"
 msgstr "Wordt gebruikt om de berekeningsvolgorde te bepalen"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__appears_on_payslip
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__appears_on_payslip
 msgid "Used to display the salary rule on payslip."
 msgstr "Wordt gebruikt om de salarisregel weer te geven op het loonafschrift."
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip,state:0
 msgid "Waiting"
 msgstr "Wachten"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.contract,schedule_pay:0
 msgid "Weekly"
 msgstr "Wekelijks"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Worked Day"
 msgstr "Gewerkte dag"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Worked Days"
 msgstr "Gewerkte dagen"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Worked Days & Inputs"
 msgstr "Gewerkte dagen & ingaves"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract__resource_calendar_id
 msgid "Working Schedule"
 msgstr "Werktijden"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:211
 #, python-format
 msgid "Wrong percentage base or quantity defined for salary rule %s (%s)."
 msgstr ""
 "Verkeerd basispercentage of hoeveelheid gedefinieerd voor salarisregel %s "
 "(%s)."
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:217
 #, python-format
 msgid "Wrong python code defined for salary rule %s (%s)."
 msgstr "Foutieve python code gedefinieerd voor salarisregel %s (%s)."
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:240
 #, python-format
 msgid "Wrong python condition defined for salary rule %s (%s)."
 msgstr "Foutieve python voorwaarde gedefinieerd voor salarisregel %s (%s)."
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:204
 #, python-format
 msgid "Wrong quantity defined for salary rule %s (%s)."
 msgstr "Verkeerd aantal gedefinieerd voor salarisregel %s (%s)."
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:234
 #, python-format
 msgid "Wrong range condition defined for salary rule %s (%s)."
 msgstr "Verkeerde bereikvoorwaarde gedefinieerd voor salarisregel %s (%s)."
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:36
 #, python-format
 msgid "You cannot create a recursive salary structure."
 msgstr "U kan geen recursieve salarisstructuur aanmaken."
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:127
 #, python-format
 msgid "You cannot delete a payslip which is not draft or cancelled!"
 msgstr ""
 "U kunt geen loonstrook verwijderen dat niet een concept of geannuleerd is!"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/wizard/hr_payroll_community_v13_payslips_by_employees.py:24
 #, python-format
 msgid "You must select employee(s) to generate payslip(s)."
 msgstr "U dient werknemer(s) te selecteren om loonafschrift(en) te genereren."
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:525
 #, python-format
 msgid "You must set a contract to create a payslip line."
 msgstr ""
 "U dient een contract in te stellen om een loonafschriftregel te maken."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__amount_percentage_base
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__amount_percentage_base
 msgid "result will be affected to a variable"
 msgstr "resultaat wordt beïnvloed door een variabele"
 