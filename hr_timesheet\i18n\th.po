# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_timesheet
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# Odo<PERSON> Thaidev <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid "%s Spent"
msgstr "%sจ่ายแล้ว"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid "(%(sign)s%(hours)s:%(minutes)s remaining)"
msgstr "(%(sign)s%(hours)s:%(minutes)s คงเหลืออยู่)"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid "(%s days remaining)"
msgstr "(%s วัน คงเหลือ)"

#. module: hr_timesheet
#: model_terms:digest.tip,tip_description:hr_timesheet.digest_tip_hr_timesheet_0
msgid "<b class=\"tip_title\">Tip: Record your Timesheets faster</b>"
msgstr "<b class=\"tip_title\">เคล็ดลับ: บันทึกใบบันทึกเวลาของคุณเร็วขึ้น</b>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "<em class=\"font-weight-normal text-muted\">Timesheets for employee:</em>"
msgstr ""
"<em class=\"font-weight-normal text-muted\">ใบบันทึกเวลาสำหรับพนักงาน:</em>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "<em class=\"font-weight-normal text-muted\">Timesheets for project:</em>"
msgstr "<em class=\"font-weight-normal text-muted\">ใบบันทึกสำหรับโปรเจกต์:</em>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "<em class=\"font-weight-normal text-muted\">Timesheets for task:</em>"
msgstr "<em class=\"font-weight-normal text-muted\">ใบบันทึกเวลาสำหรับงาน:</em>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "<em class=\"font-weight-normal text-muted\">Timesheets on </em>"
msgstr "<em class=\"font-weight-normal text-muted\">ใบบันทึกเวลาบน </em>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_kanban_account_analytic_line
msgid "<i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr "<i class=\"fa fa-calendar\" role=\"img\" aria-label=\"วันที่\" title=\"Date\"/>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o \" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o \" "
"title=\"ค่าที่ตั้งไว้นี้เป็นค่าเฉพาะบริษัท\" "
"groups=\"base.group_multi_company\"/>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" "
"title=\"ค่าที่ตั้งไว้นี้เป็นค่าเฉพาะบริษัท\" "
"groups=\"base.group_multi_company\"/>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_invoice_form
msgid "<span class=\"o_stat_text\">Recorded</span>"
msgstr "<span class=\"o_stat_text\">บันทึกแล้ว</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_view_form_inherit_timesheet
msgid "<span class=\"o_stat_text\">Timesheets</span>"
msgstr "<span class=\"o_stat_text\">ใบบันทึกเวลา</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid ""
"<span class=\"text-nowrap\" attrs=\"{'invisible' : [('encode_uom_in_days', '=', True)]}\">Hours Spent on Sub-tasks:</span>\n"
"                                <span class=\"text-nowrap\" attrs=\"{'invisible' : [('encode_uom_in_days', '=', False)]}\">Days Spent on Sub-tasks:</span>"
msgstr ""
"<span class=\"text-nowrap\" attrs=\"{'invisible' : [('encode_uom_in_days', '=', True)]}\">ชั่วโมงที่ใช้ในงานย่อย:</span>\n"
"                                <span class=\"text-nowrap\" attrs=\"{'invisible' : [('encode_uom_in_days', '=', False)]}\">วันที่ใช้ไปกับงานย่อย:</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
msgid ""
"<span class=\"text-nowrap\" attrs=\"{'invisible' : [('encode_uom_in_days', '=', True)]}\">Sub-tasks Hours Spent</span>\n"
"                                <span class=\"text-nowrap\" attrs=\"{'invisible' : [('encode_uom_in_days', '=', False)]}\">Sub-tasks Days Spent</span>"
msgstr ""
"<span class=\"text-nowrap\" attrs=\"{'invisible' : [('encode_uom_in_days', '=', True)]}\">งานย่อยชั่วโมงที่ใช้ไป</span>\n"
"                                <span class=\"text-nowrap\" attrs=\"{'invisible' : [('encode_uom_in_days', '=', False)]}\">งานย่อยวันที่ใช้ไป</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span style=\"margin-right: 15px;\">Total (Days)</span>"
msgstr "<span style=\"margin-right: 15px;\">รวม (วัน)</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span style=\"margin-right: 15px;\">Total (Hours)</span>"
msgstr "<span style=\"margin-right: 15px;\">รวม (ชั่วโมง)</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_tasks_list_inherit
msgid "<span> day(s)</span>"
msgstr "<span> วัน </span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_tasks_list_inherit
msgid "<span> hour(s)</span>"
msgstr "<span> ชั่วโมง</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Date</span>"
msgstr "<span>วันที่</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Description</span>"
msgstr "<span>คำอธิบาย</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Project</span>"
msgstr "<span>โปรเจกต์</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Responsible</span>"
msgstr "<span>ความรับผิดชอบ</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Task</span>"
msgstr "<span>งาน</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "<strong>Days recorded:</strong>"
msgstr "<strong>วันที่ถูกบันทึก:</strong>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_kanban_account_analytic_line
msgid "<strong>Duration: </strong>"
msgstr "<strong>ระยะเวลา: </strong>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "<strong>Hours recorded:</strong>"
msgstr "<strong>ชั่วโมงที่ถูกบันทึก:</strong>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_task_planned_hours_template
msgid "<strong>Planned Days:</strong>"
msgstr "<strong>วันที่วางแผน:</strong>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_task
msgid "<strong>Progress:</strong>"
msgstr "<strong>ความคืบหน้า:</strong>"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__analytic_account_active
msgid "Active Analytic Account"
msgstr "เปิดใช้งานบัญชีวิเคราะห์"

#. module: hr_timesheet
#: model:res.groups,name:hr_timesheet.group_timesheet_manager
msgid "Administrator"
msgstr "ผู้ดูแลระบบ"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "All"
msgstr "ทั้งหมด"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_all
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_activity_all
msgid "All Timesheets"
msgstr "ใบบันทึกเวลาทั้งหมด"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__allow_timesheets
msgid "Allow timesheets"
msgstr "อนุญาติใบบันทึกเวลา"

#. module: hr_timesheet
#: code:addons/hr_timesheet/__init__.py:0
#, python-format
msgid "Analysis"
msgstr "การวิเคราะห์"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__analytic_account_id
msgid "Analytic Account"
msgstr "บัญชีวิเคราะห์"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_form
msgid "Analytic Entry"
msgstr "รายการวิเคราะห์"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr "ไลน์การวิเคราะห์"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_project__analytic_account_id
msgid ""
"Analytic account to which this project is linked for financial management. "
"Use an analytic account to record cost and revenue on your project."
msgstr ""
"บัญชีวิเคราะห์ที่โปรเจกต์นี้เชื่อมโยงกับการจัดการทางการเงิน "
"ใช้บัญชีวิเคราะห์เพื่อบันทึกต้นทุนและรายได้ในโปรเจกต์ของคุณ"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Apple App Store"
msgstr "Apple App Store"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_view_form_inherit_timesheet
msgid "Application Settings"
msgstr "ตั้งค่าแอปพลิเคชั่น"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__timesheet_ids
msgid "Associated Timesheets"
msgstr "ใบบันทึกที่เกี่ยวข้อง"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__module_project_timesheet_synchro
msgid "Awesome Timesheet"
msgstr "ใบบันทึกเวลาที่ยอดเยี่ยม"

#. module: hr_timesheet
#: model:project.task.type,legend_blocked:hr_timesheet.internal_project_default_stage
msgid "Blocked"
msgstr "บล็อก"

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.menu_hr_activity_analysis
msgid "By Employee"
msgstr "โดยพนักงาน"

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_report_timesheet_by_project
msgid "By Project"
msgstr "ตามโปรเจกต์"

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_report_timesheet_by_task
msgid "By Task"
msgstr "ตามงาน"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_project_collaborator
msgid "Collaborators in project shared"
msgstr "ผู้ทำงานร่วมกันในโปรเจกต์ที่แชร์"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_res_company
msgid "Companies"
msgstr "บริษัท"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.hr_timesheet_menu_configuration
msgid "Configuration"
msgstr "การกำหนดค่า"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_project_task_create_timesheet
msgid "Create Timesheet from task"
msgstr "สร้างใบบันทึกเวลาจากงาน"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee__currency_id
msgid "Currency"
msgstr "สกุลเงิน"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
#, python-format
msgid "Date"
msgstr "วันที่"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_tasks_list_inherit
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Days Spent"
msgstr "วันที่ใช้ไป"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Days recorded on sub-tasks:"
msgstr "วันที่บันทึกไว้ในงานย่อย:"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_res_company__internal_project_id
msgid "Default project value for timesheet generated from time off type."
msgstr "ค่าโปรเจกต์เริ่มต้นสำหรับใบบันทึกเวลาที่สร้างจากประเภทเวลาหยุด"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_create_timesheet_view_form
msgid "Delete"
msgstr "ลบ"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__department_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "Department"
msgstr "แผนก"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_create_timesheet_view_form
msgid "Describe your activity..."
msgstr "อธิบายกิจกรรมของคุณ..."

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__description
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
#, python-format
msgid "Description"
msgstr "คำอธิบาย"

#. module: hr_timesheet
#. openerp-web
#: code:addons/hr_timesheet/static/src/js/timesheet_config_form_view.js:0
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_create_timesheet_view_form
#, python-format
msgid "Discard"
msgstr "ละทิ้ง"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__progress
msgid "Display progress of current task."
msgstr "แสดงความคืบหน้าของงานปัจจุบัน"

#. module: hr_timesheet
#. openerp-web
#: code:addons/hr_timesheet/static/src/js/timesheet_config_form_view.js:0
#, python-format
msgid "Download our App"
msgstr "ดาวน์โหลดแอปของเรา"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_create_timesheet_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Duration"
msgstr "ระยะเวลา"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Duration (Days)"
msgstr "ระยะเวลา (วัน)"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Duration (Hours)"
msgstr " ระยะเวลา (ชั่วโมง)"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__hours_effective
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_tree
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Effective Hours"
msgstr "ชั่วโมงที่มีมีประสิทธิภาพ"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model,name:hr_timesheet.model_hr_employee
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__employee_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
#, python-format
msgid "Employee"
msgstr "พนักงาน"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__reminder_user_allow
msgid "Employee Reminder"
msgstr "ตัวเตือนพนักงาน"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_project__allow_timesheets
msgid "Enable timesheeting on the project."
msgstr "เปิดใช้งานใบบันทึกเวลาบนโปรเจกต์"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__encode_uom_in_days
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__encode_uom_in_days
msgid "Encode Uom In Days"
msgstr "เข้ารหัสหน่วยวัดเป็นวัน"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__timesheet_encode_uom_id
msgid "Encoding Unit"
msgstr "หน่วยเข้ารหัส"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__encoding_uom_id
msgid "Encoding Uom"
msgstr "การเข้ารหัส Uom"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Generate timesheets upon time off validation"
msgstr "สร้างใบบันทึกเวลาในการตรวจสอบการลา"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Google Chrome Store"
msgstr "Google Chrome Store"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Google Play Store"
msgstr "Google Play Store"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "Group By"
msgstr "จัดกลุ่มตาม"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_ir_http
msgid "HTTP Routing"
msgstr "การกำหนด HTTP"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__has_planned_hours_tasks
msgid "Has Planned Hours Tasks"
msgstr "มีการวางแผนชั่วโมงงาน"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid "Hours"
msgstr "ชั่วโมง"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__effective_hours
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_tasks_list_inherit
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Hours Spent"
msgstr "ชั่วโมงที่ใช้"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Hours recorded on sub-tasks:"
msgstr "ชั่วโมงที่ถูกบันทึกไว้ในงานย่อย:"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__id
msgid "ID"
msgstr "ไอดี"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_res_config_settings__reminder_manager_allow
msgid "If checked, send an email to all manager"
msgstr "หากเลือก จะส่งอีเมลถึงผู้จัดการทั้งหมด"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_res_config_settings__reminder_user_allow
msgid ""
"If checked, send an email to all users who have not recorded their timesheet"
msgstr ""
"หากเลือก จะส่งอีเมลถึงผู้ใช้ทุกคนที่ยังไม่ได้บันทึกในใบบันทึกเวลาของตนเอง"

#. module: hr_timesheet
#: model:project.task.type,legend_normal:hr_timesheet.internal_project_default_stage
msgid "In Progress"
msgstr "กำลังดำเนินการ"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Initially Planned Days"
msgstr "วันที่วางแผนไว้เบื้องต้น"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_tree
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Initially Planned Hours"
msgstr "ชั่วโมงที่วางแผนไว้เบื้องต้น"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/res_company.py:0
#: model:project.task.type,name:hr_timesheet.internal_project_default_stage
#, python-format
msgid "Internal"
msgstr "ภายใน"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_company__internal_project_id
msgid "Internal Project"
msgstr "โปรเจกต์เบื้องต้น"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__is_encode_uom_days
msgid "Is Encode Uom Days"
msgstr "คือการเข้ารหัส Uom วัน"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__is_internal_project
msgid "Is Internal Project"
msgstr "เป็นโปรเจกต์ภายใน"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งล่าสุดเมื่อ"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Last month"
msgstr "เดือนที่แล้ว"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Last week"
msgstr "สัปดาห์ที่แล้ว"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Last year"
msgstr "ปีที่แล้ว"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_invoice_form
msgid "Log time on tasks"
msgstr "บันทึกเวลาในงาน"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__reminder_manager_allow
msgid "Manager Reminder"
msgstr "จัดการตัวเตือน"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/res_company.py:0
#, python-format
msgid "Meeting"
msgstr "การประชุม"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_ir_ui_menu
msgid "Menu"
msgstr "เมนู"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_project_task_user_view_search
msgid "My Team's Projects"
msgstr "โปรเจกต์ของทีมฉัน"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_view_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_project_task_user_view_search
msgid "My Team's Tasks"
msgstr "งานของทีมฉัน"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.act_hr_timesheet_line
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_activity_mine
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_activity_user
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "My Timesheets"
msgstr "ใบบันทึกเวลาของฉัน"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Newest"
msgstr "ใหม่สุด"

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_report
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_project
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_task
msgid "No activities found"
msgstr "ไม่พบกิจกรรม"

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_all
msgid "No activities found. Let's start a new one!"
msgstr "ไม่พบกิจกรรม มาเริ่มกันใหม่!"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "None"
msgstr "ไม่มี"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__overtime
msgid "Overtime"
msgstr "ล่วงเวลา"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__partner_id
msgid "Partner"
msgstr "พาร์ทเนอร์"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__hours_planned
msgid "Planned Hours"
msgstr "ชั่วโมงที่วางแผน"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_uom_uom
msgid "Product Unit of Measure"
msgstr "หน่วยวัดสินค้า"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__progress
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__progress
#, python-format
msgid "Progress"
msgstr "ความคืบหน้า"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model,name:hr_timesheet.model_project_project
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__project_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet_project
#, python-format
msgid "Project"
msgstr "โปรเจกต์"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_company__project_time_mode_id
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__project_time_mode_id
msgid "Project Time Unit"
msgstr "หน่วยเวลาของโปรเจกต์"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_project
msgid "Project's Timesheets"
msgstr "ใบบันทึกเวลาของโปรเจกต์"

#. module: hr_timesheet
#: model:project.task.type,legend_done:hr_timesheet.internal_project_default_stage
msgid "Ready"
msgstr "พร้อม"

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line_by_project
msgid "Record a new activity"
msgstr "บันทึกกิจกรรมใหม่"

#. module: hr_timesheet
#: model_terms:digest.tip,tip_description:hr_timesheet.digest_tip_hr_timesheet_0
msgid ""
"Record your timesheets in an instant by pressing Shift + the corresponding "
"hotkey to add 15min to your projects."
msgstr ""
"บันทึกใบบันทึกเวลาของคุณในทันทีโดยกด Shift + ปุ่มลัดที่เกี่ยวข้องเพื่อเพิ่ม "
"15 นาทีให้กับโปรเจกต์ของคุณ"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid "Recorded"
msgstr "บันทึกแล้ว"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Remaining Days"
msgstr "วันที่เหลือ"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Remaining Days:"
msgstr "วันที่คงเหลือ::"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__remaining_hours
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__remaining_hours
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_tree
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Remaining Hours"
msgstr "ชั่วโมงที่เหลือ"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Remaining Hours:"
msgstr "ชั่วโมงคงเหลือ:"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__remaining_hours
msgid "Remaining Invoiced Time"
msgstr "เวลาที่เหลืออยู่ในใบแจ้งหนี้"

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.menu_timesheets_reports
msgid "Reporting"
msgstr "การรายงาน"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_create_timesheet_view_form
msgid "Save"
msgstr "บันทึก"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_create_timesheet_view_form
msgid "Save time"
msgstr "ประหยัดเวลา"

#. module: hr_timesheet
#. openerp-web
#: code:addons/hr_timesheet/static/src/xml/qr_modal_template.xml:0
#, python-format
msgid "Scan this QR code to get the Awesome Timesheet app:"
msgstr "สแกนโค้ด QR นี้เพื่อดาวน์โหลดแอปใบบันทึกเวลาที่ยอดเยี่ยม"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in All"
msgstr "ค้นหาในทั้งหมด"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Description"
msgstr "ค้นหาในคำอธิบาย"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Employee"
msgstr "ค้นหาในพนักงาน"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Project"
msgstr "ค้นหาในโปรเจกต์"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Task"
msgstr "ค้นหางาน"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid "See timesheet entries"
msgstr "ดูรายการใบบันทึกเวลา"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Send a periodical email reminder to timesheets managers"
msgstr "ส่งอีเมล์เตือนความจำเป็นระยะถึงใบบันทึกเวลาของผู้จัดการ"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Send a periodical email reminder to timesheets users"
msgstr "ส่งการแจ้งเตือนทางอีเมลเป็นระยะถึงผู้ใช้ใบบันทึกเวลา"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.hr_timesheet_config_settings_action
msgid "Settings"
msgstr "ตั้งค่า"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__subtask_effective_hours
msgid "Sub-tasks Hours Spent"
msgstr "ชั่วโมงการทำงานย่อยที่ใช้ไป"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Sub-tasks Planned Days"
msgstr "วันที่วางแผนงานย่อย"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Sub-tasks Planned Hours"
msgstr "ชั่วโมงการทำงานย่อยที่วางแผนไว้"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model,name:hr_timesheet.model_project_task
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__task_id
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__task_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_project_task_timesheet
#, python-format
msgid "Task"
msgstr "งาน"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task_create_timesheet__task_id
msgid "Task for which we are creating a sales order"
msgstr "งานที่เรากำลังสร้างคำสั่งขาย"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_task
msgid "Task's Timesheets"
msgstr "ใบบันทึกเวลางาน"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_project_task_timesheet
msgid "Task:"
msgstr "งาน:"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_report_project_task_user
msgid "Tasks Analysis"
msgstr "วิเคราะห์งาน"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_view_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_project_task_user_view_search
msgid "Tasks in Overtime"
msgstr "งานล่วงเวลา"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/res_company.py:0
#, python-format
msgid "The Internal Project of a company should be in that company."
msgstr "โปรเจกต์ภายในของบริษัทควรอยู่ในบริษัทนั้น"

#. module: hr_timesheet
#: model:ir.model.constraint,message:hr_timesheet.constraint_project_task_create_timesheet_time_positive
msgid "The timesheet's time must be positive"
msgstr "เวลาของใบบันทึกเวลาต้องเป็นค่าบวก"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "There are no timesheets."
msgstr "ไม่มีใบบันทึกเวลา"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid ""
"These projects have some timesheet entries referencing them. Before removing"
" these projects, you have to remove these timesheet entries."
msgstr ""
"โปรเจกต์เหล่านี้มีรายการใบบันทึกเวลาอ้างอิง ก่อนลบโปรเจกต์เหล่านี้ "
"คุณต้องลบรายการใบบันทึกเวลาเหล่านี้ก่อน"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid ""
"These tasks have some timesheet entries referencing them. Before removing "
"these tasks, you have to remove these timesheet entries."
msgstr ""
"งานเหล่านี้มีรายการใบบันทึกเวลาที่อ้างอิง ก่อนลบงานเหล่านี้ "
"คุณต้องลบรายการใบบันทึกเวลาเหล่านี้ก่อน"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "This Quarter"
msgstr "ไตรมาสนี้"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "This month"
msgstr "เดือนนี้"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid ""
"This project has some timesheet entries referencing it. Before removing this"
" project, you have to remove these timesheet entries."
msgstr ""
"โปรเจกต์นี้มีรายการใบบันทึกเวลาที่อ้างอิง ก่อนลบโปรเจกต์นี้ "
"คุณต้องลบรายการใบบันทึกเวลาหล่านี้ก่อน"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid ""
"This task has some timesheet entries referencing it. Before removing this "
"task, you have to remove these timesheet entries."
msgstr ""
"งานนี้มีรายการใบบันทึกเวลาอ้างอิง ก่อนลบงานนี้ "
"คุณต้องลบรายการใบบันทึกเวลาเหล่านี้ก่อน"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid ""
"This task must be part of a project because there are some timesheets linked"
" to it."
msgstr ""
"งานนี้ต้องเป็นส่วนหนึ่งของโปรเจกต์เนื่องจากมีใบบันทึกเวลาที่เชื่อมโยงอยู่"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "This week"
msgstr "สัปดาห์นี้"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_res_company__project_time_mode_id
#: model:ir.model.fields,help:hr_timesheet.field_res_config_settings__project_time_mode_id
msgid ""
"This will set the unit of measure used in projects and tasks.\n"
"If you use the timesheet linked to projects, don't forget to setup the right unit of measure in your employees."
msgstr ""
"ซึ่งจะกำหนดหน่วยวัดที่ใช้ในโปรเจกต์และงานต่าง ๆ\n"
"หากคุณใช้ใบบันทึกเวลาที่เชื่อมโยงกับโปรเจกต์ อย่าลืมตั้งค่าหน่วยวัดที่เหมาะสมให้กับพนักงานของคุณ"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_project__timesheet_encode_uom_id
#: model:ir.model.fields,help:hr_timesheet.field_res_company__timesheet_encode_uom_id
#: model:ir.model.fields,help:hr_timesheet.field_res_config_settings__timesheet_encode_uom_id
msgid ""
"This will set the unit of measure used to encode timesheet. This will simply provide tools\n"
"        and widgets to help the encoding. All reporting will still be expressed in hours (default value)."
msgstr ""
"ซึ่งจะกำหนดหน่วยวัดที่ใช้ในการเข้ารหัสใบบันทึกเวลา ซึ่งก็จะให้เครื่องมือ\n"
"        และวิดเจ็ตเพื่อช่วยในการเข้ารหัส การรายงานทั้งหมดจะยังคงแสดงเป็นชั่วโมง (ค่าเริ่มต้น)"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "This year"
msgstr "ปีนี้"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__time_spent
msgid "Time"
msgstr "เวลา"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Time Encoding"
msgstr "การเข้ารหัสเวลา"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__module_project_timesheet_holidays
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Time Off"
msgstr "การลา"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "Time Spent (Days)"
msgstr "เวลาที่ใช้ (วัน)"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "Time Spent (Hours)"
msgstr "เวลาที่ใช้ (ชั่วโมง)"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__subtask_effective_hours
msgid "Time spent on the sub-tasks (and their own sub-tasks) of this task."
msgstr "เวลาที่ใช้ในงานย่อย (และงานย่อยของตัวเอง) ของงานนี้"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__effective_hours
msgid "Time spent on this task, excluding its sub-tasks."
msgstr "เวลาที่ใช้ในงานนี้ ไม่รวมงานย่อย"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__total_hours_spent
msgid "Time spent on this task, including its sub-tasks."
msgstr "เวลาที่ใช้ในงานนี้ รวมถึงงานย่อย"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Time unit used to record your timesheets"
msgstr "หน่วยเวลาที่ใช้ในการบันทึกใบบันทึกเวลาของคุณ"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_all
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_my
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_my_timesheet_line_pivot
msgid "Timesheet"
msgstr "ใบบันทึกเวลา"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_tree
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Timesheet Activities"
msgstr "กิจกรรมใบบันทึกเวลา"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee__timesheet_cost
msgid "Timesheet Cost"
msgstr "ต้นทุนใบบันทึกเวลา"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_all
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_my
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_pivot
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_my_timesheet_line_pivot
msgid "Timesheet Costs"
msgstr "ต้นทุนใบบันทึกเวลา"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__timesheet_count
msgid "Timesheet Count"
msgstr "จำนวนใบบันทึกเวลา"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__timesheet_encode_uom_id
#: model:ir.model.fields,field_description:hr_timesheet.field_res_company__timesheet_encode_uom_id
msgid "Timesheet Encoding Unit"
msgstr "หน่วยเข้ารหัสใบบันทึกเวลา"

#. module: hr_timesheet
#: model:ir.actions.report,name:hr_timesheet.timesheet_report
#: model:ir.actions.report,name:hr_timesheet.timesheet_report_project
#: model:ir.actions.report,name:hr_timesheet.timesheet_report_task
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_project_task_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet_project
msgid "Timesheet Entries"
msgstr "รายการใบบันทึกเวลา"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "Timesheet by Date"
msgstr "ใบบันทึกเวลาตามวัน"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#: code:addons/hr_timesheet/models/project.py:0
#: model:ir.actions.act_window,name:hr_timesheet.act_hr_timesheet_line_by_project
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_from_employee
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__allow_timesheets
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__timesheet_ids
#: model:ir.ui.menu,name:hr_timesheet.menu_hr_time_tracking
#: model:ir.ui.menu,name:hr_timesheet.menu_timesheets_reports_timesheet
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_root
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_layout
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_home_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_task
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_invoice_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_pivot
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_project_kanban_inherited
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
#, python-format
msgid "Timesheets"
msgstr "ใบบันทึกเวลา"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Timesheets Control"
msgstr "ควบคุมใบบันทึกเวลา"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.act_hr_timesheet_report
msgid "Timesheets by Employee"
msgstr "ใบบันทึกเวลาตามพนักงาน"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_report_by_project
msgid "Timesheets by Project"
msgstr "ใบบันทึกเวลาตามโปรเจกต์"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_report_by_task
msgid "Timesheets by Task"
msgstr "ใบบันทึกเวลาตามงาน"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__allow_timesheets
msgid "Timesheets can be logged on this task."
msgstr "สามารถบันทึกใบบันทึกเวลาในงานนี้"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid "Timesheets must be created with an active employee."
msgstr ""

#. module: hr_timesheet
#: model:digest.tip,name:hr_timesheet.digest_tip_hr_timesheet_0
msgid "Tip: Record your Timesheets faster"
msgstr "ทิป: บันทึกใบบันทึกเวลาของคุณเร็วขึ้น"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Today"
msgstr "วันนี้"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_tree
msgid "Total"
msgstr "รวม"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Total Days"
msgstr "วันทั้งหมด"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__total_hours_spent
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Total Hours"
msgstr "ชั่วโมงทั้งหมด"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__total_timesheet_time
msgid "Total Timesheet Time"
msgstr "เวลาในใบบันทึกเวลารวม"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_project__total_timesheet_time
msgid ""
"Total number of time (in the proper UoM) recorded in the project, rounded to"
" the unit."
msgstr ""
"จำนวนเวลาทั้งหมด (ใน UoM ที่เหมาะสม) ที่บันทึกในโปรเจกต์และปัดเศษเป็นหน่วย"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__remaining_hours
msgid ""
"Total remaining time, can be re-estimated periodically by the assignee of "
"the task."
msgstr "เวลาที่เหลือทั้งหมด สามารถประเมินใหม่เป็นระยะโดยผู้รับมอบหมายงาน"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "Total:"
msgstr "ทั้งหมด:"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Track your time from anywhere, even offline, with our web/mobile apps"
msgstr ""
"ติดตามเวลาของคุณได้จากทุกที่ แม้กระทั่งออฟไลน์ ด้วยเว็บ/แอปมือถือของเรา"

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line_by_project
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_report
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_all
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_project
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_task
msgid ""
"Track your working hours by projects every day and invoice this time to your"
" customers."
msgstr ""
"ติดตามชั่วโมงทำงานของคุณตามโปรเจกต์ทุกวัน "
"และออกใบแจ้งหนี้ตามเวลานี้ให้กับลูกค้าของคุณ"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/res_company.py:0
#, python-format
msgid "Training"
msgstr "อบรม"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_project__has_planned_hours_tasks
msgid "True if any of the project's task has a set planned hours"
msgstr "เป็นจริง ถ้างานของโปรเจกต์มีการตั้งค่าชั่วโมงที่วางแผนไว้"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__user_id
msgid "User"
msgstr "ผู้ใช้งาน"

#. module: hr_timesheet
#: model:res.groups,name:hr_timesheet.group_hr_timesheet_approver
msgid "User: all timesheets"
msgstr "ผู้ใช้: ใบบันทึกเวลาทั้งหมด"

#. module: hr_timesheet
#: model:res.groups,name:hr_timesheet.group_hr_timesheet_user
msgid "User: own timesheets only"
msgstr "ผู้ใช้: ใบบันทึกเวลาที่เป็นเจ้าของเท่านั้น"

#. module: hr_timesheet
#. openerp-web
#: code:addons/hr_timesheet/static/src/js/timesheet_config_form_view.js:0
#, python-format
msgid "View App"
msgstr "ดูแอป"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_uom_uom__timesheet_widget
msgid "Widget"
msgstr "วิดเจ็ต"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_uom_uom__timesheet_widget
msgid ""
"Widget used in the webclient when this unit is the one used to encode "
"timesheets."
msgstr ""
"วิดเจ็ตที่ใช้ในเว็บไคลเอ็นต์เมื่อหน่วยนี้เป็นหน่วยที่ใช้ในการเข้ารหัสใบบันทึกเวลา"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid "You cannot access timesheets that are not yours."
msgstr "คุณไม่สามารถเข้าถึงใบบันทึกเวลาที่ไม่ใช่ของคุณ"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid ""
"You cannot add timesheets to a project linked to an inactive analytic "
"account."
msgstr ""
"คุณไม่สามารถเพิ่มใบบันทึกเวลาไปยังโปรเจกต์ที่เชื่อมโยงกับบัญชีการวิเคราะห์ที่ไม่ได้ใช้งาน"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid ""
"You cannot add timesheets to a project or a task linked to an inactive "
"analytic account."
msgstr ""
"คุณไม่สามารถเพิ่มใบบันทึกเวลาในโปรเจกต์หรืองานที่เชื่อมโยงกับบัญชีการวิเคราะห์ที่ไม่ใช้งาน"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid ""
"You cannot log timesheets on this project since it is linked to an inactive "
"analytic account. Please change this account, or reactivate the current one "
"to timesheet on the project."
msgstr ""
"คุณไม่สามารถบันทึกใบบันทึกเวลาในโปรเจกต์นี้ได้ "
"เนื่องจากมีการเชื่อมโยงกับบัญชีการวิเคราะห์ที่ไม่ได้ใช้งาน "
"โปรดเปลี่ยนบัญชีนี้ "
"หรือเปิดใช้งานบัญชีปัจจุบันในใบบันทึกเวลาของโปรเจกต์อีกครั้ง"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid "You cannot set an archived employee to the existing timesheets."
msgstr ""
"คุณไม่สามารถตั้งค่าพนักงานที่ถูกเก็บถาวรแล้วเป็นใบบันทึกเวลาที่มีอยู่ได้"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid "You cannot use timesheets without an analytic account."
msgstr "คุณไม่สามารถใช้ใบบันทึกเวลาได้หากไม่มีบัญชีวิเคราะห์"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/res_company.py:0
#, python-format
msgid "days"
msgstr "วัน"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_project_task_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet_project
msgid "for the"
msgstr "สำหรับ"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/res_company.py:0
#, python-format
msgid "hours"
msgstr "ชั่วโมง"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_view_form_inherit_timesheet
msgid "per hour"
msgstr "ต่อชั่วโมง"
