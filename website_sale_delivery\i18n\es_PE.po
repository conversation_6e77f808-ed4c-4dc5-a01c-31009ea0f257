# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_sale_delivery
#
# Translators:
# <PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2016-06-16 20:05+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Peru) (http://www.transifex.com/odoo/odoo-9/language/"
"es_PE/)\n"
"Language: es_PE\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.cart_delivery
msgid ""
"<span class=\"col-xs-6 text-right text-muted\" title=\"Delivery will be "
"updated after choosing a new delivery method\"> Delivery:</span>"
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier_accessory_product_ids
msgid "Accessory Products"
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,help:website_sale_delivery.field_delivery_carrier_alternative_product_ids
msgid "Appear on the product page"
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,help:website_sale_delivery.field_delivery_carrier_accessory_product_ids
msgid "Appear on the shopping cart"
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier_available_in_pos
msgid "Available in the Point of Sale"
msgstr "Disponible en el Punto de Venta"

#. module: website_sale_delivery
#: model:ir.model,name:website_sale_delivery.model_delivery_carrier
msgid "Carrier"
msgstr "Portador"

#. module: website_sale_delivery
#: model:ir.model.fields,help:website_sale_delivery.field_delivery_carrier_to_weight
msgid ""
"Check if the product should be weighted using the hardware scale integration"
msgstr ""
"Marque si el producto debe ser pesado usando la integración hardware de la "
"balanza"

#. module: website_sale_delivery
#: model:ir.model.fields,help:website_sale_delivery.field_delivery_carrier_available_in_pos
msgid "Check if you want this product to appear in the Point of Sale"
msgstr "Marque esta casilla si quiere que este producto aparezca en el TPV"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.payment_delivery
msgid "Choose your Delivery Method"
msgstr ""

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.country_state_shipping
msgid "Country..."
msgstr "País..."

#. module: website_sale_delivery
#: model:ir.model.fields,help:website_sale_delivery.field_delivery_carrier_project_id
msgid "Create a task under this project on sale order validation."
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_sale_order_amount_delivery
msgid "Delivery Amount"
msgstr ""

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.view_delivery_carrier_form_website_delivery
msgid "Description"
msgstr "Descripción"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.view_delivery_carrier_form_website_delivery
msgid "Description displayed on the eCommerce and on the Online Quotations."
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier_website_description
msgid "Description for Online Quotations"
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier_quote_description
msgid "Description for the quote"
msgstr "Descripción para la cotización"

#. module: website_sale_delivery
#: model:ir.model.fields,help:website_sale_delivery.field_delivery_carrier_website_sequence
msgid "Determine the display order in the Website E-commerce"
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,help:website_sale_delivery.field_delivery_carrier_split_method
msgid ""
"Equal : Cost will be equally divided.\n"
"By Quantity : Cost will be divided according to product's quantity.\n"
"By Current cost : Cost will be divided according to product's current cost.\n"
"By Weight : Cost will be divided depending on its weight.\n"
"By Volume : Cost will be divided depending on its volume."
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier_attachment_count
msgid "File"
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,help:website_sale_delivery.field_sale_order_has_delivery
msgid "Has an order line set for delivery"
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_sale_order_has_delivery
msgid "Has delivery"
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier_intrastat_id
msgid "Intrastat code"
msgstr "Código Intrastat"

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier_landed_cost_ok
msgid "Landed Costs"
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier_purchase_line_warn_msg
msgid "Message for Purchase Order Line"
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier_sale_line_warn_msg
msgid "Message for Sales Order Line"
msgstr ""

#. module: website_sale_delivery
#: code:addons/website_sale_delivery/models/sale_order.py:138
#, python-format
msgid ""
"No shipping method is available for your current order and shipping address. "
"Please contact us for more information."
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier_optional_product_ids
msgid "Optional Products"
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier_pos_categ_id
msgid "Point of Sale Category"
msgstr "Categoría de Punto de Venta"

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier_purchase_requisition
msgid "Procurement"
msgstr "Requerimiento"

#. module: website_sale_delivery
#: model:ir.model.fields,help:website_sale_delivery.field_delivery_carrier_optional_product_ids
msgid "Products to propose when add to cart."
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier_project_id
msgid "Project"
msgstr "Proyecto"

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier_purchase_line_warn
msgid "Purchase Order Line"
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier_rating_ids
msgid "Rating"
msgstr "Clasificación"

#. module: website_sale_delivery
#: model:ir.model,name:website_sale_delivery.model_sale_order
msgid "Sales Order"
msgstr "Pedidos de Venta"

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier_sale_line_warn
msgid "Sales Order Line"
msgstr "Línea de Pedido de Venta"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.country_state_shipping
msgid "Select..."
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,help:website_sale_delivery.field_delivery_carrier_purchase_line_warn
#: model:ir.model.fields,help:website_sale_delivery.field_delivery_carrier_sale_line_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier_website_sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier_website_size_x
msgid "Size X"
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier_website_size_y
msgid "Size Y"
msgstr ""

#. module: website_sale_delivery
#: code:addons/website_sale_delivery/models/sale_order.py:137
#, python-format
msgid "Sorry, we are unable to ship your order"
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier_split_method
msgid "Split Method"
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier_website_style_ids
msgid "Styles"
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier_alternative_product_ids
msgid "Suggested Products"
msgstr "Productos Sugeridos"

#. module: website_sale_delivery
#: model:ir.model.fields,help:website_sale_delivery.field_sale_order_amount_delivery
msgid "The amount without tax."
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,help:website_sale_delivery.field_delivery_carrier_public_categ_ids
msgid "Those categories are used to group similar products for e-commerce."
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,help:website_sale_delivery.field_delivery_carrier_pos_categ_id
msgid "Those categories are used to group similar products for point of sale."
msgstr ""
"Esas categorías son usadas para grupos similares de productos para el punto "
"de venta."

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier_to_weight
msgid "To Weigh With Scale"
msgstr "Para Pesar con Balanza"

#. module: website_sale_delivery
#: model_terms:ir.ui.view,arch_db:website_sale_delivery.shipping_tracking
msgid "Tracking:"
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier_public_categ_ids
msgid "Website Product Category"
msgstr ""

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier_website_meta_description
msgid "Website meta description"
msgstr "Descripción meta del sitio web"

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier_website_meta_keywords
msgid "Website meta keywords"
msgstr "Palabras clave meta del sitio web"

#. module: website_sale_delivery
#: model:ir.model.fields,field_description:website_sale_delivery.field_delivery_carrier_website_meta_title
msgid "Website meta title"
msgstr "Título meta del sitio web"

#~ msgid "The full URL to access the document through the website."
#~ msgstr "La URL completa para acceder al documento a través del sitio web"

#~ msgid "Visible in Website"
#~ msgstr "Visible en Sitio Web"

#~ msgid "Website URL"
#~ msgstr "URL del Sitio Web"
