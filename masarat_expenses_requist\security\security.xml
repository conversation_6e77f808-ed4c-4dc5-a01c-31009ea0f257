<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record model="ir.module.category" id="module_finance_requst_masarat">
        <field name="name">Masarat BT Cash Approvals Forms</field>
        <field name="description">Masarat BT Cash Approvals Forms</field>
        <field name="sequence">45</field>
    </record>

    <record id="group_finance_manager" model="res.groups">
        <field name="name">Finance Manager</field>
        <field name="category_id" ref="module_finance_requst_masarat"/>
    </record>

    <record id="group_finance_approvales" model="res.groups">
        <field name="name">Finance</field>
        <field name="category_id" ref="module_finance_requst_masarat"/>
    </record>

    <record id="group_employee_finance_approvales" model="res.groups">
        <field name="name">Employee</field>
        <field name="category_id" ref="module_finance_requst_masarat"/>
    </record>

    <record model="ir.rule" id="finance_requst_records_rule">
        <field name="name">Masarat BT Cash Approvals Rules</field>
        <field name="model_id" ref="model_hr_masarat_expense"/>
        <field name="domain_force">["|", ("manager_id.user_id","=",user.id), ('employee_id.user_id','=',user.id) ]</field>
        <field name="groups" eval="[(4, ref('group_employee_finance_approvales'))]"/>
    </record>

</odoo>