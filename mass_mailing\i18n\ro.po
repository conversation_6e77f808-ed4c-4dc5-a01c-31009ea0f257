# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mass_mailing
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-02-01 17:39+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "$18"
msgstr "$18"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount2
msgid "$20"
msgstr "$20"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "% of recipients"
msgstr "% din destinatari"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "% of recipients."
msgstr "% din destinatari."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "% receive one of the"
msgstr "% primesc una dintre"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mass_mailing_kpi_link_trackers
msgid "%Click (Total)"
msgstr "% Click (total)"

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#: code:addons/mass_mailing/models/mailing_list.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (copie)"

#. module: mass_mailing
#: code:addons/mass_mailing/wizard/mailing_contact_to_list.py:0
#, python-format
msgid "%s Mailing Contacts have been added. "
msgstr "%s Contactele de mailing au fost adăugate. "

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_steps
msgid ""
"&amp;nbsp;\n"
"                                        <span class=\"fa fa-compass fa-2x text-o-color-4\" role=\"img\" aria-label=\"Choose\" title=\"Choose\"/>\n"
"                                        &amp;nbsp;"
msgstr ""
"&amp;nbsp;\n"
"                                        <span class=\"fa fa-compass fa-2x text-o-color-4\" role=\"img\" aria-label=\"Choose\" title=\"Choose\"/>\n"
"                                        &amp;nbsp;"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_steps
msgid ""
"&amp;nbsp;\n"
"                                        <span class=\"fa fa-credit-card fa-2x text-o-color-4\" role=\"img\" aria-label=\"Order\" title=\"Order\"/>\n"
"                                        &amp;nbsp;"
msgstr ""
"&amp;nbsp;\n"
"                                        <span class=\"fa fa-credit-card fa-2x text-o-color-4\" role=\"img\" aria-label=\"Order\" title=\"Order\"/>\n"
"                                        &amp;nbsp;"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_steps
msgid ""
"&amp;nbsp;\n"
"                                        <span class=\"fa fa-smile-o fa-2x text-o-color-4\" role=\"img\" aria-label=\"Enjoy\" title=\"Enjoy\"/>\n"
"                                        &amp;nbsp;"
msgstr ""
"&amp;nbsp;\n"
"                                        <span class=\"fa fa-smile-o fa-2x text-o-color-4\" role=\"img\" aria-label=\"Enjoy\" title=\"Enjoy\"/>\n"
"                                        &amp;nbsp;"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.social_links
msgid "&amp;nbsp;&amp;nbsp;"
msgstr "&amp;nbsp;&amp;nbsp;"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid ", and"
msgstr ", și"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount1
msgid "-20%"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "100%"
msgstr "100%"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_event
msgid "21 Jul"
msgstr "21 Jul"

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "24H Stats of %(mailing_type)s \"%(mailing_name)s\""
msgstr "24H Statistici de %(mailing_type)s \"%(mailing_name)s\""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "25%"
msgstr "25%"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "400px"
msgstr "400px"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "50%"
msgstr "50%"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "75%"
msgstr "75%"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "800px"
msgstr "800px"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_call_to_action
msgid "<b>50,000+ companies</b> run Odoo to grow their businesses."
msgstr ""
"Peste <b>50.000 de companii</b> folosesc Odoo pentru a-și dezvolta "
"afacerile."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert
msgid ""
"<b>Explain the benefits you offer</b>\n"
"            <br/>Don't write about products or services here, write about solutions."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_coupon_code
msgid "<b>GET $20 OFF</b>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_title
msgid "<font style=\"font-size: 42px;\">Your Title</font>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_picture
msgid ""
"<font style=\"font-size: 48px;\" class=\"o_default_snippet_text\">A punchy "
"Headline</font>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_subscription_view_form
msgid ""
"<i class=\"fa fa-ban text-danger\" role=\"img\" title=\"This email is "
"blacklisted for mass mailings\" aria-label=\"Blacklisted\" "
"attrs=\"{'invisible': [('is_blacklisted', '=', False)]}\" "
"groups=\"base.group_user\"/>"
msgstr ""
"<i class=\"fa fa-ban text-danger\" role=\"img\" title=\"This email is "
"blacklisted for mass mailings\" aria-label=\"Blacklisted\" "
"attrs=\"{'invisible': [('is_blacklisted', '=', False)]}\" "
"groups=\"base.group_user\"/>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<i class=\"fa fa-bar-chart\"/> Compare Version"
msgstr "<i class=\"fa fa-bar-chart\"/> Comparați Versiunea"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<i class=\"fa fa-copy\"/> Create an Alternative"
msgstr "<i class=\"fa fa-copy\"/> Creați o Alternativă"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"<i class=\"fa fa-envelope\"/> <span name=\"ab_test_manual\" attrs=\"{'invisible': [('ab_testing_winner_selection', '!=', 'manual')]}\">\n"
"                                                    Send this version to remaining recipients\n"
"                                                </span> <span name=\"ab_test_auto\" attrs=\"{'invisible': [('ab_testing_winner_selection', '=', 'manual')]}\">\n"
"                                                    Send Winner Now\n"
"                                                </span>"
msgstr ""
"<i class=\"fa fa-envelope\"/> <span name=\"ab_test_manual\" attrs=\"{'invisible': [('ab_testing_winner_selection', '!=', 'manual')]}\">\n"
"                                                    Trimiteți această versiune la destinatari rămași\n"
"                                                </span> <span name=\"ab_test_auto\" attrs=\"{'invisible': [('ab_testing_winner_selection', '=', 'manual')]}\">\n"
"                                                    Trimiteți câștigătorul acum\n"
"                                                </span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<i class=\"fa fa-envelope\"/> Send this as winner"
msgstr "<i class=\"fa fa-envelope\"/> Trimiteți acesta ca câștigător"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_kanban
msgid ""
"<i class=\"fa fa-exclamation-triangle\" role=\"img\" aria-label=\"Warning\" "
"title=\"Warning\"/>"
msgstr ""
"<i class=\"fa fa-exclamation-triangle\" role=\"img\" aria-label=\"Warning\" "
"title=\"Warning\"/>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<i class=\"fa fa-fw fa-circle\"/> Circles"
msgstr "<i class=\"fa fa-fw fa-circle\"/>Cercuri"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<i class=\"fa fa-fw fa-heart\"/> Hearts"
msgstr "<i class=\"fa fa-fw fa-heart\"/> Inimi"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<i class=\"fa fa-fw fa-refresh mr-1\"/> Replace Icon"
msgstr "<i class=\"fa fa-fw fa-refresh mr-1\"/>Înlocuiți pictograma"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<i class=\"fa fa-fw fa-square\"/> Squares"
msgstr "<i class=\"fa fa-fw fa-square\"/> Pătrate"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<i class=\"fa fa-fw fa-star\"/> Stars"
msgstr "<i class=\"fa fa-fw fa-star\"/> Stele"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<i class=\"fa fa-fw fa-thumbs-up\"/> Thumbs"
msgstr "<i class=\"fa fa-fw fa-thumbs-up\"/> Degete"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_blockquote
msgid ""
"<i>Write a quote here from one of your customers. Quotes are a great way to "
"build confidence in your products or services.</i>"
msgstr ""
"<i>Scrieți o citată aici de la unul dintre clienții dvs. Citații sunt o "
"excelentă modalitate de a construi încrederea în produsele sau serviciile "

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid ""
"<small class=\"o_default_snippet_text\">user / month (billed "
"annually)</small>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"<small class=\"oe_edit_only text-muted mb-2\" style=\"font-size:74%\" attrs=\"{'invisible': ['|', ('reply_to_mode', '=', 'update'), ('mailing_model_name', 'in', ['mailing.contact', 'res.partner', 'mailing.list'])],}\">\n"
"                                                    To track replies, this address must belong to this database.\n"
"                                                </small>"
msgstr ""
"<small class=\"oe_edit_only text-muted mb-2\" style=\"font-size:74%\" attrs=\"{'invisible': ['|', ('reply_to_mode', '=', 'update'), ('mailing_model_name', 'in', ['mailing.contact', 'res.partner', 'mailing.list'])],}\">\n"
"                                                    Pentru a urmări răspunsurile, această adresă trebuie să aparțină acestei baze de date.\n"
"                                                </small>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_title_text
msgid ""
"<small>\n"
"                <strong>Michael Fletcher</strong><br/>\n"
"                <small>Community Manager</small>\n"
"            </small>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_steps
msgid "<small>Step 1:</small>"
msgstr "<small>Pas 1:</small>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_steps
msgid "<small>Step 2:</small>"
msgstr "<small>Pas 2:</small>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_steps
msgid "<small>Step 3:</small>"
msgstr "<small>Pasul 3:</small>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "<small>user / month (billed annually)</small>"
msgstr "<small>utilizator / lună (facturat anual)</small>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid ""
"<span attrs=\"{'invisible': "
"[('mailing_model_name','!=','mailing.list')]}\">Mailing Contact</span>"
msgstr ""
"<span attrs=\"{'invisible': "
"[('mailing_model_name','!=','mailing.list')]}\">Contact Corespondență</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid ""
"<span class=\"fa fa-calendar-check-o mr-2 small my-auto\" aria-label=\"Sent "
"date\"/>"
msgstr ""
"<span class=\"fa fa-calendar-check-o mr-2 small my-auto\" aria-label=\"Sent "
"date\"/>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_social
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_social_left
msgid ""
"<span class=\"fa fa-copyright\" role=\"img\" aria-label=\"Copyright\" "
"title=\"Copyright\"/>"
msgstr ""
"<span class=\"fa fa-copyright\" role=\"img\" aria-label=\"Copyright\" "
"title=\"Copyright\"/>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid ""
"<span class=\"fa fa-hourglass-half mr-2 small my-auto\" aria-"
"label=\"Scheduled date\"/>"
msgstr ""
"<span class=\"fa fa-hourglass-half mr-2 small my-auto\" aria-"
"label=\"Scheduled date\"/>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid ""
"<span class=\"fa fa-hourglass-o mr-2 small my-auto\" aria-label=\"Scheduled date\"/>\n"
"                                            <span class=\"align-self-baseline\">Next Batch</span>"
msgstr ""
"<span class=\"fa fa-hourglass-o mr-2 small my-auto\" aria-label=\"Scheduled date\"/>\n"
"                                            <span class=\"align-self-baseline\">Următorul Lot</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "<span class=\"mx-2\">/</span>"
msgstr "<span class=\"mx-2\">/</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid ""
"<span class=\"o_default_snippet_text\" style=\"font-size:18px; font-weight: "
"500;\">DEFAULT</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid ""
"<span class=\"o_default_snippet_text\" style=\"font-size:18px; font-weight: "
"500;\">PRO</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
msgid "<span class=\"o_stat_text\">Blacklist</span>"
msgstr "<span class=\"o_stat_text\">Listă neagră</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
msgid "<span class=\"o_stat_text\">Bounce</span>"
msgstr "<span class=\"o_stat_text\">Răspunsuri</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
msgid "<span class=\"o_stat_text\">Opt-out</span>"
msgstr "<span class=\"o_stat_text\">Opt-out</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
msgid "<span class=\"o_stat_value\">%</span>"
msgstr "<span class=\"o_stat_value\">%</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_blockquote
msgid ""
"<span class=\"s_blockquote_author\"><b>John DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>John DOE </b> • CEO al Companiei Mele"
" </span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_numbers
msgid "<span class=\"s_number display-4 o_default_snippet_text\">12</span><br/>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_numbers
msgid "<span class=\"s_number display-4 o_default_snippet_text\">45</span><br/>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_numbers
msgid "<span class=\"s_number display-4 o_default_snippet_text\">8</span><br/>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "<span class=\"text-muted\">Blacklist</span>"
msgstr "<span class=\"text-muted\">Listă neagră</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "<span class=\"text-muted\">Bounce</span>"
msgstr "<span class=\"text-muted\">Răspunsuri</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "<span class=\"text-muted\">Mailings</span>"
msgstr "<span class=\"text-muted\">Corespondențe</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "<span class=\"text-muted\">Opt-out</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"<span name=\"canceled_text\">emails have been canceled and will not be "
"sent.</span>"
msgstr ""
"<span name=\"canceled_text\">email-uri au fost anulate și nu vor fi "
"trimise.</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_form_full_width
msgid "<span name=\"failed_text\">email(s) not sent.</span>"
msgstr "<span name=\"failed_text\">email-uri nu sunt trimise.</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<span name=\"failed_text\">emails could not be sent.</span>"
msgstr "<span name=\"failed_text\">e-mailurile nu au putut fi trimise.</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<span name=\"next_departure_text\">This mailing is scheduled for </span>"
msgstr ""
"<span name=\"next_departure_text\">Această corespondență este programată "
"pentru </span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_form_full_width
msgid "<span name=\"scheduled_text\">email(s) scheduled for </span>"
msgstr "<span name=\"scheduled_text\">email-uri programate pentru </span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"<span name=\"scheduled_text\">emails are in queue and will be sent "
"soon.</span>"
msgstr ""
"<span name=\"scheduled_text\">e-mailurile sunt în coadă și vor fi trimise în"
" curând.</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "<span name=\"sent\">emails have been sent.</span>"
msgstr "<span name=\"sent\">e-mailurile au fost trimise .</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount2
msgid ""
"<span style=\"line-height: 30px;\"><small>CODE: </small></span><strong "
"class=\"o_code h3 oe_unremovable\">45A9E77DGW8455</strong>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
msgid "<span widget=\"statinfo\">Open Recipient</span>"
msgstr "<span widget=\"statinfo\">Deschide destinatarul</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
msgid "<span>%</span>"
msgstr "<span>%</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "<span>Contacts</span>"
msgstr "<span>Contacte</span>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "<span>Select a template</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_kanban
msgid "<span>Valid Email Recipients</span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "<strong class=\"o_default_snippet_text\">24/7 Support</strong>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid ""
"<strong class=\"o_default_snippet_text\">Advanced</strong>\n"
"                                features"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid "<strong class=\"o_default_snippet_text\">Fully customizable</strong>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
msgid ""
"<strong class=\"o_default_snippet_text\">Total</strong>\n"
"                                management"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "<strong>24/7 Support</strong>"
msgstr "<strong>Suport 24/7 </strong>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "<strong>Advanced</strong> features"
msgstr "<strong>Caracteristici</strong> Avansate"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "<strong>Fully customizable</strong>"
msgstr "<strong>Complet personalizabile</strong>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "<strong>Total</strong> management"
msgstr "<strong>Total</strong> management"

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "A campaign should be set when A/B test is enabled"
msgstr "O campanie trebuie să fie setată când testul A/B este activat"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_color_blocks_2
msgid "A color block"
msgstr "Un bloc de culori"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_image_text_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_text_image_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_text_image_text_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_text_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_default_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_image_texts_image_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_mosaic_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_reversed_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_texts_image_texts_template
msgid "A great title"
msgstr "Un titlu minunat"

#. module: mass_mailing
#: model:ir.model.constraint,message:mass_mailing.constraint_mailing_contact_list_rel_unique_contact_list
msgid ""
"A mailing contact cannot subscribe to the same mailing list multiple times."
msgstr ""
"Un contact poștal nu se poate abona la aceeași listă poștală de mai multe "
"ori."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "A sample of"
msgstr "Un eșantion de"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_three_cols
msgid "A short description"
msgstr "Adaugă o scurtă descriere..."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_showcase
msgid "A short description of this great feature."
msgstr "O scurtă descriere a acestei funcții minunate."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features
msgid "A small explanation of this great <br/>feature, in clear words."
msgstr ""
"O mică explicație a acestei minunate<br/>caracteristici , în cuvinte clare. "

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_text_image
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_image
msgid "A unique value"
msgstr "O valoare unică"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "A/B Test"
msgstr "Test A/B"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_mailings_count
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__ab_testing_mailings_count
msgid "A/B Test Mailings #"
msgstr "Numărul de mailings de test A/B"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_ab_testing_open_winner_mailing
msgid "A/B Test Winner"
msgstr "Câștigătorul testului A/B"

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "A/B Test: %s"
msgstr "Test A/B: %s"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_completed
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__ab_testing_completed
msgid "A/B Testing Campaign Finished"
msgstr "Campania de testare A/B a fost finalizată"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_description
msgid "A/B Testing Description"
msgstr "Descrierea testului A/B"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_pc
msgid "A/B Testing percentage"
msgstr "A / B Procent de testare"

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
#, python-format
msgid "A/B Tests"
msgstr "Teste A/B"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "A/B Tests to review"
msgstr "Teste A/B de revizuit"

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "A/B test option has not been enabled"
msgstr "Opțiunea de testare A/B nu a fost activată"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_event
msgid "ALL DAY"
msgstr "TOATĂ ZIUA"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_needaction
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_needaction
msgid "Action Needed"
msgstr "Intervenție necesară"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__active
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__active
msgid "Active"
msgstr "Activ"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_ids
msgid "Activities"
msgstr "Activități"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Activitate Excepție Decorare"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_state
msgid "Activity State"
msgstr "Stare activitate"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_type_icon
msgid "Activity Type Icon"
msgstr "Pictograma tipului de activitate"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_three_columns
msgid ""
"Adapt these three columns to fit your design need. To duplicate, delete or "
"move columns, select the column and use the top icons to perform your "
"action."
msgstr ""
"Adaptați aceste trei coloane pentru a se potrivi nevoilor dvs. de design. "
"Pentru a duplica, șterge sau muta coloane, selectați coloana și utilizați "
"pictogramele de sus pentru a efectua acțiunea dvs."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_to_list_view_form
msgid "Add"
msgstr "Adaugă"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_contact_to_list
msgid "Add Contacts to Mailing List"
msgstr "Adăugați contacte la lista de corespondență"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mailing_contact_to_list_action
msgid "Add Selected Contacts to a Mailing List"
msgstr "Adăugați contactele selectate la o listă de corespondență"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_picture
msgid "Add a caption to enhance the meaning of this image."
msgstr "Adăugați o legendă pentru a spori semnificația acestei imagini."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Add a great slogan."
msgstr "Adăugați un slogan minunat."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_to_list_view_form
msgid "Add and Send Mailing"
msgstr "Adăugați și trimiteți corespondența"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_tree
msgid "Add to List"
msgstr "Adăugați la listă"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Advanced"
msgstr "Avansat"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Align Bottom"
msgstr "Aliniați partea de jos"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Align Middle"
msgstr "Aliniați la mijloc"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Align Top"
msgstr "Aliniați partea de sus"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Alignment"
msgstr "Aliniament"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid "Aline Turner, CTO"
msgstr "Aline Turner, CTO"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid ""
"Aline is one of the iconic people in life who can say they love what they "
"do. She mentors 100+ in-house developers and looks after the community of "
"thousands of developers."
msgstr ""
"Aline este una dintre persoanele iconice din viață care pot spune că adoră "
"ceea ce fac. Ea mentorează peste 100 de dezvoltatori interni și are grijă de"
" comunitatea a mii de dezvoltatori."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_social
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_social_left
msgid "All Rights Reserved"
msgstr "Toate Drepturile Rezervate"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "All these icons are completely free for commercial use."
msgstr ""
"Toate aceste icoane sunt complet gratuite pentru utilizare comercială."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_enabled
msgid "Allow A/B Testing"
msgstr "Permite testare A / B"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid "Allow recipients to blacklist themselves"
msgstr "Permiteți destinatarilor să se pună pe lista neagră"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_res_config_settings__show_blacklist_buttons
msgid ""
"Allow the recipient to manage himself his state in the blacklist via the "
"unsubscription page."
msgstr ""
"Permiteți destinatarului să își gestioneze singur starea în lista neagră "
"prin intermediul paginii de dezabonare."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid ""
"Allow the recipient to manage himself his state in the blacklist via the "
"unsubscription page.                                 If the option is "
"active, the 'Blacklist Me' button is hidden on the unsubscription page."
"                                   The 'come Back' button will always be "
"visible in any case to allow leads and partners to re-subscribe."
msgstr ""
"Permiteți destinatarului să își gestioneze singur starea în lista neagră "
"prin intermediul paginii de dezabonare. Dacă opțiunea este activă, butonul "
"„Pune-mă pe Lista neagră” este ascuns pe pagina de dezabonare. Butonul "
"„Revenire” va fi întotdeauna vizibil în orice caz pentru a permite "
"conducătorilor și partenerilor să se reaboneze."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Alternate Image Text"
msgstr "Text alternativ imagine"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Alternate Text"
msgstr "Text alternativ"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Alternate Text Image"
msgstr "Imagine text alternativ"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Alternate Text Image Text"
msgstr "Imagine text alternativ text"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_numbers
msgid "Amazing pages"
msgstr "Pagini uimitoare"

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#, python-format
msgid "An error occurred. Please try again later or contact us."
msgstr ""
"A apărut o eroare. Vă rugăm să încercați din nou mai târziu sau să ne "
"contactați."

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#, python-format
msgid "An error occurred. Your changes have not been saved, try again later."
msgstr ""
"A aparut o eroare. Modificările dvs. nu au fost salvate, încercați din nou "
"mai târziu."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_image_text_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_text_image_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_text_image_text_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_alternation_text_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_default_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_image_texts_image_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_mosaic_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_reversed_template
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_texts_image_texts_template
msgid "And a great subtitle"
msgstr "Și o subtitrare minunată"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_color_blocks_2
msgid "Another color block"
msgstr "Alt bloc de culoare"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_showcase
msgid "Another feature"
msgstr "O altă caracteristică"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_tag_line
msgid "Apps That Help You Grow Your Business"
msgstr "Aplicații care vă ajută să vă dezvoltați afacerea"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_title_sub
msgid "Apps That Help You Grow Your Business!"
msgstr "Aplicații care vă ajută să vă dezvoltați afacerea!"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Archive"
msgstr "Arhivează"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__archive_src_lists
msgid "Archive source mailing lists"
msgstr "Arhivați sursa listelor de corespondență"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Archived"
msgstr "Arhivat"

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing_list.py:0
#, python-format
msgid ""
"At least one of the mailing list you are trying to archive is used in an "
"ongoing mailing campaign."
msgstr ""
"Cel puțin una dintre listele de e-mail pe care încercați să o arhivați este "
"utilizată într-o campanie de corespondență în curs de desfășurare."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Attach a file"
msgstr "Atașează un fișier"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_attachment_count
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_attachment_count
msgid "Attachment Count"
msgstr "Număr atașamente"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__attachment_ids
msgid "Attachments"
msgstr "Atașamente"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Auto"
msgstr "Auto"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Background Color"
msgstr "Culoare fundal"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "Basic features"
msgstr "Caracteristici de bază"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "Basic management"
msgstr "Management de bază"

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/mass_mailing_snippets.js:0
#, python-format
msgid "Be aware that this option may not work on many mail clients"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_numbers
msgid "Beautiful snippets"
msgstr "Fragmente frumoase"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__is_blacklisted
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_subscription__is_blacklisted
msgid "Blacklist"
msgstr "Lista neagră"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_tree
msgid "Blacklist (%s)"
msgstr "Lista neagră (%s)"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribed
msgid "Blacklist Me"
msgstr " Pune-mă pe Lista neagră"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_res_config_settings__show_blacklist_buttons
msgid "Blacklist Option when Unsubscribing"
msgstr "Opțiune Lista neagră la dezabonare"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Blacklisted"
msgstr "În lista neagră"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_bl
msgid "Blacklisted Address"
msgstr "Adresă listată în lista neagră"

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.mail_blacklist_mm_menu
msgid "Blacklisted Email Addresses"
msgstr "Adrese e-mail listate negre"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__body_arch
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Body"
msgstr "Conținut"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Body Width"
msgstr "Lățime conținut"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__body_html
msgid "Body converted to be sent by mail"
msgstr "Corp convertit pentru a fi trimis prin poștă"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_product_list
msgid "Books"
msgstr "Cărți"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options_border_widgets
msgid "Border"
msgstr "Margine"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_bounce
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_subscription__message_bounce
msgid "Bounce"
msgstr "Salt"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_tree
msgid "Bounce (%)"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__bounced
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__bounced
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__bounce
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Bounced"
msgstr "Respins"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Bounced (%)"
msgstr "Sărit (%)"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__bounced_ratio
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__bounced_ratio
msgid "Bounced Ratio"
msgstr "Ratio Respingere"

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "Business Benefits on %(expected)i %(mailing_type)s Sent"
msgstr "Beneficii de afaceri pe %(expected)i %(mailing_type)s trimis"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mass_mailing_kpi_link_trackers
msgid "Button Label"
msgstr "Etichetă buton"

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "By using the <b>Breadcrumb</b>, you can navigate back to the overview."
msgstr ""
"Folosind <b>Breadcrumb</b>, puteți naviga înapoi la prezentare generală."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__calendar_date
msgid "Calendar Date"
msgstr "Dată Calendar"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__campaign_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Campaign"
msgstr "Campanie"

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.menu_view_mass_mailing_stages
msgid "Campaign Stages"
msgstr "Stadii campanie"

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.mass_mailing_tag_menu
msgid "Campaign Tags"
msgstr "Etichete Campanie"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_utm_campaigns
#: model:ir.ui.menu,name:mass_mailing.menu_email_campaigns
msgid "Campaigns"
msgstr "Campanii"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_utm_campaigns
msgid ""
"Campaigns are the perfect tool to track results across multiple mailings."
msgstr ""
"Campaniile sunt instrumentul perfect pentru a urmări rezultatele pe "
"mailinguri multiple"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_to_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_merge_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_test_form
msgid "Cancel"
msgstr "Anulează"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__canceled
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__canceled
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__cancel
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Canceled"
msgstr "Anulat(ă)"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing_test__email_to
msgid "Carriage-return-separated list of email addresses."
msgstr "Lista de adrese de e-mail separate prin caracterul de întoarcere."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__preview
msgid ""
"Catchy preview sentence that encourages recipients to open this email.\n"
"In most inboxes, this is displayed next to the subject.\n"
"Keep it empty if you prefer the first characters of your email content to appear instead."
msgstr ""
"Expresie de previzualizare atrăgătoare care încurajează destinatarii să deschidă acest e-mail.\n"
"n majoritatea căsuțelor primite, acesta este afișat lângă subiect.\n"
"Păstrați-l gol dacă preferați să apară primele caractere ale conținutului dvs. de e-mail"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Center"
msgstr "Centru"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Change Icons"
msgstr "Schimbă iconițele"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_form_full_width
msgid "Chat"
msgstr "Conversație"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_product_list
msgid "Check out all our books"
msgstr "Verificați toate cărțile noastre"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_product_list
msgid "Check out all our clothes"
msgstr "Verificați toate hainele noastre"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_product_list
msgid "Check out all our furniture"
msgstr "Verificați toată mobila noastră"

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Check the email address and click send."
msgstr "Verificați adresa de e-mail și faceți clic pe Trimite."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_title_sub
msgid "Check this out!"
msgstr "Verificați asta!"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_steps
msgid "Choose"
msgstr "Alege"

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Choose this <b>theme</b>."
msgstr "Alegeți această <b>temă</b>."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe
msgid "Choose your mailing subscriptions"
msgstr "Alegeți-vă abonamentele de corespondență"

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Click on this paragraph to edit it."
msgstr "Faceți clic pe acest paragraf pentru a-l edita."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__clicked
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__clicked
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Clicked"
msgstr "Clicked"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Clicked (%)"
msgstr "Clicked (%)"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__links_click_datetime
msgid "Clicked On"
msgstr "Clic pe"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Clicks"
msgstr "Clicuri"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_product_list
msgid "Clothes"
msgstr "Haine"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
msgid "Color"
msgstr "Color"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__color
msgid "Color Index"
msgstr "Index Culori"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_color_blocks_2
msgid ""
"Color blocks are a simple and effective way to <b>present and highlight your"
" content</b>. Choose an image or a color for the background. You can even "
"resize and duplicate the blocks to create your own layout. Add images or "
"icons to customize the blocks."
msgstr ""
"Blocurile de culori sunt un mod simplu și eficient de a <b>pentru a vă "
"prezenta și evidenția conținutul</b>. Alegeți o imagine sau o culoare pentru"
" fundal. Puteți chiar să redimensionați și să copiați blocurile pentru a "
"crea propriul aspect. Adăugați imagini sau pictograme pentru a personaliza "
"blocurile."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Colors"
msgstr "Culori"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_three_cols
msgid "Column Title"
msgstr "Titlul coloană"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_two_cols
msgid "Column title"
msgstr "Titlu coloană"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Columns"
msgstr "Coloane"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribed
msgid "Come Back"
msgstr "Revino"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_res_company
msgid "Companies"
msgstr "Companii"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__company_name
msgid "Company Name"
msgstr "Numele companiei"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.layout
msgid "Company name"
msgstr "Numele firmei"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_res_config_settings
msgid "Config Settings"
msgstr "Setări de configurare"

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.mass_mailing_configuration
msgid "Configuration"
msgstr "Configurare"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid "Configure Email Server"
msgstr "Configurare Server Email"

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Congratulations, I love your first mailing. :)"
msgstr "Felicitări, îmi place primul dvs. mail. :)"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_smtp
msgid "Connection failed (outgoing mail server problem)"
msgstr "Conectarea a eșuat (problema la serverului de trimis e-mail)"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_res_partner
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_subscription__contact_id
msgid "Contact"
msgstr "Contact"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form_simplified
msgid "Contact List"
msgstr "Listă de contacte"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_form
msgid "Contact Name"
msgstr "Numele Contactului"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_call_to_action
msgid "Contact us"
msgstr "Contactați-ne"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__contact_ids
msgid "Contacts"
msgstr "Contacte"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid "Continue reading <i class=\"fa fa-long-arrow-right align-middle ml-1\"/>"
msgstr ""
"Continuați să citiți<i class=\"fa fa-long-arrow-right align-middle ml-1\"/>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.layout
msgid "Copyright &amp;copy;"
msgstr "Copyright &amp;copy;"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__message_bounce
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact_subscription__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr "Contrare a numărului de e-mailuri rebote pentru acest contact"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__country_id
msgid "Country"
msgstr "Țară"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_cover
msgid "Cover image"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form_simplified_footer
msgid "Create"
msgstr "Creează"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.layout
msgid "Create a"
msgstr "Creează un"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.mailing_mailing_action_mail
msgid "Create a Mailing"
msgstr "Creați o corespondență"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.open_create_mass_mailing_list
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailing_lists
msgid "Create a Mailing List"
msgstr "Creați o listă de corespondență"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_utm_campaigns
msgid "Create a mailing campaign"
msgstr "Creați o campanie de e-mail"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailing_contacts
msgid "Create a mailing contact"
msgstr "Creați un contact de corespondență"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_create_mass_mailings_from_campaign
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailings_from_campaign
msgid "Create a new mailing"
msgstr "Creați o corespondență nouă"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Create an Alternative Version"
msgstr "Creați o versiune alternativă"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_subscription__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__create_uid
msgid "Created by"
msgstr "Creat de"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_subscription__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__create_date
msgid "Created on"
msgstr "Creat în"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Creation Date"
msgstr "Data creării"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_search
msgid "Creation Period"
msgstr "Perioada de creare"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "Custom"
msgstr "Personalizat"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_event
msgid ""
"Cyber-threats continue to increase.\n"
"                        <br/>The discussion will examine how to develop new norms and integrate them into EU"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_event
msgid "Cybersecurity"
msgstr "Securitate cibernetică"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "DEFAULT"
msgstr "IMPLICIT"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
msgid "Danger"
msgstr "Periculos"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options_border_line_widgets
msgid "Dashed"
msgstr "Întrerupt"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Date"
msgstr "Dată"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__calendar_date
msgid "Date at which the mailing was or will be sent."
msgstr "Data la care a fost sau va fi trimisă corespondența."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__ab_testing_schedule_datetime
#: model:ir.model.fields,help:mass_mailing.field_utm_campaign__ab_testing_schedule_datetime
msgid ""
"Date that will be used to know when to determine and send the winner mailing"
msgstr ""
"Data care va fi folosită pentru a determina când să se trimită corespondența"
" "

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_res_config_settings__mass_mailing_outgoing_mail_server
msgid "Dedicated Server"
msgstr "Server Dedicat"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Default"
msgstr "Implicit"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Default Reversed"
msgstr "Implicit inversat"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__null_value
msgid "Default Value"
msgstr "Valoare implicită"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Delete"
msgstr "Șterge"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Delete Blocks"
msgstr "Șterge blocuri"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_three_columns
msgid ""
"Delete the above image or replace it with a picture that illustrates your "
"message. Click on the picture to change its <em>rounded corner</em> style."
msgstr ""
"Ștergeți imaginea de mai sus sau înlocuiți-o cu o imagine care ilustrează "
"mesajul dumneavoastră. Faceți clic pe imagine pentru a-i schimba <em>colț "
"rotunjit</em>stil."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__delivered
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__delivered
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
msgid "Delivered"
msgstr "Livrat"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Delivered (%)"
msgstr "Livrat (%)"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Delivered to"
msgstr "Livrat către"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_title_text
msgid "Demo Signature"
msgstr "Semnatură Demo"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.mailing_mailing_action_mail
msgid "Design a striking email, define recipients and track its results."
msgstr ""
"Proiectează un email atrăgător, definește destinatarii și urmărește "
"rezultatele."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__dest_list_id
msgid "Destination Mailing List"
msgstr "Lista de corespondență a destinației"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form_simplified_footer
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_schedule_date_view_form
msgid "Discard"
msgstr "Abandonează"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid "Discover"
msgstr "Descoperă"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_showcase
msgid "Discover all the features"
msgstr "Descoperă toate caracteristicile"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "Display Inline"
msgstr "Afișare în linie"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_subscription__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__res_id
msgid "Document ID"
msgstr "Document ID"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__model
msgid "Document model"
msgstr "Model document"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_domain
msgid "Domain"
msgstr "Domeniu"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "Don't forget to send your prefered version"
msgstr ""

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Don't worry, the mailing contact we created is an internal user."
msgstr ""
"Nu vă faceți griji, contactul pe care l-am creat este un utilizator intern."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options_border_line_widgets
msgid "Dotted"
msgstr "Punctat"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options_border_line_widgets
msgid "Double"
msgstr "Dublu"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Double click an icon to replace it with one of your choice."
msgstr "Dublu click pe o pictogramă pentru a o înlocui cu una la alegere"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__state__draft
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace_report__state__draft
msgid "Draft"
msgstr "Ciornă"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Dropdown menu"
msgstr "Meniul derulant"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
msgid "Duplicate"
msgstr "Duplicare"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Duplicate blocks and columns to add more features."
msgstr ""
"Duplicați blocurile și coloanele pentru a adăuga mai multe caracteristici."

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_dup
msgid "Duplicated Email"
msgstr "Email duplicat"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Dynamic Placeholder Generator"
msgstr "Generator de loc dinamic"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_coupon_code
msgid "ENDOFSUMMER20"
msgstr "ENDOFSUMMER20"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Edit Styles"
msgstr "Editare Stiluri"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__email
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__email
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__mailing_type__mail
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_type__mail
msgid "Email"
msgstr "Email"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_tree
msgid "Email Blacklisted"
msgstr "E-mail Lista neagră"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Email Content"
msgstr "Conținut e-mail"

#. module: mass_mailing
#: code:addons/mass_mailing/models/res_users.py:0
#: model:ir.ui.menu,name:mass_mailing.mass_mailing_menu_root
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
#, python-format
msgid "Email Marketing"
msgstr "Marketing E-mail"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_thread
msgid "Email Thread"
msgstr "Fir E-mail"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Asistent de compunere email-uri"

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "Emails"
msgstr "Email-uri"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_trace_ids
msgid "Emails Statistics"
msgstr "Statistici email-uri"

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "Engagement on %(expected)i %(mailing_type)s Sent"
msgstr "Angajament pe %(expected)i %(mailing_type)s trimis"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_steps
msgid "Enjoy!"
msgstr "Bucurați-vă!"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_title_text
msgid "Enjoy,"
msgstr "Bucurați-vă,"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__error
msgid "Error"
msgstr "Eroare"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid "Event heading"
msgstr "Titlu eveniment"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__error
msgid "Exception"
msgstr "Excepție"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Exclude Blacklisted Emails"
msgstr "Excludere Email-uri Listă Neagră"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Exclude Opt Out"
msgstr "Excludere renunțare"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__expected
msgid "Expected"
msgstr "Estimat"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
msgid "Extended Filters..."
msgstr "Filtre extinse..."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.social_links
msgid "Facebook"
msgstr "Facebook"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__failed
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Failed"
msgstr "Eșuat"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__failure_type
msgid "Failure type"
msgstr "Tip Eșec"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_three_columns
msgid "Feature One"
msgstr "Prima facilitate"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_three_columns
msgid "Feature Three"
msgstr "A treia facilitate"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_three_columns
msgid "Feature Two"
msgstr "Facilitatea de doua"

#. module: mass_mailing
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid "Feedback from %(email)s: %(feedback)s"
msgstr "Feedback de la%(email)s: %(feedback)s"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__model_object_field
msgid "Field"
msgstr "Câmp"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__copyvalue
msgid ""
"Final placeholder expression, to be copy-pasted in the desired template "
"field."
msgstr ""
"Expresie înlocuitoare finală, care va fi copiată in câmpul șablon dorit."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features
msgid "First Feature"
msgstr "Prima caracteristică"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_showcase
msgid "First feature"
msgstr "Prima funcție"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "First list of Features"
msgstr "Prima listă de funcții"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Fit content"
msgstr "Conținut potrivit"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_follower_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_follower_ids
msgid "Followers"
msgstr "Persoane interesate"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_partner_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_partner_ids
msgid "Followers (Partners)"
msgstr "Urmăritori (Parteneri)"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Pictogramă minunată pentru font, de ex. fa-sarcini"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Footers"
msgstr "Subsoluri"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid ""
"Founder and chief visionary, Tony is the driving force behind the company. He loves\n"
"                                    to keep his hands full by participating in the development of the software,\n"
"                                    marketing, and customer experience strategies."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__email_from
msgid "From"
msgstr "De la"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Full"
msgstr "Complet"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Fullscreen"
msgstr "Ecran Complet"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_product_list
msgid "Furniture"
msgstr "Mobila"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_image_text
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_image_text
msgid ""
"Get your inside sales (CRM) fully integrated with online sales (eCommerce), "
"in-store sales (Point of Sale) and marketplaces like eBay and Amazon."
msgstr ""
"Obțineți-vă integral vânzările interioare (CRM) pe deplin integrate cu "
"vânzările online (comerț electronic), vânzările în magazin (punctul de "
"vânzare) și piețele de piață precum eBay și Amazon"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Great Value"
msgstr "Valoare mare"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Group By"
msgstr "Grupează după"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
msgid "Group By..."
msgstr "Grupează după..."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__has_message
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__has_message
msgid "Has Message"
msgstr "Are mesaj"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Headers"
msgstr "Antete"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Height"
msgstr "Înălțime"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_coupon_code
msgid "Here's your coupon code - but hurry! Ends 9/28"
msgstr "Aici este codul tău de cupon - dar te grabi! Se termină 9/28"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__utm_campaign__ab_testing_winner_selection__clicks_ratio
msgid "Highest Click Rate"
msgstr "Cea mai mare rata de clicuri"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__utm_campaign__ab_testing_winner_selection__opened_ratio
msgid "Highest Open Rate"
msgstr "Cea mai mare rata de deschidere"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__utm_campaign__ab_testing_winner_selection__replied_ratio
msgid "Highest Reply Rate"
msgstr "Cea mai mare rata de răspuns"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_subscription__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__id
msgid "ID"
msgstr "ID"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_trace__mail_mail_id_int
msgid ""
"ID of the related mail_mail. This field is an integer field because the "
"related mail_mail can be deleted separately from its statistics. However the"
" ID is needed for several action and controllers."
msgstr ""
"ID-ul mail_mail aferent. Acest câmp este un câmp întreg, deoarece mail_mail-"
"ul aferent poate fi șters separat de statisticile sale. Cu toate acestea, "
"ID-ul este necesar pentru mai multe de acțiuni și controale."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_exception_icon
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "Icon"
msgstr "Pictogramă"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Pictograma pentru a indica o activitate de excepție."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__message_needaction
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__message_unread
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__message_needaction
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__message_unread
msgid "If checked, new messages require your attention."
msgstr "Dacă este selectat, mesajele noi necesită atenția dumneavoastră."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__ab_testing_enabled
msgid ""
"If checked, recipients will be mailed only once for the whole campaign. This"
" lets you send different mailings to randomly selected recipients and test "
"the effectiveness of the mailings, without causing duplicate messages."
msgstr ""
"Dacă sunt bifate, destinatarii vor fi expediați prin poștă o singură dată "
"pentru întreaga campanie. Acest lucru vă permite să trimiteți mesaje "
"diferite către destinatarii selectați la întâmplare și să testați "
"eficacitatea trimiterilor, fără a provoca mesaje duplicate."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__message_has_error
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__message_has_sms_error
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__message_has_error
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Dacă este bifată, unele mesaje au o eroare de livrare."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__is_blacklisted
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact_subscription__is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr ""
"Dacă adresa de e-mail este pe lista neagră, contactul nu va mai primi mesaje"
" de corespondență în masă, din nicio listă"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_form_full_width
msgid "Ignored"
msgstr "Ignorat"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Image Text Image"
msgstr "Imagine Text Imagine"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Images"
msgstr "Imagini"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__state__in_queue
msgid "In Queue"
msgstr "În coadă"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
msgid "Info"
msgstr "Informații"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Inner Content"
msgstr "Conținut intern"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.social_links
msgid "Instagram"
msgstr "Instagram"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_email_invalid
msgid "Invalid email address"
msgstr "Adresă de email invalidă "

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid "Iris Joe, CFO"
msgstr "Iris Joe, CFO"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid ""
"Iris, with her international experience, helps us easily understand the "
"numbers and improves them. She is determined to drive success and delivers "
"her professional acumen to bring the company to the next level."
msgstr ""
"Iris, cu experiența ei internațională, ne ajută să înțelegem cu ușurință "
"numerele și să le îmbunătățim. Ea este hotărâtă să conducă succesul și își "
"oferă priceperea profesională pentru a aduce compania la nivelul următor."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__is_body_empty
msgid "Is Body Empty"
msgstr "Este corp gol"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_is_follower
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_is_follower
msgid "Is Follower"
msgstr "Este urmăritor"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__is_public
msgid "Is Public"
msgstr "Este Public"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_call_to_action
msgid "Join us and make your company a better place."
msgstr "Alătură-te nouă și fă compania ta un loc mai bun"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__kpi_mail_required
msgid "KPI mail required"
msgstr "E-mail KPI necesar"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__keep_archives
msgid "Keep Archives"
msgstr "Păstrați Arhive"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_title_text
msgid "LOGIN"
msgstr "LOGARE"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__lang
msgid "Language"
msgstr "Limba"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "Large"
msgstr "Larg"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_showcase
msgid "Last Feature"
msgstr "Ultima Funcție"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact____last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_subscription____last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list____last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list____last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge____last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing____last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date____last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test____last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace____last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report____last_update
msgid "Last Modified on"
msgstr "Ultima modificare la"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Last State Update"
msgstr "Ultima actualizare de stare"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_subscription__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__write_uid
msgid "Last Updated by"
msgstr "Ultima actualizare făcută de"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_subscription__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__write_date
msgid "Last Updated on"
msgstr "Ultima actualizare pe"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Left"
msgstr "Stânga"

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Let's try the Email Marketing app."
msgstr "Să încercăm aplicația Email Marketing."

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_link_tracker
#: model:ir.ui.menu,name:mass_mailing.link_tracker_menu_mass_mailing
msgid "Link Tracker"
msgstr "Urmărire Link"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_link_tracker_click
msgid "Link Tracker Click"
msgstr "Faceți clic pe Link Tracker"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.social_links
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__links_click_ids
msgid "Links click"
msgstr "Click Link-uri"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__mail_mail_id
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace_report__mailing_type__mail
msgid "Mail"
msgstr "E-Mail"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Mail Body"
msgstr "Cuprins e-mail"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_form_full_width
msgid "Mail Debug"
msgstr "Debug e-mail"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
msgid "Mail ID"
msgstr "ID e-mail"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__mail_mail_id_int
msgid "Mail ID (tech)"
msgstr "ID Mail (tech)"

#. module: mass_mailing
#: model:ir.actions.server,name:mass_mailing.ir_cron_mass_mailing_ab_testing_ir_actions_server
#: model:ir.cron,cron_name:mass_mailing.ir_cron_mass_mailing_ab_testing
#: model:ir.cron,name:mass_mailing.ir_cron_mass_mailing_ab_testing
msgid "Mail Marketing: A/B Testing"
msgstr "Marketing prin e-mail: A/B Testing"

#. module: mass_mailing
#: model:ir.actions.server,name:mass_mailing.ir_cron_mass_mailing_queue_ir_actions_server
#: model:ir.cron,cron_name:mass_mailing.ir_cron_mass_mailing_queue
#: model:ir.cron,name:mass_mailing.ir_cron_mass_mailing_queue
msgid "Mail Marketing: Process queue"
msgstr "Marketing prin e-mail: Procesează coada"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_render_mixin
msgid "Mail Render Mixin"
msgstr "Redare Mail Mixin"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mail_server_id
#: model:ir.model.fields,field_description:mass_mailing.field_res_config_settings__mass_mailing_mail_server_id
msgid "Mail Server"
msgstr "Server Mail"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mail_server_available
msgid "Mail Server Available"
msgstr "Server Mail Disponibil"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_mail_mail_statistics_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_link_tracker_click__mailing_trace_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_graph
msgid "Mail Statistics"
msgstr "Statistici mail"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_tree_mail
msgid "Mail Traces"
msgstr "Urmărire Mail"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__mass_mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__mass_mailing_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_graph
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Mailing"
msgstr "Expediere"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__campaign
msgid "Mailing Campaign"
msgstr "Campanie Mailing"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_res_config_settings__group_mass_mailing_campaign
msgid "Mailing Campaigns"
msgstr "Campanii de Mailing"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_contact
msgid "Mailing Contact"
msgstr "Contact Mailing"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_ir_model__is_mailing_enabled
msgid "Mailing Enabled"
msgstr "Corespondență Activată"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_list
#: model:ir.model.fields,field_description:mass_mailing.field_account_invoice_send__mailing_list_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mail_compose_message__mailing_list_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_subscription__list_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_to_list__mailing_list_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__name
msgid "Mailing List"
msgstr "Listă de discuții"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_mass_mailing_contacts
#: model:ir.ui.menu,name:mass_mailing.menu_email_mass_mailing_contacts
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_graph
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_pivot
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_tree
msgid "Mailing List Contacts"
msgstr "Persoane de contact cu lista de corespondență"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_subscription_view_form
msgid "Mailing List Subscription"
msgstr "Abonament la lista de corespondență"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_subscription_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_subscription_view_tree
msgid "Mailing List Subscriptions"
msgstr "Abonamente la lista de corespondență"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_mass_mailing_lists
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__list_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__src_list_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__contact_list_ids
#: model:ir.ui.menu,name:mass_mailing.mass_mailing_mailing_list_menu
#: model:ir.ui.menu,name:mass_mailing.menu_email_mass_mailing_lists
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_tree
msgid "Mailing Lists"
msgstr "Liste de e-mail"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_trace
msgid "Mailing Statistics"
msgstr "Statistici de Mailing"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribed
msgid "Mailing Subscriptions"
msgstr "Abonamente Corespondență"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_mail_mass_mailing_test
msgid "Mailing Test"
msgstr "Test Mailing"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mailing_trace_action
#: model:ir.ui.menu,name:mass_mailing.menu_email_statistics
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_tree
msgid "Mailing Traces"
msgstr "Urme Mailing"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_type
msgid "Mailing Type"
msgstr "Tip Mailing"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_type_description
msgid "Mailing Type Description"
msgstr "Descriere tip mailing"

#. module: mass_mailing
#: code:addons/mass_mailing/wizard/mailing_mailing_test.py:0
#, python-format
msgid "Mailing addresses incorrect: %s"
msgstr "Adrese de corespondență incorecte: %s"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailing_contacts
msgid ""
"Mailing contacts allow you to separate your marketing audience from your "
"business contact directory."
msgstr ""
"Persoana de contact vă permite să separați publicul dvs. de marketing de "
"directorul de contacte de afaceri."

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_create_mass_mailings_from_campaign
#: model:ir.actions.act_window,name:mass_mailing.action_view_mass_mailings_from_campaign
#: model:ir.actions.act_window,name:mass_mailing.mailing_mailing_action_mail
#: model:ir.ui.menu,name:mass_mailing.mass_mailing_menu
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_tree
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_calendar
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_kanban
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Mailings"
msgstr "Corespondenta"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Mailings that are assigned to me"
msgstr "Mesaje care mi se atribuie"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_main_attachment_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_main_attachment_id
msgid "Main Attachment"
msgstr "Atașament principal"

#. module: mass_mailing
#: model:res.groups,name:mass_mailing.group_mass_mailing_campaign
msgid "Manage Mass Mailing Campaigns"
msgstr "Gestionați campaniile de Mailing în masă"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid "Manage campaigns and A/B test your mailings"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__utm_campaign__ab_testing_winner_selection__manual
msgid "Manual"
msgstr "Manual"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
msgid "Marketing"
msgstr "Marketing"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Marketing Content"
msgstr "Conținut Marketing"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__name
msgid "Mass Mail"
msgstr "Mail în masă"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_account_invoice_send__mass_mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_link_tracker__mass_mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_link_tracker_click__mass_mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_compose_message__mass_mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail__mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__mass_mailing_id
#: model:ir.ui.menu,name:mass_mailing.mailing_mailing_menu_technical
#: model_terms:ir.ui.view,arch_db:mass_mailing.link_tracker_click_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.link_tracker_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Mass Mailing"
msgstr "Emailuri în masă"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mailing_trace_report_action_mail
msgid "Mass Mailing Analysis"
msgstr "Analize mail în masă"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_account_invoice_send__campaign_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_compose_message__campaign_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
msgid "Mass Mailing Campaign"
msgstr "Campanie emailuri în masă"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_account_invoice_send__mass_mailing_name
#: model:ir.model.fields,field_description:mass_mailing.field_mail_compose_message__mass_mailing_name
msgid "Mass Mailing Name"
msgstr "Nume Mailing în masă"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_trace_report
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_graph
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_pivot
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_tree
msgid "Mass Mailing Statistics"
msgstr "Statistici Emailuri în masă"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.mailing_trace_report_action_mail
msgid ""
"Mass Mailing Statistics allows you to check different mailing related information\n"
"    like number of bounced mails, opened mails, replied mails. You can sort out\n"
"    your analysis by different groups to get accurate grained analysis."
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_contact_subscription
msgid "Mass Mailing Subscription Information"
msgstr "Informații privind abonamentul de Mailing în masă"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__mailing_ids
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__mailing_mail_ids
msgid "Mass Mailings"
msgstr "Emailuri în masă"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid "Media heading"
msgstr "Titlu media"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__medium_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__medium_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Medium"
msgstr "Mediu"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mailing_list_merge_action
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_merge_view_form
msgid "Merge"
msgstr "Îmbină"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_list_merge
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_merge_view_form
msgid "Merge Mass Mailing List"
msgstr "Îmbinați lista de distribuție în masă"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__merge_options
msgid "Merge Option"
msgstr "Opțiune de îmbinare"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_list_merge__merge_options__new
msgid "Merge into a new mailing list"
msgstr "Mergeți într-o nouă listă de discuții"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_list_merge__merge_options__existing
msgid "Merge into an existing mailing list"
msgstr "Mergeți într-o listă de corespondență existentă"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_has_error
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_has_error
msgid "Message Delivery error"
msgstr "Eroare livrare mesaj"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__message_id
msgid "Message-ID"
msgstr "ID mesaj"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_ids
msgid "Messages"
msgstr "Mesaje"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid "Mich Stark, COO"
msgstr "Mich Stark, COO"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid ""
"Mich loves taking on challenges. With his multi-year experience as "
"Commercial Director in the software industry, Mich has helped the company to"
" get where it is today. Mich is among the best minds."
msgstr ""
"Lui Mich îi place să facă față provocărilor. Cu experiența sa de mai mulți "
"ani ca Director Comercial în industria software-ului, Mich a ajutat compania"
" să ajungă acolo unde este astăzi. Mich este printre cele mai bune minți."

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_email_missing
msgid "Missing email address"
msgstr "Adresa de email lipsește"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_ir_model
msgid "Models"
msgstr "Modele"

#. module: mass_mailing
#: model:mailing.mailing,sms_subject:mass_mailing.mass_mail_1
msgid "Monthly Newsletter"
msgstr "Buletin informativ lunar"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "More"
msgstr "Mai mult"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_color_blocks_2
msgid "More Details"
msgstr "Mai multe detalii"

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "More Info"
msgstr "Alte informații"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Mosaic"
msgstr "Mozaic"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_tag_line
msgid "My Account"
msgstr "Contul meu"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Data limită a activității mele"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_header_text_social
msgid "My Company"
msgstr "Compania Mea"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "My Mailings"
msgstr "Mesajele mele"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__name
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Name"
msgstr "Nume"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Name / Email"
msgstr "Nume / Email"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list_merge__new_list_name
msgid "New Mailing List Name"
msgstr "Nume nou listă de corespondență"

#. module: mass_mailing
#: model:utm.campaign,name:mass_mailing.mass_mail_campaign_1
msgid "Newsletter"
msgstr "Buletin informativ"

#. module: mass_mailing
#: model:mailing.mailing,name:mass_mailing.mass_mail_1
#: model:utm.source,name:mass_mailing.utm_source_0
msgid "Newsletter 1"
msgstr "Buletin informativ 1"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Următoarea activitate din calendar"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Data limită pentru următoarea activitate"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_summary
msgid "Next Activity Summary"
msgstr "Sumarul următoarei activități"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_type_id
msgid "Next Activity Type"
msgstr "Tip de activitate urmatoare"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "No customization"
msgstr "Fără particualizare"

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mail_mail_statistics_mailing
msgid "No data yet!"
msgstr "Nu există date încă!"

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "No mailing campaign has been found"
msgstr "Nu a fost găsită nicio campanie de corespondență"

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid ""
"No mailing for this A/B testing campaign has been sent yet! Send one first "
"and try again later."
msgstr ""
"Niciun mesaj pentru această campanie de testare A/B nu a fost trimis încă! "
"Trimite unul mai întâi și încearcă din nou mai târziu."

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailing_lists
msgid ""
"No need to import mailing lists, you can send mailings to contacts saved in "
"other Odoo apps."
msgstr ""
"Nu este necesar să importați liste de corespondență, puteți trimite mesaje "
"corespondenței către contacte salvate în alte aplicații Odoo."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_comparisons
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "No support"
msgstr "Fără suport"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_form_full_width
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "No template picked yet."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "None"
msgstr "Fără"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__email_normalized
msgid "Normalized Email"
msgstr "E-mail normalizat"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_trace__email
msgid "Normalized email address"
msgstr "Adresa de e-mail normalizată"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_needaction_counter
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_needaction_counter
msgid "Number of Actions"
msgstr "Număr de acțiuni"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_count_blacklisted
msgid "Number of Blacklisted"
msgstr "Număr de adrese blocate"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__clicks_ratio
msgid "Number of Clicks"
msgstr "Nr. Click-uri"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_count
msgid "Number of Contacts"
msgstr "Număr de contacte"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_count_email
msgid "Number of Emails"
msgstr "Număr de e-mailuri"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__mailing_count
msgid "Number of Mailing"
msgstr "Număr de mesaje corespondență"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__mailing_mail_count
msgid "Number of Mass Mailing"
msgstr "Numărul de corespondență în masă"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_count_opt_out
msgid "Number of Opted-out"
msgstr "Număr de persoane care au ales să nu primească mesaje"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_merge_view_form
msgid "Number of Recipients"
msgstr "Număr Destinatari"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_kanban
msgid "Number of bounced email."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_has_error_counter
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_has_error_counter
msgid "Number of errors"
msgstr "Numărul de erori"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__message_needaction_counter
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Număr de mesaje ce necesită intervenție"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__message_has_error_counter
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Numărul de mesaje cu eroare de livrare"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__message_unread_counter
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__message_unread_counter
msgid "Number of unread messages"
msgstr "Număr de mesaje necitite"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount2
msgid "OFF YOUR NEXT ORDER!"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount1
msgid "ON YOUR NEXT ORDER!"
msgstr "LA URMĂTOAREA DUMNEAVOASTRĂ COMANDĂ!"

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "OPENED (%i)"
msgstr "DESCHIS (%i)"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.layout
msgid "Odoo"
msgstr "Odoo"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_image_text
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_image_text
msgid "Omnichannel sales"
msgstr "Vânzări omnicanal"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid ""
"Once the best version is identified, we will send the best one to the "
"remaining recipients."
msgstr ""
"Odată ce a fost identificată cea mai bună variantă, aceasta va fi trimisă "
"restului de destinatari."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Open Date"
msgstr "Data deschiderii"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_tree
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_tree_mail
msgid "Open Recipient"
msgstr "Deschide destinatar"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__opened
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__opened
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__open
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Opened"
msgstr "Deschis(a)"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Opened (%)"
msgstr "Deschis (%)"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__open_datetime
msgid "Opened On"
msgstr "Deschis la"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__opened_ratio
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__opened_ratio
msgid "Opened Ratio"
msgstr "Rațio deschis"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__opt_out
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_subscription__opt_out
msgid "Opt Out"
msgstr "Nu participati"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__opt_out
msgid ""
"Opt out flag for a specific mailing list.This field should not be used in a "
"view without a unique and active mailing list context."
msgstr ""
"Dezactivați semnalizarea pentru o anumită listă de corespondență. Acest câmp"
" nu trebuie utilizat într-o vizualizare fără un context unic și activ al "
"listei de discuții"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_tree
msgid "Opt-out (%)"
msgstr "Nu participați (%)"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__mail_optout
msgid "Opted Out"
msgstr "Optat pentru a nu primi"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Opted-out"
msgstr "Optat pentru a nu primi"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Limba de traducere opțională (cod ISO) pentru a selecta când se trimite un "
"e-mail. Dacă nu este setat, versiunea engleză va fi folosită. Aceasta ar "
"trebui să fie de obicei o expresie rezervată care oferă limba potrivită, de "
"exemplu {{ object.partner_id.lang }}."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__null_value
msgid "Optional value to use if the target field is empty"
msgstr ""
"Valoarea optionala care va fi folosita in cazul in care campul tinta este "
"gol"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_steps
msgid "Order"
msgstr "Comandă"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_references
msgid "Our References"
msgstr "Referințele noastre"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__outgoing
msgid "Outgoing"
msgstr "Ieșiri"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_mail
msgid "Outgoing Mails"
msgstr "Email-uri Expediate"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_comparison_table
msgid "PRO"
msgstr "PRO"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_pct_blacklisted
msgid "Percentage of Blacklisted"
msgstr "Procentaj de persoane blocate"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_pct_bounce
msgid "Percentage of Bouncing"
msgstr "Procentaj de persoane care au returnat"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__contact_pct_opt_out
msgid "Percentage of Opted-out"
msgstr "Procentaj de persoane care nu participă"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__ab_testing_pc
msgid ""
"Percentage of the contacts that will be mailed. Recipients will be chosen "
"randomly."
msgstr ""
"Procentajul de persoane care vor primi e-mail. Destinatarii vor fi alesi "
"întâmplător."

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Pick the <b>email subject</b>."
msgstr "Alegeți <b> subiectul e-mailului</b>."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__copyvalue
msgid "Placeholder Expression"
msgstr "Expresie Substituenta"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid "Post heading"
msgstr "Postare rubrică"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__reply_to
msgid "Preferred Reply-To Address"
msgstr "Adresă de răspuns preferată"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__preview
msgid "Preview"
msgstr "Previzualizare"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Preview Text"
msgstr "Previzualizare Text"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
msgid "Primary"
msgstr "Primar"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_highlight
msgid "Put the focus on what you have to say!"
msgstr "Puneți accentul pe ceea ce aveți de spus!"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating
msgid "Quality"
msgstr "Calitate"

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "RECEIVED (%i)"
msgstr "PRIMIT (%i)"

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "REPLIED (%i)"
msgstr "RĂSPUNS (%i)"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_image_text
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_image
msgid "Read More"
msgstr "Mai mult"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_image_text
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_text_image
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_three_cols
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_two_cols
msgid "Read More..."
msgstr "Mai mult..."

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Ready for take-off!"
msgstr "Gata pentru decolare!"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Received"
msgstr "Primit"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__received_ratio
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__received_ratio
msgid "Received Ratio"
msgstr "Raport Primit"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_calendar
msgid "Recipient"
msgstr "Destinatar"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
msgid "Recipient Address"
msgstr "Adresă destinatar"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__reply_to_mode__update
msgid "Recipient Followers"
msgstr "Urmăritori destinatari"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_test__email_to
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_tree
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Recipients"
msgstr "Destinatari"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_model_id
msgid "Recipients Model"
msgstr "Modelul destinatarilor"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_model_name
msgid "Recipients Model Name"
msgstr "Numele modelului destinatarilor"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__mailing_model_real
msgid "Recipients Real Model"
msgstr "Beneficiarii Modelul real"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount1
msgid "Redeem Discount!"
msgstr "Valorificați reducerea!"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_event
msgid "Register"
msgstr "Înregistrați"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Regular"
msgstr "Obișnuit"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__render_model
msgid "Rendering Model"
msgstr "Model de redare"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__replied
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__replied
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__reply
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Replied"
msgstr "Răspuns"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Replied (%)"
msgstr "Răspuns (%)"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__reply_datetime
msgid "Replied On"
msgstr "Răspuns la"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__replied_ratio
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__replied_ratio
msgid "Replied Ratio"
msgstr "Raportul răspuns"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Reply Date"
msgstr "Data răspunsului"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__reply_to
msgid "Reply To"
msgstr "Răspunde la"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__reply_to_mode
msgid "Reply-To Mode"
msgstr "Modul de răspuns"

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.menu_mass_mailing_report
msgid "Reporting"
msgstr "Raportare"

#. module: mass_mailing
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid "Requested blacklisting via unsubscribe link."
msgstr "Solicitarea listei neagre prin linkul de dezabonare."

#. module: mass_mailing
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid "Requested blacklisting via unsubscription page."
msgstr "Solicitarea listei neagre prin pagina de dezabonare."

#. module: mass_mailing
#: code:addons/mass_mailing/controllers/main.py:0
#, python-format
msgid "Requested de-blacklisting via unsubscription page."
msgstr "Solicitarea eliminării listei negre prin pagina de dezabonare."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__user_id
msgid "Responsible"
msgstr "Responsabil"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__activity_user_id
msgid "Responsible User"
msgstr "Utilizator responsabil"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Restore"
msgstr "Restabiliți"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Retry"
msgstr "Reîncearcă"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Right"
msgstr "Dreapta"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options_border_widgets
msgid "Round Corners"
msgstr "Colțuri rotunde"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_has_sms_error
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Eroare livrare SMS"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Sample Icons"
msgstr "Icoane de probă"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_mailing_test
msgid "Sample Mail Wizard"
msgstr "Wizard e-mail de probă"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__schedule_type
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_schedule_date_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Schedule"
msgstr "Programare"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__scheduled
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__scheduled
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Scheduled"
msgstr "Programat"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__scheduled_date
msgid "Scheduled Date"
msgstr "Dată planificată"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_tree
msgid "Scheduled On"
msgstr "Programat la"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
msgid "Scheduled Period"
msgstr "Perioada programată"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__next_departure
msgid "Scheduled date"
msgstr "Data planificată"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__schedule_date
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing_schedule_date__schedule_date
msgid "Scheduled for"
msgstr "Planificat pentru"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Scheduled on #{record.next_departure.value}"
msgstr "Programat în #{record.next_departure.value}"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Scheduled on #{record.schedule_date.value}"
msgstr "Programat în #{record.schedule_date.value}"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "Score"
msgstr "Scor"

#. module: mass_mailing
#: code:addons/mass_mailing/models/ir_model.py:0
#, python-format
msgid ""
"Searching Mailing Enabled models supports only direct search using '='' or "
"'!='."
msgstr ""
"Căutarea modelelor de e-mail activat permite doar căutarea directă folosind "
"'=' sau '!='."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features
msgid "Second Feature"
msgstr "Facilitate secundară"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_showcase
msgid "Second feature"
msgstr "Funcție Secundară"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Second list of Features"
msgstr "Lista Secundară de funcții"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
msgid "Secondary"
msgstr "Secundar"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Select and delete blocks to remove features."
msgstr "Selectați și ștergeți blocurile pentru a elimina facilitățile."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Select mailing lists..."
msgstr "Selectați listă mail..."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Select mailing lists:"
msgstr "Selectați listele de discuții:"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__model_object_field
msgid ""
"Select target field from the related document model.\n"
"If it is a relationship field you will be able to select a target field at the destination of the relationship."
msgstr ""
"Selectati campul tinta din modelul documentului asociat.\n"
"Daca este un camp de relatie, veti putea sa selectati un camp tinta la destinatia relatiei."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__ab_testing_winner_selection
#: model:ir.model.fields,help:mass_mailing.field_utm_campaign__ab_testing_winner_selection
msgid "Selection to determine the winner mailing that will be sent."
msgstr "Selectați pentru a determina mailing-ul câștigător care va fi trimis."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_test_form
msgid "Send"
msgstr "Trimite"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_schedule_datetime
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__ab_testing_schedule_datetime
msgid "Send Final On"
msgstr "Trimite final la"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__email_from
msgid "Send From"
msgstr "Trimite de la"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_to_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_test_form
msgid "Send a Sample Mail"
msgstr "Trimite un email de probă"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_test_form
msgid "Send a sample mailing for testing purpose to the address below."
msgstr ""
"Trimiteți un e-mail de probă pentru a testa scopul la adresa de mai jos."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.utm_campaign_view_form
msgid "Send new Mailing"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__schedule_type__now
msgid "Send now"
msgstr "Trimite acum"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__schedule_type__scheduled
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_schedule_date_view_form
msgid "Send on"
msgstr "Trimite la"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__state__sending
msgid "Sending"
msgstr "Trimitere"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__sent
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__sent
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__state__done
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__trace_status__sent
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace_report__state__done
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_form_full_width
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "Sent"
msgstr "Trimis"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Sent By"
msgstr "Trimis de către"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__sent_date
msgid "Sent Date"
msgstr "Dată trimitere"

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "Sent Mailings"
msgstr "E-mail-uri trimise"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__sent_datetime
msgid "Sent On"
msgstr "Trimis la"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Sent Period"
msgstr "Perioadă Trimise"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Sent on #{record.sent_date.value}"
msgstr "Trimis la #{record.sent_date.value}"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_mass_mailing_configuration
#: model:ir.ui.menu,name:mass_mailing.menu_mass_mailing_global_settings
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Settings"
msgstr "Setări"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid ""
"Since the date and time for this test has not been scheduled, don't forget "
"to manually send your preferred version."
msgstr ""
"Deoarece data și ora pentru acest test nu a fost programată, nu uitați să "
"trimiteți manual versiunea preferată."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "Size"
msgstr "Dimensiune"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Small"
msgstr "Mic"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options_border_line_widgets
msgid "Solid"
msgstr "Solid"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__source_id
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__source_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Source"
msgstr "Sursa"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__name
msgid "Source Name"
msgstr "Nume sursa"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid ""
"Speakers from all over the world will join our experts to give inspiring "
"talks on various topics. Stay on top of the latest business management "
"trends &amp; technologies"
msgstr ""
"Vorbitori din întreaga lume se vor alătura experților noștri pentru a "
"susține discursuri inspiraționale pe diferite teme. Rămâneți la curent cu "
"ultimele trenduri de gestionare a afacerilor &amp; technologies"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_mailing__reply_to_mode__new
msgid "Specified Email Address"
msgstr "Adresa de email specificată"

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Start by creating your first <b>Mailing</b>."
msgstr "Începeți prin a crea prima dvs. <b>E-mailing</b>."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_view_form_full_width
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Start editing your mailing to design something awesome."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_report_view_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_search
msgid "State"
msgstr "Județ"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail__mailing_trace_ids
msgid "Statistics"
msgstr "Statistica"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__state
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__trace_status
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__state
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_trace_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Status"
msgstr "Stare"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Stare bazată pe activități\n"
"Întârziată: data scadentă este deja trecută\n"
"Astăzi: activității pentru astăzi\n"
"Planificate: activități viitoare."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_trace__links_click_datetime
msgid "Stores last click datetime in case of multi clicks."
msgstr "Stochează ultima dată de clic în cazul mai multor clicuri."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Stretch to Equal Height"
msgstr "Intindeți la înălțime egală"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__sub_model_object_field
msgid "Sub-field"
msgstr "Sub-câmp"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__sub_object
msgid "Sub-model"
msgstr "Submodel"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__subject
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Subject"
msgstr "Subiect"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__subject
msgid "Subject of your Mailing"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__subscription_list_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_list__subscription_ids
msgid "Subscription Information"
msgstr "Informații despre abonament"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
msgid "Success"
msgstr "Succes"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__tag_ids
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_form
msgid "Tags"
msgstr "Etichete"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_mailing_schedule_date_view_form
msgid "Take Future Schedule Date"
msgstr "Luați data programării viitoare"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_trace__message_id
msgid "Technical field for the email Message-ID (RFC 2392)"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__is_body_empty
msgid "Technical field used to determine if the mail body is empty"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__mail_server_available
msgid ""
"Technical field used to know if the user has activated the outgoing mail "
"server option in the settings"
msgstr ""
"Câmp tehnic utilizat pentru a ști dacă utilizatorul a activat opțiunea de "
"server de e-mail de ieșire în setări"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features
msgid "Tell what's the value for the <br/>customer for this feature."
msgstr ""
"Spuneți care este valoarea pentru <br/> client pentru această funcție."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Template"
msgstr "Șablon"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Test"
msgstr "Test"

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "Test Mailing"
msgstr "Test mail"

#. module: mass_mailing
#: code:addons/mass_mailing/wizard/mailing_mailing_test.py:0
#, python-format
msgid "Test mailing could not be sent to %s:<br>%s"
msgstr "Testul de e-mailing nu a putut fi trimis la %s:<br>%s"

#. module: mass_mailing
#: code:addons/mass_mailing/wizard/mailing_mailing_test.py:0
#, python-format
msgid "Test mailing successfully sent to %s"
msgstr "Testul de e-mailing a fost trimis cu succes la %s"

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/tours/mass_mailing_tour.js:0
#, python-format
msgid "Test this mailing by sending a copy to yourself."
msgstr "Testați această corespondență trimițându-vă o copie."

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace_report__state__test
msgid "Tested"
msgstr "Testat"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_highlight
msgid "Text Highlight"
msgstr "Evidențiere text"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_masonry_block_options
msgid "Text Image Text"
msgstr "Text Imagine Text"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_title_text
msgid "Thank you for joining us!"
msgstr "Mulțumesc că ni te-ai alăturat!"

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#, python-format
msgid "Thank you! Your feedback has been sent successfully!"
msgstr "Mulțumesc! Feedback-ul dvs. a fost trimis cu succes!"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_paragraph
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_block
msgid "That way, Odoo evolves much faster than any other solution."
msgstr ""
"În acest fel, Odoo evoluează mult mai repede decât orice altă soluție."

#. module: mass_mailing
#: model:ir.model.constraint,message:mass_mailing.constraint_mailing_mailing_percentage_valid
msgid "The A/B Testing Percentage needs to be between 0 and 100%"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "The actual"
msgstr "Actualul"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact_subscription__opt_out
msgid "The contact has chosen not to receive mails anymore from this list"
msgstr ""
"Persoana de contact a ales să nu mai primească e-mailuri din această listă"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_list__is_public
msgid ""
"The mailing list can be accessible by recipient in the unsubscription page "
"to allows him to update his subscription preferences."
msgstr ""
"Lista de corespondență poate fi accesată de destinatar în pagina de "
"dezabonare pentru a-i permite să își actualizeze preferințele de abonament."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_paragraph
msgid ""
"The open source model of Odoo has allowed us to leverage thousands of developers and\n"
"                                business experts to build hundreds of apps in just a few years."
msgstr ""
"Modelul open source al Odoo ne-a permis să folosim mii de dezvoltatori și\n"
"                                experți în afaceri să construiască sute de aplicații în doar câțiva ani."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_block
msgid ""
"The open source model of Odoo has allowed us to leverage thousands of developers and\n"
"                    business experts to build hundreds of apps in just a few years."
msgstr ""
"Modelul open source al Odoo ne-a permis să folosim mii de dezvoltatori și\n"
"                    experți de afaceri pentru a construi sute de aplicații în doar câțiva ani."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_text_image
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_image
msgid ""
"The open source model of Odoo has allowed us to leverage thousands of "
"developers and business experts to build hundreds of apps in just a few "
"years."
msgstr ""
"Modelul open source al Odoo ne-a permis să folosim mii de dezvoltatori și "
"experți în afaceri pentru a construi sute de aplicații în doar câțiva ani."

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "The recipient <strong>subscribed to %s</strong> mailing list(s)"
msgstr ""
"Destinatarul <strong>s-a abonat la %s</strong> listă(e) de distribuție"

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "The recipient <strong>unsubscribed from %s</strong> mailing list(s)"
msgstr ""
"Destinatarul <strong>s-a dezabonat de la %s</strong> listă(e) de distribuție"

#. module: mass_mailing
#: code:addons/mass_mailing/models/utm.py:0
#, python-format
msgid ""
"The total percentage for an A/B testing campaign should be less than 100%"
msgstr ""
"Procentul total pentru o campanie de testare A/B ar trebui să fie mai mic de"
" 100%"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid ""
"The winner has already been sent. Use <b>Compare Version</b> to get an "
"overview of this A/B testing campaign."
msgstr ""
"Versiunea câștigătoare a fost deja trimisă. Folosiți <b>Comparați "
"versiunea</b> pentru a obține o privire de ansamblu asupra acestei campanii "
"de testare A/B."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "Then on"
msgstr "Apoi pe"

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "There are no recipients selected."
msgstr "Nu există destinatari selectați."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features
msgid "Third Feature"
msgstr "A treia facilitate"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "This"
msgstr "Acestă"

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid ""
"This email from can not be used with this mail server.\n"
"Your emails might be marked as spam on the mail clients."
msgstr ""
"Acest e-mail nu poate fi folosit cu acest server de e-mail.\n"
"E-mailurile dumneavoastră ar putea fi marcate ca spam în clienții de e-mail."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_form
msgid "This email is blacklisted for mass mailings. Click to unblacklist."
msgstr ""
"Acest e-mail este listat pe lista neagră pentru trimiteri în masă. Faceți "
"clic pentru a debloca lista."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can"
" contain more than strictly an email address."
msgstr ""
"Acest câmp este folosit pentru a căuta pe adresa de e-mail deoarece câmpul "
"de e-mail principal poate conține mai mult decât strict o adresă de e-mail."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__source_id
#: model:ir.model.fields,help:mass_mailing.field_mailing_trace__source_id
msgid ""
"This is the link source, e.g. Search Engine, another domain, or name of "
"email list"
msgstr ""
"Aceasta este sursa linkului, de ex. Motor de căutare, alt domeniu sau numele"
" listei de e-mailuri"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_res_config_settings__group_mass_mailing_campaign
msgid ""
"This is useful if your marketing campaigns are composed of several emails"
msgstr ""
"Acest lucru este util dacă campaniile dvs. de marketing sunt compuse din mai"
" multe e-mailuri"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid ""
"This is useful if your marketing campaigns are composed of several emails."
msgstr ""
"Acest lucru este util dacă campaniile dvs. de marketing sunt compuse din mai"
" multe e-mailuri."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid ""
"This tool is advised if your marketing campaign is composed of several "
"emails."
msgstr ""
"Acest instrument este recomandat dacă campania dvs. de marketing este "
"compusă din mai multe e-mailuri."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"This will send the email to all recipients. Do you still want to proceed ?"
msgstr ""
"Aceasta va trimite e-mailul către toți destinatarii. Vrei să continui?"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__reply_to_mode
msgid ""
"Thread: replies go to target document. Email: replies are routed to a given "
"email."
msgstr ""
"Fir: răspunsurile merg la documentul țintă. E-mail: răspunsurile sunt "
"direcționate către un e-mail dat."

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__title_id
msgid "Title"
msgstr "Titlu"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_three_columns
msgid ""
"To add a fourth column, reduce the size of these three columns using the "
"right icon of each block. Then, duplicate one of the columns to create a new"
" one as a copy."
msgstr ""
"Pentru a adăuga o a patra coloană, reduceți dimensiunea acestor trei coloane"
" folosind pictograma din dreapta a fiecărui bloc. Apoi, dublați una dintre "
"coloane pentru a crea una nouă ca copie."

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid ""
"To send the winner mailing the campaign should not have been completed."
msgstr ""
"Pentru a trimite campania câștigătoare, campania nu ar trebui să fi fost "

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid ""
"To send the winner mailing the same campaign should be used by the mailings"
msgstr ""
"Pentru a trimite campania câștigătoare, aceeași campanie ar trebui să fie "

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_company_team
msgid "Tony Fred, CEO"
msgstr "Tony Fred, CEO"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__total
msgid "Total"
msgstr "Total"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__ab_testing_total_pc
msgid "Total A/B test percentage"
msgstr "Total procent A/B test"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_tree
msgid "Total Bounces"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Tracking"
msgstr "Trasabilitate"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "Try different variations in the campaign to compare their"
msgstr "Încercați variante diferite în campanie pentru a le compara"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "Turn every feature into a benefit for your reader."
msgstr ""
"Transformă fiecare caracteristică într-un beneficiu pentru cititorul tău."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.social_links
msgid "Twitter"
msgstr "Twitter"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace__trace_type
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_trace_report__mailing_type
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
msgid "Type"
msgstr "Tip"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipul activității de excepție din înregistrare."

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_utm_campaign
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__campaign_id
msgid "UTM Campaign"
msgstr "Campanie UTM"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__medium_id
#: model:ir.model.fields,help:mass_mailing.field_mailing_trace__medium_id
msgid "UTM Medium: delivery method (email, sms, ...)"
msgstr "Mediu UTM: metoda de livrare (e-mail, sms, ...)"

#. module: mass_mailing
#: model:ir.model.fields.selection,name:mass_mailing.selection__mailing_trace__failure_type__unknown
msgid "Unknown error"
msgstr "Eroare necunoscută"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_unread
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_unread
msgid "Unread Messages"
msgstr "Mesaje necitite"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__message_unread_counter
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Contor mesaje necitite"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_social
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_footer_social_left
#: model_terms:ir.ui.view,arch_db:mass_mailing.theme_basic_template
msgid "Unsubscribe"
msgstr "Dezabonare"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe
msgid "Unsubscribed"
msgstr "Dezabonat"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact_subscription__unsubscription_date
msgid "Unsubscription Date"
msgstr "Data dezabonării"

#. module: mass_mailing
#: code:addons/mass_mailing/models/mailing.py:0
#, python-format
msgid "Unsupported mass mailing model %s"
msgstr "Model de e-mail masiv nesuportat %s"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe
msgid "Update my subscriptions"
msgstr "Actualizează-mi abonamentele"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_paragraph
msgid ""
"Usability improvements made on Odoo will automatically apply to all\n"
"                                of our fully integrated apps."
msgstr ""
"Îmbunătățirile de utilizare făcute pe Odoo se vor aplica automat la toate\n"
" aplicațiile noastre complet integrate."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_block
msgid ""
"Usability improvements made on Odoo will automatically apply to all\n"
"                    of our fully integrated apps."
msgstr ""
"Îmbunătățirile de utilizabilitate făcute pe Odoo se vor aplica\n"
"automat tuturor aplicațiilor noastre integrate în totalitate."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount2
msgid "Use This Promo Code BEFORE 1st of August"
msgstr "Utilizați acest cod promoțional ÎNAINTE de 1 august"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid "Use a dedicated server for mailings"
msgstr "Utilizați un server dedicat pentru mailings"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__mail_server_id
#: model:ir.model.fields,help:mass_mailing.field_res_config_settings__mass_mailing_outgoing_mail_server
#: model_terms:ir.ui.view,arch_db:mass_mailing.res_config_settings_view_form
msgid ""
"Use a specific mail server in priority. Otherwise Odoo relies on the first "
"outgoing mail server available (based on their sequencing) as it does for "
"normal mails."
msgstr ""
"Utilizați un server de e-mail specific cu prioritate. În caz contrar, Odoo "
"se bazează pe primul server de poștă de ieșire disponibil (bazat pe "
"secvențierea lor), așa cum se întâmplă pentru e-mailurile normale."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_coupon_code
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount2
msgid "Use now"
msgstr "Folosește acum"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid ""
"Use this component for creating a list of featured elements to which you "
"want to bring attention."
msgstr ""
"Utilizați această componentă pentru a crea o listă de elemente prezentate la"
" care doriți să atrageți atenția."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_media_list
msgid ""
"Use this snippet to build various types of components that feature a left- "
"or right-aligned image alongside textual content. Duplicate the element to "
"create a list that fits your needs."
msgstr ""
"Utilizați acest fragment pentru a crea diferite tipuri de componente care "
"prezintă o imagine aliniată la stânga sau la dreapta alături de conținut "
"textual. Duplicați elementul pentru a crea o listă care să se potrivească "
"nevoilor dvs."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_numbers
msgid "Useful options"
msgstr "Opțiuni utile"

#. module: mass_mailing
#: model:res.groups,name:mass_mailing.group_mass_mailing_user
msgid "User"
msgstr "Operator"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_res_users
msgid "Users"
msgstr "Utilizatori"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_search
msgid "Valid Email Recipients"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Vert. Alignment"
msgstr "Aliniere verticală"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Vertical Alignment"
msgstr "Aliniament Vertical"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_header_view
msgid "View Online"
msgstr "Vizualizare Online"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_alert_options
msgid "Warning"
msgstr "Atenție"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__warning_message
msgid "Warning Message"
msgstr "Mesaj de Avertizare"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__warning_message
msgid "Warning message displayed in the mailing form view"
msgstr ""
"Mesajul de avertizare afișat în formularul de vizualizare a e-mailului"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount1
msgid ""
"We are continuing to grow and we miss seeing you be a part of it! We've "
"increased store hours and have lot's of new brands available. To welcome you"
" back please accept this 20% discount on you next purchase by clicking the "
"button."
msgstr ""
"Continuăm să creștem și ne este dor să vă vedem că faceți parte din asta! Am"
" mărit orele de magazin și avem disponibile multe branduri noi. Pentru a vă "
"întâmpina înapoi, vă rugăm să acceptați această reducere de 20% la "
"următoarea achiziție, făcând clic pe buton."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_references
msgid "We are in good company."
msgstr "Suntem în companie bună."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_title_text
msgid ""
"We want to take this opportunity to welcome you to our ever-growing "
"community!<br/>"
msgstr ""
"Vrem să profităm de această ocazie pentru a vă întâmpina în comunitatea "
"noastră în continuă creștere!<br/>"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe
msgid ""
"We would appreciate if you provide feedback about why you updated<br/>your "
"subscriptions"
msgstr ""
"Am aprecia dacă ne oferiți feedback despre motivele pentru care v-ați "
"<br/>actualizat abonamentele"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_contact__website_message_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__website_message_ids
msgid "Website Messages"
msgstr "Mesaje Website"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_contact__website_message_ids
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__website_message_ids
msgid "Website communication history"
msgstr "Istoric comunicare website"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__sub_model_object_field
msgid ""
"When a relationship field is selected as first field, this field lets you "
"select the target field within the destination document model (sub-model)."
msgstr ""
"Atunci cand un camp de relatie este selectat drept primul camp, acest camp "
"va permite sa selectati campul tinta din cadrul modelului documentului "
"destinatie (sub-model)."

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mailing_mailing__sub_object
msgid ""
"When a relationship field is selected as first field, this field shows the "
"document model the relationship goes to."
msgstr ""
"Atunci cand un camp de relatie este selectat ca prim camp, acest camp arata "
"modelul documentului la care se refera relatia."

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.mailing_mailing_schedule_date_action
msgid "When do you want to send your mailing?"
msgstr "Când doriți să trimiteți corespondența?"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_ir_model__is_mailing_enabled
msgid ""
"Whether this model supports marketing mailing capabilities (notably email "
"and SMS)."
msgstr ""
"Dacă acest model suportă capacitățile de marketing prin e-mail (în special "
"e-mail și SMS)."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_hr_options
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Width"
msgstr "Lățime"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mailing_mailing__ab_testing_winner_selection
#: model:ir.model.fields,field_description:mass_mailing.field_utm_campaign__ab_testing_winner_selection
msgid "Winner Selection"
msgstr "Selectarea câștigătorului"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_paragraph
msgid ""
"With strong technical foundations, Odoo's framework is unique.\n"
"                                It provides top notch usability that scales across all apps."
msgstr ""
"Cu fundații tehnice puternice, cadrul Odoo este unic.\n"
"Oferă o utilizare ușoară care se întinde pe toate aplicațiile."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_text_block
msgid ""
"With strong technical foundations, Odoo's framework is unique.\n"
"                    It provides top notch usability that scales across all apps."
msgstr ""
"Cu baze tehnice solide, framework-ul Odoo este unic.\n"
"                    Oferă o utilizare de top care se extinde pe toate aplicațiile."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_image
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_picture
msgid ""
"With strong technical foundations, Odoo's framework is unique. It provides "
"<strong>top notch usability that scales across all apps</strong>."
msgstr ""
"Cu fundații tehnice puternice, cadrul Odoo este unic. Oferă <strong>o "
"utilizare ușoară care se întinde pe toate aplicațiile</strong>."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_two_cols
msgid ""
"Write one paragraph describing your product,\n"
"                                    services or a specific feature. To be successful\n"
"                                    your content needs to be useful to your readers."
msgstr ""
"Scrieți un paragraf care descrie produsul dvs.,\n"
"servicii sau o caracteristică specifică. Pentru a avea succes\n"
"conținutul dvs. trebuie să fie util cititorilor dvs."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features
msgid ""
"Write what the customer would like to know, <br/>not what you want to show."
msgstr "Scrieți ce ar dori să știe clientul,  <br/> nu ce doriți să arătați."

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#, python-format
msgid "You are not authorized to do this!"
msgstr "Nu sunteți autorizat să faceți acest lucru!"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribe
msgid "You are not subscribed to any of our mailing list."
msgstr "Nu sunteți abonat la niciuna din lista noastră de corespondență."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_features_grid
msgid "You can edit colors and backgrounds to highlight features."
msgstr "Puteți edita culori și fundaluri pentru a evidenția caracteristicile."

#. module: mass_mailing
#: code:addons/mass_mailing/wizard/mailing_list_merge.py:0
#, python-format
msgid "You can only apply this action from Mailing Lists."
msgstr "Puteți aplica această acțiune numai din liste de distribuție."

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_create_mass_mailings_from_campaign
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailings_from_campaign
msgid ""
"You don't need to import your mailing lists, you can easily\n"
"                send emails<br> to any contact saved in other Odoo apps."
msgstr ""
"Nu este nevoie să importați listele dvs. de e-mail, puteți cu ușurință să\n"
"trimiteți e-mailuri <br> către orice contact salvat în alte aplicații Odoo."

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#, python-format
msgid "You have been <strong>successfully unsubscribed from %s</strong>."
msgstr "Ați fost <strong>dezabonat cu succes de la %s</strong>."

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#, python-format
msgid "You have been <strong>successfully unsubscribed</strong>."
msgstr "Ați fost <strong>dezabonat cu succes </strong>."

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#, python-format
msgid ""
"You have been successfully <strong>added to our blacklist</strong>. You will"
" not be contacted anymore by our services."
msgstr ""
"Ați fost cu succes <strong>adăugat la lista noastră neagră</strong>. Nu veți"
" mai fi contactat de serviciile noastre."

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#, python-format
msgid ""
"You have been successfully <strong>removed from our blacklist</strong>. You "
"are now able to be contacted by our services."
msgstr ""
"Ați fost cu succes <strong>eliminat din lista noastră neagră</strong>. Acum "
"puteți fi contactat de serviciile noastre."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribed
msgid "You have been successfully <strong>unsubscribed</strong>!"
msgstr "Ați fost cu succes <strong>dezabonat</strong>!"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.unsubscribed
msgid ""
"You were still subscribed to those newsletters. You will not receive any "
"news from them anymore:"
msgstr ""
"Ești încă abonat la aceste buletine informative. Nu veți mai primi nicio "
"noutate de la ei:"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_header_social
msgid "Your Logo"
msgstr "Logo dvs."

#. module: mass_mailing
#. openerp-web
#: code:addons/mass_mailing/static/src/js/unsubscribe.js:0
#, python-format
msgid "Your changes have been saved."
msgstr "Modificările au fost salvate."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_title_text
msgid ""
"Your platform is ready for work. It will help you reduce the costs of "
"digital signage, attract new customers and increase sales."
msgstr ""
"Platforma dvs. este gata de lucru. Vă va ajuta să reduceți costurile "
"semnalizării digitale, să atrageți clienți noi și să creșteți vânzările."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_mail_block_discount2
msgid "and save $20 on your next order!"
msgstr "și economisiți 20$ la următoarea comandă!"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "e.g. Check it out before it's too late!"
msgstr "de exemplu. Verifică înainte să fie prea târziu!"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_list_view_form_simplified
msgid "e.g. Consumer Newsletter"
msgstr "de ex: Buletin Informativ pentru Consumator"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mailing_contact_view_form
msgid "e.g. John Smith"
msgstr "de exemplu. John Smith"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "e.g. New Sale on all T-shirts"
msgstr "de exemplu. Vânzare nouă la toate tricourile"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_test_form
msgid ""
"<EMAIL>\n"
"<EMAIL>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.layout
msgid "free website"
msgstr "site gratuit"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "from the same campaign."
msgstr "din aceeași campanie."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "having the"
msgstr "având"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid ""
"is the winner of the A/B testing campaign and has been sent to all remaining"
" recipients."
msgstr ""
"este câștigătorul campaniei de testare A/B și a fost trimis tuturor "
"destinatarilor rămași."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "on"
msgstr "pe"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "other versions"
msgstr "alte versiuni"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mailing_mailing_schedule_date
msgid "schedule a mailing"
msgstr "programați o campanie de marketing"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "the"
msgstr " "

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "to the remaining"
msgstr "la rămășițele"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "will be sent"
msgstr "va fi trimis"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "will be sent to the remaining recipients."
msgstr "va fi trimis la destinatarii rămași."

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.ab_testing_description
msgid "will receive this"
msgstr "va primi acest"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.layout
msgid "with"
msgstr "cu"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "⌙ Active"
msgstr "⌙ Activ"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.s_rating_options
msgid "⌙ Inactive"
msgstr "⌙ Inactiv"
