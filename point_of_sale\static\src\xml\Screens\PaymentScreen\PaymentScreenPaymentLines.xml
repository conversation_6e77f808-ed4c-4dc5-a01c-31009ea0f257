<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="PaymentScreenPaymentLines" owl="1">
            <div class="paymentlines">
                <t t-foreach="props.paymentLines" t-as="line" t-key="line.cid">
                    <t t-if="line.selected">
                        <div class="paymentline selected"
                             t-att-class="selectedLineClass(line)"
                             t-on-click="trigger('select-payment-line', { cid: line.cid })">
                             <div class="payment-name">
                                 <t t-esc="line.payment_method.name" />
                             </div>
                            <div class="payment-amount">
                                <t t-if="line and line.payment_status and ['done', 'waitingCard', 'waiting', 'reversing', 'reversed'].includes(line.payment_status)">
                                        <t t-esc="env.pos.format_currency_no_symbol(line.get_amount())" />
                                </t>
                                <t t-else="">
                                        <t t-esc="formatLineAmount(line)" />
                                </t>
                            </div>
                            <t t-if="!line.payment_status or !['done', 'reversed'].includes(line.payment_status)">
                                <div class="delete-button"
                                    t-on-click="trigger('delete-payment-line', { cid: line.cid })"
                                    aria-label="Delete" title="Delete">
                                    <i class="fa fa-times-circle" />
                                </div>
                            </t>
                        </div>
                        <t t-if="line and line.payment_status">
                            <PaymentScreenElectronicPayment line="line" />
                        </t>
                    </t>
                    <t t-else="">
                        <div class="paymentline"
                             t-att-class="unselectedLineClass(line)"
                             t-on-click="trigger('select-payment-line', { cid: line.cid })">
                             <div class="payment-name">
                                 <t t-esc="line.payment_method.name" />
                             </div>
                            <div class="payment-amount">
                                <t t-if="line and line.payment_status and ['done', 'waitingCard', 'waiting', 'reversing', 'reversed'].includes(line.payment_status)">
                                        <t t-esc="env.pos.format_currency_no_symbol(line.get_amount())" />
                                </t>
                                <t t-else="">
                                        <t t-esc="formatLineAmount(line)" />
                                </t>
                            </div>
                            <t t-if="!line.payment_status or !['done', 'reversed'].includes(line.payment_status)">
                                <div class="delete-button"
                                    t-on-click="trigger('delete-payment-line', { cid: line.cid })"
                                    aria-label="Delete" title="Delete">
                                    <i class="fa fa-times-circle" />
                                </div>
                            </t>
                        </div>
                    </t>
                </t>
            </div>
    </t>

</templates>
