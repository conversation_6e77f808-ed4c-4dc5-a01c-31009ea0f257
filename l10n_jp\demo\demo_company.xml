<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_jp" model="res.partner">
        <field name="name">JP Company</field>
        <field name="vat">7482543580381</field>
        <field name="street">池上通り</field>
        <field name="city">品川区</field>
        <field name="country_id" ref="base.jp"/>
        <field name="state_id" ref="base.state_jp_jp-19"/>
        <field name="zip">140-0004</field>
        <field name="phone">+81 90-1234-5678</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.jpexample.com</field>
    </record>

    <record id="demo_company_jp" model="res.company">
        <field name="name">JP Company</field>
        <field name="partner_id" ref="partner_demo_company_jp"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_jp')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_jp.demo_company_jp'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_jp.l10n_jp1')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_jp.demo_company_jp')"/>
    </function>
</odoo>
