# #-#-#-#-#  es_DO.po (Odoo 9.0)  #-#-#-#-#
# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * stock
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2015-2016
# <PERSON>, 2016
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2015
# <PERSON> <<EMAIL>>, 2015
# #-#-#-#-#  es_DO.po (Odoo 9.0)  #-#-#-#-#
# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * stock
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2015-2016
# Je<PERSON><PERSON> <<EMAIL>>, 2015
# <PERSON> <<EMAIL>>, 2015
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-10 11:34+0000\n"
"PO-Revision-Date: 2016-06-21 16:52+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Dominican Republic) (http://www.transifex.com/odoo/"
"odoo-9/language/es_DO/)\n"
"Language: es_DO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"#-#-#-#-#  es_DO.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"#-#-#-#-#  es_DO.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock
#: code:addons/stock/models/stock_move.py:308
#, python-format
msgid " (%s reserved)"
msgstr " (%s reservado)"

#. module: stock
#: code:addons/stock/models/stock_move.py:311
#, python-format
msgid " (reserved)"
msgstr " (reservado)"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_state
msgid ""
" * Draft: not confirmed yet and will not be scheduled until confirmed.\n"
" * Waiting Another Operation: waiting for another move to proceed before it "
"becomes automatically available (e.g. in Make-To-Order flows).\n"
" * Waiting: if it is not ready to be sent because the required products "
"could not be reserved.\n"
" * Ready: products are reserved and ready to be sent. If the shipping policy "
"is 'As soon as possible' this happens as soon as anything is reserved.\n"
" * Done: has been processed, can't be modified or cancelled anymore.\n"
" * Cancelled: has been cancelled, can't be confirmed anymore."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_form
msgid "#Products"
msgstr "Productos"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:480
#, python-format
msgid "%s: Supply Product from %s"
msgstr "%s: proveer producto de %s"

#. module: stock
#: code:addons/stock/models/res_company.py:24
#, python-format
msgid "%s: Transit Location"
msgstr "%s: ubicación de tránsito"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_usage
msgid ""
"* Vendor Location: Virtual location representing the source location for "
"products coming from your vendors\n"
"* View: Virtual location used to create a hierarchical structures for your "
"warehouse, aggregating its child locations ; can't directly contain "
"products\n"
"* Internal Location: Physical locations inside your own warehouses,\n"
"* Customer Location: Virtual location representing the destination location "
"for products sent to your customers\n"
"* Inventory Loss: Virtual location serving as counterpart for inventory "
"operations used to correct stock levels (Physical inventories)\n"
"* Procurement: Virtual location serving as temporary counterpart for "
"procurement operations when the source (vendor or production) is not known "
"yet. This location should be empty when the procurement scheduler has "
"finished running.\n"
"* Production: Virtual counterpart location for production operations: this "
"location consumes the raw material and produces finished products\n"
"* Transit Location: Counterpart location that should be used in inter-"
"company or inter-warehouses operations"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ", if accounting or purchase is installed"
msgstr "si los módulos de contabilidad o compras están instalados"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "- The Odoo Team"
msgstr "El equipo de Odoo"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid ""
"<br/>\n"
"                    <strong>Here is your current inventory: </strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"label label-default text-center odoo_purple\">'Available'</"
"span>"
msgstr ""
"<span class=\"label label-default text-center odoo_purple\">'Disponible'</"
"span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"label label-default text-center odoo_purple\">Click on <span "
"class=\"fa fa-truck\"/> Delivery</span>"
msgstr ""
"<span class=\"label label-default text-center odoo_purple\">Haga clic en "
"<span class=\"fa fa-truck\"/> Entrega</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"label label-default text-center odoo_purple\">Click on <span "
"class=\"fa fa-truck\"/> Shipment</span>"
msgstr ""
"<span class=\"label label-default text-center odoo_purple\">Haga clic "
"en<span class=\"fa fa-truck\"/> Envío</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"label label-default text-center odoo_purple\">Confirm Order</"
"span>"
msgstr ""
"<span class=\"label label-default text-center odoo_purple\">Confirmar orden</"
"span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"label label-default text-center odoo_purple\">Confirm Sale</"
"span>"
msgstr ""
"<span class=\"label label-default text-center odoo_purple\">Confirme Venta</"
"span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"label label-default text-center odoo_purple\">Find Incoming "
"Shipments</span>"
msgstr ""
"<span class=\"label label-default text-center odoo_purple\">Encontrar envíos "
"entrantes</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"label label-default text-center odoo_purple\">Process the "
"products</span>"
msgstr ""
"<span class=\"label label-default text-center odoo_purple\">Procesar los "
"productos</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"label label-default text-center odoo_purple\">Validate the "
"Delivery</span>"
msgstr ""
"<span class=\"label label-default text-center odoo_purple\">Validar la "
"entrega</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"label label-default text-center odoo_purple\">Validate the "
"Receipt Order</span>"
msgstr ""
"<span class=\"label label-default text-center odoo_purple\">Validar la Orden "
"Recibo</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Forecasted</span>"
msgstr "<span class=\"o_stat_text\">Pronosticada</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
msgid ""
"<span class=\"o_stat_text\">Min :</span>\n"
"                                <span class=\"o_stat_text\">Max:</span>"
msgstr ""
"<span class=\"o_stat_text\">Min :</span>\n"
"                                <span class=\"o_stat_text\">Max:</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid ""
"<span class=\"o_stat_text\">Min:</span>\n"
"                                <span class=\"o_stat_text\">Max:</span>"
msgstr ""
"<span class=\"o_stat_text\">Min :</span>\n"
"<span class=\"o_stat_text\">Max:</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/"
">\n"
"                                        <strong>Import a file</strong><br/>\n"
"                                        <span class=\"small\">Recommended if "
"&gt;100 products</span>\n"
"                                    </span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/"
">\n"
"                                        <strong>Import</strong><br/>\n"
"                                        <span class=\"small\">&gt; 50 "
"vendors</span>\n"
"                                    </span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/"
">\n"
"                                        <strong> Create manually</strong><br/"
">\n"
"                                        <span class=\"small\">&lt; 50 "
"vendors</span>\n"
"                                    </span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <span class=\"fa\" data-icon=\"\"/"
">\n"
"                                        <strong> Create manually</strong><br/"
">\n"
"                                        <span class=\"small\">Recommended if "
"&lt;100 products</span>\n"
"                                    </span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>From the Inventory "
"application</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"<strong>Desde la aplicación de inventario</strong>\n"
"</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>From the Purchase "
"application</strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"<strong>Desde la aplicación de inventario</strong>\n"
"</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span class=\"panel-title\">\n"
"                                        <strong>From the Sales application</"
"strong>\n"
"                                    </span>"
msgstr ""
"<span class=\"panel-title\">\n"
"<strong>Desde la aplicación de ventas</strong>\n"
"</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Customer Address:</strong></span>"
msgstr "<span><strong>Dirección del cliente:</strong></span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Delivery Address:</strong></span>"
msgstr "<strong>Dirección de Envío</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Partner Address:</strong></span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Warehouse Address:</strong></span>"
msgstr "<span><strong>Dirección del almacen:</strong></span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<span>New</span>"
msgstr "<span>Nuevo</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span>No reservation or quantity done yet.</span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<span>View</span>"
msgstr "<span>Ver</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span>You need to install the Accounting or Purchases app to manage vendors."
"</span>"
msgstr ""
"<span>Necesita instalar las aplicaciones de Contabilidad o Compras para "
"gestionar proveedores.</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span>You need to install the Purchases Management app for this flow.</span>"
msgstr ""
"<span>Necesita instalar la aplicación de Gestión de compras para este flujo."
"</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<span>You need to install the Sales Management app for this flow.</span>"
msgstr ""
"<span>Necesita instalar la aplicación de gestión de ventas para este flujo.</"
"span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "<span>kg</span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "<span>m³</span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_head
msgid ""
"<strong>\n"
"                The done move line has been corrected.\n"
"            </strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "<strong>Activate <i>Track lots or serial numbers</i></strong> in your"
msgstr ""
"<strong>Activar <i>Seguimiento de lotes o números de serie</i></strong> en tu"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>Automated flows</strong>: from sale to delivery, and purchase to "
"reception"
msgstr ""
"<strong>Flujos automáticos</strong>: desde la venta hasta la entrega y "
"compra a la recepción de"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "<strong>Automated replenishment rules</strong>"
msgstr "<strong>Reglas de reposición automática</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>Available products</strong> are currently available for use in "
"filling a new order for purposes such as production or distribution. This "
"quantity does not include items already allocated to other orders or items "
"that are in transit from a supplier"
msgstr ""
"<strong>Productos disponibles</strong> están disponibles para su uso en el "
"llenado de un nuevo orden con fines de producción o distribución. Esta "
"cantidad no incluye artículos ya asignados a otras órdenes o elementos que "
"están en tránsito de un proveedor"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>Buy:</strong> the product is bought from a vendor through a Purchase "
"Order"
msgstr ""
"<strong>Comprar:</strong> realiza la compra de un vendedor a través de una "
"orden de compra"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Commitment Date</strong>"
msgstr "<strong>Fecha factura:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>Consumable products</strong> are always assumed to be in sufficient "
"quantity in your stock, therefore their available quantities are not tracked"
msgstr ""
"<strong>Productos de consumo</strong> siempre se supone que para ser en "
"cantidad suficiente en su stock, por lo tanto no se realiza el seguimiento "
"de las cantidades disponibles"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Date</strong>"
msgstr "<strong>Fecha</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<strong>Delivered Quantity</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "<strong>Enjoy your Inventory management with Odoo!</strong>"
msgstr "<strong>Disfrute de sus inventarios con Odoo!</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "<strong>Import data</strong>"
msgstr "<strong>Importación de datos</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Inventory</strong>"
msgstr "<strong>Inventario</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Location</strong>"
msgstr "<strong>Ubicación</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>Make to Order:</strong> the product is acquired only as demand "
"requires, each time a Sales Order is confirmed. This does not modify stock "
"in the medium term because you restock with the exact amount that was ordered"
msgstr ""
"<strong>Hacen a pedido:</strong> producto se adquiere sólo según la demanda, "
"se confirma cada vez que un pedido de ventas. No se modifique valores en el "
"mediano plazo porque reponer con la cantidad exacta que se ordenó"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>Make to Stock:</strong> your customers are supplied from available "
"stock. If the quantities in stock are too low to fulfill the order, a "
"Purchase Order is generated according the minimum stock rules in order to "
"get the products required"
msgstr ""
"<strong>Realizar en Stock:</strong> sus clientes vienen de stock disponible. "
"Si las cantidades en stock son demasiado bajas para cumplir la orden, se "
"genera una orden de compra según las reglas de stock mínimas para obtener "
"los productos requeridos"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>Manufacture:</strong> the product is manufactured internally or the "
"service is supplied from internal resources"
msgstr ""
"<strong>De la fabricación:</strong> internamente se fabrica el producto o el "
"servicio se provee de recursos internos"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_warehouse_orderpoint_kanban
msgid "<strong>Max qty :</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_warehouse_orderpoint_kanban
msgid "<strong>Min qty :</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>No address defined on the supplier partner:</strong> you have to "
"complete an address for the default supplier for the product concerned."
msgstr ""
"<strong>Sin dirección definida en el proveedor socio:</strong> usted tiene "
"que completar una dirección para el proveedor predeterminado para el "
"producto en cuestión."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>No bill of materials defined for production:</strong> you need to "
"create a BoM or indicate that the product can be purchased instead."
msgstr ""
"<strong>No lista de materiales para producción:</strong> necesita para crear "
"una LDM o indicar que el producto puede adquirirse en su lugar."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>No quantity available in stock:</strong> you have to create a "
"reordering rule and put it in the order, or manually procure it."
msgstr ""
"<strong>Ninguna cantidad en stock:</strong> tienes que crear una regla de "
"reaprovisionamiento y ponerlo en el orden o manualmente lo procuran."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>No supplier available for a purchase:</strong> you have to define a "
"supplier in the Procurements tab of the product form."
msgstr ""
"<strong>No disponible para la compra del proveedor:</strong> tienes que "
"definir un proveedor en la ficha de contrataciones de la forma del producto."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>On Hand products</strong> are physically located in the warehouse "
"location at the current time. This includes items that are already allocated "
"to fulfilling production needs or sales orders"
msgstr ""
"<strong>Productos de la mano</strong> son físicamente situado en la "
"ubicación del almacén en el momento actual. Esto incluye artículos que ya se "
"destinan a satisfacer las necesidades de producción o pedidos"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Order (Origin)</strong>"
msgstr "<strong>Orden (origen)</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<strong>Ordered Quantity</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Package</strong>"
msgstr "<strong>Paquete</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Product</strong>"
msgstr "<strong>Producto</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Production Lot</strong>"
msgstr "<strong>Lote de producción</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_kanban
msgid "<strong>Qty: </strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Quantity</strong>"
msgstr "<strong>Cantidad</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Scheduled Date</strong>"
msgstr "<strong>Fecha prevista</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>Service products</strong> are non-material products provided by a "
"company or an individual"
msgstr ""
"<strong>Productos de servicio</strong> son productos de material no "
"suministrados por una empresa o un individuo"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>State</strong>"
msgstr "<strong>Estado</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>Stockable products</strong> are subject to the full inventory "
"management system: minimum stock rules, automatic procurement, etc."
msgstr ""
"<strong>Productos almacenajes</strong> están sujetos al sistema de gestión "
"de inventario completo: reglas de stock mínimas, aprovisionamiento "
"automático, etc.."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_head
msgid "<strong>The initial demand has been updated.</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Total Quantity</strong>"
msgstr "<strong>Cantidad total</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "<strong>Warehouse Locations</strong>"
msgstr "<strong>Localizaciones de almacén</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"<strong>When you process an incoming shipment, internal transfer or "
"delivery</strong>, assign a lot number or different lot numbers or serial "
"numbers to a product by clicking on the <span class=\"fa fa-list\"/> icon"
msgstr ""
"<strong>Cuando se procesa un envío entrante, transferencia interna o "
"entrega</strong>, asignar mucho número o diferentes lote números o números "
"de serie de un producto haciendo clic en el <span class='fa fa-list'></span> "
"icono"

#. module: stock
#: code:addons/stock/models/stock_inventory.py:115
#, python-format
msgid "A Pack"
msgstr "Un paquete"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "A classic purchase flow looks like the following:"
msgstr "Un flujo de compra clásico se parece a lo siguiente:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "A classic sales flow looks like the following:"
msgstr "Un flujo de ventas clásico se parece a lo siguiente:"

#. module: stock
#: code:addons/stock/models/stock_move_line.py:85
#, python-format
msgid "A done move line should never have a reserved quantity."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_quant.py:75
#, python-format
msgid "A serial number should only be linked to a single product."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_product_type
msgid ""
"A stockable product is a product for which you manage stock. The \"Inventory"
"\" app has to be installed.\n"
"A consumable product, on the other hand, is a product for which stock is not "
"managed.\n"
"A service is a non-material product you provide.\n"
"A digital content is a non-material product you sell online. The files "
"attached to the products are the one that are sold on the e-commerce such as "
"e-books, music, pictures,... The \"Digital Product\" module has to be "
"installed."
msgstr ""

#. module: stock
#: model:res.groups,name:stock.group_warning_stock
msgid "A warning can be set on a partner (Stock)"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "API Documentation"
msgstr "Documentación de la API"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Accurate visibility on all your operations"
msgstr "Visibilidad correcta en todas sus operaciones"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_action
msgid "Action"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_active
#: model:ir.model.fields,field_description:stock.field_stock_incoterms_active
#: model:ir.model.fields,field_description:stock.field_stock_location_active
#: model:ir.model.fields,field_description:stock.field_stock_location_path_active
#: model:ir.model.fields,field_description:stock.field_stock_location_route_active
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_active
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_active
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_active
msgid "Active"
msgstr "Activo"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings_group_stock_adv_location
msgid ""
"Add and customize route operations to process product moves in your "
"warehouse(s): e.g. unload > quality control > stock for incoming products, "
"pick > pack > ship for outgoing products. \n"
" You can also set putaway strategies on warehouse locations in order to send "
"incoming products into specific child locations straight away (e.g. specific "
"bins, racks)."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Add and customize route operations to process product moves in your "
"warehouse(s): e.g. unload > quality control > stock for incoming products, "
"pick > pack > ship for outgoing products. You can also set putaway "
"strategies on warehouse locations in order to send incoming products into "
"specific child locations straight away (e.g. specific bins, racks)."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Additional Info"
msgstr "Información adicional"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_comment
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Additional Information"
msgstr "Información adicional"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_partner_id
msgid "Address"
msgstr "Dirección"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Advanced"
msgstr "Avanzado"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Advanced Scheduling"
msgstr ""

#. module: stock
#: selection:stock.move,procure_method:0
msgid "Advanced: Apply Procurement Rules"
msgstr "Avanzado: aplicar reglas de aprovisionamiento"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "All"
msgstr "Todos"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_type_list
msgid "All Operations"
msgstr "Todas las operaciones"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_picking_action_picking_type
msgid "All Transfers"
msgstr "Todas las transferencias"

#. module: stock
#: selection:procurement.group,move_type:0
msgid "All at once"
msgstr "Todo junto"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid ""
"All items couldn't be shipped, the remaining ones will be shipped as soon as "
"they become available."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_inventory.py:105
#, python-format
msgid "All products"
msgstr "Todos los productos"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_returned_move_ids
msgid "All returned moves"
msgstr "Todas las devoluciones"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_picking_type_entire_packs
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_show_entire_packs
msgid "Allow moving packs"
msgstr "Permiten mover paquetes"

#. module: stock
#: code:addons/stock/models/stock_quant.py:81
#, python-format
msgid "An incoming date cannot be set to an untracked product."
msgstr ""

#. module: stock
#: model:stock.inventory.line,product_name:stock.stock_inventory_line_2
msgid "Apple In-Ear Headphones"
msgstr ""

#. module: stock
#: model:stock.inventory.line,product_name:stock.stock_inventory_line_4
msgid "Apple Wireless Keyboard"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Applicable On"
msgstr "Aplicable en"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route_product_selectable
msgid "Applicable on Product"
msgstr "Aplicable en el producto"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route_product_categ_selectable
msgid "Applicable on Product Category"
msgstr "Aplicable en la categoria de productos"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route_warehouse_selectable
msgid "Applicable on Warehouse"
msgstr "Aplicable en el almacén"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_rule_form
msgid "Applied On"
msgstr "Aplicado en"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_incoterms_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_location_path_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_warehouse_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_rule_filter
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Archived"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid ""
"Are you sure you want to confirm this operation? This may lead to "
"inconsistencies in your inventory."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_overprocessed_transfer
msgid "Are you sure you want to validate this picking?"
msgstr ""

#. module: stock
#: selection:stock.picking,move_type:0
msgid "As soon as possible"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Assign Owner"
msgstr "Asignar propietario"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Assigned Moves"
msgstr "Movimientos asignados"

#. module: stock
#: selection:stock.quantity.history,compute_at_date:0
msgid "At a Specific Date"
msgstr ""

#. module: stock
#: model:ir.ui.menu,name:stock.menu_variants_action
msgid "Attribute Values"
msgstr ""

#. module: stock
#: model:ir.ui.menu,name:stock.menu_attribute_action
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Attributes"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_path_auto
msgid "Automatic Move"
msgstr "Movimiento automático"

#. module: stock
#: selection:stock.location.path,auto:0
msgid "Automatic No Step Added"
msgstr "Automático sin añadir paso"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_string_availability_info
msgid "Availability"
msgstr "Disponibilidad"

#. module: stock
#: selection:stock.move,state:0
msgid "Available"
msgstr "Reservado"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_search_form_view_stock
msgid "Available Products"
msgstr "Productos disponibles"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_backorder_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_backorder_id
msgid "Back Order of"
msgstr "Pedido en espera de"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Back Orders"
msgstr "Pedidos en espera"

#. module: stock
#: code:addons/stock/wizard/stock_backorder_confirmation.py:20
#, python-format
msgid "Back order <em>%s</em> <b>cancelled</b>."
msgstr "Nuevo orden <em>%s</em> <b>cancelado</b>."

#. module: stock
#: code:addons/stock/models/stock_picking.py:774
#, python-format
msgid "Back order <em>%s</em> <b>created</b>."
msgstr "Se ha creado la entrega parcial <em>%s</em>."

#. module: stock
#: model:ir.model,name:stock.model_stock_backorder_confirmation
msgid "Backorder Confirmation"
msgstr "Confirmación de pedidos pendientes"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "Backorder creation"
msgstr "Creación de pedidos pendientes"

#. module: stock
#: code:addons/stock/models/stock_picking.py:73
#, python-format
msgid "Backorder exists"
msgstr "La entrega parciale existe"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_backorder
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Backorders"
msgstr "Entregas parciales"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "Barcode"
msgstr "Código de barras"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_barcode_nomenclature_id
msgid "Barcode Nomenclature"
msgstr "Nomenclatura de código de barras"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_wms_barcode_nomenclature_all
msgid "Barcode Nomenclatures"
msgstr "Nomenclaturas de código de barras"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings_module_stock_barcode
msgid "Barcode Scanner"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings_module_stock_picking_batch
msgid "Batch Pickings"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Before creating your products, here are a few concepts your should "
"understand:"
msgstr ""
"Antes de crear sus productos, aquí están algunos conceptos su debe "
"comprender:"

#. module: stock
#: model:stock.location,name:stock.stock_location_4
msgid "Big Vendors"
msgstr "Proveedores Grandes"

#. module: stock
#: selection:res.partner,picking_warn:0
msgid "Blocking Message"
msgstr ""

#. module: stock
#: model:stock.inventory.line,product_name:stock.stock_inventory_line_15
msgid "Bose Mini Bluetooth Speaker"
msgstr ""

#. module: stock
#: selection:stock.warehouse,delivery_steps:0
msgid "Bring goods to output location before shipping (Pick + Ship)"
msgstr ""
"Llevar bienes a la ubicación de salida antes del envío (Empaquetado + envío)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_quant_ids
msgid "Bulk Content"
msgstr "Contenido completo"

#. module: stock
#: selection:procurement.rule,action:0
msgid "Buy"
msgstr ""

#. module: stock
#: selection:product.template,tracking:0
msgid "By Lots"
msgstr "Por lote"

#. module: stock
#: selection:product.template,tracking:0
msgid "By Unique Serial Number"
msgstr "Por nº de serie único"

#. module: stock
#: code:addons/stock/models/stock_move.py:600
#, python-format
msgid ""
"By changing this quantity here, you accept the new quantity as complete: "
"Odoo will not automatically generate a back order."
msgstr ""
"Cambiando esta cantidad aquí, acepta la nueva cantidad como completa: Odoo "
"no generará automáticamente una entrega parcial."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"By default, Odoo measures products by 'units', which are generic and "
"represent just about anything"
msgstr ""
"Por defecto, Odoo mide productos por unidades, que son genéricos y "
"representan cualquier cosa"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_procure_method
msgid ""
"By default, the system will take from the stock in the source location and "
"passively wait for availability.The other possibility allows you to directly "
"create a procurement on the source location (and thus ignore its current "
"stock) to gather products. If we want to chain moves and have this one to "
"wait for the previous,this second option should be chosen."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_active
msgid ""
"By unchecking the active field, you may hide a location without deleting it."
msgstr ""
"Si el campo activo se desmarca, permite ocultar la ubicación sin eliminarla."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_incoterms_active
msgid ""
"By unchecking the active field, you may hide an INCOTERM you will not use."
msgstr "Desmarcando el campo activo, podrá ocultar un INCOTERM que no use."

#. module: stock
#: model:stock.incoterms,name:stock.incoterm_CIP
msgid "CARRIAGE AND INSURANCE PAID TO"
msgstr ""

#. module: stock
#: model:stock.incoterms,name:stock.incoterm_CPT
msgid "CARRIAGE PAID TO"
msgstr ""

#. module: stock
#: model:stock.incoterms,name:stock.incoterm_CFR
msgid "COST AND FREIGHT"
msgstr ""

#. module: stock
#: model:stock.incoterms,name:stock.incoterm_CIF
msgid "COST, INSURANCE AND FREIGHT"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_calendar
msgid "Calendar View"
msgstr "Vista calendario"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:428
#, python-format
msgid "Can't find any customer or supplier location."
msgstr "No se puede encontrar ninguna ubicación de cliente o proveedor"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:475
#, python-format
msgid "Can't find any generic Make To Order route."
msgstr "No se puede encontrar una ruta genérica para Bajo pedido"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view2
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
#: model_terms:ir.ui.view,arch_db:stock.view_overprocessed_transfer
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_compute_wizard
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quantity_history
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
msgid "Cancel"
msgstr "Cancelar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Cancel Inventory"
msgstr "Cancelar el inventario"

#. module: stock
#: selection:stock.inventory,state:0 selection:stock.move,state:0
#: selection:stock.picking,state:0
msgid "Cancelled"
msgstr "Cancelada"

#. module: stock
#: code:addons/stock/models/stock_move.py:468
#, python-format
msgid "Cannot unreserve a done move"
msgstr "No se puede quitar la reserva de movimiento realizado"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_route_from_categ_ids
#: model:ir.model.fields,field_description:stock.field_product_template_route_from_categ_ids
msgid "Category Routes"
msgstr "Categoría de rutas"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_move_dest_exists
msgid "Chained Move Exists"
msgstr "El movimiento encadenado existe"

#. module: stock
#: model:ir.model,name:stock.model_stock_change_product_qty
msgid "Change Product Quantity"
msgstr "Cambiar cantidad producto"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Change must be higher than"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line_move_id
msgid "Change to a better name"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Check Availability"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_has_packages
msgid "Check the existence of destination packages on move lines"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_move_line_exist
msgid "Check the existence of pack operation on the picking"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_return_location
msgid "Check this box to allow using this location as a return location."
msgstr ""
"Marque esta casilla para permitir utilizar esta ubicación como una dirección."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_scrap_location
#: model:ir.model.fields,help:stock.field_stock_move_scrapped
msgid ""
"Check this box to allow using this location to put scrapped/damaged goods."
msgstr ""
"Marque esta opción para permitir utilizar esta ubicación para poner "
"mercancías desechadas/defectuosas."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_product_qty
msgid "Checked Quantity"
msgstr "Cantidad comprobada"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quantity_history_date
msgid "Choose a date to get the inventory at that date"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quantity_history_compute_at_date
msgid ""
"Choose to analyze the current inventory or from a specific date in the past."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quantity_history
msgid "Choose your date"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_picking_form
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_all
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_backorder
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_done
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_done_grouped
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_late
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_ready
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_waiting
#: model_terms:ir.actions.act_window,help:stock.stock_picking_action_picking_type
msgid "Click here to create a new transfer."
msgstr "Haga clic aquí para crear a una nueva transferencia."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_stock_scrap
msgid "Click here to scrap products."
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid "Click to add a location."
msgstr "Haga clic para agregar una ubicación."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_production_lot_form
msgid "Click to add a lot/serial number."
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint_form
msgid "Click to add a reordering rule."
msgstr "Haga clic para agregar una regla de reaprovisionamiento."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_routes_form
msgid "Click to add a route."
msgstr "Haga clic para agregar una ruta."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_picking_type_action
msgid "Click to create a new operation type."
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_action
msgid "Click to create a stock movement."
msgstr "Haga clic para crear un movimiento de acciones."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree
msgid "Click to create a stock operation."
msgstr "Haga clic para crear una operación de almacén."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.product_template_action_product
msgid "Click to define a new product."
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_picking_type_list
msgid "Click to define a new transfer."
msgstr "Haga clic para definir a una nueva transferencia."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_warehouse_form
msgid "Click to define a new warehouse."
msgstr "Haga clic para definir un nuevo almacén."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_receipt_picking_move
msgid "Click to register a product receipt."
msgstr "Haga click para registrar el recibo de un producto."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_inventory_form
msgid "Click to start an inventory."
msgstr "Haga clic en para iniciar un inventario."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_incoterms_code
msgid "Code"
msgstr "Código"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_color
msgid "Color"
msgstr "Color"

#. module: stock
#: model:ir.model,name:stock.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_company_id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_company_id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_company_id
#: model:ir.model.fields,field_description:stock.field_stock_location_company_id
#: model:ir.model.fields,field_description:stock.field_stock_location_path_company_id
#: model:ir.model.fields,field_description:stock.field_stock_location_route_company_id
#: model:ir.model.fields,field_description:stock.field_stock_move_company_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_company_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_company_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_company_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_company_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_company_id
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Company"
msgstr "Compañía"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_date_done
msgid "Completion Date of Transfer"
msgstr "Fecha de finalización de la transferencia"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history_compute_at_date
msgid "Compute"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_form
msgid "Conditions"
msgstr "Condiciones"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_config_settings
msgid "Configuration"
msgstr "Configuración"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_overprocessed_transfer
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
msgid "Confirm"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Confirmed"
msgstr "Confirmado"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Confirmed Moves"
msgstr "Movimientos confirmados"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Congratulations!"
msgstr "¡Felicidades!"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings_group_stock_tracking_owner
msgid "Consignment"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line_consume_line_ids
msgid "Consume Line"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_res_partner
msgid "Contact"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_child_ids
msgid "Contains"
msgstr "Contiene"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Content"
msgstr "Contenido"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_posx
msgid "Corridor (X)"
msgstr "Pasillo (X)"

#. module: stock
#: code:addons/stock/wizard/stock_immediate_transfer.py:24
#, python-format
msgid ""
"Could not reserve all requested products. Please use the 'Mark as Todo' "
"button to handle the reservation manually."
msgstr ""
"No podría reservar productos todo solicitados. Utilice el botón ‘Marcar como "
"Todo’ para manejar la reserva manualmente."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_count_picking
msgid "Count Picking"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_count_picking_backorders
msgid "Count Picking Backorders"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_count_picking_draft
msgid "Count Picking Draft"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_count_picking_late
msgid "Count Picking Late"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_count_picking_ready
msgid "Count Picking Ready"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_count_picking_waiting
msgid "Count Picking Waiting"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Counterpart Locations"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "Create Backorder"
msgstr "Crear entrega parcial"

#. module: stock
#: code:addons/stock/models/stock_picking.py:699
#, python-format
msgid "Create Backorder?"
msgstr "Crear entrega parcial"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_tree_view
msgid "Create Date"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_use_create_lots
msgid "Create New Lots/Serial Numbers"
msgstr ""

#. module: stock
#: selection:procurement.rule,procure_method:0
msgid "Create Procurement"
msgstr "Crear abastecimiento"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Create Vendors"
msgstr "Crear proveedores"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Create a Quotation"
msgstr "Crear cotización"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Create a RFQ"
msgstr "Crear una solicitud de cotización"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid ""
"Create a backorder, if you expect to process the remaining\n"
"                        products later.  Do not create a backorder if you "
"will not\n"
"                        supply the remaining products."
msgstr ""
"Crear un pedido, si va a procesar los productos restantes más adelante.  "
"Crear un pedido si no le suministra los productos restantes."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Create an Inventory Adjustment"
msgstr "Crear un ajuste de inventario"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Create your products"
msgstr "Cree sus productos"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_move_ids
msgid "Created Moves"
msgstr "Movimientos creados"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group_create_uid
#: model:ir.model.fields,field_description:stock.field_procurement_rule_create_uid
#: model:ir.model.fields,field_description:stock.field_product_putaway_create_uid
#: model:ir.model.fields,field_description:stock.field_product_removal_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_incoterms_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_location_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_location_path_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_location_route_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_line_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_overprocessed_transfer_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_production_lot_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_scrap_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group_create_date
#: model:ir.model.fields,field_description:stock.field_procurement_rule_create_date
#: model:ir.model.fields,field_description:stock.field_product_putaway_create_date
#: model:ir.model.fields,field_description:stock.field_product_removal_create_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_create_date
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_create_date
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat_create_date
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_create_date
#: model:ir.model.fields,field_description:stock.field_stock_incoterms_create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_create_date
#: model:ir.model.fields,field_description:stock.field_stock_location_create_date
#: model:ir.model.fields,field_description:stock.field_stock_location_path_create_date
#: model:ir.model.fields,field_description:stock.field_stock_location_route_create_date
#: model:ir.model.fields,field_description:stock.field_stock_move_line_create_date
#: model:ir.model.fields,field_description:stock.field_stock_overprocessed_transfer_create_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_create_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_create_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_create_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_create_date
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history_create_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_create_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line_create_date
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute_create_date
#: model:ir.model.fields,field_description:stock.field_stock_scrap_create_date
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report_create_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_create_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_create_date
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap_create_date
msgid "Created on"
msgstr "Creado en"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_rule_form
msgid "Creates"
msgstr "Crear"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Creation"
msgstr "Creación"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_create_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_date
#: model:ir.model.fields,field_description:stock.field_stock_production_lot_create_date
msgid "Creation Date"
msgstr "Fecha creación"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_date
msgid "Creation Date, usually the time of the order"
msgstr "Fecha de creación, usualmente la del pedido"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:433
#, python-format
msgid "Cross-Dock"
msgstr "Sin almacenaje intermedio (cross dock)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_crossdock_route_id
msgid "Crossdock Route"
msgstr "Ruta sin almacenes intemedios (Cross docking)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_forecast_cumulative_quantity
msgid "Cumulative Quantity"
msgstr "Cantidad acumulativa"

#. module: stock
#: selection:stock.quantity.history,compute_at_date:0
msgid "Current Inventory"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_current_picking_id
msgid "Current Picking"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_current_picking_move_line_ids
msgid "Current Picking Move Line"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.location_open_quants
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Current Stock"
msgstr "Stock actual"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product_qty_available
msgid ""
"Current quantity of products.\n"
"In a context with a single Stock Location, this includes goods stored at "
"this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the "
"Stock Location of this Warehouse, or any of its children.\n"
"stored in the Stock Location of the Warehouse of this Shop, or any of its "
"children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' "
"type."
msgstr ""
"Cantidad actual de los productos.\n"
"En un contexto de una sola ubicación de existencias, esto incluye los bienes "
"almacenados en esta ubicación, o cualquiera de sus hijas.\n"
"En un contexto de un solo almacén, esto incluye los bienes almacenados en la "
"ubicación de existencias de ese almacén, o cualquiera de sus hijas.\n"
"En cualquier otro caso, esto incluye los bienes almacenados en cualquier "
"ubicación de existencias de tipo 'Interna'."

#. module: stock
#: code:addons/stock/models/stock_picking.py:111
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
#, python-format
msgid "Customer"
msgstr "Cliente"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_sale_delay
#: model:ir.model.fields,field_description:stock.field_product_template_sale_delay
msgid "Customer Lead Time"
msgstr "Plazo de entrega del cliente"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner_property_stock_customer
#: model:ir.model.fields,field_description:stock.field_res_users_property_stock_customer
#: selection:stock.location,usage:0
msgid "Customer Location"
msgstr "Ubicación del cliente"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Customer Locations"
msgstr "Ubicaciones de cliente"

#. module: stock
#: model:stock.location,name:stock.stock_location_customers
#: selection:stock.picking.type,code:0
msgid "Customers"
msgstr "Clientes"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
msgid "DATE"
msgstr ""

#. module: stock
#: model:stock.incoterms,name:stock.incoterm_DAF
msgid "DELIVERED AT FRONTIER"
msgstr ""

#. module: stock
#: model:stock.incoterms,name:stock.incoterm_DAP
msgid "DELIVERED AT PLACE"
msgstr ""

#. module: stock
#: model:stock.incoterms,name:stock.incoterm_DAT
msgid "DELIVERED AT TERMINAL"
msgstr ""

#. module: stock
#: model:stock.incoterms,name:stock.incoterm_DDP
msgid "DELIVERED DUTY PAID"
msgstr ""

#. module: stock
#: model:stock.incoterms,name:stock.incoterm_DDU
msgid "DELIVERED DUTY UNPAID"
msgstr ""

#. module: stock
#: model:stock.incoterms,name:stock.incoterm_DEQ
msgid "DELIVERED EX QUAY"
msgstr ""

#. module: stock
#: model:stock.incoterms,name:stock.incoterm_DES
msgid "DELIVERED EX SHIP"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings_module_delivery_dhl
msgid "DHL"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Daily Operations"
msgstr "Operaciones diarias"

#. module: stock
#: model:ir.ui.menu,name:stock.stock_picking_type_menu
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Dashboard"
msgstr "Tablero"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_forecast_date
#: model:ir.model.fields,field_description:stock.field_stock_move_date
#: model:ir.model.fields,field_description:stock.field_stock_move_line_date
#: model_terms:ir.ui.view,arch_db:stock.stock_move_tree
msgid "Date"
msgstr "Fecha"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_tree
msgid "Date Expected"
msgstr "Fecha prevista"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_date_done
msgid "Date of Transfer"
msgstr "Fecha de transferencia"

#. module: stock
#: selection:stock.warehouse.orderpoint,lead_type:0
msgid "Day(s) to get the products"
msgstr "Día (s) para obtener los productos"

#. module: stock
#: selection:stock.warehouse.orderpoint,lead_type:0
msgid "Day(s) to purchase"
msgstr "Día (s) comprar"

#. module: stock
#: model:res.company,overdue_msg:stock.res_company_1
msgid ""
"Dear Sir/Madam,\n"
"\n"
"Our records indicate that some payments on your account are still due. "
"Please find details below.\n"
"If the amount has already been paid, please disregard this notice. "
"Otherwise, please forward us the total amount stated below.\n"
"If you have any queries regarding your account, Please contact us.\n"
"\n"
"Thank you in advance for your cooperation.\n"
"Best Regards,"
msgstr ""
"Estimado/a señor/señora,\n"
"\n"
"Nuestros registros indican que algunos pagos en nuestra cuenta están aún "
"pendientes. Puede encontrar los detalles a continuación.\n"
"Si la cantidad ha sido ya pagada, por favor, descarte esta notificación. En "
"otro caso, por favor remítanos el importe total abajo indicado.\n"
"Si tiene alguna pregunta con respecto a su cuenta, por favor contáctenos.\n"
"\n"
"Gracias de antemano por su colaboración.\n"
"Saludos cordiales,"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_default_location_dest_id
msgid "Default Destination Location"
msgstr "Ubicación destino por defecto"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_owner_id
msgid "Default Owner"
msgstr "Propietario por defecto"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_default_resupply_wh_id
msgid "Default Resupply Warehouse"
msgstr "Almacén de reabastecimiento por defecto"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_default_location_src_id
msgid "Default Source Location"
msgstr "Ubicación origen por defecto"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_production_lot_product_uom_id
#: model:ir.model.fields,help:stock.field_stock_quant_product_uom_id
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint_product_uom
msgid "Default Unit of Measure used for all stock operation."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_reception_steps
msgid "Default incoming route to follow"
msgstr "Ruta de entrada a seguir por defecto"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_delivery_steps
msgid "Default outgoing route to follow"
msgstr "Ruta de salida a seguir por defecto"

#. module: stock
#: selection:stock.move,procure_method:0
msgid "Default: Take From Stock"
msgstr "Por defecto: Obtenido desde las existencias"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_route_ids
msgid "Defaults routes through the warehouse"
msgstr "Rutas por defecto a través del almacén"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Define routes within your warehouse according to business needs, such as "
"Quality Control, After Sales Services or Supplier Returns"
msgstr ""
"Definir rutas dentro de su almacén según las necesidades del negocio, tales "
"como Control de calidad, después de la venta de servicios o proveedor de "
"devoluciones"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid ""
"Define your locations to reflect your warehouse structure and\n"
"            organization. Odoo is able to manage physical locations\n"
"            (warehouses, shelves, bin, etc), partner locations (customers,\n"
"            vendors) and virtual locations which are the counterpart of\n"
"            the stock operations like the manufacturing orders\n"
"            consumptions, inventories, etc."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_putaway_strategy_id
msgid ""
"Defines the default method used for suggesting the exact location (shelf) "
"where to store the products. This method can be enforced at the product "
"category level, and a fallback is made on the parent locations if none is "
"set here."
msgstr ""
"Define el método por defecto usado para sugerir la ubicación exacta "
"(estante) donde almacenar los productos. Este método puede ser forzado al "
"nivel de la categoría de producto, y hay una alternativa en las ubicaciones "
"padre si no se establece nada aquí."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_removal_strategy_id
msgid ""
"Defines the default method used for suggesting the exact location (shelf) "
"where to take the products from, which lot etc. for this location. This "
"method can be enforced at the product category level, and a fallback is made "
"on the parent locations if none is set here."
msgstr ""
"Define el método por defecto usado para sugerir la ubicación exacta "
"(estante) de donde coger los productos, qué lote, etc. Este método puede ser "
"forzado al nivel de la categoría de producto, y hay una alternativa en las "
"ubicaciones padre si no se establece nada aquí."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_path_form
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_rule_form
msgid "Delay"
msgstr "Retraso"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_path_delay
msgid "Delay (days)"
msgstr "Retraso (días)"

#. module: stock
#: code:addons/stock/models/product.py:332
#, python-format
msgid "Delivered Qty"
msgstr "Ctdad enviada"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Deliveries"
msgstr "Entregas"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:209
#: model:stock.picking.type,name:stock.chi_picking_type_out
#: model:stock.picking.type,name:stock.picking_type_out
#, python-format
msgid "Delivery Orders"
msgstr "Órdenes de entrega"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings_group_stock_tracking_lot
msgid "Delivery Packages"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_delivery_route_id
msgid "Delivery Route"
msgstr "Ruta de entrega"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_delivery
msgid "Delivery Slip"
msgstr "Conduce"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product_route_ids
#: model:ir.model.fields,help:stock.field_product_template_route_ids
msgid ""
"Depending on the modules installed, this will allow you to define the route "
"of the product: whether it will be bought, manufactured, MTO/MTS,..."
msgstr ""
"Dependiendo de los módulos instalados, este permite definir la ruta del "
"producto: si será comprado, facturado, bajo pedido o desde existencias..."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Deployment"
msgstr "Despliegue"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_name
msgid "Description"
msgstr "Descripción"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Delivery Orders"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Internal Transfers"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Receptions"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_description_pickingout
#: model:ir.model.fields,field_description:stock.field_product_template_description_pickingout
msgid "Description on Delivery Orders"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_description_picking
#: model:ir.model.fields,field_description:stock.field_product_template_description_picking
msgid "Description on Picking"
msgstr "Descripción de conduces"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_description_pickingin
#: model:ir.model.fields,field_description:stock.field_product_template_description_pickingin
msgid "Description on Receptions"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Destination"
msgstr "Destino"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_partner_id
msgid "Destination Address "
msgstr "Dirección de destino "

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_path_location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_move_location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_location_dest_id
msgid "Destination Location"
msgstr "Ubicación destino"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Destination Location:"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_move_dest_ids
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Destination Moves"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line_result_package_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
msgid "Destination Package"
msgstr "Paquete destino"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Destination Package :"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_route_ids
msgid "Destination route"
msgstr "Ruta destino"

#. module: stock
#: code:addons/stock/models/stock_move.py:446
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#, python-format
msgid "Detailed Operations"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_path_form
msgid "Details"
msgstr "Detalles"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_show_details_visible
msgid "Details Visible"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_procurement_rule_procure_method
msgid ""
"Determines the procurement method of the stock move that will be generated: "
"whether it will need to 'take from the available stock' in its source "
"location or needs to ignore its stock and create a procurement over there."
msgstr ""
"Determina el método de abastecimiento que se generará del movimiento de "
"existencias: si será necesario 'coger de las existencias disponibles' en su "
"ubicación origen o necesita ignorar las existencias y crea un abastecimiento "
"a partir de él."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
msgid "Discard"
msgstr "Descartar"

#. module: stock
#: model:stock.location,name:stock.location_dispatch_zone
msgid "Dispatch Zone"
msgstr "Zona de expedición"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group_display_name
#: model:ir.model.fields,field_description:stock.field_procurement_rule_display_name
#: model:ir.model.fields,field_description:stock.field_product_putaway_display_name
#: model:ir.model.fields,field_description:stock.field_product_removal_display_name
#: model:ir.model.fields,field_description:stock.field_report_stock_forecast_display_name
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_display_name
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_display_name
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat_display_name
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_display_name
#: model:ir.model.fields,field_description:stock.field_stock_incoterms_display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory_display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_display_name
#: model:ir.model.fields,field_description:stock.field_stock_location_display_name
#: model:ir.model.fields,field_description:stock.field_stock_location_path_display_name
#: model:ir.model.fields,field_description:stock.field_stock_location_route_display_name
#: model:ir.model.fields,field_description:stock.field_stock_move_display_name
#: model:ir.model.fields,field_description:stock.field_stock_move_line_display_name
#: model:ir.model.fields,field_description:stock.field_stock_overprocessed_transfer_display_name
#: model:ir.model.fields,field_description:stock.field_stock_picking_display_name
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_display_name
#: model:ir.model.fields,field_description:stock.field_stock_production_lot_display_name
#: model:ir.model.fields,field_description:stock.field_stock_quant_display_name
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_display_name
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history_display_name
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_display_name
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line_display_name
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute_display_name
#: model:ir.model.fields,field_description:stock.field_stock_scrap_display_name
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report_display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_display_name
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_display_name
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap_display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Do not hesitate to send us an email to describe your experience or to "
"suggest improvements!"
msgstr ""
"No dude en enviarnos un correo electrónico a describir su experiencia o para "
"sugerir mejoras."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line_qty_done
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view2
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: selection:stock.move,state:0 selection:stock.picking,state:0
#: selection:stock.scrap,state:0
msgid "Done"
msgstr "Realizado"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_done
msgid "Done Transfers"
msgstr "Transferencias realizadas"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_done_grouped
msgid "Done Transfers by Date"
msgstr "Transferencias realizadas por fecha"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Don’t propagate scheduling changes through chains of operations"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Download the"
msgstr "Descargue el"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_traceability_report_line.xml:78
#, python-format
msgid "Downstream Traceability"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: selection:stock.inventory,state:0 selection:stock.picking,state:0
#: selection:stock.scrap,state:0
msgid "Draft"
msgstr "Borrador"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Draft Moves"
msgstr "Movimientos borrador"

#. module: stock
#: model:stock.incoterms,name:stock.incoterm_EXW
msgid "EX WORKS"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Edit its details or add new ones"
msgstr "Editar sus detalles o añadir nuevos"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "End"
msgstr "Fin"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_traceability_report_backend.xml:14
#, python-format
msgid "Error"
msgstr ""

#. module: stock
#: model:stock.location,name:stock.stock_location_7
msgid "European Customers"
msgstr "Clientes europeos"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid ""
"Every stock operation in Odoo moves the products from one\n"
"            location to another one.  For instance, if you receive products\n"
"            from a vendor, Odoo will move products from the Vendor\n"
"            location to the Stock location. Each report can be performed on\n"
"            physical, partner or virtual locations."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_quant.py:397
#, python-format
msgid "Everything inside a package should be in the same location"
msgstr ""
"Todo lo que esté dentro de un paquete debería estar en la misma ubicación"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Excel template"
msgstr "Plantilla Excel"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_search_form_view_stock
msgid "Exhausted Stock"
msgstr "Existencias agotadas"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_date_expected
#: model:ir.model.fields,field_description:stock.field_stock_scrap_date_expected
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Expected Date"
msgstr "Fecha prevista"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings_module_product_expiry
msgid "Expiration Dates"
msgstr "Fecha de Expiración"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "External note..."
msgstr "Nota externa..."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_removal_method
msgid "FIFO, LIFO..."
msgstr "FIFO, LIFO..."

#. module: stock
#: model:stock.incoterms,name:stock.incoterm_FAS
msgid "FREE ALONGSIDE SHIP"
msgstr ""

#. module: stock
#: model:stock.incoterms,name:stock.incoterm_FCA
msgid "FREE CARRIER"
msgstr ""

#. module: stock
#: model:stock.incoterms,name:stock.incoterm_FOB
msgid "FREE ON BOARD"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings_module_delivery_fedex
msgid "FedEx"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Filters"
msgstr "Filtros"

#. module: stock
#: selection:procurement.rule,group_propagation_option:0
msgid "Fixed"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway
msgid "Fixed Locations Per Categories"
msgstr "Ubicaciones fijas por categorías"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_putaway_fixed_location_ids
msgid "Fixed Locations Per Product Category"
msgstr "Ubicaciones fijas por categoría de producto"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_group_id
msgid "Fixed Procurement Group"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"For the Odoo Team,<br/>\n"
"                            Fabien Pinckaers, Founder"
msgstr ""
"De parte del equipo Odoo,<br/>\n"
"Fabien Pinckaers, Fundador"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category_removal_strategy_id
msgid "Force Removal Strategy"
msgstr "Forzar estrategia de retirada"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_virtual_available
msgid "Forecast Quantity"
msgstr "Cantidad prevista"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product_virtual_available
msgid ""
"Forecast quantity (computed as Quantity On Hand - Outgoing + Incoming)\n"
"In a context with a single Stock Location, this includes goods stored in "
"this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the "
"Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' "
"type."
msgstr ""
"Cantidad prevista (calculada como cantidad a mano - saliente + entrante)\n"
"En un contexto de una sola ubicación de existencias, esto incluye los bienes "
"almacenados en esta ubicación, o cualquiera de sus hijas.\n"
"En un contexto de un solo almacén, esto incluye los bienes almacenados en la "
"ubicación de existencias de ese almacén, o cualquiera de sus hijas.\n"
"En cualquier otro caso, esto incluye los bienes almacenados en cualquier "
"ubicación de existencias de tipo 'Interna'."

#. module: stock
#: code:addons/stock/models/product.py:327
#: model:ir.model.fields,field_description:stock.field_product_template_virtual_available
#: model:ir.model.fields,field_description:stock.field_stock_move_availability
#, python-format
msgid "Forecasted Quantity"
msgstr "Stock virtual"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line_location_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
msgid "From"
msgstr "De"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line_from_loc
msgid "From Loc"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_show_reserved_availability
msgid "From Supplier"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_complete_name
msgid "Full Location Name"
msgstr "Nombre completo de la ubicación"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Future Activities"
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:330
#, python-format
msgid "Future Deliveries"
msgstr "Entregas futuras"

#. module: stock
#: code:addons/stock/models/product.py:335
#, python-format
msgid "Future P&L"
msgstr "P&L futuras"

#. module: stock
#: code:addons/stock/models/product.py:345
#, python-format
msgid "Future Productions"
msgstr "Producciones futuras"

#. module: stock
#: code:addons/stock/models/product.py:340
#, python-format
msgid "Future Qty"
msgstr "Ctdad futura"

#. module: stock
#: code:addons/stock/models/product.py:322
#, python-format
msgid "Future Receipts"
msgstr "Recepciones futuras"

#. module: stock
#: model:stock.location,name:stock.location_gate_a
msgid "Gate A"
msgstr "Puerta A"

#. module: stock
#: model:stock.location,name:stock.location_gate_b
msgid "Gate B"
msgstr "Puerta B"

#. module: stock
#: model:stock.location,name:stock.stock_location_5
msgid "Generic IT Vendors"
msgstr "Proveedores Genéricos de TI"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Get a full traceability from vendors to customers"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Get informative or blocking warnings on partners"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_fixed_putaway_strat_sequence
msgid ""
"Give to the more specialized category, a higher priority to have them in top "
"of the list."
msgstr ""
"Darle a la categoría más especializada una prioridad más para tenerla en lo "
"alto de la lista."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_path_filter
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_rule_filter
msgid "Global"
msgstr "Global"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_default_resupply_wh_id
msgid "Goods will always be resupplied from this warehouse"
msgstr "Los bienes se reabastecerán siempre desde este almacén"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_level_forecast_filter
msgid "Graph"
msgstr "Gráfico"

#. module: stock
#: model:stock.inventory.line,product_name:stock.stock_inventory_line_1
msgid "Graphics Card"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_filter
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_stock_level_forecast_filter
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Group By"
msgstr "Agrupar por"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Group by..."
msgstr "Agrupar por..."

#. module: stock
#: model:stock.inventory.line,product_name:stock.stock_inventory_line_9
msgid "HDD SH-1"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_move_line_exist
msgid "Has Pack Operations"
msgstr "Tiene operaciones de paquete"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_has_packages
msgid "Has Packages"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_has_scrap_move
msgid "Has Scrap Moves"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_posz
msgid "Height (Z)"
msgstr "Altura (Z)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Help rental management, by generating automated return moves for rented "
"products"
msgstr ""
"Ayudar a la administración de alquileres, generando retorno automatizado se "
"mueve para productos alquilados"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Here are some usual problems and their solutions:"
msgstr "Aquí hay algunos problemas habituales y sus soluciones:"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_receipt_picking_move
msgid ""
"Here you can receive individual products, no matter what\n"
"                purchase order or picking order they come from. You will "
"find\n"
"                the list of all products you are waiting for. Once you "
"receive\n"
"                an order, you can filter based on the name of the vendor or\n"
"                the purchase order reference. Then you can confirm all "
"products\n"
"                received using the buttons on the right of each line."
msgstr ""
"<p class=\"oe_view_nocontent_create\">\n"
"Pulse para registrar una recepción de producto.\n"
"</p><p>\n"
"Aquí puede recibir productos individuales, no importa de qué pedido de "
"compra o de \n"
"qué conduce provengan. Encontrará la lista de todos los productos que están "
"es espera. \n"
"Una vez reciba el pedido, puede realizar un filtro basado en el nombre del "
"proveedor \n"
"o la referencia del pedido de venta. Puede confirmar entonces todos los "
"productos \n"
"recibidos usando los botones a la derecha de cada línea.\n"
"</p>\n"
"            "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "How to use Lot Tracking:"
msgstr "Cómo utilizar Lot Tracking:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group_id
#: model:ir.model.fields,field_description:stock.field_procurement_rule_id
#: model:ir.model.fields,field_description:stock.field_product_putaway_id
#: model:ir.model.fields,field_description:stock.field_product_removal_id
#: model:ir.model.fields,field_description:stock.field_report_stock_forecast_id
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_id
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat_id
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_id
#: model:ir.model.fields,field_description:stock.field_stock_incoterms_id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_id
#: model:ir.model.fields,field_description:stock.field_stock_location_id
#: model:ir.model.fields,field_description:stock.field_stock_location_path_id
#: model:ir.model.fields,field_description:stock.field_stock_location_route_id
#: model:ir.model.fields,field_description:stock.field_stock_move_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line_id
#: model:ir.model.fields,field_description:stock.field_stock_overprocessed_transfer_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_id_6902
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line_id
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap_id
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap_id
msgid "ID"
msgstr "ID (identificación)"

#. module: stock
#: code:addons/stock/models/stock_inventory.py:406
#, python-format
msgid "INV:"
msgstr "FV:"

#. module: stock
#: code:addons/stock/wizard/stock_change_product_qty.py:94
#, python-format
msgid "INV: %s"
msgstr "INV: %s"

#. module: stock
#: model:stock.location,name:stock.stock_location_3
msgid "IT Vendors"
msgstr "Proveedores de TI"

#. module: stock
#: model:product.product,name:stock.product_icecream
#: model:product.template,name:stock.product_icecream_product_template
#: model:stock.inventory.line,product_name:stock.stock_inventory_line_icecream_lot0
#: model:stock.inventory.line,product_name:stock.stock_inventory_line_icecream_lot1
msgid "Ice Cream"
msgstr "Helado"

#. module: stock
#: model:product.product,description:stock.product_icecream
#: model:product.template,description:stock.product_icecream_product_template
msgid ""
"Ice cream can be mass-produced and thus is widely available in developed "
"parts of the world. Ice cream can be purchased in large cartons (vats and "
"squrounds) from supermarkets and grocery stores, in smaller quantities from "
"ice cream shops, convenience stores, and milk bars, and in individual "
"servings from small carts or vans at public events."
msgstr ""
"El helado pude producirse en masa y por tanto está ampliamente disponible en "
"las partes desarrolladas del mundo. Puede adquirirse helado en cajas grandes "
"en supermercados y tiendas de alimentación, en cantidades pequeñas en "
"heladerías y tiendas de conveniencia, y en unidades servidas de pequeños "
"carros o caravanas en eventos públicos."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid ""
"If a product is not at the right place, set the checked quantity to 0 and "
"create a new line with correct location."
msgstr ""
"Si un producto no está en el lugar adecuado, establezca la cantidad "
"comprobada a 0, y cree una nueva línea con la ubicación correcta."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_picking_type_entire_packs
#: model:ir.model.fields,help:stock.field_stock_picking_type_show_entire_packs
msgid ""
"If checked, this shows the packs to be moved as a whole in the Operations "
"tab all the time, even if there was no entire pack reserved."
msgstr ""
"Si, esto demuestra los paquetes se muevan como un todo en las operaciones "
"tab todo el tiempo, aunque no era ningún paquete todo reservado."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_path_propagate
msgid ""
"If checked, when the previous move is cancelled or split, the move generated "
"by this move will too"
msgstr ""
"Si está marcado, cuando el movimiento previo sea cancelado o dividido, el "
"movimiento generado por éste también lo será"

#. module: stock
#: model:ir.model.fields,help:stock.field_procurement_rule_propagate
msgid ""
"If checked, when the previous move of the move (which was generated by a "
"next procurement) is cancelled or split, the move generated by this move "
"will too"
msgstr ""
"Si está marcado, cuando el movimiento previo a éste (que fue generado por un "
"abastecimiento siguiente) sea cancelado o dividido, el movimiento generado "
"por éste también lo será"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_propagate
msgid "If checked, when this move is cancelled, cancel the linked move too"
msgstr ""
"Si está marcado, cuando este movimiento se cancela, también cancela el "
"movimiento relacionado."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line_result_package_id
msgid "If set, the operations are packed into this package"
msgstr "Si está establecido, las operaciones se empaquetan en este paquete"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint_active
msgid ""
"If the active field is set to False, it will allow you to hide the "
"orderpoint without removing it."
msgstr ""
"Si el campo activo se desmarca, permite ocultar la regla de stock mínimo sin "
"eliminarla."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route_active
msgid ""
"If the active field is set to False, it will allow you to hide the route "
"without removing it."
msgstr ""
"Si la casilla activo no se marca, permitirá ocultar la ruta sin eliminarla."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid ""
"If the picking is unlocked you can edit initial demand (for a draft picking) "
"or done quantities (for a done picking)."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_path_filter
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_rule_filter
msgid "If the route is global"
msgstr "Si la ruta es global"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type_show_reserved
msgid ""
"If this checkbox is ticked, Odoo will show which products are reserved (lot/"
"serial number, source location, source package)."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_show_operations
#: model:ir.model.fields,help:stock.field_stock_picking_type_show_operations
msgid ""
"If this checkbox is ticked, the pickings lines will represent detailed stock "
"operations. If not, the picking lines will represent an aggregate of "
"detailed stock operations."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type_use_create_lots
msgid ""
"If this is checked only, it will suppose you want to create new Lots/Serial "
"Numbers, so you can provide them in a text field. "
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type_use_existing_lots
msgid ""
"If this is checked, you will be able to choose the Lots/Serial Numbers. You "
"can also decide to not put lots in this operation type.  This means it will "
"create stock with no lot or not put a restriction on the lot taken. "
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_backorder_id
#: model:ir.model.fields,help:stock.field_stock_picking_backorder_id
msgid ""
"If this shipment was split, then this field links to the shipment which "
"contains the already processed part."
msgstr ""
"Si se dividió el envío, entonces este campo enlaza con el envío que contenga "
"la parte ya procesada."

#. module: stock
#: model:ir.model.fields,help:stock.field_procurement_rule_active
msgid "If unchecked, it will allow you to hide the rule without removing it."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"If you are a developer <strong>you can use our\n"
"                        API</strong> to load data automatically through\n"
"                        scripts: take a look at our"
msgstr ""
"Si eres un desarrollador <strong>puede utilizar nuestro API</strong> para "
"cargar los datos automáticamente a través de secuencias de comandos: echa un "
"vistazo a nuestro"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"If you configured automatic procurement, Odoo automatically generates\n"
"                        Procurements Orders. You usually don't need to worry "
"about them, but\n"
"                        sometimes the system can remain blocked without "
"generating a\n"
"                        corresponding document, usually due to a "
"configuration problem."
msgstr ""
"Si ha configurado automático adquisición, Odoo genera automáticamente "
"órdenes de compras. Generalmente no necesita preocuparse de ellos, pero a "
"veces el sistema puede permanecer bloqueado sin generar un documento "
"correspondiente, generalmente debido a un problema de configuración."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory_filter
msgid ""
"If you do an entire inventory, you can choose 'All Products' and it will "
"prefill the inventory with the current stock.  If you only do some products  "
"(e.g. Cycle Counting) you can choose 'Manual Selection of Products' and the "
"system won't propose anything.  You can also let the system propose for a "
"single product / lot /... "
msgstr ""
"Si hace un inventario completo, puede elegir \"Todos los productos\" y se "
"rellenará inicialmente el inventario con el stock actual. Si sólo hace "
"inventario de algunos productos (p.ej. recuento cíclico) puede elegir "
"'Selección manual de productos' y el sistema no le propondrá nada "
"inicialmente. También puede dejar que el sistema le proponga un único "
"producto / lote / ..."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"If you have less than 50 vendors, we recommend you\n"
"                                        to create them manually."
msgstr "Si tienes menos de 50 proveedores, te recomendamos crear manualmente."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "If you want to do it yourself:"
msgstr "Si desea hacerlo usted mismo:"

#. module: stock
#: model:ir.model,name:stock.model_stock_immediate_transfer
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Immediate Transfer"
msgstr "Transferencia inmediata"

#. module: stock
#: code:addons/stock/models/stock_picking.py:662
#, python-format
msgid "Immediate Transfer?"
msgstr "¿Transferencia inmediata?"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
msgid "Immediate transfer?"
msgstr "¿Transferencia inmediata?"

#. module: stock
#: selection:res.config.settings,module_procurement_jit:0
msgid "Immediately after sales order confirmation"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Import using the top left button in"
msgstr "Importar mediante el botón superior izquierdo"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"In Odoo, <strong>Reordering Rules</strong> are used to replenish your "
"products.\n"
"                        Odoo will automatically propose a procurement to buy "
"new products if you are\n"
"                        running out of stock."
msgstr ""
"En Odoo, <strong>Reordenar reglas</strong> se utilizan para reponer sus "
"productos.\n"
"                        Odoo automáticamente propondrá una contratación para "
"comprar nuevos productos si se está ejecutando fuera de stock."

#. module: stock
#: selection:stock.inventory,state:0
msgid "In Progress"
msgstr "En proceso"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_in_type_id
msgid "In Type"
msgstr "Tipo de entrada"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"In case of unique serial numbers, each serial number corresponds\n"
"                        to exactly one piece.  In case of lots, you need to "
"supply the quantity\n"
"                        for each lot when you move that product."
msgstr ""
"En el caso de números de serie únicos, cada número de serie corresponde a "
"exactamente una sola pieza.  En el caso de lotes, deberá suministrar la "
"cantidad de cada lote cuando mueves ese producto."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"In short, you will get a more efficient warehouse management that leads\n"
"                        to inventory reduction and better efficiencies in "
"your daily operations."
msgstr ""
"En Resumen, usted conseguirá una gestión más eficiente del almacén que lleva "
"a la reducción del inventario y mejor eficiencia en sus operaciones diarias."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_exhausted
msgid "Include Exhausted Products"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_incoming_qty
#: model:ir.model.fields,field_description:stock.field_product_template_incoming_qty
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Incoming"
msgstr "Entrada"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_receipt_picking_move
msgid "Incoming  Products"
msgstr "Productos a recibir"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_in_date
msgid "Incoming Date"
msgstr "Fecha de entrada"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_reception_steps
msgid "Incoming Shipments"
msgstr "Envíos a recibir"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_incoterms_code
msgid "Incoterm Standard Code"
msgstr "Código estándar del incoterm"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_incoterms_tree
#: model:ir.model,name:stock.model_stock_incoterms
#: model:ir.ui.menu,name:stock.menu_action_incoterm_open
#: model_terms:ir.ui.view,arch_db:stock.stock_incoterms_form
#: model_terms:ir.ui.view,arch_db:stock.stock_incoterms_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_incoterms_tree
msgid "Incoterms"
msgstr "Incoterms"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_incoterms_name
msgid ""
"Incoterms are series of sales terms. They are used to divide transaction "
"costs and responsibilities between buyer and seller and reflect state-of-the-"
"art transportation practices."
msgstr ""
"Los incoterms son una serie de términos de venta. Se usan para dividir los "
"costes de la transacción y las responsabilidades entre el comprador y el "
"vendedor y reflejan las últimas prácticas en el transporte"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_product_uom_qty
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Initial Demand"
msgstr "Demanda inicial"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Initial Inventory"
msgstr "inventario inicial"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:97
#: model:stock.location,name:stock.stock_location_company
#, python-format
msgid "Input"
msgstr "Entrada"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_wh_input_stock_loc_id
msgid "Input Location"
msgstr "Ubicación de entrada"

#. module: stock
#: code:addons/stock/models/stock_scrap.py:131
#, python-format
msgid "Insufficient Quantity"
msgstr ""

#. module: stock
#: model:stock.location,name:stock.stock_location_inter_wh
msgid "Inter Company Transit"
msgstr "Tránsito intercompañía"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
#: selection:stock.picking.type,code:0
msgid "Internal"
msgstr "Interno"

#. module: stock
#: selection:stock.location,usage:0
msgid "Internal Location"
msgstr "Ubicación interna"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Internal Locations"
msgstr "Ubicaciones Internas"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_production_lot_ref
msgid "Internal Reference"
msgstr "Referencia interna"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:231
#: model:stock.picking.type,name:stock.picking_type_internal
#, python-format
msgid "Internal Transfers"
msgstr "Transferencias internas"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company_internal_transit_location_id
msgid "Internal Transit Location"
msgstr "Ubicación del tránsito interno"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_int_type_id
msgid "Internal Type"
msgstr "Tipo interno"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_production_lot_ref
msgid ""
"Internal reference number in case it differs from the manufacturer's lot/"
"serial number"
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:240
#, python-format
msgid "Invalid domain left operand %s"
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:242
#, python-format
msgid "Invalid domain operator %s"
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:244
#, python-format
msgid "Invalid domain right operand %s"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_category_id
msgid "Inventoried Category"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_location_id
msgid "Inventoried Location"
msgstr "Ubicación inventariada"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_lot_id
msgid "Inventoried Lot/Serial Number"
msgstr "Lote/Nº de serie inventariado"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_partner_id
msgid "Inventoried Owner"
msgstr "Propietario del inventario"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_package_id
msgid "Inventoried Pack"
msgstr "Paquete inventariado"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_product_id
msgid "Inventoried Product"
msgstr "Producto inventariado"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_ids
msgid "Inventories"
msgstr "Inventarios"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_filter
msgid "Inventories Month"
msgstr "Mes de los inventarios"

#. module: stock
#: model:ir.actions.act_window,name:stock.quantsact
#: model:ir.actions.act_window,name:stock.stock_picking_type_action
#: model:ir.actions.report,name:stock.action_report_inventory
#: model:ir.model,name:stock.model_stock_inventory
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_inventory_id
#: model:ir.model.fields,field_description:stock.field_stock_move_inventory_id
#: model:ir.ui.menu,name:stock.menu_stock_root
#: model:ir.ui.menu,name:stock.menu_valuation
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_pivot
msgid "Inventory"
msgstr "Inventario"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Inventory Adjustment"
msgstr "Ajuste de Inventario"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_inventory_form
#: model:ir.ui.menu,name:stock.menu_action_inventory_form
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Inventory Adjustments"
msgstr "Ajustes de inventario"

#. module: stock
#: model:web.planner,tooltip_planner:stock.planner_inventory
msgid "Inventory Configuration: a step-by-step guide."
msgstr "Configuración de inventario: una guía paso a paso."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_date
msgid "Inventory Date"
msgstr "Fecha del inventario"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_line_tree2
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Inventory Details"
msgstr "Detalles del inventario"

#. module: stock
#: model:ir.model,name:stock.model_stock_inventory_line
msgid "Inventory Line"
msgstr "Línea inventario"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_property_stock_inventory
#: model:ir.model.fields,field_description:stock.field_product_template_property_stock_inventory
msgid "Inventory Location"
msgstr "Ubicación perdida de inventario"

#. module: stock
#: model:ir.model,name:stock.model_stock_location
msgid "Inventory Locations"
msgstr "Ubicaciones de inventario"

#. module: stock
#: selection:stock.location,usage:0
msgid "Inventory Loss"
msgstr "Pérdidas de inventario"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_filter
msgid "Inventory Product"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_name
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_filter
msgid "Inventory Reference"
msgstr "Referencia inventario"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_quantity_history
msgid "Inventory Report"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_location_route
msgid "Inventory Routes"
msgstr "Rutas de inventario"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Inventory Settings"
msgstr "Ajustes de inventario"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree
msgid "Inventory Valuation"
msgstr "Valoración del inventario"

#. module: stock
#: model:stock.location,name:stock.location_inventory
msgid "Inventory adjustment"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid ""
"Inventory adjustments will be made by comparing the theoretical and the "
"checked quantities."
msgstr ""
"Los ajustes de inventario se realizarán comparando las cantidades teóricas y "
"comprobadas."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history_date
msgid "Inventory at Date"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_filter
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Inventory of"
msgstr "Inventario de"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_is_locked
#: model:ir.model.fields,field_description:stock.field_stock_move_line_is_locked
#: model:ir.model.fields,field_description:stock.field_stock_picking_is_locked
msgid "Is Locked"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_return_location
msgid "Is a Return Location?"
msgstr "Es ubicación para devolución?"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_scrap_location
msgid "Is a Scrap Location?"
msgstr "¿Es una ubicación de chatarra?"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_is_initial_demand_editable
#: model:ir.model.fields,field_description:stock.field_stock_move_line_is_initial_demand_editable
msgid "Is initial demand editable"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_is_quantity_done_editable
msgid "Is quantity done editable"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"It is also possible to import your initial inventory from an Excel or CSV "
"file.\n"
"                        If you want to do that, contact your Odoo project "
"manager."
msgstr ""
"También es posible importar el inventario inicial de un archivo Excel o "
"CSV.\n"
"                        Si desea hacerlo, comuníquese con su Gerente de "
"proyecto de Odoo."

#. module: stock
#: code:addons/stock/models/stock_quant.py:244
#, python-format
msgid "It is not possible to reserve more products than you have in stock."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_quant.py:246
#, python-format
msgid "It is not possible to unreserve more products than you have in stock."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"It is therefore a good idea to check and try to resolve those procurement\n"
"                        exceptions. These are accessible from the Schedulers "
"menu (you need the Stock\n"
"                        Manager role to see it)."
msgstr ""
"Por lo tanto es una buena idea para comprobar y tratar de resolver las "
"excepciones de contratación. Estos son accesibles desde el menú de "
"programadores (que tenga el rol de administrador de Stock para verlo)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "It is time to make your initial Inventory. In order to do so:"
msgstr "Es momento de hacer su inventario inicial. Para ello:"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_product_packaging
msgid ""
"It specifies attributes of packaging like type, quantity of packaging,etc."
msgstr ""
"Indica los atributos del empaquetado como el tipo, la cantidad de paquetes, "
"etc."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_move_type
msgid "It specifies goods to be deliver partially or all at once"
msgstr "Especifica si los bienes se entregan parcialmente o de una"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
msgid "LOCATION"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
msgid "LOT/SERIAL NUMBER"
msgstr ""

#. module: stock
#: model:stock.inventory.line,product_name:stock.stock_inventory_line_13
msgid "Laptop Customized"
msgstr ""

#. module: stock
#: model:stock.inventory.line,product_name:stock.stock_inventory_line_14
msgid "Laptop E5023"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_last_done_picking
msgid "Last 10 Done Pickings"
msgstr "Últimos 10 conduces realizados"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group___last_update
#: model:ir.model.fields,field_description:stock.field_procurement_rule___last_update
#: model:ir.model.fields,field_description:stock.field_product_putaway___last_update
#: model:ir.model.fields,field_description:stock.field_product_removal___last_update
#: model:ir.model.fields,field_description:stock.field_report_stock_forecast___last_update
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation___last_update
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty___last_update
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat___last_update
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer___last_update
#: model:ir.model.fields,field_description:stock.field_stock_incoterms___last_update
#: model:ir.model.fields,field_description:stock.field_stock_inventory___last_update
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line___last_update
#: model:ir.model.fields,field_description:stock.field_stock_location___last_update
#: model:ir.model.fields,field_description:stock.field_stock_location_path___last_update
#: model:ir.model.fields,field_description:stock.field_stock_location_route___last_update
#: model:ir.model.fields,field_description:stock.field_stock_move___last_update
#: model:ir.model.fields,field_description:stock.field_stock_move_line___last_update
#: model:ir.model.fields,field_description:stock.field_stock_overprocessed_transfer___last_update
#: model:ir.model.fields,field_description:stock.field_stock_picking___last_update
#: model:ir.model.fields,field_description:stock.field_stock_picking_type___last_update
#: model:ir.model.fields,field_description:stock.field_stock_production_lot___last_update
#: model:ir.model.fields,field_description:stock.field_stock_quant___last_update
#: model:ir.model.fields,field_description:stock.field_stock_quant_package___last_update
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history___last_update
#: model:ir.model.fields,field_description:stock.field_stock_return_picking___last_update
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line___last_update
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute___last_update
#: model:ir.model.fields,field_description:stock.field_stock_scrap___last_update
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report___last_update
#: model:ir.model.fields,field_description:stock.field_stock_warehouse___last_update
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint___last_update
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty___last_update
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap___last_update
msgid "Last Modified on"
msgstr "Última modificación en"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group_write_uid
#: model:ir.model.fields,field_description:stock.field_procurement_rule_write_uid
#: model:ir.model.fields,field_description:stock.field_product_putaway_write_uid
#: model:ir.model.fields,field_description:stock.field_product_removal_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_incoterms_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_location_path_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_location_route_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_location_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_line_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_overprocessed_transfer_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_production_lot_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_scrap_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap_write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group_write_date
#: model:ir.model.fields,field_description:stock.field_procurement_rule_write_date
#: model:ir.model.fields,field_description:stock.field_product_putaway_write_date
#: model:ir.model.fields,field_description:stock.field_product_removal_write_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_write_date
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_write_date
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat_write_date
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_write_date
#: model:ir.model.fields,field_description:stock.field_stock_incoterms_write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_write_date
#: model:ir.model.fields,field_description:stock.field_stock_location_path_write_date
#: model:ir.model.fields,field_description:stock.field_stock_location_route_write_date
#: model:ir.model.fields,field_description:stock.field_stock_location_write_date
#: model:ir.model.fields,field_description:stock.field_stock_move_line_write_date
#: model:ir.model.fields,field_description:stock.field_stock_move_write_date
#: model:ir.model.fields,field_description:stock.field_stock_overprocessed_transfer_write_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_write_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_write_date
#: model:ir.model.fields,field_description:stock.field_stock_production_lot_write_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_write_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_write_date
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history_write_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line_write_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_write_date
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute_write_date
#: model:ir.model.fields,field_description:stock.field_stock_scrap_write_date
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report_write_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_write_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_write_date
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap_write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: stock
#: code:addons/stock/models/stock_picking.py:71
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#, python-format
msgid "Late"
msgstr "Retrasado"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Late Activities"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_late
msgid "Late Transfers"
msgstr "Transferencias retrasadas"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_lead_days
msgid "Lead Time"
msgstr "Tiempo de entrega"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_lead_type
msgid "Lead Type"
msgstr "Tipo plomo"

#. module: stock
#: selection:procurement.rule,group_propagation_option:0
msgid "Leave Empty"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route_company_id
msgid "Leave this field empty if this route is shared between all companies"
msgstr "Dejar este campo vacío si la ruta se compartirá entre las compañías"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_parent_left
msgid "Left Parent"
msgstr "Padre izquierdo"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_company_id
#: model:ir.model.fields,help:stock.field_stock_quant_company_id
msgid "Let this field empty if this location is shared between companies"
msgstr ""
"Dejar vacío este campo si la ubicación se compartirá entre las compañías"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Like with the sales flow, Odoo inventory management is\n"
"                        fully integrated with the purchase app."
msgstr ""
"Como con el flujo de ventas, gestión de inventario de Odoo está totalmente "
"integrado con la aplicación de la compra."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Linked Moves"
msgstr "Movimientos enlazados"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Localization"
msgstr "Localización"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "Locate"
msgstr ""

#. module: stock
#: code:addons/stock/models/barcode.py:12
#: model:ir.model.fields,field_description:stock.field_product_product_location_id
#: model:ir.model.fields,field_description:stock.field_product_template_location_id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_location_id
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat_fixed_location_id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_inventory_location_id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_location_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_location_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_location_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap_location_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_location_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_location_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_repair_location_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap_location_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_unbuild_location_id
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
#, python-format
msgid "Location"
msgstr "Ubicación"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_location_barcode
msgid "Location BarCode"
msgstr "Código de barras de ubicación"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_location_name
#: model:ir.model.fields,field_description:stock.field_stock_location_name
msgid "Location Name"
msgstr "Nombre ubicación"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_path_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_path_tree
msgid "Location Paths"
msgstr "Rutas de ubicaciones"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_lot_stock_id
msgid "Location Stock"
msgstr "Ubicación de existencias"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_usage
msgid "Location Type"
msgstr "Tipo de ubicación"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_location_dest_id
msgid "Location where the system will stock the finished products."
msgstr "Ubicación donde el sistema almacenará los productos finalizados."

#. module: stock
#: model:ir.actions.act_window,name:stock.action_location_form
#: model:ir.ui.menu,name:stock.menu_action_location_form
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_path_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Locations"
msgstr "Ubicaciones"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Lock"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_category_form_view_inherit
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Logistics"
msgstr "Logística"

#. module: stock
#: code:addons/stock/models/barcode.py:13
#: model:ir.model.fields,field_description:stock.field_stock_move_line_lot_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap_lot_id
#, python-format
msgid "Lot"
msgstr "Lote"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_lot_barcode
msgid "Lot BarCode"
msgstr "Código de barras de lote"

#. module: stock
#: model:ir.model,name:stock.model_stock_production_lot
msgid "Lot/Serial"
msgstr "Lote/Nº de serie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Lot/Serial :"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_lot_id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_prod_lot_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line_lot_name
#: model:ir.model.fields,field_description:stock.field_stock_production_lot_name
#: model:ir.model.fields,field_description:stock.field_stock_quant_lot_id
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_lot_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model:res.request.link,name:stock.req_link_tracking
msgid "Lot/Serial Number"
msgstr "Lote/Nº de serie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_tree
msgid "Lot/Serial Number Inventory"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings_group_stock_production_lot
msgid "Lots & Serial Numbers"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line_lots_visible
msgid "Lots Visible"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Lots can be encoded on incoming shipments, internal transfers and\n"
"                        outgoing deliveries according to the settings in the "
"operation type.\n"
"                        The tracking can be configured on every product: not "
"any tracing at\n"
"                        all, tracking by lot, or tracking by unique serial "
"number."
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_production_lot_form
#: model:ir.ui.menu,name:stock.menu_action_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_tree
msgid "Lots/Serial Numbers"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:534
#, python-format
msgid "MTO"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_mto_pull_id
msgid "MTO rule"
msgstr "Regla obtener desde existencias"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:473
#: model:stock.location.route,name:stock.route_warehouse0_mto
#, python-format
msgid "Make To Order"
msgstr "Bajo pedido"

#. module: stock
#: selection:stock.warehouse,delivery_steps:0
msgid ""
"Make packages into a dedicated location, then bring them to the output "
"location for shipping (Pick + Pack + Ship)"
msgstr ""
"Empaquetar en una ubicación dedicada, y llevarlo entonces a la ubicación de "
"salida para su envío (Recoger + empaquetar + enviar)"

#. module: stock
#: model:res.groups,name:stock.group_tracking_owner
msgid "Manage Different Stock Owners"
msgstr "Gestionar diferentes propietarios de existencias"

#. module: stock
#: model:res.groups,name:stock.group_production_lot
msgid "Manage Lots / Serial Numbers"
msgstr "Gestionar lotes / números de serie"

#. module: stock
#: model:res.groups,name:stock.group_stock_multi_locations
msgid "Manage Multiple Stock Locations"
msgstr ""

#. module: stock
#: model:res.groups,name:stock.group_stock_multi_warehouses
msgid "Manage Multiple Warehouses"
msgstr ""

#. module: stock
#: model:res.groups,name:stock.group_tracking_lot
msgid "Manage Packages"
msgstr "Gestionar paquetes"

#. module: stock
#: model:res.groups,name:stock.group_adv_location
msgid "Manage Push and Pull inventory flows"
msgstr "Gestionar flujos de inventario push y pull"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Manage default locations per product"
msgstr "Gestionar ubicaciones predeterminadas por producto"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Manage product manufacturing chains"
msgstr "Gestión de cadenas de fabricación de producto"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Manage product packages (e.g. pack of 6 bottles, box of 10 pieces)"
msgstr ""

#. module: stock
#: model:res.groups,name:stock.group_stock_manager
msgid "Manager"
msgstr "Responsable"

#. module: stock
#: selection:stock.location.path,auto:0
msgid "Manual Operation"
msgstr "Operación manual"

#. module: stock
#: selection:res.config.settings,module_procurement_jit:0
msgid "Manually or based on automatic scheduler"
msgstr ""

#. module: stock
#: selection:procurement.rule,action:0
msgid "Manufacture"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Manufacturing"
msgstr ""

#. module: stock
#: selection:stock.picking.type,code:0
msgid "Manufacturing Operation"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Mark as Todo"
msgstr "Marcar 'Por realizar'"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_inventory_control
msgid "Master Data"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_product_max_qty
msgid "Maximum Quantity"
msgstr "Cantidad máxima"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner_picking_warn_msg
#: model:ir.model.fields,field_description:stock.field_res_users_picking_warn_msg
msgid "Message for Stock Picking"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_removal_method
msgid "Method"
msgstr "Método"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company_propagation_minimum_delta
msgid "Minimum Delta for Propagation of a Date Change on moves linked together"
msgstr ""
"Diferencia mínima para la propagación de un cambio de fecha en los "
"movimientos enlazados"

#. module: stock
#: model:ir.model,name:stock.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "Regla de inventario mínimo"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_product_min_qty
msgid "Minimum Quantity"
msgstr "Cantidad mínima"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_orderpoint_ids
msgid "Minimum Stock Rules"
msgstr "Reglas de stock mínimo"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "Misc"
msgstr "Varios"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree
msgid ""
"Most operations are prepared automatically by Odoo according\n"
"                to your preconfigured logistics rules, but you can also "
"record\n"
"                manual stock movements."
msgstr ""
"Mayoría de las operaciones se prepara automáticamente Odoo según las reglas "
"preconfiguradas de la logística, pero también puede registrar movimientos de "
"stock manual."

#. module: stock
#: model:stock.inventory.line,product_name:stock.stock_inventory_line_5
msgid "Mouse, Optical"
msgstr ""

#. module: stock
#: model:stock.inventory.line,product_name:stock.stock_inventory_line_11
msgid "Mouse, Wireless"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line_move_id
msgid "Move"
msgstr "Movimiento"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
msgid "Move Detail"
msgstr ""

#. module: stock
#: selection:procurement.rule,action:0
msgid "Move From Another Location"
msgstr "Mover desde otra ubicación"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_move_line_ids
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_move_line_ids
msgid "Move Line"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_move_line_nosuggest_ids
msgid "Move Line Nosuggest"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
msgid "Move Lines"
msgstr "Mover líneas"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_procure_method
msgid "Move Supply Method"
msgstr "Método de abastecimiento del movimiento"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_date
msgid ""
"Move date: scheduled date until move is done, then date of actual move "
"processing"
msgstr ""
"Fecha del movimiento: Fecha planificada hasta que el movimiento esté "
"realizado, después fecha real en que el movimiento ha sido procesado."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_origin_returned_move_id
msgid "Move that created the return move"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.act_product_stock_move_open
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_product_return_moves
#: model_terms:ir.ui.view,arch_db:stock.stock_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking_board
msgid "Moves"
msgstr "Movimientos"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint_group_id
msgid ""
"Moves created through this orderpoint will be put in this procurement group. "
"If none is given, the moves generated by procurement rules will be grouped "
"into one big picking."
msgstr ""
"Los movimientos creados por esta orden de abastecimiento serán colocados en "
"este grupo de abastecimiento. Si no se proporciona ningún grupo, los "
"movimientos generados por las reglas de abastecimiento serán agrupados en un "
"gran conduce."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings_group_stock_adv_location
msgid "Multi-Step Routes"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings_group_stock_multi_warehouses
msgid "Multi-Warehouses"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "My Activities"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_name
#: model:ir.model.fields,field_description:stock.field_product_putaway_name
#: model:ir.model.fields,field_description:stock.field_product_removal_name
#: model:ir.model.fields,field_description:stock.field_stock_incoterms_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_name
msgid "Name"
msgstr "Nombre"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_search_form_view_stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Negative Stock"
msgstr "Existencias negativas"

#. module: stock
#: code:addons/stock/models/stock_scrap.py:20
#: code:addons/stock/models/stock_scrap.py:64
#: code:addons/stock/models/stock_scrap.py:65 selection:stock.move,state:0
#, python-format
msgid "New"
msgstr "Nuevo"

#. module: stock
#: code:addons/stock/models/stock_move_line.py:172
#: code:addons/stock/models/stock_picking.py:584
#, python-format
msgid "New Move:"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_new_quantity
msgid "New Quantity on Hand"
msgstr "Nueva cantidad a mano"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_form
msgid "New Transfer"
msgstr "Nueva transferencia"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "No Backorder"
msgstr "No hay pedido pendiente"

#. module: stock
#: selection:res.partner,picking_warn:0
msgid "No Message"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings_propagation_minimum_delta
#: model:ir.model.fields,field_description:stock.field_res_config_settings_use_propagation_minimum_delta
msgid "No Rescheduling Propagation"
msgstr ""

#. module: stock
#: selection:product.template,tracking:0
msgid "No Tracking"
msgstr "Sin seguimiento"

#. module: stock
#: code:addons/stock/models/stock_move_line.py:388
#, python-format
msgid "No negative quantities allowed"
msgstr "Sin cantidades negativas permitidas"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
msgid "No operation made on this lot."
msgstr ""

#. module: stock
#: code:addons/stock/models/procurement.py:185
#, python-format
msgid ""
"No procurement rule found. Please verify the configuration of your routes"
msgstr ""

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:55
#, python-format
msgid ""
"No products to return (only lines in Done state and not fully returned yet "
"can be returned)!"
msgstr ""
"¡No hay productos a devolver (sólo las líneas en estado realizado y no "
"totalmente devueltas puede sen devueltas)!"

#. module: stock
#: code:addons/stock/models/procurement.py:67
#, python-format
msgid "No source location defined on procurement rule: %s!"
msgstr ""

#. module: stock
#: model:stock.location,name:stock.stock_location_8
msgid "Non European Customers"
msgstr "Clientes no europeos"

#. module: stock
#: selection:stock.move,priority:0 selection:stock.picking,priority:0
msgid "Normal"
msgstr "Normal"

#. module: stock
#: selection:stock.move,priority:0 selection:stock.picking,priority:0
msgid "Not urgent"
msgstr "No urgente"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_note
#: model:ir.model.fields,field_description:stock.field_stock_picking_note
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Notes"
msgstr "Notas"

#. module: stock
#: code:addons/stock/models/stock_picking.py:531
#, python-format
msgid "Nothing to check the availability for."
msgstr "Nada para lo que comprobar disponibilidad."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Now, all your product quantities are correctly set."
msgstr "Ahora, todas las cantidades de producto se establecen correctamente."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_delay
msgid "Number of Days"
msgstr "Número de días"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint_lead_days
msgid ""
"Number of days after the orderpoint is triggered to receive the products or "
"to order to the vendor"
msgstr ""
"Número de días antes de que el punto de pedido sea generado para recibir los "
"productos o para pedirlos al proveedor"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_path_delay
msgid "Number of days needed to transfer the goods"
msgstr "Número de días necesarios para transferir los bienes"

#. module: stock
#: code:addons/stock/models/stock_picking.py:75
#, python-format
msgid "OK"
msgstr "Aceptar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Odoo handles <strong>advanced push/pull routes configuration</strong>, for "
"example:"
msgstr ""
"Odoo maneja la <strong>configuración de rutas avanzado de vaivén</strong>, "
"por ejemplo:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Odoo has preconfigured <strong>one Warehouse</strong> for you."
msgstr "Odoo ha reconfigurado <strong>un almacén</strong> para usted."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Odoo inventory management is fully integrated with sales and\n"
"                        invoicing process. Everything is automated from the "
"initial\n"
"                        quotation to the delivery and the final invoice."
msgstr ""
"Gestión de inventario de Odoo está totalmente integrado con el proceso de "
"ventas y facturación. Todo automatizado desde el presupuesto inicial para la "
"entrega y la factura final."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Odoo is able to run advanced traceability by using Product Lots and Serial\n"
"                        Numbers, usually identified by bar codes stuck on "
"the products."
msgstr ""
"Odoo es capaz de ejecutar la trazabilidad avanzado mediante lotes de "
"producto y números de serie, normalmente identificados por códigos de barras "
"en los productos."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Of course, feel free to add your own. Please note that Odoo is able to "
"convert units within the same category, for example, liters to gallons in "
"the volume category"
msgstr ""
"Por supuesto, no dude en añadir sus propios. Odoo es capaz de convertir las "
"unidades dentro de la misma categoría, por ejemplo, de litros a galones en "
"la categoría de volumen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree
msgid "On Hand"
msgstr " Disponible"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "On Hand / Available Quantities"
msgstr "Mano / disponibles cantidades"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_kanban_stock_view
msgid "On hand:"
msgstr "A mano:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Once it's fully working, give us some feedback: we love to hear from our "
"customer. It would be great if you could send us a photo of your warehouse to"
msgstr ""
"Una vez que está funcionando plenamente, darnos alguna opinión: nos encanta "
"oír de nuestros clientes. Sería fantástico si usted podría enviarnos una "
"foto de su almacén para"

#. module: stock
#: code:addons/stock/models/stock_inventory.py:113
#, python-format
msgid "One Lot/Serial Number"
msgstr "Un lote/nº de serie"

#. module: stock
#: code:addons/stock/models/stock_inventory.py:111
#, python-format
msgid "One owner only"
msgstr "Sólo un propietario"

#. module: stock
#: code:addons/stock/models/stock_inventory.py:106
#, python-format
msgid "One product category"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_inventory.py:111
#, python-format
msgid "One product for a specific owner"
msgstr "Un producto para un propietario específico"

#. module: stock
#: code:addons/stock/models/stock_inventory.py:107
#, python-format
msgid "One product only"
msgstr "Sólo un producto"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_path_name
msgid "Operation Name"
msgstr "Nombre operación"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_picking_type_id
#: model:ir.model.fields,field_description:stock.field_stock_location_path_picking_type_id
#: model:ir.model.fields,field_description:stock.field_stock_move_picking_type_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_picking_type_id
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
msgid "Operation Type"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_procurement_rule_picking_type_id
msgid ""
"Operation Type determines the way the picking should be shown in the view, "
"reports, ..."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_return_picking_type_id
msgid "Operation Type for Returns"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_name
msgid "Operation Types Name"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_move_line_ids
#: model:ir.ui.menu,name:stock.menu_stock_warehouse_mgmt
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Operations"
msgstr "Conduces"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_pickingtype
msgid "Operations Types"
msgstr "Tipos de conduce"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_partner_id
msgid ""
"Optional address where goods are to be delivered, specifically used for "
"allotment"
msgstr ""
"Dirección opcional cuando las mercancías deben ser entregadas, utilizado "
"específicamente para lotes."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_posx
#: model:ir.model.fields,help:stock.field_stock_location_posy
#: model:ir.model.fields,help:stock.field_stock_location_posz
msgid "Optional localization details, for information purpose only"
msgstr "Detalles de ubicación opcionales, sólo para fines de información."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_returned_move_ids
msgid "Optional: all returned moves created from this move"
msgstr ""
"Opcional: todos los movimientos de devolución creados por este movimiento"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_move_dest_ids
msgid "Optional: next stock move when chaining them"
msgstr "Opcional: Siguiente movimiento de existencias, cuando se encadenan."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_move_orig_ids
msgid "Optional: previous stock move when chaining them"
msgstr "Opcional: movimiento previo cuando se encadenan"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Order Date"
msgstr "Fecha orden"

#. module: stock
#: model:stock.location,name:stock.location_order
msgid "Order Processing"
msgstr "Procesando pedido"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line_ordered_qty
#: model:ir.model.fields,field_description:stock.field_stock_move_ordered_qty
msgid "Ordered Quantity"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Orders processed today or scheduled for today"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Origin"
msgstr "Origen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Origin Moves"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_origin_returned_move_id
msgid "Origin return move"
msgstr "Origen del movimiento de devolución"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_original_location_id
msgid "Original Location"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_move_orig_ids
msgid "Original Move"
msgstr "Movimiento original"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_out_type_id
msgid "Out Type"
msgstr "Tipo de salida"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_outgoing_qty
#: model:ir.model.fields,field_description:stock.field_product_template_outgoing_qty
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Outgoing"
msgstr "Salida"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_delivery_steps
msgid "Outgoing Shippings"
msgstr "Envíos salientes"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:99
#: model:stock.location,name:stock.stock_location_output
#, python-format
msgid "Output"
msgstr "Salida"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_wh_output_stock_loc_id
msgid "Output Location"
msgstr "Ubicación de salida"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_overprocessed_transfer_overprocessed_product_name
msgid "Overprocessed Product Name"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_partner_id
#: model:ir.model.fields,field_description:stock.field_stock_location_partner_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line_owner_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_owner_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_owner_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_owner_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap_owner_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
msgid "Owner"
msgstr "Propietario"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_restrict_partner_id
msgid "Owner "
msgstr "Propietario "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Owner :"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_partner_id
msgid "Owner of the location if not internal"
msgstr "Propietario de la ubicación si no es interna"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line_owner_id
msgid "Owner of the quants"
msgstr "Propietario de los quants"

#. module: stock
#: code:addons/stock/models/product.py:337
#, python-format
msgid "P&L Qty"
msgstr "Ctdad P&L"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_traceability_report_backend.xml:6
#, python-format
msgid "PRINT"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
msgid "PRODUCT"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:216
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_package_id
#, python-format
msgid "Pack"
msgstr "Paquete"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_pack_type_id
msgid "Pack Type"
msgstr "Tipo de empaquetado"

#. module: stock
#: code:addons/stock/models/barcode.py:14
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap_package_id
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_tree
#, python-format
msgid "Package"
msgstr "Paquete"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_quant_package_barcode_small
msgid "Package BarCode"
msgstr "Código de barras del paquete"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_quant_package_barcode
msgid "Package BarCode with Contents"
msgstr "Código de barras del paquete con contenido"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
msgid "Package Name"
msgstr "Nombre del paquete"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package_name
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Package Reference"
msgstr "Referencia del paquete"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Package Transfers"
msgstr "Las transferencias del paquete"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_package_view
#: model:ir.ui.menu,name:stock.menu_package
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Packages"
msgstr "Paquetes"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_package_view
msgid ""
"Packages are usually created by pack operations made on transfers and can "
"contains several different products. You can then reuse a package to move "
"its whole content somewhere else, or to pack it into another bigger package. "
"A package can also be unpacked, allowing the disposal of its former content "
"as single units again."
msgstr ""
"Los paquetes son generalmente creados por paquete las operaciones realizadas "
"en las transferencias y pueden contiene varios productos diferentes. Luego "
"puede volver a utilizar un paquete para mover su contenido entero en algún "
"otro lugar, o para embalar en otro paquete más grande. Un paquete también "
"puede ser descomprimido, lo que permite la eliminación de su contenido "
"anterior como unidades individuales otra vez."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
msgid "Packaging"
msgstr "Empaquetado"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_wh_pack_stock_loc_id
msgid "Packing Location"
msgstr "Ubicación de empaquetado"

#. module: stock
#: model:ir.model,name:stock.model_stock_move_line
msgid "Packing Operation"
msgstr "Operación de empaquetado"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:100
#: model:stock.location,name:stock.location_pack_zone
#, python-format
msgid "Packing Zone"
msgstr "Zona de empaquetado"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Packs and Lots"
msgstr "Packs y Lotes"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_compute_wizard
msgid "Parameters"
msgstr "Parámetros"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_location_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_parent_location_id
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Parent Location"
msgstr "Ubicación padre"

#. module: stock
#: selection:procurement.group,move_type:0
msgid "Partial"
msgstr "Parcial"

#. module: stock
#: selection:stock.move,state:0
msgid "Partially Available"
msgstr "Parcialmente disponible"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group_partner_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_partner_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Partner"
msgstr "Empresa"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_partner_address_id
msgid "Partner Address"
msgstr "Dirección empresa"

#. module: stock
#: model:stock.location,name:stock.stock_location_locations_partner
msgid "Partner Locations"
msgstr "Ubicaciones de empresas"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_inventory_form
msgid ""
"Periodical Inventories are used to count the number of products\n"
"            available per location. You can use it once a year when you do\n"
"            the general inventory or whenever you need it, to adapt the\n"
"            current inventory level of a product."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Periodical Tasks"
msgstr "Tareas periódicas"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_filter
msgid "Physical Inventories by Month"
msgstr "Inventarios físicos por mes"

#. module: stock
#: model:stock.location,name:stock.stock_location_locations
msgid "Physical Locations"
msgstr "Ubicaciones físicas"

#. module: stock
#: model:ir.model,name:stock.model_stock_quant_package
msgid "Physical Packages"
msgstr "Paquetes físicos"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:224
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_pick_ids
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_pick_ids
#, python-format
msgid "Pick"
msgstr "Recogida"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:435
#, python-format
msgid "Pick + Pack + Ship"
msgstr "Recoger + empaquetar + enviar"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:434
#, python-format
msgid "Pick + Ship"
msgstr "Recoger + enviar"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_pick_type_id
msgid "Pick Type"
msgstr "Tipo de conduce"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_overprocessed_transfer_picking_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_picking_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap_picking_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Picking"
msgstr "Conduce"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Picking List"
msgstr "Conduce"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Picking Lists"
msgstr "Conduces"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form
msgid "Picking Moves"
msgstr ""

#. module: stock
#: model:ir.actions.report,name:stock.action_report_picking
msgid "Picking Operations"
msgstr "Conduces concodigos de barra"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Picking list"
msgstr "Conduce"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.procurement_group_form_view
msgid "Pickings"
msgstr "Conduces"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Pickings already processed"
msgstr "Conduces ya procesados"

#. module: stock
#: model:ir.actions.act_window,name:stock.do_view_pickings
msgid "Pickings for Groups"
msgstr "Conduces para grupos"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Pickings that are late on scheduled time"
msgstr "Conduces retrasados según planificación"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_level_forecast_filter
msgid "Pivot"
msgstr "Pivot"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Planned Transfer"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_web_planner
msgid "Planner"
msgstr "Planificador"

#. module: stock
#: code:addons/stock/models/stock_picking.py:636
#, python-format
msgid "Please add some lines to move"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_picking.py:805
#, python-format
msgid "Please process some quantities to put in the pack first!"
msgstr ""
"Por favor procesar algunas cantidades para poner en el paquete de primero!"

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:126
#, python-format
msgid "Please specify at least one non-zero quantity."
msgstr "Especifique por favor al menos una cantidad no nula."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Positive Stock"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_product_packaging
msgid "Preferred Packaging"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_route_ids
msgid "Preferred route"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Print"
msgstr "Imprimir"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_printed
msgid "Printed"
msgstr "Impreso"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat_sequence
#: model:ir.model.fields,field_description:stock.field_stock_move_priority
#: model:ir.model.fields,field_description:stock.field_stock_picking_priority
msgid "Priority"
msgstr "Prioridad"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_priority
msgid ""
"Priority for this picking. Setting manually a value here would set it as "
"priority for all the moves"
msgstr ""
"Prioridad para este conduce. Estableciendo manualmente un valor aquí, lo "
"pondrá como prioridad para todos los movimientos"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Process operations faster with barcodes"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Process picking in batch per worker"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_overprocessed_transfer
msgid "Processed more than initial demand"
msgstr ""

#. module: stock
#: selection:stock.location,usage:0
msgid "Procurement"
msgstr "Abastecimiento"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Procurement Exceptions"
msgstr "Excepciones abastecimiento"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_group_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_group_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_group_id
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Procurement Group"
msgstr "Grupo de abastecimiento"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_location_id
msgid "Procurement Location"
msgstr "Ubicación de abastecimiento"

#. module: stock
#: model:ir.model,name:stock.model_procurement_group
msgid "Procurement Requisition"
msgstr "Petición de abastecimiento"

#. module: stock
#: model:ir.model,name:stock.model_procurement_rule
#: model:ir.model.fields,field_description:stock.field_stock_move_rule_id
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_rule_form
msgid "Procurement Rule"
msgstr "Regla de abastecimiento"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route_pull_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_rule_tree
msgid "Procurement Rules"
msgstr "Reglas de abastecimiento"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.procurement_group_form_view
msgid "Procurement group"
msgstr ""

#. module: stock
#: model:ir.actions.server,name:stock.ir_cron_scheduler_action_ir_actions_server
#: model:ir.cron,cron_name:stock.ir_cron_scheduler_action
#: model:ir.cron,name:stock.ir_cron_scheduler_action
msgid "Procurement: run scheduler"
msgstr ""

#. module: stock
#: model:stock.location,name:stock.location_procurement
msgid "Procurements"
msgstr "Abastecimientos"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line_produce_line_ids
msgid "Produce Line"
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:347
#, python-format
msgid "Produced Qty"
msgstr "Ctdad producida"

#. module: stock
#: model:ir.model,name:stock.model_product_product
#: model:ir.model.fields,field_description:stock.field_report_stock_forecast_product_id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_product_id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_product_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line_product_id
#: model:ir.model.fields,field_description:stock.field_stock_move_product_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_product_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot_product_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_product_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line_product_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap_product_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_product_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_product_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_repair_product_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap_product_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_unbuild_product_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_lot_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Product"
msgstr "Producto"

#. module: stock
#: code:addons/stock/models/stock_location.py:173
#: model:ir.model.fields,field_description:stock.field_stock_location_route_categ_ids
#: model:ir.ui.menu,name:stock.menu_product_category_config_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#, python-format
msgid "Product Categories"
msgstr "Categorías de productos"

#. module: stock
#: model:ir.model,name:stock.model_product_category
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat_category_id
msgid "Product Category"
msgstr "Categoría de producto"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_product_code
msgid "Product Code"
msgstr "Código de producto"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
msgid "Product Lots"
msgstr "Lotes de producto"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
msgid "Product Lots Filter"
msgstr "Filtro lotes de producto"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
msgid "Product Move"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_move_line_action
#: model:ir.ui.menu,name:stock.stock_move_line_menu
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "Product Moves"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_product_name
msgid "Product Name"
msgstr "Nombre del producto"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_product_packagings
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Product Packages"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_product_template
#: model:ir.model.fields,field_description:stock.field_report_stock_forecast_product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_move_product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_product_tmpl_id
msgid "Product Template"
msgstr "Plantilla producto"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_product_type
msgid "Product Type"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Product Types"
msgstr "Tipos de productos"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_product_uom
msgid "Product Unit of Measure"
msgstr "Unidad de medida del producto"

#. module: stock
#: model:ir.ui.menu,name:stock.product_product_menu
msgid "Product Variants"
msgstr "Variantes de Producto"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid ""
"Product this lot/serial number contains. You cannot change it anymore if it "
"has already been moved."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_has_tracking
msgid "Product with Tracking"
msgstr ""

#. module: stock
#: model:stock.location,name:stock.location_production
#: selection:stock.location,usage:0
msgid "Production"
msgstr "Producción"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_property_stock_production
#: model:ir.model.fields,field_description:stock.field_product_template_property_stock_production
msgid "Production Location"
msgstr "Ubicación de producción"

#. module: stock
#: code:addons/stock/models/stock_location.py:163
#: code:addons/stock/wizard/stock_quantity_history.py:29
#: model:ir.actions.act_window,name:stock.act_product_location_open
#: model:ir.actions.act_window,name:stock.product_template_action_product
#: model:ir.model.fields,field_description:stock.field_stock_location_route_product_ids
#: model:ir.ui.menu,name:stock.menu_product_in_config_stock
#: model:ir.ui.menu,name:stock.menu_product_variant_config_stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#, python-format
msgid "Products"
msgstr "Productos"

#. module: stock
#: code:addons/stock/models/product.py:310
#, python-format
msgid "Products: "
msgstr "Productos: "

#. module: stock
#: selection:procurement.rule,group_propagation_option:0
msgid "Propagate"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_propagate
#: model:ir.model.fields,field_description:stock.field_stock_location_path_propagate
#: model:ir.model.fields,field_description:stock.field_stock_move_propagate
msgid "Propagate cancel and split"
msgstr "Propagar cancelación y división"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_rule_form
msgid "Propagation Options"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_group_propagation_option
msgid "Propagation of Procurement Group"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Purchase Flow"
msgstr "Flujo de Compra"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_push_rule_id
msgid "Push Rule"
msgstr "Regla push"

#. module: stock
#: model:ir.actions.act_window,name:stock.stolocpath
#: model:ir.model.fields,field_description:stock.field_stock_location_route_push_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Push Rules"
msgstr "Reglas push"

#. module: stock
#: model:ir.model,name:stock.model_stock_location_path
msgid "Pushed Flow"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat_putaway_id
msgid "Put Away Method"
msgstr "Método de deshecho"

#. module: stock
#: model:ir.model,name:stock.model_product_putaway
#: model:ir.model.fields,field_description:stock.field_stock_location_putaway_strategy_id
msgid "Put Away Strategy"
msgstr "Estrategia de deshecho"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Put your products in packs (e.g. parcels, boxes) and track them"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway
msgid "Putaway"
msgstr "Deshechar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
msgid "QUANTITY"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_qty_multiple
msgid "Qty Multiple"
msgstr "Ctdad múltiple"

#. module: stock
#: sql_constraint:stock.warehouse.orderpoint:0
msgid "Qty Multiple must be greater than or equal to zero."
msgstr "El múltiplo de la cantidad debe ser mayor o igual a 0."

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:98
#, python-format
msgid "Quality Control"
msgstr "Control calidad"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_wh_qc_stock_loc_id
msgid "Quality Control Location"
msgstr "Ubicación del control de calidad"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_repair_quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap_quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_unbuild_quant_ids
msgid "Quant"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_forecast_quantity
#: model:ir.model.fields,field_description:stock.field_stock_production_lot_product_qty
#: model:ir.model.fields,field_description:stock.field_stock_quant_quantity
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line_quantity
#: model:ir.model.fields,field_description:stock.field_stock_scrap_scrap_qty
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Quantity"
msgstr "Cantidad"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Quantity :"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_quantity_done
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_kanban
msgid "Quantity Done"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "Quantity Multiple"
msgstr "Múltiplo de la cantidad"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_qty_available
#: model:ir.model.fields,field_description:stock.field_product_template_qty_available
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form
msgid "Quantity On Hand"
msgstr "Stock real"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_reserved_availability
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form
msgid "Quantity Reserved"
msgstr "Cantidad reservada"

#. module: stock
#: code:addons/stock/wizard/stock_change_product_qty.py:77
#, python-format
msgid "Quantity cannot be negative."
msgstr "La cantidad no puede ser negativa"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_availability
msgid "Quantity in stock that can still be reserved for this move"
msgstr ""
"Cantidad en existencias que puede ser reservada aún para este movimiento"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_product_qty
msgid "Quantity in the default UoM of the product"
msgstr "Cantidad en la UdM por defecto del producto"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product_incoming_qty
msgid ""
"Quantity of planned incoming products.\n"
"In a context with a single Stock Location, this includes goods arriving to "
"this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods arriving to the "
"Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods arriving to any Stock Location with "
"'internal' type."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product_outgoing_qty
msgid ""
"Quantity of planned outgoing products.\n"
"In a context with a single Stock Location, this includes goods leaving this "
"Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods leaving the Stock "
"Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods leaving any Stock Location with 'internal' "
"type."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_quantity
msgid ""
"Quantity of products in this quant, in the default unit of measure of the "
"product"
msgstr ""
"Cantidad de productos en este quant, en la unidad de medida por defecto del "
"producto"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_reserved_quantity
msgid ""
"Quantity of reserved products in this quant, in the default unit of measure "
"of the product"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_reserved_availability
msgid "Quantity that has already been reserved for this move"
msgstr "Cantidad que ha sido ya reservada para este movimiento"

#. module: stock
#: model:ir.actions.act_window,name:stock.lot_open_quants
#: model:ir.model,name:stock.model_stock_quant
#: model:ir.model.fields,field_description:stock.field_stock_production_lot_quant_ids
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Quants"
msgstr "Quants"

#. module: stock
#: code:addons/stock/models/stock_quant.py:69
#, python-format
msgid "Quants cannot be created for consumables or services."
msgstr ""

#. module: stock
#: model:stock.inventory.line,product_name:stock.stock_inventory_line_12
msgid "RAM SR5"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_rate_picking_backorders
msgid "Rate Picking Backorders"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_rate_picking_late
msgid "Rate Picking Late"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: selection:stock.picking,state:0
msgid "Ready"
msgstr "Preparado"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_product_qty
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_line_tree2
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Real Quantity"
msgstr "Cantidad real"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line_product_qty
msgid "Real Reserved Quantity"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_reception_route_id
msgid "Receipt Route"
msgstr "Ruta de recepción"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:432
#, python-format
msgid "Receipt in 1 step"
msgstr "Recepción en 1 paso"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:432
#, python-format
msgid "Receipt in 2 steps"
msgstr "Recepción en 2 pasos"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:433
#, python-format
msgid "Receipt in 3 steps"
msgstr "Recepción en 3 pasos"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:202
#: model:stock.picking.type,name:stock.chi_picking_type_in
#: model:stock.picking.type,name:stock.picking_type_in
#, python-format
msgid "Receipts"
msgstr "Recepciones"

#. module: stock
#: selection:stock.warehouse,reception_steps:0
msgid "Receive goods directly in stock (1 step)"
msgstr "Recibir bienes directamente en las existencias (1 paso)"

#. module: stock
#: code:addons/stock/models/product.py:324
#, python-format
msgid "Received Qty"
msgstr "Ctdad recibida"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Receptions"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group_name
#: model:ir.model.fields,field_description:stock.field_stock_move_line_reference
#: model:ir.model.fields,field_description:stock.field_stock_move_reference
#: model:ir.model.fields,field_description:stock.field_stock_picking_name
#: model:ir.model.fields,field_description:stock.field_stock_scrap_name
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking_board
msgid "Reference"
msgstr "Referencia"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_sequence_id
msgid "Reference Sequence"
msgstr "Secuencia de la referencia"

#. module: stock
#: sql_constraint:stock.picking:0
msgid "Reference must be unique per company!"
msgstr "La referencia debe ser única por compañía"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_origin
msgid "Reference of the document"
msgstr "Referencia del documento"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Register lots, packs, location"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Remaining parts of picking partially processed"
msgstr "Partes restantes del conduce parcialmente procesado"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_removal
msgid "Removal"
msgstr "Retirada"

#. module: stock
#: model:ir.model,name:stock.model_product_removal
#: model:ir.model.fields,field_description:stock.field_stock_location_removal_strategy_id
msgid "Removal Strategy"
msgstr "Estrategia de retirada"

#. module: stock
#: code:addons/stock/models/stock_quant.py:104
#, python-format
msgid "Removal strategy %s not implemented."
msgstr "Estrategia de retirada %s no implementada."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_reordering_max_qty
#: model:ir.model.fields,field_description:stock.field_product_template_reordering_max_qty
msgid "Reordering Max Qty"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_reordering_min_qty
#: model:ir.model.fields,field_description:stock.field_product_template_reordering_min_qty
msgid "Reordering Min Qty"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.act_stock_warehouse_2_stock_warehouse_orderpoint
#: model:ir.actions.act_window,name:stock.action_orderpoint_form
#: model:ir.actions.act_window,name:stock.product_open_orderpoint
#: model:ir.model.fields,field_description:stock.field_product_product_nbr_reordering_rules
#: model:ir.model.fields,field_description:stock.field_product_template_nbr_reordering_rules
#: model:ir.ui.menu,name:stock.menu_reordering_rules_config
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Reordering Rules"
msgstr "Reglas de reabastecimiento"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Reordering Rules Search"
msgstr "Búsqueda de reglas de reabastecimiento"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_warehouse_report
msgid "Reporting"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings_use_propagation_minimum_delta
msgid ""
"Rescheduling applies to any chain of operations (e.g. Make To Order, Pick "
"Pack Ship). In the case of MTO sales, a vendor delay (updated incoming date) "
"impacts the expected delivery date to the customer. \n"
" This option allows to not propagate the rescheduling if the change is not "
"critical."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Rescheduling applies to any chain of operations (e.g. Make To Order, Pick "
"Pack Ship). In the case of MTO sales, a vendor delay (updated incoming date) "
"impacts the expected delivery date to the customer. This option allows to "
"not propagate the rescheduling if the change is not critical."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings_module_procurement_jit
msgid "Reservation"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Reservations"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line_product_uom_qty
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree
msgid "Reserved"
msgstr "Reservado"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_reserved_quantity
msgid "Reserved Quantity"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings_module_procurement_jit
msgid ""
"Reserving products manually in delivery orders or by running the scheduler "
"is advised to better manage priorities in case of long customer lead times "
"or/and frequent stock-outs."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Resolve Procurement Exceptions"
msgstr "Resolver Excepciones Adquisiciones"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_responsible_id
#: model:ir.model.fields,field_description:stock.field_product_template_responsible_id
msgid "Responsible"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_resupply_route_ids
msgid "Resupply Routes"
msgstr "Rutas de reabastecimiento"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_resupply_wh_ids
msgid "Resupply Warehouses"
msgstr "Almacenes de reabastecimiento"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quantity_history
msgid "Retrieve the Inventory Quantities"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
msgid "Return"
msgstr "Devolver"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_location_id
msgid "Return Location"
msgstr "Ubicación para devolucion"

#. module: stock
#: model:ir.model,name:stock.model_stock_return_picking
msgid "Return Picking"
msgstr "Devolver conduce"

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:97
#, python-format
msgid "Return of %s"
msgstr ""

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:147
#, python-format
msgid "Returned Picking"
msgstr "Conduce devuelto"

#. module: stock
#: model:ir.actions.act_window,name:stock.act_stock_return_picking
msgid "Reverse Transfer"
msgstr "Revertir transferencia"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_parent_right
msgid "Right Parent"
msgstr "Padre derecho"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_route_id
#: model:ir.model.fields,field_description:stock.field_stock_location_path_route_id
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_view_search
msgid "Route"
msgstr "Ruta"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route_name
msgid "Route Name"
msgstr "Nombre de la ruta"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_route_sequence
#: model:ir.model.fields,field_description:stock.field_stock_location_path_route_sequence
msgid "Route Sequence"
msgstr "Secuencia de la ruta"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_routes_form
#: model:ir.model.fields,field_description:stock.field_product_category_route_ids
#: model:ir.model.fields,field_description:stock.field_product_product_route_ids
#: model:ir.model.fields,field_description:stock.field_product_template_route_ids
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_route_ids
#: model:ir.ui.menu,name:stock.menu_routes_config
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_tree
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Routes"
msgstr "Rutas"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Routes Management"
msgstr "Gestión de rutas"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_resupply_route_ids
msgid ""
"Routes will be created for these resupply warehouses and you can select them "
"on products and product categories"
msgstr ""
"Las rutas se crearán para este almacén de reabastecimiento y podrá "
"seleccionarlas en los productos y las categorías de producto"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "Rules"
msgstr "Reglas"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_procurement_compute
#: model:ir.ui.menu,name:stock.menu_procurement_compute
msgid "Run Scheduler"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_scheduler_compute
msgid "Run Scheduler Manually"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_compute_wizard
msgid "Run Schedulers"
msgstr "Ejecutar planificadores"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Sales Flow"
msgstr "Flujo de Ventas"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Scheduled"
msgstr "Planificado"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_scheduled_date
msgid "Scheduled Date"
msgstr "Fecha prevista"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_date_expected
msgid "Scheduled date for the processing of this move"
msgstr "Fecha planificada para el procesado de este movimiento."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_scheduled_date
msgid ""
"Scheduled time for the first part of the shipment to be processed. Setting "
"manually a value here would set it as expected date for all the stock moves."
msgstr ""
"Fecha programada para la primera parte del envío a ser procesada. Establecer "
"manualmente un valor aquí significará la fecha esperada para todos los "
"movimientos de existencias."

#. module: stock
#: code:addons/stock/models/stock_picking.py:818
#: model:ir.model.fields,field_description:stock.field_stock_move_scrap_ids
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap_scrap_id
#: model:ir.ui.menu,name:stock.menu_stock_scrap
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view2
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#, python-format
msgid "Scrap"
msgstr "Desecho"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap_scrap_location_id
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
msgid "Scrap Location"
msgstr "Ubicación desecho"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap_move_id
msgid "Scrap Move"
msgstr "Movimiento desecho"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_scrap
msgid "Scrap Orders"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_scrapped
#: model:stock.location,name:stock.stock_location_scrapped
msgid "Scrapped"
msgstr "Desechado"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_stock_scrap
msgid ""
"Scrapping a product will remove it from your stock. The product will\n"
"                end up in a scrap location that can be used for reporting "
"purpose."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Scraps"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_filter
msgid "Search Inventory"
msgstr "Buscar inventario"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_line_search
msgid "Search Inventory Lines"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_rule_filter
msgid "Search Procurement"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
msgid "Search Scrap"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_path_filter
msgid "Search Stock Location Paths"
msgstr "Buscar Stock Ubicación Caminos"

#. module: stock
#: code:addons/stock/models/stock_inventory.py:108
#, python-format
msgid "Select products manually"
msgstr "Seleccione los productos de forma manual"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Select the places where this route can be selected"
msgstr "Seleccione los lugares donde la ruta puede ser seleccionada"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner_picking_warn
#: model:ir.model.fields,help:stock.field_res_users_picking_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Sell and purchase products in different units of measure"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_sequence
#: model:ir.model.fields,field_description:stock.field_stock_location_path_sequence
#: model:ir.model.fields,field_description:stock.field_stock_location_route_sequence
#: model:ir.model.fields,field_description:stock.field_stock_move_sequence
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_sequence
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Sequence"
msgstr "Secuencia"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:678
#, python-format
msgid "Sequence in"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:682
#, python-format
msgid "Sequence internal"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:679
#, python-format
msgid "Sequence out"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:680
#, python-format
msgid "Sequence packing"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:681
#, python-format
msgid "Sequence picking"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_prodlot_name
msgid "Serial Number Name"
msgstr "Nombre del número de serie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_warehouse_id
msgid "Served Warehouse"
msgstr "Almacén servido"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set Putaway Strategies on Locations"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set Warehouse Routes"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_category_removal_strategy_id
msgid ""
"Set a specific removal strategy that will be used regardless of the source "
"location for this product category"
msgstr ""
"Establezca una estrategia de retirada específica que se usará "
"independientemente de la ubicación origen para esta categoría de producto"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set expiration dates on lots &amp; serial numbers"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set owner on stored products"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set product attributes (e.g. color, size) to manage variants"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Set the <i>Real Quantity</i> for each Product and <i>Validate the Inventory</"
"i>"
msgstr ""
"Establecer la <i>Cantidad Real</i> de cada producto y <i>validar el "
"inventario</i>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Set the products you want to track with lots or serial numbers by setting "
"the Tracking field on the product form"
msgstr ""
"Los productos que desea seguir con números de serie o lotes estableciendo el "
"campo de rastreo en el formulario del producto"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Set to Draft"
msgstr "Cambiar a borrador"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_location_id
msgid ""
"Sets a location if you produce at a fixed location. This can be a partner "
"location if you subcontract the manufacturing operations."
msgstr ""
"Indica una ubicación si se producen en una ubicación fija. Puede ser una "
"ubicación de empresa si subcontrata las operaciones de fabricación."

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_config_settings
#: model:ir.ui.menu,name:stock.menu_stock_general_settings
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Settings"
msgstr "Configuración"

#. module: stock
#: model:stock.location,name:stock.stock_location_components
msgid "Shelf 1"
msgstr "Estante 1"

#. module: stock
#: model:stock.location,name:stock.stock_location_14
msgid "Shelf 2"
msgstr "Estante 2"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_posy
msgid "Shelves (Y)"
msgstr "Estantería (Y)"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:434
#, python-format
msgid "Ship Only"
msgstr "Sólo enviar"

#. module: stock
#: selection:stock.warehouse,delivery_steps:0
msgid "Ship directly from stock (Ship only)"
msgstr "Enviar directamente desde la existencias (Sólo enviar)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Shipping Connectors"
msgstr "Conectores de envío"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_move_type
msgid "Shipping Policy"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Shipping connectors allow to compute accurate shipping costs, print shipping "
"labels and request carrier picking at your warehouse to ship to the "
"customer. Apply shipping connector from delivery methods."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_code
msgid "Short Name"
msgstr "Nombre corto"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_code
msgid "Short name used to identify your warehouse"
msgstr "Nombre corto usado para identificar su almacén"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_show_check_availability
msgid "Show Check Availability"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_show_operations
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_show_operations
msgid "Show Detailed Operations"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_show_mark_as_todo
msgid "Show Mark As Todo"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_show_operations
msgid "Show Operations"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_show_reserved
msgid "Show Reserved"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_show_validate
msgid "Show Validate"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Show all records which has next action date is before today"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_string_availability_info
msgid "Show various information on stock availability for this move"
msgstr ""
"Mostrar información variada sobre la disponibilidad de existencias para este "
"movimiento"

#. module: stock
#: model:stock.location,name:stock.location_refrigerator_small
msgid "Small Refrigerator"
msgstr "Pequeño refrigerador"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Source"
msgstr "Texto original"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_origin
#: model:ir.model.fields,field_description:stock.field_stock_picking_origin
#: model:ir.model.fields,field_description:stock.field_stock_scrap_origin
msgid "Source Document"
msgstr "Documento origen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_location_src_id
#: model:ir.model.fields,field_description:stock.field_stock_location_path_location_from_id
#: model:ir.model.fields,field_description:stock.field_stock_move_location_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_location_id
msgid "Source Location"
msgstr "Ubicación origen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Source Location:"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line_package_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
msgid "Source Package"
msgstr "Paquete fuente"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Source Package :"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_procurement_rule_location_src_id
msgid "Source location is action=move"
msgstr "Ubicación origen es la acción=movimiento"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory_lot_id
msgid ""
"Specify Lot/Serial Number to focus your inventory on a particular Lot/Serial "
"Number."
msgstr ""
"Especifique lote/nº de serie para centrar su inventario en un lote/nº de "
"serie en particular."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory_partner_id
msgid "Specify Owner to focus your inventory on a particular Owner."
msgstr ""
"Especifique propietario para centrar su inventario en un propietario en "
"particular."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory_package_id
msgid "Specify Pack to focus your inventory on a particular Pack."
msgstr ""
"Especifique paquete para centrar su inventario en un paquete en particular."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory_category_id
msgid ""
"Specify Product Category to focus your inventory on a particular Category."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory_product_id
msgid "Specify Product to focus your inventory on a particular Product."
msgstr ""
"Especifique producto para centrar su inventario en un producto en particular."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Start Inventory"
msgstr "Iniciar inventario"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_state
#: model:ir.model.fields,field_description:stock.field_stock_inventory_state
#: model:ir.model.fields,field_description:stock.field_stock_move_line_state
#: model:ir.model.fields,field_description:stock.field_stock_move_state
#: model:ir.model.fields,field_description:stock.field_stock_picking_state
#: model:ir.model.fields,field_description:stock.field_stock_scrap_state
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_filter
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Status"
msgstr "Estado"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:96
#: model:stock.location,name:stock.stock_location_shop0
#: model:stock.location,name:stock.stock_location_stock
#, python-format
msgid "Stock"
msgstr "Existencias"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "Stock Inventory"
msgstr "Inventario de existencias"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_line_tree
msgid "Stock Inventory Lines"
msgstr "Líneas de regularización de inventario"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_level_forecast_report_product
#: model:ir.actions.act_window,name:stock.action_stock_level_forecast_report_template
msgid "Stock Level Forecast"
msgstr "Previsión niveles de existencias"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_level_forecast_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_level_forecast_graph
#: model_terms:ir.ui.view,arch_db:stock.view_stock_level_forecast_pivot
msgid "Stock Level forecast"
msgstr "Previsión del Nivel de existencias"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#: model_terms:ir.ui.view,arch_db:stock.view_location_tree2
msgid "Stock Location"
msgstr "Ubicación de existencias"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Stock Locations"
msgstr "Ubicaciones de existencias"

#. module: stock
#: model:ir.model,name:stock.model_stock_move
#: model:ir.model.fields,field_description:stock.field_product_product_stock_move_ids
#: model:ir.model.fields,field_description:stock.field_stock_move_line_move_id
msgid "Stock Move"
msgstr "Movimiento de existencias"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_move_action
#: model:ir.model.fields,field_description:stock.field_stock_picking_move_lines
#: model:ir.ui.menu,name:stock.stock_move_menu
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Stock Moves"
msgstr "Movimientos de existencias"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_graph
#: model_terms:ir.ui.view,arch_db:stock.view_move_pivot
msgid "Stock Moves Analysis"
msgstr "Análisis de los movimientos de existencias"

#. module: stock
#: model:ir.actions.act_window,name:stock.product_open_quants
#: model:ir.actions.act_window,name:stock.product_template_open_quants
msgid "Stock On Hand"
msgstr "Cantidad a mano"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
msgid "Stock Operation"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_all
msgid "Stock Operations"
msgstr "Operaciones de existencias"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner_picking_warn
#: model:ir.model.fields,field_description:stock.field_res_users_picking_warn
#: model:ir.model.fields,field_description:stock.field_stock_move_line_picking_id
msgid "Stock Picking"
msgstr "Conduce de almacen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_stock_quant_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_view_graph
msgid "Stock Quant"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_quantity_history
msgid "Stock Quantity History"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that are Available (Ready to process)"
msgstr ""
"Movimientos de existencias que están disponibles (preparados para ser "
"procesados)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that are Confirmed, Available or Waiting"
msgstr ""
"Los movimientos de existencias que están confirmados, disponibles o en "
"espera."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that have been processed"
msgstr "Movimientos de existencias que han sido procesados"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings_group_stock_multi_locations
msgid "Storage Locations"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings_group_stock_multi_locations
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Store products in specific locations of your warehouse (e.g. bins, racks) "
"and to track inventory accordingly."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route_supplied_wh_id
msgid "Supplied Warehouse"
msgstr "Almacén suministrado"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Supply Chain"
msgstr "Cadena de suministro"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_procure_method
msgid "Supply Method"
msgstr "Método de suministro"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route_supplier_wh_id
msgid "Supplying Warehouse"
msgstr "Almacén de suministro"

#. module: stock
#: selection:procurement.rule,procure_method:0
msgid "Take From Stock"
msgstr "Obtener de las existencias"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Technical Information"
msgstr "Información técnica"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_warehouse_id
msgid ""
"Technical field depicting the warehouse to consider for the route selection "
"on the next procurement (if any)."
msgstr ""
"Campo técnico que indica el almacén a considerar para la selección de ruta "
"en el siguiente abastecimiento (si lo hay)."

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company_internal_transit_location_id
msgid ""
"Technical field used for resupply routes between warehouses that belong to "
"this company"
msgstr ""
"Campo técnico usada para reabastecer rutas entre almacenes que pertenecen a "
"esta compañía"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_show_check_availability
msgid ""
"Technical field used to compute whether the check availability button should "
"be shown."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_show_mark_as_todo
msgid ""
"Technical field used to compute whether the mark as todo button should be "
"shown."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_show_validate
msgid "Technical field used to compute whether the validate should be shown."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_restrict_partner_id
msgid ""
"Technical field used to depict a restriction on the ownership of quants to "
"consider when marking this move as 'done'"
msgstr ""
"Campo técnico que muestra la restricción en el propietario de los quants a "
"considerar cuando se marque este movimiento como realizado."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_price_unit
msgid ""
"Technical field used to record the product cost set by the user during a "
"picking confirmation (when costing method used is 'average price' or "
"'real'). Value given in company currency and in product uom."
msgstr ""
"Campo técnico usado para registrar el coste de un producto establecido por "
"el usuario durante la confirmación del conduce (cuando el método de coste "
"usado es 'precio medio' o 'real'). El valor viene dado en la moneda de la "
"compañía y en la UdM del producto."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line_produce_line_ids
msgid "Technical link to see which line was produced with this. "
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line_consume_line_ids
msgid "Technical link to see who consumed what. "
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_product_tmpl_id
msgid "Technical: used in views"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product_stock_move_ids
#: model:ir.model.fields,help:stock.field_product_product_stock_quant_ids
msgid "Technical: used to compute quantities."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_product_tmpl_id
msgid "Template"
msgstr "Plantilla"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_path_auto
msgid ""
"The 'Manual Operation' value will create a stock move after the current one."
"With 'Automatic No Step Added', the location is replaced in the original "
"move."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "The RFQ becomes a Purchase Order and a Transfer Order is created"
msgstr ""
"La solicitud de cotización se convierte en una orden de compra y se crea una "
"orden de transferencia"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product_sale_delay
#: model:ir.model.fields,help:stock.field_product_template_sale_delay
msgid ""
"The average delay in days between the confirmation of the customer order and "
"the delivery of the finished products. It's the time you promise to your "
"customers."
msgstr ""
"El retraso medio en días entre la confirmación del pedido de cliente y la "
"entrega de los productos finales. Es el tiempo que promete a sus clientes."

#. module: stock
#: code:addons/stock/models/stock_move.py:1033
#, python-format
msgid ""
"The backorder <a href=# data-oe-model=stock.picking data-oe-id=%d>%s</a> has "
"been created."
msgstr ""

#. module: stock
#: sql_constraint:stock.location:0
msgid "The barcode for a location must be unique per company !"
msgstr "El código de barras de una ubicación debe ser único por compañía"

#. module: stock
#: sql_constraint:stock.warehouse:0
msgid "The code of the warehouse must be unique per company!"
msgstr "El código del almacén debe ser único por compañía"

#. module: stock
#: sql_constraint:stock.production.lot:0
msgid "The combination of serial number and product must be unique !"
msgstr "La combinación de producto y número de serie debe ser única!"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_company_id
msgid "The company is automatically set from your user preferences."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory_date
msgid ""
"The date that will be used for the stock level check of the products and the "
"validation of the stock move related to this inventory."
msgstr ""
"La fecha que será usada para la comprobación del nivel de existencias de los "
"productos y la validación del movimiento de existencias relacionado con este "
"inventario."

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:143
#, python-format
msgid ""
"The default resupply warehouse should be different than the warehouse itself!"
msgstr ""
"El almacén de reabastecimiento por defecto debe ser distinto al del almacén "
"en sí."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route_pull_ids
msgid ""
"The demand represented by a procurement from e.g. a sale order, a reordering "
"rule, another move, needs to be solved by applying a procurement rule. "
"Depending on the action on the procurement rule,this triggers a purchase "
"order, manufacturing order or another move. This way we create chains in the "
"reverse order from the endpoint with the original demand to the starting "
"point. That way, it is always known where we need to go and that is why they "
"are preferred over push rules."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_category_form_view_inherit
msgid ""
"The following routes will apply to the products in this category taking into "
"account parent categories:"
msgstr ""
"Las siguientes rutas se aplicarán a los productos en esta categoría teniendo "
"en cuenta las categorías padre:"

#. module: stock
#: sql_constraint:stock.warehouse:0
msgid "The name of the warehouse must be unique per company!"
msgstr "El nombre del almacén debe ser único por compañía"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_path_location_dest_id
msgid "The new location where the goods need to go"
msgstr "La nueva ubicación donde las mercancías deban ir"

#. module: stock
#: model:ir.model,name:stock.model_stock_picking_type
msgid "The operation type determines the picking view"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_picking_type_action
msgid ""
"The operation type system allows you to assign each stock\n"
"                operation a specific type which will alter its views "
"accordingly.\n"
"                On the operation type you could e.g. specify if packing is "
"needed by default,\n"
"                if it should show the customer."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_package_id
msgid "The package containing this quant"
msgstr "El paquete que contiene este quant"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_location_id
msgid ""
"The parent location that includes this location. Example : The 'Dispatch "
"Zone' is the 'Gate 1' parent location."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint_qty_multiple
msgid ""
"The procurement quantity will be rounded up to this multiple.  If it is 0, "
"the exact quantity will be used."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_rule_id
msgid "The procurement rule that created this stock move"
msgstr "La regla de compras que creó este movimiento de stock"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid "The product"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_push_rule_id
msgid "The push rule that created this stock move"
msgstr "La regla push que creó este movimiento"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "The quotation becomes a Sales Order and a Transfer Order is created"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move.py:266
#: code:addons/stock/models/stock_move_line.py:79
#, python-format
msgid ""
"The requested operation cannot be processed because of a programming error "
"setting the `product_qty` field instead of the `product_uom_qty`."
msgstr ""
"La operación solicitada no puede ser procesada debido a un error de "
"programación estableciendo el campo 'product_qty' en lugar del campo "
"'product_uom_qty'."

#. module: stock
#: code:addons/stock/models/stock_inventory.py:144
#: code:addons/stock/models/stock_inventory.py:146
#: code:addons/stock/models/stock_inventory.py:148
#: code:addons/stock/models/stock_inventory.py:150
#, python-format
msgid "The selected inventory options are not coherent."
msgstr "Las opciones de inventario seleccionadas no son coherentes"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line_picking_id
msgid "The stock operation where the packing has been made"
msgstr "La operación de existencia en la que se creó el paquete"

#. module: stock
#: model:ir.model.fields,help:stock.field_procurement_rule_warehouse_id
msgid "The warehouse this rule is for"
msgstr "El almacén para el que es esta regla"

#. module: stock
#: model:ir.model.fields,help:stock.field_procurement_rule_propagate_warehouse_id
msgid ""
"The warehouse to propagate on the created move/procurement, which can be "
"different of the warehouse this rule is for (e.g for resupplying rules from "
"another warehouse)"
msgstr ""
"El almacén a propagar en el movimiento/abastecimiento creado, que puede ser "
"diferente del almacén para el que es esta regla (por ejemplo, para reglas de "
"reabastecimiento desde otro almacén)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line_theoretical_qty
msgid "Theoretical Quantity"
msgstr "Cantidad teórica"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.quantsact
msgid ""
"This analysis gives you a fast overview on the current stock level of your "
"products and their current inventory value."
msgstr ""
"Este análisis le da una visión rápida sobre el nivel de stock actual de sus "
"productos y su valor actual del inventario."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_package_packaging_id
msgid ""
"This field should be completed only if everything inside the package share "
"the same product, otherwise it doesn't really makes sense."
msgstr ""
"Este campo debe ser completado sólo si todo lo de dentro del paquete "
"contiene el mismo producto. De otra forma, no tiene mucho sentido."

#. module: stock
#: model:ir.model.fields,help:stock.field_procurement_rule_name
msgid "This field will fill the packing origin and the name of its moves"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"This guide helps getting started with Odoo Inventory.\n"
"                        Once you are done, you will benefit from:"
msgstr ""
"Esta guía de ayuda para empezar con el inventario de Odoo.\n"
"Una vez que haya terminado, usted se beneficiará de:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"This is quite complex to set up, so <strong>contact your Project Manager</"
"strong> to address this."
msgstr ""
"Esto es bastante complejo de configurar, así que <strong>comuníquese con su "
"Gerente de proyecto</strong> a dirección este."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type_default_location_dest_id
msgid ""
"This is the default destination location when you create a picking manually "
"with this operation type. It is possible however to change it or that the "
"routes put another location. If it is empty, it will check for the customer "
"location on the partner. "
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type_default_location_src_id
msgid ""
"This is the default source location when you create a picking manually with "
"this operation type. It is possible however to change it or that the routes "
"put another location. If it is empty, it will check for the supplier "
"location on the partner. "
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_production_lot_form
msgid ""
"This is the list of all the production lots you recorded. When\n"
"            you select a lot, you can get the traceability of the products "
"contained in lot."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_path_picking_type_id
msgid "This is the operation type that will be put on the stock moves"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_owner_id
msgid "This is the owner of the quant"
msgstr "Éste es el propietario del quant"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_product_uom_qty
msgid ""
"This is the quantity of products from an inventory point of view. For moves "
"in the state 'done', this is the quantity of products that were actually "
"moved. For other moves, this is the quantity of product that is planned to "
"be moved. Lowering this quantity does not generate a backorder. Changing "
"this quantity on assigned moves affects the product reservation, and should "
"be done with care."
msgstr ""
"Ésta es la cantidad de productos desde un punto de vista de inventario. Para "
"movimientos en el estado 'Realizado', ésta es la cantidad de productos que "
"se movieron realmente. Para otros movimiento, ésta es la cantidad de "
"producto que está planeado mover. Disminuyendo esta cantidad no se genera un "
"pedido en espera. Cambiando esta cantidad en movimientos asignados, afecta "
"la reserva de producto, y debe ser realizado con cuidado."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_action
msgid ""
"This menu gives you the full traceability of inventory\n"
"                operations on a specific product. You can filter on the "
"product\n"
"                to see all the past or future movements for the product."
msgstr ""
"Este menú le da la completa trazabilidad de las operaciones de inventario en "
"un producto específico. Puede filtrar en el producto para ver el pasado o "
"los movimientos futuros del producto."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid ""
"This note will show up  on internal transfer orders (e.g. where to pick the "
"product in the warehouse)."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "This note will show up on delivery orders."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid ""
"This note will show up on the receipt orders (e.g. where to store the "
"product in the warehouse)."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
msgid ""
"This picking appears to be chained with another operation. Later, if you "
"receive the goods you are returning now, make sure to <b>reverse</b> the "
"returned picking in order to avoid logistic rules to be applied again (which "
"would create duplicated operations)"
msgstr ""
"Esta selección parece encadenarse con otra operación. Más adelante, si usted "
"recibe las mercancías que están volviendo ahora, asegúrese de que a "
"<b>inversa</b> la cosecha devuelto para evitar reglas logísticas a aplicarse "
"otra vez (que serían crear duplicadas operaciones)"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_change_product_qty_new_quantity
msgid ""
"This quantity is expressed in the Default Unit of Measure of the product."
msgstr ""
"Esta cantidad está expresada en la unidad de medida por defecto del producto."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_path_location_from_id
msgid ""
"This rule can be applied when a move is confirmed that has this location as "
"destination location"
msgstr ""
"Esta regla se puede aplicar cuando una jugada se confirma que tiene esta "
"ubicación como ubicación de destino"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "This shipment is a backorder of"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner_property_stock_customer
#: model:ir.model.fields,help:stock.field_res_users_property_stock_customer
msgid ""
"This stock location will be used, instead of the default one, as the "
"destination location for goods you send to this partner"
msgstr ""
"Se utilizará esta ubicación de existencias, en lugar de la ubicación por "
"defecto, como la ubicación de destino para enviar mercancías a esta empresa"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner_property_stock_supplier
#: model:ir.model.fields,help:stock.field_res_users_property_stock_supplier
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for goods you receive from the current partner"
msgstr ""
"Esta ubicación de existencias será utilizada, en lugar de la ubicación por "
"defecto, como la ubicación de origen para recibir mercancías desde esta "
"empresa"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product_property_stock_production
#: model:ir.model.fields,help:stock.field_product_template_property_stock_production
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated by manufacturing orders."
msgstr ""
"Se usará esta ubicación de existencias, en lugar de la de por defecto, como "
"la ubicación origen para los movimientos de existencias generados por las "
"órdenes de fabricación."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product_property_stock_inventory
#: model:ir.model.fields,help:stock.field_product_template_property_stock_inventory
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated when you do an inventory."
msgstr ""
"Se usará esta ubicación de existencias, en lugar de la de por defecto, como "
"la ubicación origen para los movimientos de existencias generados cuando se "
"realizan inventarios."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line_location_dest_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
msgid "To"
msgstr "A"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line_to_loc
msgid "To Loc"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "To Receive"
msgstr "Para recibir"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"To better organize your stock, you can create\n"
"                            subdivisions of your Warehouse called "
"<strong>Locations</strong> (ex:\n"
"                            Shipping area, Merchandise return, Shelf 34 "
"etc).\n"
"                            Do not use Locations if you do not manage "
"inventory per zone."
msgstr ""
"Para organizar mejor su acción, puede crear subdivisiones de su almacén "
"llamado <strong>ubicaciones</strong> (ex: zona de envío, retorno de "
"mercancía, estante 34 etc.).\n"
"                            No utilice lugares si no manejas inventario por "
"zona."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"To create them, click on <strong><span class=\"fa fa-refresh\"/> Reordering</"
"strong> on"
msgstr ""
"Para crearlos, haga clic en <strong> <span class='fa fa-refresh'></span> "
"Español</strong> en"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"To use more precise units like pounds or kilograms, activate<i> Some "
"products may be sold/purchased in different unit of measures (advanced)</i> "
"in the"
msgstr ""
"Para utilizar unidades más precisas como libras o kilogramos, activar<i> "
"algunos productos pueden vender/comprar en diferentes unidades de medidas "
"(avanzado)</i> en el"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Today"
msgstr "Hoy"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Today Activities"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_total_qty
msgid "Total Quantity"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category_total_route_ids
msgid "Total routes"
msgstr "Todas las rutas aplicadas"

#. module: stock
#: code:addons/stock/models/stock_production_lot.py:53
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
#, python-format
msgid "Traceability"
msgstr "Trazabilidad"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
msgid "Traceability Report"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings_module_product_expiry
msgid ""
"Track following dates on lots & serial numbers: best before, removal, end of "
"life, alert. \n"
" Such dates are set automatically at lot/serial number creation based on "
"values set on the product (in days)."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Track following dates on lots & serial numbers: best before, removal, end of "
"life, alert. Such dates are set automatically at lot/serial number creation "
"based on values set on the product (in days)."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Track product location in your warehouse"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product_tracking
#: model:ir.model.fields,field_description:stock.field_product_template_tracking
#: model:ir.model.fields,field_description:stock.field_stock_scrap_tracking
msgid "Tracking"
msgstr "Seguimiento"

#. module: stock
#: model:ir.model,name:stock.model_stock_picking
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Transfer"
msgstr "Transferir"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_picking_partner_id
msgid "Transfer Destination Address"
msgstr "Dirección de destino "

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_picking_id
msgid "Transfer Reference"
msgstr "Transferencia de referencia"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree
#: model:ir.ui.menu,name:stock.all_picking
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Transfers"
msgstr "Transferencias"

#. module: stock
#: selection:stock.location,usage:0
msgid "Transit Location"
msgstr "Ubicación de tránsito"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Transit Locations"
msgstr "Ubicación de tránsito"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_picking_code
#: model:ir.model.fields,field_description:stock.field_stock_picking_picking_type_code
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_code
msgid "Type of Operation"
msgstr "Tipo de operación"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings_module_delivery_ups
msgid "UPS"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings_module_delivery_usps
msgid "USPS"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory_line_prodlot_name
#: model:ir.model.fields,help:stock.field_stock_production_lot_name
msgid "Unique Lot/Serial Number"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_form
msgid "Unit Of Measure"
msgstr "Unidad de medida"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_price_unit
msgid "Unit Price"
msgstr "Precio unidad"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line_product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_move_product_uom
#: model:ir.model.fields,field_description:stock.field_stock_production_lot_product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap_product_uom_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking_board
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "Unit of Measure"
msgstr "Unidad de medida"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Units Of Measure"
msgstr ""

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_unit_measure_stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Units of Measure"
msgstr "Unidades de medida"

#. module: stock
#: model:ir.ui.menu,name:stock.product_uom_menu
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Units of Measures"
msgstr "Unidad de medida"

#. module: stock
#: code:addons/stock/models/stock_quant.py:311
#, python-format
msgid "Unknown Pack"
msgstr "Paquete desconocido"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"Unless you are starting a new business, you probably have a list of vendors "
"you would like to import."
msgstr ""
"A menos que usted está comenzando un nuevo negocio, es probable que tenga "
"una lista de proveedores que desea importar."

#. module: stock
#: selection:stock.warehouse,reception_steps:0
msgid "Unload in input location then go to stock (2 steps)"
msgstr ""
"Descargar en la ubicación de entrada y luego llevar a existencias (2 pasos)"

#. module: stock
#: selection:stock.warehouse,reception_steps:0
msgid ""
"Unload in input location, go through a quality control before being admitted "
"in stock (3 steps)"
msgstr ""
"Descargar en la ubicación de entrada, someter a un control de calidad antes "
"de ser admitido en las existencias (3 pasos)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Unlock"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Unpack"
msgstr "Desempaquetar"

#. module: stock
#: code:addons/stock/models/product.py:342
#, python-format
msgid "Unplanned Qty"
msgstr "Ctdad no planificada"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Unreserve"
msgstr "Anular reserva"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_uom_form_action
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_line_tree2
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "UoM"
msgstr "UdM"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_uom_categ_form_action
msgid "UoM Categories"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "Up/Down Traceability"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_view_change_product_quantity
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
msgid "Update Product Quantity"
msgstr "Actualizar la cantidad de productos"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_product_view_form_easy_inherit_stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "Update Qty On Hand"
msgstr "Actualización Cantidad En La Mano"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_traceability_report_line.xml:77
#: model:ir.actions.client,name:stock.action_stock_report
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#, python-format
msgid "Upstream Traceability"
msgstr ""

#. module: stock
#: selection:stock.move,priority:0 selection:stock.picking,priority:0
msgid "Urgent"
msgstr "Urgente"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_use_existing_lots
msgid "Use Existing Lots/Serial Numbers"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Use your own routes and putaway strategies"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type_sequence
msgid "Used to order the 'All Operations' kanban view"
msgstr "Usado para ordenar la vista kanban de 'Todas la operaciones'"

#. module: stock
#: model:res.groups,name:stock.group_stock_user
msgid "User"
msgstr "Usuario"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Validate"
msgstr "Validar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Validate Inventory"
msgstr "Validar inventario"

#. module: stock
#: selection:stock.inventory,state:0
msgid "Validated"
msgstr "Validado"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty_product_variant_count
msgid "Variant Count"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Vendor"
msgstr "Proveedor"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner_property_stock_supplier
#: model:ir.model.fields,field_description:stock.field_res_users_property_stock_supplier
#: selection:stock.location,usage:0
msgid "Vendor Location"
msgstr "Ubicación del Proveedor"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Vendor Locations"
msgstr "Ubicaciones de Proveedor"

#. module: stock
#: model:stock.location,name:stock.stock_location_suppliers
#: selection:stock.picking.type,code:0
msgid "Vendors"
msgstr "Vendedores"

#. module: stock
#: selection:stock.move,priority:0 selection:stock.picking,priority:0
msgid "Very Urgent"
msgstr "Muy urgente"

#. module: stock
#: selection:stock.location,usage:0
msgid "View"
msgstr "Vista"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_view_location_id
msgid "View Location"
msgstr "Ver ubicación"

#. module: stock
#: model:stock.location,name:stock.stock_location_locations_virtual
msgid "Virtual Locations"
msgstr "Ubicaciones virtuales"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Volume"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: selection:stock.picking,state:0
msgid "Waiting"
msgstr "En espera"

#. module: stock
#: selection:stock.move,state:0
msgid "Waiting Another Move"
msgstr "Esperando otro movimiento"

#. module: stock
#: selection:stock.picking,state:0
msgid "Waiting Another Operation"
msgstr "Esperando otra operación"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: selection:stock.move,state:0
msgid "Waiting Availability"
msgstr "Esperando disponibilidad"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Waiting Moves"
msgstr "Movimientos a la espera"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_waiting
msgid "Waiting Transfers"
msgstr "Conduces esperando disponibilidad"

#. module: stock
#: model:ir.model,name:stock.model_stock_warehouse
#: model:ir.model.fields,field_description:stock.field_product_product_warehouse_id
#: model:ir.model.fields,field_description:stock.field_product_template_warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_location_path_warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_move_warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type_warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint_warehouse_id
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_warehouse_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_partner_stock_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_tree
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Warehouse"
msgstr "Almacén"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Warehouse Configuration"
msgstr "Configuración del almacén"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_warehouse_config
msgid "Warehouse Management"
msgstr "Gestión de almacenes"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_name
msgid "Warehouse Name"
msgstr "Nombre del almacén"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_rule_propagate_warehouse_id
msgid "Warehouse to Propagate"
msgstr "Almacén a propagar"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:704
#, python-format
msgid "Warehouse's Routes"
msgstr "Rutas del almacén"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_warehouse_form
#: model:ir.model.fields,field_description:stock.field_stock_location_route_warehouse_ids
#: model:ir.ui.menu,name:stock.menu_action_warehouse_form
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Warehouses"
msgstr "Almacenes"

#. module: stock
#: code:addons/stock/models/stock_move_line.py:109
#: selection:res.partner,picking_warn:0
#, python-format
msgid "Warning"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_partner_stock_warnings_form
msgid "Warning on the Picking"
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:303
#, python-format
msgid "Warning!"
msgstr "¡Aviso!"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings_group_warning_stock
msgid "Warnings"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"We handle the whole import process\n"
"                                        for you: simply send your Odoo "
"project\n"
"                                        manager a CSV file containing all "
"your\n"
"                                        data."
msgstr ""
"Manejamos el proceso de importación todo para usted: su Gerente de proyecto "
"de Odoo envíe un archivo CSV que contiene todos los datos."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"We handle the whole import process\n"
"                                        for you: simply send your Odoo "
"project\n"
"                                        manager a CSV file containing all "
"your\n"
"                                        products."
msgstr ""
"Manejamos el proceso de importación todo para usted: su Gerente de proyecto "
"de Odoo envíe un archivo CSV que contiene todos sus productos."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "We hope this guide helped you implement Odoo Inventory."
msgstr "Esperamos que esta guía te ayudado a implementar Odoo inventario."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Welcome"
msgstr "Bienvenido"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route_push_ids
msgid ""
"When a move is foreseen to a location, the push rule will automatically "
"create a move to a next location after. This is mainly only needed when "
"creating manual operations e.g. 2/3 step manual purchase order or 2/3 step "
"finished product manual manufacturing order. In other cases, it is important "
"to use pull rules where you know where you are going based on a demand."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route_warehouse_selectable
msgid ""
"When a warehouse is selected for this route, this route should be seen as "
"the default route when products pass through this warehouse.  This behaviour "
"can be overridden by the routes on the Product/Product Categories or by the "
"Preferred Routes on the Procurement"
msgstr ""
"Cuando se selecciona un almacén para esta ruta, esta ruta puede considerarse "
"como la ruta por defecto cuando los productos pasan a través de este "
"almacén.  Este comportamiento puede cambiarse por las rutas en las "
"categorías de producto/producto o por las rutas preferidas en la consecución"

#. module: stock
#: selection:stock.picking,move_type:0
msgid "When all products are ready"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route_product_selectable
msgid ""
"When checked, the route will be selectable in the Inventory tab of the "
"Product form.  It will take priority over the Warehouse route. "
msgstr ""
"Cuando se activa, la ruta se podrá seleccionar en la pestaña Inventario de "
"la forma del producto. Se tendrá prioridad sobre la ruta Warehouse."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route_product_categ_selectable
msgid ""
"When checked, the route will be selectable on the Product Category.  It will "
"take priority over the Warehouse route. "
msgstr ""
"Cuando se activa, la ruta se podrá seleccionar en la categoría de producto. "
"Se tendrá prioridad sobre la ruta Almacen."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "When everything is set, click on <i>Start Inventory</i>"
msgstr "Cuando todo está listo, haga clic en <i> Iniciar Inventario </i>"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_putaway_fixed_location_ids
msgid ""
"When the method is fixed, this location will be used to store the products"
msgstr ""
"Cuando el método es fijo, se usará esta ubicación para almacenar los "
"productos"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_is_locked
msgid ""
"When the picking is not done this allows changing the initial demand. When "
"the picking is done this allows changing the done quantities."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint_product_min_qty
msgid ""
"When the virtual stock goes below the Min Quantity specified for this field, "
"Odoo generates a procurement to bring the forecasted quantity to the Max "
"Quantity."
msgstr ""
"Cuando las existencias virtuales estén por debajo de la cantidad mínima "
"especificada en este campo, Odoo generará un abastecimiento para llevar la "
"cantidad prevista a la cantidad máxima."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint_product_max_qty
msgid ""
"When the virtual stock goes below the Min Quantity, Odoo generates a "
"procurement to bring the forecasted quantity to the Quantity specified as "
"Max Quantity."
msgstr ""
"Cuando las existencias virtuales estén por debajo de la cantidad, Odoo "
"generará un abastecimiento para llevar la cantidad prevista a la cantidad "
"especificada como aquí como máxima."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_compute_wizard
msgid ""
"When you run the schedulers, Odoo tries to reserve the available stock to "
"fulfill the existing pickings\n"
"                and verify if some reordering rules should be triggered."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
msgid ""
"When you select a serial number (lot), the quantity is corrected with "
"respect to\n"
"                            the quantity of that serial number (lot) and not "
"to the total quantity of the product."
msgstr ""
"Cuando selecciona un nº de serie (lote), la cantidad se corrige con respecto "
"a la cantidad de ese nº de serie (lote), y no de la cantidad total del "
"producto."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_additional
msgid "Whether the move was added after the picking's confirmation"
msgstr ""

#. module: stock
#: model:product.product,name:stock.test_quant_product
#: model:product.template,name:stock.test_quant_product_product_template
msgid "Whiteboard"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line_wizard_id
msgid "Wizard"
msgstr "Asistente"

#. module: stock
#: code:addons/stock/models/stock_production_lot.py:37
#, python-format
msgid "You are not allowed to create a lot for this picking type"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move.py:622
#, python-format
msgid ""
"You are using a unit of measure smaller than the one you are using in order "
"to stock your product. This can lead to rounding problem on reserved "
"quantity! You should use the smaller unit of measure possible in order to "
"valuate your stock or change its rounding precision to a smaller value "
"(example: 0.00001)."
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_routes_form
msgid ""
"You can define here the main routes that run through\n"
"                    your warehouses and that define the flows of your "
"products. These\n"
"                    routes can be assigned to a product, a product category "
"or be fixed\n"
"                    on procurement or sales order."
msgstr ""
"Puede definir aquí las principales rutas que discurren por\n"
"                     sus almacenes y que definen los flujos de sus "
"productos. Estas\n"
"                     rutas se pueden asignar a un producto, una categoría de "
"producto o fijarse\n"
"                     en el orden de adquisición o de venta."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint_form
msgid ""
"You can define your minimum stock rules, so that Odoo will automatically "
"create draft manufacturing orders or request for quotations according to the "
"stock level. Once the virtual stock of a product (= stock on hand minus all "
"confirmed orders and reservations) is below the minimum quantity, Odoo will "
"generate a procurement request to increase the stock up to the maximum "
"quantity."
msgstr ""
"Puede definir sus reglas de valores mínimos, de manera que Odoo creará "
"automáticamente los proyectos de órdenes de fabricación o solicitud de "
"cotizaciones de acuerdo con el nivel de existencias. Una vez que la acción "
"virtual de un producto (= existencias disponibles menos todos los pedidos y "
"reservas confirmadas) está por debajo de la cantidad mínima, Odoo generará "
"una solicitud de adquisición para aumentar el capital hasta la cantidad "
"máxima."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "You can delete lines to ignore some products."
msgstr "Puede eliminar líneas para ignorar algunos productos."

#. module: stock
#: code:addons/stock/models/product.py:509
#, python-format
msgid ""
"You can not change the type of a product that is currently reserved on a "
"stock move. If you need to change the type, you should first unreserve the "
"stock move."
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:502
#, python-format
msgid ""
"You can not change the unit of measure of a product that has already been "
"used in a done stock move. If you need to change the unit of measure, you "
"may deactivate this product."
msgstr ""
"No puede cambiar la unidad de medida de un producto que ya ha sido usado en "
"un movimiento de mercancía validado. Si necesita cambiar la unidad de "
"medida, puede desactivar este producto."

#. module: stock
#: code:addons/stock/models/stock_move_line.py:337
#, python-format
msgid ""
"You can not delete product moves if the picking is done. You can only "
"correct the done quantities."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move_line.py:115
#, python-format
msgid "You can not enter negative quantities!"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_inventory.py:392
#, python-format
msgid "You can only adjust stockable products."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move.py:1041
#, python-format
msgid "You can only delete draft moves."
msgstr "Sólo puede eliminar movimientos borrador."

#. module: stock
#: code:addons/stock/models/stock_move_line.py:125
#, python-format
msgid "You can only process 1.0 %s for products with unique serial number."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "You can review and edit the predefined units via the"
msgstr "Puede revisar y editar las unidades predefinidas a través de la"

#. module: stock
#: code:addons/stock/models/stock_move.py:913
#, python-format
msgid "You cannot cancel a stock move that has been set to 'Done'."
msgstr ""
"No puede cancelar un movimiento de existencias que está como 'Realizado'."

#. module: stock
#: code:addons/stock/models/stock_scrap.py:71
#, python-format
msgid "You cannot delete a scrap which is done."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_inventory.py:380
#, python-format
msgid ""
"You cannot have two inventory adjustements in state 'in Progess' with the "
"same product(%s), same location(%s), same package, same owner and same lot. "
"Please first validatethe first inventory adjustement with this product "
"before creating another one."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_inventory.py:159
#, python-format
msgid ""
"You cannot set a negative product quantity in an inventory line:\n"
"\t%s - qty: %s"
msgstr ""
"No puede establecer una cantidad negativa de producto en una línea de "
"inventario:\n"
"%s - Ctdad.: %s"

#. module: stock
#: code:addons/stock/models/stock_move.py:1069
#, python-format
msgid "You cannot split a draft move. It needs to be confirmed first."
msgstr ""
"No puede dividir un movimiento borrador. Necesita ser confirmado primero."

#. module: stock
#: code:addons/stock/models/stock_move.py:1065
#, python-format
msgid "You cannot split a move done"
msgstr "No puede dividir un movimiento realizado"

#. module: stock
#: code:addons/stock/models/stock_move_line.py:151
#: code:addons/stock/models/stock_move_line.py:157
#, python-format
msgid ""
"You cannot use the same serial number twice. Please correct the serial "
"numbers encoded."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_picking.py:643
#, python-format
msgid "You cannot validate a transfer if you have not processed any quantity."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid ""
"You do not have any products reserved for this picking.  Please click the "
"'Reserve' button\n"
"                                to check if products are available."
msgstr ""
"No tienes artículos reservados para este conduce. Por favor, haga clic en el "
"botón 'Reserva'\n"
"                                 para comprobar si los productos están "
"disponibles."

#. module: stock
#: code:addons/stock/models/stock_move_line.py:131
#, python-format
msgid ""
"You have already assigned this serial number to this product. Please correct "
"the serial numbers encoded."
msgstr ""

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:106
#, python-format
msgid "You have manually created product lines, please delete them to proceed"
msgstr ""
"Ha creado manualmente líneas de producto. Bórrelas por favor para proceder."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "You have processed less products than the initial demand."
msgstr "Usted ha procesado menos productos que la demanda inicial."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_overprocessed_transfer
msgid ""
"You have processed more than what was initially\n"
"                    planned for the product"
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:304
#, python-format
msgid ""
"You have products in stock that have no lot number.  You can assign serial "
"numbers by doing an inventory.  "
msgstr ""
"Usted tiene productos en stock que no tienen número de lote. Puede asignar "
"números de serie haciendo un inventario."

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:788
#, python-format
msgid ""
"You have to select a product unit of measure in the same category than the "
"default unit of measure of the product"
msgstr ""
"Tiene que seleccionar una unidad de medida del producto de la misma "
"categoría que la unidad de medida por defecto del producto."

#. module: stock
#: code:addons/stock/models/stock_location.py:91
#, python-format
msgid "You have to set a name for this location."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
msgid ""
"You haven't entered <i>done</i> quantities, by clicking on <i>apply</i>\n"
"                        Odoo will process all the <i>reserved</i> quantities."
msgstr ""

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:44
#, python-format
msgid "You may only return Done pickings"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_inventory.py:22
#, python-format
msgid "You must define a warehouse for the company: %s."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move_line.py:386
#: code:addons/stock/models/stock_picking.py:656
#, python-format
msgid "You need to supply a lot/serial number for %s."
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:369
#, python-format
msgid ""
"You still have some active reordering rules on this product. Please archive "
"or delete them first."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move.py:318
#, python-format
msgid ""
"You try to move a product using a UoM that is not compatible with the UoM of "
"the product moved. Please use an UoM in the same UoM category."
msgstr ""
"Está tratando de mover un producto usando una UdM que no es compatible con "
"la UdM del producto movido. Utilice una UdM de la misma categoría."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Your Products"
msgstr "Tus productos"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Your Situation"
msgstr "Situación"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Your Vendors"
msgstr "Sus Proveedores"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "Your Warehouse"
msgstr "Almacén"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
msgid "_Apply"
msgstr "_Aplicar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
msgid "_Cancel"
msgstr "_Cancelar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "a stockable Product"
msgstr "Productos almacenables"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "and simply enter a minimum and maximum quantity."
msgstr "y simplemente entrar en un mínimo y cantidad máxima."

#. module: stock
#: model:ir.model,name:stock.model_barcode_rule
msgid "barcode.rule"
msgstr "barcode.rule"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings_module_delivery_bpost
msgid "bpost"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "configuration menu"
msgstr "Configuración del menú"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_path_form
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_rule_form
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "days"
msgstr "días"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "days to be propagated"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "e.g. Annual inventory"
msgstr "Por ejemplo, Inventario anual"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_rule_form
msgid "e.g. Buy"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "e.g. LOT/0001/20121"
msgstr "e.g. LOT/0001/20121"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "e.g. PO0032"
msgstr "Por ejemplo, PO0032"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"either by manually updating the Done quantity on the product lines, or let "
"Odoo do it automatically while validating"
msgstr ""
"ya sea mediante la actualización de forma manual la cantidad Hecho en las "
"líneas de productos, o dejar Odoo lo hace automáticamente al validar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"either by manually updating the Done quantity on the product lines, or scan "
"them with the Odoo Barcode app, or let Odoo do it automatically while "
"validating"
msgstr ""
"ya sea mediante la actualización de forma manual la cantidad Hecho en las "
"líneas de productos, o escanearlos con la aplicación Odoo código de barras, "
"o dejar Odoo lo hace automáticamente al validar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "for a customer and add products"
msgstr "para un cliente y añadir productos"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"for more\n"
"                        information."
msgstr ""
"para more\n"
"información."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "from your vendor with the products and the requested quantities"
msgstr "de su proveedor con los productos y las cantidades solicitadas"

#. module: stock
#: model:stock.inventory.line,product_name:stock.stock_inventory_line_3
msgid "iPad Mini"
msgstr ""

#. module: stock
#: model:stock.inventory.line,product_name:stock.stock_inventory_line_6
msgid "iPod"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid "in"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"is displayed on the transfer if your products supply chain is properly "
"configured. Otherwise, <strong>Check the availability</strong> manually"
msgstr ""
"se visualiza en la transferencia si su cadena de productos de alimentación "
"está correctamente configurado. De lo contrario, <strong> Comprobar la "
"disponibilidad </strong> manualmente"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid "is not available in sufficient quantity"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid ""
"on the purchase order form or click on <i>Receive Products</i> to see the "
"Transfer Order"
msgstr ""
"en el formulario de orden de compra o haga clic en <i> Recibir Productos </"
"i> para ver el orden de transferencia"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "on the sales order form to see Transfer Order"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_report_stock_forecast
msgid "report.stock.forecast"
msgstr "report.stock.forecast"

#. module: stock
#: model:ir.model,name:stock.model_res_config_settings
msgid "res.config.settings"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_fixed_putaway_strat
msgid "stock.fixed.putaway.strat"
msgstr "stock.fixed.putaway.strat"

#. module: stock
#: model:ir.model,name:stock.model_stock_overprocessed_transfer
msgid "stock.overprocessed.transfer"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_return_picking_line
msgid "stock.return.picking.line"
msgstr "stock.return.picking.line"

#. module: stock
#: model:ir.model,name:stock.model_stock_scrap
msgid "stock.scrap"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_traceability_report
msgid "stock.traceability.report"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_warn_insufficient_qty
msgid "stock.warn.insufficient.qty"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_warn_insufficient_qty_scrap
msgid "stock.warn.insufficient.qty.scrap"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "the list of products"
msgstr "la lista de productos"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "the list of vendors"
msgstr "la lista de proveedores"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "to mark the products as transferred to your stock location"
msgstr "para marcar los productos como trasladado a su ubicación social"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "via the"
msgstr "a través de la"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "when you receive the ordered products"
msgstr "cuando usted recibe los productos solicitados"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_planner
msgid "with the <i>Validate</i> button"
msgstr "Con el boton <i>Validar</i>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "↳Put in Pack"
msgstr "Poner en un paquete"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "⇒ Set quantities to 0"
msgstr "⇒ Establecer cantidad a 0"
