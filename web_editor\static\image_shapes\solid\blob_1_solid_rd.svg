<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="800" height="600">
    <defs>
        <clipPath id="clip-path" clipPathUnits="objectBoundingBox">
            <use xlink:href="#filterPath" fill="none"></use>
        </clipPath>
        <path id="filterPath"
            d="M0.0483,0.4143l0.0127-0.1106a0.2729,0.3029,0,0,1,0.2416-0.2628l0.3291-0.0392A0.2729,0.3029,0,0,1,0.8793,0.1215l0.0538,0.0799a0.2728,0.3028,0,0,1-0.0043,0.3689L0.8342,0.7036a0.2684,0.2979,0,0,1-0.0384,0.044l-0.1937,0.1812a0.2729,0.3029,0,0,1-0.3217,0.0243l-0.1409-0.0989A0.2728,0.3028,0,0,1,0.0313,0.4871h0A0.2736,0.3036,0,0,0,0.0483,0.4143Z">
        </path>
    </defs><svg viewBox="82.87645721435547 90.12640380859375 163.90713500976562 122.14910888671875"
        preserveAspectRatio="none">
        <path class="background"
            d="M231.6,190.43C310.4,139.49,55.33,26.54,85.78,136.85c10.87,39.4-8.86,48.87-1,58.6C108.75,225.24,200.87,210.29,231.6,190.43Z"
            fill="#7C6576"></path>
    </svg><svg viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
        <use xlink:href="#filterPath" fill="darkgrey"></use>
    </svg>
    <image xlink:href="" clip-path="url(#clip-path)"></image>
</svg>
