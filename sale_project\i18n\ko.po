# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_project
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON>, 2023
# Sarah <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:54+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: Sarah <PERSON>, 2023\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid "Create Invoice"
msgstr "청구서 발행"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__service_tracking
#: model:ir.model.fields,field_description:sale_project.field_product_template__service_tracking
msgid "Create on Order"
msgstr "주문서 생성"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__visible_project
msgid "Display project"
msgstr "프로젝트 표시"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__project_id
msgid "Generated Project"
msgstr "생성된 프로젝트"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__task_id
msgid "Generated Task"
msgstr "생성된 작업"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__has_any_so_to_invoice
msgid "Has SO to Invoice"
msgstr "청구서를 발행할 판매주문서가 있습니다"

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice as soon as this service is sold, and create a task in an existing "
"project to track the time spent."
msgstr ""

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice ordered quantities as soon as this service is sold, and create a "
"project for the order with a task for each sales order line to track the "
"time spent."
msgstr ""

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice ordered quantities as soon as this service is sold, and create an "
"empty project for the order to track the time spent."
msgstr ""

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid "Invoice ordered quantities as soon as this service is sold."
msgstr "이 서비스 판매 후 즉시 주문 수량에 대한 청구서를 발행하십시오."

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__is_service
msgid "Is a Service"
msgstr "서비스 여부"

#. module: sale_project
#: code:addons/sale_project/models/sale_order.py:0
#, python-format
msgid "New"
msgstr "신규"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.portal_tasks_list_inherit
msgid "None"
msgstr "없음"

#. module: sale_project
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__no
msgid "Nothing"
msgstr "아무 것도 없음"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_count
msgid "Number of Projects"
msgstr "프로젝트 수"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__invoice_count
msgid "Number of invoices"
msgstr "청구서 수"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_product_product__service_tracking
#: model:ir.model.fields,help:sale_project.field_product_template__service_tracking
msgid ""
"On Sales order confirmation, this product can generate a project and/or task.         From those, you can track the service you are selling.\n"
"         'In sale order's project': Will use the sale order's configured project if defined or fallback to         creating a new project based on the selected template."
msgstr ""
"판매 오더 확인시 이 제품은 프로젝트 및 작업을 생성할 수 있습니다. 이를 통해 판매중인 서비스를 추적 할 수 있습니다. \n"
"         '판매 주문 프로젝트' : 정의된 경우 판매 주문의 구성된 프로젝트를 사용하거나 선택한 서식을 기반으로 새 프로젝트를 만드는 데 대체합니다."

#. module: sale_project
#: model:ir.model,name:sale_project.model_product_product
msgid "Product"
msgstr "품목"

#. module: sale_project
#: model:ir.model,name:sale_project.model_product_template
msgid "Product Template"
msgstr "품목 양식"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__project_id
#: model:ir.model.fields,field_description:sale_project.field_product_template__project_id
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_id
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__project_only
msgid "Project"
msgstr "프로젝트"

#. module: sale_project
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__task_in_project
msgid "Project & Task"
msgstr "프로젝트 및 작업"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__project_template_id
#: model:ir.model.fields,field_description:sale_project.field_product_template__project_template_id
msgid "Project Template"
msgstr "프로젝트 서식"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order_line__project_id
msgid "Project generated by the sales order item"
msgstr "판매 주문 항목에 의해 생성된 프로젝트"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__project_sale_order_id
msgid "Project's sale order"
msgstr "프로젝트 판매 주문서"

#. module: sale_project
#: code:addons/sale_project/models/sale_order.py:0
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_ids
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
#, python-format
msgid "Projects"
msgstr "프로젝트"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order__project_ids
msgid "Projects used in this sales order."
msgstr "이 판매 주문에 사용된 프로젝트."

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_search
#: model_terms:ir.ui.view,arch_db:sale_project.project_task_view_search
msgid "Sale Order"
msgstr "판매 주문"

#. module: sale_project
#: code:addons/sale_project/controllers/portal.py:0
#: code:addons/sale_project/models/project.py:0
#: model:ir.model,name:sale_project.model_sale_order
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_order_id
#: model:ir.model.fields,field_description:sale_project.field_project_task__sale_order_id
#: model:ir.model.fields,field_description:sale_project.field_report_project_task_user__sale_order_id
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
#, python-format
msgid "Sales Order"
msgstr "판매 주문"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_search
msgid "Sales Order Id"
msgstr ""

#. module: sale_project
#: code:addons/sale_project/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_line_id
#: model:ir.model.fields,field_description:sale_project.field_project_task__sale_line_id
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.project_task_view_search
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
#, python-format
msgid "Sales Order Item"
msgstr "판매 주문 항목"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_task__sale_line_id
msgid ""
"Sales Order Item to which the time spent on this task will be added, in "
"order to be invoiced to your customer."
msgstr ""

#. module: sale_project
#: model:ir.model,name:sale_project.model_sale_order_line
msgid "Sales Order Line"
msgstr "판매 주문 내역"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order_line__is_service
msgid ""
"Sales Order item should generate a task and/or a project, depending on the "
"product settings."
msgstr "판매 주문 항목은 제품 설정에 따라 작업 및 프로젝트를 생성해야 합니다."

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_project__sale_line_id
msgid ""
"Sales order item to which the project is linked. Link the timesheet entry to"
" the sales order item defined on the project. Only applies on tasks without "
"sale order item defined, and if the employee is not in the 'Employee/Sales "
"Order Item Mapping' of the project."
msgstr ""

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_project__sale_order_id
#: model:ir.model.fields,help:sale_project.field_project_task__project_sale_order_id
msgid "Sales order to which the project is linked."
msgstr "프로젝트가 연결된 판매 주문."

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_task__sale_order_id
msgid "Sales order to which the task is linked."
msgstr "작업이 연결된 판매 주문."

#. module: sale_project
#: code:addons/sale_project/controllers/portal.py:0
#, python-format
msgid "Search in Invoice"
msgstr "청구서 검색"

#. module: sale_project
#: code:addons/sale_project/controllers/portal.py:0
#, python-format
msgid "Search in Sales Order"
msgstr "판매 주문 검색"

#. module: sale_project
#: code:addons/sale_project/controllers/portal.py:0
#, python-format
msgid "Search in Sales Order Item"
msgstr "판매 주문 항목 검색"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_product_product__project_id
#: model:ir.model.fields,help:sale_project.field_product_template__project_id
msgid ""
"Select a billable project on which tasks can be created. This setting must "
"be set for each company."
msgstr ""

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_product_product__project_template_id
#: model:ir.model.fields,help:sale_project.field_product_template__project_template_id
msgid ""
"Select a billable project to be the skeleton of the new created project when"
" selling the current product. Its stages and tasks will be duplicated."
msgstr ""

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order__project_id
msgid "Select a non billable project on which tasks can be created."
msgstr "작업을 만들 수 있는 청구 불가능한 프로젝트를 선택하십시오."

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_task
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__task_global_project
msgid "Task"
msgstr "작업"

#. module: sale_project
#: code:addons/sale_project/models/sale_order.py:0
#, python-format
msgid ""
"Task Created (%s): <a href=# data-oe-model=project.task data-oe-id=%d>%s</a>"
msgstr ""
"작업 생성됨 (%s) : <a href=# data-oe-model=project.task data-oe-id=%d>%s</a>"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "반복되는 작업"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order_line__task_id
msgid "Task generated by the sales order item"
msgstr "판매 주문 항목에 의해 생성된 작업"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__tasks_count
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
msgid "Tasks"
msgstr "작업"

#. module: sale_project
#: model:ir.model,name:sale_project.model_report_project_task_user
msgid "Tasks Analysis"
msgstr "작업 분석"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__tasks_ids
msgid "Tasks associated to this sale"
msgstr "이 판매와 관련된 작업"

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"The product %s should not have a global project since it will generate a "
"project."
msgstr "%s 상품은 프로젝트를 생성하므로 전역 프로젝트가 있어서는 안 됩니다."

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"The product %s should not have a project nor a project template since it "
"will not generate project."
msgstr "%s 상품은 프로젝트를 생성하지 않으므로 프로젝트나 프로젝트 서식을 가지고 있지 않아야 합니다."

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"The product %s should not have a project template since it will generate a "
"task in a global project."
msgstr "%s 상품은 전역 프로젝트에서 작업을 생성하므로 프로젝트 서식이 없어야 합니다."

#. module: sale_project
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "There is nothing to invoice in this project."
msgstr ""

#. module: sale_project
#: code:addons/sale_project/models/sale_order.py:0
#, python-format
msgid ""
"This task has been created from: <a href=# data-oe-model=sale.order data-oe-"
"id=%d>%s</a> (%s)"
msgstr ""
"이 작업은 다음에서 생성되었습니다 : <a href=# data-oe-model=sale.order data-oe-id=%d>%s</a>"
" (%s)"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__task_to_invoice
msgid "To invoice"
msgstr "청구서 발행 대기"

#. module: sale_project
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid ""
"You cannot link the order item %(order_id)s - %(product_id)s to this task "
"because it is a re-invoiced expense."
msgstr ""
"재청구된 비용이므로 이 작업을 위해 %(order_id)s - %(product_id)s 아이템의 주문을 연결할 수 없습니다. "

#. module: sale_project
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid ""
"You have to unlink the task from the sale order item in order to delete it."
msgstr "작업을 삭제하려면 판매 주문 항목에서 작업을 연결 해제해야 합니다."

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.portal_tasks_list_inherit
msgid "for sale order item:"
msgstr ""

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.portal_tasks_list_inherit
msgid "for sale order:"
msgstr ""
