<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="ir_actions_server_action_open_presence_view" model="ir.actions.server">
        <field name="name">Compute presence and open presence view</field>
        <field name="model_id" ref="hr.model_hr_employee" />
        <field name="state">code</field>
        <field name="code">
action = env['hr.employee']._action_open_presence_view()
        </field>
    </record>

    <menuitem id="menu_hr_presence_view" name="Employee Presence" action="ir_actions_server_action_open_presence_view" parent="hr.hr_menu_hr_reports" groups="hr.group_hr_manager"/>

</odoo>
