# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_paypal
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>, 2021
# Lucia <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-31 17:26+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: Lucia Pac<PERSON>co, 2023\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.mail_template_paypal_invite_user_to_configure
msgid ""
"<br/><br/>\n"
"                Thanks,<br/>\n"
"                <b>The Odoo Team</b>"
msgstr ""
"<br/><br/>\n"
"                Gracias,<br/>\n"
"                <b>El equipo de Odoo</b>"

#. module: payment_paypal
#: code:addons/payment_paypal/models/payment_acquirer.py:0
#, python-format
msgid "Add your PayPal account to Odoo"
msgstr "Agregue su cuenta de Paypal a Odoo"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__paypal_email_account
msgid "Email"
msgstr "Correo electrónico"

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.mail_template_paypal_invite_user_to_configure
msgid ""
"Hello,\n"
"                <br/><br/>\n"
"                You have received a payment through PayPal.<br/>\n"
"                Kindly follow the instructions given by PayPal to create your account.<br/>\n"
"                Then, help us complete your Paypal credentials in Odoo.<br/><br/>"
msgstr ""
"Hola,\n"
"                <br/><br/>\n"
"                Recibió un pago a través de PayPal.<br/>\n"
"                Siga las instrucciones dadas por PayPal para crear su cuenta.<br/>\n"
"                Cuando termine, ayúdenos a completar sus credenciales de Paypal en Odoo.<br/><br/>"

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.payment_acquirer_form
msgid "How to configure your paypal account?"
msgstr "¿Cómo configurar su cuenta de Paypal?"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__paypal_seller_account
msgid "Merchant Account ID"
msgstr "ID de la cuenta del comerciante"

#. module: payment_paypal
#: code:addons/payment_paypal/models/payment_transaction.py:0
#, python-format
msgid "Missing value for txn_id (%(txn_id)s) or txn_type (%(txn_type)s)."
msgstr "Valor que falta para txn_id (%(txn_id)s) o txn_type (%(txn_type)s)."

#. module: payment_paypal
#: code:addons/payment_paypal/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "No se encontró ninguna transacción que coincida con la referencia %s."

#. module: payment_paypal
#: code:addons/payment_paypal/controllers/main.py:0
#, python-format
msgid "Notification data were not acknowledged."
msgstr "No se reconocieron los datos de las notificaciones."

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__paypal_pdt_token
msgid "PDT Identity Token"
msgstr "Token de identidad PDT"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_transaction__paypal_type
msgid "PayPal Transaction Type"
msgstr "Tipo de transacción de PayPal"

#. module: payment_paypal
#: model:ir.model,name:payment_paypal.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Método de pago"

#. module: payment_paypal
#: model:ir.model,name:payment_paypal.model_account_payment_method
msgid "Payment Methods"
msgstr "Métodos de pago"

#. module: payment_paypal
#: model:ir.model,name:payment_paypal.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transacción de pago"

#. module: payment_paypal
#: model:account.payment.method,name:payment_paypal.payment_method_paypal
#: model:ir.model.fields.selection,name:payment_paypal.selection__payment_acquirer__provider__paypal
msgid "Paypal"
msgstr "Paypal"

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_acquirer__paypal_use_ipn
msgid "Paypal Instant Payment Notification"
msgstr "Notificación de pago instantáneo de Paypal"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__provider
msgid "Provider"
msgstr "Proveedor"

#. module: payment_paypal
#: code:addons/payment_paypal/models/payment_transaction.py:0
#, python-format
msgid "Received data with invalid payment status: %s"
msgstr "Información recibida con estado de pago no válido: %s"

#. module: payment_paypal
#: code:addons/payment_paypal/controllers/main.py:0
#, python-format
msgid ""
"Received unrecognized authentication check response code: received %s, "
"expected VERIFIED or INVALID."
msgstr ""
"Código de respuesta de autenticación no reconocido recibido: recibido %s, "
"esperado VERIFIED o INVALID."

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.mail_template_paypal_invite_user_to_configure
msgid "Set Paypal credentials"
msgstr "Establecer credenciales de Paypal"

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_acquirer__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr ""
"El proveedor de servicios de pago que se utilizará con este método de pago"

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_acquirer__paypal_email_account
msgid ""
"The public business email solely used to identify the account with PayPal"
msgstr ""
"El correo electrónico público de la empresa que se utiliza exclusivamente "
"para identificar la cuenta con PayPal"

#. module: payment_paypal
#: code:addons/payment_paypal/controllers/main.py:0
#, python-format
msgid ""
"The status of transaction with reference %(ref)s was not synchronized "
"because the 'Payment data transfer' option is not enabled on the PayPal "
"dashboard."
msgstr ""
"No se puso sincronizar el estado de la transacción con referencia %(ref)s "
"porque la opción 'transferencia de datos de pago' no está habilitada en el "
"tablero de PayPal."

#. module: payment_paypal
#: code:addons/payment_paypal/controllers/main.py:0
#, python-format
msgid ""
"The status of transaction with reference %(ref)s was not synchronized "
"because the PDT Identify Token is not configured on the acquirer "
"%(acq_link)s."
msgstr ""
"No se pudo sincronizar el estado de la transacción con referencia %(ref)s "
"porque la identidad del Token PDT no está configurada en el método de pago "
"%(acq_link)s."

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_transaction__paypal_type
msgid "This has no use in Odoo except for debugging."
msgstr ""
"Esto no tiene ninguna utilidad en Odoo, excepto para la solución de bugs."

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__paypal_use_ipn
msgid "Use IPN"
msgstr "Habilitar IPN"
