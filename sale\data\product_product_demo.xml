<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="product.consu_delivery_01" model="product.product">
            <field name="invoice_policy">order</field>
        </record>

        <record id="product.consu_delivery_02" model="product.product">
            <field name="invoice_policy">delivery</field>
        </record>

        <record id="product.consu_delivery_03" model="product.product">
            <field name="invoice_policy">delivery</field>
        </record>

        <record id="product.product_delivery_01" model="product.product">
            <field name="invoice_policy">delivery</field>
        </record>

        <record id="product.product_delivery_02" model="product.product">
            <field name="invoice_policy">delivery</field>
        </record>

        <record id="product.product_order_01" model="product.product">
            <field name="invoice_policy">order</field>
        </record>

        <record id="product.product_delivery_02" model="product.product">
            <field name="invoice_policy">delivery</field>
        </record>

        <record id="product.product_delivery_01" model="product.product">
            <field name="invoice_policy">delivery</field>
        </record>

        <record id="product.product_delivery_02" model="product.product">
            <field name="invoice_policy">delivery</field>
        </record>

        <record id="product.product_product_27" model="product.product">
            <field name="invoice_policy">delivery</field>
        </record>

        <record id="product.product_product_25" model="product.product">
            <field name="invoice_policy">delivery</field>
        </record>

        <record id="product.product_product_24" model="product.product">
            <field name="invoice_policy">order</field>
        </record>

        <record id="product.product_product_22" model="product.product">
            <field name="invoice_policy">delivery</field>
        </record>

        <record id="product.product_product_20" model="product.product">
            <field name="invoice_policy">delivery</field>
        </record>

        <record id="product.product_product_16" model="product.product">
            <field name="invoice_policy">order</field>
        </record>

        <record id="product.product_product_13" model="product.product">
            <field name="invoice_policy">delivery</field>
        </record>

        <record id="product.product_product_12" model="product.product">
            <field name="invoice_policy">delivery</field>
        </record>

        <record id="product.product_product_11b" model="product.product">
            <field name="invoice_policy">delivery</field>
        </record>

        <record id="product.product_product_11" model="product.product">
            <field name="invoice_policy">delivery</field>
        </record>

        <record id="product.product_product_10" model="product.product">
            <field name="invoice_policy">delivery</field>
        </record>

        <record id="product.product_product_9" model="product.product">
            <field name="invoice_policy">delivery</field>
        </record>

        <record id="product.product_product_8" model="product.product">
            <field name="invoice_policy">delivery</field>
        </record>

        <record id="product.product_product_7" model="product.product">
            <field name="invoice_policy">delivery</field>
        </record>

        <record id="product.product_product_6" model="product.product">
            <field name="invoice_policy">delivery</field>
        </record>

        <record id="product.product_product_5" model="product.product">
            <field name="invoice_policy">delivery</field>
        </record>

        <record id="product.product_product_4c" model="product.product">
            <field name="invoice_policy">delivery</field>
        </record>

        <record id="product.product_product_4b" model="product.product">
            <field name="invoice_policy">delivery</field>
        </record>

        <record id="product.product_product_4" model="product.product">
            <field name="invoice_policy">delivery</field>
        </record>

        <record id="product.product_product_3" model="product.product">
            <field name="invoice_policy">delivery</field>
            <field name="expense_policy">cost</field>
        </record>

        <record id="product.product_product_2" model="product.product">
            <field name="invoice_policy">delivery</field>
        </record>

        <record id="product.product_product_1" model="product.product">
            <field name="invoice_policy">delivery</field>
        </record>

        <!-- Expensable products -->
        <record id="product.expense_product" model="product.product">
            <field name="invoice_policy">order</field>
            <field name="expense_policy">sales_price</field>
        </record>

        <record id="product.expense_hotel" model="product.product">
            <field name="invoice_policy">delivery</field>
            <field name="expense_policy">cost</field>
        </record>

    </data>
</odoo>
