<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="hr_plan_view_search" model="ir.ui.view">
            <field name="name">hr.plan.view.search</field>
            <field name="model">hr.plan</field>
            <field name="arch" type="xml">
                <search string="Plan">
                    <field name="name"/>
                    <separator/>
                    <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                </search>
            </field>
        </record>

        <record id="hr_plan_view_tree" model="ir.ui.view">
            <field name="name">hr.plan.view.tree</field>
            <field name="model">hr.plan</field>
            <field name="arch" type="xml">
                <tree string="Planning">
                    <field name="name"/>
                </tree>
            </field>
        </record>

        <record id="hr_plan_view_form" model="ir.ui.view">
            <field name="name">hr.plan.view.form</field>
            <field name="model">hr.plan</field>
            <field name="arch" type="xml">
                <form string="Planning">
                    <sheet>
                        <widget name="web_ribbon" title="Archived" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                        <div class="oe_title">
                            <label for="name" string="Plan Name"/>
                            <h1>
                                <field name="name" placeholder="e.g. Onboarding"/>
                            </h1>
                        </div>
                        <group string="Activities">
                            <field name="active" invisible="1"/>
                            <field name="plan_activity_type_ids" nolabel="1">
                                <tree editable="bottom">
                                    <field name="activity_type_id"/>
                                    <field name="summary"/>
                                    <field name="responsible"/>
                                    <field name="responsible_id" attrs="{'readonly': [('responsible', '!=', 'other')]}"/>
                                </tree>
                            </field>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="hr_plan_activity_type_view_tree" model="ir.ui.view">
            <field name="name">hr.plan.activity.type.view.tree</field>
            <field name="model">hr.plan.activity.type</field>
            <field name="arch" type="xml">
                <tree string="Activities">
                    <field name="activity_type_id"/>
                    <field name="summary"/>
                    <field name="responsible"/>
                </tree>
            </field>
        </record>

        <record id="hr_plan_activity_type_view_form" model="ir.ui.view">
            <field name="name">hr.plan.activity.type.view.form</field>
            <field name="model">hr.plan.activity.type</field>
            <field name="arch" type="xml">
                <form string="Activity">
                    <sheet>
                        <group>
                            <field name="activity_type_id"/>
                            <field name="summary"/>
                            <field name="responsible"/>
                            <field name="responsible_id" attrs="{'invisible': [('responsible', '!=', 'other')]}"/>
                            <field name="note"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="hr_plan_action" model="ir.actions.act_window">
            <field name="name">Planning</field>
            <field name="res_model">hr.plan</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="hr_plan_view_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Add a new plan
                </p>
            </field>
        </record>

        <record id="hr_plan_activity_type_action" model="ir.actions.act_window">
            <field name="name">Planning Types</field>
            <field name="res_model">hr.plan.activity.type</field>
            <field name="view_mode">tree,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Add a new planning activity
                </p>
            </field>
        </record>

    </data>
</odoo>
