# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_work_entry_contract
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <arn<PERSON>@allegro.lv>, 2022
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: ievaputnina <<EMAIL>>, 2023\n"
"Language-Team: Latvian (https://app.transifex.com/odoo/teams/41243/lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : 2);\n"

#. module: hr_work_entry_contract
#: code:addons/hr_work_entry_contract/models/hr_work_entry.py:0
#, python-format
msgid "%s does not have a contract from %s to %s."
msgstr ""

#. module: hr_work_entry_contract
#: code:addons/hr_work_entry_contract/models/hr_work_entry.py:0
#, python-format
msgid ""
"%s has multiple contracts from %s to %s. A work entry cannot overlap "
"multiple contracts."
msgstr ""

#. module: hr_work_entry_contract
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_regeneration_wizard
msgid ""
"<i class=\"fa fa-exclamation-triangle mr-1\" title=\"Warning\"/>You are not "
"allowed to regenerate validated work entries"
msgstr ""

#. module: hr_work_entry_contract
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_regeneration_wizard
msgid "<i class=\"fa fa-info-circle mr-1\" title=\"Hint\"/>"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model.fields,help:hr_work_entry_contract.field_hr_work_entry_type__is_leave
msgid "Allow the work entry type to be linked with time off types."
msgstr ""

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_compensatory
msgid "Compensatory Time Off"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry__contract_id
msgid "Contract"
msgstr "Līgums"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__create_uid
msgid "Created by"
msgstr "Izveidoja"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__create_date
msgid "Created on"
msgstr "Izveidots"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__display_name
msgid "Display Name"
msgstr "Attēlotais nosaukums"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__earliest_available_date_message
msgid "Earliest Available Date Message"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__earliest_available_date
msgid "Earliest date"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model,name:hr_work_entry_contract.model_hr_employee
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry__employee_id
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__employee_id
msgid "Employee"
msgstr "Darbinieks"

#. module: hr_work_entry_contract
#: model:ir.model,name:hr_work_entry_contract.model_hr_contract
msgid "Employee Contract"
msgstr "Darbinieka Darba Līgums"

#. module: hr_work_entry_contract
#. openerp-web
#: code:addons/hr_work_entry_contract/static/src/js/work_entries_calendar.js:0
#, python-format
msgid "Everybody's work entries"
msgstr ""

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_extra_hours
msgid "Extra Hours"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__date_from
msgid "From"
msgstr "No"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_contract__date_generated_from
msgid "Generated From"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_contract__date_generated_to
msgid "Generated To"
msgstr ""

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_leave
msgid "Generic Time Off"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model,name:hr_work_entry_contract.model_hr_work_entry
msgid "HR Work Entry"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model,name:hr_work_entry_contract.model_hr_work_entry_type
msgid "HR Work Entry Type"
msgstr ""

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_home_working
msgid "Home Working"
msgstr "Darbs no mājām"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__id
msgid "ID"
msgstr "ID"

#. module: hr_work_entry_contract
#: code:addons/hr_work_entry_contract/wizard/hr_work_entry_regeneration_wizard.py:0
#, python-format
msgid ""
"In order to regenerate the work entries, you need to provide the wizard with"
" an employee_id, a date_from and a date_to. In addition to that, the time "
"interval defined by date_from and date_to must not contain any validated "
"work entries."
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard____last_update
msgid "Last Modified on"
msgstr "Pēdējoreiz modificēts"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__write_uid
msgid "Last Updated by"
msgstr "Pēdējoreiz atjaunoja"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__write_date
msgid "Last Updated on"
msgstr "Pēdējoreiz atjaunots"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__latest_available_date_message
msgid "Latest Available Date Message"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__latest_available_date
msgid "Latest date"
msgstr ""

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_legal_leave
msgid "Legal Leaves 2022"
msgstr ""

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_long_leave
msgid "Long Term Time Off"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model,name:hr_work_entry_contract.model_hr_work_entry_regeneration_wizard
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_regeneration_wizard
msgid "Regenerate Employee Work Entries"
msgstr ""

#. module: hr_work_entry_contract
#. openerp-web
#: code:addons/hr_work_entry_contract/static/src/js/work_entries_controller_mixin.js:0
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_regeneration_wizard
#, python-format
msgid "Regenerate Work Entries"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__search_criteria_completed
msgid "Search Criteria Completed"
msgstr ""

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_sick_leave
msgid "Sick Time Off"
msgstr ""

#. module: hr_work_entry_contract
#: code:addons/hr_work_entry_contract/wizard/hr_work_entry_regeneration_wizard.py:0
#, python-format
msgid ""
"The from date must be >= '%(earliest_available_date)s' and the to date must "
"be <= '%(latest_available_date)s', which correspond to the generated work "
"entries time interval."
msgstr ""

#. module: hr_work_entry_contract
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_contract_view_form_inherit
msgid "This work entry cannot be validated. The work entry type is undefined."
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_type__is_leave
msgid "Time Off"
msgstr "Atvaļinājumi"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__date_to
msgid "To"
msgstr "Kam"

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_unpaid_leave
msgid "Unpaid"
msgstr "Neapmaksāts"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__valid
msgid "Valid"
msgstr "Derīgs"

#. module: hr_work_entry_contract
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_regeneration_wizard
msgid "Work Entries"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__validated_work_entry_ids
msgid "Work Entries Within Interval"
msgstr ""

#. module: hr_work_entry_contract
#: model:ir.actions.act_window,name:hr_work_entry_contract.hr_work_entry_regeneration_wizard_action
msgid "Work Entry Regeneration"
msgstr ""
