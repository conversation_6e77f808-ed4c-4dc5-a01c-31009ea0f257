# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* repair
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <brencic<PERSON><EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:18+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Slovak (https://app.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: repair
#: model:ir.actions.report,print_report_name:repair.action_report_repair_order
msgid ""
"(\n"
"                object.state == 'draft' and 'Repair Quotation - %s' % (object.name) or\n"
"                'Repair Order - %s' % (object.name))"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "(<i>Remove</i>)"
msgstr "(<i>Odstrániť</i>)"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "(update)"
msgstr "(aktualizácia)"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__state
msgid ""
"* The 'Draft' status is used when a user is encoding a new and unconfirmed repair order.\n"
"* The 'Confirmed' status is used when a user confirms the repair order.\n"
"* The 'Ready to Repair' status is used to start to repairing, user can start repairing only after repair order is confirmed.\n"
"* The 'Under Repair' status is used when the repair is ongoing.\n"
"* The 'To be Invoiced' status is used to generate the invoice before or after repairing done.\n"
"* The 'Done' status is set when repairing is completed.\n"
"* The 'Cancelled' status is used when user cancel repair order."
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid ": Insufficient Quantity To Repair"
msgstr ""

#. module: repair
#: model:mail.template,body_html:repair.mail_template_repair_quotation
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px;font-size: 13px;\">\n"
"        Hello <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br/>\n"
"        Here is your repair order <strong t-out=\"object.name or ''\">RO/00004</strong>\n"
"        <t t-if=\"object.invoice_method != 'none'\">\n"
"            amounting in <strong><t t-out=\"format_amount(object.amount_total, object.pricelist_id.currency_id) or ''\">$ 100.00</t>.</strong><br/>\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            .<br/>\n"
"        </t>\n"
"        You can reply to this email if you have any questions.\n"
"        <br/><br/>\n"
"        Thank you,\n"
"        <t t-if=\"user.signature\">\n"
"            <br/>\n"
"            <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<i>(Add)</i>"
msgstr "<i>(Pridať)</i>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid ""
"<span class=\"o_stat_text\">1</span>\n"
"                                <span class=\"o_stat_text\">Invoices</span>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">Repairs</span>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Lot/Serial Number:</strong>"
msgstr "<strong>Šarža/Sériové číslo:</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Operations</strong>"
msgstr "<strong>Operatíva</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Parts</strong>"
msgstr "<strong>Diely</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Printing Date:</strong>"
msgstr "<strong>Dátum tlače:</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Product to Repair:</strong>"
msgstr "<strong>Produkt na opravu:</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Shipping address :</strong>"
msgstr "<strong>Adresa doručenia :</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Total Without Taxes</strong>"
msgstr "<strong>Celkom bez dane</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Total</strong>"
msgstr "<strong>Celkom</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Warranty:</strong>"
msgstr "<strong>Záruka:</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_warn_insufficient_qty_repair_form_view
msgid "? This may lead to inconsistencies in your inventory."
msgstr "? Môže to viesť k nezrovnalostiam v inventári."

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_needaction
msgid "Action Needed"
msgstr "Potrebná akcia"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_ids
msgid "Activities"
msgstr "Aktivity"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Označenie výnimky v aktivite"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_state
msgid "Activity State"
msgstr "Stav aktivity"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikona typ aktivity"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_line__type__add
msgid "Add"
msgstr "Pridať"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Add internal notes."
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Add quotation notes."
msgstr ""

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__invoice_method__after_repair
msgid "After Repair"
msgstr "Po oprave"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_attachment_count
msgid "Attachment Count"
msgstr "Počet príloh"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__invoice_method__b4repair
msgid "Before Repair"
msgstr "Pred opravou"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_make_invoice
msgid "Cancel"
msgstr "Zrušené"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Cancel Repair"
msgstr "Zrušiť opravu"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_line__state__cancel
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__cancel
msgid "Cancelled"
msgstr "Zrušené"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__product_uom_category_id
#: model:ir.model.fields,field_description:repair.field_repair_line__product_uom_category_id
#: model:ir.model.fields,field_description:repair.field_repair_order__product_uom_category_id
msgid "Category"
msgstr "Kategória"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__partner_id
msgid ""
"Choose partner for whom the order will be invoiced and delivered. You can "
"find a partner by its Name, TIN, Email or Internal Reference."
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_tags__color
msgid "Color Index"
msgstr "Index farieb"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__company_id
#: model:ir.model.fields,field_description:repair.field_repair_line__company_id
#: model:ir.model.fields,field_description:repair.field_repair_order__company_id
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Company"
msgstr "Spoločnosť"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_menu_config
msgid "Configuration"
msgstr "Konfigurácia"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Confirm Repair"
msgstr "Potvrdiť opravu"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_line__state__confirmed
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__confirmed
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Confirmed"
msgstr "Potvrdené"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_fee__product_uom_category_id
#: model:ir.model.fields,help:repair.field_repair_line__product_uom_category_id
#: model:ir.model.fields,help:repair.field_repair_order__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Ku konverzii medzi mernými jednotkami môže dôjsť len v prípade, že patria do"
" rovnakej kategórie. Konverzia bude spravená na základe pomerov."

#. module: repair
#: code:addons/repair/models/repair.py:0 code:addons/repair/models/repair.py:0
#, python-format
msgid ""
"Couldn't find a pricelist line matching this product and quantity.\n"
"You have to change either the product, the quantity or the pricelist."
msgstr ""
"Nemožno nájsť riadok cenníku zodpovedajúci tomuto produktu a množstvu.\n"
"Budete musieť zmeniť buď produkt, množstvo alebo cenník."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_make_invoice
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Create Invoice"
msgstr "Vytvoriť faktúru"

#. module: repair
#: model:ir.model,name:repair.model_repair_order_make_invoice
msgid "Create Mass Invoice (repair)"
msgstr "Založenie hromadnej faktúry (oprava)"

#. module: repair
#: model_terms:ir.actions.act_window,help:repair.action_repair_order_tag
msgid "Create a new tag"
msgstr "Vytvoriť nový tag"

#. module: repair
#: model:ir.actions.act_window,name:repair.act_repair_invoice
#: model_terms:ir.ui.view,arch_db:repair.view_make_invoice
msgid "Create invoices"
msgstr "Vytvorené faktúry"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__create_uid
#: model:ir.model.fields,field_description:repair.field_repair_line__create_uid
#: model:ir.model.fields,field_description:repair.field_repair_order__create_uid
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__create_uid
#: model:ir.model.fields,field_description:repair.field_repair_tags__create_uid
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__create_uid
msgid "Created by"
msgstr "Vytvoril"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__create_date
#: model:ir.model.fields,field_description:repair.field_repair_line__create_date
#: model:ir.model.fields,field_description:repair.field_repair_order__create_date
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__create_date
#: model:ir.model.fields,field_description:repair.field_repair_tags__create_date
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__create_date
msgid "Created on"
msgstr "Vytvorené"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__currency_id
#: model:ir.model.fields,field_description:repair.field_repair_line__currency_id
#: model:ir.model.fields,field_description:repair.field_repair_order__currency_id
msgid "Currency"
msgstr "Mena"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__partner_id
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Customer"
msgstr "Zákazník"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__default_address_id
msgid "Default Address"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__address_id
msgid "Delivery Address"
msgstr "Adresa dodania"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__name
#: model:ir.model.fields,field_description:repair.field_repair_line__name
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Description"
msgstr "Popis"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__location_dest_id
msgid "Dest. Location"
msgstr "Cieľ. lokácia"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__display_name
#: model:ir.model.fields,field_description:repair.field_repair_line__display_name
#: model:ir.model.fields,field_description:repair.field_repair_order__display_name
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__display_name
#: model:ir.model.fields,field_description:repair.field_repair_tags__display_name
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__display_name
msgid "Display Name"
msgstr "Zobrazovaný názov"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_warn_insufficient_qty_repair_form_view
msgid "Do you confirm you want to repair"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_make_invoice
msgid "Do you really want to create the invoice(s)?"
msgstr "Naozaj chcete vytvoriť faktúru (y)?"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_line__state__done
msgid "Done"
msgstr "Hotové"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_line__state__draft
msgid "Draft"
msgstr "Návrh"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid ""
"Draft invoices for this order will be cancelled. Do you confirm the action?"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "End Repair"
msgstr "Ukončiť opravu"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_line__tracking
#: model:ir.model.fields,help:repair.field_repair_order__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "Zabezpečte sledovateľnosť skladovateľného produktu vo vašom sklade."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Extra Info"
msgstr "Ďalšie informácie"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Fees"
msgstr "Poplatky"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_follower_ids
msgid "Followers"
msgstr "Odberatelia"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_partner_ids
msgid "Followers (Partners)"
msgstr "Odberatelia (partneri)"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Úžasná ikona fronty napr. fa-tasks"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Future Activities"
msgstr "Budúce aktivity"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Group By"
msgstr "Zoskupiť podľa"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__group
msgid "Group by partner invoice address"
msgstr "Zoskupiť podľa fakturačnej adresy partnera"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__has_message
msgid "Has Message"
msgstr "Má správu"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "History"
msgstr "História"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__id
#: model:ir.model.fields,field_description:repair.field_repair_line__id
#: model:ir.model.fields,field_description:repair.field_repair_order__id
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__id
#: model:ir.model.fields,field_description:repair.field_repair_tags__id
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__id
msgid "ID"
msgstr "ID"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_exception_icon
msgid "Icon"
msgstr "Ikona"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikona indikujúca výnimočnú aktivitu."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_needaction
#: model:ir.model.fields,help:repair.field_repair_order__message_unread
msgid "If checked, new messages require your attention."
msgstr "Ak označené, potom nové správy vyžadujú vašu pozornosť."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_has_error
#: model:ir.model.fields,help:repair.field_repair_order__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Ak označené, potom majú niektoré správy chybu dodania."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__priority
msgid "Important repair order"
msgstr ""

#. module: repair
#: model_terms:ir.actions.act_window,help:repair.action_repair_order_tree
msgid ""
"In a repair order, you can detail the components you remove,\n"
"                add or replace and record the time you spent on the different\n"
"                operations."
msgstr ""
"V opravnej objednávke, môžete detailovať komponenty, ktoré odberete,\n"
"pridáte alebo nahradíte, a zaznamenáte čas strávený na rôznych\n"
"operáciach."

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__internal_notes
msgid "Internal Notes"
msgstr "Interné poznámky"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__move_id
msgid "Inventory Move"
msgstr "Pohyb zásob"

#. module: repair
#: model:ir.actions.act_window,name:repair.action_repair_move_lines
msgid "Inventory Moves"
msgstr "Inventúrne pohyby"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__invoice_id
msgid "Invoice"
msgstr "Faktúra"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__invoice_line_id
#: model:ir.model.fields,field_description:repair.field_repair_line__invoice_line_id
msgid "Invoice Line"
msgstr "Položky faktúry"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__invoice_method
msgid "Invoice Method"
msgstr "Fakturačná metóda"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__invoice_state
msgid "Invoice State"
msgstr "Stav faktúry"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Invoice address:"
msgstr "Fakturačná adresa:"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Invoice and shipping address:"
msgstr "Fakturačná a dodacia adresa:"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Invoice created"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__invoiced
#: model:ir.model.fields,field_description:repair.field_repair_line__invoiced
#: model:ir.model.fields,field_description:repair.field_repair_order__invoiced
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Invoiced"
msgstr "Fakturované"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__partner_invoice_id
msgid "Invoicing Address"
msgstr "Fakturačná adresa"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_is_follower
msgid "Is Follower"
msgstr "Odberateľ"

#. module: repair
#: model:ir.model,name:repair.model_account_move
msgid "Journal Entry"
msgstr "Vstup účtovnej knihy"

#. module: repair
#: model:ir.model,name:repair.model_account_move_line
msgid "Journal Item"
msgstr "Položka účtovnej knihy"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee____last_update
#: model:ir.model.fields,field_description:repair.field_repair_line____last_update
#: model:ir.model.fields,field_description:repair.field_repair_order____last_update
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice____last_update
#: model:ir.model.fields,field_description:repair.field_repair_tags____last_update
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair____last_update
msgid "Last Modified on"
msgstr "Posledná úprava"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__write_uid
#: model:ir.model.fields,field_description:repair.field_repair_line__write_uid
#: model:ir.model.fields,field_description:repair.field_repair_order__write_uid
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__write_uid
#: model:ir.model.fields,field_description:repair.field_repair_tags__write_uid
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__write_uid
msgid "Last Updated by"
msgstr "Naposledy upravoval"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__write_date
#: model:ir.model.fields,field_description:repair.field_repair_line__write_date
#: model:ir.model.fields,field_description:repair.field_repair_order__write_date
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__write_date
#: model:ir.model.fields,field_description:repair.field_repair_tags__write_date
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__write_date
msgid "Last Updated on"
msgstr "Naposledy upravované"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Late Activities"
msgstr "Omeškané aktivity"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__location_id
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__location_id
msgid "Location"
msgstr "Miesto (adresa)"

#. module: repair
#: model:ir.model,name:repair.model_stock_production_lot
#: model:ir.model.fields,field_description:repair.field_repair_line__lot_id
#: model:ir.model.fields,field_description:repair.field_repair_order__lot_id
msgid "Lot/Serial"
msgstr "Šarža/sériové"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_main_attachment_id
msgid "Main Attachment"
msgstr "Hlavná príloha"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_has_error
msgid "Message Delivery error"
msgstr "Chyba zobrazovania správ"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_ids
msgid "Messages"
msgstr "Správy"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__move_id
msgid "Move"
msgstr "Presunúť"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__move_id
msgid "Move created by the repair order"
msgstr "Pohyb vytvorený opravnou objednávkou"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Termín mojej aktivity"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Ďalšia udalosť kalendára aktivít"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Ďalší konečný termín aktivity"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_summary
msgid "Next Activity Summary"
msgstr "Zhrnutie ďalšej aktivity"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_type_id
msgid "Next Activity Type"
msgstr "Typ ďalšej aktivity"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__invoice_method__none
msgid "No Invoice"
msgstr "Žiadna faktúra"

#. module: repair
#: code:addons/repair/models/repair.py:0 code:addons/repair/models/repair.py:0
#, python-format
msgid "No account defined for product \"%s\"."
msgstr "Žiadny účet zadefinovaný pre produkt \"%s\"."

#. module: repair
#: code:addons/repair/models/repair.py:0 code:addons/repair/models/repair.py:0
#, python-format
msgid "No pricelist found."
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "No product defined on fees."
msgstr ""

#. module: repair
#: model_terms:ir.actions.act_window,help:repair.action_repair_order_tree
msgid "No repair order found. Let's create one!"
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:0 code:addons/repair/models/repair.py:0
#, python-format
msgid "No valid pricelist line found."
msgstr ""

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__priority__0
msgid "Normal"
msgstr "Normálna"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_needaction_counter
msgid "Number of Actions"
msgstr "Počet akcií"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_has_error_counter
msgid "Number of errors"
msgstr "Počet chýb"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Počet správ, ktoré vyžadujú akciu"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Počet doručených správ s chybou"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_unread_counter
msgid "Number of unread messages"
msgstr "Počet neprečítaných správ"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Only draft repairs can be confirmed."
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__fees_lines
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Operations"
msgstr "Operácie"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__operations
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Parts"
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Please define an accounting sales journal for the company %s (%s)."
msgstr "Definujte účtovný denník predaja spoločnosti %s (%s)."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Price"
msgstr "Cena"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__pricelist_id
msgid "Pricelist"
msgstr "Cenník"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__pricelist_id
msgid "Pricelist of the selected partner."
msgstr "Cenník zvoleného partnera."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Print Quotation"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__priority
msgid "Priority"
msgstr "Priority"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__product_id
#: model:ir.model.fields,field_description:repair.field_repair_line__product_id
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__product_id
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Product"
msgstr "Produkt"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Product Moves"
msgstr "Pohyby produktu"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_qty
msgid "Product Quantity"
msgstr "Množstvo produktu"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__tracking
#: model:ir.model.fields,field_description:repair.field_repair_order__tracking
msgid "Product Tracking"
msgstr "Sledovanie produktu"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__product_uom
#: model:ir.model.fields,field_description:repair.field_repair_line__product_uom
#: model:ir.model.fields,field_description:repair.field_repair_order__product_uom
msgid "Product Unit of Measure"
msgstr "Merná jednotka produktu"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_id
msgid "Product to Repair"
msgstr "Produkt na opravu"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__lot_id
msgid "Products repaired are all belonging to this lot"
msgstr "Opravené produkty patria všetky k tejto šarži"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__quant_ids
msgid "Quant"
msgstr "Množstvo"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__product_uom_qty
#: model:ir.model.fields,field_description:repair.field_repair_line__product_uom_qty
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__quantity
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_tree
msgid "Quantity"
msgstr "Množstvo"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__draft
msgid "Quotation"
msgstr "Cenová ponuka"

#. module: repair
#: model:ir.actions.report,name:repair.action_report_repair_order
msgid "Quotation / Order"
msgstr "Cenová ponuka / objednávka"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__quotation_notes
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Quotation Notes"
msgstr "Poznámky cenovej ponuky"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Quotations"
msgstr "Cenové ponuky"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Ready To Repair"
msgstr "Pripravené na opravu"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__ready
msgid "Ready to Repair"
msgstr "Pripravené na opravu"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_line__type__remove
msgid "Remove"
msgstr "Odstrániť"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_account_bank_statement_line__repair_ids
#: model:ir.model.fields,field_description:repair.field_account_move__repair_ids
#: model:ir.model.fields,field_description:repair.field_account_payment__repair_ids
#: model:ir.model.fields,field_description:repair.field_stock_move__repair_id
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__repair_id
msgid "Repair"
msgstr "Oprava"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__description
msgid "Repair Description"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_account_move_line__repair_fee_ids
msgid "Repair Fee"
msgstr ""

#. module: repair
#: model:ir.model,name:repair.model_repair_fee
msgid "Repair Fees"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_account_move_line__repair_line_ids
msgid "Repair Line"
msgstr "Opravný riadok"

#. module: repair
#: model:ir.model,name:repair.model_repair_line
msgid "Repair Line (parts)"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Repair Notes"
msgstr ""

#. module: repair
#: model:ir.model,name:repair.model_repair_order
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Repair Order"
msgstr "Objednávka na opravu"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Repair Order #:"
msgstr "Objednávka na opravu #:"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__repair_id
#: model:ir.model.fields,field_description:repair.field_repair_line__repair_id
msgid "Repair Order Reference"
msgstr "Referencia objednávky na opravu"

#. module: repair
#: model:ir.actions.act_window,name:repair.action_repair_order_graph
#: model:ir.actions.act_window,name:repair.action_repair_order_tree
#: model:ir.model.fields,field_description:repair.field_stock_production_lot__repair_order_ids
#: model_terms:ir.ui.view,arch_db:repair.stock_production_lot_view_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_graph
#: model_terms:ir.ui.view,arch_db:repair.view_repair_pivot
msgid "Repair Orders"
msgstr "Opravné objednávky"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_menu_tag
msgid "Repair Orders Tags"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Repair Quotation #:"
msgstr "Opravná cenová ponuka #:"

#. module: repair
#: model:mail.template,name:repair.mail_template_repair_quotation
msgid "Repair Quotation: Send by email"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__name
msgid "Repair Reference"
msgstr "Referencia opravy"

#. module: repair
#: model:product.product,name:repair.product_service_order_repair
#: model:product.template,name:repair.product_service_order_repair_product_template
msgid "Repair Services"
msgstr ""

#. module: repair
#: model:ir.model,name:repair.model_repair_tags
#: model_terms:ir.ui.view,arch_db:repair.view_repair_tag_form
msgid "Repair Tags"
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Repair must be canceled in order to reset it to draft."
msgstr "Oprava musí byť zrušená, aby sa obnovila na koncept."

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Repair must be confirmed before starting reparation."
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Repair must be repaired in order to make the product moves."
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Repair must be under repair in order to end reparation."
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_production_lot__repair_order_count
msgid "Repair order count"
msgstr ""

#. module: repair
#: code:addons/repair/models/stock_production_lot.py:0
#, python-format
msgid "Repair orders of %s"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__repaired
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__done
msgid "Repaired"
msgstr "Opravené"

#. module: repair
#: model:ir.ui.menu,name:repair.menu_repair_order
#: model:ir.ui.menu,name:repair.repair_menu
msgid "Repairs"
msgstr "Opravy"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_tree
msgid "Repairs order"
msgstr "Objednávka na opravu"

#. module: repair
#: model:ir.ui.menu,name:repair.repair_menu_reporting
msgid "Reporting"
msgstr "Prehľady"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__user_id
msgid "Responsible"
msgstr "Zodpovedný"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_user_id
msgid "Responsible User"
msgstr "Zodpovedný užívateľ"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Chyba doručenia SMS"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__sale_order_id
msgid "Sale Order"
msgstr "Objednávka predaja"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__sale_order_id
msgid "Sale Order from which the product to be repaired comes from."
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__schedule_date
msgid "Scheduled Date"
msgstr "Naplánovaný dátum"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Search Repair Orders"
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__invoice_method
msgid ""
"Selecting 'Before Repair' or 'After Repair' will allow you to generate "
"invoice before or after the repair is done respectively. 'No invoice' means "
"you don't want to generate invoice for this repair order."
msgstr ""
"Zvolenie \"Pred opravou\" alebo \"Po oprave\" vám umožní generovať faktúry "
"pred alebo po ukončení samotnej opravy. \"Žiadna faktúra\" znamená, že "
"nechcete generovať faktúry pre túto objednávku na opravu."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Send Quotation"
msgstr "Odošlite cenovú ponuku"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Serial number is required for operation lines with products: %s"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Set to Draft"
msgstr "Nastaviť na koncept"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Show all records which has next action date is before today"
msgstr "Zobraz všetky záznamy, ktorých následná aktivita je pred dnešným dňom"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__location_id
msgid "Source Location"
msgstr "Zdrojová lokácia"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Start Repair"
msgstr "Začať opravu"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__state
#: model:ir.model.fields,field_description:repair.field_repair_order__state
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Status"
msgstr "Stav"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status založený na aktivitách\n"
"Zmeškané: dátum už vypršal\n"
"Dnes: dátum aktivity je dnes\n"
"Plán: budúce aktivity"

#. module: repair
#: model:ir.model,name:repair.model_stock_move
msgid "Stock Move"
msgstr "Pohyb skladu"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__price_subtotal
#: model:ir.model.fields,field_description:repair.field_repair_line__price_subtotal
msgid "Subtotal"
msgstr "Medzisúčet"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_tags__name
msgid "Tag Name"
msgstr "Názov tagu"

#. module: repair
#: model:ir.model.constraint,message:repair.constraint_repair_tags_name_uniq
msgid "Tag name already exists!"
msgstr "Názov značky už existuje!"

#. module: repair
#: model:ir.actions.act_window,name:repair.action_repair_order_tag
#: model:ir.model.fields,field_description:repair.field_repair_order__tag_ids
#: model_terms:ir.ui.view,arch_db:repair.view_repair_tag_search
#: model_terms:ir.ui.view,arch_db:repair.view_repair_tag_tree
msgid "Tags"
msgstr "Tagy"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Tax"
msgstr "Daň"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__tax_id
#: model:ir.model.fields,field_description:repair.field_repair_line__tax_id
#: model:ir.model.fields,field_description:repair.field_repair_order__amount_tax
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Taxes"
msgstr "Dane"

#. module: repair
#: model:ir.model.constraint,message:repair.constraint_repair_order_name
msgid "The name of the Repair Order must be unique!"
msgstr "Názov objednávky na opravu musí byť jedinečné!"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid ""
"The product unit of measure you chose has a different category than the "
"product unit of measure."
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_line__state
msgid ""
"The status of a repair line is set automatically to the one of the linked "
"repair order."
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__location_id
msgid "This is the location where the product to repair is located."
msgstr ""

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__2binvoiced
msgid "To be Invoiced"
msgstr "Na vyfaktúrovanie"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Today Activities"
msgstr "Dnešné aktivity"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__price_total
#: model:ir.model.fields,field_description:repair.field_repair_line__price_total
#: model:ir.model.fields,field_description:repair.field_repair_order__amount_total
msgid "Total"
msgstr "Celkom"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Total amount"
msgstr "Celková suma"

#. module: repair
#: model:ir.model,name:repair.model_stock_traceability_report
msgid "Traceability Report"
msgstr "Report sledovateľnosti"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__type
msgid "Type"
msgstr "Typ"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Typ výnimočnej aktivity v zázname."

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__state__under_repair
msgid "Under Repair"
msgstr "Opravuje sa"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__price_unit
#: model:ir.model.fields,field_description:repair.field_repair_line__price_unit
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Unit Price"
msgstr "Jednotková cena"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__product_uom_name
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_tree
msgid "Unit of Measure"
msgstr "Merná jednotka"

#. module: repair
#: model:product.product,uom_name:repair.product_service_order_repair
#: model:product.template,uom_name:repair.product_service_order_repair_product_template
msgid "Units"
msgstr "Jednotky"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_unread
msgid "Unread Messages"
msgstr "Neprečítané správy"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Počítadlo neprečítaných správ"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__amount_untaxed
msgid "Untaxed Amount"
msgstr "Suma bez DPH"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Untaxed amount"
msgstr "Nezdanená suma"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "UoM"
msgstr "Merná jednotka"

#. module: repair
#: model:ir.model.fields.selection,name:repair.selection__repair_order__priority__1
msgid "Urgent"
msgstr "Súrne"

#. module: repair
#: model:ir.model,name:repair.model_stock_warn_insufficient_qty_repair
msgid "Warn Insufficient Repair Quantity"
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "Warning"
msgstr "Varovanie"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__guarantee_limit
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Warranty Expiration"
msgstr "Vypršanie záruky"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__website_message_ids
msgid "Website Messages"
msgstr "Správy webstránok"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__website_message_ids
msgid "Website communication history"
msgstr "História komunikácie webstránok"

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid ""
"You can not delete a repair order once it has been confirmed. You must first"
" cancel it."
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid ""
"You can not delete a repair order which is linked to an invoice which has "
"been posted once."
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "You can not enter negative quantities."
msgstr "Záporné množstvá nemožno zadať."

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "You cannot cancel a completed repair order."
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "You cannot delete a completed repair order."
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:0 code:addons/repair/models/repair.py:0
#, python-format
msgid ""
"You have to select a pricelist in the Repair form !\n"
" Please set one before choosing a product."
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:0
#, python-format
msgid "You have to select an invoice address in the repair form."
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.stock_warn_insufficient_qty_repair_form_view
msgid "from location"
msgstr ""

#. module: repair
#: model:mail.template,report_name:repair.mail_template_repair_quotation
msgid "{{ (object.name or '').replace('/','_') }}"
msgstr ""

#. module: repair
#: model:mail.template,subject:repair.mail_template_repair_quotation
msgid ""
"{{ object.partner_id.name }} Repair Orders (Ref {{ object.name or 'n/a' }})"
msgstr ""
