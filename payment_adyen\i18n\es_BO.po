# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * payment_adyen
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Language-Team: Spanish (Bolivia) (https://www.transifex.com/odoo/teams/41243/es_BO/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_BO\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment.py:186
#, python-format
msgid "; multiple order found"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment.py:184
#, python-format
msgid "; no order found"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment.py:234
#, python-format
msgid "Adyen: feedback error"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment.py:196
#, python-format
msgid "Adyen: invalid merchantSig, received %s, computed %s"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment.py:182
#, python-format
msgid "Adyen: received data for reference %s"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment.py:175
#, python-format
msgid ""
"Adyen: received data with missing reference (%s) or missing pspReference "
"(%s)"
msgstr ""

#. module: payment_adyen
#: model_terms:ir.ui.view,arch_db:payment_adyen.acquirer_form_adyen
msgid "How to configure your Adyen account?"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_acquirer_adyen_merchant_account
msgid "Merchant Account"
msgstr ""

#. module: payment_adyen
#: model:ir.model,name:payment_adyen.model_payment_acquirer
msgid "Payment Acquirer"
msgstr ""

#. module: payment_adyen
#: model:ir.model,name:payment_adyen.model_payment_transaction
msgid "Payment Transaction"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_acquirer_adyen_skin_code
msgid "Skin Code"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_acquirer_adyen_skin_hmac_key
msgid "Skin HMAC Key"
msgstr ""
