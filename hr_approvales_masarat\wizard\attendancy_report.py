# -*- coding:utf-8 -*-
from odoo import api, fields, models, _
from datetime import datetime, timedelta
from pytz import timezone
from odoo.exceptions import ValidationError


class ManagerEmployeeAttendancyRepo(models.TransientModel):
    _name = "hr.manager.attendance.report"

    def _get_my_employees_ids(self):
        employees = self.env['hr.employee'].sudo().search([('parent_id.user_id', '=', self.env.uid)])
        employees_list = []
        for elem in employees:
            employees_list.append((elem.id,elem.name))

        employees = self.env['hr.employee'].sudo().search([('user_id', '=', self.env.uid)])
        employees_list.append((employees.id, employees.name))
        return employees_list

    employee_selection = fields.Selection(
        selection=lambda self: self._get_my_employees_ids(),
        string="Employee")

    employee_id = fields.Many2one('hr.employee', string="الموظف")
    all_absence = fields.Boolean(string="كل الموظفين")
    type = fields.Selection([('absence','غياب'),('latency','تأخير')],string="النوع")
    employee_with_absance = fields.Boolean(string="تقرير غياب فقط!")
    # employee_with_latency = fields.Boolean(string="تقرير تأخير فقط!")
    detail_type = fields.Selection([('detailed','تفصيلي'),('agregation','تجميعي')],string="نوع التقرير")
    date_start = fields.Date(string='تاريخ البدأ')
    date_end = fields.Date(string='تاريخ الانتهاء')

    def get_employee_attendancy_report_action(self):
        if self.employee_id:
            ee = self.employee_id.id
        elif self.employee_selection:
            ee = int(self.employee_selection)
        else:
            ee = False
        data = {'model': self._name,'type':self.type,'employee_with_absance':self.employee_with_absance ,'all_absence':self.all_absence, 'detail_type':self.detail_type, 'employee_id': ee, 'date_start':str(self.date_start), 'date_end':str(self.date_end)}
        return self.sudo().env.ref('hr_approvales_masarat.employee_attendancy_report_x1').report_action(self, data=data)


class clientInvoiceReportAction(models.AbstractModel):
    _name = "report.hr_approvales_masarat.employee_attendancy_report_id"
    _description = 'Employee Attendance Details Report'


    def get_attendancy_dict(self,date_start, date_end):
        dates_list = []
        start_date = datetime.strptime(date_start,"%Y-%m-%d")
        while str(start_date.date()) <= str(date_end):
            dates_list.append(str(start_date.date()))
            start_date = start_date + timedelta(days=1)
        return dates_list

    def convert_TZ_UTC(self, TZ_datetime):
        fmt = "%Y-%m-%d %H:%M:%S"
        # Current time in UTC
        now_utc = datetime.now(timezone('UTC'))
        # Convert to current user time zone
        now_timezone = now_utc.astimezone(timezone(self.env.user.tz))
        UTC_OFFSET_TIMEDELTA = datetime.strptime(now_utc.strftime(fmt), fmt) - datetime.strptime(now_timezone.strftime(fmt), fmt)
        local_datetime = datetime.strptime(str(TZ_datetime), fmt)
        result_utc_datetime = local_datetime - UTC_OFFSET_TIMEDELTA
        # return result_utc_datetime.strftime(fmt)
        return result_utc_datetime

    def get_attendancy_note(self,computed_latency, computed_latency_note):
        if computed_latency and not computed_latency_note:
            return 'تأخير'
        else:
            return computed_latency_note

    def get_all_absense(self,start_date, end_date,detail_type):
        employees_dict = {}
        attendancy_with_restriction = self.env['resource.calendar'].search([('there_is_letancy','=',True)])
        employee_running_contracts = self.env['hr.contract'].search([('state', '=', 'open'),('resource_calendar_id','in',attendancy_with_restriction.ids)]).employee_id
        attendancy_list = self.get_attendancy_dict(start_date, end_date)
        for employee in employee_running_contracts:
            weekdays = employee.contract_id.resource_calendar_id.attendance_ids.mapped('dayofweek')
            for day in attendancy_list:
                employee_attendancy = self.env['hr.attendance'].search([('employee_id', '=', employee.id), ('attendance_date', '=', day)], limit=1)
                # check_for_absence_leave = self.env['hr.masarat.absence'].search([('employee_id', '=', employee.id), ('state', 'in', ['hr_approval', 'manager_approval']),('absence_start_at', '<=', day), ('absence_end_at', '>=', day)])
                check_for_holiday_leave = self.env['hr.leave'].search([('state', 'in', ('validate1', 'validate')), ('employee_id', '=', employee.id),('request_unit_half', '=', False), ('date_from', '<=', day), ('date_to', '>=', day)])
                check_for_work_assignment = self.env['hr.masarat.work.assignment'].search([('employee_id', '=', employee.id), ('state', 'in', ['hr_approval', 'manager_approval']),('start_date', '<=', day), ('end_date', '>=', day)], limit=1)
                check_if_in_global_leave = self.env['resource.calendar.leaves'].search([('date_from', '<=', day), ('date_to', '>=', day), ('resource_id', '=', False),('calendar_id', '=', employee.contract_id.resource_calendar_id.id)], limit=1)
                is_weekend = str(datetime.strptime(day, "%Y-%m-%d").weekday()) not in weekdays
                # if (not is_weekend) and (not check_if_in_global_leave) and (not check_for_work_assignment) and (not check_for_absence_leave) and (not check_for_holiday_leave) and (not employee_attendancy):
                if (not is_weekend) and (not check_if_in_global_leave) and (not check_for_work_assignment) and (not check_for_holiday_leave) and (not employee_attendancy):
                    employees_dict.setdefault(employee.id, {'name': employee.name,'attendancy_type': employee.contract_id.resource_calendar_id.name,'department': employee.department_id.name,'absence_days': []})
                    check_for_absence_leave = self.env['hr.masarat.absence'].search([('employee_id', '=', employee.id), ('state', 'in', ['hr_approval', 'manager_approval']),('absence_start_at', '<=', day), ('absence_end_at', '>=', day)])
                    if check_for_absence_leave:
                        employees_dict[employee.id]['absence_days'].append({'date':str(day), 'note1':' غياب بإذن ', 'note2':''})
                    else:
                        employees_dict[employee.id]['absence_days'].append({'date':str(day), 'note1': ' غياب ', 'note2': ''})
        for employee in employees_dict.keys():
            if detail_type == 'detailed':
                employees_dict[employee]['absence_days'] = sorted(employees_dict[employee]['absence_days'], key=lambda x: x['date'], reverse=True)
            else:
                employees_dict[employee]['absence_days'] = [{'date': len(list(filter(lambda x:x['note1']==' غياب ', employees_dict[employee]['absence_days']))), 'note1':' غياب ', 'note2':''}, {'date': len(list(filter(lambda x:x['note1']==' غياب بإذن ', employees_dict[employee]['absence_days']))), 'note1':' غياب بإذن ', 'note2':''}]
        return {'all_absence':True, 'employees_dict':employees_dict, 'start_date':start_date,'end_date':end_date,'type':' غياب ','detail_type_name':str(detail_type).replace('detailed','تفصيلي').replace('agregation', 'تجميعي')}

    def get_all_latency(self,start_date, end_date,detail_type):
        employees_dict = {}
        attendancy_with_restriction = self.env['resource.calendar'].search([('there_is_letancy', '=', True)])
        employee_running_contracts = self.env['hr.contract'].search([('state', '=', 'open'), ('resource_calendar_id', 'in', attendancy_with_restriction.ids)]).employee_id
        attendancy_list = self.get_attendancy_dict(start_date, end_date)
        for employee in employee_running_contracts:
            weekdays = employee.contract_id.resource_calendar_id.attendance_ids.mapped('dayofweek')
            for day in attendancy_list:
                employee_attendancy = self.env['hr.attendance'].search([('employee_id', '=', employee.id), ('attendance_date', '=', day)], limit=1)
                if employee_attendancy and employee_attendancy.computed_latency and not employee_attendancy.computed_latency_note:
                    employees_dict.setdefault(employee.id, {'name': employee.name,
                                                            'attendancy_type': employee.contract_id.resource_calendar_id.name,
                                                            'department': employee.department_id.name,
                                                            'absence_days': []})
                    employees_dict[employee.id]['absence_days'].append({'date': str(day), 'note1': employee_attendancy.computed_latency, 'note2': int(employee_attendancy.computed_latency / employee_attendancy.attendance_type_id.penalty)})

        colomn = 'تاريخ :: محتسب :: فعلي '

        for employee in employees_dict.keys():
            if detail_type == 'detailed':
                employees_dict[employee]['absence_days'] = sorted(employees_dict[employee]['absence_days'],key=lambda x: x['date'], reverse=True)
            else:
                colomn = "عدد الأيام :: محتسب :: فعلي "
                employees_dict[employee]['absence_days'] = [{'date': len(list(employees_dict[employee]['absence_days'])),'note1': sum([x['note1'] for x in employees_dict[employee]['absence_days']]), 'note2': sum([x['note2'] for x in employees_dict[employee]['absence_days']])}]
        return {'all_absence': True, 'employees_dict': employees_dict, 'start_date': start_date, 'end_date': end_date,'type':" تأخير ",'type_colomn':colomn,
                'detail_type_name': str(detail_type).replace('detailed', 'تفصيلي').replace('agregation', 'تجميعي')}

    def _get_report_values(self, docids, data=None):
        if data['all_absence'] and data['type'] == 'absence':
            all_absence_list = self.get_all_absense(data['date_start'],data['date_end'], data['detail_type'])
            return all_absence_list
        if data['all_absence'] and data['type'] == 'latency':
            all_latency_list = self.get_all_latency(data['date_start'],data['date_end'], data['detail_type'])
            return all_latency_list
        else:
            employee = self.env['hr.employee'].search([('id', '=', data['employee_id'])])
            name = employee.name
            absence_count = 0
            department = employee.department_id.name
            contract_attendancy = self.env['hr.contract'].search([('employee_id', '=', data['employee_id']),('state', '=', 'open')])
            if not contract_attendancy:
                raise ValidationError('This Employee Has no Running Contract')
            attendance_type = contract_attendancy.resource_calendar_id
            if attendance_type:
                weekdays = attendance_type.attendance_ids.mapped('dayofweek')
            attendancy_list = self.get_attendancy_dict(data['date_start'],data['date_end'])
            atten_obj = []
            for elem in attendancy_list:
                employee_attendancy = self.env['hr.attendance'].search([('employee_id', '=', data['employee_id']),('attendance_date', '=',elem)],limit=1)
                check_for_holiday_leave = self.env['hr.leave'].search([('state', 'in', ('validate1', 'validate')), ('employee_id', '=', data['employee_id']), ('request_unit_half', '=', False), ('date_from', '<=', elem), ('date_to', '>=', elem )])
                check_for_absence_leave = self.env['hr.masarat.absence'].search([('employee_id', '=', data['employee_id']),('state', 'in', ['hr_approval', 'manager_approval']),('absence_start_at', '<=', elem),('absence_end_at', '>=', elem)])
                check_for_work_assignment = self.env['hr.masarat.work.assignment'].search([('employee_id', '=', data['employee_id']), ('state', 'in', ['hr_approval', 'manager_approval']), ('start_date', '<=', elem), ('end_date', '>=',elem)],limit=1)
                check_if_in_global_leave = self.env['resource.calendar.leaves'].search([('date_from', '<=', elem), ('date_to', '>=', elem), ('resource_id', '=', False), ('calendar_id', '=', attendance_type.id)],limit=1)
                val = {
                    'check_in': '00:00:00',
                    'check_out': '00:00:00',
                    'worked_hours': '00:00',
                    'attendance_date': str(elem),
                    'state': '',
                    'computed_latency_minutes': '0'}
                if employee_attendancy:
                    note = self.get_attendancy_note(employee_attendancy.computed_latency, employee_attendancy.computed_latency_note)
                    val['check_in'] = str(self.convert_TZ_UTC(employee_attendancy.check_in))[11:]
                    val['check_out'] = str(self.convert_TZ_UTC(employee_attendancy.check_out))[11:]
                    val['worked_hours'] = '{0:02.0f}:{1:02.0f}'.format(*divmod(employee_attendancy.worked_hours * 60, 60))
                    val['state'] = note
                    val['computed_latency_minutes'] = str(employee_attendancy.computed_latency)
                    if str(datetime.strptime(elem, "%Y-%m-%d").weekday()) not in weekdays: ### If there is attendancy at weekend
                        val['state'] = 'عطلة نهاية اسبوع'
                        val['computed_latency_minutes'] = 0

                if not employee_attendancy and check_for_holiday_leave:
                    val['state'] = val['state']+str(check_for_holiday_leave.holiday_status_id.name)
                if not employee_attendancy and check_for_work_assignment:
                    val['state'] = val['state']+str(dict(check_for_work_assignment._fields['assignment_type'].selection).get(check_for_work_assignment.assignment_type))
                if not employee_attendancy and check_for_absence_leave:
                    val['state'] = val['state']+str(" الموظف لديه اذن غياب ")
                if not employee_attendancy and check_if_in_global_leave:
                    val['state'] = val['state']+str(check_if_in_global_leave.name)
                if (str(datetime.strptime(elem, "%Y-%m-%d").weekday()) not in weekdays) and (val['check_in'] == '00:00:00'):  ### If there is attendancy at weekend
                    continue
                if not val['state'] and not employee_attendancy:
                    if data['employee_with_absance']:
                        val['state'] = val['state'] + ' غياب '
                        absence_count+=1
                    else:
                        absence_count += 1
                        continue
                    # val['state'] = val['state']+ ' غياب '
                atten_obj.append(val)

            atten_obj = sorted(atten_obj, key=lambda x:x['attendance_date'],reverse=True)
            final_vals = {'atten_obj':atten_obj,'detail_type_name':str(data['detail_type']).replace('detailed','تفصيلي').replace('agregation', 'تجميعي'), 'attendance_type':attendance_type.name,'absence_count':absence_count, 'name':name, 'department':department,'all_absence':False}
            return final_vals

        #