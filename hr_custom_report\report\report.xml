<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>
        <template id="hr_custom_report_id">
            <t t-call="web.html_container">
                <t t-call="web.external_layout">
                    <div class="page" style="direction: rtl;">
                        <h5 class="text-center">HR Custom Report</h5>
                        <table class="table table-bordered">
                            <thead>
                                <tr class="text-center">
                                    <th>ر.م</th>
                                    <t t-foreach="report_fields_label" t-as="f">
                                        <th style="font-size: 12px;"><span t-esc="str(f).replace('_',' ')"/></th>
                                    </t>
                                </tr>
                            </thead>
                            <t t-set="index" t-value="1"/>
                            <tbody>
                                <tr t-foreach="vals" t-as="each_vals" class="text-center">
                                    <td>
                                        <span t-esc="index"/>
                                        <t t-set="index" t-value="index+1"/>
                                    </td>
                                    <t t-foreach="report_fields" t-as="fi">
                                        <td style="font-size: 12px;"><span t-esc="each_vals[fi]"/></td>
                                    </t>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </t>
            </t>
        </template>


        <report
                id="hr_custom_fields_report"
                model="hr.custom.wizard"
                string="HR Custom Report"
                name="hr_custom_report.hr_custom_report_id"
                file="hr_custom_report.hr_custom_report_id"
                report_type="qweb-pdf"
                menu="False"
        />

    </data>
</odoo>