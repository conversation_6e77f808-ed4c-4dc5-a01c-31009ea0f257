<?xml version="1.0" encoding="utf-8"?>
<odoo>
        <record id="removal_fefo" model="product.removal">
            <field name="name">First Expiry First Out (FEFO)</field>
            <field name="method">fefo</field>
        </record>
        <record id="mail_activity_type_alert_date_reached" model="mail.activity.type">
            <field name="name">Alert Date Reached</field>
            <field name="category">default</field>
            <field name="res_model">stock.production.lot</field>
            <field name="icon">fa-tasks</field>
            <field name="delay_count">0</field>
        </record>
</odoo>

