<?xml version="1.0"?>
<odoo>

    <record id="work_assignment_view_form" model="ir.ui.view">
        <field name="name">hr.masarat.work.assignment.form</field>
        <field name="model">hr.masarat.work.assignment</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <field name="state" widget="statusbar"/>
                    <button string="Manager Approval" attrs="{'invisible': ['|',('is_manager','!=','1'),('state','!=','draft')]}" name="make_manager_approval" type="object" class="oe_highlight"/>
                    <button string="Manager Refuse" attrs="{'invisible': ['|',('is_manager','!=','1'),('state','!=','draft')]}" name="make_manager_refused" type="object"/>
                    <button string="HR Approval" name="make_hr_approval" type="object" class="oe_highlight" groups="hr_approvales_masarat.group_hr_approvales_masarat"/>
                    <button string="HR Refuse" name="make_hr_refused" type="object" groups="hr_approvales_masarat.group_hr_approvales_masarat"/>
<!--                    <button string="Cancel" attrs="{'invisible': ['|',('is_manager','!=','1'),('state','=','draft')]}" name="make_cancel_approval" type="object"/>-->
                    <button string="Cancel" attrs="{'invisible': [('state','=','draft')]}" name="make_cancel_approval" type="object"/>
                </header>
                <sheet>
                    <div>
                        <h2>
                            <field name="employee_id" placeholder="Employee" attrs="{'readonly': [('is_hr_group', '!=', 'yes')]}"/>
                            <field name="is_hr_group" invisible="1"/>
                        </h2>
                    </div>
                    <group>
                        <group string="Request Info">
                            <field name="is_manager" invisible="1"/>
                            <field name="manager_id"  options='{"no_open": True}'/>
                            <field name="request_date"/>
                            <field name="request_date_3days" invisible="1"/>
                        </group>
                        <group string="Work Assignment Info">
                            <field name="assignment_type" attrs="{'readonly': [('state','!=','draft')]}"/>
                            <field name="start_date_time" attrs="{'readonly': [('state','!=','draft')]}"/>
                            <field name="end_date_time" attrs="{'readonly': [('state','!=','draft')]}"/>
                            <field name="total_work_hours"/>
                            <field name="start_date" invisible="1"/>
                            <field name="end_date" invisible="1"/>
                        </group>
                        <group >
                            <field name="additional_note"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>


    <record id="latency_view_tree" model="ir.ui.view">
        <field name="name">hr.masarat.work.assignment.tree</field>
        <field name="model">hr.masarat.work.assignment</field>
        <field name="arch" type="xml">
            <tree>
                <field name="assignment_name"/>
                <field name="assignment_type"/>
                <field name="total_work_hours"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <record id="action_work_assignment_view" model="ir.actions.act_window">
        <field name="name">طلبات تكليف بعمل</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">hr.masarat.work.assignment</field>
        <field name="view_mode">tree,form</field>
    </record>


    <menuitem
            id="menu_work_assignment"
            name="نموذج تكليف بعمل/عمل من المنزل"
            parent="hr_masarat_approvals"
            action="action_work_assignment_view"
            sequence="50"/>


</odoo>

