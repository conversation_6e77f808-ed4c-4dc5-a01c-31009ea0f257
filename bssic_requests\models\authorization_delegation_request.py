from odoo import models, fields, api, _
from odoo.exceptions import UserError


class BSSICAuthorizationDelegationRequest(models.Model):
    """Authorization Delegation Request Model - Proxy to bssic.request"""
    _name = 'bssic.authorization.delegation.request'
    _description = 'BSSIC Authorization Delegation Request'
    _inherit = 'bssic.request'
    _auto = False  # Don't create database table
    _table = 'bssic_request'  # Use the same table as bssic.request

    # Authorization delegation specific fields
    ceiling_reason = fields.Text(string='Reason for Ceiling Increase', tracking=True)
    delegation_details = fields.Text(string='Details', tracking=True)
    delegation_max_amount = fields.Float(string='Max. Amount', tracking=True)
    delegation_auth_limit = fields.Float(string='Auth O.D. Limit', tracking=True)
    delegation_from_date = fields.Date(string='From Date', tracking=True)
    delegation_to_date = fields.Date(string='To Date', tracking=True)

    @api.constrains('ceiling_reason', 'delegation_details', 'delegation_from_date', 'delegation_to_date',
                    'delegation_max_amount', 'delegation_auth_limit', 'state', 'show_authorization_delegation_fields')
    def _check_required_authorization_delegation_fields(self):
        """Validate required fields for authorization delegation requests"""
        for record in self:
            if record.show_authorization_delegation_fields and record.state != 'draft':

                if not record.ceiling_reason:
                    raise UserError(_('Reason for Ceiling Increase is required for Authorization Delegation requests.'))

                if not record.delegation_details:
                    raise UserError(_('Details are required for Authorization Delegation requests.'))

                if not record.delegation_from_date:
                    raise UserError(_('From Date is required for Authorization Delegation requests.'))

                if not record.delegation_to_date:
                    raise UserError(_('To Date is required for Authorization Delegation requests.'))

                if not record.delegation_max_amount or record.delegation_max_amount <= 0:
                    raise UserError(_('Max. Amount must be greater than zero for Authorization Delegation requests.'))

                if not record.delegation_auth_limit or record.delegation_auth_limit <= 0:
                    raise UserError(_('Auth O.D. Limit must be greater than zero for Authorization Delegation requests.'))

                if record.delegation_from_date and record.delegation_to_date:
                    if record.delegation_to_date <= record.delegation_from_date:
                        raise UserError(_('To Date must be after From Date in Authorization Delegation requests.'))

    @api.model
    def create(self, vals):
        """Redirect create to bssic.request with authorization delegation defaults"""
        # Set request type code for authorization delegation
        if not vals.get('request_type_code'):
            vals['request_type_code'] = 'authorization_delegation'

        # Find and set the authorization delegation request type
        if not vals.get('request_type_id'):
            request_type = self.env['bssic.request.type'].search([
                ('code', '=', 'authorization_delegation')
            ], limit=1)
            if request_type:
                vals['request_type_id'] = request_type.id

        return self.env['bssic.request'].create(vals)

    @api.model
    def search(self, args, offset=0, limit=None, order=None, count=False):
        """Redirect search to bssic.request with authorization delegation filter"""
        request_model = self.env['bssic.request']
        auth_args = args + [('request_type_id.code', '=', 'authorization_delegation')]
        return request_model.search(auth_args, offset=offset, limit=limit, order=order, count=count)

    def write(self, vals):
        """Redirect write to bssic.request"""
        return self.env['bssic.request'].browse(self.ids).write(vals)

    def unlink(self):
        """Redirect unlink to bssic.request"""
        return self.env['bssic.request'].browse(self.ids).unlink()

    @api.model
    def browse(self, ids):
        """Redirect browse to bssic.request"""
        return self.env['bssic.request'].browse(ids)

    def read(self, fields=None, load='_classic_read'):
        """Redirect read to bssic.request"""
        return self.env['bssic.request'].browse(self.ids).read(fields=fields, load=load)

    @api.model
    def default_get(self, fields_list):
        """Set default values for authorization delegation requests"""
        res = super(BSSICAuthorizationDelegationRequest, self).default_get(fields_list)
        
        # Set default request type for authorization delegation
        if 'request_type_code' in fields_list:
            res['request_type_code'] = 'authorization_delegation'
        
        if 'request_type_id' in fields_list:
            request_type = self.env['bssic.request.type'].search([
                ('code', '=', 'authorization_delegation')
            ], limit=1)
            if request_type:
                res['request_type_id'] = request_type.id

        return res
