# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * product_margin
#
# Translators:
# <AUTHOR> <EMAIL>, 2016
# <PERSON> <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2016-02-18 04:03+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Colombia) (http://www.transifex.com/odoo/odoo-9/"
"language/es_CO/)\n"
"Language: es_CO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_purchase_num_invoiced
msgid "# Invoiced in Purchase"
msgstr "# Facturado en Compras"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_sale_num_invoiced
msgid "# Invoiced in Sale"
msgstr "Nº Facturado en Venta"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "#Purchased"
msgstr "Núm. comprados"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Analysis Criteria"
msgstr "Criterio de Análisis"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_sale_avg_price
msgid "Avg. Price in Customer Invoices."
msgstr "Precio Medio en las Facturas de Cliente"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_purchase_avg_price
msgid "Avg. Price in Vendor Bills "
msgstr "Avg . Precio en Facturas de Proveedores"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_purchase_avg_price
#: model:ir.model.fields,field_description:product_margin.field_product_product_sale_avg_price
msgid "Avg. Unit Price"
msgstr "Precio Unitario Prom."

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.product_margin_form_view
msgid "Cancel"
msgstr "Cancelar"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Catalog Price"
msgstr "Precio Catálogo"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_membership
msgid "Check if the product is eligible for membership."
msgstr "Compruebe si el producto es elegible para la asociación."

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin_create_date
msgid "Created on"
msgstr "Creado"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_taxes_id
msgid "Customer Taxes"
msgstr "Impuestos del Cliente"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_membership_date_from
msgid "Date from which membership becomes active."
msgstr "Fecha desde la que la membresía se activa."

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_membership_date_to
msgid "Date until which membership remains active."
msgstr "Fecha hasta la que la membresía permanece activa."

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin_display_name
msgid "Display Name"
msgstr "Nombre Público"

#. module: product_margin
#: selection:product.margin,invoice_state:0
#: selection:product.product,invoice_state:0
msgid "Draft, Open and Paid"
msgstr "Borrador, Abierto y Pagado"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_expected_margin
msgid "Expected Margin"
msgstr "Margen Previsto"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_expected_margin_rate
msgid "Expected Margin (%)"
msgstr "Margen Previsto (%)"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_sale_expected
msgid "Expected Sale"
msgstr "Venta Prevista"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_expected_margin
msgid "Expected Sale - Normal Cost"
msgstr "Venta Prevista - Coste Normal"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_sales_gap
msgid "Expected Sale - Turn Over"
msgstr "Venta Prevista - Volumen de Negocio"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_expected_margin_rate
msgid "Expected margin * 100 / Expected Sale"
msgstr "Margen previsto * 100 / Venta Prevista"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_property_account_expense_id
msgid "Expense Account"
msgstr "Cuenta de gastos"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin_from_date
msgid "From"
msgstr "Desde"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.product_margin_form_view
msgid "General Information"
msgstr "Información General"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin_id
msgid "ID"
msgstr "ID"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_property_account_income_id
msgid "Income Account"
msgstr "Cuenta de Ingresos"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin_invoice_state
#: model:ir.model.fields,field_description:product_margin.field_product_product_invoice_state
msgid "Invoice State"
msgstr "Estado Factura"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin___last_update
msgid "Last Modified on"
msgstr "Última Modificación el"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin_write_uid
msgid "Last Updated by"
msgstr "Actualizado por"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin_write_date
msgid "Last Updated on"
msgstr "Actualizado"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_date_from
msgid "Margin Date From"
msgstr "Fecha Desde del Margen"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_date_to
msgid "Margin Date To"
msgstr "Fecha Hasta del Margen"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Margins"
msgstr "Márgenes"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_membership
msgid "Membership"
msgstr "Membresía"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_membership_date_to
msgid "Membership End Date"
msgstr "Fecha Final de la Membresía"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_membership_date_from
msgid "Membership Start Date"
msgstr "Fecha de Inicio de la Membresía"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_normal_cost
msgid "Normal Cost"
msgstr "Coste Normal"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_purchase_gap
msgid "Normal Cost - Total Cost"
msgstr "Coste Normal - Coste Total"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.product_margin_form_view
msgid "Open Margins"
msgstr "Abrir Márgenes"

#. module: product_margin
#: selection:product.margin,invoice_state:0
#: selection:product.product,invoice_state:0
msgid "Open and Paid"
msgstr "Abierto y Pagado"

#. module: product_margin
#: selection:product.margin,invoice_state:0
#: selection:product.product,invoice_state:0
msgid "Paid"
msgstr "Pagada"

#. module: product_margin
#: model:ir.model,name:product_margin.model_product_product
msgid "Product"
msgstr "Producto"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_email_template_id
msgid "Product Email Template"
msgstr "Plantilla de Correo Electrónico del Producto"

#. module: product_margin
#: model:ir.model,name:product_margin.model_product_margin
msgid "Product Margin"
msgstr "Margen del Producto"

#. module: product_margin
#: code:addons/product_margin/wizard/product_margin.py:66
#: model:ir.actions.act_window,name:product_margin.product_margin_act_window
#: model:ir.ui.menu,name:product_margin.menu_action_product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_graph
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
#, python-format
msgid "Product Margins"
msgstr "Márgenes de Producto"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.product_margin_form_view
msgid "Properties categories"
msgstr "Categorías de propiedades"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_purchase_gap
msgid "Purchase Gap"
msgstr "Diferencia Compra"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Purchases"
msgstr "Compras"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Sales"
msgstr "Ventas"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_sales_gap
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "Sales Gap"
msgstr "Diferencia Ventas"

#. module: product_margin
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_form
msgid "Standard Price"
msgstr "Precio Estándar"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_normal_cost
msgid "Sum of Multiplication of Cost price and quantity of Vendor Bills"
msgstr ""
"Suma de la Multiplicación del precio de Costo y la cantidad de las Facturas "
"de Proveedor"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_turnover
msgid ""
"Sum of Multiplication of Invoice price and quantity of Customer Invoices"
msgstr ""
"Suma de la Multiplicación del precio de Factura y la cantidad de las "
"Facturas de Cliente"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_total_cost
msgid "Sum of Multiplication of Invoice price and quantity of Vendor Bills "
msgstr ""
"Suma de la Multiplicación del precio de Factura y la cantidad de las "
"Facturas de Proveedor"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_sale_expected
msgid ""
"Sum of Multiplication of Sale Catalog price and quantity of Customer Invoices"
msgstr ""
"Suma de la Multiplicación del precio del Catálogo de Venta y la cantidad de "
"las Facturas de Cliente"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_sale_num_invoiced
msgid "Sum of Quantity in Customer Invoices"
msgstr "Suma de Cantidad en Facturas de Cliente"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_purchase_num_invoiced
msgid "Sum of Quantity in Vendor Bills"
msgstr "Suma de Cantidad de Facturas de Proveedores"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_property_account_expense_id
msgid ""
"This account will be used for invoices instead of the default one to value "
"expenses for the current product."
msgstr ""
"Esta cuenta se utilizará en facturas, en lugar de la cuenta por defecto, "
"para valorar gastos de este producto."

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_property_account_income_id
msgid ""
"This account will be used for invoices instead of the default one to value "
"sales for the current product."
msgstr ""
"Esta cuenta se utilizará en facturas, en lugar de la cuenta por defecto, "
"para valorar las ventas de este producto."

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_margin_to_date
msgid "To"
msgstr "A"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_total_cost
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "Total Cost"
msgstr "Costo Total"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_total_margin
msgid "Total Margin"
msgstr "Margen Total"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_total_margin_rate
msgid "Total Margin Rate(%)"
msgstr "Tasa de Margen Total (%)"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_total_margin_rate
msgid "Total margin * 100 / Turnover"
msgstr "Margen total * 100 / Volumen de negocio"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_turnover
#: model_terms:ir.ui.view,arch_db:product_margin.view_product_margin_tree
msgid "Turnover"
msgstr "Volumen de negocio"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_total_margin
msgid "Turnover - Standard price"
msgstr "Volumen de negocio - Precio estándar"

#. module: product_margin
#: model:ir.model.fields,field_description:product_margin.field_product_product_supplier_taxes_id
msgid "Vendor Taxes"
msgstr "Impuestos de Proveedor"

#. module: product_margin
#: model:ir.model.fields,help:product_margin.field_product_product_email_template_id
msgid ""
"When validating an invoice, an email will be sent to the customer based on "
"this template. The customer will receive an email for each product linked to "
"an email template."
msgstr ""
"Al validar una factura, un correo electrónico será enviado al cliente basado "
"en esta plantilla. El cliente recibirá un correo electrónico para cada "
"producto vinculado a una plantilla de correo electrónico."
