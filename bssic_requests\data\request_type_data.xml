<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <!-- &#x62A;&#x623;&#x643;&#x62F; &#x645;&#x646; &#x648;&#x62C;&#x648;&#x62F; &#x62C;&#x645;&#x64A;&#x639; &#x623;&#x646;&#x648;&#x627;&#x639; &#x627;&#x644;&#x637;&#x644;&#x628;&#x627;&#x62A; &#x647;&#x646;&#x627; -->
        <record id="request_type_password_reset" model="bssic.request.type">
            <field name="name">Password Reset</field>
            <field name="code">password_reset</field>
            <field name="show_password_fields" eval="True"/>
        </record>

        <record id="request_type_usb" model="bssic.request.type">
            <field name="name">USB Access</field>
            <field name="code">usb</field>
            <field name="show_usb_fields" eval="True"/>
        </record>

        <record id="request_type_extension" model="bssic.request.type">
            <field name="name">Extension Request</field>
            <field name="code">extension</field>
            <field name="show_extension_fields" eval="True"/>
        </record>

        <record id="request_type_permission" model="bssic.request.type">
            <field name="name">Permission Request</field>
            <field name="code">permission</field>
            <field name="show_permission_fields" eval="True"/>
        </record>

        <record id="request_type_email" model="bssic.request.type">
            <field name="name">Email Request</field>
            <field name="code">email</field>
            <field name="show_email_fields" eval="True"/>
        </record>

        <record id="request_type_technical" model="bssic.request.type">
            <field name="name">Technical Request</field>
            <field name="code">technical</field>
        </record>

        <record id="request_type_authorization_delegation" model="bssic.request.type">
            <field name="name">Authorization Delegation</field>
            <field name="code">authorization_delegation</field>
            <field name="show_authorization_delegation_fields" eval="True"/>
        </record>

        <record id="request_type_free_entry" model="bssic.request.type">
            <field name="name">Free Form</field>
            <field name="code">free_entry</field>
            <field name="show_free_entry_fields" eval="True"/>
        </record>
    </data>
</odoo>