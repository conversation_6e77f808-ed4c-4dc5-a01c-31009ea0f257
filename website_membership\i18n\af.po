# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_membership
#
# Translators:
# <PERSON> <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2015-11-11 08:48+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Afrikaans (http://www.transifex.com/odoo/odoo-9/language/"
"af/)\n"
"Language: af\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "All"
msgstr "Almal"

#. module: website_membership
#: code:addons/website_membership/controllers/main.py:88
#, python-format
msgid "All Countries"
msgstr "Alle Lande"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Associations"
msgstr "Assosiasies"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Find a business partner"
msgstr "Vind 'n sakevennoot"

#. module: website_membership
#: code:addons/website_membership/controllers/main.py:130
#, python-format
msgid "Free Members"
msgstr "Gratis Lede"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_country
msgid "Location"
msgstr "Plek"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.footer_custom
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Members"
msgstr "Lede"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "No result found."
msgstr "Geen resultate is gevind nie."

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Our Members Directory"
msgstr "Ons Ledegids"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_google_map
msgid "World Map"
msgstr "Wêreldkaart"

#. module: website_membership
#: model:ir.model,name:website_membership.model_membership_membership_line
msgid "membership.membership_line"
msgstr "membership.membership_line"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "pull-left"
msgstr "pull-left"
