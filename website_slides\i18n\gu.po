# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_slides
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 06:49+0000\n"
"PO-Revision-Date: 2018-10-08 06:49+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Gujarati (https://www.transifex.com/odoo/teams/41243/gu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: gu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__count_views
msgid "# Views"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__embed_views
msgid "# of Embedded Views"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_views
msgid "# of Website Views"
msgstr ""

#. module: website_slides
#: model:mail.template,subject:website_slides.slide_template_shared
msgid "${user.name} shared a ${object.slide_type} with you!"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid ""
"(Un)archiving a channel automatically (un)archives its slides. Do you want "
"to proceed?"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_search
msgid ". Please try again with different keywords."
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid ""
"<b class=\"oe_slide_js_embed_option_link\" data-slide-option-"
"id=\"#slide_email\"><i class=\"fa fa-envelope\"/> Email</b>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid ""
"<b class=\"oe_slide_js_embed_option_link\" data-slide-option-"
"id=\"#slide_embed\"><i class=\"fa fa-code\"/> Embed</b>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid ""
"<b class=\"oe_slide_js_embed_option_link\" data-slide-option-"
"id=\"#slide_share\"><i class=\"fa fa-share-alt\"/> Share</b>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.home
msgid "<b>Share:</b>"
msgstr ""

#. module: website_slides
#: model:mail.template,body_html:website_slides.slide_template_shared
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hello<br/><br/>\n"
"        ${user.name} shared the ${object.slide_type} <strong>${object.name}</strong> with you!\n"
"        <div style=\"margin: 16px 8px 16px 8px; text-align: center;\">\n"
"            <a href=\"${object.website_url}\">\n"
"                <img alt=\"${object.name}\" src=\"${ctx['base_url']}/web/image/slide.slide/${object.id}/image\" style=\"height:auto; width:150px; margin: 16px;\"/>\n"
"            </a>\n"
"        </div>\n"
"        <div style=\"margin: 16px 8px 16px 8px; text-align: center;\">\n"
"            <a href=\"${object.website_url}\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">View <strong>${object.name}</strong></a>\n"
"        </div>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""

#. module: website_slides
#: model:mail.template,body_html:website_slides.slide_template_published
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hello<br/><br/>\n"
"        A new ${object.slide_type} <strong>${object.name}</strong> has been published on ${object.channel_id.name} at ${format_tz(object.write_date, tz=user.tz)}\n"
"        <div style=\"margin: 16px 8px 16px 8px; text-align: center;\">\n"
"            <a href=\"${object.website_url}\">\n"
"                <img alt=\"${object.name}\" src=\"${ctx['base_url']}/web/image/slide.slide/${object.id}/image\" style=\"height:auto; width:150px; margin: 16px;\"/>\n"
"            </a>\n"
"        </div>\n"
"        <div style=\"margin: 16px 8px 16px 8px; text-align: center;\">\n"
"            <a href=\"${object.website_url}\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">View <strong>${object.name}</strong></a>\n"
"        </div>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "<i class=\"fa fa-align-justify\"/> Transcript"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.home
msgid "<i class=\"fa fa-arrow-right\"/> See all"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "<i class=\"fa fa-bar-chart\"/> Statistics"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "<i class=\"fa fa-circle-o\"/> Website Views"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.home
#: model_terms:ir.ui.view,arch_db:website_slides.slides_channel_header
msgid "<i class=\"fa fa-cloud-upload\"/> Upload"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "<i class=\"fa fa-code\"/> Embedded Views"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "<i class=\"fa fa-comments-o\"/> Comments"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "<i class=\"fa fa-envelope-o\"/> Send Email"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide_forbidden
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid ""
"<i class=\"fa fa-exclamation-triangle\" role=\"img\" aria-"
"label=\"Attention\" title=\"Attention\"/> This"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "<i class=\"fa fa-home\"/> About"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_channel_header
msgid "<i class=\"fa fa-home\"/> Home"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid ""
"<i class=\"fa fa-info-circle\"/>\n"
"                                        The social sharing module will be unlocked when a moderator will allow your publication."
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "<i class=\"fa fa-play\"/> Total Views"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "<i class=\"fa fa-share-alt\"/> Share"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "<i class=\"fa fa-spinner fa-spin\"/> Loading ..."
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "<i class=\"fa fa-thumbs-down\"/> Dislikes"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "<i class=\"fa fa-thumbs-up\"/> Likes"
msgstr ""

#. module: website_slides
#: code:addons/website_slides/models/slides.py:107
#, python-format
msgid ""
"<p>This channel is private and its content is restricted to some users.</p>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid ""
"<span class=\"badge badge-pill\" id=\"facebook-badge\">0</span>\n"
"                                                    <i class=\"fa fa-facebook-square\"/> Facebook"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid ""
"<span class=\"badge badge-pill\" id=\"google-badge\">0</span>\n"
"                                                    <i class=\"fa fa-google-plus-square\"/> Google+"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid ""
"<span class=\"badge badge-pill\" id=\"linkedin-badge\">0</span>\n"
"                                                    <i class=\"fa fa-linkedin-square\"/> LinkedIn"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid ""
"<span class=\"badge badge-pill\" id=\"total-share\">0</span>\n"
"                                                    <i class=\"fa fa-share-alt\"/> Social Shares"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid ""
"<span class=\"badge badge-pill\" id=\"twitter-badge\">0</span>\n"
"                                                    <i class=\"fa fa-twitter-square\"/> Twitter"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-arrow-right\"/>\n"
"                                    Create a Google Project and Get a Key"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "<span class=\"form-text\">Send presentation through email</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_media
msgid ""
"<span class=\"form-text\">Use permanent link to share in social media</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Slides</span>\n"
"                        <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "<span class=\"text-muted small\">views</span>"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_channel_header
msgid "<span class=\"text-muted\">Sort by:</span>"
msgstr ""

#. module: website_slides
#: sql_constraint:slide.tag:0
msgid "A tag must be unique!"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "API Key"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_needaction
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_needaction
msgid "Action Needed"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "Actions"
msgstr "ક્રિયાઓ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__active
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__active
msgid "Active"
msgstr "સક્રિય"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.action_slides_slides
msgid "Add a new slide"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Archived"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_attachment_count
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: website_slides
#: selection:slide.slide,download_security:0
msgid "Authenticated Users Only"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_5
msgid "Awesome Timesheet by Odoo"
msgstr ""

#. module: website_slides
#: model:slide.tag,name:website_slides.tag1
msgid "CMS"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_see
msgid "Can See"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_upload
msgid "Can Upload"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/website_slides.editor.js:55
#, python-format
msgid "Cancel"
msgstr "રદ કરો"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.action_ir_slide_category
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__category_ids
#: model:ir.ui.menu,name:website_slides.menu_action_ir_slide_category_global
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Categories"
msgstr "શ્રેણીઓ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:254
#: code:addons/website_slides/static/src/xml/website_slides.xml:54
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__category_id
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slides_category_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slides_category_tree
#, python-format
msgid "Category"
msgstr "વર્ગ"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides.xml:81
#: model:ir.model.fields,field_description:website_slides.field_slide_category__channel_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__channel_id
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_tree
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
#, python-format
msgid "Channel"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "Channel #{slide.channel_id.name}"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__group_ids
msgid "Channel Groups"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.home
msgid "Channel Settings"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:174
#, python-format
msgid ""
"Channel contains the given title, please change before Save or Publish."
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Channel visibility"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.channels
msgid "Channel without promoted slide"
msgstr ""

#. module: website_slides
#: model:ir.actions.act_url,name:website_slides.action_open_channels
#: model:ir.actions.act_window,name:website_slides.action_slide_channels
#: model:ir.ui.menu,name:website_slides.menu_action_slide_channels_global
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Channels"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_10
msgid "Company Culture"
msgstr ""

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__datas
msgid "Content"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides.xml:20
#, python-format
msgid "Content Preview"
msgstr ""

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:264
#, python-format
msgid ""
"Could not fetch data from url. Document or access right not available.\n"
"Here is the received response: %s"
msgstr ""

#. module: website_slides
#: code:addons/website_slides/models/slides.py:330
#, python-format
msgid ""
"Could not fetch data from url. Document or access right not available:\n"
"%s"
msgstr ""

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.action_slide_channels
msgid "Create a channel"
msgstr ""

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.action_ir_slide_category
msgid "Create a new category"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:223
#, python-format
msgid "Create new tag '%s'"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_category__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__create_uid
msgid "Created by"
msgstr "બનાવનાર"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_category__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__create_date
msgid "Created on"
msgstr ""

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.channel_public
msgid ""
"Default channel for slides, all public users can access content of this "
"channel."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides.xml:60
#: code:addons/website_slides/static/src/xml/website_slides.xml:62
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__description
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__description
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
#, python-format
msgid "Description"
msgstr "વર્ણન"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides.xml:71
#, python-format
msgid "Discard"
msgstr ""

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_6
msgid "Discover how easy marketing your business can be with Odoo"
msgstr ""

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_0
msgid ""
"Discover how you can integrate MRP, PLM, Quality and Maintenance in a single application to maximize the efficiency of your manufacturing operations.\n"
"\n"
"Read more about the features at: https://www.odoo.com/page/manufacturing-features\n"
"\n"
"Discover Odoo MRP: https://www.odoo.com/page/manufacturing"
msgstr ""

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_1
msgid "Discover the CRM sales people love https://www.odoo.com/page/crm"
msgstr ""

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_2
msgid ""
"Discover the all-in-one business management software solution that fits any business size and use case.\n"
"\n"
"Learn more at https://odoo.com"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__dislikes
msgid "Dislikes"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_category__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__display_name
msgid "Display Name"
msgstr "પ્રદર્શન નામ"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_category__sequence
#: model:ir.model.fields,help:website_slides.field_slide_channel__sequence
msgid "Display order"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
#: selection:slide.slide,slide_type:0
msgid "Document"
msgstr "દસ્તાવેજ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__document_id
msgid "Document ID"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__url
msgid "Document URL"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_channel_header
msgid "Documents"
msgstr "દસ્તાવેજો"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "Download"
msgstr "ડાઉનલોડ"

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_5
msgid ""
"Download Awesome Timesheet by Odoo and discover how easy time tracking and employee management can be!\n"
"\n"
"Chrome: http://bit.ly/2613LcY\n"
"iOS: http://bit.ly/1ZUZsZD"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__download_security
msgid "Download Security"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__publish_template_id
msgid "Email template to send slide publication through email"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__share_template_id
msgid "Email template used when sharing a slide"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__embed_code
msgid "Embed Code"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__embedcount_ids
msgid "Embed Count"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_embed
msgid "Embed in your website"
msgstr ""

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_embed
msgid "Embedded Slides View Counter"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "Embeds"
msgstr ""

#. module: website_slides
#: model:slide.channel,name:website_slides.channel_private
msgid "Employee Channel"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__access_error_msg
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Error Message"
msgstr ""

#. module: website_slides
#: selection:slide.slide,download_security:0
msgid "Everyone"
msgstr "દરેક વ્યક્તિને"

#. module: website_slides
#: selection:slide.channel,promote_strategy:0
msgid "Featured Presentation"
msgstr ""

#. module: website_slides
#: model:slide.category,name:website_slides.category_1
msgid "Featured Presentations"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__promoted_slide_id
msgid "Featured Slide"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__promote_strategy
msgid "Featuring Policy"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/controllers/main.py:274
#: code:addons/website_slides/static/src/js/slides_upload.js:91
#, python-format
msgid "File is too big. File size cannot exceed 25MB"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_follower_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_follower_ids
msgid "Followers"
msgstr "અનુયાયીઓ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_channel_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_channel_ids
msgid "Followers (Channels)"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_partner_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_see_full
msgid "Full Access"
msgstr "પૂર્ણ વપરાશ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "General"
msgstr "જનરલ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__website_slide_google_app_key
#: model:ir.model.fields,field_description:website_slides.field_website__website_slide_google_app_key
msgid "Google Doc Key"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Google Drive API Key"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_graph
msgid "Graph of Slides"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Group By"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__group_ids
msgid "Groups allowed to see presentations in this channel"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__upload_group_ids
msgid ""
"Groups allowed to upload presentations in this channel. If void, every user "
"can upload."
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_category__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__id
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__id
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__id
msgid "ID"
msgstr "ઓળખ"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_unread
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_unread
msgid "If checked new messages require your attention."
msgstr "જો ચેક કરેલા નવા સંદેશા માટે તમારું ધ્યાન આવશ્યક છે"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_needaction
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_needaction
msgid "If checked, new messages require your attention."
msgstr "જો ચેક કરેલું હોય, તો નવા સંદેશા માટે તમારું ધ્યાન આવશ્યક છે."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_has_error
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "જો ચેક કરેલું હોય, તો કેટલાક મેસેજીસમાં ડિલિવરીની ભૂલ છે."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image
msgid "Image"
msgstr "ચિત્ર"

#. module: website_slides
#: selection:slide.slide,slide_type:0
msgid "Infographic"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_channel_header
msgid "Infographics"
msgstr ""

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:296
#, python-format
msgid ""
"Internal server error, please try again later or contact administrator.\n"
"Here is the error message: %s"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:86
#, python-format
msgid "Invalid file type. Please select pdf or image file"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_is_follower
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__is_published
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_published
msgid "Is published"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_category____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_channel____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_embed____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_slide____last_update
#: model:ir.model.fields,field_description:website_slides.field_slide_tag____last_update
msgid "Last Modified on"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_category__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__write_uid
msgid "Last Updated by"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_category__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__write_date
msgid "Last Updated on"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "Last slide"
msgstr ""

#. module: website_slides
#: selection:slide.channel,promote_strategy:0
msgid "Latest Published"
msgstr ""

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_8
msgid "Learn more at https://www.odoo.com/page/sales"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__likes
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "Likes"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_main_attachment_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: website_slides
#: model:slide.tag,name:website_slides.tag2
msgid "Marketing"
msgstr "પ્રચાર"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_medium
msgid "Medium"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_has_error
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__access_error_msg
msgid "Message to display when not accessible due to access rights"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_ids
msgid "Messages"
msgstr "સંદેશાઓ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__mime_type
msgid "Mime-type"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
#: model_terms:ir.ui.view,arch_db:website_slides.slides_channel_header
#: selection:slide.channel,promote_strategy:0
msgid "Most Viewed"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_channel_header
#: selection:slide.channel,promote_strategy:0
msgid "Most Voted"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_category__name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__name
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__name
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Name"
msgstr "નામ"

#. module: website_slides
#: model:mail.template,subject:website_slides.slide_template_published
msgid "New ${object.slide_type} published on ${object.channel_id.name}"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/website_slides.editor.js:39
#, python-format
msgid "New slide"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_channel_header
msgid "Newest"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "Next slide"
msgstr ""

#. module: website_slides
#: selection:slide.channel,promote_strategy:0
msgid "No Featured Presentation"
msgstr ""

#. module: website_slides
#: selection:slide.slide,download_security:0
msgid "No One"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.channel_not_found
msgid "No channel created or published yet."
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "No presentation available."
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.home
msgid "No presentation published yet."
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_needaction_counter
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_category__nbr_documents
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_documents
msgid "Number of Documents"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_category__nbr_infographics
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_infographics
msgid "Number of Infographics"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_category__nbr_presentations
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_presentations
msgid "Number of Presentations"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_category__nbr_videos
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_videos
msgid "Number of Videos"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_has_error_counter
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_has_error_counter
msgid "Number of error"
msgstr "ભૂલની સંખ્યા"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_needaction_counter
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "સંદેશાઓની સંખ્યા જે ક્રિયા માટે જરૂરી છે"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_has_error_counter
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "વિતરણ ભૂલ સાથે સંદેશાઓની સંખ્યા"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_unread_counter
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_unread_counter
msgid "Number of unread messages"
msgstr "ન વાંચેલા સંદેશાની સંખ્યા"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_2
msgid "Odoo All-in-One Software Demonstration"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_1
msgid "Odoo CRM"
msgstr ""

#. module: website_slides
#: model:slide.category,name:website_slides.category_2
#: model:slide.tag,name:website_slides.tag5
msgid "Odoo Days"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_7
msgid "Odoo Inventory"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_0
msgid "Odoo MRP"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_6
msgid "Odoo Marketing"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_8
msgid "Odoo Sales"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_3
msgid "Odoo Sign Demonstration"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/website_slides.editor.js:40
#, python-format
msgid "On which channel do you want to add a slide?"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_9
msgid "Open Days 2014 Infographic"
msgstr ""

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_11
msgid "OpenSource CMS, A performance comparison - Mantavya Gajjar"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides.xml:28
#, python-format
msgid "PDF or Image File"
msgstr ""

#. module: website_slides
#: model:slide.channel,name:website_slides.channel_partial
msgid "Partner Channel"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "Please"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides.js:56
#, python-format
msgid "Please <a href=\"/web?redirect=%s\">login</a> to vote this slide"
msgstr ""

#. module: website_slides
#: code:addons/website_slides/models/slides.py:333
#: code:addons/website_slides/models/slides.py:562
#, python-format
msgid "Please enter valid Youtube or Google Doc URL"
msgstr ""

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:256
#, python-format
msgid "Please enter valid youtube or google doc url"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__slide_id
#: selection:slide.slide,slide_type:0
msgid "Presentation"
msgstr ""

#. module: website_slides
#: model:mail.message.subtype,description:website_slides.mt_channel_slide_published
#: model:mail.message.subtype,name:website_slides.mt_channel_slide_published
msgid "Presentation Published"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_channel_header
#: model:website.menu,name:website_slides.website_menu_slides
msgid "Presentations"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "Previous slide"
msgstr ""

#. module: website_slides
#: selection:slide.channel,visibility:0
msgid "Private"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.channels
msgid "Private channel"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_grid_view
msgid "Promote this #{slide.slide_type}"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.channels
#: model_terms:ir.ui.view,arch_db:website_slides.home
msgid "Promoted slide"
msgstr ""

#. module: website_slides
#: selection:slide.channel,visibility:0
msgid "Public"
msgstr ""

#. module: website_slides
#: model:slide.channel,name:website_slides.channel_public
msgid "Public Channel"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__date_published
msgid "Publish Date"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Published"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__publish_template_id
msgid "Published Template"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "Related"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__website_id
#: model:ir.model.fields,help:website_slides.field_slide_slide__website_id
msgid "Restrict publishing to this website."
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.home
msgid "Results for"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.home
msgid ""
"Review your channel settings to promote your most viewed or recent published"
" presentation"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__is_seo_optimized
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_seo_optimized
msgid "SEO optimized"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides.xml:70
#, python-format
msgid "Save and Publish"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides.xml:69
#, python-format
msgid "Save as Draft"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_channel_header
msgid "Search"
msgstr "શોધ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Search Slides"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_channel_header
msgid "Search in #{channel.name}"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Security"
msgstr "સુરક્ષા"

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/website_slides.editor.js:44
#, python-format
msgid "Select"
msgstr "પસંદ કરો"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.channels
msgid "Select a Channel"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_embed
msgid "Select page to start with"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_category__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__sequence
msgid "Sequence"
msgstr "ક્રમ"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_media
msgid "Share Link"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "Share count"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_share
msgid "Share on Facebook"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_share
msgid "Share on Google Plus"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_share
msgid "Share on LinkedIn"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_media
msgid "Share on Social Networks"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_share
msgid "Share on Twitter"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "Share with a friend"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__share_template_id
msgid "Shared Template"
msgstr ""

#. module: website_slides
#: selection:slide.channel,visibility:0
msgid "Show channel but restrict presentations"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Slide"
msgstr ""

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel
msgid "Slide Channel"
msgstr ""

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_tag
msgid "Slide Tag"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "Slide image"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__custom_slide_id
msgid "Slide to Promote"
msgstr ""

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.action_slides_slides
#: model:ir.model,name:website_slides.model_slide_slide
#: model:ir.model.fields,field_description:website_slides.field_slide_category__slide_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_ids
#: model:ir.ui.menu,name:website_slides.menu_website_slides_root
#: model:ir.ui.menu,name:website_slides.menu_website_slides_root_global
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_tree
msgid "Slides"
msgstr ""

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_category
msgid "Slides Category"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_search
msgid "Sorry, but nothing matches your search criteria"
msgstr ""

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_3
msgid ""
"Stop doing manual printing and scanning. Move to electronic signature with Odoo Sign!\n"
"\n"
"Discover more at https://www.odoo.com/page/sign"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_tag_form
msgid "Tag"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:277
#: code:addons/website_slides/static/src/xml/website_slides.xml:48
#: model:ir.actions.act_window,name:website_slides.action_slide_tag
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__tag_ids
#: model:ir.ui.menu,name:website_slides.menu_slide_tag
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_tag_tree
#, python-format
msgid "Tags"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Tags..."
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.home
msgid "The"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__slide_type
msgid ""
"The document type will be set automatically based on the document URL and "
"properties (e.g. height and width for presentation and document)."
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__website_url
#: model:ir.model.fields,help:website_slides.field_slide_slide__website_url
msgid "The full URL to access the document through the website."
msgstr ""

#. module: website_slides
#: sql_constraint:slide.slide:0
msgid "The slide name must be unique within a channel"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__url
msgid "Third Party Website URL"
msgstr ""

#. module: website_slides
#: model_terms:slide.channel,access_error_msg:website_slides.channel_partial
#: model_terms:slide.channel,access_error_msg:website_slides.channel_private
#: model_terms:slide.channel,access_error_msg:website_slides.channel_public
msgid "This channel is private and its content is restricted to some users."
msgstr ""

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:260
#, python-format
msgid ""
"This video already exists in this channel <a target=\"_blank\" "
"href=\"/slides/slide/%s\">click here to view it </a>"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_thumb
msgid "Thumbnail"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides.xml:42
#: code:addons/website_slides/static/src/xml/website_slides.xml:44
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__name
#, python-format
msgid "Title"
msgstr "શીર્ષક"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_category__total
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total
msgid "Total"
msgstr "કુલ"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__total_views
msgid "Total # Views"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__index_content
msgid "Transcript"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_type
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Type"
msgstr "પ્રકાર"

#. module: website_slides
#: code:addons/website_slides/controllers/main.py:156
#, python-format
msgid "Uncategorized"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.home
msgid "Uncategorized presentation"
msgstr ""

#. module: website_slides
#: code:addons/website_slides/models/slides.py:551
#, python-format
msgid "Unknown document"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_grid_view
msgid "Unpublished"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_unread
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_unread
msgid "Unread Messages"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_unread_counter
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__upload_group_ids
msgid "Upload Groups"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.home
msgid ""
"Upload PDF presentations, documents, videos or infographic using the button "
"below."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides.xml:8
#: model_terms:ir.ui.view,arch_db:website_slides.home
#: model_terms:ir.ui.view,arch_db:website_slides.slides_channel_header
#, python-format
msgid "Upload Presentation"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides.xml:12
#, python-format
msgid "Uploading presentation..."
msgstr ""

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.channel_private
msgid "Used to publish internal slides of company."
msgstr ""

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.channel_partial
msgid "Used to publish slides in partner network privately."
msgstr ""

#. module: website_slides
#: selection:slide.slide,slide_type:0
msgid "Video"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_channel_header
msgid "Videos"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
#: model_terms:ir.ui.view,arch_db:website_slides.slides_grid_view
msgid "Views"
msgstr "દેખાવો"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.related_slides
msgid "Views ."
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__visibility
msgid "Visibility"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_published
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_published
msgid "Visible on current website"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Waiting for validation"
msgstr ""

#. module: website_slides
#: model:slide.slide,description:website_slides.slide_7
msgid ""
"Warehouse management software for the 21st century.\n"
"\n"
"Discover Odoo Inventory: https://www.odoo.com/page/warehouse"
msgstr ""

#. module: website_slides
#: model:ir.model,name:website_slides.model_website
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_id
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Website"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_message_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_url
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_url
msgid "Website URL"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__website_message_ids
#: model:ir.model.fields,help:website_slides.field_slide_slide__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_description
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_description
msgid "Website meta description"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_keywords
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_keywords
msgid "Website meta keywords"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_title
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_title
msgid "Website meta title"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_og_img
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_og_img
msgid "Website opengraph image"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides_upload.js:116
#, python-format
msgid "You can not upload password protected file."
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/js/slides.js:65
#, python-format
msgid "You have already voted for this slide"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides.xml:36
#, python-format
msgid "Youtube Video URL"
msgstr ""

#. module: website_slides
#. openerp-web
#: code:addons/website_slides/static/src/xml/website_slides.xml:34
#, python-format
msgid "Youtube or Google Doc URL"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__document_id
msgid "Youtube or Google Document ID"
msgstr ""

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__url
msgid "Youtube or Google Document URL"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "by email!"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.home
msgid "is empty."
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_detail_view
msgid "is private"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide_forbidden
msgid "is private."
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "login"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_search
msgid "results found for the given criteria"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.related_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_grid_view
msgid "slide.name"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "suggest_slide.name"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "to send this"
msgstr ""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "<EMAIL>"
msgstr ""
