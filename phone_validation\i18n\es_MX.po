# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* phone_validation
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON>, 2022
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: phone_validation
#: code:addons/phone_validation/models/phone_blacklist.py:0
#: code:addons/phone_validation/models/phone_blacklist.py:0
#, python-format
msgid " Please correct the number and try again."
msgstr "Modifique el número e inténtelo de nuevo."

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_needaction
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_needaction
msgid "Action Needed"
msgstr "Acción requerida"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__active
msgid "Active"
msgstr "Activo"

#. module: phone_validation
#: model_terms:ir.actions.act_window,help:phone_validation.phone_blacklist_action
msgid "Add a phone number in the blacklist"
msgstr "Agregue un número de teléfono a la lista negra"

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_form
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_search
msgid "Archived"
msgstr "Archivado"

#. module: phone_validation
#: code:addons/phone_validation/models/phone_blacklist.py:0
#, python-format
msgid "Are you sure you want to unblacklist this Phone Number?"
msgstr ""
"¿Está seguro de que desea eliminar este número de teléfono de la lista de "
"exclusión?"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_attachment_count
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_attachment_count
msgid "Attachment Count"
msgstr "Número de archivos adjuntos"

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_form
msgid "Blacklist"
msgstr "Lista negra"

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_tree
msgid "Blacklist Date"
msgstr "Fecha de la lista negra"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__mobile_blacklisted
msgid "Blacklisted Phone Is Mobile"
msgstr "El teléfono de la lista negra es un celular"

#. module: phone_validation
#: model:ir.actions.act_window,name:phone_validation.phone_blacklist_action
msgid "Blacklisted Phone Numbers"
msgstr "Números de teléfono de la lista negra"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__phone_blacklisted
msgid "Blacklisted Phone is Phone"
msgstr "El teléfono de la lista negra es un teléfono fijo"

#. module: phone_validation
#: model_terms:ir.actions.act_window,help:phone_validation.phone_blacklist_action
msgid "Blacklisted phone numbers won't receive SMS Mailings anymore."
msgstr ""
"Los números de teléfono incluidos en la lista negra no recibirán más SMS."

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_remove_view_form
msgid "Confirm"
msgstr "Confirmar"

#. module: phone_validation
#: model:ir.model,name:phone_validation.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__create_uid
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__create_date
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__create_date
msgid "Created on"
msgstr "Creado el"

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_remove_view_form
msgid "Discard"
msgstr "Descartar"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__display_name
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__phone_sanitized
msgid ""
"Field used to store sanitized phone number. Helps speeding up searches and "
"comparisons."
msgstr ""
"Campo utilizado para almacenar el número de teléfono válido. Ayuda a "
"acelerar las búsquedas y las comparaciones."

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_follower_ids
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_partner_ids
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (partners)"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__has_message
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__has_message
msgid "Has Message"
msgstr "Tiene un mensaje"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__id
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__id
msgid "ID"
msgstr "ID"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__message_needaction
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__message_unread
#: model:ir.model.fields,help:phone_validation.field_phone_blacklist__message_needaction
#: model:ir.model.fields,help:phone_validation.field_phone_blacklist__message_unread
msgid "If checked, new messages require your attention."
msgstr ""
"Si se encuentra seleccionado, significa que hay nuevos mensajes que "
"requieren su atención."

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__message_has_error
#: model:ir.model.fields,help:phone_validation.field_phone_blacklist__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""
"Si se encuentra seleccionado, algunos mensajes presentan un error de envío."

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__phone_sanitized_blacklisted
msgid ""
"If the sanitized phone number is on the blacklist, the contact won't receive"
" mass mailing sms anymore, from any list"
msgstr ""
"Si el teléfono está en la lista negra, el contacto ya no recibirá envío "
"masivo de sms de ninguna lista."

#. module: phone_validation
#: code:addons/phone_validation/tools/phone_validation.py:0
#, python-format
msgid "Impossible number %s: probably invalid number of digits."
msgstr "Número incorrecto %s: número de dígitos no válido."

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__mobile_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a mobile number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"Indica si el número de teléfono depurado de la lista negra es un número "
"celular. Sirve para distinguir qué número está en la lista negra cuando hay "
"un campo de celular y otro de teléfono fijo en un modelo."

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__phone_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a phone number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"Indica si el número de teléfono de la lista negra es un teléfono fijo. Sirve"
" para distinguir qué número está en la lista negra cuando hay un campo de "
"celular y otro de teléfono fijo en un modelo."

#. module: phone_validation
#: code:addons/phone_validation/tools/phone_validation.py:0
#, python-format
msgid "Invalid number %s: probably incorrect prefix."
msgstr "Número incorrecto %s: prefijo no válido."

#. module: phone_validation
#: code:addons/phone_validation/models/mail_thread_phone.py:0
#: code:addons/phone_validation/models/mail_thread_phone.py:0
#, python-format
msgid "Invalid primary phone field on model %s"
msgstr "Campo de teléfono principal no válido en el modelo %s"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_is_follower
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist____last_update
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove____last_update
msgid "Last Modified on"
msgstr "Modificado por última vez el"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__write_uid
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__write_date
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_main_attachment_id
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_main_attachment_id
msgid "Main Attachment"
msgstr "Archivos adjuntos principales"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_has_error
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_has_error
msgid "Message Delivery error"
msgstr "Error en la entrega del mensaje"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_ids
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: phone_validation
#: code:addons/phone_validation/models/mail_thread_phone.py:0
#, python-format
msgid "Missing definition of phone fields."
msgstr "Falta la definición de los campos de teléfono."

#. module: phone_validation
#: model:ir.model.constraint,message:phone_validation.constraint_phone_blacklist_unique_number
msgid "Number already exists"
msgstr "El número ya existe"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_needaction_counter
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_has_error_counter
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_has_error_counter
msgid "Number of errors"
msgstr "Número de errores"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__message_needaction_counter
#: model:ir.model.fields,help:phone_validation.field_phone_blacklist__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Número de mensajes que requieren una acción"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__message_has_error_counter
#: model:ir.model.fields,help:phone_validation.field_phone_blacklist__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de entrega"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__message_unread_counter
#: model:ir.model.fields,help:phone_validation.field_phone_blacklist__message_unread_counter
msgid "Number of unread messages"
msgstr "Número de mensajes sin leer"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_phone_blacklist__number
msgid "Number should be E164 formatted"
msgstr "El número debe tener formato E164"

#. module: phone_validation
#: model:ir.ui.menu,name:phone_validation.phone_menu_main
msgid "Phone / SMS"
msgstr "Teléfono/SMS"

#. module: phone_validation
#: model:ir.model,name:phone_validation.model_phone_blacklist
#: model:ir.ui.menu,name:phone_validation.phone_blacklist_menu
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_form
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_tree
msgid "Phone Blacklist"
msgstr "Lista negra de números telefónicos"

#. module: phone_validation
#: model:ir.model,name:phone_validation.model_mail_thread_phone
msgid "Phone Blacklist Mixin"
msgstr "Mixin de lista negra de teléfonos"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__phone_sanitized_blacklisted
msgid "Phone Blacklisted"
msgstr "Teléfono en la lista negra"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__number
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__phone
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_remove_view_form
msgid "Phone Number"
msgstr "Número de teléfono"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__phone_mobile_search
msgid "Phone/Mobile"
msgstr "Teléfono/Celular"

#. module: phone_validation
#: code:addons/phone_validation/models/mail_thread_phone.py:0
#, python-format
msgid ""
"Please enter at least 3 characters when searching a Phone/Mobile number."
msgstr ""
"Introduzca al menos 3 caracteres cuando busque un número de "
"teléfono/celular."

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__reason
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_remove_view_form
msgid "Reason"
msgstr "Motivo"

#. module: phone_validation
#: model:ir.model,name:phone_validation.model_phone_blacklist_remove
msgid "Remove phone from blacklist"
msgstr "Eliminar teléfono de la lista negra"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__phone_sanitized
msgid "Sanitized Number"
msgstr "Número depurado"

#. module: phone_validation
#: code:addons/phone_validation/tools/phone_validation.py:0
#, python-format
msgid "Unable to parse %(phone)s: %(error)s"
msgstr "No se puede analizar %(phone)s: %(error)s"

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_form
msgid "Unblacklist"
msgstr "Eliminar de la lista negra"

#. module: phone_validation
#: code:addons/phone_validation/models/phone_blacklist.py:0
#, python-format
msgid "Unblacklisting Reason: %s"
msgstr "Motivo de la eliminación de la lista negra: %s"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_unread
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_unread
msgid "Unread Messages"
msgstr "Mensajes sin leer"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_unread_counter
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Número de mensajes sin leer"

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_remove_view_form
msgid "phone_blacklist_removal"
msgstr "phone_blacklist_removal"
