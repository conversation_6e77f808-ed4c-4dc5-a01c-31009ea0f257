<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="tax2" model="account.tax.template">
            <field name="chart_template_id" ref="no_chart_template"/>
            <field name="name">1 Inngående mva høy sats 25%</field>
            <field name="sequence">0</field>
            <field name="description">Fradrag for inngående mva</field>
            <field name="amount">25</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_25"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2711'),
                    'plus_report_line_ids': [ref('tax_report_line_code_1')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2711'),
                    'minus_report_line_ids': [ref('tax_report_line_code_1')],
                }),
            ]"/>
        </record>

       <record id="tax1" model="account.tax.template">
            <field name="chart_template_id" ref="no_chart_template"/>
            <field name="name">0 Ingen mvabehandling 0%</field>
            <field name="description">Ingen mvabehandling(anskaffelser)</field>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_0"/>
            <field name="active" eval="False"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="tax3" model="account.tax.template">
            <field name="chart_template_id" ref="no_chart_template"/>
            <field name="name">3 Utgående mva høy sats 25%</field>
            <field name="description">Utgående mva</field>
            <field name="amount">25</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_25"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_code_3')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2701'),
                    'plus_report_line_ids': [ref('tax_report_line_code_3_tax')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_code_3')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2701'),
                    'minus_report_line_ids': [ref('tax_report_line_code_3_tax')],
                }),
            ]"/>
        </record>

        <record id="tax4" model="account.tax.template">
            <field name="chart_template_id" ref="no_chart_template"/>
            <field name="name">5 Mvafritt salg 0%</field>
            <field name="description">Mvafritt salg</field>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_code_5')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_code_5')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="tax5" model="account.tax.template">
            <field name="chart_template_id" ref="no_chart_template"/>
            <field name="name">6 Omsetning utenfor mvaloven 0%</field>
            <field name="description">Omsetning utenfor merverdiavgiftsloven</field>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_code_6')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_code_6')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="tax6" model="account.tax.template">
            <field name="chart_template_id" ref="no_chart_template"/>
            <field name="name">7 Ingen mvabehandling(inntekter) 0%</field>
            <field name="description">Ingen mvabehandling(inntekter)</field>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="tax7" model="account.tax.template">
            <field name="chart_template_id" ref="no_chart_template"/>
            <field name="name">11 Inngående mva middel sats 15%</field>
            <field name="description">Fradrag for inngående mva</field>
            <field name="amount">15</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_15"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2713'),
                    'plus_report_line_ids': [ref('tax_report_line_code_11')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2713'),
                    'minus_report_line_ids': [ref('tax_report_line_code_11')],
                }),
            ]"/>
        </record>

        <record id="tax8" model="account.tax.template">
            <field name="chart_template_id" ref="no_chart_template"/>
            <field name="name">12 Inngående mva råfisk 11%</field>
            <field name="description">Fradrag for inngående mva</field>
            <field name="amount">11.11</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_15"/>
            <field name="active" eval="False"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2710'),
                    'plus_report_line_ids': [ref('tax_report_line_code_12')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2710'),
                    'minus_report_line_ids': [ref('tax_report_line_code_12')],
                }),
            ]"/>
        </record>

        <record id="tax9" model="account.tax.template">
            <field name="chart_template_id" ref="no_chart_template"/>
            <field name="name">13 Inngående mva lav sats 12%</field>
            <field name="description">Fradrag for inngående mva</field>
            <field name="amount">12</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_12"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2714'),
                    'plus_report_line_ids': [ref('tax_report_line_code_13')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2714'),
                    'minus_report_line_ids': [ref('tax_report_line_code_13')],
                }),
            ]"/>
        </record>

        <record id="tax10" model="account.tax.template">
            <field name="chart_template_id" ref="no_chart_template"/>
            <field name="name">14 Innførselsmva høy sats 25%</field>
            <field name="description">Fradrag for innførselsmva</field>
            <field name="amount">25</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_25"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2711'),
                    'plus_report_line_ids': [ref('tax_report_line_code_14')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2711'),
                    'minus_report_line_ids': [ref('tax_report_line_code_14')],
                }),
            ]"/>
        </record>

        <record id="tax11" model="account.tax.template">
            <field name="chart_template_id" ref="no_chart_template"/>
            <field name="name">15 Innførselsmva middel sats 15%</field>
            <field name="description">Fradrag for innførselsmva</field>
            <field name="amount">15</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_15"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2713'),
                    'plus_report_line_ids': [ref('tax_report_line_code_15')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2713'),
                    'minus_report_line_ids': [ref('tax_report_line_code_15')],
                }),
            ]"/>
        </record>

        <record id="tax12" model="account.tax.template">
            <field name="chart_template_id" ref="no_chart_template"/>
            <field name="name">20 Grunnlag ved innførsel av varer nullsats 0%</field>
            <field name="description">Grunnlag ved innførsel av varer</field>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="tax13" model="account.tax.template">
            <field name="chart_template_id" ref="no_chart_template"/>
            <field name="name">21 Grunnlag ved innførsel av varer høy sats 25%</field>
            <field name="description">Grunnlag ved innførsel av varer</field>
            <field name="amount">25</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_25"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="tax14" model="account.tax.template">
            <field name="chart_template_id" ref="no_chart_template"/>
            <field name="name">22 Grunnlag ved innførsel av varer middel sats 15%</field>
            <field name="description">Grunnlag ved innførsel av varer</field>
            <field name="amount">15</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_15"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="tax15" model="account.tax.template">
            <field name="chart_template_id" ref="no_chart_template"/>
            <field name="name">31 Utgående mva middel sats 15%</field>
            <field name="description">Utgående mva</field>
            <field name="amount">15</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_15"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_code_31')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2703'),
                    'plus_report_line_ids': [ref('tax_report_line_code_31_tax')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_code_31')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2703'),
                    'minus_report_line_ids': [ref('tax_report_line_code_31_tax')],
                }),
            ]"/>
        </record>

        <record id="tax16" model="account.tax.template">
            <field name="chart_template_id" ref="no_chart_template"/>
            <field name="name">32 Utgående mva råfisk 11%</field>
            <field name="description">Utgående mva</field>
            <field name="amount">11.11</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_15"/>
            <field name="active" eval="False"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_code_32')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2700'),
                    'plus_report_line_ids': [ref('tax_report_line_code_32_tax')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_code_32')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2700'),
                    'minus_report_line_ids': [ref('tax_report_line_code_32_tax')],
                }),
            ]"/>
        </record>

        <record id="tax17" model="account.tax.template">
            <field name="chart_template_id" ref="no_chart_template"/>
            <field name="name">33 Utgående mva lav sats 12%</field>
            <field name="description">Utgående mva</field>
            <field name="amount">12</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_12"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_code_33')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2704'),
                    'plus_report_line_ids': [ref('tax_report_line_code_33_tax')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_code_33')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2704'),
                    'minus_report_line_ids': [ref('tax_report_line_code_33_tax')],
                }),
            ]"/>
        </record>

        <record id="tax18" model="account.tax.template">
            <field name="chart_template_id" ref="no_chart_template"/>
            <field name="name">51 Salg av klimakvoter og gull til næringsdrivende 0%</field>
            <field name="description">Salg av klimakvoter og gull til næringsdrivende</field>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_0"/>
            <field name="active" eval="False"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_code_51')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_code_51')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="tax19" model="account.tax.template">
            <field name="chart_template_id" ref="no_chart_template"/>
            <field name="name">52 Utførsel av varer og tjenester nullsats 0%</field>
            <field name="description">Utførsel av varer og tjenester</field>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="tax_group_id" ref="tax_group_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_code_52')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_code_52')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="tax20" model="account.tax.template">
            <field name="chart_template_id" ref="no_chart_template"/>
            <field name="name">81 Innførsel av varer med fradrag for innførselsmva høy sats 25%</field>
            <field name="description">Innførsel av varer med fradrag for innførselsmva</field>
            <field name="amount">25</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_25"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_code_81')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2741'),
                    'plus_report_line_ids': [ref('tax_report_line_code_81_tax')],
                }),
                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2727'),
                    'plus_report_line_ids': [ref('tax_report_line_code_81_tax')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_code_81')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2741'),
                    'minus_report_line_ids': [ref('tax_report_line_code_81_tax')],
                }),
                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2727'),
                    'minus_report_line_ids': [ref('tax_report_line_code_81_tax')],
                }),
            ]"/>
        </record>

        <record id="tax21" model="account.tax.template">
            <field name="chart_template_id" ref="no_chart_template"/>
            <field name="name">82 Innførsel av varer uten fradrag for innførselsmva høy sats 25%</field>
            <field name="description">Innførsel av varer uten fradrag for innførselsmva</field>
            <field name="amount">25</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_25"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_code_82')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2741'),
                    'plus_report_line_ids': [ref('tax_report_line_code_82_tax')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_code_82')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2741'),
                    'minus_report_line_ids': [ref('tax_report_line_code_82_tax')],
                }),
            ]"/>
        </record>

        <record id="tax22" model="account.tax.template">
            <field name="chart_template_id" ref="no_chart_template"/>
            <field name="name">83 Innførsel av varer med fradrag for innførselsmva middel sats 15%</field>
            <field name="description">Innførsel av varer med fradrag for innførselsmva</field>
            <field name="amount">15</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_15"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_code_83')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2742'),
                    'plus_report_line_ids': [ref('tax_report_line_code_83_tax')],
                }),
                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2728'),
                    'plus_report_line_ids': [ref('tax_report_line_code_83_tax')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_code_83')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2742'),
                    'minus_report_line_ids': [ref('tax_report_line_code_83_tax')],
                }),
                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2728'),
                    'minus_report_line_ids': [ref('tax_report_line_code_83_tax')],
                }),
            ]"/>
        </record>

        <record id="tax23" model="account.tax.template">
            <field name="chart_template_id" ref="no_chart_template"/>
            <field name="name">84 Innførsel av varer uten fradrag for innførselsmva middel sats 15%</field>
            <field name="description">Innførsel av varer uten fradrag for innførselsmva</field>
            <field name="amount">15</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_15"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_code_84')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2742'),
                    'plus_report_line_ids': [ref('tax_report_line_code_84_tax')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_code_84')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2742'),
                    'minus_report_line_ids': [ref('tax_report_line_code_84_tax')],
                }),
            ]"/>
        </record>

        <record id="tax24" model="account.tax.template">
            <field name="chart_template_id" ref="no_chart_template"/>
            <field name="name">85 Innførsel av varer uten mva beregning 0%</field>
            <field name="description">Innførsel av varer som det ikke skal beregnes mervediavgift av</field>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_code_85')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_code_85')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="tax25" model="account.tax.template">
            <field name="chart_template_id" ref="no_chart_template"/>
            <field name="name">86 Tjenester kjøpt fra utlandet med fradrag for mva høy sats 25%</field>
            <field name="description">Tjenester kjøpt fra utlandet med fradrag for mva</field>
            <field name="amount">25</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_25"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_code_86')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2712'),
                    'plus_report_line_ids': [ref('tax_report_line_code_86_tax')],
                }),
                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2702'),
                    'plus_report_line_ids': [ref('tax_report_line_code_86_tax')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_code_86')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2712'),
                    'minus_report_line_ids': [ref('tax_report_line_code_86_tax')],
                }),
                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2702'),
                    'minus_report_line_ids': [ref('tax_report_line_code_86_tax')],
                }),
            ]"/>
        </record>

        <record id="tax26" model="account.tax.template">
            <field name="chart_template_id" ref="no_chart_template"/>
            <field name="name">87 Tjenester kjøpt fra utlandet uten fradrag for mva høy sats 25%</field>
            <field name="description">Tjenester kjøpt fra utlandet uten fradrag for mva</field>
            <field name="amount">25</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_25"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_code_87')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2712'),
                    'plus_report_line_ids': [ref('tax_report_line_code_87_tax')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_code_87')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2712'),
                    'minus_report_line_ids': [ref('tax_report_line_code_87_tax')],
                }),
            ]"/>
        </record>

        <record id="tax27" model="account.tax.template">
            <field name="chart_template_id" ref="no_chart_template"/>
            <field name="name">88 Tjenester kjøpt fra utlandet med fradrag for mva lav sats 12%</field>
            <field name="description">Tjenester kjøpt fra utlandet med fradrag for mva</field>
            <field name="amount">12</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_12"/>
            <field name="active" eval="False"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_code_88')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2712'),
                    'plus_report_line_ids': [ref('tax_report_line_code_88_tax')],
                }),
                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2702'),
                    'plus_report_line_ids': [ref('tax_report_line_code_88_tax')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_code_88')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2712'),
                    'minus_report_line_ids': [ref('tax_report_line_code_88_tax')],
                }),
                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2702'),
                    'minus_report_line_ids': [ref('tax_report_line_code_88_tax')],
                }),
            ]"/>
        </record>

        <record id="tax28" model="account.tax.template">
            <field name="chart_template_id" ref="no_chart_template"/>
            <field name="name">89 Tjenester kjøpt fra utlandet uten fradrag for mva lav sats 12%</field>
            <field name="description">Tjenester kjøpt fra utlandet uten fradrag for mva</field>
            <field name="amount">12</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_12"/>
            <field name="active" eval="False"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_code_89')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2712'),
                    'plus_report_line_ids': [ref('tax_report_line_code_89_tax')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_code_89')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2712'),
                    'minus_report_line_ids': [ref('tax_report_line_code_89_tax')],
                }),
            ]"/>
        </record>

        <record id="tax29" model="account.tax.template">
            <field name="chart_template_id" ref="no_chart_template"/>
            <field name="name">91 Kjøp av klimakvoter eller gull med fradrag for mva høy sats 25%</field>
            <field name="description">Kjøp av klimakvoter eller gull med fradrag for mva</field>
            <field name="amount">25</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_25"/>
            <field name="active" eval="False"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_code_91')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2711'),
                    'plus_report_line_ids': [ref('tax_report_line_code_91_tax')],
                }),
                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2701'),
                    'plus_report_line_ids': [ref('tax_report_line_code_91_tax')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_code_91')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2711'),
                    'minus_report_line_ids': [ref('tax_report_line_code_91_tax')],
                }),
                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2701'),
                    'minus_report_line_ids': [ref('tax_report_line_code_91_tax')],
                }),
            ]"/>
        </record>

        <record id="tax30" model="account.tax.template">
            <field name="chart_template_id" ref="no_chart_template"/>
            <field name="name">92 Kjøp av klimakvoter eller gull uten fradrag for mva høy sats 25%</field>
            <field name="description">Kjøp av klimakvoter eller gull uten fradrag for mva</field>
            <field name="amount">25</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="tax_group_id" ref="tax_group_25"/>
            <field name="active" eval="False"/>
            <field name="invoice_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_code_92')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2711'),
                    'plus_report_line_ids': [ref('tax_report_line_code_92_tax')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5,0,0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_code_92')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('chart2711'),
                    'minus_report_line_ids': [ref('tax_report_line_code_92_tax')],
                }),
            ]"/>
        </record>
    </data>
</odoo>
