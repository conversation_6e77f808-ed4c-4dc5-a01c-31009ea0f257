<odoo>
    <!--Hr Department Inherit Kanban view-->
    <record id="hr_department_view_kanban" model="ir.ui.view">
        <field name="name">hr.department.kanban.inherit</field>
        <field name="model">hr.department</field>
        <field name="inherit_id" ref="hr.hr_department_view_kanban"/>
        <field name="groups_id" eval="[(4,ref('hr_expense.group_hr_expense_team_approver'))]"/>
        <field name="arch" type="xml">
            <data>
                <xpath expr="//templates" position="before">
                    <field name="expense_sheets_to_approve_count"/>
                </xpath>

                <xpath expr="//div[hasclass('o_kanban_primary_right')]" position="inside">
                    <div t-if="record.expense_sheets_to_approve_count.raw_value > 0" class="row ml16">
                        <div class="col-9">
                            <a name="%(action_hr_expense_sheet_department_to_approve)d" type="action">
                                Expense Reports
                            </a>
                        </div>
                        <div class="col-3 text-right">
                            <t t-esc="record.expense_sheets_to_approve_count.raw_value"/>
                        </div>
                    </div>
                </xpath>

                <xpath expr="//div[hasclass('o_kanban_manage_reports')]" position="inside">
                    <a role="menuitem" class="dropdown-item" name="%(action_hr_expense_sheet_department_filtered)d" type="action">
                        Expenses
                    </a>
                </xpath>
            </data>
        </field>
    </record>
</odoo>
