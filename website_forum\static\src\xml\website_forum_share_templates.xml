<templates id="template" xml:space="preserve">
    <t t-name="website.social_modal">
        <div role="dialog" class="modal fade" id="oe_social_share_modal">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <header class="modal-header alert alert-info mb0" role="status">
                        <h4 class="modal-title">Thanks for posting!</h4>
                        <button type="button" class="close" data-dismiss="modal">
                            <span role="img" aria-label="Close">x</span>
                        </button>
                    </header>
                    <main class="modal-body">
                        <t t-if="target_type == 'question'" t-call="website_forum.social_message_question"/>
                        <t t-if="target_type == 'answer'" t-call="website_forum.social_message_answer"/>
                        <t t-if="target_type == 'default'" t-call="website_forum.social_message_default"/>
                        <div t-if="state != 'pending'" class="share-icons text-center text-primary">
                            <t t-foreach="medias" t-as="media">
                                <a style="cursor: pointer" t-attf-class="fa-stack fa-lg share #{media}" t-attf-aria-label="Share on #{media}" t-attf-title="Share on #{media}">
                                    <span class="fa fa-square fa-stack-2x"></span>
                                    <span t-attf-class="oe_social_#{media} fa fa-#{media} fa-stack-1x fa-inverse"></span>
                                </a>
                            </t>
                        </div>
                    </main>
                </div>
            </div>
        </div>
    </t>
    <t t-name="website_forum.spam_search_name">
        <t t-foreach="posts" t-as="post">
            <div class="card mb-1 o_spam_character">
                <div class="card-body py-2">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" t-attf-id="post_#{post.id}" t-att-value='post.id' checked='checked'/>
                        <label class="custom-control-label" t-attf-for="post_#{post.id}">
                            <b><t t-esc="post.name" /></b>
                            <p class='text-muted'><t t-esc="post.content" /></p>
                        </label>
                    </div>
                </div>
            </div>
        </t>
    </t>
    <t t-name="website_forum.social_message_question">
        <p>On average, <b>45% of questions shared</b> on social networks get an answer within
        5 hours. Questions shared on two social networks have <b>65% more chance to get an
        answer</b> !</p>
        <p t-if="state == 'pending'">You can share your question once it has been validated</p>
    </t>
    <t t-name="website_forum.social_message_answer">
        <p>By sharing you answer, you will get additional <b>karma points</b> if your
        answer is selected as the right one. See what you can do with karma
        <a href="/forum/help-1/faq" target="_blank">here</a>.</p>
    </t>
    <t t-name="website_forum.social_message_default">
        <p>Share this content to increase your chances to be featured on the front page and attract more visitors.</p>
    </t>

    <t t-name="website.social_alert">
        <div class="alert alert-info alert-dismissable" role="status">
            <button type="button" class="close" data-dismiss="alert">
                <span role="img" aria-label="Close">&#215;</span>
            </button>
            <p>Move this question to the top of the list by sharing it on social networks.</p><br/>
            <div>
                <t t-foreach="medias" t-as="media">
                    <a style="cursor: pointer" t-attf-class="fa-stack fa-lg share oe_share_bump #{media}" t-attf-aria-label="Share on #{media}" t-attf-title="Share on #{media}">
                        <span class="fa fa-square fa-stack-2x"></span>
                        <span t-attf-class="oe_social_#{media} fa fa-#{media} fa-stack-1x fa-inverse"></span>
                    </a>
                </t>
            </div>
        </div>
    </t>
</templates>
