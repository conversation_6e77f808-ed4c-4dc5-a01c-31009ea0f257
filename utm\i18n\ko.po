# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* utm
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_medium__active
msgid "Active"
msgstr "활성"

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_campaign__is_auto_campaign
msgid "Allows us to filter relevant Campaigns"
msgstr "관련 캠페인에 필터를 할 수 있습니다."

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Approval-based Flow"
msgstr "결재 기반 프로세스"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Approved"
msgstr "결재 완료"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_search
msgid "Archived"
msgstr "보관됨"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.action_view_utm_tag
msgid "Assign tags to your campaigns to organize, filter and track them."
msgstr "캠페인에 태그를 두어 캠페인을 체계화하고 필터링 및 추적하십시오."

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Audience-driven Flow"
msgstr "잠재 고객 중심 프로세스"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__is_auto_campaign
msgid "Automatically Generated Campaign"
msgstr "자동 생성 캠페인"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_mixin__campaign_id
msgid "Campaign"
msgstr "캠페인"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__name
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form_quick_create
msgid "Campaign Name"
msgstr "캠페인명"

#. module: utm
#: model:ir.model,name:utm.model_utm_stage
msgid "Campaign Stage"
msgstr "캠페인 단계"

#. module: utm
#: model:ir.actions.act_window,name:utm.action_view_utm_tag
#: model_terms:ir.ui.view,arch_db:utm.utm_tag_view_tree
msgid "Campaign Tags"
msgstr "캠페인 태그"

#. module: utm
#: model:ir.actions.act_window,name:utm.utm_campaign_action
#: model:ir.ui.menu,name:utm.menu_utm_campaign_act
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Campaigns"
msgstr "캠페인"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_campaign_action
msgid ""
"Campaigns are used to centralize your marketing efforts and track their "
"results."
msgstr "캠페인은 마케팅 활동을 한 방향으로 집중시키고 그 결과를 추적하는 데 사용됩니다."

#. module: utm
#: model:utm.campaign,name:utm.utm_campaign_christmas_special
msgid "Christmas Special"
msgstr "크리스마스 스페셜"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Collect ideas, design creative content and publish it once reviewed."
msgstr "아이디어를 모으고, 창의적인 콘텐츠를 디자인한 후 수정을 거쳐 게시하세요."

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__color
#: model:ir.model.fields,field_description:utm.field_utm_tag__color
msgid "Color Index"
msgstr "색상표"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Copywriting"
msgstr "저작권"

#. module: utm
#: model:utm.source,name:utm.utm_source_craigslist
msgid "Craigslist"
msgstr ""

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_medium_action
msgid "Create a Medium"
msgstr "매체 만들기"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.action_view_utm_tag
msgid "Create a Tag"
msgstr "태그 만들기"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_campaign_action
msgid "Create a campaign"
msgstr "캠페인 만들기"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.action_view_utm_stage
msgid "Create a stage for your campaigns"
msgstr "귀하의 캠페인에 단계 만들기"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__create_uid
#: model:ir.model.fields,field_description:utm.field_utm_medium__create_uid
#: model:ir.model.fields,field_description:utm.field_utm_source__create_uid
#: model:ir.model.fields,field_description:utm.field_utm_stage__create_uid
#: model:ir.model.fields,field_description:utm.field_utm_tag__create_uid
msgid "Created by"
msgstr "작성자"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__create_date
#: model:ir.model.fields,field_description:utm.field_utm_medium__create_date
#: model:ir.model.fields,field_description:utm.field_utm_source__create_date
#: model:ir.model.fields,field_description:utm.field_utm_stage__create_date
#: model:ir.model.fields,field_description:utm.field_utm_tag__create_date
msgid "Created on"
msgstr "작성일자"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Creative Flow"
msgstr "창의적인 플로우"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_kanban
msgid "Delete"
msgstr "삭제"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Deploy"
msgstr "배포하다"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Deployed"
msgstr "배포됨"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: model:utm.stage,name:utm.campaign_stage_2
#, python-format
msgid "Design"
msgstr "디자인"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__display_name
#: model:ir.model.fields,field_description:utm.field_utm_medium__display_name
#: model:ir.model.fields,field_description:utm.field_utm_source__display_name
#: model:ir.model.fields,field_description:utm.field_utm_stage__display_name
#: model:ir.model.fields,field_description:utm.field_utm_tag__display_name
msgid "Display Name"
msgstr "표시명"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Done"
msgstr "완료"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_kanban
msgid "Dropdown menu"
msgstr "드롭다운 메뉴"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_kanban
msgid "Edit"
msgstr "편집하기"

#. module: utm
#: model:utm.campaign,name:utm.utm_campaign_email_campaign_products
msgid "Email Campaign - Products"
msgstr "캠페인 이메일 전송 - 상품"

#. module: utm
#: model:utm.campaign,name:utm.utm_campaign_email_campaign_services
msgid "Email Campaign - Services"
msgstr "이메일 캠페인 - 서비스"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Event-driven Flow"
msgstr "행사 중심 플로우"

#. module: utm
#: model:utm.source,name:utm.utm_source_facebook
msgid "Facebook"
msgstr "페이스북"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Gather Data"
msgstr "데이터 수집"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid ""
"Gather data, build a recipient list and write content based on your "
"Marketing target."
msgstr "데이터를 수집하고, 수신자 목록을 작성하고, 마케팅 목표에 따라 콘텐츠를 작성하세요."

#. module: utm
#: model:utm.source,name:utm.utm_source_glassdoor
msgid "Glassdoor"
msgstr ""

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Group By"
msgstr "그룹별"

#. module: utm
#: model:ir.model,name:utm.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP 라우팅"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__id
#: model:ir.model.fields,field_description:utm.field_utm_medium__id
#: model:ir.model.fields,field_description:utm.field_utm_source__id
#: model:ir.model.fields,field_description:utm.field_utm_stage__id
#: model:ir.model.fields,field_description:utm.field_utm_tag__id
msgid "ID"
msgstr "ID"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Ideas"
msgstr "아이디어"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign____last_update
#: model:ir.model.fields,field_description:utm.field_utm_medium____last_update
#: model:ir.model.fields,field_description:utm.field_utm_source____last_update
#: model:ir.model.fields,field_description:utm.field_utm_stage____last_update
#: model:ir.model.fields,field_description:utm.field_utm_tag____last_update
msgid "Last Modified on"
msgstr "최근 수정일"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__write_uid
#: model:ir.model.fields,field_description:utm.field_utm_medium__write_uid
#: model:ir.model.fields,field_description:utm.field_utm_source__write_uid
#: model:ir.model.fields,field_description:utm.field_utm_stage__write_uid
#: model:ir.model.fields,field_description:utm.field_utm_tag__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__write_date
#: model:ir.model.fields,field_description:utm.field_utm_medium__write_date
#: model:ir.model.fields,field_description:utm.field_utm_source__write_date
#: model:ir.model.fields,field_description:utm.field_utm_stage__write_date
#: model:ir.model.fields,field_description:utm.field_utm_tag__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Later"
msgstr "이전"

#. module: utm
#: model:utm.source,name:utm.utm_source_mailing
msgid "Lead Recall"
msgstr "영업제안 리콜"

#. module: utm
#: model:ir.ui.menu,name:utm.menu_link_tracker_root
msgid "Link Tracker"
msgstr "링크 추적기"

#. module: utm
#: model:utm.source,name:utm.utm_source_linkedin
msgid "LinkedIn"
msgstr "링크드인"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "List-Building"
msgstr "목록 작성"

#. module: utm
#: model:utm.tag,name:utm.utm_tag_1
msgid "Marketing"
msgstr "마케팅"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_mixin__medium_id
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_form
msgid "Medium"
msgstr "중간"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_medium__name
msgid "Medium Name"
msgstr "중간 이름"

#. module: utm
#: model:ir.actions.act_window,name:utm.utm_medium_action
#: model:ir.ui.menu,name:utm.menu_utm_medium
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_tree
msgid "Mediums"
msgstr "매체"

#. module: utm
#: model:utm.source,name:utm.utm_source_monster
msgid "Monster"
msgstr ""

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_stage__name
#: model:ir.model.fields,field_description:utm.field_utm_tag__name
msgid "Name"
msgstr "이름"

#. module: utm
#: model:utm.stage,name:utm.default_utm_stage
msgid "New"
msgstr "신규"

#. module: utm
#: model:utm.source,name:utm.utm_source_newsletter
msgid "Newsletter"
msgstr "뉴스레터"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_source_action
msgid "No Sources yet!"
msgstr "아직 소스가 없습니다!"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Pre-Launch"
msgstr "사전 출시"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Prepare Campaigns and get them approved before making them go live."
msgstr "캠페인을 준비하고 승인이 완료되면 게시하세요."

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid ""
"Prepare your Campaign, test it with part of your audience and deploy it "
"fully afterwards."
msgstr "캠페인을 준비하고, 일부 잠재 고객들을 대상으로 테스트한 후 공식적으로 배포하세요."

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Report"
msgstr "보고서"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__user_id
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Responsible"
msgstr "담당자"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Review"
msgstr "검토"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Running"
msgstr "실행 중"

#. module: utm
#: model:utm.campaign,name:utm.utm_campaign_fall_drive
msgid "Sale"
msgstr "매출"

#. module: utm
#: model:utm.stage,name:utm.campaign_stage_1
msgid "Schedule"
msgstr "예약"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_search
msgid "Search UTM Medium"
msgstr "UTM 매체 검색"

#. module: utm
#: model:utm.source,name:utm.utm_source_search_engine
msgid "Search engine"
msgstr "검색 엔진"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Send"
msgstr "보내기"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: model:utm.stage,name:utm.campaign_stage_3
#, python-format
msgid "Sent"
msgstr "전송됨"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_stage__sequence
msgid "Sequence"
msgstr "순서"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Soft-Launch"
msgstr "소프트 런칭"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Soft-Launch Flow"
msgstr "소프트 런칭 플로우"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_mixin__source_id
#: model_terms:ir.ui.view,arch_db:utm.utm_source_view_form
#: model_terms:ir.ui.view,arch_db:utm.utm_source_view_tree
msgid "Source"
msgstr "원본"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_source__name
msgid "Source Name"
msgstr "출처명"

#. module: utm
#: model:ir.actions.act_window,name:utm.utm_source_action
#: model:ir.ui.menu,name:utm.menu_utm_source
msgid "Sources"
msgstr "출처"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__stage_id
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Stage"
msgstr "단계"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_stage_view_search
#: model_terms:ir.ui.view,arch_db:utm.utm_stage_view_tree
msgid "Stages"
msgstr "단계"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.action_view_utm_stage
msgid ""
"Stages allow you to organize your workflow  (e.g. : plan, design, in "
"progress,  done, …)."
msgstr "단계를 사용하면 작업 순서를 구성할 수 있습니다 (예 : 계획, 디자인, 진행 중, 완료 등)."

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_tag__color
msgid ""
"Tag color. No color means no display in kanban to distinguish internal tags "
"from public categorization tags."
msgstr "태그 색상. 색상을 설정하지 않으면 칸반에 내부 태그와 공개 분류 태그를 구분하기 위한 색상이 표시되지 않습니다."

#. module: utm
#: model:ir.model.constraint,message:utm.constraint_utm_tag_name_uniq
msgid "Tag name already exists !"
msgstr "동일한 태그명이 존재합니다!"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__tag_ids
msgid "Tags"
msgstr "태그"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "This Month"
msgstr "이번 달"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "This Week"
msgstr "금주"

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_mixin__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""
"이것은 다른 캠페인 활동을 추적할 때 도움을 주는 이름입니다.  예시 : Fall_Drive, Christmas_Special"

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_mixin__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr "이것은 배송 방법입니다. 예시 : 엽서, 이메일 또는 배너광고"

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_mixin__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr "이것은 링크의 소스입니다. 예시 : 검색엔진, 다른 도메인 또는 이메일 목록의 이름"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "To be Approved"
msgstr "승인 예정"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid ""
"Track incoming events (e.g. : Christmas, Black Friday, ...) and publish "
"timely content."
msgstr ""

#. module: utm
#: model:utm.source,name:utm.utm_source_twitter
msgid "Twitter"
msgstr "트위터"

#. module: utm
#: model:ir.model,name:utm.model_utm_campaign
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form
msgid "UTM Campaign"
msgstr "UTM 캠페인"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_tree
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "UTM Campaigns"
msgstr "UTM 캠페인"

#. module: utm
#: model:ir.model,name:utm.model_utm_medium
msgid "UTM Medium"
msgstr "UTM 매체"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_medium_action
msgid ""
"UTM Mediums track the mean that was used to attract traffic (e.g. "
"\"Website\", \"Twitter\", ...)."
msgstr "UTM 매체는 트래픽을 유도하는 데 사용된 수단(예. \"웹사이트\", \"트위터\" 등)을 추적합니다."

#. module: utm
#: model:ir.model,name:utm.model_utm_mixin
msgid "UTM Mixin"
msgstr "UTM 혼합"

#. module: utm
#: model:ir.model,name:utm.model_utm_source
msgid "UTM Source"
msgstr "UTM 소스"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_source_action
msgid ""
"UTM Sources track where traffic comes from  (e.g. \"May Newsletter\", \"\", "
"...)."
msgstr "UTM 소스는 트래픽의 출처(예. \"5월 뉴스레터\", \"\", ...)를 추적합니다."

#. module: utm
#: model:ir.actions.act_window,name:utm.action_view_utm_stage
msgid "UTM Stages"
msgstr "UTM 단계"

#. module: utm
#: model:ir.model,name:utm.model_utm_tag
msgid "UTM Tag"
msgstr "UTM 태그"

#. module: utm
#: model:ir.ui.menu,name:utm.marketing_utm
msgid "UTMs"
msgstr "UTMs"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Use This For My Campaigns"
msgstr "내 캠페인에 사용하기"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form_quick_create
msgid "e.g. Black Friday"
msgstr "예. 블랙 프라이데이"
