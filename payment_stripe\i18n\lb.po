# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_stripe
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-27 09:11+0000\n"
"PO-Revision-Date: 2019-08-26 09:12+0000\n"
"Language-Team: Luxembourgish (https://www.transifex.com/odoo/teams/41243/lb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_stripe
#. openerp-web
#: code:addons/payment_stripe/static/src/xml/stripe_templates.xml:0
#, python-format
msgid "&times;"
msgstr ""

#. module: payment_stripe
#: model:ir.model.fields,help:payment_stripe.field_payment_acquirer__stripe_image_url
msgid ""
"A relative or absolute URL pointing to a square image of your brand or "
"product. As defined in your Stripe profile. See: "
"https://stripe.com/docs/checkout"
msgstr ""

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__stripe_image_url
msgid "Checkout Image URL"
msgstr ""

#. module: payment_stripe
#. openerp-web
#: code:addons/payment_stripe/static/src/xml/stripe_templates.xml:0
#, python-format
msgid "Close"
msgstr ""

#. module: payment_stripe
#. openerp-web
#: code:addons/payment_stripe/static/src/xml/stripe_templates.xml:0
#, python-format
msgid "Error"
msgstr ""

#. module: payment_stripe
#. openerp-web
#: code:addons/payment_stripe/static/src/js/stripe.js:0
#, python-format
msgid "Just one more second, We are redirecting you to Stripe..."
msgstr ""

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_acquirer
msgid "Payment Acquirer"
msgstr ""

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_token__stripe_payment_method
msgid "Payment Method ID"
msgstr ""

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_token
msgid "Payment Token"
msgstr ""

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_transaction
msgid "Payment Transaction"
msgstr ""

#. module: payment_stripe
#. openerp-web
#: code:addons/payment_stripe/static/src/js/stripe.js:0
#, python-format
msgid "Payment error"
msgstr ""

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment.py:0
#, python-format
msgid ""
"Perhaps the problem can be solved by double-checking your credit card "
"details, or contacting your bank?"
msgstr ""

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__provider
msgid "Provider"
msgstr ""

#. module: payment_stripe
#: model:ir.model.fields.selection,name:payment_stripe.selection__payment_acquirer__provider__stripe
msgid "Stripe"
msgstr ""

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_transaction__stripe_payment_intent
msgid "Stripe Payment Intent ID"
msgstr ""

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__stripe_publishable_key
msgid "Stripe Publishable Key"
msgstr ""

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__stripe_secret_key
msgid "Stripe Secret Key"
msgstr ""

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment.py:0
#, python-format
msgid "Stripe gave us the following info about the problem: '%s'"
msgstr ""

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment.py:0
#, python-format
msgid "Stripe: %s orders found for reference %s"
msgstr ""

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment.py:0
#, python-format
msgid "Stripe: no order found for reference %s"
msgstr ""

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment.py:0
#, python-format
msgid ""
"Unable to convert Stripe customer for SCA compatibility. Is there at least "
"one card for this customer in the Stripe backend?"
msgstr ""

#. module: payment_stripe
#. openerp-web
#: code:addons/payment_stripe/static/src/js/payment_form.js:0
#, python-format
msgid "Unable to save card"
msgstr ""

#. module: payment_stripe
#. openerp-web
#: code:addons/payment_stripe/static/src/js/payment_form.js:0
#, python-format
msgid "We are not able to add your payment method at the moment. "
msgstr ""

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment.py:0
#, python-format
msgid "We're sorry to report that the transaction has failed."
msgstr ""
