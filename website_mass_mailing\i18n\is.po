# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_mass_mailing
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:18+0000\n"
"PO-Revision-Date: 2018-08-24 09:35+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Icelandic (https://www.transifex.com/odoo/teams/41243/is/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: is\n"
"Plural-Forms: nplurals=2; plural=(n % 10 != 1 || n % 100 == 11);\n"

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_popup
msgid "&amp;times;"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_popup
msgid ""
"<strong>Newsletter Popup!</strong> The newsletter popup snippet effect is "
"active on this page. Click"
msgstr ""

#. module: website_mass_mailing
#. openerp-web
#: code:addons/website_mass_mailing/static/src/js/website_mass_mailing.editor.js:15
#, python-format
msgid "Add a Newsletter Subscribe Button"
msgstr ""

#. module: website_mass_mailing
#. openerp-web
#: code:addons/website_mass_mailing/static/src/js/website_mass_mailing.editor.js:53
#, python-format
msgid "Add a Newsletter Subscribe Popup"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.newsletter_subscribe_options
msgid "Change Newsletter"
msgstr ""

#. module: website_mass_mailing
#: model:ir.model,name:website_mass_mailing.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_mail_block_footer_social
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_mail_block_footer_social_left
msgid "Contact"
msgstr "Tengiliður"

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.res_config_settings_view_form
msgid "Encourage to subscribe to mailing lists with a popup"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.social_links
msgid "Facebook"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.social_links
msgid "Google Plus"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_popup
msgid "Here To Edit Dialog Content"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.social_links
msgid "Instagram"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.social_links
msgid "LinkedIn"
msgstr ""

#. module: website_mass_mailing
#: model:ir.model,name:website_mass_mailing.model_mail_mass_mailing_list
msgid "Mailing List"
msgstr "Mailing List"

#. module: website_mass_mailing
#. openerp-web
#: code:addons/website_mass_mailing/static/src/js/website_mass_mailing.editor.js:21
#, python-format
msgid "Newsletter"
msgstr "Newsletter"

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.view_mail_mass_mailing_list_form
msgid "Popup Content"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_form
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_popup
msgid "Subscribe"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_form
msgid "Thanks"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_form
msgid "Thanks for your subscription!"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.res_config_settings_view_form
msgid ""
"This adds a new Newsletter Popup snippet to drag & drop on your website "
"pages. It triggers a popup when visitors land on the page. This popup "
"prompts them to enter their email address to join a mailing list."
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.social_links
msgid "Twitter"
msgstr ""

#. module: website_mass_mailing
#. openerp-web
#: code:addons/website_mass_mailing/static/src/js/website_mass_mailing.editor.js:83
#, python-format
msgid "Type Here ..."
msgstr ""

#. module: website_mass_mailing
#: model:res.groups,name:website_mass_mailing.group_website_popup_on_exit
msgid "Use subscription pop up on the website"
msgstr ""

#. module: website_mass_mailing
#: model:ir.model.fields,field_description:website_mass_mailing.field_res_config_settings__group_website_popup_on_exit
msgid "Website Popup"
msgstr ""

#. module: website_mass_mailing
#: model:ir.model.fields,field_description:website_mass_mailing.field_mail_mass_mailing_list__popup_content
msgid "Website Popup Content"
msgstr ""

#. module: website_mass_mailing
#: model:ir.model.fields,field_description:website_mass_mailing.field_mail_mass_mailing_list__popup_redirect_url
msgid "Website Popup Redirect URL"
msgstr ""

#. module: website_mass_mailing
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_form
#: model_terms:ir.ui.view,arch_db:website_mass_mailing.s_newsletter_subscribe_popup
msgid "your email..."
msgstr ""
