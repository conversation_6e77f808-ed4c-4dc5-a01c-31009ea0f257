# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* iap
# 
# Translators:
# <PERSON><PERSON>hu<PERSON>, 2021
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: Tr<PERSON><PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid "Account Information"
msgstr "Thông tin tài khoản"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__account_token
msgid "Account Token"
msgstr "Token tài khoản"

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/js/crash_manager.js:0
#: code:addons/iap/static/src/xml/iap_templates.xml:0
#, python-format
msgid "Buy credits"
msgstr "Buy credits"

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/js/crash_manager.js:0
#, python-format
msgid "Cancel"
msgstr "Hủy"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__company_ids
msgid "Company"
msgstr "Công ty"

#. module: iap
#: model:ir.model,name:iap.model_res_config_settings
msgid "Config Settings"
msgstr "Thiết lập cấu hình"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__create_uid
msgid "Created by"
msgstr "Tạo bởi"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__create_date
msgid "Created on"
msgstr "Thời điểm tạo"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "Documentation"
msgstr "Tài liệu"

#. module: iap
#: model:ir.ui.menu,name:iap.iap_root_menu
msgid "IAP"
msgstr "IAP"

#. module: iap
#: model:ir.actions.act_window,name:iap.iap_account_action
#: model:ir.model,name:iap.model_iap_account
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid "IAP Account"
msgstr "Tài khoản IAP"

#. module: iap
#: model:ir.ui.menu,name:iap.iap_account_menu
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_tree
msgid "IAP Accounts"
msgstr "Tài khoản IAP"

#. module: iap
#: model:ir.model,name:iap.model_iap_enrich_api
msgid "IAP Lead Enrichment API"
msgstr "IAP Lead Enrichment API"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__id
msgid "ID"
msgstr "ID"

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/js/crash_manager.js:0
#, python-format
msgid "Insufficient Balance"
msgstr "Thiếu cân bằng"

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/xml/iap_templates.xml:0
#, python-format
msgid "Insufficient credit to perform this service."
msgstr "Tín dụng không đủ để thực hiện dịch vụ này."

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account____last_update
msgid "Last Modified on"
msgstr "Sửa lần cuối vào"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "Odoo IAP"
msgstr "Odoo IAP"

#. module: iap
#: model:ir.actions.server,name:iap.open_iap_account
msgid "Open IAP Account"
msgstr "Open IAP Account"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__service_name
msgid "Service Name"
msgstr "Tên dịch vụ"

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/js/crash_manager.js:0
#, python-format
msgid "Start a Trial at Odoo"
msgstr "Start a Trial at Odoo"

#. module: iap
#: code:addons/iap/tools/iap_tools.py:0
#, python-format
msgid ""
"The url that this service requested returned an error. Please contact the "
"author of the app. The url it tried to contact was %s"
msgstr ""
"The url that this service requested returned an error. Please contact the "
"author of the app. The url it tried to contact was %s"

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/xml/iap_templates.xml:0
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
#, python-format
msgid "View My Services"
msgstr "View My Services"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "View your IAP Services and recharge your credits"
msgstr "View your IAP Services and recharge your credits"
