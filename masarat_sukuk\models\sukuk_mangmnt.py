# -*- coding: utf-8 -*-
from odoo import tools, api, fields, models
from datetime import date, datetime
from odoo.exceptions import ValidationError

class SukukManagement(models.Model):#####طلب دفتر شيكات 
    _name = "sukuk.management"

    name = fields.Char(string='الاسم', compute='get_name')
    indicative_number = fields.Char(string='رقم اشاري')
    state=fields.Selection([('draft','مسودة'),('confirmed','مؤكدة'),('cancel','ملغية')], default='draft', string='الحالة',)
    bank_id = fields.Many2one('res.bank', string='اسم المصرف',required=True)
    branch_id = fields.Many2one('bank.branch',domain="[('bank_id','=',bank_id)]",string="أسم الفرع",required=True)
    account_no_id = fields.Many2one('account.account',string="رقم الحساب",required=True)
    suke_book_required = fields.Integer(string='عدد الدفاتر المطلوبة',required=True)
    order_date = fields.Date(string='تاريخ الطلب', default=lambda self: fields.Date.to_string(date.today()), readonly=True)
    employee_id = fields.Many2one('hr.employee', string="اسم مقدم الطلب", readonly=True, store=True)
    person_signature=fields.Many2one('sukuk.management.signature', string="اسم المخول بالتوقيع", required=True)
    receive_date = fields.Date(string='تاريخ الاستلام')
    suke_item_ids = fields.One2many('sukuk.management.item', 'sukuk_management_id')
    attached_file = fields.Binary(string="ارفاق ملف")
    person_signature_visible = fields.Boolean(compute="get_person_signature_visible")

    def get_person_signature_visible(self):
        if self.env.uid == self.person_signature.employee_id.user_id.id:
            self.person_signature_visible = True
        else:
            self.person_signature_visible = False


    @api.onchange('suke_book_required','bank_id','branch_id','account_no_id')
    def get_suke_item_ids(self):
        self.suke_item_ids = False
        if self.suke_book_required and self.bank_id and self.branch_id:
            books_list = []
            self.env.cr.execute("select COALESCE(max(suke_book_number), 0 ) from sukuk_management_item where bank_id = %s and branch_id = %s ",(self.bank_id.id, self.branch_id.id))
            i = self.env.cr.fetchone()[0]+1
            k=1
            while k < self.suke_book_required+1:
                books_list.append((0, 0, {
                    'suke_book_number': i,
                    'bank_id': self.bank_id.id,
                    'branch_id': self.branch_id.id,
                    'account_no_id': self.account_no_id.id,
                }))
                i+=1
                k+=1
                self.suke_item_ids = False
                self.suke_item_ids = books_list
        else:
            self.suke_item_ids = False


    @api.depends('bank_id','branch_id','suke_book_required')
    def get_name(self):
        for elem in self:
            elem.name=''
            if elem.bank_id and elem.suke_book_required and elem.branch_id:
                elem.name = str(elem.bank_id.name)+'-'+str(elem.branch_id.name)+'-'+str(elem.suke_book_required)

    def make_confirmed(self):
        if not self.suke_item_ids:
            raise ValidationError('لا يوجد دفاتر لتأكيد العملية')
        if self.suke_item_ids:
            for elem in self.suke_item_ids:
                if not elem.number_of_page_demand:
                    raise ValidationError(str(elem.suke_book_number)+' يرجى ادخال عدد اوراق الدفاتر المطلوبة دفتر ')
                if not elem.number_of_page_received:
                    raise ValidationError(str(elem.suke_book_number)+'يرجى ادخال عدد اوراق الدفتر المستلمة دفتر ')

                if not elem.serial_no_from or not elem.serial_no_to:
                    raise ValidationError(str(elem.suke_book_number)+'يرجى ادخال الرقم التسلسلي دفتر ')
                if elem.number_of_page_received and elem.serial_no_from and elem.serial_no_to:
                    if elem.number_of_page_received != ((elem.serial_no_to - elem.serial_no_from)+1):
                        raise ValidationError(str(elem.suke_book_number)+'يجب أن يكون التسلسل مطابق لعدد الأوراق المستلمة دفتر ')

        self.state = 'confirmed'

    def make_cancel(self):
        self.state = 'cancel'
    def make_draft(self):
        self.state = 'draft'

    @api.model
    def default_get(self, fields):
        res = super(SukukManagement, self).default_get(fields)
        user_id = self._context.get('uid')
        employee_id = self.env['hr.employee'].search([('user_id', '=', user_id)])
        res['employee_id'] = employee_id.id
        return res

    def write(self,vals):
        res = super(SukukManagement, self).write(vals)
        if 'state' not in vals.keys() and self.state in ('cancel','confirmed'):
            raise ValidationError('لا يمكن تعديل صك وهو بحالة تأكيد أو الغاء')
        return res

    def unlink(self):
        for elem in self:
            if elem.state in ('confirmed', 'cancel'):
                raise ValidationError("You can't delete the recorde which is not in draft state!")
        return super(SukukManagement, self).unlink()


    def action_send_notification_to_maneger(self,vals_list,recode_id):
        email_to = self.env['sukuk.management.signature'].search([('id','=',vals_list['person_signature'])]).employee_id.work_email
        user_id = self._context.get('uid')
        email_from = self.env['hr.employee'].search([('user_id', '=', user_id)])
        bank = self.env['res.bank'].search([('id','=',vals_list['bank_id'])]).name
        branch = self.env['bank.branch'].search([('id','=',vals_list['branch_id'])]).name
        get_name_for_email =str(bank)+'-'+str(branch)+'-'+str(vals_list['suke_book_required'])
        web_base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        web_base_url += '/web#id=%d&view_type=form&model=%s' % (recode_id, self._name)
        body = """
        <div dir="rtl">
        <p><font style="font-size: 14px;"> لديك طلب موافقة على صك """+str(get_name_for_email)+""" </font></p>
                  <p><font style="font-size: 14px;"> مقدم الطلب """+str(email_from.name)+"""</font></p>
                  <p><font style="font-size: 14px;"> """+web_base_url+"""</font></p>          
        </div>
                """

        template_id = self.env['mail.mail'].create({
            'subject':'طلب صكوك',
            'email_from':email_from.work_email,
            'email_to': email_to,
            'body_html':body
        })
        #### freaa
        template_id.send()

    @api.model
    def create(self, vals_list):
        obj = super(SukukManagement, self).create(vals_list)
        recode_id = obj.id
        self.sudo().action_send_notification_to_maneger(vals_list,recode_id)
        return obj


class SukukManagementItem(models.Model): ##### دفتر شيكات 
    _name = "sukuk.management.item"

    name = fields.Char(string='الاسم', compute='get_name')

    sukuk_management_id = fields.Many2one('sukuk.management',ondelete='cascade')
    state = fields.Selection([('draft', 'مسودة'), ('confirmed', 'مؤكدة'), ('cancel', 'ملغية')], related='sukuk_management_id.state' ,store=True)

    bank_id = fields.Many2one('res.bank', string='اسم المصرف', required=True)
    branch_id = fields.Many2one('bank.branch', domain="[('bank_id','=',bank_id)]", string="أسم الفرع", required=True)
    account_no_id = fields.Many2one('account.account', string="رقم الحساب", required=True)
    person_signature=fields.Many2one('sukuk.management.signature', related="sukuk_management_id.person_signature", string="اسم المخول بالتوقيع",store=True)
    suke_book_number = fields.Integer(string='رقم الدفتر', required=True)
    number_of_page_demand = fields.Integer(string="عدد اوراق الدفتر المطلوبة")
    number_of_page_received = fields.Integer(string="عدد اوراق الدفتر المستلمة")
    serial_no_from = fields.Integer(string="رقم تسلسلي من")
    serial_no_to = fields.Integer(string="رقم تسلسلي الى")
    sukuk_page_ids = fields.One2many('sukuk.management.page','sukuk_book_id')

    @api.depends('bank_id', 'branch_id', 'suke_book_number')
    def get_name(self):
        for elem in self:
            elem.name = ''
            if elem.bank_id and elem.suke_book_number and elem.branch_id:
                elem.name = str(elem.bank_id.name) + '-' + str(elem.branch_id.name) + '-' + str(elem.suke_book_number)

class SukukItemPage(models.Model): ##### ورقة شيك 
    _name = "sukuk.management.page"

    name = fields.Char(string='الاسم', compute='get_name')
    wallet_name = fields.Char(string='اسم الحافظة', readonly=True)
    sukuk_book_id = fields.Many2one('sukuk.management.item', required=True ,ondelete='cascade')
    state = fields.Selection([('draft', 'مسودة'), ('confirmed', 'مؤكدة'), ('cancel', 'ملغية')], default="draft",required=True)

    bank_id = fields.Many2one('res.bank', related="sukuk_book_id.bank_id",string='اسم المصرف')
    branch_id = fields.Many2one('bank.branch', related="sukuk_book_id.branch_id", string="أسم الفرع")
    account_no_id = fields.Many2one('account.account',related="sukuk_book_id.account_no_id", string="رقم الحساب")
    #paid to
    paid_to_bank_id = fields.Many2one('res.bank', string='اسم المصرف المدفوع له')
    paid_to_branch_id = fields.Many2one('bank.branch', string="أسم الفرع المدفوع له")
    partner_paid_to = fields.Many2one('res.partner', string="أسم المدفوع له")

    ####
    user_id = fields.Many2one('res.users',string='مقدم الطلب',default=lambda self: self.env.uid)
    person_signature = fields.Many2one('sukuk.management.signature', related="sukuk_book_id.person_signature",string="اسم المخول بالتوقيع")
    suke_book_number = fields.Integer(related="sukuk_book_id.suke_book_number",string='رقم الدفتر')
    serial_no = fields.Integer(string="رقم تسلسلي",required=True)
    time_stamp = fields.Date(string="التاريخ",  default=lambda self: fields.Date.to_string(date.today()), readonly=True)
    amount = fields.Float(string="القيمة",required=True)

    person_signature_visible = fields.Boolean(compute="get_person_signature_visible")



    def get_name(self):
        for elem in self:
            if elem.serial_no and elem.time_stamp:
                elem.name = str(elem.serial_no)+'-'+str(elem.time_stamp)
            else:
                elem.name=''

    def get_person_signature_visible(self):
        if self.env.uid == self.person_signature.employee_id.user_id.id:
            self.person_signature_visible = True
        else:
            self.person_signature_visible = False


    def make_confirmed(self):
        pages = self.env['sukuk.management.page'].search_count([('suke_book_number','=',self.suke_book_number),('serial_no','=',self.serial_no),('account_no_id','=',self.account_no_id.id)])
        if pages > 1:
            raise ValidationError("هذا الصك تم استعماله سابقاً يرجى التأكد !")
        self.state = 'confirmed'
    def make_cancel(self):
        self.state = 'cancel'
    def make_draft(self):
        self.state = 'draft'

    def write(self,vals):
        res = super(SukukItemPage, self).write(vals)
        if 'state' not in vals.keys() and self.state in ('cancel','confirmed'):
            raise ValidationError('لا يمكن تعديل صك وهو بحالة تأكيد أو الغاء')
        return res

    def unlink(self):
        for elem in self:
            if elem.state in ('confirmed', 'cancel'):
                raise ValidationError("You can't delete the recorde which is not in draft state!")
        return super(SukukItemPage, self).unlink()

    def action_send_notification_to_maneger(self,vals_list,recode_id):
        email_to = self.env['sukuk.management.signature'].search([('id','=',vals_list['person_signature'])]).employee_id.work_email
        user_id = self._context.get('uid')
        email_from = self.env['hr.employee'].search([('user_id', '=', user_id)])
        bank = self.env['res.bank'].search([('id','=',vals_list['bank_id'])]).name
        branch = self.env['bank.branch'].search([('id','=',vals_list['branch_id'])]).name
        # get_name_for_email =str(bank)+'-'+str(branch)+'-'+str(vals_list['suke_book_required'])
        web_base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        web_base_url += '/web#id=%d&view_type=form&model=%s' % (recode_id, self._name)

        body = """
        <div dir="rtl">
        <p><font style="font-size: 14px;"> لديك طلب موافقة على صك  </font></p>
                  <p><font style="font-size: 14px;"> مقدم الطلب """+str(email_from.name)+"""</font></p>
                  <a href="%s">Request Link</a>
        </div>""" % (web_base_url)


        template_id = self.env['mail.mail'].create({
            'subject':'طلب تأكيد صك',
            'email_from':email_from.work_email,
            'email_to': email_to,
            'body_html':body
        })
        #### freaa
        template_id.send()

    @api.model
    def create(self,vals):
        res = super(SukukItemPage, self).create(vals)
        # self.sudo().action_send_notification_to_maneger(vals,res.id)
        return res


class SukukManagementChargePerson(models.Model):
    _name = "sukuk.management.signature"

    name = fields.Char(related='employee_id.name')
    bank_id = fields.Many2one('res.bank', string='اسم المصرف', required=True)
    branch_id = fields.Many2one('bank.branch', domain="[('bank_id','=',bank_id)]", string="أسم الفرع", required=True)
    account_no_id = fields.Many2one('account.account', string="رقم الحساب", required=True)
    employee_id = fields.Many2one('hr.employee', string="اسم المخول بالتوقيع", required=True)
