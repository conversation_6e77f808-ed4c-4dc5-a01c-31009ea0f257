<?xml version="1.0" encoding="utf-8"?>

<odoo>
<data>

    <record id="group_use_lead" model="res.groups">
        <field name="name">Show Lead Menu</field>
        <field name="category_id" ref="base.module_category_hidden"/>
    </record>

    <record id="group_use_recurring_revenues" model="res.groups">
        <field name="name">Show Recurring Revenues Menu</field>
        <field name="category_id" ref="base.module_category_hidden"/>
    </record>

    <record model="res.users" id="base.user_root">
        <field eval="[(4,ref('base.group_partner_manager'))]" name="groups_id"/>
    </record>

    <record model="res.users" id="base.user_admin">
        <field eval="[(4,ref('base.group_partner_manager'))]" name="groups_id"/>
    </record>

    <record id="contacts.res_partner_menu_config" model="ir.ui.menu">
        <field name="name">Configuration</field>
        <field name="groups_id" eval="[(4, ref('sales_team.group_sale_manager'))]"/>
    </record>

</data>

<data noupdate="1">

    <record id="crm_rule_personal_lead" model="ir.rule">
        <field name="name">Personal Leads</field>
        <field ref="model_crm_lead" name="model_id"/>
        <field name="domain_force">['|',('user_id','=',user.id),('user_id','=',False)]</field>
        <field name="groups" eval="[(4, ref('sales_team.group_sale_salesman'))]"/>
    </record>

    <record id="crm_lead_company_rule" model="ir.rule">
        <field name="name">CRM Lead Multi-Company</field>
        <field name="model_id" ref="model_crm_lead"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id', 'in', company_ids)]</field>
    </record>

    <record id="crm_rule_all_lead" model="ir.rule">
        <field name="name">All Leads</field>
        <field ref="model_crm_lead" name="model_id"/>
        <field name="domain_force">[(1,'=',1)]</field>
        <field name="groups" eval="[(4, ref('sales_team.group_sale_salesman_all_leads'))]"/>
    </record>

    <record id="crm_activity_report_rule_all_activities" model="ir.rule">
        <field name="name">All Activities</field>
        <field ref="model_crm_activity_report" name="model_id"/>
        <field name="domain_force">[(1,'=',1)]</field>
        <field name="groups" eval="[(4, ref('sales_team.group_sale_salesman_all_leads'))]"/>
    </record>

    <record id="crm_activity_report_rule_personal_activities" model="ir.rule">
        <field name="name">Personal Activities</field>
        <field ref="model_crm_activity_report" name="model_id"/>
        <field name="domain_force">['|',('user_id','=',user.id),('user_id','=',False)]</field>
        <field name="groups" eval="[(4, ref('sales_team.group_sale_salesman'))]"/>
    </record>

    <record id="crm_activity_report_rule_multi_company" model="ir.rule">
        <field name="name">CRM Lead Multi-Company</field>
        <field name="model_id" ref="model_crm_activity_report"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id', 'in', company_ids)]</field>
    </record>

</data>

</odoo>
