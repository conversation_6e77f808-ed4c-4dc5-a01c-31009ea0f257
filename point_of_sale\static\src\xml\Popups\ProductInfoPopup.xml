<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="ProductInfoPopup" owl="1">
        <div role="dialog" class="modal-dialog">
            <div class="popup product-info-popup">
                <header class="title">
                    Product information
                </header>
                <main class="body">
                    <div class="section-product-info-title">
                        <div class="column flex-start">
                            <span t-esc="props.product.display_name" class="global-info-title product-name"/>
                            <span><t t-if="props.product.default_code" t-esc="props.product.default_code"/> <t t-if="props.product.default_code and props.product.barcode"> - </t> <t t-if="props.product.barcode" t-esc="props.product.barcode"/></span>
                        </div>
                        <div class="column flex-end">
                            <span t-esc="env.pos.format_currency(productInfo.all_prices.price_with_tax)" class="global-info-title" />
                            <t t-foreach="productInfo.all_prices.tax_details" t-as="tax" t-key="tax.name">
                                <div><t t-esc="tax.name"/>: <t t-esc="env.pos.format_currency(tax.amount)"/></div>
                            </t>
                        </div>
                    </div>
                    <div class="section-financials">
                        <div class="section-title">
                            <span>Financials</span>
                            <div class="section-title-line"></div>
                        </div>
                        <div class="section-financials-body">
                            <table>
                                <tr>
                                    <td>Price excl. VAT:</td>
                                    <td><t t-esc="env.pos.format_currency(productInfo.all_prices.price_without_tax)"/></td>
                                </tr>
                                <tr>
                                    <td>Cost:</td>
                                    <td><t t-esc="costCurrency"/></td>
                                </tr>
                                <tr>
                                    <td>Margin:</td>
                                    <td><t t-esc="marginCurrency"/> (<t t-esc="marginPercent"/>%) </td>
                                </tr>
                            </table>
                            <table>
                                <t t-foreach="productInfo.pricelists" t-as="pricelist" t-key="pricelist.name">
                                    <tr>
                                        <td t-esc="pricelist.name"/>
                                        <td t-esc="env.pos.format_currency(pricelist.price)"/>
                                    </tr>
                                </t>
                            </table>
                        </div>
                    </div>
                    <div class="section-inventory" t-if="productInfo.warehouses.length > 0">
                        <div class="section-title">
                            <span>Inventory <t t-if="env.pos.pos_session.update_stock_at_closing">(as of opening)</t></span>
                            <div class="section-title-line"></div>
                        </div>
                        <div class="section-inventory-body">
                            <table class="mobile-table">
                                <t t-foreach="productInfo.warehouses" t-as="warehouse" t-key="warehouse.name">
                                    <tr>
                                        <td><span t-esc="warehouse.name" class="table-name"/>:</td>
                                        <td><t t-esc="warehouse.available_quantity"/> <t t-esc="warehouse.uom"/> available,</td>
                                        <td><t t-esc="warehouse.forecasted_quantity"/> forecasted</td>
                                    </tr>
                                </t>
                            </table>
                        </div>
                    </div>
                    <div class="section-supplier" t-if="productInfo.suppliers.length > 0">
                        <div class="section-title">
                            <span>Replenishment</span>
                            <div class="section-title-line"></div>
                        </div>
                        <div class="section-supplier-body">
                            <table class="mobile-table">
                                <t t-foreach="productInfo.suppliers" t-as="supplier" t-key="supplier.name">
                                    <tr>
                                        <td><span t-esc="supplier.name" class="table-name"/>:</td>
                                        <div class="mobile-line">
                                            <td><t t-esc="supplier.delay"/> Days</td>
                                            <td><t t-esc="env.pos.format_currency(supplier.price)"/></td>
                                        </div>
                                    </tr>
                                </t>
                            </table>
                        </div>
                    </div>
                    <div class="extra">
                        <div class="section-variants" t-if="productInfo.variants.length > 0">
                            <div class="section-title">
                                <span>Attributes</span>
                                <div class="section-title-line"></div>
                            </div>
                            <div class="section-variants-body">
                                <table class="mobile-table">
                                    <t t-foreach="productInfo.variants" t-as="variant" t-key="variant.name">
                                        <tr>
                                            <td><span t-esc="variant.name" class="table-name"/>:</td>
                                            <td class="table-value">
                                                <t t-foreach="variant.values" t-as="attribute_value" t-key="attribute_value.name">
                                                    <span class="searchable" t-on-click="searchProduct(attribute_value.search)"><t t-esc="attribute_value.name"/></span> <t t-if="attribute_value_index lt variant.values.length - 1">, </t>
                                                </t>
                                            </td>
                                        </tr>
                                    </t>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="section-order">
                        <div class="section-title">
                            <span>Order</span>
                            <div class="section-title-line"></div>
                        </div>
                        <div class="section-order-body">
                            <table>
                                <tr>
                                    <td>Total Price excl. VAT:</td>
                                    <td t-esc="orderPriceWithoutTaxCurrency" class="table-value"/>
                                </tr>
                                <tr>
                                    <td>Total Cost:</td>
                                    <td t-esc="orderCostCurrency" class="table-value"/>
                                </tr>
                                <tr>
                                    <td>Total Margin:</td>
                                    <td class="table-value"><t t-esc="orderMarginCurrency"/> (<t t-esc="orderMarginPercent"/>%)</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </main>
                <footer class="footer">
                    <div class="button cancel" t-on-click="cancel">
                        Close
                    </div>
                </footer>
            </div>
        </div>
    </t>
</templates>
