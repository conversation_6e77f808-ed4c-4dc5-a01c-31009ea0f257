# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* crm_iap_enrich
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>, 2021
# ma<PERSON><PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:53+0000\n"
"PO-Revision-Date: 2021-09-14 12:21+0000\n"
"Last-Translator: ma<PERSON><PERSON>, 2022\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_notfound
msgid ""
"<span> No company data found based on the email address or email address is "
"one of an email provider. No credit was consumed. </span>"
msgstr ""
"<span>No se encontraron datos de la empresa basado en la dirección de correo"
" electrónico ó la dirección de correo es de un proveedor de correos "
"genérico. No se han consumido créditos. </span>"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_no_email
msgid ""
"<span>Enrichment could not be done because the email address does not look "
"valid.</span>"
msgstr ""
"<span>No se pudo hacer el enriquecimiento ya que la dirección de correo no "
"es válida.</span>"

#. module: crm_iap_enrich
#: model:ir.model.fields,field_description:crm_iap_enrich.field_crm_lead__show_enrich_button
msgid "Allow manual enrich"
msgstr "Permitir mejoras manuales"

#. module: crm_iap_enrich
#: model:ir.actions.server,name:crm_iap_enrich.ir_cron_lead_enrichment_ir_actions_server
#: model:ir.cron,cron_name:crm_iap_enrich.ir_cron_lead_enrichment
#: model:ir.cron,name:crm_iap_enrich.ir_cron_lead_enrichment
msgid "CRM: enrich leads (IAP)"
msgstr "CRM: enriquece iniciativas (IAP)"

#. module: crm_iap_enrich
#: model:ir.model,name:crm_iap_enrich.model_res_config_settings
msgid "Config Settings"
msgstr "Opciones de configuración"

#. module: crm_iap_enrich
#: model:ir.actions.server,name:crm_iap_enrich.action_enrich_mail
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.crm_lead_view_form
msgid "Enrich"
msgstr "Enriquecer"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.crm_lead_view_form
msgid "Enrich lead with company data"
msgstr "Enriquecer lead con la información de la empresa"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.crm_lead_view_form
msgid "Enrich opportunity with company data"
msgstr "Enriquecer oportunidad con información de la empresa"

#. module: crm_iap_enrich
#: model:ir.model.fields,field_description:crm_iap_enrich.field_crm_lead__iap_enrich_done
msgid "Enrichment done"
msgstr "Enriquecimiento hecho"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_no_credit
msgid "IAP account"
msgstr "Cuenta IAP"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_no_email
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_notfound
msgid "Lead Enrichment (based on email address)"
msgstr "Enriquecimiento de leads (basado en la dirección de correo)"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_no_credit
msgid "Lead enriched (based on email address)"
msgstr "Lead enriquecido (basado en la dirección de correo)"

#. module: crm_iap_enrich
#: code:addons/crm_iap_enrich/models/crm_lead.py:0
#, python-format
msgid "Lead enriched based on email address"
msgstr "Iniciativa enriquecida basado en dirección de correo"

#. module: crm_iap_enrich
#: model:ir.model,name:crm_iap_enrich.model_crm_lead
msgid "Lead/Opportunity"
msgstr "Iniciativa/Oportunidad"

#. module: crm_iap_enrich
#: model:ir.model.fields,help:crm_iap_enrich.field_crm_lead__iap_enrich_done
msgid ""
"Whether IAP service for lead enrichment based on email has been performed on"
" this lead."
msgstr ""
"Si se ha enriquecido esta iniciativa usando el servicio de enriquecimiento "
"de iniciativas IAP basado en dirección de correo."

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_no_credit
msgid "Your balance for Lead Enrichment is insufficient. Please go to your"
msgstr ""
"Tu balance para Enriquecimiento de Iniciativas es insuficiente. Por favor ve"
" a tu"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_no_credit
msgid "to buy credits."
msgstr "a comprar créditos."
