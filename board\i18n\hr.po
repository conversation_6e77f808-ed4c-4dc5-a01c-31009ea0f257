# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* board
# 
# Translators:
# <PERSON><PERSON><PERSON> <ivic<PERSON>.<PERSON><PERSON><PERSON><PERSON>@storm.hr>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:21+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Croatian (https://app.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: board
#. openerp-web
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
#, python-format
msgid "\"%s\" added to dashboard"
msgstr "\"%s\" dodan na nadzornu ploču"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid ""
"\"Add to\n"
"                Dashboard\""
msgstr ""
"\"Dodaj na\n"
"                Ploču\""

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "&nbsp;"
msgstr "&nbsp;"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/js/add_to_board_menu.js:0
#, python-format
msgid "'%s' added to dashboard"
msgstr "'%s' dodano na nadzornu ploču"

#. module: board
#. openerp-web
#: code:addons/board/static/src/add_to_board/add_to_board.xml:0
#: code:addons/board/static/src/legacy/xml/board.xml:0
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Add"
msgstr "Dodaj"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Add to my Dashboard"
msgstr "Dodaj na moju kontrolnu ploču"

#. module: board
#. openerp-web
#: code:addons/board/static/src/add_to_board/add_to_board.xml:0
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Add to my dashboard"
msgstr "Dodaj na moju ploču"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/js/board_view.js:0
#, python-format
msgid "Are you sure you want to remove this item?"
msgstr "Želite li doista ukloniti ovu stavku ?"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/js/board_view.js:0
#: model:ir.model,name:board.model_board_board
#, python-format
msgid "Board"
msgstr "Ploča"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Change Layout"
msgstr "Promjeni raspored"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Change Layout.."
msgstr "Promjeni raspored..."

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Choose dashboard layout"
msgstr "Odaberi raspored"

#. module: board
#. openerp-web
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
#: code:addons/board/static/src/legacy/js/add_to_board_menu.js:0
#, python-format
msgid "Could not add filter to dashboard"
msgstr "Ne može se dodati filtar na nadzornu ploču"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/js/board_view.js:0
#, python-format
msgid "Edit Layout"
msgstr "Uredi raspored"

#. module: board
#: model:ir.model.fields,field_description:board.field_board_board__id
msgid "ID"
msgstr "ID"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Layout"
msgstr "Izgled"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/js/board_view.js:0
#: model:ir.actions.act_window,name:board.open_board_my_dash_action
#: model:ir.ui.menu,name:board.menu_board_my_dash
#: model_terms:ir.ui.view,arch_db:board.board_my_dash_view
#, python-format
msgid "My Dashboard"
msgstr "Moja nadzorna ploča"

#. module: board
#. openerp-web
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
#: code:addons/board/static/src/legacy/js/add_to_board_menu.js:0
#, python-format
msgid "Please refresh your browser for the changes to take effect."
msgstr "Molimo osvježite vaš preglednik kako bi izmjene bile vidljive."

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid ""
"To add your first report into this dashboard, go to any\n"
"                menu, switch to list or graph view, and click"
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid ""
"You can filter and group data before inserting into the\n"
"                dashboard using the search options."
msgstr ""

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Your personal dashboard is empty"
msgstr "Vaša nadzorna ploča je prazna"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "in the extended search options."
msgstr "U opcijama naprednog pretraživanja."
