# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_picking_batch
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>rawit <PERSON>hue <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON>ichano<PERSON> Jamwutthipreecha, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: Rasar<PERSON>yar Lappiam, 2023\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>FROM</strong>"
msgstr "<strong>จาก</strong>"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>Lot/Serial Number</strong>"
msgstr "<strong>ล็อค/เลขซีเรียล</strong>"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>Package</strong>"
msgstr "<strong>แพ็คเกจ</strong>"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>Product Barcode</strong>"
msgstr "<strong>บาร์โค้ดสินค้า</strong>"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>Responsible:</strong>"
msgstr "<strong>รับผิดชอบ:</strong>"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>TO</strong>"
msgstr "<strong>ถึง</strong>"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "<strong>→</strong>"
msgstr "<strong>→</strong>"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_needaction
msgid "Action Needed"
msgstr "ต้องดำเนินการ"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_ids
msgid "Activities"
msgstr "กิจกรรม"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "การตกแต่งข้อยกเว้นกิจกรรม"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_state
msgid "Activity State"
msgstr "สถานะกิจกรรม"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_type_icon
msgid "Activity Type Icon"
msgstr "ไอคอนประเภทกิจกรรม"

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/models/stock_picking.py:0
#: code:addons/stock_picking_batch/wizard/stock_add_to_wave.py:0
#, python-format
msgid "Add Operations"
msgstr "เพิ่มการดำเนินการ"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_to_batch_form
msgid "Add pickings to"
msgstr "เพิ่มการเบิกสินค้าไปที่"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_add_to_wave_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_to_batch_form
msgid "Add to"
msgstr "เพิ่มไปยัง"

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/models/stock_move_line.py:0
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_add_to_wave_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.view_move_line_tree_detailed_wave
#, python-format
msgid "Add to Wave"
msgstr "เพิ่มลงในกลุ่ม"

#. module: stock_picking_batch
#: model:ir.actions.act_window,name:stock_picking_batch.stock_picking_to_batch_action_stock_picking
msgid "Add to batch"
msgstr "เพิ่มลงในชุด"

#. module: stock_picking_batch
#: model:ir.actions.act_window,name:stock_picking_batch.stock_add_to_wave_action_stock_picking
msgid "Add to wave"
msgstr "เพิ่มลงในกลุ่ม"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Allocation"
msgstr "การจัดสรร"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__allowed_picking_ids
msgid "Allowed Picking"
msgstr "อนุญาตให้เบิกสินค้าได้"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_attachment_count
msgid "Attachment Count"
msgstr "จํานวนสิ่งที่แนบมา"

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_backorder_confirmation
msgid "Backorder Confirmation"
msgstr "ยืนยันสั่งซื้อล่วงหน้า"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Barcode"
msgstr "บาร์โค้ด"

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
#: model:ir.actions.report,name:stock_picking_batch.action_report_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_move_line__batch_id
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking__batch_id
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__name
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__batch_id
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
#, python-format
msgid "Batch Transfer"
msgstr "การโอนย้ายเป็นกลุ่ม"

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_picking_to_batch
msgid "Batch Transfer Lines"
msgstr "รายการโอนย้ายเป็นชุด"

#. module: stock_picking_batch
#: model:ir.actions.act_window,name:stock_picking_batch.stock_picking_batch_action
#: model:ir.ui.menu,name:stock_picking_batch.stock_picking_batch_menu
msgid "Batch Transfers"
msgstr "การโอนเป็นชุด"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Batch Transfers not finished"
msgstr "การโอนย้ายเป็นชุดไม่เสร็จสิ้น"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_move_line__batch_id
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking__batch_id
msgid "Batch associated to this transfer"
msgstr "ชุดที่เกี่ยวข้องกับการโอนย้ายนี้"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_type_kanban_batch
msgid "Batches"
msgstr "ชุด"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_add_to_wave_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_to_batch_form
msgid "Cancel"
msgstr "ยกเลิก"

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_picking_batch__state__cancel
msgid "Cancelled"
msgstr "ยกเลิกแล้ว"

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/wizard/stock_add_to_wave.py:0
#, python-format
msgid "Cannot create wave transfers"
msgstr "ไม่สามารถสร้างการโอนย้ายเป็นกลุ่มได้"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Check Availability"
msgstr "สำรองสินค้า"

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
#, python-format
msgid "Choose Labels Layout"
msgstr "เลือกเค้าโครงป้ายกำกับ"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__company_id
msgid "Company"
msgstr "บริษัท"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_add_to_wave_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_to_batch_form
msgid "Confirm"
msgstr "ยืนยัน"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__count_picking_batch
msgid "Count Picking Batch"
msgstr "จำนวนการเบิกสินค้าเป็นชุด"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_type__count_picking_wave
msgid "Count Picking Wave"
msgstr "จำนวนการเบิกสินค้าเป็นกลุ่ม"

#. module: stock_picking_batch
#: model_terms:ir.actions.act_window,help:stock_picking_batch.stock_picking_batch_action
msgid "Create a new batch transfer"
msgstr "สร้างการโอนย้ายชุดใหม่"

#. module: stock_picking_batch
#: model_terms:ir.actions.act_window,help:stock_picking_batch.action_picking_tree_wave
msgid "Create a new wave transfer"
msgstr "สร้างการโอนย้ายใหม่"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__create_uid
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__create_uid
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__create_date
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__create_date
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Detailed Operations"
msgstr "รายละเอียดการดำเนินงาน"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__display_name
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__display_name
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__display_name
msgid "Display Name"
msgstr "ชื่อที่ใช้แสดง"

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_picking_batch__state__done
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Done"
msgstr "เสร็จสิ้น"

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_picking_batch__state__draft
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Draft"
msgstr "ฉบับร่าง"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_follower_ids
msgid "Followers"
msgstr "ผู้ติดตาม"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_partner_ids
msgid "Followers (Partners)"
msgstr "ผู้ติดตาม (คู่ค้า)"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "ไอคอนแบบอักษรที่ยอดเยี่ยมเช่น fa-tasks"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Future Activities"
msgstr "กิจกรรมในอนาคต"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Group By"
msgstr "จัดกลุ่มตาม"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__has_message
msgid "Has Message"
msgstr "มีข้อความ"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__id
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__id
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__id
msgid "ID"
msgstr "รหัส"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_exception_icon
msgid "Icon"
msgstr "ไอคอน"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "ไอคอนเพื่อระบุกิจกรรมการยกเว้น"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_needaction
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_unread
msgid "If checked, new messages require your attention."
msgstr "ถ้าเลือก ข้อความใหม่จะต้องการความสนใจจากคุณ"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_has_error
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "ถ้าเลือก ข้อความบางข้อความมีข้อผิดพลาดในการส่ง"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "In Progress"
msgstr "กำลังดำเนินการ"

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_picking_batch__state__in_progress
msgid "In progress"
msgstr "อยู่ระหว่างดำเนินการ"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_is_follower
msgid "Is Follower"
msgstr "เป็นผู้ติดตาม"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave____last_update
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch____last_update
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งสุดท้ายเมื่อ"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__write_uid
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__write_uid
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__write_uid
msgid "Last Updated by"
msgstr "อัพเดทครั้งสุดท้ายโดย"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__write_date
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__write_date
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__write_date
msgid "Last Updated on"
msgstr "อัพเดทครั้งสุดท้ายเมื่อ"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Late Activities"
msgstr "กิจกรรม"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__line_ids
msgid "Line"
msgstr "Line"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__picking_ids
msgid "List of transfers associated to this batch"
msgstr "รายการโอนย้ายที่เกี่ยวข้องกับชุดนี้"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_main_attachment_id
msgid "Main Attachment"
msgstr "เอกสารหลักที่แนบมา"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_has_error
msgid "Message Delivery error"
msgstr "เกิดการผิดพลาดในการส่งข้อความ"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_ids
msgid "Messages"
msgstr "ข้อความ"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__mode
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__mode
msgid "Mode"
msgstr "โหมด"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.view_move_line_tree
msgid "Move Lines"
msgstr "ย้ายรายการ"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "หมดเขตกิจกรรมของฉัน"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__name
msgid "Name of the batch transfer"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "ปฏิทินอีเวนต์กิจกรรมถัดไป"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "กำหนดกิจกรรมสุดท้าย"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_summary
msgid "Next Activity Summary"
msgstr "สรุปกิจกรรมถัดไป"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_type_id
msgid "Next Activity Type"
msgstr "ประเภทกิจกรรมถัดไป"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_needaction_counter
msgid "Number of Actions"
msgstr "จํานวนการดําเนินการ"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_has_error_counter
msgid "Number of errors"
msgstr "จํานวนข้อผิดพลาด"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "จํานวนข้อความที่ต้องการการดําเนินการ"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "จํานวนข้อความที่มีข้อผิดพลาดในการส่ง"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__message_unread_counter
msgid "Number of unread messages"
msgstr "จํานวนข้อความที่ยังไม่ได้อ่าน"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__picking_type_id
msgid "Operation Type"
msgstr "ประเภทการดำเนินงาน"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Operations"
msgstr "การดำเนินการ"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__user_id
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_to_batch__user_id
msgid "Person responsible for this batch transfer"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_add_to_wave__user_id
msgid "Person responsible for this wave transfer"
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__picking_ids
msgid "Picking"
msgstr "การรับสินค้า"

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_picking_type
msgid "Picking Type"
msgstr "ประเภทการรับสินค้า"

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
#, python-format
msgid ""
"Please add 'Done' quantities to the batch picking to create a new pack."
msgstr "โปรดเพิ่มจำนวน 'เสร็จสิ้น' ให้กับการเลือกชุดงาน เพื่อสร้างแพ็คใหม่"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Print"
msgstr "พิมพ์"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Print Labels"
msgstr "พิมพ์ฉลาก"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Product"
msgstr "สินค้า"

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "เคลื่อนย้ายสินค้า ( ในสต็อค )"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Put in Pack"
msgstr "ใส่ในแพ็ค"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Quantity"
msgstr "จำนวน"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__user_id
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__user_id
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_to_batch__user_id
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Responsible"
msgstr "รับผิดชอบ"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__activity_user_id
msgid "Responsible User"
msgstr "ผู้ใช้ที่รับผิดชอบ"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_has_sms_error
msgid "SMS Delivery error"
msgstr "ข้อผิดพลาดในการส่ง SMS"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__scheduled_date
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Scheduled Date"
msgstr "วันที่ตามกำหนดการ"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__scheduled_date
msgid ""
"Scheduled date for the transfers to be processed.\n"
"              - If manually set then scheduled date for all transfers in batch will automatically update to this date.\n"
"              - If not manually changed and transfers are added/removed/updated then this will be their earliest scheduled date\n"
"                but this scheduled date will not be set for all transfers in batch."
msgstr ""
"วันที่กำหนดสำหรับการโอนย้ายที่จะดำเนินการ\n"
"- หากตั้งค่าด้วยตนเอง วันที่กำหนดไว้สำหรับการโอนทั้งหมดในชุดจะอัปเดตเป็นวันที่นี้โดยอัตโนมัติ\n"
"- หากไม่ได้เปลี่ยนแปลงด้วยตนเองและมีการเพิ่ม/ลบ/อัปเดตการถ่ายโอน จะเป็นวันที่กำหนดไว้เร็วที่สุด\n"
"แต่วันที่กำหนดนี้จะไม่ถูกกำหนดไว้สำหรับการโอนย้ายทั้งหมดในชุด"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Search Batch Transfer"
msgstr "ค้นหาการโอนย้ายเป็นชุด"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Set quantities"
msgstr "กำหนดปริมาณ"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__show_allocation
msgid "Show Allocation"
msgstr "แสดงการจัดสรร"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__show_check_availability
msgid "Show Check Availability"
msgstr "แสดงรายการตรวจสอบความพร้อมจำหน่ายสินค้า"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__show_validate
msgid "Show Validate"
msgstr "แสดงการตรวจสอบ"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Show all records which has next action date is before today"
msgstr "Show all records which has next action date is before today"

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
#, python-format
msgid ""
"Some transfers are still waiting for goods. Please check or force their "
"availability before setting this batch to done."
msgstr ""

#. module: stock_picking_batch
#: model:mail.message.subtype,description:stock_picking_batch.mt_batch_state
#: model:mail.message.subtype,name:stock_picking_batch.mt_batch_state
msgid "Stage Changed"
msgstr "สถานะเปลี่ยนแปลง"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__state
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "State"
msgstr "สถานะ"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Status"
msgstr "สถานะ"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_tree
msgid "Stock Batch Transfer"
msgstr "การโอนย้ายสต็อกเป็นชุด"

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_package_destination
msgid "Stock Package Destination"
msgstr "ปลายทางพัสดุภัณฑ์"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__move_line_ids
msgid "Stock move lines"
msgstr "รายการย้ายสต็อก"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__move_ids
msgid "Stock moves"
msgstr "การย้ายสต็อก"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Summary:"
msgstr "สรุป"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__show_allocation
msgid ""
"Technical Field used to decide whether the button \"Allocation\" should be "
"displayed."
msgstr "ฟิลด์ทางเทคนิคใช้เพื่อตัดสินใจว่าควรแสดงปุ่ม \"การจัดสรร\" หรือไม่"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__show_check_availability
msgid ""
"Technical field used to compute whether the check availability button should"
" be shown."
msgstr ""

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__show_validate
msgid ""
"Technical field used to decide whether the validate button should be shown."
msgstr ""

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
#, python-format
msgid ""
"The following transfers cannot be added to batch transfer %s. Please check their states and operation types, if they aren't immediate transfers.\n"
"\n"
"Incompatibilities: %s"
msgstr ""
"ไม่สามารถเพิ่มการโอนย้ายต่อไปนี้ในการโอนย้ายแบบชุด %s โปรดตรวจสอบสถานะและประเภทการดำเนินการว่าไม่ใช่การโอนทันที\n"
"\n"
"ความเข้ากันไม่ได้: %s"

#. module: stock_picking_batch
#: model_terms:ir.actions.act_window,help:stock_picking_batch.stock_picking_batch_action
msgid ""
"The goal of the batch transfer is to group operations that may\n"
"            (needs to) be done together in order to increase their efficiency.\n"
"            It may also be useful to assign jobs (one person = one batch) or\n"
"            help the timing management of operations (tasks to be done at 1pm)."
msgstr ""
"เป้าหมายของการโอนย้ายแบบกลุ่มคือการจัดกลุ่มการปฏิบัติงานที่อาจ\n"
"             (จำเป็นต้อง) ทำร่วมกันเพื่อเพิ่มประสิทธิภาพการดำเนินการ\n"
"นอกจากนี้ยังอาจเป็นประโยชน์ในการมอบหมายงาน             (หนึ่งคน = หนึ่งกลุ่ม) หรือ\n"
"             ช่วยบริหารจัดการเวลาการดำเนินงาน (งานเสร็จ 13.00 น.)"

#. module: stock_picking_batch
#: model_terms:ir.actions.act_window,help:stock_picking_batch.action_picking_tree_wave
msgid ""
"The goal of the wave transfer is to group operations from differents transfer\n"
"                    together in order to increase their efficiency.\n"
"                    It may also be useful to assign jobs (one person = one batch) or\n"
"                    help the timing management of operations (tasks to be done at 1pm)."
msgstr ""

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/wizard/stock_add_to_wave.py:0
#, python-format
msgid "The selected operations should belong to a unique company."
msgstr "การดำเนินงานที่เลือกควรเป็นของบริษัทที่ไม่ซ้ำใคร"

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/wizard/stock_picking_to_batch.py:0
#, python-format
msgid "The selected pickings should belong to an unique company."
msgstr "การเบิกสินค้าที่เลือกควรเป็นของบริษัทที่ไม่ซ้ำใคร"

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/wizard/stock_add_to_wave.py:0
#, python-format
msgid "The selected transfers should belong to a unique company."
msgstr "การโอนย้ายที่เลือกควรเป็นของบริษัทที่ไม่ซ้ำกัน"

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/wizard/stock_add_to_wave.py:0
#, python-format
msgid "The selected transfers should belong to the same operation type"
msgstr "การโอนย้ายที่เลือกควรเป็นประเภทการดำเนินการเดียวกัน"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__is_wave
msgid "This batch is a wave"
msgstr "ชุดนี้เป็นกลุ่ม"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_filter
msgid "Today Activities"
msgstr "กิจกรรมวันนี้"

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_picking
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.report_picking_batch
msgid "Transfer"
msgstr "การโอน"

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
#, python-format
msgid "Transferred by"
msgstr "โอนย้ายแล้ว โดย"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__picking_ids
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Transfers"
msgstr "โอนย้าย"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__picking_type_code
msgid "Type of Operation"
msgstr "ประเภทของการดำเนินการ"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "พิมพ์กิจกรรมข้อยกเว้นบนบันทึก"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_unread
msgid "Unread Messages"
msgstr "ข้อความที่ยังไม่ได้อ่าน"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__message_unread_counter
msgid "Unread Messages Counter"
msgstr "ตัวนับข้อความที่ยังไม่ได้อ่าน"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_batch_form
msgid "Validate"
msgstr "ตรวจสอบ"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_add_to_wave__wave_id
msgid "Wave Transfer"
msgstr "การโอนย้ายเป็นกลุ่ม"

#. module: stock_picking_batch
#: model:ir.model,name:stock_picking_batch.model_stock_add_to_wave
msgid "Wave Transfer Lines"
msgstr "รายการโอนย้ายแบบกลุ่ม"

#. module: stock_picking_batch
#: model:ir.actions.act_window,name:stock_picking_batch.action_picking_tree_wave
#: model:ir.ui.menu,name:stock_picking_batch.stock_picking_wave_menu
msgid "Wave Transfers"
msgstr "การโอนย้ายเป็นกลุ่ม"

#. module: stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_picking_batch.stock_picking_type_kanban_batch
msgid "Waves"
msgstr "กลุ่ม"

#. module: stock_picking_batch
#: model:ir.model.fields,field_description:stock_picking_batch.field_stock_picking_batch__website_message_ids
msgid "Website Messages"
msgstr "ข้อความเว็บไซต์"

#. module: stock_picking_batch
#: model:ir.model.fields,help:stock_picking_batch.field_stock_picking_batch__website_message_ids
msgid "Website communication history"
msgstr "ประวัติการสื่อสารของเว็บไซต์"

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
#, python-format
msgid "You cannot delete Done batch transfers."
msgstr "คุณไม่สามารถลบการโอนย้ายแบบชุดที่เสร็จสิ้นแล้วได้"

#. module: stock_picking_batch
#: code:addons/stock_picking_batch/models/stock_picking_batch.py:0
#, python-format
msgid "You have to set some pickings to batch."
msgstr "คุณต้องตั้งค่าการเลือกบางส่วนเป็นชุดงาน"

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_picking_to_batch__mode__new
msgid "a new batch transfer"
msgstr "การโอนย้ายชุดใหม่"

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_add_to_wave__mode__new
msgid "a new wave transfer"
msgstr "การโอนย้ายแบบกลุ่มใหม่"

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_picking_to_batch__mode__existing
msgid "an existing batch transfer"
msgstr "การโอนย้ายเป็นชุดที่มีอยู่"

#. module: stock_picking_batch
#: model:ir.model.fields.selection,name:stock_picking_batch.selection__stock_add_to_wave__mode__existing
msgid "an existing wave transfer"
msgstr "การโอนย้ายเป็นกลุ่มที่มีอยู่"
