# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * iap
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-10 11:35+0000\n"
"PO-Revision-Date: 2017-10-10 11:35+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Kabyle (https://www.transifex.com/odoo/teams/41243/kab/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: kab\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid "Account Information"
msgstr ""

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account_account_token
msgid "Account Token"
msgstr ""

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/js/crash_manager.js:39
#, python-format
msgid "Cancel"
msgstr "Sefsex"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account_company_id
msgid "Company"
msgstr "Takebbwanit"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account_create_uid
msgid "Created by"
msgstr "Yerna-t"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account_create_date
msgid "Created on"
msgstr "Yerna di"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account_display_name
msgid "Display Name"
msgstr ""

#. module: iap
#: model:ir.ui.menu,name:iap.iap_root_menu
msgid "IAP"
msgstr ""

#. module: iap
#: model:ir.actions.act_window,name:iap.iap_account_action
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid "IAP Account"
msgstr ""

#. module: iap
#: model:ir.ui.menu,name:iap.iap_account_menu
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_tree
msgid "IAP Accounts"
msgstr ""

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account_id
msgid "ID"
msgstr "Asulay"

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/xml/iap_templates.xml:22
#, python-format
msgid "In-App Purchases"
msgstr ""

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/js/crash_manager.js:31
#, python-format
msgid "Insufficient Balance"
msgstr ""

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/xml/iap_templates.xml:14
#, python-format
msgid "Insufficient credit to perform this service."
msgstr ""

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account___last_update
msgid "Last Modified on"
msgstr "Aleqqem aneggaru di"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account_write_uid
msgid "Last Updated by"
msgstr "Aleqqem aneggaru sɣuṛ"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account_write_date
msgid "Last Updated on"
msgstr "Aleqqem aneggaru di"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account_service_name
msgid "Service Name"
msgstr ""

#. module: iap
#: model:ir.model,name:iap.model_iap_account
msgid "iap.account"
msgstr ""
