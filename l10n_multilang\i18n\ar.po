# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * l10n_multilang
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 14:33+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_account
msgid "Account"
msgstr "الحساب "

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_chart_template
msgid "Account Chart Template"
msgstr "قالب مخطط الحساب"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_account_tag
msgid "Account Tag"
msgstr "علامة تصنيف الحساب "

#. module: l10n_multilang
#: model:ir.model.fields,help:l10n_multilang.field_res_country_state__name
msgid ""
"Administrative divisions of a country. E.g. Fed. State, Departement, Canton"
msgstr "التقسيم الإداري للدولة. مثال: ولايات فيدرالية، أقسام، أقاليم"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_analytic_account
#: model:ir.model.fields,field_description:l10n_multilang.field_account_analytic_account__name
msgid "Analytic Account"
msgstr "الحساب التحليلي"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_res_country_state
msgid "Country state"
msgstr "الولاية "

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_tax_template__description
msgid "Display on Invoices"
msgstr "العرض على الفواتير "

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_fiscal_position
#: model:ir.model.fields,field_description:l10n_multilang.field_account_fiscal_position__name
msgid "Fiscal Position"
msgstr "الوضع المالي "

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_fiscal_position_template__name
msgid "Fiscal Position Template"
msgstr "قالب الوضع المالي "

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_base_language_install
msgid "Install Language"
msgstr "تثبيت اللغة"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_journal
msgid "Journal"
msgstr "دفتر اليومية"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_journal__name
msgid "Journal Name"
msgstr "اسم دفتر اليومية"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_tax__description
msgid "Label on Invoices"
msgstr "تسمية الفواتير"

#. module: l10n_multilang
#: model:ir.model.fields,help:l10n_multilang.field_account_fiscal_position__note
msgid "Legal mentions that have to be printed on the invoices."
msgstr "الإشارات القانونية التي يجب طباعتها على الفواتير."

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_account__name
#: model:ir.model.fields,field_description:l10n_multilang.field_account_account_tag__name
#: model:ir.model.fields,field_description:l10n_multilang.field_account_account_template__name
#: model:ir.model.fields,field_description:l10n_multilang.field_account_chart_template__name
msgid "Name"
msgstr "الاسم"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_fiscal_position__note
#: model:ir.model.fields,field_description:l10n_multilang.field_account_fiscal_position_template__note
msgid "Notes"
msgstr "الملاحظات"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_chart_template__spoken_languages
msgid "Spoken Languages"
msgstr "اللغات المتحدث بها "

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_res_country_state__name
msgid "State Name"
msgstr "اسم الولاية"

#. module: l10n_multilang
#: model:ir.model.fields,help:l10n_multilang.field_account_chart_template__spoken_languages
msgid ""
"State here the languages for which the translations of templates could be "
"loaded at the time of installation of this localization module and copied in"
" the final object when generating them from templates. You must provide the "
"language codes separated by ';'"
msgstr ""
"اذكر هنا اللغات التي يمكن تحميل ترجمات القوالب لها في وقت تثبيت تطبيق "
"الأقلمة هذا ونسخ في الكائن النهائي عند إنشائها من القوالب. يجب توفير رموز "
"لغة مفصولة بـ '؛' "

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_tax
msgid "Tax"
msgstr "الضريبة"

#. module: l10n_multilang
#: model:ir.model.fields,field_description:l10n_multilang.field_account_tax__name
#: model:ir.model.fields,field_description:l10n_multilang.field_account_tax_template__name
msgid "Tax Name"
msgstr "اسم الضريبة"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_fiscal_position_template
msgid "Template for Fiscal Position"
msgstr "قالب الوضع المالي "

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_account_template
msgid "Templates for Accounts"
msgstr "قوالب الحسابات"

#. module: l10n_multilang
#: model:ir.model,name:l10n_multilang.model_account_tax_template
msgid "Templates for Taxes"
msgstr "قوالب الضرائب"
