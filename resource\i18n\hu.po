# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* resource
# 
# Translators:
# <PERSON><PERSON>, 2021
# <PERSON>, 2021
# krnkris, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON>gy <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Hungarian (https://app.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: resource
#: code:addons/resource/models/resource.py:0
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (másolat)"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__active
#: model:ir.model.fields,field_description:resource.field_resource_resource__active
msgid "Active"
msgstr "Aktív"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__day_period__afternoon
msgid "Afternoon"
msgstr "Délután"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_resource_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Archived"
msgstr "Archivált"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid ""
"Are you sure you want to switch this calendar to 1 week calendar ? All "
"entries will be lost"
msgstr ""
"Biztos benne, hogy átvált egyhetes naptárra? Minden adata el fog veszni"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid ""
"Are you sure you want to switch this calendar to 2 weeks calendar ? All "
"entries will be lost"
msgstr ""
"Biztos benne, hogy átvált kéthetes naptárra? Minden adata el fog veszni"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Attendances can't overlap."
msgstr "A jelenlétek nem fedhetik egymást."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__hours_per_day
msgid "Average Hour per Day"
msgstr "Átlagóra naponta"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar__hours_per_day
msgid ""
"Average hours per day a resource is supposed to work with this calendar."
msgstr ""

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__two_weeks_calendar
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__two_weeks_calendar
msgid "Calendar in 2 weeks mode"
msgstr "Naptár kéthetes módban"

#. module: resource
#: model:ir.actions.act_window,name:resource.resource_calendar_closing_days
msgid "Closing Days"
msgstr "Closing Days"

#. module: resource
#: model:ir.model,name:resource.model_res_company
msgid "Companies"
msgstr "Vállalatok"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__company_id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__company_id
#: model:ir.model.fields,field_description:resource.field_resource_mixin__company_id
#: model:ir.model.fields,field_description:resource.field_resource_resource__company_id
#: model:ir.model.fields,field_description:resource.field_resource_test__company_id
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Company"
msgstr "Vállalat"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__create_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__create_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__create_uid
#: model:ir.model.fields,field_description:resource.field_resource_resource__create_uid
#: model:ir.model.fields,field_description:resource.field_resource_test__create_uid
msgid "Created by"
msgstr "Létrehozta"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__create_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__create_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__create_date
#: model:ir.model.fields,field_description:resource.field_resource_resource__create_date
#: model:ir.model.fields,field_description:resource.field_resource_test__create_date
msgid "Created on"
msgstr "Létrehozva"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__day_period
msgid "Day Period"
msgstr "Napi időszak"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__dayofweek
msgid "Day of Week"
msgstr "A hét napja"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_res_company__resource_calendar_id
#: model:ir.model.fields,field_description:resource.field_res_users__resource_calendar_id
msgid "Default Working Hours"
msgstr "Alapértelmezett munkaórák"

#. module: resource
#: model:ir.model.fields,help:resource.field_res_users__resource_calendar_id
#: model:ir.model.fields,help:resource.field_resource_mixin__resource_calendar_id
#: model:ir.model.fields,help:resource.field_resource_resource__calendar_id
#: model:ir.model.fields,help:resource.field_resource_test__resource_calendar_id
msgid "Define the schedule of resource"
msgstr "Az erőforrás ütemezésének meghatározása"

#. module: resource
#: model_terms:ir.actions.act_window,help:resource.action_resource_calendar_form
msgid ""
"Define working hours and time table that could be scheduled to your project "
"members"
msgstr ""
"Határozzon meg munkaórákat és idő táblát melyet hozzárendelhet a  projekt "
"téma munka csoport tagjaihoz."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__display_name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__display_name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__display_name
#: model:ir.model.fields,field_description:resource.field_resource_resource__display_name
#: model:ir.model.fields,field_description:resource.field_resource_test__display_name
msgid "Display Name"
msgstr "Név megjelenítése"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__display_type
msgid "Display Type"
msgstr "Típus megjeleítése"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__time_efficiency
msgid "Efficiency Factor"
msgstr "Hatékonysági tényező"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__date_to
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__date_to
msgid "End Date"
msgstr "Záródátum"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__two_weeks_explanation
msgid "Explanation"
msgstr "Magyarázat"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__week_type__0
msgid "First"
msgstr "Első"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "First week"
msgstr "Első hét"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__4
msgid "Friday"
msgstr "Péntek"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Friday Afternoon"
msgstr "Péntek délután"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Friday Morning"
msgstr "Péntek reggel"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_attendance__sequence
msgid "Gives the sequence of this line when displaying the resource calendar."
msgstr ""

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__global_leave_ids
msgid "Global Time Off"
msgstr ""

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Group By"
msgstr "Csoportosítás"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_attendance_form
msgid "Hours"
msgstr "óra"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_resource__resource_type__user
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Human"
msgstr "Ember"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__id
#: model:ir.model.fields,field_description:resource.field_resource_resource__id
#: model:ir.model.fields,field_description:resource.field_resource_test__id
msgid "ID"
msgstr "Azonosító"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_leaves__resource_id
msgid ""
"If empty, this is a generic time off for the company. If a resource is set, "
"the time off is only for this resource"
msgstr ""

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource__active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"Ha az aktív mező hamisra van állítva, akkor elrejtheti az erőforrást, "
"anélkül, hogy törölné azt."

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar__active
msgid ""
"If the active field is set to false, it will allow you to hide the Working "
"Time without removing it."
msgstr ""

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Impossible to switch calendar type for the default company schedule."
msgstr ""

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid ""
"In a calendar with 2 weeks mode, all periods need to be in the sections."
msgstr ""

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar____last_update
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance____last_update
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves____last_update
#: model:ir.model.fields,field_description:resource.field_resource_resource____last_update
#: model:ir.model.fields,field_description:resource.field_resource_test____last_update
msgid "Last Modified on"
msgstr "Legutóbb módosítva"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__write_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__write_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__write_uid
#: model:ir.model.fields,field_description:resource.field_resource_resource__write_uid
#: model:ir.model.fields,field_description:resource.field_resource_test__write_uid
msgid "Last Updated by"
msgstr "Frissítette"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__write_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__write_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__write_date
#: model:ir.model.fields,field_description:resource.field_resource_resource__write_date
#: model:ir.model.fields,field_description:resource.field_resource_test__write_date
msgid "Last Updated on"
msgstr "Frissítve "

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Leave Date"
msgstr "Távollét dátuma"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_form
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_tree
msgid "Leave Detail"
msgstr "Távollét részletei"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_resource__resource_type__material
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Material"
msgstr "Anyag"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__0
msgid "Monday"
msgstr "Hétfő"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Monday Afternoon"
msgstr "Hétfő délután"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Monday Morning"
msgstr "Hétfő reggel"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__day_period__morning
msgid "Morning"
msgstr "Reggel"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__name
#: model:ir.model.fields,field_description:resource.field_resource_resource__name
#: model:ir.model.fields,field_description:resource.field_resource_test__name
msgid "Name"
msgstr "Név"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_leaves__time_type__other
msgid "Other"
msgstr "Egyéb"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Period"
msgstr "Időszak"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__name
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_form
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_tree
msgid "Reason"
msgstr "Ok"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource__user_id
msgid "Related user name for the resource to manage its access."
msgstr ""
"Az erőforráshoz kapcsolódó felhasználó neve, aki annak hozzáférését kezeli."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__resource_id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__resource_id
#: model:ir.model.fields,field_description:resource.field_resource_mixin__resource_id
#: model:ir.model.fields,field_description:resource.field_resource_test__resource_id
#: model:ir.ui.menu,name:resource.menu_resource_config
#: model_terms:ir.ui.view,arch_db:resource.resource_resource_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Resource"
msgstr "Erőforrás"

#. module: resource
#: model:ir.model,name:resource.model_resource_mixin
msgid "Resource Mixin"
msgstr ""

#. module: resource
#: model:ir.actions.act_window,name:resource.action_resource_calendar_leave_tree
#: model:ir.actions.act_window,name:resource.resource_calendar_leaves_action_from_calendar
#: model:ir.ui.menu,name:resource.menu_view_resource_calendar_leaves_search
msgid "Resource Time Off"
msgstr ""

#. module: resource
#: model:ir.model,name:resource.model_resource_calendar_leaves
msgid "Resource Time Off Detail"
msgstr ""

#. module: resource
#: model:ir.model,name:resource.model_resource_calendar
msgid "Resource Working Time"
msgstr "Erőforrás munkaidő"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__calendar_id
msgid "Resource's Calendar"
msgstr "Erőforrás naptára"

#. module: resource
#: model:ir.actions.act_window,name:resource.action_resource_resource_tree
#: model:ir.actions.act_window,name:resource.resource_resource_action_from_calendar
#: model:ir.model,name:resource.model_resource_resource
#: model:ir.model.fields,field_description:resource.field_res_users__resource_ids
#: model:ir.ui.menu,name:resource.menu_resource_resource
#: model_terms:ir.ui.view,arch_db:resource.resource_resource_tree
msgid "Resources"
msgstr "Erőforrások"

#. module: resource
#: model:ir.actions.act_window,name:resource.resource_calendar_resources_leaves
msgid "Resources Time Off"
msgstr ""

#. module: resource
#: model_terms:ir.actions.act_window,help:resource.action_resource_resource_tree
#: model_terms:ir.actions.act_window,help:resource.resource_resource_action_from_calendar
msgid ""
"Resources allow you to create and manage resources that should be involved "
"in a specific project phase. You can also set their efficiency level and "
"workload based on their weekly working hours."
msgstr ""
"Az erőforrások modul lehetővé teszi Önnek, hogy létrehozza és kezelje azokat"
" az erőforrásokat, amelyeket be szeretne vonni egy speciális projekt "
"fázisaiba. Beállíthatja az erőforrások hatékonysági fokát és "
"munkaterheltségét azok heti munkaórái alapján."

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__5
msgid "Saturday"
msgstr "Szombat"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Search Resource"
msgstr "Erőforrás keresése"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Search Working Period Time Off"
msgstr ""

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_search
msgid "Search Working Time"
msgstr "Munkaidő keresése"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__week_type__1
msgid "Second"
msgstr "Második"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Second week"
msgstr "Második hét"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__display_type__line_section
msgid "Section"
msgstr "Szakasz"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__sequence
msgid "Sequence"
msgstr "Sorszám"

#. module: resource
#: code:addons/resource/models/res_company.py:0
#, python-format
msgid "Standard 40 hours/week"
msgstr "Normál 40 óra/hét"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__date_from
msgid "Start Date"
msgstr "Kezdődátum"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_attendance__hour_from
msgid ""
"Start and End time of working.\n"
"A specific value of 24:00 is interpreted as 23:59:59.999999."
msgstr ""
"A munka kezdő és befejező időpontja.\n"
"A 24:00 speciális érték 23:59:59:999999-ként lesz értelmezve."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__date_from
msgid "Starting Date"
msgstr "Kezdés dátuma"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Starting Date of Leave"
msgstr "A távollét kezdő dátuma"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__6
msgid "Sunday"
msgstr "Vasárnap"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Switch to 1 week calendar"
msgstr "Váltás egyhetes naptárra"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Switch to 2 weeks calendar"
msgstr "Váltás kéthetes naptárra"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_attendance__display_type
msgid "Technical field for UX purpose."
msgstr "Technikai mező UX célokra."

#. module: resource
#: model:ir.model,name:resource.model_resource_test
msgid "Test Resource Model"
msgstr ""

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "The current week (from %s to %s) correspond to the  %s one."
msgstr ""

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "The start date of the time off must be earlier than the end date."
msgstr ""
"A szabadság kezdő dátumának korábbinak kell lennie, mint a befejező dátuma."

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar__tz
#: model:ir.model.fields,help:resource.field_resource_mixin__tz
#: model:ir.model.fields,help:resource.field_resource_resource__tz
#: model:ir.model.fields,help:resource.field_resource_test__tz
msgid ""
"This field is used in order to define in which timezone the resources will "
"work."
msgstr ""

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource__time_efficiency
msgid ""
"This field is used to calculate the expected duration of a work order at "
"this work center. For example, if a work order takes one hour and the "
"efficiency factor is 100%, then the expected duration will be one hour. If "
"the efficiency factor is 200%, however the expected duration will be 30 "
"minutes."
msgstr ""

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__3
msgid "Thursday"
msgstr "Csütörtök"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Thursday Afternoon"
msgstr "Csütörtök délután"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Thursday Morning"
msgstr "Csütörtök reggel"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__leave_ids
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_leaves__time_type__leave
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Time Off"
msgstr "Szabadság"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__time_type
msgid "Time Type"
msgstr "Időtípus"

#. module: resource
#: model:ir.model.constraint,message:resource.constraint_resource_resource_check_time_efficiency
msgid "Time efficiency must be strictly positive"
msgstr "Az időhatékonyságnak mindenképpen pozitívnak kell lennie"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__tz
#: model:ir.model.fields,field_description:resource.field_resource_mixin__tz
#: model:ir.model.fields,field_description:resource.field_resource_resource__tz
#: model:ir.model.fields,field_description:resource.field_resource_test__tz
msgid "Timezone"
msgstr "Időzóna"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__tz_offset
msgid "Timezone offset"
msgstr "Időzóna eltolódás"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__1
msgid "Tuesday"
msgstr "Kedd"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Tuesday Afternoon"
msgstr "Kedd délután"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Tuesday Morning"
msgstr "Kedd reggel"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__resource_type
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Type"
msgstr "Típus"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__user_id
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "User"
msgstr "Felhasználó"

#. module: resource
#: model:ir.model,name:resource.model_res_users
msgid "Users"
msgstr "Felhasználók"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__2
msgid "Wednesday"
msgstr "Szerda"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Wednesday Afternoon"
msgstr "Szerda délután"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Wednesday Morning"
msgstr "Szerda reggel"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__week_type
msgid "Week Number"
msgstr "Hét száma"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_leaves__time_type
msgid ""
"Whether this should be computed as a time off or as work time (eg: "
"formation)"
msgstr ""

#. module: resource
#: model:ir.model,name:resource.model_resource_calendar_attendance
msgid "Work Detail"
msgstr "Munka részletei"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Work Resources"
msgstr "Munka erőforrások"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__hour_from
msgid "Work from"
msgstr "Munka kezdete"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__hour_to
msgid "Work to"
msgstr "Munka vége"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_res_company__resource_calendar_ids
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__calendar_id
#: model:ir.model.fields,field_description:resource.field_resource_mixin__resource_calendar_id
#: model:ir.model.fields,field_description:resource.field_resource_test__resource_calendar_id
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Working Hours"
msgstr "Munkaórák"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Working Hours of %s"
msgstr "%s munkaóra"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__attendance_ids
#: model:ir.model.fields,field_description:resource.field_resource_resource__calendar_id
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_attendance_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_attendance_tree
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_tree
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Working Time"
msgstr "Munkaidő"

#. module: resource
#: model:ir.actions.act_window,name:resource.action_resource_calendar_form
#: model:ir.ui.menu,name:resource.menu_resource_calendar
msgid "Working Times"
msgstr "Munkaidő"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "You can't delete section between weeks."
msgstr ""

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "first"
msgstr "első"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "other week"
msgstr "másik hét"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "second"
msgstr "második"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "this week"
msgstr "ez a hét"
