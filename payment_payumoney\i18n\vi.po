# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_payumoney
# 
# Translators:
# <PERSON>, 2021
# Duy <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>hi <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:49+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: payment_payumoney
#: code:addons/payment_payumoney/models/payment_transaction.py:0
#, python-format
msgid "Invalid shasign: received %(sign)s, computed %(computed)s."
msgstr ""

#. module: payment_payumoney
#: model:ir.model.fields,field_description:payment_payumoney.field_payment_acquirer__payumoney_merchant_key
msgid "Merchant Key"
msgstr "Merchant Key"

#. module: payment_payumoney
#: model:ir.model.fields,field_description:payment_payumoney.field_payment_acquirer__payumoney_merchant_salt
msgid "Merchant Salt"
msgstr "Merchant Salt"

#. module: payment_payumoney
#: code:addons/payment_payumoney/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "Không tìm thấy giao dịch nào khớp với mã %s."

#. module: payment_payumoney
#: model:account.payment.method,name:payment_payumoney.payment_method_payumoney
#: model:ir.model.fields.selection,name:payment_payumoney.selection__payment_acquirer__provider__payumoney
msgid "PayUmoney"
msgstr "PayUmoney"

#. module: payment_payumoney
#: model:ir.model,name:payment_payumoney.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "NCC dịch vụ thanh toán"

#. module: payment_payumoney
#: model:ir.model,name:payment_payumoney.model_account_payment_method
msgid "Payment Methods"
msgstr "Phương thức thanh toán"

#. module: payment_payumoney
#: model:ir.model,name:payment_payumoney.model_payment_transaction
msgid "Payment Transaction"
msgstr "Giao dịch thanh toán"

#. module: payment_payumoney
#: model:ir.model.fields,field_description:payment_payumoney.field_payment_acquirer__provider
msgid "Provider"
msgstr "Nhà cung cấp"

#. module: payment_payumoney
#: code:addons/payment_payumoney/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing reference (%(ref)s) or shasign (%(sign)s)"
msgstr ""

#. module: payment_payumoney
#: model:ir.model.fields,help:payment_payumoney.field_payment_acquirer__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr ""

#. module: payment_payumoney
#: model:ir.model.fields,help:payment_payumoney.field_payment_acquirer__payumoney_merchant_key
msgid "The key solely used to identify the account with PayU money"
msgstr "Mã khoá chỉ được sử dụng để xác định tài khoản với PayUmoney."

#. module: payment_payumoney
#: code:addons/payment_payumoney/models/payment_transaction.py:0
#, python-format
msgid "The payment encountered an error with code %s"
msgstr "Thanh toán gặp lỗi với mã %s"
