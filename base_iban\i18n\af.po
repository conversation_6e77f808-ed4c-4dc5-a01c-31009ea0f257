# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * base_iban
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Language-Team: Afrikaans (https://www.transifex.com/odoo/teams/41243/af/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: af\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: base_iban
#: model:ir.model,name:base_iban.model_res_partner_bank
msgid "Bank Accounts"
msgstr ""

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:57
#, python-format
msgid "Cannot compute the BBAN because the account number is not an IBAN."
msgstr ""

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:26
#, python-format
msgid "No IBAN !"
msgstr ""

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:34
#, python-format
msgid ""
"The IBAN does not seem to be correct. You should have entered something like this %s\n"
"Where B = National bank code, S = Branch code, C = Account No, k = Check digit"
msgstr ""

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:30
#, python-format
msgid "The IBAN is invalid, it should begin with the country code"
msgstr ""

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:40
#, python-format
msgid "This IBAN does not pass the validation check, please verify it."
msgstr ""
