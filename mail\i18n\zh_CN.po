# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail
# 
# Translators:
# <PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# 深圳市耀影科技有限公司_QQ1006399173, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: Chloe Wang, 2023\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid " This channel is private. People must be invited to join it."
msgstr " 该频道是私有的，只有被邀请的人才可以参与."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/rtc/rtc.js:0
#, python-format
msgid "\"%s\" requires \"%s\" access"
msgstr "\"%s\" 需要\"%s\" 访问权限"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/rtc/rtc.js:0
#, python-format
msgid "\"%s\" requires microphone access"
msgstr "\"%s\"需要麦克风访问权限"

#. module: mail
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid "%(activity_name)s: %(summary)s assigned to you"
msgstr "%(activity_name)s: %(summary)s 分配给您"

#. module: mail
#: code:addons/mail/models/res_partner.py:0
#, python-format
msgid ""
"%(email)s is not recognized as a valid email. This is required to create a "
"new customer."
msgstr "%(email)s不是有效的电子邮件。需要创建新的客户。"

#. module: mail
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "%(user_name)s invited you to follow %(document)s document: %(title)s"
msgstr "%(user_name)s邀请您关注%(document)s 文件: %(title)s"

#. module: mail
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "%(user_name)s invited you to follow a new document."
msgstr "%(user_name)s 邀请您关注新文件。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "%d Message"
msgstr "%d 消息"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "%d Messages"
msgstr "%d 消息"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "%d days overdue"
msgstr "逾期%d天"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#, python-format
msgid "%d days overdue:"
msgstr "逾期%d 天："

#. module: mail
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (副本)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/thread/thread.js:0
#, python-format
msgid "%s and %s are typing..."
msgstr "%s 和 %s 在输入..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message_reaction_group/message_reaction_group.js:0
#, python-format
msgid "%s and %s have reacted with %s"
msgstr "%s 和%s 已经与%s发生了反应"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "%s connected"
msgstr "%s 已连接"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "%s created"
msgstr "%s 创建"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "%s from %s"
msgstr "%s 由 %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_request/notification_request.js:0
#, python-format
msgid "%s has a request"
msgstr "%s 有一个请求"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message_reaction_group/message_reaction_group.js:0
#, python-format
msgid "%s has reacted with %s"
msgstr "%s 已与%s发生反应"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/thread/thread.js:0
#, python-format
msgid "%s is typing..."
msgstr "%s 正在输入..."

#. module: mail
#: code:addons/mail/models/mail_channel_partner.py:0
#, python-format
msgid "%s started a live conference"
msgstr "%s发起了实时会议"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/thread/thread.js:0
#, python-format
msgid "%s, %s and more are typing..."
msgstr "%s, %s 及更多人在沟通..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message_reaction_group/message_reaction_group.js:0
#, python-format
msgid "%s, %s, %s and %s other persons have reacted with %s"
msgstr "%s,%s,%s,和其他%s个人对%s作出了回应"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message_reaction_group/message_reaction_group.js:0
#, python-format
msgid "%s, %s, %s and 1 other person have reacted with %s"
msgstr "%s%s%s和其他 1 个人对%s作出了回应"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message_reaction_group/message_reaction_group.js:0
#, python-format
msgid "%s, %s, %s have reacted with %s"
msgstr "%s, %s, %s 已与%s发生反应"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "(from"
msgstr "(从"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "(originally assigned to"
msgstr "（最初分配给"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/thread/thread.js:0
#, python-format
msgid ", "
msgstr ", "

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid ", enter to"
msgstr "按Enter键确认保存 或 点击"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
msgid "-&gt;"
msgstr "-&gt;"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid ". Narrow your search to see more choices."
msgstr ". 缩小搜索范围以查看更多选择."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid ".<br/>"
msgstr ".<br/>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "0 Future"
msgstr "将来 0"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "0 Late"
msgstr "迟到 0"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "0 Today"
msgstr "今天 0"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid ""
"<b attrs=\"{'invisible': [('no_record', '=', False)]}\" class=\"text-"
"warning\">No record for this model</b>"
msgstr ""
"<b attrs=\"{'invisible': [('no_record', '=', False)]}\" class=\"text-"
"warning\">该模型没有记录</b>"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"<br><br>\n"
"            Type <b>@username</b> to mention someone, and grab his attention.<br>\n"
"            Type <b>#channel</b> to mention a channel.<br>\n"
"            Type <b>/command</b> to execute a command.<br>"
msgstr ""
"<br><br>\n"
"            输入 <b>@username</b> 提及某人，并引起他的注意.<br>\n"
"            输入 <b>#channel</b>提示频道 <br>\n"
"            输入 <b>/command</b>执行命令.<br>"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"<div class=\"o_mail_notification\">created <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"
msgstr ""
"<div class=\"o_mail_notification\">创建<a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"<div class=\"o_mail_notification\">invited <a href=\"#\" data-oe-"
"model=\"res.partner\" data-oe-"
"id=\"%(new_partner_id)d\">%(new_partner_name)s</a> to the channel</div>"
msgstr ""
"<div class=\"o_mail_notification\">邀请 <a href=\"#\" data-oe-"
"model=\"res.partner\" data-oe-"
"id=\"%(new_partner_id)d\">%(new_partner_name)s</a>加入到频道 </div>"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "<div class=\"o_mail_notification\">joined the channel</div>"
msgstr "<div class=\"o_mail_notification\">加入到频道</div>"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "<div class=\"o_mail_notification\">left the channel</div>"
msgstr "<div class=\"o_mail_notification\">离开频道</div>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "<i class=\"fa fa-globe\" aria-label=\"Document url\"/>"
msgstr "<i class=\"fa fa-globe\" aria-label=\"文件 url\"/>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid ""
"<p><b>Chat with coworkers</b> in real-time using direct "
"messages.</p><p><i>You might need to invite users from the Settings app "
"first.</i></p>"
msgstr "<p>使用私信实时<b>与同事聊天</b>。</p><p><i>您可能需要在“设置”应用中邀请用户。</i></p>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid ""
"<p><b>Write a message</b> to the members of the channel here.</p> <p>You can"
" notify someone with <i>'@'</i> or link another channel with <i>'#'</i>. "
"Start your message with <i>'/'</i> to get the list of possible commands.</p>"
msgstr ""
"<p>向频道中的其他用户<b>发信息</b>。</p><p>您可以使用 <i>'@' </i>通知相关人员或用 <i>'#' </i>链接另一频道。以 "
"<i>'/'</i> 开头编辑消息获得可以使用的命令列表。</p>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid ""
"<p>Channels make it easy to organize information across different topics and"
" groups.</p> <p>Try to <b>create your first channel</b> (e.g. sales, "
"marketing, product XYZ, after work party, etc).</p>"
msgstr ""
"<p>通过频道可以轻松跨越不同主题和组织信息。</p><p>尝试<b>创建您的第一个频道</b>（例如，销售、营销、产品矩阵、工作之余的聚会等）。</p>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "<p>Create a channel here.</p>"
msgstr "<p>创建新频道。</p>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "<p>Create a public or private channel.</p>"
msgstr "<p>创建一个公共或私人频道。</p>"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"<p>Dear Sender,<br /><br />\n"
"The message below could not be accepted by the address %(alias_display_name)s.\n"
"Only %(contact_description)s are allowed to contact it.<br /><br />\n"
"Please make sure you are using the correct address or contact us at %(default_email)s instead.<br /><br />\n"
"Kind Regards,</p>"
msgstr ""
"<p>亲爱的发件人,<br /><br />\n"
"该地址无法接受以下消息 %(alias_display_name)s.\n"
"只允许 %(contact_description)s联系它.<br /><br />\n"
"请确保您使用正确的地址或通过 %(default_email)s联系我们.<br /><br />\n"
"此致敬礼,</p>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<span attrs=\"{'invisible': [('composition_mode', '!=', 'mass_mail')]}\">\n"
"                                <strong>Email mass mailing</strong> on\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', True)]}\">the selected records</span>\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', False)]}\">the current search filter</span>.\n"
"                            </span>\n"
"                            <span name=\"document_followers_text\" attrs=\"{'invisible':['|', ('model', '=', False), ('composition_mode', '=', 'mass_mail')]}\">Followers of the document and</span>"
msgstr ""
"<span attrs=\"{'invisible': [('composition_mode', '!=', 'mass_mail')]}\">\n"
"                                <strong>Email群发</strong> 在\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', True)]}\">选定的记录</span>\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', False)]}\">当前搜索筛选器</span>.\n"
"                            </span>\n"
"                            <span name=\"document_followers_text\" attrs=\"{'invisible':['|', ('model', '=', False), ('composition_mode', '=', 'mass_mail')]}\">单据的关注者和</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<span attrs=\"{'invisible': [('use_active_domain', '=', True)]}\">\n"
"                                    If you want to send it for all the records matching your search criterion, check this box :\n"
"                                </span>\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', False)]}\">\n"
"                                    If you want to use only selected records please uncheck this selection box :\n"
"                                </span>"
msgstr ""
"<span attrs=\"{'invisible': [('use_active_domain', '=', True)]}\">\n"
"                                    如果要发送与搜索条件匹配的所有记录，请选中此框：\n"
"                                </span>\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', False)]}\">\n"
"                                    如果您只想使用选定的记录，请取消选中此选择框：\n"
"                                </span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "<span class=\"col-md-5 col-lg-4 col-sm-12 pl-0\">Force a language: </span>"
msgstr "<span class=\"col-md-5 col-lg-4 col-sm-12 pl-0\">强制语言：</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid ""
"<span class=\"fa fa-info-circle\"/> Caution: It won't be possible to send "
"this mail again to the recipients you did not select."
msgstr "<span class=\"fa fa-info-circle\"/> 注意：无法再次将此邮件发送给您未选择的收件人。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Activities</span>"
msgstr "<span class=\"o_form_label\">活动</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Custom ICE server list</span>"
msgstr "<span class=\"o_form_label\">自定义 ICE 服务器列表</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"<span class=\"o_stat_text\">Add</span>\n"
"                                    <span class=\"o_stat_text\">Context Action</span>"
msgstr ""
"<span class=\"o_stat_text\">添加</span>\n"
"                                    <span class=\"o_stat_text\">上下文操作</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"<span class=\"o_stat_text\">Remove</span>\n"
"                                    <span class=\"o_stat_text\">Context Action</span>"
msgstr ""
"<span class=\"o_stat_text\">移除</span>\n"
"                                    <span class=\"o_stat_text\">上下文操作</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span>@</span>"
msgstr "<span>@</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<strong>\n"
"                                    All records matching your current search filter will be mailed,\n"
"                                    not only the ids selected in the list view.\n"
"                                </strong><br/>\n"
"                                The email will be sent for all the records selected in the list.<br/>\n"
"                                Confirming this wizard will probably take a few minutes blocking your browser."
msgstr ""
"<strong>\n"
"                                    与当前搜索过滤器匹配的所有记录及在列表视图中选中的 ID都将以邮件发送，\n"
"                                </strong><br/>\n"
"                                在列表中选中的所有记录都将发送Email。<br/>\n"
"                                该操作可能使您几分钟内无法打开浏览器。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
msgid ""
"<strong>Internal communication</strong>: Replying will post an internal "
"note. Followers won't receive any email notification."
msgstr "<strong>内部沟通</strong>: 回复将发布内部注释。 关注者将不会收到任何电子邮件通知。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<strong>Only records checked in list view will be used.</strong><br/>\n"
"                                The email will be sent for all the records selected in the list."
msgstr ""
"<strong>仅使用列表中选中的记录。</strong><br/>\n"
"                                在列表中选中的所有记录都将收到Email。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "<strong>Original note:</strong>"
msgstr "<strong>原始说明：</strong>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "<strong>Recommended Activities</strong>"
msgstr "<strong>推荐活动</strong>"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,help:mail.field_mail_channel__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr "为该别名创建新记录时，将对其进行评估以提供默认值的Python字典。"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_bus_presence_partner_or_guest_exists
msgid "A bus presence must have a user or a guest."
msgstr "总线必须存在用户或者游客."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_channel_partner_partner_or_guest_exists
msgid "A channel member must be a partner or a guest."
msgstr "频道成员必须是合作伙伴或游客."

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "A channel of type 'chat' cannot have more than two users."
msgstr "“聊天”类型的频道不能有两个以上的用户."

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"A chat should not be created with more than 2 persons. Create a group "
"instead."
msgstr "不能同时和2个人以上一起聊天。请创建一个聊天组。"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_message_reaction_partner_or_guest_exists
msgid "A message reaction must be from a partner or from a guest."
msgstr "信息反应必须来自业务伙伴或游客。"

#. module: mail
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "A next activity can only be planned on models that use the chatter"
msgstr "只能在使用聊天的模块上计划下一个活动"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_shortcode_action
msgid ""
"A shortcode is a keyboard shortcut. For instance, you type #gm and it will "
"be transformed into \"Good Morning\"."
msgstr "短码是一种输入快捷方式。例如，您键入 #gm，它将变成\"Good Morning\"。"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_res_users_settings_volumes_partner_or_guest_exists
msgid "A volume setting must have a partner or a guest."
msgstr "音量设置必须有业务合作伙伴或游客。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_invitation_card/rtc_invitation_card.xml:0
#: code:addons/mail/static/src/components/rtc_invitation_card/rtc_invitation_card.xml:0
#, python-format
msgid "Accept"
msgstr "接受"

#. module: mail
#: model:ir.model,name:mail.model_res_groups
msgid "Access Groups"
msgstr "访问用户组"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__access_token
msgid "Access Token"
msgstr "访问令牌"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_category
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__category
msgid "Action"
msgstr "动作"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_needaction
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction
#: model:ir.model.fields,field_description:mail.field_res_users__message_needaction
msgid "Action Needed"
msgstr "需要行动"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__state
#: model:ir.model.fields,field_description:mail.field_ir_cron__state
msgid "Action To Do"
msgstr "待办的行动"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "动作窗口视图"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_category
#: model:ir.model.fields,help:mail.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr "操作可能触发特定的行为，如打开日历视图或在上传单据时自动标记"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__default
msgid "Activated by default when subscribing."
msgstr "订阅时默认启用."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__active
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__active
#: model:ir.model.fields,field_description:mail.field_mail_channel__active
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
msgid "Active"
msgstr "启用"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__active_domain
msgid "Active domain"
msgstr "启用域名"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#: code:addons/mail/static/src/xml/systray.xml:0
#: model:ir.actions.act_window,name:mail.mail_activity_action
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_ids
#: model:ir.model.fields,field_description:mail.field_res_users__activity_ids
#: model:ir.ui.menu,name:mail.menu_mail_activities
#: model:mail.message.subtype,name:mail.mt_activities
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
#, python-format
msgid "Activities"
msgstr "活动"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/views/activity/activity_view.js:0
#: code:addons/mail/static/src/xml/systray.xml:0
#: model:ir.model,name:mail.model_mail_activity
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_type_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_type_id
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_act_window_view__view_mode__activity
#: model:ir.model.fields.selection,name:mail.selection__ir_ui_view__type__activity
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_calendar
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
#, python-format
msgid "Activity"
msgstr "活动"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_exception_decoration
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_exception_decoration
#: model:ir.model.fields,field_description:mail.field_res_users__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "活动异常勋章"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_mixin
msgid "Activity Mixin"
msgstr "活动Mixin"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "Activity Settings"
msgstr "活动设置"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_state
#: model:ir.model.fields,field_description:mail.field_res_users__activity_state
msgid "Activity State"
msgstr "活动状态"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_type
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_type_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Activity Type"
msgstr "活动类型"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_type_icon
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_type_icon
#: model:ir.model.fields,field_description:mail.field_res_users__activity_type_icon
msgid "Activity Type Icon"
msgstr "活动类型图表"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_type_action
#: model:ir.ui.menu,name:mail.menu_mail_activity_type
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Activity Types"
msgstr "活动类型"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_type
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_type
msgid "Activity User Type"
msgstr "活动用户类型"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#, python-format
msgid "Activity type"
msgstr "活动类型"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Add Email Blacklist"
msgstr "添加 Email 黑名单"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower_list_menu/follower_list_menu.xml:0
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__partner_ids
#: model:ir.model.fields,field_description:mail.field_ir_cron__partner_ids
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__followers
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
#, python-format
msgid "Add Followers"
msgstr "添加关注者"

#. module: mail
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "Add Followers can only be done on a mail thread model"
msgstr "仅在邮件线程模型上可以添加关注者"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__add_sign
#: model:ir.model.fields,field_description:mail.field_mail_mail__add_sign
#: model:ir.model.fields,field_description:mail.field_mail_message__add_sign
msgid "Add Sign"
msgstr "添加签名"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_action_list/message_action_list.js:0
#, python-format
msgid "Add a Reaction"
msgstr "添加反应"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Add a description"
msgstr "添加描述"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Add a new %(document)s or send an email to %(email_link)s"
msgstr "新增 %(document)s 或发送邮件至%(email_link)s"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid "Add an email address to the blacklist"
msgstr "将电子邮件地址添加到黑名单"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/text_emojis.xml:0
#, python-format
msgid "Add an emoji"
msgstr "添加一个表情"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Add attachment"
msgstr "添加附件"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_box/attachment_box.xml:0
#, python-format
msgid "Add attachments"
msgstr "添加附件"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Add contacts to notify..."
msgstr "添加联系人到通知中..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Add or join a channel"
msgstr "添加或加入频道"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Add users"
msgstr "添加用户"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Add your twilio credentials for ICE servers"
msgstr "为ICE服务器添加您的twilio凭据"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"Adding followers on channels is not possible. Consider adding members "
"instead."
msgstr "无法在频道上添加关注者。考虑改为添加成员。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__partner_ids
msgid "Additional Contacts"
msgstr "添加联系人"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Advanced"
msgstr "高级"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Advanced Settings"
msgstr "高级设置"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__decoration_type__warning
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_exception_decoration__warning
msgid "Alert"
msgstr "警报"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_tree
msgid "Alias"
msgstr "别名"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_contact
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_contact
msgid "Alias Contact Security"
msgstr "安全联系人别名"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__alias_domain
msgid "Alias Domain"
msgstr "别名域"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_name
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_name
msgid "Alias Name"
msgstr "别名"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_domain
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_domain
msgid "Alias domain"
msgstr "域名别名"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_model_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_model_id
msgid "Aliased Model"
msgstr "模型别名"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_alias
#: model:ir.ui.menu,name:mail.mail_alias_menu
msgid "Aliases"
msgstr "别名"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.js:0
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#, python-format
msgid "All"
msgstr "全部"

#. module: mail
#: code:addons/mail/models/ir_attachment.py:0
#, python-format
msgid "An access token must be provided for each attachment."
msgstr "必须为每个附件提供一个访问令牌。"

#. module: mail
#: code:addons/mail/models/res_partner.py:0
#, python-format
msgid "An email is required for find_or_create to work"
msgstr "要 find_or_create功能工作，需要一个电子邮件"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_group/notification_group.xml:0
#, python-format
msgid "An error occurred when sending an email."
msgstr "发送电子邮件时发生错误。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#: code:addons/mail/static/src/components/thread_view/thread_view.xml:0
#, python-format
msgid "An error occurred while fetching messages."
msgstr "获取消息时发生错误。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/user/user.js:0
#, python-format
msgid "An unexpected error occurred during the creation of the chat."
msgstr "创建聊天时发生意外错误。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#, python-format
msgid "And"
msgstr "和"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#, python-format
msgid "And 1 other member."
msgstr "和另外 1 名其它成员."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#: code:addons/mail/static/src/models/message/message.js:0
#, python-format
msgid "Anonymous"
msgstr "匿名"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__model_id
msgid "Applies to"
msgstr "应用于"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower_subtype_list/follower_subtype_list.xml:0
#, python-format
msgid "Apply"
msgstr "应用"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_search
msgid "Archived"
msgstr "已归档"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/delete_message_confirm_dialog/delete_message_confirm_dialog.xml:0
#, python-format
msgid "Are you sure you want to delete this message?"
msgstr "您确定要删除此消息吗?"

#. module: mail
#: code:addons/mail/wizard/mail_resend_cancel.py:0
#, python-format
msgid ""
"Are you sure you want to discard %s mail delivery failures? You won't be "
"able to re-send these mails later!"
msgstr "您确定要丢弃%s邮件传递失败吗？ 您以后将无法重新发送这些邮件！"

#. module: mail
#: code:addons/mail/models/mail_blacklist.py:0
#: code:addons/mail/models/mail_thread_blacklist.py:0
#, python-format
msgid "Are you sure you want to unblacklist this Email Address?"
msgstr "您确定要将此电子邮件地址取消列入黑名单吗？"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#, python-format
msgid "Assign to ..."
msgstr "指派给…"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#, python-format
msgid "Assign/unassign to me"
msgstr "指派/取消指派给我"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_activity__user_id
#, python-format
msgid "Assigned to"
msgstr "分派给"

#. module: mail
#: code:addons/mail/models/mail_activity.py:0
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid ""
"Assigned user %s has no access to the document and is not able to handle "
"this activity."
msgstr "指定用户 %s 对单据无权限，无法处理这个活动 。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Attach a file"
msgstr "添加附件"

#. module: mail
#: model:ir.model,name:mail.model_ir_attachment
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__attachment_ids
msgid "Attachment"
msgstr "附件"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_res_partner__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_res_users__message_attachment_count
msgid "Attachment Count"
msgstr "附件数量"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#, python-format
msgid "Attachment counter loading..."
msgstr "附件计数器加载中..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_box/attachment_box.xml:0
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__attachment_ids
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Attachments"
msgstr "附件"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__attachment_ids
#: model:ir.model.fields,help:mail.field_mail_message__attachment_ids
msgid ""
"Attachments are linked to a document through model / res_id and to the "
"message through this field."
msgstr "附件通过模型/ReSIDID与单据链接，并通过该字段与消息链接。"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__partners
msgid "Authenticated Partners"
msgstr "身份验证过的业务伙伴"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_id
#: model:ir.model.fields,field_description:mail.field_mail_message__author_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Author"
msgstr "作者"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,help:mail.field_mail_mail__author_id
#: model:ir.model.fields,help:mail.field_mail_message__author_id
msgid ""
"Author of the message. If not set, email_from may hold an email address that"
" did not match any partner."
msgstr "消息的作者。如果没设置，email_from 可能保存一个不匹配任何合作伙伴的EMail地址."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_avatar
#: model:ir.model.fields,field_description:mail.field_mail_message__author_avatar
msgid "Author's avatar"
msgstr "作者形象"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__group_public_id
msgid "Authorized Group"
msgstr "授权群组"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,field_description:mail.field_mail_template__auto_delete
msgid "Auto Delete"
msgstr "自动删除"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Auto Subscribe Groups"
msgstr "自动订阅组"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__group_ids
msgid "Auto Subscription"
msgstr "自动订阅"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Auto subscription"
msgstr "自动订阅"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__automated
msgid "Automated activity"
msgstr "自动活动"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__triggered_next_type_id
msgid ""
"Automatically schedule this activity once the current one is marked as done."
msgstr "将当前活动标记为完成后，自动安排此活动。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_invitation_card/rtc_invitation_card.xml:0
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_channel__avatar_128
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_1920
#, python-format
msgid "Avatar"
msgstr "形象"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_1024
msgid "Avatar 1024"
msgstr "形象 1024"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_128
msgid "Avatar 128"
msgstr "形象 128"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_256
msgid "Avatar 256"
msgstr "形象 256"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_512
msgid "Avatar 512"
msgstr "形象 512"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_request/notification_request.xml:0
#, python-format
msgid "Avatar of OdooBot"
msgstr "OdooBot的形象"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Avatar of guest"
msgstr "游客形象"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Avatar of user"
msgstr "游客形象"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#, python-format
msgid "Away"
msgstr "离开"

#. module: mail
#: model:ir.model,name:mail.model_base
msgid "Base"
msgstr "基础"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "Best regards,"
msgstr "致以最诚挚的问候,"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_res_partner__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_res_users__is_blacklisted
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Blacklist"
msgstr "黑名单"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Blacklist Date"
msgstr "黑名单日期"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_bl
msgid "Blacklisted Address"
msgstr "黑名单地址"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_blacklist_action
msgid "Blacklisted Email Addresses"
msgstr "列入黑名单的电子邮件地址"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__body_html
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__body_html
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Body"
msgstr "正文"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "Bot"
msgstr "机器人"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_bounce
#: model:ir.model.fields,field_description:mail.field_res_partner__message_bounce
#: model:ir.model.fields,field_description:mail.field_res_users__message_bounce
msgid "Bounce"
msgstr "退回"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_popover/notification_popover.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__bounce
#, python-format
msgid "Bounced"
msgstr "被退回"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Browser default"
msgstr "默认浏览器"

#. module: mail
#: code:addons/mail/models/mail_thread_cc.py:0
#, python-format
msgid "CC Email"
msgstr "抄送邮件"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_call
msgid "Call"
msgstr "电话"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/media_preview/media_preview.xml:0
#, python-format
msgid "Camera is off"
msgstr "关闭相机"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__can_edit_body
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__can_edit_body
msgid "Can Edit Body"
msgstr "允许编辑"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__can_write
msgid "Can Write"
msgstr "可写"

#. module: mail
#: code:addons/mail/models/mail_notification.py:0
#, python-format
msgid "Can not update the message or recipient of a notification."
msgstr "无法更新通知的消息或收件人。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#: code:addons/mail/static/src/components/attachment_delete_confirm_dialog/attachment_delete_confirm_dialog.xml:0
#: code:addons/mail/static/src/components/delete_message_confirm_dialog/delete_message_confirm_dialog.xml:0
#: code:addons/mail/static/src/components/follower_subtype_list/follower_subtype_list.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Cancel"
msgstr "取消"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Cancel Email"
msgstr "取消Email"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid "Cancel notification in failure"
msgstr "取消失败通知"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_popover/notification_popover.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__canceled
#, python-format
msgid "Canceled"
msgstr "已取消"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__cancel
msgid "Cancelled"
msgstr "已取消"

#. module: mail
#: model:ir.model,name:mail.model_mail_shortcode
msgid "Canned Response / Shortcode"
msgstr "自动回复"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__canned_response_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__canned_response_ids
msgid "Canned Responses"
msgstr "预设回复"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_cc
msgid "Carbon copy message recipients"
msgstr "抄送"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_cc
msgid "Carbon copy recipients"
msgstr "抄送收件人"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_cc
msgid "Carbon copy recipients (placeholders may be used here)"
msgstr "抄送收件人（可以在这里使用占位符）"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__catchall_formatted
msgid "Catchall"
msgstr "预设"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__catchall_email
msgid "Catchall Email"
msgstr "预设邮件"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_template__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_cc
msgid "Cc"
msgstr "抄送"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__chaining_type
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__chaining_type
msgid "Chaining Type"
msgstr "链接类型"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/rtc_call_viewer/rtc_call_viewer.js:0
#, python-format
msgid "Change Layout"
msgstr "更改布局"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_option_list/rtc_option_list.xml:0
#, python-format
msgid "Change layout"
msgstr "更改布局"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,help:mail.field_mail_activity_type__decoration_type
msgid "Change the background color of the related activities of this type."
msgstr "修改此类相关活动的背景."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "Changed"
msgstr "已修改"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.js:0
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.js:0
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__channel_id
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__channel_id
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__channel_type__channel
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_partner_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_kanban
#, python-format
msgid "Channel"
msgstr "频道"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"Channel \"%(channel_name)s\" only accepts members of group "
"\"%(group_name)s\". Forbidden for: %(guest_names)s"
msgstr "频道“%(channel_name)s”只接受组“%(group_name)s”的成员。禁止：%(guest_names)s"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"Channel \"%(channel_name)s\" only accepts members of group "
"\"%(group_name)s\". Forbidden for: %(partner_names)s"
msgstr "频道 \"%(channel_name)s\" 只接受 \"%(group_name)s\"组的成员. 禁止: %(partner_names)s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__channel_partner_id
msgid "Channel Partner"
msgstr "频道业务伙伴"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__channel_type
msgid "Channel Type"
msgstr "频道类型"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar_category_item/discuss_sidebar_category_item.xml:0
#, python-format
msgid "Channel settings"
msgstr "频道设置"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#: model:ir.model.fields,field_description:mail.field_mail_guest__channel_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__channel_ids
#: model:ir.model.fields,field_description:mail.field_res_users__channel_ids
#: model:ir.ui.menu,name:mail.mail_channel_menu_settings
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_partner_view_tree
#, python-format
msgid "Channels"
msgstr "频道"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_channel_partner_action
#: model:ir.ui.menu,name:mail.mail_channel_partner_menu
msgid "Channels/Partner"
msgstr "频道/业务伙伴"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.js:0
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.js:0
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__channel_type__chat
#, python-format
msgid "Chat"
msgstr "聊天"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_shortcode_action
msgid "Chat Shortcode"
msgstr "聊天简码"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__channel_type
msgid ""
"Chat is private and unique between 2 persons. Group is private among invited"
" persons. Channel can be freely joined (depending on its configuration)."
msgstr "在 2 个人之间的聊天是私密且唯一的。受邀者之间的群聊是私密的。频道可以自由加入（取决于其配置）。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__child_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__child_ids
msgid "Child Messages"
msgstr "下级消息"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Choose an example"
msgstr "选择一个示例"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#: code:addons/mail/static/src/components/thread_view/thread_view.xml:0
#, python-format
msgid "Click here to retry"
msgstr "单击此处重试"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "Click on your message"
msgstr "点击您的留言"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_in_reply_to_view/message_in_reply_to_view.xml:0
#, python-format
msgid "Click to see the attachments"
msgstr "点击查看附件"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#: code:addons/mail/static/src/components/follower_subtype_list/follower_subtype_list.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
#, python-format
msgid "Close"
msgstr "关闭"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Close (Esc)"
msgstr "关闭 (Esc)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#, python-format
msgid "Close chat window"
msgstr "关闭聊天视窗"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#, python-format
msgid "Close conversation"
msgstr "关闭对话"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel_partner__fold_state__closed
msgid "Closed"
msgstr "已关闭"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated carbon copy recipients addresses"
msgstr "逗号分隔抄送收件人邮件地址"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated ids of recipient partners"
msgstr "逗号分隔的收件人合作伙伴的邮件地址"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__partner_to
msgid ""
"Comma-separated ids of recipient partners (placeholders may be used here)"
msgstr "逗号分隔的收件人合作伙伴的邮件地址（可以使用占位符）"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_to
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated recipient addresses"
msgstr "逗号分隔的收件人邮件地址"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_to
msgid "Comma-separated recipient addresses (placeholders may be used here)"
msgstr "逗号分隔的收件人邮件地址（可以在这里使用占位符）"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__comment
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__comment
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Comment"
msgstr "备注"

#. module: mail
#: model:ir.model,name:mail.model_res_company
msgid "Companies"
msgstr "公司"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#: code:addons/mail/static/src/models/mail_template/mail_template.js:0
#: model:ir.actions.act_window,name:mail.action_email_compose_message_wizard
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Compose Email"
msgstr "撰写邮件"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__composition_mode
msgid "Composition mode"
msgstr "写作模式"

#. module: mail
#: model:ir.model,name:mail.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your ICE server list for webRTC"
msgstr "为 webRTC 配置 ICE 服务器列表"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your activity types"
msgstr "配置您的活动类型"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your own email servers"
msgstr "配置您的邮件服务器参数"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Confirm"
msgstr "确认"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_delete_confirm_dialog/attachment_delete_confirm_dialog.js:0
#: code:addons/mail/static/src/components/delete_message_confirm_dialog/delete_message_confirm_dialog.js:0
#, python-format
msgid "Confirmation"
msgstr "确认"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "Congratulations, you're done with your activities."
msgstr "恭喜，您已完成您的活动。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "Congratulations, your inbox is empty"
msgstr "恭喜，您的收件箱已清空"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/widgets/discuss/discuss.js:0
#, python-format
msgid "Congratulations, your inbox is empty!"
msgstr "恭喜，您的收件箱是空的！"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_smtp
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_smtp
msgid "Connection failed (outgoing mail server problem)"
msgstr "连接失败(发件服务器问题)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to_force_new
msgid "Considers answers as new thread"
msgstr "将答案视为新行程"

#. module: mail
#: model:ir.model,name:mail.model_res_partner
msgid "Contact"
msgstr "联系人"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_activity
msgid "Contacts"
msgstr "联系人"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel_partner__last_interest_dt
msgid ""
"Contains the date and time of the last interesting event that happened in "
"this channel for this partner. This includes: creating, joining, pinning, "
"and new message posted."
msgstr "包含此合作伙伴在此频道中发生的最后一个有趣事件的日期和时间。这包括：创建、加入、固定和发布新消息。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__content
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Content"
msgstr "内容"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__body
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__body
#: model:ir.model.fields,field_description:mail.field_mail_mail__body
#: model:ir.model.fields,field_description:mail.field_mail_message__body
msgid "Contents"
msgstr "内容"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__fold_state
msgid "Conversation Fold State"
msgstr "对话收拢状态"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__is_minimized
msgid "Conversation is minimized"
msgstr "对话已最小化"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#, python-format
msgid "Conversations"
msgstr "对话"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_bounce
#: model:ir.model.fields,help:mail.field_res_partner__message_bounce
#: model:ir.model.fields,help:mail.field_res_users__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr "此联系人退回邮件数量计数器"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__country_id
msgid "Country"
msgstr "国家"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity_view.xml:0
#, python-format
msgid "Create"
msgstr "创建"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__next_activity
msgid "Create Next Activity"
msgstr "创建新活动"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_uid
msgid "Create Uid"
msgstr "创建Uid"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/channel_invitation_form/channel_invitation_form.js:0
#, python-format
msgid "Create group chat"
msgstr "创建群聊"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Create new %(document)s"
msgstr "新建 %(document)s"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Create new %(document)s by sending an email to %(email_link)s"
msgstr "通过向%(document)s发送电子邮件来创建新的%(email_link)s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.js:0
#, python-format
msgid "Create or search channel..."
msgstr "创建或搜索频道..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#, python-format
msgid "Created"
msgstr "已创建"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Created By"
msgstr "创建人"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_guest__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings__create_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__create_uid
msgid "Created by"
msgstr "创建人"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__create_date
#: model:ir.model.fields,field_description:mail.field_mail_channel__create_date
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__create_date
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__create_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_guest__create_date
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__create_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_date
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__create_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings__create_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__create_date
msgid "Created on"
msgstr "创建时间"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/chatter/chatter.js:0
#, python-format
msgid "Creating a new record..."
msgstr "创建新纪录…"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Creation Date"
msgstr "创建时间"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__credential
msgid "Credential"
msgstr "凭据"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__currency_id
msgid "Currency"
msgstr "币种"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__starred
#: model:ir.model.fields,help:mail.field_mail_message__starred
msgid "Current user has a starred notification linked to this message"
msgstr "当前用户有一个打星号的提醒与此消息连接"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_bounced_content
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "自定义退回消息"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__custom_channel_name
msgid "Custom channel name"
msgstr "自定义频道名称"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_notification_notification_partner_required
msgid "Customer is required for inbox / email notification"
msgstr "客户需要收件箱/电子邮件通知"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__date
#: model:ir.model.fields,field_description:mail.field_mail_message__date
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Date"
msgstr "日期"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__days
msgid "Days"
msgstr "天"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#, python-format
msgid "Deadline"
msgstr "截止日期"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Deafen"
msgstr "关闭扬声器"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "Dear"
msgstr "尊敬的"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__decoration_type
msgid "Decoration Type"
msgstr "排版类型"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__default
msgid "Default"
msgstr "默认"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__default_display_mode
msgid "Default Display Mode"
msgstr "默认显示模式"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_note
msgid "Default Note"
msgstr "默认备注"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__summary
msgid "Default Summary"
msgstr "默认摘要"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_user_id
msgid "Default User"
msgstr "默认用户"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__null_value
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__null_value
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__null_value
#: model:ir.model.fields,field_description:mail.field_mail_template__null_value
msgid "Default Value"
msgstr "默认值"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_defaults
msgid "Default Values"
msgstr "默认值"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__use_default_to
msgid "Default recipients"
msgstr "默认收件人"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__use_default_to
msgid ""
"Default recipients of the record:\n"
"- partner (using id on a partner or the partner_id field) OR\n"
"- email (using email_from or email field)"
msgstr ""
"记录的默认收件人： \n"
"- 合作伙伴（使用合作伙伴的Id或partner_id字段）或\n"
"- EMail(使用发件人或email字段)"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_shortcode_action
msgid "Define a new chat shortcode"
msgstr "定义新的聊天简码"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_label
msgid "Delay Label"
msgstr "延迟标签"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_from
msgid "Delay Type"
msgstr "延迟类型"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Delay after releasing push-to-talk"
msgstr "释放一键通话后延迟"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_unit
msgid "Delay units"
msgstr "延迟单位"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/delete_message_confirm_dialog/delete_message_confirm_dialog.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/xml/composer.xml:0
#: code:addons/mail/static/src/xml/composer.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
#, python-format
msgid "Delete"
msgstr "删除"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete
msgid "Delete Emails"
msgstr "删除邮件"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete_message
msgid "Delete Message Copy"
msgstr "删除消息副本"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__exception
msgid "Delivery Failed"
msgstr "投递失败"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "Delivery failure"
msgstr "传递失败"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__description
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__description
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__description
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Description"
msgstr "说明"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__description
msgid ""
"Description that will be added in the message posted for this subtype. If "
"void, the name will be added instead."
msgstr "将为该子模型的消息添加注解。如为空，则添加名称。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__default_display_mode
msgid ""
"Determines how the channel will be displayed by default when opening it from"
" its invitation link. No value means display text (no voice/video)."
msgstr "确定从邀请链接打开频道时默认显示频道的方式。无值表示显示文本（无语音/视频）"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Direct Messages"
msgstr "私信"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity_mark_done_popover/activity_mark_done_popover.xml:0
#: code:addons/mail/static/src/components/composer/composer.xml:0
#: code:addons/mail/static/src/models/discuss_sidebar_category_item/discuss_sidebar_category_item.js:0
#: code:addons/mail/static/src/models/discuss_sidebar_category_item/discuss_sidebar_category_item.js:0
#: code:addons/mail/static/src/xml/activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
#, python-format
msgid "Discard"
msgstr "丢弃"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid "Discard delivery failures"
msgstr "放弃发送失败"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_cancel_action
msgid "Discard mail delivery failures"
msgstr "丢弃发送失败的邮件"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_group/notification_group.xml:0
#, python-format
msgid "Discard message delivery failures"
msgstr "丢弃消息传递失败"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_tree
#, python-format
msgid "Disconnect"
msgstr "中断连接"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "Disconnected from the RTC call by the server"
msgstr "服务器断开与 RTC 调用的连接"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#: code:addons/mail/static/src/models/discuss/discuss.js:0
#: model:ir.actions.client,name:mail.action_discuss
#: model:ir.ui.menu,name:mail.mail_menu_technical
#: model:ir.ui.menu,name:mail.menu_root_discuss
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
#, python-format
msgid "Discuss"
msgstr "讨论"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Discuss sidebar"
msgstr "讨论侧边栏"

#. module: mail
#: model:ir.model,name:mail.model_mail_channel
msgid "Discussion Channel"
msgstr "讨论频道"

#. module: mail
#: model:mail.message.subtype,name:mail.mt_comment
msgid "Discussions"
msgstr "讨论"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_cancel
msgid "Dismiss notification for resend by model"
msgstr "重发请求被拒绝"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__display_name
#: model:ir.model.fields,field_description:mail.field_mail_alias__display_name
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__display_name
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__display_name
#: model:ir.model.fields,field_description:mail.field_mail_channel__display_name
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__display_name
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__display_name
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_followers__display_name
#: model:ir.model.fields,field_description:mail.field_mail_guest__display_name
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__display_name
#: model:ir.model.fields,field_description:mail.field_mail_mail__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__display_name
#: model:ir.model.fields,field_description:mail.field_mail_notification__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__display_name
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__display_name
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__display_name
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__display_name
#: model:ir.model.fields,field_description:mail.field_res_users_settings__display_name
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"Display an option on related documents to open a composition wizard with "
"this template"
msgstr "显示一个关联单据的选项 ，用该模版打开撰写向导"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__res_name
msgid "Display name of the related document."
msgstr "相关单据的显示名称."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete_message
msgid ""
"Do not keep a copy of the email in the document communication history (mass "
"mailing only)"
msgstr "不要在单据通信历史中保存EMail副本（仅限群发邮件）"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_delete_confirm_dialog/attachment_delete_confirm_dialog.js:0
#, python-format
msgid "Do you really want to delete \"%s\"?"
msgstr "您真的要删除“%s”吗？"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Document"
msgstr "文件"

#. module: mail
#: model:ir.model,name:mail.model_mail_followers
msgid "Document Followers"
msgstr "单据关注者"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model_id
msgid "Document Model"
msgstr "单据模型"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_name
msgid "Document Name"
msgstr "单据名称"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Documentation"
msgstr "文档"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity_mark_done_popover/activity_mark_done_popover.xml:0
#: code:addons/mail/static/src/xml/activity.xml:0
#, python-format
msgid "Done"
msgstr "完成"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#, python-format
msgid "Done & Launch Next"
msgstr "完成并启动下一页"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity_mark_done_popover/activity_mark_done_popover.js:0
#: code:addons/mail/static/src/xml/activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#, python-format
msgid "Done & Schedule Next"
msgstr "完成&安排下一次"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Download"
msgstr "下载"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_option_list/rtc_option_list.xml:0
#, python-format
msgid "Download logs"
msgstr "下载日志"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/drop_zone/drop_zone.xml:0
#, python-format
msgid "Drag Files Here"
msgstr "将档案拖动到此处"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Dropdown menu"
msgstr "下拉菜单"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__date_deadline
msgid "Due Date"
msgstr "到期日期"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_date_deadline_range
msgid "Due Date In"
msgstr "截止日期至"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "Due in %d days"
msgstr "在 %d 天 到期"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#, python-format
msgid "Due in %d days:"
msgstr "截止 %d 天:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#, python-format
msgid "Due on"
msgstr "截止"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range_type
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_date_deadline_range_type
msgid "Due type"
msgstr "到期类型"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_dup
msgid "Duplicated Email"
msgstr "复制电子邮件"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__voice_active_duration
msgid "Duration of voice activity in ms"
msgstr "语音活动的持续时间（毫秒）"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Dynamic Placeholder Generator"
msgstr "动态定位符生成器"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#, python-format
msgid "Edit"
msgstr "编辑"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Edit Partners"
msgstr "编辑伙伴"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower_subtype_list/follower_subtype_list.xml:0
#, python-format
msgid "Edit Subscription of"
msgstr "编辑订阅"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower/follower.xml:0
#: code:addons/mail/static/src/components/follower/follower.xml:0
#, python-format
msgid "Edit subscription"
msgstr "编辑订阅"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__email
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__partner_email
#: model:ir.model.fields,field_description:mail.field_mail_followers__email
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__email
#: model:ir.model.fields,field_description:mail.field_res_partner__email
#: model:ir.model.fields,field_description:mail.field_res_users__email
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__email
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_type__email
#: model:mail.activity.type,name:mail.mail_activity_data_email
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Email"
msgstr "Email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__email
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Email Address"
msgstr "Email地址"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Email Alias"
msgstr "电子邮箱别名"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias
msgid "Email Aliases"
msgstr "EMail别名"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_mixin
msgid "Email Aliases Mixin"
msgstr "EMail别名 Mixin"

#. module: mail
#: model:ir.ui.menu,name:mail.mail_blacklist_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Email Blacklist"
msgstr "EMail黑名单"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_cc
msgid "Email CC management"
msgstr "电子邮件副本管理"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Email Configuration"
msgstr "EMail配置"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__composition_mode__mass_mail
msgid "Email Mass Mailing"
msgstr "邮件群发"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Email Preview"
msgstr "EMail预览"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Email Search"
msgstr "EMail搜索"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__template_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__template_id
msgid "Email Template"
msgstr "EMail模板"

#. module: mail
#: model:ir.model,name:mail.model_mail_template_preview
msgid "Email Template Preview"
msgstr "EMail模板预览"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_email_template_tree_all
#: model:ir.model,name:mail.model_mail_template
#: model:ir.ui.menu,name:mail.menu_email_templates
msgid "Email Templates"
msgstr "EMail模板"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread
msgid "Email Thread"
msgstr "邮件主题"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_blacklist_unique_email
msgid "Email address already exists!"
msgstr "EMail地址已存在！"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,help:mail.field_mail_mail__email_from
#: model:ir.model.fields,help:mail.field_mail_message__email_from
msgid ""
"Email address of the sender. This field is set when no matching partner is "
"found and replaces the author_id field in the chatter."
msgstr "发送者的EMail地址。当找不到业务伙伴的匹配的邮件时候，这个字段就被设置。并且Chatter中的作者id被替换掉."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Email address to which replies will be redirected"
msgstr "回复将被重定向到的电子邮件地址"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"Email address to which replies will be redirected when sending emails in "
"mass"
msgstr "批量发送电子邮件时将回复重定向到的电子邮件地址"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__reply_to
msgid ""
"Email address to which replies will be redirected when sending emails in "
"mass; only used when the reply is not logged in the original discussion "
"thread."
msgstr "当群发邮件时，回复将被重定向的电子邮件地址;仅当回复没有被记录在原始讨论线程中时使用。"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid ""
"Email addresses that are blacklisted won't receive Email mailings anymore."
msgstr "被列入黑名单的电子邮件地址将不再接收电子邮件。"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Email alias %(alias_name)s cannot be used on %(count)d records at the same "
"time. Please update records one by one."
msgstr "电子邮件别名%(alias_name)s 不能同时用于 %(count)d条记录。请一一更新记录。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__email_cc
msgid "Email cc"
msgstr "邮件抄送"

#. module: mail
#: model:ir.model,name:mail.model_mail_compose_message
msgid "Email composition wizard"
msgstr "EMail撰写向导"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Email message"
msgstr "EMail消息"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_message
msgid "Email resend wizard"
msgstr "EMail重发向导"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__mail_template_ids
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__mail_template_ids
msgid "Email templates"
msgstr "电子邮件模板"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_mail
#: model:ir.ui.menu,name:mail.menu_mail_mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Emails"
msgstr "EMail"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Emojis"
msgstr "表情符号"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_internal
#: model:ir.model.fields,field_description:mail.field_mail_message__is_internal
msgid "Employee Only"
msgstr "仅限员工"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model_fields__tracking
msgid "Enable Ordered Tracking"
msgstr "启用跟踪"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_request/notification_request.xml:0
#, python-format
msgid "Enable desktop notifications to chat."
msgstr "启用聊天桌面通知."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid "Envelope Example"
msgstr "信封样例"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_popover/notification_popover.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__decoration_type__danger
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_exception_decoration__danger
#, python-format
msgid "Error"
msgstr "错误"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__error_msg
msgid "Error Message"
msgstr "错误消息"

#. module: mail
#: code:addons/mail/models/update.py:0
#, python-format
msgid "Error during communication with the publisher warranty server."
msgstr "当与保障服务器通信时发生错误。"

#. module: mail
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid ""
"Error without exception. Probably due do concurrent access update of "
"notification records. Please see with an administrator."
msgstr "错误没有例外。可能由于并发访问更新通知记录。请与管理员联系。"

#. module: mail
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid ""
"Error without exception. Probably due do sending an email without computed "
"recipients."
msgstr "无异常错误。可能是由于没有找到收件人EMail。"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_followers_mail_followers_res_partner_res_model_id_uniq
msgid "Error, a partner cannot follow twice the same object."
msgstr "错误，用户不能重复创建联系人。"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__everyone
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__public__public
msgid "Everyone"
msgstr "所有人"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__exception
#: model:mail.activity.type,name:mail.mail_activity_data_warning
msgid "Exception"
msgstr "异常"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__member_count
msgid "Excluding guests from count."
msgstr "不包括游客。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_option_list/rtc_option_list.xml:0
#, python-format
msgid "Exit full screen"
msgstr "退出全屏"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Extended Filters..."
msgstr "扩展筛选..."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__fail_counter
msgid "Fail Mail"
msgstr "失败的邮件"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Failed"
msgstr "失败的"

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Failed to render QWeb template : %s)"
msgstr "无法渲染 QWeb 模板: %s)"

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Failed to render inline_template template : %s)"
msgstr "无法渲染 inline_template 模板： : %s)"

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Failed to render template : %(xml_id)s (%(view_id)d)"
msgstr "无法渲染模板 : %(xml_id)s (%(view_id)d)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__failure_reason
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Failure Reason"
msgstr "失败原因"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_reason
msgid "Failure reason"
msgstr "失败原因"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__failure_reason
msgid ""
"Failure reason. This is usually the exception thrown by the email server, "
"stored to ease the debugging of mailing issues."
msgstr "失败原因。通过EMail服务器抛出的异常，存储以缓解邮件问题的调试。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__failure_type
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_type
msgid "Failure type"
msgstr "失败类型"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__starred_partner_ids
msgid "Favorited By"
msgstr "收藏夹"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "Feedback"
msgstr "反馈"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_template__model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field
msgid "Field"
msgstr "字段"

#. module: mail
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Field \"Mail Activity\" cannot be changed to \"False\"."
msgstr "字段“邮件活动”不能更改为“False”。"

#. module: mail
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Field \"Mail Blacklist\" cannot be changed to \"False\"."
msgstr "“邮件黑名单”字段不能更改为“False”。"

#. module: mail
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Field \"Mail Thread\" cannot be changed to \"False\"."
msgstr "字段\"Type\" 不能在模型上更改."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_desc
msgid "Field Description"
msgstr "字段说明"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_groups
msgid "Field Groups"
msgstr "字段组"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_type
msgid "Field Type"
msgstr "字段类型"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Field details"
msgstr "字段详情"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__relation_field
msgid ""
"Field used to link the related model to the subtype model when using "
"automatic subscription on a related document. The field is used to compute "
"getattr(related_document.relation_field)."
msgstr ""
"字段用于在使用相关单据的自动订阅时将相关模型链接到子类型模型。该字段用于计算GETAFTR（RelabyDo.DeffixFieldFieldfield）。"

#. module: mail
#: model:ir.model,name:mail.model_ir_model_fields
msgid "Fields"
msgstr "字段"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__copyvalue
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__copyvalue
#: model:ir.model.fields,help:mail.field_mail_render_mixin__copyvalue
#: model:ir.model.fields,help:mail.field_mail_template__copyvalue
msgid ""
"Final placeholder expression, to be copy-pasted in the desired template "
"field."
msgstr "最终的占位符表达式，可以复制粘贴到目标模版字段."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Find or create a channel..."
msgstr "查找或创建一个频道..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Find or start a conversation..."
msgstr "查找或开始对话..."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel_partner__fold_state__folded
msgid "Folded"
msgstr "收起"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follow_button/follow_button.xml:0
#, python-format
msgid "Follow"
msgstr "关注"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower_list_menu/follower_list_menu.xml:0
#: model:ir.actions.act_window,name:mail.action_view_followers
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_follower_ids
#: model:ir.ui.menu,name:mail.menu_email_followers
#: model_terms:ir.ui.view,arch_db:mail.view_followers_tree
#, python-format
msgid "Followers"
msgstr "关注者"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_partner_ids
msgid "Followers (Partners)"
msgstr "关注者(业务伙伴)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_subscription_form
msgid "Followers Form"
msgstr "关注者表单"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Followers of"
msgstr "关注者"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__followers
msgid "Followers only"
msgstr "仅关注者"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follow_button/follow_button.xml:0
#, python-format
msgid "Following"
msgstr "正在关注中"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__icon
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_type_icon
#: model:ir.model.fields,help:mail.field_mail_activity_type__icon
#: model:ir.model.fields,help:mail.field_res_partner__activity_type_icon
#: model:ir.model.fields,help:mail.field_res_users__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "完美的图标，例如FA任务"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__email_formatted
msgid "Formatted Email"
msgstr "格式化的邮件"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_from
#: model:ir.model.fields,field_description:mail.field_mail_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_template__email_from
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_from
msgid "From"
msgstr "从"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Full composer"
msgstr "完全创建者"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_option_list/rtc_option_list.xml:0
#, python-format
msgid "Full screen"
msgstr "全屏显示"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__default_display_mode__video_full_screen
msgid "Full screen video"
msgstr "全屏视频"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "Future"
msgstr "未来"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Future Activities"
msgstr "未来活动"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Gateway"
msgstr "网关"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_user_type__generic
msgid "Generic User From Record"
msgstr "记录显示为一般用户"

#. module: mail
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Go to the configuration panel"
msgstr "至配置面板"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__channel_type__group
msgid "Group"
msgstr "群组"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Group By"
msgstr "分组"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Group Name"
msgstr "群组名称"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Group by..."
msgstr "分组…"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#, python-format
msgid "Grouped Chat"
msgstr "群聊"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_tree
msgid "Groups"
msgstr "群组"

#. module: mail
#: code:addons/mail/controllers/discuss.py:0
#: model:ir.model,name:mail.model_mail_guest
#: model:ir.model.fields,field_description:mail.field_bus_presence__guest_id
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__guest_id
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__guest_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_guest_id
#: model:ir.model.fields,field_description:mail.field_mail_message__author_guest_id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__guest_id
#: model_terms:ir.ui.view,arch_db:mail.mail_guest_view_tree
#, python-format
msgid "Guest"
msgstr "游客"

#. module: mail
#: code:addons/mail/models/mail_guest.py:0
#, python-format
msgid "Guest's name cannot be empty."
msgstr "游客姓名不能为空."

#. module: mail
#: code:addons/mail/models/mail_guest.py:0
#, python-format
msgid "Guest's name is too long."
msgstr "游客姓名太长."

#. module: mail
#: model:ir.ui.menu,name:mail.mail_guest_menu
msgid "Guests"
msgstr "顾客"

#. module: mail
#: model:ir.model,name:mail.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP 路由"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_users__notification_type__email
msgid "Handle by Emails"
msgstr "用邮件处理"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_users__notification_type__inbox
msgid "Handle in Odoo"
msgstr "在ERP内处理"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__has_cancel
msgid "Has Cancel"
msgstr "已取消"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Has Mentions"
msgstr "提及"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__has_message
#: model:ir.model.fields,field_description:mail.field_mail_channel__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__has_message
#: model:ir.model.fields,field_description:mail.field_res_partner__has_message
#: model:ir.model.fields,field_description:mail.field_res_users__has_message
msgid "Has Message"
msgstr "有消息"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__is_deaf
msgid "Has disabled incoming sound"
msgstr "禁用来电声音"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__has_error
#: model:ir.model.fields,field_description:mail.field_mail_message__has_error
#: model:ir.model.fields,help:mail.field_mail_mail__has_error
#: model:ir.model.fields,help:mail.field_mail_message__has_error
msgid "Has error"
msgstr "有误差"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__headers
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Headers"
msgstr "标题"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Hello"
msgstr "您好"

#. module: mail
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "Hello,"
msgstr "你好,"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__help_message
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__message
msgid "Help message"
msgstr "帮助消息"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__hidden
msgid "Hidden"
msgstr "隐藏"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Hide Member List"
msgstr "隐藏成员列表"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__hidden
msgid "Hide the subtype in the follower options"
msgstr "隐藏关注者选项的子类型"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__is_internal
#: model:ir.model.fields,help:mail.field_mail_message__is_internal
msgid ""
"Hide to public / portal users, independently from subtype configuration."
msgstr "对公共/门户用户隐藏，独立于子类型配置."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "History"
msgstr "历史"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__voice_active_duration
msgid ""
"How long the audio broadcast will remain active after passing the volume "
"threshold"
msgstr "超过音量阈值后音频广播将保持活动多长时间"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "ICE Servers"
msgstr "ICE 服务器"

#. module: mail
#: model:ir.model,name:mail.model_mail_ice_server
#: model_terms:ir.ui.view,arch_db:mail.view_ice_server_form
msgid "ICE server"
msgstr "ICE 服务器"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_ice_servers
#: model:ir.ui.menu,name:mail.mail_channel_ice_servers_menu
msgid "ICE servers"
msgstr "ICE 服务器"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__id
#: model:ir.model.fields,field_description:mail.field_mail_alias__id
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__id
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__id
#: model:ir.model.fields,field_description:mail.field_mail_channel__id
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__id
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__id
#: model:ir.model.fields,field_description:mail.field_mail_followers__id
#: model:ir.model.fields,field_description:mail.field_mail_guest__id
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__id
#: model:ir.model.fields,field_description:mail.field_mail_mail__id
#: model:ir.model.fields,field_description:mail.field_mail_message__id
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__id
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__id
#: model:ir.model.fields,field_description:mail.field_mail_notification__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__id
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__id
#: model:ir.model.fields,field_description:mail.field_mail_template__id
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__id
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__id
#: model:ir.model.fields,field_description:mail.field_res_users_settings__id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__id
msgid "ID"
msgstr "ID"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_thread_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr "上级记录ID支持别名(例如:项目支持任务创建别名)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_exception_icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__icon
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_exception_icon
#: model:ir.model.fields,field_description:mail.field_res_users__activity_exception_icon
msgid "Icon"
msgstr "图标"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_exception_icon
#: model:ir.model.fields,help:mail.field_res_partner__activity_exception_icon
#: model:ir.model.fields,help:mail.field_res_users__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "表示异常活动的图标。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__res_id
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_id
msgid "Id of the followed resource"
msgstr "被关注资源的ID"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_form
msgid "Identity"
msgstr "身份"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "Idle"
msgstr "空闲"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_unread
#: model:ir.model.fields,help:mail.field_mail_channel__message_needaction
#: model:ir.model.fields,help:mail.field_mail_channel__message_unread
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread__message_unread
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_unread
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_unread
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction
#: model:ir.model.fields,help:mail.field_res_partner__message_unread
#: model:ir.model.fields,help:mail.field_res_users__message_needaction
#: model:ir.model.fields,help:mail.field_res_users__message_unread
msgid "If checked, new messages require your attention."
msgstr "确认后, 出现提示消息."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,help:mail.field_mail_channel__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_has_error
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error
#: model:ir.model.fields,help:mail.field_res_users__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "如果勾选此项， 某些消息将会产生传递错误。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__send_mail
msgid ""
"If checked, the partners will receive an email warning they have been added "
"in the document's followers."
msgstr "如果勾选此项，业务伙伴将收到邮件提醒他们已经被添加为单据的关注者。"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model_fields__tracking
msgid ""
"If set every modification done to this field is tracked in the chatter. "
"Value is used to order tracking values."
msgstr "设置对该字段所做的每个修改都在聊天框被跟踪。值被用于排序跟踪值。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible. Unless a timezone is specified, "
"it is considered as being in UTC timezone."
msgstr "如果设置，队列管理器将在该日期之后发送电子邮件。 如果未设置，电子邮件将尽快发送。 除非指定时区，否则它被视为在 UTC 时区。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible. You can use dynamic expressions "
"expression."
msgstr "如果设置，队列管理器将在该日期之后发送电子邮件。如果未设置，电子邮件将尽快发送。您可以使用动态表达式表达式。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_bounced_content
#: model:ir.model.fields,help:mail.field_mail_channel__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr "如果设置，此内容将自动发送给未经授权的用户，而不是默认消息。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__is_blacklisted
#: model:ir.model.fields,help:mail.field_res_partner__is_blacklisted
#: model:ir.model.fields,help:mail.field_res_users__is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr "存在于黑名单的EMail表示收件者不会再收到任何列表的群发邮件"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__reply_to_force_new
#: model:ir.model.fields,help:mail.field_mail_message__reply_to_force_new
msgid ""
"If true, answers do not go in the original document discussion thread. "
"Instead, it will check for the reply_to in tracking message-id and "
"redirected accordingly. This has an impact on the generated message-id."
msgstr ""
"如果为真，那么答案不会出现在原始文档讨论线程中。相反，它将检查跟踪message-id中的reply_to，并相应地重定向。这将对生成的message-"
"id产生影响。"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__alias_domain
msgid ""
"If you have setup a catch-all email domain redirected to the Odoo server, "
"enter the domain name here."
msgstr "如果您设置了电子邮件域名重定向到这个Odoo服务器，请在这里输入域名。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid ""
"If you want to re-send them, click Cancel now, then click on the "
"notification and review them one by one by clicking on the red envelope next"
" to each message."
msgstr "如果您想重新发送，请点击取消，然后点击每条消息旁边的红色信封提示逐一检查。"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__use_twilio_rtc_servers
msgid "If you want to use twilio as TURN/STUN server provider"
msgstr "如果您想使用twilio作为TURN/STUN服务器提供商"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Ignore all failures"
msgstr "忽略所有失败"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_channel__image_128
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_1920
#, python-format
msgid "Image"
msgstr "图像"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_1024
msgid "Image 1024"
msgstr "图像 1024"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_128
msgid "Image 128"
msgstr "图像128"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_256
msgid "Image 256"
msgstr "图像 256"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_512
msgid "Image 512"
msgstr "图像 512"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Image is a link"
msgstr "图像为链接"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "Inactive Alias"
msgstr "未启用的别名"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_type__inbox
#, python-format
msgid "Inbox"
msgstr "收件箱"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_invitation_card/rtc_invitation_card.xml:0
#, python-format
msgid "Incoming Call..."
msgstr "来电..."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__automated
msgid ""
"Indicates this activity has been created automatically and not by any user."
msgstr "表示这个活动是自动生成的，不是任何用户创建。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#, python-format
msgid "Info"
msgstr "信息"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__initial_res_model
msgid "Initial model"
msgstr "初始模型"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__parent_id
#: model:ir.model.fields,help:mail.field_mail_mail__parent_id
#: model:ir.model.fields,help:mail.field_mail_message__parent_id
msgid "Initial thread message."
msgstr "初始化线索消息."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Input device"
msgstr "输入设备"

#. module: mail
#: model:ir.ui.menu,name:mail.mail_channel_integrations_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Integrations"
msgstr "集成"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__internal
msgid "Internal Only"
msgstr "仅内部的"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_email_invalid
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_email_invalid
msgid "Invalid email address"
msgstr "无效的EMail地址"

#. module: mail
#: code:addons/mail/models/mail_blacklist.py:0
#, python-format
msgid "Invalid email address %r"
msgstr "无效的EMail地址 %r"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Invalid expression, it must be a literal python dictionary definition e.g. "
"\"{'field': 'value'}\""
msgstr "错误的表达式，必须是符合文法的python字典定义，例如： \"{'field': 'value'}\""

#. module: mail
#: code:addons/mail/models/mail_thread_blacklist.py:0
#: code:addons/mail/models/mail_thread_blacklist.py:0
#, python-format
msgid "Invalid primary email field on model %s"
msgstr "初级邮件模型中无效的部分 %s"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"Invalid value when creating a channel with members, only 4 or 6 are allowed."
msgstr "创建包含成员的频道时的值无效，仅允许 4 或 6 个。"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"Invalid value when creating a channel with memberships, only 0 is allowed."
msgstr "创建具有会员资格的频道时的值无效，仅允许为 0。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "Invitation Link"
msgstr "邀请链接"

#. module: mail
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "Invitation to follow %(document_model)s: %(document_name)s"
msgstr "邀请关注 %(document_model)s: %(document_name)s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/thread/thread.js:0
#, python-format
msgid "Invite Follower"
msgstr "邀请关注者"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "Invite people"
msgstr "邀请人员"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/channel_invitation_form/channel_invitation_form.js:0
#, python-format
msgid "Invite to Channel"
msgstr "邀请加入频道"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/channel_invitation_form/channel_invitation_form.js:0
#, python-format
msgid "Invite to group chat"
msgstr "邀请加入群聊"

#. module: mail
#: model:ir.model,name:mail.model_mail_wizard_invite
msgid "Invite wizard"
msgstr "邀请向导"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__public__private
msgid "Invited people only"
msgstr "仅邀请人员"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__is_active
msgid "Is Active"
msgstr "处于启用状态"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_current_user_or_guest_author
#: model:ir.model.fields,field_description:mail.field_mail_message__is_current_user_or_guest_author
msgid "Is Current User Or Guest Author"
msgstr "是当前用户还是游客作者"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__is_mail_template_editor
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__is_mail_template_editor
msgid "Is Editor"
msgstr "编辑器"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_partner__message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_users__message_is_follower
msgid "Is Follower"
msgstr "关注者"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__is_member
msgid "Is Member"
msgstr "是会员"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__is_read
msgid "Is Read"
msgstr "已读"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__is_chat
msgid "Is a chat"
msgstr "是聊天"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__is_discuss_sidebar_category_channel_open
msgid "Is discuss sidebar category channel open?"
msgstr "是否打开讨论侧边栏类别聊天？"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__is_discuss_sidebar_category_chat_open
msgid "Is discuss sidebar category chat open?"
msgstr "是否打开讨论侧边栏类别聊天？"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__is_muted
msgid "Is microphone muted"
msgstr "麦克风静音"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__is_pinned
msgid "Is pinned on the interface"
msgstr "是否置顶"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__is_camera_on
msgid "Is sending user video"
msgstr "正在发送用户视频"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__is_screen_sharing_on
msgid "Is sharing the screen"
msgstr "正在共享屏幕"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#, python-format
msgid "Issue with audio"
msgstr "音频问题"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_kanban
msgid "Join"
msgstr "加入"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Join Call"
msgstr "加入语音会议"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/welcome_view/welcome_view.xml:0
#, python-format
msgid "Join Channel"
msgstr "加入频道"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Join Video Call"
msgstr "加入视频会议"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_channel_action_view
msgid "Join a group"
msgstr "加入群组"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#, python-format
msgid "LIVE"
msgstr "在线"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__lang
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__lang
#: model:ir.model.fields,field_description:mail.field_mail_guest__lang
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__lang
#: model:ir.model.fields,field_description:mail.field_mail_template__lang
msgid "Language"
msgstr "语言"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__fetched_message_id
msgid "Last Fetched"
msgstr "最后一次提取"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__last_interest_dt
msgid "Last Interest"
msgstr "持续的兴趣"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity____last_update
#: model:ir.model.fields,field_description:mail.field_mail_activity_type____last_update
#: model:ir.model.fields,field_description:mail.field_mail_alias____last_update
#: model:ir.model.fields,field_description:mail.field_mail_blacklist____last_update
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove____last_update
#: model:ir.model.fields,field_description:mail.field_mail_channel____last_update
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner____last_update
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session____last_update
#: model:ir.model.fields,field_description:mail.field_mail_compose_message____last_update
#: model:ir.model.fields,field_description:mail.field_mail_followers____last_update
#: model:ir.model.fields,field_description:mail.field_mail_guest____last_update
#: model:ir.model.fields,field_description:mail.field_mail_ice_server____last_update
#: model:ir.model.fields,field_description:mail.field_mail_mail____last_update
#: model:ir.model.fields,field_description:mail.field_mail_message____last_update
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction____last_update
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype____last_update
#: model:ir.model.fields,field_description:mail.field_mail_notification____last_update
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel____last_update
#: model:ir.model.fields,field_description:mail.field_mail_resend_message____last_update
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner____last_update
#: model:ir.model.fields,field_description:mail.field_mail_shortcode____last_update
#: model:ir.model.fields,field_description:mail.field_mail_template____last_update
#: model:ir.model.fields,field_description:mail.field_mail_template_preview____last_update
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value____last_update
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite____last_update
#: model:ir.model.fields,field_description:mail.field_res_users_settings____last_update
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes____last_update
msgid "Last Modified on"
msgstr "最后修改时间"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__channel_last_seen_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__seen_message_id
msgid "Last Seen"
msgstr "最近一次查阅"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__write_date
msgid "Last Updated On"
msgstr "最后更新时间"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_guest__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings__write_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__write_date
#: model:ir.model.fields,field_description:mail.field_mail_channel__write_date
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__write_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_guest__write_date
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__write_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_date
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__write_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings__write_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__write_date
msgid "Last Updated on"
msgstr "最后更新时间"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "Late"
msgstr "迟到"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Late Activities"
msgstr "最近的活动"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__layout
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_layout_xmlid
#: model:ir.model.fields,field_description:mail.field_mail_message__email_layout_xmlid
msgid "Layout"
msgstr "布局"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/discuss_sidebar_category_item/discuss_sidebar_category_item.js:0
#: code:addons/mail/static/src/models/discuss_sidebar_category_item/discuss_sidebar_category_item.js:0
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_kanban
#, python-format
msgid "Leave"
msgstr "离开"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar_category_item/discuss_sidebar_category_item.xml:0
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Leave this channel"
msgstr "退出此频道"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_cc__email_cc
msgid "List of cc from incoming emails."
msgstr "收到的邮件里的抄送列表。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__partner_ids
msgid ""
"List of partners that will be added as follower of the current document."
msgstr "列表中的业务伙伴将添加至当前单据的关注者。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "List users in the current channel"
msgstr "在当前频道列出用户"

#. module: mail
#: model:ir.model,name:mail.model_mail_channel_partner
msgid "Listeners of a Channel"
msgstr "频道的收听者"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "Load more"
msgstr "加载更多"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Loading"
msgstr "正在加载"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#: code:addons/mail/static/src/components/thread_view/thread_view.xml:0
#: code:addons/mail/static/src/components/thread_view/thread_view.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Loading..."
msgstr "正在加载..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/composer_view/composer_view.js:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Log"
msgstr "记录"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#, python-format
msgid "Log a note"
msgstr "记录一个备注"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Log a note..."
msgstr "记录一个备注..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Log an Activity"
msgstr "记录一个活动"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__is_log
msgid "Log an Internal Note"
msgstr "记录内部备注"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_text_input/composer_text_input.js:0
#, python-format
msgid "Log an internal note..."
msgstr "记录内部备注..."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__reply_to_mode__update
msgid "Log in the original discussion thread"
msgstr "登录原始讨论线程"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#, python-format
msgid "Log note"
msgstr "记录备注"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/welcome_view/welcome_view.js:0
#, python-format
msgid "Logged in as %s"
msgstr ""

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/mail_template/mail_template.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_mail_id
#, python-format
msgid "Mail"
msgstr "邮件"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_activity
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Activity"
msgstr "邮件活动"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_activity_type_id
msgid "Mail Activity Type"
msgstr "邮件活动类型"

#. module: mail
#: model:ir.model,name:mail.model_mail_blacklist
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_blacklist
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Blacklist"
msgstr "邮件黑名单"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_blacklist
msgid "Mail Blacklist mixin"
msgstr "mixin邮件黑名单"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Mail Channel Form"
msgstr "邮件频道表单"

#. module: mail
#: model:ir.model,name:mail.model_mail_composer_mixin
msgid "Mail Composer Mixin"
msgstr "邮件编辑器混合"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/notification_group/notification_group.js:0
#, python-format
msgid "Mail Failures"
msgstr "邮件失败"

#. module: mail
#: model:ir.model,name:mail.model_mail_channel_rtc_session
msgid "Mail RTC session"
msgstr "邮件 RTC 会话"

#. module: mail
#: model:ir.model,name:mail.model_mail_render_mixin
msgid "Mail Render Mixin"
msgstr "邮件渲染混合"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__template_id
msgid "Mail Template"
msgstr "邮件模板"

#. module: mail
#: model:res.groups,name:mail.group_mail_template_editor
msgid "Mail Template Editor"
msgstr "邮件模板编辑器"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_thread
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Thread"
msgstr "邮件线程"

#. module: mail
#: model:ir.model,name:mail.model_mail_tracking_value
msgid "Mail Tracking Value"
msgstr "邮件跟踪值"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__is_notification
msgid "Mail has been created to notify people of an existing mail.message"
msgstr "邮件被创建，以通知当前收件人"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_mail_scheduler_action_ir_actions_server
#: model:ir.cron,cron_name:mail.ir_cron_mail_scheduler_action
#: model:ir.cron,name:mail.ir_cron_mail_scheduler_action
msgid "Mail: Email Queue Manager"
msgstr "邮件：EMail队列管理器"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Mailbox unavailable - %s"
msgstr "邮箱不可用 - %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.js:0
#, python-format
msgid "Mailboxes"
msgstr "邮箱"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_ids
msgid "Mails"
msgstr "邮件"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_res_partner__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_res_users__message_main_attachment_id
msgid "Main Attachment"
msgstr "主要附件"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tools/debug_manager.js:0
#, python-format
msgid "Manage Messages"
msgstr "管理消息"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to_force_new
msgid ""
"Manage answers as new incoming emails instead of replies going to the same "
"thread."
msgstr "将回复作为新的传入电子邮件进行管理，而不是将回复发送到同一线程."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#: code:addons/mail/static/src/components/activity/activity.xml:0
#, python-format
msgid "Mark Done"
msgstr "标记完成"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Mark all read"
msgstr "标记全部已读"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Mark as Done"
msgstr "标记为完成"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/components/thread_needaction_preview/thread_needaction_preview.xml:0
#: code:addons/mail/static/src/components/thread_preview/thread_preview.xml:0
#, python-format
msgid "Mark as Read"
msgstr "标记为已读"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#, python-format
msgid "Mark as Todo"
msgstr "标记为待办"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Mark as done"
msgstr "完成"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_meeting
msgid "Meeting"
msgstr "会议"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__member_count
msgid "Member Count"
msgstr "成员数量"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__channel_partner_ids
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Members"
msgstr "成员"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__group_ids
msgid ""
"Members of those groups will automatically added as followers. Note that "
"they will be able to manage their subscription manually if necessary."
msgstr "这些群组的成员将自动添加为关注者。注意, 如果有必要，他们可以手工管理他们的订阅。"

#. module: mail
#: model:ir.model,name:mail.model_base_partner_merge_automatic_wizard
msgid "Merge Partner Wizard"
msgstr "合并业务伙伴向导"

#. module: mail
#: code:addons/mail/wizard/base_partner_merge_automatic_wizard.py:0
#, python-format
msgid "Merged with the following partners:"
msgstr "合并以下业务伙伴:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message/message.js:0
#: model:ir.model,name:mail.model_mail_message
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__message_id
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__message
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#, python-format
msgid "Message"
msgstr "消息"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_text_input/composer_text_input.js:0
#, python-format
msgid "Message #%s..."
msgstr "消息 #%s..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_text_input/composer_text_input.js:0
#, python-format
msgid "Message %s..."
msgstr "消息%s..."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_has_error
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error
#: model:ir.model.fields,field_description:mail.field_res_users__message_has_error
msgid "Message Delivery error"
msgstr "消息传递错误"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__mail_message_id
msgid "Message ID"
msgstr "消息ID"

#. module: mail
#: model:ir.model,name:mail.model_mail_notification
msgid "Message Notifications"
msgstr "消息通知"

#. module: mail
#: model:ir.model,name:mail.model_mail_message_reaction
msgid "Message Reaction"
msgstr "消息反应"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_message_reaction_action
#: model:ir.ui.menu,name:mail.mail_message_reaction_menu
msgid "Message Reactions"
msgstr "消息反应"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_name
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_name
#: model:ir.model.fields,field_description:mail.field_mail_message__record_name
msgid "Message Record Name"
msgstr "消息记录名称"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__name
msgid "Message Type"
msgstr "消息类型"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_group/notification_group.xml:0
#, python-format
msgid "Message delivery failure image"
msgstr "消息传递失败图像"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__description
#: model:ir.model.fields,help:mail.field_mail_message__description
msgid "Message description: either the subject, or the beginning of the body"
msgstr "消息描述：主题或正文的开始部分"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/composer_view/composer_view.js:0
#, python-format
msgid "Message posted on \"%s\""
msgstr "在“%s”上发布的消息"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_to
msgid "Message recipients (emails)"
msgstr "消息收件人(EMail)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__references
msgid "Message references, such as identifiers of previous messages"
msgstr "消息引用"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Message should be a valid EmailMessage instance"
msgstr "消息应该具有一个电子邮件消息实例"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__name
msgid ""
"Message subtype gives a more precise type on the message, especially for "
"system notifications. For example, it can be a notification related to a new"
" record (New), or to a stage change in a process (Stage change). Message "
"subtypes allow to precisely tune the notifications the user want to receive "
"on its wall."
msgstr ""
"消息子类型在消息上提供了更精确的类型，尤其是对于系统通知。例如，它可以是与新记录（新）相关的通知，或者是过程（阶段更改）中的阶段改变。消息子类型允许精确调整用户希望在其壁上接收到的通知。"

#. module: mail
#: model:ir.model,name:mail.model_mail_message_subtype
msgid "Message subtypes"
msgstr "消息子类型"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__subtype_ids
msgid ""
"Message subtypes followed, meaning subtypes that will be pushed onto the "
"user's Wall."
msgstr "消息子类型跟随，意味着将被推到用户墙上的子类型。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__message_type
#: model:ir.model.fields,help:mail.field_mail_mail__message_type
#: model:ir.model.fields,help:mail.field_mail_message__message_type
msgid ""
"Message type: email for email message, notification for system message, "
"comment for other messages such as user replies"
msgstr "消息类型：EMail用于 邮件消息， 通知用户系统消息，评论用于其他消息，例如用户回复"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__message_id
#: model:ir.model.fields,help:mail.field_mail_message__message_id
msgid "Message unique identifier"
msgstr "消息唯一编号"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_id
#: model:ir.model.fields,field_description:mail.field_mail_message__message_id
msgid "Message-Id"
msgstr "消息ID"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#: model:ir.actions.act_window,name:mail.action_view_mail_message
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_ids
#: model:ir.ui.menu,name:mail.menu_mail_message
#: model_terms:ir.ui.view,arch_db:mail.view_message_tree
#, python-format
msgid "Messages"
msgstr "消息"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Messages Search"
msgstr "消息搜索"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "Messages can be <b>starred</b> to remind you to check back later."
msgstr "消息允许 <b>星号</b> 标出来以便提醒您过后查看."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "Messages marked as read will appear in the history."
msgstr "标记为已读的消息将出现在历史记录中。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__internal
msgid ""
"Messages with internal subtypes will be visible only by employees, aka "
"members of base_user group"
msgstr "内部消息只有员工可见，即基础用户组成员"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Messages with tracking values cannot be modified"
msgstr "无法修改具有追踪值的消息"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Minimum activity for voice detection"
msgstr "语音检测的最小活动"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_email_missing
msgid "Missing email"
msgstr "缺少电子邮件"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_email_missing
msgid "Missing email addresss"
msgstr "缺少电子邮件地址"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__res_model
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__model
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
msgid "Model"
msgstr "模型"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model_change
msgid "Model has change"
msgstr "模型已更改"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_model
msgid "Model of the followed resource"
msgstr "关注资源的模型"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__res_model
msgid ""
"Model the subtype applies to. If False, this subtype applies to all models."
msgstr "子类型适用的模型：如果错误，子类型适用于所有模型."

#. module: mail
#: model:ir.model,name:mail.model_ir_model
msgid "Models"
msgstr "模型"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid ""
"Modifying the model can have an impact on existing activities using this "
"activity type, be careful."
msgstr "注意，这个活动类型修改模型会影响现有活动."

#. module: mail
#: model:ir.model,name:mail.model_base_module_uninstall
msgid "Module Uninstall"
msgstr "模块卸载"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__months
msgid "Months"
msgstr "月份"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "More"
msgstr "更多"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Mute"
msgstr "静音"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__my_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_partner__my_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_users__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "我的活动截止时间"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__name
#: model:ir.model.fields,field_description:mail.field_mail_channel__name
#: model:ir.model.fields,field_description:mail.field_mail_followers__name
#: model:ir.model.fields,field_description:mail.field_mail_guest__name
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__name
#: model:ir.model.fields,field_description:mail.field_mail_template__name
msgid "Name"
msgstr "名称"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__record_name
#: model:ir.model.fields,help:mail.field_mail_mail__record_name
#: model:ir.model.fields,help:mail.field_mail_message__record_name
msgid "Name get of the related document."
msgstr "获得相关单据的名称."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__report_name
msgid ""
"Name to use for the generated report file (may contain placeholders)\n"
"The extension can be omitted and will then come from the report type."
msgstr ""
"用于生成的报表文件的名称（可能包含占位符）\n"
"可以省略扩展，然后来自报表类型。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__needaction
#: model:ir.model.fields,field_description:mail.field_mail_message__needaction
#: model:ir.model.fields,help:mail.field_mail_mail__needaction
#: model:ir.model.fields,help:mail.field_mail_message__needaction
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Need Action"
msgstr "需要行动"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.xml:0
#, python-format
msgid "New Channel"
msgstr "新频道"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_char
msgid "New Value Char"
msgstr "新字符值"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_datetime
msgid "New Value Datetime"
msgstr "新日期时间值"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_float
msgid "New Value Float"
msgstr "新浮点值"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_integer
msgid "New Value Integer"
msgstr "新整数值"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_monetary
msgid "New Value Monetary"
msgstr "新货币值"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_text
msgid "New Value Text"
msgstr "新文本值"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#: code:addons/mail/static/src/models/chat_window/chat_window.js:0
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "New message"
msgstr "新建消息"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "New messages"
msgstr "新建消息"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "New messages appear here."
msgstr "新消息出现在这里."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "New values"
msgstr "新值"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Next"
msgstr "下一页"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Next (Right-Arrow)"
msgstr "下一步 (右箭头)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
msgid "Next Activities"
msgstr "下一步活动"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#, python-format
msgid "Next Activity"
msgstr "下一个活动"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_users__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "下一活动截止日期"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_summary
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_summary
#: model:ir.model.fields,field_description:mail.field_res_users__activity_summary
msgid "Next Activity Summary"
msgstr "下一活动摘要"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_users__activity_type_id
msgid "Next Activity Type"
msgstr "下一活动类型"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__has_recommended_activities
msgid "Next activities available"
msgstr "下一活动可用"

#. module: mail
#: code:addons/mail/models/mail_notification.py:0
#, python-format
msgid "No Error"
msgstr "没有错误"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#, python-format
msgid "No IM status available"
msgstr "没有可用的 IM 状态"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__no_record
msgid "No Record"
msgstr "没有记录"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/webclient/commands/mail_providers.js:0
#, python-format
msgid "No channel found"
msgstr "未找到相应的频道"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.xml:0
#, python-format
msgid "No conversation selected."
msgstr "未选择对话。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_list/notification_list.xml:0
#, python-format
msgid "No conversation yet..."
msgstr "暂无对话..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "No history messages"
msgstr "没有历史讯息"

#. module: mail
#: code:addons/mail/wizard/mail_resend_message.py:0
#, python-format
msgid "No message_id found in context"
msgstr "上下文中没有找到message_id"

#. module: mail
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid "No recipient found."
msgstr "没有找到收件人。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "No starred messages"
msgstr "没有已加星标的消息"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reply_to_force_new
#: model:ir.model.fields,field_description:mail.field_mail_message__reply_to_force_new
msgid "No threading for answers"
msgstr "无响应"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/webclient/commands/mail_providers.js:0
#, python-format
msgid "No user found"
msgstr "未找到相应的用户"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "No user found that is not already a member of this channel."
msgstr "未找到尚未成为此频道成员的用户。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#, python-format
msgid "No users found"
msgstr "未找到相应的用户"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__default
msgid "None"
msgstr "无"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__email_normalized
#: model:ir.model.fields,field_description:mail.field_res_partner__email_normalized
#: model:ir.model.fields,field_description:mail.field_res_users__email_normalized
msgid "Normalized Email"
msgstr "规范化邮件"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message/message.js:0
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_note
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_note
#: model:ir.model.fields,field_description:mail.field_mail_activity__note
#: model:mail.message.subtype,name:mail.mt_note
#, python-format
msgid "Note"
msgstr "笔记"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users__notification_type
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Notification"
msgstr "通知"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_notification
msgid "Notification Email"
msgstr "通知电子邮件"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__notification_type
msgid "Notification Type"
msgstr "通知类型"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_delete_notification_ir_actions_server
#: model:ir.cron,cron_name:mail.ir_cron_delete_notification
#: model:ir.cron,name:mail.ir_cron_delete_notification
msgid "Notification: Delete Notifications older than 6 Month"
msgstr "通知：删除超过6个月的通知"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_notification_action
#: model:ir.model.fields,field_description:mail.field_mail_mail__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__notification_ids
#: model:ir.ui.menu,name:mail.mail_notification_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_tree
msgid "Notifications"
msgstr "通知"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__notify
msgid "Notify followers"
msgstr "通知关注者"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__notify
msgid "Notify followers of the document (mass post only)"
msgstr "通知此单据的关注者(只用于批量发送)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_needaction_counter
msgid "Number of Actions"
msgstr "动作数量"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_count
msgid ""
"Number of days/week/month before executing the action. It allows to plan the"
" action deadline."
msgstr "在执行该操作前几天/周/月，允许操的截止日期。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_has_error_counter
msgid "Number of errors"
msgstr "错误数"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_channel__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_users__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "需要作业消息数量"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_channel__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_has_error_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error_counter
#: model:ir.model.fields,help:mail.field_res_users__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "发送错误的消息数量"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_channel__message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_unread_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_unread_counter
#: model:ir.model.fields,help:mail.field_res_users__message_unread_counter
msgid "Number of unread messages"
msgstr "未读消息数量"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_borders
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
msgid "Odoo"
msgstr "Odoo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_alert/notification_alert.xml:0
#, python-format
msgid ""
"Odoo Push notifications have been blocked. Go to your browser settings to "
"allow them."
msgstr "Odoo推送通知已被阻止。请转到您的浏览器设置以允许它们。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_request/notification_request.js:0
#, python-format
msgid ""
"Odoo will not have the permission to send native notifications on this "
"device."
msgstr "没有权限在该设备上发送本地通知。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "Offline"
msgstr "离线"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_delete_confirm_dialog/attachment_delete_confirm_dialog.xml:0
#, python-format
msgid "Ok"
msgstr "确定"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_char
msgid "Old Value Char"
msgstr "旧字符值"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_datetime
msgid "Old Value DateTime"
msgstr "旧日期时间值"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_float
msgid "Old Value Float"
msgstr "旧浮点值"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_integer
msgid "Old Value Integer"
msgstr "旧整数值"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_monetary
msgid "Old Value Monetary"
msgstr "旧货币值"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_text
msgid "Old Value Text"
msgstr "旧文本值"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Old values"
msgstr "旧值"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid ""
"Once a message has been starred, you can come back and review it at any time"
" here."
msgstr "如果消息已生效，您可以返回并随时在此对它进行检查。"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_res_users_settings_unique_user_id
msgid "One user should only have one mail user settings."
msgstr "一个用户应该只有一个邮件用户设置。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "Online"
msgstr "线上"

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Only administrators are allowed to export mail message"
msgstr "只允许管理员导出邮件消息"

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Only administrators are allowed to use grouped read on message model"
msgstr "仅允许管理员在消息模型上使用分组阅读"

#. module: mail
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Only custom models can be modified."
msgstr "仅有自定义模型能被修改."

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Only logged notes can have their content updated on model '%s'"
msgstr "只有记录的笔记才能在模型“%s”上更新其内容"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Only messages type comment can have their content updated"
msgstr "只有消息类型的备注才能更新其内容"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"Only messages type comment can have their content updated on model "
"'mail.channel'"
msgstr "只有消息类型的备注才能在模型“mail.channel”上更新其内容"

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#: code:addons/mail/models/mail_render_mixin.py:0
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Only users belonging to the \"%s\" group can modify dynamic templates."
msgstr "只有属于“%s”组的用户才能修改动态模板。"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel_partner__fold_state__open
msgid "Open"
msgstr "打开"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_form
msgid "Open Document"
msgstr "打开单据"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_form
msgid "Open Parent Document"
msgstr "打开上级单据"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.js:0
#, python-format
msgid "Open chat"
msgstr "打开聊天"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_activity_notice/rtc_activity_notice.xml:0
#, python-format
msgid "Open conference:"
msgstr "打开会议："

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#, python-format
msgid "Open in Discuss"
msgstr "在讨论打开"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "Open profile"
msgstr "打开个人资料"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_optout
msgid "Opted Out"
msgstr "选择退出"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_force_thread_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr "无论是否回复，所有的接收的邮件都将附上一条线索（记录）选配的ID。如果设置了，这个将完全阻止新记录的创建。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_notification__mail_mail_id
msgid "Optional mail_mail ID. Used mainly to optimize searches."
msgstr "mail_mail ID可选。主要用于优化搜索."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__mail_server_id
msgid ""
"Optional preferred server for outgoing mails. If not set, the highest "
"priority one will be used."
msgstr "可选的推荐发信邮件服务器。如果没有设置，优先级最高的一个会被选中。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__report_template
msgid "Optional report to print and attach"
msgstr "可选的打印和附加报表"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__lang
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__lang
#: model:ir.model.fields,help:mail.field_mail_render_mixin__lang
#: model:ir.model.fields,help:mail.field_mail_template__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"在发送邮件时可选择的语言代码(ISO 代码)。如果没有设置，会使用英文版本。一般用占位符来确定合适的语言，例如: {{ "
"object.partner_id.lang }}."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__null_value
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__null_value
#: model:ir.model.fields,help:mail.field_mail_render_mixin__null_value
#: model:ir.model.fields,help:mail.field_mail_template__null_value
msgid "Optional value to use if the target field is empty"
msgstr "如果目标字段为空则使用此可选值"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to_mode
msgid ""
"Original Discussion: Answers go in the original document discussion thread. \n"
" Another Email Address: Answers go to the email address mentioned in the tracking message-id instead of original document discussion thread. \n"
" This has an impact on the generated message-id."
msgstr ""
"原始讨论:答案在原始文档讨论线程中.\n"
" 另一个电子邮件地址: 答复转到跟踪讯息 ID 中提到的电子邮件地址，而不是原始文档讨论线程.\n"
" 这对产生的信息 ID 有影响."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_in_reply_to_view/message_in_reply_to_view.xml:0
#, python-format
msgid "Original message was deleted"
msgstr "原邮件已删除"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__outgoing
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Outgoing"
msgstr "发出"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__mail_server_id
msgid "Outgoing Mail Server"
msgstr "发信邮件服务器"

#. module: mail
#: model:ir.model,name:mail.model_mail_mail
msgid "Outgoing Mails"
msgstr "寄出邮件"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_server_id
msgid "Outgoing mail server"
msgstr "邮件发送服务器"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/views/activity/activity_renderer.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__overdue
#, python-format
msgid "Overdue"
msgstr "逾期"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Override author's email"
msgstr "覆盖作者EMail"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_user_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_user_id
msgid "Owner"
msgstr "所有者"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "PDF file"
msgstr "PDF文件"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__parent_id
msgid "Parent"
msgstr "上级"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_message__parent_id
msgid "Parent Message"
msgstr "上级消息"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_model_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_parent_model_id
msgid "Parent Model"
msgstr "上级模型"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_thread_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "上级记录ID"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_model_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"父级模型拥有别名。拥有别名参考的模型不一定是alias_model_id给出的模型 (例如：project(parent_model) 和任务 "
"(模型))"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__parent_id
msgid ""
"Parent subtype, used for automatic subscription. This field is not correctly"
" named. For example on a project, the parent_id of project subtypes refers "
"to task-related subtypes."
msgstr "父类,用于自动订阅.这个字段没有正确的命名。例如在一个项目上，项目子类型的上一级ID指任务相关的子类型。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__partner_id
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__partner_id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__partner_id
msgid "Partner"
msgstr "业务伙伴"

#. module: mail
#: code:addons/mail/models/res_partner.py:0
#, python-format
msgid "Partner Profile"
msgstr "业务伙伴特征"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_readonly
msgid "Partner Readonly"
msgstr "业务合作伙伴只读"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_partner
msgid "Partner with additional information for mail resend"
msgstr "为邮件重新发送提供附加信息的合作伙伴"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__notified_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__notified_partner_ids
msgid "Partners with Need Action"
msgstr "需要行动的业务伙伴"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/delete_message_confirm_dialog/delete_message_confirm_dialog.xml:0
#, python-format
msgid ""
"Pay attention: The followers of this document who were notified by email "
"will still be able to read the content of this message and reply to it."
msgstr "注意：收到邮件通知的本文档关注者仍然可以阅读此消息的内容并进行回复。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_request/notification_request.js:0
#, python-format
msgid "Permission denied"
msgstr "没有权限"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__phone
#: model:ir.model.fields,field_description:mail.field_res_users__phone
msgid "Phone"
msgstr "电话"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__phonecall
msgid "Phonecall"
msgstr "电话"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__copyvalue
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__copyvalue
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__copyvalue
#: model:ir.model.fields,field_description:mail.field_mail_template__copyvalue
msgid "Placeholder Expression"
msgstr "占位符表达式"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/views/activity/activity_renderer.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__planned
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__planned
#, python-format
msgid "Planned"
msgstr "已计划"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity_box/activity_box.xml:0
#, python-format
msgid "Planned activities"
msgstr "计划的活动"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Planned in"
msgstr "计划于"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_suggested_recipient/composer_suggested_recipient.js:0
#, python-format
msgid "Please complete customer's information"
msgstr "请填写客户信息"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Please contact us instead using"
msgstr "不要使用，请联系我们"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.js:0
#: code:addons/mail/static/src/models/composer_view/composer_view.js:0
#, python-format
msgid "Please wait while the file is uploading."
msgstr "文件上传中，请稍等。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_container/chatter_container.xml:0
#: code:addons/mail/static/src/components/discuss/discuss.xml:0
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#, python-format
msgid "Please wait..."
msgstr "请等待......"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users__notification_type
msgid ""
"Policy on how to handle Chatter notifications:\n"
"- Handle by Emails: notifications are sent to your email address\n"
"- Handle in Odoo: notifications appear in your Odoo Inbox"
msgstr ""
"处理聊天通知的规则:\n"
"- 通过EMail处理: 通知会发送至您的EMail地址\n"
"- 系统内部处理: 通知会出现在Odoo收件箱"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_contact
#: model:ir.model.fields,help:mail.field_mail_channel__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"政策是通过邮件网关在单据上提交一个消息\n"
"- everyone: 任何人都可以提交\n"
"- partners: 只有认证过的合作伙伴\n"
"- followers: 只有相关单据或下列频道成员的跟随者\n"

#. module: mail
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Portal Access Granted"
msgstr "授权门户访问权限"

#. module: mail
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Portal Access Revoked"
msgstr "被撤销门户访问"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__composition_mode__mass_post
msgid "Post on Multiple Documents"
msgstr "在多个文档上发布"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__composition_mode__comment
msgid "Post on a document"
msgstr "提交在文件上"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "Post your message on the thread"
msgstr "在帖子上发布您的消息"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Posting a message should be done on a business document. Use message_notify "
"to send a notification to an user."
msgstr "应在业务文档上发布消息。使用 message_notify 向用户发送通知。"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Posting a message with channels as listeners is not supported since Odoo "
"14.3+. Please update code accordingly."
msgstr "自 Odoo 14.3+ 起，不支持使用频道作为侦听器发布消息。请相应地更新代码。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_borders
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "Powered by"
msgstr "技术提供"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__previous_type_ids
msgid "Preceding Activities"
msgstr "预先活动"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__reply_to
msgid "Preferred response address"
msgstr "首选回复地址"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Press a key to register it as the push-to-talk shortcut"
msgstr "按下一个按键将其注册为一键通话快捷方式"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#: code:addons/mail/static/src/components/mail_template/mail_template.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#, python-format
msgid "Preview"
msgstr "预览"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Preview of"
msgstr "预览"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Previous"
msgstr "上一页"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Previous (Left-Arrow)"
msgstr "上一个 (左箭头)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__previous_activity_type_id
msgid "Previous Activity Type"
msgstr "前一活动类型"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Print"
msgstr "打印"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__public
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Privacy"
msgstr "隐私"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#, python-format
msgid "Private channel"
msgstr "私人频道"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/discuss_sidebar_category/discuss_sidebar_category.js:0
#, python-format
msgid "Public Channels"
msgstr "公众频道"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#, python-format
msgid "Public channel"
msgstr "公共频道"

#. module: mail
#: model:ir.model,name:mail.model_publisher_warranty_contract
msgid "Publisher Warranty Contract"
msgstr "出版商保修合同"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_module_update_notification_ir_actions_server
#: model:ir.cron,cron_name:mail.ir_cron_module_update_notification
#: model:ir.cron,name:mail.ir_cron_module_update_notification
msgid "Publisher: Update Notification"
msgstr "发布者: 更新通知"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__push_to_talk_key
msgid "Push-To-Talk shortcut"
msgstr "一键通话快捷方式"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Push-to-talk key"
msgstr "一键通话按键"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar/discuss_sidebar.xml:0
#, python-format
msgid "Quick search..."
msgstr "快速搜索..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_tree
msgid "RTC Session"
msgstr "RTC会话"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__rtc_session_ids
msgid "RTC Sessions"
msgstr "RTC 会话"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_channel_rtc_session_action
#: model:ir.ui.menu,name:mail.mail_channel_rtc_session_menu
msgid "RTC sessions"
msgstr "RTC 会话"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings_volumes__volume
msgid ""
"Ranges between 0.0 and 1.0, scale depends on the browser implementation"
msgstr "范围在 0.0 和 1.0 之间，比例取决于浏览器实现"

#. module: mail
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid "Re:"
msgstr "回复："

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__guest_id
msgid "Reacting Guest"
msgstr "反应的游客"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__partner_id
msgid "Reacting Partner"
msgstr "反应的伙伴"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_reaction_view_tree
msgid "Reaction"
msgstr "反应"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reaction_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__reaction_ids
msgid "Reactions"
msgstr "反应"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__read_date
msgid "Read Date"
msgstr "读取日期"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_popover/notification_popover.js:0
#, python-format
msgid "Ready"
msgstr "就绪"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__ready
msgid "Ready to Send"
msgstr "准备好发送"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__reason
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Reason"
msgstr "原因"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__received
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Received"
msgstr "已接收"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Received by %s"
msgstr "由 %s 收到"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Received by %s and %s"
msgstr "由%s 和 %s 收到"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Received by %s, %s and more"
msgstr "由 %s, %s 等人收到"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Received by Everyone"
msgstr "所有人已收到"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__partner_id
#: model:ir.model.fields,field_description:mail.field_mail_notification__res_partner_id
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Recipient"
msgstr "收件人"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__partner_ids
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Recipients"
msgstr "收件人"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__recommended_activity_type_id
msgid "Recommended Activity Type"
msgstr "推荐的活动类型"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__resource_ref
msgid "Record"
msgstr "记录"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_force_thread_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_force_thread_id
msgid "Record Thread ID"
msgstr "记录线索ID"

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Records:"
msgstr "记录:"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__reply_to_mode__new
msgid "Redirect to another email address"
msgstr "重定向到另一个电子邮件地址"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__references
msgid "References"
msgstr "参考"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_invitation_card/rtc_invitation_card.xml:0
#: code:addons/mail/static/src/components/rtc_invitation_card/rtc_invitation_card.xml:0
#, python-format
msgid "Refuse"
msgstr "拒绝"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Regards,"
msgstr "祝愿,"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Register new key"
msgstr "注册新密钥"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Reject"
msgstr "拒绝"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__parent_id
#: model:ir.model.fields,field_description:mail.field_res_users__parent_id
msgid "Related Company"
msgstr "关联公司"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__res_id
#: model:ir.model.fields,field_description:mail.field_mail_message__res_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_id
msgid "Related Document ID"
msgstr "相关单据编号"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__model
#: model:ir.model.fields,field_description:mail.field_mail_mail__model
#: model:ir.model.fields,field_description:mail.field_mail_message__model
#: model:ir.model.fields,field_description:mail.field_mail_template__model
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_model
msgid "Related Document Model"
msgstr "相关的单据模型"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_model
msgid "Related Document Model Name"
msgstr "相关的单据模型名称"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__mail_template_id
msgid "Related Mail Template"
msgstr "相关的邮件模板"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Related Message"
msgstr "相关的消息"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__partner_id
msgid "Related Partner"
msgstr "相关的业务伙伴"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__relation_field
msgid "Relation field"
msgstr "关联字段"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#: code:addons/mail/static/src/components/attachment_image/attachment_image.xml:0
#: code:addons/mail/static/src/components/attachment_image/attachment_image.xml:0
#, python-format
msgid "Remove"
msgstr "移除"

#. module: mail
#: model:ir.model,name:mail.model_mail_blacklist_remove
msgid "Remove email from blacklist wizard"
msgstr "从黑名单向导中删除电子邮件"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Remove the contextual action to use this template on related documents"
msgstr "移除上下文动作，使用相关单据的模板"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower/follower.xml:0
#: code:addons/mail/static/src/components/follower/follower.xml:0
#, python-format
msgid "Remove this follower"
msgstr "移除此关注者"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__render_model
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__render_model
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__render_model
#: model:ir.model.fields,field_description:mail.field_mail_template__render_model
msgid "Rendering Model"
msgstr "呈现模型"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to_mode
msgid "Replies"
msgstr "回复"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Reply"
msgstr "回复"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_template__reply_to
msgid "Reply To"
msgstr "回复"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,help:mail.field_mail_mail__reply_to
#: model:ir.model.fields,help:mail.field_mail_message__reply_to
msgid ""
"Reply email address. Setting the reply_to bypasses the automatic thread "
"creation."
msgstr "回复EMail地址。设置回复到reply_to以绕过自动创建。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__reply_to
msgid "Reply-To"
msgstr "回复 至"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Replying to"
msgstr "回复"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__report_name
msgid "Report Filename"
msgstr "报告文件名"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__request_partner_id
msgid "Requesting Partner"
msgstr "请求业务合作伙伴"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users__res_users_settings_ids
msgid "Res Users Settings"
msgstr "资源用户设置"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_message_action
msgid "Resend mail"
msgstr "重发邮件"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Resend to selected"
msgstr "重发至已选中"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend_wizard_id
msgid "Resend wizard"
msgstr "重发向导"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Reset Zoom"
msgstr "重置缩放"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Reset Zoom (0)"
msgstr "重置缩放 (0)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_id
msgid "Responsible"
msgstr "负责人"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_users__activity_user_id
msgid "Responsible User"
msgstr "负责用户"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__restrict_template_rendering
msgid "Restrict Template Rendering"
msgstr "限制模板渲染"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Restrict mail templates and Jinja rendering."
msgstr "限制邮件模板和 Jinja 渲染。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Restrict mail templates edition and QWEB placeholders usage."
msgstr "限制邮件模板版本和 QWEB 占位符的使用。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Retry"
msgstr "重试"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__body_html
msgid "Rich-text Contents"
msgstr "富文本内容"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__body_html
msgid "Rich-text/HTML message"
msgstr "富文本/HTML消息"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__rtc_inviting_session_id
msgid "Ringing session"
msgstr "振铃会话"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Rotate"
msgstr "替换"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Rotate (r)"
msgstr "旋转 (r)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__rtc_session_ids
msgid "Rtc Session"
msgstr "Rtc 会话"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "SMTP Server"
msgstr "SMTP 服务器"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__user_id
#: model:ir.model.fields,field_description:mail.field_res_users__user_id
msgid "Salesperson"
msgstr "销售员"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Save"
msgstr "保存"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Save as a new template"
msgstr "保存为新模版"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Save as new template"
msgstr "保存为新模版"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_count
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Schedule"
msgstr "安排"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#: code:addons/mail/static/src/models/activity/activity.js:0
#: code:addons/mail/static/src/models/chatter/chatter.js:0
#, python-format
msgid "Schedule Activity"
msgstr "安排活动"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Schedule activities to help you get things done."
msgstr "安排活动以帮助您完成工作。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#: code:addons/mail/static/src/xml/activity_view.xml:0
#, python-format
msgid "Schedule activity"
msgstr "安排活动"

#. module: mail
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid "Schedule an Activity"
msgstr "安排活动"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Schedule an activity"
msgstr "安排活动"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__scheduled_date
msgid "Scheduled Date"
msgstr "安排的日期"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__scheduled_date
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Scheduled Send Date"
msgstr "安排的发送日期"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
msgid "Search Alias"
msgstr "搜索别名"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_search
msgid "Search Groups"
msgstr "搜索群组"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_search
msgid "Search RTC session"
msgstr "搜索 RTC 会话"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window/chat_window.js:0
#: code:addons/mail/static/src/components/discuss/discuss.js:0
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.js:0
#, python-format
msgid "Search user..."
msgstr "搜索用户..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/views/activity/activity_controller.js:0
#, python-format
msgid "Search: %s"
msgstr "搜索: %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Seen by %s"
msgstr "被%s 看到"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Seen by %s and %s"
msgstr "被 %s 和 %s 看到"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Seen by %s, %s and more"
msgstr "被 %s, %s 等人看到"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Seen by Everyone"
msgstr "所有人已查看"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#, python-format
msgid "Select a user..."
msgstr "选择用户…"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__model_object_field
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__model_object_field
#: model:ir.model.fields,help:mail.field_mail_render_mixin__model_object_field
#: model:ir.model.fields,help:mail.field_mail_template__model_object_field
msgid ""
"Select target field from the related document model.\n"
"If it is a relationship field you will be able to select a target field at the destination of the relationship."
msgstr ""
"从相关单据模型中选择目标字段。\n"
"如果这是个关系型字段，您可以选择关系型字段的目标字段。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid ""
"Select the action to do on each mail and correct the email address if "
"needed. The modified address will be saved on the corresponding contact."
msgstr "选择每个邮件上的操作，并在需要时更正EMail地址。修改后的地址将保存在相应的联系人上。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__public__groups
#, python-format
msgid "Selected group of users"
msgstr "选择用户组"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "Selected users:"
msgstr "选择用户:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/composer_view/composer_view.js:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Send"
msgstr "发送"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend
msgid "Send Again"
msgstr "再次发送"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__send_mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__email
msgid "Send Email"
msgstr "发送EMail"

#. module: mail
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "Send Mail (%s)"
msgstr "发送邮件（ %s）"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/mail_template/mail_template.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
#, python-format
msgid "Send Now"
msgstr "立即发送"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#, python-format
msgid "Send a message"
msgstr "发送消息"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_text_input/composer_text_input.js:0
#, python-format
msgid "Send a message to followers..."
msgstr "向关注者发送消息..."

#. module: mail
#: model:ir.actions.act_window,name:mail.action_partner_mass_mail
msgid "Send email"
msgstr "发送邮件"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#, python-format
msgid "Send message"
msgstr "发送消息"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_from
msgid "Sender address"
msgstr "发件人地址"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_from
msgid ""
"Sender address (placeholders may be used here). If not set, the default "
"value will be the author's email alias if configured, or email address."
msgstr "发送人地址（所在位置会被在这里使用），如果不设置，作者的邮箱别名或邮箱地址就会被作为默认值。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_popover/notification_popover.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__sent
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__sent
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
#, python-format
msgid "Sent"
msgstr "已发送"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__sequence
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__sequence
msgid "Sequence"
msgstr "序号"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_server
msgid "Server Action"
msgstr "服务器动作"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__active
msgid "Set active to false to hide the channel without removing it."
msgstr "将 启用 设置为 否 以隐藏频道而不删除它。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_option_list/rtc_option_list.xml:0
#: code:addons/mail/static/src/models/rtc_call_viewer/rtc_call_viewer.js:0
#, python-format
msgid "Settings"
msgstr "设置"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Share screen"
msgstr "共享屏幕"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.js:0
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.js:0
#, python-format
msgid "Shift left"
msgstr "向左移"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.js:0
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.js:0
#, python-format
msgid "Shift right"
msgstr "向右移"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__description
#: model:ir.model.fields,field_description:mail.field_mail_message__description
msgid "Short description"
msgstr "简述"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_shortcode_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_shortcode_view_tree
msgid "Shortcodes"
msgstr "短码"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__source
msgid "Shortcut"
msgstr "快捷"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_layout_menu/rtc_layout_menu.xml:0
#, python-format
msgid "Show All"
msgstr "显示全部"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower_list_menu/follower_list_menu.xml:0
#, python-format
msgid "Show Followers"
msgstr "显示关注者"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Show Member List"
msgstr "显示成员列表"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Show a helper message"
msgstr "显示帮助消息"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Show all records which has next action date is before today"
msgstr "显示所有的在今天之前的下一个行动日期的记录"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_suggested_recipient_list/composer_suggested_recipient_list.xml:0
#, python-format
msgid "Show less"
msgstr "折叠"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_suggested_recipient_list/composer_suggested_recipient_list.xml:0
#, python-format
msgid "Show more"
msgstr "展开"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_layout_menu/rtc_layout_menu.xml:0
#, python-format
msgid "Show only video"
msgstr "仅显示视频"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "Showing"
msgstr "显示"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_layout_menu/rtc_layout_menu.xml:0
#, python-format
msgid "Sidebar"
msgstr "侧边栏"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__ref_ir_act_window
msgid "Sidebar action"
msgstr "边栏操作"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__ref_ir_act_window
msgid ""
"Sidebar action to make this template available on records of the related "
"document model"
msgstr "用于在相关单据上调用此模版的边栏按钮"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
msgid "Source"
msgstr "来源"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_user_type__specific
msgid "Specific User"
msgstr "特定用户"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__res_model
msgid ""
"Specify a model if the activity should be specific to a model and not "
"available when managing activities for other models."
msgstr "如果活动应特定于某个模型并且在管理其他模型的活动时不可用，则指定一个模型。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_layout_menu/rtc_layout_menu.xml:0
#, python-format
msgid "Spotlight"
msgstr "聚光灯"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred
#: model:ir.model.fields,field_description:mail.field_mail_message__starred
#, python-format
msgid "Starred"
msgstr "星标消息"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Start a Call"
msgstr "开启语音会议"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Start a Video Call"
msgstr "开启视频会议"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.xml:0
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Start a conversation"
msgstr "开启新会议"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar/discuss_sidebar.xml:0
#: code:addons/mail/static/src/components/discuss_sidebar/discuss_sidebar.xml:0
#, python-format
msgid "Start a meeting"
msgstr "开始会议"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__state
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_form
msgid "State"
msgstr "状态"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__state
#: model:ir.model.fields,field_description:mail.field_mail_notification__notification_status
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Status"
msgstr "状态"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,help:mail.field_res_partner__activity_state
#: model:ir.model.fields,help:mail.field_res_users__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"基于活动的状态 \n"
" 逾期：已经超过截止日期 \n"
" 现今：活动日期是当天 \n"
" 计划：未来活动。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#, python-format
msgid "Stop adding users"
msgstr "停止添加用户"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Stop camera"
msgstr "关闭相机"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Stop replying"
msgstr "停止回复"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Stop screen sharing"
msgstr "停止屏幕共享"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__push_to_talk_key
msgid ""
"String formatted to represent a key with modifiers following this pattern: "
"shift.ctrl.alt.key, e.g: truthy.1.true.b"
msgstr "格式化字符串以表示具有以下模式的修饰符的键：shift.ctrl.alt.key，例如：truthy.1.true.b"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__sub_model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__sub_model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__sub_model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_template__sub_model_object_field
msgid "Sub-field"
msgstr "子字段"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__sub_object
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__sub_object
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__sub_object
#: model:ir.model.fields,field_description:mail.field_mail_template__sub_object
msgid "Sub-model"
msgstr "子模型"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__subject
#: model:ir.model.fields,field_description:mail.field_mail_mail__subject
#: model:ir.model.fields,field_description:mail.field_mail_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_template__subject
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__subject
msgid "Subject"
msgstr "主题"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__subject
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Subject (placeholders may be used here)"
msgstr "主题（可以在这里使用占位符）"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Subject..."
msgstr "主题..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "Subject:"
msgstr "主题:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__substitution
msgid "Substitution"
msgstr "替换"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__subtype_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_message__subtype_id
#: model_terms:ir.ui.view,arch_db:mail.view_message_subtype_tree
msgid "Subtype"
msgstr "子类型"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_message_subtype
#: model:ir.ui.menu,name:mail.menu_message_subtype
msgid "Subtypes"
msgstr "子类型"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__suggested_next_type_ids
msgid "Suggest"
msgstr "建议"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__chaining_type__suggest
msgid "Suggest Next Activity"
msgstr "建议下一个活动"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__suggested_next_type_ids
msgid "Suggest these activities once the current one is marked as done."
msgstr "一旦当前活动被标记为完成，建议这些活动。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_summary
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_summary
#: model:ir.model.fields,field_description:mail.field_mail_activity__summary
msgid "Summary"
msgstr "摘要"

#. module: mail
#: model:ir.model,name:mail.model_ir_config_parameter
msgid "System Parameter"
msgstr "系统参数"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message/message.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__notification
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__notification
#, python-format
msgid "System notification"
msgstr "系统通知"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__model_id
msgid "Targeted model"
msgstr "目标模型"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__vat
#: model:ir.model.fields,field_description:mail.field_res_users__vat
msgid "Tax ID"
msgstr "税ID"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__has_recommended_activities
msgid "Technical field for UX purpose"
msgstr "用于UX目的技术字段"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__res_model_change
msgid "Technical field for UX related behaviour"
msgstr "用于UX目的技术字段"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__can_write
msgid "Technical field to hide buttons if the current user has no access."
msgstr "如果当前用户没有访问权限，则隐藏按钮。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__initial_res_model
msgid ""
"Technical field to keep track of the model at the start of editing to "
"support UX related behaviour"
msgstr "技术字段，在编辑开始时跟踪模型，以支持用户体验相关行为"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__activity_user_field_name
#: model:ir.model.fields,help:mail.field_ir_cron__activity_user_field_name
msgid "Technical name of the user on the record"
msgstr "记录上用户的技术名称"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_template_preview_action
msgid "Template Preview"
msgstr "模板预览"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__lang
msgid "Template Preview Language"
msgstr "模板预览语言"

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Template rendering for language should be called with a list of IDs."
msgstr "应使用 ID 列表调用语言的模板渲染."

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Template rendering should be called on a valid record IDs."
msgstr "应在有效的记录 ID 上调用模板渲染."

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Template rendering should be called only using on a list of IDs."
msgstr "模板渲染只能在 ID 列表中调用。"

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid ""
"Template rendering supports only inline_template, qweb, or qweb_view (view "
"or raw)."
msgstr "模版渲染仅支持 inline_template, qweb, 或 qweb_view (view 或 raw)."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_tree
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Templates"
msgstr "模板"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Text file"
msgstr "文本文件"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The"
msgstr "此"

#. module: mail
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "The 'Due Date In' value can't be negative."
msgstr "'期望到期日'不能是以前的日期。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/rtc_call_viewer/rtc_call_viewer.js:0
#, python-format
msgid "The FullScreen mode was denied by the browser"
msgstr "浏览器不允许使用全屏模式"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_partner__vat
#: model:ir.model.fields,help:mail.field_res_users__vat
msgid ""
"The Tax Identification Number. Complete it if the contact is subjected to "
"government taxes. Used in some legal statements."
msgstr "税号。 如果联系人需缴纳税款，请填写。 用于某些法律声明。"

#. module: mail
#: code:addons/mail/models/ir_attachment.py:0
#, python-format
msgid ""
"The attachment %s does not exist or you do not have the rights to access it."
msgstr "附件%s不存在或者您没有权限访问它。"

#. module: mail
#: code:addons/mail/models/ir_attachment.py:0
#, python-format
msgid "The attachment %s does not exist."
msgstr "附件%s不存在。"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_channel_uuid_unique
msgid "The channel UUID must be unique"
msgstr "频道 UUID 必须是唯一的"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"The e-mail alias %(matching_alias_name)s is already linked with "
"%(alias_model_name)s. Choose another alias or change it on the linked model."
msgstr ""
"e-mail 别名 %(matching_alias_name)s已经被链接至 %(alias_model_name)s. "
"请选择其他别名或者修改它链接的模型."

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"The e-mail alias %(matching_alias_name)s is already used as "
"%(alias_duplicate)s alias. Please choose another alias."
msgstr ""
"e-mail 别名 %(matching_alias_name)s 已经被 %(alias_duplicate)s用作别名. 请选择其他别名."

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"The e-mail alias %(matching_alias_name)s is already used by the "
"%(document_name)s %(model_name)s. Choose another alias or change it on the "
"other document."
msgstr ""
"e-mail 别名 %(matching_alias_name)s已经被 %(document_name)s %(model_name)s使用. "
"请使用其他别名或者在其他文档上修改它."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The email sent to"
msgstr "EMail发到"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_shortcode__substitution
msgid "The escaped html code replacing the shortcut"
msgstr "html 转义码替换快捷方式"

#. module: mail
#: code:addons/mail/models/mail_composer_mixin.py:0
#, python-format
msgid "The field %s does not exist on the model %s"
msgstr "字段 %s 不存在模型%s中"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_partner__user_id
#: model:ir.model.fields,help:mail.field_res_users__user_id
msgid "The internal user in charge of this contact."
msgstr "负责此联系人的内部用户。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_model_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"相应于这个别名对应的模型(Odoo单据种类)。任何一封不属于对某个已存在的记录的到来邮件，将导致此模块中新记录的创建(例如，一个新的项目任务)。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_name
#: model:ir.model.fields,help:mail.field_mail_channel__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr "邮件别名的名称，例如，如果您要收取 <<EMAIL>> 的EMail,别名就要设为：\"jobs\""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_user_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""
"基于此别名接收到的邮件，创建记录的所有人。若此字段没有设置，系统将试图根据发送者(From)地址来查找正确的所有者，如果对于那个地址没有系统用户被发现，将使用系统管理员账户。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__scheduled_date
msgid "The queue manager will send the email after the date"
msgstr "队列管理器将在日期之后发送电子邮件"

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid ""
"The requested operation cannot be completed due to security restrictions. Please contact your system administrator.\n"
"\n"
"(Document type: %s, Operation: %s)"
msgstr ""
"由于安全限制，请求的操作无法完成。请联系您的系统管理员。\n"
"\n"
"(单据类型: %s, 操作: %s)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_shortcode__source
msgid "The shortcut which must be replaced in the Chat Messages"
msgstr "在聊天消息中，必须替换掉的快捷方式"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/follower/follower.js:0
#, python-format
msgid "The subscription preferences were successfully applied."
msgstr "已成功应用订阅偏好."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__model_id
#: model:ir.model.fields,help:mail.field_mail_template_preview__model_id
msgid "The type of document this template can be used with"
msgstr "该模板可使用的单据类型"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "There are no messages in this conversation."
msgstr "此对话中没有消息。"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_channel_rtc_session_channel_partner_unique
msgid "There can only be one rtc session per channel partner"
msgstr "每个频道业务合作伙伴只能有一个 rtc 会话"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "This"
msgstr "此"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity_view.xml:0
#, python-format
msgid "This action will send an email."
msgstr "此操作将发出EMail。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_form_inherit_mail
msgid "This email is blacklisted for mass mailings. Click to unblacklist."
msgstr "此电子邮件已列入群发邮件黑名单。单击以取消黑名单。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__email
msgid "This field is case insensitive."
msgstr "此字段不区分大小写。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__email_normalized
#: model:ir.model.fields,help:mail.field_res_partner__email_normalized
#: model:ir.model.fields,help:mail.field_res_users__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can"
" contain more than strictly an email address."
msgstr "这个字段用来在邮件地址上搜索，因为初始的邮件字段会包含严格来说至少两个邮件地址。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__public
msgid ""
"This group is visible by non members. Invisible groups can add members "
"through the invite button."
msgstr "这个小组是由非成员可见。非可见小组可以通过邀请按钮添加成员。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "This is their first connection. Wish them luck."
msgstr "这是他们的第一次联系。祝他们好运。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete
#: model:ir.model.fields,help:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,help:mail.field_mail_template__auto_delete
msgid ""
"This option permanently removes any track of email after it's been sent, "
"including from the Technical menu in the Settings, in order to preserve "
"storage space of your Odoo database."
msgstr "此选项在发送电子邮件后会永久删除电子邮件的任何跟踪，包括从\"设置\"中的\"技术\"菜单中删除，以便保留 Odoo 数据库的存储空间。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "This record has an exception activity."
msgstr "此记录有异常活动。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "Save the record before scheduling an activity!"
msgstr ""

#. module: mail
#: code:addons/mail/models/mail_channel_partner.py:0
#, python-format
msgid "This user can not be added in this channel"
msgstr "此用户无法添加到此频道"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Thread"
msgstr "线程"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar_category_item/discuss_sidebar_category_item.xml:0
#: code:addons/mail/static/src/components/thread_needaction_preview/thread_needaction_preview.xml:0
#: code:addons/mail/static/src/components/thread_preview/thread_preview.xml:0
#, python-format
msgid "Thread Image"
msgstr "线程图像"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_layout_menu/rtc_layout_menu.xml:0
#, python-format
msgid "Tiled"
msgstr "平铺"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__timezone
msgid "Timezone"
msgstr "时区"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_to
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_to
msgid "To"
msgstr "至"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__email_to
msgid "To (Emails)"
msgstr "至（EMail）"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__recipient_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__partner_to
msgid "To (Partners)"
msgstr "至（合作伙伴）"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_todo
msgid "To Do"
msgstr "待办"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window/chat_window.xml:0
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "To:"
msgstr "至:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#: code:addons/mail/static/src/js/views/activity/activity_renderer.js:0
#: code:addons/mail/static/src/models/message/message.js:0
#: code:addons/mail/static/src/models/message/message.js:0
#: code:addons/mail/static/src/xml/systray.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__today
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__today
#, python-format
msgid "Today"
msgstr "今天"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Today Activities"
msgstr "今天的活动"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#, python-format
msgid "Today:"
msgstr "今天:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "Tomorrow"
msgstr "明天"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#, python-format
msgid "Tomorrow:"
msgstr "明天:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Topics discussed in this group..."
msgstr "在该群组中讨论的话题 ..."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,help:mail.field_mail_message__tracking_value_ids
msgid ""
"Tracked values are stored in a separate model. This field allow to "
"reconstruct the tracking and to generate statistics on the model."
msgstr "在单独的模型中存储跟踪值。该字段允许重建跟踪，以便在模型中生成统计数据。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Tracking"
msgstr "追溯"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_tree
msgid "Tracking Value"
msgstr "追踪值"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_tracking_value
#: model:ir.ui.menu,name:mail.menu_mail_tracking_value
msgid "Tracking Values"
msgstr "追踪值"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__tracking_sequence
msgid "Tracking field sequence"
msgstr "跟踪字段次序"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__tracking_value_ids
msgid "Tracking values"
msgstr "追踪值"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__triggered_next_type_id
msgid "Trigger"
msgstr "触发器"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__chaining_type__trigger
msgid "Trigger Next Activity"
msgstr "触发下一个活动"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Turn camera on"
msgstr "打开相机"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__twilio_account_token
msgid "Twilio Account Auth Token"
msgstr "Twilio 帐户身份验证令牌"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__twilio_account_sid
msgid "Twilio Account SID"
msgstr "Twilio 帐户 SID"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__message_type
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__server_type
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_type
#: model:ir.model.fields,field_description:mail.field_mail_message__message_type
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Type"
msgstr "类型"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_from
msgid "Type of delay"
msgstr "延迟类别"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__state
#: model:ir.model.fields,help:mail.field_ir_cron__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Execute Python Code': a block of python code that will be executed\n"
"- 'Create': create a new record with new values\n"
"- 'Update a Record': update the values of a record\n"
"- 'Execute several actions': define an action that triggers several other server actions\n"
"- 'Send Email': automatically send an email (Discuss)\n"
"- 'Add Followers': add followers to a record (Discuss)\n"
"- 'Create Next Activity': create an activity (Discuss)"
msgstr ""
"服务器操作的类型。 提供以下值：\n"
"-“执行Python代码”：将要执行的python代码块\n"
"-'创建'：使用新值创建新记录\n"
"-“更新记录”：更新记录的值\n"
"-“执行多个动作”：定义一个触发其他几个服务器动作的动作\n"
"-“发送电子邮件”：自动发送电子邮件（讨论）\n"
"-“添加关注者”：将关注者添加到记录中（讨论）\n"
"-“创建下一个活动”：创建活动（讨论）"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_exception_decoration
#: model:ir.model.fields,help:mail.field_res_partner__activity_exception_decoration
#: model:ir.model.fields,help:mail.field_res_users__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "记录的异常活动类型。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "Type the name of a person"
msgstr "请输入需要邀请的人员名字"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__uri
msgid "URI"
msgstr "URI"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__uuid
msgid "UUID"
msgstr "UUID"

#. module: mail
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid "Unable to connect to SMTP Server"
msgstr "无法连接到 SMTP 服务器"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Unable to log message, please configure the sender's email address."
msgstr "无法记录消息，请配置发件人的EMail地址。"

#. module: mail
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "Unable to post message, please configure the sender's email address."
msgstr "无法发布邮件，请配置发件人的EMail地址。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Unblacklist"
msgstr "黑名单"

#. module: mail
#: code:addons/mail/models/mail_blacklist.py:0
#, python-format
msgid "Unblacklisting Reason: %s"
msgstr "黑名单取消原因: %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Undeafen"
msgstr "打开扬声器"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follow_button/follow_button.xml:0
#, python-format
msgid "Unfollow"
msgstr "取消关注"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_alias_alias_unique
msgid ""
"Unfortunately this email alias is already used, please choose a unique one"
msgstr "可惜 这个EMail别名已经被使用，请选择一个不一样的"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_unit
msgid "Unit of delay"
msgstr "延迟单位"

#. module: mail
#: code:addons/mail/models/mail_notification.py:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__unknown
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__unknown
#, python-format
msgid "Unknown error"
msgstr "未知错误"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Unmute"
msgstr "打开麦克风"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar_category_item/discuss_sidebar_category_item.xml:0
#, python-format
msgid "Unpin Conversation"
msgstr "取消固定通话"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_unread
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_unread
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_unread
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_unread
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_unread
#: model:ir.model.fields,field_description:mail.field_res_partner__message_unread
#: model:ir.model.fields,field_description:mail.field_res_users__message_unread
msgid "Unread Messages"
msgstr "未读消息"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_unread_counter
msgid "Unread Messages Counter"
msgstr "未读消息数"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Unread messages"
msgstr "未读消息"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Unstar all"
msgstr "取消所有星标"

#. module: mail
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "Unsupported report type %s found."
msgstr "发现不支持的报告类型 %s。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__upload_file
#: model:mail.activity.type,name:mail.mail_activity_data_upload_document
#, python-format
msgid "Upload Document"
msgstr "上传文件"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Upload file"
msgstr "上传文件"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#, python-format
msgid "Uploaded"
msgstr "已上传"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#: code:addons/mail/static/src/components/attachment_image/attachment_image.xml:0
#, python-format
msgid "Uploading"
msgstr "上传中"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__activity_user_type
#: model:ir.model.fields,help:mail.field_ir_cron__activity_user_type
msgid ""
"Use 'Specific User' to always assign the same user on the next activity. Use"
" 'Generic User From Record' to specify the field name of the user to choose "
"on the record."
msgstr "使用 '特定用户' 总是在下一个活动中分配相同的用户。使用 '记录中的通用用户' 指定在记录上选择的用户的字段名."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Use Push-to-talk"
msgstr "使用一键通话"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__use_twilio_rtc_servers
msgid "Use Twilio ICE servers"
msgstr "使用 Twilio ICE 服务器"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__use_active_domain
msgid "Use active domain"
msgstr "使用启用域名"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__template_id
msgid "Use template"
msgstr "使用模版"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__use_push_to_talk
msgid "Use the push to talk feature"
msgstr "使用一键通话功能"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_tracking_value__currency_id
msgid "Used to display the currency when tracking monetary values"
msgstr "用于在追踪货币价值时显示货币"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__sequence
msgid "Used to order subtypes."
msgstr "用于排序子类型。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__user_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "User"
msgstr "用户"

#. module: mail
#: model:ir.model,name:mail.model_bus_presence
msgid "User Presence"
msgstr "用户上线"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__user_setting_id
msgid "User Setting"
msgstr "用户设置"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_guest_action
#: model:ir.actions.act_window,name:mail.res_users_settings_action
#: model:ir.model,name:mail.model_res_users_settings
#: model:ir.ui.menu,name:mail.res_users_settings_menu
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_tree
msgid "User Settings"
msgstr "用户设置"

#. module: mail
#: model:ir.model,name:mail.model_res_users_settings_volumes
msgid "User Settings Volumes"
msgstr "用户设置数量"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__user_notification
msgid "User Specific Notification"
msgstr "用户特定通知"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_field_name
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_field_name
msgid "User field name"
msgstr "用户字段名字"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "User is a bot"
msgstr "用户是机器人"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "User is idle"
msgstr "用户空闲"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "User is offline"
msgstr "用户离线"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "User is online"
msgstr "用户上线"

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "User:"
msgstr "用户:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__username
msgid "Username"
msgstr "用户名"

#. module: mail
#: model:ir.model,name:mail.model_res_users
msgid "Users"
msgstr "用户"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "Users in this channel: %(members)s %(dots)s and you."
msgstr "此频道中的用户: %(members)s %(dots)s和您."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__restrict_template_rendering
msgid ""
"Users will still be able to render templates.\n"
"However only Mail Template Editors will be able to create new dynamic templates or modify existing ones."
msgstr ""
"用户仍然可以渲染模板.\n"
"但是只有邮件模板编辑器才能创建新的动态模板或修改现有模板."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid ""
"Using your own email server is required to send/receive emails in Community "
"and Enterprise versions. Online users already benefit from a ready-to-use "
"email server (@mycompany.odoo.com)."
msgstr ""
"在社区和企业版本中，发送/接收电子邮件需要使用自己的电子邮件服务器。上线用户已受益于随时可用的电子邮件服务器 "
"(@mycompany.odoo.com)。"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Value for `mail.catchall.domain.allowed` cannot be validated.\n"
"It should be a comma separated list of domains e.g. example.com,example.org."
msgstr ""
"`mail.catchall.domain.allowed` 的值无法验证。\n"
"应该为一个以逗号分隔的域名列表，例如：example.com,example.org。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Video"
msgstr "视频"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#: model:ir.model,name:mail.model_ir_ui_view
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
#, python-format
msgid "View"
msgstr "视图"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "View %s"
msgstr "视图 %s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:mail.field_ir_ui_view__type
msgid "View Type"
msgstr "视图类型"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_image/attachment_image.xml:0
#, python-format
msgid "View image"
msgstr "查看图像"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar_category/discuss_sidebar_category.xml:0
#, python-format
msgid "View or join channels"
msgstr "查看或加入频道"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Viewer"
msgstr "观众"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Voice"
msgstr "语音"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__volume
msgid "Volume"
msgstr "体积"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Volume per partner"
msgstr "每个业务合作伙伴的数量"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__volume_settings_ids
msgid "Volumes of other partners"
msgstr "其他业务合作伙伴的数量"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "Warning"
msgstr "警告"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__weeks
msgid "Weeks"
msgstr "周"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/welcome_view/welcome_view.xml:0
#, python-format
msgid "What's your name?"
msgstr "您叫什么名字？"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__sub_model_object_field
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__sub_model_object_field
#: model:ir.model.fields,help:mail.field_mail_render_mixin__sub_model_object_field
#: model:ir.model.fields,help:mail.field_mail_template__sub_model_object_field
msgid ""
"When a relationship field is selected as first field, this field lets you "
"select the target field within the destination document model (sub-model)."
msgstr "如果首先选择了一个关系型字段，这个字段可用于选择目标单据模型的目标字段。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__sub_object
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__sub_object
#: model:ir.model.fields,help:mail.field_mail_render_mixin__sub_object
#: model:ir.model.fields,help:mail.field_mail_template__sub_object
msgid ""
"When a relationship field is selected as first field, this field shows the "
"document model the relationship goes to."
msgstr "如果关系型字段被选为第一个字段，这个字段显示这个关系指向的单据模型。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__is_log
msgid "Whether the message is an internal note (comment mode only)"
msgstr "该消息是否是一个内部备注(注释模式)"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model__is_mail_activity
msgid "Whether this model supports activities."
msgstr "此模型是否支持活动。"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model__is_mail_blacklist
msgid "Whether this model supports blacklist."
msgstr "该模型是否支持黑名单。"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model__is_mail_thread
msgid "Whether this model supports messages and notifications."
msgstr "该模型是否支持消息和通知。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Who can follow the group's activities?"
msgstr "谁能关注群组的活动?"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity_mark_done_popover/activity_mark_done_popover.xml:0
#: code:addons/mail/static/src/xml/activity.xml:0
#, python-format
msgid "Write Feedback"
msgstr "反馈"

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Wrong operation name (%s)"
msgstr "错误的操作名称 (%s)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "YYYY-MM-DD HH:MM:SS"
msgstr "YYYY-MM-DD HH:MM:SS"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#: code:addons/mail/static/src/models/message/message.js:0
#, python-format
msgid "Yesterday"
msgstr "昨天"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#, python-format
msgid "Yesterday:"
msgstr "昨天:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/discuss_sidebar_category_item/discuss_sidebar_category_item.js:0
#, python-format
msgid ""
"You are about to leave this group conversation and will no longer have "
"access to it unless you are invited again. Are you sure you want to "
"continue?"
msgstr "您即将离开此群组对话，除非再次受到邀请，否则将无法再访问它。 您确定您要继续吗？"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "You are alone in this channel."
msgstr "您是此频道的唯一用户."

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "You are in a private conversation with <b>@%s</b>."
msgstr "您在与<b>@%s</b> 的私有对话中."

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "You are in channel <b>#%s</b>."
msgstr "您在频道 <b>#%s</b>."

#. module: mail
#: code:addons/mail/controllers/discuss.py:0
#, python-format
msgid "You are not allowed to upload an attachment here."
msgstr "您无权在此处上传附件。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/discuss_sidebar_category_item/discuss_sidebar_category_item.js:0
#, python-format
msgid ""
"You are the administrator of this channel. Are you sure you want to leave?"
msgstr "您是该频道的管理员。您确定要离开吗？"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid ""
"You can mark any message as 'starred', and it shows up in this mailbox."
msgstr "您可以给任何消息加星号，然后它会出现在邮箱里。"

#. module: mail
#: code:addons/mail/models/mail_channel_partner.py:0
#, python-format
msgid "You can not write on %(field_name)s."
msgstr "您不能修改 %(field_name)s."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/user/user.js:0
#, python-format
msgid "You can only chat with existing users."
msgstr "您只能与现有用户聊天。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/partner/partner.js:0
#, python-format
msgid "You can only chat with partners that have a dedicated user."
msgstr "您只能与拥有专属用户的业务合作伙伴聊天。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging/messaging.js:0
#, python-format
msgid "You can only open the profile of existing channels."
msgstr "您只能打开现有频道的配置文件."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/user/user.js:0
#, python-format
msgid "You can only open the profile of existing users."
msgstr "您只能打开现有用户的个人资料。"

#. module: mail
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid ""
"You cannot create a new user from here.\n"
" To create new user please go to configuration panel."
msgstr ""
"您不能在这里创建新的用户。\n"
" 如需创建新用户，请转到配置面板。"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"You cannot delete those groups, as the Whole Company group is required by "
"other modules."
msgstr "您不能删除这些群组，因为整个公司群组被其它模块引用。"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"You cannot use anything else than unaccented latin characters in the alias "
"address (%s)."
msgstr "您不能在别名地址中使用除不带重音的拉丁字符之外的任何内容 (%s)."

#. module: mail
#: code:addons/mail/models/mail_thread_blacklist.py:0
#, python-format
msgid ""
"You do not have the access right to unblacklist emails. Please contact your "
"administrator."
msgstr "<br>您没有将电子邮件列入黑名单的访问权限。请联系您的管理员。"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "You have been assigned to %s"
msgstr "您已被指派到 %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "You have been assigned to the"
msgstr "您被指派到"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "You have been invited to #%s"
msgstr "您已受邀参加  #%s"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__attachment_ids
msgid ""
"You may attach files to this template, to be added to all emails created "
"from this template"
msgstr "您可以对这个模版附加文件，可以附加到所有基于此模版生成的邮件中"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "You unpinned your conversation with %s."
msgstr "您取消了与 %s的对话."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "You unsubscribed from %s."
msgstr "您退出了\"%s\" 会议对话。"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/welcome_view/welcome_view.xml:0
#, python-format
msgid "You've been invited to a chat!"
msgstr "您已被邀请参加聊天!"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/welcome_view/welcome_view.xml:0
#, python-format
msgid "You've been invited to a meeting!"
msgstr "您已被邀请参加一个会议！"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_author_prefix/message_author_prefix.xml:0
#, python-format
msgid "You:"
msgstr "您："

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "Your"
msgstr "您的"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/media_preview/media_preview.xml:0
#, python-format
msgid "Your browser does not support videoconference"
msgstr "您的浏览器不支持视频会议"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/rtc/rtc.js:0
#, python-format
msgid "Your browser does not support voice activation"
msgstr "您的浏览器不支持语音通话"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/thread/thread.js:0
#, python-format
msgid "Your browser does not support webRTC."
msgstr "您的浏览器不支持 webRTC."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/welcome_view/welcome_view.xml:0
#, python-format
msgid "Your name"
msgstr "您的名字"

#. module: mail
#: code:addons/mail/controllers/home.py:0
#, python-format
msgid ""
"Your password is the default (admin)! If this system is exposed to untrusted"
" users it is important to change it immediately for security reasons. I will"
" keep nagging you about it!"
msgstr "您的密码是默认密码（admin）！ 如果此系统暴露给不受信任的用户，则出于安全原因立即更改它很重要。 我会继续唠叨您！"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Zoom In"
msgstr "放大"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Zoom In (+)"
msgstr "放大 (+)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Zoom Out"
msgstr "缩小"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Zoom Out (-)"
msgstr "缩小 (-)"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "addresses linked to registered partners"
msgstr "链接到注册合作伙伴的地址"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_from__current_date
msgid "after completion date"
msgstr "完成日期之后"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_from__previous_activity
msgid "after previous activity deadline"
msgstr "上次活动结束后"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "alias %(name)s: %(error)s"
msgstr "别名 %(name)s: %(error)s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "assigned you an activity"
msgstr "指派给您一个活动"

#. module: mail
#: model:mail.channel,name:mail.channel_2
msgid "board-meetings"
msgstr "董事会－会议"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "bounce"
msgstr "bounce"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "by"
msgstr "单位"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "cancel"
msgstr "取消"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid ""
"cannot be processed. This address\n"
"    is used to collect replies and should not be used to directly contact"
msgstr ""
"无法处理。此地址\n"
"        用于收集答复，不用于直接联系"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "catchall"
msgstr "catchall"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "channel"
msgstr "频道"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__days
msgid "days"
msgstr "天数"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#, python-format
msgid "deaf"
msgstr "聋"

#. module: mail
#. openerp-web
#: code:addons/mail/models/mail_thread.py:0
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "document"
msgstr "单据"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "done"
msgstr "完成"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. \"mycompany.com\""
msgstr "例如 \"mycompany.com\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. 65ea4f9e948b693N5156F350256bd152"
msgstr "例如 65ea4f9e948b693N5156F350256bd152"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. ACd5543a0b450ar4c7t95f1b6e8a39t543"
msgstr "例如 ACd5543a0b450ar4c7t95f1b6e8a39t543"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "e.g. Calendar: Reminder"
msgstr "例如日历:提醒"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "e.g. Discuss proposal"
msgstr "例如.讨论提案"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "e.g. Schedule a meeting"
msgstr "例如安排会议"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "e.g. Users"
msgstr "例如用户"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "e.g. support"
msgstr "例如支持"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "e.g. true.true..f"
msgstr "例如 true.true..f"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "escape to"
msgstr "按Esc键取消 或 点击"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#, python-format
msgid "for %s"
msgstr "对于 %s"

#. module: mail
#: model:mail.channel,name:mail.channel_all_employees
msgid "general"
msgstr "通用"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "has been created from:"
msgstr "已经创建自:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "has been modified from:"
msgstr "已经被修改自:"

#. module: mail
#: code:addons/mail/models/models.py:0
#, python-format
msgid "incorrectly configured alias"
msgstr "不正确配置的别名"

#. module: mail
#: code:addons/mail/models/models.py:0
#, python-format
msgid "incorrectly configured alias (unknown reference record)"
msgstr "错误地别名配置（未知参考记录）"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#, python-format
msgid "live"
msgstr "在线"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "mail_blacklist_removal"
msgstr "mail_blacklist_removal"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"message_post does not support model and res_id parameters anymore. Please "
"call message_post on record."
msgstr "message_post 不再支持模型和 res_id 参数. 请在记录上调用 message_post ."

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"message_post does not support subtype parameter anymore. Please give a valid"
" subtype_id or subtype_xmlid value instead."
msgstr "message_post 不再支持自类型参数.请改为提供有效 subtype_id or或者subtype_xmlid 值."

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "message_post partner_ids and must be integer list, not commands."
msgstr "message_post partner_ids 必须是整数列表，不能是命令."

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "model %s does not accept document creation"
msgstr "模型 %s 不接受单据创建"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__months
msgid "months"
msgstr "月"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "ms"
msgstr "秒"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#, python-format
msgid "muted"
msgstr "静音"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/utils.js:0
#, python-format
msgid "now"
msgstr "现在"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "on"
msgstr "on"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "on:"
msgstr "于:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/mail_template/mail_template.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "or"
msgstr "或"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#, python-format
msgid "other members."
msgstr "其他成员."

#. module: mail
#: model:mail.channel,name:mail.channel_3
msgid "rd"
msgstr "研发"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.js:0
#, python-format
msgid "read less"
msgstr "阅读更少"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.js:0
#, python-format
msgid "read more"
msgstr "阅读全文"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "record:"
msgstr "记录："

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"reply to missing document (%(model)s,%(thread)s), fall back on document "
"creation"
msgstr "回复丢失的文档 (%(model)s,%(thread)s), 退回到文档创建"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"reply to model %s that does not accept document update, fall back on "
"document creation"
msgstr "回复模型 %s 不接受单据更新单据创建"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "restricted to channel members"
msgstr "仅限于频道成员"

#. module: mail
#: code:addons/mail/models/models.py:0
#, python-format
msgid "restricted to followers"
msgstr "仅限于关注者"

#. module: mail
#: code:addons/mail/models/models.py:0
#, python-format
msgid "restricted to known authors"
msgstr "仅限于已知的作者"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "results out of"
msgstr "结果出自于"

#. module: mail
#: model:mail.channel,name:mail.channel_1
msgid "sales"
msgstr "销售"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "save"
msgstr "保存"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "some specific addresses"
msgstr "一些具体地址"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_ice_server__server_type__stun
msgid "stun:"
msgstr "stun:"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "target model unspecified"
msgstr "目标模型未指定"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "team."
msgstr "团队."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "this document"
msgstr "此单据"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "to close for"
msgstr "关闭于"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "toggle push-to-talk"
msgstr "切换一键通话"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_ice_server__server_type__turn
msgid "turn:"
msgstr "turn:"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "unknown error"
msgstr "未知错误"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "unknown target model %s"
msgstr "位置目标模型 %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
msgid "using"
msgstr "使用中"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/many2one_avatar_user.xml:0
#, python-format
msgid "value"
msgstr "值"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__weeks
msgid "weeks"
msgstr "周"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "{{ object.partner_id.lang }}"
msgstr "{{ object.partner_id.lang }}"
