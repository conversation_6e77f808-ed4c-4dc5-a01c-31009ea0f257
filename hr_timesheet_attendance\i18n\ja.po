# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_timesheet_attendance
# 
# Translators:
# <PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report__company_id
msgid "Company"
msgstr "会社"

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report__date
#: model_terms:ir.ui.view,arch_db:hr_timesheet_attendance.view_hr_timesheet_attendance_report_search
msgid "Date"
msgstr "日付"

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report__display_name
msgid "Display Name"
msgstr "表示名"

#. module: hr_timesheet_attendance
#: model_terms:ir.ui.view,arch_db:hr_timesheet_attendance.view_hr_timesheet_attendance_report_search
msgid "Employee"
msgstr "従業員"

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report__id
msgid "ID"
msgstr "ID"

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report____last_update
msgid "Last Modified on"
msgstr "最終更新日"

#. module: hr_timesheet_attendance
#: model_terms:ir.actions.act_window,help:hr_timesheet_attendance.action_hr_timesheet_attendance_report
msgid "No data yet!"
msgstr "まだデータはありません！"

#. module: hr_timesheet_attendance
#: model_terms:ir.ui.view,arch_db:hr_timesheet_attendance.hr_timesheet_attendance_report_view_tree
msgid "Sum of Total Attendance"
msgstr ""

#. module: hr_timesheet_attendance
#: model_terms:ir.ui.view,arch_db:hr_timesheet_attendance.hr_timesheet_attendance_report_view_tree
msgid "Sum of Total Difference"
msgstr ""

#. module: hr_timesheet_attendance
#: model_terms:ir.ui.view,arch_db:hr_timesheet_attendance.hr_timesheet_attendance_report_view_tree
msgid "Sum of Total Timesheet"
msgstr ""

#. module: hr_timesheet_attendance
#: model:ir.actions.act_window,name:hr_timesheet_attendance.action_hr_timesheet_attendance_report
#: model:ir.ui.menu,name:hr_timesheet_attendance.menu_hr_timesheet_attendance_report
msgid "Timesheet / Attendance"
msgstr "タイムシート / 出退勤"

#. module: hr_timesheet_attendance
#: model_terms:ir.ui.view,arch_db:hr_timesheet_attendance.hr_timesheet_attendance_report_view_graph
#: model_terms:ir.ui.view,arch_db:hr_timesheet_attendance.hr_timesheet_attendance_report_view_tree
#: model_terms:ir.ui.view,arch_db:hr_timesheet_attendance.view_hr_timesheet_attendance_report_pivot
#: model_terms:ir.ui.view,arch_db:hr_timesheet_attendance.view_hr_timesheet_attendance_report_search
msgid "Timesheet Attendance"
msgstr "タイムシート勤怠"

#. module: hr_timesheet_attendance
#: model:ir.model,name:hr_timesheet_attendance.model_hr_timesheet_attendance_report
msgid "Timesheet Attendance Report"
msgstr "タイムシート出席レポート"

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report__total_attendance
msgid "Total Attendance"
msgstr "出勤日の合計"

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report__total_difference
msgid "Total Difference"
msgstr "差分の合計"

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report__total_timesheet
msgid "Total Timesheet"
msgstr "全ての勤務表"

#. module: hr_timesheet_attendance
#: model:ir.model.fields,field_description:hr_timesheet_attendance.field_hr_timesheet_attendance_report__user_id
msgid "User"
msgstr "ユーザ"
