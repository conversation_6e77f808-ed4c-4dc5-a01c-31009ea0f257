# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_holidays
# 
# Translators:
# <PERSON>, 2021
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:53+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_type.py:0
#, python-format
msgid " days"
msgstr "días"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_type.py:0
#, python-format
msgid " hours"
msgstr "horas"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important &gt;&lt;/td&gt;"
msgstr "!important &gt;&lt;/td&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important /&gt;"
msgstr "!important /&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important/&gt;"
msgstr "!important/&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important; font-size: 10px\" &gt;"
msgstr "!important; font-size: 10px\" &gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important; font-size: 8px; min-width: 18px\"&gt;"
msgstr "!important; font-size: 8px; min-width: 18px\"&gt;"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%(leave_type)s are only valid between %(start)s and %(end)s"
msgstr "%(leave_type)s solo es válido entre %(start)s y %(end)s"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%(leave_type)s are only valid starting from %(date)s"
msgstr "%(leave_type)s solo es válido desde %(date)s"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%(leave_type)s are only valid until %(date)s"
msgstr "%(leave_type)s solo es válido hasta %(date)s"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%(leave_type)s: %(duration).2f days (%(start)s)"
msgstr "%(leave_type)s: %(duration).2f días (%(start)s)"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%(person)s on %(leave_type)s: %(duration).2f days (%(start)s)"
msgstr "%(person)s en %(leave_type)s: %(duration).2f días (%(start)s)"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%(person)s on %(leave_type)s: %(duration).2f hours on %(date)s"
msgstr "%(person)s en %(leave_type)s: %(duration).2f horas en %(date)s"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_type.py:0
#, python-format
msgid "%g remaining out of %g"
msgstr "%g restante de %g"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_accrual_plan.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (copia)"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid "%s (from %s to %s)"
msgstr "%s (de %s a %s)"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid "%s (from %s to No Limit)"
msgstr "%s (de %s a Sin límite)"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%s : %.2f days"
msgstr "%s : %.2f días"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%s : %.2f hours"
msgstr "%s : %.2f horas"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%s on Time Off : %.2f day(s)"
msgstr "%s de tiempo personal: %.2f día(s)"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%s on Time Off : %.2f hour(s)"
msgstr "%s de tiempo personal : %.2f hora(s)"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "%s: Time Off"
msgstr "%s: Tiempo personal"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&gt;"
msgstr "&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;/td&gt;"
msgstr "&lt;/td&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;/th&gt;"
msgstr "&lt;/th&gt;"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid ""
"&lt;td class=\"text-center oe_leftfit oe_rightfit\" style=\"background-"
"color:"
msgstr ""
"&lt;td class=\"text-center oe_leftfit oe_rightfit\" style=\"background-"
"color:"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;td style=background-color:"
msgstr "&lt;td style=background-color:"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;th class=\"text-center\" colspan="
msgstr "&lt;th class=\"text-center\" colspan="

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "(based on worked time)"
msgstr "(según el tiempo trabajado)"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__10
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__10
msgid "10:00 AM"
msgstr "10:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__22
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__22
msgid "10:00 PM"
msgstr "10:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__10_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__10_5
msgid "10:30 AM"
msgstr "10:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__22_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__22_5
msgid "10:30 PM"
msgstr "10:30 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__11
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__11
msgid "11:00 AM"
msgstr "11:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__23
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__23
msgid "11:00 PM"
msgstr "11:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__11_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__11_5
msgid "11:30 AM"
msgstr "11:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__23_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__23_5
msgid "11:30 PM"
msgstr "11:30 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__0
msgid "12:00 AM"
msgstr "12:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__12
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__12
msgid "12:00 PM"
msgstr "12:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__0_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__0_5
msgid "12:30 AM"
msgstr "12:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__12_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__12_5
msgid "12:30 PM"
msgstr "12:30 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__1
msgid "1:00 AM"
msgstr "1:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__13
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__13
msgid "1:00 PM"
msgstr "1:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__1_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__1_5
msgid "1:30 AM"
msgstr "1:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__13_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__13_5
msgid "1:30 PM"
msgstr "1:30 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__2
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__2
msgid "2:00 AM"
msgstr "2:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__14
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__14
msgid "2:00 PM"
msgstr "2:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__2_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__2_5
msgid "2:30 AM"
msgstr "2:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__14_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__14_5
msgid "2:30 PM"
msgstr "2:30 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__3
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__3
msgid "3:00 AM"
msgstr "3:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__15
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__15
msgid "3:00 PM"
msgstr "3:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__3_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__3_5
msgid "3:30 AM"
msgstr "3:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__15_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__15_5
msgid "3:30 PM"
msgstr "3:30 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__4
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__4
msgid "4:00 AM"
msgstr "4:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__16
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__16
msgid "4:00 PM"
msgstr "4:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__4_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__4_5
msgid "4:30 AM"
msgstr "4:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__16_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__16_5
msgid "4:30 PM"
msgstr "4:30 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__5
msgid "5:00 AM"
msgstr "5:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__17
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__17
msgid "5:00 PM"
msgstr "5:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__5_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__5_5
msgid "5:30 AM"
msgstr "5:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__17_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__17_5
msgid "5:30 PM"
msgstr "5:30 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__6
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__6
msgid "6:00 AM"
msgstr "6:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__18
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__18
msgid "6:00 PM"
msgstr "6:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__6_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__6_5
msgid "6:30 AM"
msgstr "6:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__18_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__18_5
msgid "6:30 PM"
msgstr "6:30 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__7
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__7
msgid "7:00 AM"
msgstr "7:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__19
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__19
msgid "7:00 PM"
msgstr "7:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__7_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__7_5
msgid "7:30 AM"
msgstr "7:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__19_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__19_5
msgid "7:30 PM"
msgstr "7:30 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__8
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__8
msgid "8:00 AM"
msgstr "8:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__20
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__20
msgid "8:00 PM"
msgstr "8:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__8_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__8_5
msgid "8:30 AM"
msgstr "8:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__20_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__20_5
msgid "8:30 PM"
msgstr "8:30 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__9
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__9
msgid "9:00 AM"
msgstr "9:00 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__21
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__21
msgid "9:00 PM"
msgstr "9:00 PM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__9_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__9_5
msgid "9:30 AM"
msgstr "9:30 AM"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_from__21_5
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_hour_to__21_5
msgid "9:30 PM"
msgstr "9:30 PM"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<i class=\"fa fa-check\"/> Validate"
msgstr "<i class=\"fa fa-check\"/> Validar"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\" attrs=\"{'invisible': ['|', ('allocation_type', '=', "
"'accrual'), ('state', 'not in', ('draft', 'confirm'))]}\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Flecha\" attrs=\"{'invisible': ['|', ('allocation_type', '=', "
"'accrual'), ('state', 'not in', ('draft', 'confirm'))]}\"/>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\" attrs=\"{'invisible': [('allocation_type', '=', "
"'accrual')]}\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Flecha\" attrs=\"{'invisible': [('allocation_type', '=', "
"'accrual')]}\"/>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<i class=\"fa fa-thumbs-up\"/> Approve"
msgstr "<i class=\"fa fa-thumbs-up\"/> Aprobar"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<i class=\"fa fa-times\"/> Refuse"
msgstr "<i class=\"fa fa-times\"/> Rechazar"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid ""
"<span attrs=\"{'invisible': [('holiday_type', '!=', 'employee')]}\">\n"
"                        The employee has a different timezone than yours! Here dates and times are displayed in the employee's timezone\n"
"                    </span>\n"
"                    <span attrs=\"{'invisible': [('holiday_type', '!=', 'department')]}\">\n"
"                        The department's company has a different timezone than yours! Here dates and times are displayed in the company's timezone\n"
"                    </span>\n"
"                    <span attrs=\"{'invisible': [('holiday_type', '!=', 'company')]}\">\n"
"                        The company has a different timezone than yours! Here dates and times are displayed in the company's timezone\n"
"                    </span>\n"
"                    ("
msgstr ""
"<span attrs=\"{'invisible': [('holiday_type', '!=', 'employee')]}\">\n"
"                        ¡El empleado tiene una zona horaria diferente a la suya! Aquí se muestran las fechas y horas en la zona horaria del empleado\n"
"                    </span>\n"
"                    <span attrs=\"{'invisible': [('holiday_type', '!=', 'department')]}\">\n"
"                        ¡La empresa del departamento tiene una zona horaria diferente a la suya! Aquí se muestran las fechas y horas en la zona horaria de la empresa\n"
"                    </span>\n"
"                    <span attrs=\"{'invisible': [('holiday_type', '!=', 'company')]}\">\n"
"                        ¡La empresa tiene una zona horaria diferente a la suya! Aquí se muestran las fechas y horas en la zona horaria de la empresa\n"
"                    </span>\n"
"                    ("

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_kanban_view_employees_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_kanban_view_public_employees_kanban
msgid ""
"<span class=\"fa fa-plane text-success\" role=\"img\" aria-label=\"Present but on leave\" title=\"Present but on leave\" name=\"presence_absent_active\">\n"
"                    </span>"
msgstr ""
"<span class=\"fa fa-plane text-success\" role=\"img\" aria-label=\"Present but on leave\" title=\"Presente pero con permiso\" name=\"presence_absent_active\">\n"
"                    </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_kanban_view_employees_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_kanban_view_public_employees_kanban
msgid ""
"<span class=\"fa fa-plane text-warning\" role=\"img\" aria-label=\"To "
"define\" title=\"On Leave\"/>"
msgstr ""
"<span class=\"fa fa-plane text-warning\" role=\"img\" aria-label=\"To "
"define\" title=\"Con permiso\"/>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid ""
"<span class=\"ml8\" attrs=\"{'invisible': [('type_request_unit', '=', 'hour')]}\">Days</span>\n"
"                                <span class=\"ml8\" attrs=\"{'invisible': [('type_request_unit', '!=', 'hour')]}\">Hours</span>"
msgstr ""
"<span class=\"ml8\" attrs=\"{'invisible': [('type_request_unit', '=', 'hour')]}\">Dias</span>\n"
"                                <span class=\"ml8\" attrs=\"{'invisible': [('type_request_unit', '!=', 'hour')]}\">Horas</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Time Off\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                    Tiempo personal\n"
"                                </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_employee_public_form_view_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Off Till\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                            Ausente hasta\n"
"                        </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Time Off\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                            Tiempo personal\n"
"                        </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "<span class=\"o_stat_text\">Accruals</span>"
msgstr "<span class=\"o_stat_text\">Acumulados</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "<span class=\"o_stat_text\">Allocations</span>"
msgstr "<span class=\"o_stat_text\">Asignaciones</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "<span class=\"o_stat_text\">Time Off</span>"
msgstr "<span class=\"o_stat_text\">Tiempo personal</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid ""
"<span class=\"oe_inline\" attrs=\"{'invisible': ['|', ('request_unit_half', '=', True), ('request_unit_hours', '=', True)]}\">\n"
"                                    From\n"
"                                </span>"
msgstr ""
"<span class=\"oe_inline\" attrs=\"{'invisible': ['|', ('request_unit_half', '=', True), ('request_unit_hours', '=', True)]}\">\n"
"                                    Desde\n"
"                                </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid ""
"<span class=\"oe_inline\" attrs=\"{'invisible': ['|', ('request_unit_half', '=', True), ('request_unit_hours', '=', True)]}\">\n"
"                                    To\n"
"                                </span>"
msgstr ""
"<span class=\"oe_inline\" attrs=\"{'invisible': ['|', ('request_unit_half', '=', True), ('request_unit_hours', '=', True)]}\">\n"
"                                    Hasta\n"
"                                </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<span class=\"text-muted\">from </span>"
msgstr "<span class=\"text-muted\">de </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "<span class=\"text-muted\">to </span>"
msgstr "<span class=\"text-muted\">a </span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "<span>Days</span>"
msgstr "<span>Días</span>"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "<strong>Departments and Employees</strong>"
msgstr "<strong>Departamentos y empleados</strong>"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_action_approve_department
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_holiday_allocation_id
msgid ""
"A great way to keep track on employee’s PTOs, sick days, and approval "
"status."
msgstr ""
"Una excelente manera de realizar un seguimiento del tiempo personal pagado "
"de los empleados, los permisos por enfermedad y el estado de aprobación."

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_my
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_new_request
msgid ""
"A great way to keep track on your time off requests, sick days, and approval"
" status."
msgstr ""
"Una excelente manera de llevar el seguimiento de sus solicitudes de tiempo "
"personal, permisos por enfermedad y estados de aprobación."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "A time off cannot be duplicated."
msgstr "No se puede duplicar un tiempo personal."

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#, python-format
msgid "AVAILABLE"
msgstr "DISPONIBLE"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__show_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__show_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__show_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__show_leaves
msgid "Able to see Remaining Time Off"
msgstr "Puede ver el tiempo personal restante"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
msgid "Absence"
msgstr "Ausencia"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_department__absence_of_today
msgid "Absence by Today"
msgstr "Ausencias por día"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
msgid ""
"Absent Employee(s), Whose time off requests are either confirmed or "
"validated on today"
msgstr ""
"Empleados ausentes, cuyas solicitudes de tiempo personal se confirmaron o "
"validaron para hoy"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_employee_action_from_department
msgid "Absent Employees"
msgstr "Empleados ausentes"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__is_absent
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__is_absent
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__is_absent
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__is_absent
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_employee_view_search
msgid "Absent Today"
msgstr "Ausente hoy"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__allocation_type__accrual
msgid "Accrual Allocation"
msgstr "Asignación acumulada"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Accrual Allocations"
msgstr "Asignaciones acumuladas"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "Accrual Level"
msgstr "Nivel de acumulación"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_accrual_plan
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__accrual_plan_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__accrual_plan_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Accrual Plan"
msgstr "Plan de acumulación"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_accrual_level
msgid "Accrual Plan Level"
msgstr "Nivel de plan de acumulación"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_accrual_plan.py:0
#, python-format
msgid "Accrual Plan's Employees"
msgstr "Plan de acumulación de empleados"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.open_view_accrual_plans
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_accrual_menu_configuration
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_tree
msgid "Accrual Plans"
msgstr "Planes de acumulación"

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.hr_leave_allocation_cron_accrual_ir_actions_server
#: model:ir.cron,cron_name:hr_holidays.hr_leave_allocation_cron_accrual
#: model:ir.cron,name:hr_holidays.hr_leave_allocation_cron_accrual
msgid "Accrual Time Off: Updates the number of time off"
msgstr "Tiempo personal acumulado: actualiza la cantidad de tiempo libre"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__accruals_ids
msgid "Accruals"
msgstr "Acumulaciones"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__accrual_count
msgid "Accruals count"
msgstr "Número de acumulaciones"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_needaction
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_needaction
msgid "Action Needed"
msgstr "Acción requerida"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__active_employee
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__active
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__active_employee
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__active_employee
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__active
msgid "Active"
msgstr "Activo"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Active Allocations"
msgstr "Asignaciones activas"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__active_employee
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Active Employee"
msgstr "Empleado activo"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Active Time Off"
msgstr "Tiempo personal activo"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Active Types"
msgstr "Tipos activos"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_ids
msgid "Activities"
msgstr "Actividades"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_exception_decoration
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoración de actividad de excepción"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_state
msgid "Activity State"
msgstr "Estado de la actividad"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_type_icon
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icono de tipo de actividad"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.mail_activity_type_action_config_hr_holidays
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_menu_config_activity_type
msgid "Activity Types"
msgstr "Tipos de actividad"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Add a new level"
msgstr "Agregar un nuevo nivel"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid "Add a reason..."
msgstr "Agregar una razón..."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__added_value_type
msgid "Added Value Type"
msgstr "Agregar tipo de valor"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Adds"
msgstr "Agrega"

#. module: hr_holidays
#: model:res.groups,name:hr_holidays.group_hr_holidays_manager
msgid "Administrator"
msgstr "Administrador"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__transition_mode__end_of_accrual
msgid "After this accrual's period"
msgstr "Después de este periodo de acumulación"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_date_from_period__pm
msgid "Afternoon"
msgstr "Tarde"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_allocation_action_all
msgid "All Allocations"
msgstr "Todas las asignaciones"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.action_hr_holidays_dashboard
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_action_approve_department
msgid "All Time Off"
msgstr "Todo el tiempo personal"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__duration_display
msgid "Allocated (Days/Hours)"
msgstr "Asignado (días/horas)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__holiday_allocation_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__allocation_ids
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__leave_type__allocation
#: model:mail.message.subtype,name:hr_holidays.mt_leave_allocation
msgid "Allocation"
msgstr "Asignación"

#. module: hr_holidays
#: model:mail.activity.type,name:hr_holidays.mail_act_leave_allocation_approval
msgid "Allocation Approval"
msgstr "Aprobación de asignación"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__private_name
msgid "Allocation Description"
msgstr "Descripción de la asignación"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__allocation_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__allocation_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__allocation_display
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__allocation_display
msgid "Allocation Display"
msgstr "Visualización de asignaciones"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__holiday_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__holiday_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__holiday_type
msgid "Allocation Mode"
msgstr "Modo de asignación"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__allocation_notif_subtype_id
msgid "Allocation Notification Subtype"
msgstr "Subtipo de notificación de asignación"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/js/time_off_calendar.js:0
#: model:mail.message.subtype,description:hr_holidays.mt_leave_allocation
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#, python-format
msgid "Allocation Request"
msgstr "Solicitud de asignación"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_activity
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Allocation Requests"
msgstr "Solicitudes de asignación"

#. module: hr_holidays
#: model:mail.activity.type,name:hr_holidays.mail_act_leave_allocation_second_approval
msgid "Allocation Second Approve"
msgstr "Segunda aprobación de asignación"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__allocation_type
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Allocation Type"
msgstr "Tipo de asignación"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__allocation_used_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__allocation_used_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__allocation_used_display
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__allocation_used_display
msgid "Allocation Used Display"
msgstr "Visualización de asignación utilizada"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"Allocation of %(allocation_name)s : %(duration).2f %(duration_type)s to "
"%(person)s"
msgstr ""
"Asignación de %(allocation_name)s : %(duration).2f %(duration_type)s para "
"%(person)s"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__week_day
msgid "Allocation on"
msgstr "Asignado el"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid "Allocation request must be confirmed in order to approve it."
msgstr "La solicitud de asignación debe confirmarse para poder aprobarla."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"Allocation request must be confirmed or validated in order to refuse it."
msgstr ""
"Se debe confirmar o validar la solicitud de asignación para poder "
"rechazarla."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"Allocation request must be in Draft state (\"To Submit\") in order to "
"confirm it."
msgstr ""
"La solicitud de asignación debe estar en estado de borrador (\"Por enviar\")"
" para poder confirmarla."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"Allocation request state must be \"Refused\" or \"To Approve\" in order to "
"be reset to Draft."
msgstr ""
"El estado de la solicitud de asignación debe ser \"rechazado\" o \"por "
"aprobar\" para restablecerla a borrador."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_department__allocation_to_approve_count
msgid "Allocation to Approve"
msgstr "Asignación por aprobar"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_allocation_action_approve_department
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_menu_manager_approve_allocations
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_departure_wizard_view_form
msgid "Allocations"
msgstr "Asignaciones"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Allow To Join Supporting Document"
msgstr "Permitir unirse a documentos de apoyo"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__holiday_type
msgid ""
"Allow to create requests in batchs:\n"
"- By Employee: for a specific employee\n"
"- By Company: all employees of the specified company\n"
"- By Department: all employees of the specified department\n"
"- By Employee Tag: all employees of the specific employee group category"
msgstr ""
"Permita crear solicitudes en lote:\n"
"- Por empleado: para un empleado en específico\n"
"- Por empresa: todos los empleados de la empresa especificada\n"
"- Por departamento: todos los empleados del departamento especificado\n"
"- Por etiqueta de empleado: todos los empleados de las etiquetas/categorías  de empleado especificadas"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Analyze from"
msgstr "Analizar de"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_evaluation_report_graph
msgid "Appraisal Analysis"
msgstr "Análisis de evaluación"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__allocation_validation_type
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Approval"
msgstr "Aprobación"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_approvals
msgid "Approvals"
msgstr "Aprobaciones"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
#, python-format
msgid "Approve"
msgstr "Aprobar"

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.ir_actions_server_approve_allocations
msgid "Approve Allocations"
msgstr "Aprobar asignaciones"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_holidays_summary_employee__holiday_type__approved
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__validate
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__validate
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Approved"
msgstr "Aprobado"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Approved Allocations"
msgstr "Asignaciones aprobadas"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Approved Requests"
msgstr "Solicitudes aprobadas"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Approved Time Off"
msgstr "Tiempo personal aprobado"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__allocation_validation_type__officer
msgid "Approved by Time Off Officer"
msgstr "Aprobado por el encargado de tiempo personal"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__apr
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__apr
msgid "April"
msgstr "Abril"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_departure_wizard__archive_allocation
msgid "Archive Employee Allocations"
msgstr "Archivar asignaciones de empleado"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holidays_status_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Archived"
msgstr "Archivado"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Archived Time Off"
msgstr "Tiempo personal archivado"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__action_with_unused_accruals
msgid "At the end of the calendar year, unused accruals will be"
msgstr "Al final del año, las acumulaciones sin utilizar serán"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "At the end of the year, unused accruals will be"
msgstr "Al final del año, las acumulaciones sin utilizar serán"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__supported_attachment_ids
msgid "Attach File"
msgstr "Adjuntar archivo"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_attachment_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_attachment_count
msgid "Attachment Count"
msgstr "Número de archivos adjuntos"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__attachment_ids
msgid "Attachments"
msgstr "Archivos adjuntos"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__aug
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__aug
msgid "August"
msgstr "Agosto"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#, python-format
msgid "Available"
msgstr "Disponible"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/hr_holidays/static/src/components/thread_icon/thread_icon.xml:0
#, python-format
msgid "Away"
msgstr "Ausente"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__is_based_on_worked_time
msgid "Based on worked time"
msgstr "Según el tiempo trabajado"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_employee_base
msgid "Basic Employee"
msgstr "Empleado básico"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__color_name__black
msgid "Black"
msgstr "Negro"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__color_name__blue
msgid "Blue"
msgstr "Azul"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_holidays_summary_employee__holiday_type__both
msgid "Both Approved and Confirmed"
msgstr "Aprobados y confirmados"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__color_name__brown
msgid "Brown"
msgstr "Café"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__holiday_type__company
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__holiday_type__company
msgid "By Company"
msgstr "Por empresa"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__holiday_type__department
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__holiday_type__department
msgid "By Department"
msgstr "Por departamento"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__holiday_type__employee
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__holiday_type__employee
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__holiday_type__employee
msgid "By Employee"
msgstr "Por empleado"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__holiday_type__category
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__holiday_type__category
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__holiday_type__category
msgid "By Employee Tag"
msgstr "Por etiqueta de empleado"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__leave_validation_type__manager
msgid "By Employee's Approver"
msgstr "Por aprobador de empleado"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__leave_validation_type__both
msgid "By Employee's Approver and Time Off Officer"
msgstr "Por aprobador de empleado y encargado de tiempo personal"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__holiday_type
msgid ""
"By Employee: Allocation/Request for individual Employee, By Employee Tag: "
"Allocation/Request for group of employees in category"
msgstr ""
"Por empleado: peticiones/asignaciones por cada empleado individualmente, Por"
" etiqueta de empleado: peticiones/asignaciones por grupo de empleados en la "
"categoría"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__leave_validation_type__hr
msgid "By Time Off Officer"
msgstr "Por el encargado de tiempo personal"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__can_approve
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__can_approve
msgid "Can Approve"
msgstr "Puede aprobar"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__can_reset
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__can_reset
msgid "Can reset"
msgstr "Puede restablecer"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_summary_employee
msgid "Cancel"
msgstr "Cancelar"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_departure_wizard__cancel_leaves
msgid "Cancel Future Leaves"
msgstr "Cancelar permisos futuros"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_departure_wizard__cancel_leaves
msgid "Cancel all time off after this date."
msgstr "Cancelar todo el tiempo personal después de esta fecha."

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__cancel
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__cancel
msgid "Cancelled"
msgstr "Cancelado"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__category_id
msgid "Category of Employee"
msgstr "Categoría del empleado"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__maximum_leave
msgid "Choose a cap for this accrual. 0 means no cap."
msgstr ""
"Elija un límite máximo para esta acumulación. 0 significa que no hay límite."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__responsible_id
msgid ""
"Choose the Time Off Officer who will be notified to approve allocation or "
"Time Off request"
msgstr ""
"Elija al encargado de tiempo personal que recibirá notificación para aprobar"
" asignaciones o solicitudes de tiempo personal"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Click on the 'Edit' button to add new rules."
msgstr "Haga clic en el botón de Editar para agregar nuevas reglas."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__color
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__color
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Color"
msgstr "Color"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__color_name
msgid "Color in Report"
msgstr "Color en reporte"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__employee_company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__employee_company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__company_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_search_hr_holidays_employee_type_report
msgid "Company"
msgstr "Empresa"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__mode_company_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__mode_company_id
msgid "Company Mode"
msgstr "Modo de empresa"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_comp
msgid "Compensatory Days"
msgstr "Días compensatorios"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_configuration
msgid "Configuration"
msgstr "Configuración"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Confirm"
msgstr "Confirmar"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_holidays_summary_employee__holiday_type__confirmed
msgid "Confirmed"
msgstr "Confirmado"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"Could not find an allocation of type %(leave_type)s for the requested time "
"period."
msgstr ""
"No se puede encontrar una asignación de tipo %(leave_type)s para el periodo "
"de tiempo solicitado"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__icon_id
msgid "Cover Image"
msgstr "Imagen de portada"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_approve_department
msgid "Create a new time off allocation"
msgstr "Crear una nueva asignación de tiempo personal"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_all
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_my
msgid "Create a new time off allocation request"
msgstr "Crear una nueva solicitud de asignación de tiempo personal"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__create_date
msgid "Created on"
msgstr "Creado el"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__current_leave_state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__current_leave_state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__current_leave_state
msgid "Current Time Off Status"
msgstr "Estado de tiempos personales actuales"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__current_leave_id
msgid "Current Time Off Type"
msgstr "Tipo de tiempos personales actuales"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Current Year"
msgstr "Año actual"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_unit_hours
msgid "Custom Hours"
msgstr "Horas personalizadas"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#, python-format
msgid "DAYS"
msgstr "DÍAS"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__daily
msgid "Daily"
msgstr "Diariamente"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_new_request
#: model:ir.ui.menu,name:hr_holidays.hr_leave_menu_new_request
msgid "Dashboard"
msgstr "Tablero"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_date_from_period
msgid "Date Period Start"
msgstr "Fecha de inicio de periodo "

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__lastcall
msgid "Date of the last accrual allocation"
msgstr "Fecha de la última asignación de acumulación"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__nextcall
msgid "Date of the next accrual allocation"
msgstr "Fecha de la siguiente asignación acumulada"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Dates"
msgstr "Fechas"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__request_unit__day
msgid "Day"
msgstr "Día"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__added_value_type__days
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
#, python-format
msgid "Days"
msgstr "Días"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__group_days_allocation
msgid "Days Allocated"
msgstr "Dias asignados"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_unit_custom
msgid "Days-long custom hours"
msgstr "Horas de trabajo personalizadas en el día"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__dec
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__dec
msgid "December"
msgstr "Diciembre"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
#, python-format
msgid "Delete"
msgstr "Eliminar"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_department
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__department_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__department_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Department"
msgstr "Departamento"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Department search"
msgstr "Búsqueda de departamento"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "Asistente de salida"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__name
msgid "Description"
msgstr "Descripción"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__name_validity
msgid "Description with validity"
msgstr "Descripción con validez"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Display Option"
msgstr "Mostrar opción"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__create_calendar_meeting
msgid "Display Time Off in Calendar"
msgstr "Mostrar tiempos personales en el calendario"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "Dropdown menu"
msgstr "Menú desplegable"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__duration
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Duration"
msgstr "Duración"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__number_of_days
msgid "Duration (Days)"
msgstr "Duración (días)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__number_of_days_display
msgid "Duration (days)"
msgstr "Duración (días)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__number_of_hours_display
msgid "Duration (hours)"
msgstr "Duración (horas)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__number_of_days_display
msgid "Duration in days"
msgstr "Duración en días"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__number_of_days
msgid "Duration in days. Reference field to use when necessary."
msgstr ""
"Duración en días. Campo de referencia a utilizar cuando sea necesario."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__number_of_hours_display
msgid "Duration in hours"
msgstr "Duración en horas"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#, python-format
msgid "Edit"
msgstr "Editar"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
msgid "Edit Allocation"
msgstr "Editar asignación"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "Edit Time Off"
msgstr "Editar tiempo personal"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_employee
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__employee_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__employee_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__employee_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__employee_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__employee_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_search_hr_holidays_employee_type_report
msgid "Employee"
msgstr "Empleado"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__employee_requests
msgid "Employee Requests"
msgstr "Solicitudes de empleados"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__category_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__category_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__category_id
msgid "Employee Tag"
msgstr "Etiqueta del empleado"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__emp
msgid "Employee(s)"
msgstr "Empleado(s)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__employee_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__employees_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__employee_ids
msgid "Employees"
msgstr "Empleados"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__date_to
msgid "End Date"
msgstr "Fecha de finalización"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__employee_requests__yes
msgid "Extra Days Requests Allowed"
msgstr "Solicitudes de días adicionales permitidas"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__feb
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__feb
msgid "February"
msgstr "Febrero"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__duration_display
msgid ""
"Field allowing to see the allocation duration in days or hours depending on "
"the type_request_unit"
msgstr ""
"Campo que permite ver la duración de la asignación en días u horas "
"dependiendo de type_request_unit"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__duration_display
msgid ""
"Field allowing to see the leave request duration in days or hours depending "
"on the leave_type_request_unit"
msgstr ""
"Campo que permite ver la duración de la solicitud de permisos en días u "
"horas dependiendo de leave_type_request_unit"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid ""
"Filters only on allocations that belong to an time off type that is 'active'"
" (active field is True)"
msgstr ""
"Filtra solo las asignaciones que pertenecen a un tipo de tiempo personal que"
" está \"activo\" (el campo activo es Verdadero)."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid ""
"Filters only on requests that belong to an time off type that is 'active' "
"(active field is True)"
msgstr ""
"Filtra solo las solicitudes que pertenecen a un tipo de tiempo personal que "
"está \"activo\" (el campo activo es Verdadero)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__first_approver_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__approver_id
msgid "First Approval"
msgstr "Primera aprobación"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_day
msgid "First Day"
msgstr "Primer día"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_day_display
msgid "First Day Display"
msgstr "Pantalla del primer día"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_month
msgid "First Month"
msgstr "Primer mes"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_month_day
msgid "First Month Day"
msgstr "Primer mes y día"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__first_month_day_display
msgid "First Month Day Display"
msgstr "Pantalla del primer mes y día"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_follower_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_partner_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (partners)"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__activity_type_icon
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icono de Font Awesome ej. fa-tasks"

#. module: hr_holidays
#: code:addons/hr_holidays/report/holidays_summary_report.py:0
#, python-format
msgid "Form content is missing, this report cannot be printed."
msgstr "Falta el contenido del formulario, no se puede imprimir este reporte."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__frequency
msgid "Frequency"
msgstr "Frecuencia"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__fri
msgid "Friday"
msgstr "Viernes"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__start_datetime
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "From"
msgstr "Desde"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__leave_date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__leave_date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__leave_date_from
msgid "From Date"
msgstr "Desde la fecha"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_activity
msgid "From:"
msgstr "Desde:"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Future Activities"
msgstr "Actividades futuras"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Group By"
msgstr "Agrupar por"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__group_days_leave
msgid "Group Time Off"
msgstr "Tiempo personal grupal"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#, python-format
msgid "HOURS"
msgstr "HORAS"

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.action_hr_approval
msgid "HR Approval"
msgstr "Autorización por RR. HH."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__report_note
msgid "HR Comments"
msgstr "Comentarios de RR. HH."

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_holidays_summary_employee
msgid "HR Time Off Summary Report By Employee"
msgstr "Reporte resumido de tiempo personal de RR. HH. por empleado"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_unit_half
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__request_unit__half_day
msgid "Half Day"
msgstr "Medio día"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__has_message
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__has_message
msgid "Has Message"
msgstr "Tiene un mensaje"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__has_valid_allocation
msgid "Has Valid Allocation"
msgstr "Tiene una asignación válida"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__is_hatched
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__is_hatched
msgid "Hatched"
msgstr "Creado"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__multi_employee
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__multi_employee
msgid "Holds whether this allocation concerns more than 1 employee"
msgstr "Contiene si esta asignación se refiere a más de 1 empleado"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__holiday_status
msgid "Holiday Status"
msgstr "Estado de día festivo"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_report_hr_holidays_report_holidayssummary
msgid "Holidays Summary Report"
msgstr "Reporte de resumen de días festivos"

#. module: hr_holidays
#: model:mail.message.subtype,description:hr_holidays.mt_leave_home_working
#: model:mail.message.subtype,name:hr_holidays.mt_leave_home_working
msgid "Home Working"
msgstr "Trabajando en casa"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_hour_from
msgid "Hour from"
msgstr "Hora desde"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_hour_to
msgid "Hour to"
msgstr "Hora hasta"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__added_value_type__hours
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__request_unit__hour
#, python-format
msgid "Hours"
msgstr "Horas"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__hr_icon_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__hr_icon_display
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__hr_icon_display
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__hr_icon_display
msgid "Hr Icon Display"
msgstr "Visualización del icono de RR. HH."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__id
msgid "ID"
msgstr "ID"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_exception_icon
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_exception_icon
msgid "Icon"
msgstr "Icono"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__activity_exception_icon
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icono para indicar una actividad de excepción."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__number_of_days_display
msgid ""
"If Accrual Allocation: Number of days allocated in addition to the ones you "
"will get via the accrual' system."
msgstr ""
"Si hay asignación acumulada: número de días asignados además de los días que"
" recibirá a través del sistema de acumulaciones."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__number_of_hours_display
msgid ""
"If Accrual Allocation: Number of hours allocated in addition to the ones you"
" will get via the accrual' system."
msgstr ""
"Si hay asignación acumulada: número de horas asignadas además de las horas "
"que recibirá a través del sistema de acumulaciones."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_needaction
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_unread
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_needaction
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_unread
msgid "If checked, new messages require your attention."
msgstr ""
"Si se encuentra seleccionado, hay nuevos mensajes que requieren su atención."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_has_error
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Si se encuentra seleccionado, algunos mensajes tienen error de envío."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__active_employee
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__active_employee
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_employee_type_report__active_employee
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_report__active_employee
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"Si se desmarca el campo activo, permite ocultar el registro del recurso sin "
"eliminarlo."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__active
msgid ""
"If the active field is set to false, it will allow you to hide the time off "
"type without removing it."
msgstr ""
"Si el campo activo se establece en falso, le permitirá ocultar el tipo de "
"tiempo libre sin eliminarlo."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__parent_id
msgid "If this field is empty, this level is the first one."
msgstr "Si este campo está vacío, este nivel es el primero."

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_duration_check
msgid ""
"If you want to change the number of days you should use the 'period' mode"
msgstr "Si desea cambiar la cantidad de días, debe usar el modo 'periodo'"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_plan__transition_mode__immediately
msgid "Immediately"
msgstr "Inmediatamente"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_plan__transition_mode
msgid ""
"Immediately: When the date corresponds to the new level, your accrual is automatically computed, granted and you switch to new level\n"
"                After this accrual's period: When the accrual is complete (a week, a month), and granted, you switch to next level if allocation date corresponds"
msgstr ""
"Inmediatamente: cuando la fecha corresponde al nuevo nivel, se calcula su acumulación de forma automática, se concede y se pasa al nuevo nivel\n"
"                 Después de este periodo de acumulación: cuando la acumulación se completa (una semana, un mes), y se concede, se pasa al siguiente nivel si la fecha de asignación corresponde"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "In"
msgstr "En"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_is_follower
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__is_officer
msgid "Is Officer"
msgstr "Es encargado"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__unpaid
msgid "Is Unpaid"
msgstr "Sin pagar"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__color_name__ivory
msgid "Ivory"
msgstr "Marfil"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__jan
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__jan
msgid "January"
msgstr "Enero"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__job_id
msgid "Job"
msgstr "Trabajo"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Job Position"
msgstr "Puesto de trabajo"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__jul
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__jul
msgid "July"
msgstr "Julio"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__jun
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__jun
msgid "June"
msgstr "Junio"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_my
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_new_request
msgid "Keep track of your PTOs."
msgstr "Lleve el registro de sus tiempos personales pagados."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__time_type
msgid "Kind of Leave"
msgstr "Tipo de permiso"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee____last_update
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave____last_update
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level____last_update
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan____last_update
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation____last_update
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report____last_update
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report____last_update
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar____last_update
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Late Activities"
msgstr "Actividades atrasadas"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__color_name__lavender
msgid "Lavender"
msgstr "Lavanda"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_resource_calendar_leaves__holiday_id
msgid "Leave Request"
msgstr "Solicitud de permiso"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__leave_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__holiday_status_id
msgid "Leave Type"
msgstr "Tipo de permiso"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__leave_validation_type
msgid "Leave Validation"
msgstr "Validación de permisos"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__leaves_taken
msgid "Leaves Taken"
msgstr "Permisos tomados"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__holiday_status__left
msgid "Left"
msgstr "Izquierda"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#, python-format
msgid "Legend"
msgstr "Leyenda"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__level
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__level_ids
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Level"
msgstr "Nivel"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__transition_mode
msgid "Level Transition"
msgstr "Transición de nivel"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__level
msgid "Level computed through the sequence."
msgstr "Nivel calculado a través de la secuencia."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__level_count
msgid "Levels"
msgstr "Niveles"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__color_name__lightblue
msgid "Light Blue"
msgstr "Azul claro"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__color_name__lightcoral
msgid "Light Coral"
msgstr "Coral claro"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__color_name__lightcyan
msgid "Light Cyan"
msgstr "Turquesa claro"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__color_name__lightgreen
msgid "Light Green"
msgstr "Verde claro"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__color_name__lightpink
msgid "Light Pink"
msgstr "Rosa claro"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__color_name__lightsalmon
msgid "Light Salmon"
msgstr "Salmón claro"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__color_name__lightyellow
msgid "Light Yellow"
msgstr "Amarillo claro"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Limit of"
msgstr "Límite de"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__maximum_leave
msgid "Limit to"
msgstr "Limitar a"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__linked_request_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__linked_request_ids
msgid "Linked Requests"
msgstr "Solicitudes vinculadas"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__action_with_unused_accruals__lost
msgid "Lost"
msgstr "Perdido"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__color_name__magenta
msgid "Magenta"
msgstr "Magenta"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_main_attachment_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_main_attachment_id
msgid "Main Attachment"
msgstr "Archivo adjunto principal"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__manager_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__manager_id
msgid "Manager"
msgstr "Gerente"

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.action_manager_approval
msgid "Manager Approval"
msgstr "Aprobación del gerente"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__mar
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__mar
msgid "March"
msgstr "Marzo"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Mark as Draft"
msgstr "Marcar como borrador"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__max_leaves
msgid "Max Leaves"
msgstr "Máximo de permisos"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holiday_status_view_kanban
msgid "Max Time Off:"
msgstr "Tiempo personal máximo:"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__max_leaves
msgid "Maximum Allowed"
msgstr "Máximo permitido"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__remaining_leaves
msgid "Maximum Time Off Allowed - Time Off Already Taken"
msgstr "Tiempo personal máximo permitido - Tiempo personal ya tomado"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__virtual_remaining_leaves
msgid ""
"Maximum Time Off Allowed - Time Off Already Taken - Time Off Waiting "
"Approval"
msgstr ""
"Tiempo personal máximo permitido - Tiempo personal ya tomado - Tiempo "
"personal en espera de aprobación"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__first_month__may
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__may
msgid "May"
msgstr "Mayo"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_action_approve_department
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_holiday_allocation_id
msgid "Meet the time off dashboard."
msgstr "Conozca el tablero de tiempo personal"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__meeting_id
msgid "Meeting"
msgstr "Reunión"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_has_error
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_has_error
msgid "Message Delivery error"
msgstr "Error de envío de mensaje"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_mail_message_subtype
msgid "Message subtypes"
msgstr "Subtipos de mensaje"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_ids
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Mode"
msgstr "Modo"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__mon
msgid "Monday"
msgstr "Lunes"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Month"
msgstr "Mes"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__monthly
msgid "Monthly"
msgstr "Mensual"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__request_date_from_period__am
msgid "Morning"
msgstr "Mañana"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__multi_employee
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__multi_employee
msgid "Multi Employee"
msgstr "Multiempleado"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__my_activity_date_deadline
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Fecha límite de mi actividad"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_allocation_action_my
#: model:ir.ui.menu,name:hr_holidays.menu_open_allocation
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "My Allocations"
msgstr "Mis asignaciones"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "My Department"
msgstr "Mi departamento"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "My Requests"
msgstr "Mis solicitudes"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "My Team"
msgstr "Mi equipo"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_my
#: model:ir.ui.menu,name:hr_holidays.hr_leave_menu_my
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_my_leaves
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "My Time Off"
msgstr "Mi tiempo personal"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__name
#: model_terms:ir.ui.view,arch_db:hr_holidays.resource_calendar_leaves_tree_inherit
msgid "Name"
msgstr "Nombre"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Need Second Approval"
msgstr "Necesita segunda aprobación"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__draft
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__draft
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__draft
msgid "New"
msgstr "Nuevo"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "New %(leave_type)s Request created by %(user)s"
msgstr "%(user)s creo una nueva solicitud de %(leave_type)s"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/js/time_off_calendar.js:0
#: code:addons/hr_holidays/static/src/js/time_off_calendar_employee.js:0
#, python-format
msgid "New Allocation"
msgstr "Nueva asignación"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/js/time_off_calendar_employee.js:0
#, python-format
msgid "New Allocation Request"
msgstr "Nueva solicitud de asignación"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"New Allocation Request created by %(user)s: %(count)s Days of "
"%(allocation_type)s"
msgstr ""
"Nueva solicitud de asignación creada por %(user)s: %(count)s días de "
"%(allocation_type)s"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/js/time_off_calendar.js:0
#: code:addons/hr_holidays/static/src/js/time_off_calendar_employee.js:0
#, python-format
msgid "New Time Off"
msgstr "Nuevo tiempo personal"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/js/time_off_calendar.js:0
#: code:addons/hr_holidays/static/src/js/time_off_calendar_employee.js:0
#, python-format
msgid "New time off"
msgstr "Nuevo tiempo personal"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_calendar_event_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Siguiente evento en el calendario de actividades."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_date_deadline
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Fecha límite de la siguiente actividad"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_summary
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_summary
msgid "Next Activity Summary"
msgstr "Resumen de la siguiente actividad"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_type_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_type_id
msgid "Next Activity Type"
msgstr "Tipo de la siguiente actividad"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__requires_allocation__no
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "No Limit"
msgstr "Sin límite"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__leave_validation_type__no_validation
msgid "No Validation"
msgstr "Sin validación"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.action_hr_available_holidays_report
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_action_action_department
msgid "No data yet!"
msgstr "¡No hay datos aún!"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "No limit"
msgstr "Sin límite"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "No rules added"
msgstr "No se agregaron reglas"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__allocation_validation_type__no
msgid "No validation needed"
msgstr "No se necesita validación"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/xml/leave_stats_templates.xml:0
#: code:addons/hr_holidays/static/src/xml/leave_stats_templates.xml:0
#, python-format
msgid "None"
msgstr "Ninguno"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__employee_requests__no
msgid "Not Allowed"
msgstr "No permitido"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__nov
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__nov
msgid "November"
msgstr "Noviembre"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__number_of_hours_text
msgid "Number Of Hours Text"
msgstr "Número de horas en texto"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_needaction_counter
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__number_of_days
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__number_of_days
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__number_of_days
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_tree
msgid "Number of Days"
msgstr "Número de días"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__leaves_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__leaves_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__leaves_count
msgid "Number of Time Off"
msgstr "Número de tiempos personales"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__number_of_days_display
msgid ""
"Number of days of the time off request according to your working schedule. "
"Used for interface."
msgstr ""
"Número de días de tiempo personal solicitado de acuerdo a su horario de "
"trabajo. Se utiliza para la interfaz."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__number_of_days
msgid ""
"Number of days of the time off request. Used in the calculation. To manually"
" correct the duration, use this field."
msgstr ""
"Número de días de tiempo personal solicitado. Se utiliza en el cálculo. Para"
" corregir manualmente la duración, utilice este campo."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_has_error_counter
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_has_error_counter
msgid "Number of errors"
msgstr "Número de errores"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__number_of_hours_display
msgid ""
"Number of hours of the time off request according to your working schedule. "
"Used for interface."
msgstr ""
"Número de horas de tiempo personal solicitadas de acuerdo a su horario de "
"trabajo. Se utiliza para la interfaz."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_needaction_counter
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Número de mensajes que requieren una acción"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_has_error_counter
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__message_unread_counter
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__message_unread_counter
msgid "Number of unread messages"
msgstr "Número de mensajes sin leer"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__oct
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__oct
msgid "October"
msgstr "Octubre"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_employee_public_form_view_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid "Off Till"
msgstr "Ausente hasta"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Off Today"
msgstr "Con permiso hoy"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__hr_icon_display__presence_holiday_absent
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__hr_icon_display__presence_holiday_absent
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__hr_icon_display__presence_holiday_absent
msgid "On leave"
msgstr "Con permiso"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/hr_holidays/static/src/components/thread_icon/thread_icon.xml:0
#, python-format
msgid "Online"
msgstr "En línea"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Only a Time Off Manager can approve/refuse its own requests."
msgstr ""
"Solo un gerente de tiempo personal puede aprobar o rechazar sus propias "
"solicitudes."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Only a Time Off Manager can reset a refused leave."
msgstr ""
"Solo un gerente de tiempo personal puede restablecer un permiso rechazado."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Only a Time Off Manager can reset a started leave."
msgstr ""
"Solo un gerente de tiempo personal puede restablecer un permiso ya iniciado."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Only a Time Off Manager can reset other people leaves."
msgstr ""
"Solo un gerente de tiempo personal puede restablecer los permisos de otras "
"personas."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"Only a Time off Approver can apply the second approval on allocation "
"requests."
msgstr ""
"Solo un aprobador de tiempo personal puede aplicar la segunda aprobación a "
"solicitudes de asignación."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid "Only a time off Manager can approve its own requests."
msgstr ""
"Solo un gerente de tiempo personal puede aprobar sus propias solicitudes."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid "Only a time off Manager can reset other people allocation."
msgstr ""
"Solo un gerente de tiempo personal puede restablecer la asignación de otras "
"personas."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"Only a time off Officer/Responsible or Manager can approve or refuse time "
"off requests."
msgstr ""
"Solo un encargado/responsable o gerente de tiempo personal puede aprobar o "
"rechazar solicitudes de tiempo personal."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__is_based_on_worked_time
msgid ""
"Only accrue for the time worked by the employee. This is the time when the "
"employee did not take time off."
msgstr ""
"Solo acumule según el tiempo trabajado por el empleado. Este es el tiempo "
"cuando el empleado no tomó tiempo personal."

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__time_type__other
msgid "Other"
msgstr "Otro"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/hr_holidays/static/src/components/thread_icon/thread_icon.xml:0
#, python-format
msgid "Out of office"
msgstr "Fuera de la oficina"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/models/partner/partner.js:0
#, python-format
msgid "Out of office until %s"
msgstr "Fuera de la oficina hasta %s"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_dashboard
msgid "Overview"
msgstr "Información general"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_cl
msgid "Paid Time Off"
msgstr "Tiempo personal pagado"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__parent_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__parent_id
msgid "Parent"
msgstr "Padre"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.hr_holiday_status_dv
msgid "Parental Leaves"
msgstr "Permisos paternales"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_search_hr_holidays_employee_type_report
msgid "Period"
msgstr "Periodo"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__holiday_status__planned
msgid "Planned"
msgstr "Planeado"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__hr_icon_display__presence_holiday_present
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__hr_icon_display__presence_holiday_present
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__hr_icon_display__presence_holiday_present
msgid "Present but on leave"
msgstr "Presente pero con permiso"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__parent_id
msgid "Previous Level"
msgstr "Nivel anterior"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_summary_employee
msgid "Print"
msgstr "Imprimir"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.open_view_public_holiday
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_public_time_off_menu_configuration
msgid "Public Holidays"
msgstr "Días festivos públicos"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.resource_calendar_form_inherit
msgid "Public Time Off"
msgstr "Tiempo personal público"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__added_value
msgid "Rate"
msgstr "Tasa"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__notes
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__notes
msgid "Reasons"
msgstr "Razones"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__color_name__red
msgid "Red"
msgstr "Rojo"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
#, python-format
msgid "Refuse"
msgstr "Rechazar"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__refuse
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__refuse
#, python-format
msgid "Refused"
msgstr "Rechazado"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__allocation_type__regular
msgid "Regular Allocation"
msgstr "Asignación regular"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__user_id
msgid "Related user name for the resource to manage its access."
msgstr "Usuario relacionado con el recurso para gestionar su acceso."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_tree
msgid "Remaining Days"
msgstr "Días restantes"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__remaining_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__remaining_leaves
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__remaining_leaves
msgid "Remaining Paid Time Off"
msgstr "Tiempo personal pagado restante"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__remaining_leaves
msgid "Remaining Time Off"
msgstr "Tiempo personal restante"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid "Remaining leaves"
msgstr "Permisos restantes"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_departure_wizard__archive_allocation
msgid "Remove employee from existing accrual plans."
msgstr "Remover al empleado de planes de acumulación actuales."

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_report
msgid "Reporting"
msgstr "Reportes"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
msgid "Request Allocation"
msgstr "Solicitar asignación"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_date_to
msgid "Request End Date"
msgstr "Fecha de finalización de la solicitud"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__request_date_from
msgid "Request Start Date"
msgstr "Fecha de inicio de la solicitud"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.res_users_view_form
msgid "Request Time off"
msgstr "Solicitar tiempo personal"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__leave_type
msgid "Request Type"
msgstr "Tipo de solicitud"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__duration_display
msgid "Requested (Days/Hours)"
msgstr "Solicitado (días/horas)"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__requires_allocation
msgid "Requires allocation"
msgstr "Necesita asignación"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.resource_calendar_global_leaves_action_from_calendar
msgid "Resource Time Off"
msgstr "Recurso de tiempo personal"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_resource_calendar_leaves
msgid "Resource Time Off Detail"
msgstr "Detalle del tiempo personal de los recursos"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__responsible_id
msgid "Responsible Time Off Officer"
msgstr "Encargado de tiempo personal responsable"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__activity_user_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__activity_user_id
msgid "Responsible User"
msgstr "Usuario responsable"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Rules"
msgstr "Reglas"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid "Run until"
msgstr "Ejecutar hasta"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__sat
msgid "Saturday"
msgstr "Sábado"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_search_hr_holidays_employee_type_report
msgid "Search Time Off"
msgstr "Buscar tiempo personal"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holidays_status_filter
msgid "Search Time Off Type"
msgstr "Buscar tipo de tiempo personal"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Search allocations"
msgstr "Buscar asignaciones"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__second_approver_id
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__validate1
msgid "Second Approval"
msgstr "Segunda aprobación"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_day
msgid "Second Day"
msgstr "Segundo día"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_day_display
msgid "Second Day Display"
msgstr "Pantalla del segundo día"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_month
msgid "Second Month"
msgstr "Segundo mes"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_month_day
msgid "Second Month Day"
msgstr "Segundo mes y día"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__second_month_day_display
msgid "Second Month Day Display"
msgstr "Pantalla del segundo mes y día"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee__holiday_type
msgid "Select Time Off Type"
msgstr "Seleccione el tipo de tiempo personal "

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_employee__leave_manager_id
#: model:ir.model.fields,help:hr_holidays.field_hr_employee_base__leave_manager_id
#: model:ir.model.fields,help:hr_holidays.field_hr_employee_public__leave_manager_id
#: model:ir.model.fields,help:hr_holidays.field_res_users__leave_manager_id
msgid ""
"Select the user responsible for approving \"Time Off\" of this employee.\n"
"If empty, the approval is done by an Administrator or Approver (determined in settings/users)."
msgstr ""
"Seleccione el usuario responsable de aprobar \"tiempo personal\" de este empleado.\n"
"Si se queda en blanco, la aprobación la realiza un administrador o un aprobador (se determina en ajustes/usuarios)."

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__second_month__sep
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__yearly_month__sep
msgid "September"
msgstr "Septiembre"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__sequence
msgid "Sequence is generated automatically by start time delta."
msgstr ""
"La secuencia se genera de forma automática al empezar delta de tiempo."

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__allocation_validation_type__set
msgid "Set by Time Off Officer"
msgstr "Establecido por el encargado de tiempo personal"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Show all records which has next action date is before today"
msgstr ""
"Mostrar todos los registros que tienen la próxima fecha de acción antes de "
"hoy"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_sl
#: model:mail.message.subtype,description:hr_holidays.mt_leave_sick
#: model:mail.message.subtype,name:hr_holidays.mt_leave_sick
msgid "Sick Time Off"
msgstr "Tiempo personal por enfermedad"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_plan__time_off_type_id
msgid ""
"Specify if this accrual plan can only be used with this Time Off Type.\n"
"                Leave empty if this accrual plan can be used with any Time Off Type."
msgstr ""
"Especifique si este plan de acumulación solo se puede utilizar con este tipo de tiempo personal.\n"
"                Deje vacío si este plan de acumulación solo se puede usar con este tipo de tiempo personal."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__date_from
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Start Date"
msgstr "Fecha de inicio"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__start_count
msgid "Start after"
msgstr "Empezar después"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Starts"
msgstr "Empieza"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "Starts immediately after allocation start date"
msgstr "Empieza inmediatamente después de la fecha de inicio de la asignación"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__state
msgid "State"
msgstr "Estado"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_employee_type_report__state
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report__state
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Status"
msgstr "Estado"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__activity_state
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estado basado en actividades\n"
"Vencida: la fecha límite ya pasó\n"
"Hoy: la fecha límite es hoy\n"
"Planeada: futuras actividades."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__is_striked
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__is_striked
msgid "Striked"
msgstr "Tachado"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Such grouping is not allowed."
msgstr "No se permite esta agrupación."

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Sum"
msgstr "Suma"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__virtual_leaves_taken
msgid "Sum of validated and non validated time off requests."
msgstr "Suma de solicitudes de tiempo personal validadas y no validadas."

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__sun
msgid "Sunday"
msgstr "Domingo"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__supported_attachment_ids_count
msgid "Supported Attachment Ids Count"
msgstr "Número de IDs de archivos adjuntos compatibles"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__leave_type_support_document
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__support_document
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "Supporting Document"
msgstr "Documentos de apoyo"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Supporting Documents"
msgstr "Documentos de apoyo"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#, python-format
msgid "TAKEN"
msgstr "TOMADO"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__leave_type_request_unit
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__type_request_unit
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__request_unit
msgid "Take Time Off in"
msgstr "Tomar tiempo personal en"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__holiday_status__taken
#, python-format
msgid "Taken"
msgstr "Tomado"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__taken_leave_ids
msgid "Taken Leave"
msgstr "Permiso tomado"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__start_count
msgid ""
"The accrual starts after a defined period from the allocation start date. "
"This field defines the number of days, months or years after which accrual "
"is used."
msgstr ""
"La acumulación comienza después de un periodo definido desde la fecha de "
"inicio de la asignación. Este campo define el número de días, meses o años "
"después de los cuales se utiliza la acumulación."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__color
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__color
msgid ""
"The color selected here will be used in every screen with the time off type."
msgstr ""
"El color seleccionado aquí se utilizará en cada pantalla con el tipo tiempo "
"personal."

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_accrual_level_check_dates
msgid "The dates you've set up aren't correct. Please check them."
msgstr "Las fechas que estableció no son correctas. Compruébelas."

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_allocation_duration_check
msgid "The duration must be greater than 0."
msgstr "La duración debe ser mayor a 0."

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_allocation_type_value
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_type_value
msgid ""
"The employee, department, company or employee category of this request is "
"missing. Please make sure that your user login is linked to an employee."
msgstr ""
"Falta el empleado, el departamento, la empresa o la categoría de empleado de"
" esta solicitud. Por favor, asegúrese de que el nombre de usuario esté "
"vinculado a un empleado."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"The following employees are not supposed to work during that period:\n"
" %s"
msgstr ""
"Los siguientes empleados no deben trabajar durante ese periodo:\n"
" %s"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__added_value
msgid ""
"The number of hours/days that will be incremented in the specified Time Off "
"Type for every period"
msgstr ""
"El número de horas/días que se incrementarán en el tipo de tiempo personal "
"especificado para cada periodo"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"The number of remaining time off is not sufficient for this time off type.\n"
"Please also check the time off waiting for validation."
msgstr ""
"El número de tiempos personales restantes no es suficiente para esta clase de tiempo personal.\n"
"Por favor, compruebe también el tiempo de espera para la validación."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"\n"
"The employees that lack allocation days are:\n"
"%s"
msgstr ""
"\n"
"Los empleados sin asignación de días son:\n"
"%s"

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_date_check2
msgid "The start date must be anterior to the end date."
msgstr "La fecha de inicio debe ser anterior a la fecha de finalización."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__state
msgid ""
"The status is set to 'To Submit', when a time off request is created.\n"
"The status is 'To Approve', when time off request is confirmed by user.\n"
"The status is 'Refused', when time off request is refused by manager.\n"
"The status is 'Approved', when time off request is approved by manager."
msgstr ""
"El estado se establece en `Para enviar', cuando se crea una solicitud de tiempo personal.\n"
"El estado es 'Por aprobar', cuando el usuario confirma la solicitud de tiempo personal.\n"
"El estado es 'Rechazado', cuando el gerente rechaza la solicitud de tiempo personal.\n"
"El estado es 'Aprobado', cuando el gerente aprueba la solicitud de tiempo personal."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__state
msgid ""
"The status is set to 'To Submit', when an allocation request is created.\n"
"The status is 'To Approve', when an allocation request is confirmed by user.\n"
"The status is 'Refused', when an allocation request is refused by manager.\n"
"The status is 'Approved', when an allocation request is approved by manager."
msgstr ""
"El estado se establece en 'Para enviar', cuando se crea una solicitud de asignación.\n"
"El estado es 'Por aprobar', cuando el usuario confirma una solicitud de asignación.\n"
"El estado es 'Rechazado', cuando el gerente rechaza una solicitud de asignación.\n"
"El estado es 'Aprobado', cuando el gerente aprueba una solicitud de asignación."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "The time off has been automatically approved"
msgstr "El tiempo personal se autorizó automáticamente"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__sequence
msgid ""
"The type with the smallest sequence is the default value in time off request"
msgstr ""
"El tipo con la secuencia más pequeña es el valor predeterminado en la "
"solicitud de tiempo personal"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"This allocation have already ran once, any modification won't be effective "
"to the days allocated to the employee. If you need to change the "
"configuration of the allocation, cancel and create a new one."
msgstr ""
"Ya se ejecutó esta asignación una vez, cualquier modificación no se aplicará"
" a los días asignados al empleado. Si necesita cambiar la configuración de "
"la asignación, cancélela y cree una nueva."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__first_approver_id
msgid ""
"This area is automatically filled by the user who validate the time off"
msgstr ""
"Esta área se completa automáticamente con el usuario que valida el tiempo "
"personal"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__second_approver_id
msgid ""
"This area is automatically filled by the user who validate the time off with"
" second level (If time off type need second validation)"
msgstr ""
"Esta área se completa automáticamente con el usuario que valida el tiempo "
"personal con segundo nivel (si el tipo de tiempo personal necesita una "
"segunda validación)"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__approver_id
msgid ""
"This area is automatically filled by the user who validates the allocation"
msgstr ""
"Esta área se completa automáticamente con el usuario que valida la "
"asignación"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__color_name
msgid ""
"This color will be used in the time off summary located in Reporting > Time "
"off by Department."
msgstr ""
"Este color se usará en el resumen de tiempo personal ubicado en Reportes > "
"Tiempo personal por departamento."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_accrual_level__start_type
msgid "This field defines the unit of time after which the accrual starts."
msgstr ""
"Este campo define la unidad de tiempo después de la cual comienza la "
"acumulación."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__has_valid_allocation
msgid "This indicates if it is still possible to use this type of leave"
msgstr "Esto indica si aún es posible usar este tipo de permiso"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "This modification is not allowed in the current state."
msgstr "No se permite esta modificación en el estado actual."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__leaves_taken
msgid ""
"This value is given by the sum of all time off requests with a negative "
"value."
msgstr ""
"Este valor viene dado por la suma de todas las solicitudes de tiempo "
"personal con un valor negativo."

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__max_leaves
msgid ""
"This value is given by the sum of all time off requests with a positive "
"value."
msgstr ""
"Este valor viene dado por la suma de todas las solicitudes de tiempo "
"personal con un valor positivo."

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__thu
msgid "Thursday"
msgstr "Jueves"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_holiday_allocation_id
#: model:ir.model,name:hr_holidays.model_hr_leave
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__leave_manager_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__leave_manager_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__leave_manager_id
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__leave_manager_id
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__leave_type__request
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__time_type__leave
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_root
#: model:ir.ui.menu,name:hr_holidays.menu_open_department_leave_approve
#: model:mail.message.subtype,name:hr_holidays.mt_leave
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_departure_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_form
msgid "Time Off"
msgstr "Tiempo personal"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/js/time_off_calendar.js:0
#: code:addons/hr_holidays/static/src/js/time_off_calendar_employee.js:0
#, python-format
msgid "Time Off : %s"
msgstr "Tiempo personal : %s"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_allocation
msgid "Time Off Allocation"
msgstr "Asignación de tiempo personal"

#. module: hr_holidays
#: code:addons/hr_holidays/report/hr_leave_employee_type_report.py:0
#: code:addons/hr_holidays/report/hr_leave_report.py:0
#: model:ir.actions.act_window,name:hr_holidays.action_hr_available_holidays_report
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_action_department
#, python-format
msgid "Time Off Analysis"
msgstr "Análisis de tiempo personal"

#. module: hr_holidays
#: model:mail.activity.type,name:hr_holidays.mail_act_leave_approval
msgid "Time Off Approval"
msgstr "Aprobación de tiempo personal"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_tree_inherit_leave
msgid "Time Off Approver"
msgstr "Aprobador de tiempo personal"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_report_calendar
msgid "Time Off Calendar"
msgstr "Calendario de tiempo personal"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_employee.py:0
#, python-format
msgid "Time Off Dashboard"
msgstr "Tablero de Tiempo personal"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__private_name
msgid "Time Off Description"
msgstr "Descripción del tiempo personal"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__leave_notif_subtype_id
msgid "Time Off Notification Subtype"
msgstr "Subtipo de notificación de tiempo personal"

#. module: hr_holidays
#: model:res.groups,name:hr_holidays.group_hr_holidays_user
msgid "Time Off Officer"
msgstr "Encargado de tiempo personal"

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_all
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_approve_department
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_leave_allocation_action_my
msgid ""
"Time Off Officers allocate time off days to employees (e.g. paid time off).<br>\n"
"                Employees request allocations to Time Off Officers (e.g. recuperation days)."
msgstr ""
"Los encargados de tiempo personal asignan días de tiempo personal a los empleados (ej. tiempo personal pagado).<br>\n"
"                Los empleados solicitan asignaciones a los encargados de tiempo personal (ej. días de recuperación)."

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/js/time_off_calendar.js:0
#: code:addons/hr_holidays/static/src/js/time_off_calendar_employee.js:0
#: model:ir.actions.act_window,name:hr_holidays.hr_leave_action_my_request
#: model:mail.message.subtype,description:hr_holidays.mt_leave
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_employee_view_dashboard
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_activity
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_calendar
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_dashboard
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#, python-format
msgid "Time Off Request"
msgstr "Solicitud de tiempo personal"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Time Off Requests"
msgstr "Solicitudes de tiempo personal"

#. module: hr_holidays
#: model:res.groups,name:hr_holidays.group_hr_holidays_responsible
msgid "Time Off Responsible"
msgstr "Responsable de tiempo personal"

#. module: hr_holidays
#: model:mail.activity.type,name:hr_holidays.mail_act_leave_second_approval
msgid "Time Off Second Approve"
msgstr "Segunda aprobación de tiempo personal"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.action_hr_holidays_summary_employee
#: model:ir.actions.report,name:hr_holidays.action_report_holidayssummary
#: model:ir.actions.report,name:hr_holidays.action_report_holidayssummary2
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_graph
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_pivot
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_summary_employee
msgid "Time Off Summary"
msgstr "Resumen de tiempo personal"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_employee_type_report
#: model:ir.model,name:hr_holidays.model_hr_leave_report
msgid "Time Off Summary / Report"
msgstr "Resumen / reporte de tiempo personal"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holiday_status_view_kanban
msgid "Time Off Taken:"
msgstr "Tiempo personal tomado:"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_leave_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__holiday_status_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_plan__time_off_type_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__holiday_status_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__name
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_status_normal_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Time Off Type"
msgstr "Tipo de tiempo personal"

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.open_view_holiday_status
#: model:ir.ui.menu,name:hr_holidays.hr_holidays_status_menu_configuration
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holidays_status_filter
msgid "Time Off Types"
msgstr "Tipos de tiempo personal"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Time Off of Your Team Member"
msgstr "Tiempo personal del miembro de su equipo"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_department__leave_to_approve_count
msgid "Time Off to Approve"
msgstr "Tiempo personal por aprobar"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Time Off."
msgstr "Tiempo personal."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__leaves_taken
msgid "Time off Already Taken"
msgstr "Tiempo personal ya tomado"

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.act_hr_employee_holiday_request
msgid "Time off Analysis"
msgstr "Análisis de tiempo personal"

#. module: hr_holidays
#: model:ir.actions.server,name:hr_holidays.action_hr_holidays_by_employee_and_type_report
msgid "Time off Analysis by Employee and Time Off Type"
msgstr "Análisis de tiempo personal por empleado y tipo de tiempo personal"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
msgid "Time off Taken/Total Allocated"
msgstr "Tiempo personal tomado/total asignado"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Time off of people you are manager of"
msgstr "Tiempo personal de las personas que gestiona"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"Time off request must be confirmed (\"To Approve\") in order to approve it."
msgstr "Se debe confirmar el tiempo personal (\"Por aprobar\") para aprobarlo."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Time off request must be confirmed in order to approve it."
msgstr "La solicitud de tiempo personal debe confirmarse para aprobarla."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Time off request must be confirmed or validated in order to refuse it."
msgstr ""
"La solicitud de tiempo personal debe confirmarse o validarse para "
"rechazarla."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"Time off request must be in Draft state (\"To Submit\") in order to confirm "
"it."
msgstr ""
"La solicitud de tiempo personal debe estar en estado borrador (\"Por "
"enviar\") para confirmarla."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"Time off request state must be \"Refused\" or \"To Approve\" in order to be "
"reset to draft."
msgstr ""
"El estado de solicitud de tiempo personal debe ser \"Rechazada\" o \"Por "
"aprobar\" para restablecer a borrador"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__tz
msgid "Timezone"
msgstr "Zona horaria"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_report_calendar__stop_datetime
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
msgid "To"
msgstr "A"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__confirm
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
#, python-format
msgid "To Approve"
msgstr "Por aprobar"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__leave_date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__leave_date_to
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__leave_date_to
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__leave_date_to
msgid "To Date"
msgstr "Hasta la fecha"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave__state__draft
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_allocation__state__draft
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_employee_type_report__state__draft
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report__state__draft
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_report_calendar__state__draft
msgid "To Submit"
msgstr "Por enviar"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_activity
msgid "To:"
msgstr "A:"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Today Activities"
msgstr "Actividades de hoy"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__allocations_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__allocations_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__allocations_count
msgid "Total number of allocations"
msgstr "Número total de asignaciones"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__allocation_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__allocation_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__allocation_count
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__allocation_count
msgid "Total number of days allocated."
msgstr "Número total de días asignados."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee__allocation_used_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_base__allocation_used_count
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_public__allocation_used_count
#: model:ir.model.fields,field_description:hr_holidays.field_res_users__allocation_used_count
msgid "Total number of days off used"
msgstr "Número total de días de tiempo personal utilizados"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_employee__remaining_leaves
#: model:ir.model.fields,help:hr_holidays.field_hr_employee_base__remaining_leaves
#: model:ir.model.fields,help:hr_holidays.field_hr_employee_public__remaining_leaves
msgid ""
"Total number of paid time off allocated to this employee, change this value "
"to create allocation/time off request. Total based on all the time off types"
" without overriding limit."
msgstr ""
"Número total de tiempo personal pagado asignado a este empleado, cambie este"
" valor para crear una solicitud de asignación/tiempo personal. Total basado "
"en todos los tipos de tiempo personal sin límite superior."

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__action_with_unused_accruals__postponed
msgid "Transferred to the next year"
msgstr "Transferido al próximo año"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__tue
msgid "Tuesday"
msgstr "Martes"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__bimonthly
msgid "Twice a month"
msgstr "Dos veces al mes"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__biyearly
msgid "Twice a year"
msgstr "Dos veces al año"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter_report
msgid "Type"
msgstr "Tipo"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave__activity_exception_decoration
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_allocation__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de actividad de excepción registrada."

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__tz
msgid "Tz"
msgstr "Zona horaria"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__tz_mismatch
msgid "Tz Mismatch"
msgstr "Discordancia de zona horaria"

#. module: hr_holidays
#: model:hr.leave.type,name:hr_holidays.holiday_status_unpaid
msgid "Unpaid"
msgstr "Sin pagar"

#. module: hr_holidays
#: model:mail.message.subtype,description:hr_holidays.mt_leave_unpaid
#: model:mail.message.subtype,name:hr_holidays.mt_leave_unpaid
msgid "Unpaid Time Off"
msgstr "Tiempo personal sin pagar"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_unread
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_unread
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_leave_allocation_filter
msgid "Unread Messages"
msgstr "Mensajes sin leer"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__message_unread_counter
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Número de mensajes sin leer"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__user_id
msgid "User"
msgstr "Usuario"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#, python-format
msgid "User is away"
msgstr "El usuario no está"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#, python-format
msgid "User is online"
msgstr "El usuario está en línea"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#, python-format
msgid "User is out of office"
msgstr "El usuario está fuera de la oficina"

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_res_users
msgid "Users"
msgstr "Usuarios"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_tree
msgid "Validate"
msgstr "Validar"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/xml/time_off_calendar.xml:0
#, python-format
msgid "Validated"
msgstr "Validado"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave__validation_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_allocation__validation_type
msgid "Validation Type"
msgstr "Tipo de validación"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "Validity Period"
msgstr "Periodo de validez "

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Validity Start"
msgstr "Iniciar validez"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_tree
msgid "Validity Stop"
msgstr "Detener validez"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__color_name__violet
msgid "Violet"
msgstr "Violeta"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__virtual_remaining_leaves
msgid "Virtual Remaining Time Off"
msgstr "Tiempo personal restante virtual"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_type__virtual_leaves_taken
msgid "Virtual Time Off Already Taken"
msgstr "Tiempo personal virtual ya tomado"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__confirm
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__confirm
msgid "Waiting Approval"
msgstr "En espera de aprobación"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee__current_leave_state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_base__current_leave_state__validate1
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_employee_public__current_leave_state__validate1
msgid "Waiting Second Approval"
msgstr "En espera de la segunda aprobación"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "Waiting for Approval"
msgstr "En espera de aprobación"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__week_day__wed
msgid "Wednesday"
msgstr "Miércoles"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__weekly
msgid "Weekly"
msgstr "Semanalmente"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__color_name__wheat
msgid "Wheat"
msgstr "Trigo"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_leave_type__time_type
msgid ""
"Whether this should be computed as a holiday or as work time (eg: formation)"
msgstr ""
"Si esto debe calcularse como vacaciones o como tiempo de trabajo (por "
"ejemplo, formación)"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__frequency__yearly
msgid "Yearly"
msgstr "Anualmente"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__yearly_day
msgid "Yearly Day"
msgstr "Día anual"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__yearly_day_display
msgid "Yearly Day Display"
msgstr "Pantalla de día anual"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__yearly_month
msgid "Yearly Month"
msgstr "Mes anual"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_type__requires_allocation__yes
msgid "Yes"
msgstr "Sí"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "You can not have 2 time off that overlaps on the same day."
msgstr "No puede tener 2 tiempos personales que se traslapen el mismo día."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"You can not set 2 time off that overlaps on the same day for the same "
"employee."
msgstr ""
"No puede establecer 2 tiempos personales que se traslapen el mismo día para "
"el mismo empleado."

#. module: hr_holidays
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_accrual_level_start_count_check
msgid "You can not start an accrual in the past."
msgstr "No puede iniciar una acumulación en el pasado."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "You cannot delete a time off which is in %s state"
msgstr "No puede eliminar un tiempo personal que está en el estado %s "

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "You cannot delete a time off which is in the past"
msgstr "No puede eliminar un tiempo personal en el pasado"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid "You cannot delete an allocation request which is in %s state."
msgstr "No puede eliminar una solicitud de asignación que está en estado %s."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"You cannot delete an allocation request which has some validated leaves."
msgstr ""
"No puede eliminar una solicitud de asignación que tiene permisos validados."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"You cannot first approve a time off for %s, because you are not his time off"
" manager"
msgstr ""
"No puede ser el primero en aprobar un tiempo personal para %s porque no es "
"su gerente de tiempo personal"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"You don't have the rights to apply second approval on a time off request"
msgstr ""
"No tiene acceso para aplicar la segunda aprobación en una solicitud de "
"tiempo personal"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"You have several allocations for those type and period.\n"
"Please split your request to fit in their number of days."
msgstr ""
"Tiene varias asignaciones para ese tipo y periodo.\n"
"Divida su solicitud para que se ajuste a su número de días."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "You must be %s\\'s Manager to approve this leave"
msgstr "Debe ser el gerente de %s para aprobar este permiso"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"You must be either %s's manager or Time off Manager to approve this leave"
msgstr ""
"Para aprobar este permiso usted debe ser ya sea el gerente de %s o el "
"gerente de tiempo personal"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"You must be either %s's manager or time off manager to approve this time off"
msgstr ""
"Debe ser el gerente de %so el gerente de tiempo personal para aprobar esta "
"tiempo personal"

#. module: hr_holidays
#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: model:ir.model.constraint,message:hr_holidays.constraint_hr_leave_accrual_level_added_value_greater_than_zero
msgid "You must give a rate greater than 0 in accrual plan levels."
msgstr "Debe dar una tasa mayor a 0 en niveles de plan de acumulación."

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"You must have manager rights to modify/validate a time off that already "
"begun"
msgstr ""
"Debe tener derechos de gerente para modificar/validar un tiempo personal que"
" ya comenzó"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Your %(leave_type)s planned on %(date)s has been accepted"
msgstr "Se aceptó su %(leave_type)s planeado para el %(date)s"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#, python-format
msgid "Your %(leave_type)s planned on %(date)s has been refused"
msgstr "Se rechazó su %(leave_type)s planeado para el %(date)s"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "after allocation date"
msgstr "después de la fecha de asignación"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "after allocation start date"
msgstr "después de la fecha de inicio de la asignación"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "and on the"
msgstr "y en el "

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_available_holidays_report_tree
msgid "by Employee"
msgstr "por empleado"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_report_employee_time_off
msgid "by Employee and Time Off Type"
msgstr "por empleado y tipo de tiempo personal"

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_summary_all
msgid "by Type"
msgstr "por tipo"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "day of the month"
msgstr "día del mes"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/xml/leave_stats_templates.xml:0
#: code:addons/hr_holidays/static/src/xml/leave_stats_templates.xml:0
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__start_type__day
#, python-format
msgid "day(s)"
msgstr "día(s)"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_activity
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_activity
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
#, python-format
msgid "days"
msgstr "días"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "days of the months"
msgstr "días del mes"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_form_manager
msgid "e.g. Time Off type (From validity start to validity end / no limit)"
msgstr ""
"Por ejemplo, Tipo de tiempo personal (desde el inicio de la validez hasta el"
" final de la validez / sin límite)"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave.py:0
#: code:addons/hr_holidays/models/hr_leave_allocation.py:0
#, python-format
msgid "hours"
msgstr "horas"

#. module: hr_holidays
#. openerp-web
#: code:addons/hr_holidays/static/src/xml/leave_stats_templates.xml:0
#: code:addons/hr_holidays/static/src/xml/leave_stats_templates.xml:0
#, python-format
msgid "in"
msgstr "en"

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_leave_accrual_plan_level.py:0
#, python-format
msgid "last day"
msgstr "último día"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "lost"
msgstr "perdido"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__start_type__month
msgid "month(s)"
msgstr "mes(es)"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "of"
msgstr "de"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "of the"
msgstr "del"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
msgid "of the month"
msgstr "del mes"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "on"
msgstr "de"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_level_view_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "on the"
msgstr "en el"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_accrual_plan_view_form
msgid "postponed"
msgstr "pospuesto"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "refused"
msgstr "rechazado"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_leave_accrual_level__sequence
msgid "sequence"
msgstr "secuencia"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "to"
msgstr "a"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_report_calendar_view_search
msgid "validate"
msgstr "validar"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_allocation_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_leave_view_kanban
msgid "validated"
msgstr "validado"

#. module: hr_holidays
#: model:ir.model.fields.selection,name:hr_holidays.selection__hr_leave_accrual_level__start_type__year
msgid "year(s)"
msgstr "año(s)"
