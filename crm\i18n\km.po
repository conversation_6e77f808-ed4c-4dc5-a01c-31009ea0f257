# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * crm
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON> <PERSON>uphorn <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 06:48+0000\n"
"PO-Revision-Date: 2018-10-08 06:48+0000\n"
"Last-Translator: <PERSON> Souphorn <<EMAIL>>, 2018\n"
"Language-Team: Khmer (https://www.transifex.com/odoo/teams/41243/km/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: km\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__meeting_count
#: model:ir.model.fields,field_description:crm.field_res_partner__meeting_count
msgid "# Meetings"
msgstr "# ការប្រជំុ"

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_0
msgid "% endif"
msgstr ""

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_0
msgid ""
"% set email = object.env['crm.team'].search([('alias_name','!=', False)],limit=1).alias_id.display_name\n"
"    % if email\n"
"    <strong style=\"font-size: 16px;\">Try the mail gateway</strong>"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tour.js:28
#, python-format
msgid ""
"<b>Choose a name</b> for your opportunity, example: <i>'Need a new "
"website'</i>"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tour.js:36
#, python-format
msgid ""
"<b>Drag &amp; drop opportunities</b> between columns as you progress in your"
" sales cycle."
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tour.js:86
#, python-format
msgid ""
"<b>Invite coworkers</b> via email.<br/><i>Enter one email per line.</i>"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_team.py:139
#, python-format
msgid ""
"<p class='o_view_nocontent_smiling_face'>Add new opportunities</p><p>\n"
"    Looks like you are not a member of a Sales Team. You should add yourself\n"
"    as a member of one of the Sales Team.\n"
"</p>"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tour.js:67
#, python-format
msgid ""
"<p><b>Send messages</b> to your prospect and get replies automatically "
"attached to this opportunity.</p><p class=\"mb0\">Type <i>'@'</i> to mention"
" people - it's like cc-ing on emails.</p>"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tour.js:52
#, python-format
msgid ""
"<p>You will be able to customize your followup activities. "
"Examples:</p><ol><li>introductory email</li><li>call 10 days "
"after</li><li>second call 3 days after, ...</li></ol><p "
"class='mb0'><i>Select a standard activity for now.</i></p>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('meeting_count', '&lt;', 2)]}\"> Meetings</span>\n"
"                                    <span class=\"o_stat_text\" attrs=\"{'invisible': [('meeting_count', '&gt;', 1)]}\"> Meeting</span>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "<span class=\"oe_grey\"> at </span>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "Accept Emails From"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_needaction
msgid "Action Needed"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__active
#: model:ir.model.fields,field_description:crm.field_crm_lead__active
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__active
msgid "Active"
msgstr "សកម្ម"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_ids
#: model:ir.ui.menu,name:crm.crm_activity_report_menu
msgid "Activities"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_activity_report_action
#: model:ir.actions.act_window,name:crm.crm_activity_report_action_team
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_graph
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_pivot
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_team_salesteams_view_kanban
msgid "Activities Analysis"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_users__target_sales_done
msgid "Activities Done Target"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Activities Todo"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_state
msgid "Activity State"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__mail_activity_type_id
msgid "Activity Type"
msgstr ""

#. module: crm
#: model:ir.ui.menu,name:crm.crm_team_menu_config_activity_types
msgid "Activity Types"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:933
#, python-format
msgid "Add a new lead"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Add a qualification step before creating an opportunity"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "Address"
msgstr "អាសយដ្ឋាន"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_id
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Alias"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
msgid "Analysis"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__deduplicate
msgid "Apply deduplication"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.merge_opportunity_form
msgid "Assign opportunities to"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Assign these opportunities to"
msgstr "កំណត់ឱកាសទាំងនេះទៅអោយ"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
msgid "Assign this opportunity to"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_open
msgid "Assignation Date"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "Avg. of Probability"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__is_blacklisted
msgid "Blacklist"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:400
#, python-format
msgid "Boom! Team record for the past 30 days."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_bounce
msgid "Bounce"
msgstr ""

#. module: crm
#: model:ir.ui.menu,name:crm.crm_menu_root
#: model_terms:ir.ui.view,arch_db:crm.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "CRM"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_activity_report
msgid "CRM Activity Analysis"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_stage
msgid "CRM Stages"
msgstr ""

#. module: crm
#: model:mail.activity.type,name:crm.mail_activity_demo_call_demo
msgid "Call for Demo"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__campaign_id
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Campaign"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_lost_view_form
#: model_terms:ir.ui.view,arch_db:crm.merge_opportunity_form
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Cancel"
msgstr "លុបចោល"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_team_act_tree
msgid "Cases by Sales Team"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage__on_change
msgid "Change Probability Automatically"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_form
msgid "Channel"
msgstr "ឆានែល"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__use_leads
msgid ""
"Check this box to filter and qualify incoming requests as leads before "
"converting them into opportunities and assigning them to a salesperson."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__use_opportunities
msgid "Check this box to manage a presales process with opportunities."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__city
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "City"
msgstr "ក្រុង"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__tag_ids
msgid ""
"Classify and analyze your lead/opportunity categories like: Training, "
"Service"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tour.js:32
#, python-format
msgid "Click here to <b>add your opportunity</b>."
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tour.js:24
#, python-format
msgid ""
"Click here to <b>create your first opportunity</b> and add it to your "
"pipeline."
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tour.js:60
#, python-format
msgid "Click on the opportunity to zoom in."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_closed
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Closed Date"
msgstr ""

#. module: crm
#: code:addons/crm/wizard/crm_lead_to_opportunity.py:87
#, python-format
msgid "Closed/Dead leads cannot be converted into opportunities."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__color
#: model:ir.model.fields,field_description:crm.field_crm_lead_tag__color
msgid "Color Index"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__company_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__company_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Company"
msgstr "ក្រុមហ៊ុន"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
msgid "Company Name"
msgstr "ឈ្មោះក្រុមហ៊ុន"

#. module: crm
#: model:ir.model,name:crm.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: crm
#: model:ir.ui.menu,name:crm.crm_menu_config
msgid "Configuration"
msgstr "កំណត់ផ្លាស់ប្តូរ"

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tour.js:76
#: code:addons/crm/static/src/js/tour.js:81
#, python-format
msgid "Configuration options are available in the Settings app."
msgstr ""

#. module: crm
#: model:crm.lead.tag,name:crm.categ_oppor7
msgid "Consulting"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_res_partner
msgid "Contact"
msgstr "ទំនាក់ទំនង"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__contact_name
msgid "Contact Name"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Contacts"
msgstr "ទំនាក់ទំនង"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__dashboard_graph_model
msgid "Content"
msgstr "មាតិកា"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__name
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__name
msgid "Conversion Action"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_conversion
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Conversion Date"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Conversion Date from Lead to Opportunity"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Conversion Options"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_lead2opportunity_partner_mass
msgid "Convert Lead to Opportunity (in mass)"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_lead2opportunity_partner
msgid "Convert Lead to Opportunity (not in mass)"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Convert to Opportunities"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Convert to Opportunity"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.action_crm_send_mass_convert
msgid "Convert to opportunities"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:1116
#: selection:crm.lead2opportunity.partner,name:0
#: selection:crm.lead2opportunity.partner.mass,name:0
#: model:ir.actions.act_window,name:crm.action_crm_lead2opportunity_partner
#, python-format
msgid "Convert to opportunity"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid ""
"Convert visitors of your website into leads in the CRM. We do data "
"enrichment based on their IP address."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__country_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__country_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Country"
msgstr "ប្រទេស"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_create_opportunity_simplified
msgid "Create"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_create_opportunity_simplified
msgid "Create & Edit"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__module_crm_reveal
msgid "Create Leads/Opportunities from your website's traffic"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
msgid "Create Opportunity"
msgstr ""

#. module: crm
#: selection:crm.lead2opportunity.partner,action:0
#: selection:crm.partner.binding,action:0
msgid "Create a new customer"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_case_form_view_salesteams_lead
#: model_terms:ir.actions.act_window,help:crm.crm_case_form_view_salesteams_opportunity
msgid "Create a new lead"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lead_tag_action
msgid "Create a new tag for your opportunities"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.create_opportunity_simplified
#: model_terms:ir.ui.view,arch_db:crm.view_create_opportunity_simplified
msgid "Create an Opportunity"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.relate_partner_opportunities
msgid "Create an new opportunity related to this customer"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:935
#, python-format
msgid "Create an opportunity in your pipeline"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lead_tag_action
msgid ""
"Create tags that fit your business (product structure, sales type, etc.) to "
"better manage and tack your opportunities."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__author_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Created By"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_tag__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_partner_binding__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_stage__create_uid
msgid "Created by"
msgstr "បង្កើតដោយ"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_tag__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__create_date
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__create_date
#: model:ir.model.fields,field_description:crm.field_crm_partner_binding__create_date
#: model:ir.model.fields,field_description:crm.field_crm_stage__create_date
msgid "Created on"
msgstr "បង្កើតនៅ"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Creation Date"
msgstr "ថ្ងៃបង្កើត"

#. module: crm
#: model:ir.actions.server,name:crm.action_your_pipeline
msgid "Crm: My Pipeline"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__company_currency
msgid "Currency"
msgstr "រូបិយវត្ថុ"

#. module: crm
#: code:addons/crm/models/crm_lead.py:1168
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__partner_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__partner_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__partner_id
#: model:ir.model.fields,field_description:crm.field_crm_partner_binding__partner_id
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
#, python-format
msgid "Customer"
msgstr "អតិថិជន"

#. module: crm
#: code:addons/crm/models/crm_lead.py:1170
#, python-format
msgid "Customer Email"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__partner_name
msgid "Customer Name"
msgstr ""

#. module: crm
#: model:ir.ui.menu,name:crm.res_partner_menu_customer
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Customers"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__date
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Date"
msgstr "កាលបរិច្ឆេត"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Date Closed"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__day_open
msgid "Days to Assign"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__day_close
msgid "Days to Close"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__crm_alias_prefix
msgid "Default Alias Name for Leads"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lost_reason_action
msgid "Define a new lost reason"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_kanban_view_leads
msgid "Delete"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
msgid "Describe the lead..."
msgstr ""

#. module: crm
#: model:crm.lead.tag,name:crm.categ_oppor5
msgid "Design"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_digest_digest
msgid "Digest"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_create_opportunity_simplified
msgid "Discard"
msgstr "បោះបង់"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead_tag__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__display_name
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__display_name
#: model:ir.model.fields,field_description:crm.field_crm_partner_binding__display_name
#: model:ir.model.fields,field_description:crm.field_crm_stage__display_name
msgid "Display Name"
msgstr "ឈ្មោះសំរាប់បង្ហាញ"

#. module: crm
#: code:addons/crm/models/digest.py:18 code:addons/crm/models/digest.py:29
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr ""

#. module: crm
#: selection:crm.lead2opportunity.partner,action:0
#: selection:crm.lead2opportunity.partner.mass,action:0
#: selection:crm.partner.binding,action:0
msgid "Do not link to a customer"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_kanban_view_leads
msgid "Dropdown menu"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_kanban_view_leads
msgid "Edit"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__email_from
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "Email"
msgstr "អុីម៉ែល"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "Email Alias"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__email_from
msgid "Email address of the contact"
msgstr ""

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_0
msgid ""
"Email sent to <strong>${email}</strong> generate opportunities in your "
"pipeline.<br>"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid ""
"Emails received to that address generate new leads not assigned to any Sales"
" Team yet. This can be made when converting them into opportunities. "
"Incoming emails can be automatically assigned to specific Sales Teams. To do"
" so, set an email alias on the Sales Team."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_stage__requirements
msgid ""
"Enter here the internal requirements for this stage (ex: Offer sent to "
"customer). It will appear as a tooltip over the stage's name."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__date_deadline
msgid "Estimate of the date on which the opportunity will be won."
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_calendar_event
msgid "Event"
msgstr "ព្រិត្តិការណ៍"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_deadline
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Expected Closing"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Expected Closing Date"
msgstr ""

#. module: crm
#: selection:crm.team,dashboard_graph_group_pipeline:0
msgid "Expected Closing Day"
msgstr ""

#. module: crm
#: selection:crm.team,dashboard_graph_group_pipeline:0
msgid "Expected Closing Month"
msgstr ""

#. module: crm
#: selection:crm.team,dashboard_graph_group_pipeline:0
msgid "Expected Closing Week"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__planned_revenue
msgid "Expected Revenue"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "Expected Revenues"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__dashboard_graph_period_pipeline
msgid "Expected to Close"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_stage__legend_priority
msgid ""
"Explanation text to help users using the star and priority mechanism on "
"stages or issues that are in this stage."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Extended Filters"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
msgid "Extra Info"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage__fold
msgid "Folded in Pipeline"
msgstr ""

#. module: crm
#: model:mail.activity.type,name:crm.mail_activity_demo_followup_quote
msgid "Follow-up Quote"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_follower_ids
msgid "Followers"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_channel_ids
msgid "Followers (Channels)"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "Followup"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__force_assignation
msgid "Force assignation"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Format phone numbers based on national conventions"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:606
#, python-format
msgid "From %s : %s"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Future Activities"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid ""
"Generate leads from incoming emails and assign them\n"
"                                    to a Sales Team manually"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_lead_lost
msgid "Get Lost Reason"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_stage_form
msgid "Give your team the requirements to move an opportunity to this stage."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__email_cc
msgid "Global CC"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:398
#, python-format
msgid "Go, go, go! Congrats for your first deal."
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tour.js:90
#: code:addons/crm/static/src/js/tour.js:95
#, python-format
msgid "Good job! You completed the tour of the CRM app."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Group By"
msgstr "ជា​ក្រុម​តាម"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__dashboard_graph_group_pipeline
msgid "Grouping Method"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lead_action_activities
msgid ""
"Here is the list of your next activities. Those are linked to your opportunities.\n"
"                   To set a next activity, go on an opportunity and add one. It will then appear in this list."
msgstr ""

#. module: crm
#: selection:crm.lead,priority:0
msgid "High"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__dashboard_graph_group_pipeline
msgid "How this channel's dashboard graph will group the results."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__id
#: model:ir.model.fields,field_description:crm.field_crm_lead__id
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__id
#: model:ir.model.fields,field_description:crm.field_crm_lead_tag__id
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__id
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__id
#: model:ir.model.fields,field_description:crm.field_crm_partner_binding__id
#: model:ir.model.fields,field_description:crm.field_crm_stage__id
msgid "ID"
msgstr "ID"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__message_unread
msgid "If checked new messages require your attention."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_res_partner__team_id
msgid ""
"If set, this Sales Team will be used for sales and assignations related to "
"this partner"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__is_blacklisted
#: model:ir.model.fields,help:crm.field_crm_lead__partner_is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead2opportunity_partner_mass__force_assignation
msgid "If unchecked, this will leave the salesman of duplicated opportunities"
msgstr ""

#. module: crm
#: model:ir.ui.menu,name:crm.menu_import_crm
msgid "Import & Synchronize"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:1230
#, python-format
msgid "Import Template for Leads & Opportunities"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_search
msgid "Include archived"
msgstr ""

#. module: crm
#: model:crm.lead.tag,name:crm.categ_oppor4
msgid "Information"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "Initial Contact Information"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "Internal Notes"
msgstr ""

#. module: crm
#: selection:crm.team,dashboard_graph_model:0
msgid "Invoices"
msgstr "វិកិយប័ត្រ"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__function
msgid "Job Position"
msgstr "តំណែង"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__kanban_state
msgid "Kanban State"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_digest_digest__kpi_crm_lead_created_value
msgid "Kpi Crm Lead Created Value"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_digest_digest__kpi_crm_opportunities_won_value
msgid "Kpi Crm Opportunities Won Value"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_action_last
msgid "Last Action"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report____last_update
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner____last_update
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass____last_update
#: model:ir.model.fields,field_description:crm.field_crm_lead____last_update
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost____last_update
#: model:ir.model.fields,field_description:crm.field_crm_lead_tag____last_update
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason____last_update
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity____last_update
#: model:ir.model.fields,field_description:crm.field_crm_partner_binding____last_update
#: model:ir.model.fields,field_description:crm.field_crm_stage____last_update
msgid "Last Modified on"
msgstr "កាលបរិច្ឆេតកែប្រែចុងក្រោយ"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_last_stage_update
msgid "Last Stage Update"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_tag__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_partner_binding__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_stage__write_uid
msgid "Last Updated by"
msgstr "ផ្លាស់ប្តូរចុងក្រោយ"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_tag__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__write_date
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__write_date
#: model:ir.model.fields,field_description:crm.field_crm_partner_binding__write_date
#: model:ir.model.fields,field_description:crm.field_crm_stage__write_date
msgid "Last Updated on"
msgstr "ផ្លាស់ប្តូរចុងក្រោយ"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Late Activities"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:913
#: selection:crm.activity.report,lead_type:0 selection:crm.lead,type:0
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__lead_id
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#, python-format
msgid "Lead"
msgstr "Lead"

#. module: crm
#: model:ir.model,name:crm.model_crm_lead_tag
msgid "Lead Tag"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_tag_form
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_tag_tree
msgid "Lead Tags"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_lead
msgid "Lead/Opportunity"
msgstr "Leads/ឱកាស"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_case_form_view_salesteams_lead
#: model:ir.actions.act_window,name:crm.crm_lead_all_leads
#: model:ir.model.fields,field_description:crm.field_crm_team__use_leads
#: model:ir.model.fields,field_description:crm.field_res_config_settings__group_use_lead
#: model:ir.ui.menu,name:crm.crm_menu_leads
#: model:ir.ui.menu,name:crm.crm_opportunity_report_menu_lead
#: model:ir.ui.menu,name:crm.menu_crm_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Leads"
msgstr "Lead"

#. module: crm
#: model:ir.actions.act_window,name:crm.action_report_crm_lead_salesteam
#: model:ir.actions.act_window,name:crm.crm_opportunity_report_action_lead
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_graph_lead
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_pivot_lead
msgid "Leads Analysis"
msgstr "វិភាគ Lead"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.action_report_crm_lead_salesteam
msgid ""
"Leads Analysis allows you to check different CRM related information like "
"the treatment delays or number of leads per state. You can sort out your "
"leads analysis by different groups to get accurate grained analysis."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
msgid "Leads Form"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_calendar_view_leads
msgid "Leads Generation"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Leads that are assigned to me"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid ""
"Leads that you selected that have duplicates. If the list is empty, it means"
" that no duplicates were found"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Leads with existing duplicates (for information)"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__opportunity_ids
msgid "Leads/Opportunities"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tour.js:47
#, python-format
msgid "Let's schedule an activity."
msgstr ""

#. module: crm
#: selection:crm.lead2opportunity.partner,action:0
#: selection:crm.partner.binding,action:0
msgid "Link to an existing customer"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__partner_id
msgid ""
"Linked partner (optional). Usually created when converting the lead. You can"
" find a partner by its Name, TIN, Email or Internal Reference."
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:1122
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
#, python-format
msgid "Lost"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lead_lost_action
#: model:ir.model.fields,field_description:crm.field_crm_lead__lost_reason
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__lost_reason_id
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_lost_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Lost Reason"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lost_reason_action
#: model:ir.ui.menu,name:crm.menu_crm_lost_reason
msgid "Lost Reasons"
msgstr ""

#. module: crm
#: selection:crm.lead,priority:0
msgid "Low"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: crm
#: model:mail.activity.type,name:crm.mail_activity_demo_make_quote
msgid "Make Quote"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__generate_lead_from_alias
msgid "Manual Assignation of Emails"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Manual Assignation of Incoming Emails"
msgstr ""

#. module: crm
#: model:ir.actions.server,name:crm.action_mark_activities_done
msgid "Mark All Activities as Done"
msgstr ""

#. module: crm
#: model:ir.actions.server,name:crm.action_mark_late_activities_done
msgid "Mark Late Activities as Done"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "Mark Lost"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "Mark Won"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
msgid "Mark as Lost"
msgstr ""

#. module: crm
#: model:ir.actions.server,name:crm.action_mark_as_lost
msgid "Mark as lost"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "Marketing"
msgstr ""

#. module: crm
#: selection:crm.lead,priority:0
#: model:ir.model.fields,field_description:crm.field_crm_lead__medium_id
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Medium"
msgstr "Medium"

#. module: crm
#: code:addons/crm/models/crm_lead.py:958
#, python-format
msgid "Meeting scheduled at '%s'<br> Subject: %s <br> Duration: %s hour(s)"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.act_crm_opportunity_calendar_event_new
#: model:ir.actions.act_window,name:crm.calendar_event_partner
#: model:ir.model.fields,field_description:crm.field_res_partner__meeting_ids
#: model_terms:ir.ui.view,arch_db:crm.view_partners_form_crm1
msgid "Meetings"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.action_merge_opportunities
#: model_terms:ir.ui.view,arch_db:crm.merge_opportunity_form
msgid "Merge"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.merge_opportunity_form
msgid "Merge Leads/Opportunities"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_merge_opportunity
msgid "Merge Opportunities"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.merge_opportunity_act
msgid "Merge leads/opportunities"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead2opportunity_partner_mass__deduplicate
msgid "Merge with existing leads/opportunities of each partner"
msgstr ""

#. module: crm
#: selection:crm.lead2opportunity.partner,name:0
#: selection:crm.lead2opportunity.partner.mass,name:0
msgid "Merge with existing opportunities"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:556
#, python-format
msgid "Merged lead"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:588
#, python-format
msgid "Merged leads"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:588
#, python-format
msgid "Merged opportunities"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:556
#, python-format
msgid "Merged opportunity"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_ids
msgid "Messages"
msgstr "សារ"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "Misc"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__mobile
msgid "Mobile"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "My Activities"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "My Leads"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "My Opportunities"
msgstr ""

#. module: crm
#: model:ir.ui.menu,name:crm.menu_crm_opportunities
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "My Pipeline"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_tag__name
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__name
msgid "Name"
msgstr "ឈ្មោះ"

#. module: crm
#: model:crm.stage,name:crm.stage_lead1
msgid "New"
msgstr "ថ្មី"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_digest_digest__kpi_crm_lead_created
msgid "New Leads/Opportunities"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.action_opportunity_form
msgid "New Opportunity"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lead_action_activities
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_tree_activity
msgid "Next Activities"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: crm
#: selection:crm.lead,kanban_state:0
msgid "Next activity is planned"
msgstr ""

#. module: crm
#: selection:crm.lead,kanban_state:0
msgid "Next activity late"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:1190
#, python-format
msgid "No Subject"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lead_action_activities
msgid "No next activity"
msgstr ""

#. module: crm
#: selection:crm.lead,kanban_state:0
msgid "No next activity planned"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "No salesperson"
msgstr ""

#. module: crm
#: model:crm.lost.reason,name:crm.lost_reason_3
msgid "Not enough stock"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__description
msgid "Notes"
msgstr "កំណត់សម្គាល់"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_has_error_counter
msgid "Number of error"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__opportunities_count
msgid "Number of open opportunities"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__message_unread_counter
msgid "Number of unread messages"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_case_form_view_salesteams_opportunity
msgid ""
"Odoo helps you keep track of your sales pipeline to follow\n"
"                    up potential sales and better forecast your future revenues."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_salesteams_view_kanban
msgid "Open Opportunities"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_salesteams_view_kanban
msgid "Open Opportunity"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_lost_reason
msgid "Opp. Lost Reason"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_case_form_view_salesteams_opportunity
#: model:ir.actions.act_window,name:crm.crm_lead_opportunities
#: model:ir.actions.act_window,name:crm.relate_partner_opportunities
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__opportunity_ids
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__opportunity_ids
#: model:ir.model.fields,field_description:crm.field_res_partner__opportunity_ids
#: model:ir.ui.menu,name:crm.menu_crm_config_opportunity
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_graph
#: model_terms:ir.ui.view,arch_db:crm.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
#: model_terms:ir.ui.view,arch_db:crm.view_partners_form_crm1
msgid "Opportunities"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Opportunities Analysis"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.action_report_crm_opportunity_salesteam
msgid ""
"Opportunities Analysis gives you an instant access to your opportunities "
"with information such as the expected revenue, planned cost, missed "
"deadlines or the number of interactions per opportunity. This report is "
"mainly used by the sales manager in order to do the periodic review with the"
" channels of the sales pipeline."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__opportunities_amount
msgid "Opportunities Revenues"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_digest_digest__kpi_crm_opportunities_won
msgid "Opportunities Won"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Opportunities that are assigned to me"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Opportunities with a date of Expected Closing which is in the past"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:446
#: code:addons/crm/models/crm_lead.py:888
#: selection:crm.activity.report,lead_type:0 selection:crm.lead,type:0
#: model:ir.model.fields,field_description:crm.field_calendar_event__opportunity_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__name
#: model:ir.model.fields,field_description:crm.field_res_partner__opportunity_count
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
#, python-format
msgid "Opportunity"
msgstr ""

#. module: crm
#: model:mail.message.subtype,name:crm.mt_lead_create
#: model:mail.message.subtype,name:crm.mt_salesteam_lead
msgid "Opportunity Created"
msgstr ""

#. module: crm
#: model:mail.message.subtype,name:crm.mt_lead_lost
#: model:mail.message.subtype,name:crm.mt_salesteam_lead_lost
msgid "Opportunity Lost"
msgstr ""

#. module: crm
#: model:mail.message.subtype,name:crm.mt_salesteam_lead_stage
msgid "Opportunity Stage Changed"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_create_opportunity_simplified
msgid "Opportunity Title"
msgstr ""

#. module: crm
#: model:mail.message.subtype,name:crm.mt_lead_won
#: model:mail.message.subtype,name:crm.mt_salesteam_lead_won
msgid "Opportunity Won"
msgstr ""

#. module: crm
#: model:mail.message.subtype,description:crm.mt_lead_create
msgid "Opportunity created"
msgstr ""

#. module: crm
#: model:mail.message.subtype,description:crm.mt_lead_lost
msgid "Opportunity lost"
msgstr ""

#. module: crm
#: model:mail.message.subtype,description:crm.mt_lead_won
msgid "Opportunity won"
msgstr ""

#. module: crm
#: model:crm.lead.tag,name:crm.categ_oppor8
msgid "Other"
msgstr ""

#. module: crm
#: selection:crm.lead,activity_state:0
msgid "Overdue"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Overdue Opportunities"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__partner_address_email
msgid "Partner Contact Email"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__partner_address_name
msgid "Partner Contact Name"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__partner_address_phone
msgid "Partner Contact Phone"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__partner_is_blacklisted
msgid "Partner is blacklisted"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_partner_binding
msgid "Partner linking/binding in CRM wizard"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__partner_id
msgid "Partner/Customer"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__phone
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "Phone"
msgstr "ទូរស័ព្ទ"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__module_crm_phone_validation
msgid "Phone Formatting"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_team.py:155
#: selection:crm.team,dashboard_graph_model:0
#: model:ir.actions.act_window,name:crm.crm_lead_opportunities_tree_view
#: model:ir.model.fields,field_description:crm.field_crm_team__use_opportunities
#: model:ir.ui.menu,name:crm.crm_opportunity_report_menu
#: model:ir.ui.menu,name:crm.menu_crm_config_lead
#, python-format
msgid "Pipeline"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.action_report_crm_opportunity_salesteam
#: model:ir.actions.act_window,name:crm.crm_opportunity_report_action
#: model:ir.actions.act_window,name:crm.crm_opportunity_report_action_graph
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_pivot
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_graph
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_pivot
msgid "Pipeline Analysis"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_opportunity_report_action
msgid ""
"Pipeline Analysis gives you an instant access to\n"
"your opportunities with information such as the expected revenue, planned cost,\n"
"missed deadlines or the number of interactions per opportunity. This report is\n"
"mainly used by the sales manager in order to do the periodic review with the\n"
"teams of the sales pipeline."
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_opportunity_report_action_graph
msgid ""
"Pipeline Analysis gives you an instant access to\n"
"your opportunities with information such as the expected revenue, planned cost,\n"
"missed deadlines or the number of interactions per opportunity. This report is\n"
"mainly used by the sales manager in order to periodically review the pipeline\n"
"with the the Sales Team."
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_team.py:224
#, python-format
msgid "Pipeline: Expected Revenue"
msgstr ""

#. module: crm
#: selection:crm.lead,activity_state:0
msgid "Planned"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:660
#, python-format
msgid ""
"Please select more than one element (lead or opportunity) from the list "
"view."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__priority
msgid "Priority"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage__legend_priority
msgid "Priority Management Explanation"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__probability
#: model:ir.model.fields,field_description:crm.field_crm_lead__probability
msgid "Probability"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage__probability
msgid "Probability (%)"
msgstr ""

#. module: crm
#: model:crm.lead.tag,name:crm.categ_oppor1
msgid "Product"
msgstr "ផលិតផល"

#. module: crm
#: model:crm.stage,name:crm.stage_lead3
msgid "Proposition"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__expected_revenue
msgid "Prorated Revenue"
msgstr ""

#. module: crm
#: model:crm.stage,name:crm.stage_lead2
msgid "Qualified"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tour.js:13
#: code:addons/crm/static/src/js/tour.js:18
#, python-format
msgid ""
"Ready to boost your sales? Your <b>Pipeline</b> can be found here, under the"
" <b>CRM</b> app."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__referred
msgid "Referred By"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__action
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__action
#: model:ir.model.fields,field_description:crm.field_crm_partner_binding__action
msgid "Related Customer"
msgstr ""

#. module: crm
#: model:ir.ui.menu,name:crm.crm_menu_report
msgid "Reporting"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage__requirements
#: model_terms:ir.ui.view,arch_db:crm.crm_stage_form
msgid "Requirements"
msgstr "តម្រូវការ"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "Restore"
msgstr ""

#. module: crm
#: selection:crm.team,dashboard_graph_model:0
#: model:ir.ui.menu,name:crm.crm_menu_sales
msgid "Sales"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Sales Channel"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_crm_team
msgid "Sales Channels"
msgstr "លក់"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__team_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__team_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__team_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__team_id
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__team_id
#: model:ir.model.fields,field_description:crm.field_crm_stage__team_id
#: model:ir.model.fields,field_description:crm.field_res_partner__team_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Sales Team"
msgstr "ក្រុមលក់"

#. module: crm
#: code:addons/crm/models/crm_lead.py:1125
#, python-format
msgid "Sales Team Settings"
msgstr "កំណត់ការលក់"

#. module: crm
#: model:ir.ui.menu,name:crm.crm_team_config
msgid "Sales Teams"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__user_ids
msgid "Salesmen"
msgstr ""

#. module: crm
#: selection:crm.team,dashboard_graph_group_pipeline:0
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__user_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__user_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__user_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__user_id
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__user_id
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Salesperson"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Search Leads"
msgstr "ស្រាវជ្រាវ"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Search Opportunities"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.merge_opportunity_form
msgid "Select Leads/Opportunities"
msgstr "ឧកាស"

#. module: crm
#: model:ir.actions.act_window,name:crm.action_lead_mass_mail
msgid "Send an email"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage__sequence
msgid "Sequence"
msgstr "លំដាប់"

#. module: crm
#: model:crm.lead.tag,name:crm.categ_oppor3
msgid "Services"
msgstr "សេវាកម្ម"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_stage_action
msgid "Set a new stage in your opportunity pipeline"
msgstr "ការកំណត់យុទ្ឋសាស្រ្តនៅក្នុងអាកាស"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_stage__on_change
msgid ""
"Setting this stage will change the probability automatically on the "
"opportunity."
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_config_settings_action
#: model:ir.ui.menu,name:crm.crm_config_settings_menu
msgid "Settings"
msgstr ""

#. module: crm
#: model:res.groups,name:crm.group_use_lead
msgid "Show Lead Menu"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Show all opportunities for which the next action date is before today"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Show only lead"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Show only opportunity"
msgstr ""

#. module: crm
#: model:crm.lead.tag,name:crm.categ_oppor2
msgid "Software"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lost_reason_action
msgid ""
"Some examples of lost reasons: \"We don't have people/skill\", \"Price too "
"high\""
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__source_id
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Source"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_stage__team_id
msgid ""
"Specific team that uses this stage. Other teams will not be able to see or "
"use this stage."
msgstr ""

#. module: crm
#: selection:crm.team,dashboard_graph_group_pipeline:0
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__stage_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__stage_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_stage_form
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Stage"
msgstr ""

#. module: crm
#: model:mail.message.subtype,name:crm.mt_lead_stage
msgid "Stage Changed"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage__name
msgid "Stage Name"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_stage_search
msgid "Stage Search"
msgstr ""

#. module: crm
#: model:mail.message.subtype,description:crm.mt_lead_stage
msgid "Stage changed"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_stage_action
#: model:ir.ui.menu,name:crm.menu_crm_lead_stage_act
#: model_terms:ir.ui.view,arch_db:crm.crm_stage_tree
msgid "Stages"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_stage_action
msgid ""
"Stages allow salespersons to easily track how a specific opportunity\n"
"            is positioned in the sales cycle."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__state_id
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "State"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__street
msgid "Street"
msgstr "ផ្លូវ"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "Street 2..."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "Street..."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__street2
msgid "Street2"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_lost_view_form
msgid "Submit"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__subtype_id
msgid "Subtype"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__subject
msgid "Summary"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Tag"
msgstr "ធែក"

#. module: crm
#: sql_constraint:crm.lead.tag:0
msgid "Tag name already exists !"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lead_tag_action
#: model:ir.model.fields,field_description:crm.field_crm_lead__tag_ids
#: model:ir.ui.menu,name:crm.menu_crm_lead_categ
msgid "Tags"
msgstr "ធែក"

#. module: crm
#: model:ir.ui.menu,name:crm.sales_team_menu_team_pipeline
msgid "Team Pipelines"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_id
msgid ""
"The email address associated with this channel. New emails received will "
"automatically create new leads assigned to the channel."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__dashboard_graph_model
msgid "The graph this channel will display in the Dashboard.\n"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__partner_name
msgid ""
"The name of the future partner company that will be created while converting"
" the lead into opportunity"
msgstr ""

#. module: crm
#: sql_constraint:crm.lead:0
msgid "The probability of closing the deal should be between 0% and 100%!"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__dashboard_graph_period_pipeline
msgid "The time period this channel's dashboard graph will consider."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__email_cc
msgid ""
"These email addresses will be added to the CC field of all inbound and "
"outbound emails for this record before being sent. Separate multiple email "
"addresses with a comma"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_kanban_view_leads
msgid ""
"This bar allows to filter the opportunities based on scheduled activities."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tour.js:42
#, python-format
msgid "This opportunity has <b>no activity planned</b>."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_stage__probability
msgid ""
"This percentage depicts the default/average probability of the Case for this"
" stage to be a success"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_opportunity_report_action_lead
msgid "This report analyses the source of your leads."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_stage__fold
msgid ""
"This stage is folded in the kanban view when there are no records in that "
"stage to display."
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:1088
#, python-format
msgid "This target does not exist."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__title
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "Title"
msgstr "ចំណងជើង​"

#. module: crm
#: selection:crm.lead,activity_state:0
msgid "Today"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Today Activities"
msgstr ""

#. module: crm
#: model:crm.lost.reason,name:crm.lost_reason_1
msgid "Too expensive"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
msgid "Tracking"
msgstr ""

#. module: crm
#: model:crm.lead.tag,name:crm.categ_oppor6
msgid "Training"
msgstr ""

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_0
msgid "Try Now"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__lead_type
#: model:ir.model.fields,field_description:crm.field_crm_lead__type
msgid "Type"
msgstr "ប្រភេទ"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_activity_report__lead_type
#: model:ir.model.fields,help:crm.field_crm_lead__type
msgid "Type is used to separate Leads and Opportunities"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Unassigned"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_salesteams_view_kanban
msgid "Unassigned Lead"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__unassigned_leads_count
#: model_terms:ir.ui.view,arch_db:crm.crm_team_salesteams_view_kanban
msgid "Unassigned Leads"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_team.py:201
#, python-format
msgid "Undefined"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_unread
#: model_terms:ir.ui.view,arch_db:crm.crm_case_kanban_view_leads
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead_kanban
msgid "Unread Messages"
msgstr "សារមិនទាន់អាន"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Use an External Email Server"
msgstr ""

#. module: crm
#: selection:crm.lead2opportunity.partner.mass,action:0
msgid "Use existing partner or create"
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_case_form_view_salesteams_lead
msgid ""
"Use leads if you need a qualification step before creating an\n"
"                    opportunity or a customer. It can be a business card you received,\n"
"                    a contact form filled in your website, or a file of unqualified\n"
"                    prospects you import, etc."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid ""
"Use leads if you need a qualification step before creating an opportunity or"
" a customer. It can be a business card you received, a contact form filled "
"in your website, or a file of unqualified prospects you import, etc. Once "
"qualified, the lead can be converted into a business opportunity and/or a "
"new customer in your address book."
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lost_reason_action
msgid "Use lost reasons to explain why an opportunity is lost."
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.relate_partner_opportunities
msgid ""
"Use opportunities to keep track of your sales pipeline, follow\n"
"                up potential sales and better forecast your future revenues."
msgstr ""

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tour.js:72
#, python-format
msgid "Use the breadcrumbs to <b>go back to your sales pipeline</b>."
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__user_login
msgid "Used to log into the system"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_stage__sequence
msgid "Used to order stages. Lower is better."
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__user_email
msgid "User Email"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__user_login
msgid "User Login"
msgstr ""

#. module: crm
#: model:ir.model,name:crm.model_res_users
msgid "Users"
msgstr "អ្នកប្រើ"

#. module: crm
#: selection:crm.lead,priority:0
msgid "Very High"
msgstr ""

#. module: crm
#: model:crm.lost.reason,name:crm.lost_reason_2
msgid "We don't have people/skills"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__website
msgid "Website"
msgstr "វែបសាយ"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Website Lead Generation"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__website_message_ids
msgid "Website Messages"
msgstr "សារវែបសាយ"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__website_message_ids
msgid "Website communication history"
msgstr "ប្រវត្តិទំនាក់ទំនងវែបសាយ"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__website
msgid "Website of the contact"
msgstr ""

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__team_id
msgid ""
"When sending mails, the default email address is taken from the Sales Team."
msgstr ""

#. module: crm
#: selection:crm.team,dashboard_graph_period_pipeline:0
msgid "Within a Month"
msgstr ""

#. module: crm
#: selection:crm.team,dashboard_graph_period_pipeline:0
msgid "Within a Week"
msgstr ""

#. module: crm
#: selection:crm.team,dashboard_graph_period_pipeline:0
msgid "Within a Year"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:1121
#: model:crm.stage,name:crm.stage_lead4
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
#, python-format
msgid "Won"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_users__target_sales_won
msgid "Won in Opportunities Target"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:402
#, python-format
msgid "Yeah! Deal of the last 7 days for the team."
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_team.py:122
#, python-format
msgid ""
"You have to enable the Pipeline on your Sales Team to be able to set it as a"
" content for the graph"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:404
#, python-format
msgid "You just beat your personal record for the past 30 days."
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:406
#, python-format
msgid "You just beat your personal record for the past 7 days."
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.relate_partner_opportunities
msgid ""
"You will be able to plan meetings and log activities from\n"
"                opportunities, convert them into quotations, attach related\n"
"                documents, track all discussions, and much more."
msgstr ""

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_case_form_view_salesteams_opportunity
msgid ""
"You will be able to plan meetings and phone calls from\n"
"                    opportunities, convert them into quotations, attach related\n"
"                    documents, track all discussions, and much more."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "ZIP"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__zip
msgid "Zip"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_create_opportunity_simplified
msgid "e.g. Customer Deal"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "e.g. Product Pricing"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_form_view_oppor
msgid "e.g. www.odoo.com"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:946
#, python-format
msgid "or send an email to %s"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage__team_count
msgid "team_count"
msgstr ""

#. module: crm
#: code:addons/crm/models/crm_lead.py:952
#, python-format
msgid "unknown"
msgstr ""
