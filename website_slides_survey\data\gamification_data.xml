<?xml version="1.0" encoding="utf-8"?>
<odoo><data noupdate="0">
    <record id="website_slides.badge_data_certification" model="gamification.badge">
        <field name="is_published" eval="True"/>
    </record>
    <record id="website_slides.badge_data_certification_goal" model="gamification.goal.definition">
        <field name="domain">[
            ('survey_scoring_success', '=', True),
            ('slide_id.slide_type', '=', 'certification')
        ]</field>
    </record>
</data></odoo>
