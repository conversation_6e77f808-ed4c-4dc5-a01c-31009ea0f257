# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_track_quiz
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# İlknur Gözütok, 2023
# <PERSON><PERSON><PERSON>l <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_track__quiz_questions_count
msgid "# Quiz Questions"
msgstr "# Quiz Soruları"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_leaderboard
msgid ". Try another search."
msgstr ". Başka bir arama yapmayı deneyin."

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.all_visitor_card
msgid "<span class=\"text-muted small font-weight-bold\">Points</span>"
msgstr ""

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.top3_visitor_card
msgid "<span class=\"text-muted\">Points</span>"
msgstr "<span class=\"text-muted\">Puanlar</span>"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_track_view_form
msgid "Add Quiz"
msgstr "Sınav Ekle"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/js/event_quiz.js:0
#, python-format
msgid "All questions must be answered !"
msgstr "Tüm sorular cevaplanmalıdır !"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_view_form
msgid "Allow multiple tries"
msgstr "Çoğul denemeye izin ver"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__text_value
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__answer_ids
msgid "Answer"
msgstr "Cevap"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "Check answers"
msgstr "Cevapları kontrol et"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "Check your answers"
msgstr "Yanıtları kontrol edin"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_track_visitor__quiz_completed
msgid "Completed"
msgstr "Tamamlandı"

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_13_question_0_0
msgid "Concrete Blocks Wall"
msgstr "Beton Blok Duvar"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "Congratulations, you scored a total of"
msgstr "Tebrikler, toplamda puan aldınız"

#. module: website_event_track_quiz
#: model:ir.model,name:website_event_track_quiz.model_event_quiz_question
msgid "Content Quiz Question"
msgstr "İçerik Testi Sorusu"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__is_correct
msgid "Correct"
msgstr "Doğru"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__correct_answer_id
msgid "Correct Answer"
msgstr "Doğru Cevap"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "Correct."
msgstr "Doğru."

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__create_uid
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__create_uid
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__create_uid
msgid "Created by"
msgstr "Oluşturan"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__create_date
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__create_date
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__create_date
msgid "Created on"
msgstr "Oluşturulma"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__display_name
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__display_name
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__display_name
msgid "Display Name"
msgstr "Görünüm Adı"

#. module: website_event_track_quiz
#: model:event.quiz.answer,comment:website_event_track_quiz.event_7_track_1_question_0_1
msgid "Even if there will be some."
msgstr "Bazıları olacak olsa bile."

#. module: website_event_track_quiz
#: model:event.quiz.answer,comment:website_event_track_quiz.event_7_track_5_question_0_1
msgid "Even if you have a big trunk, some long products need to be secured."
msgstr ""
"Büyük bir bagajınız olsa bile, bazı uzun ürünlerin emniyete alınması "
"gerekir."

#. module: website_event_track_quiz
#: model:ir.model,name:website_event_track_quiz.model_event_event
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__event_id
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_view_search
msgid "Event"
msgstr "Etkinlik"

#. module: website_event_track_quiz
#: model:ir.actions.act_window,name:website_event_track_quiz.event_quiz_question_action
msgid "Event Quiz Questions"
msgstr "Etkinlik Quiz Soruları"

#. module: website_event_track_quiz
#: model:ir.actions.act_window,name:website_event_track_quiz.event_quiz_action
msgid "Event Quizzes"
msgstr "Etkinlik Sınavları"

#. module: website_event_track_quiz
#: model:ir.model,name:website_event_track_quiz.model_event_track
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__event_track_id
msgid "Event Track"
msgstr "Etkinlik Yeri"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__comment
msgid "Extra Comment"
msgstr "Ekstra Yorum"

#. module: website_event_track_quiz
#: model_terms:ir.actions.act_window,help:website_event_track_quiz.event_quiz_question_action
msgid ""
"From here you will be able to examine all quiz questions you have linked to "
"Tracks."
msgstr ""
"Buradan, Tracks'e bağladığınız tüm test sorularını inceleyebilirsiniz."

#. module: website_event_track_quiz
#: model_terms:ir.actions.act_window,help:website_event_track_quiz.event_quiz_action
msgid ""
"From here you will be able to overview all quizzes you have linked to "
"Tracks."
msgstr ""
"Buradan, Tracks'e bağladığınız tüm testleri gözden geçirebileceksiniz."

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_track_view_form
msgid "Go to Quiz"
msgstr "Sınava git"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_question_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_view_search
msgid "Group By"
msgstr "Grupla"

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_5_question_1_0
msgid "Hammer"
msgstr "Çekiç"

#. module: website_event_track_quiz
#: model:event.quiz.answer,comment:website_event_track_quiz.event_7_track_5_question_1_0
msgid "Hammer won't be of any help here!"
msgstr "Hammer burada yardımcı olmayacak!"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__id
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__id
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__id
msgid "ID"
msgstr "ID"

#. module: website_event_track_quiz
#: model:event.quiz.answer,comment:website_event_track_quiz.event_7_track_5_question_0_0
msgid ""
"In order to avoid accident, you need to secure any product of this kind "
"during transportation!"
msgstr ""
"Kazaları önlemek için bu tür ürünleri nakliye sırasında emniyete "
"almalısınız!"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "Incorrect."
msgstr "Yanlış."

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_track__is_quiz_completed
msgid "Is Quiz Done"
msgstr "Sınav Yapıldı mı"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz____last_update
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer____last_update
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question____last_update
msgid "Last Modified on"
msgstr "Son Düzenleme"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__write_uid
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__write_uid
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleyen"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__write_date
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__write_date
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme"

#. module: website_event_track_quiz
#: model:ir.model.fields,help:website_event_track_quiz.field_event_quiz__repeatable
msgid "Let attendees reset the quiz and try again."
msgstr "Katılımcıların testi sıfırlamasına ve tekrar denemesine izin verin."

#. module: website_event_track_quiz
#: model:event.quiz.answer,comment:website_event_track_quiz.event_7_track_1_question_1_1
msgid "Lumbers need first to be cut from trees!"
msgstr "Kerestelerin önce ağaçlardan kesilmesi gerekiyor!"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.leaderboard_search_bar
msgid "Mobile sub-nav"
msgstr "Mobil alt navigasyon"

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_13_question_0_2
msgid "Mud Wall"
msgstr "çamur duvar"

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_1_question_0_1
msgid "Music"
msgstr "Müzik"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__name
msgid "Name"
msgstr "Adı"

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_5_question_0_1
msgid "No"
msgstr "Hayır"

#. module: website_event_track_quiz
#: model_terms:ir.actions.act_window,help:website_event_track_quiz.event_quiz_question_action
msgid "No Quiz Question yet!"
msgstr "Henüz Sınav Sorusu yok!"

#. module: website_event_track_quiz
#: model_terms:ir.actions.act_window,help:website_event_track_quiz.event_quiz_action
msgid "No Quiz added yet!"
msgstr "Henüz Test eklenmemiş!"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_leaderboard
msgid "No user found for"
msgstr "İçin kullanıcı bulunamadı"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__awarded_points
msgid "Number of Points"
msgstr "Puan Sayısı"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "Oopsie, you did not score any point on this quiz."
msgstr "Hata, bu testte herhangi bir puan almadın."

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_1_question_0_2
msgid "Open Source Apps"
msgstr "Açık Kaynak Uygulamalar"

#. module: website_event_track_quiz
#: model:event.quiz.answer,comment:website_event_track_quiz.event_7_track_1_question_0_2
msgid "OpenWood is not an Open Source congres about Apps."
msgstr "OpenWood, Uygulamalar hakkında bir Açık Kaynak kongresi değildir."

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "Point"
msgstr "Puan"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__awarded_points
#, python-format
msgid "Points"
msgstr "Puanlar"

#. module: website_event_track_quiz
#: model:event.quiz,name:website_event_track_quiz.event_7_track_13_quiz
msgid "Pretty. Ugly. Lovely."
msgstr "Tatlı. Çirkin. Sevimli."

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__question_id
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__name
msgid "Question"
msgstr "Soru"

#. module: website_event_track_quiz
#: code:addons/website_event_track_quiz/models/event_quiz.py:0
#, python-format
msgid ""
"Question \"%s\" must have 1 correct answer and at least 1 incorrect answer "
"to be valid."
msgstr ""
"\"%s\" sorusunun geçerli olabilmesi için 1 doğru cevabı ve en az 1 yanlış "
"cevabı olmalıdır."

#. module: website_event_track_quiz
#: code:addons/website_event_track_quiz/models/event_quiz.py:0
#, python-format
msgid "Question \"%s\" must have 1 correct answer to be valid."
msgstr "\"%s\" sorusunun geçerli olabilmesi için 1 doğru yanıtı olmalıdır."

#. module: website_event_track_quiz
#: model:ir.model,name:website_event_track_quiz.model_event_quiz_answer
msgid "Question's Answer"
msgstr "Sorunun Cevabı"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__question_ids
msgid "Questions"
msgstr "Sorular"

#. module: website_event_track_quiz
#: model:ir.model,name:website_event_track_quiz.model_event_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__quiz_id
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_track__quiz_id
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_question_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_view_form
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_track_content
msgid "Quiz"
msgstr "Sınav"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_track__quiz_points
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_track_visitor__quiz_points
msgid "Quiz Points"
msgstr "Sınav Puanları"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_question_view_form
msgid "Quiz Question"
msgstr "Sınav Sorusu"

#. module: website_event_track_quiz
#: model:ir.ui.menu,name:website_event_track_quiz.event_quiz_question_menu
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_question_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_question_view_tree
msgid "Quiz Questions"
msgstr "Sınav Soruları"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/js/event_quiz.js:0
#, python-format
msgid "Quiz validation error"
msgstr "Sınav doğrulama hatası"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_track__quiz_ids
#: model:ir.ui.menu,name:website_event_track_quiz.event_quiz_menu
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_view_tree
msgid "Quizzes"
msgstr "Sınavlar"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "Reset"
msgstr "Sıfırla"

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_5_question_1_2
msgid "Scotch tape"
msgstr "Scotch tape"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.leaderboard_search_bar
msgid "Search"
msgstr "Arama"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.leaderboard_search_bar
msgid "Search Attendees"
msgstr "Katılımcılar Ara"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.leaderboard_search_bar
msgid "Search courses"
msgstr "Kursları ara"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.leaderboard_search_bar
msgid "Search users"
msgstr "Kullanıcı ara"

#. module: website_event_track_quiz
#: model:event.quiz,name:website_event_track_quiz.event_7_track_5_quiz
msgid "Securing your Lumber during transport"
msgstr "Taşıma sırasında Kerestenizin Emniyete Alınması"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__sequence
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__sequence
msgid "Sequence"
msgstr "Sıra"

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_13_question_0_1
msgid "Steel Wall"
msgstr "Çelik duvar"

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_1_question_1_1
msgid "Stores !"
msgstr "Mağazalar !"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_track_content
msgid "Take the Quiz"
msgstr "Sınav Olmak"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "The correct answer was:"
msgstr "Doğru cevap şuydu:"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_leaderboard
msgid "There is currently no leaderboard available"
msgstr "Şu anda kullanılabilir bir skor tablosu yok"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/js/event_quiz.js:0
#, python-format
msgid "There was an error validating this quiz."
msgstr "Bu sınav doğrulanırken bir hata oluştu."

#. module: website_event_track_quiz
#: model:ir.model.fields,help:website_event_track_quiz.field_event_quiz_answer__comment
msgid ""
"This comment will be displayed to the user if he selects this answer, after submitting the quiz.\n"
"                It is used as a small informational text helping to understand why this answer is correct / incorrect."
msgstr ""

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/js/event_quiz.js:0
#, python-format
msgid "This quiz is already done. Retaking it is not possible."
msgstr "Bu sınav zaten yapıldı. Geri almak mümkün değil."

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_5_question_1_1
msgid "Tie-down straps and other wooden blocks"
msgstr "Bağlama kayışları ve diğer ahşap bloklar"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_view_search
msgid "Track"
msgstr "İzle"

#. module: website_event_track_quiz
#: model:ir.model,name:website_event_track_quiz.model_event_track_visitor
msgid "Track / Visitor Link"
msgstr "Bölüm / Ziyaretçi Bağlantısı"

#. module: website_event_track_quiz
#: model:event.quiz.question,name:website_event_track_quiz.event_7_track_5_question_0
msgid "Transporting lumber from stores to your house is safe."
msgstr "Mağazalardan evinize kereste taşımak güvenlidir."

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_1_question_1_0
msgid "Trees !"
msgstr "Ağaçlar !"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__repeatable
msgid "Unlimited Tries"
msgstr "Sınırsız Deneme"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.top3_visitor_card
msgid "User rank"
msgstr "Kullanıcı sıralaması"

#. module: website_event_track_quiz
#: model:event.quiz.answer,comment:website_event_track_quiz.event_7_track_5_question_1_2
msgid "Well, it could work but you will need a lot of tape!"
msgstr "İşe yarayabilir ama çok fazla banda ihtiyacınız olacak!"

#. module: website_event_track_quiz
#: model:event.quiz,name:website_event_track_quiz.event_7_track_1_quiz
msgid "What This Event Is All About"
msgstr "Bu Etkinlik Neyle İlgili"

#. module: website_event_track_quiz
#: model:event.quiz.question,name:website_event_track_quiz.event_7_track_5_question_1
msgid "What kind of tool are needed to secure your lumber ?"
msgstr "Kerestenizi sabitlemek için ne tür bir alete ihtiyaç vardır?"

#. module: website_event_track_quiz
#: model:event.quiz.question,name:website_event_track_quiz.event_7_track_13_question_0
msgid "What kind of wall is transformed here ?"
msgstr "Burada ne tür bir duvar dönüştürülüyor?"

#. module: website_event_track_quiz
#: model:event.quiz.question,name:website_event_track_quiz.event_7_track_1_question_0
msgid "What will we talk about during this event ?"
msgstr "Bu etkinlikte neler konuşacağız?"

#. module: website_event_track_quiz
#: model:event.quiz.question,name:website_event_track_quiz.event_7_track_1_question_1
msgid "Where does lumber comes from ?"
msgstr "Kereste nereden geliyor?"

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_1_question_0_0
msgid "Wood"
msgstr "Ahşap"

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_5_question_0_0
msgid "Yes"
msgstr "Evet"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.all_visitor_card
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.top3_visitor_card
msgid "You"
msgstr "Siz"

#. module: website_event_track_quiz
#: model:event.quiz.answer,comment:website_event_track_quiz.event_7_track_1_question_0_0
msgid "You're really smart !"
msgstr "Gerçekten akıllısın!"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_view_form
msgid "e.g. Test your Knowledge"
msgstr "Örneğin. Bilgini test et"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_question_view_form
msgid "e.g. What is Joe's favorite motto?"
msgstr "Örneğin. Joe'nun favori sloganı nedir?"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "point!"
msgstr "puan!"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "points!"
msgstr "puanlar!"
