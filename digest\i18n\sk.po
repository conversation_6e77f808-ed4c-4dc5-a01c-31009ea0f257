# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* digest
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:21+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Slovak (https://app.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "<span class=\"button\" id=\"button_open_report\">Open Report</span>"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "<span class=\"odoo_link_text\">Odoo</span>"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "<span style=\"color: #8f8f8f;\">Unsubscribe</span>"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Activate"
msgstr "Aktivovať"

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__state__activated
msgid "Activated"
msgstr "Aktivovaný"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Add new users as recipient of a periodic email with key metrics"
msgstr "Pridajte nového užívateľa ako príjemcu mailov s kľúčovými metrikami"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_tip__group_id
msgid "Authorized Group"
msgstr "Autorizovaná skupina"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__available_fields
msgid "Available Fields"
msgstr "Dostupné polia"

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Choose the metrics you care about"
msgstr "Vyberte si ukazatele ktoré vás zaujímajú"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__company_id
msgid "Company"
msgstr "Spoločnosť"

#. module: digest
#: model:ir.model,name:digest.model_res_config_settings
msgid "Config Settings"
msgstr "Nastavenia konfigurácie"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Configure Digest Emails"
msgstr "Nakonfigurovať maily s výberom"

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Connect"
msgstr "Pripojte sa"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_res_users_connected
msgid "Connected Users"
msgstr "Pripojení užívatelia"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid ""
"Create or edit the mail template: you may get computed KPI's value using "
"these fields:"
msgstr ""

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__create_uid
#: model:ir.model.fields,field_description:digest.field_digest_tip__create_uid
msgid "Created by"
msgstr "Vytvoril"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__create_date
#: model:ir.model.fields,field_description:digest.field_digest_tip__create_date
msgid "Created on"
msgstr "Vytvorené"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__currency_id
msgid "Currency"
msgstr "Mena"

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__daily
msgid "Daily"
msgstr "Denne"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Deactivate for everyone"
msgstr "Deaktivovať pre každého"

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__state__deactivated
msgid "Deactivated"
msgstr "Deaktivované"

#. module: digest
#: model:ir.model,name:digest.model_digest_digest
msgid "Digest"
msgstr "Prehľad"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_res_config_settings__digest_id
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Digest Email"
msgstr "Mail s prehľadom"

#. module: digest
#: model:ir.actions.act_window,name:digest.digest_digest_action
#: model:ir.actions.server,name:digest.ir_cron_digest_scheduler_action_ir_actions_server
#: model:ir.cron,cron_name:digest.ir_cron_digest_scheduler_action
#: model:ir.cron,name:digest.ir_cron_digest_scheduler_action
#: model:ir.model.fields,field_description:digest.field_res_config_settings__digest_emails
#: model:ir.ui.menu,name:digest.digest_menu
msgid "Digest Emails"
msgstr "Maily s prehľadom"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Digest Name"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.portal_digest_unsubscribed
msgid "Digest Subscriptions"
msgstr "Prihlásenie na odber výberu"

#. module: digest
#: model:ir.actions.act_window,name:digest.digest_tip_action
#: model:ir.model,name:digest.model_digest_tip
#: model:ir.ui.menu,name:digest.digest_tip_menu
msgid "Digest Tips"
msgstr "Tipy prehľadu"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__display_name
#: model:ir.model.fields,field_description:digest.field_digest_tip__display_name
msgid "Display Name"
msgstr "Zobrazovaný názov"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "General"
msgstr "Všeobecné"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_search
msgid "Group by"
msgstr "Zoskupiť podľa"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_1
msgid ""
"Have a question about a document? Click on the responsible user's picture to"
" start a conversation. If his avatar has a green dot, he is online."
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "How to customize your digest?"
msgstr "Ako prispôsobiť svoj prehľad?"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__id
#: model:ir.model.fields,field_description:digest.field_digest_tip__id
msgid "ID"
msgstr "ID"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "In order to build your customized digest, follow these steps:"
msgstr ""
"Pre vybudovanie prispôsobeného prehľadu postupujte podľa týchto krokov:"

#. module: digest
#: code:addons/digest/controllers/portal.py:0
#, python-format
msgid "Invalid periodicity set on digest"
msgstr "Je zvolená nesprávna periodicita na prehľade"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__is_subscribed
msgid "Is user subscribed"
msgstr "Je užívateľ prihlásený na odber"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_tree
msgid "KPI Digest"
msgstr "Výber KPI"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_tip_view_form
msgid "KPI Digest Tip"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_tip_view_tree
msgid "KPI Digest Tips"
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "KPIs"
msgstr "KPIs"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_mail_message_total_value
msgid "Kpi Mail Message Total Value"
msgstr "Celková hodnota mailu KPI"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_res_users_connected_value
msgid "Kpi Res Users Connected Value"
msgstr "Hodnota pripojená používateľmi KPI Res"

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Last 24 hours"
msgstr "Posledných 24h"

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Last 30 Days"
msgstr "Posledných 30 dní"

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Last 7 Days"
msgstr "Posledných 7 dní"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest____last_update
#: model:ir.model.fields,field_description:digest.field_digest_tip____last_update
msgid "Last Modified on"
msgstr "Posledná úprava"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__write_uid
#: model:ir.model.fields,field_description:digest.field_digest_tip__write_uid
msgid "Last Updated by"
msgstr "Naposledy upravoval"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__write_date
#: model:ir.model.fields,field_description:digest.field_digest_tip__write_date
msgid "Last Updated on"
msgstr "Naposledy upravované"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_mail_message_total
msgid "Messages"
msgstr "Správy"

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__monthly
msgid "Monthly"
msgstr "Mesačne"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__name
#: model:ir.model.fields,field_description:digest.field_digest_tip__name
msgid "Name"
msgstr "Meno"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid ""
"New users are automatically added as recipient of the following digest "
"email."
msgstr ""
"Noví užívatelia budú automaticky prihlásení na odber nasledovného výberu:"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__next_run_date
msgid "Next Send Date"
msgstr "Dátum ďalšieho zaslania"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_section_mobile
msgid "Odoo Mobile"
msgstr "Mobilné Odoo"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__periodicity
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_search
msgid "Periodicity"
msgstr "Periodicita"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "Powered by"
msgstr "Prinášané"

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Prefer a broader overview ?"
msgstr ""

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_0
msgid ""
"Press ALT in any screen to highlight shortcuts for every button in the "
"screen. It is useful to process multiple documents in batch."
msgstr ""

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__quarterly
msgid "Quarterly"
msgstr "Štvrťročne"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__user_ids
#: model:ir.model.fields,field_description:digest.field_digest_tip__user_ids
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Recipients"
msgstr "Príjemcovia"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_section_mobile
msgid "Run your business from anywhere with <b>Odoo Mobile</b>."
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Select your KPIs in the KPI's tab."
msgstr "Na karte KPI vyberte svoje KPI."

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Send Now"
msgstr "Odoslať teraz"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "Sent by"
msgstr "Poslané "

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_tip__sequence
msgid "Sequence"
msgstr "Postupnosť"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Statistics"
msgstr "Štatistiky"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__state
msgid "Status"
msgstr "Stav"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Subscribe"
msgstr "Odoberať"

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Switch to weekly Digests"
msgstr "Zmeniť na týždenné prehľady"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_tip__tip_description
msgid "Tip description"
msgstr "Popis tipu"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_2
msgid "Tip: A calculator in Odoo"
msgstr ""

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_1
msgid "Tip: Click on an avatar to chat with a user"
msgstr ""

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_3
msgid "Tip: How to ping users in internal notes?"
msgstr ""

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_4
msgid "Tip: Knowledge is power"
msgstr ""

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_0
msgid "Tip: Speed up your workflow with shortcuts"
msgstr ""

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_2
msgid "Tip: A calculator in Odoo"
msgstr ""

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_1
msgid "Tip: Click on an avatar to chat with a user"
msgstr ""

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_3
msgid "Tip: How to ping users in internal notes?"
msgstr ""

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_4
msgid "Tip: Knowledge is power"
msgstr ""

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_0
msgid "Tip: Speed up your workflow with shortcuts"
msgstr ""

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_3
msgid ""
"Type \"@\" to notify someone in a message, or \"#\" to link to a channel. "
"Try to notify @OdooBot to test the feature."
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Unsubscribe me"
msgstr "Odhlásiť sa z odberu"

#. module: digest
#: model:ir.model.fields,help:digest.field_digest_tip__sequence
msgid "Used to display digest tip in email template base on order"
msgstr ""

#. module: digest
#: model:ir.model,name:digest.model_res_users
msgid "Users"
msgstr "Užívatelia"

#. module: digest
#: model:ir.model.fields,help:digest.field_digest_tip__user_ids
msgid "Users having already received this tip"
msgstr "Užívatelia ktorí už dostali tento tip"

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Want to customize this email?"
msgstr "Chcete upraviť tento email?"

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid ""
"We have noticed you did not connect these last few days. We have "
"automatically switched your preference to %(new_perioridicy_str)s Digests."
msgstr ""

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__weekly
msgid "Weekly"
msgstr "Týždenne"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_2
msgid ""
"When editing a number, you can use formulae by typing the `=` character. "
"This is useful when computing a margin or a discount on a quotation, sale "
"order or invoice."
msgstr ""

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_4
msgid ""
"When following documents, use the pencil icon to fine-tune the information you want to receive.\n"
"Follow a project / sales team to keep track of this project's tasks / this team's opportunities."
msgstr ""

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.portal_digest_unsubscribed
msgid "You have been successfully unsubscribed from"
msgstr "Boli ste úspešne odhlásený z"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "You may want to add new computed fields with Odoo Studio:"
msgstr ""
"Možno budete chcieť pridať nové vypočítané polia v aplikácii Odoo Studio:"

#. module: digest
#: model:digest.digest,name:digest.digest_digest_default
msgid "Your Odoo Periodic Digest"
msgstr "Váš pravidelný prehľad Odoo"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "e.g. Your Weekly Digest"
msgstr "napr. Váš týždenný prehľad"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid ""
"first create a boolean field called\n"
"                                                <code>kpi_myfield</code>\n"
"                                                and display it in the KPI's tab;"
msgstr ""

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "monthly"
msgstr "mesačne"

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "quarterly"
msgstr "štvrťročne"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid ""
"then create a computed field called\n"
"                                                <code>kpi_myfield_value</code>\n"
"                                                that will compute your customized KPI."
msgstr ""

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "weekly"
msgstr "týždenne"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid ""
"you must create 2 fields on the\n"
"                                                <code>digest</code>\n"
"                                                object:"
msgstr ""
