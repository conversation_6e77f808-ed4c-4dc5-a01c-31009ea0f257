# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * iap
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-02 10:05+0000\n"
"PO-Revision-Date: 2018-10-02 10:05+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Gujarati (https://www.transifex.com/odoo/teams/41243/gu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: gu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid "Account Information"
msgstr ""

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__account_token
msgid "Account Token"
msgstr ""

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/js/crash_manager.js:23
#, python-format
msgid "Buy credits at Odoo"
msgstr ""

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/xml/iap_templates.xml:41
#, python-format
msgid "Buy more credits"
msgstr ""

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/js/crash_manager.js:61
#, python-format
msgid "Cancel"
msgstr "રદ કરો"

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/xml/iap_templates.xml:28
#, python-format
msgid "Checking remaining credit ..."
msgstr ""

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__company_id
msgid "Company"
msgstr "કંપની"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__create_uid
msgid "Created by"
msgstr "બનાવનાર"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__create_date
msgid "Created on"
msgstr ""

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__display_name
msgid "Display Name"
msgstr "પ્રદર્શન નામ"

#. module: iap
#: model:ir.ui.menu,name:iap.iap_root_menu
msgid "IAP"
msgstr ""

#. module: iap
#: model:ir.actions.act_window,name:iap.iap_account_action
#: model:ir.model,name:iap.model_iap_account
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid "IAP Account"
msgstr ""

#. module: iap
#: model:ir.ui.menu,name:iap.iap_account_menu
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_tree
msgid "IAP Accounts"
msgstr ""

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__id
msgid "ID"
msgstr "ઓળખ"

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/xml/iap_templates.xml:21
#, python-format
msgid "In-App Purchases"
msgstr ""

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/js/crash_manager.js:51
#, python-format
msgid "Insufficient Balance"
msgstr ""

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/xml/iap_templates.xml:31
#, python-format
msgid "Insufficient credit"
msgstr ""

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/xml/iap_templates.xml:14
#, python-format
msgid "Insufficient credit to perform this service."
msgstr ""

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account____last_update
msgid "Last Modified on"
msgstr ""

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__write_uid
msgid "Last Updated by"
msgstr ""

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__write_date
msgid "Last Updated on"
msgstr ""

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/xml/iap_templates.xml:34
#, python-format
msgid "Remaining :"
msgstr ""

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__service_name
msgid "Service Name"
msgstr ""

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/js/crash_manager.js:23
#, python-format
msgid "Start a Trial at Odoo"
msgstr ""

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/xml/iap_templates.xml:37
#, python-format
msgid "Temporarily unavailable"
msgstr ""

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/xml/iap_templates.xml:34
#, python-format
msgid "credits"
msgstr ""
