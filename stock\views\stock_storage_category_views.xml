<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="stock_storage_category_form" model="ir.ui.view">
        <field name="name">stock.storage.category.form</field>
        <field name="model">stock.storage.category</field>
        <field name="arch" type="xml">
            <form string="Storage Category">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="%(action_storage_category_locations)d" string="Locations" type="action" class="oe_stat_button" icon="fa-arrows-v"/>
                    </div>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="allow_new_product"/>
                        </group>
                        <group>
                            <label for="max_weight"/>
                            <div class="o_row">
                                <field name="max_weight"/>
                                <span><field name="weight_uom_name"/></span>
                            </div>
                            <field name="company_id" groups="base.group_multi_company"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Capacity by Package" name="package_capacity" groups="stock.group_tracking_lot">
                            <field name="package_capacity_ids" context="{'default_storage_category_id': id, 'default_company_id': company_id}">
                                <tree editable="bottom">
                                    <field name="package_type_id" required="1"/>
                                    <field name="quantity"/>
                                    <field name="company_id" invisible="1"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Capacity by Product" name="product_capacity">
                            <field name="product_capacity_ids" context="{'default_storage_category_id': id, 'default_company_id': company_id}">
                                <tree editable="bottom">
                                    <field name="product_id" required="1" context="{'default_detailed_type': 'product'}"/>
                                    <field name="quantity"/>
                                    <field name="product_uom_id" groups="uom.group_uom" options="{'no_create': True, 'no_open': True}"/>
                                    <field name="company_id" invisible="1"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="stock_storage_category_tree" model="ir.ui.view">
        <field name="name">stock.storage.category.tree</field>
        <field name="model">stock.storage.category</field>
        <field name="arch" type="xml">
            <tree string="Storage Categories" multi_edit="1">
                <field name="name"/>
                <field name="max_weight" string="Max Weight"/>
                <field name="allow_new_product"/>
                <field name="company_id" groups="base.group_multi_company"/>
            </tree>
        </field>
    </record>

    <record id="action_storage_category" model="ir.actions.act_window">
        <field name="name">Storage Categories</field>
        <field name="res_model">stock.storage.category</field>
        <field name="type">ir.actions.act_window</field>
        <field name="view_mode">tree,form</field>
        <field name="view_id" ref="stock_storage_category_tree"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Add a new storage category
            </p>
        </field>
    </record>

    <record id="stock_storage_category_capacity_tree" model="ir.ui.view">
        <field name="name">stock.storage.category.capacity.tree</field>
        <field name="model">stock.storage.category.capacity</field>
        <field name="arch" type="xml">
            <tree string="Storage Category Capacity" multi_edit="1" editable="bottom">
                <field name="storage_category_id"/>
                <field name="product_id" invisible="context.get('default_product_id')" attrs="{'required': [('package_type_id', '=', False)], 'readonly': [('package_type_id', '!=', False)]}"/>
                <field name="package_type_id" attrs="{'required': [('product_id', '=', False)], 'readonly': [('product_id', '!=', False)]}" invisible="context.get('hide_package_type', False)" groups="stock.group_tracking_lot"/>
                <field name="quantity"/>
                <field name="product_uom_id" groups="uom.group_uom" options="{'no_create': True, 'no_open': True}"/>
                <field name="company_id" invisible="1"/>
            </tree>
        </field>
    </record>

    <record id="action_storage_category_capacity" model="ir.actions.act_window">
        <field name="name">Storage Category Capacity</field>
        <field name="res_model">stock.storage.category.capacity</field>
        <field name="type">ir.actions.act_window</field>
        <field name="view_mode">tree</field>
    </record>

    <menuitem id="menu_storage_categoty_config" action="action_storage_category" name="Storage Categories" parent="menu_warehouse_config" sequence="6" groups="stock.group_stock_storage_categories"/>
    <menuitem id="menu_storage_categoty_capacity_config" action="action_storage_category_capacity" name="Storage Category Capacity" parent="menu_warehouse_config" sequence="7" groups="base.group_no_one"/>

</odoo>
