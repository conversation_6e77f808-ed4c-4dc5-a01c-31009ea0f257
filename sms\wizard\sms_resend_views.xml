<?xml version="1.0" encoding="utf-8"?>
<odoo><data>
    <record id="mail_resend_message_view_form" model="ir.ui.view">
        <field name="name">sms.resend.form</field>
        <field name="model">sms.resend</field>
        <field name="groups_id" eval="[(4,ref('base.group_user'))]"/>
        <field name="arch" type="xml">
            <form string="Edit Partners">
                <field name="mail_message_id" invisible="1"/>
                <field name="has_cancel" invisible="1"/>
                <field name="has_insufficient_credit" invisible="1"/>
                <field name="has_unregistered_account" invisible="1"/>
                <field name="recipient_ids">
                    <tree string="Recipient" editable="top" create="0" delete="0">
                        <field name="partner_name"/>
                        <field name="sms_number"/>
                        <field name="failure_type" string="Reason"/>
                        <field name="resend" widget="boolean_toggle"/>
                        <field name="notification_id" invisible="1"/>
                    </tree>
                </field>
                <div class="alert alert-warning" role="alert" attrs="{'invisible': [('has_cancel', '=', False)]}">
                    <span class="fa fa-info-circle"/> Caution: It won't be possible to send this SMS again to the recipients you did not select.
                </div>
                <footer>
                    <button string="Buy credits" name="action_buy_credits" type="object" class="btn-primary o_mail_send" 
                            attrs="{'invisible': [('has_insufficient_credit', '=', False)]}" data-hotkey="q"/>
                    <button string="Set up an account" name="action_buy_credits" type="object" class="btn-primary o_mail_send" 
                            attrs="{'invisible': [('has_unregistered_account', '=', False)]}" data-hotkey="q"/>
                    <button string="Resend" name="action_resend" type="object" class="btn-primary o_mail_send" data-hotkey="w"/>
                    <button string="Ignore all" name="action_cancel" type="object" class="btn-secondary" data-hotkey="x"/>
                    <button string="Discard" class="btn-secondary" special="cancel" data-hotkey="z"/>
                </footer>
            </form>
        </field>
    </record>
    
    <record id="sms_resend_action" model="ir.actions.act_window">
        <field name="name">Sending Failures</field>
        <field name="res_model">sms.resend</field>
        <field name="type">ir.actions.act_window</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>
</data></odoo>
