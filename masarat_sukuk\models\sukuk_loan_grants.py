# -*- coding:utf-8 -*-

from dateutil.relativedelta import relativedelta
from odoo import api, fields, models, _
from odoo.exceptions import ValidationError, UserError, AccessError
from datetime import date, datetime
from odoo.tools import float_compare, float_round, float_repr
from collections import defaultdict



class LoanGrantsSukukReportWizard(models.TransientModel):
    _name = "loangrant.sukuk.wizard.report"

    type = fields.Selection([('loan','سلفة'),('grants','عهدة')], default='loan', string='النوع')

    loan_id = fields.Many2one('hr.loan', string='السلفة')
    grant_id = fields.Many2one('hr.masarat.expense', domain="[('payment_state', '=', 'paid')]", string='العهدة')
    partner_paid_to = fields.Many2one('res.partner', required=True, string='اسم المدفوع له')
    # loan_partner_id = fields.Many2one('hr.employee', related='loan_id.employee_id', string='اسم المدفوع له')
    # grant_partner_id = fields.Many2one('hr.employee',related='grant_id.employee_id', string='اسم المدفوع له')
    amount = fields.Float(string='القيمة', compute='_get_amount')

    bank_id = fields.Many2one('res.bank', string='اسم المصرف',required=True)
    branch_id = fields.Many2one('bank.branch', string="أسم الفرع", domain="[('bank_id','=',bank_id)]",required=True)
    account_no_id = fields.Many2one('account.account', string="رقم الحساب",required=True)
    dialog_box = fields.Text()
    user_id = fields.Many2one('res.users', string='مقدم الطلب', default=lambda self: self.env.uid)
    sukuk_page_ids = fields.One2many('loangrant.sukuk.wizard.page','sukuk_wizard_id')

    sukuk_computed = fields.Boolean()


    @api.onchange('type')
    def change_type(self):
        if self.type == 'loan':
            self.grant_id = False
        if self.type == 'grants':
            self.loan_id = False

    @api.depends('grant_id','type','loan_id')
    def _get_amount(self):
        for elem in self:
            elem.amount = 0
            if elem.loan_id:
                elem.amount = elem.loan_id.loan_amount
            if elem.grant_id:
                elem.amount = elem.grant_id.amount

    def get_available_suke(self):
        sukuk_books = self.env['sukuk.management.item'].search([('state', '=', 'confirmed'),
                                                                ('bank_id', '=', self.bank_id.id),
                                                                ('branch_id', '=', self.branch_id.id),
                                                                ('account_no_id','=',self.account_no_id.id)])
        for each_book in sukuk_books:
            serial_no_from = each_book.serial_no_from
            serial_no_to = each_book.serial_no_to
            while serial_no_from <= serial_no_to:
                sukuk_page = self.env['sukuk.management.page'].search([('sukuk_book_id','=',each_book.id),
                                                                       ('serial_no','=',serial_no_from)])
                if sukuk_page:
                    serial_no_from+=1
                else:
                    suke = {
                        'sukuk_book_id':each_book.id,
                        'state':'draft',
                        'bank_id':each_book.bank_id.id,
                        'branch_id':each_book.branch_id.id,
                        'partner_paid_to':self.partner_paid_to.id,
                        'user_id':self.env.uid,
                        'person_signature':each_book.person_signature.id,
                        'suke_book_number':each_book.suke_book_number,
                        'serial_no':serial_no_from,
                        'time_stamp':str(fields.Date.to_string(date.today())),
                        'amount':self.amount,
                        'note':''
                        }
                    return suke
        return {'partner_paid_to':self.partner_id.id,
                'note':'لا يوجد صك متاح'}


    def generate_payslips_sukuk(self):
        sukuk_pages = []
        suke = self.get_available_suke()
        sukuk_pages.append((0, 0, suke))
        self.sukuk_page_ids = False
        self.sukuk_page_ids = sukuk_pages
        self.sukuk_computed = True
        return {"type": "ir.actions.act_window",
                "view_mode":"form",
                "res_model":"loangrant.sukuk.wizard.report",
                "target":"new",
                "res_id":self.id}


    def get_confirm(self):
        for elem in self.sukuk_page_ids:
            wallet_name = self.loan_id.name or 'Expense-'+self.grant_id.expense_name
            suke_id = self.env['sukuk.management.page'].create({
                "sukuk_book_id": elem.sukuk_book_id.id,
                "bank_id":elem.bank_id.id,
                "branch_id":elem.branch_id.id,
                "partner_paid_to":elem.partner_paid_to.id,
                "state": "draft",
                "wallet_name":wallet_name,
                "user_id": elem.user_id.id,
                "person_signature": elem.person_signature.id,
                "suke_book_number": elem.suke_book_number,
                "serial_no": elem.serial_no,
                "time_stamp": elem.time_stamp,
                "amount": elem.amount,
            })

            if self.grant_id: ## linking Suke with Grant
                self.grant_id.suke_id = suke_id.id
            if self.loan_id: ## linking Suke with Loan
                self.loan_id.suke_id = suke_id.id

    @api.onchange('bank_id','branch_id','account_no_id')
    def get_dialog_box(self):
        if self.bank_id and self.branch_id and self.account_no_id:
            sukuk_books = self.env['sukuk.management.item'].search([('bank_id', '=', self.bank_id.id), ('branch_id', '=', self.branch_id.id),('account_no_id', '=', self.account_no_id.id)])
            if sukuk_books:
                self.dialog_box = str(len(sukuk_books))+" دفاتر متاحة "
            else:
                self.dialog_box = "لا يوجد دفاتر متاحة بالمعلومات المرفقة"
        else:
            self.dialog_box = ""



class LoanGrantsSukukReporPagetWizard(models.TransientModel):
    _name = "loangrant.sukuk.wizard.page"

    sukuk_wizard_id = fields.Many2one('loangrant.sukuk.wizard.report')

    sukuk_book_id = fields.Many2one('sukuk.management.item', ondelete='cascade')
    state = fields.Selection([('draft', 'مسودة'), ('confirmed', 'مؤكدة'), ('cancel', 'ملغية')], default="draft")
    bank_id = fields.Many2one('res.bank', string='اسم المصرف')
    branch_id = fields.Many2one('bank.branch', string="أسم الفرع")

    partner_paid_to = fields.Many2one('res.partner', string="أسم المدفوع له")

    user_id = fields.Many2one('res.users', string='مقدم الطلب')
    person_signature = fields.Many2one('sukuk.management.signature',string="اسم المخول بالتوقيع")
    suke_book_number = fields.Integer( string='رقم الدفتر')
    serial_no = fields.Integer(string="رقم تسلسلي")
    time_stamp = fields.Date(string="التاريخ")
    amount = fields.Float(string="القيمة")
    note=fields.Text(string='ملاحظة')

    @api.onchange('sukuk_book_id')
    def change_suke_book_number(self):
        for elem in self:
            if elem.sukuk_book_id:
                elem.suke_book_number = elem.sukuk_book_id.suke_book_number
                elem.serial_no = False
                elem.note = False



class HrEmployeeExpenseRequest(models.Model):
    _inherit = "hr.masarat.expense"

    suke_id = fields.Many2one('sukuk.management.page', string="الصك")



class AccountJournl(models.Model):
    _inherit = "account.journal"

    grant_id = fields.Many2one('hr.masarat.expense',string="العهدة" , readonly=True)
    suke_id = fields.Many2one('sukuk.management.page', related='grant_id.suke_id', string="الصك")



class AccountMove(models.Model):
    _inherit = "account.move"

    grant_id = fields.Many2one('hr.masarat.expense',string="العهدة" , readonly=True)
    suke_id = fields.Many2one('sukuk.management.page', related='grant_id.suke_id', string="الصك")

    def _post(self, soft=True):
        """Post/Validate the documents.

        Posting the documents will give it a number, and check that the document is
        complete (some fields might not be required if not posted but are required
        otherwise).
        If the journal is locked with a hash table, it will be impossible to change
        some fields afterwards.

        :param soft (bool): if True, future documents are not immediately posted,
            but are set to be auto posted automatically at the set accounting date.
            Nothing will be performed on those documents before the accounting date.
        :return Model<account.move>: the documents that have been posted
        """
        if soft:
            future_moves = self.filtered(lambda move: move.date > fields.Date.context_today(self))
            future_moves.auto_post = True
            for move in future_moves:
                msg = _('This move will be posted at the accounting date: %(date)s', date=format_date(self.env, move.date))
                move.message_post(body=msg)
            to_post = self - future_moves
        else:
            to_post = self

        # `user_has_group` won't be bypassed by `sudo()` since it doesn't change the user anymore.
        if not self.env.su and not self.env.user.has_group('account.group_account_invoice'):
            raise AccessError(_("You don't have the access rights to post an invoice."))
        for move in to_post:
            if move.partner_bank_id and not move.partner_bank_id.active:
                raise UserError(_("The recipient bank account link to this invoice is archived.\nSo you cannot confirm the invoice."))
            if move.state == 'posted':
                raise UserError(_('The entry %s (id %s) is already posted.') % (move.name, move.id))
            if not move.line_ids.filtered(lambda line: not line.display_type):
                raise UserError(_('You need to add a line before posting.'))
            if move.auto_post and move.date > fields.Date.context_today(self):
                date_msg = move.date.strftime(get_lang(self.env).date_format)
                raise UserError(_("This move is configured to be auto-posted on %s", date_msg))

            if not move.partner_id:
                if move.is_sale_document():
                    raise UserError(_("The field 'Customer' is required, please complete it to validate the Customer Invoice."))
                elif move.is_purchase_document():
                    raise UserError(_("The field 'Vendor' is required, please complete it to validate the Vendor Bill."))

            if move.is_invoice(include_receipts=True) and float_compare(move.amount_total, 0.0, precision_rounding=move.currency_id.rounding) < 0:
                raise UserError(_("You cannot validate an invoice with a negative total amount. You should create a credit note instead. Use the action menu to transform it into a credit note or refund."))

            # Handle case when the invoice_date is not set. In that case, the invoice_date is set at today and then,
            # lines are recomputed accordingly.
            # /!\ 'check_move_validity' must be there since the dynamic lines will be recomputed outside the 'onchange'
            # environment.
            if not move.invoice_date:
                if move.is_sale_document(include_receipts=True):
                    move.invoice_date = fields.Date.context_today(self)
                    move.with_context(check_move_validity=False)._onchange_invoice_date()
                elif move.is_purchase_document(include_receipts=True):
                    raise UserError(_("The Bill/Refund date is required to validate this document."))

            # When the accounting date is prior to the tax lock date, move it automatically to the next available date.
            # /!\ 'check_move_validity' must be there since the dynamic lines will be recomputed outside the 'onchange'
            # environment.
            if (move.company_id.tax_lock_date and move.date <= move.company_id.tax_lock_date) and (move.line_ids.tax_ids or move.line_ids.tax_tag_ids):
                move.date = move._get_accounting_date(move.invoice_date or move.date, True)
                move.with_context(check_move_validity=False)._onchange_currency()

        # Create the analytic lines in batch is faster as it leads to less cache invalidation.

        to_post.mapped('line_ids').create_analytic_lines()
        to_post.write({
            'state': 'posted',
            'posted_before': True,
        })

        for move in to_post:
            move.message_subscribe([p.id for p in [move.partner_id] if p not in move.sudo().message_partner_ids])

            # Compute 'ref' for 'out_invoice'.
            if move._auto_compute_invoice_reference():
                to_write = {
                    'payment_reference': move._get_invoice_computed_reference(),
                    'line_ids': []
                }
                for line in move.line_ids.filtered(lambda line: line.account_id.user_type_id.type in ('receivable', 'payable')):
                    to_write['line_ids'].append((1, line.id, {'name': to_write['payment_reference']}))
                move.write(to_write)

        for move in to_post:
            if move.is_sale_document() \
                    and move.journal_id.sale_activity_type_id \
                    and (move.journal_id.sale_activity_user_id or move.invoice_user_id).id not in (self.env.ref('base.user_root').id, False):
                move.activity_schedule(
                    date_deadline=min((date for date in move.line_ids.mapped('date_maturity') if date), default=move.date),
                    activity_type_id=move.journal_id.sale_activity_type_id.id,
                    summary=move.journal_id.sale_activity_note,
                    user_id=move.journal_id.sale_activity_user_id.id or move.invoice_user_id.id,
                )

        customer_count, supplier_count = defaultdict(int), defaultdict(int)
        for move in to_post:
            ######### Freaa Added to link Move Id with Sukuk number
            discription = False
            for ll in move.line_ids:
                search_for_journal = self.env['account.journal'].search([('default_account_id', '=', ll.account_id.id)])
                if search_for_journal.suke_id:
                    discription = 'check' + '-' + str(search_for_journal.suke_id.bank_id.name) + '-' + str(
                        search_for_journal.suke_id.branch_id.name) + '- Serial No:' + str(
                        search_for_journal.suke_id.serial_no) + '-' + str(search_for_journal.suke_id.amount)
            for lll in move.line_ids:
                if discription:
                    lll.name = discription
            ### done

            if move.is_sale_document():
                customer_count[move.partner_id] += 1
            elif move.is_purchase_document():
                supplier_count[move.partner_id] += 1
        for partner, count in customer_count.items():
            (partner | partner.commercial_partner_id)._increase_rank('customer_rank', count)
        for partner, count in supplier_count.items():
            (partner | partner.commercial_partner_id)._increase_rank('supplier_rank', count)

        # Trigger action for paid invoices in amount is zero
        to_post.filtered(
            lambda m: m.is_invoice(include_receipts=True) and m.currency_id.is_zero(m.amount_total)
        ).action_invoice_paid()

        # Force balance check since nothing prevents another module to create an incorrect entry.
        # This is performed at the very end to avoid flushing fields before the whole processing.
        to_post._check_balanced()
        return to_post

