<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="mail.MessageInReplyToView" owl="1">
        <small class="o_MessageInReplyToView text-small pt-1 pr-2 position-relative">
            <t t-if="messageInReplyToView">
                <t t-if="!messageInReplyToView.messageView.message.parentMessage.isEmpty">
                    <b class="o_MessageInReplyToView_author text-muted ml-2">@<t t-esc="messageInReplyToView.messageView.message.parentMessage.authorName"/></b>
                    <br t-if="messageInReplyToView and messageInReplyToView.messageView.threadView.threadViewer.chatWindow"/>
                    <span class="o_MessageInReplyToView_body ml-1" t-on-click="messageInReplyToView.onClickReply">
                        <t t-if="messageInReplyToView.hasBodyBackLink">
                            <t t-raw="messageInReplyToView.messageView.message.parentMessage.prettyBody"/>
                        </t>
                        <t t-if="messageInReplyToView.hasAttachmentBackLink">
                            <span class="font-italic mr-2">Click to see the attachments</span>
                            <i class="fa fa-image"/>
                        </t>
                    </span>
                </t>
                <t t-if="messageInReplyToView.messageView.message.parentMessage.isEmpty">
                    <i class="o_MessageInReplyToView_deletedMessage text-muted ml-2">Original message was deleted</i>
                </t>
            </t>
        </small>
    </t>
</templates>
