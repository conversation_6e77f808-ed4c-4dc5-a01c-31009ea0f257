<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="tax_report_vat" model="account.tax.report">
        <field name="name">VAT Report</field>
        <field name="country_id" ref="base.rs"/>
    </record>

    <record id="tax_report_title_operations" model="account.tax.report.line">
        <field name="name">Amount of fee without VAT</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="sequence">1</field>
        <field name="formula">None</field>
    </record>

    <record id="tax_report_title_operations_turnover" model="account.tax.report.line">
        <field name="name">I. Trade of goods and services</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations"/>
        <field name="sequence">1</field>
        <field name="formula">None</field>
    </record>

    <record id="tax_report_line_001" model="account.tax.report.line">
        <field name="name">001 - Turnover of goods and services exempt from VAT with the right to deduct previous tax</field>
        <field name="code">c001</field>
        <field name="tag_name">001</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations_turnover"/>
        <field name="sequence">1</field>
    </record>

    <record id="tax_report_line_002" model="account.tax.report.line">
        <field name="name">002 - Turnover of goods and services exempt from VAT without the right to deduct previous tax</field>
        <field name="code">c002</field>
        <field name="tag_name">002</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations_turnover"/>
        <field name="sequence">2</field>
    </record>

    <record id="tax_report_line_003" model="account.tax.report.line">
        <field name="name">003 - Turnover of goods and services at the general rate</field>
        <field name="code">c003</field>
        <field name="tag_name">003</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations_turnover"/>
        <field name="sequence">3</field>
    </record>

    <record id="tax_report_line_004" model="account.tax.report.line">
        <field name="name">004 - Turnover of goods and services at the special rate</field>
        <field name="code">c004</field>
        <field name="tag_name">004</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations_turnover"/>
        <field name="sequence">4</field>
    </record>

    <record id="tax_report_line_005" model="account.tax.report.line">
        <field name="name">005 - Total (001 + 002 + 003 + 004)</field>
        <field name="formula">c001 + c002 + c003 + c004</field>
        <field name="code">c005</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations_turnover"/>
        <field name="sequence">5</field>
    </record>

    <record id="tax_report_title_operations_previous_tax" model="account.tax.report.line">
        <field name="name">II. Previous Tax</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations"/>
        <field name="sequence">2</field>
        <field name="formula">None</field>
    </record>

    <record id="tax_report_line_006" model="account.tax.report.line">
        <field name="name">006 - Previous tax paid upon import</field>
        <field name="code">c006</field>
        <field name="tag_name">006</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations_previous_tax"/>
        <field name="sequence">1</field>
    </record>

    <record id="tax_report_line_007" model="account.tax.report.line">
        <field name="name">007 - VAT Compensation paid to the farmer</field>
        <field name="code">c007</field>
        <field name="tag_name">007</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations_previous_tax"/>
        <field name="sequence">2</field>
    </record>

    <record id="tax_report_line_008" model="account.tax.report.line">
        <field name="name">008 - Previous tax, except for the previous tax with item no. 6. and 7</field>
        <field name="code">c008</field>
        <field name="tag_name">008</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations_previous_tax"/>
        <field name="sequence">3</field>
    </record>

    <record id="tax_report_line_009" model="account.tax.report.line">
        <field name="name">009 - Total (006 + 007 + 008)</field>
        <field name="formula">c006 + c007 + c008</field>
        <field name="code">c009</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations_previous_tax"/>
        <field name="sequence">4</field>
    </record>

    <record id="tax_report_title_VAT" model="account.tax.report.line">
        <field name="name">VAT</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="sequence">2</field>
        <field name="formula">None</field>
    </record>

    <record id="tax_report_title_VAT_turnover" model="account.tax.report.line">
        <field name="name">I. Trade of goods and services</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_VAT"/>
        <field name="sequence">1</field>
        <field name="formula">None</field>
    </record>

    <record id="tax_report_line_103" model="account.tax.report.line">
        <field name="name">103 - Turnover of goods and services at the general rate</field>
        <field name="code">c103</field>
        <field name="tag_name">103</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_VAT_turnover"/>
        <field name="sequence">1</field>
    </record>

    <record id="tax_report_line_104" model="account.tax.report.line">
        <field name="name">104 - Turnover of goods and services at the special rate</field>
        <field name="code">c104</field>
        <field name="tag_name">104</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_VAT_turnover"/>
        <field name="sequence">2</field>
    </record>

    <record id="tax_report_line_105" model="account.tax.report.line">
        <field name="name">105 - Total (103 + 104)</field>
        <field name="formula">c103 + c104</field>
        <field name="code">c105</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_VAT_turnover"/>
        <field name="sequence">3</field>
    </record>

    <record id="tax_report_title_VAT_previous_tax" model="account.tax.report.line">
        <field name="name">II. Previous Tax</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_VAT"/>
        <field name="sequence">2</field>
        <field name="formula">None</field>
    </record>

    <record id="tax_report_line_106" model="account.tax.report.line">
        <field name="name">106 - Previous tax paid upon import</field>
        <field name="code">c106</field>
        <field name="tag_name">106</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_VAT_previous_tax"/>
        <field name="sequence">1</field>
    </record>

    <record id="tax_report_line_107" model="account.tax.report.line">
        <field name="name">107 - VAT Compensation paid to the farmer</field>
        <field name="code">c107</field>
        <field name="tag_name">107</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_VAT_previous_tax"/>
        <field name="sequence">2</field>
    </record>

    <record id="tax_report_line_108" model="account.tax.report.line">
        <field name="name">108 - Previous tax, except for the previous tax with item no. 6. and 7</field>
        <field name="code">c108</field>
        <field name="tag_name">108</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_VAT_previous_tax"/>
        <field name="sequence">3</field>
    </record>

    <record id="tax_report_line_109" model="account.tax.report.line">
        <field name="name">109 - Total (106 + 107 + 108)</field>
        <field name="formula">c106 + c107 + c108</field>
        <field name="code">c109</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_VAT_previous_tax"/>
        <field name="sequence">4</field>
    </record>

    <record id="tax_report_title_VAT_liability" model="account.tax.report.line">
        <field name="name">III. Tax liability</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_VAT"/>
        <field name="sequence">3</field>
        <field name="formula">None</field>
    </record>

    <record id="tax_report_line_110" model="account.tax.report.line">
        <field name="name">110 - Amount of VAT in tax period (105 - 109)</field>
        <field name="formula">c105 - c109</field>
        <field name="code">c110</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_VAT_liability"/>
        <field name="sequence">1</field>
    </record>
</odoo>
