<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="eg_standard_sale_14" model="account.tax.template">
        <field name="name">VAT 14%</field>
        <field name="type_tax_use">sale</field>
        <field name="amount">14</field>
        <field name="amount_type">percent</field>
        <field name="description">VAT 14%</field>
        <field name="l10n_eg_eta_code">t1_v009</field>
        <field name="tax_group_id" ref="eg_tax_vat"/>
        <field name="chart_template_id" ref="egypt_chart_template_standard"/>
        <field name="invoice_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_vat_return_sale_base_fourteen')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_201017'),
                'plus_report_line_ids': [ref('tax_report_vat_return_sale_tax_fourteen')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_vat_return_sale_base_fourteen')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_201017'),
                'minus_report_line_ids': [ref('tax_report_vat_return_sale_tax_fourteen')],
            }),
        ]"/>
    </record>

    <record id="eg_standard_purchase_14" model="account.tax.template">
        <field name="name">VAT 14%</field>
        <field name="type_tax_use">purchase</field>
        <field name="amount">14</field>
        <field name="amount_type">percent</field>
        <field name="description">VAT 14%</field>
        <field name="l10n_eg_eta_code">t1_v009</field>
        <field name="tax_group_id" ref="eg_tax_vat"/>
        <field name="chart_template_id" ref="egypt_chart_template_standard"/>
        <field name="invoice_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_vat_return_expense_base_fourteen')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_104041'),
                'plus_report_line_ids': [ref('tax_report_vat_return_expense_tax_fourteen')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_vat_return_expense_base_fourteen')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_104041'),
                'minus_report_line_ids': [ref('tax_report_vat_return_expense_tax_fourteen')],
            }),
        ]"/>
    </record>

    <record id="eg_zero_sale_0" model="account.tax.template">
        <field name="name">Zero Rated 0%</field>
        <field name="type_tax_use">sale</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="description">Zero Rated 0%</field>
        <field name="tax_group_id" ref="eg_tax_group_other"/>
        <field name="chart_template_id" ref="egypt_chart_template_standard"/>
        <field name="invoice_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_vat_return_sale_base_zero')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': False,
                'plus_report_line_ids': [ref('tax_report_vat_return_sale_tax_zero')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_vat_return_sale_base_zero')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': False,
                'minus_report_line_ids': [ref('tax_report_vat_return_sale_tax_zero')],
            }),
        ]"/>
    </record>

    <record id="eg_zero_purchase_0" model="account.tax.template">
        <field name="name">Zero Rated 0%</field>
        <field name="type_tax_use">purchase</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="description">Zero Rated 0%</field>
        <field name="tax_group_id" ref="eg_tax_group_other"/>
        <field name="chart_template_id" ref="egypt_chart_template_standard"/>
        <field name="invoice_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_vat_return_expense_base_zero')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': False,
                'plus_report_line_ids': [ref('tax_report_vat_return_expense_tax_zero')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_vat_return_expense_base_zero')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': False,
                'minus_report_line_ids': [ref('tax_report_vat_return_expense_tax_zero')],
            }),
        ]"/>
    </record>

    <record id="eg_exempt_sale" model="account.tax.template">
        <field name="name">Exempt</field>
        <field name="type_tax_use">sale</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="description">Exempt</field>
        <field name="l10n_eg_eta_code">t1_v003</field>
        <field name="tax_group_id" ref="eg_tax_group_other"/>
        <field name="chart_template_id" ref="egypt_chart_template_standard"/>
        <field name="invoice_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_vat_return_sale_base_exempt')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': False,
                'plus_report_line_ids': [ref('tax_report_vat_return_sale_tax_exempt')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_vat_return_sale_base_exempt')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': False,
                'minus_report_line_ids': [ref('tax_report_vat_return_sale_tax_exempt')],
            }),
        ]"/>
    </record>

    <record id="eg_exempt_purchase" model="account.tax.template">
        <field name="name">Exempt</field>
        <field name="type_tax_use">purchase</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="description">Exempt</field>
        <field name="l10n_eg_eta_code">t1_v003</field>
        <field name="tax_group_id" ref="eg_tax_group_other"/>
        <field name="chart_template_id" ref="egypt_chart_template_standard"/>
        <field name="invoice_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_vat_return_expense_base_exempt')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': False,
                'plus_report_line_ids': [ref('tax_report_vat_return_expense_tax_exempt')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_vat_return_expense_base_exempt')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': False,
                'minus_report_line_ids': [ref('tax_report_vat_return_expense_tax_exempt')],
            }),
        ]"/>
    </record>

    <record id="eg_stamp_tax_20_sale" model="account.tax.template">
        <field name="name">Stamp</field>
        <field name="type_tax_use">sale</field>
        <field name="amount">20</field>
        <field name="amount_type">percent</field>
        <field name="description">Stamp</field>
        <field name="l10n_eg_eta_code">t5_st01</field>
        <field name="tax_group_id" ref="eg_tax_group_stamp"/>
        <field name="chart_template_id" ref="egypt_chart_template_standard"/>
        <field name="invoice_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_other_taxes_stamp_tax_base_sales')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_201025'),
                'plus_report_line_ids': [ref('tax_report_other_taxes_stamp_tax_tax_sales')],
                'use_in_tax_closing': False,
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_other_taxes_stamp_tax_base_sales')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_201025'),
                'minus_report_line_ids': [ref('tax_report_other_taxes_stamp_tax_tax_sales')],
                'use_in_tax_closing': False,
            }),
        ]"/>
    </record>

    <record id="eg_stamp_tax_20_purchase" model="account.tax.template">
        <field name="name">Stamp</field>
        <field name="type_tax_use">purchase</field>
        <field name="amount">20</field>
        <field name="amount_type">percent</field>
        <field name="description">Stamp</field>
        <field name="l10n_eg_eta_code">t5_st01</field>
        <field name="tax_group_id" ref="eg_tax_group_stamp"/>
        <field name="chart_template_id" ref="egypt_chart_template_standard"/>
        <field name="invoice_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_other_taxes_stamp_purchase_tax_base_purchase')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_400077'),
                'plus_report_line_ids': [ref('tax_report_other_taxes_stamp_purchase_tax_tax_purchase')],
                'use_in_tax_closing': False,
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_other_taxes_stamp_purchase_tax_base_purchase')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_400077'),
                'minus_report_line_ids': [ref('tax_report_other_taxes_stamp_purchase_tax_tax_purchase')],
                'use_in_tax_closing': False,
            }),
        ]"/>
    </record>

    <record id="eg_schedule_tax_8_purchase" model="account.tax.template">
        <field name="name">Schedule 8%</field>
        <field name="type_tax_use">purchase</field>
        <field name="amount">8</field>
        <field name="amount_type">percent</field>
        <field name="description">SCHD 8%</field>
        <field name="l10n_eg_eta_code">t2_tbl01</field>
        <field name="tax_group_id" ref="eg_tax_group_schedule_8"/>
        <field name="chart_template_id" ref="egypt_chart_template_standard"/>
        <field name="invoice_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_purchase_base_eight')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_400075'),
                'plus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_purchase_tax_eight')],
                'use_in_tax_closing': False,
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_purchase_base_eight')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_400075'),
                'minus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_purchase_tax_eight')],
                'use_in_tax_closing': False,
            }),
        ]"/>
    </record>

    <record id="eg_schedule_tax_8_sale" model="account.tax.template">
        <field name="name">Schedule 8%</field>
        <field name="type_tax_use">sale</field>
        <field name="amount">8</field>
        <field name="amount_type">percent</field>
        <field name="description">SCHD 8%</field>
        <field name="l10n_eg_eta_code">t2_tbl01</field>
        <field name="tax_group_id" ref="eg_tax_group_schedule_8"/>
        <field name="chart_template_id" ref="egypt_chart_template_standard"/>
        <field name="invoice_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_sale_base_eight')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_201024'),
                'plus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_sale_tax_eight')],
                'use_in_tax_closing': False,
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_sale_base_eight')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_201024'),
                'minus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_sale_tax_eight')],
                'use_in_tax_closing': False,
            }),
        ]"/>
    </record>

    <record id="eg_withholding_1_sale" model="account.tax.template">
        <field name="name">Withholding -1%</field>
        <field name="type_tax_use">sale</field>
        <field name="amount">-1</field>
        <field name="amount_type">percent</field>
        <field name="description">WH -1%</field>
        <field name="tax_group_id" ref="eg_tax_group_withholding_1"/>
        <field name="chart_template_id" ref="egypt_chart_template_standard"/>
        <field name="invoice_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_withholding_tax_sale_base_one')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_104042'),
                'minus_report_line_ids': [ref('tax_report_withholding_tax_sale_tax_one')],
                'use_in_tax_closing': False,
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_withholding_tax_sale_base_one')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_104042'),
                'plus_report_line_ids': [ref('tax_report_withholding_tax_sale_tax_one')],
                'use_in_tax_closing': False,
            }),
        ]"/>
    </record>

    <record id="eg_schedule_tax_10_purchase" model="account.tax.template">
        <field name="name">Schedule 10%</field>
        <field name="type_tax_use">purchase</field>
        <field name="amount">10</field>
        <field name="amount_type">percent</field>
        <field name="description">SCHD 10%</field>
        <field name="tax_group_id" ref="eg_tax_group_schedule_10"/>
        <field name="chart_template_id" ref="egypt_chart_template_standard"/>
        <field name="invoice_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_purchase_base_ten')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_400075'),
                'plus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_purchase_tax_ten')],
                'use_in_tax_closing': False,
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_purchase_base_ten')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_400075'),
                'minus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_purchase_tax_ten')],
                'use_in_tax_closing': False,
            }),
        ]"/>
    </record>

    <record id="eg_withholding_05_sale" model="account.tax.template">
        <field name="name">Withholding -0.5%</field>
        <field name="type_tax_use">sale</field>
        <field name="amount">-0.5</field>
        <field name="amount_type">percent</field>
        <field name="description">WH -0.5%</field>
        <field name="tax_group_id" ref="eg_tax_group_withholding_half"/>
        <field name="chart_template_id" ref="egypt_chart_template_standard"/>
        <field name="invoice_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_withholding_tax_sale_base_half')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_104042'),
                'minus_report_line_ids': [ref('tax_report_withholding_tax_sale_tax_half')],
                'use_in_tax_closing': False,
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_withholding_tax_sale_base_half')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_104042'),
                'plus_report_line_ids': [ref('tax_report_withholding_tax_sale_tax_half')],
                'use_in_tax_closing': False,
            }),
        ]"/>
    </record>

    <record id="eg_withholding_05_purchase" model="account.tax.template">
        <field name="name">Withholding -0.5%</field>
        <field name="type_tax_use">purchase</field>
        <field name="amount">-0.5</field>
        <field name="amount_type">percent</field>
        <field name="description">WH -0.5%</field>
        <field name="tax_group_id" ref="eg_tax_group_withholding_half"/>
        <field name="chart_template_id" ref="egypt_chart_template_standard"/>
        <field name="invoice_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_withholding_tax_purchase_base_half')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_201020'),
                'minus_report_line_ids': [ref('tax_report_withholding_tax_purchase_tax_half')],
                'use_in_tax_closing': False,
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_withholding_tax_purchase_base_half')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_201020'),
                'plus_report_line_ids': [ref('tax_report_withholding_tax_purchase_tax_half')],
                'use_in_tax_closing': False,
            }),
        ]"/>
    </record>

    <record id="eg_withholding_1_purchase" model="account.tax.template">
        <field name="name">Withholding -1%</field>
        <field name="type_tax_use">purchase</field>
        <field name="amount">-1</field>
        <field name="amount_type">percent</field>
        <field name="description">WH -1%</field>
        <field name="tax_group_id" ref="eg_tax_group_withholding_1"/>
        <field name="chart_template_id" ref="egypt_chart_template_standard"/>
        <field name="invoice_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_withholding_tax_purchase_base_one')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_201020'),
                'minus_report_line_ids': [ref('tax_report_withholding_tax_purchase_tax_one')],
                'use_in_tax_closing': False,
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_withholding_tax_purchase_base_one')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_201020'),
                'plus_report_line_ids': [ref('tax_report_withholding_tax_purchase_tax_one')],
                'use_in_tax_closing': False,
            }),
        ]"/>
    </record>

    <record id="eg_schedule_tax_10_sale" model="account.tax.template">
        <field name="name">Schedule 10%</field>
        <field name="type_tax_use">sale</field>
        <field name="amount">10</field>
        <field name="amount_type">percent</field>
        <field name="description">SCHD 10%</field>
        <field name="tax_group_id" ref="eg_tax_group_schedule_10"/>
        <field name="chart_template_id" ref="egypt_chart_template_standard"/>
        <field name="invoice_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_sale_base_ten')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_201024'),
                'plus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_sale_tax_ten')],
                'use_in_tax_closing': False,
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_sale_base_ten')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_201024'),
                'minus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_sale_tax_ten')],
                'use_in_tax_closing': False,
            }),
        ]"/>
    </record>

    <record id="eg_withholding_3_sale" model="account.tax.template">
        <field name="name">Withholding -3%</field>
        <field name="type_tax_use">sale</field>
        <field name="amount">-3</field>
        <field name="amount_type">percent</field>
        <field name="description">WH -3%</field>
        <field name="l10n_eg_eta_code">t4_w004</field>
        <field name="tax_group_id" ref="eg_tax_group_withholding_3"/>
        <field name="chart_template_id" ref="egypt_chart_template_standard"/>
        <field name="invoice_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_withholding_tax_sale_base_three')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_104042'),
                'minus_report_line_ids': [ref('tax_report_withholding_tax_sale_tax_three')],
                'use_in_tax_closing': False,
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_withholding_tax_sale_base_three')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_104042'),
                'plus_report_line_ids': [ref('tax_report_withholding_tax_sale_tax_three')],
                'use_in_tax_closing': False,
            }),
        ]"/>
    </record>

    <record id="eg_schedule_tax_1_sale" model="account.tax.template">
        <field name="name">Schedule 1%</field>
        <field name="type_tax_use">sale</field>
        <field name="amount">1</field>
        <field name="amount_type">percent</field>
        <field name="description">SCHD 1%</field>
        <field name="tax_group_id" ref="eg_tax_group_schedule_1"/>
        <field name="chart_template_id" ref="egypt_chart_template_standard"/>
        <field name="invoice_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_sale_base_one')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_201024'),
                'plus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_sale_tax_one')],
                'use_in_tax_closing': False,
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_sale_base_one')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_201024'),
                'minus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_sale_tax_one')],
                'use_in_tax_closing': False,
            }),
        ]"/>
    </record>

    <record id="eg_withholding_3_purchase" model="account.tax.template">
        <field name="name">Withholding -3%</field>
        <field name="type_tax_use">purchase</field>
        <field name="amount">-3</field>
        <field name="amount_type">percent</field>
        <field name="description">WH -3%</field>
        <field name="l10n_eg_eta_code">t4_w004</field>
        <field name="tax_group_id" ref="eg_tax_group_withholding_3"/>
        <field name="chart_template_id" ref="egypt_chart_template_standard"/>
        <field name="invoice_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_withholding_tax_purchase_base_three')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_201020'),
                'minus_report_line_ids': [ref('tax_report_withholding_tax_purchase_tax_three')],
                'use_in_tax_closing': False,
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_withholding_tax_purchase_base_three')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_201020'),
                'plus_report_line_ids': [ref('tax_report_withholding_tax_purchase_tax_three')],
                'use_in_tax_closing': False,
            }),
        ]"/>
    </record>

    <record id="eg_schedule_tax_1_purchase" model="account.tax.template">
        <field name="name">Schedule 1%</field>
        <field name="type_tax_use">purchase</field>
        <field name="amount">1</field>
        <field name="amount_type">percent</field>
        <field name="description">SCHD 1%</field>
        <field name="tax_group_id" ref="eg_tax_group_schedule_1"/>
        <field name="chart_template_id" ref="egypt_chart_template_standard"/>
        <field name="invoice_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_purchase_base_one')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_400075'),
                'plus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_purchase_tax_one')],
                'use_in_tax_closing': False,
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_purchase_base_one')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_400075'),
                'minus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_purchase_tax_one')],
                'use_in_tax_closing': False,
            }),
        ]"/>
    </record>

    <record id="eg_withholding_5_sale" model="account.tax.template">
        <field name="name">Withholding -5%</field>
        <field name="type_tax_use">sale</field>
        <field name="amount">-5</field>
        <field name="amount_type">percent</field>
        <field name="description">WH -5%</field>
        <field name="tax_group_id" ref="eg_tax_group_withholding_5"/>
        <field name="chart_template_id" ref="egypt_chart_template_standard"/>
        <field name="invoice_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_withholding_tax_sale_base_five')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_104042'),
                'minus_report_line_ids': [ref('tax_report_withholding_tax_sale_tax_five')],
                'use_in_tax_closing': False,
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_withholding_tax_sale_base_five')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_104042'),
                'plus_report_line_ids': [ref('tax_report_withholding_tax_sale_tax_five')],
                'use_in_tax_closing': False,
            }),
        ]"/>
    </record>

    <record id="eg_schedule_tax_15_purchase" model="account.tax.template">
        <field name="name">Schedule 15%</field>
        <field name="type_tax_use">purchase</field>
        <field name="amount">15</field>
        <field name="amount_type">percent</field>
        <field name="description">SCHD 15%</field>
        <field name="tax_group_id" ref="eg_tax_group_schedule_15"/>
        <field name="chart_template_id" ref="egypt_chart_template_standard"/>
        <field name="invoice_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_purchase_base_fifteen')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_400075'),
                'plus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_purchase_tax_fifteen')],
                'use_in_tax_closing': False,
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_purchase_base_fifteen')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_400075'),
                'minus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_purchase_tax_fifteen')],
                'use_in_tax_closing': False,
            }),
        ]"/>
    </record>

    <record id="eg_withholding_5_purchase" model="account.tax.template">
        <field name="name">Withholding -5%</field>
        <field name="type_tax_use">purchase</field>
        <field name="amount">-5</field>
        <field name="amount_type">percent</field>
        <field name="description">WH -5%</field>
        <field name="tax_group_id" ref="eg_tax_group_withholding_5"/>
        <field name="chart_template_id" ref="egypt_chart_template_standard"/>
        <field name="invoice_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_withholding_tax_purchase_base_five')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_201020'),
                'minus_report_line_ids': [ref('tax_report_withholding_tax_purchase_tax_five')],
                'use_in_tax_closing': False,
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_withholding_tax_purchase_base_five')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_201020'),
                'plus_report_line_ids': [ref('tax_report_withholding_tax_purchase_tax_five')],
                'use_in_tax_closing': False,
            }),
        ]"/>
    </record>

    <record id="eg_schedule_tax_15_sale" model="account.tax.template">
        <field name="name">Schedule 15%</field>
        <field name="type_tax_use">sale</field>
        <field name="amount">15</field>
        <field name="amount_type">percent</field>
        <field name="description">SCHD 15%</field>
        <field name="tax_group_id" ref="eg_tax_group_schedule_15"/>
        <field name="chart_template_id" ref="egypt_chart_template_standard"/>
        <field name="invoice_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_sale_base_fifteen')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_201024'),
                'plus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_sale_tax_fifteen')],
                'use_in_tax_closing': False,
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_sale_base_fifteen')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_201024'),
                'minus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_sale_tax_fifteen')],
                'use_in_tax_closing': False,
            }),
        ]"/>
    </record>

    <record id="eg_schedule_tax_30_sale" model="account.tax.template">
        <field name="name">Schedule 30%</field>
        <field name="type_tax_use">sale</field>
        <field name="amount">30</field>
        <field name="amount_type">percent</field>
        <field name="description">SCHD 30%</field>
        <field name="tax_group_id" ref="eg_tax_group_schedule_30"/>
        <field name="chart_template_id" ref="egypt_chart_template_standard"/>
        <field name="invoice_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_sale_base_thirty')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_201024'),
                'plus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_sale_tax_thirty')],
                'use_in_tax_closing': False,
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_sale_base_thirty')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_201024'),
                'minus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_sale_tax_thirty')],
                'use_in_tax_closing': False,
            }),
        ]"/>
    </record>

    <record id="eg_schedule_tax_30_purchase" model="account.tax.template">
        <field name="name">Schedule 30%</field>
        <field name="type_tax_use">purchase</field>
        <field name="amount">30</field>
        <field name="amount_type">percent</field>
        <field name="description">SCHD 30%</field>
        <field name="tax_group_id" ref="eg_tax_group_schedule_30"/>
        <field name="chart_template_id" ref="egypt_chart_template_standard"/>
        <field name="invoice_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_purchase_base_thirty')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_400075'),
                'plus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_purchase_tax_thirty')],
                'use_in_tax_closing': False,
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_purchase_base_thirty')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_400075'),
                'minus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_purchase_tax_thirty')],
                'use_in_tax_closing': False,
            }),
        ]"/>
    </record>

    <record id="eg_schedule_tax_05_purchase" model="account.tax.template">
        <field name="name">Schedule 0.5%</field>
        <field name="type_tax_use">purchase</field>
        <field name="amount">0.5</field>
        <field name="amount_type">percent</field>
        <field name="description">SCHD 0.5%</field>
        <field name="tax_group_id" ref="eg_tax_group_schedule_half"/>
        <field name="chart_template_id" ref="egypt_chart_template_standard"/>
        <field name="invoice_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_purchase_base_half')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_400075'),
                'plus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_purchase_tax_half')],
                'use_in_tax_closing': False,
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_purchase_base_half')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_400075'),
                'minus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_purchase_tax_half')],
                'use_in_tax_closing': False,
            }),
        ]"/>
    </record>

    <record id="eg_schedule_tax_05_sale" model="account.tax.template">
        <field name="name">Schedule 0.5%</field>
        <field name="type_tax_use">sale</field>
        <field name="amount">0.5</field>
        <field name="amount_type">percent</field>
        <field name="description">SCHD 0.5%</field>
        <field name="tax_group_id" ref="eg_tax_group_schedule_half"/>
        <field name="chart_template_id" ref="egypt_chart_template_standard"/>
        <field name="invoice_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_sale_base_half')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_201024'),
                'plus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_sale_tax_half')],
                'use_in_tax_closing': False,
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[Command.clear(),
             Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_sale_base_half')],
            }),
             Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_201024'),
                'minus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_sale_tax_half')],
                'use_in_tax_closing': False,
            }),
        ]"/>
    </record>

    <record id="eg_schedule_tax_5_purchase" model="account.tax.template">
        <field name="name">Schedule 5%</field>
        <field name="type_tax_use">purchase</field>
        <field name="amount">5</field>
        <field name="amount_type">percent</field>
        <field name="description">SCHD 5%</field>
        <field name="tax_group_id" ref="eg_tax_group_schedule_5"/>
        <field name="chart_template_id" ref="egypt_chart_template_standard"/>
        <field name="invoice_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_purchase_base_five')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_400075'),
                'plus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_purchase_tax_five')],
                'use_in_tax_closing': False,
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_purchase_base_five')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_400075'),
                'minus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_purchase_tax_five')],
                'use_in_tax_closing': False,
            }),
        ]"/>
    </record>

    <record id="eg_schedule_tax_5_sale" model="account.tax.template">
        <field name="name">Schedule 5%</field>
        <field name="type_tax_use">sale</field>
        <field name="amount">5</field>
        <field name="amount_type">percent</field>
        <field name="description">SCHD 5%</field>
        <field name="tax_group_id" ref="eg_tax_group_schedule_5"/>
        <field name="chart_template_id" ref="egypt_chart_template_standard"/>
        <field name="invoice_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_sale_base_five')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_201024'),
                'plus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_sale_tax_five')],
                'use_in_tax_closing': False,
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[Command.clear(),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_sale_base_five')],
            }),
            Command.create({
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('egy_account_201024'),
                'minus_report_line_ids': [ref('tax_report_schedule_tax_schedule_tax_sale_tax_five')],
                'use_in_tax_closing': False,
            }),
        ]"/>
    </record>
</odoo>
