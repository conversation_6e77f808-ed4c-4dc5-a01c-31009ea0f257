# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* board
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:21+0000\n"
"Last-Translator: Kevilyn Rosa, 2023\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: board
#. openerp-web
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
#, python-format
msgid "\"%s\" added to dashboard"
msgstr "\"%s\" adicionado ao painel"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid ""
"\"Add to\n"
"                Dashboard\""
msgstr ""
"\"Adicione ao\n"
"                Painel\""

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "&nbsp;"
msgstr "&nbsp;"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/js/add_to_board_menu.js:0
#, python-format
msgid "'%s' added to dashboard"
msgstr "'%s' adicionado ao painel"

#. module: board
#. openerp-web
#: code:addons/board/static/src/add_to_board/add_to_board.xml:0
#: code:addons/board/static/src/legacy/xml/board.xml:0
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Add"
msgstr "Adicionar"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Add to my Dashboard"
msgstr "Adicionar ao Meu Painel"

#. module: board
#. openerp-web
#: code:addons/board/static/src/add_to_board/add_to_board.xml:0
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Add to my dashboard"
msgstr "Adicionar ao meu painel"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/js/board_view.js:0
#, python-format
msgid "Are you sure you want to remove this item?"
msgstr "Você tem certeza que deseja remover este item?"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/js/board_view.js:0
#: model:ir.model,name:board.model_board_board
#, python-format
msgid "Board"
msgstr "Painel"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Change Layout"
msgstr "Alterar Layout"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Change Layout.."
msgstr "Alterar Layout."

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Choose dashboard layout"
msgstr "Escolha o Layout do Painel"

#. module: board
#. openerp-web
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
#: code:addons/board/static/src/legacy/js/add_to_board_menu.js:0
#, python-format
msgid "Could not add filter to dashboard"
msgstr "Não foi possível adicionar o filtro ao painel"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/js/board_view.js:0
#, python-format
msgid "Edit Layout"
msgstr "Editar Layout"

#. module: board
#: model:ir.model.fields,field_description:board.field_board_board__id
msgid "ID"
msgstr "ID"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Layout"
msgstr "Layout"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/js/board_view.js:0
#: model:ir.actions.act_window,name:board.open_board_my_dash_action
#: model:ir.ui.menu,name:board.menu_board_my_dash
#: model_terms:ir.ui.view,arch_db:board.board_my_dash_view
#, python-format
msgid "My Dashboard"
msgstr "Meu Painel"

#. module: board
#. openerp-web
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
#: code:addons/board/static/src/legacy/js/add_to_board_menu.js:0
#, python-format
msgid "Please refresh your browser for the changes to take effect."
msgstr ""
"Por gentileza, recarregue a página para que as alterações surtam efeito."

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid ""
"To add your first report into this dashboard, go to any\n"
"                menu, switch to list or graph view, and click"
msgstr ""
"Para adicionar seu primeiro relatório neste painel, acesse qualquer\n"
"menu, troque para a exibição de lista ou gráfico e clique"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid ""
"You can filter and group data before inserting into the\n"
"                dashboard using the search options."
msgstr ""
"Você pode filtrar e agrupar dados antes de inserir no\n"
"painel usando as opções de pesquisa."

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "Your personal dashboard is empty"
msgstr "Seu Painel Pessoal está vazio"

#. module: board
#. openerp-web
#: code:addons/board/static/src/legacy/xml/board.xml:0
#, python-format
msgid "in the extended search options."
msgstr "nas opções estendidas de pesquisa."
