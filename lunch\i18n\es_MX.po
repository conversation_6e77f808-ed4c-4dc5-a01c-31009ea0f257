# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* lunch
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_temaki
msgid "1 Avocado - 1 Salmon - 1 Eggs - 1 Tuna"
msgstr "1 Aguacate - 1 Salmón - 1 Huevos- 1 Atún"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_chirashi
msgid "2 Tempuras, Cabbages, Onions, Sesame Sauce"
msgstr "2 Tempuras, coles, cebollas, salsa de sésamo"

#. module: lunch
#: model:lunch.order,name:lunch.order_line_5
#: model:lunch.product,name:lunch.product_4formaggi
msgid "4 Formaggi"
msgstr "4 Formaggi"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_salmon
msgid "4 Sushi Salmon - 6 Maki Salmon - 4 Sashimi Salmon"
msgstr "4 Sushi de salmón - 6 Maki de salmón - 4 Sashimi de salmón"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_maki
msgid "6 Maki Salmon - 6 Maki Tuna - 6 Maki Shrimp/Avocado"
msgstr "6 Maki de salmón - 6 Maki de atún - 6 Maki de camarón y aguacate"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
msgid ""
"<i class=\"fa fa-check\" role=\"img\" aria-label=\"Receive button\" "
"title=\"Receive button\"/>"
msgstr ""
"<i class=\"fa fa-check\" role=\"img\" aria-label=\"Receive button\" "
"title=\"Botón de recibir\"/>"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
#: model_terms:ir.ui.view,arch_db:lunch.view_lunch_cashmove_kanban
#: model_terms:ir.ui.view,arch_db:lunch.view_lunch_cashmove_report_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Fecha\"/>"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.view_lunch_cashmove_kanban
#: model_terms:ir.ui.view,arch_db:lunch.view_lunch_cashmove_report_kanban
msgid "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Amount\" title=\"Amount\"/>"
msgstr "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Amount\" title=\"Importe\"/>"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
msgid "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Money\" title=\"Money\"/>"
msgstr "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Money\" title=\"Dinero\"/>"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
msgid ""
"<i class=\"fa fa-phone\" role=\"img\" aria-label=\"Order button\" "
"title=\"Order button\"/>"
msgstr ""
"<i class=\"fa fa-phone\" role=\"img\" aria-label=\"Order button\" "
"title=\"Botón de ordenar\"/>"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
msgid ""
"<i class=\"fa fa-times\" role=\"img\" aria-label=\"Cancel button\" "
"title=\"Cancel button\"/>"
msgstr ""
"<i class=\"fa fa-times\" role=\"img\" aria-label=\"Cancel button\" "
"title=\"Botón de cancelar\"/>"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Lunch Overdraft</span>\n"
"                                <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"o_form_label\">Sobregiro de almuerzo</span>\n"
"                                <span class=\"fa fa-lg fa-building-o\" title=\"Los valores establecidos aquí son específicos de la empresa.\" aria-label=\"Los valores establecidos aquí son específicos de la empresa.\" groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: lunch
#: model:mail.template,body_html:lunch.lunch_order_mail_supplier
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Lunch Order</span><br/>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ user.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"user.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"    <div>\n"
"        <t t-set=\"lines\" t-value=\"ctx.get('lines', [])\"/>\n"
"        <t t-set=\"order\" t-value=\"ctx.get('order')\"/>\n"
"        <t t-set=\"currency\" t-value=\"user.env['res.currency'].browse(order.get('currency_id'))\"/>\n"
"        <p>\n"
"        Dear <t t-out=\"order.get('supplier_name', '')\">Laurie Poiret</t>,\n"
"        </p><p>\n"
"        Here is, today orders for <t t-out=\"order.get('company_name', '')\">LunchCompany</t>:\n"
"        </p>\n"
"\n"
"        <t t-if=\"sites\">\n"
"            <br/>\n"
"            <p>Location</p>\n"
"            <t t-foreach=\"site\" t-as=\"site\">\n"
"                <p><t t-out=\"site['name'] or ''\"/> : <t t-out=\"site['address'] or ''\"/></p>\n"
"            </t>\n"
"            <br/>\n"
"        </t>\n"
"\n"
"        <table>\n"
"            <thead>\n"
"                <tr style=\"background-color:rgb(233,232,233);\">\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\"><strong>Product</strong></th>\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\"><strong>Comments</strong></th>\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\"><strong>Person</strong></th>\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\"><strong>Site</strong></th>\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\" align=\"center\"><strong>Qty</strong></th>\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\" align=\"center\"><strong>Price</strong></th>\n"
"                </tr>\n"
"            </thead>\n"
"            <tbody>\n"
"                <tr t-foreach=\"lines\" t-as=\"line\">\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\" t-out=\"line['product'] or ''\">Sushi salmon</td>\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\">\n"
"                    <t t-if=\"line['toppings']\">\n"
"                        <t t-out=\"line['toppings'] or ''\">Soy sauce</t>\n"
"                    </t>\n"
"                    <t t-if=\"line['note']\">\n"
"                        <div style=\"color: rgb(173,181,189);\" t-out=\"line['note'] or ''\">With wasabi.</div>\n"
"                    </t>\n"
"                    </td>\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\" t-out=\"line['username'] or ''\">lap</td>\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\" t-out=\"line['site'] or ''\">Office 1</td>\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\" align=\"right\" t-out=\"line['quantity'] or ''\">10</td>\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\" align=\"right\" t-out=\"format_amount(line['price'], currency) or ''\">$ 1.00</td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td/>\n"
"                    <td/>\n"
"                    <td/>\n"
"                    <td/>\n"
"                    <td style=\"width: 100%; font-size: 13px; border-top: 1px solid black;\"><strong>Total</strong></td>\n"
"                    <td style=\"width: 100%; font-size: 13px; border-top: 1px solid black;\" align=\"right\"><strong t-out=\"format_amount(order['amount_total'], currency) or ''\">$ 10.00</strong></td>\n"
"                </tr>\n"
"            </tbody>\n"
"        </table>\n"
"\n"
"        <p>Do not hesitate to contact us if you have any questions.</p>\n"
"    </div>\n"
"                    </td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td style=\"text-align:center;\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"user.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"user.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"user.company_id.phone and (user.company_id.email or user.company_id.website)\">|</t>\n"
"                    <t t-if=\"user.company_id.email\">\n"
"                        <a t-attf-href=\"'mailto:%s' % {{ user.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"user.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"user.company_id.email and user.company_id.website\">|</t>\n"
"                    <t t-if=\"user.company_id.website\">\n"
"                        <a t-attf-href=\"'%s' % {{ user.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"user.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"        "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- ENCABEZADO -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Orden de almuerzo</span><br/>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ user.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"user.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENIDO -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"    <div>\n"
"        <t t-set=\"lines\" t-value=\"ctx.get('lines', [])\"/>\n"
"        <t t-set=\"order\" t-value=\"ctx.get('order')\"/>\n"
"        <t t-set=\"currency\" t-value=\"user.env['res.currency'].browse(order.get('currency_id'))\"/>\n"
"        <p>\n"
"        Apreciable <t t-out=\"order.get('supplier_name', '')\">Laurie Poiret</t>,\n"
"        </p><p>\n"
"        Aquí está su orden para <t t-out=\"order.get('company_name', '')\">LunchCompany</t>:\n"
"        </p>\n"
"\n"
"        <t t-if=\"sites\">\n"
"            <br/>\n"
"            <p>Ubicación</p>\n"
"            <t t-foreach=\"site\" t-as=\"site\">\n"
"                <p><t t-out=\"site['name'] or ''\"/> : <t t-out=\"site['address'] or ''\"/></p>\n"
"            </t>\n"
"            <br/>\n"
"        </t>\n"
"\n"
"        <table>\n"
"            <thead>\n"
"                <tr style=\"background-color:rgb(233,232,233);\">\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\"><strong>Producto</strong></th>\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\"><strong>Comentarios</strong></th>\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\"><strong>Persona</strong></th>\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\"><strong>Sitio</strong></th>\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\" align=\"center\"><strong>Cantidad</strong></th>\n"
"                    <th style=\"width: 100%; min-width: 96px; font-size: 13px;\" align=\"center\"><strong>Precio</strong></th>\n"
"                </tr>\n"
"            </thead>\n"
"            <tbody>\n"
"                <tr t-foreach=\"lines\" t-as=\"line\">\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\" t-out=\"line['product'] or ''\">Sushi de salmón</td>\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\">\n"
"                    <t t-if=\"line['toppings']\">\n"
"                        <t t-out=\"line['toppings'] or ''\">Salsa de soya</t>\n"
"                    </t>\n"
"                    <t t-if=\"line['note']\">\n"
"                        <div style=\"color: rgb(173,181,189);\" t-out=\"line['note'] or ''\">Con wasabi.</div>\n"
"                    </t>\n"
"                    </td>\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\" t-out=\"line['username'] or ''\">Compras dentro de la aplicación</td>\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\" t-out=\"line['site'] or ''\">Oficina 1</td>\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\" align=\"right\" t-out=\"line['quantity'] or ''\">10</td>\n"
"                    <td style=\"width: 100%; font-size: 13px;\" valign=\"top\" align=\"right\" t-out=\"format_amount(line['price'], currency) or ''\">$ 1.00</td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td/>\n"
"                    <td/>\n"
"                    <td/>\n"
"                    <td/>\n"
"                    <td style=\"width: 100%; font-size: 13px; border-top: 1px solid black;\"><strong>Total</strong></td>\n"
"                    <td style=\"width: 100%; font-size: 13px; border-top: 1px solid black;\" align=\"right\"><strong t-out=\"format_amount(order['amount_total'], currency) or ''\">$ 10.00</strong></td>\n"
"                </tr>\n"
"            </tbody>\n"
"        </table>\n"
"\n"
"        <p>No dude en contactarnos si tiene alguna pregunta.</p>\n"
"    </div>\n"
"                    </td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td style=\"text-align:center;\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- PIE -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"user.company_id.name or ''\">SuEmpresa</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"user.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"user.company_id.phone and (user.company_id.email or user.company_id.website)\">|</t>\n"
"                    <t t-if=\"user.company_id.email\">\n"
"                        <a t-attf-href=\"'mailto:%s' % {{ user.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"user.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"user.company_id.email and user.company_id.website\">|</t>\n"
"                    <t t-if=\"user.company_id.website\">\n"
"                        <a t-attf-href=\"'%s' % {{ user.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"user.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- CON TECNOLOGÍA DE -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Con tecnología de <a target=\"_blank\" href=\"https://www.odoo.com\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"        "

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_cashmove_report_action_control_accounts
msgid ""
"A cashmove can either be an expense or a payment.<br>\n"
"            An expense is automatically created at the order receipt.<br>\n"
"            A payment represents the employee reimbursement to the company."
msgstr ""
"Un movimiento de efectivo puede ser un gasto o un pago.<br>\n"
"           Un gasto se crea automáticamente en el recibo de la orden.<br>\n"
"            Un pago representa el reembolso del empleado a la empresa."

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_action
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_action_statbutton
msgid "A product is defined by its name, category, price and vendor."
msgstr "Un producto se define por su nombre, categoría, precio y proveedor."

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_alert__notification_moment__am
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__moment__am
msgid "AM"
msgstr "AM"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_needaction
msgid "Action Needed"
msgstr "Acción requerida"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__active
#: model:ir.model.fields,field_description:lunch.field_lunch_order__active
#: model:ir.model.fields,field_description:lunch.field_lunch_product__active
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__active
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__active
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_search
msgid "Active"
msgstr "Activo"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_ids
msgid "Activities"
msgstr "Actividades"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoración de actividad de excepción"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_state
msgid "Activity State"
msgstr "Estado de la actividad"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icono de tipo de actividad"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_form
msgid "Add To Cart"
msgstr "Agregar al carrito"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_location__address
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "Address"
msgstr "Dirección"

#. module: lunch
#: model:res.groups,name:lunch.group_lunch_manager
msgid "Administrator"
msgstr "Administrador"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__name
msgid "Alert Name"
msgstr "Nombre de la alerta"

#. module: lunch
#: model:lunch.alert,name:lunch.alert_office_3
msgid "Alert for Office 3"
msgstr "Alerta para la oficina 3"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_alert__mode__alert
msgid "Alert in app"
msgstr "Alerta en la aplicación"

#. module: lunch
#: model:ir.ui.menu,name:lunch.lunch_alert_menu
msgid "Alerts"
msgstr "Alertas"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__amount
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove_report__amount
msgid "Amount"
msgstr "Importe"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_category_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_form
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_search
msgid "Archived"
msgstr "Archivado"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_order__available_toppings_1
#: model:ir.model.fields,help:lunch.field_lunch_order__available_toppings_2
#: model:ir.model.fields,help:lunch.field_lunch_order__available_toppings_3
msgid "Are extras available for this product"
msgstr "¿Hay extras disponibles para este producto?"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_attachment_count
msgid "Attachment Count"
msgstr "Número de archivos adjuntos"

#. module: lunch
#: model:ir.model.constraint,message:lunch.constraint_lunch_supplier_automatic_email_time_range
msgid "Automatic Email Sending Time should be between 0 and 12"
msgstr ""
"La hora de envío automático de correos electrónicos debe estar entre 0 y 12"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "Availability"
msgstr "Disponibilidad"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Available Today"
msgstr "Disponible hoy"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__available_toppings_1
msgid "Available Toppings 1"
msgstr "Toppings disponibles 1"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__available_toppings_2
msgid "Available Toppings 2"
msgstr "Toppings disponibles 2"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__available_toppings_3
msgid "Available Toppings 3"
msgstr "Toppings disponibles 3"

#. module: lunch
#: model:lunch.product,name:lunch.product_bacon
#: model:lunch.product,name:lunch.product_bacon_0
msgid "Bacon"
msgstr "Tocino"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_bacon
#: model_terms:lunch.product,description:lunch.product_bacon_0
msgid "Beef, Bacon, Salad, Cheddar, Fried Onion, BBQ Sauce"
msgstr "Res, tocino, ensalada, queso cheddar, cebolla frita, salsa BBQ "

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_cheese_burger_0
#: model_terms:lunch.product,description:lunch.product_cheeseburger
msgid "Beef, Cheddar, Salad, Fried Onions, BBQ Sauce"
msgstr "Res, queso cheddar, ensalada, cebollas fritas, salsa BBQ"

#. module: lunch
#: model:lunch.order,name:lunch.order_line_1
#: model:lunch.product,name:lunch.product_Bolognese
msgid "Bolognese Pasta"
msgstr "Pasta boloñesa"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_country
msgid "Brie, Honey, Walnut Kernels"
msgstr "Queso brie, miel, granos de nuez"

#. module: lunch
#: model:lunch.product.category,name:lunch.categ_burger
msgid "Burger"
msgstr "Hamburguesa"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_search_2
msgid "By Employee"
msgstr "Por empleado"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_search
msgid "By User"
msgstr "Por usuario"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Cancel"
msgstr "Cancelar"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_order__state__cancelled
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "Cancelled"
msgstr "Cancelado"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_cashmove_action_payment
#: model:ir.ui.menu,name:lunch.lunch_cashmove_report_menu_payment
msgid "Cash Moves"
msgstr "Movimientos de efectivo"

#. module: lunch
#: model:ir.model,name:lunch.model_lunch_cashmove_report
msgid "Cashmoves report"
msgstr "Reporte de movimientos de efectivo"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Categories"
msgstr "Categorías"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Category"
msgstr "Categoría"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_alert__mode__chat
msgid "Chat notification"
msgstr "Notificación de chat"

#. module: lunch
#: model:lunch.product,name:lunch.product_cheese_ham
msgid "Cheese And Ham"
msgstr "Queso y jamón"

#. module: lunch
#: model:lunch.product,name:lunch.product_cheese_burger_0
#: model:lunch.product,name:lunch.product_cheeseburger
msgid "Cheese Burger"
msgstr "Hamburguesa con queso"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_cheese_ham
msgid "Cheese, Ham, Salad, Tomatoes, cucumbers, eggs"
msgstr "Queso, jamón, ensalada, tomates, pepinos, huevos"

#. module: lunch
#: model:lunch.order,name:lunch.order_line_4
#: model:lunch.product,name:lunch.product_chicken_curry
msgid "Chicken Curry"
msgstr "Curry de pollo"

#. module: lunch
#: model:lunch.product.category,name:lunch.categ_chirashi
msgid "Chirashi"
msgstr "Chirashi"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__city
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "City"
msgstr "Ciudad"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_action_control_suppliers
msgid ""
"Click on the <span class=\"fa fa-phone text-success\" title=\"Order button\"></span> to announce that the order is ordered.<br>\n"
"            Click on the <span class=\"fa fa-check text-success\" title=\"Receive button\"></span> to announce that the order is received.<br>\n"
"            Click on the <span class=\"fa fa-times-circle text-danger\" title=\"Cancel button\"></span> red X to announce that the order isn't available."
msgstr ""
"Haga clic en <span class=\"fa fa-phone text-success\" title=\"Botón de ordenar\"></span> para anunciar que se realizó la orden.<br>\n"
"            Haga clic en <span class=\"fa fa-check text-success\" title=\"Botón de recibir\"></span> para anunciar que se recibió la orden.<br>\n"
"            Haga clic en la <span class=\"fa fa-times-circle text-danger\" title=\"Botón de cancelar\"></span>  X roja para anunciar que la orden no está disponible."

#. module: lunch
#: model:lunch.product,name:lunch.product_club
#: model:lunch.product,name:lunch.product_club_0
msgid "Club"
msgstr "Club"

#. module: lunch
#: model:lunch.product,name:lunch.product_coke_0
msgid "Coca Cola"
msgstr "Coca Cola"

#. module: lunch
#: model:ir.model,name:lunch.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_location__company_id
#: model:ir.model.fields,field_description:lunch.field_lunch_order__company_id
#: model:ir.model.fields,field_description:lunch.field_lunch_product__company_id
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__company_id
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__company_id
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__company_id
msgid "Company"
msgstr "Empresa"

#. module: lunch
#: model:ir.model,name:lunch.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: lunch
#: model:ir.ui.menu,name:lunch.menu_lunch_config
msgid "Configuration"
msgstr "Configuración"

#. module: lunch
#. openerp-web
#: code:addons/lunch/static/src/js/lunch_controller_common.js:0
#, python-format
msgid "Configure Your Order"
msgstr "Configure su orden"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Confirm"
msgstr "Confirmar"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_cashmove_report_action_control_accounts
#: model:ir.ui.menu,name:lunch.lunch_cashmove_report_menu_control_accounts
msgid "Control Accounts"
msgstr "Cuentas de control"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_order_action_control_suppliers
#: model:ir.ui.menu,name:lunch.lunch_order_menu_control_suppliers
msgid "Control Vendors"
msgstr "Control de proveedores"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__country_id
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "Country"
msgstr "País"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_cashmove_report_action_control_accounts
msgid "Create a new payment"
msgstr "Crear un nuevo pago"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_category_action
msgid "Create a new product category"
msgstr "Crear una nueva categoría de producto"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_action
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_action_statbutton
msgid "Create a new product for lunch"
msgstr "Crear un nuevo producto de almuerzo"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_alert_action
msgid "Create new lunch alerts"
msgstr "Crear nuevas alertas de almuerzo"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__create_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__create_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_location__create_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_order__create_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_product__create_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__create_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__create_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__create_date
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__create_date
#: model:ir.model.fields,field_description:lunch.field_lunch_location__create_date
#: model:ir.model.fields,field_description:lunch.field_lunch_order__create_date
#: model:ir.model.fields,field_description:lunch.field_lunch_product__create_date
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__create_date
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__create_date
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__create_date
msgid "Created on"
msgstr "Creado el"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__cron_id
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__cron_id
msgid "Cron"
msgstr "Cron"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__currency_id
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove_report__currency_id
#: model:ir.model.fields,field_description:lunch.field_lunch_order__currency_id
#: model:ir.model.fields,field_description:lunch.field_lunch_product__currency_id
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__currency_id
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__currency_id
#: model:ir.model.fields,field_description:lunch.field_res_config_settings__currency_id
msgid "Currency"
msgstr "Divisa"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_search
msgid "Currently inactive"
msgstr "Actualmente inactivo"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__date
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove_report__date
msgid "Date"
msgstr "Fecha"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__delivery
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__delivery__delivery
msgid "Delivery"
msgstr "Entrega"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__description
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove_report__description
#: model:ir.model.fields,field_description:lunch.field_lunch_order__product_description
#: model:ir.model.fields,field_description:lunch.field_lunch_product__description
msgid "Description"
msgstr "Descripción"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_form
msgid "Discard"
msgstr "Descartar"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__mode
msgid "Display"
msgstr "Mostrar"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove_report__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_location__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_order__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_product__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__display_name
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__display_reorder_button
msgid "Display Reorder Button"
msgstr "Mostrar botón de reordenar"

#. module: lunch
#: model:lunch.product.category,name:lunch.categ_drinks
msgid "Drinks"
msgstr "Bebidas"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__email
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__send_by__mail
msgid "Email"
msgstr "Correo electrónico"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_alert__recipients__last_month
msgid "Employee who ordered last month"
msgstr "Empleado que ordenó el mes pasado"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_alert__recipients__last_week
msgid "Employee who ordered last week"
msgstr "Empleado que ordenó la semana pasada"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_alert__recipients__last_year
msgid "Employee who ordered last year"
msgstr "Empleado que ordenó el año pasado"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_alert__recipients__everyone
msgid "Everyone"
msgstr "Todos"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_label_1
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_label_1
msgid "Extra 1 Label"
msgstr "Etiqueta del extra 1"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_quantity_1
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_quantity_1
msgid "Extra 1 Quantity"
msgstr "Cantidad del extra 1"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_label_2
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_label_2
msgid "Extra 2 Label"
msgstr "Etiqueta del extra 2"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_quantity_2
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_quantity_2
msgid "Extra 2 Quantity"
msgstr "Cantidad del extra 2"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_label_3
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_label_3
msgid "Extra 3 Label"
msgstr "Etiqueta del extra 3"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_quantity_3
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_quantity_3
msgid "Extra 3 Quantity"
msgstr "Cantidad del extra 3"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__display_toppings
msgid "Extras"
msgstr "Extras"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_ids_1
msgid "Extras 1"
msgstr "Extras 1"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_ids_2
msgid "Extras 2"
msgstr "Extras 2"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__topping_ids_3
msgid "Extras 3"
msgstr "Extras 3"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_res_users__favorite_lunch_product_ids
msgid "Favorite Lunch Product"
msgstr "Producto favorito para el almuerzo"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__favorite_user_ids
msgid "Favorite User"
msgstr "Usuario favorito"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (partners)"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icono de Font Awesome ej. fa-tasks"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__email_formatted
msgid "Format email address \"Name <email@domain>\""
msgstr "Dirección de correo electrónico con formato \"Nombre <email@domain>\""

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__email_formatted
msgid "Formatted Email"
msgstr "Correo electrónico con formato"

#. module: lunch
#: model_terms:lunch.order,product_description:lunch.order_line_2
#: model_terms:lunch.product,description:lunch.product_italiana
msgid "Fresh Tomatoes, Basil, Mozzarella"
msgstr "Tomates frescos, albahaca, queso mozzarella"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__fri
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__fri
msgid "Fri"
msgstr "Vie"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Friday"
msgstr "Viernes"

#. module: lunch
#: model:lunch.order,name:lunch.order_line_3
#: model:lunch.product,name:lunch.product_gouda
msgid "Gouda Cheese"
msgstr "Queso gouda"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_search_2
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Group By"
msgstr "Agrupar por"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_club
#: model_terms:lunch.product,description:lunch.product_club_0
msgid "Ham, Cheese, Vegetables"
msgstr "Jamón, queso, vegetales"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__has_message
msgid "Has Message"
msgstr "Tiene un mensaje"

#. module: lunch
#: model:ir.module.category,description:lunch.module_lunch_category
msgid ""
"Helps you handle your lunch needs, if you are a manager you will be able to "
"create new products, cashmoves and to confirm or cancel orders."
msgstr ""
"Le ayuda a manejar sus necesidades de almuerzo. Si es un gerente, podrá "
"crear nuevos productos, movimientos de efectivo, y confirmar o cancelar "
"órdenes."

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_category_action
msgid "Here you can access all categories for the lunch products."
msgstr "Aquí puede acceder a las categorías de alimentos."

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_action_by_supplier
msgid "Here you can see today's orders grouped by vendors."
msgstr "Aquí puede ver las órdenes de hoy agrupadas por proveedor."

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_cashmove_report_action_account
msgid ""
"Here you can see your cash moves.<br>A cash move can either be an expense or a payment.\n"
"            An expense is automatically created when an order is received while a payment is a reimbursement to the company encoded by the manager."
msgstr ""
"Aquí puede ver sus movimientos de efectivo.<br>Un movimiento de efectivo puede ser un gasto o un pago.\n"
"            Un gasto se crea de forma automática cuando se recibe una orden, mientras que un pago es un reembolso a la empresa codificado por el gerente."

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__id
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__id
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove_report__id
#: model:ir.model.fields,field_description:lunch.field_lunch_location__id
#: model:ir.model.fields,field_description:lunch.field_lunch_order__id
#: model:ir.model.fields,field_description:lunch.field_lunch_product__id
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__id
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__id
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__id
msgid "ID"
msgstr "ID"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_exception_icon
msgid "Icon"
msgstr "Icono"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icono para indicar una actividad de excepción."

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__message_needaction
#: model:ir.model.fields,help:lunch.field_lunch_supplier__message_unread
msgid "If checked, new messages require your attention."
msgstr ""
"Si se encuentra seleccionado, hay nuevos mensajes que requieren su atención."

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Si se encuentra seleccionado, algunos mensajes tienen error de envío."

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__image_1920
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__image_1920
msgid "Image"
msgstr "Imagen"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__image_1024
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__image_1024
msgid "Image 1024"
msgstr "Imagen 1024"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__image_128
#: model:ir.model.fields,field_description:lunch.field_lunch_product__image_128
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__image_128
msgid "Image 128"
msgstr "Imagen 128"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__image_1920
msgid "Image 1920"
msgstr "Imagen 1920"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__image_256
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__image_256
msgid "Image 256"
msgstr "Imagen 256"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__image_512
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__image_512
msgid "Image 512"
msgstr "Imagen 512"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_form
msgid "Information, allergens, ..."
msgstr "Información, alérgenos, ...."

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__available_today
msgid "Is Displayed Today"
msgstr "Se visualiza hoy"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__is_favorite
msgid "Is Favorite"
msgstr "Es favorito"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__is_new
msgid "Is New"
msgstr "Es nuevo"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_res_users__last_lunch_location_id
msgid "Last Lunch Location"
msgstr "Ubicación del último almuerzo"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert____last_update
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove____last_update
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove_report____last_update
#: model:ir.model.fields,field_description:lunch.field_lunch_location____last_update
#: model:ir.model.fields,field_description:lunch.field_lunch_order____last_update
#: model:ir.model.fields,field_description:lunch.field_lunch_product____last_update
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category____last_update
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier____last_update
#: model:ir.model.fields,field_description:lunch.field_lunch_topping____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__last_order_date
msgid "Last Order Date"
msgstr "Fecha de la última orden"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__write_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__write_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_location__write_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_order__write_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_product__write_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__write_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__write_uid
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__write_date
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__write_date
#: model:ir.model.fields,field_description:lunch.field_lunch_location__write_date
#: model:ir.model.fields,field_description:lunch.field_lunch_order__write_date
#: model:ir.model.fields,field_description:lunch.field_lunch_product__write_date
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__write_date
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__write_date
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__location_ids
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__available_location_ids
msgid "Location"
msgstr "Ubicación"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_location__name
msgid "Location Name"
msgstr "Nombre de ubicación"

#. module: lunch
#: model:ir.ui.menu,name:lunch.lunch_location_menu
msgid "Locations"
msgstr "Ubicaciones"

#. module: lunch
#: model:ir.module.category,name:lunch.module_lunch_category
#: model:ir.ui.menu,name:lunch.menu_lunch
#: model_terms:ir.ui.view,arch_db:lunch.res_config_settings_view_form
msgid "Lunch"
msgstr "Almuerzos"

#. module: lunch
#: model:ir.model,name:lunch.model_lunch_alert
msgid "Lunch Alert"
msgstr "Alerta de almuerzo"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_alert_action
msgid "Lunch Alerts"
msgstr "Alertas de almuerzo"

#. module: lunch
#: code:addons/lunch/models/lunch_cashmove.py:0
#: code:addons/lunch/report/lunch_cashmove_report.py:0
#: model:ir.model,name:lunch.model_lunch_cashmove
#, python-format
msgid "Lunch Cashmove"
msgstr "Movimiento de efectivo de almuerzo"

#. module: lunch
#: model:ir.model,name:lunch.model_lunch_topping
msgid "Lunch Extras"
msgstr "Extras del almuerzo"

#. module: lunch
#. openerp-web
#: code:addons/lunch/static/src/js/lunch_kanban_view.js:0
#, python-format
msgid "Lunch Kanban"
msgstr "Kanban de almuerzo"

#. module: lunch
#. openerp-web
#: code:addons/lunch/static/src/js/lunch_list_view.js:0
#, python-format
msgid "Lunch List"
msgstr "Lista del almuerzo"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__lunch_location_id
msgid "Lunch Location"
msgstr "Ubicación de almuerzo"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_location_action
#: model:ir.model,name:lunch.model_lunch_location
msgid "Lunch Locations"
msgstr "Ubicaciones de almuerzo"

#. module: lunch
#: model:lunch.product,name:lunch.product_maki
msgid "Lunch Maki 18pc"
msgstr "Almuerzo maki 18 pzas."

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_res_company__lunch_minimum_threshold
msgid "Lunch Minimum Threshold"
msgstr "Umbral mínimo de almuerzo"

#. module: lunch
#: model:ir.model,name:lunch.model_lunch_order
msgid "Lunch Order"
msgstr "Orden de almuerzo"

#. module: lunch
#: model:ir.model,name:lunch.model_lunch_product
msgid "Lunch Product"
msgstr "Producto de almuerzo"

#. module: lunch
#: model:ir.model,name:lunch.model_lunch_product_category
msgid "Lunch Product Category"
msgstr "Categoría de producto de almuerzo"

#. module: lunch
#: model:lunch.product,name:lunch.product_salmon
msgid "Lunch Salmon 20pc"
msgstr "Almuerzo de salmón 20 pzas."

#. module: lunch
#: model:ir.model,name:lunch.model_lunch_supplier
msgid "Lunch Supplier"
msgstr "Proveedor de almuerzo"

#. module: lunch
#: model:lunch.product,name:lunch.product_temaki
msgid "Lunch Temaki mix 3pc"
msgstr "Almuerzo mezcla temaki 3 pzas."

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_order_action_cancel
msgid "Lunch: Cancel meals"
msgstr "Almuerzo: Cancelar las comidas"

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_order_action_confirm
msgid "Lunch: Receive meals"
msgstr "Almuerzo: Recibir las comidas"

#. module: lunch
#: model:mail.template,name:lunch.lunch_order_mail_supplier
msgid "Lunch: Send by email"
msgstr "Almuerzo: Enviar por correo electrónico"

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_alert_cron_sa_224
msgid "Lunch: alert chat notification (Alert for Office 3)"
msgstr "Almuerzo: alerta de notificación de chat (alerta para la oficina 3)"

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_supplier_cron_sa_225
msgid "Lunch: send automatic email to Coin gourmand"
msgstr "Almuerzo: enviar correo electrónico automático a Coin Gourmand"

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_supplier_cron_sa_221
msgid "Lunch: send automatic email to Lunch Supplier"
msgstr ""
"Almuerzo: enviar correo electrónico automático al proveedor de almuerzos"

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_supplier_cron_sa_226
msgid "Lunch: send automatic email to Pizza Inn"
msgstr "Almuerzo: enviar correo electrónico automático a Pizza Inn"

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_supplier_cron_sa_228
msgid "Lunch: send automatic email to Sushi Shop"
msgstr "Almuerzo: enviar correo electrónico automático a Sushi Shop"

#. module: lunch
#: model:ir.actions.server,name:lunch.lunch_supplier_cron_sa_227
msgid "Lunch: send automatic email to The Corner"
msgstr "Almuerzo: enviar correo electrónico automático a The Corner"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_main_attachment_id
msgid "Main Attachment"
msgstr "Archivo adjunto principal"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_res_config_settings__currency_id
msgid "Main currency of the company."
msgstr "Divisa principal de la empresa."

#. module: lunch
#: model:ir.ui.menu,name:lunch.menu_lunch_admin
msgid "Manager"
msgstr "Gerente"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_res_config_settings__company_lunch_minimum_threshold
msgid "Maximum Allowed Overdraft"
msgstr "Sobregiro máximo permitido"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.res_config_settings_view_form
msgid "Maximum overdraft that your employees can reach"
msgstr "Sobregiro máximo que sus empleados pueden alcanzar"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__message
msgid "Message"
msgstr "Mensaje"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_has_error
msgid "Message Delivery error"
msgstr "Error de envío de mensaje"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__moment
msgid "Moment"
msgstr "Momento"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__mon
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__mon
msgid "Mon"
msgstr "Lun"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Monday"
msgstr "Lunes"

#. module: lunch
#: model:lunch.product,name:lunch.product_mozzarella
msgid "Mozzarella"
msgstr "Queso mozzarella"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_mozzarella
msgid "Mozzarella, Pesto, Tomatoes"
msgstr "Queso mozzarella, pesto, tomates"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_cashmove_report_action_account
msgid "My Account"
msgstr "Mi cuenta"

#. module: lunch
#: model:ir.ui.menu,name:lunch.lunch_cashmove_report_menu_form
msgid "My Account History"
msgstr "Historial de mi cuenta"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_search
msgid "My Account grouped"
msgstr "Mi cuenta agrupada"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Fecha límite de mi actividad"

#. module: lunch
#: model:ir.ui.menu,name:lunch.menu_lunch_title
msgid "My Lunch"
msgstr "Mi almuerzo"

#. module: lunch
#: model:ir.ui.menu,name:lunch.lunch_order_menu_tree
msgid "My Order History"
msgstr "Mi historial de órdenes"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_order_action
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "My Orders"
msgstr "Mis órdenes"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__name
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__name
msgid "Name"
msgstr "Nombre"

#. module: lunch
#: model:lunch.product,name:lunch.product_Napoli
msgid "Napoli Pasta"
msgstr "Pasta napolitana"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.view_lunch_product_kanban_order
msgid "New"
msgstr "Nuevo"

#. module: lunch
#: model:ir.ui.menu,name:lunch.lunch_order_menu_form
msgid "New Order"
msgstr "Nueva orden"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__new_until
msgid "New Until"
msgstr "Nuevo hasta"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Siguiente evento en el calendario de actividades."

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Fecha límite de la siguiente actividad"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_summary
msgid "Next Activity Summary"
msgstr "Resumen de la siguiente actividad"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_type_id
msgid "Next Activity Type"
msgstr "Tipo de la siguiente actividad"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__delivery__no_delivery
msgid "No Delivery"
msgstr "No hay entrega"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_cashmove_report_action_account
msgid "No cash move yet"
msgstr "No hay movimiento de efectivo aún"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_action_control_suppliers
msgid "No lunch order yet"
msgstr "No hay orden de almuerzo aún"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_action
msgid "No previous order found"
msgstr "No se encontró ninguna orden anterior."

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.res_config_settings_view_form
msgid "None"
msgstr "Ninguno"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_1__0_more
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_2__0_more
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_3__0_more
msgid "None or More"
msgstr "Ninguno o más"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "Not Received"
msgstr "No recibido"

#. module: lunch
#. openerp-web
#: code:addons/lunch/static/src/js/lunch_controller_common.js:0
#, python-format
msgid "Not enough money in your wallet"
msgstr "No hay suficiente dinero en su cartera"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__note
msgid "Notes"
msgstr "Notas"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_action_by_supplier
msgid "Nothing to order today"
msgstr "No hay nada que ordenar hoy"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__notification_moment
msgid "Notification Moment"
msgstr "Momento de notificación"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__notification_time
msgid "Notification Time"
msgstr "Hora de notificación"

#. module: lunch
#: model:ir.model.constraint,message:lunch.constraint_lunch_alert_notification_time_range
msgid "Notification time must be between 0 and 12"
msgstr "La hora de notificación debe estar entre 0 y 12"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_has_error_counter
msgid "Number of errors"
msgstr "Número de errores"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Número de mensajes que requieren una acción"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__message_unread_counter
msgid "Number of unread messages"
msgstr "Número de mensajes sin leer"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_1__1_more
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_2__1_more
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_3__1_more
msgid "One or More"
msgstr "Uno o más"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_1__1
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_2__1
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__topping_quantity_3__1
msgid "Only One"
msgstr "Solo uno"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
msgid "Order"
msgstr "Orden"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__date
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "Order Date"
msgstr "Fecha de orden"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__automatic_email_time
msgid "Order Time"
msgstr "Hora de orden"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_product_action_order
msgid "Order Your Lunch"
msgstr "Ordene su almuerzo"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Order lines Tree"
msgstr "Árbol de líneas de órdenes"

#. module: lunch
#. openerp-web
#: code:addons/lunch/static/src/xml/lunch_templates.xml:0
#, python-format
msgid "Order now"
msgstr "Ordene ahora"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_order__state__ordered
msgid "Ordered"
msgstr "Ordenado"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "Orders"
msgstr "Órdenes"

#. module: lunch
#: model:mail.template,subject:lunch.lunch_order_mail_supplier
msgid "Orders for {{ ctx['order']['company_name'] }}"
msgstr "Ordenes para {{ ctx['order']['company_name'] }}"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.res_config_settings_view_form
msgid "Overdraft"
msgstr "Sobregiro"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_alert__notification_moment__pm
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__moment__pm
msgid "PM"
msgstr "PM"

#. module: lunch
#: model:lunch.product.category,name:lunch.categ_pasta
msgid "Pasta"
msgstr "Pasta"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_search
msgid "Payment"
msgstr "Pago"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_cashmove_action_payment
msgid ""
"Payments are used to register liquidity movements. You can process those "
"payments by your own means or by using installed facilities."
msgstr ""
"Los pagos se utilizan para registrar movimientos de liquidez. Puede procesar"
" esos pagos por tus propios medios o utilizando las instalaciones "
"instaladas."

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__phone
#: model:ir.model.fields.selection,name:lunch.selection__lunch_supplier__send_by__phone
msgid "Phone"
msgstr "Teléfono"

#. module: lunch
#: model:lunch.product.category,name:lunch.categ_pizza
msgid "Pizza"
msgstr "Pizza"

#. module: lunch
#: model:lunch.product,name:lunch.product_funghi
msgid "Pizza Funghi"
msgstr "Pizza Funghi"

#. module: lunch
#: model:lunch.order,name:lunch.order_line_2
#: model:lunch.product,name:lunch.product_italiana
msgid "Pizza Italiana"
msgstr "Pizza italiana"

#. module: lunch
#: model:lunch.product,name:lunch.product_margherita
#: model:lunch.product,name:lunch.product_pizza_0
msgid "Pizza Margherita"
msgstr "Pizza Margherita"

#. module: lunch
#: model:lunch.product,name:lunch.product_vege
msgid "Pizza Vegetarian"
msgstr "Pizza vegetariana"

#. module: lunch
#: model_terms:lunch.alert,message:lunch.alert_office_3
msgid "Please order"
msgstr "Por favor, ordene"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__price
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__price
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Price"
msgstr "Precio"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__product_id
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Product"
msgstr "Producto"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__is_available_at
msgid "Product Availability"
msgstr "Disponibilidad del producto"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_product_category_action
#: model:ir.ui.menu,name:lunch.lunch_product_category_menu
msgid "Product Categories"
msgstr "Categorías de productos"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_category_view_form
msgid "Product Categories Form"
msgstr "Formulario de categorías de productos"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__category_id
#: model:ir.model.fields,field_description:lunch.field_lunch_product__category_id
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__name
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_category_view_tree
msgid "Product Category"
msgstr "Categoría de producto"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product_category__product_count
msgid "Product Count"
msgstr "Número de productos"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_product__product_image
msgid "Product Image"
msgstr "Imagen del producto"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__name
#: model:ir.model.fields,field_description:lunch.field_lunch_product__name
msgid "Product Name"
msgstr "Nombre del producto"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Product Search"
msgstr "Búsqueda de producto"

#. module: lunch
#: code:addons/lunch/models/lunch_order.py:0
#, python-format
msgid "Product is no longer available."
msgstr "El producto ya no está disponible."

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_product_action
#: model:ir.actions.act_window,name:lunch.lunch_product_action_statbutton
#: model:ir.ui.menu,name:lunch.lunch_product_menu
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_category_view_form
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_category_view_kanban
msgid "Products"
msgstr "Productos"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_form
msgid "Products Form"
msgstr "Formulario de productos"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_category_view_tree
msgid "Products List"
msgstr "Lista de productos"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_tree
msgid "Products Tree"
msgstr "Árbol de productos"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__quantity
msgid "Quantity"
msgstr "Cantidad"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Re-order"
msgstr "Reordenar"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_kanban
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Receive"
msgstr "Recibir"

#. module: lunch
#: model:ir.model.fields.selection,name:lunch.selection__lunch_order__state__confirmed
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "Received"
msgstr "Recibido"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__recipients
msgid "Recipients"
msgstr "Destinatarios"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_cashmove_action_payment
msgid "Register a payment"
msgstr "Registrar un pago"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
msgid "Reset"
msgstr "Restablecer"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__responsible_id
msgid "Responsible"
msgstr "Responsable"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__activity_user_id
msgid "Responsible User"
msgstr "Usuario responsable"

#. module: lunch
#: model:lunch.product,name:lunch.product_chirashi
msgid "Salmon and Avocado"
msgstr "Salmón y aguacate"

#. module: lunch
#: model:lunch.product.category,name:lunch.categ_sandwich
msgid "Sandwich"
msgstr "Sándwich"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__sat
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__sat
msgid "Sat"
msgstr "Sáb"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Saturday"
msgstr "Sábado"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_form
msgid "Save"
msgstr "Guardar"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "Search"
msgstr "Búsqueda"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__send_by
msgid "Send Order By"
msgstr "Enviar orden por"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_config_settings_action
#: model:ir.ui.menu,name:lunch.lunch_settings_menu
msgid "Settings"
msgstr "Ajustes"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__until
msgid "Show Until"
msgstr "Mostrar hasta"

#. module: lunch
#: model:lunch.product,name:lunch.product_spicy_tuna
msgid "Spicy Tuna"
msgstr "Atún picante"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__state_id
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "State"
msgstr "Estado"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__state
msgid "Status"
msgstr "Estado"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estado basado en actividades\n"
"Vencida: la fecha límite ya pasó\n"
"Hoy: la fecha límite es hoy\n"
"Planeada: futuras actividades."

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__street
msgid "Street"
msgstr "Calle"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "Street 2..."
msgstr "Calle 2..."

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "Street..."
msgstr "Calle..."

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__street2
msgid "Street2"
msgstr "Calle2"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_action_control_suppliers
msgid "Summary of all lunch orders, grouped by vendor and by date."
msgstr ""
"Resumen de todas las órdenes de almuerzo, agrupadas por vendedor y por "
"fecha."

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__sun
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__sun
msgid "Sun"
msgstr "Dom"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Sunday"
msgstr "Domingo"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__supplier_id
msgid "Supplier"
msgstr "Proveedor"

#. module: lunch
#: model:lunch.product.category,name:lunch.categ_sushi
msgid "Sushi"
msgstr "Sushi"

#. module: lunch
#: model:lunch.product.category,name:lunch.categ_temaki
msgid "Temaki"
msgstr "Temaki"

#. module: lunch
#: model:lunch.product,name:lunch.product_country
msgid "The Country"
msgstr "El país"

#. module: lunch
#: code:addons/lunch/models/lunch_product.py:0
#, python-format
msgid ""
"The following product categories are archived. You should either unarchive the categories or change the category of the product.\n"
"%s"
msgstr ""
"Las siguientes categorías de productos están archivadas. Debe desarchivar las categorías o cambiar la categoría del producto.\n"
"%s"

#. module: lunch
#: code:addons/lunch/models/lunch_product.py:0
#, python-format
msgid ""
"The following suppliers are archived. You should either unarchive the suppliers or change the supplier of the product.\n"
"%s"
msgstr ""
"Los siguientes proveedores están archivados. Debe desarchivar los proveedores o cambiar el proveedor del producto.\n"
"%s"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_product_category__product_count
msgid "The number of products related to this category"
msgstr "El número de productos relacionados con esta categoría"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__responsible_id
msgid ""
"The responsible is the person that will order lunch for everyone. It will be"
" used as the 'from' when sending the automatic email."
msgstr ""
"El responsable es la persona que ordenará el almuerzo para todos. Se "
"utilizará en el campo 'de' cuando se envíe el correo electrónico automático."

#. module: lunch
#: code:addons/lunch/models/lunch_order.py:0
#: code:addons/lunch/models/lunch_order.py:0
#, python-format
msgid "The vendor related to this order is not available today."
msgstr "El vendedor relacionado a esta orden no está disponible hoy."

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_order_action
msgid ""
"There is no previous order recorded. Click on \"My Lunch\" and then create a"
" new lunch order."
msgstr ""
"No hay ninguna orden anterior registrada. Haga clic en \"Mi almuerzo\" y "
"luego cree una nueva orden"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_action_order
msgid "There is no product available today"
msgstr "No hay productos disponibles hoy"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_topping__topping_category
msgid "This field is a technical field"
msgstr "Este campo es un campo técnico."

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__recurrency_end_date
msgid "This field is used in order to "
msgstr "Este campo se utiliza para"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__available_today
msgid "This is True when if the supplier is available today"
msgstr "Es True si el proveedor está disponible hoy"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__thu
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__thu
msgid "Thu"
msgstr "Jue"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Thursday"
msgstr "Jueves"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__tz
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__tz
msgid "Timezone"
msgstr "Zona horaria"

#. module: lunch
#. openerp-web
#: code:addons/lunch/static/src/js/lunch_widget.js:0
#: model:ir.model.fields.selection,name:lunch.selection__lunch_order__state__new
#, python-format
msgid "To Order"
msgstr "Por ordenar"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_payment_dialog
msgid "To add some money to your wallet, please contact your lunch manager."
msgstr "Contacte a su gerente de almuerzo para agregar dinero a su cartera."

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_location_action
msgid "To see some locations, create one using the create button"
msgstr "Para ver algunas ubicaciones, cree una con el botón de crear"

#. module: lunch
#: model_terms:ir.actions.act_window,help:lunch.lunch_product_action_order
msgid ""
"To see some products, check if your vendors are available today and that you"
" have configured some products"
msgstr ""
"Para ver algunos productos, verifique si sus proveedores están disponibles "
"hoy y si ha configurado algunos productos"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "Today"
msgstr "Hoy"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_order_action_by_supplier
#: model:ir.ui.menu,name:lunch.lunch_order_menu_by_supplier
msgid "Today's Orders"
msgstr "Órdenes de hoy"

#. module: lunch
#: model_terms:lunch.order,product_description:lunch.order_line_5
#: model_terms:lunch.product,description:lunch.product_4formaggi
msgid "Tomato sauce, Olive oil, Fresh Tomatoes, Onions, Vegetables, Parmesan"
msgstr ""
"Salsa de tomate, aceite de oliva, tomates frescos, cebollas, vegetales, "
"queso parmesano"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_Napoli
msgid "Tomatoes, Basil"
msgstr "Tomates, albahaca"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_margherita
#: model_terms:lunch.product,description:lunch.product_pizza_0
msgid "Tomatoes, Mozzarella"
msgstr "Tomates, queso mozzarella"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_vege
msgid "Tomatoes, Mozzarella, Mushrooms, Peppers, Olives"
msgstr "Tomates, queso mozzarella, champiñones, pimientos, aceitunas"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_funghi
msgid "Tomatoes, Mushrooms, Mozzarella"
msgstr "Tomates, champiñones, queso mozzarella"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_topping__topping_category
msgid "Topping Category"
msgstr "Categoría de toppings"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_ids_1
msgid "Topping Ids 1"
msgstr "Ids toppings 1"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_ids_2
msgid "Topping Ids 2"
msgstr "Ids toppings 2"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__topping_ids_3
msgid "Topping Ids 3"
msgstr "Ids toppings 3"

#. module: lunch
#. openerp-web
#: code:addons/lunch/static/src/xml/lunch_templates.xml:0
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_tree
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_tree_2
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_tree
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_tree
#, python-format
msgid "Total"
msgstr "Total"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__price
msgid "Total Price"
msgstr "Precio total"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__tue
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__tue
msgid "Tue"
msgstr "Mar"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Tuesday"
msgstr "Martes"

#. module: lunch
#: model:lunch.product,name:lunch.product_tuna
msgid "Tuna"
msgstr "Atún"

#. module: lunch
#: model_terms:lunch.product,description:lunch.product_tuna
msgid "Tuna, Mayonnaise"
msgstr "Atún, mayonesa"

#. module: lunch
#: model:ir.model.fields,help:lunch.field_lunch_supplier__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de actividad de excepción registrada."

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_unread
msgid "Unread Messages"
msgstr "Mensajes sin leer"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Número de mensajes sin leer"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__recurrency_end_date
msgid "Until"
msgstr "Hasta"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove__user_id
#: model:ir.model.fields,field_description:lunch.field_lunch_cashmove_report__user_id
#: model:ir.model.fields,field_description:lunch.field_lunch_order__user_id
#: model:res.groups,name:lunch.group_lunch_user
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "User"
msgstr "Usuario"

#. module: lunch
#: model:ir.model,name:lunch.model_res_users
msgid "Users"
msgstr "Usuarios"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_order__supplier_id
#: model:ir.model.fields,field_description:lunch.field_lunch_product__supplier_id
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__partner_id
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "Vendor"
msgstr "Proveedor"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_order_view_search
msgid "Vendor Orders by Date"
msgstr "Órdenes de proveedor por fecha"

#. module: lunch
#: model:ir.actions.act_window,name:lunch.lunch_vendors_action
#: model:ir.ui.menu,name:lunch.lunch_vendors_menu
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Vendors"
msgstr "Proveedores"

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_alert__wed
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__wed
msgid "Wed"
msgstr "Mié"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_product_view_search
msgid "Wednesday"
msgstr "Miércoles"

#. module: lunch
#: code:addons/lunch/controllers/main.py:0
#, python-format
msgid ""
"You are trying to impersonate another user, but this can only be done by a "
"lunch manager"
msgstr ""
"Está tratando de hacerse pasar por otro usuario, pero esto solo lo puede "
"hacer un gerente de almuerzo."

#. module: lunch
#: code:addons/lunch/models/lunch_order.py:0
#, python-format
msgid "You have to order one and only one %s"
msgstr "Tiene que ordenar uno y solo uno %s"

#. module: lunch
#: code:addons/lunch/models/lunch_order.py:0
#, python-format
msgid "You should order at least one %s"
msgstr "Debe ordenar al menos uno %s"

#. module: lunch
#. openerp-web
#: code:addons/lunch/static/src/xml/lunch_templates.xml:0
#, python-format
msgid "Your Account"
msgstr "Su cuenta"

#. module: lunch
#. openerp-web
#: code:addons/lunch/static/src/xml/lunch_templates.xml:0
#, python-format
msgid ""
"Your cart\n"
"                ("
msgstr ""
"Su carrito\n"
"                ("

#. module: lunch
#. openerp-web
#: code:addons/lunch/static/src/xml/lunch_templates.xml:0
#, python-format
msgid "Your order"
msgstr "Su orden"

#. module: lunch
#: code:addons/lunch/models/lunch_order.py:0
#, python-format
msgid ""
"Your wallet does not contain enough money to order that. To add some money "
"to your wallet, please contact your lunch manager."
msgstr ""
"No cuenta con suficiente dinero en su cartera para ordenar eso. Contacte con"
" el gerente de almuerzo para agregar más dinero a su cartera."

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "ZIP"
msgstr "C.P."

#. module: lunch
#: model:ir.model.fields,field_description:lunch.field_lunch_supplier__zip_code
msgid "Zip"
msgstr "C.P."

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_form
msgid "alert form"
msgstr "formulario de alerta"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_form
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_form
msgid "cashmove form"
msgstr "formulario de movimientos de efectivo"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_tree
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_tree_2
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_tree
msgid "cashmove tree"
msgstr "árbol de movimientos de efectivo"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_form
msgid "e.g. Order before 11am"
msgstr "Por ejemplo, ordenar antes de las 11 AM"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_supplier_view_form
msgid "e.g. The Pizzeria Inn"
msgstr "Por ejemplo, The Pizzeria Inn"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_search_2
msgid "lunch cashmove"
msgstr "movimiento de efectivo del almuerzo"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_report_view_search
#: model_terms:ir.ui.view,arch_db:lunch.lunch_cashmove_view_search
msgid "lunch employee payment"
msgstr "pago de almuerzo de empleado"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_kanban
msgid "on"
msgstr "en"

#. module: lunch
#: model_terms:ir.ui.view,arch_db:lunch.lunch_alert_view_kanban
msgid "to"
msgstr "a"
