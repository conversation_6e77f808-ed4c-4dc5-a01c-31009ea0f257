<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1400 1400">
	<style>
		@media only screen and (max-width: 300px) {
			svg {transform: scaleY(5); transform-origin: center bottom;}
		}
    </style>
	<linearGradient id="gradient" gradientUnits="userSpaceOnUse" x1="0" y1="1400" x2="1400" y2="1400">
		<stop offset="0" stop-color="#FFFFFF" stop-opacity=".25"/>
		<stop offset="1" stop-color="#FFFFFF" stop-opacity="0"/>
	</linearGradient>
	<rect fill="url(#gradient)" width="1400" height="1400"/>
	<path fill="#F6F6F6" d="M0,1354.3v45.7h1400v-45.7C915.7,1395.1,448,1397.5,0,1354.3z"/>
</svg>
