<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="action_report_repair_order" model="ir.actions.report">
            <field name="name">Quotation / Order</field>
            <field name="model">repair.order</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">repair.report_repairorder2</field>
            <field name="report_file">repair.report_repairorder</field>
            <field name="print_report_name">(
                object.state == 'draft' and 'Repair Quotation - %s' % (object.name) or
                'Repair Order - %s' % (object.name))</field>
            <field name="binding_model_id" ref="model_repair_order"/>
            <field name="binding_type">report</field>
        </record>
    </data>
</odoo>
