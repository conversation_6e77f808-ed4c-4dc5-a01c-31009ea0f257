# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* im_livechat_mail_bot
# 
# Translators:
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: im_livechat_mail_bot
#: code:addons/im_livechat_mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Good, you can customize canned responses in the live chat "
"application.<br/><br/><b>It's the end of this overview</b>, enjoy "
"discovering Odoo!"
msgstr ""

#. module: im_livechat_mail_bot
#: model:ir.model,name:im_livechat_mail_bot.model_mail_bot
msgid "Mail Bot"
msgstr ""

#. module: im_livechat_mail_bot
#: code:addons/im_livechat_mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Not sure what you are doing. Please, type <span "
"class=\"o_odoobot_command\">:</span> and wait for the propositions. Select "
"one of them and press enter."
msgstr ""

#. module: im_livechat_mail_bot
#: model:ir.model.fields,field_description:im_livechat_mail_bot.field_res_users__odoobot_state
msgid "OdooBot Status"
msgstr "وضعیت ربات اودوو"

#. module: im_livechat_mail_bot
#: model:ir.model.fields.selection,name:im_livechat_mail_bot.selection__res_users__odoobot_state__onboarding_canned
msgid "Onboarding canned"
msgstr ""

#. module: im_livechat_mail_bot
#: code:addons/im_livechat_mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"That's me! 🎉<br/>Try typing <span class=\"o_odoobot_command\">:</span> to "
"use canned responses."
msgstr ""
"ایول! 🎉حالا با زدن <span class=\"o_odoobot_command\">:</span> سعی کن از "
"پیغام‌های ذخیره شده ارسال کنی."

#. module: im_livechat_mail_bot
#: model:ir.model,name:im_livechat_mail_bot.model_res_users
msgid "Users"
msgstr "کاربران"
