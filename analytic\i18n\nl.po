# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* analytic
# 
# Translators:
# <PERSON>, 2021
# 04a2cd0fd6ee22172c36ea91f27a38c5_60041bf, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
msgid "<span class=\"o_stat_text\">Gross Margin</span>"
msgstr "<span class=\"o_stat_text\">Bruto marge</span>"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_needaction
msgid "Action Needed"
msgstr "Actie gevraagd"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__active
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__active
msgid "Active"
msgstr "Actief"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.action_account_analytic_account_form
#: model_terms:ir.actions.act_window,help:analytic.action_analytic_account_form
msgid "Add a new analytic account"
msgstr "Voeg een nieuwe kostenplaats toe"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_tag_action
msgid "Add a new tag"
msgstr "Voeg een nieuw label toe"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__amount
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_form
msgid "Amount"
msgstr "Bedrag"

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_account
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__account_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__account_id
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Analytic Account"
msgstr "Kostenplaats"

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_distribution
msgid "Analytic Account Distribution"
msgstr "Kostenverdeling"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_group_action
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_group_form_view
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_group_tree_view
msgid "Analytic Account Groups"
msgstr "Kostenplaatsgroepen"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_res_config_settings__group_analytic_accounting
#: model:res.groups,name:analytic.group_analytic_accounting
msgid "Analytic Accounting"
msgstr "Kostenplaatsen"

#. module: analytic
#: model:res.groups,name:analytic.group_analytic_tags
msgid "Analytic Accounting Tags"
msgstr "Kostenplaatslabels"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.action_account_analytic_account_form
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__analytic_distribution_ids
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Analytic Accounts"
msgstr "Kostenplaatsen"

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_group
msgid "Analytic Categories"
msgstr "Kostenplaatscategorieën"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__active_analytic_distribution
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_tag_form_view
msgid "Analytic Distribution"
msgstr "Kostenverdeling"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_graph
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_pivot
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_tree
msgid "Analytic Entries"
msgstr "Kostenplaatsboekingen"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_form
msgid "Analytic Entry"
msgstr "Kostenplaatsboeking"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_line_action_entries
msgid "Analytic Items"
msgstr "Kostenplaatsenboekingen"

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_line
msgid "Analytic Line"
msgstr "Kostenplaatsregel"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__line_ids
msgid "Analytic Lines"
msgstr "Kostenplaatsregels"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__name
msgid "Analytic Tag"
msgstr "Kostenplaatslabel"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_tag_action
#: model:ir.model,name:analytic.model_account_analytic_tag
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_tag_form_view
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_tag_tree_view
msgid "Analytic Tags"
msgstr "Kostenplaatslabels"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_tag_form_view
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_tag_view_search
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
msgid "Archived"
msgstr "Gearchiveerd"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
msgid "Associated Partner"
msgstr "Gekoppelde relatie"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_attachment_count
msgid "Attachment Count"
msgstr "Aantal bijlagen"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__balance
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Balance"
msgstr "Saldo"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_kanban
msgid "Balance:"
msgstr "Saldo:"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__category
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Category"
msgstr "Categorie"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.action_analytic_account_form
msgid "Chart of Analytic Accounts"
msgstr "Kostenplaatsenschema"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__children_ids
msgid "Childrens"
msgstr "Onderliggende"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_group_action
msgid "Click to add a new analytic account group."
msgstr "Klik om een nieuwe kostenplaats groep aan te maken."

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__color
msgid "Color Index"
msgstr "Kleurindex"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__company_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__company_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__company_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__company_id
msgid "Company"
msgstr "Bedrijf"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__complete_name
msgid "Complete Name"
msgstr "Volledige naam"

#. module: analytic
#: model:ir.model,name:analytic.model_res_config_settings
msgid "Config Settings"
msgstr "Configuratie instellingen"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Conversie tussen maateenheden kan alleen plaatsvinden als deze behoren tot "
"dezelfde categorie. De conversie wordt gemaakt op basis van ratio's."

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid ""
"Costs will be created automatically when you register supplier\n"
"                invoices, expenses or timesheets."
msgstr ""
"Kosten worden automatisch aangemaakt wanneer je leveranciersfacturen,\n"
"declaraties of urenstaten registreert."

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__credit
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Credit"
msgstr "Credit"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__currency_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__partner_id
msgid "Customer"
msgstr "Klant"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__date
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Date"
msgstr "Datum"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__debit
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Debit"
msgstr "Debet"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__description
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__name
msgid "Description"
msgstr "Omschrijving"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_follower_ids
msgid "Followers"
msgstr "Volgers"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_partner_ids
msgid "Followers (Partners)"
msgstr "Volgers (Relaties)"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_line_action
msgid "Gross Margin"
msgstr "Bruto marge"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__group_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__group_id
msgid "Group"
msgstr "Groep"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Group By..."
msgstr "Groeperen op..."

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__has_message
msgid "Has Message"
msgstr "Heeft bericht"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__id
msgid "ID"
msgstr "ID"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_needaction
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_unread
msgid "If checked, new messages require your attention."
msgstr "Indien aangevinkt vragen nieuwe berichten je aandacht."

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Indien aangevinkt hebben sommige leveringen een fout."

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__active
msgid ""
"If the active field is set to False, it will allow you to hide the account "
"without removing it."
msgstr ""
"Als het veld \"actief\" uitstaat, kun je de grootboekrekening verbergen "
"zonder deze te verwijderen."

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid ""
"In Odoo, sales orders and projects are implemented using\n"
"                analytic accounts. You can track costs and revenues to analyse\n"
"                your margins easily."
msgstr ""
"In Odoo worden verkopen en projecten geïmplementeerd door gebruik\n"
"te maken van kostenplaatsen. Je kunt kosten en omzet eenvoudig\n"
"vergelijken om je marges te analyseren."

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_is_follower
msgid "Is Follower"
msgstr "Is een volger"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account____last_update
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution____last_update
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group____last_update
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line____last_update
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag____last_update
msgid "Last Modified on"
msgstr "Laatst gewijzigd op"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_tag__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_main_attachment_id
msgid "Main Attachment"
msgstr "Hoofdbijlage"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_has_error
msgid "Message Delivery error"
msgstr "Bericht afleverfout"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_ids
msgid "Messages"
msgstr "Berichten"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__name
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Name"
msgstr "Naam"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid "No activity yet"
msgstr "Nog geen activiteit"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
msgid "No activity yet on this account"
msgstr "Nog geen activiteit voor deze kostenplaats"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_needaction_counter
msgid "Number of Actions"
msgstr "Aantal acties"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_has_error_counter
msgid "Number of errors"
msgstr "Aantal fouten"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Aantal berichten die actie vereisen"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Aantal berichten met leveringsfout"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_unread_counter
msgid "Number of unread messages"
msgstr "Aantal ongelezen berichten"

#. module: analytic
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_line__category__other
msgid "Other"
msgstr "Overige"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__parent_id
msgid "Parent"
msgstr "Bovenliggend"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_group__parent_path
msgid "Parent Path"
msgstr "Bovenliggend pad"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__tag_id
msgid "Parent tag"
msgstr "Bovenliggend label"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__partner_id
msgid "Partner"
msgstr "Relatie"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution__percentage
msgid "Percentage"
msgstr "Percentage"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__unit_amount
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_tree
msgid "Quantity"
msgstr "Aantal"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__code
msgid "Reference"
msgstr "Referentie"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid ""
"Revenues will be created automatically when you create customer\n"
"                invoices. Customer invoices can be created based on sales orders\n"
"                (fixed price invoices), on timesheets (based on the work done) or\n"
"                on expenses (e.g. reinvoicing of travel costs)."
msgstr ""
"Omzetten worden automatisch aangemaakt wanneer je verkoopfacturen\n"
"aanmaakt. Verkoopfacturen kunnen gebaseerd zijn op verkopen (met vaste prijs),\n"
"op urenstaten (gebaseerd op de geleverde prestaties) of op declaraties\n"
"(bijv. herfactureren van reiskosten)."

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Search Analytic Lines"
msgstr "Zoek kostenplaats boekingen"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_tag_view_search
msgid "Search Analytic Tags"
msgstr "Zoek kostenplaatslabel"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_tag__active
msgid "Set active to false to hide the Analytic Tag without removing it."
msgstr ""
"Zet actief naar onwaar om het kostenplaatslabel te verbergen zonder het te "
"verwijderen."

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__tag_ids
msgid "Tags"
msgstr "Labels"

#. module: analytic
#: model:ir.model.constraint,message:analytic.constraint_account_analytic_distribution_check_percentage
msgid ""
"The percentage of an analytic distribution should be between 0 and 100."
msgstr ""
"Het percentage of een kostenplaatsverdeling moet liggen tussen 0 en 100."

#. module: analytic
#: code:addons/analytic/models/analytic_account.py:0
#, python-format
msgid ""
"The selected account belongs to another company than the one you're trying "
"to create an analytic item for"
msgstr ""
"De geselecteerde rekening behoort bij een ander bedrijf dan het bedrijf "
"waarvoor je een kostenplaats probeert aan te maken."

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_group_action
msgid "This allows you to classify your analytic accounts."
msgstr "Hiermee kun je je kostenplaatsen classificeren."

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_tree
msgid "Total"
msgstr "Totaal"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__product_uom_id
msgid "Unit of Measure"
msgstr "Maateenheid"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_unread
msgid "Unread Messages"
msgstr "Ongelezen berichten"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Aantal ongelezen berichten"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__product_uom_category_id
msgid "UoM Category"
msgstr "Maateenheid categorie"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__user_id
msgid "User"
msgstr "Gebruiker"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
msgid "e.g. Project XYZ"
msgstr "bijv. Project XYZ"
