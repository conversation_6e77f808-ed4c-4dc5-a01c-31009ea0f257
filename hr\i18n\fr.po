# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr
# 
# Translators:
# <PERSON>, 2021
# f5f0a0dac17dde787a1d812a6989680e, 2021
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# Geoffrey_<PERSON>F <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON>é<PERSON>le Collart <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_company__hr_presence_control_email_amount
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_email_amount
msgid "# emails to send"
msgstr "# courriels à envoyer"

#. module: hr
#: code:addons/hr/models/hr_job.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (copie)"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_job_view_kanban
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: hr
#: model:ir.actions.report,print_report_name:hr.hr_employee_print_badge
msgid "'Print Badge - %s' % (object.name).replace('/', '')"
msgstr "'Imprimer le badge - %s' % (object.name).replace('/', '')"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "12 days / year, including <br>6 of your choice."
msgstr "12 jours / an, dont <br>6 de votre choix"

#. module: hr
#: code:addons/hr/models/hr_employee.py:0
#, python-format
msgid ""
"<b>Congratulations!</b> May I recommend you to setup an <a "
"href=\"%s\">onboarding plan?</a>"
msgstr ""
"<b>Félicitations !</b> Puis-je vous recommander de configurer un <a "
"href=\"%s\">plan d'intégration ?</a>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"
msgstr "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "<small><b>READ</b></small>"
msgstr "<small><b>LIRE</b></small> "

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.hr_kanban_view_employees
msgid ""
"<span class=\"fa fa-circle text-success\" role=\"img\" aria-label=\"Present\" title=\"Present\" name=\"presence_present\">\n"
"                                                </span>"
msgstr ""
"<span class=\"fa fa-circle text-success\" role=\"img\" aria-label=\"Present\" title=\"Present\" name=\"presence_present\">\n"
"                                                </span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.hr_kanban_view_employees
msgid ""
"<span class=\"fa fa-circle text-warning\" role=\"img\" aria-label=\"To define\" title=\"To define\" name=\"presence_to_define\">\n"
"                                                </span>"
msgstr ""
"<span class=\"fa fa-circle text-warning\" role=\"img\" aria-label=\"To define\" title=\"To define\" name=\"presence_to_define\">\n"
"                                                </span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.hr_kanban_view_employees
msgid ""
"<span class=\"fa fa-circle-o text-muted\" role=\"img\" aria-label=\"Absent\" title=\"Absent\" name=\"presence_absent\">\n"
"                                                </span>"
msgstr ""
"<span class=\"fa fa-circle-o text-muted\" role=\"img\" aria-label=\"Absent\" title=\"Absent\" name=\"presence_absent\">\n"
"                                                </span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.hr_kanban_view_employees
msgid ""
"<span class=\"fa fa-circle-o text-success\" role=\"img\" aria-label=\"Present but not active\" title=\"Present but not active\" name=\"presence_absent_active\">\n"
"                                                </span>"
msgstr ""
"<span class=\"fa fa-circle-o text-success\" role=\"img\" aria-label=\"Present but not active\" title=\"Present but not active\" name=\"presence_absent_active\">\n"
"                                                </span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "<span class=\"ml8 mr-2\">IP Addresses (comma-separated)</span>"
msgstr "<span class=\"ml8 mr-2\">Adresses IP (séparées par des virgules)</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "<span class=\"ml8 mr-2\">Minimum number of emails to sent </span>"
msgstr "<span class=\"ml8 mr-2\">Nombre minimum d'emails envoyés </span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid "<span class=\"o_form_label o_hr_form_label\">Close Activities</span>"
msgstr "<span class=\"o_form_label o_hr_form_label\">Activités de clotûre</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid "<span class=\"o_form_label o_hr_form_label\">Detailed Reason</span>"
msgstr "<span class=\"o_form_label o_hr_form_label\">Raison détaillée</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid "<span class=\"o_form_label o_hr_form_label\">Personal Info</span>"
msgstr ""
"<span class=\"o_form_label o_hr_form_label\">Informations "
"personnelles</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Presence Control</span>"
msgstr "<span class=\"o_form_label\">Contrôle de présence</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                        Not Connected\n"
"                                    </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"Pas connecté\n"
"</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Not Connected\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                    Pas connecté\n"
"                                </span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "<span class=\"o_stat_text\">Connected Since</span>"
msgstr "<span class=\"o_stat_text\">Connecté depuis</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_partner_view_form
msgid "<span class=\"o_stat_text\">Employee(s)</span>"
msgstr "<span class=\"o_stat_text\">Employée(s)</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "<span class=\"o_stat_text\">Present Since</span>"
msgstr "<span class=\"o_stat_text\">Présent depuis</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "<span>Km</span>"
msgstr "<span>Km</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "<strong><span>Reporting</span></strong>"
msgstr "<strong><span>Rapports</span></strong>"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "A full-time position <br>Attractive salary package."
msgstr "Une position temps-plein <br> Package salarial attrayant."

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_employee_user_uniq
msgid "A user cannot be linked to multiple employees in the same company."
msgstr ""
"Un utilisateur ne peut pas être lié à plusieurs employés de la même société."

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_presence_state__absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_presence_state__absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_presence_state__absent
msgid "Absent"
msgstr "Absent"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Achieve monthly sales objectives"
msgstr "Atteindre les objectifs mensuels de vente"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_needaction
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_needaction
#: model:ir.model.fields,field_description:hr.field_hr_job__message_needaction
msgid "Action Needed"
msgstr "Nécessite une action"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__active
#: model:ir.model.fields,field_description:hr.field_hr_employee__active
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__active
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__active
#: model:ir.model.fields,field_description:hr.field_hr_plan__active
#: model:ir.model.fields,field_description:hr.field_hr_work_location__active
msgid "Active"
msgstr "Actif"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_ids
#: model:ir.model.fields,field_description:hr.field_hr_plan__plan_activity_type_ids
#: model_terms:ir.ui.view,arch_db:hr.hr_plan_activity_type_view_tree
#: model_terms:ir.ui.view,arch_db:hr.hr_plan_view_form
msgid "Activities"
msgstr "Activités"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_plan_activity_type_view_form
msgid "Activity"
msgstr "Activité"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Activité exception décoration"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_config_plan
msgid "Activity Planning"
msgstr "Planning d'activités"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_state
msgid "Activity State"
msgstr "Status de l'activité"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__activity_type_id
msgid "Activity Type"
msgstr "Type d'activité"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icône de type d'activité"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_tree
msgid "Activity by"
msgstr "Activité par"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.act_employee_from_department
#: model_terms:ir.actions.act_window,help:hr.hr_employee_public_action
#: model_terms:ir.actions.act_window,help:hr.open_view_employee_list_my
msgid "Add a new employee"
msgstr "Ajouter un nouvel employé"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_plan_action
msgid "Add a new plan"
msgstr "Ajouter un nouveau plan"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_plan_activity_type_action
msgid "Add a new planning activity"
msgstr "Ajouter une nouvelle activité de planification"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__departure_description
#: model:ir.model.fields,field_description:hr.field_hr_employee__departure_description
msgid "Additional Information"
msgstr "Informations supplémentaires"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__additional_note
#: model:ir.model.fields,field_description:hr.field_res_users__additional_note
msgid "Additional Note"
msgstr "Note additionnelle"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Additional languages"
msgstr "Langues additionnelles"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__address_home_id
#: model:ir.model.fields,field_description:hr.field_res_users__address_home_id
msgid "Address"
msgstr "Adresse"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Administrative Work"
msgstr "Travail administratif"

#. module: hr
#: model:res.groups,name:hr.group_hr_manager
msgid "Administrator"
msgstr "Administrateur"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__module_hr_presence
msgid "Advanced Presence Control"
msgstr "Contrôle des présences avancé"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Advanced presence of employees"
msgstr "Présences des employés avancées"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_mail_alias__alias_contact
#: model:ir.model.fields,field_description:hr.field_mail_channel__alias_contact
msgid "Alias Contact Security"
msgstr "Sécurité d'alias de contact"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__lang
#: model:ir.model.fields,help:hr.field_res_users__private_lang
msgid ""
"All the emails and documents sent to this contact will be translated in this"
" language."
msgstr ""
"Tous les courriels et documents envoyés à ce contact seront traduits dans "
"cette langue."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Allow employees to update their own data"
msgstr "Autoriser les employés à mettre à jour leurs propres données."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Allow employees to update their own data."
msgstr "Autoriser les employés à mettre à jour leurs propres données."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid "Apply"
msgstr "Appliquer"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Approvers"
msgstr "Validateurs"

#. module: hr
#. openerp-web
#: code:addons/hr/static/src/js/hr_employee.js:0
#: code:addons/hr/static/src/js/hr_employee.js:0
#, python-format
msgid "Archive"
msgstr "Archiver"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__archive_private_address
msgid "Archive Private Address"
msgstr "Archiver l'adresse privée"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.hr_plan_view_form
#: model_terms:ir.ui.view,arch_db:hr.hr_plan_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
#: model_terms:ir.ui.view,arch_db:hr.view_department_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Archived"
msgstr "Archivé"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid ""
"As an employee of our company, you will <b>collaborate with each department to create and deploy\n"
"                                disruptive products.</b> Come work at a growing company that offers great benefits with opportunities to\n"
"                                moving forward and learn alongside accomplished leaders. We're seeking an experienced and outstanding member of staff.\n"
"                                <br><br>\n"
"                                This position is both <b>creative and rigorous</b> by nature you need to think outside the box.\n"
"                                We expect the candidate to be proactive and have a \"get it done\" spirit. To be successful,\n"
"                                you will have solid solving problem skills."
msgstr ""
"En tant qu'employé de notre société, vous serez amené à <b>collaborer avec chaque département afin de créer et déployer des \n"
"                                produits audacieux.</b> Venez travailler dans une société en pleine expansion qui offre de nombreux bénéfices avec l'opportunité de \n"
"                                progresser et d'apprendre auprès de leaders accomplis. Nous recherchons pour l'équipe quelqu'un d'exceptionnel et d'expérimenté.\n"
"                                <br><br>\n"
"                                La position est à la fois <b>créative et rigoureuse</b>, vous devrez sortir des sentiers battus.\n"
"                                Nous attendons du candidat d'être proactif et d'avoir un esprit \"jusqu'au bout\". Posséder de solides capacités de résolution de problèmes est un atout.\n"
"                                "

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_attachment_count
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_attachment_count
#: model:ir.model.fields,field_description:hr.field_hr_job__message_attachment_count
msgid "Attachment Count"
msgstr "Compte des pièces jointes"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Attendance"
msgstr "Présence"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Attendance/Point of Sale"
msgstr "Présence/Point de vente"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__mail_alias__alias_contact__employees
msgid "Authenticated Employees"
msgstr "Employés identifiés"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.mail_channel_view_form_
msgid "Auto Subscribe Departments"
msgstr "Abonnement automatique des départements"

#. module: hr
#: model:ir.model.fields,help:hr.field_mail_channel__subscription_department_ids
msgid "Automatically subscribe members of those departments to the channel."
msgstr "Abonnez automatiquement les membres de ces départements à ces canaux."

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Autonomy"
msgstr "Autonomie"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Available"
msgstr "Disponible"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_1920
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_1920
msgid "Avatar"
msgstr "Avatar"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_1024
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_1024
msgid "Avatar 1024"
msgstr "Avatar 1024"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_128
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_128
msgid "Avatar 128"
msgstr "Avatar 128"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_256
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_256
msgid "Avatar 256"
msgstr "Avatar 256"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_512
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_512
msgid "Avatar 512"
msgstr "Avatar 512"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Away"
msgstr "Absent"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__bachelor
msgid "Bachelor"
msgstr "Bachelier"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Bachelor Degree or Higher"
msgstr "Licence ou plus"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__barcode
#: model:ir.model.fields,field_description:hr.field_res_users__barcode
msgid "Badge ID"
msgstr "ID du badge"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__bank_account_id
msgid "Bank Account Number"
msgstr "Numéro de compte bancaire"

#. module: hr
#: model:ir.model,name:hr.model_base
msgid "Base"
msgstr "Base"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_ip
msgid "Based on IP Address"
msgstr "Sur base de l'adresse IP"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__module_hr_attendance
msgid "Based on attendances"
msgstr "Sur base des présences"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_email
msgid "Based on number of emails sent"
msgstr "Sur base du nombre de courriels envoyés"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_login
msgid "Based on user status in system"
msgstr "Basé sur le statut de l'utilisateur dans le système"

#. module: hr
#: model:ir.model,name:hr.model_hr_employee_base
msgid "Basic Employee"
msgstr "Employé basique"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__can_edit
msgid "Can Edit"
msgstr "Peut modifier"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.plan_wizard
msgid "Cancel"
msgstr "Annuler"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__certificate
#: model:ir.model.fields,field_description:hr.field_res_users__certificate
msgid "Certificate Level"
msgstr "Niveau du certificat"

#. module: hr
#: model:ir.actions.act_window,name:hr.res_users_action_my
msgid "Change my Preferences"
msgstr "Changer mes préférences"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.hr_kanban_view_employees
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Chat"
msgstr "Messagerie instantanée"

#. module: hr
#: model:hr.job,name:hr.job_ceo
msgid "Chief Executive Officer"
msgstr "Directeur Général"

#. module: hr
#: model:hr.job,name:hr.job_cto
msgid "Chief Technical Officer"
msgstr "Directeur Informatique"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__child_ids
msgid "Child Departments"
msgstr "Sous-départements"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Citizenship"
msgstr "Citoyenneté"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "City"
msgstr "Ville"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__coach_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__coach_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__coach_id
#: model:ir.model.fields,field_description:hr.field_res_users__coach_id
#: model:ir.model.fields.selection,name:hr.selection__hr_plan_activity_type__responsible__coach
msgid "Coach"
msgstr "Mentor"

#. module: hr
#: code:addons/hr/models/hr_plan.py:0
#, python-format
msgid "Coach of employee %s is not set."
msgstr "Le mentor de l'employé %s n'est pas défini."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__color
#: model:ir.model.fields,field_description:hr.field_hr_employee__color
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__color
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__color
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__color
msgid "Color Index"
msgstr "Couleur"

#. module: hr
#: model:ir.model,name:hr.model_res_company
#: model_terms:ir.ui.view,arch_db:hr.view_department_tree
msgid "Companies"
msgstr "Sociétés"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__company_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__company_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__company_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__company_id
#: model:ir.model.fields,field_description:hr.field_hr_job__company_id
#: model:ir.model.fields,field_description:hr.field_hr_work_location__company_id
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Company"
msgstr "Société"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__company_country_id
msgid "Company Country"
msgstr "Pays de la société"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.print_employee_badge
msgid "Company Logo"
msgstr "Logo de la société"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__resource_calendar_id
msgid "Company Working Hours"
msgstr "Heures de travail de l'entreprise"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_id
msgid "Company employee"
msgstr "Employé de la société"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__complete_name
msgid "Complete Name"
msgstr "Nom complet"

#. module: hr
#: model:ir.model,name:hr.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de config"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_human_resources_configuration
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "Configuration"
msgstr "Configuration"

#. module: hr
#: model:hr.job,name:hr.job_consultant
msgid "Consultant"
msgstr "Consultant"

#. module: hr
#: model:ir.model,name:hr.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Contact Information"
msgstr "Information de contact"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__contractor
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__employee_type__contractor
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__employee_type__contractor
msgid "Contractor"
msgstr "Contractant"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Country"
msgstr "Pays"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__company_country_code
msgid "Country Code"
msgstr "Code du pays"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__country_of_birth
#: model:ir.model.fields,field_description:hr.field_res_users__country_of_birth
msgid "Country of Birth"
msgstr "Pays de naissance"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__create_date
msgid "Create Date"
msgstr "Date de création"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_department_kanban_action
#: model_terms:ir.actions.act_window,help:hr.hr_department_tree_action
msgid "Create a new department"
msgstr "Créer un nouveau département"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Create content that will help our users on a daily basis"
msgstr ""
"Création de contenu qui aidera nos utilisateurs sur une base journalière"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form
msgid "Create employee"
msgstr "Créer un employé"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_job__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_plan__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_plan_wizard__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_work_location__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__create_date
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__create_date
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__create_date
#: model:ir.model.fields,field_description:hr.field_hr_employee__create_date
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__create_date
#: model:ir.model.fields,field_description:hr.field_hr_job__create_date
#: model:ir.model.fields,field_description:hr.field_hr_plan__create_date
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__create_date
#: model:ir.model.fields,field_description:hr.field_hr_plan_wizard__create_date
#: model:ir.model.fields,field_description:hr.field_hr_work_location__create_date
msgid "Created on"
msgstr "Créé le"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__no_of_employee
msgid "Current Number of Employees"
msgstr "Nombre d'employés actuel"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Customer Relationship"
msgstr "Relation client"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__birthday
#: model:ir.model.fields,field_description:hr.field_res_users__birthday
msgid "Date of Birth"
msgstr "Date de naissance"

#. module: hr
#: code:addons/hr/models/hr_departure_reason.py:0
#, python-format
msgid "Default departure reasons cannot be deleted."
msgstr "La raison de départ par défaut ne peut pas être effacée."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__resource_calendar_id
msgid "Define the schedule of resource"
msgstr "Définir la planification de la ressource"

#. module: hr
#: model:ir.model,name:hr.model_hr_department
#: model:ir.model.fields,field_description:hr.field_hr_employee__department_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__department_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__department_id
#: model:ir.model.fields,field_description:hr.field_hr_job__department_id
#: model:ir.model.fields,field_description:hr.field_res_users__department_id
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Department"
msgstr "Département"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__name
msgid "Department Name"
msgstr "Nom du département"

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_department_kanban_action
#: model:ir.actions.act_window,name:hr.hr_department_tree_action
#: model:ir.ui.menu,name:hr.menu_hr_department_kanban
#: model:ir.ui.menu,name:hr.menu_hr_department_tree
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
msgid "Departments"
msgstr "Départements"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Departure"
msgstr "Départ"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__departure_date
#: model:ir.model.fields,field_description:hr.field_hr_employee__departure_date
msgid "Departure Date"
msgstr "Date de départ"

#. module: hr
#: model:ir.model,name:hr.model_hr_departure_reason
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__departure_reason_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__departure_reason_id
msgid "Departure Reason"
msgstr "Raison du départ"

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_departure_reason_action
#: model:ir.ui.menu,name:hr.menu_hr_departure_reason_tree
msgid "Departure Reasons"
msgstr "Raisons du départ"

#. module: hr
#: model:ir.model,name:hr.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "Assistant de départ"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Dependant"
msgstr "À charge"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__child_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__child_ids
msgid "Direct subordinates"
msgstr "Subordonnés directs"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_hr_employee
msgid "Directory"
msgstr "Annuaire"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid "Discard"
msgstr "Ignorer"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Discover our products."
msgstr "Découvrez nos produits"

#. module: hr
#: model:ir.model,name:hr.model_mail_channel
msgid "Discussion Channel"
msgstr "Canal de discussion"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__display_name
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__display_name
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__display_name
#: model:ir.model.fields,field_description:hr.field_hr_employee__display_name
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__display_name
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__display_name
#: model:ir.model.fields,field_description:hr.field_hr_job__display_name
#: model:ir.model.fields,field_description:hr.field_hr_plan__display_name
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__display_name
#: model:ir.model.fields,field_description:hr.field_hr_plan_wizard__display_name
#: model:ir.model.fields,field_description:hr.field_hr_work_location__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__marital__divorced
msgid "Divorced"
msgstr "Divorcé(e)"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__doctor
msgid "Doctor"
msgstr "Docteur"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__driving_license
msgid "Driving License"
msgstr "Permis de conduire"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid ""
"Each employee has a chance to see the impact of his work.\n"
"                        You can make a real contribution to the success of the company.\n"
"                        <br>\n"
"                        Several activities are often organized all over the year, such as weekly\n"
"                        sports sessions, team building events, monthly drink, and much more"
msgstr ""
"Chaque employé a la chance de voir l'impact de son travail.\n"
"                        Vous pouvez avoir une réelle contribution au succès de la société.\n"
"                        <br>\n"
"                        De nombreuses activités sont souvent organisées au cours de l'année, comme des sessions\n"
"                        de sport hebdomadaires, évènements de team building, apéros mensuels, et bien d'autres"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Eat &amp; Drink"
msgstr "Restauration"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Education"
msgstr "Éducation"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Email"
msgstr "Courriel"

#. module: hr
#: model:ir.model,name:hr.model_mail_alias
msgid "Email Aliases"
msgstr "Alias de messagerie"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Emergency"
msgstr "Urgence"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__emergency_contact
#: model:ir.model.fields,field_description:hr.field_res_users__emergency_contact
msgid "Emergency Contact"
msgstr "Contact d'urgence"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__emergency_phone
#: model:ir.model.fields,field_description:hr.field_res_users__emergency_phone
msgid "Emergency Phone"
msgstr "Téléphone d'urgence"

#. module: hr
#: model:ir.model,name:hr.model_hr_employee
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__employee_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__employee_id
#: model:ir.model.fields,field_description:hr.field_hr_plan_wizard__employee_id
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__employee
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__employee_type__employee
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__employee_type__employee
#: model:ir.model.fields.selection,name:hr.selection__hr_plan_activity_type__responsible__employee
#: model:ir.ui.menu,name:hr.menu_human_resources_configuration_employee
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Employee"
msgstr "Employé"

#. module: hr
#: model:ir.model,name:hr.model_hr_employee_category
msgid "Employee Category"
msgstr "Catégorie de l'employé"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_count
msgid "Employee Count"
msgstr "Nombre d'employés"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_employee_self_edit
msgid "Employee Editing"
msgstr "Edition de l'employé"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.print_employee_badge
msgid "Employee Image"
msgstr "Photo de l'employé"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__private_lang
msgid "Employee Lang"
msgstr "Langue de l'employé"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__name
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
msgid "Employee Name"
msgstr "Nom de l'employé"

#. module: hr
#: model:ir.actions.act_window,name:hr.open_view_categ_form
#: model:ir.model.fields,field_description:hr.field_res_users__category_ids
#: model_terms:ir.ui.view,arch_db:hr.view_employee_category_form
msgid "Employee Tags"
msgstr "Étiquettes d'employé"

#. module: hr
#. openerp-web
#: code:addons/hr/static/src/js/hr_employee.js:0
#, python-format
msgid "Employee Termination"
msgstr "Fin de collaboration"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__employee_type
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__employee_type
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__employee_type
#: model:ir.model.fields,field_description:hr.field_res_users__employee_type
msgid "Employee Type"
msgstr "Type d'employé"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Employee Update Rights"
msgstr "Mise à jour de droits des employés"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__bank_account_id
#: model:ir.model.fields,help:hr.field_res_users__employee_bank_account_id
msgid "Employee bank salary account"
msgstr "Compte bancaire du salaire de l'employé"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_bank_account_id
msgid "Employee's Bank Account Number"
msgstr "Numéro de compte bancaire de l'employé"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_country_id
msgid "Employee's Country"
msgstr "Pays de l'employé"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_kanban_view_employees
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Employee's Name"
msgstr "Nom de l'employé"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form
msgid "Employee(s)"
msgstr "Employé(s)"

#. module: hr
#: model:ir.actions.act_window,name:hr.act_employee_from_department
#: model:ir.actions.act_window,name:hr.hr_employee_action_from_user
#: model:ir.actions.act_window,name:hr.hr_employee_public_action
#: model:ir.actions.act_window,name:hr.open_view_employee_list
#: model:ir.actions.act_window,name:hr.open_view_employee_list_my
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__employee_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__employee_ids
#: model:ir.model.fields,field_description:hr.field_res_partner__employee_ids
#: model:ir.ui.menu,name:hr.menu_hr_employee_payroll
#: model:ir.ui.menu,name:hr.menu_hr_employee_user
#: model:ir.ui.menu,name:hr.menu_hr_root
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_tree
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_view_activity
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:hr.view_department_tree
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_tree
#: model_terms:ir.ui.view,arch_db:hr.view_partner_tree2
msgid "Employees"
msgstr "Employés"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_partner__employees_count
#: model:ir.model.fields,field_description:hr.field_res_users__employees_count
msgid "Employees Count"
msgstr "Compteur des employés"

#. module: hr
#: model:ir.actions.act_window,name:hr.open_view_employee_tree
msgid "Employees Structure"
msgstr "Structure des employés"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_category_list
msgid "Employees Tags"
msgstr "Étiquettes d'employés"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Enrich employee profiles with skills and resumes"
msgstr "Enrichissez les profils des employés avec des compétences et des CV"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__address_home_id
#: model:ir.model.fields,help:hr.field_res_users__address_home_id
msgid ""
"Enter here the private address of the employee, not the one linked to your "
"company."
msgstr ""
"Saisissez ici l'adresse personnelle de l'employé, mais pas celle associée à "
"votre entreprise."

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Expand your knowledge of various business industries"
msgstr "Etendez votre connaissance des business liés à différentes industries"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__no_of_recruitment
msgid "Expected New Employees"
msgstr "Nouveaux employés prévus"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job__expected_employees
msgid ""
"Expected number of employees for this job position after new recruitment."
msgstr "Nombre d'employés pour ce poste après de nouveaux recrutements."

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Experience in writing online content"
msgstr "Expérience en rédaction de contenu online. "

#. module: hr
#: model:hr.job,name:hr.job_developer
msgid "Experienced Developer"
msgstr "Développeur expérimenté"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__gender__female
msgid "Female"
msgstr "Féminin"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__study_field
#: model:ir.model.fields,field_description:hr.field_res_users__study_field
msgid "Field of Study"
msgstr "Champ d’étude"

#. module: hr
#: model:hr.departure.reason,name:hr.departure_fired
msgid "Fired"
msgstr "Licencié"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_follower_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_follower_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__message_follower_ids
msgid "Followers"
msgstr "Abonnés"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_partner_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_partner_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__message_partner_ids
msgid "Followers (Partners)"
msgstr "Abonnés (Partenaires)"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icône Font Awesome ex. fa-tasks"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__freelance
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__employee_type__freelance
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__employee_type__freelance
msgid "Freelancer"
msgstr "Travailleur indépendant"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Fruit, coffee and <br>snacks provided."
msgstr "Fruits, café et <br>snacks sont fournis."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Future Activities"
msgstr "Activités futures"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__gender
#: model:ir.model.fields,field_description:hr.field_res_users__gender
msgid "Gender"
msgstr "Genre"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Generate"
msgstr "Générer"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Google Adwords experience"
msgstr "Expérience avec Google Adwords"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__graduate
msgid "Graduate"
msgstr "Graduat"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Great team of smart people, in a friendly and open culture"
msgstr ""
"Chouette équipe de personnes intelligentes, dans une ambiance chaleureuse et"
" ouverte. "

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Group By"
msgstr "Regrouper par"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_mail_channel__subscription_department_ids
msgid "HR Departments"
msgstr "Départements RH"

#. module: hr
#: model:ir.actions.server,name:hr.ir_cron_data_check_work_permit_validity_ir_actions_server
#: model:ir.cron,cron_name:hr.ir_cron_data_check_work_permit_validity
#: model:ir.cron,name:hr.ir_cron_data_check_work_permit_validity
msgid "HR Employee: check work permit validity"
msgstr "HR Employé: Vérification de la validité du permis de travail"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "HR Settings"
msgstr "Paramètres RH"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__has_message
#: model:ir.model.fields,field_description:hr.field_hr_employee__has_message
#: model:ir.model.fields,field_description:hr.field_hr_job__has_message
msgid "Has Message"
msgstr "A un message"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Highly creative and autonomous"
msgstr "Hautement créatif et autonome"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__no_of_hired_employee
msgid "Hired Employees"
msgstr "Employés recrutés"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__km_home_work
#: model:ir.model.fields,field_description:hr.field_res_users__km_home_work
msgid "Home-Work Distance"
msgstr "Distance domicile-lieu de travail"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__hr_icon_display
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__hr_icon_display
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__hr_icon_display
msgid "Hr Icon Display"
msgstr "Visibilité de l'icône RH"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__hr_presence_state
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__hr_presence_state
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__hr_presence_state
#: model:ir.model.fields,field_description:hr.field_res_users__hr_presence_state
msgid "Hr Presence State"
msgstr "Etat de présence RH"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_hr_main
msgid "Human Resources"
msgstr "Ressources humaines"

#. module: hr
#: model:hr.job,name:hr.job_hrm
msgid "Human Resources Manager"
msgstr "Responsable des Ressources humaines"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__id
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__id
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__id
#: model:ir.model.fields,field_description:hr.field_hr_employee__id
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__id
#: model:ir.model.fields,field_description:hr.field_hr_job__id
#: model:ir.model.fields,field_description:hr.field_hr_plan__id
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__id
#: model:ir.model.fields,field_description:hr.field_hr_plan_wizard__id
#: model:ir.model.fields,field_description:hr.field_hr_work_location__id
msgid "ID"
msgstr "ID"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__id_card
msgid "ID Card Copy"
msgstr "Copie de la carte d'identité"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__barcode
#: model:ir.model.fields,help:hr.field_res_users__barcode
msgid "ID used for employee identification."
msgstr "Identifiant utilisé pour l'identification de l'employé."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_exception_icon
msgid "Icon"
msgstr "Icône"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icône pour indiquer une activité d'exception."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__identification_id
#: model:ir.model.fields,field_description:hr.field_res_users__identification_id
msgid "Identification No"
msgstr "N° d'identification"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_needaction
#: model:ir.model.fields,help:hr.field_hr_department__message_unread
#: model:ir.model.fields,help:hr.field_hr_employee__message_needaction
#: model:ir.model.fields,help:hr.field_hr_employee__message_unread
#: model:ir.model.fields,help:hr.field_hr_job__message_needaction
#: model:ir.model.fields,help:hr.field_hr_job__message_unread
msgid "If checked, new messages require your attention."
msgstr "Si coché, de nouveaux messages demandent votre attention."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_has_error
#: model:ir.model.fields,help:hr.field_hr_employee__message_has_error
#: model:ir.model.fields,help:hr.field_hr_job__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Si actif, certains messages ont une erreur de livraison."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"Si vous cochez la case \"Active\", la resource sera cachée mais pas "
"supprimée."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_1920
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_1920
msgid "Image"
msgstr "Image"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_1024
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_1024
msgid "Image 1024"
msgstr "Image 1024"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_128
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_128
msgid "Image 128"
msgstr "Image 128"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_256
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_256
msgid "Image 256"
msgstr "Image 256"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_512
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_512
msgid "Image 512"
msgstr "Image 512"

#. module: hr
#: code:addons/hr/models/hr_employee.py:0
#, python-format
msgid "Import Template for Employees"
msgstr "Import de modèles pour employés "

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "In Position"
msgstr "En poste"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "In Recruitment"
msgstr "En recrutement"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_is_follower
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_is_follower
#: model:ir.model.fields,field_description:hr.field_hr_job__message_is_follower
msgid "Is Follower"
msgstr "Est un abonné"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__is_system
msgid "Is System"
msgstr "est lié au système"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_tree
msgid "Job"
msgstr "Poste"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__description
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "Job Description"
msgstr "Description du poste"

#. module: hr
#: model:ir.model,name:hr.model_hr_job
#: model:ir.model.fields,field_description:hr.field_hr_employee__job_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__job_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__job_id
#: model:ir.model.fields,field_description:hr.field_hr_job__name
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Job Position"
msgstr "Poste Occupé"

#. module: hr
#: model:ir.actions.act_window,name:hr.action_hr_job
#: model:ir.ui.menu,name:hr.menu_view_hr_job
msgid "Job Positions"
msgstr "Postes"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__job_title
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__job_title
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__job_title
#: model:ir.model.fields,field_description:hr.field_res_users__job_title
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
msgid "Job Title"
msgstr "Titre du poste"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__jobs_ids
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Jobs"
msgstr "Postes"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__lang
msgid "Lang"
msgstr "Langue"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Language"
msgstr "Langue"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__last_activity
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__last_activity
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__last_activity
#: model:ir.model.fields,field_description:hr.field_res_users__last_activity
msgid "Last Activity"
msgstr "Dernière activité"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__last_activity_time
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__last_activity_time
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__last_activity_time
#: model:ir.model.fields,field_description:hr.field_res_users__last_activity_time
msgid "Last Activity Time"
msgstr "Heure de la dernière activité"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department____last_update
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason____last_update
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard____last_update
#: model:ir.model.fields,field_description:hr.field_hr_employee____last_update
#: model:ir.model.fields,field_description:hr.field_hr_employee_category____last_update
#: model:ir.model.fields,field_description:hr.field_hr_employee_public____last_update
#: model:ir.model.fields,field_description:hr.field_hr_job____last_update
#: model:ir.model.fields,field_description:hr.field_hr_plan____last_update
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type____last_update
#: model:ir.model.fields,field_description:hr.field_hr_plan_wizard____last_update
#: model:ir.model.fields,field_description:hr.field_hr_work_location____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_job__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_plan__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_plan_wizard__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_work_location__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__write_date
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__write_date
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__write_date
#: model:ir.model.fields,field_description:hr.field_hr_employee__write_date
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__write_date
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__write_date
#: model:ir.model.fields,field_description:hr.field_hr_job__write_date
#: model:ir.model.fields,field_description:hr.field_hr_plan__write_date
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__write_date
#: model:ir.model.fields,field_description:hr.field_hr_plan_wizard__write_date
#: model:ir.model.fields,field_description:hr.field_hr_work_location__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Late Activities"
msgstr "Activités en retard"

#. module: hr
#: model:ir.actions.act_window,name:hr.plan_wizard_action
#: model_terms:ir.ui.view,arch_db:hr.plan_wizard
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Launch Plan"
msgstr "Plan de lancement"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Lead the entire sales cycle"
msgstr "Dirige le cycle de vente entier"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__marital__cohabitant
msgid "Legal Cohabitant"
msgstr "Cohabitant légal"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.action_hr_job
msgid "Let's create a job position."
msgstr "Créons une offre d'emploi."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Location"
msgstr "Lieu"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_work_location__location_number
msgid "Location Number"
msgstr "Numéro du lieu"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_main_attachment_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_main_attachment_id
#: model:ir.model.fields,field_description:hr.field_hr_job__message_main_attachment_id
msgid "Main Attachment"
msgstr "Pièce jointe principale"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__gender__male
msgid "Male"
msgstr "Masculin"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__manager_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__parent_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__parent_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__parent_id
#: model:ir.model.fields,field_description:hr.field_res_users__employee_parent_id
#: model:ir.model.fields.selection,name:hr.selection__hr_plan_activity_type__responsible__manager
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Manager"
msgstr "Gestionnaire"

#. module: hr
#: code:addons/hr/models/hr_plan.py:0
#, python-format
msgid "Manager of employee %s is not set."
msgstr "Le manager de l'employé %s n'est pas défini."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__marital
#: model:ir.model.fields,field_description:hr.field_res_users__marital
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Marital Status"
msgstr "État civil"

#. module: hr
#: model:hr.job,name:hr.job_marketing
msgid "Marketing and Community Manager"
msgstr "Gestionnaire de communauté et marketing"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__marital__married
msgid "Married"
msgstr "Marié"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__master
msgid "Master"
msgstr "Master"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Master demos of our software"
msgstr "Maitriser les démonstrations de nos produits"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__member_ids
msgid "Members"
msgstr "Membres"

#. module: hr
#: model:ir.model,name:hr.model_ir_ui_menu
msgid "Menu"
msgstr "Menu"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_has_error
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_has_error
#: model:ir.model.fields,field_description:hr.field_hr_job__message_has_error
msgid "Message Delivery error"
msgstr "Erreur d'envoi du message"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__message_ids
msgid "Messages"
msgstr "Messages"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Must Have"
msgstr "Compétences nécessaires"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Échéance de mon activité"

#. module: hr
#. openerp-web
#: code:addons/hr/static/src/xml/hr_templates.xml:0
#, python-format
msgid "My Profile"
msgstr "Mon profil"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__name
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__name
#: model:ir.model.fields,field_description:hr.field_hr_plan__name
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__responsible_id
msgid "Name"
msgstr "Nom"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__country_id
msgid "Nationality (Country)"
msgstr "Nationalité (pays)"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Negotiate and contract"
msgstr "Négocier et conclure"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Activité suivante de l'événement du calendrier"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Date limite de l'activité à venir"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_summary
msgid "Next Activity Summary"
msgstr "Résumé d'activité suivant"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_type_id
msgid "Next Activity Type"
msgstr "Type d'activités à venir"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Nice to have"
msgstr "Agréable d'avoir"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "No dumb managers, no stupid tools to use, no rigid working hours"
msgstr ""
"Pas de managers simplets, d'outils idiots à utiliser, d'heures de travail "
"rigides"

#. module: hr
#: code:addons/hr/models/hr_plan.py:0
#, python-format
msgid "No specific user given on activity %s."
msgstr "Absence d'utilisateur précis lié à l'activité %s."

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid ""
"No waste of time in enterprise processes, real responsibilities and autonomy"
msgstr ""
"Pas de perte de temps dans des processus internes, véritable autonomie et "
"responsabilités"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_job__state__open
msgid "Not Recruiting"
msgstr "Pas de recrutement"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Not available"
msgstr "Pas disponible"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__note
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__note
msgid "Note"
msgstr "Note"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__notes
msgid "Notes"
msgstr "Notes"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_needaction_counter
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_needaction_counter
#: model:ir.model.fields,field_description:hr.field_hr_job__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'actions"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__children
#: model:ir.model.fields,field_description:hr.field_res_users__children
msgid "Number of Children"
msgstr "Nombre d'enfants"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job__no_of_employee
msgid "Number of employees currently occupying this job position."
msgstr "Nombre d'employés occupant actuellement ce poste."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_has_error_counter
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_has_error_counter
#: model:ir.model.fields,field_description:hr.field_hr_job__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'erreurs"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job__no_of_hired_employee
msgid ""
"Number of hired employees for this job position during recruitment phase."
msgstr ""
"Nombre d'employés engagés pour ce poste pendant la période du recrutement."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_needaction_counter
#: model:ir.model.fields,help:hr.field_hr_employee__message_needaction_counter
#: model:ir.model.fields,help:hr.field_hr_job__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Nombre de messages exigeant une action"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_has_error_counter
#: model:ir.model.fields,help:hr.field_hr_employee__message_has_error_counter
#: model:ir.model.fields,help:hr.field_hr_job__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de messages avec des erreurs d'envoi"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job__no_of_recruitment
msgid "Number of new employees you expect to recruit."
msgstr "Nombre d'employés que vous pensez recruter."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_unread_counter
#: model:ir.model.fields,help:hr.field_hr_employee__message_unread_counter
#: model:ir.model.fields,help:hr.field_hr_job__message_unread_counter
msgid "Number of unread messages"
msgstr "Nombre de messages non lus"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_department_tree_action
msgid ""
"Odoo's department structure is used to manage all documents\n"
"                    related to employees by departments: expenses, timesheets,\n"
"                    leaves, recruitments, etc."
msgstr ""
"La structure des départments d'Odoo est utilisée pour gérer tous les documents\n"
"                    liés aux employés : notes de frais, Feuilles de temps,\n"
"                    congés, recrutement, etc."

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_department_kanban_action
msgid ""
"Odoo's department structure is used to manage all documents\n"
"                related to employees by departments: expenses, timesheets,\n"
"                time off, recruitments, etc."
msgstr ""
"La structure des départments d'Odoo est utilisée pour gérer tous les documents\n"
"liés aux employés : notes de frais, Feuilles de temps,\n"
"congés, recrutement, etc."

#. module: hr
#: model:res.groups,name:hr.group_hr_user
msgid "Officer"
msgstr "Responsable"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__other
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__gender__other
#: model:ir.model.fields.selection,name:hr.selection__hr_plan_activity_type__responsible__other
msgid "Other"
msgstr "Autre"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Our Product"
msgstr "Notre produit"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__pin
#: model:ir.model.fields,field_description:hr.field_res_users__pin
msgid "PIN"
msgstr "Code"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "PIN Code"
msgstr "Code PIN"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__pin
#: model:ir.model.fields,help:hr.field_res_users__pin
msgid ""
"PIN used to Check In/Out in the Kiosk Mode of the Attendance application (if"
" enabled in Configuration) and to change the cashier in the Point of Sale "
"application."
msgstr ""
"Le code PIN est utilisé pour le Check In/Out du mode kiosque de "
"l'application Présences (si activé dans la configuration) et pour changer de"
" caissier dans l'application Point de vente. "

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__parent_id
msgid "Parent Department"
msgstr "Département parent"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__user_partner_id
#: model:ir.model.fields,help:hr.field_hr_employee_public__user_partner_id
msgid "Partner-related data of the user"
msgstr "Données du partenaire lié à l'utilisateur"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Passion for software products"
msgstr "Passion pour les produits logiciels"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__passport_id
#: model:ir.model.fields,field_description:hr.field_res_users__passport_id
msgid "Passport No"
msgstr "N° de passeport"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Payroll"
msgstr "Paie"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Perfect written English"
msgstr "Parfaite rédaction en anglais"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Perks"
msgstr "Avantages"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Personal Evolution"
msgstr "Évolution personnelle"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Phone"
msgstr "Téléphone"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__place_of_birth
#: model:ir.model.fields,field_description:hr.field_res_users__place_of_birth
msgid "Place of Birth"
msgstr "Lieu de naissance"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_plan_wizard__plan_id
#: model_terms:ir.ui.view,arch_db:hr.hr_plan_view_search
msgid "Plan"
msgstr "Plan"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_plan_view_form
msgid "Plan Name"
msgstr "Nom de la récurrence"

#. module: hr
#: model:ir.model,name:hr.model_hr_plan_wizard
msgid "Plan Wizard"
msgstr "Assistant Plan"

#. module: hr
#: model:ir.model,name:hr.model_hr_plan_activity_type
msgid "Plan activity type"
msgstr "Planifier le type d'activité"

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_plan_action
#: model_terms:ir.ui.view,arch_db:hr.hr_plan_view_form
#: model_terms:ir.ui.view,arch_db:hr.hr_plan_view_tree
msgid "Planning"
msgstr "Planification"

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_plan_activity_type_action
#: model:ir.ui.menu,name:hr.menu_config_plan_types
msgid "Planning Types"
msgstr "Types de planification"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_config_plan_plan
msgid "Plans"
msgstr "Plans"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Play any sport with colleagues, <br>the bill is covered."
msgstr "Faites du sport avec vos collègues, <br> la facture sera couverte."

#. module: hr
#: model:ir.model.fields,help:hr.field_mail_alias__alias_contact
#: model:ir.model.fields,help:hr.field_mail_channel__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Politique pour poster un message sur le document en utilisant la passerelle de messagerie.\n"
"- tout le monde: tout le monde peut poster\n"
"- partenaires: partenaires authentifiés uniquement\n"
"- abonnés: seuls les abonnés du document concerné ou les membres des chaînes suivantes\n"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Presence of employees"
msgstr "Présence des employés"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Presence reporting screen, email and IP address control."
msgstr "Écran de rapport de présence, contrôle d'adresse e-mail et IP."

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_presence_state__present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_presence_state__present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_presence_state__present
msgid "Present"
msgstr "Présent"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_absent_active
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_absent_active
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_absent_active
msgid "Present but not active"
msgstr "Présent mais inactif"

#. module: hr
#: model:ir.actions.report,name:hr.hr_employee_print_badge
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Print Badge"
msgstr "Imprimer le badge"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Private Address"
msgstr "Adresse personnelle"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__private_city
msgid "Private City"
msgstr "Ville privé"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Private Contact"
msgstr "Coordonnées privées"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__private_country_id
msgid "Private Country"
msgstr "Pays privé"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_email
#: model:ir.model.fields,field_description:hr.field_res_users__private_email
msgid "Private Email"
msgstr "Adresse électronique privée"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Private Information"
msgstr "Informations privées"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__phone
#: model:ir.model.fields,field_description:hr.field_res_users__employee_phone
msgid "Private Phone"
msgstr "Numéro de téléphone privé"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__private_state_id
msgid "Private State"
msgstr "Etat privé"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__private_street
msgid "Private Street"
msgstr "Rue privée"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__private_street2
msgid "Private Street2"
msgstr "Rue 2 privée"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__private_zip
msgid "Private Zip"
msgstr "Zip privé"

#. module: hr
#: model:ir.model,name:hr.model_hr_employee_public
msgid "Public Employee"
msgstr "Fonctionnaire"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Qualify the customer needs"
msgstr "Qualifier les besoins du client"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.action_hr_job
msgid "Ready to recruit more efficiently?"
msgstr "Prêt à recruter plus efficacement ?"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Real responsibilities and challenges in a fast evolving company"
msgstr ""
"De vraies responsabilités et de vrais défis dans une société évoluant "
"rapidement"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__name
msgid "Reason"
msgstr "Motif"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "Recruitment"
msgstr "Recrutement"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_job__state__recruit
msgid "Recruitment in Progress"
msgstr "Recrutement en cours"

#. module: hr
#: code:addons/hr/models/hr_employee.py:0
#: model:ir.actions.act_window,name:hr.hr_departure_wizard_action
#, python-format
msgid "Register Departure"
msgstr "Enregistrer le départ"

#. module: hr
#: code:addons/hr/models/res_partner.py:0
#, python-format
msgid "Related Employees"
msgstr "Employés associés"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Related User"
msgstr "Utilisateur lié"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_ids
msgid "Related employee"
msgstr "Employé associé"

#. module: hr
#: model:ir.model.fields,help:hr.field_res_partner__employee_ids
msgid "Related employees based on their private address"
msgstr "Employés associés sur base de leur adresse privée"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__user_id
#: model:ir.model.fields,help:hr.field_resource_resource__user_id
msgid "Related user name for the resource to manage its access."
msgstr "Utilisateur associé à la ressource pour gérer les droits d'accès."

#. module: hr
#: model:ir.ui.menu,name:hr.hr_menu_hr_reports
#: model:ir.ui.menu,name:hr.menu_hr_reporting_timesheet
msgid "Reporting"
msgstr "Analyse"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__requirements
msgid "Requirements"
msgstr "Exigences"

#. module: hr
#: model:hr.departure.reason,name:hr.departure_resigned
msgid "Resigned"
msgstr "A démissionné"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__resource_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__resource_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__resource_id
msgid "Resource"
msgstr "Ressource"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__resource_calendar_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__resource_calendar_id
msgid "Resource Calendar"
msgstr "Calendrier de la ressource"

#. module: hr
#: model:ir.model,name:hr.model_resource_resource
msgid "Resources"
msgstr "Ressources"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Responsibilities"
msgstr "Responsabilités"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__responsible
msgid "Responsible"
msgstr "Responsable"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_user_id
msgid "Responsible User"
msgstr "Responsable"

#. module: hr
#: model:hr.departure.reason,name:hr.departure_retired
msgid "Retired"
msgstr "Pensionné"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__sinid
msgid "SIN No"
msgstr "Numéro d'assurance sociale"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__ssnid
msgid "SSN No"
msgstr "Numéro de sécurité sociale"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Schedule"
msgstr "Horaire"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__study_school
#: model:ir.model.fields,field_description:hr.field_res_users__study_school
msgid "School"
msgstr "Établissement scolaire"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__coach_id
#: model:ir.model.fields,help:hr.field_hr_employee_base__coach_id
#: model:ir.model.fields,help:hr.field_hr_employee_public__coach_id
#: model:ir.model.fields,help:hr.field_res_users__coach_id
msgid ""
"Select the \"Employee\" who is the coach of this employee.\n"
"The \"Coach\" has no specific rights or responsibilities by default."
msgstr ""
"Sélectionnez la personne qui est le mentor de cet employé.\n"
"Le mentor n'a pas de droits spécifiques ou de responsabilités par défaut. "

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__sequence
#: model:ir.model.fields,field_description:hr.field_hr_job__sequence
msgid "Sequence"
msgstr "Séquence"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Set default company schedule to manage your employees working time"
msgstr ""
"Définir le calendrier par défaut de l'entreprise pour gérer le temps de "
"travail de vos employés"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job__state
msgid ""
"Set whether the recruitment process is open or closed for this job position."
msgstr ""
"Définir si le processus de recrutement est en cours ou clôturé pour ce poste"
" de travail."

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_config_settings_action
#: model:ir.ui.menu,name:hr.hr_menu_configuration
msgid "Settings"
msgstr "Paramètres"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__marital__single
msgid "Single"
msgstr "Célibataire"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__module_hr_skills
msgid "Skills Management"
msgstr "Gestion des compétences"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__sinid
msgid "Social Insurance Number"
msgstr "Numéro d'assurance sociale"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__ssnid
msgid "Social Security Number"
msgstr "Numéro de sécurité sociale"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_plan_activity_type__responsible_id
msgid "Specific responsible of activity if not linked to the employee."
msgstr "Responsable spécifique de l'activité s'il n'est pas lié à l'employé."

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Sport Activity"
msgstr "Activités sportives"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__spouse_birthdate
#: model:ir.model.fields,field_description:hr.field_res_users__spouse_birthdate
msgid "Spouse Birthdate"
msgstr "Date de naissance de l'épouse"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__spouse_complete_name
#: model:ir.model.fields,field_description:hr.field_res_users__spouse_complete_name
msgid "Spouse Complete Name"
msgstr "Nom complet de l'épouse"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "Start Recruitment"
msgstr "Lancer le recrutement"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "State"
msgstr "État"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__state
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Status"
msgstr "État"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Statut basé sur les activités\n"
"En retard : la date d'échéance est déjà dépassée\n"
"Aujourd'hui : la date d'activité est aujourd'hui\n"
"Planifiée : activités futures"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "Stop Recruitment"
msgstr "Arrêter le recrutement"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Street 2..."
msgstr "Rue 2..."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Street..."
msgstr "Rue..."

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Strong analytical skills"
msgstr "Compétences analytiques avancées"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__student
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__employee_type__student
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__employee_type__student
msgid "Student"
msgstr "Etudiant"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__summary
msgid "Summary"
msgstr "Résumé"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__name
msgid "Tag Name"
msgstr "Nom de l'étiquette"

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_employee_category_name_uniq
msgid "Tag name already exists !"
msgstr "Ce nom d'étiquette existe déjà !"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__category_ids
#: model:ir.ui.menu,name:hr.menu_view_employee_category_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Tags"
msgstr "Étiquettes"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Technical Expertise"
msgstr "Expertise technique"

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_employee_barcode_uniq
msgid ""
"The Badge ID must be unique, this one is already assigned to another "
"employee."
msgstr ""
"l'ID du badge doit être unique, celui-ci est déjà utilisé pour un autre "
"employé."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__company_country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"Code de pays ISO en deux caractères. \n"
"Vous pouvez utiliser ce champ pour une recherche rapide."

#. module: hr
#: code:addons/hr/models/hr_employee.py:0
#, python-format
msgid "The PIN must be a sequence of digits."
msgstr "Le code PIN doit être une séquence de chiffres."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__is_address_home_a_company
#: model:ir.model.fields,field_description:hr.field_res_users__is_address_home_a_company
msgid "The employee address has a company linked"
msgstr "L'adresse de l'employé a une société liée"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__employee_type
#: model:ir.model.fields,help:hr.field_hr_employee_base__employee_type
#: model:ir.model.fields,help:hr.field_hr_employee_public__employee_type
#: model:ir.model.fields,help:hr.field_res_users__employee_type
msgid ""
"The employee type. Although the primary purpose may seem to categorize "
"employees, this field has also an impact in the Contract History. Only "
"Employee type is supposed to be under contract and will have a Contract "
"History."
msgstr ""
"Le type : employé. Bien que le but premier pourrait sembler être la "
"catégorisation, ce champ a également un impact sur l'historique des "
"contrats. Seul le type : employé est supposé être sous contrat et avoir un "
"historique de contrat. "

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_job_no_of_recruitment_positive
msgid "The expected number of new employees must be positive."
msgstr "Le nombre de nouveaux employés attendus doit être positif"

#. module: hr
#: code:addons/hr/models/hr_employee.py:0
#, python-format
msgid ""
"The fields \"%s\" you try to read is not available on the public employee "
"profile."
msgstr ""
"Les champs \"%s\" que vous essayez de lire n'est pas disponible sur le "
"profil de fonctionnaire."

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_job_name_company_uniq
msgid "The name of the job position must be unique per department in company!"
msgstr "Le nom du poste doit être unique par département de la société !"

#. module: hr
#: model:res.groups,comment:hr.group_hr_user
msgid "The user will be able to approve document created by employees."
msgstr ""
"L'utilisateur sera capable d'approuver un document créer par les employés. "

#. module: hr
#: model:res.groups,comment:hr.group_hr_manager
msgid ""
"The user will have access to the human resources configuration as well as "
"statistic reports."
msgstr ""
"L'utilisateur aura accès à la configuration des ressources humaines ainsi "
"qu'aux rapports de statistique."

#. module: hr
#: code:addons/hr/models/hr_employee.py:0
#, python-format
msgid "The work permit of %(employee)s expires at %(date)s."
msgstr "Le permis de travail de %(employee)s expire le %(date)s."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__tz
#: model:ir.model.fields,help:hr.field_hr_employee_base__tz
#: model:ir.model.fields,help:hr.field_hr_employee_public__tz
msgid ""
"This field is used in order to define in which timezone the resources will "
"work."
msgstr ""
"Ce champ est utilisé pour définir dans quel fuseau horaire la ressource "
"travaillera."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__tz
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__tz
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__tz
msgid "Timezone"
msgstr "Fuseau horaire"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_presence_state__to_define
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_presence_state__to_define
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_presence_state__to_define
msgid "To Define"
msgstr "A définir"

#. module: hr
#: code:addons/hr/models/hr_employee.py:0
#, python-format
msgid ""
"To avoid multi company issues (loosing the access to your previous "
"contracts, leaves, ...), you should create another employee in the new "
"company instead."
msgstr ""
"Pour éviter les problèmes de multi-compagnie (perdre l'accès aux contrats "
"précédents, congés, ...), vous devriez plutôt créer un autre employé dans la"
" nouvelle société. "

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_to_define
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_to_define
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_to_define
msgid "To define"
msgstr "A définir"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Today Activities"
msgstr "Activités du jour"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__total_employee
msgid "Total Employee"
msgstr "Total employé"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__expected_employees
msgid "Total Forecasted Employees"
msgstr "Nombre total d'employés prévu"

#. module: hr
#: model:hr.job,name:hr.job_trainee
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__trainee
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__employee_type__trainee
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__employee_type__trainee
msgid "Trainee"
msgstr "Stagiaire"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Trainings"
msgstr "Formations"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type d'activité d'exception enregistrée"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_undetermined
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_undetermined
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_undetermined
msgid "Undetermined"
msgstr "Indéterminé"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_unread
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_unread
#: model:ir.model.fields,field_description:hr.field_hr_job__message_unread
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Unread Messages"
msgstr "Messages non lus"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_unread_counter
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_unread_counter
#: model:ir.model.fields,field_description:hr.field_hr_job__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Compteur de messages non lus"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__user_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__user_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__user_id
#: model:ir.model.fields,field_description:hr.field_resource_resource__user_id
msgid "User"
msgstr "Utilisateur"

#. module: hr
#: code:addons/hr/models/hr_plan.py:0
#, python-format
msgid "User linked to employee %s is required."
msgstr "Utilisateur lié à l'employé %s est requis."

#. module: hr
#: code:addons/hr/models/hr_plan.py:0
#, python-format
msgid "User of coach of employee %s is not set."
msgstr "L'utilisateur du mentor de l'employé %s n'est pas défini."

#. module: hr
#: code:addons/hr/models/hr_plan.py:0
#, python-format
msgid "User of manager of employee %s is not set."
msgstr "L'utilisateur du responsable de l'employé %s n'est pas défini."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__user_partner_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__user_partner_id
msgid "User's partner"
msgstr "Partenaire de l'utilisateur"

#. module: hr
#: model:ir.model,name:hr.model_res_users
msgid "Users"
msgstr "Utilisateurs"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_job_view_kanban
msgid "Vacancies :"
msgstr "Postes vacants :"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_company__hr_presence_control_ip_list
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_ip_list
msgid "Valid IP addresses"
msgstr "Adresses IP valides"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Valid work permit for Belgium"
msgstr "Permis de travail valide pour la Belgique"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__visa_expire
#: model:ir.model.fields,field_description:hr.field_res_users__visa_expire
msgid "Visa Expiration Date"
msgstr "Date d'expiration du visa"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__visa_no
#: model:ir.model.fields,field_description:hr.field_res_users__visa_no
msgid "Visa No"
msgstr "N° de visa"

#. module: hr
#: code:addons/hr/models/hr_employee.py:0
#, python-format
msgid "Warning"
msgstr "Avertissement"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "What We Offer"
msgstr "Ce que nous offrons"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "What's great in the job?"
msgstr "Qu'est-ce qui est génial dans ce travail ?"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__marital__widower
msgid "Widower"
msgstr "Veuf(ve)"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.act_employee_from_department
#: model_terms:ir.actions.act_window,help:hr.hr_employee_public_action
msgid ""
"With just a quick glance on the Odoo employee screen, you\n"
"                can easily find all the information you need for each person;\n"
"                contact data, job position, availability, etc."
msgstr ""
"Avec un coup d’œil rapide sur l'écran Odoo des employés, vous pouvez "
"aisément trouver toute l'information dont vous avez besoin sur chaque "
"personne; informations de contact, position de travail, disponibilité, etc."

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.open_view_employee_list_my
msgid ""
"With just a quick glance on the Odoo employee screen, you\n"
"               can easily find all the information you need for each person;\n"
"               contact data, job position, availability, etc."
msgstr ""
"D'un simple coup d'œil sur l'écran des employés d'Odoo, vous \n"
"pouvez facilement trouver toutes les informations dont vous avez besoin pour chaque personne; \n"
"données de contact, poste, disponibilité, etc."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__address_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__address_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__address_id
#: model:ir.model.fields,field_description:hr.field_hr_work_location__address_id
#: model:ir.model.fields,field_description:hr.field_res_users__address_id
msgid "Work Address"
msgstr "Adresse professionnelle"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_email
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_email
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_email
#: model:ir.model.fields,field_description:hr.field_res_users__work_email
msgid "Work Email"
msgstr "Adresse électronique professionnelle"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Work Information"
msgstr "Informations professionnelles"

#. module: hr
#: model:ir.model,name:hr.model_hr_work_location
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_location_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_location_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_location_id
#: model:ir.model.fields,field_description:hr.field_hr_work_location__name
#: model:ir.model.fields,field_description:hr.field_res_users__work_location_id
#: model_terms:ir.ui.view,arch_db:hr.hr_work_location_form_view
#: model_terms:ir.ui.view,arch_db:hr.hr_work_location_tree_view
msgid "Work Location"
msgstr "Lieu de travail"

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_work_location_action
#: model:ir.ui.menu,name:hr.menu_hr_work_location_tree
msgid "Work Locations"
msgstr "Lieux de travail"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__mobile_phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__mobile_phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__mobile_phone
#: model:ir.model.fields,field_description:hr.field_res_users__mobile_phone
msgid "Work Mobile"
msgstr "Tél. portable professionnel"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Work Organization"
msgstr "Organisation du travail"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__has_work_permit
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Work Permit"
msgstr "Permis de travail"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_permit_expiration_date
msgid "Work Permit Expiration Date"
msgstr "Date d'expiration du permis de travail"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__permit_no
#: model:ir.model.fields,field_description:hr.field_res_users__permit_no
msgid "Work Permit No"
msgstr "N° de permis de travail"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_permit_scheduled_activity
msgid "Work Permit Scheduled Activity"
msgstr "Activité planifiée du permis de travail"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_phone
#: model:ir.model.fields,field_description:hr.field_res_users__work_phone
msgid "Work Phone"
msgstr "Téléphone professionnel"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__resource_calendar_id
msgid "Working Hours"
msgstr "Heures de travail"

#. module: hr
#: code:addons/hr/models/res_users.py:0
#, python-format
msgid ""
"You are only allowed to update your preferences. Please contact a HR officer"
" to update other information."
msgstr ""
"Vous êtes seulement autorisé à mettre à jour vos préférences. Veuillez "
"contacter un responsable RH pour mettre à jour les autres informations."

#. module: hr
#. openerp-web
#: code:addons/hr/static/src/models/employee/employee.js:0
#, python-format
msgid "You can only chat with employees that have a dedicated user."
msgstr ""
"Vous pouvez seulement dialoguer avec les employés qui ont un utilisateur "
"désigné. "

#. module: hr
#: code:addons/hr/models/hr_department.py:0
#, python-format
msgid "You cannot create recursive departments."
msgstr "Vous ne pouvez pas créer des départements récursifs. "

#. module: hr
#: code:addons/hr/models/hr_employee.py:0
#, python-format
msgid "You do not have access to this document."
msgstr "Vous n'avez pas accès à ce document."

#. module: hr
#: code:addons/hr/models/res_config_settings.py:0
#, python-format
msgid "You should select at least one Advanced Presence Control option."
msgstr ""
"Vous devez sélectionner au minimum une option de contrôle de présence "
"avancée. "

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "ZIP"
msgstr "Code postal"

#. module: hr
#: code:addons/hr/models/mail_alias.py:0
#, python-format
msgid "addresses linked to registered employees"
msgstr "adresses liées aux employés enregistrés"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_department_form
msgid "department"
msgstr "département"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
msgid "e.g. John Doe"
msgstr "ex. John Doe"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_plan_view_form
msgid "e.g. Onboarding"
msgstr "ex. Embarquement"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "e.g. Sales Manager"
msgstr "e.g. Directeur Commercial"

#. module: hr
#: model:ir.model,name:hr.model_hr_plan
msgid "plan"
msgstr "plan"

#. module: hr
#: code:addons/hr/models/models.py:0
#, python-format
msgid "restricted to employees"
msgstr "restreint aux employés"
