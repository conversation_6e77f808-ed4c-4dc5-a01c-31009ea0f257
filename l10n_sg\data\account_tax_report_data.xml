<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="tax_report" model="account.tax.report">
        <field name="name">Tax Report</field>
        <field name="country_id" ref="base.sg"/>
    </record>

    <record id="account_tax_report_line_supplies" model="account.tax.report.line">
        <field name="name">Supplies</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
    </record>

    <record id="account_tax_report_line_std_rated_supplies" model="account.tax.report.line">
        <field name="name">Box 1 - Total value of standard-rated supplies</field>
        <field name="tag_name">Box 1</field>
        <field name="code">BOX1</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref='account_tax_report_line_supplies'/>
    </record>

    <record id="account_tax_report_line_zero_rated_supplies" model="account.tax.report.line">
        <field name="name">Box 2 - Total value of zero-rated supplies</field>
        <field name="tag_name">Box 2</field>
        <field name="code">BOX2</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref='account_tax_report_line_supplies'/>
    </record>

    <record id="account_tax_report_line_exempt_supplies" model="account.tax.report.line">
        <field name="name">Box 3 - Total value of exempt supplies</field>
        <field name="tag_name">Box 3</field>
        <field name="code">BOX3</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="3"/>
        <field name="parent_id" ref='account_tax_report_line_supplies'/>
    </record>

    <record id="account_tax_report_line_total_supplies" model="account.tax.report.line">
        <field name="name">Box 4 - Total value of (1) + (2) + (3)</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="4"/>
        <field name="formula">BOX1 + BOX2 + BOX3</field>
        <field name="parent_id" ref='account_tax_report_line_supplies'/>
    </record>

    <record id="account_tax_report_line_purchases" model="account.tax.report.line">
        <field name="name">Purchases</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
    </record>

    <record id="account_tax_report_line_total_taxable_purchases" model="account.tax.report.line">
        <field name="name">Box 5 - Total value of taxable purchases</field>
        <field name="tag_name">Box 5</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref='account_tax_report_line_purchases'/>
    </record>

    <record id="account_tax_report_line_taxes" model="account.tax.report.line">
        <field name="name">Taxes</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="3"/>
    </record>

    <record id="account_tax_report_line_output_tax_due" model="account.tax.report.line">
        <field name="name">Box 6 - Output tax due</field>
        <field name="tag_name">Box 6</field>
        <field name="code">BOX6</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref='account_tax_report_line_taxes'/>
    </record>

    <record id="account_tax_report_line_inp_tax_refund_claim" model="account.tax.report.line">
        <field name="name">Box 7 - Less : Input tax and refunds claimed</field>
        <field name="tag_name">Box 7</field>
        <field name="code">BOX7</field>
        <field name="parent_id" ref='account_tax_report_line_taxes'/>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
    </record>

    <record id="account_tax_report_line_total_gst_paid_iras" model="account.tax.report.line">
        <field name="name">Box 8 - Equals : Net GST to be paid to IRAS</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="3"/>
        <field name="formula">BOX6 - BOX7</field>
        <field name="parent_id" ref='account_tax_report_line_taxes'/>
    </record>

    <record id="account_tax_report_line_applicable" model="account.tax.report.line">
        <field name="name">Applicable to Taxable Persons under Major Exported Scheme / Approved 3rd Party Logistics Company / Other Approved Schemes Only</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="4"/>
    </record>

    <record id="account_tax_report_line_applicable_goods_imported_value" model="account.tax.report.line">
        <field name="name">Box 9 - Total value of goods imported under this scheme</field>
        <field name="tag_name">Box 9</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="account_tax_report_line_applicable"/>
    </record>

    <record id="account_tax_report_line_revenues" model="account.tax.report.line">
        <field name="name">Revenue</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="6"/>
    </record>

    <record id="account_tax_report_line_revenues_accounting_period" model="account.tax.report.line">
        <field name="name">Box 13 - Revenue for the accounting period</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="formula">net_profit</field>
        <field name="parent_id" ref="account_tax_report_line_revenues"/>
    </record>
</odoo>
