<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="partner_ri" model="res.partner">
        <field name='name'>(AR) Responsable Inscripto</field>
        <field name='l10n_ar_afip_responsibility_type_id' ref="res_IVARI"/>
        <field name='l10n_latam_identification_type_id' ref="l10n_ar.it_cuit"/>
        <field name='vat'>30111111118</field>
        <field name="street">Calle Falsa 123</field>
        <field name="city">Rosario</field>
        <field name="country_id" ref="base.ar"/>
        <field name="state_id" ref="base.state_ar_s"/>
        <field name="zip">2000</field>
        <field name="phone">****** 123 8069</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.example.com</field>
    </record>

    <record id="company_ri" model="res.company">
        <field name='parent_id' ref='base.main_company'/>
        <field name='currency_id' ref='base.ARS'/>
        <field name='partner_id' ref='partner_ri'/>
        <field name='name'>(AR) Responsable Inscripto</field>
        <field name="l10n_ar_afip_start_date" eval="time.strftime('%Y-01-01')"/>
        <field name='l10n_ar_gross_income_type'>local</field>
        <field name='l10n_ar_gross_income_number'>901-********</field>
    </record>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_ar.company_ri'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_ar.l10nar_ri_chart_template')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_ar.company_ri')"/>
    </function>

    <record id="bank_account_ri" model="res.partner.bank">
        <field name="acc_number">7982898111100056688080</field>
        <field name="partner_id" ref="l10n_ar.partner_ri"/>
        <field name="company_id" ref="l10n_ar.company_ri"/>
    </record>

    <data noupdate="1">

        <record id="sale_expo_journal_ri" model="account.journal">
            <field name='name'>Expo Sales Journal</field>
            <field name='company_id' ref="l10n_ar.company_ri"/>
            <field name='type'>sale</field>
            <field name='code'>S0002</field>
            <field name='l10n_latam_use_documents' eval="True"/>
            <field name='l10n_ar_afip_pos_number'>2</field>
            <field name='l10n_ar_afip_pos_partner_id' ref="l10n_ar.partner_ri"/>
            <field name='l10n_ar_afip_pos_system'>FEERCEL</field>
            <field name='refund_sequence' eval="False"/>
        </record>

    </data>

</odoo>
