<?xml version="1.0"?>
<odoo>
    <!-- Actions -->
    <record id="gift_card_action" model="ir.actions.act_window">
        <field name="name">Gift Cards</field>
        <field name="res_model">gift.card</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_valid': True}</field>
    </record>

    <!-- view mode -->
    <record id="gift_card_view_tree" model="ir.ui.view">
        <field name="name">gift.card.tree</field>
        <field name="model">gift.card</field>
        <field name="arch" type="xml">
            <tree string="Gift Card">
                <field name="code"/>
                <field name="balance"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <record id="gift_card_view_form" model="ir.ui.view">
        <field name="name">gift.card.form</field>
        <field name="model">gift.card</field>
        <field name="arch" type="xml">
            <form>
                <header>
                     <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="create_date"/>
                            <field name="expired_date"/>
                            <field name="code"/>
                        </group>
                        <group name="gift_card">
                            <field name="currency_id" attrs="{'invisible': True}"/>
                            <field name="initial_amount"/>
                            <field name="balance"/>
                            <field name="partner_id"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Searching -->
    <record id="gift_card_view_search" model="ir.ui.view">
        <field name="name">gift.card.search</field>
        <field name="model">gift.card</field>
        <field name="arch" type="xml">
            <search string="Gift Card">
                <field name="code"/>
                <filter name="valid" string="Valid" domain="[('state', '=', 'valid')]" />
            </search>
        </field>
    </record>
</odoo>
