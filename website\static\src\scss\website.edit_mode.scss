$-editor-messages-margin-x: 2%;
%o-editor-messages {
    width: 100% - $-editor-messages-margin-x * 2; // Need to be forced here to avoid flickering
    margin: 20px $-editor-messages-margin-x;
    border: 2px dashed #999999;
    padding: 12px 0px;
    text-align: center;
    color: #999999;

    &:before {
        content: attr(data-editor-message);
        display: block;
        font-size: 20px;
        line-height: 50px; // Useful for the "wizz" animation on snippet click to be more visible
    }
    &:after {
        content: attr(data-editor-sub-message);
        display: block;
    }
}
.o_we_snippet_area_animation {
    animation-delay: 999ms; // Disable it but allow to inherit the animation

    &::before {
        animation: inherit;
        animation-delay: 0ms;
    }
}

.oe_structure_not_nearest .oe_drop_zone {
    &:before {
        opacity: 0.5;
        line-height: 35px !important;
    }
}

.o_editable {
    &:not(:empty), &[data-oe-type] {
        &:not([data-oe-model="ir.ui.view"]):not([data-oe-type="html"]):not(.o_editable_no_shadow):not([data-oe-type="image"]):hover,
        &.o_editable_date_field_linked {
            box-shadow: $o-brand-odoo 0 0 5px 2px inset;
        }
        &[data-oe-type="image"]:not(.o_editable_no_shadow):hover {
            position: relative;

            &:after {
                content: "";
                pointer-events: none;
                @include o-position-absolute(0, 0, 0, 0);
                z-index: 1;
                box-shadow: $o-brand-odoo 0 0 5px 2px inset;
            }
        }
    }
    &:focus, &[data-oe-type] {
        min-height: 0.8em;
        min-width: 8px;

        // TODO this feature just needs to be reviewed to not have to make
        // exceptions such as this
        &#o_footer_scrolltop_wrapper {
            min-height: 0;
            min-width: 0;
        }
    }
    &.o_is_inline_editable {
        display: inline-block;
    }
    .btn, &.btn {
        -webkit-user-select: auto;
        -moz-user-select: auto;
        -ms-user-select: auto;
        user-select: auto;
        cursor: text!important;
    }
    /* Summernote not Support for placeholder text https://github.com/summernote/summernote/issues/581 */
    &[placeholder]:empty:not(:focus):before {
        content: attr(placeholder);
        opacity: 0.3;
        pointer-events: none;
    }

    &.oe_structure.oe_empty, &[data-oe-type=html], .oe_structure.oe_empty {
        &#wrap:empty, &#wrap > .oe_drop_zone.oe_insert:not(.oe_vertical):only-child {
            @extend %o-editor-messages;
            padding: 112px 0px;
        }
        > .oe_drop_zone.oe_insert:not(.oe_vertical):only-child {
            @extend %o-editor-messages;
            height: auto;
            color: $o-brand-odoo;
        }
        > p:empty:only-child {
            color: #aaa;
        }
    }
}
.editor_enable [data-oe-readonly]:hover {
    cursor: default;
}
.oe_structure_solo > .oe_drop_zone {
    // TODO implement something more robust. This is currently for our only
    // use case of oe_structure_solo: the footer. The dropzone in there need to
    // be 1px lower that the end-of-page dropzone to distinguish them. The
    // usability has to be reviewed anyway.
    transform: translateY(10px); // For some reason "1px" is not enough...
}

/* Prevent the text contents of draggable elements from being selectable. */
[draggable] {
    user-select: none;
}

.oe_editable:focus,
.css_editable_hidden,
.editor_enable .css_editable_mode_hidden {
    outline: none!important;
}

.editor_enable .css_non_editable_mode_hidden,
.o_editable .media_iframe_video .css_editable_mode_display {
    display: block!important;
}

// TODO: in master check if the class / rule is relevant at all
.editor_enable [data-oe-type=html].oe_no_empty:empty {
    height: 16px!important;
}

// EDITOR BAR
table.editorbar-panel {
    cursor: pointer;
    width: 100%;
    td { border: 1px solid #aaa}
    td.selected { background-color: #b1c9d9}
}
.link-style {
    .dropdown > .btn {
        min-width: 160px;
    }
    .link-style {
        display: none;
    }
    li {
        text-align: center;
        label {
            width: 100px;
            margin: 0 5px;
        }
    }
    .col-md-2 > * {
        line-height: 2em;
    }
}

// fontawesome
#wrap.o_editable .fa {
    cursor: pointer;
}

// parallax dropzones are in conflict with outside of parallax dropzones
.parallax .oe_structure > .oe_drop_zone {
    &:first-child {
        top: 16px;
    }
    &:last-child {
        bottom: 16px;
    }
}

.editor_enable .o_add_language {
    display: none !important;
}

// Facebook Page
.editor_enable .o_facebook_page:not(.o_facebook_preview) {
    iframe {
        pointer-events: none;
    }
    .o_facebook_alert .o_add_facebook_page {
        cursor: pointer;
    }
}

body.editor_enable.editor_has_snippets {
    padding-top: 0 !important;

    .s_popup .modal {
        // s_popup in edit mode
        background-color: transparent;

        &.fade .modal-dialog {
            transform: none;
        }
    }

    #oe_main_menu_navbar + #wrapwrap .o_header_affixed {
        top: 0;
    }
}

.editor_has_snippets {
    .o_header_affixed {
        right: $o-we-sidebar-width !important;
    }
}

// s_countdown preview classes
body.editor_enable {
    .s_countdown {
        .s_countdown_enable_preview {
            display: initial !important;
        }
        .s_countdown_none {
            display: none !important;
        }
    }
}

//s_dynamic_snippet
body.editor_enable {
    .s_dynamic {
        [data-url] {
            cursor: inherit;
        }
    }
}

// Website Animate
.editor_enable.o_animated_text_highlighted {
    .o_animated_text {
        position: relative;

        &:after {
            content: "";
            pointer-events: none;
            @include o-position-absolute(0, 0, 0, 0);
            z-index: 1;
            // This border is useful when there is a green background behind
            // the text.
            border: 1px dotted white;
            background-color: rgba(greenyellow, .2);
        }
    }
}

// Inputs in editable zones should not be possible to interact with.
// TODO as this was done as a fix in 13.0, this only targets the inputs of
// specific snippets and even targets snippets of other apps (which do not
// declare files for edit mode only). In master it should be refactored to
// target all inputs and/or target specific snippets in their own app.
.editor_enable {
    .s_website_form, .s_searchbar_input, .js_subscribe, .s_group, .s_donation_form {
        input:not(.o_translatable_attribute) {
            pointer-events: none;
        }
    }
    .s_website_form {
        [data-toggle="datetimepicker"], textarea:not(.o_translatable_attribute):not(.o_translatable_text) {
            pointer-events: none;
        }
    }
}
