# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_coupon
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# Vo Thanh Thuy, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-08 06:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: Vo Thanh Thuy, 2021\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_coupon
#: model:coupon.program,name:pos_coupon.15_pc_on_next_order
msgid "15% on next order"
msgstr "15% cho đơn hàng tiếp theo"

#. module: pos_coupon
#: model:mail.template,body_html:pos_coupon.mail_coupon_template
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"    <tr><td valign=\"top\" style=\"text-align: center; font-size: 14px;\">\n"
"        <t t-if=\"object.partner_id.name\">\n"
"            Congratulations <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br/>\n"
"        </t>\n"
"\n"
"        Here is your reward from <t t-out=\"object.program_id.company_id.name or ''\">YourCompany</t>.<br/>\n"
"\n"
"        <t t-if=\"object.program_id.reward_type == 'discount'\">\n"
"            <t t-if=\"object.program_id.discount_type == 'fixed_amount'\">\n"
"                <span style=\"font-size: 50px; color: #875A7B; font-weight: bold;\" t-out=\"'%s' % format_amount(object.program_id.discount_fixed_amount, object.program_id.currency_id) or ''\">$ 10.0</span><br/>\n"
"                <strong style=\"font-size: 24px;\">off on your next order</strong><br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                <span style=\"font-size: 50px; color: #875A7B; font-weight: bold;\">\n"
"                    <t t-out=\"object.program_id.discount_percentage or ''\">10.0</t> %\n"
"                </span>\n"
"                <t t-if=\"object.program_id.discount_apply_on == 'specific_products'\">\n"
"                    <br/>\n"
"                    <t t-if=\"len(object.program_id.discount_specific_product_ids) != 1\">\n"
"                        <t t-set=\"display_specific_products\" t-value=\"True\"/>\n"
"                        <strong style=\"font-size: 24px;\">\n"
"                            on some products*\n"
"                        </strong>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        <strong style=\"font-size: 24px;\" t-out=\"'on %s' % object.program_id.discount_specific_product_ids.name or ''\">Chair floor protection</strong>\n"
"                    </t>\n"
"                </t>\n"
"                <t t-elif=\"object.program_id.discount_apply_on == 'cheapest_product'\">\n"
"                    <br/><strong style=\"font-size: 24px;\">\n"
"                        off on the cheapest product\n"
"                    </strong>\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    <br/><strong style=\"font-size: 24px;\">\n"
"                        off on your next order\n"
"                    </strong>\n"
"                </t>\n"
"                <br/>\n"
"            </t>\n"
"        </t>\n"
"        <t t-elif=\"object.program_id.reward_type == 'product'\">\n"
"            <span style=\"font-size: 36px; color: #875A7B; font-weight: bold;\" t-out=\"'get %s free %s' % (object.program_id.reward_product_quantity, object.program_id.reward_product_id.name) or ''\">Chair floor protection</span><br/>\n"
"            <strong style=\"font-size: 24px;\">on your next order</strong><br/>\n"
"        </t>\n"
"        <t t-elif=\"object.program_id.reward_type == 'free_shipping'\">\n"
"            <span style=\"font-size: 36px; color: #875A7B; font-weight: bold;\">\n"
"                get free shipping\n"
"            </span><br/>\n"
"            <strong style=\"font-size: 24px;\">on your next order</strong><br/>\n"
"        </t>\n"
"    </td></tr>\n"
"    <tr style=\"margin-top: 16px\"><td valign=\"top\" style=\"text-align: center; font-size: 14px;\">\n"
"        Use this promo code\n"
"        <t t-if=\"object.expiration_date\">\n"
"            before <t t-out=\"object.expiration_date or ''\">2021-06-05</t>\n"
"        </t>\n"
"        <p style=\"margin-top: 16px;\">\n"
"            <strong style=\"padding: 16px 8px 16px 8px; border-radius: 3px; background-color: #F1F1F1;\" t-out=\"object.code or ''\">13996301932606901095</strong>\n"
"        </p>\n"
"        <t t-if=\"object.program_id.rule_min_quantity not in [0, 1]\">\n"
"            <span style=\"font-size: 14px;\">\n"
"                Minimum purchase of <t t-out=\"object.program_id.rule_min_quantity or ''\">10</t> products\n"
"            </span><br/>\n"
"        </t>\n"
"        <t t-if=\"object.program_id.rule_minimum_amount != 0.00\">\n"
"            <span style=\"font-size: 14px;\">\n"
"                Valid for purchase above <t t-out=\"object.program_id.company_id.currency_id.symbol or ''\">$</t><t t-out=\"'%0.2f' % float(object.program_id.rule_minimum_amount) or ''\">10.00</t>\n"
"            </span><br/>\n"
"        </t>\n"
"        <t t-if=\"display_specific_products\">\n"
"            <span>\n"
"                *Valid for following products: <t t-out=\"', '.join(object.program_id.discount_specific_product_ids.mapped('name')) or ''\">Office Chair Black</t>\n"
"            </span><br/>\n"
"        </t>\n"
"        <br/>\n"
"        Thank you,\n"
"        <t t-if=\"object.source_pos_order_id.user_id.signature\">\n"
"            <br/>\n"
"            <t t-out=\"object.source_pos_order_id.user_id.signature or ''\">--<br/>Mitchell Admin</t>\n"
"        </t>\n"
"    </td></tr>\n"
"</tbody></table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"    <tr><td valign=\"top\" style=\"text-align: center; font-size: 14px;\">\n"
"        <t t-if=\"object.partner_id.name\">\n"
"            Chúc mừng <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br/>\n"
"        </t>\n"
"\n"
"        Đây là phần thưởng cho bạn từ <t t-out=\"object.program_id.company_id.name or ''\">YourCompany</t>.<br/>\n"
"\n"
"        <t t-if=\"object.program_id.reward_type == 'discount'\">\n"
"            <t t-if=\"object.program_id.discount_type == 'fixed_amount'\">\n"
"                <span style=\"font-size: 50px; color: #875A7B; font-weight: bold;\" t-out=\"'%s' % format_amount(object.program_id.discount_fixed_amount, object.program_id.currency_id) or ''\">giảm $ 10.0</span><br/>\n"
"                <strong style=\"font-size: 24px;\">cho đơn hàng tiếp theo</strong><br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                <span style=\"font-size: 50px; color: #875A7B; font-weight: bold;\">\n"
"                    <t t-out=\"object.program_id.discount_percentage or ''\">10.0</t> %\n"
"                </span>\n"
"                <t t-if=\"object.program_id.discount_apply_on == 'specific_products'\">\n"
"                    <br/>\n"
"                    <t t-if=\"len(object.program_id.discount_specific_product_ids) != 1\">\n"
"                        <t t-set=\"display_specific_products\" t-value=\"True\"/>\n"
"                        <strong style=\"font-size: 24px;\">\n"
"                            chiết khấu cho một số sản phẩm*\n"
"                        </strong>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        <strong style=\"font-size: 24px;\" t-out=\"'on %s' % object.program_id.discount_specific_product_ids.name or ''\">Miếng đệm chân ghế</strong>\n"
"                    </t>\n"
"                </t>\n"
"                <t t-elif=\"object.program_id.discount_apply_on == 'cheapest_product'\">\n"
"                    <br/><strong style=\"font-size: 24px;\">\n"
"                        giảm giá cho sản phẩm rẻ nhất\n"
"                    </strong>\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    <br/><strong style=\"font-size: 24px;\">\n"
"                        giảm giá cho đơn hàng tiếp theo\n"
"                    </strong>\n"
"                </t>\n"
"                <br/>\n"
"            </t>\n"
"        </t>\n"
"        <t t-elif=\"object.program_id.reward_type == 'product'\">\n"
"            <span style=\"font-size: 36px; color: #875A7B; font-weight: bold;\" t-out=\"'get %s free %s' % (object.program_id.reward_product_quantity, object.program_id.reward_product_id.name) or ''\">Miếng đệm chân ghế</span><br/>\n"
"            <strong style=\"font-size: 24px;\">cho đơn hàng tiếp theo</strong><br/>\n"
"        </t>\n"
"        <t t-elif=\"object.program_id.reward_type == 'free_shipping'\">\n"
"            <span style=\"font-size: 36px; color: #875A7B; font-weight: bold;\">\n"
"                được vận chuyển miễn phí\n"
"            </span><br/>\n"
"            <strong style=\"font-size: 24px;\">cho đơn hàng tiếp theo</strong><br/>\n"
"        </t>\n"
"    </td></tr>\n"
"    <tr style=\"margin-top: 16px\"><td valign=\"top\" style=\"text-align: center; font-size: 14px;\">\n"
"        Sử dụng mã khuyến mãi này\n"
"        <t t-if=\"object.expiration_date\">\n"
"            trước <t t-out=\"object.expiration_date or ''\">2021-06-05</t>\n"
"        </t>\n"
"        <p style=\"margin-top: 16px;\">\n"
"            <strong style=\"padding: 16px 8px 16px 8px; border-radius: 3px; background-color: #F1F1F1;\" t-out=\"object.code or ''\">13996301932606901095</strong>\n"
"        </p>\n"
"        <t t-if=\"object.program_id.rule_min_quantity not in [0, 1]\">\n"
"            <span style=\"font-size: 14px;\">\n"
"                Mua tối thiểu <t t-out=\"object.program_id.rule_min_quantity or ''\">10</t> sản phẩm\n"
"            </span><br/>\n"
"        </t>\n"
"        <t t-if=\"object.program_id.rule_minimum_amount != 0.00\">\n"
"            <span style=\"font-size: 14px;\">\n"
"                Áp dụng cho đơn hàng trên <t t-out=\"object.program_id.company_id.currency_id.symbol or ''\">$</t><t t-out=\"'%0.2f' % float(object.program_id.rule_minimum_amount) or ''\">10.00</t>\n"
"            </span><br/>\n"
"        </t>\n"
"        <t t-if=\"display_specific_products\">\n"
"            <span>\n"
"                *Áp dụng cho các sản phẩm sau: <t t-out=\"', '.join(object.program_id.discount_specific_product_ids.mapped('name')) or ''\">Ghế văn phòng màu đen</t>\n"
"            </span><br/>\n"
"        </t>\n"
"        <br/>\n"
"        Chân thành cảm ơn,\n"
"        <t t-if=\"object.source_pos_order_id.user_id.signature\">\n"
"            <br/>\n"
"            <t t-out=\"object.source_pos_order_id.user_id.signature or ''\">--<br/>Mitchell Admin</t>\n"
"        </t>\n"
"    </td></tr>\n"
"</tbody></table>\n"
"            "

#. module: pos_coupon
#: code:addons/pos_coupon/models/coupon.py:0
#, python-format
msgid ""
"A coupon from the same program has already been reserved for this order."
msgstr ""
"Phiếu giảm giá từ cùng một chương trình đã được đặt trước cho đơn hàng này. "

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_coupon_program__promo_barcode
msgid ""
"A technical field used as an alternative to the promo_code. This is "
"automatically generated when promo_code is changed."
msgstr ""
"Trường kỹ thuật được dùng để thay thế cho promo_code. Được tạo tự động khi "
"promo_code thay đổi. "

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/xml/ActivePrograms.xml:0
#, python-format
msgid "Active Programs"
msgstr "Chương trình đang hoạt động"

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_pos_order__applied_program_ids
msgid "Applied Programs"
msgstr "Chương trình được áp dụng"

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_coupon_coupon__pos_order_id
msgid "Applied on PoS Order"
msgstr "Được áp dụng cho đơn hàng POS"

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/js/ProductScreen.js:0
#, python-format
msgid "Are you sure you want to deactivate %s in this order?"
msgstr "Bạn có chắc chắn muốn hủy kích hoạt %s trong đơn hàng này? "

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/xml/OrderReceipt.xml:0
#: model:ir.model.fields,field_description:pos_coupon.field_coupon_program__promo_barcode
#, python-format
msgid "Barcode"
msgstr "Mã vạch"

#. module: pos_coupon
#: model:ir.model,name:pos_coupon.model_barcode_rule
msgid "Barcode Rule"
msgstr "Quy tắc mã vạch"

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_pos_order__used_coupon_ids
msgid "Consumed Coupons"
msgstr "Phiếu giảm giá đã dùng"

#. module: pos_coupon
#: model:ir.model,name:pos_coupon.model_coupon_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_pos_order_line__coupon_id
#: model:ir.model.fields.selection,name:pos_coupon.selection__barcode_rule__type__coupon
msgid "Coupon"
msgstr "Phiếu giảm giá"

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "Coupon Codes"
msgstr "Mã phiếu giảm giá"

#. module: pos_coupon
#: model:ir.model,name:pos_coupon.model_coupon_program
msgid "Coupon Program"
msgstr "Chương trình phiếu khuyến mãi"

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_pos_config__coupon_program_ids
#: model:ir.ui.menu,name:pos_coupon.menu_coupon_type_config
#: model_terms:ir.ui.view,arch_db:pos_coupon.res_config_view_form_inherit_pos_coupon
msgid "Coupon Programs"
msgstr "Chương trình phiếu giảm giá"

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_pos_order_line__coupon_id
msgid "Coupon that generated this reward."
msgstr "Phiếu giảm giá đã tạo phần thưởng này. "

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_pos_config__use_coupon_programs
msgid "Coupons & Promotions"
msgstr "Phiếu giảm giá & Khuyến mãi"

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_pos_config__program_ids
msgid "Coupons and Promotions"
msgstr "Phiếu giảm giá và khuyến mãi"

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/js/ProductScreen.js:0
#, python-format
msgid "Deactivating program"
msgstr "Hủy kích hoạt chương trình"

#. module: pos_coupon
#: model_terms:ir.ui.view,arch_db:pos_coupon.pos_coupon_pos_config_view_form
msgid "Define the coupon and promotion programs you can use in this PoS."
msgstr ""
"Xác định chương trình giảm giá và khuyến mãi bạn có thể sử dụng trong POS "
"này. "

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/xml/ControlButtons/PromoCodeButton.xml:0
#, python-format
msgid "Enter Code"
msgstr "Nhập mã"

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/js/ControlButtons/PromoCodeButton.js:0
#, python-format
msgid "Enter Promotion or Coupon Code"
msgstr "Nhập mã phiếu giảm giá hoặc mã khuyến mãi"

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_pos_order_line__is_program_reward
msgid ""
"Flag indicating that this order line is a result of coupon/promo program."
msgstr ""
"Cờ chỉ rằng dòng đơn hàng này là kết quả của chương trình giảm giá/khuyến "
"mãi. "

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_pos_order__generated_coupon_ids
msgid "Generated Coupons"
msgstr "Phiếu giảm giá được tạo"

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_pos_order_line__is_program_reward
msgid "Is reward line"
msgstr "Là dòng phần thưởng"

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/js/ProductScreen.js:0
#, python-format
msgid "No"
msgstr "Không"

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_coupon_program__pos_order_line_ids
msgid "Order lines where this program is applied."
msgstr "Dòng đơn hàng khi chương trình này được áp dụng. "

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_coupon_program__pos_order_count
msgid "PoS Order Count"
msgstr "Số đơn hàng POS"

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_coupon_program__pos_order_line_ids
msgid "PoS Order Lines"
msgstr "Dòng đơn hàng POS"

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_coupon_coupon__source_pos_order_id
msgid "PoS Order Reference"
msgstr "Tham chiếu đơn hàng POS"

#. module: pos_coupon
#: code:addons/pos_coupon/models/coupon_program.py:0
#, python-format
msgid "PoS Orders"
msgstr "Các đơn hàng POS"

#. module: pos_coupon
#: model_terms:ir.ui.view,arch_db:pos_coupon.pos_coupon_program_view_coupon_program_form
#: model_terms:ir.ui.view,arch_db:pos_coupon.pos_coupon_program_view_promo_program_form
msgid "PoS Sales"
msgstr "Doanh số POS"

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_coupon_coupon__pos_order_id
msgid "PoS order where this coupon is consumed/booked."
msgstr "Đơn hàng POS mà phiếu giảm giá này được dùng/đặt trước. "

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_coupon_coupon__source_pos_order_id
msgid "PoS order where this coupon is generated."
msgstr "Đơn hàng POS mà phiếu giảm giá này được tạo. "

#. module: pos_coupon
#: model:ir.model,name:pos_coupon.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Cấu hình POS"

#. module: pos_coupon
#: model:ir.model,name:pos_coupon.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "Dòng đơn hàng POS"

#. module: pos_coupon
#: model:ir.model,name:pos_coupon.model_pos_order
msgid "Point of Sale Orders"
msgstr "Đơn hàng POS"

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_coupon_program__pos_config_ids
msgid "Point of Sales"
msgstr "POS"

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_coupon_program__pos_order_ids
msgid "Pos Order"
msgstr "Đơn hàng POS"

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_pos_order_line__program_id
msgid "Program"
msgstr "Chương trình"

#. module: pos_coupon
#: code:addons/pos_coupon/models/pos_config.py:0
#, python-format
msgid "Program: %(name)s (%(type)s), Reward Product: `%(reward_product)s`"
msgstr ""
"Chương trình: %(name)s (%(type)s), Sản phẩm tặng: `%(reward_product)s`"

#. module: pos_coupon
#: model_terms:ir.ui.view,arch_db:pos_coupon.pos_coupon_pos_config_view_form
msgid "Promotion & coupon programs to use."
msgstr "Chương trình khuyến mãi & giảm giá sẽ sử dụng. "

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_pos_config__promo_program_ids
#: model:ir.ui.menu,name:pos_coupon.menu_promotion_type_config
#: model_terms:ir.ui.view,arch_db:pos_coupon.res_config_view_form_inherit_pos_coupon
msgid "Promotion Programs"
msgstr "Chương trình khuyến mãi"

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_pos_order_line__program_id
msgid "Promotion/Coupon Program where this reward line is based."
msgstr "Chương trình khuyến mãi/giảm giá mà dòng phần thưởng này dựa vào. "

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/xml/ControlButtons/ResetProgramsButton.xml:0
#, python-format
msgid "Reset Programs"
msgstr "Đặt lại chương trình"

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_pos_order__applied_program_ids
msgid ""
"Technical field. This is set when the order is validated. We normally get "
"this value thru the `program_id` of the reward lines."
msgstr ""
"Trường kỹ thuật. Được đặt khi đơn hàng được xác nhận. Chúng tôi thường nhận "
"giá trị qua `program_id` của dòng phần thưởng. "

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_coupon_program__pos_order_ids
msgid "The PoS orders where this program is applied."
msgstr "Đơn hàng POS mà chương trình này được áp dụng. "

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_coupon_program__valid_partner_ids
msgid "These are the partners that can avail this program."
msgstr "Đây là các đối tác có thể thực hiện chương trình này. "

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_coupon_program__valid_product_ids
msgid "These are the products that are valid in this program."
msgstr "Đây là các sản phẩm hợp lệ cho chương trình này. "

#. module: pos_coupon
#: code:addons/pos_coupon/models/pos_config.py:0
#, python-format
msgid "This coupon is invalid (%s)."
msgstr "Phiếu giảm giá này không hợp lệ (%s)."

#. module: pos_coupon
#: code:addons/pos_coupon/models/pos_config.py:0
#, python-format
msgid ""
"To continue, make the following reward products to be available in Point of "
"Sale."
msgstr ""
"Để tiếp tục, hãy đặt các sản phẩm tặng ở trạng thái có sẵn trong POS. "

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_barcode_rule__type
msgid "Type"
msgstr "Loại"

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_pos_config__use_coupon_programs
msgid "Use coupon and promotion programs in this PoS configuration."
msgstr "Sử dụng phiếu giảm giá và khuyến mãi trong cấu hình POS này. "

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_coupon_program__valid_partner_ids
msgid "Valid Partner"
msgstr "Đối tác hợp lệ"

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_coupon_program__valid_product_ids
msgid "Valid Product"
msgstr "Sản phẩm hợp lệ"

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "Valid until:"
msgstr "Có giá trị đến: "

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/js/ProductScreen.js:0
#, python-format
msgid "Yes"
msgstr "Có"

#. module: pos_coupon
#: model:mail.template,report_name:pos_coupon.mail_coupon_template
msgid "Your Coupon Code"
msgstr "Mã phiếu giảm giá"

#. module: pos_coupon
#: model:mail.template,subject:pos_coupon.mail_coupon_template
msgid "Your reward coupon from {{ object.program_id.company_id.name }} "
msgstr ""
"Phiếu giảm giá tặng thưởng của bạn từ {{ object.program_id.company_id.name "
"}} "

#. module: pos_coupon
#: model:mail.template,name:pos_coupon.mail_coupon_template
msgid "[POS] Coupon: Send by Email"
msgstr "Phiếu giảm giá [POS]: Gửi qua email"

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "no expiration"
msgstr "không có hạn"
