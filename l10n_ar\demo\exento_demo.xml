<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="partner_exento" model="res.partner">
        <field name='name'>(AR) Exento</field>
        <field name='l10n_ar_afip_responsibility_type_id' ref="res_IVAE"/>
        <field name='l10n_latam_identification_type_id' ref="l10n_ar.it_cuit"/>
        <field name='vat'>30999999995</field>
        <field name="street">Soyexento 123</field>
        <field name="city">Rosario</field>
        <field name="country_id" ref="base.ar"/>
        <field name="state_id" ref="base.state_ar_s"/>
        <field name="zip">2000</field>
        <field name="phone">****** 123 8069</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.exento.com</field>
    </record>

    <record id="company_exento" model="res.company">
        <field name='parent_id' ref='base.main_company'/>
        <field name='currency_id' ref='base.ARS'/>
        <field name='partner_id' ref='partner_exento'/>
        <field name='name'>(AR) Exento</field>
        <field name='l10n_ar_gross_income_type'>exempt</field>
        <field name="l10n_ar_afip_start_date" eval="time.strftime('%Y-%m')+'-1'"/>
    </record>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_ar.company_exento'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_ar.l10nar_ex_chart_template')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_ar.company_exento')"/>
    </function>

    <data noupdate="1">

        <record id="sale_expo_journal_exento" model="account.journal">
            <field name='name'>Expo Sales Journal</field>
            <field name='company_id' ref="l10n_ar.company_exento"/>
            <field name='type'>sale</field>
            <field name='code'>S0002</field>
            <field name='l10n_latam_use_documents' eval="True"/>
            <field name='l10n_ar_afip_pos_number'>2</field>
            <field name='l10n_ar_afip_pos_partner_id' ref="l10n_ar.partner_exento"/>
            <field name='l10n_ar_afip_pos_system'>FEERCEL</field>
        </record>

    </data>

</odoo>
