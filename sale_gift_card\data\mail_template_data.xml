<?xml version="1.0" ?>
<odoo>
    <record id="mail_template_gift_card" model="mail.template">
        <field name="name">Gift Card: Send by Email</field>
        <field name="model_id" ref="model_gift_card"/>
        <field name="subject">Your Gift Card</field>
        <field name="partner_to">{{ object.partner_id.id or object.buy_line_id.order_id.partner_id.id }}</field>
        <field name="body_html" type="html">
            <div style="margin:0px; font-size:24px; font-family:arial, 'helvetica neue', helvetica, sans-serif; line-height:36px; color:#333333; text-align: center">
                Here is your gift card!
            </div>
            <div style="padding-top:20px; padding-bottom:20px">
                <img src="/gift_card/static/img/gift_card.png" style="display:block; border:0; outline:none; text-decoration:none; margin:auto;" width="300"/>
            </div>
            <div style="padding:0; margin:0px; padding-top:35px; padding-bottom:35px; text-align:center;">
                <h3 style="margin:0px; line-height:48px; font-family:arial, 'helvetica neue', helvetica, sans-serif; font-size:40px; font-style:normal; font-weight:normal; color:#333333; text-align:center">
                    <strong t-out="format_amount(object.initial_amount, object.currency_id) or ''">$ 150.00</strong></h3>
            </div>
            <div style="padding:0; margin:0px; padding-top:35px; padding-bottom:35px; background-color:#efefef; text-align:center;">
                <p style="margin:0px; font-size:14px;font-family:arial, 'helvetica neue', helvetica, sans-serif; line-height:21px; color:#333333">
                    <strong>Gift Card Code</strong>
                </p>
                <p style="margin:0px; font-size:25px;font-family:arial, 'helvetica neue', helvetica, sans-serif; line-height:38px; color:#A9A9A9" t-out="object.code or ''">4f10-15d6-41b7-b04c-7b3e</p>
            </div>
            <div style="padding:0; margin:0px; padding-top:10px; padding-bottom:10px; text-align:center;">
                <h3 style="margin:0px; line-height:17px; font-family:arial, 'helvetica neue', helvetica, sans-serif; font-size:14px; font-style:normal; font-weight:normal; color:#A9A9A9; text-align:center">Card expires <t t-out="format_date(object.expired_date) or ''">05/05/2021</t></h3>
            </div>
            <div style="padding:20px; margin:0px; text-align:center;">
                <span style="background-color:#999999; display:inline-block; width:auto; border-radius:5px;">
                    <a t-attf-href="{{ object.buy_line_id.order_id.get_base_url() }}/shop" target="_blank" style="text-decoration:none; font-family:arial, 'helvetica neue', helvetica, sans-serif; font-size:22px; color:#FFFFFF; border-style:solid; border-color:#999999; border-width:20px 30px; display:inline-block; background-color:#999999; border-radius:5px; font-weight:bold; font-style:normal; line-height:26px; width:auto; text-align:center">Use it right now!</a>
                </span>
            </div>
        </field>
        <field name="auto_delete" eval="True"/>
    </record>
</odoo>
