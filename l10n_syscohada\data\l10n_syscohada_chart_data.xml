<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record model="account.account.type" id="account_type_special">
        <field name="name">Compt<PERSON> spéciaux</field>
        <field name="internal_group">off_balance</field>
    </record>

    <!-- Chart Template -->
    <record id="syscohada_chart_template" model="account.chart.template">
        <field name="name">SYSCOHADA - Plan de compte</field>
        <field name="bank_account_code_prefix">52</field>
        <field name="cash_account_code_prefix">51</field>
        <field name="transfer_account_code_prefix">588</field>
        <field name="code_digits">6</field>
        <field name="currency_id" ref="base.XOF"/>
    </record>

</odoo>
