# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_sms
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:54+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: 山西清水欧度(QQ:54773801) <<EMAIL>>, 2022\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: event_sms
#: model:ir.model,name:event_sms.model_event_mail
msgid "Event Automated Mailing"
msgstr "自动发邮件"

#. module: event_sms
#: model:ir.model,name:event_sms.model_event_registration
msgid "Event Registration"
msgstr "活动登记"

#. module: event_sms
#: model:sms.template,name:event_sms.sms_template_data_event_registration
msgid "Event: Registration"
msgstr "活动：注册"

#. module: event_sms
#: model:sms.template,name:event_sms.sms_template_data_event_reminder
msgid "Event: Reminder"
msgstr "活动：提醒"

#. module: event_sms
#: model:ir.model,name:event_sms.model_event_type_mail
msgid "Mail Scheduling on Event Category"
msgstr "基于活动分类的邮件调度"

#. module: event_sms
#: model:ir.model,name:event_sms.model_event_mail_registration
msgid "Registration Mail Scheduler"
msgstr "登记邮件调度"

#. module: event_sms
#: model:ir.model.fields.selection,name:event_sms.selection__event_mail__notification_type__sms
#: model:ir.model.fields.selection,name:event_sms.selection__event_type_mail__notification_type__sms
msgid "SMS"
msgstr "短信"

#. module: event_sms
#: model:ir.model,name:event_sms.model_sms_template
msgid "SMS Templates"
msgstr "短信模板"

#. module: event_sms
#: model:ir.model.fields,field_description:event_sms.field_event_mail__notification_type
#: model:ir.model.fields,field_description:event_sms.field_event_type_mail__notification_type
msgid "Send"
msgstr "发送"

#. module: event_sms
#: model:sms.template,body:event_sms.sms_template_data_event_reminder
msgid ""
"{{ object.event_id.organizer_id.name or object.event_id.company_id.name or "
"user.env.company.name }}: We are excited to remind you that the {{ "
"object.event_id.name }} event is starting {{ object.get_date_range_str() }}."
" We confirm your registration and hope to meet you there."
msgstr ""
"{{ object.event_id.organizer_id.name 或 object.event_id.company_id.name 或 "
"user.env.company.name }}。我们很高兴地提醒您，{{ object.event_id.name }}活动即将开始{{ "
"object.get_date_range_str() }}。我们确认您的注册，并希望能在那里见到您。"

#. module: event_sms
#: model:sms.template,body:event_sms.sms_template_data_event_registration
msgid ""
"{{ object.event_id.organizer_id.name or object.event_id.company_id.name or "
"user.env.company.name }}: We are happy to confirm your registration for the "
"{{ object.event_id.name }} event."
msgstr ""
"{{ object.event_id.organizer_id.name 或 object.event_id.company_id.name 或 "
"user.env.company.name }}。我们很高兴确认您对{{ object.event_id.name }}活动的注册。"
