<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <menuitem id="account_reports_pt_statements_menu" name="Portugal" parent="account.menu_finance_reports" sequence="0" groups="account.group_account_readonly" />

    <data>

    <record id="pt_chart_template" model="account.chart.template">
        <field name="name">Portugal - Template do Plano de Contas SNC</field>
        <field name="cash_account_code_prefix">11</field>
        <field name="bank_account_code_prefix">12</field>
        <field name="transfer_account_code_prefix">15</field>
        <field name="currency_id" ref="base.EUR"/>
        <field name="country_id" ref="base.pt"/>
    </record>

    <record id="chart_13" model="account.account.template">
      <field name="code">13</field>
      <field name="name">Outros depósitos bancários</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_1411" model="account.account.template">
      <field name="code">1411</field>
      <field name="name">Potencialmente favoráveis</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_1412" model="account.account.template">
      <field name="code">1412</field>
      <field name="name">Potencialmente desfavoráveis</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_1421" model="account.account.template">
      <field name="code">1421</field>
      <field name="name">Activos financeiros</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_1422" model="account.account.template">
      <field name="code">1422</field>
      <field name="name">Passivos financeiros</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_1431" model="account.account.template">
      <field name="code">1431</field>
      <field name="name">Outros activos financeiros</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_1432" model="account.account.template">
      <field name="code">1432</field>
      <field name="name">Outros passivos financeiros</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2111" model="account.account.template">
      <field name="code">2111</field>
      <field name="name">Clientes gerais</field>
      <field name="reconcile" eval="True"/>
      <field name="user_type_id" ref="account.data_account_type_receivable" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2112" model="account.account.template">
      <field name="code">2112</field>
      <field name="name">Clientes empresa mãe</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2113" model="account.account.template">
      <field name="code">2113</field>
      <field name="name">Clientes empresas subsidiárias</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2114" model="account.account.template">
      <field name="code">2114</field>
      <field name="name">Clientes empresas associadas</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2115" model="account.account.template">
      <field name="code">2115</field>
      <field name="name">Clientes empreendimentos conjuntos</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2116" model="account.account.template">
      <field name="code">2116</field>
      <field name="name">Clientes outras partes relacionadas</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2117" model="account.account.template">
      <field name="code">2117</field>
      <field name="name">Clientes gerais (PoS)</field>
      <field name="reconcile" eval="True"/>
      <field name="user_type_id" ref="account.data_account_type_receivable" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2121" model="account.account.template">
      <field name="code">2121</field>
      <field name="name">Clientes gerais</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2122" model="account.account.template">
      <field name="code">2122</field>
      <field name="name">Clientes empresa mãe</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2123" model="account.account.template">
      <field name="code">2123</field>
      <field name="name">Clientes empresas subsidiárias</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2124" model="account.account.template">
      <field name="code">2124</field>
      <field name="name">Clientes empresas associadas</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2125" model="account.account.template">
      <field name="code">2125</field>
      <field name="name">Clientes empreendimentos conjuntos</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2126" model="account.account.template">
      <field name="code">2126</field>
      <field name="name">Clientes outras partes relacionadas</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_218" model="account.account.template">
      <field name="code">218</field>
      <field name="name">Adiantamentos de clientes</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_219" model="account.account.template">
      <field name="code">219</field>
      <field name="name">Perdas por imparidade acumuladas</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2211" model="account.account.template">
      <field name="code">2211</field>
      <field name="name">Fornecedores gerais</field>
      <field name="reconcile" eval="True"/>
      <field name="user_type_id" ref="account.data_account_type_payable" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2212" model="account.account.template">
      <field name="code">2212</field>
      <field name="name">Fornecedores empresa mãe</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2213" model="account.account.template">
      <field name="code">2213</field>
      <field name="name">Fornecedores empresas subsidiárias</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2214" model="account.account.template">
      <field name="code">2214</field>
      <field name="name">Fornecedores empresas associadas</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2215" model="account.account.template">
      <field name="code">2215</field>
      <field name="name">Fornecedores empreendimentos conjuntos</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2216" model="account.account.template">
      <field name="code">2216</field>
      <field name="name">Fornecedores outras partes relacionadas</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2221" model="account.account.template">
      <field name="code">2221</field>
      <field name="name">Fornecedores gerais</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2222" model="account.account.template">
      <field name="code">2222</field>
      <field name="name">Fornecedores empresa mãe</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2223" model="account.account.template">
      <field name="code">2223</field>
      <field name="name">Fornecedores empresas subsidiárias</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2224" model="account.account.template">
      <field name="code">2224</field>
      <field name="name">Fornecedores empresas associadas</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2225" model="account.account.template">
      <field name="code">2225</field>
      <field name="name">Fornecedores empreendimentos conjuntos</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2226" model="account.account.template">
      <field name="code">2226</field>
      <field name="name">Fornecedores outras partes relacionadas</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_225" model="account.account.template">
      <field name="code">225</field>
      <field name="name">Facturas em recepção e conferência</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_229" model="account.account.template">
      <field name="code">229</field>
      <field name="name">Perdas por imparidade acumuladas</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2311" model="account.account.template">
      <field name="code">2311</field>
      <field name="name">Aos órgãos sociais</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2312" model="account.account.template">
      <field name="code">2312</field>
      <field name="name">Ao pessoal</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2321" model="account.account.template">
      <field name="code">2321</field>
      <field name="name">Aos órgãos sociais</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2322" model="account.account.template">
      <field name="code">2322</field>
      <field name="name">Ao pessoal</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2371" model="account.account.template">
      <field name="code">2371</field>
      <field name="name">Dos órgãos sociais</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2372" model="account.account.template">
      <field name="code">2372</field>
      <field name="name">Do pessoal</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2381" model="account.account.template">
      <field name="code">2381</field>
      <field name="name">Com os órgãos sociais</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_239" model="account.account.template">
      <field name="code">239</field>
      <field name="name">Perdas por imparidade acumuladas</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2431" model="account.account.template">
      <field name="code">2431</field>
      <field name="name">Iva suportado</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2432" model="account.account.template">
      <field name="code">2432</field>
      <field name="name">Iva dedutível</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2433" model="account.account.template">
      <field name="code">2433</field>
      <field name="name">Iva liquidado</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2434" model="account.account.template">
      <field name="code">2434</field>
      <field name="name">Iva regularizações</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2435" model="account.account.template">
      <field name="code">2435</field>
      <field name="name">Iva apuramento</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2436" model="account.account.template">
      <field name="code">2436</field>
      <field name="name">Iva a pagar</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2437" model="account.account.template">
      <field name="code">2437</field>
      <field name="name">Iva a recuperar</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2438" model="account.account.template">
      <field name="code">2438</field>
      <field name="name">Iva reembolsos pedidos</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2439" model="account.account.template">
      <field name="code">2439</field>
      <field name="name">Iva liquidações oficiosas</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_244" model="account.account.template">
      <field name="code">244</field>
      <field name="name">Outros impostos</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_245" model="account.account.template">
      <field name="code">245</field>
      <field name="name">Contribuições para a segurança social</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_246" model="account.account.template">
      <field name="code">246</field>
      <field name="name">Tributos das autarquias locais</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_248" model="account.account.template">
      <field name="code">248</field>
      <field name="name">Outras tributações</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2511" model="account.account.template">
      <field name="code">2511</field>
      <field name="name">Empréstimos bancários</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2512" model="account.account.template">
      <field name="code">2512</field>
      <field name="name">Descobertos bancários</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2513" model="account.account.template">
      <field name="code">2513</field>
      <field name="name">Locações financeiras</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2521" model="account.account.template">
      <field name="code">2521</field>
      <field name="name">Empréstimos por obrigações</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2531" model="account.account.template">
      <field name="code">2531</field>
      <field name="name">Empresa mãe suprimentos e outros mútuos</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2532" model="account.account.template">
      <field name="code">2532</field>
      <field name="name">Outros participantes suprimentos e outros mútuos</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_254" model="account.account.template">
      <field name="code">254</field>
      <field name="name">Subsidiárias, associadas e empreendimentos conjuntos</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_258" model="account.account.template">
      <field name="code">258</field>
      <field name="name">Outros financiadores</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_261" model="account.account.template">
      <field name="code">261</field>
      <field name="name">Accionistas c. subscrição</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_262" model="account.account.template">
      <field name="code">262</field>
      <field name="name">Quotas não liberadas</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_263" model="account.account.template">
      <field name="code">263</field>
      <field name="name">Adiantamentos por conta de lucros</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_264" model="account.account.template">
      <field name="code">264</field>
      <field name="name">Resultados atribuídos</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_265" model="account.account.template">
      <field name="code">265</field>
      <field name="name">Lucros disponíveis</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_266" model="account.account.template">
      <field name="code">266</field>
      <field name="name">Empréstimos concedidos empresa mãe</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_268" model="account.account.template">
      <field name="code">268</field>
      <field name="name">Outras operações</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_269" model="account.account.template">
      <field name="code">269</field>
      <field name="name">Perdas por imparidade acumuladas</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2711" model="account.account.template">
      <field name="code">2711</field>
      <field name="name">Fornecedores de investimentos contas gerais</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2712" model="account.account.template">
      <field name="code">2712</field>
      <field name="name">Facturas em recepção e conferência</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2713" model="account.account.template">
      <field name="code">2713</field>
      <field name="name">Adiantamentos a fornecedores de investimentos</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2721" model="account.account.template">
      <field name="code">2721</field>
      <field name="name">Devedores por acréscimo de rendimentos</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2722" model="account.account.template">
      <field name="code">2722</field>
      <field name="name">Credores por acréscimos de gastos</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_273" model="account.account.template">
      <field name="code">273</field>
      <field name="name">Benefícios pós emprego</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2741" model="account.account.template">
      <field name="code">2741</field>
      <field name="name">Activos por impostos diferidos</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_2742" model="account.account.template">
      <field name="code">2742</field>
      <field name="name">Passivos por impostos diferidos</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_275" model="account.account.template">
      <field name="code">275</field>
      <field name="name">Credores por subscrições não liberadas</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_276" model="account.account.template">
      <field name="code">276</field>
      <field name="name">Adiantamentos por conta de vendas</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_278" model="account.account.template">
      <field name="code">278</field>
      <field name="name">Outros devedores e credores</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_279" model="account.account.template">
      <field name="code">279</field>
      <field name="name">Perdas por imparidade acumuladas</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_281" model="account.account.template">
      <field name="code">281</field>
      <field name="name">Gastos a reconhecer</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_282" model="account.account.template">
      <field name="code">282</field>
      <field name="name">Rendimentos a reconhecer</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_291" model="account.account.template">
      <field name="code">291</field>
      <field name="name">Impostos</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_292" model="account.account.template">
      <field name="code">292</field>
      <field name="name">Garantias a clientes</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_293" model="account.account.template">
      <field name="code">293</field>
      <field name="name">Processos judiciais em curso</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_294" model="account.account.template">
      <field name="code">294</field>
      <field name="name">Acidentes de trabalho e doenças profissionais</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_295" model="account.account.template">
      <field name="code">295</field>
      <field name="name">Matérias ambientais</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_296" model="account.account.template">
      <field name="code">296</field>
      <field name="name">Contratos onerosos</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_297" model="account.account.template">
      <field name="code">297</field>
      <field name="name">Reestruturação</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_298" model="account.account.template">
      <field name="code">298</field>
      <field name="name">Outras provisões</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_311" model="account.account.template">
      <field name="code">311</field>
      <field name="name">Mercadorias</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_312" model="account.account.template">
      <field name="code">312</field>
      <field name="name">Matérias primas, subsidiárias e de consumo</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_313" model="account.account.template">
      <field name="code">313</field>
      <field name="name">Activos biológicos</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_317" model="account.account.template">
      <field name="code">317</field>
      <field name="name">Devoluções de compras</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_318" model="account.account.template">
      <field name="code">318</field>
      <field name="name">Descontos e abatimentos em compras</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_325" model="account.account.template">
      <field name="code">325</field>
      <field name="name">Mercadorias em trânsito</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_326" model="account.account.template">
      <field name="code">326</field>
      <field name="name">Mercadorias em poder de terceiros</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_329" model="account.account.template">
      <field name="code">329</field>
      <field name="name">Perdas por imparidade acumuladas</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_331" model="account.account.template">
      <field name="code">331</field>
      <field name="name">Matérias primas</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_332" model="account.account.template">
      <field name="code">332</field>
      <field name="name">Matérias subsidiárias</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_333" model="account.account.template">
      <field name="code">333</field>
      <field name="name">Embalagens</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_334" model="account.account.template">
      <field name="code">334</field>
      <field name="name">Materiais diversos</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_335" model="account.account.template">
      <field name="code">335</field>
      <field name="name">Matérias em trânsito</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_339" model="account.account.template">
      <field name="code">339</field>
      <field name="name">Perdas por imparidade acumuladas</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_346" model="account.account.template">
      <field name="code">346</field>
      <field name="name">Produtos em poder de terceiros</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_349" model="account.account.template">
      <field name="code">349</field>
      <field name="name">Perdas por imparidade acumuladas</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_351" model="account.account.template">
      <field name="code">351</field>
      <field name="name">Subprodutos</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_352" model="account.account.template">
      <field name="code">352</field>
      <field name="name">Desperdícios, resíduos e refugos</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_359" model="account.account.template">
      <field name="code">359</field>
      <field name="name">Perdas por imparidade acumuladas</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_36" model="account.account.template">
      <field name="code">36</field>
      <field name="name">Produtos e trabalhos em curso</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_3711" model="account.account.template">
      <field name="code">3711</field>
      <field name="name">Animais</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_3712" model="account.account.template">
      <field name="code">3712</field>
      <field name="name">Plantas</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_3721" model="account.account.template">
      <field name="code">3721</field>
      <field name="name">Animais</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_3722" model="account.account.template">
      <field name="code">3722</field>
      <field name="name">Plantas</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_382" model="account.account.template">
      <field name="code">382</field>
      <field name="name">Mercadorias</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_383" model="account.account.template">
      <field name="code">383</field>
      <field name="name">Matérias primas, subsidiárias e de consumo</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_384" model="account.account.template">
      <field name="code">384</field>
      <field name="name">Produtos acabados e intermédios</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_385" model="account.account.template">
      <field name="code">385</field>
      <field name="name">Subprodutos, desperdícios, resíduos e refugos</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_386" model="account.account.template">
      <field name="code">386</field>
      <field name="name">Produtos e trabalhos em curso</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_387" model="account.account.template">
      <field name="code">387</field>
      <field name="name">Activos biológicos</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_39" model="account.account.template">
      <field name="code">39</field>
      <field name="name">Adiantamentos por conta de compras</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_4111" model="account.account.template">
      <field name="code">4111</field>
      <field name="name">Participações de capital método da equiv. patrimonial</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_4112" model="account.account.template">
      <field name="code">4112</field>
      <field name="name">Participações de capital outros métodos</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_4113" model="account.account.template">
      <field name="code">4113</field>
      <field name="name">Empréstimos concedidos</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_4121" model="account.account.template">
      <field name="code">4121</field>
      <field name="name">Participações de capital método da equiv. patrimonial</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_4122" model="account.account.template">
      <field name="code">4122</field>
      <field name="name">Participações de capital outros métodos</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_4123" model="account.account.template">
      <field name="code">4123</field>
      <field name="name">Empréstimos concedidos</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_4131" model="account.account.template">
      <field name="code">4131</field>
      <field name="name">Participações de capital método da equiv. patrimonial</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_4132" model="account.account.template">
      <field name="code">4132</field>
      <field name="name">Participações de capital outros métodos</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_4133" model="account.account.template">
      <field name="code">4133</field>
      <field name="name">Empréstimos concedidos</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_4141" model="account.account.template">
      <field name="code">4141</field>
      <field name="name">Participações de capital</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_4142" model="account.account.template">
      <field name="code">4142</field>
      <field name="name">Empréstimos concedidos</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_4151" model="account.account.template">
      <field name="code">4151</field>
      <field name="name">Detidos até à maturidade</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_4158" model="account.account.template">
      <field name="code">4158</field>
      <field name="name">Acções da sgm (6500x1,00)</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_419" model="account.account.template">
      <field name="code">419</field>
      <field name="name">Perdas por imparidade acumuladas</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_421" model="account.account.template">
      <field name="code">421</field>
      <field name="name">Terrenos e recursos naturais</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_422" model="account.account.template">
      <field name="code">422</field>
      <field name="name">Edifícios e outras construções</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_426" model="account.account.template">
      <field name="code">426</field>
      <field name="name">Outras propriedades de investimento</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_428" model="account.account.template">
      <field name="code">428</field>
      <field name="name">Depreciações acumuladas</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_429" model="account.account.template">
      <field name="code">429</field>
      <field name="name">Perdas por imparidade acumuladas</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_431" model="account.account.template">
      <field name="code">431</field>
      <field name="name">Terrenos e recursos naturais</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_432" model="account.account.template">
      <field name="code">432</field>
      <field name="name">Edifícios e outras construções</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_433" model="account.account.template">
      <field name="code">433</field>
      <field name="name">Equipamento básico</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_434" model="account.account.template">
      <field name="code">434</field>
      <field name="name">Equipamento de transporte</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_435" model="account.account.template">
      <field name="code">435</field>
      <field name="name">Equipamento administrativo</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_436" model="account.account.template">
      <field name="code">436</field>
      <field name="name">Equipamentos biológicos</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_437" model="account.account.template">
      <field name="code">437</field>
      <field name="name">Outros activos fixos tangíveis</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_438" model="account.account.template">
      <field name="code">438</field>
      <field name="name">Depreciações acumuladas</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_439" model="account.account.template">
      <field name="code">439</field>
      <field name="name">Perdas por imparidade acumuladas</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_441" model="account.account.template">
      <field name="code">441</field>
      <field name="name">Goodwill</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_442" model="account.account.template">
      <field name="code">442</field>
      <field name="name">Projectos de desenvolvimento</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_443" model="account.account.template">
      <field name="code">443</field>
      <field name="name">Programas de computador</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_444" model="account.account.template">
      <field name="code">444</field>
      <field name="name">Propriedade industrial</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_446" model="account.account.template">
      <field name="code">446</field>
      <field name="name">Outros activos intangíveis</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_448" model="account.account.template">
      <field name="code">448</field>
      <field name="name">Depreciações acumuladas</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_449" model="account.account.template">
      <field name="code">449</field>
      <field name="name">Perdas por imparidade acumuladas</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_451" model="account.account.template">
      <field name="code">451</field>
      <field name="name">Investimentos financeiros em curso</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_452" model="account.account.template">
      <field name="code">452</field>
      <field name="name">Propriedades de investimento em curso</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_453" model="account.account.template">
      <field name="code">453</field>
      <field name="name">Activos fixos tangíveis em curso</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_454" model="account.account.template">
      <field name="code">454</field>
      <field name="name">Activos intangíveis em curso</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_455" model="account.account.template">
      <field name="code">455</field>
      <field name="name">Adiantamentos por conta de investimentos</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_459" model="account.account.template">
      <field name="code">459</field>
      <field name="name">Perdas por imparidade acumuladas</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_469" model="account.account.template">
      <field name="code">469</field>
      <field name="name">Perdas por imparidade acumuladas</field>
      <field name="user_type_id" ref="account.data_account_type_current_assets" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_51" model="account.account.template">
      <field name="code">51</field>
      <field name="name">Capital</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_521" model="account.account.template">
      <field name="code">521</field>
      <field name="name">Valor nominal</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_522" model="account.account.template">
      <field name="code">522</field>
      <field name="name">Descontos e prémios</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_53" model="account.account.template">
      <field name="code">53</field>
      <field name="name">Outros instrumentos de capital próprio</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_54" model="account.account.template">
      <field name="code">54</field>
      <field name="name">Prémios de emissão</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_551" model="account.account.template">
      <field name="code">551</field>
      <field name="name">Reservas legais</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_552" model="account.account.template">
      <field name="code">552</field>
      <field name="name">Outras reservas</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_56" model="account.account.template">
      <field name="code">56</field>
      <field name="name">Resultados transitados</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_5711" model="account.account.template">
      <field name="code">5711</field>
      <field name="name">Ajustamentos de transição</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_5712" model="account.account.template">
      <field name="code">5712</field>
      <field name="name">Lucros não atribuídos</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_5713" model="account.account.template">
      <field name="code">5713</field>
      <field name="name">Decorrentes de outras variações nos capitais próprios d</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_579" model="account.account.template">
      <field name="code">579</field>
      <field name="name">Outros</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_5811" model="account.account.template">
      <field name="code">5811</field>
      <field name="name">Antes de imposto sobre o rendimento</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_5812" model="account.account.template">
      <field name="code">5812</field>
      <field name="name">Impostos diferidos</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_5891" model="account.account.template">
      <field name="code">5891</field>
      <field name="name">Antes de imposto sobre o rendimento</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_5892" model="account.account.template">
      <field name="code">5892</field>
      <field name="name">Impostos diferidos</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_591" model="account.account.template">
      <field name="code">591</field>
      <field name="name">Diferenças de conversão de demonstrações financeiras</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_592" model="account.account.template">
      <field name="code">592</field>
      <field name="name">Ajustamentos por impostos diferidos</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_593" model="account.account.template">
      <field name="code">593</field>
      <field name="name">Subsídios</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_594" model="account.account.template">
      <field name="code">594</field>
      <field name="name">Doações</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_599" model="account.account.template">
      <field name="code">599</field>
      <field name="name">Outras</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_611" model="account.account.template">
      <field name="code">611</field>
      <field name="name">Mercadorias</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_612" model="account.account.template">
      <field name="code">612</field>
      <field name="name">Matérias primas, subsidiárias e de consumo</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_613" model="account.account.template">
      <field name="code">613</field>
      <field name="name">Activos biológicos (compras)</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_621" model="account.account.template">
      <field name="code">621</field>
      <field name="name">Subcontratos</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6221" model="account.account.template">
      <field name="code">6221</field>
      <field name="name">Trabalhos especializados</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6222" model="account.account.template">
      <field name="code">6222</field>
      <field name="name">Publicidade e propaganda</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6223" model="account.account.template">
      <field name="code">6223</field>
      <field name="name">Vigilância e segurança</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6224" model="account.account.template">
      <field name="code">6224</field>
      <field name="name">Honorários</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6225" model="account.account.template">
      <field name="code">6225</field>
      <field name="name">Comissões</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6226" model="account.account.template">
      <field name="code">6226</field>
      <field name="name">Conservação e reparação</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6228" model="account.account.template">
      <field name="code">6228</field>
      <field name="name">Outros</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6231" model="account.account.template">
      <field name="code">6231</field>
      <field name="name">Ferramentas e utensílios de desgaste rápido</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6232" model="account.account.template">
      <field name="code">6232</field>
      <field name="name">Livros de documentação técnica</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6233" model="account.account.template">
      <field name="code">6233</field>
      <field name="name">Material de escritório</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6234" model="account.account.template">
      <field name="code">6234</field>
      <field name="name">Artigos de oferta</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6238" model="account.account.template">
      <field name="code">6238</field>
      <field name="name">Outros</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6241" model="account.account.template">
      <field name="code">6241</field>
      <field name="name">Electricidade</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6242" model="account.account.template">
      <field name="code">6242</field>
      <field name="name">Combustíveis</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6243" model="account.account.template">
      <field name="code">6243</field>
      <field name="name">Água</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6248" model="account.account.template">
      <field name="code">6248</field>
      <field name="name">Outros</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6251" model="account.account.template">
      <field name="code">6251</field>
      <field name="name">Deslocações e estadas</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6252" model="account.account.template">
      <field name="code">6252</field>
      <field name="name">Transporte de pessoal</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6253" model="account.account.template">
      <field name="code">6253</field>
      <field name="name">Transportes de mercadorias</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6258" model="account.account.template">
      <field name="code">6258</field>
      <field name="name">Outros</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6261" model="account.account.template">
      <field name="code">6261</field>
      <field name="name">Rendas e alugueres</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6262" model="account.account.template">
      <field name="code">6262</field>
      <field name="name">Comunicação</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6263" model="account.account.template">
      <field name="code">6263</field>
      <field name="name">Seguros</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6264" model="account.account.template">
      <field name="code">6264</field>
      <field name="name">Royalties</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6265" model="account.account.template">
      <field name="code">6265</field>
      <field name="name">Contencioso e notariado</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6266" model="account.account.template">
      <field name="code">6266</field>
      <field name="name">Despesas de representação</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6267" model="account.account.template">
      <field name="code">6267</field>
      <field name="name">Limpeza, higiene e conforto</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6268" model="account.account.template">
      <field name="code">6268</field>
      <field name="name">Outros serviços</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_631" model="account.account.template">
      <field name="code">631</field>
      <field name="name">Remunerações dos órgãos sociais</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_632" model="account.account.template">
      <field name="code">632</field>
      <field name="name">Remunerações do pessoal</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6331" model="account.account.template">
      <field name="code">6331</field>
      <field name="name">Prémios para pensões</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6332" model="account.account.template">
      <field name="code">6332</field>
      <field name="name">Outros benefícios</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_634" model="account.account.template">
      <field name="code">634</field>
      <field name="name">Indemnizações</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_635" model="account.account.template">
      <field name="code">635</field>
      <field name="name">Encargos sobre remunerações</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_636" model="account.account.template">
      <field name="code">636</field>
      <field name="name">Seguros de acidentes no trabalho e doenças profissionais</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_637" model="account.account.template">
      <field name="code">637</field>
      <field name="name">Gastos de acção social</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_638" model="account.account.template">
      <field name="code">638</field>
      <field name="name">Outros gastos com o pessoal</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_641" model="account.account.template">
      <field name="code">641</field>
      <field name="name">Propriedades de investimento</field>
      <field name="user_type_id" ref="account.data_account_type_depreciation" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_642" model="account.account.template">
      <field name="code">642</field>
      <field name="name">Activos fixos tangíveis</field>
      <field name="user_type_id" ref="account.data_account_type_depreciation" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_643" model="account.account.template">
      <field name="code">643</field>
      <field name="name">Activos intangíveis</field>
      <field name="user_type_id" ref="account.data_account_type_depreciation" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6511" model="account.account.template">
      <field name="code">6511</field>
      <field name="name">Clientes</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6512" model="account.account.template">
      <field name="code">6512</field>
      <field name="name">Outros devedores</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_652" model="account.account.template">
      <field name="code">652</field>
      <field name="name">Em inventários</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_653" model="account.account.template">
      <field name="code">653</field>
      <field name="name">Em investimentos financeiros</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_654" model="account.account.template">
      <field name="code">654</field>
      <field name="name">Em propriedades de investimento</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_655" model="account.account.template">
      <field name="code">655</field>
      <field name="name">Em activos fixos tangíveis</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_656" model="account.account.template">
      <field name="code">656</field>
      <field name="name">Em activos intangíveis</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_657" model="account.account.template">
      <field name="code">657</field>
      <field name="name">Em investimentos em curso</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_658" model="account.account.template">
      <field name="code">658</field>
      <field name="name">Em activos não correntes detidos para venda</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_661" model="account.account.template">
      <field name="code">661</field>
      <field name="name">Em instrumentos financeiros</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_662" model="account.account.template">
      <field name="code">662</field>
      <field name="name">Em investimentos financeiros</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_663" model="account.account.template">
      <field name="code">663</field>
      <field name="name">Em propriedades de investimento</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_664" model="account.account.template">
      <field name="code">664</field>
      <field name="name">Em activos biológicos</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_671" model="account.account.template">
      <field name="code">671</field>
      <field name="name">Impostos</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_672" model="account.account.template">
      <field name="code">672</field>
      <field name="name">Garantias a clientes</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_673" model="account.account.template">
      <field name="code">673</field>
      <field name="name">Processos judiciais em curso</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_674" model="account.account.template">
      <field name="code">674</field>
      <field name="name">Acidentes de trabalho e doenças profissionais</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_675" model="account.account.template">
      <field name="code">675</field>
      <field name="name">Matérias ambientais</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_676" model="account.account.template">
      <field name="code">676</field>
      <field name="name">Contratos onerosos</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_677" model="account.account.template">
      <field name="code">677</field>
      <field name="name">Reestruturação</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_678" model="account.account.template">
      <field name="code">678</field>
      <field name="name">Outras provisões</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6811" model="account.account.template">
      <field name="code">6811</field>
      <field name="name">Impostos directos</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6812" model="account.account.template">
      <field name="code">6812</field>
      <field name="name">Impostos indirectos</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6813" model="account.account.template">
      <field name="code">6813</field>
      <field name="name">Taxas</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_682" model="account.account.template">
      <field name="code">682</field>
      <field name="name">Descontos de pronto pagamento concedidos</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_683" model="account.account.template">
      <field name="code">683</field>
      <field name="name">Dívidas incobráveis</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6841" model="account.account.template">
      <field name="code">6841</field>
      <field name="name">Sinistros</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6842" model="account.account.template">
      <field name="code">6842</field>
      <field name="name">Quebras</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6848" model="account.account.template">
      <field name="code">6848</field>
      <field name="name">Outras perdas</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6851" model="account.account.template">
      <field name="code">6851</field>
      <field name="name">Cobertura de prejuízos</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6852" model="account.account.template">
      <field name="code">6852</field>
      <field name="name">Aplicação do método da equivalência patrimonial</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6853" model="account.account.template">
      <field name="code">6853</field>
      <field name="name">Alienações</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6858" model="account.account.template">
      <field name="code">6858</field>
      <field name="name">Outros gastos e perdas</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6861" model="account.account.template">
      <field name="code">6861</field>
      <field name="name">Cobertura de prejuízos</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6862" model="account.account.template">
      <field name="code">6862</field>
      <field name="name">Alienações</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6868" model="account.account.template">
      <field name="code">6868</field>
      <field name="name">Outros gastos e perdas</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6871" model="account.account.template">
      <field name="code">6871</field>
      <field name="name">Alienações</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6872" model="account.account.template">
      <field name="code">6872</field>
      <field name="name">Sinistros</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6873" model="account.account.template">
      <field name="code">6873</field>
      <field name="name">Abates</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6874" model="account.account.template">
      <field name="code">6874</field>
      <field name="name">Gastos em propriedades de investimento</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6878" model="account.account.template">
      <field name="code">6878</field>
      <field name="name">Outros gastos e perdas</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6881" model="account.account.template">
      <field name="code">6881</field>
      <field name="name">Correcções relativas a períodos anteriores</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6882" model="account.account.template">
      <field name="code">6882</field>
      <field name="name">Donativos</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6883" model="account.account.template">
      <field name="code">6883</field>
      <field name="name">Quotizações</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6884" model="account.account.template">
      <field name="code">6884</field>
      <field name="name">Ofertas e amostras de inventários</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6885" model="account.account.template">
      <field name="code">6885</field>
      <field name="name">Insuficiência da estimativa para impostos</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6886" model="account.account.template">
      <field name="code">6886</field>
      <field name="name">Perdas em instrumentos financeiros</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6888" model="account.account.template">
      <field name="code">6888</field>
      <field name="name">Outros não especificados</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6911" model="account.account.template">
      <field name="code">6911</field>
      <field name="name">Juros de financiamento obtidos</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6918" model="account.account.template">
      <field name="code">6918</field>
      <field name="name">Outros juros</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_692" model="account.account.template">
      <field name="code">692</field>
      <field name="name">Diferenças de câmbio desfavoráveis</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6921" model="account.account.template">
      <field name="code">6921</field>
      <field name="name">Relativos a financiamentos obtidos</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6928" model="account.account.template">
      <field name="code">6928</field>
      <field name="name">Outras</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6981" model="account.account.template">
      <field name="code">6981</field>
      <field name="name">Relativos a financiamentos obtidos</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_6988" model="account.account.template">
      <field name="code">6988</field>
      <field name="name">Outros</field>
      <field name="user_type_id" ref="account.data_account_type_expenses" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_711" model="account.account.template">
      <field name="code">711</field>
      <field name="name">Mercadoria</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_712" model="account.account.template">
      <field name="code">712</field>
      <field name="name">Produtos acabados e intermédios</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_713" model="account.account.template">
      <field name="code">713</field>
      <field name="name">Subprodutos, desperdícios, resíduos e refugos</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_714" model="account.account.template">
      <field name="code">714</field>
      <field name="name">Activos biológicos</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_716" model="account.account.template">
      <field name="code">716</field>
      <field name="name">Iva das vendas com imposto incluído</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_717" model="account.account.template">
      <field name="code">717</field>
      <field name="name">Devoluções de vendas</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_718" model="account.account.template">
      <field name="code">718</field>
      <field name="name">Descontos e abatimentos em vendas</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_721" model="account.account.template">
      <field name="code">721</field>
      <field name="name">Serviço a</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_722" model="account.account.template">
      <field name="code">722</field>
      <field name="name">Serviço b</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_725" model="account.account.template">
      <field name="code">725</field>
      <field name="name">Serviços secundários</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_726" model="account.account.template">
      <field name="code">726</field>
      <field name="name">Iva dos serviços com imposto incluído</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_728" model="account.account.template">
      <field name="code">728</field>
      <field name="name">Descontos e abatimentos</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_731" model="account.account.template">
      <field name="code">731</field>
      <field name="name">Produtos acabados e intermédios</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_732" model="account.account.template">
      <field name="code">732</field>
      <field name="name">Subprodutos, desperdícios, resíduos e refugos</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_733" model="account.account.template">
      <field name="code">733</field>
      <field name="name">Produtos e trabalhos em curso</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_734" model="account.account.template">
      <field name="code">734</field>
      <field name="name">Activos biológicos</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_741" model="account.account.template">
      <field name="code">741</field>
      <field name="name">Activos fixos tangíveis</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_742" model="account.account.template">
      <field name="code">742</field>
      <field name="name">Activos intangíveis</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_743" model="account.account.template">
      <field name="code">743</field>
      <field name="name">Propriedades de investimento</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_744" model="account.account.template">
      <field name="code">744</field>
      <field name="name">Activos por gastos diferidos</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_751" model="account.account.template">
      <field name="code">751</field>
      <field name="name">Subsídios do estado e outros entes públicos</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_752" model="account.account.template">
      <field name="code">752</field>
      <field name="name">Subsídios de outras entidades</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7611" model="account.account.template">
      <field name="code">7611</field>
      <field name="name">Propriedades de investimento</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7612" model="account.account.template">
      <field name="code">7612</field>
      <field name="name">Activos fixos tangíveis</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7613" model="account.account.template">
      <field name="code">7613</field>
      <field name="name">Activos intangíveis</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_76211" model="account.account.template">
      <field name="code">76211</field>
      <field name="name">Clientes</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_76212" model="account.account.template">
      <field name="code">76212</field>
      <field name="name">Outros devedores</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7622" model="account.account.template">
      <field name="code">7622</field>
      <field name="name">Em inventários</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7623" model="account.account.template">
      <field name="code">7623</field>
      <field name="name">Em investimentos financeiros</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7624" model="account.account.template">
      <field name="code">7624</field>
      <field name="name">Em propriedades de investimento</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7625" model="account.account.template">
      <field name="code">7625</field>
      <field name="name">Em activos fixos tangíveis</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7626" model="account.account.template">
      <field name="code">7626</field>
      <field name="name">Em activos intangíveis</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7627" model="account.account.template">
      <field name="code">7627</field>
      <field name="name">Em investimentos em curso</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7628" model="account.account.template">
      <field name="code">7628</field>
      <field name="name">Em activos não correntes detidos para venda</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7631" model="account.account.template">
      <field name="code">7631</field>
      <field name="name">Impostos</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7632" model="account.account.template">
      <field name="code">7632</field>
      <field name="name">Garantias a clientes</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7633" model="account.account.template">
      <field name="code">7633</field>
      <field name="name">Processos judiciais em curso</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7634" model="account.account.template">
      <field name="code">7634</field>
      <field name="name">Acidentes no trabalho e doenças profissionais</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7635" model="account.account.template">
      <field name="code">7635</field>
      <field name="name">Matérias ambientais</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7636" model="account.account.template">
      <field name="code">7636</field>
      <field name="name">Contratos onerosos</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7637" model="account.account.template">
      <field name="code">7637</field>
      <field name="name">Reestruturação</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7638" model="account.account.template">
      <field name="code">7638</field>
      <field name="name">Outras provisões</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_771" model="account.account.template">
      <field name="code">771</field>
      <field name="name">Em instrumentos financeiros</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_772" model="account.account.template">
      <field name="code">772</field>
      <field name="name">Em investimentos financeiros</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_773" model="account.account.template">
      <field name="code">773</field>
      <field name="name">Em propriedades de investimento</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_774" model="account.account.template">
      <field name="code">774</field>
      <field name="name">Em activos biológicos</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7811" model="account.account.template">
      <field name="code">7811</field>
      <field name="name">Serviços sociais</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7812" model="account.account.template">
      <field name="code">7812</field>
      <field name="name">Aluguer de equipamento</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7813" model="account.account.template">
      <field name="code">7813</field>
      <field name="name">Estudos, projectos e assistência tecnológica</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7814" model="account.account.template">
      <field name="code">7814</field>
      <field name="name">Royalties</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7815" model="account.account.template">
      <field name="code">7815</field>
      <field name="name">Desempenho de cargos sociais noutras empresas</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7816" model="account.account.template">
      <field name="code">7816</field>
      <field name="name">Outros rendimentos suplementares</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_783" model="account.account.template">
      <field name="code">783</field>
      <field name="name">Recuperação de dívidas a receber</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7841" model="account.account.template">
      <field name="code">7841</field>
      <field name="name">Sinistros</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7842" model="account.account.template">
      <field name="code">7842</field>
      <field name="name">Sobras</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7848" model="account.account.template">
      <field name="code">7848</field>
      <field name="name">Outros ganhos</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7851" model="account.account.template">
      <field name="code">7851</field>
      <field name="name">Aplicação do método da equivalência patrimonial</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7852" model="account.account.template">
      <field name="code">7852</field>
      <field name="name">Alienações</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7858" model="account.account.template">
      <field name="code">7858</field>
      <field name="name">Outros rendimentos e ganhos</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7861" model="account.account.template">
      <field name="code">7861</field>
      <field name="name">Diferenças de câmbio favoráveis</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7862" model="account.account.template">
      <field name="code">7862</field>
      <field name="name">Alienações</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7868" model="account.account.template">
      <field name="code">7868</field>
      <field name="name">Outros rendimentos e ganhos</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7871" model="account.account.template">
      <field name="code">7871</field>
      <field name="name">Alienações</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7872" model="account.account.template">
      <field name="code">7872</field>
      <field name="name">Sinistros</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7873" model="account.account.template">
      <field name="code">7873</field>
      <field name="name">Rendas e outros rendimentos em propriedades de investimento</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7878" model="account.account.template">
      <field name="code">7878</field>
      <field name="name">Outros rendimentos e ganhos</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7881" model="account.account.template">
      <field name="code">7881</field>
      <field name="name">Correcções relativas a períodos anteriores</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7882" model="account.account.template">
      <field name="code">7882</field>
      <field name="name">Excesso da estimativa para impostos</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7883" model="account.account.template">
      <field name="code">7883</field>
      <field name="name">Imputação de subsídios para investimentos</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7884" model="account.account.template">
      <field name="code">7884</field>
      <field name="name">Ganhos em outros instrumentos financeiros</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7885" model="account.account.template">
      <field name="code">7885</field>
      <field name="name">Restituição de impostos</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7888" model="account.account.template">
      <field name="code">7888</field>
      <field name="name">Outros não especificados</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7911" model="account.account.template">
      <field name="code">7911</field>
      <field name="name">De depósitos</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7912" model="account.account.template">
      <field name="code">7912</field>
      <field name="name">De outras aplicações de meios financeiros líquidos</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7913" model="account.account.template">
      <field name="code">7913</field>
      <field name="name">De financiamentos concedidos a associadas e emp. conjun</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7914" model="account.account.template">
      <field name="code">7914</field>
      <field name="name">De financiamentos concedidos a subsidiárias</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7915" model="account.account.template">
      <field name="code">7915</field>
      <field name="name">De financiamentos obtidos</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7918" model="account.account.template">
      <field name="code">7918</field>
      <field name="name">De outros financiamentos obtidos</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7921" model="account.account.template">
      <field name="code">7921</field>
      <field name="name">De aplicações de meios financeiros líquidos</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7922" model="account.account.template">
      <field name="code">7922</field>
      <field name="name">De associadas e empreendimentos conjuntos</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7923" model="account.account.template">
      <field name="code">7923</field>
      <field name="name">De subsidiárias</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_7928" model="account.account.template">
      <field name="code">7928</field>
      <field name="name">Outras</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_798" model="account.account.template">
      <field name="code">798</field>
      <field name="name">Outros rendimentos similares</field>
      <field name="user_type_id" ref="account.data_account_type_revenue" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_811" model="account.account.template">
      <field name="code">811</field>
      <field name="name">Resultado antes de impostos</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_8121" model="account.account.template">
      <field name="code">8121</field>
      <field name="name">Imposto estimado para o período</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_8122" model="account.account.template">
      <field name="code">8122</field>
      <field name="name">Imposto diferido</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_818" model="account.account.template">
      <field name="code">818</field>
      <field name="name">Resultado líquido</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    <record id="chart_89" model="account.account.template">
      <field name="code">89</field>
      <field name="name">Dividendos antecipados</field>
      <field name="user_type_id" ref="account.data_account_type_current_liabilities" />
      <field name="chart_template_id" ref="pt_chart_template"/>
    </record>

    </data>
</odoo>
