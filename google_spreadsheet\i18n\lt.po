# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* google_spreadsheet
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: <PERSON><PERSON> V<PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: google_spreadsheet
#. openerp-web
#: code:addons/google_spreadsheet/static/src/xml/addtospreadsheet.xml:0
#, python-format
msgid "Add to Google Spreadsheet"
msgstr "Pridėti prie \"Google Spreadsheet\""

#. module: google_spreadsheet
#: model:ir.model,name:google_spreadsheet.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigūracijos nustatymai"

#. module: google_spreadsheet
#: model:ir.model,name:google_spreadsheet.model_google_drive_config
msgid "Google Drive templates config"
msgstr "\"Google Drive\" šablonų nustat."

#. module: google_spreadsheet
#: model:ir.actions.act_window,name:google_spreadsheet.action_ir_attachment_google_spreadsheet_tree
#: model:ir.ui.menu,name:google_spreadsheet.menu_reporting_dashboard_google_spreadsheets
#: model_terms:ir.actions.act_window,help:google_spreadsheet.action_ir_attachment_google_spreadsheet_tree
#: model_terms:ir.ui.view,arch_db:google_spreadsheet.view_ir_attachment_google_spreadsheet_form
#: model_terms:ir.ui.view,arch_db:google_spreadsheet.view_ir_attachment_google_spreadsheet_tree
msgid "Google Spreadsheets"
msgstr "Google Spreadsheets"

#. module: google_spreadsheet
#: model_terms:ir.ui.view,arch_db:google_spreadsheet.view_ir_attachment_google_spreadsheet_form
#: model_terms:ir.ui.view,arch_db:google_spreadsheet.view_ir_attachment_google_spreadsheet_tree
msgid "Name"
msgstr "Pavadinimas"

#. module: google_spreadsheet
#: model_terms:ir.ui.view,arch_db:google_spreadsheet.res_config_settings_view_form
msgid ""
"Please use the settings of Google Drive\n"
"                            on the left or above."
msgstr ""
"Naudokite \"Google Drive\" nustatymus\n"
"kairėje arba viršuje."

#. module: google_spreadsheet
#: model:ir.model.fields,help:google_spreadsheet.field_res_config_settings__google_drive_uri_copy
msgid "The URL to generate the authorization code from Google"
msgstr "URL, naudojamas gauti patvirtinimo kodą iš \"Google\""

#. module: google_spreadsheet
#: model:ir.model.fields,field_description:google_spreadsheet.field_res_config_settings__google_drive_uri_copy
msgid "URI Copy"
msgstr "URI kopija"
