<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="800" height="600">
  <defs>
    <clipPath id="clip-path" clipPathUnits="objectBoundingBox">
      <use xlink:href="#filterPath" fill="none"></use>
    </clipPath>
    <path id="filterPath"
      d="M0.2142,0.096c-0.413,1.8378,1.2192,0.3258,0.6396,0.1988C0.6879,0.2584,0.2791-0.1927,0.2142,0.096Z"></path>
  </defs><svg viewBox="75.05909729003906 83.49757385253906 154.99501037597656 130.04640197753906"
    preserveAspectRatio="none">
    <path class="background"
      d="M223.45,129.45c-47.29,21.63-17.59,56.7-41.95,81.07-8,8-81.08-7.91-98.62-44.28-20.21-41.91,8.66-85.46,32.38-81.49C152.05,90.91,253.9,115.53,223.45,129.45Z"
      fill="none" stroke="#3AADAA" stroke-miterlimit="10" stroke-width="2"></path>
  </svg><svg viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
    <use xlink:href="#filterPath" fill="darkgrey"></use>
  </svg>
  <image xlink:href="" clip-path="url(#clip-path)"></image>
</svg>
