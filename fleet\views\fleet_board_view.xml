<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="fleet_costs_report_view_search" model="ir.ui.view">
        <field name="name">fleet.vehicle.cost.view.search</field>
        <field name="model">fleet.vehicle.cost.report</field>
        <field name="arch" type="xml">
            <search string="Fleet Costs Analysis">
                <field name="name" filter_domain="[('name', 'ilike', self)]"/>
                <field name="driver_id" filter_domain="[('driver_id', 'ilike', self)]"/>
                <field name="date_start"/>
                <filter string="Service" name="service" domain="[('cost_type', '=', 'service')]"/>
                <filter string="Contract" name="contract" domain="[('cost_type', '=', 'contract')]"/>
                <separator/>
                <filter name="filter_date_start" date="date_start" default_period="this_year"/>
                <group expand="1" string="Group By">
                    <filter string="Vehicle" name="vehicle" context="{'group_by':'vehicle_id'}"/>
                    <filter string="Driver" name="driver" context="{'group_by':'driver_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="fleet_costs_report_view_pivot" model="ir.ui.view">
        <field name="name">fleet.vehicle.cost.view.pivot</field>
        <field name="model">fleet.vehicle.cost.report</field>
        <field name="arch" type="xml">
            <pivot sample="1">
                <field name="date_start" type="col" interval="year" />
                <field name="cost_type" type="col" />
                <field name="vehicle_id" type="row" />
                <field name="cost" type="measure" />
            </pivot>
        </field>
    </record>

    <record id="fleet_costs_report_view_graph" model="ir.ui.view">
        <field name="name">fleet.vehicle.cost.view.graph</field>
        <field name="model">fleet.vehicle.cost.report</field>
        <field name="arch" type="xml">
            <graph string="Fleet Costs Analysis" sample="1">
                <field name="date_start" interval="month"/>
                <field name="cost_type"/>
                <field name="cost" type="measure"/>
            </graph>
        </field>
    </record>

    <record id="fleet_vechicle_costs_report_view_tree" model="ir.ui.view">
        <field name="name">fleet.vehicle.cost.report.view.tree</field>
        <field name="model">fleet.vehicle.cost.report</field>
        <field name="arch" type="xml">
            <tree string="Fleet Costs Analysis">
                <field name="name"/>
                <field name="driver_id" optional="show"/>
                <field name="fuel_type" optional="hide"/>
                <field name="date_start" optional="show"/>
                <field name="cost" optional="show" sum="Sum of Cost"/>
                <field name="cost_type" optional="show"/>
                <field name="company_id" groups="base.group_multi_company"/>
            </tree>
        </field>
    </record>

 <record id="fleet_costs_reporting_action" model="ir.actions.act_window">
      <field name="name">Costs Analysis</field>
      <field name="res_model">fleet.vehicle.cost.report</field>
      <field name="view_mode">graph,pivot</field>
      <field name="view_id"></field>  
      <field name="context" eval="{'search_default_filter_date_start': 1}"/>
      <field name="search_view_id" ref="fleet.fleet_costs_report_view_search"/>
      <field name="help" type="html">
        <p class="o_view_nocontent_empty_folder">
          No data for analysis
        </p><p>
          Manage efficiently your different effective vehicles Costs with Odoo.
        </p>
      </field>
    </record>
    <menuitem name="Reporting" parent="menu_root" id="menu_fleet_reporting" sequence="99" groups="fleet_group_manager"/>
    <menuitem id="menu_fleet_reporting_costs"
              name="Costs"
              parent="menu_fleet_reporting"
              action="fleet_costs_reporting_action"
              sequence="1"
              groups="fleet_group_manager"/>
</odoo>
