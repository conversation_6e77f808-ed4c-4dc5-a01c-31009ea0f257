<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="mrp_subcontracting_product_template_search_view" model="ir.ui.view">
            <field name="name">mrp.subcontracting.product.template.search</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="product.product_template_search_view" />
            <field name="groups_id" eval="[(4, ref('mrp.group_mrp_user'))]"/>
            <field name="arch" type="xml">
                <xpath expr="//filter[@name='filter_to_purchase']" position="after">
                    <filter string="Can be Subcontracted" name="filter_can_be_subcontracted" domain="[('bom_ids.type', '=', 'subcontract')]" />
                </xpath>
            </field>
        </record>
    </data>
</odoo>

