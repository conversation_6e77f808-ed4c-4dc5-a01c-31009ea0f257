<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <template id="report_mrp_bom">
        <div class="container o_mrp_bom_report_page">
            <t t-if="data.get('components') or data.get('lines') or data.get('operations')">
                <div class="row">
                    <div class="col-lg-12">
                        <h1 style="display:inline;">BoM Structure </h1>
                        <h1 style="display:inline;" t-if="data['report_structure'] != 'bom_structure'" class="o_mrp_prod_cost">&amp; Cost</h1>
                        <h3>
                            <a href="#" t-if="data['report_type'] == 'html'" t-att-data-res-id="data['product'].id" t-att-data-model="data['product']._name" class="o_mrp_bom_action">
                                <t t-esc="data['bom_prod_name']"/>
                            </a>
                            <t t-else="" t-esc="data['bom_prod_name']"/>
                        </h3>
                        <h6 t-if="data['bom'].code">Reference: <t t-esc="data['bom'].code"/></h6>
                    </div>
                </div>
                <t t-set="currency" t-value="data['currency']"/>
                <div class="row">
                    <div class="col-lg-12">
                        <div class="mt16">
                            <table width="100%" class="o_mrp_bom_expandable">
                                <thead>
                                    <tr>
                                        <th>Product</th>
                                        <th name="th_mrp_bom_h">BoM</th>
                                        <th class="text-right">Quantity</th>
                                        <th class="text-left" groups="uom.group_uom">Unit of Measure</th>
                                        <th t-if="data['report_structure'] != 'bom_structure'" class="o_mrp_prod_cost text-right" title="This is the cost defined on the product.">Product Cost</th>
                                        <th t-if="data['report_structure'] != 'bom_structure'" class="o_mrp_bom_cost text-right" title="This is the cost based on the BoM of the product. It is computed by summing the costs of the components and operations needed to build the product.">BoM Cost</th>
                                        <th t-if="data['report_type'] == 'html' and data['has_attachments']" class="o_mrp_has_attachments" title="Files attached to the product">Attachments</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <span><a href="#" t-if="data['report_type'] == 'html'" t-att-data-res-id="data['product'].id" t-att-data-model="data['product']._name" class="o_mrp_bom_action"><t t-esc="data['bom_prod_name']"/></a><t t-else="" t-esc="data['bom_prod_name']"/></span>
                                        </td>
                                        <td name="td_mrp_bom">
                                            <div><a href="#" t-if="data['report_type'] == 'html'" t-att-data-res-id="data['bom'].id" t-att-data-model="'mrp.bom'" class="o_mrp_bom_action"><t t-esc="data['code']"/></a><t t-else="" t-esc="data['code']"/></div>
                                        </td>
                                        <td class="text-right"><span><t t-esc="data['bom_qty']" t-options='{"widget": "float", "decimal_precision": "Product Unit of Measure"}'/></span></td>
                                        <td groups="uom.group_uom"><span><t t-esc="data['bom'].product_uom_id.name"/></span></td>
                                        <td t-if="data['report_structure'] != 'bom_structure'" class="o_mrp_prod_cost text-right">
                                            <span><t t-esc="data['price']" t-options='{"widget": "monetary", "display_currency": currency}'/></span>
                                        </td>
                                        <td t-if="data['report_structure'] != 'bom_structure'" class="o_mrp_bom_cost text-right">
                                            <span><t t-esc="data['bom_cost']" t-options='{"widget": "monetary", "display_currency": currency}'/></span>
                                        </td>
                                        <td t-if="data['report_type'] == 'html'">
                                            <span>
                                                <t t-if="data['attachments']">
                                                    <a href="#" role="button" t-att-data-res-id="data['attachments'].ids" t-att-data-model="'mrp.document'" class="o_mrp_show_attachment_action fa fa-fw o_button_icon fa-files-o"/>
                                                </t>
                                            </span>
                                        </td>
                                    </tr>
                                    <t t-if="data['report_type'] == 'html'" t-call="mrp.report_mrp_bom_line"/>
                                    <t t-if="data['report_type'] == 'pdf'" t-call="mrp.report_mrp_bom_pdf_line"/>
                                </tbody>
                                <tfoot>
                                    <tr t-if="data['report_structure'] != 'bom_structure'" class="o_mrp_prod_cost">
                                        <td></td>
                                        <t t-foreach="range(data.get('extra_column_count', 0))" t-as="index">
                                            <td/>
                                        </t>
                                        <td name="td_mrp_bom_f" class="text-right">
                                            <span><t t-if="data['byproducts']" t-esc="data['bom_prod_name']"/></span>
                                        </td>
                                        <td class="text-right"><span><strong>Unit Cost</strong></span></td>
                                        <td groups="uom.group_uom"><span><t t-esc="data['bom'].product_uom_id.name"/></span></td>
                                        <td class="text-right">
                                            <span><t t-esc="data['price']/data['bom_qty']" t-options='{"widget": "monetary", "display_currency": currency}'/></span>
                                        </td>
                                        <td class="text-right">
                                            <span><t t-esc="data['cost_share'] * data['total'] / data['bom_qty']" t-options='{"widget": "monetary", "display_currency": currency}'/></span>
                                        </td>
                                    </tr>
                                    <t t-if="data['report_structure'] != 'bom_structure'" t-foreach="data['byproducts']" t-as="byproduct">
                                        <tr class="o_mrp_bom_cost">
                                            <td/>
                                            <t t-foreach="range(data.get('extra_column_count', 0))" t-as="index">
                                                <td/>
                                            </t>
                                            <td name="td_mrp_bom_byproducts_f" class="text-right">
                                                <span><t t-esc="byproduct['product_name']"/></span>
                                            </td>
                                            <td class="text-right"><span><strong>Unit Cost</strong></span></td>
                                            <td groups="uom.group_uom"><span><t t-esc="byproduct['product_uom']"/></span></td>
                                            <td class="text-right">
                                                <span><t t-esc="byproduct['product_cost'] / byproduct['product_qty']" t-options='{"widget": "monetary", "display_currency": currency}'/></span>
                                            </td>
                                            <td class="text-right">
                                                <span><t t-esc="byproduct['cost_share'] * data['total'] / byproduct['product_qty']" t-options='{"widget": "monetary", "display_currency": currency}'/></span>
                                            </td>
                                        </tr>
                                    </t>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </t>
            <t t-else="">
                <h1 class="text-center">No data available.</h1>
            </t>
        </div>
    </template>

    <template id="report_mrp_bom_line">
        <t t-set="currency" t-value="data['currency']"/>
        <t t-foreach="data['components']" t-as="l">
            <t t-set="space_td" t-value="'margin-left: '+ str(l['level'] * 20) + 'px;'"/>
            <tr class="o_mrp_bom_report_line" t-att-data-id="l['child_bom']" t-att-parent_id="l['parent_id']" t-att-data-line="l['line_id']" t-att-data-product_id="l['prod_id']" t-att-data-qty="l['prod_qty']" t-att-data-level="l['level']">
                <td>
                    <div t-att-style="space_td">
                        <t t-if="l['child_bom']">
                            <div t-att-data-function="'get_bom'" class="o_mrp_bom_unfoldable fa fa-fw fa-caret-right" style="display:inline-block;" role="img" aria-label="Unfold" title="Unfold"/>
                        </t>
                        <div t-att-class="None if l['child_bom'] else 'o_mrp_bom_no_fold'" style="display:inline-block;">
                            <a href="#" t-att-data-res-id="l['prod_id']" t-att-data-model="'product.product'" class="o_mrp_bom_action"><t t-esc="l['prod_name']"/></a>
                        </div>
                        <t t-if="l['phantom_bom']">
                            <div class="fa fa-dropbox" title="This is a BoM of type Kit!" role="img" aria-label="This is a BoM of type Kit!"/>
                        </t>
                    </div>
                </td>
                <td name="td_mrp_bom">
                    <div>
                        <a href="#" t-att-data-res-id="l['child_bom']" t-att-data-model="'mrp.bom'" class="o_mrp_bom_action"><t t-esc="l['code']"/></a>
                  </div>
                </td>
                <td class="text-right"><span><t t-esc="l['prod_qty']" t-options='{"widget": "float", "decimal_precision": "Product Unit of Measure"}'/></span></td>
                <td groups="uom.group_uom"><span><t t-esc="l['prod_uom']"/></span></td>
                <td class="o_mrp_prod_cost text-right">
                    <span t-esc="l['prod_cost']" t-options='{"widget": "monetary", "display_currency": currency}'/>
                </td>
                <td class="o_mrp_bom_cost text-right">
                    <span t-esc="l['total']" t-options='{"widget": "monetary", "display_currency": currency}'/>
                </td>
                <td>
                    <span>
                        <t t-if="l['attachments']">
                            <a href="#" role="button" t-att-data-res-id="l['attachments'].ids" t-att-data-model="'mrp.document'" class="o_mrp_show_attachment_action fa fa-fw o_button_icon fa-files-o"/>
                        </t>
                    </span>
                </td>
            </tr>
        </t>
        <t t-if="data['operations']" name="operations">
            <t t-set="space_td" t-value="'margin-left: '+ str(data['level'] * 20) + 'px;'"/>
            <tr class="o_mrp_bom_report_line o_mrp_bom_cost" t-att-data-product_id="data['product'].id" t-att-data-id="'operation-' + str(data['bom'].id)" t-att-data-bom-id="data['bom'].id" t-att-parent_id="data['bom'].id" t-att-data-qty="data['bom_qty']" t-att-data-level="data['level']">
                <td name="td_opr">
                    <span t-att-style="space_td"/>
                    <span class="o_mrp_bom_unfoldable fa fa-fw fa-caret-right" t-att-data-function="'get_operations'" role="img" aria-label="Unfold" title="Unfold"/>
                    Operations
                </td>
                <t t-foreach="range(data.get('extra_column_count', 0))" t-as="index">
                    <td/>
                </t>
                <td/>
                <td class="text-right">
                    <span t-esc="data['operations_time']" t-options='{"widget": "float_time"}'/>
                </td>
                <td groups="uom.group_uom"><span>Minutes</span></td>
                <td/>
                <td class="o_mrp_bom_cost text-right">
                    <span t-esc="data['operations_cost']" t-options='{"widget": "monetary", "display_currency": currency}'/>
                </td>
                <td/>
            </tr>
        </t>
        <t t-if="data['byproducts']">
            <t t-set="space_td" t-value="'margin-left: '+ str(data['level'] * 20) + 'px;'"/>
            <tr class="o_mrp_bom_report_line o_mrp_bom_cost" t-att-data-id="'byproduct-' + str(data['bom'].id)" t-att-data-bom-id="data['bom'].id" t-att-parent_id="data['bom'].id" t-att-data-qty="data['bom_qty']" t-att-data-level="data['level']" t-att-data-total="data['total']">
                <td name="td_byproducts">
                    <span t-att-style="space_td"/>
                    <span class="o_mrp_bom_unfoldable fa fa-fw fa-caret-right" t-att-data-function="'get_byproducts'" role="img" aria-label="Unfold" title="Unfold"/>
                    By-Products
                </td>
                <t t-foreach="range(data.get('extra_column_count', 0))" t-as="index">
                    <td/>
                </t>
                <td/>
                <td class="text-right">
                    <span t-esc="data['byproducts_total']"/>
                </td>
                <td groups="uom.group_uom"/>
                <td/>
                <td class="text-right">
                    <span t-esc="data['byproducts_cost']" t-options='{"widget": "monetary", "display_currency": currency}'/>
                </td>
            </tr>
        </t>
    </template>

    <template id="report_mrp_operation_line">
      <t t-set="currency" t-value="data['currency']"/>
      <t t-foreach="data['operations']" t-as="op">
          <t t-set="space_td" t-value="'margin-left: '+ str(op['level'] * 20) + 'px;'"/>
          <tr class="o_mrp_bom_report_line o_mrp_bom_cost" t-att-parent_id="'operation-' + str(data['bom_id'])">
              <td name="td_opr_line">
                  <span t-att-style="space_td"/>
                  <a href="#" t-att-data-res-id="op['operation'].id" t-att-data-model="'mrp.routing.workcenter'" class="o_mrp_bom_action"><t t-esc="op['name']"/></a>
              </td>
              <t t-foreach="range(data.get('extra_column_count', 0))" t-as="index">
                  <td/>
              </t>
              <td/>
              <td class="text-right">
                  <span t-esc="op['duration_expected']" t-options='{"widget": "float_time"}'/>
              </td>
              <td groups="uom.group_uom"><span>Minutes</span></td>
              <td/>
              <td class="o_mrp_bom_cost text-right">
                  <span t-esc="op['total']" t-options='{"widget": "monetary", "display_currency": currency}'/>
              </td>
              <td/>
          </tr>
      </t>
    </template>

    <template id="report_mrp_byproduct_line">
        <t t-set="currency" t-value="data['currency']"/>
        <t t-foreach="data['byproducts']" t-as="byproduct">
            <t t-set="space_td" t-value="'margin-left: '+ str(byproduct['level'] * 20) + 'px;'"/>
            <tr class="o_mrp_bom_report_line o_mrp_bom_cost"  t-att-parent_id="'byproduct-' + str(data['bom_id'])">
                <td name="td_byproduct_line">
                    <span t-att-style="space_td"/>
                    <a href="#" t-att-data-res-id="byproduct['product_id'].id" t-att-data-model="byproduct['product_id']._name" class="o_mrp_bom_action"><t t-esc="byproduct['product_name']"/></a>
                </td>
                <t t-foreach="range(data.get('extra_column_count', 0))" t-as="index">
                    <td/>
                </t>
                <td/>
                <td class="text-right">
                    <span t-esc="byproduct['product_qty']"/>
                </td>
                <td groups="uom.group_uom"><span t-esc="byproduct['product_uom']"/></td>
                <td class="text-right">
                    <span t-esc="byproduct['product_cost']" t-options='{"widget": "monetary", "display_currency": currency}'/>
                </td>
                <td class="text-right">
                    <span t-esc="byproduct['bom_cost']" t-options='{"widget": "monetary", "display_currency": currency}'/>
                </td>
                <td/>
            </tr>
        </t>
    </template>

    <template id="report_mrp_bom_pdf">
        <t t-call="web.html_container">
            <t t-call="mrp.report_mrp_bom"/>
        </t>
    </template>

    <template id="report_mrp_bom_pdf_line">
      <t t-set="currency" t-value="data['currency']"/>
      <t t-foreach="data['lines']" t-as="l">
          <t t-set="space_td" t-value="'margin-left: '+ str(l['level'] * 20) + 'px;'"/>
          <tr t-if="data['report_structure'] != 'bom_structure' or l['type'] not in ['operation', 'byproduct']">
              <td>
                  <div t-att-style="space_td">
                    <div><t t-esc="l['name']"/></div>
                  </div>
              </td>
              <td name="td_mrp_code">
                  <div t-if="l.get('code')" t-esc="l['code']" />
              </td>
              <td class="text-right">
                  <span>
                      <t t-if="l['type'] == 'operation'" t-esc="l['quantity']" t-options='{"widget": "float_time"}'/>
                      <t t-if="l['type'] in ['bom', 'byproduct']" t-esc="l['quantity']" t-options='{"widget": "float", "decimal_precision": "Product Unit of Measure"}'/>
                  </span>
              </td>
              <td groups="uom.group_uom"><span><t t-esc="l['uom']"/></span></td>
              <td t-if="data['report_structure'] != 'bom_structure'" class="o_mrp_prod_cost text-right">
                  <span t-if="'prod_cost' in l" t-esc="l['prod_cost']" t-options='{"widget": "monetary", "display_currency": currency}'/>
              </td>
              <td t-if="data['report_structure'] != 'bom_structure'" class="o_mrp_bom_cost text-right">
                  <span t-esc="l['bom_cost']" t-options='{"widget": "monetary", "display_currency": currency}'/>
              </td>
          </tr>
      </t>
    </template>

    <template id="report_bom_structure">
        <t t-set="data_report_landscape" t-value="True"/>
        <t t-call="web.basic_layout">
            <t t-call-assets="mrp.assets_common" t-js="False"/>
            <t t-foreach="docs" t-as="data">
                <div class="page">
                    <t t-call="mrp.report_mrp_bom"/>
                </div>
                <p style="page-break-before:always;"> </p>
            </t>
        </t>
    </template>
</odoo>
