# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock
# 
# Translators:
# <PERSON> <<EMAIL>>, 2021
# Андрей <PERSON>ев <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <and<PERSON>.sa<PERSON>@me.com>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# Константин <PERSON>н <<EMAIL>>, 2021
# Collex100, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> <koro<PERSON><PERSON>@gmail.com>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <naws<PERSON><EMAIL>>, 2021
# <PERSON>ina <PERSON>androva <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# Martin Trigaux, 2022
# valmasone, 2022
# <AUTHOR> <EMAIL>, 2022
# Alena Vlasova, 2023
# alenafairy, 2023
# Walid Hanniche, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:18+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: Walid Hanniche, 2023\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"\n"
"\n"
"%s --> Product UoM is %s (%s) - Move UoM is %s (%s)"
msgstr ""
"\n"
"\n"
" %s --> ед.изм. продукта %s (%s) - ед.изм. перемещения %s (%s)"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"\n"
"\n"
"Blocking: %s"
msgstr ""
"\n"
"\n"
"Блокировка: %s "

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid ""
"\n"
"\n"
"Transfers %s: You cannot validate these transfers if no quantities are reserved nor done. To force these transfers, switch in edit more and encode the done quantities."
msgstr ""
"\n"
"\n"
"Перемещения %s: Вы не можете подтвердить эти переводы, если не зарезервировано количество, которое не было выполнено. Чтобы принудительно перенести их, переключитесь в режим редактирования и запишите выбранное количество."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid ""
"\n"
"\n"
"Transfers %s: You need to supply a Lot/Serial number for products %s."
msgstr ""
"\n"
"\n"
"Перемещения%s: Необходимо указать партию/серийный номер для продуктов: %s."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"\n"
"(%s) exists in location %s"
msgstr ""
"\n"
"(%s) существует в месте хранения %s"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__state
msgid ""
" * Draft: The transfer is not confirmed yet. Reservation doesn't apply.\n"
" * Waiting another operation: This transfer is waiting for another operation before being ready.\n"
" * Waiting: The transfer is waiting for the availability of some products.\n"
"(a) The shipping policy is \"As soon as possible\": no product could be reserved.\n"
"(b) The shipping policy is \"When all products are ready\": not all the products could be reserved.\n"
" * Ready: The transfer is ready to be processed.\n"
"(a) The shipping policy is \"As soon as possible\": at least one product has been reserved.\n"
"(b) The shipping policy is \"When all products are ready\": all product have been reserved.\n"
" * Done: The transfer has been processed.\n"
" * Cancelled: The transfer has been cancelled."
msgstr ""
"* Черновик: перевод еще не подтвержден. Бронирование не действует. * "
"Ожидание другой операции: этот перевод ожидает следующей операции, прежде "
"чем будет готов. * Ожидание: перевод ожидает наличия некоторых товаров. (а) "
"Политика доставки - «Как можно скорее»: ни один продукт не может быть "
"зарезервирован. (б) Политика доставки: «Когда все продукты готовы»: не все "
"продукты можно зарезервировать. * Готово: перевод готов к обработке. (а) "
"Политика доставки - «Как можно скорее»: как минимум один продукт был "
"зарезервирован. (б) Политика доставки: «Когда все продукты будут готовы»: "
"все продукты зарезервированы. * Выполнено: перевод был обработан. * "
"Отменено: перевод отменен."

#. module: stock
#: code:addons/stock/models/stock_production_lot.py:0
#, python-format
msgid " - Product: %s, Serial Number: %s"
msgstr " - Продукт: %s, Серийный номер: %s"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__cyclic_inventory_frequency
msgid ""
" When different than 0, inventory count date for products stored at this "
"location will be automatically set at the defined frequency."
msgstr ""
"При значении, отличном от 0, дата инвентаризации продукции, хранящейся в "
"данном месте, будет автоматически устанавливаться с заданной периодичностью."

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "%(warehouse)s: Supply Product from %(supplier)s"
msgstr "%(warehouse)s: Поставляемый продукт от %(supplier)s"

#. module: stock
#: code:addons/stock/models/stock_package_type.py:0
#: code:addons/stock/models/stock_storage_category.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (копия)"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid ""
"%s use default source or destination locations from warehouse %s that will "
"be archived."
msgstr ""
"%s использовать места хранения со склада по умолчанию %s, которые будут "
"заархивированы."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
msgid "&gt;"
msgstr "&gt;"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_inventory
msgid "'Count Sheet'"
msgstr "'Счетная ведомость'"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_delivery
msgid ""
"'Delivery Slip - %s - %s' % (object.partner_id.name or '', object.name)"
msgstr ""
"'Квитанция о доставке - %s - %s' % (object.partner_id.name or '', "
"object.name)"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_location_barcode
msgid "'Location - %s' % object.name"
msgstr "'Местоположение - %s' % object.name"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_lot_label
msgid "'Lot-Serial - %s' % object.name"
msgstr "'Партия/Серийный - %s' % object.name"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_picking_type_label
msgid "'Operation-type - %s' % object.name"
msgstr "'Тип операции - %s' % object.name"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_picking
msgid ""
"'Picking Operations - %s - %s' % (object.partner_id.name or '', object.name)"
msgstr ""
"'Операции сортировки - %s - %s' % (object.partner_id.name or '', "
"object.name)"

#. module: stock
#: code:addons/stock/models/stock_production_lot.py:0
#, python-format
msgid "(copy of) %s"
msgstr "(копия) %s"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__state
#: model:ir.model.fields,help:stock.field_stock_move_line__state
msgid ""
"* New: When the stock move is created and not yet confirmed.\n"
"* Waiting Another Move: This state can be seen when a move is waiting for another one, for example in a chained flow.\n"
"* Waiting Availability: This state is reached when the procurement resolution is not straight forward. It may need the scheduler to run, a component to be manufactured...\n"
"* Available: When products are reserved, it is set to 'Available'.\n"
"* Done: When the shipment is processed, the state is 'Done'."
msgstr ""
"* Новый: когда перемещение запаса создается и еще не подтверждено.\n"
"* Ожидание новой операции: Это состояние можно увидеть, когда движение ожидает другого, например, в цепном потоке.\n"
"* Ожидание доступности: Это состояние достигается, когда разрешение на закупку не является прямым. Может понадобиться планировщик для запуска, компонент для производства ...\n"
"* Доступно: если продукт зарезервирован, он имеет значение 'Доступен'.\n"
"* Выполнено: Когда обработка отправлена, состояние 'Выполнено'."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__usage
msgid ""
"* Vendor Location: Virtual location representing the source location for products coming from your vendors\n"
"* View: Virtual location used to create a hierarchical structures for your warehouse, aggregating its child locations ; can't directly contain products\n"
"* Internal Location: Physical locations inside your own warehouses,\n"
"* Customer Location: Virtual location representing the destination location for products sent to your customers\n"
"* Inventory Loss: Virtual location serving as counterpart for inventory operations used to correct stock levels (Physical inventories)\n"
"* Production: Virtual counterpart location for production operations: this location consumes the components and produces finished products\n"
"* Transit Location: Counterpart location that should be used in inter-company or inter-warehouses operations"
msgstr ""
"* Местоположение поставщика: виртуальное местоположение, представляющее "
"исходное местоположение для продуктов, поступающих от ваших поставщиков. * "
"Просмотр: виртуальное местоположение, используемое для создания "
"иерархических структур для вашего склада, объединяя его дочерние "
"местоположения; не может напрямую содержать продукты * Внутреннее "
"местоположение: физические местоположения внутри ваших собственных складов, "
"* Местоположение клиента: виртуальное местоположение, представляющее "
"конечное местоположение для продуктов, отправленных вашим клиентам * Потеря "
"запасов: виртуальное местоположение, служащее аналогом для операций с "
"запасами, используемых для корректировки уровней запасов (Физические запасы)"
" * Производство: виртуальное место-аналог для производственных операций: это"
" местоположение потребляет сырье и производит готовую продукцию * Транзитное"
" местоположение: местоположение-контрагент, которое должно использоваться в "
"межфирменных или межскладских операциях"

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid "+ %d day(s)"
msgstr "+ %d день (дни)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid ", max:"
msgstr "Макс:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "-&gt;"
msgstr "-&gt;"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid ""
".\n"
"            Manual actions may be needed."
msgstr ". Возможно понадобится ручная действие."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__day
msgid "1 Day"
msgstr "1 день"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__month
msgid "1 Month"
msgstr "1 месяц"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__week
msgid "1 Week"
msgstr "1 неделя"

#. module: stock
#: code:addons/stock/models/stock_scrap.py:0
#, python-format
msgid ": Insufficient Quantity To Scrap"
msgstr ": Недостаточное количество для утилизации"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid ""
"<br/>\n"
"                    <strong>Current Inventory: </strong>"
msgstr ""
"<br/>\n"
"                    <strong>Текущие запасы: </strong>"

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid ""
"<br>A need is created in <b>%s</b> and a rule will be triggered to fulfill "
"it."
msgstr ""
"<br> Необходимость создается <b>в %s </b> и запускаться правило для его "
"выполнения."

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid ""
"<br>If the products are not available in <b>%s</b>, a rule will be triggered"
" to bring products in this location."
msgstr ""
"<br>Если продукция недоступна в <b>%s</b>, сработает правило для доставки "
"продукции в это место."

#. module: stock
#: model:mail.template,body_html:stock.mail_template_data_delivery_confirmation
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hello <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"        We are glad to inform you that your order has been shipped.\n"
"        <t t-if=\"hasattr(object, 'carrier_tracking_ref') and object.carrier_tracking_ref\">\n"
"            Your tracking reference is\n"
"            <strong>\n"
"            <t t-if=\"object.carrier_tracking_url\">\n"
"                <t t-set=\"multiple_carrier_tracking\" t-value=\"object.get_multiple_carrier_tracking()\"/>\n"
"                <t t-if=\"multiple_carrier_tracking\">\n"
"                    <t t-foreach=\"multiple_carrier_tracking\" t-as=\"line\">\n"
"                        <br/><a t-att-href=\"line[1]\" target=\"_blank\" t-out=\"line[0] or ''\"/>\n"
"                    </t>\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    <a t-attf-href=\"{{ object.carrier_tracking_url }}\" target=\"_blank\" t-out=\"object.carrier_tracking_ref or ''\"/>.\n"
"                </t>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                <t t-out=\"object.carrier_tracking_ref or ''\"/>.\n"
"            </t>\n"
"            </strong>\n"
"        </t>\n"
"        <br/><br/>\n"
"        Please find your delivery order attached for more details.<br/><br/>\n"
"        Thank you,\n"
"        <t t-if=\"user.signature\">\n"
"            <br/>\n"
"            <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>\n"
"        "
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_view_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"
msgstr "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/>\n"
"                                All products could not be reserved. Click on the \"Check Availability\" button to try to reserve products."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/>\n"
"                                Все продукты не могут быть зарезервированы. Нажмите кнопку \"Проверить доступность\" чтобы попытаться зарезервировать продукты."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_mrp_line
msgid ""
"<i class=\"fa fa-fw fa-caret-right\" role=\"img\" aria-label=\"Unfold\" "
"title=\"Unfold\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-caret-right\" role=\"img\" aria-label=\"Unfold\" "
"title=\"Unfold\"/>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
msgid ""
"<span attrs=\"{'invisible': ['|', ('state', '=', 'done'), "
"('from_immediate_transfer', '=', True)]}\"> / </span>"
msgstr ""
"<span attrs=\"{'invisible': ['|', ('state', '=', 'done'), "
"('from_immediate_transfer', '=', True)]}\"> / </span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid ""
"<span class=\"d-none d-sm-block o_print_label_text\">Print Label</span>\n"
"                                        <span class=\"d-block d-sm-none fa fa-print\"/>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid ""
"<span class=\"d-none d-sm-block o_print_label_text\">Print Labels</span>\n"
"                                        <span class=\"d-block d-sm-none fa fa-print\"/>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Установленные здесь значения "
"зависят от конкретной компании.\" groups=\"base.group_multi_company\"/>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Forecasted</span>"
msgstr "<span class=\"o_stat_text\">Прогноз</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid ""
"<span class=\"o_stat_text\">In:</span>\n"
"                                <span class=\"o_stat_text\">Out:</span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid ""
"<span class=\"o_stat_text\">Min:</span>\n"
"                                <span class=\"o_stat_text\">Max:</span>"
msgstr ""
"<span class=\"o_stat_text\">Мин :</span>\n"
"<span class=\"o_stat_text\">Макс:</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">On Hand</span>"
msgstr "<span class=\"o_stat_text\">В наличии</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "<span class=\"o_stat_text\">Operations</span>"
msgstr "<span class=\"o_stat_text\">операции</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "<span class=\"o_stat_text\">Transfers</span>"
msgstr "<span class=\"o_stat_text\">Перемещения</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_mrp_line
msgid ""
"<span role=\"img\" class=\"o_stock_reports_stream\" title=\"Traceability "
"Report\" aria-label=\"Traceability Report\"><i class=\"fa fa-fw fa-level-up "
"fa-rotate-270\"/></span>"
msgstr ""
"<span role=\"img\" class=\"o_stock_reports_stream\" title=\"Traceability "
"Report\" aria-label=\"Traceability Report\"><i class=\"fa fa-fw fa-level-up "
"fa-rotate-270\"/></span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Customer Address:</strong></span>"
msgstr "<span><strong>Адрес Заказчика:</strong></span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Delivery Address:</strong></span>"
msgstr "<span><strong>Адрес Доставки:</strong></span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Vendor Address:</strong></span>"
msgstr "<span><strong>Адрес Поставщика:</strong></span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Warehouse Address:</strong></span>"
msgstr "<span><strong>Адрес Склада:</strong></span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
msgid "<span>Assign Serial Numbers</span>"
msgstr "<span>Назначьте серийные номера</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
msgid "<span>Clear All</span>"
msgstr "Удалить все"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_lot_label
msgid "<span>LN/SN:</span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<span>New</span>"
msgstr "<span>Новое</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "<span>Package Type: </span>"
msgstr "<span>Тип упаковки: </span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_report_delivery_no_package_section_line
msgid "<span>Products with no package assigned</span>"
msgstr "<span>Продукты, для которых не указана упаковка</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<span>Remaining quantities not yet delivered:</span>"
msgstr "<span>Оставшееся количество, которое еще не поставлено:</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "<span>kg</span>"
msgstr "<span>кг</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "<span>×</span>"
msgstr "<span>×</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_head
msgid ""
"<strong>\n"
"                The done move line has been corrected.\n"
"            </strong>"
msgstr "<strong>Выполненный строку перемещение было исправлено.</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Available Quantity</strong>"
msgstr "<strong>Доступное количество</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Counted Quantity</strong>"
msgstr "<strong>Подсчитанное количество</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<strong>Delivered</strong>"
msgstr "<strong>Доставлено</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid ""
"<strong>Due to some stock moves done between your initial update of the "
"quantity and now, the difference of quantity is not consistent "
"anymore.</strong>"
msgstr ""
"<strong>Из-за некоторых перемещений запасов, произведенных между вашим "
"первоначальным обновлением количества и настоящим моментом, разница в "
"количестве больше не является согласованной.</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>From</strong>"
msgstr "<strong>Из</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Location</strong>"
msgstr "<strong>Место Размещения</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Lot/Serial Number</strong>"
msgstr "<strong>Партия/Серийный Номер</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_warehouse_orderpoint_kanban
msgid "<strong>Max qty :</strong>"
msgstr "<strong>Макс кол-во:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_warehouse_orderpoint_kanban
msgid "<strong>Min qty :</strong>"
msgstr "<strong>Мин кол-во:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>On hand Quantity</strong>"
msgstr "<strong>Количество в наличии</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Order:</strong>"
msgstr "<strong>Заказ:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<strong>Ordered</strong>"
msgstr "<strong>Заказано</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "<strong>Package Type:</strong>"
msgstr "<strong>Тип упаковки:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Package</strong>"
msgstr "<strong>Упаковка</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Product Barcode</strong>"
msgstr "<strong>Штрих-код товара</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Product</strong>"
msgstr "<strong>Продукт</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Quantity</strong>"
msgstr "<strong>Количество</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Scheduled Date:</strong>"
msgstr "<strong>Запланированная дата:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<strong>Shipping Date:</strong>"
msgstr "<strong>Дата отгрузки:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<strong>Signature</strong>"
msgstr "<strong>Подпись</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Status:</strong>"
msgstr "<strong>Статус:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_head
msgid "<strong>The initial demand has been updated.</strong>"
msgstr "<strong>Начальный спрос был восстановлен.</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>To</strong>"
msgstr "<strong>к</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "<strong>Tracked product(s):</strong>"
msgstr "<strong>Отслеживаемый продукт (продукты):</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
msgid "<strong>Where do you want to send the products ?</strong>"
msgstr "<strong>Куда вы хотите отправить товары?</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
msgid "? This may lead to inconsistencies in your inventory."
msgstr "? Это может привести к несоответствиям на складе."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_barcode_uniq
msgid "A barcode can only be assigned to one package type !"
msgstr "Штрих-код может быть присвоен только одному типу упаковки!"

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid "A done move line should never have a reserved quantity."
msgstr ""
"Выполненный строку перемещения никогда не должен иметь зарезервированную "
"количество."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__detailed_type
#: model:ir.model.fields,help:stock.field_product_template__detailed_type
#: model:ir.model.fields,help:stock.field_stock_move__product_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""
"Складируемый - это продукт, для которого вы отслеживаете остатки на складе. "
"Необходимо установить приложение Склад. Потребляемый - это продукт, для "
"которого остатки не учитываются. Услуга - это нематериальный продукт, "
"который вы предоставляете."

#. module: stock
#: model:res.groups,name:stock.group_warning_stock
msgid "A warning can be set on a partner (Stock)"
msgstr "Предупреждение может быть настроено на партнера (Склад)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__action
msgid "Action"
msgstr "Действие"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_needaction
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_needaction
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_needaction
msgid "Action Needed"
msgstr "Требует внимания"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__active
#: model:ir.model.fields,field_description:stock.field_stock_location_route__active
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__active
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__active
#: model:ir.model.fields,field_description:stock.field_stock_rule__active
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__active
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__active
msgid "Active"
msgstr "Активно"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_ids
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_ids
msgid "Activities"
msgstr "Действия"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_exception_decoration
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Оформление исключения активности"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_state
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_state
msgid "Activity State"
msgstr "Статус действия"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_type_icon
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_type_icon
msgid "Activity Type Icon"
msgstr "Иконка типа действия"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Add a Product"
msgstr "Добавить продукт"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_production_lot_form
msgid "Add a lot/serial number"
msgstr "Добавить партию / серийный номер"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid "Add a new location"
msgstr "Добавить новое местонахождение"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_routes_form
msgid "Add a new route"
msgstr "Добавить новый маршрут"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_storage_category
msgid "Add a new storage category"
msgstr "Добавить категорию мест хранения"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid ""
"Add an internal note that will be printed on the Picking Operations sheet"
msgstr ""
"Добавить внутреннюю заметку, которая будет напечатана на листе операций по "
"комплектованию"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__group_stock_adv_location
msgid ""
"Add and customize route operations to process product moves in your warehouse(s): e.g. unload > quality control > stock for incoming products, pick > pack > ship for outgoing products. \n"
" You can also set putaway strategies on warehouse locations in order to send incoming products into specific child locations straight away (e.g. specific bins, racks)."
msgstr ""
"Добавьте и настройте марщруты, чтобы обрабатывать перемещение товара в ваших"
" складских помещениях: например, выгрузка > контроль качества > место для "
"входящих товаров, комплектование > упаковка > отгрузка исходящих товаров. Вы"
" также можете установить правила перемещения в места хранения, чтобы сразу "
"отправлять входящие товары в конкретные вложенные места (например, "
"специальные контейнеры, стеллажи)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Add and customize route operations to process product moves in your "
"warehouse(s): e.g. unload > quality control > stock for incoming products, "
"pick > pack > ship for outgoing products. You can also set putaway "
"strategies on warehouse locations in order to send incoming products into "
"specific child locations straight away (e.g. specific bins, racks)."
msgstr ""
"Добавьте и настройте марщруты, чтобы обрабатывать перемещение товара в ваших"
" складских помещениях: например, выгрузка > контроль качества > место для "
"входящих товаров, комплектование > упаковка > отгрузка исходящих товаров. Вы"
" также можете установить правила перемещения в места хранения, чтобы сразу "
"отправлять входящие товары в конкретные вложенные места (например, "
"специальные контейнеры, стеллажи)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Add quality checks to your transfer operations"
msgstr "Добавить проверки качества для операций перемещения"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Additional Info"
msgstr "Доп. инфо"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__comment
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Additional Information"
msgstr "Дополнительная информация"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__partner_id
msgid "Address"
msgstr "Адрес"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__partner_address_id
msgid "Address where goods should be delivered. Optional."
msgstr "Адрес, куда нужно доставлять товар. Необязательно."

#. module: stock
#: model:res.groups,name:stock.group_stock_manager
msgid "Administrator"
msgstr "Администратор"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Advanced Scheduling"
msgstr "Детальное планирование"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__procure_method__make_to_order
msgid "Advanced: Apply Procurement Rules"
msgstr "Дополнительно: Применить правила снабжения"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "All"
msgstr "Все"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_picking_action_picking_type
msgid "All Transfers"
msgstr "Все Перемещения"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__procurement_group__move_type__one
msgid "All at once"
msgstr "Всё сразу"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"All our contractual relations will be governed exclusively by United States "
"law."
msgstr ""
"Все наши договорные отношения будут регулироваться исключительно "
"законодательством страны."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__returned_move_ids
msgid "All returned moves"
msgstr "Все возвращенные перемещения"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Allocation"
msgstr "Планирование"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__allow_new_product
msgid "Allow New Product"
msgstr "Разрешить новый продукт"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_storage_category__allow_new_product__mixed
msgid "Allow mixed products"
msgstr "Разрешить смешанные продукты"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__allowed_location_ids
msgid "Allowed Location"
msgstr "Разрешенное местоположение"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Annual Inventory Day and Month"
msgstr "Дата ежегодной инвентаризации"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__annual_inventory_month
#: model:ir.model.fields,field_description:stock.field_res_config_settings__annual_inventory_month
msgid "Annual Inventory Month"
msgstr "Месяц ежегодной инвентаризации"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company__annual_inventory_month
#: model:ir.model.fields,help:stock.field_res_config_settings__annual_inventory_month
msgid ""
"Annual inventory month for products not in a location with a cyclic "
"inventory date. Set to no month if no automatic annual inventory."
msgstr ""
"Месяц ежегодной инвентаризации для товаров, не находящихся в месте с "
"циклической датой инвентаризации. Установите значение \"Нет месяца\", если "
"автоматическая годовая инвентаризация не проводится."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Applicability"
msgstr "Применимость"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Applicable On"
msgstr "Применяется к"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__packaging_selectable
msgid "Applicable on Packaging"
msgstr "Применимо к упаковке"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__product_selectable
msgid "Applicable on Product"
msgstr "Применимо к продукту"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__product_categ_selectable
msgid "Applicable on Product Category"
msgstr "Применимо к категории продуктов"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__warehouse_selectable
msgid "Applicable on Warehouse"
msgstr "Применяется на склад"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_adjustment_name_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Apply"
msgstr "Применить"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__route_ids
msgid ""
"Apply specific route(s) for the replenishment instead of product's default "
"routes."
msgstr ""
"Примените конкретные маршруты для пополнения, а не маршруты по умолчанию для"
" товара."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__4
msgid "April"
msgstr "Апрель"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_warehouse_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Archived"
msgstr "Архивировано"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__move_type__direct
msgid "As soon as possible"
msgstr "Как можно быстрее"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/js/report_stock_reception.js:0
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
#, python-format
msgid "Assign"
msgstr "Назначить"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/report_stock_reception.xml:0
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
#, python-format
msgid "Assign All"
msgstr "Назначить всем"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__owner_id
msgid "Assign Owner"
msgstr "Назначить владельца"

#. module: stock
#: model:ir.actions.act_window,name:stock.act_assign_serial_numbers
#: model_terms:ir.ui.view,arch_db:stock.view_assign_serial_numbers
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
msgid "Assign Serial Numbers"
msgstr "Назначить серийные номера"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Assigned Moves"
msgstr "Назначенные перемещения"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__user_id
msgid "Assigned To"
msgstr "Ответственный"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__reservation_method__at_confirm
msgid "At Confirmation"
msgstr "При подтверждении"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_attachment_count
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_attachment_count
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_attachment_count
msgid "Attachment Count"
msgstr "Количество вложений"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_attribute_action
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Attributes"
msgstr "Атрибуты"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__8
msgid "August"
msgstr "Август"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse_orderpoint__trigger__auto
msgid "Auto"
msgstr "Авто"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Automate Orders"
msgstr "Автозаказ"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__auto
msgid "Automatic Move"
msgstr "Автоматическое перемещение"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__auto__transparent
msgid "Automatic No Step Added"
msgstr "Автоматически без добавления шагов"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Automatically open reception report when a receipt is validated."
msgstr ""

#. module: stock
#. openerp-web
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/static/src/xml/forecast_widget.xml:0
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__assigned
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__products_availability_state__available
#, python-format
msgid "Available"
msgstr "Доступно"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_search_form_view_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_product_search_form_view
msgid "Available Products"
msgstr "Доступные продукты"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__available_quantity
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Available Quantity"
msgstr "Доступное количество"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Available quantity should be set to zero before changing type"
msgstr "Перед изменением типа доступное количество необходимо обнулить."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__backorder_id
msgid "Back Order of"
msgstr "Дозаказ из"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__backorder_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Back Orders"
msgstr "Возвраты"

#. module: stock
#: model:ir.model,name:stock.model_stock_backorder_confirmation
msgid "Backorder Confirmation"
msgstr "Подтверждение Заказа Возврата"

#. module: stock
#: model:ir.model,name:stock.model_stock_backorder_confirmation_line
msgid "Backorder Confirmation Line"
msgstr "Строка подтверждения отложенного заказа"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__backorder_confirmation_line_ids
msgid "Backorder Confirmation Lines"
msgstr "Строки подтверждения отложенного заказа"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "Backorder creation"
msgstr "Создание заказа возврата"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_backorder
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Backorders"
msgstr "Возвраты"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__barcode
#: model:ir.model.fields,field_description:stock.field_stock_package_type__barcode
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__barcode
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Barcode"
msgstr "Штрих-код"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_wms_barcode_nomenclature_all
msgid "Barcode Nomenclatures"
msgstr "Номенклатуры штрих-кодов"

#. module: stock
#: model:ir.model,name:stock.model_barcode_rule
msgid "Barcode Rule"
msgstr "Правило штрих-кода"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_barcode
msgid "Barcode Scanner"
msgstr "Сканер штрих-кода"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_picking_batch
msgid "Batch Transfers"
msgstr "Групповые перемещения"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__reservation_method__by_date
msgid "Before scheduled date"
msgstr "Перед плановой датой"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"Below text serves as a suggestion and doesn’t engage Odoo S.A. "
"responsibility."
msgstr ""
"Приведенный ниже текст носит рекомендательный характер и не влечет за собой "
"ответственности со стороны компании."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_partner__picking_warn__block
msgid "Blocking Message"
msgstr "Блокирующее сообщение"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__quant_ids
msgid "Bulk Content"
msgstr "Основное Содержание"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_template__tracking__lot
msgid "By Lots"
msgstr "По Партиям"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_template__tracking__serial
msgid "By Unique Serial Number"
msgstr "По Уникальным Серийным Номерам"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__procure_method
msgid ""
"By default, the system will take from the stock in the source location and "
"passively wait for availability. The other possibility allows you to "
"directly create a procurement on the source location (and thus ignore its "
"current stock) to gather products. If we want to chain moves and have this "
"one to wait for the previous, this second option should be chosen."
msgstr ""
"По умолчанию, система будет брать из запаса в исходном месте и пассивно "
"ждать наличия. Другая возможность позволяет напрямую создать закупку на "
"исходном месте хранения (и таким образом игнорировать его текущего запаса), "
"чтобы собрать продукты. Если мы хотим, чтобы была цепь перемещения и один "
"должен дождаться предыдущего, то должен быть выбран второй вариант."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__active
msgid ""
"By unchecking the active field, you may hide a location without deleting it."
msgstr ""
"Убрав отметку с поля 'Активно', вы можете скрыть место хранения без его "
"удаления."

#. module: stock
#: model:product.product,name:stock.product_cable_management_box
#: model:product.template,name:stock.product_cable_management_box_product_template
msgid "Cable Management Box"
msgstr "Распределительная коробка для провода"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_calendar
msgid "Calendar View"
msgstr "Вид календаря"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Can't find any customer or supplier location."
msgstr "Не могу найти ни одного места хранения заказчика или поставщика."

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Can't find any generic route %s."
msgstr "Невозможно найти общий маршрут %s."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view2
#: model_terms:ir.ui.view,arch_db:stock.view_assign_serial_numbers
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_compute_wizard
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quantity_history
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rules_report
msgid "Cancel"
msgstr "Отмена"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__propagate_cancel
msgid "Cancel Next Move"
msgstr "Отменить следующий ход"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__cancel
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__cancel
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__cancel
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Cancelled"
msgstr "Отменено"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Cancelled Moves"
msgstr "отменены перемещения"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"Cannot set the done quantity from this stock move, work directly with the "
"move lines."
msgstr ""
"Невозможно установить проделанной количество из этого складского движения, "
"работайте непосредственно со строками перемещения."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__capacity_ids
msgid "Capacity"
msgstr "Вместимость"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "Capacity by Package"
msgstr "Вместимость по упаковкам"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "Capacity by Product"
msgstr "Вместимость по продукту"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Categorize your locations for smarter putaway rules"
msgstr "Распределение мест по категориям для более удобного хранения"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_uom_category_id
#: model:ir.model.fields,field_description:stock.field_stock_move__product_uom_category_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_uom_category_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__product_uom_category_id
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Category"
msgstr "Категория"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__route_from_categ_ids
#: model:ir.model.fields,field_description:stock.field_product_template__route_from_categ_ids
msgid "Category Routes"
msgstr "Категории технологических карт"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"Certain countries apply withholding at source on the amount of invoices, in "
"accordance with their internal legislation. Any withholding at source will "
"be paid by the client to the tax authorities. Under no circumstances can My "
"Company (Chicago) become involved in costs related to a country's "
"legislation. The amount of the invoice will therefore be due to My Company "
"(Chicago) in its entirety and does not include any costs relating to the "
"legislation of the country in which the client is located."
msgstr ""
"В некоторых странах в соответствии с внутренним законодательством при оплате"
" счетов удерживается сумма налога. Любые удержания выплачиваются клиентом в "
"налоговые органы. Ни при каких обстоятельствах компания не может быть "
"вовлечена в расходы, связанные с законодательством той или иной страны. "
"Таким образом, сумма счета будет причитаться компании в полном объеме и не "
"включает в себя никаких расходов, связанных с законодательством страны, в "
"которой находится клиент."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__move_dest_exists
msgid "Chained Move Exists"
msgstr "Связанное перемещение существует"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_change_product_quantity
#: model:ir.model,name:stock.model_stock_change_product_qty
msgid "Change Product Quantity"
msgstr "Изменить количество продукта"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__move_id
msgid "Change to a better name"
msgstr "Измените на лучшее название"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Changing %s is restricted, you can't do this operation."
msgstr ""

#. module: stock
#: code:addons/stock/models/product_strategy.py:0
#: code:addons/stock/models/stock_location.py:0
#: code:addons/stock/models/stock_orderpoint.py:0
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/models/stock_production_lot.py:0
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid ""
"Changing the company of this record is forbidden at this point, you should "
"rather archive it and create a new one."
msgstr ""
"Смена компании этой записи на данном этапе запрещена, лучше заархивируйте ее"
" и создайте новую."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Changing the operation type of this record is forbidden at this point."
msgstr "Изменение типа операции этой записи на данном этапе запрещено."

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid "Changing the product is only allowed in 'Draft' state."
msgstr "Изменение продукта разрешено только в состоянии «Черновик»."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Check Availability"
msgstr "Проверить доступность"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__has_packages
msgid "Check the existence of destination packages on move lines"
msgstr "Проверьте наличие пакетов предназначенных для строках перемещения"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__move_line_exist
msgid "Check the existence of pack operation on the picking"
msgstr "Проверьте наличие операции упаковки при упаковке"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__return_location
msgid "Check this box to allow using this location as a return location."
msgstr ""
"Отметьте, чтобы разрешить использование этого места хранения как  места "
"хранения возврата."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__scrap_location
#: model:ir.model.fields,help:stock.field_stock_move__scrapped
msgid ""
"Check this box to allow using this location to put scrapped/damaged goods."
msgstr ""
"Отметьте, чтобы разрешить использование этого места хранения для "
"бракованных/сломанных товаров."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Choose Labels Layout"
msgstr "Выбрать макет этикеток"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quantity_history__inventory_datetime
#: model:ir.model.fields,help:stock.field_stock_request_count__inventory_date
msgid "Choose a date to get the inventory at that date"
msgstr "Выберите дату, чтобы получить инвентаризацию на эту дату"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Choose destination location"
msgstr "Выберите местонахождение назначения"

#. module: stock
#: model:ir.model,name:stock.model_product_label_layout
msgid "Choose the sheet layout to print the labels"
msgstr "Выберите макет листа для печати этикеток"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quantity_history
msgid "Choose your date"
msgstr "Выберите свою дату"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Clear"
msgstr "Очистить"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
msgid "Clear Lines"
msgstr "Очистить строки"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_traceability_report_backend.xml:0
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
#: model_terms:ir.ui.view,arch_db:stock.view_stock_replenishment_info
#, python-format
msgid "Close"
msgstr "Закрыть"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__sequence_code
msgid "Code"
msgstr "Код"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__color
msgid "Color"
msgstr "Цвет"

#. module: stock
#: model:ir.model,name:stock.model_res_company
msgid "Companies"
msgstr "Компании"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__company_id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__company_id
#: model:ir.model.fields,field_description:stock.field_stock_location__company_id
#: model:ir.model.fields,field_description:stock.field_stock_location_route__company_id
#: model:ir.model.fields,field_description:stock.field_stock_move__company_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__company_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__company_id
#: model:ir.model.fields,field_description:stock.field_stock_package_type__company_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__company_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__company_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__company_id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__company_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__company_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__company_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__company_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__company_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__company_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__company_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__company_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__company_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__company_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Company"
msgstr "Компания"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs"
msgstr "Рассчитать стоимость доставки"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr "Расчет цены и доставка DHL"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with Easypost"
msgstr "Расчет цены и доставка Easypost"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr "Расчет цены и доставка FedEx"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr "Расчет цены и доставка UPS"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr "Расчет цены и доставка USPS"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr "Расчет цены и доставка bpost"

#. module: stock
#: model:ir.model,name:stock.model_res_config_settings
msgid "Config Settings"
msgstr "Конфигурационные настройки"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_config_settings
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Configuration"
msgstr "Настройки"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.package_level_form_edit_view
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_request_count_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quantity_history
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "Confirm"
msgstr "Подтвердить"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__confirmed
msgid "Confirmed"
msgstr "Подтвержден"

#. module: stock
#: model:ir.model,name:stock.model_stock_inventory_conflict
msgid "Conflict in Inventory"
msgstr "Несоответствие в инвентаризации"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Conflict in Inventory Adjustment"
msgstr "Конфликт при корректировке запасов"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__quant_to_fix_ids
msgid "Conflicts"
msgstr "Конфликты"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_tracking_owner
msgid "Consignment"
msgstr "Комиссионная торговля"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__consume_line_ids
msgid "Consume Line"
msgstr "потребленный строку"

#. module: stock
#: model:ir.model,name:stock.model_res_partner
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_partner_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__partner_id
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Contact"
msgstr "Контакт"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__child_ids
msgid "Contains"
msgstr "Содержит"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Content"
msgstr "Содержимое"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_reset_view
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_set_view
msgid "Continue"
msgstr "Далее"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__product_uom_category_id
#: model:ir.model.fields,help:stock.field_stock_move__product_uom_category_id
#: model:ir.model.fields,help:stock.field_stock_move_line__product_uom_category_id
#: model:ir.model.fields,help:stock.field_stock_scrap__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Преобразование между единицами измерения возможно, если они принадлежат "
"одной категории. Преобразование будет сделано на основе коэффициентов."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__posx
msgid "Corridor (X)"
msgstr "Ряд (X)"

#. module: stock
#: code:addons/stock/wizard/stock_immediate_transfer.py:0
#, python-format
msgid ""
"Could not reserve all requested products. Please use the 'Mark as Todo' "
"button to handle the reservation manually."
msgstr ""
"Не удалось зарезервировать все запрошенные продукты. Пожалуйста, используйте"
" кнопку 'Отметить как Выполнено' для обработки бронирования вручную."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_request_count__set_count
msgid "Count"
msgstr "Количество"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking
msgid "Count Picking"
msgstr "количество комплектувань"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_backorders
msgid "Count Picking Backorders"
msgstr "Количество обратных заказов комплектования"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_draft
msgid "Count Picking Draft"
msgstr "Количество черновиков комплектувань"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_late
msgid "Count Picking Late"
msgstr "Количество просроченных комплектований"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_ready
msgid "Count Picking Ready"
msgstr "Количество комплектувань готово"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_waiting
msgid "Count Picking Waiting"
msgstr "Подсчет комплектувань ожидания"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_inventory
msgid "Count Sheet"
msgstr "Счетная ведомость"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_quantity
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Counted Quantity"
msgstr "Подсчитанное количество"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Counterpart Locations"
msgstr "Местонахождение"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "Create Backorder"
msgstr "Создать Возврат"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Create Backorder?"
msgstr "Создать Возврат?"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_use_create_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking__use_create_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__use_create_lots
msgid "Create New Lots/Serial Numbers"
msgstr "Создать новую партию/серийный номер"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid ""
"Create a backorder if you expect to process the remaining\n"
"                        products later. Do not create a backorder if you will not\n"
"                        process the remaining products."
msgstr ""
"Создайте предварительный заказ, если вы ожидаете обрабатывать другие товары "
"позже. Не создавайте попредене заказ, если вы не будете обрабатывать другие "
"товары."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_picking_type_action
msgid "Create a new operation type"
msgstr "Создайте новый тип операции"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_package_view
msgid "Create a new package"
msgstr "Создать новый пакет"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Create customizable worksheets for your quality checks"
msgstr "Создать индивидуальные таблицы для проверок качества"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_putaway_tree
msgid ""
"Create new putaway rules to dispatch automatically specific products to "
"their appropriate destination location upon receptions."
msgstr ""
"Создайте новые правила отгрузки, чтобы автоматически отправлять определенные"
" товары в соответствующие места при приемке."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__create_uid
#: model:ir.model.fields,field_description:stock.field_product_removal__create_uid
#: model:ir.model.fields,field_description:stock.field_product_replenish__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_location__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_location_route__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_move__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_level__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_type__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_request_count__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_rule__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_scrap__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__create_uid
msgid "Created by"
msgstr "Создан"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__create_date
#: model:ir.model.fields,field_description:stock.field_product_removal__create_date
#: model:ir.model.fields,field_description:stock.field_product_replenish__create_date
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__create_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__create_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__create_date
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__create_date
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__create_date
#: model:ir.model.fields,field_description:stock.field_stock_location__create_date
#: model:ir.model.fields,field_description:stock.field_stock_location_route__create_date
#: model:ir.model.fields,field_description:stock.field_stock_move__create_date
#: model:ir.model.fields,field_description:stock.field_stock_move_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__create_date
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__create_date
#: model:ir.model.fields,field_description:stock.field_stock_package_level__create_date
#: model:ir.model.fields,field_description:stock.field_stock_package_type__create_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__create_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__create_date
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__create_date
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quant__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__create_date
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__create_date
#: model:ir.model.fields,field_description:stock.field_stock_request_count__create_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__create_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_rule__create_date
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__create_date
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute__create_date
#: model:ir.model.fields,field_description:stock.field_stock_scrap__create_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__create_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__create_date
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__create_date
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__create_date
#: model:ir.model.fields,field_description:stock.field_stock_track_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__create_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__create_date
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__create_date
msgid "Created on"
msgstr "Создан"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid ""
"Creating a new warehouse will automatically activate the Storage Locations "
"setting"
msgstr ""
"При создании нового склада автоматически активизируется настройка Места "
"хранения"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__date
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Creation Date"
msgstr "Дата создания"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__date
msgid "Creation Date, usually the time of the order"
msgstr "Дата создания, используется время заказа"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Cross-Dock"
msgstr "Кросс-Докинг"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__crossdock_route_id
msgid "Crossdock Route"
msgstr "Кросс-Докинг маршрут"

#. module: stock
#: model:ir.actions.act_window,name:stock.location_open_quants
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Current Stock"
msgstr "Текущий запас"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__qty_available
msgid ""
"Current quantity of products.\n"
"In a context with a single Stock Location, this includes goods stored at this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"stored in the Stock Location of the Warehouse of this Shop, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"Текущее количество продукции.\n"
"В контексте Единого Места хранения включает в себя товары, хранящиеся в этом месте или любом из его филиалов.\n"
"В контексте единого Склада это включает в себя товары, хранящиеся в Месте хранения этого Склада или в любом из его филиалов.\n"
"Хранится в Месте Хранения склада этого магазина или любого из его филиалов.\n"
"В противном случае, включает в себя товары, хранящиеся в любом Месте Хранения «внутреннего» типа."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__picking_quantity__custom
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__custom
msgid "Custom"
msgstr "Пользовательский"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Customer"
msgstr "Заказчик"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__sale_delay
#: model:ir.model.fields,field_description:stock.field_product_template__sale_delay
msgid "Customer Lead Time"
msgstr "Срок поставки заказчика"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__property_stock_customer
#: model:ir.model.fields,field_description:stock.field_res_users__property_stock_customer
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__customer
msgid "Customer Location"
msgstr "Место хранения заказчика"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Customer Locations"
msgstr "Места хранения заказчиков"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Cyclic Inventory"
msgstr "Периодическая инвентаризация"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_dhl
msgid "DHL Express Connector"
msgstr "DHL Экспресс-коннектор"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__date
#: model:ir.model.fields,field_description:stock.field_stock_move_line__date
#: model:ir.model.fields,field_description:stock.field_stock_scrap__date_done
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Date"
msgstr "Дата"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Date Processing"
msgstr "Дата обработки"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__date_deadline
#: model:ir.model.fields,help:stock.field_stock_picking__date_deadline
msgid "Date Promise to the customer on the top level document (SO/PO)"
msgstr ""
"Дата выдачи клиенту на документе верхнего уровня (заказ на покупку/закупку)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__date
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Date Scheduled"
msgstr "Плановая дата"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__date_planned
msgid "Date at which the replenishment should take place."
msgstr "Дата, на которую предстоит пополнение."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__date_done
msgid "Date at which the transfer has been processed or cancelled."
msgstr "Дата, когда было совершено обработку или отмены перемещения."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__next_inventory_date
msgid "Date for next planned inventory based on cyclic schedule."
msgstr ""
"Дата следующей плановой инвентаризации на основе циклического графика."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__date_done
msgid "Date of Transfer"
msgstr "Дата перемещения"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__last_inventory_date
msgid "Date of the last inventory at this location."
msgstr "Дата последней инвентаризации на данном участке."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__reservation_date
msgid "Date to Reserve"
msgstr "Дата резервирования"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Day and month that annual inventory counts should occur."
msgstr "День и месяц, когда должна быть проведена ежегодная инвентаризация."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__annual_inventory_day
#: model:ir.model.fields,field_description:stock.field_res_config_settings__annual_inventory_day
msgid "Day of the month"
msgstr "день месяца"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company__annual_inventory_day
#: model:ir.model.fields,help:stock.field_res_config_settings__annual_inventory_day
msgid ""
"Day of the month when the annual inventory should occur. If zero or negative, then the first day of the month will be selected instead.\n"
"        If greater than the last day of a month, then the last day of the month will be selected instead."
msgstr ""
"День месяца, когда должна проводиться годовая инвентаризация. Если нулевой или отрицательный, то вместо него будет выбран первый день месяца.\n"
" Если больше последнего дня месяца, то вместо него будет выбран последний день месяца."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__reservation_days_before
msgid "Days"
msgstr "День"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__reservation_days_before_priority
msgid "Days when starred"
msgstr "Дни, отмеченные звездами"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__date_deadline
#: model:ir.model.fields,field_description:stock.field_stock_picking__date_deadline
msgid "Deadline"
msgstr "Крайний Срок"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Deadline exceed or/and by the scheduled"
msgstr "Превышение сроков или/и несоблюдение сроков"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid "Deadline updated due to delay on %s"
msgstr "Сроки обновлены в связи с задержкой %s"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__12
msgid "December"
msgstr "Декабрь"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__default_location_dest_id
msgid "Default Destination Location"
msgstr "Место Назначения По умолчанию"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__default_location_src_id
msgid "Default Source Location"
msgstr "Исходное местоположение по умолчанию"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__reception_steps
msgid "Default incoming route to follow"
msgstr "По умолчанию входящий маршрут следования"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__delivery_steps
msgid "Default outgoing route to follow"
msgstr "По умолчанию исходящий маршрут следующий"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_production_lot__product_uom_id
#: model:ir.model.fields,help:stock.field_stock_quant__product_uom_id
#: model:ir.model.fields,help:stock.field_stock_return_picking_line__uom_id
#: model:ir.model.fields,help:stock.field_stock_storage_category_capacity__product_uom_id
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__product_uom
msgid "Default unit of measure used for all stock operations."
msgstr ""
"Стандартная единица измерения, используемая для всех складских операций."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__procure_method__make_to_stock
msgid "Default: Take From Stock"
msgstr "По умолчанию: Брать со склада"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__route_ids
msgid "Defaults routes through the warehouse"
msgstr "По умолчанию маршруты идут через склад"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint
msgid ""
"Define a minimum stock rule so that Odoo creates automatically requests for "
"quotations or confirmed manufacturing orders to resupply your stock."
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_warehouse_form
msgid "Define a new warehouse"
msgstr "Определите новый склад"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid ""
"Define your locations to reflect your warehouse structure and\n"
"            organization. Odoo is able to manage physical locations\n"
"            (warehouses, shelves, bin, etc), partner locations (customers,\n"
"            vendors) and virtual locations which are the counterpart of\n"
"            the stock operations like the manufacturing orders\n"
"            consumptions, inventories, etc."
msgstr ""
"Определите свои местоположения, чтобы отразить структуру склада и\n"
"             организации. Odoo может управлять физическими местоположениями\n"
"             (склады, полки, бункер и т. д.), места партнеров (клиенты,\n"
"             поставщиками) и виртуальными объектами, которые являются аналогами\n"
"             операции с запасами, такие как производственные заказы\n"
"             потребления, запасов и т. д."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__removal_strategy_id
msgid ""
"Defines the default method used for suggesting the exact location (shelf) where to take the products from, which lot etc. for this location. This method can be enforced at the product category level, and a fallback is made on the parent locations if none is set here.\n"
"\n"
"FIFO: products/lots that were stocked first will be moved out first.\n"
"LIFO: products/lots that were stocked last will be moved out first.\n"
"Closet location: products/lots closest to the target location will be moved out first.\n"
"FEFO: products/lots with the closest removal date will be moved out first (the availability of this method depends on the \"Expiration Dates\" setting)."
msgstr ""
"Определяет метод по умолчанию, используемый для предложения точного места хранения (полки), откуда брать товар, из какой партии и т.д. для данного места. Этот метод может быть применен на уровне категории товара, и если он не задан, то откат будет сделан на родительские место хранения.\n"
"\n"
"FIFO: товары/партии, которые были складированы первыми, будут перемещены первыми.\n"
"LIFO: товары/партии, которые были складированы последними, будут перемещены первыми. Местонахождение: продукты/партии, расположенные ближе всего к целевому месту, будут перемещены первыми.\n"
"FEFO: продукты/партии с ближайшей датой удаления будут перемещены первыми (доступность этого метода зависит от настройки \"Срок годности\")."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__delay_alert_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__delay_alert_date
msgid "Delay Alert Date"
msgstr "Отложить дату предупреждения"

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid "Delay on %s"
msgstr "Задержка %s"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__delivery_steps__ship_only
msgid "Deliver goods directly (1 step)"
msgstr "Отгрузить товары сразу (1 шаг)"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Deliver in 1 step (ship)"
msgstr "Доставить за 1 шаг (отгрузка)"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Deliver in 2 steps (pick + ship)"
msgstr "Доставить за 2 шага (комплектование + отгрузка)"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Deliver in 3 steps (pick + pack + ship)"
msgstr "Доставить за 3 шага (комплектование + упаковка + отгрузка)"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Delivered Qty"
msgstr "Доставленное кол-во"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__code__outgoing
#: model:ir.ui.menu,name:stock.menu_delivery
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Delivery"
msgstr "Доставка"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Delivery Address"
msgstr "Адрес доставки"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery
msgid "Delivery Methods"
msgstr "Способы доставки"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#: model:stock.picking.type,name:stock.chi_picking_type_out
#: model:stock.picking.type,name:stock.picking_type_out
#, python-format
msgid "Delivery Orders"
msgstr "Заказы на отгрузку"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__delivery_route_id
msgid "Delivery Route"
msgstr "Маршрут доставки"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_delivery
msgid "Delivery Slip"
msgstr "Бланк доставки"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__move_type
msgid "Delivery Type"
msgstr "Тип доставки"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__sale_delay
#: model:ir.model.fields,help:stock.field_product_template__sale_delay
msgid ""
"Delivery lead time, in days. It's the number of days, promised to the "
"customer, between the confirmation of the sales order and the delivery."
msgstr ""
"Срок выполнения доставки в днях. Это количество дней, обещанная заказчику, "
"между подтверждением заказа на продажу и доставкой."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__delivery_count
msgid "Delivery order count"
msgstr "Подсчет заказов на поставку"

#. module: stock
#: code:addons/stock/models/stock_production_lot.py:0
#, python-format
msgid "Delivery orders of %s"
msgstr "Заказы на поставку %s"

#. module: stock
#: model:mail.template,name:stock.mail_template_data_delivery_confirmation
msgid "Delivery: Send by Email"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_uom_qty
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
msgid "Demand"
msgstr "Потребность"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_packaging__route_ids
msgid ""
"Depending on the modules installed, this will allow you to define the route "
"of the product in this packaging: whether it will be bought, manufactured, "
"replenished on order, etc."
msgstr ""
"В зависимости от установленных модулей это позволит вам определить маршрут "
"движения продукта: будет ли он куплен, изготовлен, пополнен по заказу и т. "
"д."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__route_ids
#: model:ir.model.fields,help:stock.field_product_template__route_ids
msgid ""
"Depending on the modules installed, this will allow you to define the route "
"of the product: whether it will be bought, manufactured, replenished on "
"order, etc."
msgstr ""
"В зависимости от установленных модулей это позволит вам определить маршрут "
"движения продукта: будет ли он куплен, изготовлен, пополнен по заказу и т. "
"д."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__name
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__note
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "Description"
msgstr "Описание"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Delivery Orders"
msgstr "Описание для заказов на доставку"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Internal Transfers"
msgstr "Описание для внутреннего перемещения"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Receipts"
msgstr "Описание для поступлений"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__description_picking
msgid "Description of Picking"
msgstr "Описание комплектования"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__description_pickingout
#: model:ir.model.fields,field_description:stock.field_product_template__description_pickingout
msgid "Description on Delivery Orders"
msgstr "Описание на заказах на доставку"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__description_picking
#: model:ir.model.fields,field_description:stock.field_product_template__description_picking
msgid "Description on Picking"
msgstr "Описание по Комлектации"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__description_pickingin
#: model:ir.model.fields,field_description:stock.field_product_template__description_pickingin
msgid "Description on Receptions"
msgstr "Описание на приемах"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__description_picking
msgid "Description picking"
msgstr "Выбор описания"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__partner_id
msgid "Destination Address "
msgstr "Адрес назначения "

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#: model:ir.model.fields,field_description:stock.field_stock_move__location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__location_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#, python-format
msgid "Destination Location"
msgstr "Место назначения"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Destination Location:"
msgstr "Расположение назначения:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_dest_ids
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Destination Moves"
msgstr "перемещение назначения"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__result_package_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
msgid "Destination Package"
msgstr "Назначение Пакета"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Destination Package :"
msgstr "Назначение упаковки:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__location_dest_id
msgid "Destination location"
msgstr "Место назначения"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__route_ids
msgid "Destination route"
msgstr "Маршрут места назначения"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#, python-format
msgid "Detailed Operations"
msgstr "Подробные операции"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_details_visible
msgid "Details Visible"
msgstr "видимые детали"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_diff_quantity
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Difference"
msgstr "Расхождение"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_reset_view
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_set_view
#: model_terms:ir.ui.view,arch_db:stock.package_level_form_edit_view
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_adjustment_name_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_request_count_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
#: model_terms:ir.ui.view,arch_db:stock.view_stock_orderpoint_snooze
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "Discard"
msgstr "Отменить"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Discard and manually resolve the conflict"
msgstr "Отмените и устраните конфликт вручную"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__display_assign_serial
msgid "Display Assign Serial"
msgstr "Отобразить назначенный серийный"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__display_complete
msgid "Display Complete"
msgstr "Отображение завершено"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_lot_on_delivery_slip
msgid "Display Lots & Serial Numbers on Delivery Slips"
msgstr "Отобразить номер партии / серийный номер на квитанциях"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__display_name
#: model:ir.model.fields,field_description:stock.field_product_removal__display_name
#: model:ir.model.fields,field_description:stock.field_product_replenish__display_name
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__display_name
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__display_name
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__display_name
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__display_name
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__display_name
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__display_name
#: model:ir.model.fields,field_description:stock.field_stock_location__display_name
#: model:ir.model.fields,field_description:stock.field_stock_location_route__display_name
#: model:ir.model.fields,field_description:stock.field_stock_move__display_name
#: model:ir.model.fields,field_description:stock.field_stock_move_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__display_name
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__display_name
#: model:ir.model.fields,field_description:stock.field_stock_package_level__display_name
#: model:ir.model.fields,field_description:stock.field_stock_package_type__display_name
#: model:ir.model.fields,field_description:stock.field_stock_picking__display_name
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__display_name
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__display_name
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quant__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__display_name
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__display_name
#: model:ir.model.fields,field_description:stock.field_stock_request_count__display_name
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__display_name
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_rule__display_name
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__display_name
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute__display_name
#: model:ir.model.fields,field_description:stock.field_stock_scrap__display_name
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__display_name
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__display_name
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__display_name
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__display_name
#: model:ir.model.fields,field_description:stock.field_stock_track_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__display_name
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: stock
#: model:res.groups,name:stock.group_auto_reception_report
msgid "Display Reception Report at Validation"
msgstr ""

#. module: stock
#: model:res.groups,name:stock.group_lot_on_delivery_slip
msgid "Display Serial & Lot Number in Delivery Slips"
msgstr "Отобразить номер партии / серийный номер на квитанциях"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.package_level_tree_view_picking
msgid "Display package content"
msgstr "Отобразить содержимое контейнера"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_quant_package__package_use__disposable
msgid "Disposable Box"
msgstr "Одноразовая коробка"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
msgid "Do you confirm you want to scrap"
msgstr "Подтвердите, чтобы отправить в отходы"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Documentation"
msgstr "Документация"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__qty_done
#: model:ir.model.fields,field_description:stock.field_stock_package_level__is_done
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__done
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__done
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__done
#: model:ir.model.fields.selection,name:stock.selection__stock_scrap__state__done
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view2
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
msgid "Done"
msgstr "Выполнено"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__draft
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__draft
#: model:ir.model.fields.selection,name:stock.selection__stock_scrap__state__draft
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Draft"
msgstr "Черновик"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Draft Moves"
msgstr "Черновик Перемещения"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_easypost
msgid "Easypost Connector"
msgstr "коннектор Easypost"

#. module: stock
#: code:addons/stock/models/stock_orderpoint.py:0
#, python-format
msgid "Edit Product"
msgstr "Редактировать продукт"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"Editing quantities in an Inventory Adjustment location is forbidden,those "
"locations are used as counterpart when correcting the quantities."
msgstr ""
"Редактирование количеств в месте сборки запасов запрещено, эти "
"местоположения используются в качестве аналогов при корректировке количеств."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Effective Date"
msgstr "Срок эффективности"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Email Confirmation"
msgstr "Подтверждение по email"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__stock_move_email_validation
#: model:ir.model.fields,field_description:stock.field_res_config_settings__stock_move_email_validation
msgid "Email Confirmation picking"
msgstr "Выбор подтверждения по электронной почте"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Email Template"
msgstr "Шаблон письма"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__stock_mail_confirmation_template_id
#: model:ir.model.fields,field_description:stock.field_res_config_settings__stock_mail_confirmation_template_id
msgid "Email Template confirmation picking"
msgstr "Выбор шаблона электронного письма с подтверждением"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company__stock_mail_confirmation_template_id
#: model:ir.model.fields,help:stock.field_res_config_settings__stock_mail_confirmation_template_id
msgid "Email sent to the customer once the order is done."
msgstr ""
"Электронное письмо отправляется клиенту после того, как заказ будет "
"выполнен."

#. module: stock
#: model_terms:digest.tip,tip_description:stock.digest_tip_stock_0
msgid ""
"Enjoy a quick-paced experience with the Odoo barcode app. It is blazing fast"
" and works even without a stable internet connection. It supports all flows:"
" inventory adjustments, batch picking, moving lots or pallets, low inventory"
" checks, etc. Go to the \"Apps\" menu to activate the barcode interface."
msgstr ""
"Оцените эффективность модуля для работы со штрихкодами. Он отличается "
"молниеносной скоростью и работает даже без стабильного подключения к "
"Интернету. Модуль поддерживает все процессы: корректировку запасов, "
"комплектацию партий, перемещение партий или паллет, проверку низкого уровня "
"запасов и т.д. Для активации модуля штрихкодов перейдите в модуль "
"\"Приложения\"."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__tracking
#: model:ir.model.fields,help:stock.field_product_template__tracking
#: model:ir.model.fields,help:stock.field_stock_move__has_tracking
#: model:ir.model.fields,help:stock.field_stock_move_line__tracking
#: model:ir.model.fields,help:stock.field_stock_quant__tracking
#: model:ir.model.fields,help:stock.field_stock_scrap__tracking
#: model:ir.model.fields,help:stock.field_stock_track_line__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "Обеспечение отслеживания товара, хранящегося на вашем складе."

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_traceability_report_backend.xml:0
#, python-format
msgid "Error"
msgstr "Ошибка"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid ""
"Every stock operation in Odoo moves the products from one\n"
"            location to another one.  For instance, if you receive products\n"
"            from a vendor, Odoo will move products from the Vendor\n"
"            location to the Stock location. Each report can be performed on\n"
"            physical, partner or virtual locations."
msgstr ""
"Каждая операция на складе в Odoo перемещает продукты с одного\n"
"             расположение к другому. Например, если вы получаете продукты\n"
"             от поставщика, Odoo будет перемещать продукты от Вендора\n"
"             местоположение к месту склада. Каждый отчет может быть\n"
"             физического, партнерского или виртуального местоположения."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "Exception(s) occurred on the picking"
msgstr "Исключение произошло на комплектовании"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "Exception(s):"
msgstr "Исключение(я):"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"Existing Serial Numbers (%s). Please correct the serial numbers encoded."
msgstr ""
"Существующие серийные номера (%s). Пожалуйста, исправьте зашифрованные "
"серийные номера."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid "Existing Serial numbers. Please correct the serial numbers encoded:"
msgstr ""
"Существующие серийные номера. Пожалуйста, исправьте зашифрованные серийные "
"номера:"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/forecast_widget.xml:0
#, python-format
msgid "Exp"
msgstr "Ожид."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Exp %s"
msgstr "План %s"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__products_availability_state__expected
msgid "Expected"
msgstr "Ожидается"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "Expected Delivery:"
msgstr "Ожидаемая поставка:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_product_expiry
msgid "Expiration Dates"
msgstr "Срок годности"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "External note..."
msgstr "Внешняя заметка..."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_removal__method
msgid "FIFO, LIFO..."
msgstr "ФИФО, ЛИФО..."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__2
msgid "February"
msgstr "Февраль"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_fedex
msgid "FedEx Connector"
msgstr "коннектор FedEx"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__filtered_location
msgid "Filtered Location"
msgstr "отфильтрованные местонахождение"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Filters"
msgstr "Фильтры"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__next_serial_number
#: model:ir.model.fields,field_description:stock.field_stock_move__next_serial
msgid "First SN"
msgstr "Первый SN"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__group_propagation_option__fixed
msgid "Fixed"
msgstr "Статичный"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__group_id
msgid "Fixed Procurement Group"
msgstr "Исправленная группа поставок"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_traceability_report_line.xml:0
#: code:addons/stock/static/src/xml/stock_traceability_report_line.xml:0
#, python-format
msgid "Fold"
msgstr "Свернуть"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_follower_ids
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_follower_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_follower_ids
msgid "Followers"
msgstr "Подписчики"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_partner_ids
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_partner_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_partner_ids
msgid "Followers (Partners)"
msgstr "Подписчики (Партнеры)"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__activity_type_icon
#: model:ir.model.fields,help:stock.field_stock_production_lot__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Иконка со шрифтом Font awesome, например. fa-tasks"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__removal_strategy_id
msgid "Force Removal Strategy"
msgstr "Использовать стратегию ротации"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_forecast
msgid "Forecast"
msgstr "Прогноз"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__forecast_availability
msgid "Forecast Availability"
msgstr "Прогнозируемое наличие"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable_config
msgid "Forecast Description"
msgstr "Описание прогнозирования"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__virtual_available
msgid ""
"Forecast quantity (computed as Quantity On Hand - Outgoing + Incoming)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"Прогноз количества (рассчитывается как Количество в наличии - исходящее + входящее)\n"
"В контексте с одним Складским Местом хранения сюда входят товары, хранящиеся в этом месте или каком-либо из его дочерних.\n"
"В контексте одного Склада это включает в себя товары, хранящиеся в Месте хранения Склада или любом из его дочерних.\n"
"В противном случае, это включает в себя товары, хранящиеся на любых Местах хранения с типом \"внутренний\"."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__free_qty
msgid ""
"Forecast quantity (computed as Quantity On Hand - reserved quantity)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"Прогнозируемое количество (рассчитывается как «Количество в наличии - "
"Зарезервированное количество»). В контексте с одним местоположением запасов "
"сюда входят товары, хранящиеся в этом месте или в любом из его дочерних "
"элементов. В контексте одного Склада сюда входят товары, хранящиеся в "
"местоположении запасов этого Склада или любого из его дочерних элементов. В "
"противном случае сюда входят товары, хранящиеся на любом складе "
"«внутреннего» типа."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_replenishment_header
msgid "Forecasted"
msgstr "прогнозируемый"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "Forecasted Date"
msgstr "Прогнозируемая дата"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__report_stock_quantity__state__out
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_search
msgid "Forecasted Deliveries"
msgstr "Прогнозируемые поставки"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__forecast_expected_date
msgid "Forecasted Expected date"
msgstr "Прогнозируемая ожидаемая дата"

#. module: stock
#: model:ir.actions.act_window,name:stock.report_stock_quantity_action
#: model:ir.actions.act_window,name:stock.report_stock_quantity_action_product
#: model:ir.ui.menu,name:stock.menu_forecast_inventory
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Forecasted Inventory"
msgstr "Прогнозируемые запасы"

#. module: stock
#: code:addons/stock/models/product.py:0
#: model:ir.model.fields,field_description:stock.field_product_product__virtual_available
#: model:ir.model.fields,field_description:stock.field_product_template__virtual_available
#: model:ir.model.fields,field_description:stock.field_stock_move__availability
#: model_terms:ir.ui.view,arch_db:stock.view_stock_product_tree
#, python-format
msgid "Forecasted Quantity"
msgstr "Прогнозируемое количество"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__report_stock_quantity__state__in
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_search
msgid "Forecasted Receipts"
msgstr "Прогнозируемые поступления"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/forecast_widget.xml:0
#: model:ir.actions.client,name:stock.stock_replenishment_product_product_action
#: model:ir.actions.report,name:stock.stock_replenishment_report_product_product_action
#: model:ir.actions.report,name:stock.stock_replenishment_report_product_template_action
#, python-format
msgid "Forecasted Report"
msgstr "Прогнозный отчет"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__report_stock_quantity__state__forecast
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_search
msgid "Forecasted Stock"
msgstr " Прогнозируемый склад"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__forecast_weight
msgid "Forecasted Weight"
msgstr "Прогнозируемый вес"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Forecasted with Pending"
msgstr "Прогнозируемые запасы с ожиданиями"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_replenishment_header
msgid "Forecasted<br/>+ Pending"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_label_layout__print_format
msgid "Format"
msgstr "Формат"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Free Stock"
msgstr "Доступно на складе"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__free_qty
msgid "Free To Use Quantity "
msgstr "Количество в свободном использовании"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__location_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__location_id
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "From"
msgstr "Из"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__owner_id
msgid "From Owner"
msgstr "От владельца"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_reserved_availability
msgid "From Supplier"
msgstr "От поставщика"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__complete_name
msgid "Full Location Name"
msgstr "Полное Название Места хранения"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Future Activities"
msgstr "Будущие действия"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Future Deliveries"
msgstr "Будущие доставки"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Future P&L"
msgstr "Будущие п/у"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Future Productions"
msgstr "Будущие производства"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Future Receipts"
msgstr "Будущие поступления"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Get a full traceability from vendors to customers"
msgstr ""
"Получите возможность отслеживания движения от поставщиков к потребителям"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Get informative or blocking warnings on partners"
msgstr "Получите информативные или блокировочные предупреждения о партнерах"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_putaway_rule__sequence
msgid ""
"Give to the more specialized category, a higher priority to have them in top"
" of the list."
msgstr ""
"Дайте более специализированные категории, более высокий приоритет, чтобы "
"иметь их в верхней части списка."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__sequence
msgid "Gives the sequence of this line when displaying the warehouses."
msgstr "Выдает последовательность этой строки при отображении складов."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Group By"
msgstr "Группировка"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Group by..."
msgstr "Группировать по…"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__group_stock_picking_wave
msgid "Group your move operations in wave transfer to process them together"
msgstr ""
"Группируйте операции по перемещению в групповой передаче, чтобы обрабатывать"
" их вместе"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_message
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__has_message
#: model:ir.model.fields,field_description:stock.field_stock_scrap__has_message
msgid "Has Message"
msgstr "Есть сообщение"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_line_exist
msgid "Has Pack Operations"
msgstr "Имеет операции упаковки"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_packages
msgid "Has Packages"
msgstr "имеет пакеты"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_scrap_move
msgid "Has Scrap Moves"
msgstr "Перемещение отходов?"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_tracking
msgid "Has Tracking"
msgstr "имеет отслеживания"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_has_variants
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__product_has_variants
msgid "Has variants"
msgstr "имеет варианты"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Having Category"
msgstr "Наличие категории"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__height
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Height"
msgstr "Высота"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__posz
msgid "Height (Z)"
msgstr "Высота (Z)"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_height
msgid "Height must be positive"
msgstr "Высота должна быть положительной"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__snoozed_until
msgid "Hidden until next scheduler."
msgstr "Скрыт до следующего планирования."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__hide_picking_type
msgid "Hide Picking Type"
msgstr "Скрыть тип комплектации"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
#, python-format
msgid "History"
msgstr "История"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__reservation_method
msgid "How products in transfers of this operation type should be reserved."
msgstr ""
"Как должны быть зарезервированы продукты в перемещениях данного типа "
"операций."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__id
#: model:ir.model.fields,field_description:stock.field_product_removal__id
#: model:ir.model.fields,field_description:stock.field_product_replenish__id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__id
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__id
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__id
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__id
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__id
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__id
#: model:ir.model.fields,field_description:stock.field_stock_location__id
#: model:ir.model.fields,field_description:stock.field_stock_location_route__id
#: model:ir.model.fields,field_description:stock.field_stock_move__id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__id
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__id
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__id
#: model:ir.model.fields,field_description:stock.field_stock_package_type__id
#: model:ir.model.fields,field_description:stock.field_stock_picking__id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__id
#: model:ir.model.fields,field_description:stock.field_stock_quant__id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__id
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__id
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__id
#: model:ir.model.fields,field_description:stock.field_stock_request_count__id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__id
#: model:ir.model.fields,field_description:stock.field_stock_rule__id
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__id
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute__id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__id
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__id
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__id
#: model:ir.model.fields,field_description:stock.field_stock_track_line__id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__id
msgid "ID"
msgstr "Идентификатор"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_exception_icon
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_exception_icon
msgid "Icon"
msgstr "Иконка"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__activity_exception_icon
#: model:ir.model.fields,help:stock.field_stock_production_lot__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Значок, обозначающий действие исключения."

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"If a payment is still outstanding more than sixty (60) days after the due "
"payment date, My Company (Chicago) reserves the right to call on the "
"services of a debt recovery company. All legal expenses will be payable by "
"the client."
msgstr ""
"Если платеж остается непогашенным более шестидесяти (60) дней после "
"наступления срока платежа, компания оставляет за собой право обратиться к "
"услугам компании по взысканию задолженности. Все судебные издержки "
"оплачивает клиент."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_storage_category__allow_new_product__same
msgid "If all products are same"
msgstr "Если все продукты одинаковы"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__message_needaction
#: model:ir.model.fields,help:stock.field_stock_picking__message_unread
#: model:ir.model.fields,help:stock.field_stock_production_lot__message_needaction
#: model:ir.model.fields,help:stock.field_stock_production_lot__message_unread
#: model:ir.model.fields,help:stock.field_stock_scrap__message_needaction
#: model:ir.model.fields,help:stock.field_stock_scrap__message_unread
msgid "If checked, new messages require your attention."
msgstr "Если отмечено, новые сообщения будут требовать вашего внимания."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__message_has_error
#: model:ir.model.fields,help:stock.field_stock_picking__message_has_sms_error
#: model:ir.model.fields,help:stock.field_stock_production_lot__message_has_error
#: model:ir.model.fields,help:stock.field_stock_production_lot__message_has_sms_error
#: model:ir.model.fields,help:stock.field_stock_scrap__message_has_error
#: model:ir.model.fields,help:stock.field_stock_scrap__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Если отмечено - некоторые сообщения имеют ошибку доставки."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__propagate_cancel
msgid "If checked, when this move is cancelled, cancel the linked move too"
msgstr ""
"Если отмечено, то это перемещение отменено, связанные перемещения тоже "
"отменяются"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__result_package_id
msgid "If set, the operations are packed into this package"
msgstr "Если установлено, операции упакованы в этот пакет"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__active
msgid ""
"If the active field is set to False, it will allow you to hide the "
"orderpoint without removing it."
msgstr ""
"Если поле \"Активно\" имеет значение \"Ложь\", это позволит скрыть точку "
"заказа, не удаляя её."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route__active
msgid ""
"If the active field is set to False, it will allow you to hide the route "
"without removing it."
msgstr ""
"Если значение активного поля — Ложь, это позволит вам скрыть маршрут, не "
"удаляя его.."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_storage_category__allow_new_product__empty
msgid "If the location is empty"
msgstr "Если место хранения пусто"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid ""
"If the picking is unlocked you can edit initial demand (for a draft picking)"
" or done quantities (for a done picking)."
msgstr ""
"Если комплектования разблокирован, вы можете отредактировать начальный спрос"
" (для черновики комплектования) или выполнены количества (для завершенного "
"комплектования)."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__show_reserved
#: model:ir.model.fields,help:stock.field_stock_picking_type__show_reserved
msgid ""
"If this checkbox is ticked, Odoo will automatically pre-fill the detailed "
"operations with the corresponding products, locations and lot/serial "
"numbers."
msgstr ""
"Если этот флажок установлен, Odoo автоматически предварительно заполнит "
"подробные операции с соответствующими продуктами, местоположениями и "
"номерами партий / серийными номерами."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__print_label
msgid "If this checkbox is ticked, label will be print in this operation."
msgstr ""
"Если этот флажок установлен, то в данной операции будет печататься этикетка."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__show_operations
#: model:ir.model.fields,help:stock.field_stock_picking_type__show_operations
msgid ""
"If this checkbox is ticked, the pickings lines will represent detailed stock"
" operations. If not, the picking lines will represent an aggregate of "
"detailed stock operations."
msgstr ""
"Если это будет обозначено, строки комплектования будут представлять "
"детальные складские операции. Если нет, то строка комплектования "
"представлять совокупность детальных складских операций."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_type_use_create_lots
#: model:ir.model.fields,help:stock.field_stock_picking__use_create_lots
#: model:ir.model.fields,help:stock.field_stock_picking_type__use_create_lots
msgid ""
"If this is checked only, it will suppose you want to create new Lots/Serial "
"Numbers, so you can provide them in a text field. "
msgstr ""
"Если это только отмечено, предполагается, что вы хотите создать новые "
"партии/серийные номера, поэтому вы можете указать их в текстовом поле. "

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_type_use_existing_lots
#: model:ir.model.fields,help:stock.field_stock_picking__use_existing_lots
#: model:ir.model.fields,help:stock.field_stock_picking_type__use_existing_lots
msgid ""
"If this is checked, you will be able to choose the Lots/Serial Numbers. You "
"can also decide to not put lots in this operation type.  This means it will "
"create stock with no lot or not put a restriction on the lot taken. "
msgstr ""
"Если этот флажок установлен, вы сможете указывать партии / серийные номера. "
"Вы также сможете не указывать партию для операций. При этом будет создано "
"хранение без партии или без ограничения на указанную партию."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__backorder_id
msgid ""
"If this shipment was split, then this field links to the shipment which "
"contains the already processed part."
msgstr ""
"Если этот груз был разделен, то это поле ссылки на отгрузки, которая "
"содержит уже обработанную часть."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__picking_type_entire_packs
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_type_entire_packs
#: model:ir.model.fields,help:stock.field_stock_picking__picking_type_entire_packs
#: model:ir.model.fields,help:stock.field_stock_picking_type__show_entire_packs
msgid "If ticked, you will be able to select entire packages to move"
msgstr "Если обозначено, вы сможете выбрать пакеты для перемещения"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__active
msgid "If unchecked, it will allow you to hide the rule without removing it."
msgstr ""
"Если флажок не установлен, это позволит вам скрыть правило, не удаляя его."

#. module: stock
#: model:ir.model,name:stock.model_stock_immediate_transfer
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__backorder_confirmation_id
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__immediate_transfer_id
#: model:ir.model.fields,field_description:stock.field_stock_move__from_immediate_transfer
#: model:ir.model.fields,field_description:stock.field_stock_picking__immediate_transfer
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Immediate Transfer"
msgstr "Немедленное перемещение"

#. module: stock
#: model:ir.model,name:stock.model_stock_immediate_transfer_line
msgid "Immediate Transfer Line"
msgstr "Строка немедленного перемещения"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__immediate_transfer_line_ids
msgid "Immediate Transfer Lines"
msgstr "Строки немедленного перемещения"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Immediate Transfer?"
msgstr "Немедленное Перемещение?"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
msgid "Immediate transfer?"
msgstr "Немедленное перемещение?"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Import"
msgstr "Импорт"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__in_type_id
msgid "In Type"
msgstr "Входящий тип"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"In order for it to be admissible, My Company (Chicago) must be notified of "
"any claim by means of a letter sent by recorded delivery to its registered "
"office within 8 days of the delivery of the goods or the provision of the "
"services."
msgstr ""
"Для того чтобы претензия могла быть удовлетворена, компания должна быть "
"уведомлена заказным письмом, отправленным на ее юридический адрес в течение "
"8 дней с момента поставки товара или оказания услуг."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__incoming_qty
#: model:ir.model.fields,field_description:stock.field_product_template__incoming_qty
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Incoming"
msgstr "Входящие"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__in_date
msgid "Incoming Date"
msgstr "Дата поступления"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Incoming Draft Transfer"
msgstr "Черновик входящего перемещения"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__incoming_move_line_ids
msgid "Incoming Move Line"
msgstr "Строка по входящему перемещению"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__reception_steps
msgid "Incoming Shipments"
msgstr "Прибывающие грузы"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__inventory_diff_quantity
msgid ""
"Indicates the gap between the product's theoretical quantity and its counted"
" quantity."
msgstr ""
"Указывает на разницу между теоретическим количеством продукта и его учетным "
"количеством."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_kandan
msgid "Initial Demand"
msgstr "Первоначальный Спрос"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Input"
msgstr "Вход"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_input_stock_loc_id
msgid "Input Location"
msgstr "Зона приемки"

#. module: stock
#: code:addons/stock/models/res_company.py:0
#, python-format
msgid "Inter-warehouse transit"
msgstr "Межскладской транзит"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Internal"
msgstr "Внутреннее"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__internal
msgid "Internal Location"
msgstr "Внутреннее место хранения"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Internal Locations"
msgstr "Внутренние места хранения"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__ref
msgid "Internal Reference"
msgstr "Внутренний артикул"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__code__internal
msgid "Internal Transfer"
msgstr "Внутреннее перемещение"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#: model:stock.picking.type,name:stock.picking_type_internal
#, python-format
msgid "Internal Transfers"
msgstr "Внутренние перемещения"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__internal_transit_location_id
msgid "Internal Transit Location"
msgstr "Внутреннее Транзитное Место хранения"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__int_type_id
msgid "Internal Type"
msgstr "Внутренний тип"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__child_internal_location_ids
msgid "Internal locations amoung descendants"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_production_lot__ref
msgid ""
"Internal reference number in case it differs from the manufacturer's "
"lot/serial number"
msgstr ""
"Внутренний номер в случае, если он отличается от номера партии/серийного "
"номера производителя"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Invalid domain left operand %s"
msgstr "Недопустимый левый операнд домена %s "

#. module: stock
#: code:addons/stock/models/product.py:0 code:addons/stock/models/product.py:0
#, python-format
msgid "Invalid domain operator %s"
msgstr "Недопустимый оператор домена %s "

#. module: stock
#: code:addons/stock/models/product.py:0 code:addons/stock/models/product.py:0
#, python-format
msgid "Invalid domain right operand '%s'. It must be of type Integer/Float"
msgstr "Недопустимый домена '%s'. Он должен быть типа Integer/Float"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"Invalid rule's configuration, the following rule causes an endless loop: %s"
msgstr ""
"Неверная настройка правила, следующее правило вызывает бесконечный цикл: %s"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_quantity_auto_apply
msgid "Inventoried Quantity"
msgstr "Инвентарное количество"

#. module: stock
#: model:ir.actions.server,name:stock.action_view_inventory_tree
#: model:ir.actions.server,name:stock.action_view_quants
#: model:ir.model.fields,field_description:stock.field_stock_move__is_inventory
#: model:ir.model.fields,field_description:stock.field_stock_move_line__is_inventory
#: model:ir.ui.menu,name:stock.menu_stock_root
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_partner_stock_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_pivot
msgid "Inventory"
msgstr "Склад"

#. module: stock
#: code:addons/stock/wizard/stock_inventory_adjustment_name.py:0
#, python-format
msgid "Inventory Adjustment"
msgstr "Коррекция остатков"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__inventory_adjustment_name
msgid "Inventory Adjustment Name"
msgstr "Наименование коррекции остатков"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_inventory_adjustement_name
#: model:ir.model,name:stock.model_stock_inventory_adjustment_name
msgid "Inventory Adjustment Reference / Reason"
msgstr "Данные/причина корректировки запасов"

#. module: stock
#: model:ir.model,name:stock.model_stock_inventory_warning
msgid "Inventory Adjustment Warning"
msgstr "Уведомление о коррекции остатков"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#: model:ir.ui.menu,name:stock.menu_action_inventory_tree
#, python-format
msgid "Inventory Adjustments"
msgstr "Коррекция остатков"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "Inventory Count Sheet"
msgstr "Ведомость инвентаризации"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_request_count__inventory_date
msgid "Inventory Date"
msgstr "Дата инвентаризации"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__cyclic_inventory_frequency
msgid "Inventory Frequency (Days)"
msgstr "Период инвентаризации (дней)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__property_stock_inventory
#: model:ir.model.fields,field_description:stock.field_product_template__property_stock_inventory
msgid "Inventory Location"
msgstr "Место инвентаризации"

#. module: stock
#: model:ir.model,name:stock.model_stock_location
msgid "Inventory Locations"
msgstr "Места запасов"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__inventory
msgid "Inventory Loss"
msgstr "Потери при Инвентаризации"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Inventory On Hand"
msgstr "Запасы в наличии"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_picking_type_action
msgid "Inventory Overview"
msgstr "Обзор склада"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_quantity_set
msgid "Inventory Quantity Set"
msgstr "Набор количества остатков"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_adjustment_name_form_view
msgid "Inventory Reference / Reason"
msgstr "Данные/причина инвентаризации"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_valuation
msgid "Inventory Report"
msgstr "Отчет об инвентаризации"

#. module: stock
#: model:ir.model,name:stock.model_stock_location_route
msgid "Inventory Routes"
msgstr "Маршруты инвентаризации"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree
msgid "Inventory Valuation"
msgstr "Оценка запасов"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/inventory_report.xml:0
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__inventory_datetime
#, python-format
msgid "Inventory at Date"
msgstr "Инвентаризации на дату"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_is_follower
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_is_follower
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_is_follower
msgid "Is Follower"
msgstr "Подписчик"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__is_fresh_package
msgid "Is Fresh Package"
msgstr "Есть свежим пакетом"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__is_locked
#: model:ir.model.fields,field_description:stock.field_stock_move_line__is_locked
#: model:ir.model.fields,field_description:stock.field_stock_picking__is_locked
msgid "Is Locked"
msgstr "Заблокировано"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__is_signed
msgid "Is Signed"
msgstr "Подписано"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__return_location
msgid "Is a Return Location?"
msgstr "Это место получения возвратов?"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__scrap_location
msgid "Is a Scrap Location?"
msgstr "Это место для хранения брака?"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__is_initial_demand_editable
#: model:ir.model.fields,field_description:stock.field_stock_move_line__is_initial_demand_editable
msgid "Is initial demand editable"
msgstr "Первоначальный спрос редактируемый"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_deadline_issue
msgid "Is late"
msgstr "Поздно"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__has_deadline_issue
msgid "Is late or will be late depending on the deadline and scheduled date"
msgstr ""
"Задерживается или будет задерживаться в зависимости от срока и "
"запланированной даты"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__is_quantity_done_editable
msgid "Is quantity done editable"
msgstr "Есть ли величина редактированной"

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid ""
"It is not allowed to import reserved quantity, you have to use the quantity "
"directly."
msgstr ""
"Импорт зарезервированного количества не допускается, необходимо использовать"
" количество напрямую."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"It is not possible to reserve more products of %s than you have in stock."
msgstr "Невозможно заказать больше товаров %s чем у вас на складе."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"It is not possible to unreserve more products of %s than you have in stock."
msgstr "Невозможно восстановить больше товаров %s чем у вас на складе."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__move_type
msgid "It specifies goods to be deliver partially or all at once"
msgstr "Это определяет, будут ли товары доставлены по частям или все сразу"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__json_popover
msgid "JSON data for the popover widget"
msgstr "JSON-данные для виджета всплывающего окна"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__1
msgid "January"
msgstr "Январь"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__json_lead_days
msgid "Json Lead Days"
msgstr "Json дни"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__json_replenishment_history
msgid "Json Replenishment History"
msgstr "История восполнения Json"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__7
msgid "July"
msgstr "Июль"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__6
msgid "June"
msgstr "Июнь"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Keep Counted Quantity"
msgstr "Сохранить подсчитанное количество"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Keep Difference"
msgstr "Сохранить разницу"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid ""
"Keep the <strong>Counted Quantity</strong> (the Difference will be updated)"
msgstr ""
"Сохранить <strong>подсчитанное количество</strong>(Разница будет обновлена)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid ""
"Keep the <strong>Difference</strong> (the Counted Quantity will be updated "
"to reflect the same difference as when you counted)"
msgstr ""
"Сохраните <strong>разницу</strong> (подсчитанное количество будет обновлено,"
" чтобы отразить ту же разницу, что и при подсчете)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Last 12 Months"
msgstr "Последние 12 месяцев"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Last 3 Months"
msgstr "Последние 3 месяца"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Last 30 Days"
msgstr "30 дней"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__last_delivery_partner_id
msgid "Last Delivery Partner"
msgstr "Последний партнер по поставке"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__last_inventory_date
msgid "Last Effective Inventory"
msgstr "Последняя успешная инвентаризация"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group____last_update
#: model:ir.model.fields,field_description:stock.field_product_removal____last_update
#: model:ir.model.fields,field_description:stock.field_product_replenish____last_update
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity____last_update
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial____last_update
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation____last_update
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line____last_update
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty____last_update
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer____last_update
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line____last_update
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name____last_update
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict____last_update
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning____last_update
#: model:ir.model.fields,field_description:stock.field_stock_location____last_update
#: model:ir.model.fields,field_description:stock.field_stock_location_route____last_update
#: model:ir.model.fields,field_description:stock.field_stock_move____last_update
#: model:ir.model.fields,field_description:stock.field_stock_move_line____last_update
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze____last_update
#: model:ir.model.fields,field_description:stock.field_stock_package_destination____last_update
#: model:ir.model.fields,field_description:stock.field_stock_package_level____last_update
#: model:ir.model.fields,field_description:stock.field_stock_package_type____last_update
#: model:ir.model.fields,field_description:stock.field_stock_picking____last_update
#: model:ir.model.fields,field_description:stock.field_stock_picking_type____last_update
#: model:ir.model.fields,field_description:stock.field_stock_production_lot____last_update
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule____last_update
#: model:ir.model.fields,field_description:stock.field_stock_quant____last_update
#: model:ir.model.fields,field_description:stock.field_stock_quant_package____last_update
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history____last_update
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info____last_update
#: model:ir.model.fields,field_description:stock.field_stock_request_count____last_update
#: model:ir.model.fields,field_description:stock.field_stock_return_picking____last_update
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line____last_update
#: model:ir.model.fields,field_description:stock.field_stock_rule____last_update
#: model:ir.model.fields,field_description:stock.field_stock_rules_report____last_update
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute____last_update
#: model:ir.model.fields,field_description:stock.field_stock_scrap____last_update
#: model:ir.model.fields,field_description:stock.field_stock_storage_category____last_update
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity____last_update
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report____last_update
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation____last_update
#: model:ir.model.fields,field_description:stock.field_stock_track_line____last_update
#: model:ir.model.fields,field_description:stock.field_stock_warehouse____last_update
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint____last_update
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap____last_update
msgid "Last Modified on"
msgstr "Последнее изменение"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__write_uid
#: model:ir.model.fields,field_description:stock.field_product_removal__write_uid
#: model:ir.model.fields,field_description:stock.field_product_replenish__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_location__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_location_route__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_move__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_level__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_type__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_request_count__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_rule__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_scrap__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__write_uid
msgid "Last Updated by"
msgstr "Последний раз обновил"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__write_date
#: model:ir.model.fields,field_description:stock.field_product_removal__write_date
#: model:ir.model.fields,field_description:stock.field_product_replenish__write_date
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__write_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__write_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__write_date
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__write_date
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__write_date
#: model:ir.model.fields,field_description:stock.field_stock_location__write_date
#: model:ir.model.fields,field_description:stock.field_stock_location_route__write_date
#: model:ir.model.fields,field_description:stock.field_stock_move__write_date
#: model:ir.model.fields,field_description:stock.field_stock_move_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__write_date
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__write_date
#: model:ir.model.fields,field_description:stock.field_stock_package_level__write_date
#: model:ir.model.fields,field_description:stock.field_stock_package_type__write_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__write_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__write_date
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__write_date
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quant__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__write_date
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__write_date
#: model:ir.model.fields,field_description:stock.field_stock_request_count__write_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__write_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_rule__write_date
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__write_date
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute__write_date
#: model:ir.model.fields,field_description:stock.field_stock_scrap__write_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__write_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__write_date
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__write_date
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__write_date
#: model:ir.model.fields,field_description:stock.field_stock_track_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__write_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__write_date
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__products_availability_state__late
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Late"
msgstr "Просрочено"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Late Activities"
msgstr "Просроченные действия"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_late
msgid "Late Transfers"
msgstr "Просроченные Перемещения"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__products_availability
msgid "Latest product availability status of the picking"
msgstr "Актуальное состояние доступности продукции при комплектации"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__lead_days_date
msgid "Lead Days Date"
msgstr "Дата проведения"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__delay
msgid "Lead Time"
msgstr "Время выполнения заказа"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "Lead Times"
msgstr "Срок пополнения"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_request_count__set_count__empty
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__group_propagation_option__none
msgid "Leave Empty"
msgstr "Оставьте пустым"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route__company_id
#: model:ir.model.fields,help:stock.field_stock_rule__route_company_id
msgid "Leave this field empty if this route is shared between all companies"
msgstr ""
"Оставьте это поле пустым, если этот маршрут общий между всеми компаниями"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Legend"
msgstr "История"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__packaging_length
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Length"
msgstr "Длина"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_length
msgid "Length must be positive"
msgstr "Длина должна быть положительной"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__length_uom_name
msgid "Length unit of measure label"
msgstr "Обозначение единицы измерения длины"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__company_id
#: model:ir.model.fields,help:stock.field_stock_quant__company_id
msgid "Let this field empty if this location is shared between companies"
msgstr ""
"Оставьте это поле пустым, если это место хранения общее для всех компаний"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Linked Moves"
msgstr "Связанные Перемещения"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "List view of operations"
msgstr "Просмотр списка операций"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__location_id
#: model:ir.model.fields,field_description:stock.field_product_template__location_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__location_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__location_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__location_ids
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__location_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__location_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__location_id
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__location
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_tree2
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Location"
msgstr "Место"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_location_barcode
msgid "Location Barcode"
msgstr "Штрих-код местонахождение"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__name
msgid "Location Name"
msgstr "Название места хранения"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__lot_stock_id
msgid "Location Stock"
msgstr "Место хранения запасов"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__usage
msgid "Location Type"
msgstr "Тип места хранения"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__location_dest_id
msgid "Location where the system will stock the finished products."
msgstr "Место, где система будет хранить готовые продукты."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Location: Store to"
msgstr "Расположение: Склад в"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Location: When arrives to"
msgstr "Расположение: Когда прибывает в"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_location_form
#: model:ir.actions.act_window,name:stock.action_prod_inv_location_form
#: model:ir.actions.act_window,name:stock.action_storage_category_locations
#: model:ir.ui.menu,name:stock.menu_action_location_form
#: model_terms:ir.ui.view,arch_db:stock.report_location_barcode
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Locations"
msgstr "Места хранения"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Lock"
msgstr "Блокировать"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_category_form_view_inherit
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Logistics"
msgstr "Логистика"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__lot
msgid "Lot"
msgstr "Партия"

#. module: stock
#: model:ir.model,name:stock.model_stock_production_lot
#: model:ir.model.fields,field_description:stock.field_stock_scrap__lot_id
msgid "Lot/Serial"
msgstr "Партия/Серийный номер"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
msgid "Lot/Serial #"
msgstr "Партия/Серийный номер"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Lot/Serial :"
msgstr "Партия/Серийный номер:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__lot_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__name
#: model:ir.model.fields,field_description:stock.field_stock_quant__lot_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Lot/Serial Number"
msgstr "Партия/Серийный номер"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_lot_label
msgid "Lot/Serial Number (PDF)"
msgstr "Партия/Серийный номер (PDF)"

#. module: stock
#: model:ir.actions.report,name:stock.label_lot_template
msgid "Lot/Serial Number (ZPL)"
msgstr "Партия/Серийный номер (ZPL)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__lot_name
msgid "Lot/Serial Number Name"
msgstr "Название партии/серийного номера"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "Lot/Serial Numbers"
msgstr "Партии/Серийные номера"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_production_lot
msgid "Lots & Serial Numbers"
msgstr "Партии/Серийные номера"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Lots &amp; Serial numbers will appear on the delivery slip"
msgstr "Партия/Серийный номер появится на транспортной накладной"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__lots_visible
msgid "Lots Visible"
msgstr "Партии отображаются"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "Lots or serial numbers were not provided for tracked products"
msgstr ""
"Партийные или серийные номера не были предоставлены для отслеживания товаров"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_production_lot_form
#: model:ir.ui.menu,name:stock.menu_action_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_tree
msgid "Lots/Serial Numbers"
msgstr "Партии/Серийные номера"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_production_lot_form
msgid ""
"Lots/Serial numbers help you tracking the path followed by your products.\n"
"            From their traceability report you will see the full history of their use, as well as their composition."
msgstr ""
"Партии/серийные номера помогают отслеживать маршрут, пройденный вашей "
"продукцией. Из отчета о трассировке вы увидите полную историю их применения,"
" а также комплектацию."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__mto_pull_id
msgid "MTO rule"
msgstr "MTO правило"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_main_attachment_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_main_attachment_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_main_attachment_id
msgid "Main Attachment"
msgstr "Основное вложение"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Make To Order"
msgstr "Сделать на Заказ"

#. module: stock
#: model:res.groups,name:stock.group_tracking_owner
msgid "Manage Different Stock Owners"
msgstr "Управление различными владельцами запасов"

#. module: stock
#: model:res.groups,name:stock.group_production_lot
msgid "Manage Lots / Serial Numbers"
msgstr "Управление Партиями/Серийными номерами"

#. module: stock
#: model:res.groups,name:stock.group_stock_multi_locations
msgid "Manage Multiple Stock Locations"
msgstr "Управление несколькими местонахождению на складе"

#. module: stock
#: model:res.groups,name:stock.group_stock_multi_warehouses
msgid "Manage Multiple Warehouses"
msgstr "Управление несколькими внутренними складами"

#. module: stock
#: model:res.groups,name:stock.group_tracking_lot
msgid "Manage Packages"
msgstr "Управление упаковкой"

#. module: stock
#: model:res.groups,name:stock.group_adv_location
msgid "Manage Push and Pull inventory flows"
msgstr "Управление толкать и тянуть потоков инвентаризации"

#. module: stock
#: model:res.groups,name:stock.group_stock_storage_categories
msgid "Manage Storage Categories"
msgstr "Управлять категориями мест хранения"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Manage product packagings (e.g. pack of 6 bottles, box of 10 pieces)"
msgstr ""
"Управляйте товарами в блоках (например, упаковка из 6 бутылок, коробка по 10"
" штук)"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse_orderpoint__trigger__manual
msgid "Manual"
msgstr "Вручную"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__auto__manual
msgid "Manual Operation"
msgstr "Ручное управление"

#. module: stock
#: code:addons/stock/wizard/product_replenish.py:0
#: code:addons/stock/wizard/product_replenish.py:0
#, python-format
msgid "Manual Replenishment"
msgstr "Пополнение вручную"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__reservation_method__manual
msgid "Manually"
msgstr "Вручную"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Manufacturing"
msgstr "Обрабатывающая промышленность"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__3
msgid "March"
msgstr "Март"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Mark as Todo"
msgstr "Отметить"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_max_qty
msgid "Max Quantity"
msgstr "Макс. количество"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__max_weight
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__max_weight
msgid "Max Weight"
msgstr "Максимальный вес"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_tree
msgid "Max Weight (kg)"
msgstr ""

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_max_weight
msgid "Max Weight must be positive"
msgstr "Максимальный вес должен быть положительным"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_positive_max_weight
msgid "Max weight should be a positive number."
msgstr "Максимальный вес должен быть положительным числом."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__reservation_days_before_priority
msgid ""
"Maximum number of days before scheduled date that priority picking products "
"should be reserved."
msgstr ""
"Максимальное количество дней до запланированной даты, за которое необходимо "
"зарезервировать продукты для приоритетной комплектации."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__reservation_days_before
msgid ""
"Maximum number of days before scheduled date that products should be "
"reserved."
msgstr ""
"Максимальное количество дней до запланированной даты, за которое необходимо "
"зарезервировать продукцию."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__max_weight
msgid "Maximum weight shippable in this packaging"
msgstr "Максимальный вес, упакованный в эту упаковку"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__5
msgid "May"
msgstr "Май"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_has_error
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_has_error
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_has_error
msgid "Message Delivery error"
msgstr "Ошибка доставки сообщения"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__picking_warn_msg
#: model:ir.model.fields,field_description:stock.field_res_users__picking_warn_msg
msgid "Message for Stock Picking"
msgstr "Сообщение для комплектования запасов"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_ids
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_ids
msgid "Messages"
msgstr "Сообщения"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_removal__method
msgid "Method"
msgstr "Метод"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_min_qty
msgid "Min Quantity"
msgstr "Мин. количество"

#. module: stock
#: model:ir.model,name:stock.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "Правило минимального остатка"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__orderpoint_ids
msgid "Minimum Stock Rules"
msgstr "Правила минимальных запасов"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__move_ids
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__move_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__move_ids
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__move_id
msgid "Move"
msgstr "Перемещение"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
msgid "Move Detail"
msgstr "детали перемещения"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_type_entire_packs
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_entire_packs
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_type_entire_packs
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__show_entire_packs
msgid "Move Entire Packages"
msgstr "Переместить цели пакеты"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_label_layout__move_line_ids
#: model:ir.model.fields,field_description:stock.field_stock_move__move_line_ids
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__move_line_ids
#: model:ir.model.fields,field_description:stock.field_stock_package_level__move_line_ids
msgid "Move Line"
msgstr "Переместить Строку"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_line_nosuggest_ids
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_line_nosuggest_ids
msgid "Move Line Nosuggest"
msgstr "Непереминний строку перемещения"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree_detailed
msgid "Move Lines"
msgstr "Строки Перемещения"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_lines_count
msgid "Move Lines Count"
msgstr "Подсчет строки перемещений"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__origin_returned_move_id
msgid "Move that created the return move"
msgstr "Перемещение, которое создало возврат перемещения"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__product_return_moves
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
msgid "Moves"
msgstr "Перемещения"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__group_id
msgid ""
"Moves created through this orderpoint will be put in this procurement group."
" If none is given, the moves generated by stock rules will be grouped into "
"one big picking."
msgstr ""
"Перемещение, созданные с помощью этого пункта назначения, будут помещены в "
"эту группу закупок. Если ничего не задано, перемещение, созданные по "
"складским правилам, будут сгруппированы в одно большое комплектования."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_adv_location
msgid "Multi-Step Routes"
msgstr "Многошаговые маршруты"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_multiple
msgid "Multiple Quantity"
msgstr "Множитель на количество"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_capacity_unique_package_type
msgid "Multiple capacity rules for one package type."
msgstr "Несколько правил вместимости для одного типа упаковки."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_capacity_unique_product
msgid "Multiple capacity rules for one product."
msgstr "Несколько правил вместимости для одного продукта."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__my_activity_date_deadline
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Крайний срок моей активности "

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"My Company (Chicago) undertakes to do its best to supply performant services"
" in due time in accordance with the agreed timeframes. However, none of its "
"obligations can be considered as being an obligation to achieve results. My "
"Company (Chicago) cannot under any circumstances, be required by the client "
"to appear as a third party in the context of any claim for damages filed "
"against the client by an end consumer."
msgstr ""
"Компания обязуется сделать все возможное для своевременного предоставления "
"качественных услуг в соответствии с оговоренными сроками. Однако ни одно из "
"ее обязательств не может рассматриваться как требование достижения "
"результата. Компания ни при каких обстоятельствах не может быть привлечена "
"клиентом к выступлению в качестве третьей стороны в контексте любого иска о "
"возмещении ущерба, поданного против клиента конечным потребителем."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "My Counts"
msgstr "Мои подсчеты"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "My Transfers"
msgstr "Мои перемещения"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_removal__name
#: model:ir.model.fields,field_description:stock.field_stock_rule__name
#: model:ir.model.fields,field_description:stock.field_stock_track_line__product_display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__name
msgid "Name"
msgstr "Название"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__nbr_moves_in
#: model:ir.model.fields,field_description:stock.field_product_template__nbr_moves_in
msgid "Nbr Moves In"
msgstr "Кол-во движений в"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__nbr_moves_out
#: model:ir.model.fields,field_description:stock.field_product_template__nbr_moves_out
msgid "Nbr Moves Out"
msgstr "Кол-во движений из"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_search_form_view_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_product_search_form_view
msgid "Negative Forecasted Quantity"
msgstr "Количество негативных прогнозов"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Negative Stock"
msgstr "Отрицательный Запас"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__net_weight
msgid "Net Weight"
msgstr "Масса нетто"

#. module: stock
#: code:addons/stock/models/stock_scrap.py:0
#: code:addons/stock/models/stock_scrap.py:0
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__draft
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__new
#, python-format
msgid "New"
msgstr "Новое"

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid "New Move:"
msgstr "Новое перемещение:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__new_quantity
msgid "New Quantity on Hand"
msgstr "Новое количество в наличии"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_form
msgid "New Transfer"
msgstr "Новое перемещение"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_calendar_event_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Календарное событие для следующего действия"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_date_deadline
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Крайний срок следующего действия"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_summary
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_summary
msgid "Next Activity Summary"
msgstr "Краткое описание следующего действия"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_type_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_type_id
msgid "Next Activity Type"
msgstr "Тип следующего действия"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__next_inventory_date
msgid "Next Expected Inventory"
msgstr "Следующая плановая инвентаризация"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__inventory_date
msgid "Next date the On Hand Quantity should be counted."
msgstr "На следующую дату необходимо подсчитать количество в наличии."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "Next transfer(s) impacted:"
msgstr "Следующие перемещения повлияли:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "No Backorder"
msgstr "Нет Возврата"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_partner__picking_warn__no-message
msgid "No Message"
msgstr "Не предупреждать"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_template__tracking__none
msgid "No Tracking"
msgstr "Не Отслеживать движение"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "No allocation need found for incoming products."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid "No negative quantities allowed"
msgstr "Не допускается отрицательных величин"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
msgid "No operation made on this lot."
msgstr "Нет операций для этой партии."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_get_picking_type_operations
msgid "No operations found. Let's create a tranfer !"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.product_template_action_product
msgid "No product found. Let's create one!"
msgstr "Нет продуктов. Можете создать."

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:0
#, python-format
msgid ""
"No products to return (only lines in Done state and not fully returned yet "
"can be returned)."
msgstr ""
"Нет товаров для возврата (могут быть возвращены только строки в состоянии "
"`Выполнено` и не полностью)."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_putaway_tree
msgid "No putaway rule found. Let's create one!"
msgstr "Нет информации о правилах приемки. Давайте создадим их!"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint
msgid "No reordering rule found"
msgstr "Нет правил дозаказа"

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid ""
"No rule has been found to replenish \"%s\" in \"%s\".\n"
"Verify the routes configuration on the product."
msgstr ""
"Не найдено правило для пополнения \"%s\" in \"%s\".\n"
"Проверьте конфигурацию маршрутов по продукту."

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid "No source location defined on stock rule: %s!"
msgstr "Местонахождение источника не определено по правилу запаса:%s !"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_action
msgid "No stock move found"
msgstr "Перемещение складских запасов не найдено"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "No Stock On Hand"
msgstr "Нет товара в наличии"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_picking_form
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_all
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_backorder
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_late
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_ready
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_waiting
#: model_terms:ir.actions.act_window,help:stock.action_picking_type_list
#: model_terms:ir.actions.act_window,help:stock.stock_picking_action_picking_type
msgid "No transfer found. Let's create one!"
msgstr "Нет перемещений."

#. module: stock
#: code:addons/stock/report/report_stock_reception.py:0
#, python-format
msgid "No transfers selected or a delivery order selected"
msgstr ""

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__priority__0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__priority__0
msgid "Normal"
msgstr "Нормальный"

#. module: stock
#. openerp-web
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/static/src/xml/forecast_widget.xml:0
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
#, python-format
msgid "Not Available"
msgstr "Не доступно"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Not Snoozed"
msgstr "Не приглушено"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Note"
msgstr "Заметка"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__note
msgid "Notes"
msgstr "Заметки"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Nothing to check the availability for."
msgstr "Нечего проверить на наличие."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__11
msgid "November"
msgstr "Ноябрь"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_needaction_counter
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_needaction_counter
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_needaction_counter
msgid "Number of Actions"
msgstr "Количество действий"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__next_serial_count
#: model:ir.model.fields,field_description:stock.field_stock_move__next_serial_count
msgid "Number of SN"
msgstr "Количество SN"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_has_error_counter
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_has_error_counter
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_has_error_counter
msgid "Number of errors"
msgstr "Количество ошибок"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__nbr_moves_in
#: model:ir.model.fields,help:stock.field_product_template__nbr_moves_in
msgid "Number of incoming stock moves in the past 12 months"
msgstr "Количество входящих перемещений запасов за последние 12 месяцев"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__message_needaction_counter
#: model:ir.model.fields,help:stock.field_stock_production_lot__message_needaction_counter
#: model:ir.model.fields,help:stock.field_stock_scrap__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Количество сообщений, требующих действия"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__message_has_error_counter
#: model:ir.model.fields,help:stock.field_stock_production_lot__message_has_error_counter
#: model:ir.model.fields,help:stock.field_stock_scrap__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Количество сообщений с ошибкой отправки"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__nbr_moves_out
#: model:ir.model.fields,help:stock.field_product_template__nbr_moves_out
msgid "Number of outgoing stock moves in the past 12 months"
msgstr "Количество исходящих перемещений запасов за последние 12 месяцев"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__message_unread_counter
#: model:ir.model.fields,help:stock.field_stock_production_lot__message_unread_counter
#: model:ir.model.fields,help:stock.field_stock_scrap__message_unread_counter
msgid "Number of unread messages"
msgstr "Количество непрочитанных сообщений"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__10
msgid "October"
msgstr "Октябрь"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__on_hand
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_on_hand
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_replenishment_header
msgid "On Hand"
msgstr "В наличии"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "On Hand Quantity"
msgstr "Количество в наличии"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__available_quantity
msgid ""
"On hand quantity which hasn't been reserved on a transfer, in the default "
"unit of measure of the product"
msgstr ""
"Количество на складе, которое не было зарезервировано при перемещении, в "
"единицах измерения продукта по умолчанию"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_kanban_stock_view
msgid "On hand:"
msgstr "В наличии:"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Only a stock manager can validate an inventory adjustment."
msgstr "Только менеджер по запасам может подтвердить корректировку запасов."

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_type_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_type_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__name
#: model:ir.model.fields,field_description:stock.field_stock_rule__picking_type_id
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#, python-format
msgid "Operation Type"
msgstr "Тип операции"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__return_picking_type_id
msgid "Operation Type for Returns"
msgstr "Тип операции для возвратов"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking_type_label
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_tree
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Operation Types"
msgstr "Типы операций"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Operation not supported"
msgstr "Операция не поддерживается"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_id
msgid "Operation type"
msgstr "Тип операции"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_picking_type_label
msgid "Operation type (PDF)"
msgstr "Тип операции (PDF)"

#. module: stock
#: model:ir.actions.report,name:stock.label_picking_type
msgid "Operation type (ZPL)"
msgstr "Тип операции (ZPL)"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_get_picking_type_operations
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_line_ids
#: model:ir.ui.menu,name:stock.menu_stock_warehouse_mgmt
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Operations"
msgstr "Операции"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_type_list
#: model:ir.ui.menu,name:stock.menu_pickingtype
msgid "Operations Types"
msgstr "Типы операций"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_line_ids_without_package
msgid "Operations without package"
msgstr "Операции без упаковки"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__partner_id
msgid ""
"Optional address where goods are to be delivered, specifically used for "
"allotment"
msgstr ""
"Дополнительный адрес доставки товара, используется специально для выделения"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__posx
#: model:ir.model.fields,help:stock.field_stock_location__posy
#: model:ir.model.fields,help:stock.field_stock_location__posz
msgid "Optional localization details, for information purpose only"
msgstr "Дополнительные подробности локализации, только в информативных целях"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__returned_move_ids
msgid "Optional: all returned moves created from this move"
msgstr ""
"Дополнительно: все возвращенные перемещения, созданные из этого перемещения"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__move_dest_ids
msgid "Optional: next stock move when chaining them"
msgstr "Опционально: следующее перемещение ТМЦ при его сцеплении"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__move_orig_ids
msgid "Optional: previous stock move when chaining them"
msgstr "Необязательно: предыдущее перемещение запаса при объединении цепочки"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Options"
msgstr "Параметры"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "Order"
msgstr "Заказ"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Order Once"
msgstr "Разовый заказ"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Order signed"
msgstr "Ордер подписан"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Order signed by %s"
msgstr "Заказ подписан %s "

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__orderpoint_ids
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__orderpoint_id
msgid "Orderpoint"
msgstr "Orderpoint"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Origin"
msgstr "Источник"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Origin Moves"
msgstr "происхождение перемещения"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__origin_returned_move_id
msgid "Origin return move"
msgstr "Происхождение Перемещения Возврата"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__original_location_id
msgid "Original Location"
msgstr "происхождение местонахождение"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_orig_ids
msgid "Original Move"
msgstr "Оригинал Перемещения"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__orderpoint_id
msgid "Original Reordering Rule"
msgstr "Исходное правило дозаказа"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Other Information"
msgstr "Прочая информация"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"Our invoices are payable within 21 working days, unless another payment "
"timeframe is indicated on either the invoice or the order. In the event of "
"non-payment by the due date, My Company (Chicago) reserves the right to "
"request a fixed interest payment amounting to 10% of the sum remaining due. "
"My Company (Chicago) will be authorized to suspend any provision of services"
" without prior warning in the event of late payment."
msgstr ""
"Наши счета подлежат оплате в течение 21 рабочего дня, если в счете или "
"заказе не указан иной срок оплаты. В случае неуплаты в установленный срок "
"компания оставляет за собой право потребовать уплаты фиксированного процента"
" в размере 10% от суммы, оставшейся к оплате. В случае просрочки платежа "
"компания имеет право приостановить предоставление услуг без предварительного"
" предупреждения."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__out_type_id
msgid "Out Type"
msgstr "Тип отгрузки"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__outgoing_qty
#: model:ir.model.fields,field_description:stock.field_product_template__outgoing_qty
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Outgoing"
msgstr "Исходящие"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Outgoing Draft Transfer"
msgstr "Черновик исходящего перемещения"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__outgoing_move_line_ids
msgid "Outgoing Move Line"
msgstr "Строка по исходящему перемещению"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__delivery_steps
msgid "Outgoing Shipments"
msgstr "Исходящие грузы"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Output"
msgstr "Выход"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_output_stock_loc_id
msgid "Output Location"
msgstr "Зона отгрузки"

#. module: stock
#: model:ir.ui.menu,name:stock.stock_picking_type_menu
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rules_report
msgid "Overview"
msgstr "Обзор"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__owner_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__owner_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__owner_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
msgid "Owner"
msgstr "Владелец"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__restrict_partner_id
msgid "Owner "
msgstr "Владелец "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Owner :"
msgstr "владелец:"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "P&L Qty"
msgstr "Кол-во п/у"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_traceability_report_backend.xml:0
#, python-format
msgid "PRINT"
msgstr "ПЕЧАТЬ"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Pack"
msgstr "Упаковка"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__pack_type_id
msgid "Pack Type"
msgstr "Тип упаковки"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__delivery_steps__pick_pack_ship
msgid "Pack goods, send goods in output and then deliver (3 steps)"
msgstr ""
"Упаковывать товары, переместить в зону погрузки, а затем отгрузить (3 шага)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__package_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__package_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__package_id
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__package
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_tree
msgid "Package"
msgstr "Пакет"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_quant_package_barcode_small
msgid "Package Barcode (PDF)"
msgstr "Штрих-код упаковки (PDF)"

#. module: stock
#: model:ir.actions.report,name:stock.label_package_template
msgid "Package Barcode (ZPL)"
msgstr "Штрих-код упаковки (ZPL)"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_quant_package_barcode
msgid "Package Barcode with Content"
msgstr "Штрих-код упаковки с содержанием"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__package_capacity_ids
msgid "Package Capacity"
msgstr "Емкость упаковки"

#. module: stock
#: code:addons/stock/models/stock_package_level.py:0
#, python-format
msgid "Package Content"
msgstr "содержимое упаковки"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__package_level_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__package_level_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__package_level_ids
msgid "Package Level"
msgstr "уровень упаковки"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__package_level_ids_details
msgid "Package Level Ids Details"
msgstr "Id деталей уровня упаковки"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
msgid "Package Name"
msgstr "Наименование упаковки"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__name
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Package Reference"
msgstr "Ссылка на Пакет"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Package Transfers"
msgstr "Трансферы Пакетов"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_packaging__package_type_id
#: model:ir.model.fields,field_description:stock.field_stock_package_type__name
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__package_type_ids
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__package_type_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__package_type_id
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Package Type"
msgstr "Тип упаковки"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_package_type_view
#: model:ir.ui.menu,name:stock.menu_packaging_types
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_tree
msgid "Package Types"
msgstr "Типы упаковок"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__package_use
msgid "Package Use"
msgstr "Использование пакета"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Package type"
msgstr "Тип упаковки"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_package_view
#: model:ir.model,name:stock.model_stock_quant_package
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_tracking_lot
#: model:ir.ui.menu,name:stock.menu_package
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Packages"
msgstr "Упаковка"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_package_view
msgid ""
"Packages are usually created via transfers (during pack operation) and can contain different products.\n"
"                Once created, the whole package can be moved at once, or products can be unpacked and moved as single units again."
msgstr ""
"Расфасовка обычно создается путем перемещения (во время операции по "
"упаковке) и может содержать различные продукты. После создания упаковка "
"может быть перемещена целиком за один раз, либо продукты могут быть "
"распакованы и снова перемещены как отдельные единицы."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_packaging_id
msgid "Packaging"
msgstr "Упаковка"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__height
msgid "Packaging Height"
msgstr "Высота упаковки"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__packaging_length
msgid "Packaging Length"
msgstr "Длина упаковки"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__width
msgid "Packaging Width"
msgstr "Ширина упаковки"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__packaging_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Packagings"
msgstr "Упаковка"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_pack_stock_loc_id
msgid "Packing Location"
msgstr "Зона упаковки"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Packing Zone"
msgstr "Зона упаковки"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_compute_wizard
msgid "Parameters"
msgstr "Параметры"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__location_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__parent_location_id
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Parent Location"
msgstr "Главное место хранения"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__parent_path
msgid "Parent Path"
msgstr "Родительский путь"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__procurement_group__move_type__direct
msgid "Partial"
msgstr "Частично"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__partially_available
msgid "Partially Available"
msgstr "Доступно частично"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__partner_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Partner"
msgstr "Партнёр"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__partner_address_id
msgid "Partner Address"
msgstr "Адрес партнера"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__pick_ids
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__pick_ids
#, python-format
msgid "Pick"
msgstr "Комплектование"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__pick_type_id
msgid "Pick Type"
msgstr "Тип Комплектования"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__picking_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Picking"
msgstr "Комплектование"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Picking Lists"
msgstr "Комплектовочные листы"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_picking
msgid "Picking Operations"
msgstr "Операции Комплектования"

#. module: stock
#: model:ir.model,name:stock.model_stock_picking_type
msgid "Picking Type"
msgstr "Тип комплектования"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__picking_type_code_domain
msgid "Picking Type Code Domain"
msgstr "Кодовый домен типа комплектования"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Picking list"
msgstr "Комплектовочный лист"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Pickings already processed"
msgstr "Комплектование уже осуществлено"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Planned Transfer"
msgstr "Плановое перемещение"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/js/stock_rescheduling_popover.js:0
#, python-format
msgid "Planning Issue"
msgstr "Не терпит отлагательств"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Planning Issues"
msgstr "Не терпят отлагательств"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Please add 'Done' quantities to the picking to create a new pack."
msgstr "Для создания новой комплектации добавьте к сборке количество \"Готово\"."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Please add some items to move."
msgstr "Добавьте несколько элементов для перемещения."

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:0
#, python-format
msgid "Please specify at least one non-zero quantity."
msgstr "Пожалуйста, укажите как минимум одно ненулевое количество."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_reserved
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__show_reserved
msgid "Pre-fill Detailed Operations"
msgstr "Предварительно заполнить детальные операции"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/popover_widget.xml:0
#, python-format
msgid "Preceding operations"
msgstr "Предыдущие операции"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__route_id
msgid "Preferred Route"
msgstr "Рекомендуемый маршрут"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__route_ids
msgid "Preferred Routes"
msgstr "Предпочтительные маршруты"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__route_ids
msgid "Preferred route"
msgstr "Рекомендуемый маршрут"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"Press the CREATE button to define quantity for each product in your stock or"
" import them from a spreadsheet throughout Favorites"
msgstr ""
"Нажмите кнопку СОЗДАТЬ, чтобы определить количество каждого продукта на "
"складе или импортировать их из электронной таблицы в Избранное."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Print"
msgstr "Печать"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__print_label
msgid "Print Label"
msgstr "Печать этикеток"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/report_stock_reception.xml:0
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#, python-format
msgid "Print Labels"
msgstr "Печать этикеток"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__printed
msgid "Printed"
msgstr "Напечатан"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__priority
#: model:ir.model.fields,field_description:stock.field_stock_picking__priority
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__sequence
msgid "Priority"
msgstr "Приоритет"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__delay_alert_date
msgid "Process at this date to be on time"
msgstr "Обработка в этот день должна быть своевременной"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Process operations faster with barcodes"
msgstr "Выполняйте операции быстрее при помощи штрих-кодов"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Process operations in wave transfers"
msgstr "Запустить операции при массовом перемещении"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Process transfers in batch per worker"
msgstr "Возможность перемещать товары группами"

#. module: stock
#: model:ir.model,name:stock.model_procurement_group
#: model:ir.model.fields,field_description:stock.field_stock_move__group_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__group_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__group_id
msgid "Procurement Group"
msgstr "Группа поставки"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.procurement_group_form_view
msgid "Procurement group"
msgstr "Группа поставки"

#. module: stock
#: model:ir.actions.server,name:stock.ir_cron_scheduler_action_ir_actions_server
#: model:ir.cron,cron_name:stock.ir_cron_scheduler_action
#: model:ir.cron,name:stock.ir_cron_scheduler_action
msgid "Procurement: run scheduler"
msgstr "Обеспечение: запуск планировщика"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__produce_line_ids
msgid "Produce Line"
msgstr "строка изготовления"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Produced Qty"
msgstr "Произведенное кол-во"

#. module: stock
#: model:ir.model,name:stock.model_product_product
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__product_id
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__product_id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_id
#: model:ir.model.fields,field_description:stock.field_stock_move__product_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__product_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__product_id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__product_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_id
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__product_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__product_id
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__product_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__product_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__product_id
#: model:ir.model.fields,field_description:stock.field_stock_track_line__product_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__product_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__product_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Product"
msgstr "Продукт"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__products_availability
msgid "Product Availability"
msgstr "наличие товара"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__product_capacity_ids
msgid "Product Capacity"
msgstr "Объем продукции"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__categ_ids
#: model:ir.ui.menu,name:stock.menu_product_category_config_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Product Categories"
msgstr "Категории продуктов"

#. module: stock
#: model:ir.model,name:stock.model_product_category
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__category_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_categ_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_category_id
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Product Category"
msgstr "Категория продукта"

#. module: stock
#: model:ir.actions.report,name:stock.label_product_product
msgid "Product Label (ZPL)"
msgstr "Метка товара (ZPL)"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_label_product_product_view
msgid "Product Label Report"
msgstr "Отчет о маркировке продукта"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
msgid "Product Lots Filter"
msgstr "Фильтр партий продукции"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_move_line_action
#: model:ir.ui.menu,name:stock.stock_move_line_menu
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
msgid "Product Moves"
msgstr "Движения продукта"

#. module: stock
#: model:ir.model,name:stock.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Перемещение товара (Строка складского перемещения)"

#. module: stock
#: model:ir.model,name:stock.model_product_packaging
msgid "Product Packaging"
msgstr "Тара"

#. module: stock
#: model:ir.actions.report,name:stock.label_product_packaging
msgid "Product Packaging (ZPL)"
msgstr "Товар в блоке (ZPL)"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_product_packagings
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Product Packagings"
msgstr "Товары в блоках"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Product Quantity Confirmed"
msgstr "Количество товара подтверждено"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Product Quantity Updated"
msgstr "Количество товара обновлено"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/js/report_stock_forecasted.js:0
#: model:ir.model,name:stock.model_product_replenish
#, python-format
msgid "Product Replenish"
msgstr "Пополнение товара"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_stock_rule
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rules_report
msgid "Product Routes Report"
msgstr "Отчет о маршрутах товаров"

#. module: stock
#: model:ir.model,name:stock.model_product_template
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_move__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_tmpl_id
msgid "Product Template"
msgstr "Шаблон продукта"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__product_tmpl_id
msgid "Product Tmpl"
msgstr "шаблон товара"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap__tracking
msgid "Product Tracking"
msgstr "Отслеживание продукта"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__detailed_type
#: model:ir.model.fields,field_description:stock.field_product_template__detailed_type
#: model:ir.model.fields,field_description:stock.field_stock_move__product_type
msgid "Product Type"
msgstr "Тип продукта"

#. module: stock
#: model:ir.model,name:stock.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Ед. изм. продукта"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_product_normal_action
#: model:ir.ui.menu,name:stock.product_product_menu
msgid "Product Variants"
msgstr "Варианты продукта"

#. module: stock
#: code:addons/stock/report/product_label_report.py:0
#, python-format
msgid "Product model not defined, Please contact your administrator."
msgstr "Модель продукта не определена, обратитесь к администратору."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid ""
"Product this lot/serial number contains. You cannot change it anymore if it "
"has already been moved."
msgstr ""
"Товар, который содержится в партии / серийном номере. Вы не можете изменить "
"его, если он уже перемещен."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_uom_name
msgid "Product unit of measure label"
msgstr "Обозначение единицы измерения продукта"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__has_tracking
msgid "Product with Tracking"
msgstr "Продукт с отслеживанием"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__production
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Production"
msgstr "Производство"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__property_stock_production
#: model:ir.model.fields,field_description:stock.field_product_template__property_stock_production
msgid "Production Location"
msgstr "Место производства"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Production Locations"
msgstr "Места хранения продукции"

#. module: stock
#: code:addons/stock/wizard/stock_quantity_history.py:0
#: model:ir.actions.act_window,name:stock.act_product_location_open
#: model:ir.actions.act_window,name:stock.product_template_action_product
#: model:ir.model.fields,field_description:stock.field_stock_location_route__product_ids
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__product_ids
#: model:ir.ui.menu,name:stock.menu_product_in_config_stock
#: model:ir.ui.menu,name:stock.menu_product_variant_config_stock
#: model:ir.ui.menu,name:stock.menu_stock_inventory_control
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#, python-format
msgid "Products"
msgstr "Продукты"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__products_availability_state
msgid "Products Availability State"
msgstr "Статус доступности продуктов"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__priority
msgid ""
"Products will be reserved first for the transfers with the highest "
"priorities."
msgstr ""
"Первыми будут зарезервированы продукты для переводов с наивысшим "
"приоритетом."

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Products: %(location)s"
msgstr "Продукты: %(location)s"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__group_propagation_option__propagate
msgid "Propagate"
msgstr "Распространение"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__propagate_cancel
msgid "Propagate cancel and split"
msgstr "Отменить размножение и разделить"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Propagation"
msgstr "распространение"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__group_propagation_option
msgid "Propagation of Procurement Group"
msgstr "Распространение группы поставки"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__propagate_carrier
msgid "Propagation of carrier"
msgstr "Перемещение перевозчика"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__action__pull_push
msgid "Pull & Push"
msgstr "перемещение"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__action__pull
msgid "Pull From"
msgstr "Получить из"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Pull Rule"
msgstr "правило вытягивания"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Push Rule"
msgstr "Push Правило"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__action__push
msgid "Push To"
msgstr "перетащить на"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Put in Pack"
msgstr "упаковать"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Put your products in packs (e.g. parcels, boxes) and track them"
msgstr ""
"Помещайте свои товары в упаковку (например, посылки, коробки) и отслеживайте"
" их"

#. module: stock
#: model:ir.model,name:stock.model_stock_putaway_rule
msgid "Putaway Rule"
msgstr "Правило приемки на склад"

#. module: stock
#: code:addons/stock/models/product.py:0
#: model:ir.actions.act_window,name:stock.category_open_putaway
#: model:ir.actions.act_window,name:stock.location_open_putaway
#: model:ir.model.fields,field_description:stock.field_product_category__putaway_rule_ids
#: model:ir.model.fields,field_description:stock.field_product_product__putaway_rule_ids
#: model:ir.model.fields,field_description:stock.field_stock_location__putaway_rule_ids
#: model:ir.ui.menu,name:stock.menu_putaway
#: model_terms:ir.ui.view,arch_db:stock.product_category_form_view_inherit
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_product_view_form_easy_inherit_stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
#, python-format
msgid "Putaway Rules"
msgstr "Правила приемки на склад"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Putaway:"
msgstr "Удаление:"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_putaway_tree
msgid "Putaways Rules"
msgstr "Правила приемки на склад"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_orderpoint_qty_multiple_check
msgid "Qty Multiple must be greater than or equal to zero."
msgstr "Многократное количество должно быть больше или равным нулю."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_quality_control
msgid "Quality"
msgstr "Качество"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Quality Control"
msgstr "Контроль качества"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_qc_stock_loc_id
msgid "Quality Control Location"
msgstr "Зона контроля качества"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_quality_control_worksheet
msgid "Quality Worksheet"
msgstr "Производственные листы проверки качества"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_location__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_request_count__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__quant_ids
msgid "Quant"
msgstr "количество"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"Quants are auto-deleted when appropriate. If you must manually delete them, "
"please ask a stock manager to do it."
msgstr ""
"Количественный анализ автоматически удаляется при необходимости. Если "
"необходимо удалить вручную, попросите об этом менеджера по снабжению."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Quant's creation is restricted, you can't do this operation."
msgstr ""
"Создание количественного анализа ограничено, вы не можете выполнить эту "
"операцию."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Quant's editing is restricted, you can't do this operation."
msgstr ""
"Редактирование количественного анализа ограничено, вы не можете выполнить "
"эту операцию."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Quantities Already Set"
msgstr "Количества уже установлены"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Quantities To Reset"
msgstr "Сброс подсчета"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__quantity
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__product_qty
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__product_qty
#: model:ir.model.fields,field_description:stock.field_stock_quant__quantity
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__quantity
#: model:ir.model.fields,field_description:stock.field_stock_scrap__scrap_qty
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__quantity
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__quantity
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__quantity
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Quantity"
msgstr "Количество"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Quantity :"
msgstr "количество:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__quantity_done
#: model_terms:ir.ui.view,arch_db:stock.view_move_kandan
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree_detailed
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_kanban
msgid "Quantity Done"
msgstr "Количество выполнено"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "Quantity Multiple"
msgstr "Множитель количества"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__qty_available
#: model:ir.model.fields,field_description:stock.field_product_template__qty_available
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form_editable
msgid "Quantity On Hand"
msgstr "Количество в наличии"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__reserved_availability
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form_editable
msgid "Quantity Reserved"
msgstr "Зарезервированное Количество"

#. module: stock
#: code:addons/stock/wizard/stock_change_product_qty.py:0
#, python-format
msgid "Quantity cannot be negative."
msgstr "Количество не может быть отрицательным."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__is_outdated
msgid "Quantity has been moved since last count"
msgstr "Количество было перемещено с момента последнего подсчета"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__availability
msgid "Quantity in stock that can still be reserved for this move"
msgstr ""
"Количество на складе, которое еще может быть зарезервировано для этого "
"перемещения"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__product_qty
msgid "Quantity in the default UoM of the product"
msgstr "Количество в единицах измерения продукта по умолчанию"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__incoming_qty
msgid ""
"Quantity of planned incoming products.\n"
"In a context with a single Stock Location, this includes goods arriving to this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods arriving to the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods arriving to any Stock Location with 'internal' type."
msgstr ""
"Количество запланированных входящих товаров. В контексте одного места "
"хранения, это включает товары, прибывающие в это место, или любое вложенное."
" В контексте одного склада это включает товары, прибывающие в место хранения"
" запасов этого склада, или любое вложенное. В остальных случаях это включает"
" товары, прибывающие в любое место хранения внутреннего типа."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__outgoing_qty
msgid ""
"Quantity of planned outgoing products.\n"
"In a context with a single Stock Location, this includes goods leaving this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods leaving the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods leaving any Stock Location with 'internal' type."
msgstr ""
"Количество запланированных исходящих товаров. В контексте, состоящий из "
"одного места хранения, это включает товары, которые покидают это место или "
"любое вложенное. В контексте склада это включает товары, которые покидают "
"место хранения запасов на этом складе или любое вложенное. В остальных "
"случаях это включает в себя товары, покидают любое место хранения "
"внутреннего типа."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__quantity
msgid ""
"Quantity of products in this quant, in the default unit of measure of the "
"product"
msgstr ""
"Количество продуктов в этом кванте, в единице измерения продукта по "
"умолчанию"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__reserved_quantity
msgid ""
"Quantity of reserved products in this quant, in the default unit of measure "
"of the product"
msgstr ""
"Количество зарезервированных товаров в этом количестве по умолчанию единицы "
"измерения товара"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_capacity_positive_quantity
msgid "Quantity should be a positive number."
msgstr "Количество должно быть положительным числом."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__reserved_availability
msgid "Quantity that has already been reserved for this move"
msgstr "Количество, которое уже было зарезервировано для этого перемещения"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_label_layout__picking_quantity
msgid "Quantity to print"
msgstr "Количество для печати"

#. module: stock
#: model:ir.model,name:stock.model_stock_quant
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__quant_ids
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Quants"
msgstr "Части"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Quants cannot be created for consumables or services."
msgstr "Количества нельзя создавать для расходных материалов или услуг."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__assigned
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Ready"
msgstr "Можно перемещать"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_qty
msgid "Real Quantity"
msgstr "Реальное Количество"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_qty
msgid "Real Reserved Quantity"
msgstr "Реальная зарезервирована количество"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__code__incoming
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Receipt"
msgstr "Приемка"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__reception_route_id
msgid "Receipt Route"
msgstr "Маршрут получения"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#: model:stock.picking.type,name:stock.chi_picking_type_in
#: model:stock.picking.type,name:stock.picking_type_in
#, python-format
msgid "Receipts"
msgstr "Приемка"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Receive From"
msgstr "Получить от"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__reception_steps__one_step
msgid "Receive goods directly (1 step)"
msgstr "Получить товары сразу (1 шаг)"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__reception_steps__two_steps
msgid "Receive goods in input and then stock (2 steps)"
msgstr "Получить товары в зоне осмотра, а затем передать на склад (2 шага)"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__reception_steps__three_steps
msgid "Receive goods in input, then quality and then stock (3 steps)"
msgstr ""
"Получить товары в зоне осмотра, проверить на качество, а затем передать на "
"склад (3 шага)"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Receive in 1 step (stock)"
msgstr "Получить за 1 шаг (склад)"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Receive in 2 steps (input + stock)"
msgstr "Получить за 2 шага (приемка + склад)"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Receive in 3 steps (input + quality + stock)"
msgstr "Получить за 3 шага (приемка + качество + склад)"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Received Qty"
msgstr "Полученное кол-во"

#. module: stock
#: model:ir.actions.client,name:stock.stock_reception_action
#: model:ir.actions.report,name:stock.stock_reception_report_action
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_reception_report
msgid "Reception Report"
msgstr "Отчет о поставках"

#. module: stock
#: model:ir.actions.report,name:stock.label_picking
msgid "Reception Report Label"
msgstr "Этикетка отчета о поставках"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__name
#: model:ir.model.fields,field_description:stock.field_stock_move__reference
#: model:ir.model.fields,field_description:stock.field_stock_move_line__reference
#: model:ir.model.fields,field_description:stock.field_stock_picking__name
#: model:ir.model.fields,field_description:stock.field_stock_scrap__name
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
msgid "Reference"
msgstr "Ссылка"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__sequence_id
msgid "Reference Sequence"
msgstr "Используемая нумерация"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_picking_name_uniq
msgid "Reference must be unique per company!"
msgstr "Ссылка должна быть уникальной в рамках одной компании!"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__origin
msgid "Reference of the document"
msgstr "Ссылка на документ"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
msgid "Reference:"
msgstr "Ссылка:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_kandan
msgid "Register lots, packs, location"
msgstr "Регистрация партий, упаковок, мест хранения"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__stock_move_ids
msgid "Related Stock Moves"
msgstr "Связанные складские перемещения"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Remaining parts of picking partially processed"
msgstr "Остальные части упаковки частично обработаны"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_removal
msgid "Removal"
msgstr "Удаление"

#. module: stock
#: model:ir.model,name:stock.model_product_removal
#: model:ir.model.fields,field_description:stock.field_stock_location__removal_strategy_id
msgid "Removal Strategy"
msgstr "Стратегия ротации"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Removal strategy %s not implemented."
msgstr "Стратегия ротации %s не реализована."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__reordering_max_qty
#: model:ir.model.fields,field_description:stock.field_product_template__reordering_max_qty
msgid "Reordering Max Qty"
msgstr "Максимальное количество дозаказа"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__reordering_min_qty
#: model:ir.model.fields,field_description:stock.field_product_template__reordering_min_qty
msgid "Reordering Min Qty"
msgstr "Минимальное количество дозаказа"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Reordering Rule"
msgstr "Правило дозаказа"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_orderpoint
#: model:ir.model.fields,field_description:stock.field_product_product__nbr_reordering_rules
#: model:ir.model.fields,field_description:stock.field_product_template__nbr_reordering_rules
#: model:ir.ui.menu,name:stock.menu_reordering_rules_config
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable_config
msgid "Reordering Rules"
msgstr "Правила дозаказа"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Reordering Rules Search"
msgstr "Поиск правил дозаказа"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/report_stock_forecasted.xml:0
#: code:addons/stock/static/src/xml/report_stock_forecasted.xml:0
#: model:ir.actions.act_window,name:stock.action_product_replenish
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_product_view_form_easy_inherit_stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
#, python-format
msgid "Replenish"
msgstr "Пополнить"

#. module: stock
#: model:stock.location.route,name:stock.route_warehouse0_mto
msgid "Replenish on Order (MTO)"
msgstr "Пополнение заказа (Ведомость заказа материалов)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
msgid "Replenish wizard"
msgstr "Мастер пополнения"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_orderpoint_replenish
#: model:ir.actions.server,name:stock.action_replenishment
#: model:ir.ui.menu,name:stock.menu_reordering_rules_replenish
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Replenishment"
msgstr "Пополнение"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_replenishment_info
msgid "Replenishment Information"
msgstr "Информация о пополнении"

#. module: stock
#: code:addons/stock/models/stock_orderpoint.py:0
#, python-format
msgid "Replenishment Information for %s in %s"
msgstr "Информация о восполнении для %s в %s"

#. module: stock
#: code:addons/stock/models/stock_orderpoint.py:0
#, python-format
msgid "Replenishment Report"
msgstr "Отчет о восполнении"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Replenishment Report Search"
msgstr "Поиск отчетов о восполнении"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_search
msgid "Report Quantity"
msgstr "Количество отчетов"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_warehouse_report
msgid "Reporting"
msgstr "Отчетность"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_request_count
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Request a Count"
msgstr "Подать запрос на подсчет"

#. module: stock
#: model:res.groups,name:stock.group_stock_sign_delivery
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Require a signature on your delivery orders"
msgstr "Обязательная подпись на товарных накладных"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__reservation_method
msgid "Reservation Method"
msgstr "Метод резервирования"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Reservations"
msgstr "резервирование"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Reserve"
msgstr "Резерв"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_category__packaging_reserve_method__full
msgid "Reserve Only Full Packagings"
msgstr "Резервировать только полную тару"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_category__packaging_reserve_method
msgid ""
"Reserve Only Full Packagings: will not reserve partial packagings. If customer orders 2 pallets of 1000 units each and you only have 1600 in stock, then only 1000 will be reserved\n"
"Reserve Partial Packagings: allow reserving partial packagings. If customer orders 2 pallets of 1000 units each and you only have 1600 in stock, then 1600 will be reserved"
msgstr ""
"Резервировать только полную комплектацию: не будет резервироваться не полная сборка. Если клиент заказывает 2 паллеты по 1000 штук в каждой, а на складе есть только 1600, то будет зарезервировано только 1000 штук\n"
"Зарезервировать частичную упаковку: позволяет резервировать неполные паллеты. Если клиент заказывает 2 паллеты по 1000 штук, а на складе есть только 1600, то будет зарезервировано 1600 штук"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__packaging_reserve_method
msgid "Reserve Packagings"
msgstr "Резервировать упаковку"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_category__packaging_reserve_method__partial
msgid "Reserve Partial Packagings"
msgstr "Резервировать частичную упаковку"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Reserve before scheduled date"
msgstr "Резервировать до назначенной даты"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_uom_qty
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__assigned
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
msgid "Reserved"
msgstr "Зарезервировано"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__reserved_quantity
msgid "Reserved Quantity"
msgstr "Зарезервированное количество"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Reserved from stock"
msgstr "Зарезервировано на складе"

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid "Reserving a negative quantity is not allowed."
msgstr "Резервирование отрицательного количества не допускается."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__responsible_id
#: model:ir.model.fields,field_description:stock.field_product_template__responsible_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__user_id
msgid "Responsible"
msgstr "Ответственный"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_user_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_user_id
msgid "Responsible User"
msgstr "Ответственный"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Resupply"
msgstr "Пополнение запасов"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__resupply_wh_ids
msgid "Resupply From"
msgstr "Пополнение запасов из"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__resupply_route_ids
msgid "Resupply Routes"
msgstr "Маршруты снабжения"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
msgid "Return"
msgstr "Возврат"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__location_id
msgid "Return Location"
msgstr "Местоположение Возврата"

#. module: stock
#: model:ir.model,name:stock.model_stock_return_picking
msgid "Return Picking"
msgstr "Возвратная Комплектация"

#. module: stock
#: model:ir.model,name:stock.model_stock_return_picking_line
msgid "Return Picking Line"
msgstr "Вернуть строку комплектования"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__return_type_id
msgid "Return Type"
msgstr "Тип возврата"

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:0
#, python-format
msgid "Return of %s"
msgstr "Возвращение %s "

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:0
#, python-format
msgid "Returned Picking"
msgstr "Возвращенное комплектование"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Returns"
msgstr "Возвраты"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_quant_package__package_use__reusable
msgid "Reusable Box"
msgstr "Многоразовая коробка"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_package__package_use
msgid ""
"Reusable boxes are used for batch picking and emptied afterwards to be reused. In the barcode application, scanning a reusable box will add the products in this box.\n"
"        Disposable boxes aren't reused, when scanning a disposable box in the barcode application, the contained products are added to the transfer."
msgstr ""
"Многоразовые коробки используются для комплектации партий товаров и затем "
"освобождаются для повторного использования. При сканировании многоразовой "
"коробки в модуле Штрих-код в нее добавляется продукция, находящаяся в этой "
"коробке. Одноразовые коробки не используются повторно, при сканировании "
"одноразовой коробки в модуле Штрих-код содержащаяся в ней продукция "
"добавляется к перемещению."

#. module: stock
#: model:ir.actions.act_window,name:stock.act_stock_return_picking
msgid "Reverse Transfer"
msgstr "Обратное перемещение"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__name
#: model:ir.model.fields,field_description:stock.field_stock_rule__route_id
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Route"
msgstr "Маршрут"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__route_company_id
msgid "Route Company"
msgstr "Наметить маршрут"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__route_sequence
msgid "Route Sequence"
msgstr "Последовательность маршрута"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_routes_form
#: model:ir.actions.server,name:stock.action_open_routes
#: model:ir.model.fields,field_description:stock.field_product_category__route_ids
#: model:ir.model.fields,field_description:stock.field_product_packaging__route_ids
#: model:ir.model.fields,field_description:stock.field_product_product__route_ids
#: model:ir.model.fields,field_description:stock.field_product_template__route_ids
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__route_ids
#: model:ir.ui.menu,name:stock.menu_routes_config
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_tree
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Routes"
msgstr "Маршруты"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__has_available_route_ids
#: model:ir.model.fields,field_description:stock.field_product_template__has_available_route_ids
msgid "Routes can be selected on this product"
msgstr "Для данного продукта можно выбрать маршруты"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__resupply_wh_ids
msgid ""
"Routes will be created automatically to resupply this warehouse from the "
"warehouses ticked"
msgstr ""
"Маршруты будут созданы автоматически, чтобы пополнять этот склад из "
"отмеченных складов"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__resupply_route_ids
msgid ""
"Routes will be created for these resupply warehouses and you can select them"
" on products and product categories"
msgstr ""
"Маршруты будут созданы для этих складов снабжения, и вы сможете выбирать их "
"в продуктах и категориях продукции"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__rule_message
msgid "Rule Message"
msgstr "сообщение правила"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_rules_form
#: model:ir.model.fields,field_description:stock.field_stock_location_route__rule_ids
#: model:ir.ui.menu,name:stock.menu_action_rules_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_tree
msgid "Rules"
msgstr "Правила"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Rules on Categories"
msgstr "Правила для категорий"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Rules on Products"
msgstr "Правила для продуктов"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__rule_ids
msgid "Rules used"
msgstr "Используемые правила"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_procurement_compute
#: model:ir.ui.menu,name:stock.menu_procurement_compute
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_compute_wizard
msgid "Run Scheduler"
msgstr "Запустить планировщик"

#. module: stock
#: model:ir.model,name:stock.model_stock_scheduler_compute
msgid "Run Scheduler Manually"
msgstr "Запустить планировщик вручную"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "Run the scheduler"
msgstr "Запустить планировщик"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_sms
msgid "SMS Confirmation"
msgstr "SMS-подтверждение"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_has_sms_error
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_has_sms_error
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Ошибка доставки SMS"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid "STANDARD TERMS AND CONDITIONS OF SALE"
msgstr "СТАНДАРТНЫЕ ПОЛОЖЕНИЯ И УСЛОВИЯ ПРОДАЖИ"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "Sales History"
msgstr "История продаж"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__date_planned
#: model:ir.model.fields,field_description:stock.field_stock_picking__scheduled_date
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_date
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Scheduled Date"
msgstr "Запланированная дата"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__date
msgid "Scheduled date until move is done, then date of actual move processing"
msgstr ""
"Плановая дата до завершения перемещения, затем дата фактического процесса "
"перемещения"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Scheduled or processing date"
msgstr "Планируемая дата или дата обработки"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__scheduled_date
msgid ""
"Scheduled time for the first part of the shipment to be processed. Setting "
"manually a value here would set it as expected date for all the stock moves."
msgstr ""
"Запланированное для первой части отгрузки время должно быть обработано. "
"Установка значения вручную установит его в качестве предполагаемой даты для "
"всех складских перемещений."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model,name:stock.model_stock_scrap
#: model:ir.model.fields,field_description:stock.field_stock_move__scrap_ids
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__scrap_id
#: model:ir.ui.menu,name:stock.menu_stock_scrap
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view2
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#, python-format
msgid "Scrap"
msgstr "Брак"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap__scrap_location_id
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
msgid "Scrap Location"
msgstr "Место хранения брака"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap__move_id
msgid "Scrap Move"
msgstr "Перемещение брака"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_scrap
msgid "Scrap Orders"
msgstr "Записи о браке"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_stock_scrap
msgid "Scrap products"
msgstr "Забраковать товары"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__scrapped
msgid "Scrapped"
msgstr "Брак"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_stock_scrap
msgid ""
"Scrapping a product will remove it from your stock. The product will\n"
"                end up in a scrap location that can be used for reporting purpose."
msgstr ""
"Если товар забраковать, то он его не будет на складе. Он будет в месте "
"хранения брака, и может быть использован для целей отчетности."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Scraps"
msgstr "Брак"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Search Procurement"
msgstr "Искать поставку"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
msgid "Search Scrap"
msgstr "Поиск отходов"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__product_categ_id
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__product_category_id
msgid "Select category for the current product"
msgstr "Выберите категорию для продукта"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Select the places where this route can be selected"
msgstr "Выберите места, где этот маршрут можно выбрать"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner__picking_warn
#: model:ir.model.fields,help:stock.field_res_users__picking_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"\"Предупреждение\" - сообщить пользователю. \"Блокирующее сообщение\" - "
"исключительная ситуация, сообщить пользователю и заблокировать рабочий "
"процесс. Текст сообщения должен быть записан в следующее поле."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Sell and purchase products in different units of measure"
msgstr "Продавайте и покупайте товары в различных единицах измерения"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Send an automatic confirmation SMS Text Message when Delivery Orders are "
"done"
msgstr ""
"Отправить автоматическое подтверждающее SMS-сообщение, когда заказы на "
"отгрузку будут выполнены"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Send an automatic confirmation email when Delivery Orders are done"
msgstr ""
"Отправить автоматическое подтверждение по электронной почте, когда заказы на"
" отгрузку будут выполнены"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_lead_mass_mail
msgid "Send email"
msgstr "Отправить email"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__delivery_steps__pick_ship
msgid "Send goods in output and then deliver (2 steps)"
msgstr "Переместить товары в зону погрузки, а затем отгрузить (2 шага)"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__9
msgid "September"
msgstr "Сентябрь"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model.fields,field_description:stock.field_stock_location_route__sequence
#: model:ir.model.fields,field_description:stock.field_stock_move__sequence
#: model:ir.model.fields,field_description:stock.field_stock_package_type__sequence
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__sequence
#: model:ir.model.fields,field_description:stock.field_stock_rule__sequence
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__sequence
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
#, python-format
msgid "Sequence"
msgstr "Нумерация"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Sequence in"
msgstr "Последовательность в"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Sequence internal"
msgstr "Внутренняя последовательность"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Sequence out"
msgstr "Последовательность из"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Sequence packing"
msgstr "Последовательность упаковки"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Sequence picking"
msgstr "Последовательность выбора"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Sequence return"
msgstr "Возврат последовательности"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__lot_ids
msgid "Serial Numbers"
msgstr "Серийные номера"

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid ""
"Serial number (%s) already exists in location(s): %s. Please correct the "
"serial number encoded."
msgstr ""
"Серийный номер (%s) уже существует в месте(ах): %s. Пожалуйста, исправьте "
"кодировку серийного номера."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"Serial number (%s) is not located in %s, but is located in location(s): %s.\n"
"\n"
"Please correct this to prevent inconsistent data."
msgstr ""
"Серийный номер (%s) не находится в %s, а находится в месте(ах): %s.\n"
"\n"
"Пожалуйста, исправьте это, чтобы избежать несоответствия данных."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"Serial number (%s) is not located in %s, but is located in location(s): %s.\n"
"\n"
"Source location for this move will be changed to %s"
msgstr ""
"Серийный номер (%s) не находится в %s, а находится в месте(ах): %s.\n"
"\n"
"Исходное местоположение для этого перемещения будет изменено на %s"

#. module: stock
#: model:ir.actions.server,name:stock.action_view_set_quants_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Set"
msgstr "Указать"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_request_count__set_count__set
msgid "Set Current Value"
msgstr "Установить текущее значение"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set Warehouse Routes"
msgstr "Установите складские маршруты"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_category__removal_strategy_id
msgid ""
"Set a specific removal strategy that will be used regardless of the source location for this product category.\n"
"\n"
"FIFO: products/lots that were stocked first will be moved out first.\n"
"LIFO: products/lots that were stocked last will be moved out first.\n"
"Closet location: products/lots closest to the target location will be moved out first.\n"
"FEFO: products/lots with the closest removal date will be moved out first (the availability of this method depends on the \"Expiration Dates\" setting)."
msgstr ""
"Установите конкретную стратегию удаления, используемую независимо от расположения источника для данной категории продуктов. \n"
"\n"
"FIFO: товары/партии, которые были складированы первыми, будут перемещены первыми.\n"
"LIFO: товары/партии, которые были складированы последними, будут перемещены первыми. Местонахождение: продукты/партии, расположенные ближе всего к целевому месту, будут перемещены первыми.\n"
"FEFO: продукты/партии с ближайшей датой удаления будут перемещены первыми (доступность этого метода зависит от настройки \"Срок годности\")."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set expiration dates on lots &amp; serial numbers"
msgstr "Устанавливайте срок действия для партий и серийных номеров"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set owner on stored products"
msgstr "Устанавливайте владельца в карточке продуктов"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set product attributes (e.g. color, size) to manage variants"
msgstr ""
"Укажите атрибуты товара (например, цвет, размер), чтобы управлять вариантами"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Set quantities"
msgstr "Указать количество"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__location_id
msgid ""
"Sets a location if you produce at a fixed location. This can be a partner "
"location if you subcontract the manufacturing operations."
msgstr ""
"Установите место хранения, если вы производите в определенном месте. Это "
"может быть местом хранения партнера-субподрядчика."

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_config_settings
#: model:ir.ui.menu,name:stock.menu_stock_general_settings
msgid "Settings"
msgstr "Настройки"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__posy
msgid "Shelves (Y)"
msgstr "Полки (Y)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Shipments"
msgstr "Доставка"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Shipping"
msgstr "Доставка"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Shipping Connectors"
msgstr "Интеграция с курьерскими компаниями"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_type
msgid "Shipping Policy"
msgstr "Политика доставки"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Shipping connectors allow to compute accurate shipping costs, print shipping"
" labels and request carrier picking at your warehouse to ship to the "
"customer. Apply shipping connector from delivery methods."
msgstr ""
"Коннекторы для доставки позволяют вычислить точные расходы на доставку, "
"печатать ярлыки для доставки и спрашивать перевозчика на вашем складе для "
"доставки к заказчику. Примените коннектор доставки из способов доставки."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__code
msgid "Short Name"
msgstr "Короткое Наименование"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__code
msgid "Short name used to identify your warehouse"
msgstr "Краткое наименование используется для идентификации склада"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_allocation
msgid "Show Allocation"
msgstr "Показать планирование"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_check_availability
msgid "Show Check Availability"
msgstr "Показать проверку наличия"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_operations
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__show_operations
msgid "Show Detailed Operations"
msgstr "Показать подробные операции"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__show_forecasted_qty_status_button
#: model:ir.model.fields,field_description:stock.field_product_template__show_forecasted_qty_status_button
msgid "Show Forecasted Qty Status Button"
msgstr "Показать кнопку Статус прогнозируемого количества"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__show_lots_m2o
msgid "Show Lots M2O"
msgstr "Показать партии M2O"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__show_lots_text
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_lots_text
msgid "Show Lots Text"
msgstr "Показать описание партий"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_mark_as_todo
msgid "Show Mark As Todo"
msgstr "Показать Обозначены как сделать"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__show_on_hand_qty_status_button
#: model:ir.model.fields,field_description:stock.field_product_template__show_on_hand_qty_status_button
msgid "Show On Hand Qty Status Button"
msgstr "Показать кнопку Состояние остатков"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_operations
msgid "Show Operations"
msgstr "Показать операции"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_auto_reception_report
msgid "Show Reception Report at Validation"
msgstr "Показать отчет о приеме при проверке"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__show_transfers
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__show_transfers
msgid "Show Transfers"
msgstr "Показать перемещения"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_validate
msgid "Show Validate"
msgstr "Показать Подтвердить"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Показать все записи, у которых дата следующего действия не превышает текущей"
" даты"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rules_report__warehouse_ids
msgid "Show the routes that apply on selected warehouses."
msgstr "Покажите маршруты, которые применяются к избранному складов."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__show_info
msgid "Show warning"
msgstr "Показать предупреждение"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Sign"
msgstr "Подпись"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_sign_delivery
#: model:ir.model.fields,field_description:stock.field_stock_picking__signature
#: model:ir.model.fields,help:stock.field_stock_picking__signature
msgid "Signature"
msgstr "Подпись"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Signed"
msgstr "подписанный"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Size"
msgstr "Размер"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Size: Length × Width × Height"
msgstr "Размер: Длина × Ширина × Высота"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#: model:ir.actions.act_window,name:stock.action_orderpoint_snooze
#: model_terms:ir.ui.view,arch_db:stock.view_stock_orderpoint_snooze
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
#, python-format
msgid "Snooze"
msgstr "Приглушить"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__snoozed_until
msgid "Snooze Date"
msgstr "Дата приглушения"

#. module: stock
#: model:ir.model,name:stock.model_stock_orderpoint_snooze
msgid "Snooze Orderpoint"
msgstr "Отключить точку заказа"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__predefined_date
msgid "Snooze for"
msgstr "Приглушить на"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__snoozed_until
msgid "Snoozed"
msgstr "Приглушено"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid ""
"Some products of the inventory adjustment are tracked. Are you sure you "
"don't want to specify a serial or lot number for them?"
msgstr ""
"Отслеживаются некоторые товары корректировки запасов. Вы уверены, что не "
"хотите указать для них серийный номер или номер партии?"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_set_view
msgid "Some selected lines already have quantities set, they will be ignored."
msgstr ""
"Некоторые выделенные строки уже имеют заданные количества, они будут "
"исключены."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_adjustment_name_form_view
msgid ""
"Some selected lines don't have any quantities set, they will be ignored."
msgstr ""
"Некоторые выбранные строки не имеют заданных количеств, они будут исключены."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__origin
msgid "Source"
msgstr "Источник"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__origin
#: model:ir.model.fields,field_description:stock.field_stock_picking__origin
#: model:ir.model.fields,field_description:stock.field_stock_scrap__origin
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Source Document"
msgstr "Документ-источник"

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#: model:ir.model.fields,field_description:stock.field_stock_move__location_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__location_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__location_src_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__location_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#, python-format
msgid "Source Location"
msgstr "Первоначальное расположение"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Source Location:"
msgstr "Местонахождение источника:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__package_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
msgid "Source Package"
msgstr "Исходный Пакет"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Source Package :"
msgstr "Выходная упаковка:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Starred"
msgstr "Отмеченные"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__state
#: model:ir.model.fields,field_description:stock.field_stock_package_level__state
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_search
msgid "State"
msgstr "Статус"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__state
#: model:ir.model.fields,field_description:stock.field_stock_move_line__state
#: model:ir.model.fields,field_description:stock.field_stock_picking__state
#: model:ir.model.fields,field_description:stock.field_stock_scrap__state
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Status"
msgstr "Статус"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__activity_state
#: model:ir.model.fields,help:stock.field_stock_production_lot__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Статус основан на плановых действиях\n"
"Просрочено: срок исполнения истек\n"
"Сегодня: выполнить сегодня\n"
"Запланировано: срок в будущем."

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Stock"
msgstr "Запасы"

#. module: stock
#: model:ir.model,name:stock.model_stock_assign_serial
msgid "Stock Assign Serial Numbers"
msgstr "Присвоение серийных номеров на складе"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#: model_terms:ir.ui.view,arch_db:stock.view_location_tree2
msgid "Stock Location"
msgstr "Место хранения запасов"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Stock Locations"
msgstr "Места хранения запасов"

#. module: stock
#: model:ir.model,name:stock.model_stock_move
#: model:ir.model.fields,field_description:stock.field_product_product__stock_move_ids
#: model:ir.model.fields,field_description:stock.field_stock_move_line__move_id
msgid "Stock Move"
msgstr "Перемещение запасов"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_move_action
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_lines
#: model:ir.ui.menu,name:stock.stock_move_menu
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
msgid "Stock Moves"
msgstr "Движения запасов"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_graph
#: model_terms:ir.ui.view,arch_db:stock.view_move_pivot
msgid "Stock Moves Analysis"
msgstr "Анализ Складских Перемещений"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#: model:ir.actions.act_window,name:stock.dashboard_open_quants
#, python-format
msgid "Stock On Hand"
msgstr "Товар в наличии"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
msgid "Stock Operation"
msgstr "Операции с запасами"

#. module: stock
#: model:ir.model,name:stock.model_stock_package_destination
msgid "Stock Package Destination"
msgstr "Место назначения запаса"

#. module: stock
#: model:ir.model,name:stock.model_stock_package_level
msgid "Stock Package Level"
msgstr "Уровень складского запаса"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__picking_warn
#: model:ir.model.fields,field_description:stock.field_res_users__picking_warn
msgid "Stock Picking"
msgstr "Комплектование запасов"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__stock_quant_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_view_graph
msgid "Stock Quant"
msgstr "Количество на складе"

#. module: stock
#: model:ir.model,name:stock.model_stock_quantity_history
msgid "Stock Quantity History"
msgstr "История складской количества"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_quantity
msgid "Stock Quantity Report"
msgstr "Отчет о количестве запасов"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_report_reception
msgid "Stock Reception Report"
msgstr "Отчет складских поставок"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_report_product_product_replenishment
#: model:ir.model,name:stock.model_report_stock_report_product_template_replenishment
msgid "Stock Replenishment Report"
msgstr "Отчет о восполнении запасов"

#. module: stock
#: model:ir.model,name:stock.model_stock_request_count
msgid "Stock Request an Inventory Count"
msgstr "Запрос на инвентаризацию запасов"

#. module: stock
#: model:ir.model,name:stock.model_stock_rule
#: model:ir.model.fields,field_description:stock.field_stock_move__rule_id
msgid "Stock Rule"
msgstr "складское правило"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_rules_report
msgid "Stock Rules Report"
msgstr "Отчет складских правил"

#. module: stock
#: model:ir.model,name:stock.model_stock_rules_report
msgid "Stock Rules report"
msgstr "Отчет правил запаса"

#. module: stock
#: model:ir.model,name:stock.model_stock_track_confirmation
msgid "Stock Track Confirmation"
msgstr "Подтверждение отслеживания запаса"

#. module: stock
#: model:ir.model,name:stock.model_stock_track_line
msgid "Stock Track Line"
msgstr "Строка отслеживания запаса"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_ids_without_package
msgid "Stock moves not in package"
msgstr "Складские перемещения не в пакете"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that are Available (Ready to process)"
msgstr "Доступные перемещения (Готовность к обработке)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that are Confirmed, Available or Waiting"
msgstr "Перемещения, которые подтверждены, доступны или в ожидании"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that have been processed"
msgstr "Складские перемещения, которые были обработаны"

#. module: stock
#: model:ir.model,name:stock.model_stock_package_type
msgid "Stock package type"
msgstr "Тип складской упаковки"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr "Отчет складского правила"

#. module: stock
#: model:ir.model,name:stock.model_stock_replenishment_info
msgid "Stock supplier replenishment information"
msgstr "Информация о восполнении запасов поставщиками"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_template__detailed_type__product
#: model:ir.model.fields.selection,name:stock.selection__product_template__type__product
msgid "Storable Product"
msgstr "Складируемый"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"Storable products are physical items for which you manage the inventory "
"level."
msgstr ""
"Складируемые товары - это материальные объекты, которые можно хранить на "
"складе."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "Storage Capacities"
msgstr "Емкости для хранения"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_storage_category
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_storage_categories
#: model:ir.ui.menu,name:stock.menu_storage_categoty_config
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_tree
msgid "Storage Categories"
msgstr "Категории хранения"

#. module: stock
#: model:ir.model,name:stock.model_stock_storage_category
#: model:ir.model.fields,field_description:stock.field_stock_location__storage_category_id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__storage_category_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__name
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__storage_category_id
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "Storage Category"
msgstr "Категория мест хранения"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_storage_category_capacity
#: model:ir.model,name:stock.model_stock_storage_category_capacity
#: model:ir.model.fields,field_description:stock.field_product_product__storage_category_capacity_ids
#: model:ir.model.fields,field_description:stock.field_stock_package_type__storage_category_capacity_ids
#: model:ir.ui.menu,name:stock.menu_storage_categoty_capacity_config
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_capacity_tree
msgid "Storage Category Capacity"
msgstr "Емкость категорий мест хранения"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_multi_locations
msgid "Storage Locations"
msgstr "Места хранения"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__group_stock_multi_locations
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Store products in specific locations of your warehouse (e.g. bins, racks) "
"and to track inventory accordingly."
msgstr ""
"Храните товары в определенных местах на своем складе (например, коробки, "
"стеллажи) и отслеживайте запасы в их разрезе."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__location_out_id
msgid "Store to sublocation"
msgstr "Поместить в"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__supplied_wh_id
msgid "Supplied Warehouse"
msgstr "Склад Поставки"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__procure_method
#: model:ir.model.fields,field_description:stock.field_stock_rule__procure_method
msgid "Supply Method"
msgstr "Метод поставки"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__supplier_wh_id
msgid "Supplying Warehouse"
msgstr "Поставка Склада"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__procure_method__make_to_stock
msgid "Take From Stock"
msgstr "Брать со Склада"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__procure_method__mts_else_mto
msgid "Take From Stock, if unavailable, Trigger Another Rule"
msgstr "Взять со склада, если недоступно, задействовать другое правило"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__procure_method
msgid ""
"Take From Stock: the products will be taken from the available stock of the source location.\n"
"Trigger Another Rule: the system will try to find a stock rule to bring the products in the source location. The available stock will be ignored.\n"
"Take From Stock, if Unavailable, Trigger Another Rule: the products will be taken from the available stock of the source location.If there is no stock available, the system will try to find a  rule to bring the products in the source location."
msgstr ""
"Взять со склада: товары будут взяты из доступного склада исходного "
"местоположения. Запуск другого правила: система попытается найти правило "
"запаса, чтобы доставить продукты в исходное местоположение. Доступный запас "
"будет проигнорирован. «Взять со склада», если недоступен, активирует другое "
"правило: продукты будут взяты из доступного склада исходного местоположения."
" Если нет доступных складов, система попытается найти правило для доставки "
"продуктов в исходное местоположение."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__show_allocation
msgid ""
"Technical Field used to decide whether the button \"Allocation\" should be "
"displayed."
msgstr ""
"Техническое поле, используемое для определения того, должна ли отображаться "
"кнопка \"Распределение\"."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Technical Information"
msgstr "Техническая информация"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__warehouse_id
msgid ""
"Technical field depicting the warehouse to consider for the route selection "
"on the next procurement (if any)."
msgstr ""
"Техническое поле, представляющее склад для рассмотрения выбора маршрута при "
"следующей закупке (при наличии)."

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company__internal_transit_location_id
msgid ""
"Technical field used for resupply routes between warehouses that belong to "
"this company"
msgstr ""
"Техническое поле, используемое для маршрутов пополнения запасов между "
"складами, принадлежащими этой компании"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__show_check_availability
msgid ""
"Technical field used to compute whether the button \"Check Availability\" "
"should be displayed."
msgstr ""
"Техническое поле, используемое для вычисления того, должна ли отображаться "
"кнопка \"Проверить наличие\"."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__show_mark_as_todo
msgid ""
"Technical field used to compute whether the button \"Mark as Todo\" should "
"be displayed."
msgstr ""
"Техническое поле, используемое для расчета необходимости отображения кнопки "
"\"Отметить как задачу\"."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__show_validate
msgid ""
"Technical field used to decide whether the button \"Validate\" should be "
"displayed."
msgstr ""
"Техническое поле, используемое для определения необходимости отображения "
"кнопки \"Подтвердить\"."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__restrict_partner_id
msgid ""
"Technical field used to depict a restriction on the ownership of quants to "
"consider when marking this move as 'done'"
msgstr ""
"Техническое поле, используемое для обозначения ограничения на владение "
"квантами, которое необходимо учитывать при маркировке этого перемещения как "
"'Выполнено'"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__price_unit
msgid ""
"Technical field used to record the product cost set by the user during a "
"picking confirmation (when costing method used is 'average price' or "
"'real'). Value given in company currency and in product uom."
msgstr ""
"Техническое поле, используемое для записи стоимости продукта, заданной "
"пользователем во время подтверждения комплектования (когда используется "
"метод калькуляции 'средняя цена' или 'реальный'). Значение, указанное в "
"валюте компании и в продукте uom."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__produce_line_ids
msgid "Technical link to see which line was produced with this. "
msgstr ""
"Техническое ссылку, чтобы увидеть, какая строка была произведена с этим."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__consume_line_ids
msgid "Technical link to see who consumed what. "
msgstr "Техническое ссылку, чтобы увидеть, кто что ел."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__product_tmpl_id
msgid "Technical: used in views"
msgstr "Технический: используется в видах"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__stock_move_ids
#: model:ir.model.fields,help:stock.field_product_product__stock_quant_ids
msgid "Technical: used to compute quantities."
msgstr "Технический: используется для вычисления количества."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__incoming_move_line_ids
#: model:ir.model.fields,help:stock.field_stock_location__outgoing_move_line_ids
msgid "Technical: used to compute weight."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_tmpl_id
msgid "Template"
msgstr "Шаблон"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__auto
msgid ""
"The 'Manual Operation' value will create a stock move after the current one."
" With 'Automatic No Step Added', the location is replaced in the original "
"move."
msgstr ""
"Значение 'Ручная операция `создаст движение запасов после текущего. С "
"помощью пункта `Автоматически не приложены шага` расположение заменяется в "
"оригинальном перемещении."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"The Serial Number (%s) is already used in these location(s): %s.\n"
"\n"
"Is this expected? For example this can occur if a delivery operation is validated before its corresponding receipt operation is validated. In this case the issue will be solved automatically once all steps are completed. Otherwise, the serial number should be corrected to prevent inconsistent data."
msgstr ""
"Серийный номер (%s) уже используется в этом месте (местах): %s.\n"
"\n"
" Ожидается ли это? Например, это может произойти, если операция доставки будет проверена до того, как будет проверена соответствующая операция получения. В этом случае проблема будет решена автоматически после завершения всех этапов. В противном случае необходимо исправить серийный номер, чтобы избежать несогласованности данных."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid ""
"The backorder <a href=# data-oe-model=stock.picking data-oe-id=%d>%s</a> has"
" been created."
msgstr ""
"Обратное заказ <a href=# data-oe-model=stock.picking data-oe-id=%d> %s </a> "
"был создан."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_location_barcode_company_uniq
msgid "The barcode for a location must be unique per company !"
msgstr "Штрих-код для местоположения должен быть уникальным в компании!"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"The client explicitly waives its own standard terms and conditions, even if "
"these were drawn up after these standard terms and conditions of sale. In "
"order to be valid, any derogation must be expressly agreed to in advance in "
"writing."
msgstr ""
"Клиент отказывается от собственных типовых условий, даже если они были "
"сформированы после  выполнения условий продажи. Для того чтобы любое "
"отступление было действительным, оно должно быть предварительно согласовано "
"в письменной форме."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_orderpoint_product_location_check
msgid "The combination of product and location must be unique."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_production_lot.py:0
#, python-format
msgid ""
"The combination of serial number and product must be unique across a company.\n"
"Following combination contains duplicates:\n"
msgstr ""
"Комбинация серийного номера и продукта должна быть уникальной для всей компании.\n"
" Следующие комбинации содержат дубликаты:\n"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__company_id
msgid "The company is automatically set from your user preferences."
msgstr ""
"Компания автоматически устанавливается из ваших пользовательских настроек."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"The deadline has been automatically updated due to a delay on <a href='#' "
"data-oe-model='%s' data-oe-id='%s'>%s</a>."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__delay
msgid ""
"The expected date of the created transfer will be computed based on this "
"lead time."
msgstr ""
"На основании этого времени ожидания будет рассчитана ожидаемая дата "
"созданного перемещения."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__sequence
msgid "The first in the sequence is the default one."
msgstr "Первый в последовательности является стандартным."

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "The forecasted stock on the"
msgstr "Прогнозируемые запасы "

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_location_inventory_freq_nonneg
msgid "The inventory frequency (days) for a location must be non-negative"
msgstr ""
"Периодичность инвентаризации (в днях) для места должна быть неотрицательной"

#. module: stock
#: code:addons/stock/models/stock_production_lot.py:0
#, python-format
msgid "The lot name must contain at least one digit."
msgstr ""

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_warehouse_name_uniq
msgid "The name of the warehouse must be unique per company!"
msgstr "Имя склада должно быть уникальным в рамках одной компании!"

#. module: stock
#: code:addons/stock/wizard/stock_assign_serial_numbers.py:0
#, python-format
msgid "The number of Serial Numbers to generate must be greater than zero."
msgstr "Количество генерируемых серийных номеров должно быть больше нуля."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_picking_type_action
msgid ""
"The operation type system allows you to assign each stock\n"
"                operation a specific type which will alter its views accordingly.\n"
"                On the operation type you could e.g. specify if packing is needed by default,\n"
"                if it should show the customer."
msgstr ""
"Система типа операций позволяет назначать каждый запас эксплуатации "
"конкретного типа, соответственно изменит его вид. На тип операции вы могли "
"бы, например, указать, нужна упаковка по умолчанию если нужно показать "
"заказчику."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__package_id
msgid "The package containing this quant"
msgstr "Пакет, содержащий этот квант"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__location_id
msgid ""
"The parent location that includes this location. Example : The 'Dispatch "
"Zone' is the 'Gate 1' parent location."
msgstr ""
"Родительское местоположение, которое включает это местоположение. Пример: "
"'Зоной отправки' является родительское местоположение 'Gate 1'."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__qty_multiple
msgid ""
"The procurement quantity will be rounded up to this multiple.  If it is 0, "
"the exact quantity will be used."
msgstr ""
"Количество закупок будет округлено до этого множителя. Если оно равно 0, "
"будет использовано точное количество."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid "The product is not available in sufficient quantity"
msgstr "Продукт не доступен в достаточном количестве"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__inventory_quantity
msgid "The product's counted quantity."
msgstr "Подсчитанное количество продукта."

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid ""
"The quantity done for the product \"%s\" doesn't respect the rounding "
"precision defined on the unit of measure \"%s\". Please change the quantity "
"done or the rounding precision of your unit of measure."
msgstr ""
"Количество для продукта \"%s\" не соответствует точности округления, установленной для единицы измерения \"%s\".\n"
"Пожалуйста, измените количество или точность округления единицы измерения."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid ""
"The requested operation cannot be processed because of a programming error "
"setting the `product_qty` field instead of the `product_uom_qty`."
msgstr ""
"Запрошенная операция не может быть обработана из-за ошибки программирования,"
" устанавливающей поле `product_qty` вместо` product_uom_qty`."

#. module: stock
#: code:addons/stock/models/stock_location.py:0
#, python-format
msgid ""
"The selected Inventory Frequency (Days) creates a date too far into the "
"future."
msgstr ""
"Выбранная периодичность инвентаризации (дни) создает дату слишком в далеком "
"будущем."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"The serial number has already been assigned: \n"
" Product: %s, Serial Number: %s"
msgstr ""
"Серийный номер уже присвоен: \n"
"Продукт: %s, серийный номер: %s"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_warehouse_code_uniq
msgid "The short name of the warehouse must be unique per company!"
msgstr "Краткое название склада должно быть уникальным для каждой компании!"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner__property_stock_customer
#: model:ir.model.fields,help:stock.field_res_users__property_stock_customer
msgid ""
"The stock location used as destination when sending goods to this contact."
msgstr ""
"Складское местонахождение, используемый как пункт назначения для отправки "
"товаров к этому контакту."

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner__property_stock_supplier
#: model:ir.model.fields,help:stock.field_res_users__property_stock_supplier
msgid ""
"The stock location used as source when receiving goods from this contact."
msgstr ""
"Место хранения, используемое в качестве источника при получении товара от "
"этого контакта."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_id
msgid "The stock operation where the packing has been made"
msgstr "Складская операция, где упаковка была сделана"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__rule_id
msgid "The stock rule that created this stock move"
msgstr "Складское правило, создало это складское перемещение"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_compute_wizard
msgid ""
"The stock will be reserved for operations waiting for availability and the "
"reordering rules will be triggered."
msgstr ""
"Позиции будут зарезервированы для операций, которые ждут наличия, и будут "
"применены правила дозаказа."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__propagate_warehouse_id
msgid ""
"The warehouse to propagate on the created move/procurement, which can be "
"different of the warehouse this rule is for (e.g for resupplying rules from "
"another warehouse)"
msgstr ""
"Склад для распространения по созданному перемещению/заготовке, которое может"
" отличаться от склада, для которого это правило предназначено (например, для"
" повторной поставки правил с другого склада)"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_line_action
msgid "There's no product move yet"
msgstr "Еще нет товарных перемещений"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"This analysis gives you an overview of the current stock level of your "
"products."
msgstr ""
"Этот анализ позволяет получить представление о текущем уровне запасов "
"продукции."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__name
msgid "This field will fill the packing origin and the name of its moves"
msgstr ""
"Это поле будет попадать в источник упаковки и в название его перемещений."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__reservation_date
msgid ""
"This is a technical field for calculating when a move should be reserved"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__default_location_dest_id
msgid ""
"This is the default destination location when you create a picking manually "
"with this operation type. It is possible however to change it or that the "
"routes put another location. If it is empty, it will check for the customer "
"location on the partner. "
msgstr ""
"Это место назначения по умолчанию, когда вы создаете комплектования вручную "
"с помощью этого типа операции. Однако можно изменить его или назначить "
"другое местонахождение маршрутам. Если оно пустое, то проверит "
"местонахождение клиента в партнера."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__default_location_src_id
msgid ""
"This is the default source location when you create a picking manually with "
"this operation type. It is possible however to change it or that the routes "
"put another location. If it is empty, it will check for the supplier "
"location on the partner. "
msgstr ""
"Это местонахождение источника по умолчанию, когда вы создаете комплектования"
" вручную с помощью этого типа операции. Однако можно изменить его или "
"приначиты другое местонахождение маршрутам. Если оно пустое, то проверит "
"местонахождение поставщика в партнера."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__owner_id
msgid "This is the owner of the quant"
msgstr "Это владелец кванта"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__product_uom_qty
msgid ""
"This is the quantity of products from an inventory point of view. For moves "
"in the state 'done', this is the quantity of products that were actually "
"moved. For other moves, this is the quantity of product that is planned to "
"be moved. Lowering this quantity does not generate a backorder. Changing "
"this quantity on assigned moves affects the product reservation, and should "
"be done with care."
msgstr ""
"Это количество продуктов с точки зрения запасов. Для перемещений в состоянии"
" 'Выполнено' это количество продуктов, которые были фактически перемещены. "
"Для других перемещений это количество продукта, которое планируется "
"перенести. Понижение этой величины не приводит к освобождению от заказа. "
"Изменение этого количества в назначенных перемещениях влияет на "
"резервирование продукта и должно быть сделано с осторожностью."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__child_internal_location_ids
msgid ""
"This location (if it's internal) and all its descendants filtered by "
"type=Internal."
msgstr ""
"Это расположение (если оно внутреннее) и все его дочерние элементы, "
"отфильтрованные по типу type=Internal."

#. module: stock
#: code:addons/stock/models/stock_location.py:0
#, python-format
msgid ""
"This location's usage cannot be changed to view as it contains products."
msgstr ""
"Использование этого местонахождение нельзя изменить на просмотр, поскольку "
"оно содержит товары."

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid ""
"This lot %(lot_name)s is incompatible with this product %(product_name)s"
msgstr ""
"Данная партия %(lot_name)s несовместима с данным продуктом %(product_name)s"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_action
msgid ""
"This menu gives you the full traceability of inventory\n"
"                operations on a specific product. You can filter on the product\n"
"                to see all the past or future movements for the product."
msgstr ""
"Это меню позволяет вам полностью отслеживать инвентарные\n"
"                операции по конкретному продукту. Вы можете отфильтровать продукт,\n"
"                чтобы увидеть все прошлые или будущие движения продукта."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_line_action
msgid ""
"This menu gives you the full traceability of inventory operations on a specific product.\n"
"                    You can filter on the product to see all the past movements for the product."
msgstr ""
"Это меню дает вам возможность полного отслеживания операций по запасам "
"конкретного продукта. Вы можете выбрать фильтр по продукту, чтобы увидеть "
"все прошлые передвижения продукта."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "This note is added to delivery orders."
msgstr "Примечание для заказов на доставку"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid ""
"This note is added to internal transfer orders (e.g. where to pick the "
"product in the warehouse)."
msgstr ""
"Примечание для внутренних перемещений (например, где найти товар на складе)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid ""
"This note is added to receipt orders (e.g. where to store the product in the"
" warehouse)."
msgstr ""
"Примечание для поступления товаров (например, где хранить товар на складе)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
msgid ""
"This picking appears to be chained with another operation. Later, if you "
"receive the goods you are returning now, make sure to <b>reverse</b> the "
"returned picking in order to avoid logistic rules to be applied again (which"
" would create duplicated operations)"
msgstr ""
"Эта подборка оказывается связанной с другой операцией. Позже, если вы "
"получаете товары, которые вы возвращаете сейчас, убедитесь, что "
"<b>реверс</b> вернул сборку, чтобы избежать повторного применения "
"логистических правил (что создало бы дублированные операции)"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"This product has been used in at least one inventory movement. It is not "
"advised to change the Product Type since it can lead to inconsistencies. A "
"better solution could be to archive the product and create a new one "
"instead."
msgstr ""
"Этот продукт используется как минимум в одном передвижении запасов. Не "
"рекомендуется изменять тип продукта, так как это может привести к "
"противоречиям. Лучше заархивировать этот продукт и создать вместо него "
"новый."

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"This product's company cannot be changed as long as there are quantities of "
"it belonging to another company."
msgstr ""
"Компания данного продукта не может быть изменена до тех пор, пока какое-либо"
" количество продукта, принадлежит другой компании."

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"This product's company cannot be changed as long as there are stock moves of"
" it belonging to another company."
msgstr ""
"Компания данного продукта не может быть изменена до тех пор, пока существуют"
" складские перемещения, принадлежащие другой компании."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_change_product_qty__new_quantity
msgid ""
"This quantity is expressed in the Default Unit of Measure of the product."
msgstr "Это количество выражено в единицах измерения товара по умолчанию."

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/js/inventory_singleton_list_controller.js:0
#, python-format
msgid "This record already exists."
msgstr "Эта запись уже существует."

#. module: stock
#: code:addons/stock/report/report_stock_reception.py:0
#, python-format
msgid ""
"This report cannot be used for done and not done transfers at the same time"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__property_stock_production
#: model:ir.model.fields,help:stock.field_product_template__property_stock_production
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated by manufacturing orders."
msgstr ""
"Это место хранения будет использоваться вместо места хранения по умолчанию "
"как исходное место хранения для движений, созданных производственными "
"заказами."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__property_stock_inventory
#: model:ir.model.fields,help:stock.field_product_template__property_stock_inventory
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated when you do an inventory."
msgstr ""
"Это место хранения будет использоваться вместо места хранения по умолчанию "
"как исходное место хранения для движений, созданных при проведении "
"инвентаризации."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__responsible_id
#: model:ir.model.fields,help:stock.field_product_template__responsible_id
msgid ""
"This user will be responsible of the next activities related to logistic "
"operations for this product."
msgstr ""
"Этот пользователь будет отвечать за следующие действия, связанные с "
"логистическими операциями для этого товара."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_reset_view
msgid "This will discard all unapplied counts, do you want to proceed?"
msgstr ""
"Это действие сбросит все непримененные значения. вы хотите продолжить?"

#. module: stock
#: model:digest.tip,name:stock.digest_tip_stock_0
#: model_terms:digest.tip,tip_description:stock.digest_tip_stock_0
msgid "Tip: Speed up inventory operations with barcodes"
msgstr "Совет: Ускорьте операции инвентаризации с помощью штрих-кодов"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__location_dest_id
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "To"
msgstr "До"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "To Apply"
msgstr "К принятию"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__to_backorder
msgid "To Backorder"
msgstr "Отложить заказ"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "To Count"
msgstr "Считать"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_ready
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "To Do"
msgstr "Задача"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__qty_to_order
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_to_order
msgid "To Order"
msgstr "Под заказ"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__to_immediate
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "To Process"
msgstr "В процессе"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "To Reorder"
msgstr "Нужно пополнить"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "Today"
msgstr "Сегодня"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Today Activities"
msgstr "Действия на сегодня"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__total_route_ids
msgid "Total routes"
msgstr "Итого маршрутов"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Traceability"
msgstr "Учет движения"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/js/stock_traceability_report_widgets.js:0
#: model:ir.actions.client,name:stock.action_stock_report
#: model:ir.model,name:stock.model_stock_traceability_report
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#, python-format
msgid "Traceability Report"
msgstr "Отслеживание отчета"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__module_product_expiry
msgid ""
"Track following dates on lots & serial numbers: best before, removal, end of life, alert. \n"
" Such dates are set automatically at lot/serial number creation based on values set on the product (in days)."
msgstr ""
"Отслеживайте следующие даты на партиях и серийных номерах: лучше до, "
"удаления, окончание срока действия, уведомления. Такие даты устанавливаются "
"автоматически при создании партии / серийного номера на основе значений, "
"установленных на товаре (в днях)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Track following dates on lots & serial numbers: best before, removal, end of"
" life, alert. Such dates are set automatically at lot/serial number creation"
" based on values set on the product (in days)."
msgstr ""
"Отслеживайте следующие даты на партиях и серийных номерах: лучше до, "
"удаления, окончание срока действия, уведомления. Такие даты устанавливаются "
"автоматически при создании партии / серийного номера на основе значений, "
"установленных на товаре (в днях)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Track product location in your warehouse"
msgstr "Отслеживание местонахождения товара на вашем складе"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.product_template_action_product
msgid "Track your stock quantities by creating storable products."
msgstr "Отслеживайте остатки на складе, создав хранимые продукты."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Tracked Products in Inventory Adjustment"
msgstr "Отслежены товары в корректировке запасов"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__tracking
#: model:ir.model.fields,field_description:stock.field_product_template__tracking
#: model:ir.model.fields,field_description:stock.field_stock_move_line__tracking
#: model:ir.model.fields,field_description:stock.field_stock_quant__tracking
#: model:ir.model.fields,field_description:stock.field_stock_track_line__tracking
msgid "Tracking"
msgstr "Отслеживание"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__tracking_line_ids
msgid "Tracking Line"
msgstr "строка отслеживания"

#. module: stock
#: model:ir.model,name:stock.model_stock_picking
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Transfer"
msgstr "Перемещение"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__picking_quantity__picking
msgid "Transfer Quantities"
msgstr "Количество перемещений"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_tree
msgid "Transfer to"
msgstr "Перемещение в"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_all
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__delivery_ids
#: model:ir.ui.menu,name:stock.all_picking
#: model_terms:ir.ui.view,arch_db:stock.procurement_group_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "Transfers"
msgstr "Перемещения"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Transfers %s: Please add some items to move."
msgstr "Перемещения %s: Добавьте несколько элементов для перемещения."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_get_picking_type_operations
#: model_terms:ir.actions.act_window,help:stock.action_picking_form
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_all
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_backorder
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_late
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_ready
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_waiting
#: model_terms:ir.actions.act_window,help:stock.action_picking_type_list
#: model_terms:ir.actions.act_window,help:stock.stock_picking_action_picking_type
msgid "Transfers allow you to move products from one location to another."
msgstr "Перемещение товаров из одного места в другое."

#. module: stock
#: model:ir.actions.act_window,name:stock.do_view_pickings
msgid "Transfers for Groups"
msgstr "Перемещения для групп"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid ""
"Transfers that are late on scheduled time or one of pickings will be late"
msgstr "Перемещения, идущие с задержкой или один из сборов будет с опозданием"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__transit
msgid "Transit Location"
msgstr "Транзитное Место хранения"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Transit Locations"
msgstr "Транзитные Места хранения"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__trigger
msgid "Trigger"
msgstr "Триггер"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__procure_method__make_to_order
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Trigger Another Rule"
msgstr "Запустить другое правило"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Trigger Another Rule If No Stock"
msgstr "Активизируйте другое правило, если нет запасов"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/js/report_stock_forecasted.js:0
#, python-format
msgid "Try to add some incoming or outgoing transfers."
msgstr "Попробуйте добавить несколько входящих или исходящих перемещений."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_barcode_rule__type
#: model:ir.model.fields,field_description:stock.field_product_product__type
#: model:ir.model.fields,field_description:stock.field_product_template__type
msgid "Type"
msgstr "Тип"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_code
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_code
#: model:ir.model.fields,field_description:stock.field_stock_package_level__picking_type_code
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_type_code
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__code
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
msgid "Type of Operation"
msgstr "Тип Операции"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__activity_exception_decoration
#: model:ir.model.fields,help:stock.field_stock_production_lot__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Тип активности исключения в записи."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_ups
msgid "UPS Connector"
msgstr "коннектор UPS"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_usps
msgid "USPS Connector"
msgstr "коннектор USPS"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/js/report_stock_reception.js:0
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
#, python-format
msgid "Unassign"
msgstr "Не назначать"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_traceability_report_line.xml:0
#: code:addons/stock/static/src/xml/stock_traceability_report_line.xml:0
#: code:addons/stock/static/src/xml/stock_traceability_report_line.xml:0
#: code:addons/stock/static/src/xml/stock_traceability_report_line.xml:0
#, python-format
msgid "Unfold"
msgstr "Развернуть"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_production_lot__name
msgid "Unique Lot/Serial Number"
msgstr "Уникальная Партия/Серийный номер"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__price_unit
msgid "Unit Price"
msgstr "Цена за ед."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__uom_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_uom
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__product_uom_name
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__product_uom_name
#: model_terms:ir.ui.view,arch_db:stock.package_level_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree_detailed
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "Unit of Measure"
msgstr "Единица измерения"

#. module: stock
#: model:product.product,uom_name:stock.product_cable_management_box
#: model:product.template,uom_name:stock.product_cable_management_box_product_template
msgid "Units"
msgstr "Единицы"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Units Of Measure"
msgstr "Единицы измерения товара"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_unit_measure_stock
msgid "Units of Measure"
msgstr "Единицы измерения"

#. module: stock
#: model:ir.ui.menu,name:stock.product_uom_menu
msgid "Units of Measures"
msgstr "Единицы Измерения"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_uom_id
msgid "Unit of measure"
msgstr "Единица измерения"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Unknown Pack"
msgstr "Неизвестная упаковка"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Unknown stream."
msgstr "Неизвестный поток."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Unlock"
msgstr "Разблокировать"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Unpack"
msgstr "Распаковывать"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_unread
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_unread
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_unread
msgid "Unread Messages"
msgstr "Непрочитанные сообщения"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_unread_counter
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_unread_counter
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Счетчик непрочитанных сообщений"

#. module: stock
#: model:ir.actions.server,name:stock.action_unreserve_picking
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Unreserve"
msgstr "Разрезервировать"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_uom
#: model:ir.ui.menu,name:stock.menu_stock_uom_form_action
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable_config
msgid "UoM"
msgstr "ЕИТ"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_uom_categ_form_action
msgid "UoM Categories"
msgstr "Категории единицы измерения товара"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
msgid "Update Product Quantity"
msgstr "Обновить количество товаров"

#. module: stock
#: code:addons/stock/models/product.py:0
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_product_view_form_easy_inherit_stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
#, python-format
msgid "Update Quantity"
msgstr "Обновить количество"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__priority__1
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__priority__1
msgid "Urgent"
msgstr "Срочно"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_use_existing_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking__use_existing_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__use_existing_lots
msgid "Use Existing Lots/Serial Numbers"
msgstr "Использовать существующие Партии/Серийные номера"

#. module: stock
#: model:res.groups,name:stock.group_reception_report
msgid "Use Reception Report"
msgstr "Использовать отчет о поставках "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
msgid ""
"Use this assistant to replenish your stock.\n"
"                Depending on your product configuration, launching a replenishment may trigger a request for quotation,\n"
"                a manufacturing order or a transfer."
msgstr ""
"Воспользуйтесь этим помощником для пополнения запасов.\n"
"                В зависимости от конфигурации вашего продукта запуск пополнения запасов может вызвать запрос цен поставщика,\n"
"                заказ на производство или перемещение."

#. module: stock
#: model:res.groups,name:stock.group_stock_picking_wave
msgid "Use wave pickings"
msgstr "Используйте массовую сборку"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Use your own routes"
msgstr "Используйте свои собственные маршруты"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Used by"
msgstr "Используется в"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__sequence
msgid "Used to order the 'All Operations' kanban view"
msgstr "Используется, чтобы упорядочить 'Все Операции' в представление kanban"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_request_count__user_id
#: model:res.groups,name:stock.group_stock_user
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "User"
msgstr "Пользователь"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__user_id
msgid "User assigned to do product count."
msgstr "Пользователь, назначенный для выполнения подсчета товаров."

#. module: stock
#: model:ir.actions.server,name:stock.action_validate_picking
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Validate"
msgstr "Подтвердить"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/inventory_lines.xml:0
#, python-format
msgid "Validate Inventory"
msgstr "Подтвердить инвентаризацию"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_variant_count
msgid "Variant Count"
msgstr "Подсчет вариантов"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Vendor"
msgstr "Поставщик"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__property_stock_supplier
#: model:ir.model.fields,field_description:stock.field_res_users__property_stock_supplier
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__supplier
msgid "Vendor Location"
msgstr "Расположение поставщика"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Vendor Locations"
msgstr "Места хранения Поставщика"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__view
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "View"
msgstr "Осмотр"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "View Diagram"
msgstr "Просмотр диаграммы"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__view_location_id
msgid "View Location"
msgstr "Зона осмотра"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "View and allocate received quantities."
msgstr "Просматривайте и отслеживайте количество поставленных товаров."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__confirmed
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Waiting"
msgstr "Ожидание"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__waiting
msgid "Waiting Another Move"
msgstr "Ожидает другого перемещения"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__waiting
msgid "Waiting Another Operation"
msgstr "Ожидание другой операции"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__confirmed
msgid "Waiting Availability"
msgstr "Ожидание поступления"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Waiting Moves"
msgstr "Ожидает Перемещения"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_waiting
msgid "Waiting Transfers"
msgstr "Ожидает перемещения"

#. module: stock
#: model:ir.model,name:stock.model_stock_warehouse
#: model:ir.model.fields,field_description:stock.field_product_product__warehouse_id
#: model:ir.model.fields,field_description:stock.field_product_replenish__warehouse_id
#: model:ir.model.fields,field_description:stock.field_product_template__warehouse_id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_location__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_move__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__warehouse_id
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_warehouse_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_tree
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Warehouse"
msgstr "Склад"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Warehouse Configuration"
msgstr "Конфигурация склада"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__warehouse_domain_ids
msgid "Warehouse Domain"
msgstr "Складской домен"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_warehouse_config
msgid "Warehouse Management"
msgstr "Управление складом"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__warehouse_view_ids
msgid "Warehouse View"
msgstr "Виртуальное место хранения склада"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__propagate_warehouse_id
msgid "Warehouse to Propagate"
msgstr "Склад для распространения"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Warehouse view location"
msgstr "Зона осмотра склада"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Warehouse's Routes"
msgstr "Маршруты склада"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/report_stock_forecasted.xml:0
#, python-format
msgid "Warehouse:"
msgstr "Склад:"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_warehouse_form
#: model:ir.model.fields,field_description:stock.field_stock_location_route__warehouse_ids
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__warehouse_ids
#: model:ir.ui.menu,name:stock.menu_action_warehouse_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Warehouses"
msgstr "Склады"

#. module: stock
#: model:ir.model,name:stock.model_stock_warn_insufficient_qty
msgid "Warn Insufficient Quantity"
msgstr "Предупреждение о недостаточном количестве"

#. module: stock
#: model:ir.model,name:stock.model_stock_warn_insufficient_qty_scrap
msgid "Warn Insufficient Scrap Quantity"
msgstr "Предупреждение о недостаточном количестве брака"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#: code:addons/stock/models/stock_move.py:0
#: code:addons/stock/models/stock_move_line.py:0
#: code:addons/stock/models/stock_move_line.py:0
#: code:addons/stock/models/stock_quant.py:0
#: code:addons/stock/models/stock_scrap.py:0
#: code:addons/stock/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:stock.selection__res_partner__picking_warn__warning
#, python-format
msgid "Warning"
msgstr "Предупреждение"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_partner_stock_warnings_form
msgid "Warning on the Picking"
msgstr "Предупреждение по комплектованию"

#. module: stock
#: code:addons/stock/models/product.py:0 code:addons/stock/models/product.py:0
#, python-format
msgid "Warning!"
msgstr "Предупреждение!"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Warnings"
msgstr "Предупреждения"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_warning_stock
msgid "Warnings for Stock"
msgstr "Предупреждение для склада"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_picking_wave
msgid "Wave Transfers"
msgstr "Перемещение волнами"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__website_message_ids
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__website_message_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__website_message_ids
msgid "Website Messages"
msgstr "Сообщения с сайта"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__website_message_ids
#: model:ir.model.fields,help:stock.field_stock_production_lot__website_message_ids
#: model:ir.model.fields,help:stock.field_stock_scrap__website_message_ids
msgid "Website communication history"
msgstr "История общения с сайтом"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__weight_uom_name
msgid "Weight unit of measure label"
msgstr "Метка единицы измерения веса"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__weight
msgid "Weighted Product"
msgstr "Взвешенный Продукт"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route__warehouse_selectable
msgid ""
"When a warehouse is selected for this route, this route should be seen as "
"the default route when products pass through this warehouse."
msgstr ""
"Если для этого маршрута выбран склад, этот маршрут следует рассматривать как"
" маршрут по умолчанию, когда продукты проходят через этот склад."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__move_type__one
msgid "When all products are ready"
msgstr "Когда все товары готовы"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route__product_selectable
msgid ""
"When checked, the route will be selectable in the Inventory tab of the "
"Product form."
msgstr ""
"Если этот флажок установлен, маршрут будет доступен для выбора во вкладке "
"«Инвентарь» формы «Продукт»."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route__product_categ_selectable
msgid "When checked, the route will be selectable on the Product Category."
msgstr ""
"Если этот флажок установлен, маршрут будет доступен для выбора в Категории "
"Продуктов."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route__packaging_selectable
msgid "When checked, the route will be selectable on the Product Packaging."
msgstr ""
"Если флажок установлен, маршрут будет доступен для выбора на странице "
"\"Упаковка товара\"."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__location_in_id
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "When product arrives in"
msgstr "Когда товар прибывает в"

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid ""
"When products are needed in <b>%s</b>, <br/> <b>%s</b> are created from "
"<b>%s</b> to fulfill the need."
msgstr ""
"Когда товары необходимые <b>в %s,</b> <br/> <b> %s </b> <b>создается %s </b>"
" для удовлетворения потребности."

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid ""
"When products arrive in <b>%s</b>, <br/> <b>%s</b> are created to send them "
"in <b>%s</b>."
msgstr ""
"Когда товары поступают <b>в %s,</b> <br/> <b> %s </b> создан для их "
"<b>отправки %s.</b>"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__is_locked
msgid ""
"When the picking is not done this allows changing the initial demand. When "
"the picking is done this allows changing the done quantities."
msgstr ""
"Когда комплектования не выполняется, это позволяет изменить первоначальный "
"спрос. Когда комплектования выполняется, это позволяет изменить изготовлены "
"величины."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__product_min_qty
msgid ""
"When the virtual stock goes below the Min Quantity specified for this field,"
" Odoo generates a procurement to bring the forecasted quantity to the Max "
"Quantity."
msgstr ""
"Когда виртуальный склад меньше Мин. Количества, указанного в этом поле, Odoo"
" инициирует закупку, чтобы довести прогнозируемое количество до Макс. "
"Количества."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__product_max_qty
msgid ""
"When the virtual stock goes below the Min Quantity, Odoo generates a "
"procurement to bring the forecasted quantity to the Quantity specified as "
"Max Quantity."
msgstr ""
"Когда виртуальный склад меньше Мин. Количества, Odoo инициирует закупку, "
"чтобы довести прогнозируемый объем до Количества, определенного как Maкс. "
"Количество."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__propagate_carrier
msgid "When ticked, carrier of shipment will be propgated."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__propagate_cancel
msgid ""
"When ticked, if the move created by this rule is cancelled, the next move "
"will be cancelled too."
msgstr ""
"Если отмечен галочкой, то при отмене хода, созданного этим правилом, будет "
"отменен и следующий ход."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__owner_id
msgid ""
"When validating the transfer, the products will be assigned to this owner."
msgstr ""
"При подтверждении перевода товары будут закреплены за этим владельцем."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__owner_id
msgid ""
"When validating the transfer, the products will be taken from this owner."
msgstr "При подтверждении перевода товары будут забраны у этого владельца."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__additional
msgid "Whether the move was added after the picking's confirmation"
msgstr "Было добавлено перемещения после подтверждения комплектования"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__width
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Width"
msgstr "Ширина"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_width
msgid "Width must be positive"
msgstr "Ширина должна быть положительной"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__wizard_id
#: model:ir.model.fields,field_description:stock.field_stock_track_line__wizard_id
msgid "Wizard"
msgstr "Мастер"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "Write your SN/LN one by one or copy paste a list."
msgstr ""
"Напишите свой серийный номер / номер партии один за другим или скопируйте и "
"вставьте список."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint_replenish
msgid "You are good, no replenishment to perform!"
msgstr "Все в порядке, ничего пополнять не требуется!"

#. module: stock
#: code:addons/stock/models/stock_production_lot.py:0
#, python-format
msgid ""
"You are not allowed to change the product linked to a serial or lot number "
"if some stock moves have already been created with that number. This would "
"lead to inconsistencies in your stock."
msgstr ""
"Вам не разрешается изменять товар, связанный с серийным номером или номером "
"партии, если для них уже созданы складские перемещения. Это приведет к "
"несоответствию на вашем складе."

#. module: stock
#: code:addons/stock/models/stock_production_lot.py:0
#, python-format
msgid ""
"You are not allowed to create a lot or serial number with this operation "
"type. To change this, go on the operation type and tick the box \"Create New"
" Lots/Serial Numbers\"."
msgstr ""
"Вам не разрешается создавать номер партии или серийный номер с этим типом "
"операции. Чтобы изменить это, перейдите к типу операции и отметьте `Создать "
"новую партию / серийный номер`."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
msgid ""
"You are trying to put products going to different locations into the same "
"package"
msgstr ""
"Вы пытаетесь разместить товары в разных местоположением в одном пакете"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"You are using a unit of measure smaller than the one you are using in order "
"to stock your product. This can lead to rounding problem on reserved "
"quantity. You should use the smaller unit of measure possible in order to "
"valuate your stock or change its rounding precision to a smaller value "
"(example: 0.00001)."
msgstr ""
"Вы используете единицы, меньше, чем вы используете, для того, чтобы "
"поместить свой товар на склад. Это может привести к проблеме округления "
"зарезервированной количестве. Вы должны использовать меньшее единицы, чтобы "
"оценить свой запас или изменить его точность округления до меньшего значения"
" (например 0,00001)."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_routes_form
msgid ""
"You can define here the main routes that run through\n"
"                your warehouses and that define the flows of your products. These\n"
"                routes can be assigned to a product, a product category or be fixed\n"
"                on procurement or sales order."
msgstr ""
"Здесь вы можете определить основные маршруты, которые проходят через ваши "
"склады, и которые определяют процессы ваших товаров. Эти маршруты могут быть"
" назначены для товара категории или быть зафиксированными на закупки или "
"заказах на продажу."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "You can either :"
msgstr "Вы можете :"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"You can not change the type of a product that is currently reserved on a "
"stock move. If you need to change the type, you should first unreserve the "
"stock move."
msgstr ""
"Вы не можете изменить тип товара, который сейчас зарезервировано для "
"перемещения запасов. Если вам нужно изменить тип, вам сначала нужно снять "
"резерв складских перемещений."

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "You can not change the type of a product that was already used."
msgstr "Вы не можете изменить тип уже использующегося продукта."

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid ""
"You can not delete product moves if the picking is done. You can only "
"correct the done quantities."
msgstr ""
"Вы не можете удалить перемещения товара, если комплектования завершен. Вы "
"можете исправить изготовлены количестве."

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid "You can not enter negative quantities."
msgstr "Нельзя вводить отрицательные величины."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid "You can only delete draft moves."
msgstr "Вы можете удалить только черновик перемещения."

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid "You can only process 1.0 %s of products with unique serial number."
msgstr ""
"Вы можете обработать только 1.0 %s товаров с уникальным серийным номером."

#. module: stock
#: code:addons/stock/models/res_config_settings.py:0
#, python-format
msgid ""
"You can't desactivate the multi-location if you have more than once "
"warehouse by company"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_location.py:0
#, python-format
msgid "You cannot archive the location %s as it is used by your warehouse %s"
msgstr ""
"Вы не можете заархивировать это местоположение %s поскольку оно используется"
" вашим складом %s"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"You cannot cancel a stock move that has been set to 'Done'. Create a return "
"in order to reverse the moves which took place."
msgstr ""
"Вы не можете отменить перемещение товара, для которого было установлено "
"значение \"Готово\". Создайте возврат, чтобы отменить имевшие место ходы."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "You cannot change the Scheduled Date on a done or cancelled transfer."
msgstr ""
"Вы не можете изменить запланированную дату для выполненного или отмененного "
"перевода."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"You cannot change the UoM for a stock move that has been set to 'Done'."
msgstr ""
"Вы не можете изменить единицу измерения для перемещения запасов, для "
"которого было установлено значение 'Выполнено'."

#. module: stock
#: code:addons/stock/models/stock_location.py:0
#, python-format
msgid ""
"You cannot change the location type or its use as a scrap location as there "
"are products reserved in this location. Please unreserve the products first."
msgstr ""
"Вы не можете изменить тип местонахождение или использовать его как место "
"расположения, поскольку в нем есть товары, зарезервированные в этом "
"местонахождении. Пожалуйста, отвечайте сначала товары."

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"You cannot change the ratio of this unit of measure as some products with "
"this UoM have already been moved or are currently reserved."
msgstr ""
"Вы не можете изменить соотношение этой единицы измерения, так как некоторые "
"продукты с этой единицей измерения уже перемещены или зарезервированы."

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"You cannot change the unit of measure as there are already stock moves for "
"this product. If you want to change the unit of measure, you should rather "
"archive this product and create a new one."
msgstr ""
"Вы не можете изменить единицу измерения, поскольку для этого товара уже есть"
" складские перемещения. Если вы хотите изменить единицу измерения, вам "
"следует скорее архивировать этот товар и создать новый."

#. module: stock
#: code:addons/stock/models/stock_scrap.py:0
#, python-format
msgid "You cannot delete a scrap which is done."
msgstr "Вы не можете удалить отходы, которые делаются."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "You cannot modify inventory loss quantity"
msgstr "Вы не можете изменить количество потерь запасов"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"You cannot move the same package content more than once in the same transfer"
" or split the same package into two location."
msgstr ""
"Вы не можете переместить тот самый содержание пакета более одного раза в "
"одном перемещении или разделить тот же пакет на два местонахождения."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"You cannot perform the move because the unit of measure has a different "
"category as the product unit of measure."
msgstr ""
"Вы не можете выполнить перемещения, поскольку единица измерения имеет другую"
" категорию, чем единица измерения товара."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid "You cannot split a draft move. It needs to be confirmed first."
msgstr ""
"Вы не можете разделить на части черновик перемещения. Он должен быть сначала"
" подтвержден."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid "You cannot split a stock move that has been set to 'Done' or 'Cancel'."
msgstr ""
"Невозможно разделить перемещение запасов, для которых установлено значение "
"'Выполнено' или 'Отмена'."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"You cannot take products from or deliver products to a location of type "
"\"view\" (%s)."
msgstr ""
"Вы не можете брать продукты из и отправлять их в виртуальное место хранения "
"(%s)."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid "You cannot unreserve a stock move that has been set to 'Done'."
msgstr ""
"Невозможно зарезервировать складское перемещение, установленное на "
"`Выполнено`."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid "You cannot change a cancelled stock move, create a new line instead."
msgstr ""
"Вы не можете изменить отмененное перемещение запасов, вместо этого создайте "
"новую строку."

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid ""
"You cannot use the same serial number twice. Please correct the serial "
"numbers encoded."
msgstr ""
"Вы не можете использовать один и тот же серийный номер дважды. Пожалуйста, "
"исправьте зашифрованные серийные номера."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid ""
"You cannot validate a transfer if no quantities are reserved nor done. To "
"force the transfer, switch in edit mode and encode the done quantities."
msgstr ""
"Невозможно подтвердить перемещение, если нет ни зарезервированных, ни "
"выполненных количеств. Чтобы принудительно выполнить перемещение, перейдите "
"в режим редактирования и  и запишите выбранное количество."

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:0
#, python-format
msgid ""
"You have manually created product lines, please delete them to proceed."
msgstr "Вы создали строки товаров вручную, удалите их, чтобы продолжить."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
msgid ""
"You have not recorded <i>done</i> quantities yet, by clicking on "
"<i>apply</i> Odoo will process all the quantities."
msgstr ""
"Вы еще не записали <i>выполненные</i> количества, при нажатии "
"<i>применить</i> система обработает все количества."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "You have processed less products than the initial demand."
msgstr "Вы обрабатываете меньше продукции, чем в первоначальном запросе."

#. module: stock
#: code:addons/stock/models/res_config_settings.py:0
#, python-format
msgid ""
"You have product(s) in stock that have lot/serial number tracking enabled. \n"
"Switch off tracking on all the products before switching off this setting."
msgstr ""
"У вас на складе есть товар(ы), для которого включено отслеживание "
"партии/серийного номера. Перед отключением этой настройки отключите "
"отслеживание для всех товаров."

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"You have product(s) in stock that have no lot/serial number. You can assign "
"lot/serial numbers by doing an inventory adjustment."
msgstr ""
"У вас есть товары на складе без номера партии / серийного номера. Вы можете "
"присвоить номера партий / серийные номера, выполнив корректировку запасов."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid ""
"You have to define a groupby and sorted method and pass them as arguments."
msgstr ""
"Вы должны определить метод groupby, отсортировать метод и передать их в "
"качестве аргументов."

#. module: stock
#: code:addons/stock/models/stock_orderpoint.py:0
#, python-format
msgid ""
"You have to select a product unit of measure that is in the same category as"
" the default unit of measure of the product"
msgstr ""
"Необходимо выбрать единицу измерения продукта, которая находится в той же "
"категории, что и единица измерения продукта по умолчанию"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/js/inventory_singleton_list_controller.js:0
#, python-format
msgid ""
"You have tried to create a record which already exists. The existing record "
"has been modified instead."
msgstr ""

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:0
#, python-format
msgid "You may only return Done pickings."
msgstr "Вы можете вернуть только выполнены комплектования."

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:0
#, python-format
msgid "You may only return one picking at a time."
msgstr "Вы можете вернуть только одно комплектования за один раз."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid ""
"You need to activate storage locations to be able to do internal operation "
"types."
msgstr ""
"Вам необходимо активировать места хранения, чтобы иметь возможность "
"выполнять внутренние типы операций."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid "You need to set a Serial Number before generating more."
msgstr "Вам необходимо установить серийный номер, прежде чем создавать новые."

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid ""
"You need to supply a Lot/Serial Number for product: \n"
" - "
msgstr ""
"Вам необходимо указать номер партии/серийный номер продукта: \n"
"- "

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "You need to supply a Lot/Serial number for products %s."
msgstr "Вам необходимо указать номер партии/серийный номер продукции %s."

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid "You should update this document to reflect your T&amp;C."
msgstr "Вы должны обновить этот документ в соответствии с вашими Т&amp;С."

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "You still have ongoing operations for picking types %s in warehouse %s"
msgstr "На складе %s продолжаются операции по комплектации%s"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"You still have some active reordering rules on this product. Please archive "
"or delete them first."
msgstr ""
"У вас все еще есть некоторые активные правила дозаказа на этом продукте. "
"Сначала архивируйте или удалите их."

#. module: stock
#: code:addons/stock/models/stock_location.py:0
#, python-format
msgid "You still have some product in locations %s"
msgstr "У вас все еще есть товар в расположении %s"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint_replenish
msgid ""
"You'll find here smart replenishment propositions based on inventory forecasts.\n"
"            Choose the quantity to buy or manufacture and launch orders in a click.\n"
"            To save time in the future, set the rules as \"automated\"."
msgstr ""
"Вы найдете здесь предложения на пополнение, основанные на складских прогнозах.\n"
"            Укажите количество для покупки, производства и создавайте заказы по клику.\n"
"            Чтобы сэкономить время пометьте правила как \"автоматические\"."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Your stock is currently empty"
msgstr "Ваш склад в настоящее время пуст"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__print_format__zpl
msgid "ZPL Labels"
msgstr "Маркировка ZPL"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__print_format__zplxprice
msgid "ZPL Labels with price"
msgstr "Этикетки ZPL с указанием цены"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "]<br/>min:"
msgstr "] <br/> мин:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "_Cancel"
msgstr "_Отмена"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "below the inventory"
msgstr "ниже уровня запасов"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_bpost
msgid "bpost Connector"
msgstr "коннектор bpost"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "days"
msgstr "дней"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "days before when starred"
msgstr "Дней до отметки"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "days before/"
msgstr "дней до/"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "e.g. CW"
msgstr "например, CW"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "e.g. Central Warehouse"
msgstr "например, центральный склад"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "e.g. LOT/0001/20121"
msgstr "например, ПАРТИЯ/0001/20121"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "e.g. PACK0000007"
msgstr "например, PACK0000007"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "e.g. PO0032"
msgstr "прим. ЗЗ0032"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "e.g. Physical Locations"
msgstr "например, физическое расположение"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "e.g. Spare Stock"
msgstr "например, запасной фонд"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "e.g. Two-steps reception"
msgstr "Например, двухэтапная приемка"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
msgid "from location"
msgstr "из места"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid "in"
msgstr "входит в"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "is"
msgstr "является"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "manually to trigger the reordering rules right now."
msgstr "вручную для запуска правил дозаказа прямо сейчас."

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "minimum of"
msgstr "минимум"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "of"
msgstr "из"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/popover_widget.xml:0
#, python-format
msgid "planned on"
msgstr "запланировано"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "processed instead of"
msgstr "обрабатывается вместо"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_graph
msgid "report_stock_quantity_graph"
msgstr "report_stock_quantity_graph"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "should be replenished"
msgstr "должны быть пополнены"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "to reach the maximum of"
msgstr "для достижения максимума"

#. module: stock
#: model:mail.template,report_name:stock.mail_template_data_delivery_confirmation
msgid "{{ (object.name or '').replace('/','_') }}"
msgstr "{{ (object.name or '').replace('/','_') }}"

#. module: stock
#: model:mail.template,subject:stock.mail_template_data_delivery_confirmation
msgid ""
"{{ object.company_id.name }} Delivery Order (Ref {{ object.name or 'n/a' }})"
msgstr ""
"{{ object.company_id.name }} Delivery Order (Ref {{ object.name or 'n/a' }})"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid "Global Visibility Days"
msgstr "Дни глобальной видимости"
