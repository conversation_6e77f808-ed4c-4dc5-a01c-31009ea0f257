<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase Odoo Connector</title>
    <link rel="stylesheet" type="text/css" href="../common/bootstrap.min.css" />
</head>

<body>
    <div class="container">
        <section style="margin: 25px 0 60px;">
            <h2 style="color:#835977; font-family:'Lato', 'Open Sans', 'Helvetica', Sans; text-align:center; margin-bottom:32px; text-transform:capitalize">
                <b>Firebase Odoo Connector</b>
            </h2>
            <p style="font-weight: 600;font-size:20px; line-height:150% !important; font-family:'Lato', 'Open Sans', 'Helvetica', Sans !important; text-align:center;color: #646464;margin-bottom: 56px;">
                Synchronize your database with Firebase in real time. 
                From Odoo to Firebase and from Firebase to Odoo
            </p>
            <div class="text-center" style="margin-bottom: 60px">
               
         
               <a class="btn-hover" style="width:200px; font-family:'Lato', 'Open Sans', 'Helvetica', Sans; font-size:19px; font-weight:600; color:#fff; margin:20px; height:55px; text-align:center; border:none; border-radius:50px; background-color:#835977; padding:19px 66px" href="mailto:<EMAIL>?subject=Odoo Firebase Integration - v12" target="_blank">REQUEST A DEMO</a>
      
            </div>
            
            <div style="font-weight:500; font-size:15px; margin:0 auto; border:1px solid #0074ff; display:flex; background-color:#0074ff; border-radius:10px; min-width:350px">
                                                    <div class="text-center" style="color:#091E42; font-family:Montserrat; padding:0; min-width:100%">
                                                        <span class="bg-white position-relative shadow" style="padding:7px; color:#0f1e40; float:left; font-weight:600; width:65px; vertical-align:middle; border-bottom-left-radius:9px; border-top-left-radius:9px; font-size:12px; height:50px">
                                                        <span class="text-left d-inline-block pl-2">FORM LINK</span>
                                                        <span class="fa fa-play text-white position-absolute" style="font-size:18px; margin:10px 0px 0 5px; height:50px"></span>
                                                        </span>
                                                        <p style="vertical-align:middle; border-top-right-radius:9px; border-bottom-right-radius:9px; line-height:50px; color:#fff; font-weight:500; padding:0px 15px 0px 80px" class="text-center mb0">https://melkartob.typeform.com/to/yBQJ8xrg</p>
                                                    </div>
                                                </div>
            
            
            <div class="text-center">
                
                
                <div class="s_panel_video" data-video-id="eqw0CX01dsk?rel=0" style="margin:50px 0px">
            <img class="s_tooltip_tabs_tooltip_image s_figure_link img-fluid pb0" src="images/portadavideo.png?607ce9c" alt="MailChimp Tutorial" style="max-width:60%">
        </div>
                
                
                
            </div>
        </section>
        <section style="padding:34px 44px; margin-bottom: 100px; background-color:#f6f7f9; border-radius:10px">
            <h2 style="color:#835977; font-family:'Lato', 'Open Sans', 'Helvetica', Sans; text-align:center; text-transform:capitalize">
                <b>Features Highlights</b>
            </h2>
            <div class="row">
                <div class="col-md-4 col-sm-6" style="margin-top:30px">
                    <div class="bg-white shadow" style="padding:10px; border-radius:10px; min-height:270px">
                        <div class="bg-white shadow" style="width:90px; height:90px; border-radius:50%; margin:0 auto">
                            <img src="images/firebase_01.png" style="border-radius: 130px;padding:0.5em; width:90px; height:90px">
                        </div>
                        <h3 class="text-center my-3" style="font-weight: 600;font-family:'Lato', 'Open Sans', 'Helvetica', Sans; font-weight:normal; font-size:24px;color:#835977;font-weight:500">Synchronize from Odoo to Firebase</h3>
                        <p class="text-center" style="font-weight: 600;font-family:'Lato', 'Open Sans', 'Helvetica', Sans; color:#646464;">
                            Create sync rules for models.
                        </p>
                    </div>
                </div>
                <div class="col-md-4 col-sm-6" style="margin-top:30px">
                    <div class="bg-white shadow" style="padding:10px; border-radius:10px; min-height:270px">
                        <div class="bg-white shadow" style="width:90px; height:90px; border-radius:50%; margin:0 auto">
                            <img src="images/firebase_02.png" style="border-radius: 130px;padding:0.5em; width:90px; height:90px">
                        </div>
                        <h3 class="text-center my-3" style="font-weight: 600;font-family:'Lato', 'Open Sans', 'Helvetica', Sans; font-weight:normal; font-size:24px;color:#835977;font-weight:500">Sync from Firebase to Odoo</h3>
                        <p class="text-center" style="font-weight: 600;font-family:'Lato', 'Open Sans', 'Helvetica', Sans; color:#646464">
                            Real-time communication between Odoo and Firebase.
                        </p>
                    </div>
                </div>
                <div class="col-md-4 col-sm-6" style="margin-top:30px">
                    <div class="bg-white shadow" style="padding:10px; border-radius:10px; min-height:270px">
                        <div class="bg-white shadow" style="width:90px; height:90px; border-radius:50%; margin:0 auto">
                            <img src="images/firebase_03.png" style="border-radius: 130px;padding:0.5em; width:90px; height:90px">
                        </div>
                        <h3 class="text-center my-3" style="font-weight: 600;font-family:'Lato', 'Open Sans', 'Helvetica', Sans; font-weight:normal; font-size:24px;color:#835977;font-weight:500">Synchronize files in Firebase Storage</h3>
                        <p class="text-center" style="font-weight: 600;font-family:'Lato', 'Open Sans', 'Helvetica', Sans; color:#646464;">
                            Choose the model from which you want to choose the files that you are going to synchronize in the firebase storage.
                        </p>
                    </div>
                </div>
                <div class="col-md-4 col-sm-6" style="margin-top:30px">
                    <div class="bg-white shadow" style="padding:10px; border-radius:10px; min-height:270px">
                        <div class="bg-white shadow" style="width:90px; height:90px; border-radius:50%; margin:0 auto">
                            <img src="images/firebase_04.png" style="border-radius: 130px;padding:0.5em; width:90px; height:90px">
                        </div>
                        <h3 class="text-center my-3" style="font-weight: 600;font-family:'Lato', 'Open Sans', 'Helvetica', Sans; font-weight:normal; font-size:24px;color:#835977;font-weight:500">
                            Synchronize Odoo users to Firebase Authentication
                        </h3>
                        <p class="text-center" style="font-weight: 600;font-family:'Lato', 'Open Sans', 'Helvetica', Sans; color:#646464;">
                            Use the Rest Partner model in firebase authentication.
                        </p>
                    </div>
                </div>
            </div>
        </section>
        <section style="margin-bottom: 60px;">
            <div style="background-color: #f6f7f9;padding:64px; border-radius:10px;">
                <ul role="tablist" class="nav nav-tabs justify-content-center" data-tabs="tabs" style="border:none; background-color:#f6f7f9">
                    <li class="nav-item" style="border-top-right-radius:6px; border-top-left-radius:6px; background-color:#835977; margin-right:1px;">
                        <a href="#features" data-toggle="tab" aria-expanded="true" class="active" style="color: #000000;font-family:Roboto;text-transform:uppercase;font-weight:600;font-size:15px;letter-spacing:1px;padding:14px 20px;border-top-left-radius:5px;border-top-right-radius:5px;display: block;border:1px solid #dee2e6;text-decoration: none;background: #835977;">
                            Features
                        </a>
                    </li>
                    <li class="nav-item" style="border-top-right-radius:6px; border-top-left-radius:6px; background-color:#835977; margin-right:1px">
                        <a href="#screenshots" data-toggle="tab" aria-expanded="true" style="color: #000000;font-family:Roboto;text-transform:uppercase;font-weight:600;font-size:15px;letter-spacing:1px;padding:14px 20px;border-top-left-radius:5px;border-top-right-radius:5px;display: block;border:1px solid #dee2e6;text-decoration: none;background: #835977;">
                            Screenshots
                        </a>
                    </li>
                    <li class="nav-item" style="border-top-right-radius:6px; border-top-left-radius:6px; background-color:#835977; margin-right:1px">
                        <a href="#faqs" data-toggle="tab" aria-expanded="true" style="color: #000000;font-family:Roboto;text-transform:uppercase;font-weight:600;font-size:15px;letter-spacing:1px;padding:14px 20px;border-top-left-radius:5px;border-top-right-radius:5px;display: block;border:1px solid #dee2e6;text-decoration: none;background: #835977;">
                            FAQs
                        </a>
                    </li>
                </ul>
                <div id="tabs_content" class="tab-content bg-white" style="border-radius:10px; padding:2%; margin:0% 2%; padding-top:4%">
                    <!-- Features Tabs -->
                    <div class="tab-pane active show" id="features">
                        <ul style="font-weight: 600;margin-bottom:8px !important; font-size:20px; line-height:200% !important; font-family:'Lato', 'Open Sans', 'Helvetica', Sans">
                            <li>
                                Synchronize from Odoo to Firebase.
                            </li>
                            <li>
                                Sync from Firebase to Odoo.
                            </li>
                            <li>
                                Synchronize files in Firebase Storage.
                            </li>
                            <li>
                                Synchronize Odoo users to Firebase Authentication.
                            </li>
                        </ul>
                    </div>
                    <!-- ScreenShots Tabs -->
                    <div class="tab-pane" id="screenshots">
                        <h2 style="font-size:40px;color:#875A7B;font-weight: 600;font-family: 'Lato', 'Open Sans', 'Helvetica', sans-serif;text-align: center;margin: 32px 0;">Create an account - Configuration in Odoo</h2>
                        <img style="width: 100%;" src="images/cap_01.png">
                        <img style="width: 100%;" src="images/cap_02.png">
                        <h2 style="font-size:40px;color:#875A7B;font-weight: 600;font-family: 'Lato', 'Open Sans', 'Helvetica', sans-serif;text-align: center;margin: 32px 0;">Where is the information in Firebase?</h2>
                        <img style="width: 100%;" src="images/cap_03.png">
                        <img style="width: 100%;" src="images/cap_04.png">
                        <h2 style="font-size:40px;color:#875A7B;font-weight: 600;font-family: 'Lato', 'Open Sans', 'Helvetica', sans-serif;text-align: center;margin: 32px 0;">Create sync rules</h2>
                        <img style="width: 100%;" src="images/cap_05.png">
                        <img style="width: 100%;" src="images/cap_06.png">
                        <img style="width: 100%;" src="images/cap_07.png">
                        <img style="width: 100%;" src="images/cap_08.png">
                        <img style="width: 100%;" src="images/cap_09.png">
                        <img style="width: 100%;" src="images/cap_10.png">
                        <h2 style="font-size:40px;color:#875A7B;font-weight: 600;font-family: 'Lato', 'Open Sans', 'Helvetica', sans-serif;text-align: center;margin: 32px 0;">How does this look in firebase?</h2>
                        <img style="width: 100%;" src="images/cap_11.png">
                        <h2 style="font-size:40px;color:#875A7B;font-weight: 600;font-family: 'Lato', 'Open Sans', 'Helvetica', sans-serif;text-align: center;margin: 32px 0;">Synchronize the Partner model with Firebase Authentication</h2>
                        <img style="width: 100%;" src="images/cap_12.png">
                        <h2 style="font-size:40px;color:#875A7B;font-weight: 600;font-family: 'Lato', 'Open Sans', 'Helvetica', sans-serif;text-align: center;margin: 32px 0;">How does this look in firebase?</h2>
                        <img style="width: 100%;" src="images/cap_13.png">
                        <h2 style="font-size:40px;color:#875A7B;font-weight: 600;font-family: 'Lato', 'Open Sans', 'Helvetica', sans-serif;text-align: center;margin: 32px 0;">Synchronize the model you want to choose the files to store in Firebase Storage.</h2>

                        <img style="width: 100%;" src="images/cap_14.png">

                    </div>
                    <!-- FAQs Section -->
                    <div class="tab-pane" id="faqs">
                        <div class="tab__content">
                            <div class="panel-group" id="accordion">
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">
                                            <a class="collapsed" data-toggle="collapse" style="color:#A5187E;font-weight: 400;font-size: 20px;padding: 18px 15px 18px 40px;display: block;" data-parent="#accordion" href="#collapse2">
                                                Whom to contact for Customizations?
                                            </a>
                                        </h4>
                                    </div>
                                    <div id="collapse2" class="panel-collapse collapse">
                                        <div class="panel-body">
                                            You can <NAME_EMAIL>
                                        </div>
                                    </div>
                                </div>
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">
                                            <a class="collapsed" data-toggle="collapse" style="color:#A5187E;font-weight: 400;font-size: 20px;padding: 18px 15px 18px 40px;display: block;" data-parent="#accordion" href="#collapse3">
                                                Do you provide any support?
                                            </a>
                                        </h4>
                                    </div>
                                    <div id="collapse3" class="panel-collapse collapse">
                                        <div class="panel-body">Yes, We provide 90 days free support for bugs.</div>
                                    </div>
                                </div>
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">
                                            <a class="collapsed" data-toggle="collapse" style="color:#A5187E;font-weight: 400;font-size: 20px;padding: 18px 15px 18px 40px;display: block;" data-parent="#accordion" href="#collapse4">
                                                Does your app support Enterprise Version?
                                            </a>
                                        </h4>
                                    </div>
                                    <div id="collapse4" class="panel-collapse collapse">
                                        <div class="panel-body">
                                            Yes, This app Perfectly works with Community, Enterprise and Odoo.sh as well.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <section style="margin-bottom: 50px;">
            <div id="carouselExampleControls" class="carousel slide" style="padding: 0 40px;" data-ride="carousel">
                <div class="carousel-inner">
                    <div class="carousel-item active">
                        <div class="row">
                            <div class="col-xs-12 col-sm-4 col-md-4">
                                <a href="https://apps.odoo.com/apps/modules/14.0/odoo_firebase_core/" target="_blank">
                                    <img class="my-2" style="border-radius:10px;width: 100%;" src="images/firebase.png">
                                </a>
                            </div>
                            <div class="col-xs-12 col-sm-4 col-md-4">
                                <a href="https://apps.odoo.com/apps/modules/14.0/mk_typeform/" target="_blank">
                                    <img class="my-2" style="border-radius:10px;width: 100%;" src="images/typeform.png">
                                </a>
                            </div>
                            <div class="col-xs-12 col-sm-4 col-md-4">
                                <a href="https://apps.odoo.com/apps/modules/14.0/melkart_hellosign/" target="_blank">
                                    <img class="my-2" style="border-radius:10px;width: 100%;" src="images/hellosign.png">
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="carousel-item">
                        <div class="row">
                            <div class="col-xs-12 col-sm-4 col-md-4">
                                <a href="https://apps.odoo.com/apps/modules/14.0/odoo_firebase_core/" target="_blank">
                                    <img class="my-2" style="border-radius:10px;width: 100%;" src="images/firebase.png">
                                </a>
                            </div>
                            <div class="col-xs-12 col-sm-4 col-md-4">
                                <a href="https://apps.odoo.com/apps/modules/14.0/mk_typeform/" target="_blank">
                                    <img class="my-2" style="border-radius:10px;width: 100%;" src="images/typeform.png">
                                </a>
                            </div>
                            <div class="col-xs-12 col-sm-4 col-md-4">
                                <a href="https://apps.odoo.com/apps/modules/14.0/melkart_hellosign/" target="_blank">
                                    <img class="my-2" style="border-radius:10px;width: 100%;" src="images/hellosign.png">
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <a style="left: 0;" class="carousel-control-prev" href="#carouselExampleControls" role="button" data-slide="prev">
                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                    <span class="sr-only">Previous</span>
                </a>
                <a style="right: 0;" class="carousel-control-next" href="#carouselExampleControls" role="button" data-slide="next">
                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                    <span class="sr-only">Next</span>
                </a>
            </div>
        </section>
        <section class="oe_container oe_dark" style="background-color:#835a76">
    <div class="oe_row">
        <div class="oe_span12">
            <center>
                <span href="https://odoo-apps.melkart.io/" target="_blank">
                    <img class="oe_picture" src="images/logo.png" style="height:70px">
                </span>
            </center>
        </div>
    </div>
    <div class="oe_mt16 oe_mb16">
        <div class="separator" style="display:block; width:60px; height:5px; margin:1px auto 15px; background-color:#835a76; border:1px solid #835a76"></div>
    </div>
    <div class="oe_slogan">
        <div class="text-center">
            <div class="mt-5 mb-4">
                <a class="btn-hover" style="width:200px; font-size:19px; font-weight:600; color:#835a76; margin:20px; height:55px; text-align:center; border:none; border-radius:50px; padding:19px 66px; background-color:#ffff" href="mailto:<EMAIL>" target="_blank"><i class="fa fa-envelope"></i> Want Demo or Need
                    Support? </a>
            </div>
        </div>
    </div>
</section>
    </div>
    <style>
        .nav-item a.active {
            background: #fff !important;
            color: #835977 !important;
            padding-bottom: 12px !important;
        }

        .carousel-control-next-icon,
        .carousel-control-prev-icon {
            width: 36px;
            height: 36px;
        }

        .carousel-control-next-icon {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' version='1.1' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z' /%3E%3C/svg%3E");
            right: 0;
            position: absolute;
        }

        .carousel-control-prev-icon {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' version='1.1' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M15.41,16.58L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.58Z' /%3E%3C/svg%3E");
            left: 0;
            position: absolute;
        }

        .panel-body {
            padding: 0.9375rem;
        }

        .panel.panel-default {
            border: 1px solid rgba(0, 0, 0, 0.06);
        }

        .panel-heading .panel-title>a {
            position: relative;
            padding: 18px 15px 18px 50px !important;
        }

        .panel-heading .panel-title>a:before {
            content: "";
            height: 20px;
            width: 20px;
            position: absolute;
            left: 20px;
            top: 50%;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 20 20'%3E%3Cpath id='minus-circle' d='M17,13H7V11H17M12,2A10,10,0,1,0,22,12,10,10,0,0,0,12,2Z' transform='translate(-2 -2)' fill='%23835977'/%3E%3C/svg%3E%0A");
            background-repeat: no-repeat;
            transform: translateY(-50%);
        }

        .panel-heading .panel-title>a.collapsed:before {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 20 20'%3E%3Cpath id='plus-circle' d='M17,13H13v4H11V13H7V11h4V7h2v4h4M12,2A10,10,0,1,0,22,12,10,10,0,0,0,12,2Z' transform='translate(-2 -2)' fill='%23835977'/%3E%3C/svg%3E%0A");
        }
    </style>
    <!-- JS -->
    <script src="../common/jquery-3.6.0.min.js"></script>
    <script src="../common/bootstrap.bundle.min.js"></script>
    <script src="../common/scripts.js"></script>
</body>

</html>
