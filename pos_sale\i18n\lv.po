# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_sale
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <arn<PERSON>@allegro.lv>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# Will <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:18+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: Will Sensors, 2023\n"
"Language-Team: Latvian (https://app.transifex.com/odoo/teams/41243/lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : 2);\n"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderRow.xml:0
#, python-format
msgid "(left:"
msgstr "(atlika:"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/ProductScreen/Orderline.xml:0
#, python-format
msgid "(tax incl.)"
msgstr "(iekļaujot PVN)"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.pos_config_view_form_pos_sale
msgid "<span class=\"o_form_label\">Down Payment Product</span>"
msgstr "<span class=\"o_form_label\">Priekšapmaksas prece</span>"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.pos_config_view_form_pos_sale
msgid "<span class=\"o_form_label\">Sales Team</span>"
msgstr "<span class=\"o_form_label\">Pārdošanas komanda</span>"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.report_invoice_document
msgid "<span style=\"margin: 0px 5px;\">:</span>"
msgstr "<span style=\"margin: 0px 5px;\">:</span>"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "Apply a down payment"
msgstr "Pielietot priekšapmaksas maksājumu"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderManagementControlPanel.xml:0
#, python-format
msgid "Back"
msgstr "Back"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/MobileSaleOrderManagementScreen.xml:0
#, python-format
msgid "Back to list"
msgstr "Atpakaļ uz sarakstu"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderRow.js:0
#, python-format
msgid "Cancelled"
msgstr "Atcelts"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/SetSaleOrderButton.js:0
#, python-format
msgid "Cannot access order management screen if offline."
msgstr "Nevar piekļūt pasūtījumu pārvaldīšanai, ja bezsaistē."

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_order__currency_rate
msgid "Currency Rate"
msgstr "Valūtas kurss"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderList.xml:0
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderRow.xml:0
#, python-format
msgid "Customer"
msgstr "Klients"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "Customer loading error"
msgstr "Klienta ielādes kļūda"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderList.xml:0
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderRow.xml:0
#, python-format
msgid "Date"
msgstr "Datums"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.message_body
msgid "Delivered from"
msgstr "Piegādāts no"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "Do you want to load the SN/Lots linked to the Sales Order?"
msgstr ""

#. module: pos_sale
#: model:product.product,name:pos_sale.default_downpayment_product
#: model:product.template,name:pos_sale.default_downpayment_product_product_template
msgid "Down Payment (POS)"
msgstr "Priekšapmaksas maksājums (POS)"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_order_line__down_payment_details
msgid "Down Payment Details"
msgstr "Priekšapmaksas maksājuma detaļas"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_config__down_payment_product_id
msgid "Down Payment Product"
msgstr "Down Payment Product"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderManagementControlPanel.xml:0
#, python-format
msgid "E.g. customer: Steward, date: 2020-05-09"
msgstr "Piem. klients: Jānis, datums: 09.05.2020"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/ReceiptScreen/OrderReceipt.xml:0
#: model_terms:ir.ui.view,arch_db:pos_sale.report_invoice_document
#, python-format
msgid "From"
msgstr "No"

#. module: pos_sale
#: model:ir.model.fields.selection,name:pos_sale.selection__sale_report__state__invoiced
msgid "Invoiced"
msgstr "Rēķins izrakstīts"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid ""
"It seems that you didn't configure a down payment product in your point of sale.\n"
"                        You can go to your point of sale configuration to choose one."
msgstr ""

#. module: pos_sale
#: code:addons/pos_sale/models/sale_order.py:0
#, python-format
msgid "Linked POS Orders"
msgstr "Saistītie POS pasūtījumi"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_order_line__sale_order_origin_id
msgid "Linked Sale Order"
msgstr "Saistītais pārdošanas pasūtījums"

#. module: pos_sale
#: code:addons/pos_sale/models/pos_order.py:0
#, python-format
msgid "Linked Sale Orders"
msgstr "Saistītie pārdošanas pasūtījumi"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderRow.js:0
#, python-format
msgid "Locked"
msgstr "Locked"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderFetcher.js:0
#: code:addons/pos_sale/static/src/js/SetSaleOrderButton.js:0
#, python-format
msgid "Network Error"
msgstr "Tīkla kļūda"

#. module: pos_sale
#: model:ir.model.fields.selection,name:pos_sale.selection__sale_report__state__pos_draft
msgid "New"
msgstr "Jauns"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderManagementControlPanel.xml:0
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderManagementControlPanel.xml:0
#, python-format
msgid "Next Order List"
msgstr "Nākošais pasūtījumu saraksts"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "No"
msgstr "No"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "No down payment product"
msgstr "Nav priekšapmaksas maksājuma preces"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_crm_team__pos_sessions_open_count
msgid "Open POS Sessions"
msgstr "Atvērtās POS sesijas"

#. module: pos_sale
#: model:ir.actions.act_window,name:pos_sale.pos_session_action_from_crm_team
msgid "Open Sessions"
msgstr "Atvērtās sesijas"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderList.xml:0
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderRow.xml:0
#, python-format
msgid "Order"
msgstr "Maksājuma uzdevums"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_sale_order__pos_order_line_ids
#: model:ir.model.fields,field_description:pos_sale.field_sale_order_line__pos_order_line_ids
msgid "Order lines Transfered to Point of Sale"
msgstr ""

#. module: pos_sale
#: model:ir.model.fields.selection,name:pos_sale.selection__sale_report__state__paid
msgid "Paid"
msgstr "Apmaksāts"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "Percentage of %s"
msgstr "%s procents"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Pārdošanas punkta konfigurācija"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "Pasūtījuma punkta pasūtījuma rindas"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_pos_order
msgid "Point of Sale Orders"
msgstr "Pārdošanas punkta pasūtījumi"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_pos_session
msgid "Point of Sale Session"
msgstr "Pārdošanas punkta sesija"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_crm_team__pos_config_ids
msgid "Point of Sales"
msgstr "Pārdošanas punkti"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_sale_order__pos_order_count
msgid "Pos Order Count"
msgstr "POS pasūtījumu skaits"

#. module: pos_sale
#: model:ir.model.fields.selection,name:pos_sale.selection__sale_report__state__pos_done
msgid "Posted"
msgstr "Grāmatots"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderManagementControlPanel.xml:0
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderManagementControlPanel.xml:0
#, python-format
msgid "Previous Order List"
msgstr "Iepriekšējais pasūtījumu saraksts"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "Products not available in POS"
msgstr "Prece nav pieejama POS"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderRow.js:0
#, python-format
msgid "Quotation"
msgstr "Piedāvājums"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderRow.js:0
#, python-format
msgid "Quotation Sent"
msgstr "Piedāvājums nosūtīts"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/SetSaleOrderButton.xml:0
#, python-format
msgid "Quotation/Order"
msgstr "Piedāvājums/Pasūtījums"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "SN/Lots Loading"
msgstr ""

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/ProductScreen/Orderline.xml:0
#: code:addons/pos_sale/static/src/xml/ProductScreen/Orderline.xml:0
#, python-format
msgid "SO"
msgstr ""

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_order__sale_order_count
msgid "Sale Order Count"
msgstr "Pārdošanas pasūtījuma skaits"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.pos_config_view_form_pos_sale
msgid "Sales"
msgstr "Tirdzniecība"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_sale_report
msgid "Sales Analysis Report"
msgstr "Tirdzniecības analīzes atskaite"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderRow.js:0
#: model:ir.model,name:pos_sale.model_sale_order
#, python-format
msgid "Sales Order"
msgstr "Pasūtījums"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "Pasūtījuma Rinda"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_crm_team
#: model:ir.model.fields,field_description:pos_sale.field_pos_config__crm_team_id
#: model:ir.model.fields,field_description:pos_sale.field_pos_order__crm_team_id
#: model:ir.model.fields,field_description:pos_sale.field_pos_session__crm_team_id
msgid "Sales Team"
msgstr "Sales Team"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.pos_config_view_form_pos_sale
msgid "Sales are reported to the following sales team"
msgstr "Atskaites par tirdzniecību tiek nosūtītas šajai pārdošanas komandai"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderRow.xml:0
#, python-format
msgid "Salesman"
msgstr "Pārdevējs"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderList.xml:0
#, python-format
msgid "Salesperson"
msgstr "Pārdevējs"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.crm_team_view_kanban_dashboard
msgid "Session Running"
msgstr "Sesija darbijas"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_crm_team__pos_order_amount_total
msgid "Session Sale Amount"
msgstr ""

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.crm_team_view_kanban_dashboard
msgid "Sessions Running"
msgstr "Sesijas darbojas"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/SetSaleOrderButton.xml:0
#: code:addons/pos_sale/static/src/xml/SetSaleOrderButton.xml:0
#, python-format
msgid "Set Sale Order"
msgstr ""

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "Settle the order"
msgstr ""

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid ""
"Some of the products in your Sale Order are not available in POS, do you "
"want to import them?"
msgstr ""

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_order_line__sale_order_line_id
msgid "Source Sale Order Line"
msgstr ""

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderList.xml:0
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderRow.xml:0
#, python-format
msgid "State"
msgstr "Posmi"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_sale_report__state
msgid "Status"
msgstr "Statuss"

#. module: pos_sale
#: model:ir.model.fields,help:pos_sale.field_pos_order__currency_rate
msgid ""
"The rate of the currency to the currency of rate applicable at the date of "
"the order"
msgstr ""

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "There was a problem in loading the %s customer."
msgstr "Radās problēma ielādējot %s klientu."

#. module: pos_sale
#: model:ir.model.fields,help:pos_sale.field_pos_config__crm_team_id
#: model:ir.model.fields,help:pos_sale.field_pos_session__crm_team_id
msgid "This Point of sale's sales will be related to this Sales Team."
msgstr ""

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.pos_config_view_form_pos_sale
msgid "This product will be applied when down payment is made"
msgstr ""

#. module: pos_sale
#: model:ir.model.fields,help:pos_sale.field_pos_config__down_payment_product_id
msgid "This product will be used as down payment on a sale order."
msgstr ""

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderList.xml:0
#: code:addons/pos_sale/static/src/xml/OrderManagementScreen/SaleOrderRow.xml:0
#, python-format
msgid "Total"
msgstr "Summa"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.view_pos_order_form_inherit_pos_sale
msgid ""
"Transfered<br/>\n"
"                                from Sale"
msgstr ""

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.view_order_form_inherit_pos_sale
msgid ""
"Transfered<br/>\n"
"                                to POS"
msgstr ""

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderFetcher.js:0
#, python-format
msgid "Unable to fetch orders if offline."
msgstr ""

#. module: pos_sale
#: model:product.product,uom_name:pos_sale.default_downpayment_product
#: model:product.template,uom_name:pos_sale.default_downpayment_product_product_template
msgid "Units"
msgstr "Vienības"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "What do you want to do?"
msgstr "Ko Jūs vēlaties darīt?"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid "Yes"
msgstr "Jā"

#. module: pos_sale
#. openerp-web
#: code:addons/pos_sale/static/src/js/OrderManagementScreen/SaleOrderManagementScreen.js:0
#, python-format
msgid ""
"You have tried to charge a down payment of %s but only %s remains to be "
"paid, %s will be applied to the purchase order line."
msgstr ""
