# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_latam_invoice_document
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.3alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-03-30 18:43+0000\n"
"PO-Revision-Date: 2020-03-30 18:43+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_latam_invoice_document
#: model:ir.model,name:l10n_latam_invoice_document.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model,name:l10n_latam_invoice_document.model_account_move_reversal
msgid "Account Move Reversal"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_l10n_latam_document_type__active
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_document_type_filter
msgid "Active"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,help:l10n_latam_invoice_document.field_l10n_latam_document_type__internal_type
msgid "Analog to odoo account.move.move_type but with more options allowing to identify the kind of document we are working with. (not only related to account.move, could be for documents of other models like stock.picking)"
msgstr ""

#. module: l10n_latam_invoice_document
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_document_type_filter
msgid "Archived"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_l10n_latam_document_type__code
msgid "Code"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,help:l10n_latam_invoice_document.field_l10n_latam_document_type__code
msgid "Code used by different localizations"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model,name:l10n_latam_invoice_document.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_l10n_latam_document_type__country_id
msgid "Country"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_journal__l10n_latam_country_code
msgid "Country Code"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move__l10n_latam_country_code
msgid "Country Code (LATAM)"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,help:l10n_latam_invoice_document.field_l10n_latam_document_type__country_id
msgid "Country in which this type of document is valid"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_l10n_latam_document_type__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_l10n_latam_document_type__create_date
msgid "Created on"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields.selection,name:l10n_latam_invoice_document.selection__l10n_latam_document_type__internal_type__credit_note
msgid "Credit Notes"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields.selection,name:l10n_latam_invoice_document.selection__l10n_latam_document_type__internal_type__debit_note
msgid "Debit Notes"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_l10n_latam_document_type__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_l10n_latam_document_type__doc_code_prefix
msgid "Document Code Prefix"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move__l10n_latam_document_number
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move_reversal__l10n_latam_document_number
msgid "Document Number"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_invoice_report__l10n_latam_document_type_id
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move__l10n_latam_document_type_id
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move_line__l10n_latam_document_type_id
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move_reversal__l10n_latam_document_type_id
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_account_invoice_report_search
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_account_move_line_filter
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_document_type_filter
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_document_type_form
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_document_type_tree
msgid "Document Type"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.actions.act_window,name:l10n_latam_invoice_document.action_document_type
#: model:ir.ui.menu,name:l10n_latam_invoice_document.menu_document_type
msgid "Document Types"
msgstr ""

#. module: l10n_latam_invoice_document
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_document_type_filter
msgid "Group By..."
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_l10n_latam_document_type__id
msgid "ID"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,help:l10n_latam_invoice_document.field_account_journal__l10n_latam_use_documents
#: model:ir.model.fields,help:l10n_latam_invoice_document.field_account_move__l10n_latam_use_documents
msgid "If active: will be using for legal invoicing (invoices, debit/credit notes). If not set means that will be used to register accounting entries not related to invoicing legal documents. For Example: Receipts, Tax Payments, Register journal entries"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_l10n_latam_document_type__internal_type
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_document_type_filter
msgid "Internal Type"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields.selection,name:l10n_latam_invoice_document.selection__l10n_latam_document_type__internal_type__invoice
msgid "Invoices"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model,name:l10n_latam_invoice_document.model_account_invoice_report
msgid "Invoices Statistics"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model,name:l10n_latam_invoice_document.model_account_journal
msgid "Journal"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model,name:l10n_latam_invoice_document.model_account_move
msgid "Journal Entries"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model,name:l10n_latam_invoice_document.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move__l10n_latam_amount_untaxed
msgid "L10N Latam Amount Untaxed"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move__l10n_latam_available_document_type_ids
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move_reversal__l10n_latam_available_document_type_ids
msgid "L10N Latam Available Document Type"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_journal__l10n_latam_company_use_documents
msgid "L10N Latam Company Use Documents"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move_line__l10n_latam_price_net
msgid "L10N Latam Price Net"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move_line__l10n_latam_price_subtotal
msgid "L10N Latam Price Subtotal"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move_line__l10n_latam_price_unit
msgid "L10N Latam Price Unit"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move__l10n_latam_tax_ids
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move_line__l10n_latam_tax_ids
msgid "L10N Latam Tax"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move_reversal__l10n_latam_use_documents
msgid "L10N Latam Use Documents"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_l10n_latam_document_type____last_update
msgid "Last Modified on"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_l10n_latam_document_type__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_l10n_latam_document_type__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model,name:l10n_latam_invoice_document.model_l10n_latam_document_type
msgid "Latam Document Type"
msgstr ""

#. module: l10n_latam_invoice_document
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_document_type_filter
msgid "Localization"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move__l10n_latam_manual_document_number
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move_reversal__l10n_latam_manual_document_number
msgid "Manual Number"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_l10n_latam_document_type__name
msgid "Name"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_l10n_latam_document_type__report_name
msgid "Name on Reports"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,help:l10n_latam_invoice_document.field_l10n_latam_document_type__report_name
msgid "Name that will be printed in reports, for example \"CREDIT NOTE\""
msgstr ""

#. module: l10n_latam_invoice_document
#: code:addons/l10n_latam_invoice_document/models/account_move.py:0
#, python-format
msgid "Please set the document number on the following invoices %s."
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,help:l10n_latam_invoice_document.field_l10n_latam_document_type__doc_code_prefix
msgid "Prefix for Documents Codes on Invoices and Account Moves. For eg. 'FA ' will build 'FA 0001-0000001' Document Number"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_l10n_latam_document_type__sequence
msgid "Sequence"
msgstr ""

#. module: l10n_latam_invoice_document
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_document_type_filter
msgid "Show active document types"
msgstr ""

#. module: l10n_latam_invoice_document
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_document_type_filter
msgid "Show archived document types"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,help:l10n_latam_invoice_document.field_account_journal__l10n_latam_country_code
#: model:ir.model.fields,help:l10n_latam_invoice_document.field_account_move__l10n_latam_country_code
msgid "Technical field used to hide/show fields regarding the localization"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,help:l10n_latam_invoice_document.field_l10n_latam_document_type__name
msgid "The document name"
msgstr ""

#. module: l10n_latam_invoice_document
#: code:addons/l10n_latam_invoice_document/models/account_move.py:0
#, python-format
msgid "The journal require a document type but not document type has been selected on invoices %s."
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,help:l10n_latam_invoice_document.field_l10n_latam_document_type__sequence
msgid "To set in which order show the documents type taking into account the most commonly used first"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_journal__l10n_latam_use_documents
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move__l10n_latam_use_documents
msgid "Use Documents?"
msgstr ""

#. module: l10n_latam_invoice_document
#: code:addons/l10n_latam_invoice_document/models/account_move.py:0
#, python-format
msgid "Vendor bill number must be unique per vendor and company."
msgstr ""

#. module: l10n_latam_invoice_document
#: code:addons/l10n_latam_invoice_document/models/account_move.py:0
#, python-format
msgid "We do not accept the usage of document types on receipts yet. "
msgstr ""

#. module: l10n_latam_invoice_document
#: code:addons/l10n_latam_invoice_document/models/account_journal.py:0
#, python-format
msgid "You can not modify the field \"Use Documents?\" if there are validated invoices in this journal!"
msgstr ""

#. module: l10n_latam_invoice_document
#: code:addons/l10n_latam_invoice_document/models/account_move.py:0
#, python-format
msgid "You can not use a %s document type with a invoice"
msgstr ""

#. module: l10n_latam_invoice_document
#: code:addons/l10n_latam_invoice_document/models/account_move.py:0
#, python-format
msgid "You can not use a %s document type with a refund invoice"
msgstr ""

#. module: l10n_latam_invoice_document
#: code:addons/l10n_latam_invoice_document/wizards/account_move_reversal.py:0
#, python-format
msgid ""
"You can only reverse documents with legal invoicing documents from Latin America one at a time.\n"
"Problematic documents: %s"
msgstr ""
