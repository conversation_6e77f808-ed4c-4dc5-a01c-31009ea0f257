# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * barcodes
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Albanian (https://www.transifex.com/odoo/teams/41243/sq/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sq\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: barcodes
#: code:addons/barcodes/models/barcodes.py:202
#, python-format
msgid " '*' is not a valid Regex Barcode Pattern. Did you mean '.*' ?"
msgstr ""

#. module: barcodes
#: code:addons/barcodes/models/barcodes.py:200
#, python-format
msgid ": a rule can only contain one pair of braces."
msgstr ""

#. module: barcodes
#: code:addons/barcodes/models/barcodes.py:196
#, python-format
msgid ": braces can only contain N's followed by D's."
msgstr ""

#. module: barcodes
#: code:addons/barcodes/models/barcodes.py:198
#, python-format
msgid ": empty braces."
msgstr ""

#. module: barcodes
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_form
msgid ""
"<i>Barcodes Nomenclatures</i> define how barcodes are recognized and categorized.\n"
"                                When a barcode is scanned it is associated to the <i>first</i> rule with a matching\n"
"                                pattern. The pattern syntax is that of regular expression, and a barcode is matched\n"
"                                if the regular expression matches a prefix of the barcode."
msgstr ""

#. module: barcodes
#: model_terms:ir.actions.act_window,help:barcodes.action_barcode_nomenclature_form
msgid ""
"A barcode nomenclature defines how the point of sale identify and interprets"
" barcodes"
msgstr ""

#. module: barcodes
#: code:addons/barcodes/models/barcodes.py:183 selection:barcode.rule,type:0
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule_alias
#, python-format
msgid "Alias"
msgstr ""

#. module: barcodes
#: selection:barcode.nomenclature,upc_ean_conv:0
msgid "Always"
msgstr ""

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule_name
msgid "An internal identification for this barcode nomenclature rule"
msgstr ""

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_nomenclature_name
msgid "An internal identification of the barcode nomenclature"
msgstr ""

#. module: barcodes
#: code:addons/barcodes/models/barcodes.py:177
#: selection:barcode.rule,encoding:0
#, python-format
msgid "Any"
msgstr ""

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule_barcode_nomenclature_id
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_form
msgid "Barcode Nomenclature"
msgstr ""

#. module: barcodes
#: model:ir.actions.act_window,name:barcodes.action_barcode_nomenclature_form
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_tree
msgid "Barcode Nomenclatures"
msgstr ""

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule_pattern
msgid "Barcode Pattern"
msgstr ""

#. module: barcodes
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_rule_form
msgid "Barcode Rule"
msgstr ""

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcodes_barcode_events_mixin__barcode_scanned
msgid "Barcode Scanned"
msgstr ""

#. module: barcodes
#: selection:barcode.rule,type:0
msgid "Cashier"
msgstr ""

#. module: barcodes
#: model_terms:ir.actions.act_window,help:barcodes.action_barcode_nomenclature_form
msgid "Click to add a Barcode Nomenclature ."
msgstr ""

#. module: barcodes
#: selection:barcode.rule,type:0
msgid "Client"
msgstr ""

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature_create_uid
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule_create_uid
msgid "Created by"
msgstr "Krijuar nga"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature_create_date
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule_create_date
msgid "Created on"
msgstr "Krijuar me"

#. module: barcodes
#: selection:barcode.rule,type:0
msgid "Credit Card"
msgstr ""

#. module: barcodes
#. openerp-web
#: code:addons/barcodes/static/src/js/barcode_form_view.js:359
#, python-format
msgid "Discard"
msgstr ""

#. module: barcodes
#: selection:barcode.rule,type:0
msgid "Discounted Product"
msgstr ""

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature_display_name
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule_display_name
#: model:ir.model.fields,field_description:barcodes.field_barcodes_barcode_events_mixin_display_name
msgid "Display Name"
msgstr "Emri i paraqitur"

#. module: barcodes
#: selection:barcode.rule,encoding:0
msgid "EAN-13"
msgstr ""

#. module: barcodes
#: selection:barcode.nomenclature,upc_ean_conv:0
msgid "EAN-13 to UPC-A"
msgstr ""

#. module: barcodes
#: selection:barcode.rule,encoding:0
msgid "EAN-8"
msgstr ""

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule_encoding
msgid "Encoding"
msgstr ""

#. module: barcodes
#. openerp-web
#: code:addons/barcodes/static/src/js/barcode_form_view.js:301
#, python-format
msgid "Error : Barcode command is undefined"
msgstr ""

#. module: barcodes
#. openerp-web
#: code:addons/barcodes/static/src/js/barcode_form_view.js:63
#, python-format
msgid "Error : Document not editable"
msgstr ""

#. module: barcodes
#. openerp-web
#: code:addons/barcodes/static/src/js/barcode_form_view.js:332
#, python-format
msgid "Error : No last scanned barcode"
msgstr ""

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature_id
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule_id
#: model:ir.model.fields,field_description:barcodes.field_barcodes_barcode_events_mixin_id
msgid "ID"
msgstr "ID"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature___last_update
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule___last_update
#: model:ir.model.fields,field_description:barcodes.field_barcodes_barcode_events_mixin___last_update
msgid "Last Modified on"
msgstr "Modifikimi i fundit në"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature_write_uid
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule_write_uid
msgid "Last Updated by"
msgstr "Modifikuar per here te fundit nga"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature_write_date
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule_write_date
msgid "Last Updated on"
msgstr "Modifikuar per here te fundit me"

#. module: barcodes
#: selection:barcode.rule,type:0
msgid "Location"
msgstr ""

#. module: barcodes
#: selection:barcode.rule,type:0
msgid "Lot"
msgstr ""

#. module: barcodes
#: selection:barcode.nomenclature,upc_ean_conv:0
msgid "Never"
msgstr ""

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature_name
msgid "Nomenclature Name"
msgstr ""

#. module: barcodes
#: selection:barcode.rule,type:0
msgid "Package"
msgstr ""

#. module: barcodes
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_form
msgid ""
"Patterns can also define how numerical values, such as weight or price, can be\n"
"                                encoded into the barcode. They are indicated by <code>{NNN}</code> where the N's\n"
"                                define where the number's digits are encoded. Floats are also supported with the \n"
"                                decimals indicated with D's, such as <code>{NNNDD}</code>. In these cases, \n"
"                                the barcode field on the associated records <i>must</i> show these digits as \n"
"                                zeroes."
msgstr ""

#. module: barcodes
#: selection:barcode.rule,type:0
msgid "Priced Product"
msgstr ""

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule_name
msgid "Rule Name"
msgstr ""

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature_rule_ids
msgid "Rules"
msgstr ""

#. module: barcodes
#. openerp-web
#: code:addons/barcodes/static/src/js/barcode_form_view.js:352
#, python-format
msgid "Select"
msgstr ""

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule_sequence
msgid "Sequence"
msgstr "Sekuencë"

#. module: barcodes
#. openerp-web
#: code:addons/barcodes/static/src/js/barcode_form_view.js:351
#, python-format
msgid "Set quantity"
msgstr ""

#. module: barcodes
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_form
msgid "Tables"
msgstr ""

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule_pattern
msgid "The barcode matching pattern"
msgstr ""

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_nomenclature_rule_ids
msgid "The list of barcode rules"
msgstr ""

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule_alias
msgid "The matched pattern will alias to this barcode"
msgstr ""

#. module: barcodes
#: code:addons/barcodes/models/barcodes.py:196
#: code:addons/barcodes/models/barcodes.py:198
#: code:addons/barcodes/models/barcodes.py:200
#, python-format
msgid "There is a syntax error in the barcode pattern "
msgstr ""

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule_encoding
msgid ""
"This rule will apply only if the barcode is encoded with the specified "
"encoding"
msgstr ""

#. module: barcodes
#. openerp-web
#: code:addons/barcodes/static/src/js/barcode_form_view.js:64
#, python-format
msgid "To modify this document, please first start edition."
msgstr ""

#. module: barcodes
#. openerp-web
#: code:addons/barcodes/static/src/js/barcode_form_view.js:333
#, python-format
msgid "To set the quantity please scan a barcode first."
msgstr ""

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule_type
msgid "Type"
msgstr ""

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_nomenclature_upc_ean_conv
msgid ""
"UPC Codes can be converted to EAN by prefixing them with a zero. This "
"setting determines if a UPC/EAN barcode should be automatically converted in"
" one way or another when trying to match a rule with the other encoding."
msgstr ""

#. module: barcodes
#: selection:barcode.rule,encoding:0
msgid "UPC-A"
msgstr ""

#. module: barcodes
#: selection:barcode.nomenclature,upc_ean_conv:0
msgid "UPC-A to EAN-13"
msgstr ""

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature_upc_ean_conv
msgid "UPC/EAN Conversion"
msgstr ""

#. module: barcodes
#: code:addons/barcodes/models/barcodes.py:184 selection:barcode.rule,type:0
#, python-format
msgid "Unit Product"
msgstr ""

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule_sequence
msgid ""
"Used to order rules such that rules with a smaller sequence match first"
msgstr ""

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcodes_barcode_events_mixin__barcode_scanned
msgid "Value of the last barcode scanned."
msgstr ""

#. module: barcodes
#: selection:barcode.rule,type:0
msgid "Weighted Product"
msgstr ""

#. module: barcodes
#: model:ir.model,name:barcodes.model_barcode_nomenclature
msgid "barcode.nomenclature"
msgstr ""

#. module: barcodes
#: model:ir.model,name:barcodes.model_barcode_rule
msgid "barcode.rule"
msgstr ""

#. module: barcodes
#: model:ir.model,name:barcodes.model_barcodes_barcode_events_mixin
msgid "barcodes.barcode_events_mixin"
msgstr ""
