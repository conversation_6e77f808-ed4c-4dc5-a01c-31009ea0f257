# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * web
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <djord<PERSON><EMAIL>>, 2017
# <PERSON><PERSON><PERSON><PERSON> Jo<PERSON>v <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-24 09:00+0000\n"
"PO-Revision-Date: 2017-10-24 09:00+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> Jovev <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:125
#, python-format
msgid " & Close"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:481
#, python-format
msgid " [Me]"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:223
#, python-format
msgid " and "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_inputs.js:565
#: code:addons/web/static/src/js/chrome/search_view.js:203
#, python-format
msgid " or "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:246
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:149
#, python-format
msgid " records"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/view_manager.js:307
#, python-format
msgid " view couldn't be loaded"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:649
#, python-format
msgid "# Code editor"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:107
#: code:addons/web/static/src/js/chrome/search_filters.js:342
#, python-format
msgid "%(field)s %(operator)s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:108
#, python-format
msgid "%(field)s %(operator)s \"%(value)s\""
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/view_manager.js:106
#, python-format
msgid "%(view_type)s view"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:114
#, python-format
msgid "%d days ago"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:112
#, python-format
msgid "%d hours ago"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:110
#, python-format
msgid "%d minutes ago"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:116
#, python-format
msgid "%d months ago"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/debug_manager.js:141
#, python-format
msgid "%d requests (%d ms) %d queries (%d ms)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:118
#, python-format
msgid "%d years ago"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.webclient_bootstrap
msgid ""
"&lt;!--[if lt IE 10]&gt;\n"
"                        &lt;body class=\"ie9\"&gt;\n"
"                    &lt;![endif]--&gt;"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.minimal_layout
msgid "&lt;!DOCTYPE html&gt;"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:371
#, python-format
msgid "'%s' is not a correct date"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/time.js:193
#, python-format
msgid "'%s' is not a correct date, datetime nor time"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:418
#, python-format
msgid "'%s' is not a correct datetime"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:452
#, python-format
msgid "'%s' is not a correct float"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:530
#, python-format
msgid "'%s' is not a correct integer"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:488
#, python-format
msgid "'%s' is not a correct monetary field"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/time.js:205
#, python-format
msgid "'%s' is not convertible to date, datetime nor time"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:77
#, python-format
msgid "(no string)"
msgstr "(prazno)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:10
#, python-format
msgid "(nolabel)"
msgstr "(nolabel)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:244
#, python-format
msgid "1 record"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.menu_secondary
msgid "<span class=\"oe_logo_edit\">Edit Company data</span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.menu_secondary
msgid "<span>Odoo</span>"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/report/qwebactionmanager.js:112
#, python-format
msgid ""
"A popup window with your report was blocked.  You may need to change your "
"browser settings to allow popup windows for this page."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:626
#, python-format
msgid "ALL"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:627
#, python-format
msgid "ANY"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:19
#, python-format
msgid "Access Denied"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:15
#, python-format
msgid "Access Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1341
#, python-format
msgid "Access to all Enterprise Apps"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/sidebar.js:34
#, python-format
msgid "Action"
msgstr "Akcija"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:111
#, python-format
msgid "Action ID:"
msgstr "ID Radnje:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1622
#, python-format
msgid "Activate"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:230
#, python-format
msgid "Activate Assets Debugging"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1621
#: code:addons/web/static/src/js/fields/basic_fields.js:1625
#: code:addons/web/static/src/js/fields/basic_fields.js:1629
#, python-format
msgid "Active"
msgstr "Aktivan"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:1202
#: code:addons/web/static/src/xml/base.xml:1184
#: code:addons/web/static/src/xml/kanban.xml:54
#: code:addons/web/static/src/xml/kanban.xml:64
#, python-format
msgid "Add"
msgstr "Dodaj"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1006
#, python-format
msgid "Add Custom Filter"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1010
#, python-format
msgid "Add a condition"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_editable_renderer.js:516
#, python-format
msgid "Add an item"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1119
#, python-format
msgid "Add custom group"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:634
#, python-format
msgid "Add filter"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:48
#, python-format
msgid "Add new Column"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:694
#, python-format
msgid "Add new value"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1546
#, python-format
msgid "Add to Favorites"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:358
#, python-format
msgid "Add..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:1225
#, python-format
msgid "Add: "
msgstr "Dodaj: "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:983
#, python-format
msgid "Advanced Search..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:209
#, python-format
msgid "Alert"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:616
#: code:addons/web/static/src/xml/base.xml:621
#, python-format
msgid "All"
msgstr "Sve"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:367
#: code:addons/web/static/src/js/views/calendar/calendar_renderer.js:351
#, python-format
msgid "All day"
msgstr "Ceo dan"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:488
#, python-format
msgid "All users"
msgstr "svi korisnici"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:146
#, python-format
msgid "An error occurred"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:188
#, python-format
msgid ""
"An unknown CORS error occured. The error probably originates from a "
"JavaScript file served from a different origin."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1346
#, python-format
msgid "And more"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:617
#: code:addons/web/static/src/xml/base.xml:622
#, python-format
msgid "Any"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1009
#: code:addons/web/static/src/xml/base.xml:1130
#, python-format
msgid "Apply"
msgstr "Primjeni"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1626
#: code:addons/web/static/src/js/views/list/list_controller.js:138
#, python-format
msgid "Archive"
msgstr "Arhiviraj"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:26
#, python-format
msgid "Archive Records"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1625
#, python-format
msgid "Archived"
msgstr "Arhivirani"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:273
#, python-format
msgid "Are you sure that you want to remove this column ?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_menus.js:259
#, python-format
msgid "Are you sure that you want to remove this filter?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/basic/basic_controller.js:290
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:306
#, python-format
msgid "Are you sure you want to delete this record ?"
msgstr "Jeste li sigurni da želite obrisati obaj zapis?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:331
#, python-format
msgid "Attachment :"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1180
#, python-format
msgid "Available fields"
msgstr "Dostupna polja"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:509
#, python-format
msgid "Bar Chart"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1345
#, python-format
msgid "Bugfixes guarantee"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:74
#, python-format
msgid "Button"
msgstr "Dugme"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:103
#, python-format
msgid "Button Type:"
msgstr "Tip Dugmeta"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/utils.js:129
#, python-format
msgid "Bytes,Kb,Mb,Gb,Tb,Pb,Eb,Zb,Yb"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_view.js:22
#, python-format
msgid "Calendar"
msgstr "Kalendar"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_view.js:38
#, python-format
msgid "Calendar view has not defined 'date_start' attribute."
msgstr "Pregled \"Kalendar\" nema definicju 'date_start' attributa."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:223
#: code:addons/web/static/src/js/core/dialog.js:275
#: code:addons/web/static/src/js/fields/relational_fields.js:65
#: code:addons/web/static/src/js/fields/upgrade_fields.js:75
#: code:addons/web/static/src/js/services/crash_manager.js:203
#: code:addons/web/static/src/js/views/calendar/calendar_quick_create.js:56
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:267
#: code:addons/web/static/src/js/views/view_dialogs.js:364
#: code:addons/web/static/src/xml/base.xml:176
#, python-format
msgid "Cancel"
msgstr "Odustani"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:69
#, python-format
msgid "Cannot render chart with mode : "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/controllers/main.py:783
#: code:addons/web/controllers/main.py:785
#: code:addons/web/controllers/main.py:790
#: code:addons/web/controllers/main.py:791
#: code:addons/web/static/src/js/widgets/change_password.js:27
#: code:addons/web/static/src/xml/base.xml:175
#, python-format
msgid "Change Password"
msgstr "Promena lozinke"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:49
#, python-format
msgid "Change default:"
msgstr "Izmeni podrazumevano:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:886
#: code:addons/web/static/src/xml/base.xml:917
#, python-format
msgid "Clear"
msgstr "Očisti"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:227
#, python-format
msgid "Clear Events"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1630
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:317
#: code:addons/web/static/src/js/views/view_dialogs.js:111
#: code:addons/web/static/src/js/widgets/data_export.js:203
#: code:addons/web/static/src/js/widgets/debug_manager.js:428
#: code:addons/web/static/src/js/widgets/domain_selector_dialog.js:24
#, python-format
msgid "Close"
msgstr "Zatvori"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1629
#, python-format
msgid "Closed"
msgstr "Zatvoren"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:52
#, python-format
msgid "Column title"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:462
#, python-format
msgid "Condition:"
msgstr "Uslov:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:170
#, python-format
msgid "Confirm New Password"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:234
#: code:addons/web/static/src/js/core/dialog.js:284
#, python-format
msgid "Confirmation"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:291
#, python-format
msgid "Connection lost"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:300
#, python-format
msgid "Connection restored"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:37
#: code:addons/web/static/src/xml/base.xml:90
#, python-format
msgid "Context:"
msgstr "Sadržaj:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:132
#, python-format
msgid "Copied !"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:144
#, python-format
msgid "Copy the full error to clipboard"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1204
#, python-format
msgid "Could not display the selected image."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/utils.js:432
#, python-format
msgid "Could not serialize XML"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_view.js:43
#: code:addons/web/static/src/js/views/graph/graph_view.js:56
#: code:addons/web/static/src/js/views/pivot/pivot_view.js:39
#: code:addons/web/static/src/js/views/pivot/pivot_view.js:58
#: code:addons/web/static/src/xml/base.xml:505
#: code:addons/web/static/src/xml/base.xml:532
#, python-format
msgid "Count"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:44
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:224
#: code:addons/web/static/src/js/views/calendar/calendar_quick_create.js:45
#: code:addons/web/static/src/js/views/calendar/calendar_quick_create.js:80
#: code:addons/web/static/src/js/views/kanban/kanban_controller.js:343
#: code:addons/web/static/src/js/views/view_dialogs.js:370
#: code:addons/web/static/src/xml/base.xml:404
#: code:addons/web/static/src/xml/base.xml:424
#, python-format
msgid "Create"
msgstr "Kreiraj"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_controller.js:475
#, python-format
msgid "Create "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:367
#, python-format
msgid "Create \"<strong>%s</strong>\""
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:41
#, python-format
msgid "Create a %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:381
#, python-format
msgid "Create and Edit..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:55
#, python-format
msgid "Create and edit"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:410
#: code:addons/web/static/src/js/views/calendar/calendar_quick_create.js:85
#, python-format
msgid "Create: "
msgstr "Kreiraj: "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:336
#, python-format
msgid "Created by :"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:290
#, python-format
msgid "Creation Date:"
msgstr "Datum kreiranja"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:286
#, python-format
msgid "Creation User:"
msgstr "Kreirao korisnik:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_menus.js:180
#, python-format
msgid "Custom Filter"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Database"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:550
#: code:addons/web/static/src/xml/web_calendar.xml:71
#, python-format
msgid "Day"
msgstr "Dan"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1622
#, python-format
msgid "Deactivate"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:445
#, python-format
msgid "Default:"
msgstr "Podrazumevano:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:304
#: code:addons/web/static/src/js/views/form/form_controller.js:155
#: code:addons/web/static/src/js/views/list/list_controller.js:148
#: code:addons/web/static/src/xml/base.xml:1225
#: code:addons/web/static/src/xml/kanban.xml:23
#, python-format
msgid "Delete"
msgstr "Obriši"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:347
#, python-format
msgid "Delete this attachment"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:964
#, python-format
msgid "Delete this file"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:111
#: code:addons/web/static/src/js/widgets/domain_selector_dialog.js:31
#: code:addons/web/static/src/xml/base.xml:411
#: code:addons/web/static/src/xml/base.xml:434
#: code:addons/web/static/src/xml/kanban.xml:66
#: code:addons/web/static/src/xml/report.xml:18
#, python-format
msgid "Discard"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:336
#, python-format
msgid "Do you really want to delete this export template?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_renderer.js:132
#, python-format
msgid "Do you really want to delete this filter from favorites ?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1319
#, python-format
msgid "Documentation"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector_dialog.js:36
#, python-format
msgid "Domain"
msgstr "Domen"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:501
#, python-format
msgid "Domain error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:41
#, python-format
msgid "Domain:"
msgstr "Domen:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:16
#, python-format
msgid "Don't leave yet,<br />it's still loading..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:538
#, python-format
msgid "Download xls"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_controller.js:161
#, python-format
msgid "Duplicate"
msgstr "Udvostruči"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:298
#: code:addons/web/static/src/js/views/calendar/calendar_quick_create.js:50
#: code:addons/web/static/src/xml/base.xml:266
#: code:addons/web/static/src/xml/base.xml:420
#: code:addons/web/static/src/xml/base.xml:885
#: code:addons/web/static/src/xml/kanban.xml:65
#: code:addons/web/static/src/xml/report.xml:14
#, python-format
msgid "Edit"
msgstr "Uredi"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:236
#, python-format
msgid "Edit Action"
msgstr "Uredi Radnje"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:286
#, python-format
msgid "Edit Column"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:602
#, python-format
msgid "Edit Domain"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:267
#, python-format
msgid "Edit SearchView"
msgstr "Uredi PregledPretrage"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:22
#, python-format
msgid "Edit Stage"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Email"
msgstr "E-mail"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Email:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_menus.js:86
#: code:addons/web/static/src/js/chrome/search_menus.js:93
#: code:addons/web/static/src/js/chrome/view_manager.js:307
#: code:addons/web/static/src/js/services/crash_manager.js:135
#, python-format
msgid "Error"
msgstr "Greška"

#. module: web
#: code:addons/web/controllers/main.py:791
#, python-format
msgid "Error, password not changed !"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:490
#, python-format
msgid "Everybody's calendars"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_model.js:490
#, python-format
msgid "Everything"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:537
#, python-format
msgid "Expand all"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_controller.js:133
#, python-format
msgid "Export"
msgstr "Izvoz"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:200
#, python-format
msgid "Export Data"
msgstr "Izvezi podatke"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1173
#, python-format
msgid "Export Formats :"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:202
#, python-format
msgid "Export To File"
msgstr "Izvezi u datoteku"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1166
#, python-format
msgid "Export Type :"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1169
#, python-format
msgid "Export all Data"
msgstr "Izvezi sve Podatke"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/view_manager.js:512
#, python-format
msgid "Failed to evaluate search criterions"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:45
#, python-format
msgid "False"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1135
#, python-format
msgid "Favorites"
msgstr "Omiljeni"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:17
#, python-format
msgid "Field:"
msgstr "Polje:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/debug_manager.js:465
#: code:addons/web/static/src/xml/base.xml:264
#, python-format
msgid "Fields View Get"
msgstr "Uzmi Pregled Polja"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/debug_manager.js:248
#, python-format
msgid "Fields of %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1192
#, python-format
msgid "Fields to export"
msgstr "Polja za izvoz"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1128
#, python-format
msgid "File Upload"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1108
#, python-format
msgid "File upload"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_inputs.js:563
#, python-format
msgid "Filter"
msgstr "Filter"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_menus.js:86
#, python-format
msgid "Filter name is required."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_inputs.js:507
#, python-format
msgid "Filter on: %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_menus.js:93
#, python-format
msgid "Filter with same name already exists."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1002
#, python-format
msgid "Filters"
msgstr "Filteri"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:536
#, python-format
msgid "Flip axis"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:20
#, python-format
msgid "Fold"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot/pivot_controller.js:128
#, python-format
msgid ""
"For Excel compatibility, data cannot be exported if there are more than 256 columns.\n"
"\n"
"Tip: try to flip axis, filter further or reduce the number of measures."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_view.js:17
#, python-format
msgid "Form"
msgstr "Obrazac"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1338
#, python-format
msgid "Get this feature and much more with Odoo Enterprise!"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:18
#, python-format
msgid "Global Business Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_view.js:20
#, python-format
msgid "Graph"
msgstr "Grafikon"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_inputs.js:716
#: code:addons/web/static/src/xml/base.xml:1114
#, python-format
msgid "Group By"
msgstr "Grupiši po"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_inputs.js:691
#, python-format
msgid "Group by: %s"
msgstr ""

#. module: web
#: model:ir.model,name:web.model_ir_http
msgid "HTTP routing"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:250
#, python-format
msgid "I am sure about this."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:274
#, python-format
msgid "ID:"
msgstr "ID:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1204
#, python-format
msgid "Image"
msgstr "Slika"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1168
#, python-format
msgid "Import-Compatible Export"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1621
#, python-format
msgid "Inactive"
msgstr "Neaktivno"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:205
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:213
#, python-format
msgid "Invalid data"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:687
#: code:addons/web/controllers/main.py:701
#, python-format
msgid ""
"Invalid database name. Only alphanumerical characters, underscore, hyphen "
"and dot are allowed."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:600
#, python-format
msgid "Invalid domain"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/model_field_selector.js:393
#: code:addons/web/static/src/xml/base.xml:731
#, python-format
msgid "Invalid field chain"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:68
#, python-format
msgid "Invalid mode for chart"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/debug_manager.js:194
#, python-format
msgid "JS Tests"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_view.js:16
#, python-format
msgid "Kanban"
msgstr "Kanban"

#. module: web
#: code:addons/web/controllers/main.py:798
#, python-format
msgid "Languages"
msgstr "Jezici"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:298
#, python-format
msgid "Latest Modification Date:"
msgstr "Datum poslednje modifikacije"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:294
#, python-format
msgid "Latest Modification by:"
msgstr "Poslednji modifikovao:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:231
#, python-format
msgid "Leave the Developer Tools"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:510
#, python-format
msgid "Line Chart"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_view.js:21
#, python-format
msgid "List"
msgstr "Spisak"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:41
#, python-format
msgid "Load more... ("
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/loading.js:58
#, python-format
msgid "Loading"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/loading.js:56
#, python-format
msgid "Loading (%d)"
msgstr "Učitavanje (%d)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:13
#, python-format
msgid "Loading..."
msgstr "Učitavam..."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Log in"
msgstr "Prijavi me"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1324
#, python-format
msgid "Log out"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_inputs.js:456
#, python-format
msgid "M2O search fields do not currently handle multiple default values"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
msgid "Mail:"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Manage Databases"
msgstr "Upravljanje bazama podataka"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/debug_manager.js:258
#: code:addons/web/static/src/xml/base.xml:239
#, python-format
msgid "Manage Filters"
msgstr "Uredi filtere"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:633
#, python-format
msgid "Match"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:640
#, python-format
msgid "Match records with"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:638
#, python-format
msgid "Match records with the following rule:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:19
#, python-format
msgid "Maybe you should consider reloading the application by pressing F5..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:497
#: code:addons/web/static/src/xml/base.xml:524
#, python-format
msgid "Measures"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/debug_manager.js:342
#, python-format
msgid "Metadata (%s)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:107
#, python-format
msgid "Method:"
msgstr "Metod:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:16
#, python-format
msgid "Missing Record"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1343
#, python-format
msgid "Mobile support"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:341
#, python-format
msgid "Modified by :"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:45
#: code:addons/web/static/src/xml/base.xml:94
#, python-format
msgid "Modifiers:"
msgstr "Modifikatori:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:552
#: code:addons/web/static/src/xml/web_calendar.xml:73
#, python-format
msgid "Month"
msgstr "Mesec"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form/form_renderer.js:276
#: code:addons/web/static/src/xml/base.xml:868
#, python-format
msgid "More"
msgstr "Više"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.menu
msgid "More <b class=\"caret\"/>"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1188
#, python-format
msgid "Move Down"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1187
#, python-format
msgid "Move Up"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1323
#, python-format
msgid "My Odoo.com account"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:628
#, python-format
msgid "NONE"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1242
#, python-format
msgid "Name:"
msgstr "Naziv:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/basic/basic_model.js:491
#, python-format
msgid "New"
msgstr "Novi"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:166
#, python-format
msgid "New Password"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1342
#, python-format
msgid "New design"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_inputs.js:333
#, python-format
msgid "No"
msgstr "Ne"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:282
#, python-format
msgid "No Update:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:75
#, python-format
msgid ""
"No data available for this chart. Try to add some records, or make sure that"
" there is no active filter in the search bar."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:568
#, python-format
msgid ""
"No data available for this pivot table.  Try to add some records, or make sure\n"
"        that there is at least one measure and no active filter in the search bar."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:74
#, python-format
msgid "No data to display"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:567
#, python-format
msgid "No data to display."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/debug_manager.js:330
#, python-format
msgid "No metadata available"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:242
#, python-format
msgid "No records"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:387
#, python-format
msgid "No results to show..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:618
#, python-format
msgid "None"
msgstr "Prazno"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:252
#, python-format
msgid "Not enough data points"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:21
#: code:addons/web/static/src/xml/base.xml:86
#, python-format
msgid "Object:"
msgstr "Objekat:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:51
#, python-format
msgid "Odoo"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/apps.js:148
#, python-format
msgid "Odoo Apps will be available soon"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:186
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:194
#: code:addons/web/static/src/js/services/crash_manager.js:156
#, python-format
msgid "Odoo Client Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/upgrade_fields.js:86
#, python-format
msgid "Odoo Enterprise"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:118
#, python-format
msgid "Odoo Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:58
#, python-format
msgid "Odoo Session Expired"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:108
#: code:addons/web/static/src/js/services/crash_manager.js:197
#, python-format
msgid "Odoo Warning"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1638
#, python-format
msgid "Off"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/dialog.js:55
#: code:addons/web/static/src/js/core/dialog.js:199
#: code:addons/web/static/src/js/core/dialog.js:217
#: code:addons/web/static/src/js/core/dialog.js:268
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:262
#: code:addons/web/static/src/xml/base.xml:1215
#, python-format
msgid "Ok"
msgstr "Ok"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:162
#, python-format
msgid "Old Password"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1637
#, python-format
msgid "On"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:53
#, python-format
msgid "On change:"
msgstr "Pri Izmeni"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1884
#, python-format
msgid "Only Integer Value should be valid."
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:490
#, python-format
msgid ""
"Only employee can access this database. Please contact the administrator."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:481
#, python-format
msgid "Only you"
msgstr "Samo vi"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1630
#, python-format
msgid "Open"
msgstr "Otvoreno"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:215
#, python-format
msgid "Open Developer Tools"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:223
#, python-format
msgid "Open View"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:475
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:284
#: code:addons/web/static/src/js/views/form/form_controller.js:475
#: code:addons/web/static/src/js/views/form/form_controller.js:494
#, python-format
msgid "Open: "
msgstr "Otvori: "

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_background
msgid ""
"Page:\n"
"                    <span class=\"page\"/>\n"
"                    of\n"
"                    <span class=\"topage\"/>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Page: <span class=\"page\"/> / <span class=\"topage\"/>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Password"
msgstr "Lozinka"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Phone:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:511
#, python-format
msgid "Pie Chart"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:214
#, python-format
msgid ""
"Pie chart cannot display all zero numbers.. Try to change your domain to "
"display positive results"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_renderer.js:206
#, python-format
msgid ""
"Pie chart cannot mix positive and negative numbers. Try to change your "
"domain to only display positive results"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot/pivot_view.js:23
#, python-format
msgid "Pivot"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_renderer.js:331
#, python-format
msgid "Please click on the \"save\" button first"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:168
#, python-format
msgid "Please enter save field list name"
msgstr "Molim unesite ime liste za izvoz"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1203
#, python-format
msgid "Please note that only the selected ids will be exported."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1202
#, python-format
msgid ""
"Please pay attention that all records matching your search filter will be "
"exported. Not only the selected ids."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:455
#, python-format
msgid "Please select fields to export..."
msgstr "Odaberite polja za koja treba izvesti"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:441
#, python-format
msgid "Please select fields to save export list..."
msgstr "Odaberite polja za koja treba izvesti i sačuvati..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:147
#, python-format
msgid ""
"Please use the copy button to report the error to your support service."
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.menu_secondary
msgid "Powered by"
msgstr "Pogonjeno od strane"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Powered by <span>Odoo</span>"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1322
#, python-format
msgid "Preferences"
msgstr "Podešavanja"

#. module: web
#: model:ir.actions.report,name:web.action_report_externalpreview
msgid "Preview External Report"
msgstr ""

#. module: web
#: model:ir.actions.report,name:web.action_report_internalpreview
msgid "Preview Internal Report"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/sidebar.js:33
#: code:addons/web/static/src/xml/report.xml:11
#, python-format
msgid "Print"
msgstr "Štampaj"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:553
#, python-format
msgid "Quarter"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/model_field_selector.js:455
#, python-format
msgid "Relation not allowed"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:57
#, python-format
msgid "Relation:"
msgstr "Relacija"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1185
#, python-format
msgid "Remove"
msgstr "Ukloni"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1186
#, python-format
msgid "Remove All"
msgstr "Ukloni sve"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1546
#, python-format
msgid "Remove from Favorites"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/web_calendar.xml:57
#, python-format
msgid "Remove this favorite from the list"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/report/qwebactionmanager.js:94
#, python-format
msgid "Report"
msgstr "Izveštaj"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1626
#, python-format
msgid "Restore"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:27
#, python-format
msgid "Restore Records"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:222
#, python-format
msgid "Run JS Tests"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:125
#: code:addons/web/static/src/js/widgets/domain_selector_dialog.js:28
#: code:addons/web/static/src/xml/base.xml:408
#: code:addons/web/static/src/xml/base.xml:430
#: code:addons/web/static/src/xml/base.xml:1153
#: code:addons/web/static/src/xml/report.xml:17
#, python-format
msgid "Save"
msgstr "Sačuvaj"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:134
#, python-format
msgid "Save & New"
msgstr "Sacuvaj & Novo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1259
#, python-format
msgid "Save As..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1215
#, python-format
msgid "Save as:"
msgstr "Sačuvaj kao:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1141
#, python-format
msgid "Save current search"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/debug_manager.js:429
#, python-format
msgid "Save default"
msgstr "Sačuvaj podrazumevano"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1192
#, python-format
msgid "Save fields list"
msgstr "Sačuvaj spisak polja"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1218
#, python-format
msgid "Saved exports:"
msgstr "Sačuvani izvozni materijal:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_inputs.js:361
#, python-format
msgid "Search %(field)s at: %(value)s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_inputs.js:194
#: code:addons/web/static/src/js/chrome/search_inputs.js:213
#: code:addons/web/static/src/js/chrome/search_inputs.js:402
#, python-format
msgid "Search %(field)s for: %(value)s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:343
#, python-format
msgid "Search More..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:985
#, python-format
msgid "Search..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:410
#, python-format
msgid "Search: "
msgstr "Pretraga: "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:151
#, python-format
msgid "See details"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/view_dialogs.js:377
#: code:addons/web/static/src/xml/base.xml:915
#: code:addons/web/static/src/xml/base.xml:916
#, python-format
msgid "Select"
msgstr "Izaberi"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Select <i class=\"fa fa-database\"/>"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:604
#, python-format
msgid "Select a model to add a filter."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/debug_manager.js:169
#, python-format
msgid "Select a view"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:2303
#, python-format
msgid "Selected records"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:61
#, python-format
msgid "Selection:"
msgstr "Selekcija"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/debug_manager.js:426
#, python-format
msgid "Set Default"
msgstr "Postavi podrazumevano"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:259
#, python-format
msgid "Set Defaults"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1150
#, python-format
msgid "Share with all users"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/apps.js:148
#, python-format
msgid "Showing locally available modules"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:33
#, python-format
msgid "Size:"
msgstr "Veličina:"

#. module: web
#: code:addons/web/controllers/main.py:1109
#, python-format
msgid "Something horrible happened"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:98
#, python-format
msgid "Special:"
msgstr "Specijalno:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:14
#, python-format
msgid "Still loading..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:15
#, python-format
msgid "Still loading...<br />Please be patient."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/web_calendar.xml:80
#, python-format
msgid "Summary:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1320
#, python-format
msgid "Support"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1639
#, python-format
msgid "Switch Off"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1640
#, python-format
msgid "Switch On"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:548
#, python-format
msgid "Syntax error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:18
#, python-format
msgid "Take a minute to get a coffee,<br />because it's loading..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/switch_company_menu.js:38
#, python-format
msgid "Tap on the list to change company"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:240
#, python-format
msgid "Technical Translation"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_clean
msgid "Tel:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:502
#: code:addons/web/static/src/js/widgets/domain_selector.js:548
#, python-format
msgid "The domain you entered is not properly formed"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/model_field_selector.js:394
#, python-format
msgid ""
"The field chain is not valid. Did you maybe use a non-existing field name or"
" followed a non-relational field?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1259
#, python-format
msgid "The field is empty, there's nothing to save !"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/basic/basic_controller.js:380
#, python-format
msgid "The following fields are invalid:"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:785
#, python-format
msgid "The new password and its confirmation must be identical."
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:790
#, python-format
msgid ""
"The old password you provided is incorrect, your password was not changed."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/basic/basic_controller.js:75
#, python-format
msgid ""
"The record has been modified, your changes will be discarded. Do you want to"
" proceed?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1107
#, python-format
msgid "The selected file exceed the maximum file size of %s."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:1289
#, python-format
msgid ""
"The type of the field '%s' must be a many2many field with a relation to "
"'ir.attachment' model."
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:1446
#, python-format
msgid ""
"There are too many rows (%s rows, limit: 65535) to export as Excel 97-2003 "
"(.xls) format. Consider splitting the export."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1128
#, python-format
msgid "There was a problem while uploading your file"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_menus.js:258
#, python-format
msgid ""
"This filter is global and will be removed for everybody if you continue."
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.preview_externalreport
msgid "This is a sample of an external report."
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.preview_internalreport
msgid "This is a sample of an internal report."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1159
#, python-format
msgid ""
"This wizard will export all data that matches the current search criteria to a CSV file.\n"
"        You can export all data or only the fields that can be reimported after modification."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/special_fields.js:93
#, python-format
msgid ""
"Timezone Mismatch : The timezone of your browser doesn't match the selected "
"one. The time in Odoo is displayed according to your field timezone."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:63
#, python-format
msgid "Title"
msgstr "Naslov"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/web_calendar.xml:67
#, python-format
msgid "Today"
msgstr "Danas"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:226
#, python-format
msgid "Toggle Timelines"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot/pivot_model.js:453
#: code:addons/web/static/src/js/views/pivot/pivot_model.js:563
#, python-format
msgid "Total"
msgstr "Ukupno"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:196
#, python-format
msgid "Traceback:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/field_utils.js:45
#, python-format
msgid "True"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:292
#, python-format
msgid "Trying to reconnect..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:25
#, python-format
msgid "Type:"
msgstr "Tip:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/report/qwebactionmanager.js:17
#, python-format
msgid ""
"Unable to find Wkhtmltopdf on this \n"
"system. The report will be shown in html.<br><br><a href=\"http://wkhtmltopdf.org/\" target=\"_blank\">\n"
"wkhtmltopdf.org</a>"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list/list_controller.js:142
#, python-format
msgid "Unarchive"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph/graph_model.js:179
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:73
#: code:addons/web/static/src/js/views/kanban/kanban_column.js:76
#: code:addons/web/static/src/js/views/list/list_renderer.js:422
#: code:addons/web/static/src/js/views/list/list_renderer.js:425
#: code:addons/web/static/src/js/views/pivot/pivot_model.js:689
#, python-format
msgid "Undefined"
msgstr "Nedefinisan"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:574
#, python-format
msgid "Unhandled widget"
msgstr "Nepodržani ekranski dodatak"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:187
#, python-format
msgid "Unknown CORS error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/pyeval.js:1001
#, python-format
msgid "Unknown nonliteral type "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/_deprecated/data.js:737
#, python-format
msgid "Unnamed"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot/pivot_view.js:95
#, python-format
msgid "Untitled"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1301
#, python-format
msgid "Update translations"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/upgrade_fields.js:69
#, python-format
msgid "Upgrade now"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1344
#, python-format
msgid "Upgrade to future versions"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:915
#, python-format
msgid "Upload your file"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:1447
#, python-format
msgid "Uploading Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:888
#: code:addons/web/static/src/xml/base.xml:919
#: code:addons/web/static/src/xml/base.xml:974
#, python-format
msgid "Uploading..."
msgstr "Učitavanje..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1147
#, python-format
msgid "Use by default"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:17
#, python-format
msgid "Validation Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:266
#, python-format
msgid "View"
msgstr "View"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:238
#, python-format
msgid "View Fields"
msgstr "Pregled Polja"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:261
#, python-format
msgid "View Metadata"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/report/qwebactionmanager.js:112
#: code:addons/web/static/src/js/services/crash_manager.js:13
#: code:addons/web/static/src/js/services/crash_manager.js:14
#: code:addons/web/static/src/js/views/basic/basic_controller.js:78
#: code:addons/web/static/src/js/views/list/list_renderer.js:331
#, python-format
msgid "Warning"
msgstr "Upozorenje"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.benchmark_suite
msgid "Web Benchmarks"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.qunit_suite
msgid "Web Tests"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Web:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:551
#: code:addons/web/static/src/xml/web_calendar.xml:72
#, python-format
msgid "Week"
msgstr "Nedelja"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/calendar/calendar_controller.js:351
#, python-format
msgid "Week "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/rainbow_man.js:32
#, python-format
msgid "Well Done!"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:29
#, python-format
msgid "Widget:"
msgstr "Widget:"

#. module: web
#: code:addons/web/controllers/main.py:487
#, python-format
msgid "Wrong login/password"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/basic_fields.js:1884
#, python-format
msgid "Wrong value entered!"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:278
#, python-format
msgid "XML ID:"
msgstr "XML ID:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:554
#, python-format
msgid "Year"
msgstr "Godina"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_inputs.js:332
#: code:addons/web/static/src/xml/base.xml:49
#, python-format
msgid "Yes"
msgstr "Da"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/abstract_web_client.js:301
#, python-format
msgid "You are back online"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/fields/relational_fields.js:71
#, python-format
msgid "You are creating a new %s, are you sure it does not exist yet?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/model_field_selector.js:455
#, python-format
msgid "You cannot follow relations for this field chain construction"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:783
#, python-format
msgid "You cannot leave any password empty."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1299
#, python-format
msgid "You have updated"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/misc.js:17
#, python-format
msgid ""
"You may not believe it,<br />but the application is actually loading..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/report/qwebactionmanager.js:18
#, python-format
msgid ""
"You need to start OpenERP with at least two \n"
"workers to print a pdf version of the reports."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/report/qwebactionmanager.js:19
#, python-format
msgid ""
"You should upgrade your version of\n"
"Wkhtmltopdf to at least 0.12.0 in order to get a correct display of headers and footers as well as\n"
"support for table-breaking between pages.<br><br><a href=\"http://wkhtmltopdf.org/\" \n"
"target=\"_blank\">wkhtmltopdf.org</a>"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/services/crash_manager.js:58
#, python-format
msgid "Your Odoo session expired. Please refresh the current web page."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/report/qwebactionmanager.js:20
#, python-format
msgid ""
"Your installation of Wkhtmltopdf seems to be broken. The report will be "
"shown in html.<br><br><a href=\"http://wkhtmltopdf.org/\" "
"target=\"_blank\">wkhtmltopdf.org</a>"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/kanban/kanban_record.js:199
#, python-format
msgid "[No widget %s]"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:113
#, python-format
msgid "a day ago"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:109
#, python-format
msgid "about a minute ago"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:115
#, python-format
msgid "about a month ago"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:117
#, python-format
msgid "about a year ago"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:111
#, python-format
msgid "about an hour ago"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:633
#, python-format
msgid "all records"
msgstr ""

#. module: web
#: model:ir.model,name:web.model_base
msgid "base"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:28
#, python-format
msgid "child of"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:163
#: code:addons/web/static/src/js/widgets/domain_selector.js:23
#, python-format
msgid "contains"
msgstr "sadrži"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:164
#, python-format
msgid "doesn't contain"
msgstr "ne sadrži"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:263
#: code:addons/web/static/src/js/chrome/search_filters.js:292
#, python-format
msgid "greater than"
msgstr "veće od"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:265
#: code:addons/web/static/src/js/chrome/search_filters.js:294
#, python-format
msgid "greater than or equal to"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:25
#, python-format
msgid "in"
msgstr ""

#. module: web
#: model:ir.model,name:web.model_ir_qweb_field_image
msgid "ir.qweb.field.image"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:284
#: code:addons/web/static/src/js/chrome/search_filters.js:319
#: code:addons/web/static/src/js/widgets/domain_selector.js:828
#: code:addons/web/static/src/xml/base.xml:721
#, python-format
msgid "is"
msgstr "je"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:183
#, python-format
msgid "is after"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:184
#, python-format
msgid "is before"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:185
#, python-format
msgid "is between"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:165
#: code:addons/web/static/src/js/chrome/search_filters.js:181
#: code:addons/web/static/src/js/chrome/search_filters.js:261
#: code:addons/web/static/src/js/chrome/search_filters.js:290
#, python-format
msgid "is equal to"
msgstr "jednako je sa"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:338
#, python-format
msgid "is false"
msgstr "je netačno"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:320
#: code:addons/web/static/src/js/widgets/domain_selector.js:829
#, python-format
msgid "is not"
msgstr "nije"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:166
#: code:addons/web/static/src/js/chrome/search_filters.js:182
#: code:addons/web/static/src/js/chrome/search_filters.js:262
#: code:addons/web/static/src/js/chrome/search_filters.js:291
#, python-format
msgid "is not equal to"
msgstr "nije jednako sa"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:168
#: code:addons/web/static/src/js/chrome/search_filters.js:187
#: code:addons/web/static/src/js/chrome/search_filters.js:268
#: code:addons/web/static/src/js/chrome/search_filters.js:297
#: code:addons/web/static/src/js/chrome/search_filters.js:322
#: code:addons/web/static/src/js/widgets/domain_selector.js:37
#, python-format
msgid "is not set"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:167
#: code:addons/web/static/src/js/chrome/search_filters.js:186
#: code:addons/web/static/src/js/chrome/search_filters.js:267
#: code:addons/web/static/src/js/chrome/search_filters.js:296
#: code:addons/web/static/src/js/chrome/search_filters.js:321
#: code:addons/web/static/src/js/widgets/domain_selector.js:36
#, python-format
msgid "is set"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:337
#, python-format
msgid "is true"
msgstr "je tačno"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/utils.js:111
#, python-format
msgid "kMGTPE"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:264
#: code:addons/web/static/src/js/chrome/search_filters.js:293
#, python-format
msgid "less than"
msgstr "manje od"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/core/translation.js:108
#, python-format
msgid "less than a minute ago"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_filters.js:266
#: code:addons/web/static/src/js/chrome/search_filters.js:295
#, python-format
msgid "less than or equal to"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:18
#: code:addons/web/static/src/xml/base.xml:723
#, python-format
msgid "not"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_inputs.js:232
#, python-format
msgid "not a valid integer"
msgstr "nije celi broj"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/chrome/search_inputs.js:247
#, python-format
msgid "not a valid number"
msgstr "Broj je neispravan"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:24
#, python-format
msgid "not contains"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:26
#, python-format
msgid "not in"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:639
#, python-format
msgid "not set (false)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:642
#, python-format
msgid "of the following rules:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:657
#, python-format
msgid "of:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:714
#: code:addons/web/static/src/xml/base.xml:1082
#, python-format
msgid "or"
msgstr "ili"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:29
#, python-format
msgid "parent of"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:598
#, python-format
msgid "record(s)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/kanban.xml:41
#, python-format
msgid "remaining)"
msgstr "preostalo)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:723
#, python-format
msgid "set"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/domain_selector.js:639
#, python-format
msgid "set (true)"
msgstr ""
