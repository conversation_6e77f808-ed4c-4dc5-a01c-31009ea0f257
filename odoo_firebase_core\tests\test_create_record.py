from odoo.tests.common import TransactionCase
import odoo_firebase_core as odoo_firebase

class TestOdooFirebase(TransactionCase):

    def test_create_user_in_firebase(self):
        # Definir los campos del usuario
        user_data = {
            'name': '<PERSON>',
            'last_name': '<PERSON>',
            'email': '<EMAIL>',
            'password': '123456'
        }

        # Conectar con la base de datos de Firebase
        firebase_config = {
            'apiKey': 'AIzaSyDZSPJtfSEXPcBrogK0KDaKXVS2lHUTCkg',
            'authDomain': '<EMAIL>.g?serviceaccount.com',
            'databaseURL': 'https://console.firebase.google.com/project/test-db-odoo/firestore/data/~2Flead~2F1',
            'storageBucket': 'gs://test-db-odoo.appspot.com'
        }
        firebase_database = odoo_firebase.FirebaseDatabase(firebase_config)

        # Obtener la referencia a la colección de usuarios
        users_ref = firebase_database.get_collection('users')

        # Crear el registro del usuario
        result = users_ref.document(user_data['email']).set(user_data)

        # Verificar que el registro se haya creado correctamente
        self.assertTrue(result, "El registro del usuario no se creó correctamente en Firebase")