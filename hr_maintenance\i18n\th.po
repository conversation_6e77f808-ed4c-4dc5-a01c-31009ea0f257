# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_maintenance
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_equipment__assign_date
msgid "Assigned Date"
msgstr "วันที่ได้รับมอบหมาย"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_equipment__department_id
msgid "Assigned Department"
msgstr "แผนกที่ได้รับมอบหมาย"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_equipment__employee_id
msgid "Assigned Employee"
msgstr "พนักงานที่ได้รับมอบหมาย"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_res_users__equipment_count
msgid "Assigned Equipments"
msgstr ""

#. module: hr_maintenance
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_request_view_form_inherit_hr
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_request_view_search_inherit_hr
msgid "Created By"
msgstr "สร้างโดย"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_request__owner_user_id
msgid "Created by User"
msgstr "สร้างโดยผู้ใช้"

#. module: hr_maintenance
#: model:ir.model.fields.selection,name:hr_maintenance.selection__maintenance_equipment__equipment_assign_to__department
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_form_inherit_hr
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_search_inherit_hr
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_tree_inherit_hr
msgid "Department"
msgstr "แผนก"

#. module: hr_maintenance
#: model:ir.model,name:hr_maintenance.model_hr_employee
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_request__employee_id
#: model:ir.model.fields.selection,name:hr_maintenance.selection__maintenance_equipment__equipment_assign_to__employee
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_form_inherit_hr
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_search_inherit_hr
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_tree_inherit_hr
msgid "Employee"
msgstr "บุคลากร"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_hr_employee__equipment_ids
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_request__equipment_id
msgid "Equipment"
msgstr "อุปกรณ์"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_hr_employee__equipment_count
msgid "Equipments"
msgstr "อุปกรณ์"

#. module: hr_maintenance
#: model:ir.model,name:hr_maintenance.model_maintenance_equipment
msgid "Maintenance Equipment"
msgstr "อุปกรณ์บำรุงรักษา"

#. module: hr_maintenance
#: model:ir.model,name:hr_maintenance.model_maintenance_request
msgid "Maintenance Request"
msgstr "คำร้องขอบำรุงรักษา"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_res_users__equipment_ids
msgid "Managed Equipments"
msgstr ""

#. module: hr_maintenance
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_request_view_search_inherit_hr
msgid "My Maintenances"
msgstr "การซ่อมบำรุงของฉัน"

#. module: hr_maintenance
#: model:ir.model.fields.selection,name:hr_maintenance.selection__maintenance_equipment__equipment_assign_to__other
msgid "Other"
msgstr "อื่นๆ"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_equipment__owner_user_id
msgid "Owner"
msgstr "เจ้าของ"

#. module: hr_maintenance
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_kanban_inherit_hr
msgid "Unassigned"
msgstr "ไม่ได้มอบหมาย"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_equipment__equipment_assign_to
msgid "Used By"
msgstr "ใช้โดย"

#. module: hr_maintenance
#: model:ir.model,name:hr_maintenance.model_res_users
msgid "Users"
msgstr "ผู้ใช้งาน"
