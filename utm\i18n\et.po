# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* utm
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>eli Õigus <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <armaged<PERSON><EMAIL>>, 2022
# Anna, 2023
# JanaAvalah, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: JanaAvalah, 2023\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_medium__active
msgid "Active"
msgstr "Tegev"

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_campaign__is_auto_campaign
msgid "Allows us to filter relevant Campaigns"
msgstr "Võimaldab asjakohaseid kampaaniaid filtreerida"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Approval-based Flow"
msgstr "Kampaania planeerimine"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Approved"
msgstr "Kinnitatud"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_search
msgid "Archived"
msgstr "Arhiveeritud"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.action_view_utm_tag
msgid "Assign tags to your campaigns to organize, filter and track them."
msgstr ""

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Audience-driven Flow"
msgstr "Sisulooja töövoog"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__is_auto_campaign
msgid "Automatically Generated Campaign"
msgstr "Automaatselt loodud kampaania"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_mixin__campaign_id
msgid "Campaign"
msgstr "Kampaania"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__name
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form_quick_create
msgid "Campaign Name"
msgstr "Kampaania nimi"

#. module: utm
#: model:ir.model,name:utm.model_utm_stage
msgid "Campaign Stage"
msgstr "Kampaania etapp"

#. module: utm
#: model:ir.actions.act_window,name:utm.action_view_utm_tag
#: model_terms:ir.ui.view,arch_db:utm.utm_tag_view_tree
msgid "Campaign Tags"
msgstr "Kampaania sildid"

#. module: utm
#: model:ir.actions.act_window,name:utm.utm_campaign_action
#: model:ir.ui.menu,name:utm.menu_utm_campaign_act
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Campaigns"
msgstr "Kampaaniad"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_campaign_action
msgid ""
"Campaigns are used to centralize your marketing efforts and track their "
"results."
msgstr ""
"Kampaaniaid kasutatakse teie turundustegevuse tsentraliseerimiseks ja nende "
"tulemuste jälgimiseks."

#. module: utm
#: model:utm.campaign,name:utm.utm_campaign_christmas_special
msgid "Christmas Special"
msgstr "Jõulu erpakkumine"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Collect ideas, design creative content and publish it once reviewed."
msgstr ""

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__color
#: model:ir.model.fields,field_description:utm.field_utm_tag__color
msgid "Color Index"
msgstr "Värvikood"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Copywriting"
msgstr "Copywriting"

#. module: utm
#: model:utm.source,name:utm.utm_source_craigslist
msgid "Craigslist"
msgstr ""

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_medium_action
msgid "Create a Medium"
msgstr ""

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.action_view_utm_tag
msgid "Create a Tag"
msgstr ""

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_campaign_action
msgid "Create a campaign"
msgstr "Looge kampaania"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.action_view_utm_stage
msgid "Create a stage for your campaigns"
msgstr "Looge oma kampaaniate jaoks etapp"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__create_uid
#: model:ir.model.fields,field_description:utm.field_utm_medium__create_uid
#: model:ir.model.fields,field_description:utm.field_utm_source__create_uid
#: model:ir.model.fields,field_description:utm.field_utm_stage__create_uid
#: model:ir.model.fields,field_description:utm.field_utm_tag__create_uid
msgid "Created by"
msgstr "Loonud"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__create_date
#: model:ir.model.fields,field_description:utm.field_utm_medium__create_date
#: model:ir.model.fields,field_description:utm.field_utm_source__create_date
#: model:ir.model.fields,field_description:utm.field_utm_stage__create_date
#: model:ir.model.fields,field_description:utm.field_utm_tag__create_date
msgid "Created on"
msgstr "Loomise kuupäev"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Creative Flow"
msgstr "Disaineri töövoog"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_kanban
msgid "Delete"
msgstr "Kustuta"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Deploy"
msgstr "Deploy"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Deployed"
msgstr "Tehtud"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: model:utm.stage,name:utm.campaign_stage_2
#, python-format
msgid "Design"
msgstr "Disain"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__display_name
#: model:ir.model.fields,field_description:utm.field_utm_medium__display_name
#: model:ir.model.fields,field_description:utm.field_utm_source__display_name
#: model:ir.model.fields,field_description:utm.field_utm_stage__display_name
#: model:ir.model.fields,field_description:utm.field_utm_tag__display_name
msgid "Display Name"
msgstr "Näidatav nimi"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Done"
msgstr "Tehtud"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_kanban
msgid "Dropdown menu"
msgstr "Rippmenüü"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_kanban
msgid "Edit"
msgstr "Muuda"

#. module: utm
#: model:utm.campaign,name:utm.utm_campaign_email_campaign_products
msgid "Email Campaign - Products"
msgstr "E-posti kampaania  - tooted"

#. module: utm
#: model:utm.campaign,name:utm.utm_campaign_email_campaign_services
msgid "Email Campaign - Services"
msgstr "E-posti kampaania - teenused"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Event-driven Flow"
msgstr "Sündmuste kalender"

#. module: utm
#: model:utm.source,name:utm.utm_source_facebook
msgid "Facebook"
msgstr "Facebook"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Gather Data"
msgstr "Andmete kogumine"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid ""
"Gather data, build a recipient list and write content based on your "
"Marketing target."
msgstr ""

#. module: utm
#: model:utm.source,name:utm.utm_source_glassdoor
msgid "Glassdoor"
msgstr ""

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Group By"
msgstr "Grupeeri"

#. module: utm
#: model:ir.model,name:utm.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP Routing"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__id
#: model:ir.model.fields,field_description:utm.field_utm_medium__id
#: model:ir.model.fields,field_description:utm.field_utm_source__id
#: model:ir.model.fields,field_description:utm.field_utm_stage__id
#: model:ir.model.fields,field_description:utm.field_utm_tag__id
msgid "ID"
msgstr "ID"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Ideas"
msgstr "Ideed"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign____last_update
#: model:ir.model.fields,field_description:utm.field_utm_medium____last_update
#: model:ir.model.fields,field_description:utm.field_utm_source____last_update
#: model:ir.model.fields,field_description:utm.field_utm_stage____last_update
#: model:ir.model.fields,field_description:utm.field_utm_tag____last_update
msgid "Last Modified on"
msgstr "Viimati muudetud"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__write_uid
#: model:ir.model.fields,field_description:utm.field_utm_medium__write_uid
#: model:ir.model.fields,field_description:utm.field_utm_source__write_uid
#: model:ir.model.fields,field_description:utm.field_utm_stage__write_uid
#: model:ir.model.fields,field_description:utm.field_utm_tag__write_uid
msgid "Last Updated by"
msgstr "Viimati uuendas"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__write_date
#: model:ir.model.fields,field_description:utm.field_utm_medium__write_date
#: model:ir.model.fields,field_description:utm.field_utm_source__write_date
#: model:ir.model.fields,field_description:utm.field_utm_stage__write_date
#: model:ir.model.fields,field_description:utm.field_utm_tag__write_date
msgid "Last Updated on"
msgstr "Viimati uuendatud"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Later"
msgstr "Hiljem"

#. module: utm
#: model:utm.source,name:utm.utm_source_mailing
msgid "Lead Recall"
msgstr ""

#. module: utm
#: model:ir.ui.menu,name:utm.menu_link_tracker_root
msgid "Link Tracker"
msgstr "Lingi jälgija"

#. module: utm
#: model:utm.source,name:utm.utm_source_linkedin
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "List-Building"
msgstr "Nimekirja koostamine"

#. module: utm
#: model:utm.tag,name:utm.utm_tag_1
msgid "Marketing"
msgstr "Turundus"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_mixin__medium_id
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_form
msgid "Medium"
msgstr "Levitamise vahend"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_medium__name
msgid "Medium Name"
msgstr "Meediumi nimi"

#. module: utm
#: model:ir.actions.act_window,name:utm.utm_medium_action
#: model:ir.ui.menu,name:utm.menu_utm_medium
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_tree
msgid "Mediums"
msgstr "Keskmised"

#. module: utm
#: model:utm.source,name:utm.utm_source_monster
msgid "Monster"
msgstr ""

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_stage__name
#: model:ir.model.fields,field_description:utm.field_utm_tag__name
msgid "Name"
msgstr "Nimi"

#. module: utm
#: model:utm.stage,name:utm.default_utm_stage
msgid "New"
msgstr "Uus"

#. module: utm
#: model:utm.source,name:utm.utm_source_newsletter
msgid "Newsletter"
msgstr "Infoleht"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_source_action
msgid "No Sources yet!"
msgstr ""

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Pre-Launch"
msgstr "Eelseadistus"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Prepare Campaigns and get them approved before making them go live."
msgstr "Valmistage kampaaniad ette ja kinnitage need enne nende avaldamist."

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid ""
"Prepare your Campaign, test it with part of your audience and deploy it "
"fully afterwards."
msgstr ""
"Valmistage oma kampaania ette, testige seda osal oma sihtgrupist ja võtke "
"see pärast kasutusse."

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Report"
msgstr "Aruanne"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__user_id
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Responsible"
msgstr "Vastutaja"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Review"
msgstr "Ülevaade"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Running"
msgstr "Töös"

#. module: utm
#: model:utm.campaign,name:utm.utm_campaign_fall_drive
msgid "Sale"
msgstr "Müük"

#. module: utm
#: model:utm.stage,name:utm.campaign_stage_1
msgid "Schedule"
msgstr "Graafik"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_search
msgid "Search UTM Medium"
msgstr ""

#. module: utm
#: model:utm.source,name:utm.utm_source_search_engine
msgid "Search engine"
msgstr "Otsingumootor"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Send"
msgstr "Saada"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: model:utm.stage,name:utm.campaign_stage_3
#, python-format
msgid "Sent"
msgstr "Saadetud"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_stage__sequence
msgid "Sequence"
msgstr "Järjestus"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Soft-Launch"
msgstr "Reklaami käivitamine"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Soft-Launch Flow"
msgstr "Turundusspetsialisti töövoog"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_mixin__source_id
#: model_terms:ir.ui.view,arch_db:utm.utm_source_view_form
#: model_terms:ir.ui.view,arch_db:utm.utm_source_view_tree
msgid "Source"
msgstr "Alusdokument"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_source__name
msgid "Source Name"
msgstr "Allika nimi"

#. module: utm
#: model:ir.actions.act_window,name:utm.utm_source_action
#: model:ir.ui.menu,name:utm.menu_utm_source
msgid "Sources"
msgstr "Allikad"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__stage_id
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Stage"
msgstr "Etapp"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_stage_view_search
#: model_terms:ir.ui.view,arch_db:utm.utm_stage_view_tree
msgid "Stages"
msgstr "Etapid"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.action_view_utm_stage
msgid ""
"Stages allow you to organize your workflow  (e.g. : plan, design, in "
"progress,  done, …)."
msgstr ""
"Etapid võimaldavad teil organiseerida tööliikumist (nt planeerimine, disain,"
" töös, tehtud, ...)"

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_tag__color
msgid ""
"Tag color. No color means no display in kanban to distinguish internal tags "
"from public categorization tags."
msgstr ""

#. module: utm
#: model:ir.model.constraint,message:utm.constraint_utm_tag_name_uniq
msgid "Tag name already exists !"
msgstr "Sildi nimi on juba olemas!"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__tag_ids
msgid "Tags"
msgstr "Sildid"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "This Month"
msgstr "See kuu"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "This Week"
msgstr "See nädal"

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_mixin__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""
"See nimetus aitab jälgida antud kampaania tulemusi, nt Jõulu_eripakkumine"

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_mixin__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr "See on postitamise viis, nt postkaart, e-kiri, bänner-reklaam"

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_mixin__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr ""
"See on linki allikas, nt otsingumootor, teine ​​domeen või e-posti nimekirja"
" nimi"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "To be Approved"
msgstr "Kinnitamiseks"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid ""
"Track incoming events (e.g. : Christmas, Black Friday, ...) and publish "
"timely content."
msgstr ""
"Jälgi tulevaid sündmusi (nt. Suur reede, Naistepäev) ja avalda sisu "
"õigeaegselt. "

#. module: utm
#: model:utm.source,name:utm.utm_source_twitter
msgid "Twitter"
msgstr "Twitter"

#. module: utm
#: model:ir.model,name:utm.model_utm_campaign
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form
msgid "UTM Campaign"
msgstr "UTM kampaania"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_tree
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "UTM Campaigns"
msgstr "UTM Kampaaniad"

#. module: utm
#: model:ir.model,name:utm.model_utm_medium
msgid "UTM Medium"
msgstr "UTM keskmine"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_medium_action
msgid ""
"UTM Mediums track the mean that was used to attract traffic (e.g. "
"\"Website\", \"Twitter\", ...)."
msgstr ""

#. module: utm
#: model:ir.model,name:utm.model_utm_mixin
msgid "UTM Mixin"
msgstr ""

#. module: utm
#: model:ir.model,name:utm.model_utm_source
msgid "UTM Source"
msgstr "UTM allikas"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_source_action
msgid ""
"UTM Sources track where traffic comes from  (e.g. \"May Newsletter\", \"\", "
"...)."
msgstr ""

#. module: utm
#: model:ir.actions.act_window,name:utm.action_view_utm_stage
msgid "UTM Stages"
msgstr ""

#. module: utm
#: model:ir.model,name:utm.model_utm_tag
msgid "UTM Tag"
msgstr ""

#. module: utm
#: model:ir.ui.menu,name:utm.marketing_utm
msgid "UTMs"
msgstr "UTM´id"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Use This For My Campaigns"
msgstr "Kasutage seda minu kampaaniate jaoks"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form_quick_create
msgid "e.g. Black Friday"
msgstr "nt. Suur reede"
