# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# דוד<PERSON> מלכה <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> Waizer, 2022
# MichaelHadar, 2022
# NoaFarkash, 2022
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:20+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid " records"
msgstr "רשומות"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "# Code editor"
msgstr "מס' עורך קוד"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d days ago"
msgstr "לפני %d ימים"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d hours ago"
msgstr "לפני %d שעות"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d minutes ago"
msgstr "לפני %d דקות"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d months ago"
msgstr "לפני %d חודשים"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d years ago"
msgstr "לפני %d שנים"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/file_upload_mixin.js:0
#, python-format
msgid "%s Files"
msgstr "%sקבצים"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "%s days ago"
msgstr "לפני%s ימים"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct date"
msgstr "'%s' אינו מידע תקין"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/dates.js:0
#, python-format
msgid "'%s' is not a correct date or datetime"
msgstr "'%s' אינו שדה תאריך או זמן תקין"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/time.js:0
#, python-format
msgid "'%s' is not a correct date, datetime nor time"
msgstr "'%s' אינו מידע תקין, במבנה או זמן"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct datetime"
msgstr "'%s' אינו בתצורת תאריך וזמן תקינה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct float"
msgstr "'%s' אינו מספר עשרוני תקין"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct integer"
msgstr "'%s' אינו מספר שלם תקין"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct monetary field"
msgstr "'%s' אינו שדה כספי תקין"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/time.js:0
#, python-format
msgid "'%s' is not convertible to date, datetime nor time"
msgstr "'%s' אינו ניתן להמרה לתאריך, ,תאריך וזמן או זמן"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/basic/basic_model.js:0
#, python-format
msgid "'%s' is unsynchronized with '%s'."
msgstr "%sלא מסונכרן עם %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/file_upload_progress_card.js:0
#, python-format
msgid "(%s/%sMb)"
msgstr "(%s/%sמגה בייט)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "(change)"
msgstr "(שינוי)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "(count)"
msgstr "(כמות)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_bar.js:0
#: code:addons/web/static/src/search/search_bar/search_bar.js:0
#, python-format
msgid "(no result)"
msgstr "(אין תוצאות)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "(no string)"
msgstr "(ללא מחרוזת)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "(nolabel)"
msgstr "(ללא תווית)"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "07/08/2020"
msgstr "07/08/2020"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "08/07/2020"
msgstr "08/07/2020"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "1 record"
msgstr "1 רשומה"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span class=\"text-nowrap\">$ 2,887.50</span>"
msgstr "<span class=\"text-nowrap\">$ 2,887.50</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span class=\"text-nowrap\">$ <span class=\"oe_currency_value\">\n"
"                                                       22,137.50</span></span>"
msgstr ""
"<span class=\"text-nowrap\">$ <span class=\"oe_currency_value\">\n"
"                                                       22,137.50</span></span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span class=\"text-nowrap\">$ <span "
"class=\"oe_currency_value\">11,750.00</span></span>"
msgstr ""
"<span class=\"text-nowrap\">$<span "
"class=\"oe_currency_value\">11,750.00</span></span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span class=\"text-nowrap\">$ <span "
"class=\"oe_currency_value\">7,500.00</span></span>"
msgstr ""
"<span class=\"text-nowrap\">$<span "
"class=\"oe_currency_value\">7,500.00</span></span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span class=\"text-nowrap\">1,500.00</span>"
msgstr "<span class=\"text-nowrap\">1,500.00</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span class=\"text-nowrap\">2,350.00</span>"
msgstr "<span class=\"text-nowrap\">2,350.00</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span class=\"text-nowrap\">Tax 15%</span>"
msgstr "<span class=\"text-nowrap\">מס 15%</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span class=\"w-100 o_force_ltr\" itemprop=\"streetAddress\">77 Santa Barbara\n"
"                                                   Rd<br/>Pleasant Hill CA 94523<br/>United States</span>"
msgstr ""
"<span class=\"w-100 o_force_ltr\" itemprop=\"streetAddress\">רחוב יפו 97\n"
"                                                   <br/>מרכז העיר ירושלים 9434000<br/>ישראל</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span id=\"line_tax_ids\">15.00%</span>"
msgstr "<span id=\"line_tax_ids\">15.00%</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span itemprop=\"name\">Deco Addict</span>"
msgstr "<span itemprop=\"name\">דן דיזיין</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>$ <span class=\"oe_currency_value\">19,250.00</span></span>"
msgstr "<span>$<span class=\"oe_currency_value\">19,250.00</span></span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>5.000</span>"
msgstr "<span>5.000</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Amount</span>"
msgstr "<span>סכום כולל</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Description</span>"
msgstr "<span>תיאור</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span>Invoice</span>\n"
"                           <span>INV/2020/07/0003</span>"
msgstr ""
"<span>חשבונית</span>\n"
"<span>INV/2020/07/0003</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Payment terms: 30 Days</span>"
msgstr "<span>תנאי תשלום: 30 יום</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Quantity</span>"
msgstr "<span>כמות</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Taxes</span>"
msgstr "<span>מיסים</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Unit Price</span>"
msgstr "<span>מחיר יחידה</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span>[FURN_8220] Four Person Desk<br/>\n"
"                                       Four person modern office workstation</span>"
msgstr ""
"<span>[FURN_8220] שולחן עבודה לארבעה אנשים<br/>\n"
"                                       סביבת עבודה מודרנית לארבעה במשרד</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span>[FURN_8999] Three-Seat Sofa<br/>\n"
"                                       Three Seater Sofa with Lounger in Steel Grey Colour</span>"
msgstr ""
"<span>[FURN_8999] ספה תלת-מושבית<br/>\n"
"                                       ספה תלת-מושבית נפתחת לכיסא נוח בצבע אפור כסוף</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<strong>Due Date:</strong>"
msgstr "<strong>תאריך פרעון:</strong>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<strong>Invoice Date:</strong>"
msgstr "<strong>תאריך אסמכתא:</strong>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<strong>Subtotal</strong>"
msgstr "<strong>סכום ביניים</strong>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<strong>Total</strong>"
msgstr "<strong>סה\"כ</strong>"

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.js:0
#, python-format
msgid "A filter with same name already exists."
msgstr "מסנן בשם זה כבר קיים."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/custom_favorite_item.js:0
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.js:0
#, python-format
msgid "A name for your favorite filter is required."
msgstr "דרוש שם למסנן המועדף עליך."

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"A popup window has been blocked. You may need to change your browser "
"settings to allow popup windows for this page."
msgstr ""
"חלון קופץ נחסם. יתכן שתצטרך לשנות את הגדרות הדפדפן שלך כדי לאפשר חלונות "
"קופצים לדף זה."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "ALL"
msgstr "הכל"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "ANY"
msgstr "כל"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Access Denied"
msgstr "הגישה נדחתה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Access Error"
msgstr "שגיאת גישה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Access to all Enterprise Apps"
msgstr "גישה לכל היישומים הארגוניים"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Action"
msgstr "פעולה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Action ID:"
msgstr "פעולה מספר:"

#. module: web
#: model:ir.model,name:web.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "תצוגת חלון פעולה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Activate Assets Debugging"
msgstr "הפעל איתור באגים על נכסים"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Activate Tests Assets Debugging"
msgstr "הפעל איתור באגים על נכסים (טסטים)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_providers.js:0
#, python-format
msgid "Activate debug mode"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Add"
msgstr "הוסף"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_renderer.js:0
#, python-format
msgid "Add %s"
msgstr "הוסף %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#, python-format
msgid "Add Custom Filter"
msgstr "הוסף מסנן מותאם אישית"

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/group_by_menu/custom_group_by_item.xml:0
#, python-format
msgid "Add Custom Group"
msgstr "הוסף קבוצה מותאמת אישית"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Add a Column"
msgstr "הוסף עמודה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#, python-format
msgid "Add a condition"
msgstr "הוסף תנאי"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_editable_renderer.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_editable_renderer.js:0
#, python-format
msgid "Add a line"
msgstr "הוסף שורה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add branch"
msgstr "הוספת מעבר עמוד"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Add column"
msgstr "הוסף עמודה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add filter"
msgstr "הוסף מסנן"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add new value"
msgstr "הוסף ערך חדש"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add node"
msgstr "הוסף: "

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Add qweb directive context"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add tag"
msgstr "הוסף תגית"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Add to Favorites"
msgstr "הוסף למועדפים"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Additional actions"
msgstr "פעולות נוספות"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Add: "
msgstr "הוסף: "

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/signature_dialog.js:0
#, python-format
msgid "Adopt Your Signature"
msgstr "הגדר את חתימתך"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/signature_dialog.js:0
#, python-format
msgid "Adopt and Sign"
msgstr "קבל וחתום"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/confirmation_dialog/confirmation_dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#, python-format
msgid "Alert"
msgstr "אזהרה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/search_panel_model_extension.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_arch_parser.js:0
#, python-format
msgid "All"
msgstr "הכל"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_model.js:0
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_renderer.js:0
#, python-format
msgid "All day"
msgstr "כל היום"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "All users"
msgstr "כל המשתמשים"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Alt"
msgstr "Alt"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Among the"
msgstr "בין ה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#, python-format
msgid "An error occurred"
msgstr "התרחשה שגיאה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "And more"
msgstr "הוסף עוד"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Any"
msgstr "כל"

#. module: web
#: model:ir.model.fields,help:web.field_base_document_layout__report_header
msgid ""
"Appears by default on the top right corner of your printed documents (report"
" header)."
msgstr ""
"מופיע כברירת מחדל על הפינה הימנית העליונה של המסמכים המודפסים שלך (כותרת "
"דו\"ח)."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#: code:addons/web/static/src/search/group_by_menu/custom_group_by_item.xml:0
#, python-format
msgid "Apply"
msgstr "החל"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#, python-format
msgid "Archive"
msgstr "ארכיון"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Archive All"
msgstr "העבר הכל לארכיון"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#, python-format
msgid ""
"Are you sure that you want to archive all the records from this column?"
msgstr "האם אתה בטוח שאתה רוצה להעביר לארכיון את כל הרשומות מעמודה זו?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#, python-format
msgid "Are you sure that you want to archive all the selected records?"
msgstr "האם אתה בטוח שאתה רוצה להעביר לארכיון את כל הרשומות שנבחרו?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#, python-format
msgid "Are you sure that you want to archive this record?"
msgstr "האם אתה בטוח שברצונך להעביר רשומה זו לארכיון ?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#, python-format
msgid "Are you sure that you want to remove this column ?"
msgstr "האם אתה בטוח להסיר עמודה זו?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.js:0
#, python-format
msgid "Are you sure that you want to remove this filter?"
msgstr "האם אתה בטוח שברצונך להסיר את מסנן זה?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#, python-format
msgid "Are you sure you want to delete these records ?"
msgstr "האם אתה בטוח שברצונך למחוק רשומות אלו?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#, python-format
msgid "Are you sure you want to delete these records?"
msgstr "האם אתה בטוח שברצונך למחוק רשומות אלו?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_controller.js:0
#, python-format
msgid "Are you sure you want to delete this record ?"
msgstr "האם אתה בטוח שברצונך למחוק רשומה זו?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#, python-format
msgid "Are you sure you want to delete this record?"
msgstr "האם אתה בטוח שברצונך למחוק רשומה זו?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Are you sure you want to perform the following update on those"
msgstr "האם אתה בטוח שברצונך לבצע את העדכון על אלה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Ascending"
msgstr "עולה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Attach"
msgstr "צרף"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Attachment"
msgstr "קובץ מצורף"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Auto"
msgstr "אוטומטי"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Available fields"
msgstr "שדות זמינים"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Avatar"
msgstr "אווטר"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__layout_background_image
msgid "Background Image"
msgstr "תמונת רקע"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields_owl.js:0
#, python-format
msgid "Badge"
msgstr "תג"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Badges"
msgstr "תגים"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Bar Chart"
msgstr "תרשים עמודות"

#. module: web
#: model:ir.model,name:web.model_base
msgid "Base"
msgstr "בסיס"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Become Superuser"
msgstr "הפוך למשתמש על"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"Binary fields can not be exported to Excel unless their content is "
"base64-encoded. That does not seem to be the case for %s."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Binary file"
msgstr "קובץ בינארי"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Bugfixes guarantee"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Button"
msgstr "כפתור"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Button Type:"
msgstr "סוג הכפתור:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid ""
"By clicking Adopt and Sign, I agree that the chosen signature/initials will "
"be a valid electronic representation of my hand-written signature/initials "
"for all purposes when it is used on documents, including legally binding "
"contracts."
msgstr ""
"בלחיצה על קבל וחתום, אני מסכים כי החתימה / ראשי התיבות שנבחרו יהיו ייצוג "
"אלקטרוני תקף של החתימה / ראשי התיבות שנכתבו בכתב היד שלי לכל מטרה לשימוש "
"במסמכים, כולל חוזים מחייבים מבחינה משפטית."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/utils.js:0
#, python-format
msgid "Bytes|Kb|Mb|Gb|Tb|Pb|Eb|Zb|Yb"
msgstr "Bytes|Kb|Mb|Gb|Tb|Pb|Eb|Zb|Yb"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_view.js:0
#, python-format
msgid "Calendar"
msgstr "יומן"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Calendar toolbar"
msgstr "לוח שנה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_view.js:0
#, python-format
msgid "Calendar view has not defined 'date_start' attribute."
msgstr "לתצוגת יומן אין תכונת  \"date_start\" מוגדרת."

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/confirmation_dialog/confirmation_dialog.xml:0
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/upgrade_fields.js:0
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_quick_create.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_confirm_dialog.js:0
#: code:addons/web/static/src/legacy/js/views/signature_dialog.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
#, python-format
msgid "Cancel"
msgstr "בטל"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/file_upload_progress_bar.xml:0
#: code:addons/web/static/src/legacy/xml/file_upload_progress_bar.xml:0
#, python-format
msgid "Cancel Upload"
msgstr "בטל העלאה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Card color: %s"
msgstr "צבע כרטיס: %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/change_password.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Change Password"
msgstr "שנה סיסמא"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Change default:"
msgstr "שנה ברירת מחדל:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Change graph"
msgstr "שנה תרשים"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/_deprecated/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields_owl.js:0
#, python-format
msgid "Checkbox"
msgstr "תיבת סימון"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Checkboxes"
msgstr "תיבת סימון"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/colorpicker.js:0
#, python-format
msgid "Choose"
msgstr "בחר"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/file_input/file_input.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Choose File"
msgstr "בחר קובץ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu.js:0
#, python-format
msgid "Choose a debug command..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Clear"
msgstr "נקה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Clear Signature"
msgstr "נקה חתימה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/core/dialog/dialog.xml:0
#: code:addons/web/static/src/core/notifications/notification.xml:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column_quick_create.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/dialog.xml:0
#, python-format
msgid "Close"
msgstr "סגור"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Colors"
msgstr "צבעים"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_renderer.js:0
#, python-format
msgid "Column %s"
msgstr "עמודה %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Column title"
msgstr "שם עמודה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Command"
msgstr "פקודה"

#. module: web
#: model:ir.model,name:web.model_res_company
msgid "Companies"
msgstr "חברות"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__company_id
msgid "Company"
msgstr "חברה"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__company_details
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Company Details"
msgstr "פרטי חברה"

#. module: web
#: model:ir.model,name:web.model_base_document_layout
msgid "Company Document Layout"
msgstr "עיצוב מסמך של החברה"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__logo
msgid "Company Logo"
msgstr "לוגו חברה "

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__name
msgid "Company Name"
msgstr "שם החברה"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__report_header
msgid "Company Tagline"
msgstr "כותרת עליונה"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
msgid "Company name"
msgstr "שם חברה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/comparison_menu/comparison_menu.xml:0
#, python-format
msgid "Comparison"
msgstr "השוואה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Condition:"
msgstr "תנאי:"

#. module: web
#: model:ir.actions.act_window,name:web.action_base_document_layout_configurator
msgid "Configure your document layout"
msgstr "הגדר את עיצוב המסמך"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/confirmation_dialog/confirmation_dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_confirm_dialog.js:0
#, python-format
msgid "Confirmation"
msgstr "אישור"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_handlers.js:0
#, python-format
msgid "Connection lost. Trying to reconnect..."
msgstr "החיבור אבד. מנסה להתחבר מחדש..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_handlers.js:0
#, python-format
msgid "Connection restored. You are back online."
msgstr "החיבור שוחזר. חזרת לאינטרנט."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Context:"
msgstr "הקשר:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Control"
msgstr "בקרה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Control panel buttons"
msgstr "סרגל הכלים של לוח הבקרה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Copied !"
msgstr "הועתק !"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Copy"
msgstr "העתק"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#, python-format
msgid "Copy the full error to clipboard"
msgstr "העתק את השגיאה המלאה ללוח"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Copy to Clipboard"
msgstr "העתק ללוח"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
msgid "Copyright &amp;copy;"
msgstr "זכויות יוצרים &amp;copy;"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/ajax.js:0
#, python-format
msgid "Could not connect to the server"
msgstr "לא היה ניתן להתחבר לשרת"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/signature.js:0
#, python-format
msgid "Could not display the selected image"
msgstr "לא ניתן היה להציג את התמונה שנבחרה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Could not display the specified image url."
msgstr "לא ניתן היה להציג את התמונה שבקישור"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/utils.js:0
#, python-format
msgid "Could not serialize XML"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid ""
"Could not set the cover image: incorrect field (\"%s\") is provided in the "
"view."
msgstr "לא ניתן להגדיר את תמונת השער: שדה שגוי (\"%s\") בתצוגה."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_view.js:0
#: code:addons/web/static/src/legacy/js/views/graph/graph_view.js:0
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_view.js:0
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_view.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/helpers/utils.js:0
#, python-format
msgid "Count"
msgstr "כמות"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__country_id
msgid "Country"
msgstr "ארץ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_quick_create.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_controller.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Create"
msgstr "צור"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#, python-format
msgid "Create "
msgstr "צור "

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Create \"<strong>%s</strong>\""
msgstr "צור \"<strong>%s</strong>\""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Create a new record"
msgstr "צור רשומה חדשה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Create and Edit..."
msgstr "צור וערוך..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Create record"
msgstr "צור רשומה חדשה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Create: %s"
msgstr "צור: %s"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Creation Date:"
msgstr "תאריך יצירה:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Creation User:"
msgstr "משתמש היצירה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Current state"
msgstr "מצב נוכחי"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__custom_colors
msgid "Custom Colors"
msgstr "צבעים בהתאמה אישית"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Dark blue"
msgstr "כחול כהה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Dark purple"
msgstr "סגול כהה"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Database"
msgstr "מסד נתונים"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Date"
msgstr "תאריך"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Date & Time"
msgstr "תאריך ושעה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Day"
msgstr "יום"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_providers.js:0
#, python-format
msgid "Deactivate debug mode"
msgstr "בטל את מצב המפתח"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.debug_icon
msgid ""
"Debug mode is activated#{debug_mode_help}. Click here to exit debug mode."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu.js:0
#, python-format
msgid "Debug tools..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Decimal"
msgstr "עשרוני"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Default"
msgstr "ברירת מחדל"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Default:"
msgstr "ברירת מחדל:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#, python-format
msgid "Delete"
msgstr "מחק"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.xml:0
#, python-format
msgid "Delete item"
msgstr "מחק פריט "

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Delete node"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_editable_renderer.js:0
#, python-format
msgid "Delete row "
msgstr "מחק שורה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Descending"
msgstr "יורד"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Description"
msgstr "תיאור"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/js/widgets/colorpicker.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/legacy/js/widgets/translation_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Discard"
msgstr "בטל"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Discard a record modification"
msgstr "מחק שינוי הרשומה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Discard record"
msgstr "ביטול הרשומה"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/file_upload_progress_bar.js:0
#, python-format
msgid "Do you really want to cancel the upload of %s?"
msgstr "האם אתה באמת רוצה לבטל את ההעלאה של %s?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "Do you really want to delete this export template?"
msgstr "האם באמת ברצונך למחוק את תבנית הייצוא?"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__external_report_layout_id
msgid "Document Template"
msgstr "תבנית מסמך"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "Documentation"
msgstr "תיעוד"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Domain"
msgstr "דומיין"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Domain node"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "Domain not properly formed"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "Domain not supported"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Domain:"
msgstr "דומיין:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "Don't leave yet,"
msgstr "אל תעזוב עדיין"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Don't leave yet,<br />it's still loading..."
msgstr "לא לעזוב עדיין,<br />זה עדיין בטעינה..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Download"
msgstr "הורד"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Download PDF Preview"
msgstr "הורד תצוגה מקדימה של PDF"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/pivot/pivot_view.xml:0
#, python-format
msgid "Download xlsx"
msgstr "הורדת קובץ XLSX"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Draw"
msgstr "צייר"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Dropdown menu"
msgstr "תפריט נשלף"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#, python-format
msgid "Duplicate"
msgstr "שכפל"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_quick_create.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Edit"
msgstr "ערוך"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "Edit Action"
msgstr "ערוך פעולה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#, python-format
msgid "Edit Column"
msgstr "ערוך עמודה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/views/debug_items.js:0
#, python-format
msgid "Edit ControlPanelView"
msgstr "ערוך סרגל בקרה (תצוגת חיפוש)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Edit Domain"
msgstr "ערוך דומיין"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Edit Stage"
msgstr "ערוך שלב"

#. module: web
#. openerp-web
#: code:addons/web/static/src/views/debug_items.js:0
#, python-format
msgid "Edit View: "
msgstr "ערוך תצוגה: "

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Edit a record"
msgstr "ערוך רשומה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Edit record"
msgstr "עריכת רשומה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: model:ir.model.fields,field_description:web.field_base_document_layout__email
#: model_terms:ir.ui.view,arch_db:web.login
#, python-format
msgid "Email"
msgstr "דוא\"ל"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Enable profiling"
msgstr "הפעל ניתוח זמני ריצה"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Error, password not changed !"
msgstr "שגיאה, סיסמה לא הוחלפה !"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Esc to discard"
msgstr "לחץ על Esc לביטול"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_model.js:0
#, python-format
msgid "Everybody's calendars"
msgstr "היומנים של כולם"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_model.js:0
#, python-format
msgid "Everything"
msgstr "הכל"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/pivot/pivot_view.xml:0
#, python-format
msgid "Expand all"
msgstr "הגדל הכל"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "Export"
msgstr "ייצוא"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Export All"
msgstr "ייבא הכל"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "Export Data"
msgstr "ייצוא נתונים"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Export Format:"
msgstr "תצורות ייצוא :"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Exporting grouped data to csv is not supported."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/controllers/main.py:0 code:addons/web/controllers/main.py:0
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "External ID"
msgstr "מזהה חיצוני"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "External link"
msgstr "קישור חיצוני"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/control_panel_model_extension.js:0
#, python-format
msgid "Failed to evaluate search context"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "False"
msgstr "לא\\ כבוי"

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.xml:0
#, python-format
msgid "Favorites"
msgstr "מועדפים"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Field:"
msgstr "שדה:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/views/debug_items.js:0
#: code:addons/web/static/src/views/debug_items.js:0
#, python-format
msgid "Fields View Get"
msgstr "צפה בתצוגה זו"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Fields to export"
msgstr "שדות שיש לייצא"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "File"
msgstr "קובץ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "File upload"
msgstr "העלאת קובץ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/custom_favorite_item.js:0
#, python-format
msgid "Filter with same name already exists."
msgstr "כבר קיים מסנן עם השם הזה."

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/filter_menu/filter_menu.xml:0
#, python-format
msgid "Filters"
msgstr "מסננים"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/pivot/pivot_view.xml:0
#, python-format
msgid "Flip axis"
msgstr "הפוך ציר"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Fold"
msgstr "קפל"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Followed by"
msgstr "עוקבים"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__font
msgid "Font"
msgstr "גופן"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Footer"
msgstr "כותרת תחתונה "

#. module: web
#: model:ir.model.fields,help:web.field_base_document_layout__report_footer
msgid "Footer text displayed at the bottom of all reports."
msgstr "טקסט תחתון מוצג בתחתית כל הדוחות."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_controller.js:0
#: code:addons/web/static/src/views/pivot/pivot_view.js:0
#, python-format
msgid ""
"For Excel compatibility, data cannot be exported if there are more than 16384 columns.\n"
"\n"
"Tip: try to flip axis, filter further or reduce the number of measures."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/form/form_view.js:0
#, python-format
msgid "Form"
msgstr "טופס"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/qweb/qweb_view.js:0
#, python-format
msgid "Freedom View"
msgstr "תצורה חופשית"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Full Name"
msgstr "שם מלא"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Fushia"
msgstr "ורוד כהה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Get this feature and much more with Odoo Enterprise!"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_view.js:0
#: code:addons/web/static/src/views/graph/graph_view.js:0
#, python-format
msgid "Graph"
msgstr "תרשים"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Green"
msgstr "ירוק"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_controller.js:0
#: code:addons/web/static/src/search/group_by_menu/group_by_menu.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Group By"
msgstr "קבץ לפי"

#. module: web
#: model:ir.model,name:web.model_ir_http
msgid "HTTP Routing"
msgstr "ניתוב HTTP"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Handle"
msgstr ""

#. module: web
#: model:ir.model.fields,help:web.field_base_document_layout__company_details
msgid "Header text displayed at the top of all reports."
msgstr "טקסט כותרת מוצג בראש כל הדוחות."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hide in Kanban"
msgstr "הסתר בקנבן"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit DOWN to navigate to the list below"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit ENTER to"
msgstr "הקש ENTER כדי"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit ENTER to CREATE"
msgstr "הקש ENTER כדי ליצור"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit ENTER to SAVE"
msgstr "הקש ENTER כדי לשמור"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit ESCAPE to DISCARD"
msgstr "הקש ESC כדי לבטל"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#, python-format
msgid "I am sure about this."
msgstr "אני בטוח בעניין זה."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "I want to update data (import-compatible export)"
msgstr "אני רוצה לעדכן נתונים (ייצוא תואם ייבוא)"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__id
msgid "ID"
msgstr "תעודה מזהה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "ID:"
msgstr "מזהה:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/basic/basic_model.js:0
#, python-format
msgid ""
"If you change %s or %s, the synchronization will be reapplied and the data "
"will be modified."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Image"
msgstr "תמונה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "In %s days"
msgstr "בעוד %s ימים "

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Integer"
msgstr "מספר שלם"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Interval"
msgstr "מרווח"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_renderer.js:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Invalid data"
msgstr "מידע לא חוקי"

#. module: web
#: code:addons/web/controllers/main.py:0 code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"Invalid database name. Only alphanumerical characters, underscore, hyphen "
"and dot are allowed."
msgstr ""
"שם בסיס נתונים לא חוקי. מותר להשתמש רק בתווים אלפא-נומריים, קו תחתון, מקף "
"ונקודה."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Invalid domain"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Invalid field chain"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector.js:0
#, python-format
msgid ""
"Invalid field chain. You may have used a non-existing field name or followed"
" a non-relational field."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_quick_create.js:0
#: code:addons/web/static/src/legacy/js/widgets/attach_document.js:0
#, python-format
msgid "Invalid fields:"
msgstr "שדות לא חוקיים:"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Invalid inherit mode. Module %s and template name %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#, python-format
msgid ""
"It is possible that the \"t-call\" time does not correspond to the overall time of the\n"
"            template. Because the global time (in the drop down) does not take into account the\n"
"            duration which is not in the rendering (look for the template, read, inheritance,\n"
"            compilation...). During rendering, the global time also takes part of the time to make\n"
"            the profile as well as some part not logged in the function generated by the qweb."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_view.js:0
#, python-format
msgid "Kanban"
msgstr "קנבן"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column_quick_create.js:0
#, python-format
msgid "Kanban Examples"
msgstr "דוגמאות לקנבן"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Kanban: no action for type: "
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Languages"
msgstr "שפות"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout____last_update
msgid "Last Modified on"
msgstr "שינוי אחרון ב"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Latest Modification Date:"
msgstr "תאריך שינוי אחרון :"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Latest Modification by:"
msgstr "שינוי אחרון על ידי:"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Layout"
msgstr "עיצוב"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__layout_background
msgid "Layout Background"
msgstr "רקע עיצוב המסמך"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Leave the Developer Tools"
msgstr "עזוב את כלי המפתחים"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Light blue"
msgstr "כחול בהיר"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Line Chart"
msgstr "תרשים רציף"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_view.js:0
#, python-format
msgid "List"
msgstr "רשימה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Load"
msgstr "טען"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Load more... ("
msgstr "טען עוד... ("

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/webclient/loading_indicator/loading_indicator.xml:0
#, python-format
msgid "Loading"
msgstr "טוען"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Loading, please wait..."
msgstr "טוען, המתן בבקשה..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Loading..."
msgstr "טוען..."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Log in"
msgstr "התחבר"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Log in as superuser"
msgstr "התחבר כמשתמש על"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "Log out"
msgstr "התנתק"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_bold
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
#: model_terms:ir.ui.view,arch_db:web.external_layout_striped
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Logo"
msgstr "לוגו"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__logo_primary_color
msgid "Logo Primary Color"
msgstr "צבע לוגו ראשי"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__logo_secondary_color
msgid "Logo Secondary Color"
msgstr "צבע לוגו משני"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Mac"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "MailDeliveryException"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#: code:addons/web/static/src/views/pivot/pivot_view.xml:0
#, python-format
msgid "Main actions"
msgstr "פעולה עיקרית"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/debug_manager.js:0
#, python-format
msgid "Manage Attachments"
msgstr "נהל קבצים מצורפים"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Manage Databases"
msgstr "ניהול מסדי נתונים"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "Manage Filters"
msgstr "ניהול מסננים"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Many2many"
msgstr "רבים לרבים"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Many2one"
msgstr "רבים ליחיד"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Match"
msgstr "התאם"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Match records with"
msgstr "התאם רשומות עם"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Match records with the following rule:"
msgstr "התאם רשומות עם הכלל הבא:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Maybe you should consider reloading the application by pressing F5..."
msgstr "מומלץ לנסות לטעון מחדש על ידי לחיצה על F5..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_controller.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view.xml:0
#, python-format
msgid "Measures"
msgstr "מדדים"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Medium blue"
msgstr "כחול בינוני"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_quick_create.js:0
#, python-format
msgid "Meeting Subject"
msgstr "נושא הפגישה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Meeting Subject:"
msgstr "נושא הפגישה:"

#. module: web
#: model:ir.model,name:web.model_ir_ui_menu
msgid "Menu"
msgstr "תפריט"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Method:"
msgstr "שיטה:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Missing Record"
msgstr "רשומה חסרה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Mobile support"
msgstr "תמיכה בנייד"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "Model Record Rules"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Modifiers:"
msgstr "מגדירים:"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"Module %s not loaded or inexistent, or templates of addon being loaded (%s) "
"are misordered"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Monetary"
msgstr "כספי"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Month"
msgstr "חודש"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/form/form_renderer.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "More"
msgstr "עוד"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Multiline Text"
msgstr "טקסט מרובה שורות"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "My Odoo.com account"
msgstr "חשבון ה-Odoo שלי"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "NONE"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/basic/basic_model.js:0
#, python-format
msgid "New"
msgstr "חדש"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "New %s"
msgstr "%s חדש"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_controller.js:0
#, python-format
msgid "New Event"
msgstr "אירוע חדש"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "New Password"
msgstr "סיסמה חדשה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "New Password (Confirmation)"
msgstr "סיסמא חדשה (אימות)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "New design"
msgstr "עיצוב חדש"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "New template"
msgstr "תבנית חדשה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Next"
msgstr "הבא"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Next page"
msgstr "דף הבא"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_bar.js:0
#: code:addons/web/static/src/search/search_bar/search_bar.js:0
#, python-format
msgid "No"
msgstr "לא"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "No Update:"
msgstr "אין עדכון:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/client_actions.js:0
#, python-format
msgid "No action with id '%s' could be found"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "No color"
msgstr "ללא צבע"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/commands/default_providers.js:0
#: code:addons/web/static/src/core/debug/debug_menu.js:0
#, python-format
msgid "No commands found"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_renderer.js:0
#: code:addons/web/static/src/views/graph/graph_renderer.js:0
#, python-format
msgid "No data"
msgstr "אין נתונים"

#. module: web
#. openerp-web
#: code:addons/web/static/src/views/helpers/no_content_helpers.xml:0
#, python-format
msgid "No data to display"
msgstr "אין מידע להצגה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "No match found."
msgstr "לא נמצאה התאמה."

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/menus/menu_providers.js:0
#, python-format
msgid "No menu found"
msgstr "לא נמצאו תפריטים"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "No records"
msgstr "אין רשומות"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#, python-format
msgid "No records found!"
msgstr "לא נמצאו רשומות!"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/commands/command_palette.js:0
#, python-format
msgid "No results found"
msgstr " נמצאו תוצאות"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "No template found to inherit from. Module %s and template name %s"
msgstr "לא נמצאה תבנית לרשת ממנה. מודול %s ושם תבנית%s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#, python-format
msgid "No valid record to save"
msgstr "אין רשומות ולידיות לשמירה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid "No view of type '%s' could be found in the current action."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "None"
msgstr "אף אחד"

#. module: web
#: code:addons/web/models/models.py:0 code:addons/web/models/models.py:0
#: code:addons/web/models/models.py:0
#, python-format
msgid "Not Set"
msgstr "לא מוגדר"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Not active state"
msgstr "מצב לא פעיל"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Not active state, click to change it"
msgstr "מצב לא פעיל, לחץ על מנת לשנות זאת"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Object:"
msgstr "אובייקט:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: model_terms:ir.ui.view,arch_db:web.brand_promotion_message
#, python-format
msgid "Odoo"
msgstr "Odoo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/apps.js:0
#, python-format
msgid "Odoo Apps will be available soon"
msgstr "אפליקציות Odoo יהיו זמינות בקרוב"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Client Error"
msgstr "שגיאת Odoo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/upgrade_fields.js:0
#, python-format
msgid "Odoo Enterprise"
msgstr "Odoo אנטרפרייז"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Error"
msgstr "שגיאת Odoo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Network Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Server Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/public/error_notifications.js:0
#, python-format
msgid "Odoo Session Expired"
msgstr "תם זמן הפעולה של אפליקציית Odoo !"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Warning"
msgstr "אזהרת Odoo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#, python-format
msgid ""
"Of the %d records selected, only the first %d have been archived/unarchived."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/confirmation_dialog/confirmation_dialog.xml:0
#: code:addons/web/static/src/core/dialog/dialog.xml:0
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_confirm_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/public/error_notifications.js:0
#: code:addons/web/static/src/webclient/actions/action_dialog.xml:0
#, python-format
msgid "Ok"
msgstr "אישור"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Old Password"
msgstr "סיסמא ישנה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "On change:"
msgstr "בשינוי:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "One2many"
msgstr "יחיד לרבים"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"Only employees can access this database. Please contact the administrator."
msgstr "רק עובדים יכולים לגשת לבסיס הנתונים הזה. יש ליצור קשר עם מנהל האתר."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#, python-format
msgid "Only the first %d records have been deleted (out of %d selected)"
msgstr ""

#. module: web
#: code:addons/web/models/models.py:0
#, python-format
msgid ""
"Only types %(supported_types)s are supported for category (found type "
"%(field_type)s)"
msgstr ""
"רק סוגים %(supported_types)s נתמכים עבור הקטגוריה (סוג שנמצא %(field_type)s)"

#. module: web
#: code:addons/web/models/models.py:0
#, python-format
msgid ""
"Only types %(supported_types)s are supported for filter (found type "
"%(field_type)s)"
msgstr ""
"רק סוגים %(supported_types)s נתמכים עבור המסנן (סוג שנמצא %(field_type)s)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Only you"
msgstr "רק אתה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Open Command Palette"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/debug_items.js:0
#, python-format
msgid "Open View"
msgstr "פתח תצוגה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Open the next record"
msgstr "פתח את הרשומה הבאה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Open the previous record"
msgstr "פתח את הרשומה הקודמת"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Open to kanban view"
msgstr "פתח לתצוגת קנבן"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Open to list view"
msgstr "פתח לתצוגת רשימה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#, python-format
msgid "Open: "
msgstr "פתח:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_controller.js:0
#, python-format
msgid "Open: %s"
msgstr "פתיחה: %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_renderer.js:0
#, python-format
msgid "Optional columns"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Orange"
msgstr "כתום"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column_progressbar.js:0
#, python-format
msgid "Other"
msgstr "אחר"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "PDF Viewer"
msgstr "מציג PDF"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "PDF controls"
msgstr "בקרה PDF"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_striped
msgid ""
"Page:\n"
"                    <span class=\"page\"/>\n"
"                    of\n"
"                    <span class=\"topage\"/>"
msgstr ""
"עמוד:\n"
"                    <span class=\"page\"/>\n"
"                    מ\n"
"                    <span class=\"topage\"/>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Page: <span class=\"page\"/> / <span class=\"topage\"/>"
msgstr "עמוד: <span class=\"page\"/> / <span class=\"topage\"/>"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Pager"
msgstr ""

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__paperformat_id
msgid "Paper format"
msgstr "תבנית נייר"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__partner_id
msgid "Partner"
msgstr "לקוח/ספק"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Password"
msgstr "סיסמה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Percentage"
msgstr "אחוז"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Percentage Pie"
msgstr "תרשים עוגה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: model:ir.model.fields,field_description:web.field_base_document_layout__phone
#, python-format
msgid "Phone"
msgstr "טלפון"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/colorpicker.js:0
#, python-format
msgid "Pick a color"
msgstr "בחר צבע"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Pie Chart"
msgstr "תרשים עוגה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_renderer.js:0
#, python-format
msgid "Pie chart cannot mix positive and negative numbers. "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid ""
"Pie chart cannot mix positive and negative numbers. Try to change your "
"domain to only display positive results"
msgstr ""
"תרשים עוגה לא יכול לערבב מספרים חיוביים ושליליים. נסה לשנות את התחום כך "
"שיציג תוצאות חיוביות בלבד"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_view.js:0
#: code:addons/web/static/src/views/pivot/pivot_view.js:0
#, python-format
msgid "Pivot"
msgstr "ציר"

#. module: web
#: code:addons/web/controllers/pivot.py:0
#, python-format
msgid "Pivot %(title)s (%(model_name)s)"
msgstr "ציור %(title)s (%(model_name)s)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/pivot/pivot_view.xml:0
#, python-format
msgid "Pivot settings"
msgstr "הגדרות Pivot"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "Please be patient."
msgstr "נא להמתין בסבלנות"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_renderer.js:0
#, python-format
msgid "Please click on the \"save\" button first"
msgstr "נא ללחוץ קודם על כפתור ה\"שמירה\""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Please enter a numerical value"
msgstr "נא להזין ערך מספרי"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "Please enter save field list name"
msgstr "אנא הכנס שם שדה מבסיס נתונים:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/attach_document.js:0
#, python-format
msgid "Please save before attaching a file"
msgstr "נא לשמור לפני צירוף קובץ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "Please select fields to export..."
msgstr "נא לבחור שדות לייצוא"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "Please select fields to save export list..."
msgstr "נא לבחור שדות לשמירה ברשימת ייצוא"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Please update translations of :"
msgstr "אנא עדכן תרגומים של:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#, python-format
msgid ""
"Please use the copy button to report the error to your support service."
msgstr "אנא השתמש בלחצן ההעתקה כדי לדווח על השגיאה לשירות התמיכה שלך."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"Please use the following communication for your payment : <b><span>\n"
"                           INV/2020/07/0003</span></b>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.brand_promotion_message
msgid "Powered by %s%s"
msgstr "מופעל ע\"י %s%s"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Powered by <span>Odoo</span>"
msgstr "מופעל ע\"י <span>Odoo</span>"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "Preferences"
msgstr "העדפות"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__preview
msgid "Preview"
msgstr "תצוגה מקדימה"

#. module: web
#: model:ir.actions.report,name:web.action_report_externalpreview
msgid "Preview External Report"
msgstr "תצוגה מקדימה של דוח חיצוני"

#. module: web
#: model:ir.actions.report,name:web.action_report_internalpreview
msgid "Preview Internal Report"
msgstr "תצוגה מקדימה של דוח פנימי"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__preview_logo
msgid "Preview logo"
msgstr "תצוגה מקדימה של הלוגו"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Previous"
msgstr "קודם"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Previous Period"
msgstr "תקופה קודמת"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Previous Year"
msgstr "שנה קודמת"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Previous menu"
msgstr "תפריט קודם"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Previous page"
msgstr "דף קודם"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__primary_color
msgid "Primary Color"
msgstr "צבע יסוד"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/report.xml:0
#: code:addons/web/static/src/legacy/xml/report.xml:0
#, python-format
msgid "Print"
msgstr "הדפס"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/report.xml:0
#, python-format
msgid "Printing options"
msgstr "אפשרויות הדפסה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Priority"
msgstr "קְדִימוּת"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/file_upload_progress_card.js:0
#: code:addons/web/static/src/legacy/xml/file_upload_progress_card.xml:0
#: code:addons/web/static/src/legacy/xml/file_upload_progress_card.xml:0
#, python-format
msgid "Processing..."
msgstr "מבצע..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Progress Bar"
msgstr "סרגל התקדמות"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Purple"
msgstr "סגול"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Q1"
msgstr "רבעון 1"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Q2"
msgstr "רבעון 2"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Q3"
msgstr "רבעון 3"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Q4"
msgstr "רבעון 4"

#. module: web
#: model:ir.model.fields.selection,name:web.selection__ir_actions_act_window_view__view_mode__qweb
msgid "QWeb"
msgstr "QWeb"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Quarter"
msgstr "רבעון"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Quick add"
msgstr "הוספה מהירה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Quick search: %s"
msgstr "חיפוש מהיר: %s"

#. module: web
#: model:ir.model,name:web.model_ir_qweb_field_image
#: model:ir.model,name:web.model_ir_qweb_field_image_url
msgid "Qweb Field Image"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/colorpicker.xml:0
#, python-format
msgid "RGB"
msgstr "RGB"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/colorpicker.xml:0
#, python-format
msgid "RGBA"
msgstr "RGBA"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Radio"
msgstr "רדיו"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Record qweb"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Record sql"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Record traces"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Red"
msgstr "אדום"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Refresh"
msgstr "רענן"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Regenerate Assets Bundles"
msgstr "צור מחדש מקבצי נכסים"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector.js:0
#, python-format
msgid "Relation not allowed"
msgstr "יחס לא מורשה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Relation to follow"
msgstr "יחס למעקב"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Relation:"
msgstr "יחס:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Remaining Days"
msgstr "ימים שנשארו"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#, python-format
msgid "Remove"
msgstr "הסר"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Remove Cover Image"
msgstr "הסר תמונת כריכה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "Remove field"
msgstr "הסר שדה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Remove from Favorites"
msgstr "הסר מהמועדפים"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Remove tag"
msgstr "הסר תגית"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Remove this favorite from the list"
msgstr "הסר את המועדף הזה מהרשימה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid "Report"
msgstr "דוח"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__report_footer
msgid "Report Footer"
msgstr "כותרת תחתונה של הדו\"ח"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__report_layout_id
msgid "Report Layout"
msgstr "דווח על פריסה"

#. module: web
#: model:ir.actions.report,name:web.action_report_layout_preview
msgid "Report Layout Preview"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/public/error_notifications.js:0
#, python-format
msgid "Request timeout"
msgstr "הבקשה אינה בתוקף"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Reset to logo colors"
msgstr "לאפס לצבעי לוגו"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/clickbot/clickbot_loader.js:0
#, python-format
msgid "Run Click Everywhere Test"
msgstr "הרץ בדיקת \"לחץ בכל מקום\""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/debug_items.js:0
#, python-format
msgid "Run JS Mobile Tests"
msgstr "הרץ בדיקות JS (מכשיר נייד)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/debug_items.js:0
#, python-format
msgid "Run JS Tests"
msgstr "הרץ בדיקות JS"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/signature.js:0
#, python-format
msgid "SIGNATURE"
msgstr "חתימה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Salmon pink"
msgstr "ורוד בהיר"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/legacy/js/widgets/translation_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.xml:0
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
#, python-format
msgid "Save"
msgstr "שמור"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#, python-format
msgid "Save & Close"
msgstr "שמור וסגור"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#, python-format
msgid "Save & New"
msgstr "שמור וצור חדש"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Save a record"
msgstr "שמור רשומה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Save as :"
msgstr "שמור כ :"

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.xml:0
#, python-format
msgid "Save current search"
msgstr "שמור חיפוש נוכחי"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Save default"
msgstr "שמירת ברירת מחדל"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Save record"
msgstr "שמירת רשומה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#, python-format
msgid "Search"
msgstr "חיפוש"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Search More..."
msgstr "חפש עוד..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/commands/command_service.js:0
#, python-format
msgid "Search for a command..."
msgstr "חיפוש פקודה..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Search for records"
msgstr "חפש רשומות"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/commands/command_palette.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#, python-format
msgid "Search..."
msgstr "חיפוש…"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Search: %s"
msgstr "חיפוש: %s"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__secondary_color
msgid "Secondary Color"
msgstr "צבע משני"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#, python-format
msgid "See details"
msgstr "ראה פרטים"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "See examples"
msgstr "ראה דוגמאות"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Select"
msgstr "בחר"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid ""
"Select <i class=\"fa fa-database\" role=\"img\" aria-label=\"Database\" "
"title=\"Database\"/>"
msgstr ""
"בחר <i class=\"fa fa-database\" role=\"img\" aria-label=\"Database\" "
"title=\"Database\"/>"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Select Signature Style"
msgstr "בחר סגנון חתימה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Select a model to add a filter."
msgstr "בחר מודל כדי להוסיף מסנן."

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/debug_items.js:0
#, python-format
msgid "Select a view"
msgstr "בחר תצוגה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Select all"
msgstr "בחירת הכל"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Select field"
msgstr "בחר שדה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Selected records"
msgstr "רשומות שנבחרו"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Selection"
msgstr "בחירה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Selection:"
msgstr "בחירה:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/debug_manager.js:0
#, python-format
msgid "Set Default"
msgstr "קבע כברירת מחדל"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/debug_manager.js:0
#, python-format
msgid "Set Defaults"
msgstr "הגדר ברירות מחדל"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Set a Cover Image"
msgstr "הגדר תמונת כריכה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Set a kanban state..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Set a priority..."
msgstr "הגדרת עדיפות..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/special_fields.js:0
#, python-format
msgid "Set a timezone on your user"
msgstr "הגדר אזור זמן למשתמש שלך"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Set kanban state..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Set priority..."
msgstr "הגדרת עדיפות..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Settings"
msgstr "הגדרות"

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.xml:0
#, python-format
msgid "Share with all users"
msgstr "שתף עם משתמשים אחרים"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "Shortcuts"
msgstr "קיצורי דרך"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Show sub-fields"
msgstr "הצגת תת-שדה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/apps.js:0
#, python-format
msgid "Showing locally available modules"
msgstr "מציג מודולים זמינים מקומיים"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/signature.js:0
#, python-format
msgid "Signature"
msgstr "חתימה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Size:"
msgstr "גודל:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/ajax.js:0
#, python-format
msgid ""
"Something happened while trying to contact the server, check that the server"
" is online and that you still have a working network connection."
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Something horrible happened"
msgstr "משהו נוראי קרה!"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Sort graph"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Special:"
msgstr "מיוחד:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Stacked"
msgstr "מונח בערימה חסרת סדר"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Start typing..."
msgstr "התחל לכתוב..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#: code:addons/web/static/src/core/ui/block_ui.js:0
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Still loading..."
msgstr "עדיין טוען..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Still loading...<br />Please be patient."
msgstr "עדיין טוען...<br />נא להמתין."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Style"
msgstr "סגנון"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Styles"
msgstr "סגנונות"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "Support"
msgstr "תמיכה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "Syntax error"
msgstr "שגיאת תחביר"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Tags"
msgstr "תגיות"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "Take a minute to get a coffee,"
msgstr "קחו לכם רגע להכין קפה,"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Take a minute to get a coffee,<br />because it's loading..."
msgstr "הטעינה עדיין מתבצעת..."

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__vat
msgid "Tax ID"
msgstr "ח.פ / ע.מ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "Technical Translation"
msgstr "תרגום טכני"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_bold
msgid "Tel:"
msgstr "טלפון:"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Template %s already exists in module %s"
msgstr "תבנית %s קיימת כבר במודול %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Template:"
msgstr "תבנית:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Text"
msgstr "טקסט"

#. module: web
#: model:ir.model.fields,help:web.field_base_document_layout__vat
msgid ""
"The Tax Identification Number. Complete it if the contact is subjected to "
"government taxes. Used in some legal statements."
msgstr ""
"מספר זיהוי המס. השלם אותו אם איש הקשר כפוף למסים ממשלתיים. משמש בכמה הצהרות "
"משפטיות."

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"The content of this cell is too long for an XLSX file (more than %s "
"characters). Please use the CSV format for this export."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "The field is empty, there's nothing to save."
msgstr "השדה ריק, אין מה לשמור."

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "The new password and its confirmation must be identical."
msgstr "הסיסמה והאישור סיסמה צריכים להיות זהים"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"The old password you provided is incorrect, your password was not changed."
msgstr "הסיסמה הישנה שהזמנת אינה נכונה, סיסמתך לא הוחלפה."

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#: code:addons/web/static/src/public/error_notifications.js:0
#, python-format
msgid ""
"The operation was interrupted. This usually means that the current operation"
" is taking too much time."
msgstr "הפעולה הופסקה. פירוש הדבר בדרך כלל שהפעולה הנוכחית אורכת זמן רב מדי."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "The selected file exceed the maximum file size of %s."
msgstr "הקובץ שנבחר עולה על גודל מותר של %s."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid ""
"The type of the field '%s' must be a many2many field with a relation to "
"'ir.attachment' model."
msgstr "סוג השדה '%s' חייב להיות שדה רבים לרבים עם קשר למודל 'ir.attachment'."

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"There are too many rows (%s rows, limit: %s) to export as Excel 2007-2013 "
"(.xlsx) format. Consider splitting the export."
msgstr ""
"יש יותר מדי שורות (%s שורות, מגבלה: %s) לייצא כפורמט Excel 2007-2013 "
"(.xlsx). שקול לפצל את הייצוא."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "There is no available image to be set as cover."
msgstr "אין תמונה זמינה להגדרה ככריכה."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "There was a problem while uploading your file"
msgstr "היתה בעיה בהעלאת הקובץ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/date_picker.js:0
#, python-format
msgid "This date is in the future. Make sure this is what you expect."
msgstr "התאריך הזה בעתיד, כדאי לוודא שלזה ציפית."

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/datepicker/datepicker.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "This date is on the future. Make sure it is what you expected."
msgstr "תאריך זה עתידי. וודא שזה מה שציפית."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "This domain is not supported."
msgstr "הדומיין אינו נתמך."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "This file is invalid. Please select an image."
msgstr "קובץ זה אינו חוקי. אנא בחר תמונה."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.js:0
#, python-format
msgid ""
"This filter is global and will be removed for everybody if you continue."
msgstr "פילטר התצוגה הזה הוא גלובלי. אם תמשיכו, הסרתו תשפיע על כולם."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.preview_externalreport
msgid "This is a sample of an external report."
msgstr "זוהי דוגמה של דוח חיצוני."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.preview_internalreport
msgid "This is a sample of an internal report."
msgstr "זוהי דוגמה של דוח פנימי."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "This update will only consider the records of the current page."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Time"
msgstr "זמן"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/special_fields.js:0
#, python-format
msgid ""
"Timezone Mismatch : This timezone is different from that of your browser.\n"
"Please, set the same timezone as your browser's to avoid time discrepancies in your system."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Today"
msgstr "היום"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Toggle"
msgstr "החלף"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Tomorrow"
msgstr "מחר"

#. module: web
#: code:addons/web/models/models.py:0
#, python-format
msgid "Too many items to display."
msgstr "יותר מדי פריטים להציג."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_renderer.js:0
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_model.js:0
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_model.js:0
#: code:addons/web/static/src/views/graph/graph_model.js:0
#: code:addons/web/static/src/views/graph/graph_model.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#, python-format
msgid "Total"
msgstr "סה\"כ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/translation_dialog.js:0
#, python-format
msgid "Translate: %s"
msgstr "תרגום: %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "True"
msgstr "נכון"

#. module: web
#. openerp-web
#: code:addons/web/static/src/views/helpers/no_content_helpers.xml:0
#, python-format
msgid ""
"Try to add some records, or make sure that there is no\n"
"                    active filter in the search bar."
msgstr "נסה להוסיף כמה רשומות, או וודא שאין מסנן פעיל בשורת החיפוש."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_renderer.js:0
#, python-format
msgid "Try to change your domain to only display positive results"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Type:"
msgstr "סוג:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "URL"
msgstr "URL"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"Unable to find Wkhtmltopdf on this system. The report will be shown in html."
msgstr "לא ניתן למצוא Wkhtmltopdf במערכת זו. הדוח יוצג ב- html."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#, python-format
msgid "Unarchive"
msgstr "הוצא מהארכיון"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Unarchive All"
msgstr "הוצא הכל מהארכיון"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_service.js:0
#, python-format
msgid "Uncaught CORS Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_service.js:0
#, python-format
msgid "Uncaught Javascript Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_service.js:0
#, python-format
msgid "Uncaught Promise"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/controllers/main.py:0
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_model.js:0
#: code:addons/web/static/src/legacy/js/views/graph/graph_model.js:0
#: code:addons/web/static/src/legacy/js/views/graph/graph_renderer.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_model.js:0
#: code:addons/web/static/src/views/graph/graph_model.js:0
#: code:addons/web/static/src/views/graph/graph_model.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid "Undefined"
msgstr "לא מוגדר"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Unfold"
msgstr "פרוס"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_service.js:0
#, python-format
msgid ""
"Unknown CORS error\n"
"\n"
"An unknown CORS error occured.\n"
"The error probably originates from a JavaScript file served from a different origin.\n"
"(Opening your browser console might give you a hint on the error.)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/py_utils.js:0
#, python-format
msgid "Unknown nonliteral type "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_editable_renderer.js:0
#, python-format
msgid "Unlink row "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/_deprecated/data.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/control_panel/control_panel.xml:0
#, python-format
msgid "Unnamed"
msgstr "ללא שם"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_view.js:0
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_view.js:0
#: code:addons/web/static/src/views/graph/graph_view.js:0
#: code:addons/web/static/src/views/pivot/pivot_view.js:0
#, python-format
msgid "Untitled"
msgstr "ללא כותרת"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Update to:"
msgstr "עדכן ל:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/upgrade_fields.js:0
#, python-format
msgid "Upgrade now"
msgstr "שדרג עכשיו"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Upgrade to enterprise"
msgstr "שדרוג לחשבון ארגוני"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Upgrade to future versions"
msgstr "שדרג לגרסאות עתידיות"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Upload and Set"
msgstr "העלה והגדר"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/file_upload_progress_bar.js:0
#, python-format
msgid "Upload cancelled"
msgstr "העלאה בוטלה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
#, python-format
msgid "Upload your file"
msgstr "העלה את הקובץ שלך"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Uploaded"
msgstr "הועלה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Uploading"
msgstr "מעלה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Uploading Error"
msgstr "שגיאת העלאה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Uploading..."
msgstr "מעלה..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/file_upload_progress_card.js:0
#, python-format
msgid "Uploading... (%s%%)"
msgstr "מעלה... (%s%%)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column_quick_create.js:0
#, python-format
msgid "Use This For My Kanban"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.xml:0
#, python-format
msgid "Use by default"
msgstr "שימוש בזה כברירת מחדל"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu.xml:0
#, python-format
msgid "User"
msgstr "משתמש"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "User Error"
msgstr "שגיאת משתמש"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Validation Error"
msgstr "שגיאת אימות"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/basic/basic_renderer.js:0
#, python-format
msgid "Values set here are company-specific."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_model.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#, python-format
msgid "Variation"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "View %s"
msgstr "תצוגה %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "View Access Rights"
msgstr "צפה בהרשאות גישה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "View Fields"
msgstr "צפה בשדות"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/debug_manager.js:0
#: code:addons/web/static/src/legacy/debug_manager.js:0
#, python-format
msgid "View Metadata"
msgstr "צפה במטא-דאטא"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "View Record Rules"
msgstr "צפה בחוקי רשומה"

#. module: web
#: model:ir.model.fields,field_description:web.field_ir_actions_act_window_view__view_mode
msgid "View Type"
msgstr "סוג תצוגה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "View switcher"
msgstr "מחליף תצוגה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.js:0
#, python-format
msgid "Warning"
msgstr "אזהרה"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.benchmark_suite
msgid "Web Benchmarks"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.qunit_mobile_suite
msgid "Web Mobile Tests"
msgstr "בדיקות אתר במכשירים חכמים"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.qunit_suite
msgid "Web Tests"
msgstr "בדיקות אתר"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__website
msgid "Website Link"
msgstr "אתר אינטרנט"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_model.js:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Week"
msgstr "שבוע"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Widget:"
msgstr "יישומון:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Windows/Linux"
msgstr "חלונות / לינוקס"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/special_fields.js:0
#, python-format
msgid "Wrap raw html within an iframe"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Wrong login/password"
msgstr "שם משתמש / סיסמה שגויים"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "XML ID:"
msgstr "מזהה XML:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Year"
msgstr "שנה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Yellow"
msgstr "צהוב"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_bar.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.js:0
#, python-format
msgid "Yes"
msgstr "כן"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Yesterday"
msgstr "אתמול"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "You are not allowed to upload an attachment here."
msgstr "אינך רשאי להעלות כאן קובץ מצורף."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector.js:0
#, python-format
msgid "You cannot follow relations for this field chain construction"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "You cannot leave any password empty."
msgstr "לא ניתן להשאיר סיסמא כלשהי ריקה."

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "You may not believe it,"
msgstr "לא תאמינו, "

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid ""
"You may not believe it,<br />but the application is actually loading..."
msgstr "אולי קשה להאמין,<br />אבל היישום הזה טוען את עצמו כרגע..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#, python-format
msgid ""
"You need to save this new record before editing the translation. Do you want"
" to proceed?"
msgstr "עליך לשמור רשומה חדשה זו לפני עריכת התרגום. האם אתה רוצה להמשיך?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"You need to start Odoo with at least two workers to print a pdf version of "
"the reports."
msgstr ""
"אתה צריך להתחיל את Odoo עם לפחות שני שטחי עבודה כדי להדפיס גרסת PDF של "
"הדוחות."

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"You should upgrade your version of Wkhtmltopdf to at least 0.12.0 in order "
"to get a correct display of headers and footers as well as support for "
"table-breaking between pages."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#: code:addons/web/static/src/public/error_notifications.js:0
#, python-format
msgid "Your Odoo session expired. The current page is about to be refreshed."
msgstr "תם זמן הפעולה של Odoo. העמוד הנוכחי יעבור רענון."

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"Your installation of Wkhtmltopdf seems to be broken. The report will be "
"shown in html."
msgstr "לא ניתן למצוא Wkhtmltopdf במערכת זו. הדוח יוצג ב- html."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/name_and_signature.js:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Your name"
msgstr "השם שלך"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid "[No widget %s]"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "a day ago"
msgstr "לפני יום"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "about a minute ago"
msgstr "לפני כדקה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "about a month ago"
msgstr "לפני כחודש"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "about a year ago"
msgstr "לפני כשנה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "about an hour ago"
msgstr "לפני כשעה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "all records"
msgstr "כל הרשומות"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "are valid for this update."
msgstr "תקפים לעדכון זה."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "as a new"
msgstr "כחדש"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "at:"
msgstr "ב:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "because it's loading..."
msgstr "כי זה נטען..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "but the application is actually loading..."
msgstr "אבל היישום למעשה נטען..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "child of"
msgstr "בן של "

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "contains"
msgstr "מכיל"

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/search_bar/search_bar.js:0
#, python-format
msgid "date"
msgstr "תאריך"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "does not contain"
msgstr "לא מכיל"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "doesn't contain"
msgstr "לא מכיל"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/network/download.js:0
#, python-format
msgid "downloading..."
msgstr "מוריד..."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "e.g. Global Business Solutions"
msgstr "לדוגמה פתרונות עסקיים גלובליים"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "for:"
msgstr "לפי:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "greater than"
msgstr "גדול מ־"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "greater than or equal to"
msgstr "גדול מ- או שווה ל-"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/colorpicker.xml:0
#, python-format
msgid "hex"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "in"
msgstr "ב"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is"
msgstr "הוא"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is after"
msgstr "אחרי"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is after or equal to"
msgstr "אחרי או שווה ל-"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is before"
msgstr "לפני"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is before or equal to"
msgstr "לפני או שווה ל-"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is between"
msgstr "בין"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is equal to"
msgstr "שווה ל"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is false"
msgstr "אינו נכון"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is not"
msgstr "אינו"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "is not ="
msgstr "לא ="

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is not equal to"
msgstr "אינו שווה ל-"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is not set"
msgstr "אינו מוגדר"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is set"
msgstr "נקבעה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is true"
msgstr "נכון"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "it's still loading..."
msgstr "עדיין טוען..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/fields/formatters.js:0
#: code:addons/web/static/src/legacy/js/core/utils.js:0
#, python-format
msgid "kMGTPE"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "less than"
msgstr "פחות מ-"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "less than a minute ago"
msgstr "לפני פחות מדקה"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "less than or equal to"
msgstr "קטן מ- או שווה ל-"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/file_upload_mixin.js:0
#, python-format
msgid "message: %s"
msgstr "הודעה: %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#, python-format
msgid "ms"
msgstr "מילישניות ms"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/commands/default_providers.js:0
#, python-format
msgid "no description provided"
msgstr "לא נרשם תיאור"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "not"
msgstr "לא"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "not in"
msgstr "לא ב"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "not set (false)"
msgstr "לא נבחר (לא נכון)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "of the following rules:"
msgstr "מהכללים הבאים:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "of:"
msgstr "של:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/action_model.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#: code:addons/web/static/src/search/search_model.js:0
#, python-format
msgid "or"
msgstr "או"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "parent of"
msgstr "הורה של"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#, python-format
msgid "props.fields"
msgstr "props.fields"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#, python-format
msgid "query"
msgstr "שאילתא"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "record(s)"
msgstr "רשומות"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "records ?"
msgstr "רשומות ?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "remaining)"
msgstr "נותרו)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#, python-format
msgid "search"
msgstr "חיפוש"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "selected"
msgstr "נבחר"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "selected records,"
msgstr "רשומות שנבחרו,"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "set"
msgstr "מוגדר"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "set (true)"
msgstr "בחירה (נכון)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/effects/effect_service.js:0
#, python-format
msgid "well Done!"
msgstr "כל הכבוד!"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/action_dialog.xml:0
#, python-format
msgid "{\"o_act_window\": actionType === \"ir.actions.act_window\"}"
msgstr "{\"o_act_window\": actionType === \"ir.actions.act_window\"}"
