# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_transfer
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> CHEN <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON> CHEN <<EMAIL>>, 2022\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: payment_transfer
#: model_terms:ir.ui.view,arch_db:payment_transfer.transfer_transaction_status
msgid "<strong>Communication: </strong>"
msgstr "<strong>沟通:</strong>"

#. module: payment_transfer
#: model:ir.model.fields,field_description:payment_transfer.field_payment_acquirer__qr_code
msgid "Enable QR Codes"
msgstr "启用二维码"

#. module: payment_transfer
#: model:ir.model.fields,help:payment_transfer.field_payment_acquirer__qr_code
msgid "Enable the use of QR-codes when paying by wire transfer."
msgstr "在用电汇付款时，启用二维码的使用。"

#. module: payment_transfer
#: code:addons/payment_transfer/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "没有发现与参考文献%s相匹配的交易。"

#. module: payment_transfer
#: model_terms:ir.ui.view,arch_db:payment_transfer.transfer_transaction_status
msgid "Or scan me with your banking app."
msgstr "或者用您的银行应用扫描我。"

#. module: payment_transfer
#: model:ir.model,name:payment_transfer.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "支付收款"

#. module: payment_transfer
#: model:ir.model,name:payment_transfer.model_payment_transaction
msgid "Payment Transaction"
msgstr "付款交易"

#. module: payment_transfer
#: model:ir.model.fields,field_description:payment_transfer.field_payment_acquirer__provider
msgid "Provider"
msgstr "物流商"

#. module: payment_transfer
#: model:ir.model.fields,help:payment_transfer.field_payment_acquirer__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr "与该收单机构一起使用的支付服务提供商"

#. module: payment_transfer
#: code:addons/payment_transfer/models/payment_transaction.py:0
#, python-format
msgid "The customer has selected %(acq_name)s to make the payment."
msgstr "客户选择了%(acq_name)s来进行支付。"

#. module: payment_transfer
#: model:ir.model.fields.selection,name:payment_transfer.selection__payment_acquirer__provider__transfer
msgid "Wire Transfer"
msgstr "电汇"
