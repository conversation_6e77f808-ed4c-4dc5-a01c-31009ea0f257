<?xml version="1.0"?>
<odoo>

    <record id="hr_assessment_annual_report_form" model="ir.ui.view">
        <field name="name">hr.assessment.annual.report.form</field>
        <field name="model">hr.assessment.annual.report</field>
        <field name="arch" type="xml">
            <form string="تقرير المكافئة">
                <group>
                    <group>
                        <field name="year"/>
<!--                        <field name="all_employee"/>-->
                    </group>
                </group>
                <footer>
                    <button name="get_report_action" type="object" string="انشاء"
                            class="btn-primary"/>
                    <button string="الغاء" class="btn-secondary" special="cancel"/>
                </footer>

            </form>
        </field>
    </record>


    <record id="action_assessment_annual_report" model="ir.actions.act_window">
        <field name="name">تقرير التقييم السنوي</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">hr.assessment.annual.report</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="hr_assessment_annual_report_form"/>
        <field name="target">new</field>
    </record>


    <menuitem
            id="menu_assessment_annual_report"
            name="تقرير التقييم السنوي"
            parent="hr_approvales_masarat.menu_masarat_approvale_report"
            groups="hr_approvales_masarat.group_hr_approvales_masarat"
            action="action_assessment_annual_report"
            sequence="7"/>


    <template id="assessment_annual_report_id_all">
        <t t-call="web.html_container">
            <t t-call="web.external_layout">
                <t t-set="index" t-value="0"/>
                <div class="page">
                    <h4 style="text-align: center;">
                        <strong> تقرير التقييم السنوي </strong>
                        <strong><span t-esc="year"/></strong>
                    </h4>
                    <br/>
                    <br/>
                    <br/>

                    <table style="font-size:16px; width: 100%; border-collapse: collapse; border-style: solid; border: 1px solid;">
                        <thead>
                            <tr style="text-align: center; height: 20px; font-weight:bold;">
                                <th style="width: 15%; text-align: center; border: 1px solid;">النسبة الكلية</th>
                                <th style="width: 15%; text-align: center; border: 1px solid;">PREFORMANE</th>
                                <th style="width: 15%; text-align: center; border: 1px solid;">BEHAVIOR</th>
                                <th style="width: 15%; text-align: center; border: 1px solid;">TIME and ATTENDANCE</th>
                                <th style="width: 15%; text-align: center; border: 1px solid;">النصف</th>
                                <th style="width: 15%; text-align: center; border: 1px solid;">اسم الموظف</th>
                                <th style="width: 5%; text-align: center; border: 1px solid;">#</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr t-foreach="employees_dict.keys()" t-as="employee" style="text-align: center;">
                                <tr t-foreach="employees_dict[employee]['assess']" t-as="line" style="text-align: center;">
                                    <td style="border: 1px solid; text-align: center">
                                        <span t-esc="line['final_result']"/>
                                    </td>
                                    <td style="border: 1px solid; text-align: center">
                                        <span t-esc="line['performance_result']"/>
                                    </td>
                                    <td style="border: 1px solid; text-align: center">
                                        <span t-esc="line['behaviors_result']"/>
                                    </td>
                                    <td style="border: 1px solid; text-align: center">
                                        <span t-esc="line['time_attendance_result']"/>
                                    </td>
                                    <td style="border: 1px solid; text-align: center">
                                        <span t-esc="line['month']"/>
                                    </td>
                                    <td style="border: 1px solid; text-align: center">
                                        <span t-esc="employees_dict[employee]['name']"/>
                                    </td>
                                    <td style="border: 1px solid; text-align: center">
                                        <t t-set="index" t-value="index + 1"/>
                                        <span t-esc="index"/>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="border: 1px solid; text-align: center"><strong><span t-esc="employees_dict[employee]['f_final_result']"/></strong></td>
                                    <td style="border: 1px solid; text-align: center"><strong><span t-esc="employees_dict[employee]['f_performance_result']"/></strong></td>
                                    <td style="border: 1px solid; text-align: center"><strong><span t-esc="employees_dict[employee]['f_behaviors_result']"/></strong></td>
                                    <td style="border: 1px solid; text-align: center"><strong><span t-esc="employees_dict[employee]['f_time_attendance_result']"/></strong></td>
                                    <td colspan="3" style="border: 1px solid; text-align: center"><strong>المتوسط</strong></td>
                                </tr>
                                <tr style="height:10px">
                                    <td colspan="7" style="background-color: #F3F3F3">
                                    </td>
                                </tr>
                            </tr>
                        </tbody>
                    </table>
                    <br/>
                </div>
            </t>
        </t>
    </template>

<!--    <template id="reward_report_id_e">-->
<!--        <t t-call="web.html_container">-->
<!--            <t t-call="web.external_layout">-->
<!--                <t t-set="index" t-value="1"/>-->
<!--                <div class="page">-->
<!--                    <h4 style="text-align: center;">-->
<!--                        <strong>تقرير المكافئة </strong>-->
<!--                        <span t-esc="employee_name"/>-->
<!--                    </h4>-->
<!--                    <ul dir="rtl" class="nav" style="display: flex; justify-content: center;">-->
<!--                        <li>-->
<!--                            <h5 style="text-align: center;">للفترة من :-->
<!--                                <span t-esc="start_date"/>-->
<!--                            </h5>-->
<!--                        </li>-->
<!--                        <li>-->
<!--                            <h5 style="text-align: center;">الى :-->
<!--                                <span t-esc="end_date"/>-->
<!--                            </h5>-->
<!--                        </li>-->
<!--                    </ul>-->
<!--                    <br/>-->
<!--                    <br/>-->
<!--                    <table style="font-size:16px; width: 100%; border-collapse: collapse; border-style: solid; border: 1px solid;">-->
<!--                        <thead>-->
<!--                            <tr style="text-align: center; height: 20px; font-weight:bold;">-->
<!--                                <th style="width: 30%; text-align: center; border: 1px solid;">الحالة</th>-->
<!--                                <th style="width: 20%; text-align: center; border: 1px solid; font-size:12px">قيمة المكافأة</th>-->
<!--                                <th style="width: 25%; text-align: center; border: 1px solid;">سبب المكافأة</th>-->
<!--                                <th style="width: 20%; text-align: center; border: 1px solid; font-size:12px">تاريخ المكافأة</th>-->
<!--                                <th style="width: 5%; text-align: center; border: 1px solid; font-size:12px">#</th>-->
<!--                            </tr>-->
<!--                        </thead>-->
<!--                        <tbody>-->
<!--                            <tr t-foreach="employees_dict" t-as="line" style="text-align: center;">-->
<!--                                <td style="border: 1px solid;">-->
<!--                                    <span t-esc="line['state']"/>-->
<!--                                </td>-->
<!--                                <td style="border: 1px solid;">-->
<!--                                    <span t-esc="line['reward_amount']"/>-->
<!--                                </td>-->
<!--                                <td style="border: 1px solid;">-->
<!--                                    <span t-esc="line['reward_reason']"/>-->
<!--                                </td>-->
<!--                                <td style="border: 1px solid;">-->
<!--                                    <span t-esc="line['request_date']"/>-->
<!--                                </td>-->
<!--                                <td style="border: 1px solid;">-->
<!--                                    <span t-esc="index"/>-->
<!--                                    <t t-set="index" t-value="index + 1"/>-->
<!--                                </td>-->
<!--                            </tr>-->
<!--                        </tbody>-->
<!--                    </table>-->
<!--                    <br/>-->
<!--                </div>-->
<!--            </t>-->
<!--        </t>-->
<!--    </template>-->

    <template id="assessment_annual_report_id">
        <t t-if="all_employee">
            <t t-call="hr_approvales_masarat.assessment_annual_report_id_all"></t>
        </t>
        <t t-else="">
            <t t-call="hr_approvales_masarat.reward_report_id_e"></t>
        </t>
    </template>

    <report
            id="assessment_annual_report_x1"
            string="Masarat Annual Assessment Report"
            model="hr.employee"
            report_type="qweb-pdf"
            name="hr_approvales_masarat.assessment_annual_report_id"
            file="hr_approvales_masarat.assessment_annual_report_id"
            menu="False"/>


</odoo>