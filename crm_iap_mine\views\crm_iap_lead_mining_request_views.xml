<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="crm_iap_lead_mining_request_view_form" model="ir.ui.view">
        <field name="name">crm.iap.lead.mining.request.view.form</field>
        <field name="model">crm.iap.lead.mining.request</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_submit" type="object" string="Submit" states="draft" class="oe_highlight" invisible="context.get('is_modal')"/>
                    <button name="action_submit" type="object" string="Retry" states="error" class="oe_highlight" invisible="context.get('is_modal')"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,done" invisible="context.get('is_modal')"/>
                </header>
                <div class="alert alert-danger text-center my-0" role="alert" attrs="{'invisible': [('error_type', '=', False)]}">
                    <field name="error_type" invisible="1"/>
                    <span attrs="{'invisible': [('error_type', '!=', 'credits')]}">
                        <span>You do not have enough credits to submit this request.
                            <button name="action_buy_credits" type="object" class="oe_inline p-0 border-0 align-top text-primary">Buy credits.</button>
                        </span>
                    </span>
                    <span attrs="{'invisible': [('error_type', '!=', 'no_result')]}">Your request did not return any result (no credits were used). Try removing some filters.</span>
                </div>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_get_opportunity_action" class="oe_stat_button" type="object" icon="fa-star" attrs="{'invisible': ['|', ('lead_type', '!=', 'opportunity'), ('state', '!=' , 'done')]}">
                            <div class="o_stat_info">
                                <field name="lead_count"/>
                                <span class="o_stat_text">Opportunities</span>
                            </div>
                        </button>
                        <button name="action_get_lead_action" class="oe_stat_button" type="object" icon="fa-star" groups="crm.group_use_lead" attrs="{'invisible': ['|', ('lead_type', '!=', 'lead'), ('state', '!=' , 'done')]}">
                            <div class="o_stat_info">
                                <field name="lead_count"/>
                                <span class="o_stat_text">Leads</span>
                            </div>
                        </button>
                    </div>
                    <span invisible="context.get('is_modal')">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="oe_title">
                                    <h1>
                                        <field name="name"/>
                                    </h1>
                                </div>
                            </div>
                        </div>
                    </span>

                    <h2>What do you need ?</h2>

                    <group name="requests">
                        <group>
                            <div  class="o_row">
                                <field name="lead_number" attrs="{'readonly': [('state', '=', 'done')]}" nolabel="1" class="oe_inline col-md-2 pl-0"/>
                                <field name="search_type" widget="selection" attrs="{'readonly': [('state', '=', 'done')]}" nolabel="1" class="oe_inline col-md-6"/>
                            </div>
                        </group>
                    </group>

                    <group>
                        <group name="companies">
                            <field name="country_ids" widget="many2many_tags" attrs="{'readonly': [('state', '=', 'done')]}" required="True" options="{'no_create': True, 'no_open': True}"/>
                            <field name="available_state_ids" invisible="1"/>
                            <field name="state_ids" widget="many2many_tags"
                                attrs="{'invisible': ['|', ('country_ids', '=', []), ('available_state_ids', '=', [])], 'readonly': [('state', '=', 'done')]}"
                                domain="[('id', 'in', available_state_ids)]" options="{'no_create': True, 'no_open': True}"/>
                            <field name="industry_ids" widget="many2many_tags" attrs="{'readonly': [('state', '=', 'done')]}" options="{'no_create': True, 'no_open': True}" class="o_industry" required="1"/>
                            <field name="filter_on_size" attrs="{'readonly': [('state', '=', 'done')]}"/>
                            <label for="company_size_min" attrs="{'invisible': [('filter_on_size', '=', False)]}"/>
                            <div attrs="{'invisible': [('filter_on_size', '=', False)]}">
                                From
                                <field name="company_size_min" class="oe_inline col-sm-3" attrs="{'required': [('filter_on_size', '=', True)], 'readonly': [('state', '=', 'done')]}"/>
                                to
                                <field name="company_size_max" class="oe_inline col-sm-3" attrs="{'required': [('filter_on_size', '=', True)], 'readonly': [('state', '=', 'done')]}"/>
                                employees
                            </div>

                        </group>
                        <group name="lead_info">
                            <field name="lead_type" groups="crm.group_use_lead" invisible="context.get('is_modal')" attrs="{'readonly': [('state', '=', 'done')]}"/>
                            <field name="team_id" no_create="1" no_open="1" attrs="{'readonly': [('state', '=', 'done')]}" kanban_view_ref="%(sales_team.crm_team_view_kanban)s"/>
                            <field name="user_id" no_create="1" no_open="1" attrs="{'readonly': [('state', '=', 'done')]}"/>
                            <field name="tag_ids" string="Default Tags" widget="many2many_tags" attrs="{'readonly': [('state', '=', 'done')]}"/>
                        </group>
                    </group>

                    <group name="contacts" attrs="{'invisible': [('search_type', '!=', 'people')]}">
                        <div>
                            <field name="contact_number" attrs="{'readonly': [('state', '=', 'done')], 'required': [('search_type', '=', 'people')]}" nolabel="1" class="col-md-1"/>
                             <span class="col-md-6">Extra contacts per Company</span>
                        </div>
                    </group>
                    <group attrs="{'invisible': [('search_type', '!=', 'people')]}">
                        <group>
                            <field name="contact_filter_type" widget="radio" attrs="{'readonly': [('state', '=', 'done')]}" options="{'horizontal': true}"/>
                            <field name="preferred_role_id" options="{'no_create_edit': True, 'no_quick_create': True}" attrs="{'invisible': [('contact_filter_type','!=', 'role')], 'required': [('search_type', '=', 'people'), ('contact_filter_type', '=', 'role')], 'readonly': [('state', '=', 'done')]}"/>
                            <field name="role_ids" widget="many2many_tags" options="{'color_field': 'color', 'no_create_edit': True, 'no_quick_create': True}" attrs="{'invisible': ['|', ('preferred_role_id','=', False), ('contact_filter_type','!=', 'role')], 'readonly': [('state', '=', 'done')]}"/>
                            <field name="seniority_id" options="{'no_create_edit': True, 'no_quick_create': True}" attrs="{'invisible': [('contact_filter_type', '!=', 'seniority')], 'required': [('search_type', '=', 'people'), ('contact_filter_type', '=', 'seniority')], 'readonly': [('state', '=', 'done')]}"/>
                        </group>
                    </group>
                    <footer>
                        <button string="Generate Leads" name="action_submit" type="object" default_focus="1" class="btn-primary" invisible="not context.get('is_modal')" data-hotkey="q"/>
                        <button string="Cancel" class="btn-secondary" special="cancel" data-hotkey="z" invisible="not context.get('is_modal')"/>
                    </footer>
                </sheet>
            </form>
        </field>
    </record>

    <record id="crm_iap_lead_mining_request_view_tree" model="ir.ui.view">
        <field name="name">crm.iap.lead.mining.request.view.tree</field>
        <field name="model">crm.iap.lead.mining.request</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name" decoration-bf="1"/>
                <field name="display_lead_label" string="Number of Leads"/>
                <field name="search_type"/>
                <field name="country_ids" widget="many2many_tags"/>
                <field name="team_id"/>
                <field name="user_id"/>
                <field name="tag_ids" widget="many2many_tags"/>
                <field name="state" readonly="1" decoration-info="state == 'draft'" decoration-success="state == 'done'" decoration-danger="state == 'error'" widget="badge"/>
            </tree>
        </field>
    </record>

    <record id="crm_iap_lead_mining_request_view_search" model="ir.ui.view">
        <field name="name">crm.iap.lead.mining.request.view.search</field>
        <field name="model">crm.iap.lead.mining.request</field>
        <field name="arch" type="xml">
            <search string="Lead Mining Request">
                <field name="name"/>
                <field name="team_id"/>
                <field name="user_id"/>
                <field name="tag_ids"/>
                <filter name="state_is_draft" string="Draft" domain="[('state', '=', 'draft')]"/>
                <filter name="state_is_done" string="Done" domain="[('state', '=', 'done')]"/>
                <filter name="state_is_error" string="Error" domain="[('state', '=', 'error')]"/>
                <separator/>
                <filter name="type_is_lead" string="Leads" domain="[('lead_type', '=', 'lead')]"/>
                <filter name="type_is_opportunity" string="Opportunities" domain="[('lead_type', '=', 'opportunity')]"/>
                <group expand="0" string="Group By">
                    <filter string="Type" name="groupby_lead_type" domain="[]" context="{'group_by':'lead_type'}"/>
                    <filter string="Sales Team" name="groupby_team_id" domain="[]" context="{'group_by':'team_id'}"/>
                    <filter string="Salesperson" name="groupby_user_id" domain="[]" context="{'group_by':'user_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="crm_iap_lead_mining_request_action" model="ir.actions.act_window">
        <field name="name">Lead Mining Requests</field>
        <field name="res_model">crm.iap.lead.mining.request</field>
        <field name="view_mode">tree,form</field>
    </record>
</odoo>
