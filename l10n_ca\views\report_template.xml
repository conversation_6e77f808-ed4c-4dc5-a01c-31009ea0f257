<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="l10n_ca_external_layout_standard" inherit_id="web.external_layout_standard">
        <xpath expr="//div[hasclass('row')][last()]" position="after">
            <t t-if="company.account_fiscal_country_id.code == 'CA' and company.l10n_ca_pst" class="mt16">
                <div>PST: <span t-field="company.l10n_ca_pst"/></div>
            </t>
        </xpath>
    </template>

    <template id="l10n_ca_external_layout_bold" inherit_id="web.external_layout_bold">
        <xpath expr="//div[hasclass('row')]" position="after">
            <t t-if="company.account_fiscal_country_id.code == 'CA' and company.l10n_ca_pst" class="mt16">
                <div>PST: <span t-field="company.l10n_ca_pst"/></div>
            </t>
        </xpath>
    </template>

    <template id="l10n_ca_external_layout_boxed" inherit_id="web.external_layout_boxed">
        <xpath expr="//div[hasclass('row') and hasclass('mb8')]" position="after">
            <t t-if="company.account_fiscal_country_id.code == 'CA' and company.l10n_ca_pst" class="mt16">
                <div>PST: <span t-field="company.l10n_ca_pst"/></div>
            </t>
        </xpath>
    </template>

    <template id="l10n_ca_external_layout_striped" inherit_id="web.external_layout_striped">
        <xpath expr="//div[hasclass('float-left') and hasclass('company_address')]" position="after">
            <t t-if="company.account_fiscal_country_id.code == 'CA' and company.l10n_ca_pst" class="mt16">
                <div>PST: <span t-field="company.l10n_ca_pst"/></div>
            </t>
        </xpath>
    </template>
</odoo>
