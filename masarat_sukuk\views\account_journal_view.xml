<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="view_account_journal_expense_suke" model="ir.ui.view">
        <field name="name">account.journal.form.extend</field>
        <field name="model">account.journal</field>
        <field name="inherit_id" ref="account.view_account_journal_form"/>
        <field name="arch" type="xml">
            <field name="type" position="after">
                <field name="grant_id" attrs="{'invisible': [('grant_id','=',False)]}"/>
                <field name="suke_id" attrs="{'invisible': [('suke_id','=',False)]}"/>
            </field>
        </field>
    </record>
</odoo>
