# Copyright 2018 <PERSON>
# License LGPL-3.0 or later (http://www.gnu.org/licenses/lgpl.html).

from odoo.tests import common


class TestResUsers(common.TransactionCase):
    def test_chatter_position_wr(self):
        user_public = self.env.ref("base.public_user")
        user_public = user_public.with_user(user_public)

        self.assertEqual(user_public.chatter_position, "sided")
        user_public.write({"chatter_position": "normal"})
        self.assertEqual(user_public.chatter_position, "normal")
