<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="OrderlineDetails" owl="1">
        <li class="orderline" t-on-click="trigger('click-order-line', props.line)" t-att-class="{ selected: props.isSelected }">
            <span class="product-name">
                <t t-esc="productName" />
            </span>
            <span class="price">
                <t t-esc="totalPrice" />
            </span>
            <ul class="info-list">
                <li class="info">
                    <strong>
                        <t t-esc="quantity" />
                    </strong>
                    <span><t t-esc="pricePerUnit" /></span>
                </li>
                <t t-if="customerNote">
                    <li class="info orderline-note">
                        <i class="fa fa-sticky-note" role="img" aria-label="Customer Note" title="Customer Note"/>
                        <t t-esc="customerNote" />
                    </li>
                </t>
                <t t-if="hasRefundedQty()">
                    <li class="info refund-note">
                        <strong>
                            <t t-esc="getFormattedRefundedQty()" />
                        </strong>
                        <span> Refunded</span>
                    </li>
                </t>
                <t t-if="hasToRefundQty()">
                    <li class="info to-refund-highlight refund-note">
                        <t t-set="_destinationOrderUid" t-value="getToRefundDetail().destinationOrderUid"/>
                        <t t-if="_destinationOrderUid">
                            <t t-esc="getRefundingMessage()" />
                            <span t-esc="_destinationOrderUid" class="order-link" t-on-click.stop="trigger('click-refund-order-uid', _destinationOrderUid)" />
                        </t>
                        <t t-else="">
                            <t t-esc="getToRefundMessage()" />
                        </t>
                    </li>
                </t>
            </ul>
        </li>
    </t>

</templates>
