# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* fetchmail
# 
# Translators:
# <PERSON>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: <PERSON>, 2021\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Actions to Perform on Incoming Mails"
msgstr "Handlinger til at udføre på indgående mails"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__active
msgid "Active"
msgstr "Aktiv"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Advanced"
msgstr "Avanceret"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Advanced Options"
msgstr "Avancerede muligheder"

#. module: fetchmail
#: code:addons/fetchmail/models/fetchmail.py:0
#, python-format
msgid ""
"An SSL exception occurred. Check SSL/TLS configuration on server port.\n"
" %s"
msgstr ""
"Der forekom en SSL undtagelse. Tjek SSL/TLS konfiguration på server port.\n"
"   %s"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "Archived"
msgstr "Arkiveret"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__configuration
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Configuration"
msgstr "Konfiguration"

#. module: fetchmail
#: model:ir.model.fields.selection,name:fetchmail.selection__fetchmail_server__state__done
msgid "Confirmed"
msgstr "Bekræftet"

#. module: fetchmail
#: code:addons/fetchmail/models/fetchmail.py:0
#, python-format
msgid "Connection test failed: %s"
msgstr "Test af forbindelse mislykkedes: %s"

#. module: fetchmail
#: model:ir.model.fields,help:fetchmail.field_fetchmail_server__is_ssl
msgid ""
"Connections are encrypted with SSL/TLS through a dedicated port (default: "
"IMAPS=993, POP3S=995)"
msgstr ""
"Forbindelser er krypteret med SSL/TLS via en dedikeret port (standard: "
"IMAPS=993, POP3S=995)"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__object_id
msgid "Create a New Record"
msgstr "Opret et nyt datasæt"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__create_uid
msgid "Created by"
msgstr "Oprettet af"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__create_date
msgid "Created on"
msgstr "Oprettet den"

#. module: fetchmail
#: model:ir.model.fields,help:fetchmail.field_fetchmail_server__priority
msgid "Defines the order of processing, lower values mean higher priority"
msgstr ""
"Definere rangering af behandling, lavere værdier er lig med højere prioritet"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__display_name
msgid "Display Name"
msgstr "Vis navn"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_tree
msgid "Email Count"
msgstr "Email antal"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Fetch Now"
msgstr "Hent nu"

#. module: fetchmail
#: model:ir.model.fields,help:fetchmail.field_fetchmail_server__server
msgid "Hostname or IP of the mail server"
msgstr "Hostnavn eller IP på mail serveren"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__id
msgid "ID"
msgstr "ID"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "IMAP"
msgstr "IMAP"

#. module: fetchmail
#: model:ir.model.fields.selection,name:fetchmail.selection__fetchmail_server__server_type__imap
msgid "IMAP Server"
msgstr "IMAP Server"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "If SSL required."
msgstr "Hvis SSL påkrævet."

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_mail_mail__fetchmail_server_id
msgid "Inbound Mail Server"
msgstr "Indgående Mail Server"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.res_config_settings_view_form
msgid "Incoming Email Servers"
msgstr "Indgående Email Servere"

#. module: fetchmail
#: model:ir.model,name:fetchmail.model_fetchmail_server
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "Incoming Mail Server"
msgstr "Indgående mail server"

#. module: fetchmail
#: model:ir.actions.act_window,name:fetchmail.action_email_server_tree
#: model:ir.ui.menu,name:fetchmail.menu_action_fetchmail_server_tree
msgid "Incoming Mail Servers"
msgstr "Indgående mailservere"

#. module: fetchmail
#: code:addons/fetchmail/models/fetchmail.py:0
#, python-format
msgid ""
"Invalid server name !\n"
" %s"
msgstr ""
"Ugyldigt server navn !\n"
"  %s"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__attach
msgid "Keep Attachments"
msgstr "Behold vedhæftninger"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__original
msgid "Keep Original"
msgstr "Behold original"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__date
msgid "Last Fetch Date"
msgstr "Sidste hent dato"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server____last_update
msgid "Last Modified on"
msgstr "Sidst ændret den"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__write_uid
msgid "Last Updated by"
msgstr "Sidst opdateret af"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__write_date
msgid "Last Updated on"
msgstr "Sidst opdateret den"

#. module: fetchmail
#: model:ir.model.fields.selection,name:fetchmail.selection__fetchmail_server__server_type__local
msgid "Local Server"
msgstr "Lokal Server"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Login Information"
msgstr "Login-information"

#. module: fetchmail
#: model:ir.actions.server,name:fetchmail.ir_cron_mail_gateway_action_ir_actions_server
#: model:ir.cron,cron_name:fetchmail.ir_cron_mail_gateway_action
#: model:ir.cron,name:fetchmail.ir_cron_mail_gateway_action
msgid "Mail: Fetchmail Service"
msgstr "Mail: Fetchmail service"

#. module: fetchmail
#: model:ir.actions.act_window,name:fetchmail.act_server_history
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__message_ids
msgid "Messages"
msgstr "Beskeder"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__name
msgid "Name"
msgstr "Navn"

#. module: fetchmail
#: code:addons/fetchmail/models/fetchmail.py:0
#, python-format
msgid ""
"No response received. Check server information.\n"
" %s"
msgstr ""
"Intet svar modtaget. Tjek server information.\n"
"   %s"

#. module: fetchmail
#: model:ir.model.fields.selection,name:fetchmail.selection__fetchmail_server__state__draft
msgid "Not Confirmed"
msgstr "Ikke bekræftet"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.res_config_settings_view_form
msgid "Outgoing Email Servers"
msgstr "Udgående Email Servere"

#. module: fetchmail
#: model:ir.model,name:fetchmail.model_mail_mail
msgid "Outgoing Mails"
msgstr "Udgående mails"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "POP"
msgstr "POP"

#. module: fetchmail
#: model:ir.model.fields.selection,name:fetchmail.selection__fetchmail_server__server_type__pop
msgid "POP Server"
msgstr "POP Server"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_tree
msgid "POP/IMAP Servers"
msgstr "POP/IMAP Servere"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__password
msgid "Password"
msgstr "Adgangskode"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__port
msgid "Port"
msgstr "Port"

#. module: fetchmail
#: model:ir.model.fields,help:fetchmail.field_fetchmail_server__object_id
msgid ""
"Process each incoming mail as part of a conversation corresponding to this "
"document type. This will create new documents for new conversations, or "
"attach follow-up emails to the existing conversations (documents)."
msgstr ""
"Behandl hver indgående email som del af en samtale svarende til denne "
"dokument type. Dette vil oprette nye dokumenter for nye samtaler, eller "
"vedhæfte opfølgnings emails til eksisterende samtaler (dokumenter)."

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Reset Confirmation"
msgstr "Nulstil godkendelse"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "SSL"
msgstr "SSL"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__is_ssl
msgid "SSL/TLS"
msgstr "SSL / TLS"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__script
msgid "Script"
msgstr "Script"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "Search Incoming Mail Servers"
msgstr "Søg indgående mail servere"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Server & Login"
msgstr "Server & Login"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Server Information"
msgstr "Serverinformation"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__server
msgid "Server Name"
msgstr "Servernavn"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__priority
msgid "Server Priority"
msgstr "Server Prioritet"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__server_type
msgid "Server Type"
msgstr "Servertype"

#. module: fetchmail
#: code:addons/fetchmail/models/fetchmail.py:0
#, python-format
msgid ""
"Server replied with following exception:\n"
" %s"
msgstr ""
"Server svarede med følgende undtagelse:\n"
"   %s"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "Server type IMAP."
msgstr "Server type IMAP."

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "Server type POP."
msgstr "Server type POP."

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__state
msgid "Status"
msgstr "Status"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Test & Confirm"
msgstr "Test & bekræft"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__user
msgid "Username"
msgstr "Brugernavn"

#. module: fetchmail
#: model:ir.model.fields,help:fetchmail.field_fetchmail_server__original
msgid ""
"Whether a full original copy of each email should be kept for reference and "
"attached to each processed message. This will usually double the size of "
"your message database."
msgstr ""
"Hvorvidt en fuldstændig original kopi af hver email skal gemmes som "
"reference og vedhæftes hver behandlet besked. Dette vil typisk fordoble "
"størrelsen på din besked database."

#. module: fetchmail
#: model:ir.model.fields,help:fetchmail.field_fetchmail_server__attach
msgid ""
"Whether attachments should be downloaded. If not enabled, incoming emails "
"will be stripped of any attachments before being processed"
msgstr ""
"Hvorvidt vedhæftninger skal hentes ned. Hvis slået til, vil emails blive "
"frataget vedhæftelser før de behandles"
