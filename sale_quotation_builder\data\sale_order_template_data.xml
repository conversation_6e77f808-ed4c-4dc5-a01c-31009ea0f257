<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- no update so users can freely customize/delete the template -->
    <data noupdate="1">
        <record id="sale_order_template_default" model="sale.order.template">
            <field name="name">Default Template</field>
            <field name="number_of_days">30</field>

            <field name="website_description" type="xml">
                <section data-snippet-id="title" class="mt32">
                    <h2 class="o_page_header">About us</h2>
                </section>
                <section data-snippet-id="text-block">
                    <div class="row">
                        <div class="col-lg-12">
                            <p>
                                This is a <strong>sample quotation template</strong>. You should
                                customize it to fit your own needs from the <i>Sales</i>
                                application, using the menu: Configuration /
                                Quotation Templates.
                            </p><p>
                                Great quotation templates will significantly
                                <strong>boost your success rate</strong>. The
                                first section is usually about your company,
                                your references, your methodology or
                                guarantees, your team, SLA, terms and conditions, etc.
                            </p>
                        </div>
                    </div>
                </section>
                <section data-snippet-id="quality">
                    <div class="card-deck">
                        <div class="card">
                            <div class="card-header">Our Quality</div>
                            <div class="card-body">
                                Product quality is the foundation we
                                stand on; we build it with a relentless
                                focus on fabric, performance and craftsmanship.
                            </div>
                        </div>
                        <div class="card">
                            <div class="card-header">Our Service</div>
                            <div class="card-body">
                                As a leading professional services firm,
                                we know that success is all about the
                                commitment we put on strong services.
                            </div>
                        </div>
                        <div class="card">
                            <div class="card-header">Price</div>
                            <div class="card-body">
                                We always ensure that our products are
                                set at a fair price so that you will be
                                happy to buy them.
                            </div>
                        </div>
                    </div>
                </section>
                <section data-snippet-id="title" class="mt32">
                    <h2 class="o_page_header">Our Offer</h2>
                </section>
                <section data-snippet-id="text-block">
                    <p>
                        You can <strong>set a description per product</strong>. Odoo will
                        automatically create a quotation using the descriptions
                        of all products in the proposal. The table of content
                        on the left is generated automatically using the styles you
                        used in your description (heading 1, heading 2, ...)
                    </p><p>
                        If you edit a quotation from the 'Preview' of a quotation, you will
                        update that quotation only. If you edit the quotation
                        template (from the Configuration menu), all future quotations will
                        use this modified template.
                    </p>
                </section>
            </field>
        </record>

        <function model="res.company" name="_set_default_sale_order_template_id_if_empty"/>
    </data>
</odoo>
