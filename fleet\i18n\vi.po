# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* fleet
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> Nguyen <PERSON> <<EMAIL>>, 2021
# Tr<PERSON><PERSON> <<EMAIL>>, 2021
# Vo Thanh Thuy, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: Vo Thanh Thuy, 2022\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.res_config_settings_view_form
msgid "<span class=\"o_form_label\">End Date Contract Alert</span>"
msgstr "<span class=\"o_form_label\">Cảnh báo ngày kết thúc hợp đồng</span>"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.res_config_settings_view_form
msgid "<span> days before the end date</span>"
msgstr "<span> ngày trước ngày kết thúc</span>"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.res_config_settings_view_form
msgid "<span>Send an alert </span>"
msgstr "<span>Gửi cảnh báo </span>"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "<span>cm</span>"
msgstr "<span>cm</span>"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "<span>g/km</span>"
msgstr "<span>g/km</span>"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "<span>kW</span>"
msgstr "<span>kW</span>"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_1
msgid "A/C Compressor Replacement"
msgstr "Thay thế máy nén khí điều hòa"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_2
msgid "A/C Condenser Replacement"
msgstr "Thay thế bầu ngưng điều hòa"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_3
msgid "A/C Diagnosis"
msgstr "Chẩn đoán điều hòa"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_4
msgid "A/C Evaporator Replacement"
msgstr "Thay thế giàn bay hơi điều hòa"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_5
msgid "A/C Recharge"
msgstr "Nạp lại khí điều hòa"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_needaction
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_needaction
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_needaction
msgid "Action Needed"
msgstr "Cần tác vụ"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Activation Cost"
msgstr "Phí kích hoạt"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__active
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__active
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__active
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__active
msgid "Active"
msgstr "Đang hoạt động"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_ids
msgid "Activities"
msgstr "Các hoạt động"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_exception_decoration
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_exception_decoration
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Thể hiện hoạt động ngoại lệ "

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_state
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_state
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_state
msgid "Activity State"
msgstr "Trạng thái hoạt động"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_type_icon
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_type_icon
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_type_icon
msgid "Activity Type Icon"
msgstr "Biểu tượng kiểu hoạt động"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.mail_activity_type_action_config_fleet
#: model:ir.ui.menu,name:fleet.fleet_menu_config_activity_type
msgid "Activity Types"
msgstr "Kiểu hoạt động"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_tag_action
msgid "Add a new tag"
msgstr "Thêm thẻ mới"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__description
msgid "Add a note about this vehicle"
msgstr "Thêm ghi chú về phương tiện này"

#. module: fleet
#: model:res.groups,name:fleet.fleet_group_manager
msgid "Administrator"
msgstr "Quản trị viên"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_6
msgid "Air Filter Replacement"
msgstr "Thay thế bầu lọc khí"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "All vehicles"
msgstr "Tất cả các phương tiện"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_7
msgid "Alternator Replacement"
msgstr "Thay thế máy phát"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Apply New Driver"
msgstr "Áp dụng tài xế mới"

#. module: fleet
#. openerp-web
#: code:addons/fleet/static/src/js/fleet_form.js:0
#, python-format
msgid "Archive"
msgstr "Lưu trữ"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Archived"
msgstr "Đã lưu"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__next_assignation_date
msgid "Assignment Date"
msgstr "Ngày phân công"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__log_drivers
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_assignation_log_view_list
msgid "Assignment Logs"
msgstr "Nhật ký phân công"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_9
msgid "Assistance"
msgstr "Phụ xe"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_attachment_count
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_attachment_count
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_attachment_count
msgid "Attachment Count"
msgstr "Số tệp đính kèm"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_kanban
msgid "Attention: renewal overdue"
msgstr "Chú ý: quá hạn gia hạn"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__transmission__automatic
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__transmission__automatic
msgid "Automatic"
msgstr "Tự động"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Available"
msgstr "Khả dụng"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_8
msgid "Ball Joint Replacement"
msgstr "Thay thế khớp cầu"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_9
msgid "Battery Inspection"
msgstr "Kiểm tra ắc quy"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_10
msgid "Battery Replacement"
msgstr "Thay thế ắc quy"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_cost_report__vehicle_type__bike
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__vehicle_type__bike
msgid "Bike"
msgstr "Xe đạp"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Bikes"
msgstr "Xe đạp"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_11
msgid "Brake Caliper Replacement"
msgstr "Thay thế thước kẹp hãm (phanh)"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_12
msgid "Brake Inspection"
msgstr "Kiểm tra phanh"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_13
msgid "Brake Pad(s) Replacement"
msgstr "Thay thế đĩa phanh"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__brand_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Brand"
msgstr "Nhãn hiệu"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_model_brand
msgid "Brand of the vehicle"
msgstr "Nhãn hiệu của phương tiện"

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_break
msgid "Break"
msgstr "Nghỉ"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__cng
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__cng
msgid "CNG"
msgstr "CNG"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__co2
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__default_co2
msgid "CO2 Emissions"
msgstr "Lượng khí thải CO2"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_tree
msgid "CO2 Emissions g/km"
msgstr "Lượng khí thải CO2 g/km"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__co2
msgid "CO2 emissions of the vehicle"
msgstr "Lượng khí thải CO2 của phương tiện"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_1
msgid "Calculation Benefit In Kind"
msgstr "Tính lợi ích bằng hiện vật"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Cancel"
msgstr "Hủy"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_services__state__cancelled
msgid "Cancelled"
msgstr "Đã hủy"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_cost_report__vehicle_type__car
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__vehicle_type__car
msgid "Car"
msgstr "Ô tô"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_14
msgid "Car Wash"
msgstr "Rửa xe"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Cars"
msgstr "Ô tô"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__car_value
msgid "Catalog Value (VAT Incl.)"
msgstr "Giá trị (bao gồm VAT)"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_15
msgid "Catalytic Converter Replacement"
msgstr "Thay thế bộ chuyển đổi khí thải"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__category
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__category_id
msgid "Category"
msgstr "Danh mục"

#. module: fleet
#: model:ir.model.constraint,message:fleet.constraint_fleet_vehicle_model_category_name_uniq
msgid "Category name must be unique"
msgstr "Tên danh mục phải là duy nhất"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_model_category
msgid "Category of the model"
msgstr "Danh mục dòng xe"

#. module: fleet
#: model:mail.message.subtype,description:fleet.mt_fleet_driver_updated
#: model:mail.message.subtype,name:fleet.mt_fleet_driver_updated
msgid "Changed Driver"
msgstr "Đã thay đổi tài xế"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_16
msgid "Charging System Diagnosis"
msgstr "Chẩn đoán hệ thống sạc"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__vin_sn
msgid "Chassis Number"
msgstr "Số khung"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__state
msgid "Choose whether the contract is still valid or not"
msgstr "Chọn xem hợp đồng có còn hiệu lực hay không"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_service_type__category
msgid ""
"Choose whether the service refer to contracts, vehicle services or both"
msgstr "Chọn xem dịch vụ có liên quan đến hợp đồng, dịch vụ xe hay cả hai"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Close Contract"
msgstr "Đóng hợp đồng"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__contract_state__closed
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__state__closed
msgid "Closed"
msgstr "Đã đóng"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__co2_standard
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__co2_standard
msgid "Co2 Standard"
msgstr "Co2 tiêu chuẩn"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__color
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__color
msgid "Color"
msgstr "Màu sắc"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__color
msgid "Color Index"
msgstr "Mã màu"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__color
msgid "Color of the vehicle"
msgstr "Màu sắc phương tiện"

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_compact
msgid "Compact"
msgstr "Loại nhỏ"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__company_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__company_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__company_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__company_id
msgid "Company"
msgstr "Công ty"

#. module: fleet
#: model:ir.model,name:fleet.model_res_config_settings
msgid "Config Settings"
msgstr "Cài đặt cấu hình"

#. module: fleet
#: model:ir.ui.menu,name:fleet.fleet_configuration
msgid "Configuration"
msgstr "Cấu hình"

#. module: fleet
#: model:ir.model,name:fleet.model_res_partner
msgid "Contact"
msgstr "Liên hệ"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_search
msgid "Contains Vehicles"
msgstr "Chứa phương tiện"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_service_type__category__contract
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_cost_report__cost_type__contract
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Contract"
msgstr "Hợp đồng"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_graph
msgid "Contract Costs Per Month"
msgstr "Chi phí hợp đồng hàng tháng"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__contract_count
msgid "Contract Count"
msgstr "Số lượng hợp đồng"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__expiration_date
msgid "Contract Expiration Date"
msgstr "Ngày hết hạn hợp đồng"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Contract Information"
msgstr "Thông tin hợp đồng"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__start_date
msgid "Contract Start Date"
msgstr "Ngày bắt đầu hợp đồng"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_tree
msgid "Contract logs"
msgstr "Nhật ký hợp đồng"

#. module: fleet
#: model:mail.activity.type,name:fleet.mail_act_fleet_contract_to_renew
msgid "Contract to Renew"
msgstr "Hợp đồng cần gia hạn"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_kanban
msgid "Contract(s)"
msgstr "Hợp đồng"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_log_contract_action
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__log_contracts
#: model:ir.ui.menu,name:fleet.fleet_vehicle_log_contract_menu
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Contracts"
msgstr "Hợp đồng"

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_convertible
msgid "Convertible"
msgstr "Mui trần"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__cost
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__amount
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__amount
msgid "Cost"
msgstr "Chi phí"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__cost_type
msgid "Cost Type"
msgstr "Loại chi phí"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Cost that is paid only once at the creation of the contract"
msgstr "Loại chi phí được thanh toán một lần duy nhất khi tạo hợp đồng"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__cost_subtype_id
msgid "Cost type purchased with this cost"
msgstr "Kiểu chi phí được mua bằng chi phí này"

#. module: fleet
#: model:ir.ui.menu,name:fleet.menu_fleet_reporting_costs
msgid "Costs"
msgstr "Chi phí"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_costs_reporting_action
msgid "Costs Analysis"
msgstr "Phân tích chi phí"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__country_id
msgid "Country"
msgstr "Quốc gia"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__country_code
msgid "Country Code"
msgstr "Mã quốc gia"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_model_category_action
msgid "Create a new category"
msgstr "Tạo một danh mục mới"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_contract_action
msgid "Create a new contract"
msgstr "Tạo hợp đồng mới"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_model_brand_action
msgid "Create a new manufacturer"
msgstr "Tạo nhà sản xuất mới"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_model_action
msgid "Create a new model"
msgstr "Tạo phương tiện mới"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_odometer_action
msgid "Create a new odometer log"
msgstr "Tạo nhật ký công tơ mét mới"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_services_action
msgid "Create a new service entry"
msgstr "Tạo mục dịch vụ mới "

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_service_types_action
msgid "Create a new type of service"
msgstr "Tạo loại dịch vụ mới"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_state_action
msgid "Create a new vehicle status"
msgstr "Tạo trạng thái phương tiện mới"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__create_uid
msgid "Created by"
msgstr "Tạo bởi"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__create_date
msgid "Created on"
msgstr "Thời điểm tạo"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__currency_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__currency_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__currency_id
msgid "Currency"
msgstr "Tiền tệ"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Current Driver"
msgstr "Tài xế hiện tại"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__state_id
msgid "Current state of the vehicle"
msgstr "Trạng thái hiện tại của phương tiện"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__cost_frequency__daily
msgid "Daily"
msgstr "Hàng ngày"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__date_start
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__date
msgid "Date"
msgstr "Ngày"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__date
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__date
msgid "Date when the cost has been executed"
msgstr "Ngày thực hiện chi phí"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__start_date
msgid "Date when the coverage of the contract begins"
msgstr "Ngày bắt đầu phạm vi bao quát của hợp đồng"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__expiration_date
msgid ""
"Date when the coverage of the contract expirates (by default, one year after"
" begin date)"
msgstr ""
"Ngày hết hạn phạm vi bao quát của hợp đồng (mặc định, một năm sau ngày bắt "
"đầu)"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__acquisition_date
msgid "Date when the vehicle has been immatriculated"
msgstr "Ngày phương tiện đã được đăng kiểm"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_res_config_settings__delay_alert_contract
msgid "Delay alert contract outdated"
msgstr "Hoãn cảnh báo hợp đồng quá hạn"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_2
msgid "Depreciation and Interests"
msgstr "Khấu hao & Lợi nhuận"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__description
msgid "Description"
msgstr "Mô tả"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__frame_type__diamant
msgid "Diamant"
msgstr "Diamant"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__diesel
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__diesel
msgid "Diesel"
msgstr "Dầu diesel"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_services__state__done
msgid "Done"
msgstr "Hoàn thành"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_17
msgid "Door Window Motor/Regulator Replacement"
msgstr "Thay thế mô-tơ/bộ vận hành cửa sổ"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__doors
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__doors
msgid "Doors Number"
msgstr "Số cửa"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_downgraded
msgid "Downgraded"
msgstr "Xuống cấp"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__driver_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__driver_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__driver_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__purchaser_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__purchaser_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__driver_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Driver"
msgstr "Tài xế"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__driver_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__purchaser_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_odometer__driver_id
msgid "Driver address of the vehicle"
msgstr "Địa chỉ tài xế của phương tiện"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Drivers"
msgstr "Lái xe"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Drivers History"
msgstr "Lịch sử tài xế"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__history_count
msgid "Drivers History Count"
msgstr "Số lịch sử tài xế"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_assignation_log
msgid "Drivers history on a vehicle"
msgstr "Lịch sử tài xế của một phương tiện"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_kanban
msgid "Dropdown menu"
msgstr "Menu thả xuống"

#. module: fleet
#. openerp-web
#: code:addons/fleet/static/src/js/fleet_form.js:0
#, python-format
msgid ""
"Each Services and contracts of this vehicle will be considered as Archived. "
"Are you sure that you want to archive this record?"
msgstr ""
"Tất cả dịch vụ và hợp đồng của phương tiện này sẽ được coi là Lưu trữ. Bạn "
"có chắc muốn lưu trữ hồ sơ này không?"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_contract_action
msgid ""
"Each contract (e.g.: leasing) may include several services\n"
"            (reparation, insurances, periodic maintenance)."
msgstr ""
"Mỗi hợp đồng (VD: cho thuê) có thể bao gồm một vài dịch vụ\n"
"            (bồi thường, bảo hiểm, bảo trì định kỳ)."

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_service_types_action
msgid "Each service can used in contracts, as a standalone service or both."
msgstr ""
"Mỗi dịch vụ có thể được sử dụng trong hợp đồng, như một dịch vụ độc lập hoặc"
" cả hai."

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__electric
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__electric
msgid "Electric"
msgstr "Điện"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__electric_assistance
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__electric_assistance
msgid "Electric Assistance"
msgstr "Hỗ trợ điện"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_17
msgid "Emissions"
msgstr "Khí xả"

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_leasing
msgid "Employee Car"
msgstr "Xe nhân viên"

#. module: fleet
#: code:addons/fleet/models/fleet_vehicle_log_services.py:0
#, python-format
msgid "Emptying the odometer value of a vehicle is not allowed."
msgstr "Không được phép xóa giá trị đồng hồ công tơ mét của phương tiện."

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__date_end
msgid "End Date"
msgstr "Ngày kết thúc"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Engine"
msgstr "Động cơ"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_18
msgid "Engine Belt Inspection"
msgstr "Kiểm tra dây curoa động cơ"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_19
msgid "Engine Coolant Replacement"
msgstr "Thay HT làm mát máy"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_20
msgid "Engine/Drive Belt(s) Replacement"
msgstr "Thay thế dây curoa máy"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_13
msgid "Entry into service tax"
msgstr "Nhập thuế dịch vụ"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_21
msgid "Exhaust Manifold Replacement"
msgstr "Thay thế ống xả"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__contract_state__expired
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__state__expired
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
msgid "Expired"
msgstr "Đã hết hạn"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__first_contract_date
msgid "First Contract Date"
msgstr "Ngày hợp đồng đầu tiên"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Fiscality"
msgstr ""

#. module: fleet
#: model:ir.module.category,name:fleet.module_fleet_category
#: model:ir.ui.menu,name:fleet.fleet_vehicle_menu
#: model:ir.ui.menu,name:fleet.fleet_vehicles
#: model:ir.ui.menu,name:fleet.menu_root
#: model_terms:ir.ui.view,arch_db:fleet.res_config_settings_view_form
msgid "Fleet"
msgstr "Đội xe"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_cost_report
msgid "Fleet Analysis Report"
msgstr "Báo cáo phân tích đội xe"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_graph
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vechicle_costs_report_view_tree
msgid "Fleet Costs Analysis"
msgstr "Phân tích chi phí đội xe"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.res_config_settings_view_form
msgid "Fleet Management"
msgstr "Quản lý đội xe"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__manager_id
msgid "Fleet Manager"
msgstr "Người quản lý đội xe"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_service_type
msgid "Fleet Service Type"
msgstr "Loại dịch vụ đội xe"

#. module: fleet
#: model:ir.actions.server,name:fleet.ir_cron_contract_costs_generator_ir_actions_server
#: model:ir.cron,cron_name:fleet.ir_cron_contract_costs_generator
#: model:ir.cron,name:fleet.ir_cron_contract_costs_generator
msgid "Fleet: Generate contracts costs based on costs frequency"
msgstr "Đội xe: Tạo chi phí hợp đồng dựa trên tần suất chi phí"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_follower_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_follower_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_follower_ids
msgid "Followers"
msgstr "Người theo dõi"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_partner_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_partner_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_partner_ids
msgid "Followers (Partners)"
msgstr "Người theo dõi (Đối tác)"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__activity_type_icon
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__activity_type_icon
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Biểu tượng Font awesome v.d: fa-tasks"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__frame_size
msgid "Frame Size"
msgstr "Cỡ khung"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__frame_type
msgid "Frame Type"
msgstr "Kiểu khung"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__frame_type
msgid "Frame type of the bike"
msgstr "Kiểu khung xe đạp"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__cost_frequency
msgid "Frequency of the recuring cost"
msgstr "Tần suất của chi phí định kỳ này"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__fuel_type
msgid "Fuel"
msgstr "Nhiên liệu"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_22
msgid "Fuel Injector Replacement"
msgstr "Thay thế bộ phun nhiên liệu"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_23
msgid "Fuel Pump Replacement"
msgstr "Thay thế bơm nhiên liệu"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__fuel_type
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__default_fuel_type
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Fuel Type"
msgstr "Loại nhiên liệu"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__fuel_type
msgid "Fuel Used by the vehicle"
msgstr "Nhiên liệu được sử dụng bởi phương tiện"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Future Activities"
msgstr "Hoạt động tương lai"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__future_driver_id
msgid "Future Driver"
msgstr "Tài xế sắp tới"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_kanban
msgid "Future Driver :"
msgstr "Tài xế sắp tới :"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__gasoline
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__gasoline
msgid "Gasoline"
msgstr "Xăng"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Group By"
msgstr "Nhóm theo"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__contract_renewal_overdue
msgid "Has Contracts Overdue"
msgstr "Có hợp đồng quá hạn"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__contract_renewal_due_soon
msgid "Has Contracts to renew"
msgstr "Có hợp đồng cần gia hạn"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__has_message
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__has_message
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__has_message
msgid "Has Message"
msgstr "Có tin nhắn"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_24
msgid "Head Gasket(s) Replacement"
msgstr "Thay thế gioăng nắp máy"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_25
msgid "Heater Blower Motor Replacement"
msgstr "Thay thế động cơ quạt sấy"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_26
msgid "Heater Control Valve Replacement"
msgstr "Thay thế van điều khiển sấy"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_27
msgid "Heater Core Replacement"
msgstr "Thay thế giàn nóng"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_28
msgid "Heater Hose Replacement"
msgstr "Thay ống dẫn khí nóng"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__horsepower
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__horsepower
msgid "Horsepower"
msgstr "Mã lực"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__horsepower_tax
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__horsepower_tax
msgid "Horsepower Taxation"
msgstr "Thuế theo mã lực"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__hybrid
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__hybrid
msgid "Hybrid Diesel"
msgstr "Hybrid Diesel"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__full_hybrid_gasoline
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__full_hybrid_gasoline
msgid "Hybrid Gasoline"
msgstr "Hybrid Gasoline"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__hydrogen
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__hydrogen
msgid "Hydrogen"
msgstr "Hydrogen"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__id
msgid "ID"
msgstr "ID"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_exception_icon
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_exception_icon
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_exception_icon
msgid "Icon"
msgstr "Biểu tượng"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__activity_exception_icon
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__activity_exception_icon
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Biểu tượng để chỉ ra một hoạt động ngoại lệ."

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__message_needaction
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__message_unread
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__message_needaction
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__message_unread
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__message_needaction
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__message_unread
msgid "If checked, new messages require your attention."
msgstr "Nếu chọn, các tin nhắn mới yêu cầu sự chú ý của bạn. "

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__message_has_error
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__message_has_error
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Nếu đánh dấu, một số tin nhắn bị lỗi khi gửi. "

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_29
msgid "Ignition Coil Replacement"
msgstr "Thay thế bôbin đánh lửa"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__acquisition_date
msgid "Immatriculation Date"
msgstr "Ngày đăng kiểm"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__contract_state__open
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__state__open
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
msgid "In Progress"
msgstr "Đang tiến hành"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__service_ids
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Included Services"
msgstr "Các dịch vụ bao gồm"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__contract_state__futur
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__state__futur
msgid "Incoming"
msgstr "Sắp tới"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
msgid "Information"
msgstr "Thông tin"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_30
msgid "Intake Manifold Gasket Replacement"
msgstr "Thay thế gioăng cổ góp ống hút"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Invoice Date"
msgstr "Ngày hóa đơn"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_is_follower
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_is_follower
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_is_follower
msgid "Is Follower"
msgstr "Là người theo dõi"

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_junior
msgid "Junior"
msgstr "Cấp dưới"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_kanban
msgid "Km"
msgstr "Km"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__lpg
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__lpg
msgid "LPG"
msgstr "LPG"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__contract_state
msgid "Last Contract State"
msgstr "Trạng thái hợp đồng cuối cùng"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state____last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag____last_update
msgid "Last Modified on"
msgstr "Sửa lần cuối vào"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__odometer
msgid "Last Odometer"
msgstr "Chỉ số công tơ mét cuối"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Late Activities"
msgstr "Hoạt động trễ"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_contract_leasing
msgid "Leasing"
msgstr "Cho thuê"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_action
msgid "Let's create your first vehicle."
msgstr "Hãy tạo phương tiện đầu tiên."

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__license_plate
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "License Plate"
msgstr "Biển số xe"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__license_plate
msgid "License plate number of the vehicle (i = plate number for a car)"
msgstr "Biển số của phương tiện (i = biển số ô tô)"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__location
msgid "Location"
msgstr "Địa điểm"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__location
msgid "Location of the vehicle (garage, ...)"
msgstr "Vị trí của phương tiện (ga-ra, v.v)"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__image_128
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__image_128
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__image_128
msgid "Logo"
msgstr "Logo"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_kanban
msgid "MODELS"
msgstr "MẪU XE"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_main_attachment_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_main_attachment_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_main_attachment_id
msgid "Main Attachment"
msgstr "Tệp đính kèm chính"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__name
msgid "Make"
msgstr "Nhãn hiệu"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_contract_action
msgid ""
"Manage all your contracts (leasing, insurances, etc.) with\n"
"            their related services, costs. Odoo will automatically warn\n"
"            you when some contracts have to be renewed."
msgstr ""
"Quản lý tất cả hợp đồng (cho thuê, bảo hiểm, v.v) và\n"
"            các dịch vụ, chi phí liên quan. Odoo sẽ tự động cảnh báo\n"
"            bạn khi hợp đồng cần gia hạn."

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_costs_reporting_action
msgid "Manage efficiently your different effective vehicles Costs with Odoo."
msgstr "Quản lý hiệu quả chi phí các phương tiện khác nhau với Odoo."

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_11
msgid "Management Fee"
msgstr "Phí quản lý"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__transmission__manual
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__transmission__manual
msgid "Manual"
msgstr "Thủ công"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__brand_id
msgid "Manufacturer"
msgstr "Nhà sản xuất"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__brand_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_model__brand_id
msgid "Manufacturer of the vehicle"
msgstr "Nhà sản xuất phương tiện"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_model_brand_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_model_brand_menu
msgid "Manufacturers"
msgstr "Nhà sản xuất"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_has_error
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_has_error
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_has_error
msgid "Message Delivery error"
msgstr "Gửi tin nhắn bị lỗi"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_ids
msgid "Messages"
msgstr "Tin nhắn"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__model_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__model_ids
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Model"
msgstr "Dòng xe"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_model_category_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_model_category_menu
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_category_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_category_view_tree
msgid "Model Category"
msgstr "Danh mục dòng xe"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand__model_count
msgid "Model Count"
msgstr "Số dòng xe"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_tree
msgid "Model Make"
msgstr "Nhãn hiệu dòng xe"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__model_year
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__model_year
msgid "Model Year"
msgstr "Đời xe"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__name
msgid "Model name"
msgstr "Tên dòng xe"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_model
msgid "Model of a vehicle"
msgstr "Mẫu của phương tiện"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__model_id
msgid "Model of the vehicle"
msgstr "Mẫu của phương tiện"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_kanban
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_tree
msgid "Models"
msgstr "Mẫu"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__cost_frequency__monthly
msgid "Monthly"
msgstr "Hàng tháng"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__my_activity_date_deadline
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__my_activity_date_deadline
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Hạn chót hoạt động"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__name
msgid "Name"
msgstr "Tên"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__contract_renewal_name
msgid "Name of contract to renew soon"
msgstr "Tên của hợp đồng sắp cần gia hạn"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Need Action"
msgstr "Cần xử lý"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_services__state__new
msgid "New"
msgstr "Mới"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_new_request
msgid "New Request"
msgstr "Yêu cầu mới"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_calendar_event_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_calendar_event_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Sự kiện lịch hoạt động kế tiếp"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_date_deadline
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_date_deadline
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Hạn chót cho hành động kế tiếp"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_summary
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_summary
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_summary
msgid "Next Activity Summary"
msgstr "Tóm tắt hoạt động kế tiếp"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_type_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_type_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_type_id
msgid "Next Activity Type"
msgstr "Kiểu hoạt động kế tiếp"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__future_driver_id
msgid "Next Driver Address of the vehicle"
msgstr "Địa chỉ tài xế kế tiếp của phương tiện"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__cost_frequency__no
msgid "No"
msgstr "Không"

#. module: fleet
#: code:addons/fleet/models/fleet_vehicle.py:0
#, python-format
msgid "No Plate"
msgstr "Không biển số"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_costs_reporting_action
msgid "No data for analysis"
msgstr "Không có dữ liệu để phân tích"

#. module: fleet
#: code:addons/fleet/models/fleet_vehicle.py:0
#, python-format
msgid "No plate"
msgstr "Không biển số"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Note"
msgstr "Ghi chú"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__notes
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
msgid "Notes"
msgstr "Ghi chú"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_needaction_counter
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_needaction_counter
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_needaction_counter
msgid "Number of Actions"
msgstr "Số lượng hành động"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__doors
msgid "Number of doors of the vehicle"
msgstr "Số cửa của phương tiện"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_has_error_counter
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_has_error_counter
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_has_error_counter
msgid "Number of errors"
msgstr "Số lỗi"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__message_needaction_counter
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__message_needaction_counter
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Số tin nhắn cần xử lý"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__message_has_error_counter
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__message_has_error_counter
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Số tin nhắn gửi đi bị lỗi"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__seats
msgid "Number of seats of the vehicle"
msgstr "Số chỗ ngồi của phương tiện"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__message_unread_counter
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__message_unread_counter
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__message_unread_counter
msgid "Number of unread messages"
msgstr "Số tin nhắn chưa đọc"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__odometer_count
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__odometer_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Odometer"
msgstr "Công tơ mét"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_tree
msgid "Odometer Logs"
msgstr "Nhật ký công tơ mét"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__odometer_unit
msgid "Odometer Unit"
msgstr "Đơn vị công tơ mét"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__odometer
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__value
msgid "Odometer Value"
msgstr "Chỉ số công tơ mét"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_graph
msgid "Odometer Values Per Vehicle"
msgstr "Chỉ số công tơ mét trên mỗi phương tiện"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_odometer
msgid "Odometer log for a vehicle"
msgstr "Nhật ký công tơ mét cho phương tiện"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__odometer
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__odometer
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__odometer_id
msgid "Odometer measure of the vehicle at the moment of this log"
msgstr "Chỉ số công tơ mét của phương tiện tại thời điểm ghi nhật ký này"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_odometer_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_odometer_menu
msgid "Odometers"
msgstr "Công tơ mét"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_31
msgid "Oil Change"
msgstr "Thay dầu"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_32
msgid "Oil Pump Replacement"
msgstr "Thay thế bơm dầu"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_contract_omnium
msgid "Omnium"
msgstr "Omnium"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_16
msgid "Options"
msgstr "Tùy chọn"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_ordered
msgid "Ordered"
msgstr "Đã đặt"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_33
msgid "Other Maintenance"
msgstr "Bảo trì khác"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_34
msgid "Oxygen Sensor Replacement"
msgstr "Thay cảm biến ôxi"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__plan_to_change_bike
#: model:ir.model.fields,field_description:fleet.field_res_partner__plan_to_change_bike
#: model:ir.model.fields,field_description:fleet.field_res_users__plan_to_change_bike
msgid "Plan To Change Bike"
msgstr "Kế hoạch thay xe đạp"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__plan_to_change_car
#: model:ir.model.fields,field_description:fleet.field_res_partner__plan_to_change_car
#: model:ir.model.fields,field_description:fleet.field_res_users__plan_to_change_car
msgid "Plan To Change Car"
msgstr "Kế hoạch đổi xe"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Planned for Change"
msgstr "Đã có kế hoạch thay đổi"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__plug_in_hybrid_diesel
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__plug_in_hybrid_diesel
msgid "Plug-in Hybrid Diesel"
msgstr "Plug-in Hybrid Diesel"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__fuel_type__plug_in_hybrid_gasoline
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_model__default_fuel_type__plug_in_hybrid_gasoline
msgid "Plug-in Hybrid Gasoline"
msgstr "Plug-in Hybrid Gasoline"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__power
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__power
msgid "Power"
msgstr "Công suất"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_35
msgid "Power Steering Hose Replacement"
msgstr "Thay ống thủy lực trợ lái"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_36
msgid "Power Steering Pump Replacement"
msgstr "Thay thế bơm trợ lái"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__power
msgid "Power in kW of the vehicle"
msgstr "Công suất kW của phương tiện"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_kanban
msgid "Properties"
msgstr "Thuộc tính"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__net_car_value
msgid "Purchase Value"
msgstr "Giá trị mua"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__net_car_value
msgid "Purchase value of the vehicle"
msgstr "Giá trị mua phương tiện"

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_purchased
msgid "Purchased"
msgstr "Đã mua"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_37
msgid "Radiator Repair"
msgstr "Sửa chữa lò sưởi"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_action
msgid "Ready to manage your fleet more efficiently ?"
msgstr "Bạn đã sẵn sàng quản lý đội xe hiệu quả hơn chưa?"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__cost_generated
msgid "Recurring Cost"
msgstr "Chi phí định kỳ"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__cost_frequency
msgid "Recurring Cost Frequency"
msgstr "Tần suất chi phí định kỳ"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__ins_ref
msgid "Reference"
msgstr "Mã tham chiếu"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_refueling
msgid "Refueling"
msgstr "Đổ nhiên liệu"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_registered
msgid "Registered"
msgstr "Đã đăng ký"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_12
msgid "Rent (Excluding VAT)"
msgstr "Thuê (Không gồm VAT)"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_8
msgid "Repair and maintenance"
msgstr "Sửa chữa hoặc bảo trì"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_contract_repairing
msgid "Repairing"
msgstr "Đang sửa chữa"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_10
msgid "Replacement Vehicle"
msgstr "Phương tiện thay thế"

#. module: fleet
#: model:ir.ui.menu,name:fleet.menu_fleet_reporting
msgid "Reporting"
msgstr "Báo cáo"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_reserve
msgid "Reserve"
msgstr "Đặt trước"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Reset To Draft"
msgstr "Đặt lại thành nháp"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__residual_value
msgid "Residual Value"
msgstr "Giá trị còn lại"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_15
msgid "Residual value (Excluding VAT)"
msgstr "Giá trị còn lại (Không gồm VAT)"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_19
msgid "Residual value in %"
msgstr "Giá trị còn lại theo %"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__user_id
msgid "Responsible"
msgstr "Phụ trách"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__activity_user_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__activity_user_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__activity_user_id
msgid "Responsible User"
msgstr "Người phụ trách"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_38
msgid "Resurface Rotors"
msgstr "Làm lại mặt rô-to"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_39
msgid "Rotate Tires"
msgstr "Quay vòng lốp"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_40
msgid "Rotor Replacement"
msgstr "Thay Rô-to"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_services__state__running
msgid "Running"
msgstr "Đang chạy"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__seats
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__seats
msgid "Seats Number"
msgstr "Số ghế"

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_sedan
msgid "Sedan"
msgstr "Sedan"

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_senior
msgid "Senior"
msgstr "Cấp trên"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_category__sequence
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state__sequence
msgid "Sequence"
msgstr "Trình tự"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_service_type__category__service
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_cost_report__cost_type__service
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_search
msgid "Service"
msgstr "Dịch vụ"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__service_type_id
msgid "Service Type"
msgstr "Loại dịch vụ"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_service_types_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_service_types_menu
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_service_types_view_tree
msgid "Service Types"
msgstr "Loại dịch vụ"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_log_services_action
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__service_count
#: model:ir.ui.menu,name:fleet.fleet_vehicle_log_services_menu
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Services"
msgstr "Dịch vụ"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_graph
msgid "Services Costs Per Month"
msgstr "Phí dịch vụ hàng tháng"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__log_services
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_tree
msgid "Services Logs"
msgstr "Nhật ký các dịch vụ"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_log_services
msgid "Services for vehicles"
msgstr "Dịch vụ cho phương tiện"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_config_settings_action
#: model:ir.ui.menu,name:fleet.fleet_config_settings_menu
msgid "Settings"
msgstr "Cài đặt "

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Show all records which has next action date is before today"
msgstr "Hiển thị tất cả hồ sơ có ngày tác vụ kế tiếp trước hôm nay"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_6
msgid "Snow tires"
msgstr "Lốp chạy trên tuyết"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_41
msgid "Spark Plug Replacement"
msgstr "Thay thế Bu-gi"

#. module: fleet
#: code:addons/fleet/models/fleet_vehicle.py:0
#, python-format
msgid "Specify the End date of %s"
msgstr "Xác định ngày kết thúc của %s"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__state
msgid "Stage"
msgstr "Giai đoạn"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Start Contract"
msgstr "Bắt đầu hợp đồng"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__date_start
msgid "Start Date"
msgstr "Ngày bắt đầu"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_42
msgid "Starter Replacement"
msgstr "Thay thế bộ khởi động"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__state_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_state_view_tree
msgid "State"
msgstr "Trạng thái"

#. module: fleet
#: model:ir.model.constraint,message:fleet.constraint_fleet_vehicle_state_fleet_state_name_unique
msgid "State name already exists"
msgstr "Tên trạng thái đã tồn tại"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__state
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Status"
msgstr "Trạng thái"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__activity_state
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__activity_state
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Trạng thái dựa trên hoạt động\n"
"Quá hạn: Đã quá hạn chót\n"
"Hôm nay: Hôm nay là ngày hoạt động\n"
"Kế hoạch: Các hoạt động trong tương lai."

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vechicle_costs_report_view_tree
msgid "Sum of Cost"
msgstr "Tổng cộng chi phí"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_5
#: model:fleet.service.type,name:fleet.type_service_service_7
msgid "Summer tires"
msgstr "Lốp mùa hè"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag__name
msgid "Tag Name"
msgstr "Tên thẻ"

#. module: fleet
#: model:ir.model.constraint,message:fleet.constraint_fleet_vehicle_tag_name_uniq
msgid "Tag name already exists !"
msgstr "Tên thẻ đã tồn tại!"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__tag_ids
msgid "Tags"
msgstr "Thẻ"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Tax Info"
msgstr "Thông tin thuế"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_3
msgid "Tax roll"
msgstr "Danh sách thuế"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__notes
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Terms and Conditions"
msgstr "Điều khoản và Điều kiện"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"Mã ISO quốc gia có 2 ký tự. \n"
"Bạn có thể dùng trường này để tìm kiếm nhanh."

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_43
msgid "Thermostat Replacement"
msgstr "Thay thế bộ ổn nhiệt"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__next_assignation_date
msgid ""
"This is the date at which the car will be available, if not set it means "
"available instantly"
msgstr ""
"Đây là ngày xe có lịch trống, nếu không đặt nghĩa là xe có lịch trống ngay"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_44
msgid "Tie Rod End Replacement"
msgstr "Thay thế đầu rô tuyn"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_45
msgid "Tire Replacement"
msgstr "Thay lốp"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_46
msgid "Tire Service"
msgstr "Dịch vụ lốp"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_to_order
msgid "To Order"
msgstr "Để đặt"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Today Activities"
msgstr "Hoạt động hôm nay"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_tree
msgid "Total"
msgstr "Tổng"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_14
msgid "Total expenses (Excluding VAT)"
msgstr "Tổng chi phí (Không gồm VAT)"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__contract_renewal_total
msgid "Total of contracts due or overdue minus one"
msgstr "Tổng các hợp đồng đến hạn hoặc quá hạn trừ đi một"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_18
msgid "Touring Assistance"
msgstr "Hỗ trợ du lịch"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_services_action
msgid ""
"Track all the services done on your vehicle.\n"
"            Services can be of many types: occasional repair, fixed maintenance, etc."
msgstr ""
"Theo dõi tất cả các dịch vụ thực hiện bằng xe của bạn.\n"
"            Dịch vụ có thể có nhiều loại: sửa chữa không thường xuyên, bảo trì cố định, v.v."

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__trailer_hook
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__trailer_hook
msgid "Trailer Hitch"
msgstr "Móc rơ-mooc"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Trailer Hook"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__transmission
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__transmission
msgid "Transmission"
msgstr "Hộp số"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_47
msgid "Transmission Filter Replacement"
msgstr "Thay thế bộ lọc hộp số"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_48
msgid "Transmission Fluid Replacement"
msgstr "Thay thế dầu hộp số"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_49
msgid "Transmission Replacement"
msgstr "Thay thế hộp số"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__transmission
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_model__transmission
msgid "Transmission Used by the vehicle"
msgstr "Kiểu hộp số được sử dụng bởi phương tiện"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__frame_type__trapez
msgid "Trapez"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__cost_subtype_id
msgid "Type"
msgstr "Loại"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__activity_exception_decoration
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__activity_exception_decoration
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Kiểu  hoạt động ngoại lệ trên hồ sơ."

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__vin_sn
msgid "Unique number written on the vehicle motor (VIN/SN number)"
msgstr "Số duy nhất được ghi trên động cơ của phương tiện (Số VIN/SN)"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__odometer_unit
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__unit
msgid "Unit"
msgstr "Đơn vị"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__odometer_unit
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__odometer_unit
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_odometer__unit
msgid "Unit of the odometer "
msgstr "Đơn vị đồng hồ công tơ mét "

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_unread
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_unread
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_unread
msgid "Unread Messages"
msgstr "Tin chưa đọc"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__message_unread_counter
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__message_unread_counter
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Bộ đếm tin chưa đọc"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_state__sequence
msgid "Used to order the note stages"
msgstr "Được sử dụng để lập trình tự các giai đoạn ghi chú"

#. module: fleet
#: model:res.groups,name:fleet.fleet_group_user
msgid "User"
msgstr "Người dùng"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__car_value
msgid "Value of the bought vehicle"
msgstr "Giá trị của phương tiện được mua"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_assignation_log__vehicle_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__vehicle_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__vehicle_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__vehicle_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer__vehicle_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_costs_report_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_tree
msgid "Vehicle"
msgstr "Phương tiện"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_log_contract
msgid "Vehicle Contract"
msgstr "Hợp đồng phương tiện"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__vehicle_count
msgid "Vehicle Count"
msgstr "Số phương tiện"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__description
msgid "Vehicle Description"
msgstr "Mô tả phương tiện"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
msgid "Vehicle Information"
msgstr "Thông tin phương tiện"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_model_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_model_menu
msgid "Vehicle Models"
msgstr "Mẫu phương tiện"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__name
msgid "Vehicle Name"
msgstr "Tên phương tiện"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_state_action
#: model:ir.model,name:fleet.model_fleet_vehicle_state
#: model:ir.ui.menu,name:fleet.fleet_vehicle_state_menu
msgid "Vehicle Status"
msgstr "Tình trạng phương tiện"

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_tag
msgid "Vehicle Tag"
msgstr "Thẻ phương tiện"

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_tag_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_tag_menu
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_tag_view_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_tag_view_view_tree
msgid "Vehicle Tags"
msgstr "Thẻ phương tiện"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle__vehicle_type
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_report__vehicle_type
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__vehicle_type
msgid "Vehicle Type"
msgstr "Loại phương tiện"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__vehicle_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services__vehicle_id
msgid "Vehicle concerned by this log"
msgstr "Phương tiện liên quan đến nhật ký này"

#. module: fleet
#: code:addons/fleet/models/fleet_vehicle_model.py:0
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_action
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_tree
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_activity
#, python-format
msgid "Vehicles"
msgstr "Phương tiện"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_activity
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
msgid "Vehicles Contracts"
msgstr "Hợp đồng phương tiện"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_search
msgid "Vehicles costs"
msgstr "Chi phí xe cộ"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_search
msgid "Vehicles odometers"
msgstr "Công tơ mét phương tiện"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__insurer_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__vendor_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
msgid "Vendor"
msgstr "Nhà cung cấp"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services__inv_ref
msgid "Vendor Reference"
msgstr "Mã nhà cung cấp"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model__vendors
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
msgid "Vendors"
msgstr "Nhà cung cấp"

#. module: fleet
#: model:fleet.vehicle.state,name:fleet.fleet_vehicle_state_waiting_list
msgid "Waiting List"
msgstr "Danh sách chờ"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract__days_left
msgid "Warning Date"
msgstr "Ngày cảnh báo"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_kanban
msgid "Warning: renewal due soon"
msgstr "Cảnh báo: sắp cần gia hạn"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_50
msgid "Water Pump Replacement"
msgstr "Thay thế bơm nước"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__frame_type__wave
msgid "Wave"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__cost_frequency__weekly
msgid "Weekly"
msgstr "Hàng tuần"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_51
msgid "Wheel Alignment"
msgstr "Cân lốp"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_52
msgid "Wheel Bearing Replacement"
msgstr "Thay thế vòng bi"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_53
msgid "Windshield Wiper(s) Replacement"
msgstr "Thay gạt nước kính chắn gió"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_search
msgid "With Models"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Write here all other information relative to this contract"
msgstr "Viết các thông tin khác liên quan đến hợp đồng này tại đây"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract__notes
msgid "Write here all supplementary information relative to this contract"
msgstr "Viết các thông tin bổ sung liên quan đến hợp đồng này tại đây"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
msgid "Write here any other information related to the service completed."
msgstr "Viết các thông tin khác liên quan tới dịch vụ đã hoàn thành tại đây."

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Write here any other information related to this vehicle"
msgstr "Viết các thông tin khác liên quan tới phương tiện ở đây"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle__model_year
msgid "Year of the model"
msgstr "Năm sản xuất mẫu"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle_log_contract__cost_frequency__yearly
msgid "Yearly"
msgstr "Hàng năm"

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_odometer_action
msgid "You can add various odometer entries for all vehicles."
msgstr "Bạn có thể các mục nhập công tơ mét khác nhau cho tất cả phương tiện."

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_state_action
msgid ""
"You can customize available status to track the evolution of\n"
"            each vehicle. Example: active, being repaired, sold."
msgstr ""
"Bạn có thể tùy chỉnh trạng thái khả dụng để theo dõi tiến trình của\n"
"            mỗi phương tiện. VD: hoạt động, đang sửa, đã bán."

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_model_action
msgid "You can define several models (e.g. A3, A4) for each make (Audi)."
msgstr ""
"Bạn có thể xác định một số mẫu (ví dụ: A3, A4) cho mỗi nhãn hiệu (Audi)."

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "e.g. Model S"
msgstr "VD: Model S"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "e.g. PAE 326"
msgstr "VD: PAE 326"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
msgid "e.g. Tesla"
msgstr "VD. Tesla"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_kanban
msgid "img"
msgstr ""

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__odometer_unit__kilometers
msgid "km"
msgstr "km"

#. module: fleet
#: model:ir.model.fields.selection,name:fleet.selection__fleet_vehicle__odometer_unit__miles
msgid "mi"
msgstr "mi"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "show the contract for this vehicle"
msgstr "hiển thị hợp đồng cho phương tiện này"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "show the odometer logs for this vehicle"
msgstr "hiển thị nhật ký công cơ mét đối với phương tiện này"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "show the services logs for this vehicle"
msgstr "hiển thị nhật ký dịch vụ đối với phương tiện này"
