<?xml version="1.0" encoding="utf-8"?>
<odoo>

<!-- Snippets and options -->
<template id="slide_searchbar_input_snippet_options" inherit_id="website.searchbar_input_snippet_options" name="slide search bar snippet options">
    <xpath expr="//div[@data-js='SearchBar']/we-select[@data-name='scope_opt']" position="inside">
        <we-button data-set-search-type="slides" data-select-data-attribute="slides" data-name="search_slides_opt" data-form-action="/slides/all">Courses</we-button>
    </xpath>
    <xpath expr="//div[@data-js='SearchBar']/we-select[@data-name='order_opt']" position="inside">
        <we-button data-set-order-by="slide_last_update asc" data-select-data-attribute="slide_last_update asc" data-dependencies="search_slides_opt" data-name="order_slide_last_update_asc_opt">Date (old to new)</we-button>
        <we-button data-set-order-by="slide_last_update desc" data-select-data-attribute="slide_last_update desc" data-dependencies="search_slides_opt" data-name="order_slide_last_update_desc_opt">Date (new to old)</we-button>
    </xpath>
    <xpath expr="//div[@data-js='SearchBar']/div[@data-dependencies='limit_opt']" position="inside">
        <we-checkbox string="Description" data-dependencies="search_slides_opt" data-select-data-attribute="true" data-attribute-name="displayDescription"
            data-apply-to=".search-query"/>
        <we-checkbox string="Publication Date" data-dependencies="search_slides_opt" data-select-data-attribute="true" data-attribute-name="displayDetail"
            data-apply-to=".search-query"/>
    </xpath>
</template>

</odoo>
