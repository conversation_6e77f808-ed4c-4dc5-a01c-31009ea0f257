# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_expense
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON>, 2021
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:18+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid ""
"%(user)s confirms this expense is not a duplicate with similar expense."
msgstr ""
"%(user)s confirme que cette note de frais n'est pas un doublon avec une "
"dépense similaire."

#. module: hr_expense
#: model:ir.actions.report,print_report_name:hr_expense.action_report_hr_expense_sheet
msgid ""
"'Expenses - %s - %s' % (object.employee_id.name, (object.name).replace('/', "
"''))"
msgstr ""
"'Frais - %s - %s' % (object.employee_id.name, (object.name).replace('/', "
"''))"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "1 %(exp_cur)s = %(rate)s %(comp_cur)s"
msgstr "1 %(exp_cur)s = %(rate)s %(comp_cur)s"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid ""
"<i class=\"text-muted oe_edit_only\">Use this reference as a subject prefix "
"when submitting by email.</i>"
msgstr ""
"Utiliser cette référence comme préfixe de sujet lors de l'envoi par email.<i"
" class=\"text-muted oe_edit_only\">"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid ""
"<p>Approve the report here.</p><p>Tip: if you refuse, don’t forget to give "
"the reason thanks to the hereunder message tool</p>"
msgstr ""
"<p>Approuvez le rapport ici.</p><p>Conseil: Si vous le refusez, n'oubliez "
"pas d'ajouter une justification via l'outil de messagerie</p>"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid ""
"<p>Click on <b> Action Create Report </b> to submit selected expenses to "
"your manager</p>"
msgstr ""
"<p>Cliquer sur <b> Action créer un rapport </b> pour soumettre des notes de "
"frais sélectionnés à votre manager</p>"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "<p>Click on <b> Create Report </b> to create the report.</p>"
msgstr "<p>Cliquez sur <b> Créer rapport </b> pour créer un rapport.</p>"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "<p>Once your <b> Expense </b> is ready, you can save it.</p>"
msgstr ""
"<p>Une fois votre <b> Note de frais </b> prête, vous pouvez la "
"sauvegarder.</p>"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "<p>Select expenses to submit them to your manager</p>"
msgstr "<p>Sélectionner les notes de frais à soumettre à votre manager</p>"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid ""
"<p>The accountant receive approved expense reports.</p><p>He can post "
"journal entries in one click if taxes and accounts are right.</p>"
msgstr ""
"<p>Le comptable reçoit le rapport des notes de frais approuvées.</p><p> Il "
"peut poster les pièces comptables en un clic si les taxes et les comptes "
"sont bien configurés.</p>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\"/>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "<span>@</span>"
msgstr "<span>@</span>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Date:</strong>"
msgstr "<strong>Date :</strong>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Description:</strong>"
msgstr "<strong>Description :</strong>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Employee:</strong>"
msgstr "<strong>Employé :</strong>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Payment By:</strong>"
msgstr "<strong>Paiement par :</strong>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Total</strong>"
msgstr "<strong>Total</strong>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Validated By:</strong>"
msgstr "<strong>Validée par :</strong>"

#. module: hr_expense
#: model:product.product,name:hr_expense.accomodation_expense_product
#: model:product.template,name:hr_expense.accomodation_expense_product_product_template
msgid "Accomodation"
msgstr "Hébergement"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__account_id
msgid "Account"
msgstr "Compte"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Accounting"
msgstr "Comptabilité"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__accounting_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__accounting_date
msgid "Accounting Date"
msgstr "Date comptable"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_needaction
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_needaction
msgid "Action Needed"
msgstr "Nécessite une action"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_ids
msgid "Activities"
msgstr "Activités"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_exception_decoration
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Activité exception décoration"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_state
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_state
msgid "Activity State"
msgstr "Status de l'activité"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_type_icon
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icône de type d'activité"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.mail_activity_type_action_config_hr_expense
#: model:ir.ui.menu,name:hr_expense.hr_expense_menu_config_activity_type
msgid "Activity Types"
msgstr "Types d'activités"

#. module: hr_expense
#: model:res.groups,name:hr_expense.group_hr_expense_manager
msgid "Administrator"
msgstr "Administrateur"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Alias"
msgstr "Alias"

#. module: hr_expense
#: model:res.groups,name:hr_expense.group_hr_expense_user
msgid "All Approver"
msgstr "Approbateur de Tout"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all_all
msgid "All Expense Reports"
msgstr "Toutes les dépenses"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_actions_my_all
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_my_expenses_all
msgid "All My Expenses"
msgstr "Toutes mes notes de frais"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_sheet_all
msgid "All Reports"
msgstr "Tous les rapports"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__amount_residual
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__amount_residual
msgid "Amount Due"
msgstr "Montant dû"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/upload_mixin.js:0
#, python-format
msgid "An error occurred during the upload"
msgstr "Une erreur est survenue lors du chargement"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__account_id
msgid "An expense account is expected"
msgstr "Un compte est attendu"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/expense_form_view.js:0
#, python-format
msgid "An expense of same category, amount and date already exists."
msgstr "Une note de frais de même catégorie, montant et date existe déjà."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "An expense report must contain only lines from the same company."
msgstr ""
"Un rapport de notes de frais doit seulement contenir des lignes de la même "
"société."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__analytic_account_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Analytic Account"
msgstr "Compte analytique"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__analytic_tag_ids
msgid "Analytic Tags"
msgstr "Étiquettes analytiques"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_all
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_unsubmitted
msgid "Apple App Store"
msgstr "App Store d'Apple"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__approval_date
msgid "Approval Date"
msgstr "Date de validation"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Approve"
msgstr "Approuver"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Approve Report"
msgstr "Approuver le rapport"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_approve
msgid "Approve the new expense reports submitted by the employees you manage."
msgstr ""
"Approuvez les nouvelles notes de frais soumises par les employés que vous "
"gérez."

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__approved
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__approve
#: model:mail.message.subtype,name:hr_expense.mt_expense_approved
msgid "Approved"
msgstr "Confirmé par responsable"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__approved_by
msgid "Approved By"
msgstr "Approuvé par"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Approved Expenses"
msgstr "Frais approuvés"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__approved_on
msgid "Approved On"
msgstr "Approuvé le"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Archived"
msgstr "Archivé"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Attach Receipt"
msgstr "Joindre un reçu"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "Attach your receipt here."
msgstr "Ajoutez votre reçu ici."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_attachment_count
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_attachment_count
msgid "Attachment Count"
msgstr "Compte des pièces jointes"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__bank_journal_id
msgid "Bank Journal"
msgstr "Journal de banque"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__reference
msgid "Bill Reference"
msgstr "Référence de la facture"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__can_approve
msgid "Can Approve"
msgstr "Peut Approuver"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__can_reset
msgid "Can Reset"
msgstr "Réinitialiser"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_product_product__can_be_expensed
#: model:ir.model.fields,field_description:hr_expense.field_product_template__can_be_expensed
#: model_terms:ir.ui.view,arch_db:hr_expense.product_template_search_view_inherit_hr_expense
msgid "Can be Expensed"
msgstr "Peut être inséré dans une note de frais"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/expense_form_view.js:0
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
#, python-format
msgid "Cancel"
msgstr "Annuler"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Category"
msgstr "Catégorie"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Category:"
msgstr "Catégorie:"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Category: not found"
msgstr "Catégorie : introuvable"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Certified honest and conform,<br/>(Date and signature).<br/><br/>"
msgstr "Certifié conforme sur l'honneur,<br/>(Date et signature).<br/><br/>"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__company_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__company_id
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__payment_mode__company_account
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Company"
msgstr "Société"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de config"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_configuration
msgid "Configuration"
msgstr "Configuration"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Confirmed Expenses"
msgstr "Notes de frais confirmées"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Il est possible de convertir deux unités de mesures si elles appartiennent à"
" la même catégorie. Cette conversion utilise les rapports définis pour ces "
"unités."

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/xml/documents_upload_views.xml:0
#, python-format
msgid "Create"
msgstr "Créer"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_expenses_analysis_tree
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Create Report"
msgstr "Créer le Rapport"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_account
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_all
msgid "Create a new expense report"
msgstr "Créez un nouveau rapport de notes de frais"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Create expenses from incoming emails"
msgstr "Créer des notes de frais provenant des emails entrants"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_all
msgid "Create new expenses to get statistics."
msgstr "Créez de nouvelles notes de frais pour obtenir des statistiques."

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/xml/documents_upload_views.xml:0
#, python-format
msgid "Create record"
msgstr "Créer un enregistrement"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__create_date
msgid "Created on"
msgstr "Créé le"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__currency_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__currency_id
msgid "Currency"
msgstr "Devise"

#. module: hr_expense
#: model:product.product,name:hr_expense.allowance_expense_product
#: model:product.template,name:hr_expense.allowance_expense_product_product_template
msgid "Daily Allowance"
msgstr "Indemnité journalière"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Date"
msgstr "Date"

#. module: hr_expense
#: model:product.product,uom_name:hr_expense.allowance_expense_product
#: model:product.template,uom_name:hr_expense.allowance_expense_product_product_template
msgid "Days"
msgstr "Jours"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Dear"
msgstr "Cher"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__expense_alias_prefix
msgid "Default Alias Name for Expenses"
msgstr "Alias par défaut pour les dépenses"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_department
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__department_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Department"
msgstr "Département"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__name
msgid "Description"
msgstr "Description"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_all
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_unsubmitted
msgid "Did you try the mobile app?"
msgstr "Avez-vous essayé l'application mobile ?"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Digitalize your receipts with OCR and Artificial Intelligence"
msgstr "Numériser vos reçus avec l'OCR et l'Intelligence Artificielle"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: hr_expense
#: model_terms:digest.tip,tip_description:hr_expense.digest_tip_hr_expense_0
msgid ""
"Do not keep your expense tickets in your pockets any longer. Just snap a "
"picture of your receipt and let Odoo digitalizes it for you. The OCR and "
"Artificial Intelligence will fill the data automatically."
msgstr ""
"Ne gardez plus vos notes de frais dans vos poches. Prenez simplement une "
"photo de votre reçu et laissez Odoo le numériser pour vous. L'OCR et "
"l'Intelligence Artificielle rempliront les données automatiquement."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Documents"
msgstr "Documents"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__done
msgid "Done"
msgstr "Fait"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__draft
msgid "Draft"
msgstr "Brouillon"

#. module: hr_expense
#: code:addons/hr_expense/wizard/hr_expense_approve_duplicate.py:0
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__duplicate_expense_ids
#, python-format
msgid "Duplicate Expense"
msgstr "Dupliquer la note de frais"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_employee
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__employee_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__employee_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Employee"
msgstr "Employé"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__payment_mode__own_account
msgid "Employee (to reimburse)"
msgstr "Employé (à rembourser)"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_account
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_account_employee_expenses
msgid "Employee Expenses"
msgstr "Notes de frais"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__address_id
msgid "Employee Home Address"
msgstr "Adresse personnelle de l'Employé"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_account_move_line__expense_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_employee__expense_manager_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__expense_ids
#: model:ir.model.fields,field_description:hr_expense.field_res_users__expense_manager_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Expense"
msgstr "Dépense"

#. module: hr_expense
#: model:mail.activity.type,name:hr_expense.mail_act_expense_approval
msgid "Expense Approval"
msgstr "Approbation de Notes de Frais"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_approve_duplicate
msgid "Expense Approve Duplicate"
msgstr "Note de Frais Dupliquée"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_employee_tree_inherit_expense
msgid "Expense Approver"
msgstr "Approbateur des dépenses"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__date
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Expense Date"
msgstr "Date de la dépense"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Expense Digitalization (OCR)"
msgstr "Numérisation des dépenses (OCR)"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__journal_id
msgid "Expense Journal"
msgstr "Journal des notes de frais"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__expense_line_ids
msgid "Expense Lines"
msgstr "Ligne de frais"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__is_editable
msgid "Expense Lines Are Editable By Current User"
msgstr ""
"Les lignes de notes de frais sont modifiables par l'utilisateur actuel"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_employee_public__expense_manager_id
msgid "Expense Manager"
msgstr "Manager des notes de frais"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_product
#: model:ir.ui.menu,name:hr_expense.menu_hr_product
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Expense Products"
msgstr "Articles de note de frais"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_refuse_wizard
msgid "Expense Refuse Reason Wizard"
msgstr "Wizard des Raisons de Refus d'une Note de Frais"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_sheet
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__sheet_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Expense Report"
msgstr "Note de frais"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__state
msgid "Expense Report State"
msgstr "Etat de la note de frais"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__name
msgid "Expense Report Summary"
msgstr "Résumé de la note de frais"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_report
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Expense Reports"
msgstr "Notes de frais"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_department_filtered
msgid "Expense Reports Analysis"
msgstr "Analyse des notes de frais"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all_to_pay
msgid "Expense Reports To Pay"
msgstr "Notes de frais à Rembourser"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all_to_post
msgid "Expense Reports To Post"
msgstr "Notes de frais à Comptabiliser"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all_to_approve
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_department_to_approve
msgid "Expense Reports to Approve"
msgstr "Notes de frais à Approuver"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
msgid "Expense Validate Duplicate"
msgstr "Note de Frais Dupliquée"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_product
msgid "Expense products can be reinvoiced to your customers."
msgstr "Les produits de notes de frais peuvent être refacturés à vos clients."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
msgid "Expense refuse reason"
msgstr "Motif de refus de la note de frais"

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_approved
msgid "Expense report approved"
msgstr "Note de frais approuvée"

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_paid
msgid "Expense report paid"
msgstr "Note de frais remboursée"

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_refused
msgid "Expense report refused"
msgstr "Note de frais refusée"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all
msgid ""
"Expense reports regroup all the expenses incurred during a specific event."
msgstr ""
"Les notes de frais regroupent l'ensemble des dépenses engagées lors d'un "
"événement précis."

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_account_move_line__expense_id
msgid "Expense where the move line come from"
msgstr "Note de frais d'où provient la ligne d'écriture"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_root
#: model:product.product,name:hr_expense.product_product_fixed_cost
#: model:product.template,name:hr_expense.product_product_fixed_cost_product_template
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_activity
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_activity
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_expenses_analysis_tree
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Expenses"
msgstr "Notes de frais"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_actions_all
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_all_expenses
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_graph
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_graph
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_pivot
msgid "Expenses Analysis"
msgstr "Analyse des notes de frais"

#. module: hr_expense
#: model:ir.actions.report,name:hr_expense.action_report_hr_expense_sheet
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Expenses Report"
msgstr "Rapport de dépenses"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_department__expense_sheets_to_approve_count
msgid "Expenses Reports to Approve"
msgstr "Rapport de dépenses à approuver"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Expenses by Date"
msgstr "Notes de Frais par Date"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Expenses must be paid by the same entity (Company or employee)."
msgstr ""
"Les notes de frais doivent être payées par la même entinté (Société ou "
"employé)"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Expenses of Your Team Member"
msgstr "Dépense des membres de votre équipe"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Expenses to Invoice"
msgstr "Frais à facturer"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.account_journal_dashboard_kanban_view_inherit_hr_expense
msgid "Expenses to Process"
msgstr "Notes de frais à traiter"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__is_refused
msgid "Explicitly Refused by manager or accountant"
msgstr "Refusé explicitement par le responsable ou le comptable"

#. module: hr_expense
#: model:product.product,name:hr_expense.trans_expense_product
#: model:product.template,name:hr_expense.trans_expense_product_product_template
msgid "Flights, train, bus, taxi, parking"
msgstr "Vols, train, bus, taxi, parking"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_follower_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_follower_ids
msgid "Followers"
msgstr "Abonnés"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_partner_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_partner_ids
msgid "Followers (Partners)"
msgstr "Abonnés (Partenaires)"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__activity_type_icon
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icône Font Awesome e.g. fa-tasks"

#. module: hr_expense
#: model:product.product,name:hr_expense.food_expense_product
#: model:product.template,name:hr_expense.food_expense_product_product_template
msgid "Food & beverages"
msgstr "Nourriture et boissons"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Former Employees"
msgstr "Anciens employés"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Future Activities"
msgstr "Activités futures"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "General Information"
msgstr "Information Générale"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Generated Expenses"
msgstr "Générer des notes de frais"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_all
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_unsubmitted
msgid "Google Play Store"
msgstr "Google Play Store"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Group By"
msgstr "Regrouper par"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__is_multiple_currency
msgid "Handle lines with different currencies"
msgstr "Gère des lignes avec des devises différentes"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__has_message
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__has_message
msgid "Has Message"
msgstr "A un message"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__hr_expense_ids
msgid "Hr Expense"
msgstr "Dépense RH"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__hr_expense_sheet_id
msgid "Hr Expense Sheet"
msgstr "Note de frais RH"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__id
msgid "ID"
msgstr "ID"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_exception_icon
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_exception_icon
msgid "Icon"
msgstr "Icône"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__activity_exception_icon
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icône pour indiquer une activité d'exception."

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_needaction
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_unread
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_needaction
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_unread
msgid "If checked, new messages require your attention."
msgstr "Si coché, de nouveaux messages demandent votre attention."

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_has_error
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_has_sms_error
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_has_error
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si actif, certains messages ont une erreur de livraison."

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__payment_state__in_payment
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "In Payment"
msgstr "En paiement"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Included in price taxes"
msgstr "Taxes incluses dans le prix"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Incoming Emails"
msgstr "Emails entrants"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Invalid attachments!"
msgstr "Pièces jointes non valides !"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__payment_state__invoicing_legacy
msgid "Invoicing App Legacy"
msgstr "Historique de l'app facturation"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__is_editable
msgid "Is Editable By Current User"
msgstr "Est éditable par l'utilisateur actuel"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_is_follower
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_is_follower
msgid "Is Follower"
msgstr "Est un abonné"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__same_currency
msgid "Is currency_id different from the company_currency_id"
msgstr "Est-ce que currency_id est différent de company_currency_id"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_has_cost
msgid "Is product with non zero cost selected"
msgstr "Produit avec un coût sélectionné"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_journal
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Journal"
msgstr "Journal"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_move
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__account_move_id
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Journal Entry"
msgstr "Pièce comptable"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_move_line
msgid "Journal Item"
msgstr "Écriture comptable"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__label_convert_rate
msgid "Label Convert Rate"
msgstr "Taux de Conversion (texte)"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__label_total_amount_company
msgid "Label Total Amount Company"
msgstr "Étiquette montant total entreprise"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense____last_update
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate____last_update
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard____last_update
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Late Activities"
msgstr "Activités en retard"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__use_mailgateway
msgid "Let your employees record expenses by email"
msgstr "Laisser vos employés enregistrer des notes de frais par email"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_main_attachment_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_main_attachment_id
msgid "Main Attachment"
msgstr "Pièce jointe principale"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__user_id
msgid "Manager"
msgstr "Gestionnaire"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "Managers can get all reports to approve from this menu."
msgstr ""
"Les gestionnaires peuvent voir tous les rapports à approuver depuis ce menu."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_has_error
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_has_error
msgid "Message Delivery error"
msgstr "Erreur d'envoi du message"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_ids
msgid "Messages"
msgstr "Messages"

#. module: hr_expense
#: model:product.product,name:hr_expense.mileage_expense_product
#: model:product.template,name:hr_expense.mileage_expense_product_product_template
msgid "Mileage"
msgstr "Kilométrage"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__my_activity_date_deadline
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Échéance de mon activité"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_my_expenses
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "My Expenses"
msgstr "Mes notes de frais"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_actions_my_unsubmitted
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_my_expenses_to_submit
msgid "My Expenses to Report"
msgstr "Mes notes de frais à déclarer"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_my_all
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_sheet_my_reports
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "My Reports"
msgstr "Mes Rapports"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "My Team"
msgstr "Mon équipe"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Name"
msgstr "Nom"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "New Expense Report"
msgstr "Nouveau rapport de notes de frais"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_date_deadline
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Date limite de l'activité à venir"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_summary
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_summary
msgid "Next Activity Summary"
msgstr "Résumé d'activité suivant"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_type_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_type_id
msgid "Next Activity Type"
msgstr "Type d'activités à venir"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid ""
"No Expense account found for the product %s (or for its category), please "
"configure one."
msgstr ""
"Aucun compte de dépenses n'a été trouvé pour le produit %s (ou sa "
"catégorie). Veuillez en configurer un."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "No Home Address found for the employee %s, please configure one."
msgstr "Adresse personnelle de l'employé %s inconnue, la définir SVP."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "No attachment was provided"
msgstr "Aucune pièce jointe n'a été fournie"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_department_filtered
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_all
msgid "No data yet!"
msgstr "Pas de données"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_product
msgid "No expense products found. Let's create one!"
msgstr "Aucun produit de dépense trouvé. Créons-en un !"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_my_all
msgid "No expense report found. Let's create one!"
msgstr "Aucun rapport de notes de frais trouvé. Créons-en un !"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_approve
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_pay
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_post
msgid "No expense reports found"
msgstr "Aucun rapport trouvé"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all
msgid "No expense reports found. Let's create one!"
msgstr "Aucun rapport de notes de frais trouvé. Créons-en un !"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__payment_state__not_paid
msgid "Not Paid"
msgstr "Non payées"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__description
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Notes..."
msgstr "Notes..."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_needaction_counter
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'actions"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__attachment_number
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__attachment_number
msgid "Number of Attachments"
msgstr "Nombre de documents joints"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_has_error_counter
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'erreurs"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_needaction_counter
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Nombre de messages exigeant une action"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_has_error_counter
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de messages avec des erreurs d'envoi"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_unread_counter
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_unread_counter
msgid "Number of unread messages"
msgstr "Nombre de messages non lus"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register_no_user
msgid "Odoo"
msgstr "Odoo"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_account
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_all
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_my_all
msgid ""
"Once you have created your expense, submit it to your manager who will "
"validate it."
msgstr ""
"Après avoir créé une note de frais, la soumettre pour validation à votre "
"responsable."

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid ""
"Once your <b>Expense report</b> is ready, you can submit it to your manager "
"and wait for the approval from your manager."
msgstr ""
"Une fois votre <b>rapport de notes de frais</b> prêt, vous pouvez le "
"soumettre à votre manager et attendre sa validation."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Only HR Officers or the concerned employee can reset to draft."
msgstr ""
"Seul un agent RH ou l'employé concerné peuvent remettre la demande en "
"brouillon. "

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Only Managers and HR Officers can approve expenses"
msgstr ""
"Seuls un agent ou un responsable RH peuvent approuver une demande de congé"

#. module: hr_expense
#: model:product.product,name:hr_expense.other_expense_product
#: model:product.template,name:hr_expense.other_expense_product_product_template
msgid "Other expenses"
msgstr "Autres dépenses"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__done
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__payment_state__paid
#: model:mail.message.subtype,name:hr_expense.mt_expense_paid
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Paid"
msgstr "Payé"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__payment_mode
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__payment_mode
msgid "Paid By"
msgstr "Payé par"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Partial"
msgstr "Partiel"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__payment_state__partial
msgid "Partially Paid"
msgstr "Partiellement réglé"

#. module: hr_expense
#: code:addons/hr_expense/models/account_move.py:0
#, python-format
msgid "Payment Cancelled"
msgstr "Paiement annulé"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__payment_state
msgid "Payment Status"
msgstr "État du paiement"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid ""
"Please configure Default Expense account for Category expense: "
"`property_account_expense_categ_id`."
msgstr ""
"Veuillez configurer un compte de notes de frais par défaut pour la catégorie"
" de notes de frais: `property_account_expense_categ_id`."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Post Entries"
msgstr "Comptabiliser les écritures"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Post Journal Entries"
msgstr "Comptabiliser les pièces"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_post
msgid ""
"Post the journal entries of the new expense reports approved by the "
"employees' manager."
msgstr ""
"Enregistrez les pièces comptables des nouvelles notes de frais approuvées "
"par le responsable des employés."

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__post
msgid "Posted"
msgstr "Comptabilisé"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register_no_user
msgid "Powered by"
msgstr "Fourni par"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Price"
msgstr "Prix"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Price in Company Currency"
msgstr "Prix dans la Devise de la Société"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Price:"
msgstr "Prix :"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_id
msgid "Product"
msgstr "Article"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Product Name"
msgstr "Nom de l'article"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_product_template
msgid "Product Template"
msgstr "Modèle d'article"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_tree_view
msgid "Product Variants"
msgstr "Variantes d'article"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_employee_public
msgid "Public Employee"
msgstr "Fonctionnaire"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Qty"
msgstr "Qté"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__quantity
msgid "Quantity"
msgstr "Quantité"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__reason
msgid "Reason"
msgstr "Motif"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "Reason :"
msgstr "Motif :"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
msgid "Reason to refuse Expense"
msgstr "Motif du refus de la note de frais"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Receipts"
msgstr "Reçus"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Ref."
msgstr "Réf."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__is_ref_editable
msgid "Reference Is Editable By Current User"
msgstr "La référence est éditable par l'utilisateur actuel"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Refuse"
msgstr "Refuser"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_refuse_wizard_action
msgid "Refuse Expense"
msgstr "Refuser la note de frais"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__refused
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__cancel
#: model:mail.message.subtype,name:hr_expense.mt_expense_refused
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Refused"
msgstr "Refusée"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Refused Expenses"
msgstr "Dépenses refusées"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#: model:ir.model,name:hr_expense.model_account_payment_register
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
#, python-format
msgid "Register Payment"
msgstr "Enregistrer un paiement"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__module_hr_payroll_expense
msgid "Reimburse Expenses in Payslip"
msgstr "Rembourser les notes de frais sur la fiche de paie"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Reimburse expenses in payslips"
msgstr "Rembourser les notes de frais dans les fiches de paie"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Reimburse in Payslip"
msgstr "Rembourser dans la paie"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_pay
msgid ""
"Reimburse the employees who incurred these costs or simply register the "
"corresponding payments."
msgstr ""
"Remboursez les salariés qui ont engagé ces frais ou enregistrez simplement "
"les versements correspondants."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "Report"
msgstr "Rapport"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__company_currency_id
msgid "Report Company Currency"
msgstr "Rapporter la Devise de la Société"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_reports
msgid "Reporting"
msgstr "Analyse"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_sheet_all_to_approve
msgid "Reports to Approve"
msgstr "Notes de frais à Approuver"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_sheet_all_to_pay
msgid "Reports to Pay"
msgstr "Notes de frais à Rembourser"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_sheet_all_to_post
msgid "Reports to Post"
msgstr "Notes de frais à Comptabiliser"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Reset to Draft"
msgstr "Remettre en brouillon"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_user_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_user_id
msgid "Responsible User"
msgstr "Responsable"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__payment_state__reversed
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Reversed"
msgstr "Extourné"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_has_sms_error
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Erreur d'envoi SMS"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__sample
msgid "Sample"
msgstr "Échantillon"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/expense_form_view.js:0
#, python-format
msgid "Save Anyways"
msgstr "Enregistrer quand même"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/xml/documents_upload_views.xml:0
#: code:addons/hr_expense/static/src/xml/documents_upload_views.xml:0
#: code:addons/hr_expense/static/src/xml/documents_upload_views.xml:0
#, python-format
msgid "Scan"
msgstr "Scan"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/xml/expense_qr_modal_template.xml:0
#, python-format
msgid "Scan this QR code to get the Odoo app:"
msgstr "Scannez ce QR code pour obtenir l'application Odoo :"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_employee__expense_manager_id
#: model:ir.model.fields,help:hr_expense.field_res_users__expense_manager_id
msgid ""
"Select the user responsible for approving \"Expenses\" of this employee.\n"
"If empty, the approval is done by an Administrator or Approver (determined in settings/users)."
msgstr ""
"Sélectionnez l'utilisateur responsable de l'approbation des \"Notes de frais\" de cet employé.\n"
"Si vide, l'approbation est faite par un Administrateur ou un Approbateur (déterminé dans les paramètres/utilisateurs)."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid ""
"Selected Unit of Measure for expense %(expense)s does not belong to the same"
" category as the Unit of Measure of product %(product)s."
msgstr ""
"L'unité de mesure sélectionnée pour la dépense %(expense)s n'appartient pas "
"à la même catégorie que l'unité de mesure du produit %(product)s."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid ""
"Send an email to this email alias with the receipt in attachment to create "
"an expense in one click. If the first word of the mail subject contains the "
"category's internal reference or the category name, the corresponding "
"category will automatically be set. Type the expense amount in the mail "
"subject to set it on the expense too."
msgstr ""
"Envoyez un courrier électronique à cet alias avec le reçu en pièce jointe "
"pour créer une note de frais en un clic. Si le premier mot de l'objet du "
"courrier contient la référence interne de la catégorie ou le nom de la "
"catégorie, la catégorie correspondante sera automatiquement définie. Tapez "
"le montant de la dépense dans l'objet du courrier pour le définir également "
"sur la note de frais."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__module_hr_expense_extract
msgid "Send bills to OCR to generate expenses"
msgstr "Envoyer des factures à l'OCR pour générer des charges"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_configuration
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_global_settings
msgid "Settings"
msgstr "Paramètres"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Setup your domain alias"
msgstr "Configurer votre domaine d'alias"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__sheet_ids
msgid "Sheet"
msgstr "Feuille"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Montrez toutes les enregistrements pour lesquelles la date des prochaines "
"actions est pour aujourd'hui ou avant"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_all
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_unsubmitted
msgid ""
"Snap pictures of your receipts and let Odoo<br> automatically create "
"expenses for you."
msgstr ""
"Prenez vos tickets de caisse en photo et laissez <br>Odoo automatiquement "
"créer une dépense pour vous."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Specify expense journal to generate accounting entries."
msgstr "Veuillez préciser un journal pour générer les entrées comptables."

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_product_product__can_be_expensed
#: model:ir.model.fields,help:hr_expense.field_product_template__can_be_expensed
msgid "Specify whether the product can be selected in an expense."
msgstr "Indiquez si le produit peut être sélectionné dans une note de frais."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__state
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__state
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Status"
msgstr "État"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__activity_state
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Statut basé sur les activités\n"
"En retard : la date d'échéance est déjà dépassée\n"
"Aujourd'hui : la date d'activité est aujourd'hui\n"
"Planifiée : activités futures"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__state
msgid "Status of the expense."
msgstr "Etat de la note de frais."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Submit to Manager"
msgstr "Soumettre au responsable"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__reported
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__submit
msgid "Submitted"
msgstr "Soumis"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__untaxed_amount
msgid "Subtotal"
msgstr "Sous-total"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__tax_ids
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Taxes"
msgstr "Taxes"

#. module: hr_expense
#: model:res.groups,name:hr_expense.group_hr_expense_team_approver
msgid "Team Approver"
msgstr "Approbateur de l'Equipe"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid ""
"The accountant can register a payment to reimburse the employee directly."
msgstr ""
"Le comptable peut enregistrer un paiement pour rembourser l'employé "
"directement."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "The current user has no related employee. Please, create one."
msgstr ""
"L'utilisateur actuel n'a pas d'employé associé. S'il vous plaît, créez-en "
"un."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "The expense reports were successfully approved."
msgstr "Les rapports des notes de frais ont été approuvés avec succès."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid ""
"The first word of the email subject did not correspond to any category code."
" You'll have to set the category manually on the expense."
msgstr ""
"Le premier mot de l'objet de l'email ne correspond à aucun code de "
"catégorie. Vous devrez définir la catégorie manuellement sur la dépense."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
msgid ""
"The following approved expenses have similar employee, amount and category "
"than some expenses of this report. Please verify this report does not "
"contain duplicates."
msgstr ""
"Les notes de frais approuvées suivantes ont un employé, un montant et une "
"catégorie similaires à certaines dépenses de ce rapport. Veuillez vérifier "
"que ce rapport ne contient pas de doublons."

#. module: hr_expense
#: model:ir.model.constraint,message:hr_expense.constraint_hr_expense_sheet_journal_id_required_posted
msgid "The journal must be set on posted expense"
msgstr "Le journal doit être mis sur une note de frais postée"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__journal_id
msgid "The journal used when the expense is done."
msgstr "Le journal utilisé lorsque la note de frais est terminée."

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__bank_journal_id
msgid "The payment method used when the expense is paid by the company."
msgstr ""
"La méthode de paiement utilisée lorsque la dépense est payée par la société."

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__tax_ids
msgid "The taxes should be \"Included In Price\""
msgstr "Les taxes doivent être \"incluses dans le prix\""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "There are no expense reports to approve."
msgstr "Il n'y a pas de rapport de notes de frais à approuver."

#. module: hr_expense
#: model:digest.tip,name:hr_expense.digest_tip_hr_expense_0
#: model_terms:digest.tip,tip_description:hr_expense.digest_tip_hr_expense_0
msgid "Tip: Snap pictures of your receipts with the remote app"
msgstr "Astuce : prenez des photos de vos reçus avec l'application à distance"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "To Approve"
msgstr "À approuver"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "To Pay"
msgstr "A payer"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "To Post"
msgstr "A entrer en Comptabilité"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "To Report"
msgstr "À rapporter"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__draft
msgid "To Submit"
msgstr "À soumettre"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Today Activities"
msgstr "Activités du jour"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Total"
msgstr "Total"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Total %s"
msgstr "Total %s"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__total_amount
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Total Amount"
msgstr "Montant total"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_expenses_analysis_tree
msgid "Total Company Currency"
msgstr "Total en Devise de l'Entreprise"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__total_amount
msgid "Total In Currency"
msgstr "Total en Devise"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__activity_exception_decoration
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type d'activité d'exception enregistrée"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__unit_amount
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Unit Price"
msgstr "Prix unitaire"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_uom_id
msgid "Unit of Measure"
msgstr "Unité de mesure"

#. module: hr_expense
#: model:product.product,uom_name:hr_expense.accomodation_expense_product
#: model:product.product,uom_name:hr_expense.food_expense_product
#: model:product.product,uom_name:hr_expense.other_expense_product
#: model:product.product,uom_name:hr_expense.product_product_fixed_cost
#: model:product.product,uom_name:hr_expense.trans_expense_product
#: model:product.template,uom_name:hr_expense.accomodation_expense_product_product_template
#: model:product.template,uom_name:hr_expense.food_expense_product_product_template
#: model:product.template,uom_name:hr_expense.other_expense_product_product_template
#: model:product.template,uom_name:hr_expense.product_product_fixed_cost_product_template
#: model:product.template,uom_name:hr_expense.trans_expense_product_product_template
msgid "Units"
msgstr "Unité(s)"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_unread
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_unread
msgid "Unread Messages"
msgstr "Messages non lus"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_unread_counter
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Compteur de messages non lus"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_uom_category_id
msgid "UoM Category"
msgstr "Catégorie d'UdM"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/xml/documents_upload_views.xml:0
#, python-format
msgid "Upload"
msgstr "Upload"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_res_users
msgid "Users"
msgstr "Utilisateurs"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_approve_duplicate_action
msgid "Validate Duplicate Expenses"
msgstr "Valider les Notes de Frais en double"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "View Attachments"
msgstr "Voir les Pièces Jointes"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "View Expense"
msgstr "Voir la note de frais"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "View Report"
msgstr "Voir le Rapport"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "Want to manage your expenses? It starts here."
msgstr "Vous voulez gérer les notes de frais ? Ça commence ici. "

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__website_message_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__website_message_ids
msgid "Website Messages"
msgstr "Messages du site web"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__website_message_ids
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__website_message_ids
msgid "Website communication history"
msgstr "Historique de communication du site web"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid ""
"When the cost of an expense product is different than 0, then the user using"
" this product won't be able to change the amount of the expense, only the "
"quantity. Use a cost different than 0 for expense categories funded by the "
"company at fixed cost like allowances for mileage, per diem, accomodation or"
" meal."
msgstr ""
"Lorsque le coût d'un produit de note de frais est différent de 0, alors "
"l'utilisateur utilisant ce produit ne pourra pas modifier le montant de la "
"note de frais, seulement la quantité. Utilisez un coût différent de 0 pour "
"les catégories de notes de frais financées par l'entreprise à coût fixe "
"comme les indemnités de kilométrage, les indemnités journalières, "
"l'hébergement ou les repas."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You are not authorized to edit the reference of this expense report."
msgstr ""
"Vous n'êtes pas autorisé à modifier la référence de ce rapport de notes de "
"frais."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You are not authorized to edit this expense report."
msgstr "Vous n'êtes pas autorisé à modifier ce rapport de notes de frais."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You can not create report without category."
msgstr "Vous ne pouvez pas créer de rapport sans catégorie."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "You can now submit it to the manager from the following link."
msgstr ""
"Vous pouvez maintenant le soumettre au responsable à partir du lien suivant."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You can only approve your department expenses"
msgstr ""
"Vous pouvez seulement approuver les notes de frais de votre département."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You can only generate accounting entry for approved expense(s)."
msgstr ""
"Des écritures comptables ne peuvent être générées que pour des notes de "
"frais approuvées."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You can only refuse your department expenses"
msgstr "Vous pouvez seulement refuser les notes de frais de votre département"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You can't mix sample expenses and regular ones"
msgstr ""
"Vous ne pouvez pas mélanger des exemples de notes de frais et des régulières"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot add expenses of another employee."
msgstr "Vous ne pouvez pas ajouter des notes de frais pour un autre employé."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot approve your own expenses"
msgstr "Vous ne pouvez pas approuver vous-même vos notes de frais"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot delete a posted or approved expense."
msgstr "Vous ne pouvez pas supprimer une note de frais postée ou approuvée."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot delete a posted or paid expense."
msgstr "Vous ne pouvez pas supprimer une dépense comptabilisée ou payée."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot refuse your own expenses"
msgstr "Vous ne pouvez pas refuser vos propres notes de frais"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot report expenses for different companies in the same report."
msgstr ""
"Vous ne pouvez pas déclarer des notes de frais de différentes sociétés dans "
"le même rapport."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot report expenses for different employees in the same report."
msgstr ""
"Vous ne pouvez pas déclarer les dépenses de différents employés dans le même"
" rapport."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot report twice the same line!"
msgstr "Vous ne pouvez pas ajouter deux fois la même dépense !"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid ""
"You need to have at least one category that can be expensed in your database"
" to proceed!"
msgstr ""
"Vous devez avoir au moins une catégorie qui peut être passée en notes de "
"frais dans votre base de données pour continuer !"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "Your Expense"
msgstr "Votre dépense"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Your expense has been successfully registered."
msgstr "Votre note de frais a été enregistrée avec succès."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "e.g. Lunch"
msgstr "ex. Déjeuner"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "e.g. Lunch with Customer"
msgstr "e.g. Déjeuner avec un Client"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "e.g. Trip to NY"
msgstr "e.g. Voyage à New-York"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "has been refused"
msgstr "refusé"

#. module: hr_expense
#: model:product.product,uom_name:hr_expense.mileage_expense_product
#: model:product.template,uom_name:hr_expense.mileage_expense_product_product_template
msgid "km"
msgstr "km"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "to be reimbursed"
msgstr "Pour remboursement"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "to report"
msgstr "A rapporter"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "under validation"
msgstr "En cours de validation"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "use OCR to fill data from a picture of the bill"
msgstr ""
"utiliser l'OCR pour remplir les données à partir d'une image de la facture"
