<?xml version="1.0"?>
<odoo>

    <record id="sukuk_management_view_tree" model="ir.ui.view">
        <field name="name">sukuk.management.tree</field>
        <field name="model">sukuk.management</field>
        <field name="arch" type="xml">
            <tree>
                <field name="state" widget="badge" decoration-info="state == 'draft'"
                       decoration-warning="state == 'cancel'"
                       decoration-success="state == 'confirmed'"/>
                <field name="account_no_id"/>
                <field name="bank_id"/>
                <field name="branch_id"/>
                <field name="suke_book_required"/>
                <field name="order_date"/>
                <field name="employee_id"/>
                <field name="person_signature"/>
                <field name="receive_date"/>
            </tree>
        </field>
    </record>

    <record id="sukuk_management_view_form" model="ir.ui.view">
        <field name="name">sukuk.management.form</field>
        <field name="model">sukuk.management</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="make_confirmed" type="object" string="تأكيد" class="oe_highlight"
                            attrs="{'invisible':['|',('state','in',('confirmed','cancel')),('person_signature_visible','=',False)]}"/>
                    <button name="make_draft" type="object" string="ارجاع كمسودة"
                            attrs="{'invisible':['|',('state','in',('confirmed','draft')),('person_signature_visible','=',False)]}"/>
                    <button name="make_cancel" type="object" string="الغاء"
                            attrs="{'invisible':['|',('state','=','cancel'),('person_signature_visible','=',False)]}"/>
                    <field name="state" widget="statusbar"/>

                </header>
                <sheet>
                    <div class="oe_title">
                        <h2>
                            <div class="oe_inline">
                                <field name="bank_id" options="{'no_create': True, 'no_create_edit':True}" placeholder="اسم المصرف" class="oe_inline"/>
                                /
                                <field name="branch_id" options="{'no_create': True, 'no_create_edit':True}" placeholder="اسم الفرع" class="oe_inline"/>
                            </div>

                        </h2>
                    </div>
                    <group>
<!--                        <group string="معلومات الصك">-->
<!--&lt;!&ndash;                            <field name="suke_book_number"/>&ndash;&gt;-->
<!--&lt;!&ndash;                            <field name="suke_book_required"/>&ndash;&gt;-->
<!--                        </group>-->
                        <group string="معلومات الطلب">
                            <field name="indicative_number"/>
                            <field name="order_date"/>
                            <field name="employee_id" options="{'no_create': True, 'no_create_edit':True}"/>
                            <field name="person_signature_visible" invisible="1"/>
                        </group>
                        <group>
                            <field name="account_no_id"/>
                            <field name="suke_book_required"/>
                            <field name="person_signature" options="{'no_create_edit': True, 'no_quick_create': True,'no_open':True}"/>
                            <field name="receive_date"/>
                        </group>
                        <group>
                            <field name="attached_file"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="دفاتر الصكوك">
                            <field name="suke_item_ids" readonly="0">
                                <tree editable="top" create="false" delete="false">
                                    <field name="suke_book_number" readonly="0"/>
                                    <field name="bank_id" invisible="1" required="1" force_save="1"/>
                                    <field name="branch_id" invisible="1" required="1" force_save="1"/>
                                    <field name="account_no_id" invisible="1" required="1" force_save="1"/>
                                    <field name="number_of_page_demand" readonly="0"/>
                                    <field name="number_of_page_received" readonly="0"/>
                                    <field name="serial_no_from" readonly="0"/>
                                    <field name="serial_no_to" readonly="0"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>


    <record id="action_sukuk_management_view" model="ir.actions.act_window">
        <field name="name">ادارة الصكوك</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">sukuk.management</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                There is nothing yet!
            </p>
        </field>
    </record>


    <menuitem id="sukuk_management_main_menu"
              name="ادارة الصكوك"
              parent="account.menu_finance_entries"
              sequence="80"/>

    <menuitem id="sukuk_management_order_menu"
              name="طلب صكوك"
              parent="sukuk_management_main_menu"
              action="action_sukuk_management_view"
              sequence="1"/>


</odoo>
