<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_lt" model="res.partner">
        <field name="name">LT Company</field>
        <field name="vat">LT949170611</field>
        <field name="street">Išvykimas</field>
        <field name="city">Vilnius</field>
        <field name="country_id" ref="base.lt"/>
        
        <field name="zip">02188</field>
        <field name="phone">+370 612 34567</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.ltexample.com</field>
    </record>

    <record id="demo_company_lt" model="res.company">
        <field name="name">LT Company</field>
        <field name="partner_id" ref="partner_demo_company_lt"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_lt')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_lt.demo_company_lt'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_lt.account_chart_template_lithuania')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_lt.demo_company_lt')"/>
    </function>
</odoo>
