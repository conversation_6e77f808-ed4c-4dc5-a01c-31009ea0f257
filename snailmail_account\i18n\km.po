# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * snailmail_account
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON>g <<EMAIL>>, 2018
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-11-05 09:29+0000\n"
"PO-Revision-Date: 2018-08-24 09:25+0000\n"
"Last-Translator: <PERSON><PERSON>nn Seang <<EMAIL>>, 2018\n"
"Language-Team: Khmer (https://www.transifex.com/odoo/teams/41243/km/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: km\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: snailmail_account
#: model_terms:ir.ui.view,arch_db:snailmail_account.account_invoice_send_inherit_account_wizard_form
msgid "<span> to: </span>"
msgstr ""

#. module: snailmail_account
#: model:ir.model,name:snailmail_account.model_account_invoice_send
msgid "Account Invoice Send"
msgstr ""

#. module: snailmail_account
#: model:ir.model.fields,help:snailmail_account.field_account_invoice_send__snailmail_is_letter
msgid ""
"Allows to send the document by snail mail (coventional posting delivery "
"service)"
msgstr ""

#. module: snailmail_account
#: model:ir.model,name:snailmail_account.model_res_company
msgid "Companies"
msgstr "ក្រុមហ៊ុន"

#. module: snailmail_account
#: model:ir.model,name:snailmail_account.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_account_invoice_send__currency_id
msgid "Currency"
msgstr "រូបិយវត្ថុ"

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_account_invoice_send__letter_ids
msgid "Letter"
msgstr ""

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_account_invoice_send__partner_id
msgid "Partner"
msgstr "ដៃគូ"

#. module: snailmail_account
#: model_terms:ir.ui.view,arch_db:snailmail_account.res_config_settings_view_form
msgid "Print In Color"
msgstr ""

#. module: snailmail_account
#: model_terms:ir.ui.view,arch_db:snailmail_account.res_config_settings_view_form
msgid "Print on Both Sides"
msgstr ""

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_res_company__invoice_is_snailmail
msgid "Send by Letter by default"
msgstr ""

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_account_invoice_send__snailmail_is_letter
#: model:ir.model.fields,field_description:snailmail_account.field_res_config_settings__invoice_is_snailmail
msgid "Send by Post"
msgstr ""

#. module: snailmail_account
#: model:ir.model.fields,field_description:snailmail_account.field_account_invoice_send__snailmail_cost
msgid "Stamp(s)"
msgstr ""
