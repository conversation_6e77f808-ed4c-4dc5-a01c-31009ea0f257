# -*- coding: utf-8 -*-
from odoo import tools, api, fields, models
from odoo.exceptions import ValidationError
from datetime import datetime, timedelta


class HrEmployeePrivate(models.Model):

    _inherit  = "hr.employee"
    #start_work = fields.Char(string='Start Work-Day')
    # end_work = fields.Char(string='End Work-Day')
      
        
    passport_issue_location = fields.Char(string='Passport issue location', tracking=True)
    passport_issue_date = fields.Date(string="Passport issue date", tracking=True)
    passport_end_date = fields.Date(string="Passport end date", tracking=True)
    referral_date = fields.Date(string="Referral date", tracking=True)
    national_number = fields.Char(string="National Number", tracking=True)
    residence_place = fields.Char(string="Residence Place", tracking=True)
    city = fields.Char(string="City", tracking=True)
    neighborhood = fields.Char(string="Neighborhood", tracking=True)
    
    street_name = fields.Char(string="Street Name", tracking=True)
    closest_point = fields.Char(string="Closest point", tracking=True)
    
    connected_with_comp = fields.Selection(selection=[("1", "عقد اختبار"), ("2", "عقد متعاون"), ("3", "تعيين"),
                                            ("4", "معار"), ("5", "نذب"), ("6", "نقل"), ], string="Type of Contract", default="", tracking=True)
    
    join_date = fields.Date(string="Join Date", tracking=True)
    hiring_date = fields.Date(string="Hiring Date", tracking=True)
    
    
    class_id = fields.Selection(selection=[("LY-1-1", "الدرجة الاولى - المربوط الاول"), 
("LY-1-2", "الدرجة الاولى - المربوط الثاني"), 
("LY-1-3", "الدرجة الاولى - المربوط الثالث"), 
("LY-1-4", "الدرجة الاولى - المربوط الرابع"), 
("LY-1-5", "الدرجة الاولى - المربوط الخامس"), 
("LY-1-6", "الدرجة الاولى - المربوط السادس"), 
("LY-1-7", "الدرجة الاولى - المربوط السابع"), 
("LY-1-8", "الدرجة الاولى - المربوط الثامن"), 
("LY-1-9", "الدرجة الاولى - المربوط التاسع"), 
("LY-1-10", "الدرجة الاولى - المربوط العاشر"), 
("LY-2-1", "الدرجة الثانية - المربوط الاول"), 
("LY-2-2", "الدرجة الثانية - المربوط الثاني"), 
("LY-2-3", "الدرجة الثانية - المربوط الثالث"), 
("LY-2-4", "الدرجة الثانية - المربوط الرابع"), 
("LY-2-5", "الدرجة الثانية - المربوط الخامس"), 
("LY-2-6", "الدرجة الثانية - المربوط السادس"), 
("LY-2-7", "الدرجة الثانية - المربوط السابع"), 
("LY-2-8", "الدرجة الثانية - المربوط الثامن"), 
("LY-2-9", "الدرجة الثانية - المربوط التاسع"), 
("LY-2-10", "الدرجة الثانية - المربوط العاشر"), 
("LY-3-1", "الدرجة الثالثة - المربوط الاول"), 
("LY-3-2", "الدرجة الثالثة - المربوط الثاني"), 
("LY-3-3", "الدرجة الثالثة - المربوط الثالث"), 
("LY-3-4", "الدرجة الثالثة - المربوط الرابع"), 
("LY-3-5", "الدرجة الثالثة - المربوط الخامس"), 
("LY-3-6", "الدرجة الثالثة - المربوط السادس"), 
("LY-3-7", "الدرجة الثالثة - المربوط السابع"), 
("LY-3-8", "الدرجة الثالثة - المربوط الثامن"), 
("LY-3-9", "الدرجة الثالثة - المربوط التاسع"), 
("LY-3-10", "الدرجة الثالثة - المربوط العاشر"), 
("LY-4-1", "الدرجة الرابعة - المربوط الاول"), 
("LY-4-2", "الدرجة الرابعة - المربوط الثاني"), 
("LY-4-3", "الدرجة الرابعة - المربوط الثالث"), 
("LY-4-4", "الدرجة الرابعة - المربوط الرابع"), 
("LY-4-5", "الدرجة الرابعة - المربوط الخامس"), 
("LY-4-6", "الدرجة الرابعة - المربوط السادس"), 
("LY-4-7", "الدرجة الرابعة - المربوط السابع"), 
("LY-4-8", "الدرجة الرابعة - المربوط الثامن"), 
("LY-4-9", "الدرجة الرابعة - المربوط التاسع"), 
("LY-4-10", "الدرجة الرابعة - المربوط العاشر"), 
("LY-5-1", "الدرجة الخامسة - المربوط الاول"), 
("LY-5-2", "الدرجة الخامسة - المربوط الثاني"), 
("LY-5-3", "الدرجة الخامسة - المربوط الثالث"), 
("LY-5-4", "الدرجة الخامسة - المربوط الرابع"), 
("LY-5-5", "الدرجة الخامسة - المربوط الخامس"), 
("LY-5-6", "الدرجة الخامسة - المربوط السادس"), 
("LY-5-7", "الدرجة الخامسة - المربوط السابع"), 
("LY-5-8", "الدرجة الخامسة - المربوط الثامن"), 
("LY-5-9", "الدرجة الخامسة - المربوط التاسع"), 
("LY-5-10", "الدرجة الخامسة - المربوط العاشر"), 
("LY-6-1", "الدرجة السادسة - المربوط الاول"), 
("LY-6-2", "الدرجة السادسة - المربوط الثاني"), 
("LY-6-3", "الدرجة السادسة - المربوط الثالث"), 
("LY-6-4", "الدرجة السادسة - المربوط الرابع"), 
("LY-6-5", "الدرجة السادسة - المربوط الخامس"), 
("LY-6-6", "الدرجة السادسة - المربوط السادس"), 
("LY-6-7", "الدرجة السادسة - المربوط السابع"), 
("LY-6-8", "الدرجة السادسة - المربوط الثامن"), 
("LY-6-9", "الدرجة السادسة - المربوط التاسع"), 
("LY-6-10", "الدرجة السادسة - المربوط العاشر"), 
("LY-7-1", "الدرجة السابعة - المربوط الاول"), 
("LY-7-2", "الدرجة السابعة - المربوط الثاني"), 
("LY-7-3", "الدرجة السابعة - المربوط الثالث"), 
("LY-7-4", "الدرجة السابعة - المربوط الرابع"), 
("LY-7-5", "الدرجة السابعة - المربوط الخامس"), 
("LY-7-6", "الدرجة السابعة - المربوط السادس"), 
("LY-7-7", "الدرجة السابعة - المربوط السابع"), 
("LY-7-8", "الدرجة السابعة - المربوط الثامن"), 
("LY-7-9", "الدرجة السابعة - المربوط التاسع"), 
("LY-7-10", "الدرجة السابعة - المربوط العاشر"), 
("LY-8-1", "الدرجة الثامنة - المربوط الاول"), 
("LY-8-2", "الدرجة الثامنة - المربوط الثاني"), 
("LY-8-3", "الدرجة الثامنة - المربوط الثالث"), 
("LY-8-4", "الدرجة الثامنة - المربوط الرابع"), 
("LY-8-5", "الدرجة الثامنة - المربوط الخامس"), 
("LY-8-6", "الدرجة الثامنة - المربوط السادس"), 
("LY-8-7", "الدرجة الثامنة - المربوط السابع"), 
("LY-8-8", "الدرجة الثامنة - المربوط الثامن"), 
("LY-8-9", "الدرجة الثامنة - المربوط التاسع"), 
("LY-8-10", "الدرجة الثامنة - المربوط العاشر"), 
("LY-9-1", "الدرجة التاسعة - المربوط الاول"), 
("LY-9-2", "الدرجة التاسعة - المربوط الثاني"), 
("LY-9-3", "الدرجة التاسعة - المربوط الثالث"), 
("LY-9-4", "الدرجة التاسعة - المربوط الرابع"), 
("LY-9-5", "الدرجة التاسعة - المربوط الخامس"), 
("LY-9-6", "الدرجة التاسعة - المربوط السادس"), 
("LY-9-7", "الدرجة التاسعة - المربوط السابع"), 
("LY-9-8", "الدرجة التاسعة - المربوط الثامن"), 
("LY-9-9", "الدرجة التاسعة - المربوط التاسع"), 
("LY-9-10", "الدرجة التاسعة - المربوط العاشر"), 
("LY-10-1", "الدرجة العاشرة - المربوط الاول"), 
("LY-10-2", "الدرجة العاشرة - المربوط الثاني"), 
("LY-10-3", "الدرجة العاشرة - المربوط الثالث"), 
("LY-10-4", "الدرجة العاشرة - المربوط الرابع"), 
("LY-10-5", "الدرجة العاشرة - المربوط الخامس"), 
("LY-10-6", "الدرجة العاشرة - المربوط السادس"), 
("LY-10-7", "الدرجة العاشرة - المربوط السابع"), 
("LY-10-8", "الدرجة العاشرة - المربوط الثامن"), 
("LY-10-9", "الدرجة العاشرة - المربوط التاسع"), 
("LY-10-10", "الدرجة العاشرة - المربوط العاشر"), 
("LY-11-1", "الدرجة الحادى عشر - المربوط الاول"), 
("LY-11-2", "الدرجة الحادى عشر - المربوط الثاني"), 
("LY-11-3", "الدرجة الحادى عشر - المربوط الثالث"), 
("LY-11-4", "الدرجة الحادى عشر - المربوط الرابع"), 
("LY-11-5", "الدرجة الحادى عشر - المربوط الخامس"), 
("LY-11-6", "الدرجة الحادى عشر - المربوط السادس"), 
("LY-11-7", "الدرجة الحادى عشر - المربوط السابع"), 
("LY-11-8", "الدرجة الحادى عشر - المربوط الثامن"), 
("LY-11-9", "الدرجة الحادى عشر - المربوط التاسع"), 
("LY-11-10", "الدرجة الحادى عشر - المربوط العاشر"), 
("LY-12-1", "الدرجة التانية عشر - المربوط الاول"), 
("LY-12-2", "الدرجة التانية عشر - المربوط الثاني"), 
("LY-12-3", "الدرجة التانية عشر - المربوط الثالث"), 
("LY-12-4", "الدرجة التانية عشر - المربوط الرابع"), 
("LY-12-5", "الدرجة التانية عشر - المربوط الخامس"), 
("LY-12-6", "الدرجة التانية عشر - المربوط السادس"), 
("LY-12-7", "الدرجة التانية عشر - المربوط السابع"), 
("LY-12-8", "الدرجة التانية عشر - المربوط الثامن"), 
("LY-12-9", "الدرجة التانية عشر - المربوط التاسع"), 
("LY-12-10", "الدرجة التانية عشر - المربوط العاشر"), 
("LY-13-1", "الدرجة الثالثة عشر - المربوط الاول"), 
("LY-13-2", "الدرجة الثالثة عشر - المربوط الثاني"), 
("LY-13-3", "الدرجة الثالثة عشر - المربوط الثالث"), 
("LY-13-4", "الدرجة الثالثة عشر - المربوط الرابع"), 
("LY-13-5", "الدرجة الثالثة عشر - المربوط الخامس"), 
("LY-13-6", "الدرجة الثالثة عشر - المربوط السادس"), 
("LY-13-7", "الدرجة الثالثة عشر - المربوط السابع"), 
("LY-13-8", "الدرجة الثالثة عشر - المربوط الثامن"), 
("LY-13-9", "الدرجة الثالثة عشر - المربوط التاسع"), 
("LY-13-10", "الدرجة الثالثة عشر - المربوط العاشر"), 
("LY-14-1", "الدرجة الرابعة عشر - المربوط الاول"), 
("LY-14-2", "الدرجة الرابعة عشر - المربوط الثاني"), 
("LY-14-3", "الدرجة الرابعة عشر - المربوط الثالث"), 
("LY-14-4", "الدرجة الرابعة عشر - المربوط الرابع"), 
("LY-14-5", "الدرجة الرابعة عشر - المربوط الخامس"), 
("LY-14-6", "الدرجة الرابعة عشر - المربوط السادس"), 
("LY-14-7", "الدرجة الرابعة عشر - المربوط السابع"), 
("LY-14-8", "الدرجة الرابعة عشر - المربوط الثامن"), 
("LY-14-9", "الدرجة الرابعة عشر - المربوط التاسع"), 
("LY-14-10", "الدرجة الرابعة عشر - المربوط العاشر"), 
("LY-15-1", "الدرجة الخامسة عشر - المربوط الاول"), 
("LY-15-2", "الدرجة الخامسة عشر - المربوط الثاني"), 
("LY-15-3", "الدرجة الخامسة عشر - المربوط الثالث"), 
("LY-15-4", "الدرجة الخامسة عشر - المربوط الرابع"), 
("LY-15-5", "الدرجة الخامسة عشر - المربوط الخامس"), 
("LY-15-6", "الدرجة الخامسة عشر - المربوط السادس"), 
("LY-15-7", "الدرجة الخامسة عشر - المربوط السابع"), 
("LY-15-8", "الدرجة الخامسة عشر - المربوط الثامن"), 
("LY-15-9", "الدرجة الخامسة عشر - المربوط التاسع"), 
("LY-15-10", "الدرجة الخامسة عشر - المربوط العاشر"), 
("LY-16-1", "الدرجة السادسة عشر - المربوط الاول"), 
("LY-16-2", "الدرجة السادسة عشر - المربوط الثاني"), 
("LY-16-3", "الدرجة السادسة عشر - المربوط الثالث"), 
("LY-16-4", "الدرجة السادسة عشر - المربوط الرابع"), 
("LY-16-5", "الدرجة السادسة عشر - المربوط الخامس"), 
("LY-16-6", "الدرجة السادسة عشر - المربوط السادس"), 
("LY-16-7", "الدرجة السادسة عشر - المربوط السابع"), 
("LY-16-8", "الدرجة السادسة عشر - المربوط الثامن"), 
("LY-16-9", "الدرجة السادسة عشر - المربوط التاسع"), 
("LY-16-10", "الدرجة السادسة عشر - المربوط العاشر"), ], string="Class ID", default="", tracking=True)

    selection_type = fields.Selection(
        selection=[
            ("written", "كتابي"),
            ("non_written", "غير كتابي"),
        ],
        string="نوع التعيين",
        default="",
        tracking=True,
    )
    @api.onchange('connected_with_comp')
    def _onchange_connected_with_comp(self):
     if self.connected_with_comp != "3":  # تحقق إذا لم يكن "تعيين"
      self.class_id = False  # إعادة ضبط القيمة
      self.selection_type = False  # إعادة ضبط القيمة  
     
    english_name = fields.Char(string="English Name", tracking=True)
    int_id = fields.Char(string="Employee ID", tracking=True)   
 

    



    bloodtype = fields.Selection(selection=[("1", "A+"), ("2", "A-"), ("3", "B+"),
                                            ("4", "B-"), ("5", "AB+"), ("6", "AB-"), ("7", "O+"),
                                            ("8", "O-"), ], string="Blood type", default="", tracking=True)
    gender = fields.Selection([
        ('male', 'Male'),
        ('female', 'Female')
    ], tracking=True)

    ############ emergencies
    name_emergency1 = fields.Char(string="Name of closest relative 1", tracking=True)
    phone_emergency1 = fields.Char(string="Phone of closest relative 1", tracking=True)
    relation_emergency1 = fields.Char(string="Relation of closest relative 1", tracking=True)
    address_emergency1 = fields.Char(string="Address of closest relative 1", tracking=True)
    name_emergency2 = fields.Char(string="Name of closest relative 2", tracking=True)
    phone_emergency2 = fields.Char(string="Phone of closest relative 2", tracking=True)
    relation_emergency2 = fields.Char(string="Relation of closest relative 2", tracking=True)
    address_emergency2 = fields.Char(string="Address of closest relative 2", tracking=True)
    ###########################
    health_certificate = fields.Char(string="Health certificate", tracking=True)
    health_certificate_date = fields.Date(string="Health certificate date", tracking=True)
    health_certificate_expiry = fields.Date(string="Health certificate expiry", tracking=True)
    criminal_certificate = fields.Char(string="Criminal certificate", tracking=True)
    criminal_certificate_date = fields.Date(string="Criminal certificate date", tracking=True)
    criminal_certificate_expiry = fields.Date(string="Criminal certificate expiry", tracking=True)




    ######### education
    study_field_edited = fields.Char(string='Study field', tracking=True)
    certificate = fields.Selection([
        ('school_degree', 'شهادة اعدادية'),
        ('diploma', 'شهادة ثانوية'),
        ('intermediate_diploma', 'دبلوم متوسط'),
        ('high_deploma', 'دبلوم عالي'),
        ('bachelor', 'بكالوريوس'),
        ('licence', 'ليسانس'),
        ('master', 'ماجستير'),
        ('doctor', 'دكتوراه'),
        ('other', 'أخرى'),
        ('non', 'لايوجد'),
    ], default='', groups="hr.group_hr_user", tracking=True)

    certificate_issue_date = fields.Date(string='Certificate issue date', tracking=True)
    certificate_issue_place = fields.Char(string='Certificate issue place', tracking=True)

    ##################
    #### Documents
    employment_requist_document = fields.Binary(string="مباشرة عمل")
    id_document = fields.Binary(string="صورة من جواز السفر")
    #passport_document = fields.Binary(string="شهادة الميلاد")
    #nda = fields.Binary(string="نموذج عدم افشاء وعدم منافسة")
    #blood_type_doc = fields.Binary(string="فصيلة الدم")
    #residence_doc = fields.Binary(string="شهادة اقامة")
    
    #national_number_document = fields.Binary(string="الرقم الوطني")
    graduation_certificate_document = fields.Binary(string="صورة شهادة التخرج")
    family_state_certificate = fields.Binary(string="شهادة الوضع العائلي")
    #cv_document = fields.Binary(string="سيرة ذاتية (C.V)")
    #previos_experiance_document = fields.Binary(string="صورة من الخبرة السابقة")
    #trail_contract_document = fields.Binary(string="عقد عمل فترة تجريبية موقع من الطرفين")
    
    #health_certificate_document = fields.Binary(string="شهادة الحالة الطبية")
    #bank_account_document = fields.Binary(string="صورة من رقم الحساب المصرفي")
    #computer_logs_document = fields.Text(string="Computer log-on")
    #job_title_document = fields.Binary(string="الوصف الوظيفي")
    #attendance_finger_print_document = fields.Boolean(string="بصمة الحضور والانصراف")
    #coworkers_demonstration_document = fields.Boolean(string="التعريف بباقي الزملاء")
    annual_contract_document = fields.Binary(string="عقد استخدام")
    hiring_decision = fields.Binary(string="قرار تعين")
    last_financial_allowance = fields.Binary(string="اخر علاوة مالية")
    last_annual_dromotion = fields.Binary(string="اخر ترقية سنوية")
    last_secondment_decision = fields.Binary(string="قرار أخر إعارة")
    non_duality_certificate_document = fields.Binary(string="عدم الازدواجية")
    resignation_letter_document = fields.Binary(string="استقالة")
    criminal_certificate_document = fields.Binary(string="شهادة الحالة الجنائية")
    personal_photo_document = fields.Binary(string="صورة شحصية")
    non_employment_affidavit_document = fields.Binary(string="اقرا بعدم مزاولة عمل اخر")
    kye_document = fields.Binary(string="نمودج اعرف موظفك")
    
    #drive_lisence_document = fields.Binary(string="صورة رخصة قيادة للمخولين بقيادة سيارات الشركة")

    #company_car_lisence_document = fields.Binary(string="صورة تصريح لقيادة سيارة الشركة")

    grants_ids = fields.One2many('account.asset.asset','granted_to_id',readonly=True ,string="العهد المستلمة")




    @api.constrains('start_work')
    def check_work_hours(self):
        if self.start_work:
            ll = str(self.start_work).split(':')
            if (len(ll) == 3):
                if (len(ll[0]) == 2) and (len(ll[1]) == 2)and (len(ll[2]) == 2):
                    return
                else:
                    raise ValidationError('Please Enter Correct Start Work-Day')
            else:
                raise ValidationError('Please Enter Correct Start Work-Day')

    @api.constrains('end_work')
    def check_work_hours(self):
        if self.end_work:
            ll = str(self.end_work).split(':')
            if (len(ll) == 3):

                if (len(ll[0]) == 2) and (len(ll[1]) == 2)and (len(ll[2]) == 2):
                    return
                else:
                    raise ValidationError('Please Enter Correct End Work-Day')
            else:
                raise ValidationError('Please Enter Correct End Work-Day')



    def generate_random_barcode(self):
        yy = str(datetime.now().year)[-2:]
        self.env.cr.execute(
            "select max(barcode) from hr_employee where barcode like '" + yy + "%' and length(barcode) = 7;")
        try:
            default_barcode = yy + '-' + '{:0>4d}'.format(int(str(self.env.cr.fetchone()[0]).split('-')[1]) + 1)
        except:
            default_barcode = yy + '-' + '0001'
        self.barcode=default_barcode

    # ############# Image resize
     #@api.model
     #def create(self, vals):
         #pics_field = ['employmnt_requist_document','id_document','passport_document','personal_photo_document','national_number_document','graduation_certificate_document ','cv_document','previos_experiance_document','trail_contract_document','criminal_certificate_document ','helth_certificate_document','bank_account_document','computer_logs_document','job_title_document','attendance_finger_print_document','cowarkers_demonstration_document','annual_contract_document','drive_lisence_document']
         #for elem in pics_field:
             #if elem in vals:
                 #image = tools.ImageProcess(vals[elem])
                 #resize uploaded image into 250 X 250
                 #resize_image = image.resize(1024, 1024)
                 #resize_image_b64 = resize_image.image_base64()
                 #vals[elem] = resize_image_b64
         #obj = super(HrEmployeePrivate, self).create(vals)
         #return obj

    @api.constrains('barcode')
    def _check_barcode_id(self):
        if not self.barcode:
            pass
        elif len(str(self.barcode)) != 7:
            raise ValidationError('please Enter a valid barcode number (YY-XXXX)')
        elif len(str(self.barcode).split('-')[0]) !=2:
            raise ValidationError('please Enter a valid barcode number (YY-XXXX)')
        elif len(str(self.barcode).split('-')[1]) !=4:
            raise ValidationError('please Enter a valid barcode number (YY-XXXX)')

    @api.model
    def cron_health_certificate_expiry_notification(self):
        employees = self.env['hr.employee'].search([])
        for employee in employees:
            date_end = datetime.strptime(employee.health_certificate_expiry.strftime("%Y-%m-%d"), "%Y-%m-%d")
            if date_end < datetime.today():
                _summary = "تنبيه بإنتهاء مدة الشهادة الصحية"
                _note = "اسم الموظف ({}).".format(employee.name)
                hr_admins = self.env.ref("hr.group_hr_manager").users
                for user_id in hr_admins:
                    employee.activity_schedule('hr_employees_masarat.mail_activity_health_expiry',
                                            user_id=user_id.id,
                                            summary=_summary,
                                            note=_note,
                                            date_deadline=datetime.today())


class RespartnerBank(models.Model):
    _inherit = 'res.partner.bank'

    bank_branch = fields.Many2one('bank.branch',string="أسم الفرع",domain="[('bank_id','=',bank_id)]")
    acc_holder_NN = fields.Char(string='الرقم الوطني لصاحب الحساب')

class BankBranch(models.Model):
    _name = 'bank.branch'
    _rec_name = 'name'

    name = fields.Char(string="أسم الفرع")
    bank_id = fields.Many2one('res.bank',"Bank", required=True)


class StudyField(models.Model):
    _name='study.field'

    name = fields.Char(string="مجال الدراسة")
    description = fields.Text('Description')

