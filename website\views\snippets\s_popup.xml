<?xml version="1.0" encoding="utf-8"?>
<odoo>

<template id="s_popup" name="Popup">
    <div class="s_popup o_snippet_invisible" data-vcss="001">
        <div class="modal fade s_popup_middle" style="background-color: var(--black-50) !important;" data-show-after="5000" data-display="afterDelay" data-consents-duration="7" data-focus="false" data-backdrop="false" tabindex="-1" role="dialog">
            <div class="modal-dialog d-flex">
                <div class="modal-content oe_structure">
                    <div class="s_popup_close js_close_popup o_we_no_overlay o_not_editable" aria-label="Close">&#215;</div>
                    <section class="s_banner oe_img_bg o_bg_img_center pt96 pb96" data-snippet="s_banner" style="background-image: url('/web/image/website.s_popup_default_image');">
                        <div class="container">
                            <div class="row s_nb_column_fixed">
                                <div class="col-lg-10 offset-lg-1 text-center o_cc o_cc1 jumbotron pt48 pb48">
                                    <h2><font style="font-size: 62px;">Win $20</font></h2>
                                    <p class="lead">Check out now and get $20 off your first order.</p>
                                    <a href="#" class="btn btn-primary mb-2">New customer</a>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </div>
        </div>
    </div>
</template>

<template id="s_popup_options" inherit_id="website.snippet_options">
    <xpath expr="." position="inside">
        <t t-set="base_popup_options">
            <we-select string="Position" data-dependencies="layout_popup_opt"> <!-- When cookie configuration only display this for popup mode -->
                <we-button data-select-class="s_popup_top" data-name="position_top">Top</we-button>
                <we-button data-select-class="s_popup_middle" data-name="position_middle">Middle</we-button>
                <we-button data-select-class="s_popup_bottom" data-name="position_bottom">Bottom</we-button>
            </we-select>
            <we-select string="Size" data-apply-to=".modal-dialog" data-name="s_popup_size_opt">
                <we-button data-select-class="modal-sm">Small</we-button>
                <we-button data-select-class="" data-name="s_popup_size_md">Medium</we-button>
                <we-button data-select-class="modal-lg">Large</we-button>
                <we-button data-select-class="modal-xl">Extra Large</we-button>
                <we-button data-select-class="s_popup_size_full" data-name="s_popup_size_full">Full</we-button>
            </we-select>
            <we-row string="Backdrop">
                <we-checkbox data-name="popup_backdrop_opt" data-select-class="s_popup_no_backdrop|" data-set-backdrop="true" data-no-preview="true"/>
                <we-colorpicker data-dependencies="popup_backdrop_opt" data-select-style="true" data-css-property="background-color" data-color-prefix="bg-" data-css-compatible="true"/>
            </we-row>
        </t>
        <t t-set="extra_popup_options">
            <we-colorpicker string="Close Button Color" data-select-style="true" data-css-property="color" data-color-prefix="text-" data-apply-to=".s_popup_close"/>
            <we-select string="Display" data-attribute-name="display" data-attribute-default-value="always">
                <we-button data-select-data-attribute="afterDelay" data-name="show_delay">Delay</we-button>
                <we-button data-select-data-attribute="mouseExit">On Exit</we-button>
            </we-select>
            <we-input string="&#8985; Delay" title="Automatically opens the pop-up if the user stays on a page longer than the specified time." data-select-data-attribute="" data-attribute-name="showAfter" data-unit="s" data-save-unit="ms" data-dependencies="show_delay"/>
            <t t-set="unit_popup_duration">days</t>
            <we-input string="Hide For" title="Once the user closes the popup, it won't be shown again for that period of time." t-attf-data-select-data-attribute="7#{unit_popup_duration}" data-attribute-name="consentsDuration" t-att-data-unit="unit_popup_duration"/>
            <we-select string="Show on" data-no-preview="true">
                <we-button data-move-block="moveToBody">This page</we-button>
                <we-button data-move-block="moveToFooter">All pages</we-button>
            </we-select>
        </t>
        <div data-js="SnippetPopup" data-selector=".s_popup" data-exclude="#website_cookies_bar" data-target=".modal">
            <t t-out="base_popup_options"/>
            <t t-out="extra_popup_options"/>
        </div>
        <div data-js="SnippetPopup" data-selector=".s_popup#website_cookies_bar" data-target=".modal">
            <t t-out="base_popup_options"/>
        </div>
    </xpath>
</template>

<record id="website.s_popup_000_scss" model="ir.asset">
    <field name="name">Popup 000 SCSS</field>
    <field name="bundle">web.assets_frontend</field>
    <field name="path">website/static/src/snippets/s_popup/000.scss</field>
    <field name="active" eval="False"/>
</record>

<record id="website.s_popup_000_js" model="ir.asset">
    <field name="name">Popup 000 JS</field>
    <field name="bundle">web.assets_frontend</field>
    <field name="path">website/static/src/snippets/s_popup/000.js</field>
</record>

<record id="website.s_popup_001_scss" model="ir.asset">
    <field name="name">Popup 001 SCSS</field>
    <field name="bundle">web.assets_frontend</field>
    <field name="path">website/static/src/snippets/s_popup/001.scss</field>
</record>

</odoo>
