﻿<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Salgsmoms (VAT) -->
    <!-- DK salgsmoms (taxes to set on Sales in DK) -->
    <record id="tax110" model="account.tax.template">
        <field name="chart_template_id" ref="dk_chart_template"/>
        <field name="sequence">110</field>
        <field name="name">Salgsmoms 25%, varer</field>
        <field name="description">25%</field>
        <field name="amount">25</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('a8720'),
                'plus_report_line_ids': [ref('l10n_dk.account_tax_report_line_sales_tax')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('a8720'),
                'minus_report_line_ids': [ref('l10n_dk.account_tax_report_line_sales_tax')],
            }),
        ]"/>
    </record>
    <record id="tax120" model="account.tax.template">
        <field name="chart_template_id" ref="dk_chart_template"/>
        <field name="sequence">120</field>
        <field name="name">Salgsmoms 25%, ydelser</field>
        <field name="description">25%</field>
        <field name="amount">25</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('a8720'),
                'plus_report_line_ids': [ref('l10n_dk.account_tax_report_line_sales_tax')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('a8720'),
                'minus_report_line_ids': [ref('l10n_dk.account_tax_report_line_sales_tax')],
            }),
        ]"/>
    </record>

    <!-- EU salgsmoms (taxes to set on Sales in EU when outside of DK) -->
    <record id="tax210" model="account.tax.template">
        <field name="chart_template_id" ref="dk_chart_template"/>
        <field name="sequence">210</field>
        <field name="name">EU Varesalg (Virksomheder) - momsfritaget</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_dk.account_tax_report_line_section_b_product_eu')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_dk.account_tax_report_line_section_b_product_eu')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <record id="tax220" model="account.tax.template">
        <field name="chart_template_id" ref="dk_chart_template"/>
        <field name="sequence">220</field>
        <field name="name">EU Ydelsessalg (Virksomheder) - momsfritaget</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_dk.account_tax_report_line_section_b_services')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_dk.account_tax_report_line_section_b_services')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <!-- 3. Lande salgsmoms (taxes to set on Sales outside of EU) -->
    <record id="tax310" model="account.tax.template">
        <field name="chart_template_id" ref="dk_chart_template"/>
        <field name="sequence">310</field>
        <field name="name">3. Land Salg Vare / Ydelser</field>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">sale</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_dk.account_tax_report_line_section_c')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_dk.account_tax_report_line_section_c')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <!-- Købsmoms (VAT on purchase) -->

    <!-- DK købsmoms (taxes to set on purchase inside DK) -->
    <!-- 2 different taxes for merchandises and services isn't required for local taxes but is required -->
    <!-- for EU related taxes and with fiscal position and we can't map one tax to 2 other taxes because it's  -->
    <!-- a one on one relation. Thus, we set 2 taxes locally. -->
    <record id="tax400" model="account.tax.template">
        <field name="chart_template_id" ref="dk_chart_template"/>
        <field name="sequence">400</field>
        <field name="name">Købsmoms 25%, varer</field>
        <field name="description">25%</field>
        <field name="amount">25</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('a8740'),
                'plus_report_line_ids': [ref('l10n_dk.account_tax_report_line_deduction_purchase_tax')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('a8740'),
                'minus_report_line_ids': [ref('l10n_dk.account_tax_report_line_deduction_purchase_tax')],
            }),
        ]"/>
    </record>
    <record id="tax410" model="account.tax.template">
        <field name="chart_template_id" ref="dk_chart_template"/>
        <field name="sequence">410</field>
        <field name="name">Købsmoms 25% indeholdt, varer</field>
        <field name="description">25% indeholdt</field>
        <field name="amount">25</field>
        <field name="amount_type">percent</field>
        <field name="price_include" eval="True"/>
        <field name="type_tax_use">purchase</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('a8740'),
                'plus_report_line_ids': [ref('l10n_dk.account_tax_report_line_deduction_purchase_tax')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('a8740'),
                'minus_report_line_ids': [ref('l10n_dk.account_tax_report_line_deduction_purchase_tax')],
            }),
        ]"/>
    </record>
    <record id="tax420" model="account.tax.template">
        <field name="chart_template_id" ref="dk_chart_template"/>
        <field name="sequence">420</field>
        <field name="name">Købsmoms 25%, ydelser</field>
        <field name="description">25%</field>
        <field name="amount">25</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('a8740'),
                'plus_report_line_ids': [ref('l10n_dk.account_tax_report_line_deduction_purchase_tax')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('a8740'),
                'minus_report_line_ids': [ref('l10n_dk.account_tax_report_line_deduction_purchase_tax')],
            }),
        ]"/>
    </record>
    <record id="tax425" model="account.tax.template">
        <field name="chart_template_id" ref="dk_chart_template"/>
        <field name="sequence">420</field>
        <field name="name">Købsmoms 25% indeholdt, ydelser</field>
        <field name="description">25%</field>
        <field name="amount">25</field>
        <field name="amount_type">percent</field>
        <field name="price_include" eval="True"/>
        <field name="type_tax_use">purchase</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('a8740'),
                'plus_report_line_ids': [ref('l10n_dk.account_tax_report_line_deduction_purchase_tax')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('a8740'),
                'minus_report_line_ids': [ref('l10n_dk.account_tax_report_line_deduction_purchase_tax')],
            }),
        ]"/>
    </record>
    <record id="tax430" model="account.tax.template">
        <field name="chart_template_id" ref="dk_chart_template"/>
        <field name="sequence">430</field>
        <field name="name">Køb omvendt betalingspligt</field>
        <field name="amount">25</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('a8740'),
                'plus_report_line_ids': [ref('l10n_dk.account_tax_report_line_deduction_purchase_tax')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('a8725'),
                'minus_report_line_ids': [ref('l10n_dk.account_tax_report_line_sales_tax')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('a8740'),
                'minus_report_line_ids': [ref('l10n_dk.account_tax_report_line_sales_tax')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('a8725'),
                'plus_report_line_ids': [ref('l10n_dk.account_tax_report_line_deduction_purchase_tax')],
            }),
        ]"/>
    </record>
    <record id="tax450" model="account.tax.template">
        <field name="chart_template_id" ref="dk_chart_template"/>
        <field name="sequence">450</field>
        <field name="name">Restaurationsmoms 6,25%, købsmoms</field>
        <field name="description">6,25%</field>
        <field name="amount">25</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 25,
                'repartition_type': 'tax',
                'account_id': ref('a8740'),
                'plus_report_line_ids': [ref('l10n_dk.account_tax_report_line_deduction_purchase_tax')],
            }),
            (0,0, {
                'factor_percent': 75,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 25,
                'repartition_type': 'tax',
                'account_id': ref('a8740'),
                'plus_report_line_ids': [ref('l10n_dk.account_tax_report_line_deduction_purchase_tax')],
            }),
            (0,0, {
                'factor_percent': 75,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <record id="tax460" model="account.tax.template">
        <field name="chart_template_id" ref="dk_chart_template"/>
        <field name="sequence">450</field>
        <field name="name">Restaurationsmoms indeholdt 6,25%, købsmoms</field>
        <field name="description">6,25%</field>
        <field name="amount">25</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="price_include" eval="True"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 25,
                'repartition_type': 'tax',
                'account_id': ref('a8740'),
                'plus_report_line_ids': [ref('l10n_dk.account_tax_report_line_deduction_purchase_tax')],
            }),
            (0,0, {
                'factor_percent': 75,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 25,
                'repartition_type': 'tax',
                'account_id': ref('a8740'),
                'plus_report_line_ids': [ref('l10n_dk.account_tax_report_line_deduction_purchase_tax')],
            }),
            (0,0, {
                'factor_percent': 75,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <!-- EU købsmoms (taxes to set on purchase in EU outside DK) -->
    <record id="tax510" model="account.tax.template">
        <field name="chart_template_id" ref="dk_chart_template"/>
        <field name="sequence">510</field>
        <field name="name">EU Varekøb</field>
        <field name="amount">25</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_dk.account_tax_report_line_section_a_products')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('a8740'),
                'plus_report_line_ids': [ref('l10n_dk.account_tax_report_line_deduction_purchase_tax')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('a8730'),
                'minus_report_line_ids': [ref('l10n_dk.account_tax_report_line_international_purchase_products')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_dk.account_tax_report_line_section_a_products')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('a8740'),
                'minus_report_line_ids': [ref('l10n_dk.account_tax_report_line_deduction_purchase_tax')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('a8730'),
                'plus_report_line_ids': [ref('l10n_dk.account_tax_report_line_international_purchase_products')],
            }),
        ]"/>
    </record>

    <record id="tax520" model="account.tax.template">
        <field name="chart_template_id" ref="dk_chart_template"/>
        <field name="sequence">520</field>
        <field name="name">EU Ydelseskøb</field>
        <field name="amount">25</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('l10n_dk.account_tax_report_line_section_a_services')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('a8740'),
                'plus_report_line_ids': [ref('l10n_dk.account_tax_report_line_deduction_purchase_tax')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('a8731'),
                'minus_report_line_ids': [ref('l10n_dk.account_tax_report_line_international_purchase_services')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('l10n_dk.account_tax_report_line_section_a_services')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('a8740'),
                'minus_report_line_ids': [ref('l10n_dk.account_tax_report_line_deduction_purchase_tax')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('a8731'),
                'plus_report_line_ids': [ref('l10n_dk.account_tax_report_line_international_purchase_services')],
            }),
        ]"/>
    </record>


    <!-- 3. Lande købsmoms (taxes to set on purchase outside of EU) -->
    <record id="tax610" model="account.tax.template">
        <field name="chart_template_id" ref="dk_chart_template"/>
        <field name="sequence">610</field>
        <field name="name">3. Land Varekøb</field>
        <field name="amount">25</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('a8740'),
                'plus_report_line_ids': [ref('l10n_dk.account_tax_report_line_international_purchase_products')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('a8730'),
                'minus_report_line_ids': [ref('l10n_dk.account_tax_report_line_deduction_purchase_tax')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('a8740'),
                'minus_report_line_ids': [ref('l10n_dk.account_tax_report_line_international_purchase_products')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('a8730'),
                'plus_report_line_ids': [ref('l10n_dk.account_tax_report_line_deduction_purchase_tax')],
            }),
        ]"/>
    </record>
    <record id="tax620" model="account.tax.template">
        <field name="chart_template_id" ref="dk_chart_template"/>
        <field name="sequence">620</field>
        <field name="name">3. Land Ydelseskøb</field>
        <field name="amount">25</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('a8740'),
                'plus_report_line_ids': [ref('l10n_dk.account_tax_report_line_deduction_purchase_tax')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('a8731'),
                'minus_report_line_ids': [ref('l10n_dk.account_tax_report_line_international_purchase_services')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('a8740'),
                'minus_report_line_ids': [ref('l10n_dk.account_tax_report_line_deduction_purchase_tax')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('a8731'),
                'plus_report_line_ids': [ref('l10n_dk.account_tax_report_line_international_purchase_services')],
            }),
        ]"/>
    </record>

    <!--  All Energy and water taxes  -->

    <!--  oil and bottled gas tax  -->
    <!--  92.83% of the tax is deductible  -->
    <!--  https://skat.dk/data.aspx?oid=2246453  -->
    <record id="tax710" model="account.tax.template">
        <field name="chart_template_id" ref="dk_chart_template"/>
        <field name="sequence">710</field>
        <field name="name">Olie- og flaskegasafgift</field>
        <field name="amount">100</field>
        <field name="amount_type">division</field>
        <field name="price_include" eval="True"/>
        <field name="type_tax_use">purchase</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 92.83,
                'repartition_type': 'tax',
                'account_id': ref('a8775'),
                'plus_report_line_ids': [ref('l10n_dk.account_tax_report_line_deduction_oil_bottle_tax')],
            }),
            (0,0, {
                'factor_percent': 7.17,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 92.83,
                'repartition_type': 'tax',
                'account_id': ref('a8775'),
                'minus_report_line_ids': [ref('l10n_dk.account_tax_report_line_deduction_oil_bottle_tax')],
            }),
            (0,0, {
                'factor_percent': 7.17,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <!-- 0.896 cent per kilowatt (to set as quantity) can be deduced -->
    <!-- https://skat.dk/data.aspx?oid=2246453 -->
    <record id="tax720" model="account.tax.template">
        <field name="chart_template_id" ref="dk_chart_template"/>
        <field name="sequence">720</field>
        <field name="name">Elafgift</field>
        <field name="amount">0.896</field>
        <field name="amount_type">fixed</field>
        <field name="price_include" eval="True"/>
        <field name="type_tax_use">purchase</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('a8775'),
                'plus_report_line_ids': [ref('l10n_dk.account_tax_report_line_deduction_electrical_tax')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('a8775'),
                'minus_report_line_ids': [ref('l10n_dk.account_tax_report_line_deduction_electrical_tax')],
            }),
        ]"/>
    </record>
    <!--  city gas and oil tax: same than bottled but appears on a different report line  -->
    <!--  92.83% of the tax is deductible  -->
    <!--  https://skat.dk/data.aspx?oid=2246453  -->
    <record id="tax730" model="account.tax.template">
        <field name="chart_template_id" ref="dk_chart_template"/>
        <field name="sequence">730</field>
        <field name="name">Naturgas- og bygasafgift</field>
        <field name="amount">100</field>
        <field name="amount_type">division</field>
        <field name="price_include" eval="True"/>
        <field name="type_tax_use">purchase</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 92.83,
                'repartition_type': 'tax',
                'account_id': ref('a8780'),
                'plus_report_line_ids': [ref('l10n_dk.account_tax_report_line_deduction_gas_tax')],
            }),
            (0,0, {
                'factor_percent': 7.17,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 92.83,
                'repartition_type': 'tax',
                'account_id': ref('a8780'),
                'minus_report_line_ids': [ref('l10n_dk.account_tax_report_line_deduction_gas_tax')],
            }),
            (0,0, {
                'factor_percent': 7.17,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <!--  Coal tax: percentage is the same as oil and bottled gas but with it ends up in its own report line  -->
    <record id="tax740" model="account.tax.template">
        <field name="chart_template_id" ref="dk_chart_template"/>
        <field name="sequence">740</field>
        <field name="name">Kulafgift</field>
        <field name="amount">100</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 92.83,
                'repartition_type': 'tax',
                'account_id': ref('a8785'),
                'plus_report_line_ids': [ref('l10n_dk.account_tax_report_line_deduction_coal_tax')],
            }),
            (0,0, {
                'factor_percent': -92.83,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 92.83,
                'repartition_type': 'tax',
                'account_id': ref('a8785'),
                'minus_report_line_ids': [ref('l10n_dk.account_tax_report_line_deduction_coal_tax')],
            }),
            (0,0, {
                'factor_percent': -92.83,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <!-- CO2-afgift is rarely used anymore, but we include it as inactive so that it can be customized when the need comes -->
    <record id="tax750" model="account.tax.template">
        <field name="chart_template_id" ref="dk_chart_template"/>
        <field name="sequence">750</field>
        <field name="name">CO2-afgift</field>
        <field name="active" eval="False"/>
        <field name="amount">0</field>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('a8785'),
                'plus_report_line_ids': [ref('l10n_dk.account_tax_report_line_deduction_co2_tax')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('a4271'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('a8785'),
                'minus_report_line_ids': [ref('l10n_dk.account_tax_report_line_deduction_co2_tax')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('a4271'),
            }),
        ]"/>
    </record>
    <!-- water tax: 6.18 per cubic meter -->
    <!-- https://skat.dk/data.aspx?oid=2246453 -->
    <record id="tax760" model="account.tax.template">
        <field name="chart_template_id" ref="dk_chart_template"/>
        <field name="sequence">760</field>
        <field name="name">Vandafgift</field>
        <field name="amount">6.1800</field>
        <field name="amount_type">fixed</field>
        <field name="price_include" eval="True"/>
        <field name="type_tax_use">purchase</field>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('a8795'),
                'plus_report_line_ids': [ref('l10n_dk.account_tax_report_line_deduction_water_tax')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('a8795'),
                'minus_report_line_ids': [ref('l10n_dk.account_tax_report_line_deduction_water_tax')],
            }),
        ]"/>
    </record>
</odoo>
