# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* google_gmail
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON>EN <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON><PERSON><PERSON>, 2023
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-25 14:36+0000\n"
"PO-Revision-Date: 2022-03-03 13:58+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.ir_mail_server_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                        Connect your Gmail account"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                        连接您的Gmail账户"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.ir_mail_server_view_form
msgid ""
"<i class=\"fa fa-cog\"/>\n"
"                        Edit Settings"
msgstr "编辑设置"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.ir_mail_server_view_form
msgid ""
"<span attrs=\"{'invisible': ['|', ('use_google_gmail_service', '=', False), ('google_gmail_refresh_token', '=', False)]}\" class=\"badge badge-success\">\n"
"                        Gmail Token Valid\n"
"                    </span>"
msgstr ""

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Gmail Credentials</span>"
msgstr "<span class=\"o_form_label\">Gmail凭证</span>"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__google_gmail_access_token
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__google_gmail_access_token
msgid "Access Token"
msgstr "访问指示物"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__google_gmail_access_token_expiration
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__google_gmail_access_token_expiration
msgid "Access Token Expiration Timestamp"
msgstr "访问令牌到期时间戳"

#. module: google_gmail
#: code:addons/google_gmail/controllers/main.py:0
#: code:addons/google_gmail/controllers/main.py:0
#, python-format
msgid "An error occur during the authentication process."
msgstr "身份验证过程中发生错误"

#. module: google_gmail
#: code:addons/google_gmail/models/google_gmail_mixin.py:0
#, python-format
msgid "An error occurred when fetching the access token."
msgstr "获取访问令牌时发生错误。"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__google_gmail_authorization_code
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__google_gmail_authorization_code
msgid "Authorization Code"
msgstr "授权码"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.res_config_settings_view_form
msgid "Client ID"
msgstr "客户ID"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.res_config_settings_view_form
msgid "Client Secret"
msgstr "客户密钥"

#. module: google_gmail
#: model:ir.model,name:google_gmail.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.ir_mail_server_view_form
msgid "Gmail"
msgstr "Gmail"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__use_google_gmail_service
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__use_google_gmail_service
msgid "Gmail Authentication"
msgstr "Gmail认证"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_res_config_settings__google_gmail_client_identifier
msgid "Gmail Client Id"
msgstr "Gmail 客户端 ID"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_res_config_settings__google_gmail_client_secret
msgid "Gmail Client Secret"
msgstr "Gmail客户端Secret"

#. module: google_gmail
#: model:ir.model,name:google_gmail.model_google_gmail_mixin
msgid "Google Gmail Mixin"
msgstr "谷歌Gmail混合器"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__id
msgid "ID"
msgstr "ID"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin____last_update
msgid "Last Modified on"
msgstr "最后修改"

#. module: google_gmail
#: model:ir.model,name:google_gmail.model_ir_mail_server
msgid "Mail Server"
msgstr "邮件服务器"

#. module: google_gmail
#: code:addons/google_gmail/models/google_gmail_mixin.py:0
#, python-format
msgid "Only the administrator can link a Gmail mail server."
msgstr "只有管理员可以链接一个Gmail邮件服务器。"

#. module: google_gmail
#: code:addons/google_gmail/models/google_gmail_mixin.py:0
#, python-format
msgid "Please configure your Gmail credentials."
msgstr "请配置您的Gmail凭据。"

#. module: google_gmail
#: code:addons/google_gmail/models/ir_mail_server.py:0
#, python-format
msgid ""
"Please fill the \"Username\" field with your Gmail username (your email "
"address). This should be the same account as the one used for the Gmail "
"OAuthentication Token."
msgstr ""
"请在\"用户名\"一栏填写 Gmail 用户名（电子邮件地址）。该账户应与 Gmail OAuthentication Token 使用的账户一致。"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__google_gmail_refresh_token
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__google_gmail_refresh_token
msgid "Refresh Token"
msgstr "更新令牌"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.res_config_settings_view_form
msgid "Send and receive email with your Gmail account."
msgstr "用您的Gmail账户发送和接收电子邮件。"

#. module: google_gmail
#: model_terms:ir.ui.view,arch_db:google_gmail.ir_mail_server_view_form
msgid ""
"Setup your Gmail API credentials in the general settings to link a Gmail "
"account."
msgstr "在一般设置中设置您的Gmail API凭证，以链接Gmail账户。"

#. module: google_gmail
#: model:ir.model.fields,help:google_gmail.field_google_gmail_mixin__google_gmail_uri
#: model:ir.model.fields,help:google_gmail.field_ir_mail_server__google_gmail_uri
msgid "The URL to generate the authorization code from Google"
msgstr "谷歌生成授权码的URL"

#. module: google_gmail
#: model:ir.model.fields,field_description:google_gmail.field_google_gmail_mixin__google_gmail_uri
#: model:ir.model.fields,field_description:google_gmail.field_ir_mail_server__google_gmail_uri
msgid "URI"
msgstr "URI"
