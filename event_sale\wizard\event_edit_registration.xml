<?xml version="1.0" encoding="utf-8"?>
<odoo>

        <record id="view_event_registration_editor_form" model="ir.ui.view">
            <field name="name">registration.editor.form</field>
            <field name="model">registration.editor</field>
            <field name="arch" type="xml">
                <form string="Registration">
                    <p>Before confirming <field name="sale_order_id" readonly="1" class="oe_inline"/>
                    please give details about the registrations</p>
                    <field name="event_registration_ids">
                        <tree string="Registration" editable="top" create="false" delete="false">
                            <field name="event_id" readonly='1' force_save="1"/>
                            <field name="registration_id" readonly='1' force_save="1"/>
                            <field name="event_ticket_id" domain="[('event_id', '=', event_id)]" readonly='1' force_save="1"/>
                            <field name="name"/>
                            <field name="email"/>
                            <field name="mobile" class="o_force_ltr"/>
                            <field name="phone" class="o_force_ltr"/>
                            <field name="sale_order_line_id" invisible="1"/>
                        </tree>
                    </field>
                    <footer>
                        <button string="Confirm" name="action_make_registration" type="object" class="btn-primary" data-hotkey="q"/>
                        <button string="Cancel" class="btn-secondary" special="cancel" data-hotkey="z"/>
                    </footer>
                </form>
            </field>
        </record>

        <record id="action_sale_order_event_registration" model="ir.actions.act_window">
            <field name="name">Event Registrations</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">registration.editor</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="view_event_registration_editor_form"/>
            <field name="target">new</field>
            <field name="context">{}</field>
        </record>
</odoo>
