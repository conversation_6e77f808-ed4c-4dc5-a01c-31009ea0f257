<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="ST1" model="account.tax.template">
        <field name="description">15%</field>
        <field name="chart_template_id" ref="default_chart_template"/>
        <field name="type_tax_use">sale</field>
        <field name="name">Standard Rate</field>
        <field name="amount_type">percent</field>
        <field name="amount">15</field>
        <field name="tax_group_id" ref="tax_group_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('standard_rate_exclude_capital_goods_service')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('200060'),
                'plus_report_line_ids': [ref('standard_rate_exclude_capital_goods_service'), ref('vat_on_standard_rate_exclude_capital_goods_service')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('standard_rate_exclude_capital_goods_service')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('200060'),
                'minus_report_line_ids': [ref('standard_rate_exclude_capital_goods_service'), ref('vat_on_standard_rate_exclude_capital_goods_service')],
            }),
        ]"/>
    </record>

    <record id="ST1A" model="account.tax.template">
        <field name="description">15%</field>
        <field name="chart_template_id" ref="default_chart_template"/>
        <field name="type_tax_use">sale</field>
        <field name="name">Standard Rate (Capital Goods)</field>
        <field name="amount_type">percent</field>
        <field name="amount">15</field>
        <field name="tax_group_id" ref="tax_group_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('standard_rate_only_capital_goods_service')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('200060'),
                'plus_report_line_ids': [ref('standard_rate_only_capital_goods_service'), ref('vat_on_standard_rate_only_capital_goods_service')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('standard_rate_only_capital_goods_service')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('200060'),
                'minus_report_line_ids': [ref('standard_rate_only_capital_goods_service'), ref('vat_on_standard_rate_only_capital_goods_service')],
            }),
        ]"/>
    </record>

    <record id="ST2" model="account.tax.template">
        <field name="description">0%</field>
        <field name="chart_template_id" ref="default_chart_template"/>
        <field name="type_tax_use">sale</field>
        <field name="name">Zero Rate</field>
        <field name="amount_type">percent</field>
        <field name="amount">0</field>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('zero_rate_exclude_goods_exported')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('zero_rate_exclude_goods_exported')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="ST2A" model="account.tax.template">
        <field name="description">0%</field>
        <field name="chart_template_id" ref="default_chart_template"/>
        <field name="type_tax_use">sale</field>
        <field name="name">Zero Rate Exports</field>
        <field name="amount_type">percent</field>
        <field name="amount">0</field>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('zero_rate_only_goods_exported')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('zero_rate_only_goods_exported')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="ST3" model="account.tax.template">
        <field name="description">0%</field>
        <field name="chart_template_id" ref="default_chart_template"/>
        <field name="type_tax_use">sale</field>
        <field name="name">Exempt and Non-Supplies</field>
        <field name="amount_type">percent</field>
        <field name="amount">0</field>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('exempt_and_non_supplies')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('exempt_and_non_supplies')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="ST5" model="account.tax.template">
        <field name="description">15%</field>
        <field name="chart_template_id" ref="default_chart_template"/>
        <field name="type_tax_use">sale</field>
        <field name="name">Accommodation (28+ days)</field>
        <field name="amount_type">percent</field>
        <field name="amount">15</field>
        <field name="tax_group_id" ref="tax_group_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('accomodation_exceeding_28_days')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('200060'),
                'plus_report_line_ids': [ref('vat_on_accomodation_exceeding_28_days')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('accomodation_exceeding_28_days')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('200060'),
                'minus_report_line_ids': [ref('vat_on_accomodation_exceeding_28_days')],
            }),
        ]"/>
    </record>

    <record id="ST7" model="account.tax.template">
        <field name="description">15%</field>
        <field name="chart_template_id" ref="default_chart_template"/>
        <field name="type_tax_use">sale</field>
        <field name="name">Accommodation (Under 28 days)</field>
        <field name="amount_type">percent</field>
        <field name="amount">15</field>
        <field name="tax_group_id" ref="tax_group_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('accomodation_under_28_days')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('200060'),
                'plus_report_line_ids': [ref('vat_on_accomodation_under_28_days')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('accomodation_under_28_days')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('200060'),
                'minus_report_line_ids': [ref('vat_on_accomodation_under_28_days')],
            }),
        ]"/>
    </record>

    <record id="ST10" model="account.tax.template">
        <field name="description">15%</field>
        <field name="chart_template_id" ref="default_chart_template"/>
        <field name="type_tax_use">sale</field>
        <field name="name">Export of Second-hand Goods/ Change in Use</field>
        <field name="amount_type">percent</field>
        <field name="amount">15</field>
        <field name="tax_group_id" ref="tax_group_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('second_hand_goods_export')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('200060'),
                'plus_report_line_ids': [ref('vat_on_second_hand_goods_export')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('second_hand_goods_export')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('200060'),
                'minus_report_line_ids': [ref('vat_on_second_hand_goods_export')],
            }),
        ]"/>
    </record>

    <record id="ST12" model="account.tax.template">
        <field name="description">15%</field>
        <field name="chart_template_id" ref="default_chart_template"/>
        <field name="type_tax_use">sale</field>
        <field name="name">VAT Adjustments and Manual VAT</field>
        <field name="amount_type">percent</field>
        <field name="amount">15</field>
        <field name="tax_group_id" ref="tax_group_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('200060'),
                'plus_report_line_ids': [ref('other_imported_services')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('200060'),
                'minus_report_line_ids': [ref('other_imported_services')],
            }),
        ]"/>
    </record>

    <record id="PT15" model="account.tax.template">
        <field name="description">15%</field>
        <field name="chart_template_id" ref="default_chart_template"/>
        <field name="type_tax_use">purchase</field>
        <field name="name">Standard Rate</field>
        <field name="amount_type">percent</field>
        <field name="amount">15</field>
        <field name="tax_group_id" ref="tax_group_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('100060'),
                'plus_report_line_ids': [ref('other_goods_services_supplied')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('100060'),
                'minus_report_line_ids': [ref('other_goods_services_supplied')],
            }),
        ]"/>
    </record>

    <record id="PT14" model="account.tax.template">
        <field name="description">15%</field>
        <field name="chart_template_id" ref="default_chart_template"/>
        <field name="type_tax_use">purchase</field>
        <field name="name">Standard Rate (Capital Goods)</field>
        <field name="amount_type">percent</field>
        <field name="amount">15</field>
        <field name="tax_group_id" ref="tax_group_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('100060'),
                'plus_report_line_ids': [ref('capital_goods_services_supplied')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('100060'),
                'minus_report_line_ids': [ref('capital_goods_services_supplied')],
            }),
        ]"/>
    </record>

    <record id="PT14A" model="account.tax.template">
        <field name="description">15%</field>
        <field name="chart_template_id" ref="default_chart_template"/>
        <field name="type_tax_use">purchase</field>
        <field name="name">Capital Goods Imported</field>
        <field name="amount_type">percent</field>
        <field name="amount">15</field>
        <field name="tax_group_id" ref="tax_group_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('100060'),
                'plus_report_line_ids': [ref('capital_goods_imported')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('100060'),
                'minus_report_line_ids': [ref('capital_goods_imported')],
            }),
        ]"/>
    </record>

    <record id="PT15A" model="account.tax.template">
        <field name="description">15%</field>
        <field name="chart_template_id" ref="default_chart_template"/>
        <field name="type_tax_use">purchase</field>
        <field name="name">Goods and Services Imported</field>
        <field name="amount_type">percent</field>
        <field name="amount">15</field>
        <field name="tax_group_id" ref="tax_group_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('100060'),
                'plus_report_line_ids': [ref('other_goods_imported')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('100060'),
                'minus_report_line_ids': [ref('other_goods_imported')],
            }),
        ]"/>
    </record>

    <record id="PT16" model="account.tax.template">
        <field name="description">15%</field>
        <field name="chart_template_id" ref="default_chart_template"/>
        <field name="type_tax_use">purchase</field>
        <field name="name">Change in Use</field>
        <field name="amount_type">percent</field>
        <field name="amount">15</field>
        <field name="tax_group_id" ref="tax_group_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('100060'),
                'plus_report_line_ids': [ref('change_in_use')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('100060'),
                'minus_report_line_ids': [ref('change_in_use')],
            }),
        ]"/>
    </record>

    <record id="PT17" model="account.tax.template">
        <field name="description">15%</field>
        <field name="chart_template_id" ref="default_chart_template"/>
        <field name="type_tax_use">purchase</field>
        <field name="name">Bad Debts</field>
        <field name="amount_type">percent</field>
        <field name="amount">15</field>
        <field name="tax_group_id" ref="tax_group_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('100060'),
                'plus_report_line_ids': [ref('bad_debts')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('100060'),
                'minus_report_line_ids': [ref('bad_debts')],
            }),
        ]"/>
    </record>

    <record id="PT18" model="account.tax.template">
        <field name="description">15%</field>
        <field name="chart_template_id" ref="default_chart_template"/>
        <field name="type_tax_use">purchase</field>
        <field name="name">Other Adjustments</field>
        <field name="amount_type">percent</field>
        <field name="amount">15</field>
        <field name="tax_group_id" ref="tax_group_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('100060'),
                'plus_report_line_ids': [ref('others')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('100060'),
                'minus_report_line_ids': [ref('others')],
            }),
        ]"/>
    </record>

</odoo>
