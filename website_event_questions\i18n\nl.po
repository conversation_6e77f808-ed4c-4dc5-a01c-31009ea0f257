# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_questions
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_0_question_2_answer_2
msgid "A friend"
msgstr "Een vriend"

#. module: website_event_questions
#: model:event.question,title:website_event_questions.event_0_question_1
msgid "Allergies"
msgstr "Allergieën"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__name
msgid "Answer"
msgstr "Antwoord"

#. module: website_event_questions
#: model:ir.actions.act_window,name:website_event_questions.action_event_registration_report
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_registration_answer_view_graph
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_registration_answer_view_pivot
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_registration_answer_view_tree
msgid "Answer Breakdown"
msgstr "Antwoordoverzicht"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__answer_ids
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_question_view_form
msgid "Answers"
msgstr "Antwoorden"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__once_per_order
msgid "Ask only once per order"
msgstr "Één keer vragen per order"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration__registration_answer_ids
msgid "Attendee Answers"
msgstr "Antwoorden deelnemers"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__partner_id
msgid "Booked by"
msgstr "Gereserveerd door"

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_0_question_2_answer_1
msgid "Commercials"
msgstr "Advertenties"

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_7_question_0_answer_0
msgid "Consumers"
msgstr "Consumenten"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__create_uid
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__create_uid
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__create_date
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__create_date
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__display_name
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__display_name
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_event
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__event_id
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__event_id
msgid "Event"
msgstr "Evenement"

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_question
msgid "Event Question"
msgstr "Evenementvraag"

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_question_answer
msgid "Event Question Answer"
msgstr "Antwoord van de evenementvraag"

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_registration
msgid "Event Registration"
msgstr "Evenementinschrijving"

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_registration_answer
msgid "Event Registration Answer"
msgstr "Antwoord evenementinschrijving"

#. module: website_event_questions
#: model:ir.model,name:website_event_questions.model_event_type
msgid "Event Template"
msgstr "Evenementsjabloon"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__event_type_id
msgid "Event Type"
msgstr "Soort evenement"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_event__general_question_ids
msgid "General Questions"
msgstr "Algemene vragen"

#. module: website_event_questions
#: model:event.question,title:website_event_questions.event_7_question_1
msgid "How did you hear about us ?"
msgstr "Hoe heb je over ons gehoord?"

#. module: website_event_questions
#: model:event.question,title:website_event_questions.event_0_question_2
msgid "How did you learn about this event?"
msgstr "Hoe heb je over dit evenement gehoord?"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__id
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__id
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__id
msgid "ID"
msgstr "ID"

#. module: website_event_questions
#: model:ir.model.fields,help:website_event_questions.field_event_question__once_per_order
msgid ""
"If True, this question will be asked only once and its value will be "
"propagated to every attendees.If not it will be asked for every attendee of "
"a reservation."
msgstr ""
"Als dit waar is, wordt deze vraag slechts één keer gesteld en wordt de "
"waarde ervan overgedragen aan elke bezoeker, anders wordt het bij elke "
"bezoeker van een reservering gevraagd."

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question____last_update
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer____last_update
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer____last_update
msgid "Last Modified on"
msgstr "Laatst gewijzigd op"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__write_uid
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__write_uid
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__write_date
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__write_date
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: website_event_questions
#: model:event.question,title:website_event_questions.event_0_question_0
msgid "Meal Type"
msgstr "Maaltijdtype"

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_0_question_0_answer_0
msgid "Mixed"
msgstr "Gemixed"

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_type_data_conference_question_0_answer_1
msgid "No"
msgstr "Nee"

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_0_question_2_answer_0
msgid "Our website"
msgstr "Onze website"

#. module: website_event_questions
#: model:event.question,title:website_event_questions.event_type_data_conference_question_0
msgid "Participate in Social Event"
msgstr "Neem deel aan een sociaal evenement."

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_0_question_0_answer_2
msgid "Pastafarian"
msgstr "Pastafari"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__question_id
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__question_id
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_question_view_form
msgid "Question"
msgstr "Vraag"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__question_type
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__question_type
msgid "Question Type"
msgstr "Soort vraag"

#. module: website_event_questions
#: code:addons/website_event_questions/models/event_question.py:0
#, python-format
msgid "Question cannot be linked to both an Event and an Event Type."
msgstr ""
"Een vraag kan niet worden gekoppeld aan een Evenement en een Evenementsoort."

#. module: website_event_questions
#: code:addons/website_event_questions/models/event_question.py:0
#, python-format
msgid "Question cannot belong to both the event category and itself."
msgstr ""
"Vraag kan niet behoren aan een evenementcategorie en het evenement zelf."

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_event__question_ids
#: model:ir.model.fields,field_description:website_event_questions.field_event_type__question_ids
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_registration_view_form_inherit_question
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_type_view_form_inherit_question
msgid "Questions"
msgstr "Vragen"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__registration_id
msgid "Registration"
msgstr "Inschrijving"

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_7_question_0_answer_2
msgid "Research"
msgstr "Onderzoek"

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_7_question_0_answer_1
msgid "Sales"
msgstr "Verkoop"

#. module: website_event_questions
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_registration_answer_view_tree
msgid "Selected answer"
msgstr "Geselecteerd antwoord"

#. module: website_event_questions
#: model:ir.model.fields.selection,name:website_event_questions.selection__event_question__question_type__simple_choice
msgid "Selection"
msgstr "Selectie"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__sequence
#: model:ir.model.fields,field_description:website_event_questions.field_event_question_answer__sequence
msgid "Sequence"
msgstr "Reeks"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_event__specific_question_ids
msgid "Specific Questions"
msgstr "Specifieke vragen"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__value_answer_id
msgid "Suggested answer"
msgstr "Voorgesteld antwoord"

#. module: website_event_questions
#: model:ir.model.fields.selection,name:website_event_questions.selection__event_question__question_type__text_box
msgid "Text Input"
msgstr "Tekstinvoer"

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_registration_answer__value_text_box
msgid "Text answer"
msgstr "Tekst antwoord"

#. module: website_event_questions
#: model:ir.model.constraint,message:website_event_questions.constraint_event_registration_answer_value_check
msgid "There must be a suggested value or a text value."
msgstr "Er moet een voorgestelde waarde of een tekstwaarde zijn."

#. module: website_event_questions
#: model:ir.model.fields,field_description:website_event_questions.field_event_question__title
msgid "Title"
msgstr "Titel"

#. module: website_event_questions
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event_questions.event_registration_view_form_inherit_question
msgid "Type"
msgstr "Soort"

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_0_question_0_answer_1
msgid "Vegetarian"
msgstr "Vegetarisch"

#. module: website_event_questions
#: model:event.question,title:website_event_questions.event_7_question_0
msgid "Which field are you working in"
msgstr "In welk vakgebied werk je?"

#. module: website_event_questions
#: model:event.question.answer,name:website_event_questions.event_type_data_conference_question_0_answer_0
msgid "Yes"
msgstr "Ja"

#. module: website_event_questions
#: code:addons/website_event_questions/models/event_question.py:0
#, python-format
msgid ""
"You cannot change the question type of a question that already has answers!"
msgstr ""
"Je kunt het vraagtype niet wijzigen van een vraag die al antwoorden heeft!"
