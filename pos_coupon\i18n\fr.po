# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_coupon
# 
# Translators:
# <PERSON>, 2021
# RHT<PERSON><PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-08 06:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2022\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: pos_coupon
#: model:coupon.program,name:pos_coupon.15_pc_on_next_order
msgid "15% on next order"
msgstr "15% de réduction sur votre prochaine commande"

#. module: pos_coupon
#: model:mail.template,body_html:pos_coupon.mail_coupon_template
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"    <tr><td valign=\"top\" style=\"text-align: center; font-size: 14px;\">\n"
"        <t t-if=\"object.partner_id.name\">\n"
"            Congratulations <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br/>\n"
"        </t>\n"
"\n"
"        Here is your reward from <t t-out=\"object.program_id.company_id.name or ''\">YourCompany</t>.<br/>\n"
"\n"
"        <t t-if=\"object.program_id.reward_type == 'discount'\">\n"
"            <t t-if=\"object.program_id.discount_type == 'fixed_amount'\">\n"
"                <span style=\"font-size: 50px; color: #875A7B; font-weight: bold;\" t-out=\"'%s' % format_amount(object.program_id.discount_fixed_amount, object.program_id.currency_id) or ''\">$ 10.0</span><br/>\n"
"                <strong style=\"font-size: 24px;\">off on your next order</strong><br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                <span style=\"font-size: 50px; color: #875A7B; font-weight: bold;\">\n"
"                    <t t-out=\"object.program_id.discount_percentage or ''\">10.0</t> %\n"
"                </span>\n"
"                <t t-if=\"object.program_id.discount_apply_on == 'specific_products'\">\n"
"                    <br/>\n"
"                    <t t-if=\"len(object.program_id.discount_specific_product_ids) != 1\">\n"
"                        <t t-set=\"display_specific_products\" t-value=\"True\"/>\n"
"                        <strong style=\"font-size: 24px;\">\n"
"                            on some products*\n"
"                        </strong>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        <strong style=\"font-size: 24px;\" t-out=\"'on %s' % object.program_id.discount_specific_product_ids.name or ''\">Chair floor protection</strong>\n"
"                    </t>\n"
"                </t>\n"
"                <t t-elif=\"object.program_id.discount_apply_on == 'cheapest_product'\">\n"
"                    <br/><strong style=\"font-size: 24px;\">\n"
"                        off on the cheapest product\n"
"                    </strong>\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    <br/><strong style=\"font-size: 24px;\">\n"
"                        off on your next order\n"
"                    </strong>\n"
"                </t>\n"
"                <br/>\n"
"            </t>\n"
"        </t>\n"
"        <t t-elif=\"object.program_id.reward_type == 'product'\">\n"
"            <span style=\"font-size: 36px; color: #875A7B; font-weight: bold;\" t-out=\"'get %s free %s' % (object.program_id.reward_product_quantity, object.program_id.reward_product_id.name) or ''\">Chair floor protection</span><br/>\n"
"            <strong style=\"font-size: 24px;\">on your next order</strong><br/>\n"
"        </t>\n"
"        <t t-elif=\"object.program_id.reward_type == 'free_shipping'\">\n"
"            <span style=\"font-size: 36px; color: #875A7B; font-weight: bold;\">\n"
"                get free shipping\n"
"            </span><br/>\n"
"            <strong style=\"font-size: 24px;\">on your next order</strong><br/>\n"
"        </t>\n"
"    </td></tr>\n"
"    <tr style=\"margin-top: 16px\"><td valign=\"top\" style=\"text-align: center; font-size: 14px;\">\n"
"        Use this promo code\n"
"        <t t-if=\"object.expiration_date\">\n"
"            before <t t-out=\"object.expiration_date or ''\">2021-06-05</t>\n"
"        </t>\n"
"        <p style=\"margin-top: 16px;\">\n"
"            <strong style=\"padding: 16px 8px 16px 8px; border-radius: 3px; background-color: #F1F1F1;\" t-out=\"object.code or ''\">13996301932606901095</strong>\n"
"        </p>\n"
"        <t t-if=\"object.program_id.rule_min_quantity not in [0, 1]\">\n"
"            <span style=\"font-size: 14px;\">\n"
"                Minimum purchase of <t t-out=\"object.program_id.rule_min_quantity or ''\">10</t> products\n"
"            </span><br/>\n"
"        </t>\n"
"        <t t-if=\"object.program_id.rule_minimum_amount != 0.00\">\n"
"            <span style=\"font-size: 14px;\">\n"
"                Valid for purchase above <t t-out=\"object.program_id.company_id.currency_id.symbol or ''\">$</t><t t-out=\"'%0.2f' % float(object.program_id.rule_minimum_amount) or ''\">10.00</t>\n"
"            </span><br/>\n"
"        </t>\n"
"        <t t-if=\"display_specific_products\">\n"
"            <span>\n"
"                *Valid for following products: <t t-out=\"', '.join(object.program_id.discount_specific_product_ids.mapped('name')) or ''\">Office Chair Black</t>\n"
"            </span><br/>\n"
"        </t>\n"
"        <br/>\n"
"        Thank you,\n"
"        <t t-if=\"object.source_pos_order_id.user_id.signature\">\n"
"            <br/>\n"
"            <t t-out=\"object.source_pos_order_id.user_id.signature or ''\">--<br/>Mitchell Admin</t>\n"
"        </t>\n"
"    </td></tr>\n"
"</tbody></table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"<tr><td valign=\"top\" style=\"text-align: center; font-size: 14px;\">\n"
"<t t-if=\"object.partner_id.name\">\n"
"Félicitations<t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br/>\n"
"</t>\n"
"Voici votre récompense de <t t-out=\"object.program_id.company_id.name or ''\">Votre Société</t>.<br/>\n"
"<t t-if=\"object.program_id.reward_type == 'discount'\"> \n"
"<t t-if=\"object.program_id.discount_type == 'fixed_amount'\">\n"
"<span style=\"font-size: 50px; color: #875A7B; font-weight: bold;\" t-out=\"'%s' % format_amount(object.program_id.discount_fixed_amount, object.program_id.currency_id) or ''\">$ 10.0</span><br/>\n"
"<strong style=\"font-size: 24px;\">de réduction sur votre prochaine commande</strong><br/>\n"
"</t>\n"
"<t t-else=\"\">\n"
"<span style=\"font-size: 50px; color: #875A7B; font-weight: bold;\">\n"
"<t t-out=\"object.program_id.discount_percentage or ''\">10.0</t>%\n"
"</span>\n"
"<t t-if=\"object.program_id.discount_apply_on == 'specific_products'\">\n"
"<br/>\n"
"<t t-if=\"len(object.program_id.discount_specific_product_ids) != 1\">\n"
"<t t-set=\"display_specific_products\" t-value=\"True\"/>\n"
"<strong style=\"font-size: 24px;\">\n"
" sur certains produits*\n"
"</strong>\n"
"</t>\n"
"<t t-else=\"\">\n"
"<strong style=\"font-size: 24px;\" t-out=\"'on %s' % object.program_id.discount_specific_product_ids.name or ''\">Protection Sol</strong>\n"
"</t>\n"
"</t>\n"
"<t t-elif=\"object.program_id.discount_apply_on == 'cheapest_product'\">\n"
"<br/><strong style=\"font-size: 24px;\">\n"
"de remise sur l'article le moins cher\n"
"</strong>\n"
"</t>\n"
"<t t-else=\"\">\n"
"<br/><strong style=\"font-size: 24px;\">\n"
" de remise sur votre prochaine commande\n"
"</strong>\n"
"</t>\n"
"<br/>\n"
"</t>\n"
"</t>\n"
"<t t-elif=\"object.program_id.reward_type == 'product'\">\n"
"<span style=\"font-size: 36px; color: #875A7B; font-weight: bold;\" t-out=\"'get %s free %s' % (object.program_id.reward_product_quantity, object.program_id.reward_product_id.name) or ''\"> Protection Sol</span><br/>\n"
"<strong style=\"font-size: 24px;\"> sur votre prochaine commande</strong><br/>\n"
"</t>\n"
"<t t-elif=\"object.program_id.reward_type == 'free_shipping'\">\n"
"<span style=\"font-size: 36px; color: #875A7B; font-weight: bold;\">\n"
" obtenez une livraison gratuite\n"
"</span><br/>\n"
"<strong style=\"font-size: 24px;\"> sur votre prochaine commande</strong><br/>\n"
"</t>\n"
"</td></tr>\n"
"<tr style=\"margin-top: 16px\"><td valign=\"top\" style=\"text-align: center; font-size: 14px;\">\n"
" Utilisez ce code de promotion\n"
"<t t-if=\"object.expiration_date\">\n"
" avant<t t-out=\"object.expiration_date or ''\">2021_06_05</t>\n"
"</t>\n"
"<p style=\"margin-top: 16px;\">\n"
"<strong style=\"padding: 16px 8px 16px 8px; border-radius: 3px; background-color: #F1F1F1;\" t-out=\"object.code or ''\">481399630193260690109548</strong>\n"
"</p>\n"
"<t t-if=\"object.program_id.rule_min_quantity not in [0, 1]\">\n"
"<span style=\"font-size: 14px;\">\n"
"Achat minimum de<t t-out=\"object.program_id.rule_min_quantity or ''\">10</t> articles\n"
"</span><br/>\n"
"</t>\n"
"<t t-if=\"object.program_id.rule_minimum_amount != 0.00\">\n"
"<span style=\"font-size: 14px;\">\n"
"Valide pour achat supérieur à <t t-out=\"object.program_id.company_id.currency_id.symbol or ''\">$</t><t t-out=\"'%0.2f' % float(object.program_id.rule_minimum_amount) or ''\">10.00</t>\n"
"</span><br/>\n"
"</t>\n"
"<t t-if=\"display_specific_products\">\n"
"<span>\n"
"*Valide pour les articles suivants:<t t-out=\"', '.join(object.program_id.discount_specific_product_ids.mapped('name')) or ''\"> Chaise de bureau Noire</t>\n"
"</span><br/>\n"
"</t>\n"
"<br/>\n"
"Merci,\n"
"<t t-if=\"object.source_pos_order_id.user_id.signature\">\n"
"<br/>\n"
"<t t-out=\"object.source_pos_order_id.user_id.signature or ''\">--<br/> Mitchell Admin</t>\n"
"</t>\n"
"</td></tr>\n"
"</tbody></table>"

#. module: pos_coupon
#: code:addons/pos_coupon/models/coupon.py:0
#, python-format
msgid ""
"A coupon from the same program has already been reserved for this order."
msgstr "Un coupon du même programme a déjà été réservé pour cette commande."

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_coupon_program__promo_barcode
msgid ""
"A technical field used as an alternative to the promo_code. This is "
"automatically generated when promo_code is changed."
msgstr ""
"Un champ technique est utilisé comme alternative au code promotionnel. "
"Celui-ci est automatiquement généré lorsque le code promotionnel est "
"modifié."

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/xml/ActivePrograms.xml:0
#, python-format
msgid "Active Programs"
msgstr "Programmes actifs"

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_pos_order__applied_program_ids
msgid "Applied Programs"
msgstr "Programmes appliqués"

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_coupon_coupon__pos_order_id
msgid "Applied on PoS Order"
msgstr "Appliqué sur la commande du point de vente"

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/js/ProductScreen.js:0
#, python-format
msgid "Are you sure you want to deactivate %s in this order?"
msgstr "Êtes-vous sûr de vouloir désactiver %s sur cette commande ?"

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/xml/OrderReceipt.xml:0
#: model:ir.model.fields,field_description:pos_coupon.field_coupon_program__promo_barcode
#, python-format
msgid "Barcode"
msgstr "Code barre"

#. module: pos_coupon
#: model:ir.model,name:pos_coupon.model_barcode_rule
msgid "Barcode Rule"
msgstr "Règle de code-barres"

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_pos_order__used_coupon_ids
msgid "Consumed Coupons"
msgstr "Coupons utilisés "

#. module: pos_coupon
#: model:ir.model,name:pos_coupon.model_coupon_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_pos_order_line__coupon_id
#: model:ir.model.fields.selection,name:pos_coupon.selection__barcode_rule__type__coupon
msgid "Coupon"
msgstr "Coupon"

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "Coupon Codes"
msgstr "Code promotionnel"

#. module: pos_coupon
#: model:ir.model,name:pos_coupon.model_coupon_program
msgid "Coupon Program"
msgstr "Campagne de Coupon"

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_pos_config__coupon_program_ids
#: model:ir.ui.menu,name:pos_coupon.menu_coupon_type_config
#: model_terms:ir.ui.view,arch_db:pos_coupon.res_config_view_form_inherit_pos_coupon
msgid "Coupon Programs"
msgstr "Campagnes de bons de réduction"

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_pos_order_line__coupon_id
msgid "Coupon that generated this reward."
msgstr "Coupon qui a généré cette récompense."

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_pos_config__use_coupon_programs
msgid "Coupons & Promotions"
msgstr "Bons & Promotions"

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_pos_config__program_ids
msgid "Coupons and Promotions"
msgstr "Coupons et Promotions"

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/js/ProductScreen.js:0
#, python-format
msgid "Deactivating program"
msgstr "Désactivation du programme"

#. module: pos_coupon
#: model_terms:ir.ui.view,arch_db:pos_coupon.pos_coupon_pos_config_view_form
msgid "Define the coupon and promotion programs you can use in this PoS."
msgstr ""
"Définir le coupon et les programmes de promotion que vous pouvez utiliser "
"dans ce point de vente."

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/xml/ControlButtons/PromoCodeButton.xml:0
#, python-format
msgid "Enter Code"
msgstr "Saisir un code"

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/js/ControlButtons/PromoCodeButton.js:0
#, python-format
msgid "Enter Promotion or Coupon Code"
msgstr "Saisir le code de promotion ou de coupon"

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_pos_order_line__is_program_reward
msgid ""
"Flag indicating that this order line is a result of coupon/promo program."
msgstr ""
"Drapeau indiquant que cette ligne de commande est le résultat d'un "
"coupon/programme de promotion."

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_pos_order__generated_coupon_ids
msgid "Generated Coupons"
msgstr "Coupons générés"

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_pos_order_line__is_program_reward
msgid "Is reward line"
msgstr "Est une ligne de récompense"

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/js/ProductScreen.js:0
#, python-format
msgid "No"
msgstr "Non"

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_coupon_program__pos_order_line_ids
msgid "Order lines where this program is applied."
msgstr "Lignes de commande auxquelles s'applique ce programme."

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_coupon_program__pos_order_count
msgid "PoS Order Count"
msgstr "Nombre de commandes PoS"

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_coupon_program__pos_order_line_ids
msgid "PoS Order Lines"
msgstr "Lignes de commandes du PdV"

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_coupon_coupon__source_pos_order_id
msgid "PoS Order Reference"
msgstr "Référence de commande PoS"

#. module: pos_coupon
#: code:addons/pos_coupon/models/coupon_program.py:0
#, python-format
msgid "PoS Orders"
msgstr "Commandes en PdV"

#. module: pos_coupon
#: model_terms:ir.ui.view,arch_db:pos_coupon.pos_coupon_program_view_coupon_program_form
#: model_terms:ir.ui.view,arch_db:pos_coupon.pos_coupon_program_view_promo_program_form
msgid "PoS Sales"
msgstr "Ventes du PdV"

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_coupon_coupon__pos_order_id
msgid "PoS order where this coupon is consumed/booked."
msgstr ""
"Commande du point de vente pour laquelle ce coupon a été consommé/réservé."

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_coupon_coupon__source_pos_order_id
msgid "PoS order where this coupon is generated."
msgstr "Commande du point de vente où ce coupon a été généré."

#. module: pos_coupon
#: model:ir.model,name:pos_coupon.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Paramétrage du point de vente"

#. module: pos_coupon
#: model:ir.model,name:pos_coupon.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "Lignes des commandes du point de vente"

#. module: pos_coupon
#: model:ir.model,name:pos_coupon.model_pos_order
msgid "Point of Sale Orders"
msgstr "Commandes du point de vente"

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_coupon_program__pos_config_ids
msgid "Point of Sales"
msgstr "Point de vente"

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_coupon_program__pos_order_ids
msgid "Pos Order"
msgstr "Commande PdV"

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_pos_order_line__program_id
msgid "Program"
msgstr "Programme"

#. module: pos_coupon
#: code:addons/pos_coupon/models/pos_config.py:0
#, python-format
msgid "Program: %(name)s (%(type)s), Reward Product: `%(reward_product)s`"
msgstr ""
"Programme : %(name)s (%(type)s), Produit de récompense : "
"`%(reward_product)s`"

#. module: pos_coupon
#: model_terms:ir.ui.view,arch_db:pos_coupon.pos_coupon_pos_config_view_form
msgid "Promotion & coupon programs to use."
msgstr "Programmes de promotion & de coupon à utiliser."

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_pos_config__promo_program_ids
#: model:ir.ui.menu,name:pos_coupon.menu_promotion_type_config
#: model_terms:ir.ui.view,arch_db:pos_coupon.res_config_view_form_inherit_pos_coupon
msgid "Promotion Programs"
msgstr "Campagnes de promotion"

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_pos_order_line__program_id
msgid "Promotion/Coupon Program where this reward line is based."
msgstr ""
"Programmes de promotion/coupon sur lesquels est basée cette ligne de "
"récompense."

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/xml/ControlButtons/ResetProgramsButton.xml:0
#, python-format
msgid "Reset Programs"
msgstr "Réinitialiser les programmes"

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_pos_order__applied_program_ids
msgid ""
"Technical field. This is set when the order is validated. We normally get "
"this value thru the `program_id` of the reward lines."
msgstr ""
"Champ technique. Celui-ci est défini lorsque la commande est confirmée. Nous"
" recevons normalement cette valeur par le biais du 'program_id' des lignes "
"de récompense."

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_coupon_program__pos_order_ids
msgid "The PoS orders where this program is applied."
msgstr "Les commandes du point de vue auxquelles s'applique ce programme."

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_coupon_program__valid_partner_ids
msgid "These are the partners that can avail this program."
msgstr "Voici les partenaires qui peuvent bénéficier de ce programme."

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_coupon_program__valid_product_ids
msgid "These are the products that are valid in this program."
msgstr "Ce sont les articles qui sont valides pour cette campagne."

#. module: pos_coupon
#: code:addons/pos_coupon/models/pos_config.py:0
#, python-format
msgid "This coupon is invalid (%s)."
msgstr "Ce coupon est invalide (%s)."

#. module: pos_coupon
#: code:addons/pos_coupon/models/pos_config.py:0
#, python-format
msgid ""
"To continue, make the following reward products to be available in Point of "
"Sale."
msgstr ""
"Pour continuer, rendez les produits de récompense suivants disponibles dans "
"le point de vente."

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_barcode_rule__type
msgid "Type"
msgstr "Type"

#. module: pos_coupon
#: model:ir.model.fields,help:pos_coupon.field_pos_config__use_coupon_programs
msgid "Use coupon and promotion programs in this PoS configuration."
msgstr ""
"Utilisez les compagnes de promotion et les coupon dans cette configuration "
"du PdV."

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_coupon_program__valid_partner_ids
msgid "Valid Partner"
msgstr "Partenaire valide"

#. module: pos_coupon
#: model:ir.model.fields,field_description:pos_coupon.field_coupon_program__valid_product_ids
msgid "Valid Product"
msgstr "Produit valide"

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "Valid until:"
msgstr "Valide jusqu'au :"

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/js/ProductScreen.js:0
#, python-format
msgid "Yes"
msgstr "Oui"

#. module: pos_coupon
#: model:mail.template,report_name:pos_coupon.mail_coupon_template
msgid "Your Coupon Code"
msgstr "Votre code coupon"

#. module: pos_coupon
#: model:mail.template,subject:pos_coupon.mail_coupon_template
msgid "Your reward coupon from {{ object.program_id.company_id.name }} "
msgstr ""
"Votre coupon de récompense de {{ object.program_id.company_id.name }} "

#. module: pos_coupon
#: model:mail.template,name:pos_coupon.mail_coupon_template
msgid "[POS] Coupon: Send by Email"
msgstr "[PdV] Coupon: Envoyé par email"

#. module: pos_coupon
#. openerp-web
#: code:addons/pos_coupon/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "no expiration"
msgstr "pas d'expiration "
