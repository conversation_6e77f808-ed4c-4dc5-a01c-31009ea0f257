<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="PaymentScreenElectronicPayment" t-inherit="point_of_sale.PaymentScreenElectronicPayment" t-inherit-mode="extension" owl="1">
        <xpath expr="//div[hasclass('send_payment_reversal')]/.." position="replace">
            <t t-if="props.line.canBeAdjusted() &amp;&amp; props.line.order.get_total_paid() &lt; props.line.order.get_total_with_tax()">
                <div class="button send_adjust_amount" title="Adjust Amount" t-on-click="trigger('send-payment-adjust', props.line)">
                    Adjust Amount
                </div>
            </t>
            <t t-elif="props.line.can_be_reversed">
                <div class="button send_payment_reversal" title="Reverse Payment" t-on-click="trigger('send-payment-reverse', props.line)">
                    Reverse
                </div>
            </t>
        </xpath>
    </t>

</templates>
