# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_iban
# 
# Translators:
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Slovak (https://app.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: base_iban
#. openerp-web
#: code:addons/base_iban/static/src/js/iban_widget.js:0
#, python-format
msgid "Account isn't IBAN compliant."
msgstr "Účet nespĺňa správny tvar IBAN."

#. module: base_iban
#: model:ir.model,name:base_iban.model_res_partner_bank
msgid "Bank Accounts"
msgstr "Bankové účty"

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "Cannot compute the BBAN because the account number is not an IBAN."
msgstr "Nemožno vyrátať BBAN pretože číslo účtu nie je IBAN."

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "IBAN"
msgstr "IBAN"

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid ""
"The IBAN does not seem to be correct. You should have entered something like this %s\n"
"Where B = National bank code, S = Branch code, C = Account No, k = Check digit"
msgstr ""
"IBAN sa zdá byť nesprávny. Mali ste zadať niečo takéto %s\n"
"Kde B = Kód národnej banky, S = Kód várky, C = Číslo účtu, k = Kontrolná číslica"

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "The IBAN is invalid, it should begin with the country code"
msgstr "IBAN je nesprávny, mal by začínať kódom krajiny"

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "There is no IBAN code."
msgstr "IBAN neexistuje."

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "This IBAN does not pass the validation check, please verify it."
msgstr "Tento IBAN neprešiel schválením, prosím overte ho."
