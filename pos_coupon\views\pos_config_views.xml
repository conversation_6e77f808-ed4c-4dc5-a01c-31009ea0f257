<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="pos_coupon_pos_config_view_form" model="ir.ui.view">
        <field name="name">pos.config.form</field>
        <field name="model">pos.config</field>
        <field name="inherit_id" ref="point_of_sale.pos_config_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@id='pos-loyalty']" position="after">
                <div class="col-12 col-lg-6 o_setting_box" id="pos-coupon">
                    <div class="o_setting_left_pane">
                        <field name="use_coupon_programs" nolabel="1"/>
                    </div>
                    <div class="o_setting_right_pane" title="Define the coupon and promotion programs you can use in this PoS.">
                        <label for="use_coupon_programs"/>
                        <div class="text-muted">
                            Define the coupon and promotion programs you can use in this PoS.
                        </div>
                        <div attrs="{'invisible': [('use_coupon_programs', '=', False)]}" title="Promotion &amp; coupon programs to use.">
                            <div class="content-group">
                                <div class="row mt16">
                                    <label for="promo_program_ids" class="col-lg-3 o_light_label"/>
                                    <field name="promo_program_ids"
                                        widget="many2many_tags"
                                        context="{'form_view_ref': 'coupon.coupon_program_view_promo_program_form'}"
                                        domain="[('program_type', '=', 'promotion_program'), ('active', '=', True)]" />
                                </div>
                            </div>
                            <div class="content-group">
                                <div class="row mt16">
                                    <label for="coupon_program_ids" class="col-lg-3 o_light_label"/>
                                    <field name="coupon_program_ids"
                                        widget="many2many_tags"
                                        context="{'form_view_ref': 'coupon.coupon_program_view_coupon_program_form'}"
                                        domain="[('program_type', '=', 'coupon_program'), ('active', '=', True)]"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>
</odoo>
