# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_payment
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.3\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-05-11 09:26+0000\n"
"PO-Revision-Date: 2021-09-14 12:19+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Portuguese (https://app.transifex.com/odoo/teams/41243/pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_success
msgid "<b>Communication: </b>"
msgstr "<b>Comunicação:</b>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid ""
"<i class=\"fa fa-arrow-circle-right\"/><span class=\"d-none d-md-inline\"> "
"Pay Now</span>"
msgstr ""
"<i class=\"fa fa-arrow-circle-right\"/><span class=\"d-none d-md-"
"inline\">Pagar Agora</span>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "<i class=\"fa fa-fw fa-arrow-circle-right\"/> Pay Now"
msgstr "<i class=\"fa fa-fw fa-arrow-circle-right\"/>Pagar Agora"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "<i class=\"fa fa-fw fa-check-circle\"/> Paid"
msgstr "<i class=\"fa fa-fw fa-check-circle\"/>Pago"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "<i class=\"fa fa-fw fa-check-circle\"/> Pending"
msgstr "<i class=\"fa fa-fw fa-check-circle\"/>Pendente"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid ""
"<i class=\"fa fa-info\"/> You have credits card registered, you can log-in "
"to be able to use them."
msgstr ""
"<i class=\"fa fa-info\"/> Tem cartões de créditos registados, pode iniciar a"
" sessão para os poder utilizar."

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid ""
"<span class=\"badge badge-pill badge-danger\"><i class=\"fa fa-fw fa-"
"remove\"/><span class=\"d-none d-md-inline\"> Cancelled</span></span>"
msgstr ""
"<span class=\"badge badge-pill badge-danger\"><i class=\"fa fa-fw fa-"
"remove\"/><span class=\"d-none d-md-inline\">Cancelado </span></span>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid ""
"<span class=\"badge badge-pill badge-info\"><i class=\"fa fa-fw fa-"
"clock-o\"/><span class=\"d-none d-md-inline\"> Waiting for "
"Payment</span></span>"
msgstr ""
"<span class=\"badge badge-pill badge-info\"><i class=\"fa fa-fw fa-"
"clock-o\"/><span class=\"d-none d-md-inline\">À Espera de Pagamento "
"</span></span>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid ""
"<span class=\"badge badge-pill badge-primary\"><i class=\"fa fa-fw fa-"
"check\"/><span class=\"d-none d-md-inline\"> Authorized</span></span>"
msgstr ""
"<span class=\"badge badge-pill badge-primary\"><i class=\"fa fa-fw fa-"
"check\"/><span class=\"d-none d-md-inline\">Autorizado</span></span> "

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid ""
"<span class=\"badge badge-pill badge-success\"><i class=\"fa fa-fw fa-"
"check\"/><span class=\"d-none d-md-inline\"> Paid</span></span>"
msgstr ""
"<span class=\"badge badge-pill badge-success\"><i class=\"fa fa-fw fa-"
"check\"/><span class=\"d-none d-md-inline\">Pago</span></span>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid ""
"<span class=\"badge badge-pill badge-success\"><i class=\"fa fa-fw fa-"
"check\"/><span class=\"d-none d-md-inline\"> Reversed</span></span>"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid ""
"<span class=\"badge badge-pill badge-warning\"><span class=\"d-none d-md-"
"inline\"> Pending</span></span>"
msgstr ""
"<span class=\"badge badge-pill badge-warning\"><span class=\"d-none d-md-"
"inline\">Pendente</span></span>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid ""
"<strong>No suitable payment option could be found.</strong><br/>\n"
"                                If you believe that it is an error, please contact the website administrator."
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid "Close"
msgstr "Fechar"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_success
msgid ""
"Done, your online payment has been successfully processed. Thank you for "
"your order."
msgstr ""
"Terminado, Os seus pagamentos online foram processados com sucesso. Obrigado"
" pela sua encomenda."

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid "Pay Now"
msgstr "Pagar Agora"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid "Pay now"
msgstr "Pagar agora"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid "Pay with"
msgstr "Pagar com"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid "Status"
msgstr "Estado"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid "There was an error processing your payment: invalid invoice."
msgstr "Ocorreu um erro ao processar o seu pagamento: fatura inválida."

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid ""
"There was an error processing your payment: issue with credit card ID "
"validation."
msgstr ""
"Ocorreu um erro ao processar o seu pagamento: problema com a validação da "
"Id. do cartão de crédito."

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid "There was an error processing your payment: transaction failed.<br/>"
msgstr "Ocorreu um erro ao processar o seu pagamento: transação falhou.<br/>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid "There was en error processing your payment: invalid credit card ID."
msgstr ""
"Ocorreu um erro ao processar o seu pagamento: Id. do cartão de crédito "
"inválida."
