# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * base_vat
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2015-09-07 16:40+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Hindi (http://www.transifex.com/odoo/odoo-9/language/hi/)\n"
"Language: hi\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_company
msgid "Companies"
msgstr ""

#. module: base_vat
#: model:ir.model.fields,help:base_vat.field_res_company_vat_check_vies
msgid ""
"If checked, Partners VAT numbers will be fully validated against EU's VIES "
"service rather than via a simple format validation (checksum)."
msgstr ""

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_partner
msgid "Partner"
msgstr "साथी"

#. module: base_vat
#: code:addons/base_vat/base_vat.py:128
#, python-format
msgid ""
"The VAT number [%s] for partner [%s] does not seem to be valid. \n"
"Note: the expected format is %s"
msgstr ""

#. module: base_vat
#: code:addons/base_vat/base_vat.py:127
#, python-format
msgid ""
"The VAT number [%s] for partner [%s] either failed the VIES VAT validation "
"check or did not respect the expected format %s."
msgstr ""

#. module: base_vat
#: model:ir.model.fields,field_description:base_vat.field_res_company_vat_check_vies
msgid "VIES VAT Check"
msgstr ""

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.view_partner_form
msgid "e.g. BE0477472701"
msgstr ""
