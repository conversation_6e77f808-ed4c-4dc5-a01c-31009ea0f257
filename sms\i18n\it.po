# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sms
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON>essel<PERSON>ch, 2022
# <PERSON><PERSON>, 2023
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:54+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_invalid_count
msgid "# Invalid recipients"
msgstr "N. destinatari non validi"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_valid_count
msgid "# Valid recipients"
msgstr "N. destinatari validi"

#. module: sms
#: code:addons/sms/models/sms_template.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (copia)"

#. module: sms
#. openerp-web
#: code:addons/sms/static/src/js/fields_sms_widget.js:0
#: code:addons/sms/static/src/js/fields_sms_widget.js:0
#, python-format
msgid "%s characters, fits in %s SMS (%s) "
msgstr "%s caratteri, pari a %s SMS (%s) "

#. module: sms
#: code:addons/sms/wizard/sms_composer.py:0
#, python-format
msgid "%s invalid recipients"
msgstr "%s destinatari non validi"

#. module: sms
#: code:addons/sms/models/sms_sms.py:0
#, python-format
msgid ""
"%s out of the %s selected SMS Text Messages have successfully been resent."
msgstr ""
"%s  dei %s messaggi SMS selezionati sono stati reinviati con successo."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid ""
"<span class=\"fa fa-info-circle\"/> Caution: It won't be possible to send "
"this SMS again to the recipients you did not select."
msgstr ""
"<span class=\"fa fa-info-circle\"/> Attenzione: impossibile inviare "
"nuovamente questo SMS ai destinatari non selezionati."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid ""
"<span class=\"o_stat_text\">Add</span>\n"
"                                <span class=\"o_stat_text\">Context Action</span>"
msgstr ""
"<span class=\"o_stat_text\">Aggiungi azione</span>\n"
"                                <span class=\"o_stat_text\">contestuale</span>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid ""
"<span class=\"o_stat_text\">Remove</span>\n"
"                                <span class=\"o_stat_text\">Context Action</span>"
msgstr ""
"<span class=\"o_stat_text\">Rimuovi azione</span>\n"
"                                <span class=\"o_stat_text\">contestuale</span>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid ""
"<span class=\"text-warning\" attrs=\"{'invisible': [('no_record', '=', "
"False)]}\">No records</span>"
msgstr ""
"<span class=\"text-warning\" attrs=\"{'invisible': [('no_record', '=', "
"False)]}\">Nessun record</span>"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_needaction
msgid "Action Needed"
msgstr "Azione richiesta"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_base_automation__state
#: model:ir.model.fields,field_description:sms.field_ir_actions_server__state
#: model:ir.model.fields,field_description:sms.field_ir_cron__state
msgid "Action To Do"
msgstr "Azione da eseguire"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__active_domain
msgid "Active domain"
msgstr "Dominio attivo"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__active_domain_count
msgid "Active records count"
msgstr "Numero record attivi"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid ""
"Add a contextual action on the related model to open a sms composer with "
"this template"
msgstr ""
"Aggiunge un'azione contestuale nel modello correlato che apre un compositore"
" SMS con questo schema"

#. module: sms
#. openerp-web
#: code:addons/sms/static/src/components/notification_group/notification_group.xml:0
#, python-format
msgid "An error occurred when sending an SMS."
msgstr "Si è verificato un errore durante l'invio di un SMS."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__model_id
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__model_id
msgid "Applies to"
msgstr "Si applica a"

#. module: sms
#: code:addons/sms/wizard/sms_cancel.py:0
#, python-format
msgid ""
"Are you sure you want to discard %s SMS delivery failures? You won't be able"
" to re-send these SMS later!"
msgstr ""
"Eliminare veramente %s errori di consegna? In seguito non sarà più possibile"
" reinviare questi SMS."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_attachment_count
msgid "Attachment Count"
msgstr "Numero allegati"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_blacklist
msgid "Blacklisted"
msgstr "Nella lista nera"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__mobile_blacklisted
msgid "Blacklisted Phone Is Mobile"
msgstr "Telefono in lista nera è un cellulare"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__phone_blacklisted
msgid "Blacklisted Phone is Phone"
msgstr "Telefono in lista nera è un telefono"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__body
#: model:ir.model.fields,field_description:sms.field_sms_template__body
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__body
msgid "Body"
msgstr "Corpo"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Buy credits"
msgstr "Acquista crediti"

#. module: sms
#: code:addons/sms/models/sms_api.py:0
#, python-format
msgid "Buy credits."
msgstr "Acquista crediti"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_cancel
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Cancel"
msgstr "Annulla"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_cancel
msgid "Cancel notification in failure"
msgstr "Invalida notifiche di errore"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__canceled
msgid "Canceled"
msgstr "Annullato"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Check"
msgstr "Controllare"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "Choose a language:"
msgstr "Scegli una lingua:"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "Choose an example"
msgstr "Scegli un esempio per il record"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Close"
msgstr "Chiudi"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__composition_mode
msgid "Composition Mode"
msgstr "Modalità composizione"

#. module: sms
#: model:ir.model,name:sms.model_res_partner
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Contact"
msgstr "Contatto"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "Content"
msgstr "Contenuto"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_cancel__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_composer__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_resend__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_sms__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_template__create_uid
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_cancel__create_date
#: model:ir.model.fields,field_description:sms.field_sms_composer__create_date
#: model:ir.model.fields,field_description:sms.field_sms_resend__create_date
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__create_date
#: model:ir.model.fields,field_description:sms.field_sms_sms__create_date
#: model:ir.model.fields,field_description:sms.field_sms_template__create_date
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__create_date
msgid "Created on"
msgstr "Data creazione"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__partner_id
msgid "Customer"
msgstr "Cliente"

#. module: sms
#: model:sms.template,name:sms.sms_template_demo_0
msgid "Customer: automated SMS"
msgstr "Cliente: SMS automatici"

#. module: sms
#: model:sms.template,body:sms.sms_template_demo_0
msgid "Dear {{ object.display_name }} this is an automated SMS."
msgstr ""
"Gentile {{ object.display_name }} questo è un messaggio SMS automatico."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__null_value
msgid "Default Value"
msgstr "Valore predefinito"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "Discard"
msgstr "Abbandona"

#. module: sms
#: model:ir.actions.act_window,name:sms.sms_cancel_action
msgid "Discard SMS delivery failures"
msgstr "Elimina errori di consegna SMS"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_cancel
msgid "Discard delivery failures"
msgstr "Elimina errori di consegna"

#. module: sms
#: model:ir.model,name:sms.model_sms_cancel
msgid "Dismiss notification for resend by model"
msgstr "Ignoramento notifiche per reinvio per modello"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_cancel__display_name
#: model:ir.model.fields,field_description:sms.field_sms_composer__display_name
#: model:ir.model.fields,field_description:sms.field_sms_resend__display_name
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__display_name
#: model:ir.model.fields,field_description:sms.field_sms_sms__display_name
#: model:ir.model.fields,field_description:sms.field_sms_template__display_name
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: sms
#: model:ir.model,name:sms.model_mail_followers
msgid "Document Followers"
msgstr "Chi segue il documento"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_id
msgid "Document ID"
msgstr "ID documento"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_ids
msgid "Document IDs"
msgstr "ID documento"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_model
msgid "Document Model Name"
msgstr "Nome modello documento"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_duplicate
msgid "Duplicate"
msgstr "Duplica"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "Dynamic Placeholder Generator"
msgstr "Generatore segnaposto dinamico"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Edit Partners"
msgstr "Modifica partner"

#. module: sms
#: model:ir.model,name:sms.model_mail_thread
msgid "Email Thread"
msgstr "Discussione e-mail"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__error
msgid "Error"
msgstr "Errore"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__failure_type
msgid "Failure Type"
msgstr "Tipo di errore"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_notification__failure_type
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__failure_type
msgid "Failure type"
msgstr "Tipo di errore"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__model_object_field
msgid "Field"
msgstr "Campo"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__phone_sanitized
msgid ""
"Field used to store sanitized phone number. Helps speeding up searches and "
"comparisons."
msgstr ""
"Campo usato per memorizzare il numero di telefono pulito. Aiuta a "
"velocizzare le ricerche e i confronti."

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__copyvalue
msgid ""
"Final placeholder expression, to be copy-pasted in the desired template "
"field."
msgstr ""
"Espressione finale del segnaposto, da copia-incollare nel campo del modello "
"desiderato."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_follower_ids
msgid "Followers"
msgstr "Seguito da"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguito da (partner)"

#. module: sms
#: code:addons/sms/wizard/sms_composer.py:0
#, python-format
msgid "Following numbers are not correctly encoded: %s"
msgstr "I seguenti numeri non sono codificati in modo corretto: %s"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__has_cancel
msgid "Has Cancel"
msgstr "Ha annullato"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__has_insufficient_credit
msgid "Has Insufficient Credit"
msgstr "Non ha credito sufficiente"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__has_message
msgid "Has Message"
msgstr "Contiene messaggio"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_mail__has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_message__has_sms_error
msgid "Has SMS error"
msgstr "Contiene un errore SMS"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__has_unregistered_account
msgid "Has Unregistered Account"
msgstr "Ha un account non registrato"

#. module: sms
#: model:ir.model.fields,help:sms.field_mail_mail__has_sms_error
#: model:ir.model.fields,help:sms.field_mail_message__has_sms_error
msgid "Has error"
msgstr "Contiene un errore"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_cancel__help_message
msgid "Help message"
msgstr "Messaggio di aiuto"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_cancel__id
#: model:ir.model.fields,field_description:sms.field_sms_composer__id
#: model:ir.model.fields,field_description:sms.field_sms_resend__id
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__id
#: model:ir.model.fields,field_description:sms.field_sms_sms__id
#: model:ir.model.fields,field_description:sms.field_sms_template__id
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__id
msgid "ID"
msgstr "ID"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__message_needaction
#: model:ir.model.fields,help:sms.field_res_partner__message_unread
msgid "If checked, new messages require your attention."
msgstr "Se selezionata, nuovi messaggi richiedono attenzione."

#. module: sms
#: model:ir.model.fields,help:sms.field_account_analytic_account__message_has_sms_error
#: model:ir.model.fields,help:sms.field_calendar_event__message_has_sms_error
#: model:ir.model.fields,help:sms.field_crm_team__message_has_sms_error
#: model:ir.model.fields,help:sms.field_crm_team_member__message_has_sms_error
#: model:ir.model.fields,help:sms.field_fleet_vehicle__message_has_sms_error
#: model:ir.model.fields,help:sms.field_fleet_vehicle_log_contract__message_has_sms_error
#: model:ir.model.fields,help:sms.field_fleet_vehicle_log_services__message_has_sms_error
#: model:ir.model.fields,help:sms.field_gamification_badge__message_has_sms_error
#: model:ir.model.fields,help:sms.field_gamification_challenge__message_has_sms_error
#: model:ir.model.fields,help:sms.field_hr_contract__message_has_sms_error
#: model:ir.model.fields,help:sms.field_hr_department__message_has_sms_error
#: model:ir.model.fields,help:sms.field_hr_employee__message_has_sms_error
#: model:ir.model.fields,help:sms.field_hr_job__message_has_sms_error
#: model:ir.model.fields,help:sms.field_hr_leave__message_has_sms_error
#: model:ir.model.fields,help:sms.field_hr_leave_allocation__message_has_sms_error
#: model:ir.model.fields,help:sms.field_lunch_supplier__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_blacklist__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_channel__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread_blacklist__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread_cc__message_has_sms_error
#: model:ir.model.fields,help:sms.field_mail_thread_phone__message_has_sms_error
#: model:ir.model.fields,help:sms.field_maintenance_equipment__message_has_sms_error
#: model:ir.model.fields,help:sms.field_maintenance_equipment_category__message_has_sms_error
#: model:ir.model.fields,help:sms.field_maintenance_request__message_has_sms_error
#: model:ir.model.fields,help:sms.field_note_note__message_has_sms_error
#: model:ir.model.fields,help:sms.field_phone_blacklist__message_has_sms_error
#: model:ir.model.fields,help:sms.field_product_product__message_has_sms_error
#: model:ir.model.fields,help:sms.field_product_template__message_has_sms_error
#: model:ir.model.fields,help:sms.field_res_partner__message_has_error
#: model:ir.model.fields,help:sms.field_res_partner__message_has_sms_error
#: model:ir.model.fields,help:sms.field_res_users__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Se selezionata, alcuni messaggi presentano un errore di consegna."

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__phone_sanitized_blacklisted
msgid ""
"If the sanitized phone number is on the blacklist, the contact won't receive"
" mass mailing sms anymore, from any list"
msgstr ""
"Se il numero di telefono pulito è in lista nera, il contatto non sarà più "
"soggetto all'invio massivo di SMS, da nessuna lista."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_cancel
msgid ""
"If you want to re-send them, click Cancel now, then click on the "
"notification and review them one by one by clicking on the red icon next to "
"each message."
msgstr ""
"Per procedere al reinvio, fare clic prima su Annulla e poi sulla notifica. "
"Esaminarle quindi una per una con un clic sul'icona rossa accanto a ciascun "
"messaggio."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Ignore all"
msgstr "Ignora tutto"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__outgoing
msgid "In Queue"
msgstr "In coda"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__mobile_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a mobile number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"Indica se un numero di telefono pulito in lista nera è un cellulare. Aiuta a"
" distinguere qual è il numero in lista nera             quando in un modello"
" sono presenti sia il campo cellulare che telefono."

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__phone_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a phone number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"Indica se un numero di telefono pulito in lista nera è un fisso. Aiuta a "
"distinguere qual è il numero in lista nera             quando in un modello "
"sono presenti sia il campo cellulare che telefono."

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_composer__comment_single_recipient
msgid "Indicates if the SMS composer targets a single specific recipient"
msgstr ""
"Indica se il compositore SMS punta a un destinatario singolo specifico"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_credit
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_credit
msgid "Insufficient Credit"
msgstr "Credito non sufficiente"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Invalid phone number"
msgstr "Numero di telefono non valido"

#. module: sms
#: code:addons/sms/wizard/sms_composer.py:0
#, python-format
msgid "Invalid recipient number. Please update it."
msgstr "Numero destinatario non valido. Aggiornarlo."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_is_follower
msgid "Is Follower"
msgstr "Sta seguendo"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_single_valid
msgid "Is valid"
msgstr "È valido"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__mass_keep_log
msgid "Keep a note on document"
msgstr "Annota sul documento"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__lang
msgid "Language"
msgstr "Lingua"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_cancel____last_update
#: model:ir.model.fields,field_description:sms.field_sms_composer____last_update
#: model:ir.model.fields,field_description:sms.field_sms_resend____last_update
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient____last_update
#: model:ir.model.fields,field_description:sms.field_sms_sms____last_update
#: model:ir.model.fields,field_description:sms.field_sms_template____last_update
#: model:ir.model.fields,field_description:sms.field_sms_template_preview____last_update
msgid "Last Modified on"
msgstr "Ultima modifica il"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_cancel__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_composer__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_resend__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_sms__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_template__write_uid
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_cancel__write_date
#: model:ir.model.fields,field_description:sms.field_sms_composer__write_date
#: model:ir.model.fields,field_description:sms.field_sms_resend__write_date
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__write_date
#: model:ir.model.fields,field_description:sms.field_sms_sms__write_date
#: model:ir.model.fields,field_description:sms.field_sms_template__write_date
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_base_automation__sms_mass_keep_log
#: model:ir.model.fields,field_description:sms.field_ir_actions_server__sms_mass_keep_log
#: model:ir.model.fields,field_description:sms.field_ir_cron__sms_mass_keep_log
msgid "Log as Note"
msgstr "Registra come nota"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__mail_message_id
msgid "Mail Message"
msgstr "Messaggio di posta"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_ir_model__is_mail_thread_sms
msgid "Mail Thread SMS"
msgstr "Discussione SMS"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_main_attachment_id
msgid "Main Attachment"
msgstr "Allegato principale"

#. module: sms
#: model:ir.model,name:sms.model_mail_message
#: model:ir.model.fields,field_description:sms.field_sms_composer__body
#: model:ir.model.fields,field_description:sms.field_sms_resend__mail_message_id
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Message"
msgstr "Messaggio"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_has_error
msgid "Message Delivery error"
msgstr "Errore di consegna messaggio"

#. module: sms
#: model:ir.model,name:sms.model_mail_notification
msgid "Message Notifications"
msgstr "Notifiche messaggio"

#. module: sms
#: model:ir.model.fields,help:sms.field_mail_mail__message_type
#: model:ir.model.fields,help:sms.field_mail_message__message_type
msgid ""
"Message type: email for email message, notification for system message, "
"comment for other messages such as user replies"
msgstr ""
"Tipo di messaggio: e-mail per messaggio di posta elettronica, notifica per "
"messaggio di sistema, commento per altri messaggi come le risposte "
"dell'utente"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_ids
msgid "Messages"
msgstr "Messaggi"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_number_missing
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_number_missing
msgid "Missing Number"
msgstr "Numero mancante"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_cancel__model
msgid "Model"
msgstr "Modello"

#. module: sms
#: model:ir.model,name:sms.model_ir_model
msgid "Models"
msgstr "Modelli"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__name
msgid "Name"
msgstr "Nome"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__no_record
msgid "No Record"
msgstr "Nessun record"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__notification_id
msgid "Notification"
msgstr "Notifica"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_notification__notification_type
msgid "Notification Type"
msgstr "Tipo di notifica"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__sms_number
#: model:ir.model.fields,field_description:sms.field_sms_sms__number
msgid "Number"
msgstr "Numero"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__number_field_name
msgid "Number Field"
msgstr "Campo numero"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_needaction_counter
msgid "Number of Actions"
msgstr "Numero di azioni"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_has_error_counter
msgid "Number of errors"
msgstr "Numero di errori"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Numero di messaggi che richiedono un'azione"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Numero di messaggi con errore di consegna"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_composer__res_ids_count
msgid ""
"Number of recipients that will receive the SMS if sent in mass mode, without"
" applying the Active Domain value"
msgstr ""
"Numero di destinatari che ricevono l'SMS se viene inviato in modalità "
"massiva, senza applicare il valore del dominio attivo"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_composer__active_domain_count
msgid "Number of records found when searching with the value in Active Domain"
msgstr ""
"Numero di record trovati quando la ricerca viene effettuata con il valore "
"del dominio attivo"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__message_unread_counter
msgid "Number of unread messages"
msgstr "Numero di messaggi non letti"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_optout
msgid "Opted Out"
msgstr "Casi di opt-out"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Lingua di traduzione opzionale (codice ISO) da selezionare quando si invia "
"un'email. Se non impostato, verrà usata la versione inglese. Di solito "
"dovrebbe essere un'espressione segnaposto che fornisce la lingua "
"appropriata, ad esempio {{ object.partner_id.lang }}."

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__null_value
msgid "Optional value to use if the target field is empty"
msgstr "Valore opzionale da usare se il campo di destinazione è vuoto"

#. module: sms
#: model:ir.model,name:sms.model_sms_sms
msgid "Outgoing SMS"
msgstr "SMS in uscita"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__partner_id
msgid "Partner"
msgstr "Partner"

#. module: sms
#: model:ir.model,name:sms.model_mail_thread_phone
msgid "Phone Blacklist Mixin"
msgstr "Mixin lista nera telefono"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__phone_sanitized_blacklisted
msgid "Phone Blacklisted"
msgstr "Telefono in lista nera"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__phone_mobile_search
msgid "Phone/Mobile"
msgstr "Telefono/Cellulare"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__copyvalue
msgid "Placeholder Expression"
msgstr "Espressione per segnaposto"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_composer__composition_mode__comment
msgid "Post on a document"
msgstr "Messaggio in un documento"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "Preview"
msgstr "Anteprima"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "Preview of"
msgstr "Anteprima di"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Put in queue"
msgstr "Metti in coda"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Reason"
msgstr "Motivo"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__partner_name
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Recipient"
msgstr "Destinatario"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_single_number_itf
msgid "Recipient Number"
msgstr "Numero destinatario"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend__recipient_ids
msgid "Recipients"
msgstr "Destinatari"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__numbers
msgid "Recipients (Numbers)"
msgstr "Destinatari (numeri)"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_single_description
msgid "Recipients (Partners)"
msgstr "Destinatari (partner)"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__resource_ref
msgid "Record reference"
msgstr "Riferimento record"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__model
msgid "Related Document Model"
msgstr "Modello documento correlato"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "Remove the contextual action of the related model"
msgstr "Rimuove l'azione contestuale del modello correlato"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__render_model
msgid "Rendering Model"
msgstr "Rendering Model"

#. module: sms
#: model:ir.actions.server,name:sms.ir_actions_server_sms_sms_resend
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__resend
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Resend"
msgstr "Reinvia"

#. module: sms
#: model:ir.model,name:sms.model_sms_resend_recipient
msgid "Resend Notification"
msgstr "Reinvio notifica"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Retry"
msgstr "Riprova"

#. module: sms
#. openerp-web
#: code:addons/sms/static/src/components/message/message.xml:0
#: code:addons/sms/static/src/components/message/message.xml:0
#: model:ir.actions.act_window,name:sms.sms_sms_action
#: model:ir.model.fields,field_description:sms.field_mail_notification__sms_id
#: model:ir.model.fields.selection,name:sms.selection__mail_message__message_type__sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__notification_type__sms
#: model:ir.ui.menu,name:sms.sms_sms_menu
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
#, python-format
msgid "SMS"
msgstr "SMS"

#. module: sms
#: model:ir.model,name:sms.model_sms_api
msgid "SMS API"
msgstr "API SMS"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_account_analytic_account__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_calendar_event__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_crm_team__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_crm_team_member__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_fleet_vehicle__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_fleet_vehicle_log_contract__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_fleet_vehicle_log_services__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_gamification_badge__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_gamification_challenge__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_hr_contract__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_hr_department__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_hr_employee__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_hr_job__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_hr_leave__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_hr_leave_allocation__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_lunch_supplier__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_blacklist__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_channel__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread_blacklist__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread_cc__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_mail_thread_phone__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_maintenance_equipment__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_maintenance_equipment_category__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_maintenance_request__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_note_note__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_phone_blacklist__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_product_product__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_product_template__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_res_partner__message_has_sms_error
#: model:ir.model.fields,field_description:sms.field_res_users__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Errore di consegna SMS"

#. module: sms
#. openerp-web
#: code:addons/sms/static/src/models/notification_group/notification_group.js:0
#, python-format
msgid "SMS Failures"
msgstr "Errori SMS"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_notification__sms_number
msgid "SMS Number"
msgstr "Numero SMS"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "SMS Preview"
msgstr "Anteprima SMS"

#. module: sms
#. openerp-web
#: code:addons/sms/static/src/js/fields_sms_widget.js:0
#: code:addons/sms/static/src/js/fields_sms_widget.js:0
#, python-format
msgid "SMS Pricing"
msgstr "Tariffazione SMS"

#. module: sms
#: model:ir.model,name:sms.model_sms_resend
msgid "SMS Resend"
msgstr "Reinvio SMS"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_sms__state
msgid "SMS Status"
msgstr "Stato SMS"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_base_automation__sms_template_id
#: model:ir.model.fields,field_description:sms.field_ir_actions_server__sms_template_id
#: model:ir.model.fields,field_description:sms.field_ir_cron__sms_template_id
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "SMS Template"
msgstr "Modello SMS"

#. module: sms
#: model:ir.model,name:sms.model_sms_template_preview
msgid "SMS Template Preview"
msgstr "Anteprima modello SMS"

#. module: sms
#: model:ir.model,name:sms.model_sms_template
#: model:ir.ui.menu,name:sms.sms_template_menu
#: model_terms:ir.ui.view,arch_db:sms.sms_sms_view_tree
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_tree
msgid "SMS Templates"
msgstr "Modelli SMS"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "SMS content"
msgstr "Contenuto SMS"

#. module: sms
#: model:ir.actions.server,name:sms.ir_cron_sms_scheduler_action_ir_actions_server
#: model:ir.cron,cron_name:sms.ir_cron_sms_scheduler_action
#: model:ir.cron,name:sms.ir_cron_sms_scheduler_action
msgid "SMS: SMS Queue Manager"
msgstr "SMS: gestore coda SMS"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__phone_sanitized
#: model:ir.model.fields,field_description:sms.field_sms_composer__sanitized_numbers
msgid "Sanitized Number"
msgstr "Numero pulito"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_sms_view_search
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_search
msgid "Search SMS Templates"
msgstr "Ricerca modelli SMS"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__model_object_field
msgid ""
"Select target field from the related document model.\n"
"If it is a relationship field you will be able to select a target field at the destination of the relationship."
msgstr ""
"Seleziona il campo di destinazione dal modello del documento correlato.\n"
"Se è un campo relazione, può essere selezionato nella destinazione della relazione."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
#: model_terms:ir.ui.view,arch_db:sms.sms_tsms_view_form
msgid "Send Now"
msgstr "Invia ora"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Send SMS"
msgstr "Invia SMS"

#. module: sms
#: code:addons/sms/models/sms_template.py:0
#, python-format
msgid "Send SMS (%s)"
msgstr "Invia SMS (%s)"

#. module: sms
#. openerp-web
#: code:addons/sms/static/src/js/fields_phone_widget.js:0
#: code:addons/sms/static/src/js/fields_phone_widget.js:0
#: model:ir.actions.act_window,name:sms.res_partner_act_window_sms_composer_multi
#: model:ir.actions.act_window,name:sms.res_partner_act_window_sms_composer_single
#: model:ir.actions.act_window,name:sms.sms_composer_action_form
#: model:ir.model.fields.selection,name:sms.selection__ir_actions_server__state__sms
#, python-format
msgid "Send SMS Text Message"
msgstr "Invia messaggio SMS"

#. module: sms
#: model:ir.model,name:sms.model_sms_composer
msgid "Send SMS Wizard"
msgstr "Procedura invio SMS"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_composer__composition_mode__mass
msgid "Send SMS in batch"
msgstr "Invio SMS raggruppati"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "Send an SMS"
msgstr "Invio di un SMS"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__mass_force_send
msgid "Send directly"
msgstr "Invia direttamente"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_composer__composition_mode__numbers
msgid "Send to numbers"
msgstr "Invio a numeri"

#. module: sms
#: model:ir.actions.act_window,name:sms.sms_resend_action
msgid "Sending Failures"
msgstr "Invii non riusciti"

#. module: sms
#: code:addons/sms/models/ir_actions.py:0
#, python-format
msgid "Sending SMS can only be done on a mail.thread model"
msgstr ""
"L'invio di un SMS può essere effettuato solo in un modello mail.thread"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__state__sent
msgid "Sent"
msgstr "Inviato"

#. module: sms
#: model:ir.model,name:sms.model_ir_actions_server
msgid "Server Action"
msgstr "Azione server"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_server
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_server
msgid "Server Error"
msgstr "Errore del server"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.mail_resend_message_view_form
msgid "Set up an account"
msgstr "Imposta un account"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__sidebar_action_id
msgid "Sidebar action"
msgstr "Azione barra laterale"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__sidebar_action_id
msgid ""
"Sidebar action to make this template available on records of the related "
"document model"
msgstr ""
"Azione su barra laterale per rendere disponibile il modello ai record del "
"modello di documento correlato"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__comment_single_recipient
msgid "Single Mode"
msgstr "Modalità singola"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_resend_recipient__sms_resend_id
msgid "Sms Resend"
msgstr "Reinvio SMS"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__sms_template_id
msgid "Sms Template"
msgstr "Modello SMS"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__recipient_single_number
msgid "Stored Recipient Number"
msgstr "Numero destinatario memorizzato"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__sub_model_object_field
msgid "Sub-field"
msgstr "Sottocampo"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template__sub_object
msgid "Sub-model"
msgstr "Sottomodello"

#. module: sms
#: code:addons/sms/models/sms_sms.py:0
#, python-format
msgid "Success"
msgstr "Successo"

#. module: sms
#: model:ir.actions.act_window,name:sms.sms_template_preview_action
msgid "Template Preview"
msgstr "Anteprima modello"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_template_preview__lang
msgid "Template Preview Language"
msgstr "Lingua di anteprima del modello"

#. module: sms
#: model:ir.actions.act_window,name:sms.sms_template_action
msgid "Templates"
msgstr "Modelli"

#. module: sms
#: code:addons/sms/models/sms_sms.py:0
#, python-format
msgid "The SMS Text Messages could not be resent."
msgstr "I messaggi SMS non possono essere reinviati."

#. module: sms
#: code:addons/sms/models/sms_api.py:0
#, python-format
msgid "The number you're trying to reach is not correctly formatted."
msgstr ""
"Il numero che stai cercando di raggiungere non è formattato correttamente."

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__model_id
#: model:ir.model.fields,help:sms.field_sms_template_preview__model_id
msgid "The type of document this template can be used with"
msgstr "Tipo di documento con il quale può essere usato il modello"

#. module: sms
#: code:addons/sms/models/sms_sms.py:0
#, python-format
msgid "There are no SMS Text Messages to resend."
msgstr "Non ci sono SMS da reinviare."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.res_partner_view_form
msgid ""
"This phone number is blacklisted for SMS Marketing. Click to unblacklist."
msgstr ""
"Il numero di telefono è nella lista nera del marketing via SMS. Fai clic per"
" toglierlo."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_mail_mail__message_type
#: model:ir.model.fields,field_description:sms.field_mail_message__message_type
msgid "Type"
msgstr "Tipologia"

#. module: sms
#: model:ir.model.fields,help:sms.field_base_automation__state
#: model:ir.model.fields,help:sms.field_ir_actions_server__state
#: model:ir.model.fields,help:sms.field_ir_cron__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Execute Python Code': a block of python code that will be executed\n"
"- 'Create': create a new record with new values\n"
"- 'Update a Record': update the values of a record\n"
"- 'Execute several actions': define an action that triggers several other server actions\n"
"- 'Send Email': automatically send an email (Discuss)\n"
"- 'Add Followers': add followers to a record (Discuss)\n"
"- 'Create Next Activity': create an activity (Discuss)"
msgstr ""
"Tipologia azione server. Sono disponibili i seguenti valori:\n"
"- \"Esegui codice Python\": esegue un blocco di codice Python\n"
"- \"Crea\": crea un nuovo record con nuovi valori\n"
"- \"Aggiorna record\": aggiorna i valori di un record\n"
"- \"Esegui alcune azioni\": definisce un'azione che attiva altre azioni server\n"
"- \"Invia e-mail\": invia una e-mail in modo automatico (Comunicazioni)\n"
"- \"Invita a seguire\": aggiunge chi segue a un record (Comunicazioni)\n"
"- \"Crea prossima attività\": crea un'attività (Comunicazioni)"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_composer__recipient_single_number_itf
msgid ""
"UX field allowing to edit the recipient number. If changed it will be stored"
" onto the recipient."
msgstr ""
"Campo UX che consente di modificare il numero del destinatario. Se "
"modificato, viene memorizzato nel destinatario."

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_unread
msgid "Unread Messages"
msgstr "Messaggi non letti"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Numero messaggi non letti"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_acc
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_acc
msgid "Unregistered Account"
msgstr "Account non registrato"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__template_id
msgid "Use Template"
msgstr "Modello da usare"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__use_active_domain
msgid "Use active domain"
msgstr "Usa dominio attivo"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__mass_use_blacklist
msgid "Use blacklist"
msgstr "Utilizza lista nera"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_composer__res_ids_count
msgid "Visible records count"
msgstr "Numero record visibili"

#. module: sms
#: code:addons/sms/models/sms_sms.py:0
#, python-format
msgid "Warning"
msgstr "Attenzione"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_res_partner__website_message_ids
msgid "Website Messages"
msgstr "Messaggi sito web"

#. module: sms
#: model:ir.model.fields,help:sms.field_res_partner__website_message_ids
msgid "Website communication history"
msgstr "Cronologia comunicazioni sito web"

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__sub_model_object_field
msgid ""
"When a relationship field is selected as first field, this field lets you "
"select the target field within the destination document model (sub-model)."
msgstr ""
"Quando un campo relazione viene selezionato come primo, permette di "
"selezionare il campo di destinazione all'interno del modello del documento "
"di destinazione (sottomodello)."

#. module: sms
#: model:ir.model.fields,help:sms.field_sms_template__sub_object
msgid ""
"When a relationship field is selected as first field, this field shows the "
"document model the relationship goes to."
msgstr ""
"Quando un campo relazione viene selezionato come primo, mostra il modello "
"del documento a cui si riferisce la relazione."

#. module: sms
#: model:ir.model.fields,help:sms.field_ir_model__is_mail_thread_sms
msgid "Whether this model supports messages and notifications through SMS"
msgstr "Indica se il modello supporta i messaggi e le notifiche via SMS"

#. module: sms
#: model:ir.model.fields.selection,name:sms.selection__mail_notification__failure_type__sms_number_format
#: model:ir.model.fields.selection,name:sms.selection__sms_sms__failure_type__sms_number_format
msgid "Wrong Number Format"
msgstr "Numero con formato errato"

#. module: sms
#: code:addons/sms/wizard/sms_resend.py:0
#, python-format
msgid "You do not have access to the message and/or related document."
msgstr "Accesso non consentito al messaggio e/o al relativo documento."

#. module: sms
#: code:addons/sms/models/sms_api.py:0
#, python-format
msgid "You don't have an eligible IAP account."
msgstr "Non hai un account IAP valido."

#. module: sms
#: code:addons/sms/models/sms_api.py:0
#, python-format
msgid "You don't have enough credits on your IAP account."
msgstr "Non hai abbastanza crediti sul tuo account IAP."

#. module: sms
#. openerp-web
#: code:addons/sms/static/src/js/fields_sms_widget.js:0
#, python-format
msgid ""
"Your SMS Text Message must include at least one non-whitespace character"
msgstr ""
"Il messaggio SMS deve includere almeno un carattere che non sia uno spazio "
"vuoto"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "are invalid."
msgstr "non lo sono."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "e.g. +1 415 555 0100"
msgstr "es. +1 415 555 0100"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "e.g. Calendar Reminder"
msgstr "es. Promemoria calendario"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "e.g. Contact"
msgstr "es. Contatto"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_view_form
msgid "e.g. en_US or {{ object.partner_id.lang }}"
msgstr "ad es. en_US o {{ object.partner_id.lang }}"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid ""
"recipients are valid\n"
"                                and"
msgstr ""
"destinatari sono validi\n"
"                                e"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_template_preview_form
msgid "record:"
msgstr ":"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "records instead. <br/>"
msgstr "record. <br/>"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "records selected."
msgstr "record selezionati."

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.sms_composer_view_form
msgid "to send to all"
msgstr "per inviare invece a tutti i"
