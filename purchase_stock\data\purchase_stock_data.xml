<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <function model="purchase.order.line" name="_update_qty_received_method" />

		<!--
             Stock rules and routes
        -->
        <record id="route_warehouse0_buy" model='stock.location.route'>
            <field name="name">Buy</field>
            <field name="company_id"></field>
            <field name="sequence">5</field>
        </record>

        <!-- enable purchase on main warehouse -->
        <record id="stock.warehouse0" model="stock.warehouse">
            <field name="buy_to_resupply" eval="True"/>
        </record>

    </data>
</odoo>
