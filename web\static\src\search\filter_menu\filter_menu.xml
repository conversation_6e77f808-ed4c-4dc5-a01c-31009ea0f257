<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.FilterMenu" owl="1">
        <Dropdown class="o_filter_menu btn-group" togglerClass="'btn btn-light'" t-on-dropdown-item-selected="onFilterSelected">
            <t t-set-slot="toggler">
                <i class="small mr-1" t-att-class="icon"/>
                <span class="o_dropdown_title">Filters</span>
            </t>
            <t t-set="currentGroup" t-value="null"/>
            <t t-foreach="items" t-as="item" t-key="item.id">
                <t t-if="currentGroup !== null and currentGroup !== item.groupNumber">
                    <div class="dropdown-divider" role="separator"/>
                </t>
                <t t-if="item.options">
                    <Dropdown togglerClass="'o_menu_item' + (item.isActive ? ' selected' : '')">
                        <t t-set-slot="toggler">
                            <t t-esc="item.description"/>
                        </t>
                        <t t-set="subGroup" t-value="null"/>
                        <t t-foreach="item.options" t-as="option" t-key="option.id">
                            <t t-if="subGroup !== null and subGroup !== option.groupNumber">
                                <div class="dropdown-divider" role="separator"/>
                            </t>
                            <DropdownItem class="o_item_option"
                                t-att-class="{ selected: option.isActive }"
                                checked="option.isActive"
                                payload="{ itemId: item.id, optionId: option.id }"
                                parentClosingMode="'none'"
                                t-esc="option.description"
                            />
                            <t t-set="subGroup" t-value="option.groupNumber"/>
                        </t>
                    </Dropdown>
                </t>
                <t t-else="">
                    <DropdownItem class="o_menu_item"
                        t-att-class="{ selected: item.isActive }"
                        checked="item.isActive"
                        payload="{ itemId: item.id }"
                        parentClosingMode="'none'"
                        t-esc="item.description"
                    />
                </t>
                <t t-set="currentGroup" t-value="item.groupNumber"/>
            </t>
            <t t-if="items.length">
                <div role="separator" class="dropdown-divider"/>
            </t>
            <CustomFilterItem/>
        </Dropdown>
    </t>

</templates>
