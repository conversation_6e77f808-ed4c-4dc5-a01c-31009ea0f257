<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_move_line_tree" model="ir.ui.view">
        <field name="name">stock.move.line.tree</field>
        <field name="model">stock.move.line</field>
        <field name="arch" type="xml">
            <tree string="Move Lines" create="0" default_order="date">
                <field name="date"/>
                <field name="reference" string="Reference"
                       invisible="context.get('no_reference', False)"/>
                <field name="product_id"/>
                <field name="lot_id" groups="stock.group_production_lot"/>
                <field name="location_id" groups="stock.group_stock_multi_locations" />
                <field name="location_dest_id" groups="stock.group_stock_multi_locations" />
                <field name="qty_done" string="Quantity Done"/>
                <field name="product_uom_id" options="{'no_open': True, 'no_create': True}" string="Unit of Measure" groups="uom.group_uom"/>
                <field name="company_id" groups="base.group_multi_company" force_save="1"/>
                <field name="state" widget='badge' decoration-success="state == 'done'" decoration-info="state not in ('done', 'cancel')"/>
            </tree>
        </field>
    </record>
    <record id="view_move_line_tree_detailed" model="ir.ui.view">
        <field name="name">stock.move.line.tree.detailed</field>
        <field name="model">stock.move.line</field>
        <field name="priority">25</field>
        <field name="arch" type="xml">
            <tree string="Move Lines" create="0" default_order="date">
                <field name="date" optional="hide"/>
                <field name="picking_id"/>
                <field name="picking_partner_id"/>
                <field name="product_id"/>
                <field name="lot_id" groups="stock.group_production_lot" optional="hide"/>
                <field name="location_id" groups="stock.group_stock_multi_locations" />
                <field name="location_dest_id" groups="stock.group_stock_multi_locations" />
                <field name="package_id" groups="stock.group_tracking_lot"/>
                <field name="qty_done" string="Quantity Done" optional="hide"/>
                <field name="product_uom_id" options="{'no_open': True, 'no_create': True}" optional="hide" string="Unit of Measure" groups="uom.group_uom"/>
                <field name="company_id" groups="base.group_multi_company" force_save="1" optional="hide"/>
                <field name="state" widget='badge' decoration-success="state == 'done'" decoration-info="state not in ('done', 'cancel')"/>
            </tree>
        </field>
    </record>

    <record id="view_move_line_form" model="ir.ui.view">
        <field name="name">stock.move.line.form</field>
        <field name="model">stock.move.line</field>
        <field name="arch" type="xml">
            <form string="Move Lines" create="0" edit="0">
                <header>
                    <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <field name="company_id" invisible="1"/>
                    <field name="picking_id" invisible="1"/>
                    <field name="product_uom_category_id" invisible="1"/>
                    <group>
                        <group>
                            <field name="date"/>
                            <field name="reference" string="Reference"/>
                            <field name="origin"/>
                            <field name="product_id"/>
                            <field name="location_id" options="{'no_create': True}" groups="stock.group_stock_multi_locations"/>
                            <field name="location_dest_id" options="{'no_create': True}" groups="stock.group_stock_multi_locations"/>
                        </group>
                        <group>
                            <label for="product_uom_qty" string="Quantity Reserved" attrs="{'invisible': [('state', '=', 'done')]}"/>
                            <div class="o_row" attrs="{'invisible': [('state', '=', 'done')]}">
                                <field name="product_uom_qty" readonly="1"/>
                                <field name="product_uom_id" options="{'no_create': True}" string="Unit of Measure" groups="uom.group_uom"/>
                            </div>
                            <label for="qty_done" string="Quantity Done"/>
                            <div class="o_row">
                                <field name="qty_done"/>
                                <field name="product_uom_id" options="{'no_create': True}" string="Unit of Measure" groups="uom.group_uom"/>
                            </div>
                            <field name="lot_id" groups="stock.group_production_lot" context="{'default_product_id': product_id, 'active_picking_id': picking_id, 'default_company_id': company_id}" attrs="{'invisible': [('lot_id', '=', False),('lot_name', '!=', False)]}"/>
                            <field name="lot_name" groups="stock.group_production_lot" attrs="{'invisible': ['|',('lot_id', '!=', False),('lot_name', '=', False)]}"/>
                            <field name="package_id" string="Source Package" groups="stock.group_tracking_lot"/>
                            <field name="result_package_id" string="Destination Package" groups="stock.group_tracking_lot"/>
                            <field name="owner_id" string="Owner" groups="stock.group_tracking_owner"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="stock_move_line_view_search" model="ir.ui.view">
        <field name="name">stock.move.line.search</field>
        <field name="model">stock.move.line</field>
        <field name="arch" type="xml">
            <search string="Stock Moves">
                <field name="picking_id" string="Transfer"/>
                <field name="reference" string="Reference"/>
                <field name="product_id"/>
                <field name="location_id" string="Source Location" groups="stock.group_stock_multi_locations"/>
                <field name="location_dest_id" string="Destination Location" groups="stock.group_stock_multi_locations"/>
                <field name="lot_id" string="Lot/Serial Number" groups="stock.group_production_lot"/>
                <field name="package_id" string="Source Package" groups="stock.group_tracking_lot"/>
                <field name="result_package_id" string="Destination Package" groups="stock.group_tracking_lot"/>
                <field name="owner_id" string="Owner" groups="stock.group_tracking_owner"/>
                <separator/>
                <filter string="To Do" name="todo" domain="[('state', 'not in', ['done', 'draft', 'cancel'])]"/>
                <filter string="Done" name="done" domain="[('state', '=', 'done')]"/>
                <separator/>
                <filter string="Incoming" name="incoming" domain="[('picking_id.picking_type_id.code', '=', 'incoming')]"/>
                <filter string="Outgoing" name="outgoing" domain="[('picking_id.picking_type_id.code', '=', 'outgoing')]"/>
                <filter string="Internal" name="internal" domain="[('picking_id.picking_type_id.code', '=', 'internal')]"/>
                <filter string="Manufacturing" name="manufacturing" domain="[('picking_id.picking_type_id.code', '=', 'mrp_operation')]" invisible="1"/>
                <separator/>
                <filter name="date" date="date" default_period="this_month"/>
                <filter string="Last 30 Days" name="filter_last_30_days" domain="[('date','&gt;=', (context_today() - relativedelta(days=30)).strftime('%%Y-%%m-%%d'))]"/>
                <filter string="Last 3 Months" name="filter_last_3_months" domain="[('date','&gt;=', (context_today() - relativedelta(months=3)).strftime('%%Y-%%m-%%d'))]"/>
                <filter string="Last 12 Months" name="filter_last_12_months" domain="[('date','&gt;=', (context_today() - relativedelta(years=1)).strftime('%%Y-%%m-%%d'))]"/>
                <separator/>
                <filter string="Inventory" name="inventory" domain="[('is_inventory', '=', True)]"/>
                <separator/>
                <group expand="0" string="Group By">
                    <filter string="Product" name="groupby_product_id" domain="[]" context="{'group_by': 'product_id'}"/>
                    <filter string="Status" name="by_state" domain="[]"  context="{'group_by': 'state'}"/>
                    <filter string="Date" name="by_date" domain="[]" context="{'group_by': 'date'}"/>
                    <filter string="Transfers" name="by_picking" domain="[]" context="{'group_by': 'picking_id'}"/>
                    <filter string="Location" name="by_location" domain="[]" context="{'group_by': 'location_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="view_stock_move_line_kanban" model="ir.ui.view">
        <field name="name">stock.move.line.kanban</field>
        <field name="model">stock.move.line</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile">
                <field name="location_id" invisible="1"/>
                <field name="location_dest_id" invisible="1"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_card oe_kanban_global_click">
                            <strong><field name="product_id"/></strong>
                            <field name="picking_id"/>
                            <div class="row">
                                <div class="col-6">
                                    <field name="lot_id" invisible="not context.get('show_lots_m2o')" groups="stock.group_production_lot" context="{'default_product_id': product_id, 'active_picking_id': picking_id}"/>
                                    <field name="lot_name" invisible="not context.get('show_lots_text')" groups="stock.group_production_lot"/>
                                    <field name="qty_done" string="Quantity Done"/>
                                    <field name="product_uom_id" string="Unit of Measure" groups="uom.group_uom"/>
                                </div>
                                <div class="col-6 text-right">
                                    <field name="date"/>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="stock_move_line_action" model="ir.actions.act_window">
            <field name="name">Product Moves</field>
            <field name="res_model">stock.move.line</field>
            <field name="type">ir.actions.act_window</field>
            <field name="view_mode">tree,kanban,pivot,form</field>
            <field name="view_id" ref="view_move_line_tree"/>
            <field name="context">{'search_default_filter_last_12_months': 1, 'search_default_done': 1, 'search_default_groupby_product_id': 1, 'create': 0}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_empty_folder">
                    There's no product move yet
                </p><p>
                    This menu gives you the full traceability of inventory operations on a specific product.
                    You can filter on the product to see all the past movements for the product.
                </p>
            </field>
    </record>

    <menuitem
        action="stock_move_line_action"
        id="stock_move_line_menu"
        parent="stock.menu_warehouse_report" sequence="150"/>
</odoo>
