# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment
# 
# Translators:
# <PERSON> <amuni<PERSON><PERSON><PERSON>@gmail.com>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# oon arfiand<PERSON> (OonID) <<EMAIL>>, 2022
# <PERSON><PERSON> Use<PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# Muft<PERSON> Syuhada <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# whenwesober, 2022
# <PERSON><PERSON><PERSON> <wahyuse<PERSON><EMAIL>>, 2022
# <PERSON>to <PERSON> <<EMAIL>>, 2022
# Abe Manyo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:53+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: Abe Manyo, 2023\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#, python-format
msgid "1 Meeting"
msgstr "1 Meeting"

#. module: hr_recruitment
#. openerp-web
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#, python-format
msgid "<b>Click to view</b> the application."
msgstr "<b>Klik untuk melihat</b> aplikasi."

#. module: hr_recruitment
#. openerp-web
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#, python-format
msgid "<b>Did you apply by sending an email?</b> Check incoming applications."
msgstr ""
"<b>Apakah Anda melamar dengan mengirimkan email?</b> Periksa lamaran masuk."

#. module: hr_recruitment
#. openerp-web
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#, python-format
msgid "<b>Drag this card</b>, to qualify him for a first interview."
msgstr ""
"<b>Tarik kartu ini</b>, untuk memberikan dia kualifikasi untuk wawancara "
"pertama."

#. module: hr_recruitment
#. openerp-web
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#, python-format
msgid ""
"<div><b>Try to send an email</b> to the applicant.</div><div><i>Tips: All "
"emails sent or received are saved in the history here</i>"
msgstr ""
"<div><b>Coba kirim email</b> ke pelamar.</div><div><i>Tip: Semua email yang "
"dikirim atau diterima akan disimpan di sejarah di sini</i>"

#. module: hr_recruitment
#. openerp-web
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#, python-format
msgid ""
"<div>Great job! You hired a new colleague!</div><div>Try the Website app to "
"publish job offers online.</div>"
msgstr ""
"<div>Kerja bagus! Anda merekrut rekan kerja baru!</div><div>Coba app Website"
" untuk memposting lowongan kerja online.</div>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"
msgstr "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "<i class=\"fa fa-envelope-o\" role=\"img\" aria-label=\"Alias\" title=\"Alias\"/>"
msgstr "<i class=\"fa fa-envelope-o\" role=\"img\" aria-label=\"Alias\" title=\"Alias\"/>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "<i class=\"fa fa-mobile mr4\" role=\"img\" aria-label=\"Mobile\" title=\"Mobile\"/>"
msgstr "<i class=\"fa fa-mobile mr4\" role=\"img\" aria-label=\"Mobile\" title=\"Mobile\"/>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "<i class=\"fa fa-paperclip\" role=\"img\" aria-label=\"Documents\"/>"
msgstr "<i class=\"fa fa-paperclip\" role=\"img\" aria-label=\"Dokumen\"/>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid ""
"<span attrs=\"{'invisible': [('is_warning_visible', '=', False)]}\">\n"
"                            <span class=\"fa fa-exclamation-triangle text-danger pl-3\">\n"
"                            </span>\n"
"                            <span class=\"text-danger\">\n"
"                                All applications will lose their hired date and hired status.\n"
"                            </span>\n"
"                        </span>"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid ""
"<span attrs=\"{'invisible':[('salary_expected_extra','=',False)]}\"> + "
"</span>"
msgstr ""
"<span attrs=\"{'invisible':[('salary_expected_extra','=',False)]}\"> + "
"</span>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid ""
"<span attrs=\"{'invisible':[('salary_proposed_extra','=',False)]}\"> + "
"</span>"
msgstr ""
"<span attrs=\"{'invisible':[('salary_proposed_extra','=',False)]}\"> + "
"</span>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid ""
"<span class=\"badge badge-pill badge-danger pull-right mr-4\" "
"attrs=\"{'invisible': [('active', '=', True)]}\">Refused</span>"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "<span class=\"bg-success\">Hired</span>"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "<span class=\"o_stat_text\">Trackers</span>"
msgstr "<span class=\"o_stat_text\">Pelacak</span>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid ""
"<span title=\"Link Trackers\"><i class=\"fa fa-lg fa-envelope\" role=\"img\""
" aria-label=\"Link Trackers\"/></span>"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_recruitment_stage_kanban
msgid "<span>Folded in Recruitment Pipe: </span>"
msgstr "<span>Sembunyikan di Saluran Perekrutan: </span>"

#. module: hr_recruitment
#: model:mail.template,body_html:hr_recruitment.email_template_data_applicant_congratulations
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"background-color: white; border-collapse: collapse; margin-left: 20px;\">\n"
"    <tr>\n"
"        <td valign=\"top\" style=\"padding: 0px 10px;\">\n"
"            <div style=\"font-size: 13px; margin: 0px; padding: 0px;\">\n"
"                Hello,\n"
"                <br/><br/>\n"
"                We confirm we successfully received your application for the job\n"
"                \"<a t-att-href=\"hasattr(object.job_id, 'website_url') and object.job_id.website_url or ''\" style=\"color:#9A6C8E;\"><strong t-out=\"object.job_id.name or ''\">Experienced Developer</strong></a>\" at <strong t-out=\"object.company_id.name or ''\">YourCompany</strong>.\n"
"                <br/><br/>\n"
"                We will come back to you shortly.\n"
"\n"
"                <div t-if=\"'website_url' in object.job_id and object.job_id.website_url\" style=\"padding: 16px 8px 16px 8px;\">\n"
"                    <a t-att-href=\"object.job_id.website_url\" style=\"background-color: #875a7b; text-decoration: none; color: #fff; padding: 8px 16px 8px 16px; border-radius: 5px;\">Job Description</a>\n"
"                </div>\n"
"\n"
"                <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 16px 0px 16px 0px;\"/>\n"
"                <t t-if=\"object.user_id\">\n"
"                    <h3 style=\"color:#9A6C8E;\"><strong>Your Contact:</strong></h3>\n"
"                    <table>\n"
"                        <tr>\n"
"                            <td width=\"75\">\n"
"                                <img t-attf-src=\"/web/image/res.users/{{ object.user_id.id }}/avatar_128\" alt=\"Avatar\" style=\"vertical-align:baseline; width: 64px; height: 64px; object-fit: cover;\"/>\n"
"                            </td>\n"
"                            <td>\n"
"                                <strong t-out=\"object.user_id.name or ''\">Mitchell Admin</strong><br/>\n"
"                                <span>Email: <t t-out=\"object.user_id.email or ''\"><EMAIL></t></span><br/>\n"
"                                <span>Phone: <t t-out=\"object.user_id.phone or ''\">******-123-4567</t></span>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                    <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 16px 0px 16px 0px;\"/>\n"
"                </t>\n"
"\n"
"                <h3 style=\"color:#9A6C8E;\"><strong>What is the next step?</strong></h3>\n"
"                We usually <strong>answer applications within a few days.</strong><br/><br/>\n"
"                Feel free to <strong>contact us if you want a faster\n"
"                feedback</strong> or if you don't get news from us\n"
"                quickly enough (just reply to this email).\n"
"\n"
"                <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 17px 0px 16px 0px;\"/>\n"
"                <t t-set=\"location\" t-value=\"''\"/>\n"
"                <t t-if=\"object.job_id.address_id.name\">\n"
"                    <strong t-out=\"object.job_id.address_id.name or ''\">Teksa SpA</strong><br/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.street\">\n"
"                    <t t-out=\"object.job_id.address_id.street or ''\">Puerto Madero 9710</t><br/>\n"
"                    <t t-set=\"location\" t-value=\"object.job_id.address_id.street\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.street2\">\n"
"                    <t t-out=\"object.job_id.address_id.street2 or ''\">Of A15, Santiago (RM)</t><br/>\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.street2)\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.city\">\n"
"                    <t t-out=\"object.job_id.address_id.city or ''\">Pudahuel</t>,\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.city)\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.state_id.name\">\n"
"                    <t t-out=\"object.job_id.address_id.state_id.name or ''\">C1</t>,\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.state_id.name)\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.zip\">\n"
"                    <t t-out=\"object.job_id.address_id.zip or ''\">98450</t>\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.zip)\"/>\n"
"                </t>\n"
"                <br/>\n"
"                <t t-if=\"object.job_id.address_id.country_id.name\">\n"
"                    <t t-out=\"object.job_id.address_id.country_id.name or ''\">Argentina</t><br/>\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.country_id.name)\"/>\n"
"                </t>\n"
"                <br/>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</table>"
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"background-color: white; border-collapse: collapse; margin-left: 20px;\">\n"
"    <tr>\n"
"        <td valign=\"top\" style=\"padding: 0px 10px;\">\n"
"            <div style=\"font-size: 13px; margin: 0px; padding: 0px;\">\n"
"                Halo,\n"
"                <br/><br/>\n"
"                Kami mengkonfirmasi bahwa kami telah menerima lamaran Anda untuk lowongan ini\n"
"                \"<a t-att-href=\"hasattr(object.job_id, 'website_url') and object.job_id.website_url or ''\" style=\"color:#9A6C8E;\"><strong t-out=\"object.job_id.name or ''\">Developer Berpengalaman</strong></a>\" pada <strong t-out=\"object.company_id.name or ''\">YourCompany</strong>.\n"
"                <br/><br/>\n"
"                Kami akan menghubungi secepat mungkin.\n"
"\n"
"                <div t-if=\"'website_url' in object.job_id and object.job_id.website_url\" style=\"padding: 16px 8px 16px 8px;\">\n"
"                    <a t-att-href=\"object.job_id.website_url\" style=\"background-color: #875a7b; text-decoration: none; color: #fff; padding: 8px 16px 8px 16px; border-radius: 5px;\">Keterangan Pekerjaan</a>\n"
"                </div>\n"
"\n"
"                <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 16px 0px 16px 0px;\"/>\n"
"                <t t-if=\"object.user_id\">\n"
"                    <h3 style=\"color:#9A6C8E;\"><strong>Kontak Anda:</strong></h3>\n"
"                    <table>\n"
"                        <tr>\n"
"                            <td width=\"75\">\n"
"                                <img t-attf-src=\"/web/image/res.users/{{ object.user_id.id }}/avatar_128\" alt=\"Avatar\" style=\"vertical-align:baseline; width: 64px; height: 64px; object-fit: cover;\"/>\n"
"                            </td>\n"
"                            <td>\n"
"                                <strong t-out=\"object.user_id.name or ''\">Mitchell Admin</strong><br/>\n"
"                                <span>Email: <t t-out=\"object.user_id.email or ''\"><EMAIL></t></span><br/>\n"
"                                <span>Telepon: <t t-out=\"object.user_id.phone or ''\">******-123-4567</t></span>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                    <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 16px 0px 16px 0px;\"/>\n"
"                </t>\n"
"\n"
"                <h3 style=\"color:#9A6C8E;\"><strong>Apakah langkah berikutnya?</strong></h3>\n"
"                Kami biasanya <strong>menjawab lamaran dalam beberapa hari.</strong><br/><br/>\n"
"                Jangan sungkan untuk <strong>menghubungi kami bila Anda ingin mendapatkan feedback\n"
"                lebih cepat</strong> atau bila Anda menunggu terlalu lama\n"
"                (cukup membalas email ini).\n"
"\n"
"                <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 17px 0px 16px 0px;\"/>\n"
"                <t t-set=\"location\" t-value=\"''\"/>\n"
"                <t t-if=\"object.job_id.address_id.name\">\n"
"                    <strong t-out=\"object.job_id.address_id.name or ''\">Teksa SpA</strong><br/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.street\">\n"
"                    <t t-out=\"object.job_id.address_id.street or ''\">Puerto Madero 9710</t><br/>\n"
"                    <t t-set=\"location\" t-value=\"object.job_id.address_id.street\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.street2\">\n"
"                    <t t-out=\"object.job_id.address_id.street2 or ''\">Of A15, Santiago (RM)</t><br/>\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.street2)\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.city\">\n"
"                    <t t-out=\"object.job_id.address_id.city or ''\">Pudahuel</t>,\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.city)\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.state_id.name\">\n"
"                    <t t-out=\"object.job_id.address_id.state_id.name or ''\">C1</t>,\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.state_id.name)\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.zip\">\n"
"                    <t t-out=\"object.job_id.address_id.zip or ''\">98450</t>\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.zip)\"/>\n"
"                </t>\n"
"                <br/>\n"
"                <t t-if=\"object.job_id.address_id.country_id.name\">\n"
"                    <t t-out=\"object.job_id.address_id.country_id.name or ''\">Argentina</t><br/>\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.country_id.name)\"/>\n"
"                </t>\n"
"                <br/>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</table>"

#. module: hr_recruitment
#: model:mail.template,body_html:hr_recruitment.email_template_data_applicant_interest
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"background-color: white; border-collapse: collapse; margin-left: 20px;\">\n"
"    <tr>\n"
"        <td valign=\"top\" style=\"padding: 0px 10px;\">\n"
"            <div style=\"text-align: center\">\n"
"                <h2>Congratulations!</h2>\n"
"                <div style=\"color:grey;\">Your resume has been positively reviewed.</div>\n"
"                <img src=\"/hr_recruitment/static/src/img/congratulations.png\" alt=\"Congratulations!\" style=\"width:175px;margin:20px 0;\"/>\n"
"            </div>\n"
"            <div style=\"font-size: 13px; margin: 0px; padding: 0px;\">\n"
"                We just reviewed your resume, and it caught our\n"
"                attention. As we think you might be great for the\n"
"                position, your application has been short listed for a\n"
"                call or an interview.\n"
"                <br/><br/>\n"
"                <div t-if=\"'website_url' in object.job_id and object.job_id.website_url\" style=\"padding: 16px 8px 16px 8px;\">\n"
"                    <a t-att-href=\"object.job_id.website_url\" style=\"background-color: #875a7b; text-decoration: none; color: #fff; padding: 8px 16px 8px 16px; border-radius: 5px;\">Job Description</a>\n"
"                </div>\n"
"\n"
"                <t t-if=\"object.user_id\">\n"
"                    You will soon be contacted by:\n"
"                    <table>\n"
"                        <tr>\n"
"                            <td width=\"75\">\n"
"                                <img t-attf-src=\"/web/image/res.users/{{ object.user_id.id }}/avatar_128\" alt=\"Avatar\" style=\"vertical-align:baseline; width: 64px; height: 64px; object-fit: cover;\"/>\n"
"                            </td>\n"
"                            <td>\n"
"                                <strong t-out=\"object.user_id.name or ''\">Mitchell Admin</strong><br/>\n"
"                                <span>Email: <t t-out=\"object.user_id.email or ''\"><EMAIL></t></span><br/>\n"
"                                <span>Phone: <t t-out=\"object.user_id.phone or ''\">******-123-4567</t></span>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                    <br/><br/>\n"
"                </t>\n"
"                See you soon,\n"
"                <div style=\"font-size: 11px; color: grey;\">\n"
"                    -- <br/>\n"
"                    The HR Team\n"
"                    <t t-if=\"'website_url' in object.job_id and hasattr(object.job_id, 'website_url') and object.job_id.website_url\">\n"
"                        Discover <a href=\"/jobs\" style=\"text-decoration:none;color:#717188;\">all our jobs</a>.<br/>\n"
"                    </t>\n"
"                </div>\n"
"\n"
"                <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 16px 0px 16px 0px;\"/>\n"
"                <h3 style=\"color:#9A6C8E;\"><strong>What is the next step?</strong></h3>\n"
"                We usually <strong>answer applications within a few days</strong>.\n"
"                <br/><br/>\n"
"                The next step is either a call or a meeting in our offices.\n"
"                <br/>\n"
"                Feel free to <strong>contact us if you want a faster\n"
"                feedback</strong> or if you don't get news from us\n"
"                quickly enough (just reply to this email).\n"
"                <br/>\n"
"\n"
"                <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 17px 0px 16px 0px;\"/>\n"
"                <t t-set=\"location\" t-value=\"''\"/>\n"
"                <t t-if=\"object.job_id.address_id.name\">\n"
"                    <strong t-out=\"object.job_id.address_id.name or ''\">Teksa SpA</strong><br/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.street\">\n"
"                    <t t-out=\"object.job_id.address_id.street or ''\">Puerto Madero 9710</t><br/>\n"
"                    <t t-set=\"location\" t-value=\"object.job_id.address_id.street\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.street2\">\n"
"                    <t t-out=\"object.job_id.address_id.street2 or ''\">Of A15, Santiago (RM)</t><br/>\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.street2)\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.city\">\n"
"                    <t t-out=\"object.job_id.address_id.city or ''\">Pudahuel</t>,\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.city)\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.state_id.name\">\n"
"                    <t t-out=\"object.job_id.address_id.state_id.name or ''\">C1</t>,\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.state_id.name)\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.zip\">\n"
"                    <t t-out=\"object.job_id.address_id.zip or ''\">98450</t>\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.zip)\"/>\n"
"                </t>\n"
"                <br/>\n"
"                <t t-if=\"object.job_id.address_id.country_id.name\">\n"
"                    <t t-out=\"object.job_id.address_id.country_id.name or ''\">Argentina</t><br/>\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.country_id.name)\"/>\n"
"                </t>\n"
"                <br/>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</table>"
msgstr ""

#. module: hr_recruitment
#: model:mail.template,body_html:hr_recruitment.email_template_data_applicant_not_interested
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"    <tr>\n"
"        <td valign=\"top\">\n"
"            <div style=\"font-size: 13px; margin: 0px; padding: 0px;\">\n"
"                Dear,<br/><br/>\n"
"                We would like to thank you for your interest and your time.<br/>\n"
"                We wish you all the best in your future endeavors.\n"
"                <br/><br/>\n"
"                Best<br/>\n"
"                <div style=\"font-size: 11px; color: grey;\">\n"
"                    <t t-if=\"object.user_id\">\n"
"                        -- <br/>\n"
"                        <strong t-out=\"object.user_id.name or ''\">Marc Demo</strong><br/>\n"
"                        Email: <t t-out=\"object.user_id.email or ''\"><EMAIL></t><br/>\n"
"                        Phone: <t t-out=\"object.user_id.phone or ''\">******-123-4567</t>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        -- <br/>\n"
"                        <t t-out=\"object.company_id.name or ''\">YourCompany</t><br/>\n"
"                        The HR Team<br/>\n"
"                    </t>\n"
"                </div>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</table>\n"
"        "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"    <tr>\n"
"        <td valign=\"top\">\n"
"            <div style=\"font-size: 13px; margin: 0px; padding: 0px;\">\n"
"                Yth,<br/><br/>\n"
"                Kami mengucapkan terima kasih untuk perhatian dan waktu Anda.<br/>\n"
"                Kami mengharapkan yang terbaik bagi Anda untuk usaha Anda di masa depan.\n"
"                <br/><br/>\n"
"                Salam<br/>\n"
"                <div style=\"font-size: 11px; color: grey;\">\n"
"                    <t t-if=\"object.user_id\">\n"
"                        -- <br/>\n"
"                        <strong t-out=\"object.user_id.name or ''\">Marc Demo</strong><br/>\n"
"                        Email: <t t-out=\"object.user_id.email or ''\"><EMAIL></t><br/>\n"
"                        Telepon: <t t-out=\"object.user_id.phone or ''\">******-123-4567</t>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        -- <br/>\n"
"                        <t t-out=\"object.company_id.name or ''\">YourCompany</t><br/>\n"
"                        Tim HR<br/>\n"
"                    </t>\n"
"                </div>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</table>\n"
"        "

#. module: hr_recruitment
#: model:mail.template,body_html:hr_recruitment.email_template_data_applicant_refuse
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"    <tr>\n"
"        <td valign=\"top\">\n"
"            <div style=\"font-size: 13px; margin: 0px; padding: 0px;\">\n"
"                Hello,<br/><br/>\n"
"                Thank you for your interest in joining the\n"
"                <b><t t-out=\"object.company_id.name or ''\">YourCompany</t></b> team.  We\n"
"                wanted to let you know that, although your resume is\n"
"                competitive, our hiring team reviewed your application\n"
"                and <b>did not select it for further consideration</b>.\n"
"                <br/><br/>\n"
"                Please note that recruiting is hard, and we can make\n"
"                mistakes. Do not hesitate to reply to this email if you\n"
"                think we made a mistake, or if you want more information\n"
"                about our decision.\n"
"                <br/><br/>\n"
"                We will, however, keep your resume on record and get in\n"
"                touch with you about future opportunities that may be a\n"
"                better fit for your skills and experience.\n"
"                <br/><br/>\n"
"                We wish you all the best in your job search and hope we\n"
"                will have the chance to consider you for another role\n"
"                in the future.\n"
"                <br/><br/>\n"
"                Thank you,\n"
"                <div style=\"font-size: 11px; color: grey;\">\n"
"                    <t t-if=\"object.user_id\">\n"
"                        -- <br/>\n"
"                        <strong t-out=\"object.user_id.name or ''\">Mitchell Admin</strong><br/>\n"
"                        Email: <t t-out=\"object.user_id.email or ''\"><EMAIL></t><br/>\n"
"                        Phone: <t t-out=\"object.user_id.phone or ''\">******-123-4567</t>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        -- <br/>\n"
"                        <t t-out=\"object.company_id.name or ''\">YourCompany</t><br/>\n"
"                        The HR Team\n"
"                    </t>\n"
"                </div>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</table>\n"
"        "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"    <tr>\n"
"        <td valign=\"top\">\n"
"            <div style=\"font-size: 13px; margin: 0px; padding: 0px;\">\n"
"                Halo,<br/><br/>\n"
"                Terima kasih untuk keinginan Anda bergabung dengan tim kami di\n"
"                <b><t t-out=\"object.company_id.name or ''\">YourCompany</t></b>.  Kami\n"
"                ingin memberitahu Anda, walaupun resume Anda\n"
"                kompetitif, tim perekrutan kami meninjau lamaran Anda \n"
"                dan <b>tidak memilih Anda untuk pertimbangan lebih lanjut</b>.\n"
"                <br/><br/>\n"
"                Mohon ingat bahwa perekrutan itu susah, dan kami bisa\n"
"                membuat kesalahan. Jangan sungkan untuk membalas       email ini bila\n"
"                Anda berpikir kami salah, atau bila Anda ingin informasi\n"
"                lebih lanjut mengenai keputusan kami.\n"
"                <br/><br/>\n"
"                Namun kami akan menyimpan resume Anda agar kami\n"
"                dapat menghubungi Anda mengenai kesempatan masa\n"
"                depan yang mungkin lebih cocok untuk ketrampilan dan pengalaman Anda.\n"
"                <br/><br/>\n"
"                Kami mengharapkan yang terbaik untuk Anda dalam pelamaran kerja Anda dan berharap kami\n"
"                akan memiliki kesempatan untuk mempertimbangkan Anda lagi untuk peran lain\n"
"                di masa depan.\n"
"                <br/><br/>\n"
"                Terima kasih,,\n"
"                <div style=\"font-size: 11px; color: grey;\">\n"
"                    <t t-if=\"object.user_id\">\n"
"                        -- <br/>\n"
"                        <strong t-out=\"object.user_id.name or ''\">Mitchell Admin</strong><br/>\n"
"                        Email: <t t-out=\"object.user_id.email or ''\"><EMAIL></t><br/>\n"
"                        Telepon: <t t-out=\"object.user_id.phone or ''\">******-123-4567</t>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        -- <br/>\n"
"                        <t t-out=\"object.company_id.name or ''\">YourCompany</t><br/>\n"
"                        Tim HR\n"
"                    </t>\n"
"                </div>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</table>\n"
"        "

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Kamus Python yang akan dievaluasi untuk memberikan nilai baku ketika membuat"
" catatan baru untuk alias ini."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_needaction
msgid "Action Needed"
msgstr "Perlu Tindakan"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__active
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__active
msgid "Active"
msgstr "Aktif"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_ids
msgid "Activities"
msgstr "Aktivitas"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Dekorasi Pengecualian Aktivitas"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_state
msgid "Activity State"
msgstr "Status Aktivitas"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikon Jenis Aktifitas"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.mail_activity_type_action_config_hr_applicant
#: model:ir.ui.menu,name:hr_recruitment.hr_recruitment_menu_config_activity_type
msgid "Activity Types"
msgstr "Jenis Aktivitas"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_job_stage_act
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_recruitment_stage_act
msgid "Add a new stage in the recruitment process"
msgstr "Tambahkan tahap baru di proses rekrutmen"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_applicant_category_action
msgid "Add a new tag"
msgstr "Tambahkan tanda baru"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__address_id
msgid "Address where employees are working"
msgstr "Alamat tempat karyawan bekerja"

#. module: hr_recruitment
#: model:res.groups,name:hr_recruitment.group_hr_recruitment_manager
msgid "Administrator"
msgstr "Administrator"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_id
msgid "Alias"
msgstr "Alias"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_contact
msgid "Alias Contact Security"
msgstr "Alias Kontak Keamanan"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__alias_id
msgid "Alias ID"
msgstr "ID Alias"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_name
msgid "Alias Name"
msgstr "Nama Alias"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_domain
msgid "Alias domain"
msgstr "Domain alias"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_model_id
msgid "Aliased Model"
msgstr "Model Alias"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__all_application_count
msgid "All Application Count"
msgstr "Jumlah Semua Lamaran"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_crm_case_categ_all_app
msgid "All Applications"
msgstr "Semua Aplikasi"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_applicant
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__applicant_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_calendar_event__applicant_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_employee__applicant_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Applicant"
msgstr "Pelamar"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_recruitment_degree
msgid "Applicant Degree"
msgstr "Gelar Pelamar"

#. module: hr_recruitment
#: model:mail.message.subtype,name:hr_recruitment.mt_applicant_hired
#: model:mail.message.subtype,name:hr_recruitment.mt_job_applicant_hired
msgid "Applicant Hired"
msgstr "Pelamar Diterima"

#. module: hr_recruitment
#: model:mail.message.subtype,name:hr_recruitment.mt_job_applicant_stage_changed
msgid "Applicant Stage Changed"
msgstr "Perubahan Tahap Pelamar"

#. module: hr_recruitment
#: model:mail.message.subtype,description:hr_recruitment.mt_applicant_new
msgid "Applicant created"
msgstr "Pelamar dibuat"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__email_from
msgid "Applicant email"
msgstr ""

#. module: hr_recruitment
#: model:mail.message.subtype,description:hr_recruitment.mt_applicant_hired
msgid "Applicant hired"
msgstr "Pelamar diterima"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_hired_template
msgid "Applicant hired<br/>"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__partner_name
msgid "Applicant's Name"
msgstr "Nama Pelamar"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__applicant_without_email
msgid "Applicant(s) not having email"
msgstr "Pelamar tidak memiliki email"

#. module: hr_recruitment
#: model:mail.template,name:hr_recruitment.email_template_data_applicant_congratulations
msgid "Applicant: Acknowledgement"
msgstr ""

#. module: hr_recruitment
#: model:mail.template,name:hr_recruitment.email_template_data_applicant_interest
msgid "Applicant: Interest"
msgstr ""

#. module: hr_recruitment
#: model:mail.template,name:hr_recruitment.email_template_data_applicant_not_interested
msgid "Applicant: Not interested anymore"
msgstr ""

#. module: hr_recruitment
#: model:mail.template,name:hr_recruitment.email_template_data_applicant_refuse
msgid "Applicant: Refuse"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_tree_view_job
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_calendar_view
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_activity
msgid "Applicants"
msgstr "Pelamar"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_applications
msgid ""
"Applicants and their attached CV are created automatically when an email is sent.\n"
"                If you install the document management modules, all resumes are indexed automatically,\n"
"                so that you can easily search through their content."
msgstr ""
"Pelamar dan lampiran CV mereka akan dibuat otomatis ketika email dikirim.\n"
"                Jika Anda memasang modul manajemen dokumen, semua lamaran akan diberikan indeks secara otomatis,\n"
"                supaya Anda dapat mencarinya dengan mudah melalui kontennya."

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.crm_case_categ0_act_job
msgid ""
"Applicants and their attached CV are created automatically when an email is sent.\n"
"            If you install the document management modules, all resumes are indexed automatically,\n"
"            so that you can easily search through their content."
msgstr ""
"Pelamar dan lampiran CV mereka akan dibuat otomatis ketika email dikirim.\n"
" Jika Anda memasang modul manajemen dokumen, semua lamaran akan diberikan indeks secara otomatis,\n"
" supaya Anda dapat mencarinya dengan mudah melalui kontennya."

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid ""
"Applicants can send resume to this email address,<br/>it will create an "
"application automatically"
msgstr ""
"Pelamar dapat mengirim resume ke alamat email ini, <br/> ini akan membuat "
"lamaran secara otomatis"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__application_count
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__application_count
msgid "Application Count"
msgstr "Jumlah Lamaran"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Application Summary"
msgstr "Ringkasan Lamaran"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "Application email"
msgstr "Email lamaran"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_applications
#: model:ir.actions.act_window,name:hr_recruitment.crm_case_categ0_act_job
#: model:ir.ui.menu,name:hr_recruitment.menu_crm_case_categ0_act_job
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Applications"
msgstr "Lamaran"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__application_count
msgid "Applications with the same email"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Applications<br/>"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__job_id
msgid "Applied Job"
msgstr "Pekerjaan Yang Dilamar"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__priority
msgid "Appreciation"
msgstr "Penghargaan"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Archived"
msgstr "Diarsipkan"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Archived / Refused"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__date_open
msgid "Assigned"
msgstr "Ditugaskan"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_attachment_count
msgid "Attachment Count"
msgstr "Hitungan Lampiran"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__attachment_ids
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Attachments"
msgstr "Lampiran"

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#, python-format
msgid "Attachments, like resumes, get indexed automatically."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__availability
msgid "Availability"
msgstr "Ketersediaan"

#. module: hr_recruitment
#: model:hr.recruitment.degree,name:hr_recruitment.degree_bachelor
msgid "Bachelor Degree"
msgstr "Gelar Sarjana"

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#: model:hr.applicant,legend_blocked:hr_recruitment.hr_case_advertisement
#: model:hr.applicant,legend_blocked:hr_recruitment.hr_case_dev0
#: model:hr.applicant,legend_blocked:hr_recruitment.hr_case_dev1
#: model:hr.applicant,legend_blocked:hr_recruitment.hr_case_dev2
#: model:hr.applicant,legend_blocked:hr_recruitment.hr_case_dev3
#: model:hr.applicant,legend_blocked:hr_recruitment.hr_case_financejob0
#: model:hr.applicant,legend_blocked:hr_recruitment.hr_case_financejob1
#: model:hr.applicant,legend_blocked:hr_recruitment.hr_case_fresher0
#: model:hr.applicant,legend_blocked:hr_recruitment.hr_case_marketingjob0
#: model:hr.applicant,legend_blocked:hr_recruitment.hr_case_mkt0
#: model:hr.applicant,legend_blocked:hr_recruitment.hr_case_mkt1
#: model:hr.applicant,legend_blocked:hr_recruitment.hr_case_programmer
#: model:hr.applicant,legend_blocked:hr_recruitment.hr_case_salesman0
#: model:hr.applicant,legend_blocked:hr_recruitment.hr_case_salesman1
#: model:hr.applicant,legend_blocked:hr_recruitment.hr_case_traineemca0
#: model:hr.applicant,legend_blocked:hr_recruitment.hr_case_traineemca1
#: model:hr.applicant,legend_blocked:hr_recruitment.hr_case_yrsexperienceinphp0
#: model:hr.recruitment.stage,legend_blocked:hr_recruitment.stage_job1
#: model:hr.recruitment.stage,legend_blocked:hr_recruitment.stage_job2
#: model:hr.recruitment.stage,legend_blocked:hr_recruitment.stage_job3
#: model:hr.recruitment.stage,legend_blocked:hr_recruitment.stage_job4
#: model:hr.recruitment.stage,legend_blocked:hr_recruitment.stage_job5
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#, python-format
msgid "Blocked"
msgstr "Diblokir"

#. module: hr_recruitment
#: model:ir.filters,name:hr_recruitment.hr_applicant_filter_department
msgid "By Department"
msgstr "Menurut Departemen"

#. module: hr_recruitment
#: model:ir.filters,name:hr_recruitment.hr_applicant_filter_job
msgid "By Job"
msgstr "Menurut Pekerjaan"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_job_position
msgid "By Job Positions"
msgstr "Berdasarkan Jabatan Kerja"

#. module: hr_recruitment
#: model:ir.filters,name:hr_recruitment.hr_applicant_filter_recruiter
msgid "By Recruiter"
msgstr "Menurut Perekrut"

#. module: hr_recruitment
#: model_terms:digest.tip,tip_description:hr_recruitment.digest_tip_hr_recruitment_0
msgid ""
"By setting an alias to a job position, emails sent to this address create "
"applications automatically. You can even use multiple trackers to get "
"statistics according to the source of the application: LinkedIn, Monster, "
"Indeed, etc."
msgstr ""
"Dengan menetapkan alias ke jabatan kerja, email yang dikirim ke alamt ini "
"membuat lamaran secara otomatis. Anda bahkan dapat menggunakan lebih dari "
"satu tracker untuk mendapatkan statistik sesuai dengan sumber lamaran: "
"LinkedIn, Monster, Indeed, dsb."

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_calendar_event
msgid "Calendar Event"
msgstr "Acara Kalender"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__campaign_id
msgid "Campaign"
msgstr "Kampanye"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_get_refuse_reason_view_form
msgid "Cancel"
msgstr "Batal"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_graph_view_job
msgid "Cases By Stage and Estimates"
msgstr "Kasus Menurut Tahap dan Perkiraan"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_applicant_category
msgid "Category of applicant"
msgstr "Katagori pelamar"

#. module: hr_recruitment
#. openerp-web
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#, python-format
msgid "Choose an application email."
msgstr "Pilih email pelamar."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__color
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__color
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__color
msgid "Color Index"
msgstr "Indeks Warna"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__company_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Company"
msgstr "Perusahaan"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_res_config_settings
msgid "Config Settings"
msgstr "Pengaturan Konfigurasi"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_configuration
msgid "Configuration"
msgstr "Konfigurasi"

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__partner_id
#, python-format
msgid "Contact"
msgstr "Kontak"

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#, python-format
msgid "Contact Email"
msgstr "Kontak Email"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.ir_attachment_view_search_inherit_hr_recruitment
msgid "Content"
msgstr "Konten"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Contract"
msgstr "Kontrak"

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job4
msgid "Contract Proposal"
msgstr "Proposal Kontrak"

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job5
msgid "Contract Signed"
msgstr "Kontrak Ditandatangani"

#. module: hr_recruitment
#. openerp-web
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#, python-format
msgid "Copy this email address, to paste it in your email composer, to apply."
msgstr ""
"Salin alamat email ini, untuk ditempel di pembuat email Anda, untuk "
"menerapkan."

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "Create"
msgstr "Buat"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Create Employee"
msgstr "Buat Karyawan"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.create_job_simple
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "Create a Job Position"
msgstr "Buat Posisi Pekerjaan"

#. module: hr_recruitment
#. openerp-web
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#, python-format
msgid "Create your first Job Position."
msgstr "Buat Jabatan Kerja pertama Anda."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__create_uid
msgid "Created by"
msgstr "Dibuat oleh"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__create_date
msgid "Created on"
msgstr "Dibuat pada"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__create_date
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Creation Date"
msgstr "Tanggal Pembuatan"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Pesan Kustom yang Dikembalikan"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__day_close
msgid "Days to Close"
msgstr "Jumlah Hari Hingga Penyelesaian"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__day_open
msgid "Days to Open"
msgstr "Jumlah Hari Hingga Pembukaan"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_defaults
msgid "Default Values"
msgstr "Nilai Baku"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid ""
"Define a specific contact address for this job position. If you keep it "
"empty, the default email address will be used which is in human resources "
"settings"
msgstr ""
"Tentukan alamat kontak tertentu untuk posisi pekerjaan ini. Jika kosong, "
"alamat email baku yang diatur di pengaturan hr akan digunakan"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_job_stage_act
msgid ""
"Define here your stages of the recruitment process, for example:\n"
"            qualification call, first interview, second interview, refused,\n"
"            hired."
msgstr ""
"Definisikan tahap proses perekrutan Anda di sini:\n"
"            telepon kualitifkasi, wawancara pertama, wawancara kedua, ditolak,\n"
"            diterima."

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_recruitment_degree_action
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__type_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_degree_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_degree_tree
msgid "Degree"
msgstr "Gelar"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__name
msgid "Degree Name"
msgstr "Nama Gelar"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_degree
msgid "Degrees"
msgstr "Gelar"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__delay_close
msgid "Delay to Close"
msgstr "Penundaan Hingga Penutupan"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "Delete"
msgstr "Hapus"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_department
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__department_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Department"
msgstr "Departemen"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__manager_id
msgid "Department Manager"
msgstr "Manajer departemen"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_department
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_department
msgid "Departments"
msgstr "Departemen"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__description
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__name
msgid "Description"
msgstr "Deskripsi"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_digest_digest
msgid "Digest"
msgstr "Singkat"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "Discard"
msgstr "Abaikan"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__display_name
msgid "Display Name"
msgstr "Nama Tampilan"

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr "Tidak punya akses, lewati data ini untuk email singkat pengguna"

#. module: hr_recruitment
#: model:hr.recruitment.degree,name:hr_recruitment.degree_bac5
msgid "Doctoral Degree"
msgstr "Gelar Doktor"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__documents_count
msgid "Document Count"
msgstr "Jumlah Dokumen"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__document_ids
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "Documents"
msgstr "Dokumen"

#. module: hr_recruitment
#: model:hr.applicant.refuse.reason,name:hr_recruitment.refuse_reason_1
msgid "Doesn't fit the job requirements"
msgstr "Tidak memenuhi syarat kerja"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_recruitment_stage_act
msgid ""
"Don't forget to specify the department if your recruitment process\n"
"            is different according to the job position."
msgstr ""
"Jangan lupa untuk spesifikasikan departemen anda jika ada proses perekrutan\n"
"           Ini berbeda menurut posisi pekerjaan."

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "Dropdown menu"
msgstr "Menu dropdown"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Edit"
msgstr "Sunting"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__email_from
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__email
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Email"
msgstr "Email"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "Email Alias"
msgstr "Email Alias"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__template_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__template_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__template_id
msgid "Email Template"
msgstr "Template Email"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_id
msgid ""
"Email alias for this job position. New emails will automatically create new "
"applicants for this job position."
msgstr ""
"Email alias untuk posisi pekerjaan ini. Email baru secara otomatis akan "
"membuat pelamar baru untuk posisi pekerjaan ini."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__email_cc
msgid "Email cc"
msgstr "Email cc"

#. module: hr_recruitment
#: code:addons/hr_recruitment/wizard/applicant_refuse_reason.py:0
#, python-format
msgid "Email of the applicant is not set, email won't be sent."
msgstr "Email pelamar tidak ditetapkan, email tidak akan dikirim."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__name
msgid "Email subject for applications sent via email"
msgstr "Subject email untuk pelamar dikirim via email"

#. module: hr_recruitment
#: code:addons/hr_recruitment/wizard/applicant_refuse_reason.py:0
#, python-format
msgid "Email template must be selected to send a mail"
msgstr "Templat email harus dipilih untuk mengirimkan email"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_employee
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__emp_id
msgid "Employee"
msgstr "Karyawan"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__employee_name
msgid "Employee Name"
msgstr "Nama Karyawan"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__emp_id
msgid "Employee linked to the applicant."
msgstr "Karyawan terkait dengan pelamar."

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_hired_template
msgid "Employee:"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_digest_digest__kpi_hr_recruitment_new_colleagues
msgid "Employees"
msgstr "Karyawan"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__priority__3
msgid "Excellent"
msgstr "Sangat Bagus"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_department__expected_employee
msgid "Expected Employee"
msgstr "Pegawai Yang Diharapkan"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__salary_expected
msgid "Expected Salary"
msgstr "Gaji Yang Diharapkan"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__salary_expected_extra
msgid "Expected Salary Extra"
msgstr "Gaji Ekstra Yang Diharapkan"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Extended Filters"
msgstr "Penyaring Tambahan"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Extra advantages..."
msgstr "Fasilitas tambahan..."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__favorite_user_ids
msgid "Favorite User"
msgstr "User Favorit"

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job2
msgid "First Interview"
msgstr "Wawancara Pertama"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__fold
msgid "Folded in Kanban"
msgstr "Sembunyikan di Kanban"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_follower_ids
msgid "Followers"
msgstr "Pengikut"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_partner_ids
msgid "Followers (Partners)"
msgstr "Pengikut (Rekanan)"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Ikon font awesome, misalnya fa-tasks"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Future Activities"
msgstr "Kegiatan - Kegiatan Mendatang"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_source_kanban
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_source_tree
msgid "Generate Email"
msgstr "Email Dibuat"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_applicant_get_refuse_reason
msgid "Get Refuse Reason"
msgstr "Dapatkan Alasan Penolakan"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_recruitment_degree__sequence
msgid "Gives the sequence order when displaying a list of degrees."
msgstr "Tampilkan urutan penomoran ketika menampilkan daftar gelar."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_recruitment_stage__sequence
msgid "Gives the sequence order when displaying a list of stages."
msgstr "Tampilkan urutan penomoran ketika menampilkan daftar tahapan."

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__priority__1
msgid "Good"
msgstr "Baik"

#. module: hr_recruitment
#: model:hr.recruitment.degree,name:hr_recruitment.degree_graduate
msgid "Graduate"
msgstr "Lulusan"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__kanban_state__done
msgid "Green"
msgstr "Hijau"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__legend_done
msgid "Green Kanban Label"
msgstr "Label Kanban Hijau"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__kanban_state__normal
msgid "Grey"
msgstr "Abu-Abu"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__legend_normal
msgid "Grey Kanban Label"
msgstr "Label Kanban Abu-Abu"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Group By"
msgstr "Dikelompokkan berdasarkan"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__hr_responsible_id
msgid "HR Responsible"
msgstr "Penanggunga Jawab HR"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__has_message
msgid "Has Message"
msgstr "Memiliki Pesan"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__date_closed
msgid "Hire Date"
msgstr "Tanggal Perekrutan"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Hired"
msgstr "Diterima"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__hired_stage
msgid "Hired Stage"
msgstr "Tahap Direkrut"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__id
msgid "ID"
msgstr "ID"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"ID dari catatan induk alias (contoh: proyek yang menahan pembuatan alias "
"tugas)"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_exception_icon
msgid "Icon"
msgstr "Ikon"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikon untuk menunjukkan sebuah aktivitas pengecualian."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__message_needaction
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__message_unread
msgid "If checked, new messages require your attention."
msgstr "Jika dicentang, pesan baru memerlukan penanganan dan perhatian Anda."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__message_has_error
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Jika dicentang, beberapa pesan mempunyai kesalahan dalam pengiriman."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_recruitment_stage__hired_stage
msgid ""
"If checked, this stage is used to determine the hire date of an applicant"
msgstr ""
"Bila dicentang, tahap ini digunakan untuk menentukan tanggal pelamar "
"diterima"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_recruitment_stage__template_id
msgid ""
"If set, a message is posted on the applicant using the template when the "
"applicant is set to the stage."
msgstr ""
"Jika diatur, pesan yang direkam pada pelamar menggunakan contoh ketika "
"pelamar diatur pada tahapan."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Jika di tetapkan/atur, konten ini akan secara otomatis dikirimkan untuk "
"pengguna tanpa otorisasi daripada dari pesan bawaan/default."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__active
msgid ""
"If the active field is set to false, it will allow you to hide the case "
"without removing it."
msgstr ""
"Jika kolom aktif diatur ke false, akan memungkinkan Anda untuk "
"menyembunyikan kasus ini tanpa menghapusnya."

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#: model:hr.applicant,legend_normal:hr_recruitment.hr_case_advertisement
#: model:hr.applicant,legend_normal:hr_recruitment.hr_case_dev0
#: model:hr.applicant,legend_normal:hr_recruitment.hr_case_dev1
#: model:hr.applicant,legend_normal:hr_recruitment.hr_case_dev2
#: model:hr.applicant,legend_normal:hr_recruitment.hr_case_dev3
#: model:hr.applicant,legend_normal:hr_recruitment.hr_case_financejob0
#: model:hr.applicant,legend_normal:hr_recruitment.hr_case_financejob1
#: model:hr.applicant,legend_normal:hr_recruitment.hr_case_fresher0
#: model:hr.applicant,legend_normal:hr_recruitment.hr_case_marketingjob0
#: model:hr.applicant,legend_normal:hr_recruitment.hr_case_mkt0
#: model:hr.applicant,legend_normal:hr_recruitment.hr_case_mkt1
#: model:hr.applicant,legend_normal:hr_recruitment.hr_case_programmer
#: model:hr.applicant,legend_normal:hr_recruitment.hr_case_salesman0
#: model:hr.applicant,legend_normal:hr_recruitment.hr_case_salesman1
#: model:hr.applicant,legend_normal:hr_recruitment.hr_case_traineemca0
#: model:hr.applicant,legend_normal:hr_recruitment.hr_case_traineemca1
#: model:hr.applicant,legend_normal:hr_recruitment.hr_case_yrsexperienceinphp0
#: model:hr.recruitment.stage,legend_normal:hr_recruitment.stage_job1
#: model:hr.recruitment.stage,legend_normal:hr_recruitment.stage_job2
#: model:hr.recruitment.stage,legend_normal:hr_recruitment.stage_job3
#: model:hr.recruitment.stage,legend_normal:hr_recruitment.stage_job4
#: model:hr.recruitment.stage,legend_normal:hr_recruitment.stage_job5
#, python-format
msgid "In Progress"
msgstr "Dalam Proses"

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job1
msgid "Initial Qualification"
msgstr "Kualifikasi Awal"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_res_config_settings__module_hr_recruitment_survey
msgid "Interview Forms"
msgstr "Formulir Wawancara"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__is_favorite
msgid "Is Favorite"
msgstr "Apakah Favorit"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_is_follower
msgid "Is Follower"
msgstr "Pengikut"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__is_warning_visible
msgid "Is Warning Visible"
msgstr "Apakah Warning Terlihat"

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/hr_job.py:0
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__job_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#, python-format
msgid "Job"
msgstr "Pekerjaan"

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__application_ids
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_pivot_view_job
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
#, python-format
msgid "Job Applications"
msgstr "Lamaran Kerja"

#. module: hr_recruitment
#: model:utm.campaign,name:hr_recruitment.utm_campaign_job
msgid "Job Campaign"
msgstr "Kampanye Kerja"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__address_id
msgid "Job Location"
msgstr "Lokasi Kerja"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_job
msgid "Job Position"
msgstr "Posisi Kerja"

#. module: hr_recruitment
#: model:mail.message.subtype,name:hr_recruitment.mt_department_new
msgid "Job Position Created"
msgstr "Posisi Kerja Yang Dibuat"

#. module: hr_recruitment
#: model:mail.message.subtype,name:hr_recruitment.mt_job_new
msgid "Job Position created"
msgstr "Posisi kerja yang dibuat"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_config
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_job_position_config
msgid "Job Positions"
msgstr "Posisi Kerja"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Job Posting"
msgstr "Lowongan Kerja"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__job_ids
msgid "Job Specific"
msgstr "Spesifikasi Kerja"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Jobs"
msgstr "Pekerjaan"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Jobs - Recruitment Form"
msgstr "Formulir Perekrutan Kerja"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_sources
msgid "Jobs Sources"
msgstr "Sumber Kerja"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__legend_blocked
msgid "Kanban Blocked"
msgstr "Kanban Diblokir"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__legend_normal
msgid "Kanban Ongoing"
msgstr "Kanban Sedang Berlangsung "

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__kanban_state
msgid "Kanban State"
msgstr "Status Kanban"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__legend_done
msgid "Kanban Valid"
msgstr "Kanban Valid"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_digest_digest__kpi_hr_recruitment_new_colleagues_value
msgid "Kpi Hr Recruitment New Colleagues Value"
msgstr "Value Kpi Rekrutmen HR Rekan Kerja Baru"

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#, python-format
msgid "Last Meeting"
msgstr "Meeting Terakhir"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason____last_update
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant____last_update
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category____last_update
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason____last_update
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree____last_update
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source____last_update
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage____last_update
msgid "Last Modified on"
msgstr "Terakhir diubah pada"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__last_stage_id
msgid "Last Stage"
msgstr "Tahap Terakhir"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__date_last_stage_update
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Last Stage Update"
msgstr "Update Tahap Terakhir"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__write_uid
msgid "Last Updated by"
msgstr "Terakhir diperbarui oleh"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__write_date
msgid "Last Updated on"
msgstr "Terakhir diperbarui pada"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Late Activities"
msgstr "Aktifitas terakhir"

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#, python-format
msgid "Let people apply by email to save time."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_config
msgid "Let's create a job position."
msgstr "Mari membuat jabatan kerja."

#. module: hr_recruitment
#. openerp-web
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#, python-format
msgid ""
"Let's create the position. An email will be setup for applications, and a "
"public job description, if you use the Website app."
msgstr ""
"Mari buat jabatan. Email akan disetup untuk pelamar, dan job description "
"publik, bila Anda menggunakan app Website."

#. module: hr_recruitment
#. openerp-web
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#, python-format
msgid "Let's have a look at how to <b>improve</b> your <b>hiring process</b>."
msgstr ""
"Mari lihat bagaimana cara <b>meningkatkan</b><b>proses perekrutan</b> Anda."

#. module: hr_recruitment
#. openerp-web
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#, python-format
msgid "Let's have a look at the applications pipeline."
msgstr "Mari lihat pipeline lamaran."

#. module: hr_recruitment
#. openerp-web
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#, python-format
msgid "Let’s create this new employee now."
msgstr "Mari buat karyawan baru ini sekarang."

#. module: hr_recruitment
#. openerp-web
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#, python-format
msgid "Let’s go back to the dashboard."
msgstr "Mari kembali ke dashboard."

#. module: hr_recruitment
#: model:hr.recruitment.source,name:hr_recruitment.hr_recruitment_linkedin_ceo
#: model:hr.recruitment.source,name:hr_recruitment.hr_recruitment_linkedin_consultant
#: model:hr.recruitment.source,name:hr_recruitment.hr_recruitment_linkedin_cto
#: model:hr.recruitment.source,name:hr_recruitment.hr_recruitment_linkedin_developer
#: model:hr.recruitment.source,name:hr_recruitment.hr_recruitment_linkedin_hrm
#: model:hr.recruitment.source,name:hr_recruitment.hr_recruitment_linkedin_marketing
#: model:hr.recruitment.source,name:hr_recruitment.hr_recruitment_linkedin_trainee
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__email_cc
msgid "List of cc from incoming emails."
msgstr "Daftar cc dari email masuk"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_main_attachment_id
msgid "Main Attachment"
msgstr "Lampiran Utama"

#. module: hr_recruitment
#: model:hr.recruitment.degree,name:hr_recruitment.degree_licenced
msgid "Master Degree"
msgstr "Gelar Magister"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__medium_id
msgid "Medium"
msgstr "Media"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__meeting_display_date
msgid "Meeting Display Date"
msgstr "Tanggal Tampilan Rapat"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__meeting_display_text
msgid "Meeting Display Text"
msgstr "Teks Tampilan Rapat"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__meeting_ids
msgid "Meetings"
msgstr "Pertemuan"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_has_error
msgid "Message Delivery error"
msgstr "Kesalahan Pengiriman Pesan"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_ids
msgid "Messages"
msgstr "Pesan"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__partner_mobile
msgid "Mobile"
msgstr "Ponsel"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Motivations..."
msgstr "Motivasi..."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Deadline Kegiatan Saya"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "My Applications"
msgstr "Lamaran Saya"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_job_filter_recruitment
msgid "My Favorites"
msgstr "Favorit Saya"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_search_view
msgid "My Job Positions"
msgstr "Jabatan Kerja Saya"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "New"
msgstr "Baru"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_department__new_applicant_count
#: model:mail.message.subtype,name:hr_recruitment.mt_applicant_new
#: model:mail.message.subtype,name:hr_recruitment.mt_job_applicant_new
msgid "New Applicant"
msgstr "Pelamar Baru"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_department_view_kanban
msgid "New Applicants"
msgstr "Pelamar Baru"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_new_application
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__new_application_count
msgid "New Application"
msgstr "Lamaran Baru"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_applicant_action_from_department
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "New Applications"
msgstr "Lamaran Baru"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_department__new_hired_employee
msgid "New Hired Employee"
msgstr "Karyawan Baru"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_employee_view_search
msgid "Newly Hired"
msgstr "Baru Direkrut"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_employee_action_from_department
msgid "Newly Hired Employees"
msgstr "Karyawan Yang Baru Diterima"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_employee__newly_hired_employee
msgid "Newly hired employee"
msgstr "Karyawan yang baru diterima"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_tree_activity
msgid "Next Activities"
msgstr "Aktivitas Berikutnya"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Kalender Acara Aktivitas Berikutnya"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Batas Waktu Aktivitas Berikutnya"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_summary
msgid "Next Activity Summary"
msgstr "Ringkasan Aktivitas Berikutnya"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_type_id
msgid "Next Activity Type"
msgstr "Tipe Aktivitas Berikutnya"

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#, python-format
msgid "Next Meeting"
msgstr "Meeting Berikutnya"

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#, python-format
msgid "No Meeting"
msgstr "Tidak Ada Meeting"

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#, python-format
msgid "No Subject"
msgstr "Tidak Ada Judul"

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#, python-format
msgid "No application yet"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_applications
#: model_terms:ir.actions.act_window,help:hr_recruitment.crm_case_categ0_act_job
msgid "No applications yet"
msgstr "Belum ada lamaran"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_recruitment_report_filtered_department
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_recruitment_report_filtered_job
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_applicant_action_analysis
msgid "No data yet!"
msgstr "Belum ada data!"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__priority__0
msgid "Normal"
msgstr "Biasa"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_needaction_counter
msgid "Number of Actions"
msgstr "Jumlah Tindakan"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__attachment_number
msgid "Number of Attachments"
msgstr "Jumlah Lampiran"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__new_application_count
msgid ""
"Number of applications that are new in the flow (typically at first step of "
"the flow)"
msgstr "Jumlah pelaamar yang baru di alu r(biasanya di langkah pertama alur)"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__delay_close
msgid "Number of days to close"
msgstr "Jumlah hari hingga penutupan"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_has_error_counter
msgid "Number of errors"
msgstr "Jumlah kesalahan"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Jumlah pesan yang butuh tindakan"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Jumlah dari pesan dengan kesalahan pengiriman"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__message_unread_counter
msgid "Number of unread messages"
msgstr "Jumlah pesan yang belum dibaca"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_applications
msgid ""
"Odoo helps you track applicants in the recruitment\n"
"                process and follow up all operations: meetings, interviews, etc."
msgstr ""
"Odoo membantu anda melacak pelamar dalam proses perekrutan\n"
"                proses dan tindaklanjuti semua operasi: pertemuan, wawancara, dll."

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.crm_case_categ0_act_job
msgid ""
"Odoo helps you track applicants in the recruitment\n"
"            process and follow up all operations: meetings, interviews, etc."
msgstr ""
"Odoo membantu anda melacak pelamar dalam proses perekrutan\n"
"            proses dan tindaklanjuti semua operasi: pertemuan, wawancara, dll."

#. module: hr_recruitment
#: model:res.groups,name:hr_recruitment.group_hr_recruitment_user
msgid "Officer"
msgstr "Petugas"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__old_application_count
msgid "Old Application"
msgstr "Lamaran Tua"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Ongoing"
msgstr "Terus-menerus"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_res_config_settings__module_website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Online Posting"
msgstr "Rekam Daring"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"ID Opsional dari berkas (catatan) di mana semua pesan masuk akan "
"dilampirkan, bahkan jika mereka tidak menjawabnya. Jika diatur, hal ini akan"
" menonaktifkan pembuatan catatan baru sepenuhnya."

#. module: hr_recruitment
#. openerp-web
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#, python-format
msgid "Or talk about this applicant privately with your colleagues."
msgstr ""
"Atau diskusi tentang pelamar ini secara privat dengan rekan-rekan Anda."

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Other applications"
msgstr "Lamaran lai"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_user_id
msgid "Owner"
msgstr "Pemilik"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_parent_model_id
msgid "Parent Model"
msgstr "Model Induk"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "Catatan Induk ID Berkas"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Model induk dengan alias. Model yang memiliki referensi alias ini tidak "
"selalu berarti model yang diberikan oleh alias_model_id (contoh: proyek "
"(parent_model) dan tugas (model))"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__hr_responsible_id
msgid "Person responsible of validating the employee's contracts."
msgstr "Orang yang bertanggung jawab untuk memvalidasi kontak karyawan."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__partner_phone
msgid "Phone"
msgstr "Telepon"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Kebijakan untuk mengirimkan pesan di dokumen menggunakan mailgateway.\n"
"- semua orang: setiap orang dapat mengirim\n"
"- rekanan: hanya rekanan yang diijinkan\n"
"- pengikut: hanya pengikut dokumen terkait\n"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__probability
msgid "Probability"
msgstr "Probabilitas"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__salary_proposed
msgid "Proposed Salary"
msgstr "Usulan Gaji"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__salary_proposed_extra
msgid "Proposed Salary Extra"
msgstr "Usulan Gaji Ekstra"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Publish available jobs on your website"
msgstr "Publikasikan pekerjaan yang tersedia pada website Anda"

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#: model:hr.applicant,legend_done:hr_recruitment.hr_case_advertisement
#: model:hr.applicant,legend_done:hr_recruitment.hr_case_dev0
#: model:hr.applicant,legend_done:hr_recruitment.hr_case_dev1
#: model:hr.applicant,legend_done:hr_recruitment.hr_case_dev2
#: model:hr.applicant,legend_done:hr_recruitment.hr_case_dev3
#: model:hr.applicant,legend_done:hr_recruitment.hr_case_financejob0
#: model:hr.applicant,legend_done:hr_recruitment.hr_case_financejob1
#: model:hr.applicant,legend_done:hr_recruitment.hr_case_fresher0
#: model:hr.applicant,legend_done:hr_recruitment.hr_case_marketingjob0
#: model:hr.applicant,legend_done:hr_recruitment.hr_case_mkt0
#: model:hr.applicant,legend_done:hr_recruitment.hr_case_mkt1
#: model:hr.applicant,legend_done:hr_recruitment.hr_case_programmer
#: model:hr.applicant,legend_done:hr_recruitment.hr_case_salesman0
#: model:hr.applicant,legend_done:hr_recruitment.hr_case_salesman1
#: model:hr.applicant,legend_done:hr_recruitment.hr_case_traineemca0
#: model:hr.applicant,legend_done:hr_recruitment.hr_case_traineemca1
#: model:hr.applicant,legend_done:hr_recruitment.hr_case_yrsexperienceinphp0
#: model:hr.recruitment.stage,legend_done:hr_recruitment.stage_job1
#: model:hr.recruitment.stage,legend_done:hr_recruitment.stage_job2
#: model:hr.recruitment.stage,legend_done:hr_recruitment.stage_job3
#: model:hr.recruitment.stage,legend_done:hr_recruitment.stage_job4
#: model:hr.recruitment.stage,legend_done:hr_recruitment.stage_job5
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#, python-format
msgid "Ready for Next Stage"
msgstr "Siap untuk Tahap Berikutnya"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_config
msgid "Ready to recruit more efficiently?"
msgstr "Siap untuk merekrut dengan lebih efisien?"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_force_thread_id
msgid "Record Thread ID"
msgstr "Rekam ID Thread"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__user_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__user_id
msgid "Recruiter"
msgstr "Recruiter"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_root
#: model_terms:ir.ui.view,arch_db:hr_recruitment.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Recruitment"
msgstr "Perekrutan"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_job_stage_act
msgid "Recruitment / Applicants Stages"
msgstr "Rekrutmen / Tahapan Lamaran"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_recruitment_report_filtered_department
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_recruitment_report_filtered_job
#: model:ir.actions.act_window,name:hr_recruitment.hr_applicant_action_analysis
#: model:ir.ui.menu,name:hr_recruitment.hr_applicant_report_menu
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_graph
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Recruitment Analysis"
msgstr "Analisis Perekrutan"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Recruitment Done"
msgstr "Rekrutmen Selesai"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Recruitment Process"
msgstr "Proses Perekrutan"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_recruitment_stage
msgid "Recruitment Stages"
msgstr "Tahap-Tahap Rekrutme"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_department_view_kanban
msgid "Recruitments"
msgstr "Perekrutan"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__kanban_state__blocked
msgid "Red"
msgstr "Merah"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__legend_blocked
msgid "Red Kanban Label"
msgstr "Label Kanban Merah"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Refuse"
msgstr "Tolak"

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#: model:ir.actions.act_window,name:hr_recruitment.applicant_get_refuse_reason_action
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__refuse_reason_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__refuse_reason_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_get_refuse_reason_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_refuse_reason_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_refuse_reason_view_tree
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#, python-format
msgid "Refuse Reason"
msgstr "Alasan Penolakan"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_applicant_refuse_reason
msgid "Refuse Reason of Applicant"
msgstr "Tolak Alasan Pelamar"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_applicant_refuse_reason_action
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_applicant_refuse_reason
msgid "Refuse Reasons"
msgstr "Tolak Alasan"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Refused"
msgstr "Ditolak"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.report_hr_recruitment
msgid "Reporting"
msgstr "Laporan"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__requirements
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid "Requirements"
msgstr "Persyaratan"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Responsible"
msgstr "Penanggung Jawab"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_user_id
msgid "Responsible User"
msgstr "Tanggung-jawab"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Restore"
msgstr "Pulihkan"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Kesalahan Pengiriman SMS"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__salary_expected
msgid "Salary Expected by Applicant"
msgstr "Gaji Yang Diharapkan Oleh Pelamar"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__salary_expected_extra
msgid "Salary Expected by Applicant, extra advantages"
msgstr "Gaji Yang Diharapkan Oleh Pelamar, fasilitas tambahan"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__salary_proposed
msgid "Salary Proposed by the Organisation"
msgstr "Usulan Gaji Oleh Organisasi"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__salary_proposed_extra
msgid "Salary Proposed by the Organisation, extra advantages"
msgstr "Gaji Yang Diusulkan Oleh Organisasi, fasilitas tambahan"

#. module: hr_recruitment
#. openerp-web
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#, python-format
msgid "Save it !"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "Schedule Interview"
msgstr "Jadwalkan Wawancara"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Search Applicants"
msgstr "Cari Pelamar"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_source_view_search
msgid "Search Source"
msgstr "Cari Sumber"

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job3
msgid "Second Interview"
msgstr "Wawancara Kedua"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__send_mail
msgid "Send Email"
msgstr "Kirim Email"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Send Interview Survey"
msgstr "Kirim Survei Wawancara"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid ""
"Send an Interview Survey to the applicant during\n"
"                                        the recruitment process"
msgstr ""

#. module: hr_recruitment
#. openerp-web
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#, python-format
msgid "Send your email. Followers will get a copy of the communication."
msgstr "Kirim email Anda. Pengikut akan mendapatkan salinan komunikasi."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__sequence
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__sequence
msgid "Sequence"
msgstr "Urutan"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_recruitment_configuration
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_global_settings
msgid "Settings"
msgstr "Pengaturan"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Show all records which has next action date is before today"
msgstr "Tampilkan semua dokumen dengan aksi berikut sebelum hari ini"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__source_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__source_id
msgid "Source"
msgstr "Sumber"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__name
msgid "Source Name"
msgstr "Nama Sumber"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_recruitment_source
msgid "Source of Applicants"
msgstr "Sumber Pelamar"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_recruitment_source_action
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_source
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_source_tree
msgid "Sources of Applicants"
msgstr "Sumber Pelamar"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_recruitment_stage__job_ids
msgid ""
"Specific jobs that uses this stage. Other jobs will not use this stage."
msgstr ""
"Pekerjaan tertentu yang menggunakan tahap ini. Pekerjaan lain tidak akan "
"menggunakan tahap ini."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__stage_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid "Stage"
msgstr "Tahapan"

#. module: hr_recruitment
#: model:mail.message.subtype,name:hr_recruitment.mt_applicant_stage_changed
msgid "Stage Changed"
msgstr "Tahapan Diubah"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid "Stage Definition"
msgstr "Definisi Tahapan"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__name
msgid "Stage Name"
msgstr "Nama Tahap"

#. module: hr_recruitment
#: model:mail.message.subtype,description:hr_recruitment.mt_applicant_stage_changed
msgid "Stage changed"
msgstr "Tahapan diubah"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__last_stage_id
msgid ""
"Stage of the applicant before being in the current stage. Used for lost "
"cases analysis."
msgstr ""
"Tahapan pelamar sebelum berada di tahapan saat ini. Digunakan untuk analisis"
" kasus gagal."

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_recruitment_stage_act
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_stage
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_tree
msgid "Stages"
msgstr "Tahap"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Start Recruitment"
msgstr "Mulai Perekrutan"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Start recruitment"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status berdasarkan aktivitas\n"
"Terlambat: Batas waktu telah terlewati\n"
"Hari ini: Tanggal aktivitas adalah hari ini\n"
"Direncanakan: Aktivitas yang akan datang."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__name
msgid "Subject / Application Name"
msgstr "Subjek / Nama Lamaran"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_get_refuse_reason_view_form
msgid "Submit"
msgstr "Menyerahkan"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__name
msgid "Tag Name"
msgstr "Nama Tag"

#. module: hr_recruitment
#: model:ir.model.constraint,message:hr_recruitment.constraint_hr_applicant_category_name_uniq
msgid "Tag name already exists !"
msgstr "Nama tag sudah ada !"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_applicant_category_action
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__categ_ids
#: model:ir.ui.menu,name:hr_recruitment.hr_applicant_category_menu
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_category_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_category_view_tree
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Tags"
msgstr "Label"

#. module: hr_recruitment
#: model:hr.applicant.refuse.reason,name:hr_recruitment.refuse_reason_3
msgid "The applicant gets a better offer"
msgstr ""

#. module: hr_recruitment
#: model:hr.applicant.refuse.reason,name:hr_recruitment.refuse_reason_2
msgid "The applicant is not interested anymore"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__availability
msgid "The date at which the applicant will be available to start working"
msgstr "Tanggal di mana pelamar bersedia untuk mulai bekerja"

#. module: hr_recruitment
#: code:addons/hr_recruitment/wizard/applicant_refuse_reason.py:0
#, python-format
msgid ""
"The email will not be sent to the following applicant(s) as they don't have "
"email address."
msgstr ""
"Email tidak akan dikirim ke pelamar berikut karena mereka tidak memiliki "
"alamat email."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"Model (Jenis Dokumen Odoo) di mana alias ini dikaitkan. Email masuk yang "
"tidak menjawab catatan yang sudah ada akan menyebabkan pembuatan rekor baru "
"model ini (misalnya tugas proyek)"

#. module: hr_recruitment
#: model:ir.model.constraint,message:hr_recruitment.constraint_hr_recruitment_degree_name_uniq
msgid "The name of the Degree of Recruitment must be unique!"
msgstr "Nama Gelar Perekrutan harus unik!"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"Nama email alias, misalnya 'pekerjaan' jika Anda ingin menangkap email untuk"
" <<EMAIL>>"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""
"Pemilik catatan yang dibuat setelah menerima email pada alias ini. Jika "
"kolom ini belum ditetapkan, sistem akan berusaha untuk menemukan pemilik "
"yang tepat berdasarkan pengirim (dari) alamat, atau akan menggunakan akun "
"Administrator jika tidak ada pengguna sistem yang ditemukan untuk alamat "
"tersebut."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""
"Ini adalah nama yang membantu Anda melacak usaha kampanye yang berbeda-beda,"
" contohnya Fall_Drive, Natal_Spesial"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr ""
"Ini adalah metode pengiriman, contohnya Kartu Pos, Email, atau Iklan Banner"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr ""
"Ini adalah sumber link, contohnya Search Engine, domain lain, atau nama pada"
" daftar email"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_recruitment_stage__fold
msgid ""
"This stage is folded in the kanban view when there are no records in that "
"stage to display."
msgstr ""
"Tahap ini disembunyikan pada tampilan kanban ketika tidak ada catatan dalam "
"tahap itu untuk ditampilkan."

#. module: hr_recruitment
#: model:digest.tip,name:hr_recruitment.digest_tip_hr_recruitment_0
#: model_terms:digest.tip,tip_description:hr_recruitment.digest_tip_hr_recruitment_0
msgid "Tip: Let candidates apply by email"
msgstr "Tip: Biarkan kandidat melamar melalui email"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "To Recruit"
msgstr "Untuk Merekrut"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Today Activities"
msgstr "Aktivitas Hari ini"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid "Tooltips"
msgstr "Tooltip"

#. module: hr_recruitment
#: model_terms:digest.tip,tip_description:hr_recruitment.digest_tip_hr_recruitment_0
msgid "Try sending an email"
msgstr "Coba mengirim email"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Jenis dari aktivitas pengecualian pada rekaman data."

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Unassigned"
msgstr "Tak diserah-tugaskan"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_unread
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Unread Messages"
msgstr "Pesan Belum Dibaca"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Penghitung Pesan yang Belum Dibaca"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_sources
msgid "Use emails and links trackers"
msgstr "Gunakan email dan tracker link"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid ""
"Use interview forms tailored to each job position during the recruitment "
"process. Select the form to use in the job position detail form. This relies"
" on the Survey app."
msgstr ""
"Gunakan formulir wawancara yang dirancang unik untuk setiap jabatan kerja "
"selama proses rekrutmen. Pilih formulir untuk digunakan di formulir detail "
"jabatan kerja. Ini bergantung pada app Survei."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__user_email
msgid "User Email"
msgstr "Email Pengguna"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__priority__2
msgid "Very Good"
msgstr "Sangat Baik"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_sources
msgid "Want to analyse where applications come from ?"
msgstr "Ingin menganalisa dari mana lamaran datang ?"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__website_message_ids
msgid "Website Messages"
msgstr "Pesan Website"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__website_message_ids
msgid "Website communication history"
msgstr "Sejarah komunikasi website"

#. module: hr_recruitment
#. openerp-web
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#, python-format
msgid "What do you want to recruit today? Choose a job title..."
msgstr "Apa yang ingin Anda rekrut hari ini? Pilih jabatan kerja..."

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid ""
"You can define here the labels that will be displayed for the kanban state instead\n"
"                        of the default labels."
msgstr ""

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#, python-format
msgid "You must define a Contact Name for this applicant."
msgstr "Anda harus mendefinisikan Nama Kontak untuk pelamar ini."

#. module: hr_recruitment
#: model:mail.template,subject:hr_recruitment.email_template_data_applicant_congratulations
#: model:mail.template,subject:hr_recruitment.email_template_data_applicant_interest
#: model:mail.template,subject:hr_recruitment.email_template_data_applicant_not_interested
#: model:mail.template,subject:hr_recruitment.email_template_data_applicant_refuse
msgid "Your Job Application: {{ object.job_id.name }}"
msgstr "Lamaran Kerja Anda: {{ object.job_id.name }}"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_source_tree
msgid "e.g. LinkedIn"
msgstr "contoh LinkedIn"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "e.g. Sales Manager"
msgstr "mis. Manajer Penjualan"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "e.g. Sales Manager 2 year experience"
msgstr "contoh Manajer Sales 2 tahun pengalaman"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "e.g. sales-manager"
msgstr "contoh sales-manajer"

#. module: hr_recruitment
#: model:ir.actions.server,name:hr_recruitment.hr_applicant_resumes_server
msgid "hr.applicant.resumes.server"
msgstr ""
