# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_mx
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-01-29 15:07+0000\n"
"PO-Revision-Date: 2020-01-29 15:07+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_mx
#: model:ir.model.fields,field_description:l10n_mx.field_res_bank__l10n_mx_edi_code
msgid "ABM Code"
msgstr ""

#. module: l10n_mx
#: model:ir.model,name:l10n_mx.model_account_account
msgid "Account"
msgstr ""

#. module: l10n_mx
#: model:ir.model,name:l10n_mx.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_mx
#: model:ir.model,name:l10n_mx.model_account_account_tag
msgid "Account Tag"
msgstr ""

#. module: l10n_mx
#: model:ir.model,name:l10n_mx.model_res_bank
msgid "Bank"
msgstr ""

#. module: l10n_mx
#: model:ir.model,name:l10n_mx.model_res_partner_bank
msgid "Bank Accounts"
msgstr ""

#. module: l10n_mx
#: model:ir.model.fields,field_description:l10n_mx.field_account_setup_bank_manual_config__l10n_mx_edi_clabe
#: model:ir.model.fields,field_description:l10n_mx.field_res_partner_bank__l10n_mx_edi_clabe
msgid "CLABE"
msgstr ""

#. module: l10n_mx
#: model:ir.model,name:l10n_mx.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: l10n_mx
#: model_terms:ir.ui.view,arch_db:l10n_mx.res_config_settings_view_form
msgid "Create your electronic invoices automatically (CFDI format)"
msgstr ""

#. module: l10n_mx
#: model:ir.model.fields.selection,name:l10n_mx.selection__account_account_tag__nature__a
msgid "Creditable Account"
msgstr ""

#. module: l10n_mx
#: model:ir.model.fields.selection,name:l10n_mx.selection__account_account_tag__nature__d
msgid "Debitable Account"
msgstr ""

#. module: l10n_mx
#: code:addons/l10n_mx/models/chart_template.py:0
#, python-format
msgid "Effectively Paid"
msgstr ""

#. module: l10n_mx
#: model:account.tax.group,name:l10n_mx.tax_group_isr_ret_10
msgid "ISR Retencion 10%"
msgstr ""

#. module: l10n_mx
#: model:account.tax.group,name:l10n_mx.tax_group_iva_0
msgid "IVA 0%"
msgstr ""

#. module: l10n_mx
#: model:account.tax.group,name:l10n_mx.tax_group_iva_16
msgid "IVA 16% "
msgstr ""

#. module: l10n_mx
#: model:account.tax.group,name:l10n_mx.tax_group_iva_8
msgid "IVA 8%"
msgstr ""

#. module: l10n_mx
#: model:account.tax.group,name:l10n_mx.tax_group_iva_ret_10
msgid "IVA Retencion 10%"
msgstr ""

#. module: l10n_mx
#: model:account.tax.group,name:l10n_mx.tax_group_iva_ret_1067
msgid "IVA Retencion 10.67%"
msgstr ""

#. module: l10n_mx
#: model:account.tax.group,name:l10n_mx.tax_group_iva_ret_4
msgid "IVA Retencion 4%"
msgstr ""

#. module: l10n_mx
#: model:ir.model,name:l10n_mx.model_account_journal
msgid "Journal"
msgstr ""

#. module: l10n_mx
#: model:ir.model.fields,field_description:l10n_mx.field_res_config_settings__module_l10n_mx_edi
msgid "Mexican Electronic Invoicing"
msgstr ""

#. module: l10n_mx
#: model:ir.model.fields,field_description:l10n_mx.field_account_account_tag__nature
msgid "Nature"
msgstr ""

#. module: l10n_mx
#: model:ir.model.fields,help:l10n_mx.field_account_setup_bank_manual_config__l10n_mx_edi_clabe
#: model:ir.model.fields,help:l10n_mx.field_res_partner_bank__l10n_mx_edi_clabe
msgid ""
"Standardized banking cipher for Mexico. More info wikipedia.org/wiki/CLABE"
msgstr ""

#. module: l10n_mx
#: model:ir.model.fields,help:l10n_mx.field_res_bank__l10n_mx_edi_code
msgid ""
"Three-digit number assigned by the ABM to identify banking institutions (ABM"
" is an acronym for Asociación de Bancos de México)"
msgstr ""

#. module: l10n_mx
#: model:ir.model.fields,help:l10n_mx.field_account_account_tag__nature
msgid "Used in Mexican report of electronic accounting (account nature)."
msgstr ""
