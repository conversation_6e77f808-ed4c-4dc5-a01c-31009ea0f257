.wrap-row {
    position: relative;
    overflow: hidden;
    height: 310px;
    .twitter-row {
        position: absolute;
        width: 100%;
        height: auto;
        div {
            &.scrollWrapper {
                position: relative;
                overflow: hidden;
                width: 100%;
                height: 100%;
            }
            &.scrollableArea {
                position: relative;
                width: auto;
                height: 100%;
            }
            .tweet {
                border: 1px solid #ccc;
                max-width: 500px;
                width: 500px;
                font-size: 0.8em;
                padding-top: 12px;
                padding-right: 10px;
                padding-bottom: 12px;
                padding-left: 10px;
                float: left;
                display: block;
                margin: 6px;
                max-height: 90px;
                height: 90px;
                opacity: 0.6;
                h4, p {
                    padding: 0;
                    margin: 0;
                }
                .left {
                    display: block;
                    float: left;
                    width: 80px;
                    img {
                        width: 65px;
                        height: auto;
                        float: left;
                        display: block;
                        margin: 0px 5px 0px -5px;
                    }
                }
                .right {
                    display: block;
                    float: left;
                    width: 470px;
                    .top {
                        height: 20px;
                    }
                }
                h4 {
                    font-size: 14px;
                    font-family: "Helvetica Neue",Helvetica,<PERSON>l,sans-serif;
                    font-weight: bold;
                    color: #000;
                    float: left;
                    display: block;
                    position: relative;
                    margin-left: 70px;
                    margin-top: -65px;
                    span {
                        color: #ccc;
                        font-weight: bold;
                        font-size: 14px;
                    }
                }
                p {
                    line-height: 1.5em;
                    float: left;
                    position: relative;
                    display: block;
                    &.date {
                        float: right;
                        line-height: 0.5em;
                        margin-top: -60px;
                        margin-right: -10px;
                    }
                }
                .right .bottom p {
                    margin-top: -65px;
                    margin-left: 70px;
                    font-size: 12px;
                    word-break: break-word;
                }
                &:hover {
                    box-shadow: 0.5px 0.5px 0.5px 1px #428BCA;
                    cursor: pointer;
                    opacity: 1;
                }
            }
        }
    }
}
@media screen and (max-width: 580px) {
    .wrap-row {
        position: relative;
        overflow: hidden;
        height: 100px;
    }
}
