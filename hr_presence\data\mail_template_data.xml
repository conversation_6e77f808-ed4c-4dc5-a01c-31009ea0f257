<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="mail_template_presence" model="mail.template">
            <field name="name">Employee: Absence email</field>
            <field name="model_id" ref="hr.model_hr_employee"/>
            <field name="subject">Unexpected Absence</field>
            <field name="email_from">{{ user.email_formatted }}</field>
            <field name="email_to">{{ (object.user_id.email_formatted or object.work_email) }}</field>
            <field name="auto_delete" eval="False"/>
            <field name="body_html" type="html">
                <div>
                    Dear <t t-out="object.name or ''"><PERSON></t>,<br/><br/>
                    Exception made if there was a mistake of ours, it seems that you are not at your office and there is not request of time off from you.<br/>
                    Please, take appropriate measures in order to carry out this work absence.<br/>
                    Do not hesitate to contact your manager or the human resource department.
                    <br/>Best Regards,<br/><br/>
                </div>
            </field>
        </record>
    </data>
</odoo>
