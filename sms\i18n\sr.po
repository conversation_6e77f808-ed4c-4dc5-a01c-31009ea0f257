# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sms
# 
# Translators:
# <PERSON>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:17+0000\n"
"PO-Revision-Date: 2018-09-21 13:17+0000\n"
"Last-Translator: <PERSON>, 2018\n"
"Language-Team: Serbian (https://www.transifex.com/odoo/teams/41243/sr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: sms
#. openerp-web
#: code:addons/sms/static/src/js/sms_widget.js:93
#, python-format
msgid "%s chars, fits in %s SMS (%s) "
msgstr ""

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.send_sms_view_form
msgid "Cancel"
msgstr "Otkaži"

#. module: sms
#: model:ir.model,name:sms.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_send_sms__create_uid
msgid "Created by"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_send_sms__create_date
msgid "Created on"
msgstr "Kreiran"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_api__display_name
#: model:ir.model.fields,field_description:sms.field_sms_send_sms__display_name
msgid "Display Name"
msgstr ""

#. module: sms
#: model:ir.model,name:sms.model_mail_thread
msgid "Email Thread"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_api__id
#: model:ir.model.fields,field_description:sms.field_sms_send_sms__id
msgid "ID"
msgstr "ID"

#. module: sms
#: code:addons/sms/models/mail_thread.py:53
#, python-format
msgid "Insufficient credit, unable to send SMS message: %s"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_api____last_update
#: model:ir.model.fields,field_description:sms.field_sms_send_sms____last_update
msgid "Last Modified on"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_send_sms__write_uid
msgid "Last Updated by"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_send_sms__write_date
msgid "Last Updated on"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_send_sms__message
msgid "Message"
msgstr "Poruka"

#. module: sms
#: code:addons/sms/wizard/send_sms.py:82
#, python-format
msgid "Missing mobile number for %s."
msgstr ""

#. module: sms
#: code:addons/sms/models/mail_thread.py:55
#, python-format
msgid "No mobile number defined, unable to send SMS message: %s"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_send_sms__recipients
msgid "Recipients"
msgstr ""

#. module: sms
#: model:ir.model,name:sms.model_sms_api
msgid "SMS API"
msgstr ""

#. module: sms
#. openerp-web
#: code:addons/sms/static/src/xml/sms_widget.xml:4
#, python-format
msgid "SMS Pricing"
msgstr ""

#. module: sms
#: code:addons/sms/models/mail_thread.py:48
#, python-format
msgid "SMS message sent: %s"
msgstr ""

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.send_sms_view_form
msgid "Send"
msgstr "Pošalji"

#. module: sms
#: model:ir.actions.act_window,name:sms.send_sms_action
#: model:ir.actions.act_window,name:sms.send_sms_form_action
#: model:ir.model,name:sms.model_sms_send_sms
#: model_terms:ir.ui.view,arch_db:sms.partner_form_send_sms_form_view
msgid "Send SMS"
msgstr ""

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.send_sms_view_form
msgid "Send an SMS"
msgstr ""
