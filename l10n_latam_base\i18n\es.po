# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_latam_base
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-22 13:00+0000\n"
"PO-Revision-Date: 2019-08-22 13:00+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_latam_base
#: model:ir.model.fields,field_description:l10n_latam_base.field_l10n_latam_identification_type__active
#: model_terms:ir.ui.view,arch_db:l10n_latam_base.view_l10n_latam_identification_type_search
msgid "Active"
msgstr "Activo"

#. module: l10n_latam_base
#: model_terms:ir.ui.view,arch_db:l10n_latam_base.view_l10n_latam_identification_type_search
msgid "Archived"
msgstr "Archivado"

#. module: l10n_latam_base
#: model:ir.model,name:l10n_latam_base.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: l10n_latam_base
#: model:ir.model,name:l10n_latam_base.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: l10n_latam_base
#: model:ir.model.fields,field_description:l10n_latam_base.field_l10n_latam_identification_type__country_id
msgid "Country"
msgstr "País"

#. module: l10n_latam_base
#: model:ir.model.fields,field_description:l10n_latam_base.field_l10n_latam_identification_type__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: l10n_latam_base
#: model:ir.model.fields,field_description:l10n_latam_base.field_l10n_latam_identification_type__create_date
msgid "Created on"
msgstr "Creado el"

#. module: l10n_latam_base
#: model:ir.model.fields,field_description:l10n_latam_base.field_l10n_latam_identification_type__description
msgid "Description"
msgstr "Descripción"

#. module: l10n_latam_base
#: model:ir.model.fields,field_description:l10n_latam_base.field_l10n_latam_identification_type__display_name
msgid "Display Name"
msgstr "Nombre a mostrar"

#. module: l10n_latam_base
#: model:l10n_latam.identification.type,name:l10n_latam_base.it_fid
msgid "Foreign ID"
msgstr "Cédula Extranjera"

#. module: l10n_latam_base
#: model:ir.model.fields,field_description:l10n_latam_base.field_l10n_latam_identification_type__id
msgid "ID"
msgstr ""

#. module: l10n_latam_base
#: model:ir.model.fields,field_description:l10n_latam_base.field_res_partner__vat
#: model:ir.model.fields,field_description:l10n_latam_base.field_res_users__vat
#: model_terms:ir.ui.view,arch_db:l10n_latam_base.view_partner_latam_form
msgid "Identification Number"
msgstr "Número de Identificación"

#. module: l10n_latam_base
#: model:ir.model.fields,help:l10n_latam_base.field_res_partner__vat
#: model:ir.model.fields,help:l10n_latam_base.field_res_users__vat
msgid "Identification Number for selected type"
msgstr "Número de identificación para el tipo seleccionado"

#. module: l10n_latam_base
#: model:ir.actions.act_window,name:l10n_latam_base.action_l10n_latam_identification_type
#: model:ir.model.fields,field_description:l10n_latam_base.field_res_partner__l10n_latam_identification_type_id
#: model:ir.model.fields,field_description:l10n_latam_base.field_res_users__l10n_latam_identification_type_id
#: model:ir.ui.menu,name:l10n_latam_base.menu_l10n_latam_identification_type
msgid "Identification Type"
msgstr "Tipo de Identificación"

#. module: l10n_latam_base
#: model:ir.model,name:l10n_latam_base.model_l10n_latam_identification_type
msgid "Identification Types"
msgstr "Tipos de Identificación"

#. module: l10n_latam_base
#: model:ir.model.fields,field_description:l10n_latam_base.field_l10n_latam_identification_type__is_vat
msgid "Is Vat"
msgstr "Es Identificación Fiscal?"

#. module: l10n_latam_base
#: model:ir.model.fields,field_description:l10n_latam_base.field_l10n_latam_identification_type____last_update
msgid "Last Modified on"
msgstr "Última modificación en"

#. module: l10n_latam_base
#: model:ir.model.fields,field_description:l10n_latam_base.field_l10n_latam_identification_type__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: l10n_latam_base
#: model:ir.model.fields,field_description:l10n_latam_base.field_l10n_latam_identification_type__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: l10n_latam_base
#: model:ir.model.fields,field_description:l10n_latam_base.field_l10n_latam_identification_type__name
msgid "Name"
msgstr "Nombre"

#. module: l10n_latam_base
#: model_terms:ir.ui.view,arch_db:l10n_latam_base.view_partner_latam_form
msgid "Number"
msgstr "Número"

#. module: l10n_latam_base
#: model:l10n_latam.identification.type,name:l10n_latam_base.it_pass
msgid "Passport"
msgstr "Pasaporte"

#. module: l10n_latam_base
#: model:ir.model.fields,field_description:l10n_latam_base.field_l10n_latam_identification_type__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: l10n_latam_base
#: model_terms:ir.ui.view,arch_db:l10n_latam_base.view_l10n_latam_identification_type_search
msgid "Show active identification types"
msgstr "Mostrar los tipos de identificación activos"

#. module: l10n_latam_base
#: model_terms:ir.ui.view,arch_db:l10n_latam_base.view_l10n_latam_identification_type_search
msgid "Show archived identification types"
msgstr "Mostrar los tipos de identificación archivados"

#. module: l10n_latam_base
#: model:ir.model.fields,help:l10n_latam_base.field_res_partner__l10n_latam_identification_type_id
#: model:ir.model.fields,help:l10n_latam_base.field_res_users__l10n_latam_identification_type_id
msgid "The type of identification"
msgstr "Tipo de identificación"

#. module: l10n_latam_base
#: model_terms:ir.ui.view,arch_db:l10n_latam_base.view_partner_latam_form
msgid "Type"
msgstr "Tipo"

#. module: l10n_latam_base
#: model:l10n_latam.identification.type,name:l10n_latam_base.it_vat
msgid "VAT"
msgstr ""
