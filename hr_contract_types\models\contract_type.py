# -*- coding: utf-8 -*-

from odoo import api, fields, models, _


class ContractType(models.Model):
    _name = 'hr.contract.type'
    _description = 'Contract Type'
    _order = 'sequence, id'

    name = fields.Char(string='Contract Type', required=True, help="Name")
    sequence = fields.Integer(help="Gives the sequence when displaying a list of Contract.", default=10)


class ContractInherit(models.Model):
    _inherit = 'hr.contract'

    type_id = fields.Many2one('hr.contract.type', string="نوع العقد",
                              required=True, help="نوع العقد",
                              default=lambda self: self.env['hr.contract.type'].search([], limit=1))

    @api.onchange('type_id')
    def make_type_id_same_contract_type_id(self):
        ####### Both fields point to same object, however to avoid confusion with other dependent module we keep them both and make TYPE_ID only visible
        for elem in self:
            elem.contract_type_id = elem.type_id
