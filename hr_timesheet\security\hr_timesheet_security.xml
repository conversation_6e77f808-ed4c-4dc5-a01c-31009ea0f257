<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data noupdate="1">
        <record model="ir.module.category" id="base.module_category_services_timesheets">
            <field name="description">Helps you manage the timesheets.</field>
            <field name="sequence">13</field>
        </record>

        <record id="group_hr_timesheet_user" model="res.groups">
            <field name="name">User: own timesheets only</field>
            <field name="category_id" ref="base.module_category_services_timesheets"/>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
        </record>

        <record id="group_hr_timesheet_approver" model="res.groups">
            <field name="name">User: all timesheets</field>
            <field name="category_id" ref="base.module_category_services_timesheets"/>
            <field name="implied_ids" eval="[(4, ref('hr_timesheet.group_hr_timesheet_user'))]"/>
        </record>

        <record id="group_timesheet_manager" model="res.groups">
            <field name="name">Administrator</field>
            <field name="category_id" ref="base.module_category_services_timesheets"/>
            <field name="implied_ids" eval="[(4, ref('hr_timesheet.group_hr_timesheet_approver')), (4, ref('hr.group_hr_user'))]"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
        </record>

        <record id="base.default_user" model="res.users">
            <field name="groups_id" eval="[(4,ref('group_timesheet_manager'))]"/>
        </record>

        <record id="timesheet_line_rule_portal_user" model="ir.rule">
            <field name="name">account.analytic.line.timesheet.portal.user</field>
            <field name="model_id" ref="analytic.model_account_analytic_line"/>
            <field name="active">0</field>
            <field name="domain_force">[
                ('project_id', '!=', False),
                '|',
                    ('project_id.message_partner_ids', 'child_of', [user.partner_id.commercial_partner_id.id]),
                    ('task_id.message_partner_ids', 'child_of', [user.partner_id.commercial_partner_id.id]),
                ('project_id.privacy_visibility', '=', 'portal'),
                ('project_id.collaborator_ids.partner_id', 'in', [user.partner_id.id]),
            ]</field>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
            <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
        </record>

        <record id="timesheet_line_rule_user" model="ir.rule">
            <field name="name">account.analytic.line.timesheet.user</field>
            <field name="model_id" ref="analytic.model_account_analytic_line"/>
            <field name="domain_force">[
                ('user_id', '=', user.id),
                ('project_id', '!=', False),
                '|', '|',
                    ('project_id.privacy_visibility', '!=', 'followers'),
                    ('project_id.message_partner_ids', 'in', [user.partner_id.id]),
                    ('task_id.message_partner_ids', 'in', [user.partner_id.id])
            ]</field>
            <field name="groups" eval="[(4, ref('group_hr_timesheet_user'))]"/>
        </record>

        <record id="timesheet_line_rule_approver" model="ir.rule">
            <field name="name">account.analytic.line.timesheet.approver</field>
            <field name="model_id" ref="analytic.model_account_analytic_line" />
            <field name="domain_force">[
                ('project_id', '!=', False),
                '|',
                    ('project_id.privacy_visibility', '!=', 'followers'),
                    ('project_id.message_partner_ids', 'in', [user.partner_id.id])
            ]</field>
            <field name="groups" eval="[(4, ref('hr_timesheet.group_hr_timesheet_approver'))]" />
        </record>

        <record id="timesheet_line_rule_manager" model="ir.rule">
            <field name="name">account.analytic.line.timesheet.manager</field>
            <field name="model_id" ref="analytic.model_account_analytic_line"/>
            <field name="domain_force">[('project_id', '!=', False)]</field>
            <field name="groups" eval="[(4, ref('group_timesheet_manager')), (4, ref('project.group_project_manager'))]"/>
        </record>

        <record id="project.group_project_manager" model="res.groups">
            <field name="implied_ids" eval="[(4, ref('hr_timesheet.group_hr_timesheet_approver'))]"/>
        </record>

    </data>
</odoo>
