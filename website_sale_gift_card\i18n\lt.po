# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_gift_card
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:29+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: website_sale_gift_card
#: model:ir.model,name:website_sale_gift_card.model_gift_card
#: model_terms:ir.ui.view,arch_db:website_sale_gift_card.gift_card_view_search
msgid "Gift Card"
msgstr "Dovanų kortelė"

#. module: website_sale_gift_card
#: model:ir.ui.menu,name:website_sale_gift_card.website_product_gift_card_menu
msgid "Gift Cards"
msgstr "Dovanų kortelės"

#. module: website_sale_gift_card
#: model_terms:ir.ui.view,arch_db:website_sale_gift_card.pay_with_gift_card_form
msgid "Gift card code..."
msgstr "Dovanų kortelės kodas..."

#. module: website_sale_gift_card
#: model_terms:ir.ui.view,arch_db:website_sale_gift_card.pay_with_gift_card_form
msgid "Pay"
msgstr "Apmokėti"

#. module: website_sale_gift_card
#: model_terms:ir.ui.view,arch_db:website_sale_gift_card.add_gift_card
msgid "Pay with a gift card"
msgstr "Mokėti dovanų kortele"

#. module: website_sale_gift_card
#: model:ir.model.fields,help:website_sale_gift_card.field_gift_card__website_id
msgid "Restrict publishing to this website."
msgstr "Apriboti skelbimą šioje svetainėje."

#. module: website_sale_gift_card
#: model:ir.model,name:website_sale_gift_card.model_sale_order
msgid "Sales Order"
msgstr "Pardavimo užsakymas"

#. module: website_sale_gift_card
#: model:ir.model,name:website_sale_gift_card.model_sale_order_line
msgid "Sales Order Line"
msgstr "Pardavimo užsakymo eilutė"

#. module: website_sale_gift_card
#: model_terms:ir.ui.view,arch_db:website_sale_gift_card.gift_card_view_search
msgid "Valid"
msgstr "Galioja"

#. module: website_sale_gift_card
#: model:ir.model.fields,field_description:website_sale_gift_card.field_gift_card__website_id
msgid "Website"
msgstr "Svetainė"
