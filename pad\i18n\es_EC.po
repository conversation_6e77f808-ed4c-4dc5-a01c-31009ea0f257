# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * pad
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Spanish (Ecuador) (https://www.transifex.com/odoo/teams/41243/es_EC/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_EC\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pad
#: model_terms:ir.ui.view,arch_db:pad.res_config_settings_view_form
msgid "API Key"
msgstr ""

#. module: pad
#: model:ir.model,name:pad.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: pad
#: model:ir.model.fields,field_description:pad.field_pad_common_display_name
msgid "Display Name"
msgstr "Nombre a Mostrar"

#. module: pad
#: model:ir.model.fields,help:pad.field_res_company_pad_key
#: model:ir.model.fields,help:pad.field_res_config_settings_pad_key
msgid "Etherpad lite api key."
msgstr ""

#. module: pad
#: model:ir.model.fields,help:pad.field_res_company_pad_server
#: model:ir.model.fields,help:pad.field_res_config_settings_pad_server
msgid "Etherpad lite server. Example: beta.primarypad.com"
msgstr ""

#. module: pad
#: model:ir.model.fields,field_description:pad.field_pad_common_id
msgid "ID"
msgstr "ID"

#. module: pad
#: model:ir.model.fields,field_description:pad.field_pad_common___last_update
msgid "Last Modified on"
msgstr "Fecha de modificación"

#. module: pad
#. openerp-web
#: code:addons/pad/static/src/js/pad.js:147
#, python-format
msgid "Loading"
msgstr ""

#. module: pad
#: model:ir.model.fields,field_description:pad.field_res_company_pad_key
msgid "Pad Api Key"
msgstr ""

#. module: pad
#: model:ir.model.fields,field_description:pad.field_res_config_settings_pad_key
msgid "Pad Api Key *"
msgstr ""

#. module: pad
#: model:ir.model.fields,field_description:pad.field_res_company_pad_server
msgid "Pad Server"
msgstr ""

#. module: pad
#: model:ir.model.fields,field_description:pad.field_res_config_settings_pad_server
msgid "Pad Server *"
msgstr ""

#. module: pad
#: code:addons/pad/models/pad.py:58
#, python-format
msgid ""
"Pad creation failed, either there is a problem with your pad server URL or "
"with your connection."
msgstr ""

#. module: pad
#. openerp-web
#: code:addons/pad/static/src/xml/pad.xml:6
#, python-format
msgid ""
"Please enter your Etherpad credentials through the menu 'Settings > General "
"Settings'."
msgstr ""

#. module: pad
#: model_terms:ir.ui.view,arch_db:pad.res_config_settings_view_form
msgid "Server"
msgstr ""

#. module: pad
#. openerp-web
#: code:addons/pad/static/src/js/pad.js:166
#, python-format
msgid "This pad will be initialized on first edit"
msgstr ""

#. module: pad
#. openerp-web
#: code:addons/pad/static/src/js/pad.js:160
#, python-format
msgid "Unable to load pad"
msgstr ""

#. module: pad
#: model_terms:ir.ui.view,arch_db:pad.res_config_settings_view_form
msgid "e.g. beta.primarypad.com"
msgstr ""

#. module: pad
#: model:ir.model,name:pad.model_pad_common
msgid "pad.common"
msgstr ""

#. module: pad
#: model:ir.model,name:pad.model_res_config_settings
msgid "res.config.settings"
msgstr ""
