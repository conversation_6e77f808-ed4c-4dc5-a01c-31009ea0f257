<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">
    <!--
        l10n_in_code use in export GSTR hsn section report.
    -->
    <record id="uom.product_uom_unit" model="uom.uom">
        <field name="l10n_in_code">UNT-UNITS</field>
    </record>
    <record id="uom.product_uom_dozen" model="uom.uom">
        <field name="l10n_in_code">DOZ-DOZENS</field>
    </record>
    <record id="uom.product_uom_kgm" model="uom.uom">
        <field name="l10n_in_code">KGS-KILOGRAMS</field>
    </record>
    <record id="uom.product_uom_gram" model="uom.uom">
        <field name="l10n_in_code">GMS-GRAMMES</field>
    </record>
    <record id="uom.product_uom_day" model="uom.uom">
        <field name="l10n_in_code">OTH-OTHERS</field>
    </record>
    <record id="uom.product_uom_hour" model="uom.uom">
        <field name="l10n_in_code">OTH-OTHERS</field>
    </record>
    <record id="uom.product_uom_ton" model="uom.uom">
        <field name="l10n_in_code">TON-TONNES</field>
    </record>
    <record id="uom.product_uom_meter" model="uom.uom">
        <field name="l10n_in_code">MTR-METERS</field>
    </record>
    <record id="uom.product_uom_millimeter" model="uom.uom">
        <field name="l10n_in_code">MLT-MILILITRE</field>
    </record>
    <record id="uom.product_uom_km" model="uom.uom">
        <field name="l10n_in_code">KME-KILOMETRE</field>
    </record>
    <record id="uom.product_uom_cm" model="uom.uom">
        <field name="l10n_in_code">CMS-CENTIMETERS</field>
    </record>
    <record id="uom.uom_square_meter" model="uom.uom">
        <field name="l10n_in_code">SQM-SQUARE METERS</field>
    </record>
    <record id="uom.product_uom_litre" model="uom.uom">
        <field name="l10n_in_code">LTR-LITRES</field>
    </record>
    <record id="uom.product_uom_cubic_meter" model="uom.uom">
        <field name="l10n_in_code">CBM-CUBIC METERS</field>
    </record>
    <record id="uom.product_uom_lb" model="uom.uom">
        <field name="l10n_in_code">OTH-OTHERS</field>
    </record>
    <record id="uom.product_uom_oz" model="uom.uom">
        <field name="l10n_in_code">OTH-OTHERS</field>
    </record>
    <record id="uom.product_uom_inch" model="uom.uom">
        <field name="l10n_in_code">OTH-OTHERS</field>
    </record>
    <record id="uom.product_uom_foot" model="uom.uom">
        <field name="l10n_in_code">OTH-OTHERS</field>
    </record>
    <record id="uom.product_uom_mile" model="uom.uom">
        <field name="l10n_in_code">OTH-OTHERS</field>
    </record>
    <record id="uom.uom_square_foot" model="uom.uom">
        <field name="l10n_in_code">OTH-OTHERS</field>
    </record>
    <record id="uom.product_uom_floz" model="uom.uom">
        <field name="l10n_in_code">OTH-OTHERS</field>
    </record>
    <record id="uom.product_uom_qt" model="uom.uom">
        <field name="l10n_in_code">OTH-OTHERS</field>
    </record>
    <record id="uom.product_uom_gal" model="uom.uom">
        <field name="l10n_in_code">UGS-US GALLONS</field>
    </record>
    <record id="uom.product_uom_cubic_inch" model="uom.uom">
        <field name="l10n_in_code">OTH-OTHERS</field>
    </record>
    <record id="uom.product_uom_cubic_foot" model="uom.uom">
        <field name="l10n_in_code">OTH-OTHERS</field>
    </record>
</odoo>
