# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * product
#
# Translators:
# <PERSON> <<EMAIL>>, 2016
# <PERSON> <<EMAIL>>, 2015
# <PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2016-03-30 08:44+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Colombia) (http://www.transifex.com/odoo/odoo-9/"
"language/es_CO/)\n"
"Language: es_CO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: product
#: selection:product.pricelist.item,applied_on:0
msgid " Product Category"
msgstr "Categoría de Producto"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_product_variant_count
#: model:ir.model.fields,field_description:product.field_product_template_product_variant_count
msgid "# of Product Variants"
msgstr "Variantes de productos"

#. module: product
#: code:addons/product/pricelist.py:372
#, python-format
msgid "%s %% discount"
msgstr "%s %% de descuento"

#. module: product
#: code:addons/product/pricelist.py:374
#, python-format
msgid "%s %% discount and %s surcharge"
msgstr "%s %% de descuento  %s de recargo"

#. module: product
#: code:addons/product/product.py:716
#, python-format
msgid "%s (copy)"
msgstr "%s (copia)"

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_1
msgid "16 GB"
msgstr "16 GB"

#. module: product
#: model:product.product,description_sale:product.product_product_3
#: model:product.template,description_sale:product.product_product_3_product_template
msgid ""
"17\" LCD Monitor\n"
"Processor AMD 8-Core\n"
"512MB RAM\n"
"HDD SH-1"
msgstr ""
"Monitor LCD 17\"\n"
"Procesador AMD 8-Core\n"
"512MB RAM\n"
"DD SH-1"

#. module: product
#: model:product.product,description:product.product_product_25
#: model:product.template,description:product.product_product_25_product_template
msgid ""
"17\" Monitor\n"
"4GB RAM\n"
"Standard-1294P Processor\n"
"QWERTY keyboard"
msgstr ""
"Monitor 17\"\n"
"4GB RAM\n"
"Procesador Standard-1294P\n"
"Teclado QWERTY"

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_5
msgid "2.4 GHz"
msgstr "2.4 GHz"

#. module: product
#: model:product.product,description_sale:product.product_product_8
#: model:product.template,description_sale:product.product_product_8_product_template
msgid ""
"2.7GHz quad-core Intel Core i5\n"
"                Turbo Boost up to 3.2GHz\n"
"                8GB (two 4GB) memory\n"
"                1TB hard drive\n"
"                Intel Iris Pro graphics\n"
"            "
msgstr ""
"2.7GHz quad-core Intel Core i5\n"
"Turbo Boost up to 3.2GHz\n"
"8GB (two 4GB) memory\n"
"1TB Disco Duro\n"
"Intel Iris Pro graphics"

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_2
msgid "32 GB"
msgstr "32 GB"

#. module: product
#: model:product.product,description_sale:product.product_product_4
#: model:product.product,description_sale:product.product_product_4b
#: model:product.product,description_sale:product.product_product_4c
#: model:product.product,description_sale:product.product_product_4d
#: model:product.template,description_sale:product.product_product_4_product_template
#: model:product.template,description_sale:product.product_product_4b_product_template
#: model:product.template,description_sale:product.product_product_4c_product_template
#: model:product.template,description_sale:product.product_product_4d_product_template
msgid ""
"7.9‑inch (diagonal) LED-backlit, 128Gb\n"
"Dual-core A5 with quad-core graphics\n"
"FaceTime HD Camera, 1.2 MP Photos"
msgstr ""
"7.9‑inch (diagonal) LED-backlit, 128Gb\n"
"Dual-core A5 con adaptador gráfico quad-core\n"
"Cámara FaceTime HD, Fotos 1.2 MP"

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "75 percent less reflection."
msgstr "75 % Menos reflexión"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
#, fuzzy
msgid ""
"<span attrs=\"{'invisible':[('base', '!=', 'list_price')]}\">Public Price  "
"-  </span>\n"
"                            <span attrs=\"{'invisible':[('base', '!=', "
"'standard_price')]}\">Cost  -  </span>\n"
"                            <span attrs=\"{'invisible':[('base', '!=', "
"'pricelist')]}\">Other Pricelist  -  </span>"
msgstr ""
"<span attrs=\"{'invisible':[('base', 'not in', "
"('list_price','standard_price'))]}\">Precio público - </span>\n"
"<span attrs=\"{'invisible':[('base', '!=', 'pricelist')]}\">Otras Lista de "
"precios </span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "<span>kg</span>"
msgstr "<span>kg</span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist
msgid "<strong>Currency</strong>:<br/>"
msgstr "<strong>Moneda</strong>:<br/>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist
msgid "<strong>Description</strong>"
msgstr "<strong>Descripción</strong>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist
msgid "<strong>Price List Name</strong>:<br/>"
msgstr "<strong>Nombre Lista de Precios</strong>:<br/>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist
msgid "<strong>Print date</strong>:<br/>"
msgstr "<strong>Fecha de impresion</strong>:<br/>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_only_form_view
msgid ""
"<strong>Warning</strong>: adding or deleting attributes\n"
"                            will delete and recreate existing variants and "
"lead\n"
"                            to the loss of their possible customizations."
msgstr ""
"<strong>Advertencia</strong>: adición o eliminación de atributos\n"
"                            borrará y volver a crear variantes existentes y "
"la pérdida de  sus posibles personalizaciones"

#. module: product
#: code:addons/product/product.py:1011 sql_constraint:product.product:0
#, python-format
msgid "A barcode can only be assigned to one product !"
msgstr "Un código de barras sólo se puede asignar a un solo producto! "

#. module: product
#: model:ir.model.fields,help:product.field_product_category_type
msgid ""
"A category of the view type is a virtual category that can be used as the "
"parent of another category to create a hierarchical structure."
msgstr ""
"Una categoría con tipo 'Vista' es una categoría que puede ser usada como "
"padre de otra categoría, para crear una estructura jerárquica."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_type
#: model:ir.model.fields,help:product.field_product_template_type
msgid ""
"A consumable is a product for which you don't manage stock, a service is a "
"non-material product provided by a company or an individual."
msgstr ""
"Un consumible es un producto del que no se le realizan valores de invetario, "
"un servicio es un producto no - material proporcionado por una empresa o un "
"individuo ."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_description_sale
#: model:ir.model.fields,help:product.field_product_template_description_sale
msgid ""
"A description of the Product that you want to communicate to your customers. "
"This description will be copied to every Sale Order, Delivery Order and "
"Customer Invoice/Refund"
msgstr ""
"Una descripción del producto que quieras comunicar a tus clientes. Esta "
"descripción se copiará a cada Pedido, Orden de Despacho y Factura/Reintegro "
"de Cliente."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_description_purchase
#: model:ir.model.fields,help:product.field_product_template_description_purchase
msgid ""
"A description of the Product that you want to communicate to your vendors. "
"This description will be copied to every Purchase Order, Receipt and Vendor "
"Bill/Refund."
msgstr ""
"Una descripción del producto que desea comunicar a sus proveedores. Esta "
"descripción se copiará en cada orden de compra, recepción y Vendor Bill / de "
"reembolso."

#. module: product
#: model:product.product,website_description:product.product_product_9
#: model:product.template,website_description:product.product_product_9_product_template
msgid "A great Keyboard. Cordless."
msgstr "Un gran Teclado. Inalámbrico."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_description
#: model:ir.model.fields,help:product.field_product_template_description
msgid ""
"A precise description of the Product, used only for internal information "
"purposes."
msgstr ""
"Una descripción precisa del Producto, usada sólo para propósitos de "
"información interna."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_pricelist_action2
msgid ""
"A price list contains rules to be evaluated in order to compute\n"
"                the sales price of the products."
msgstr ""
"Una lista de precios contiene reglas para ser evaluadas en orden\n"
"de computar el precio de venta de los productos."

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid "A screen worthy of iPad."
msgstr "Una pantalla digna del iPad."

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid ""
"About the size of a credit card — and just 5.4 mm thin — iPod nano is the "
"thinnest iPod ever made.\n"
"                                    The 2.5-inch Multi-Touch display is "
"nearly twice as big as the display on the previous iPod nano,\n"
"                                    so you can see more of the music, "
"photos, and videos you love."
msgstr ""
"Con el tamaño de una tarjeta de crédito - y sólo 5,4 mm de grosor - iPod "
"nano es el más delgado iPod jamás creado. La pantalla Multi-Touch de 2,5 "
"pulgadas es casi el doble de grande que la pantalla del iPod nano anterior, "
"para que puedas ver más de la música, fotos y vídeos que le gustan."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_active
#: model:ir.model.fields,field_description:product.field_product_product_active
#: model:ir.model.fields,field_description:product.field_product_template_active
#: model:ir.model.fields,field_description:product.field_product_uom_active
msgid "Active"
msgstr "Activo(a)"

#. module: product
#: model:product.category,name:product.product_category_all
msgid "All"
msgstr "Todo"

#. module: product
#: code:addons/product/pricelist.py:367
#, python-format
msgid "All Products"
msgstr "Todos los Productos"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "All general settings about this product are managed on"
msgstr "Todos los ajustes generales de este producto son manejados en"

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid ""
"And at the Apple Online Store, you can configure your iMac with an even more "
"powerful Intel Core i7 processor, up to 3.5GHz."
msgstr ""
"Y en la Tienda en línea Apple, usted puede configurar su iMac con un "
"poderoso procesador Intel Core i7 de hasta 3.5 GHz."

#. module: product
#: model:product.product,website_description:product.product_product_4
#: model:product.product,website_description:product.product_product_4b
#: model:product.product,website_description:product.product_product_4c
#: model:product.product,website_description:product.product_product_4d
#: model:product.template,website_description:product.product_product_4_product_template
#: model:product.template,website_description:product.product_product_4b_product_template
#: model:product.template,website_description:product.product_product_4c_product_template
#: model:product.template,website_description:product.product_product_4d_product_template
msgid "And because it’s so easy to use, it’s easy to love."
msgstr "Y por su facilidad de uso, es fácil adorarla."

#. module: product
#: model:product.product,name:product.product_product_7
#: model:product.template,name:product.product_product_7_product_template
msgid "Apple In-Ear Headphones"
msgstr "Audífonos Apple In-Ear "

#. module: product
#: model:product.product,name:product.product_product_9
#: model:product.template,name:product.product_product_9_product_template
msgid "Apple Wireless Keyboard"
msgstr "Teclado Inalambrico Apple"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_normal_form_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "Applicable On"
msgstr "Aplicable en"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_applied_on
msgid "Apply On"
msgstr "Aplicar"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_search
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Archived"
msgstr "Archivado"

#. module: product
#: model:product.product,name:product.product_assembly
#: model:product.template,name:product.product_assembly_product_template
msgid "Assembly Service Cost"
msgstr "Costo del Servicio de Ensamblaje"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_sequence
msgid "Assigns the priority to the list of product vendor."
msgstr "Asigna la prioridad del producto en la lista de los proveedores."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_line_attribute_id
#: model:ir.model.fields,field_description:product.field_product_attribute_value_attribute_id
msgid "Attribute"
msgstr "Atributo"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value_price_extra
msgid "Attribute Price Extra"
msgstr "Atributo del Precio Extra"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value_price_ids
msgid "Attribute Prices"
msgstr "Atributos del Precio"

#. module: product
#: model:ir.actions.act_window,name:product.variants_action
#: model:ir.model.fields,field_description:product.field_product_attribute_line_value_ids
#: model:ir.ui.menu,name:product.menu_variants_action
msgid "Attribute Values"
msgstr "Atributos del Valor"

#. module: product
#: model:ir.actions.act_window,name:product.attribute_action
#: model:ir.model.fields,field_description:product.field_product_product_attribute_value_ids
#: model:ir.ui.menu,name:product.menu_attribute_action
#: model_terms:ir.ui.view,arch_db:product.product_attribute_value_view_tree
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Attributes"
msgstr "Atributos"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Auxiliary input for portable devices"
msgstr "Entrada auxiliar para dispositivos portátiles"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Auxiliary port lets you connect other audio sources, like an MP3 player"
msgstr ""
"Puerto auxiliar que le permite conectar otras fuentes de audio, como un "
"reproductor de MP3"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_barcode
#: model:ir.model.fields,field_description:product.field_product_template_barcode
msgid "Barcode"
msgstr "Código de Barras"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_base
msgid ""
"Base price for computation. \n"
" Public Price: The base price will be the Sale/public Price. \n"
" Cost Price : The base price will be the cost price. \n"
" Other Pricelist : Computation of the base price based on another Pricelist."
msgstr ""
"Precio base para el cálculo.\n"
"Precio público: El precio base será la venta / precio público.\n"
"Costo: El precio base será el costo.\n"
"Otra Lista de precios: Cálculo del precio base basada en otra lista de "
"precios."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_list_price
#: model:ir.model.fields,help:product.field_product_template_list_price
msgid ""
"Base price to compute the customer price. Sometimes called the catalog price."
msgstr ""
"Precio base para calcular el precio al cliente. A veces llamado el precio de "
"catálogo."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_base
msgid "Based on"
msgstr "Basado en"

#. module: product
#: model:product.product,name:product.consu_delivery_03
#: model:product.template,name:product.consu_delivery_03_product_template
msgid "Basic Computer"
msgstr "Computador Básico"

#. module: product
#: model:product.product,name:product.membership_2
#: model:product.template,name:product.membership_2_product_template
#, fuzzy
msgid "Basic Membership"
msgstr "Computador Básico"

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid "Beautiful 7.9‑inch display."
msgstr "Hermosa Pantalla de 7.9 pulgadas."

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "Beautiful widescreen display."
msgstr "Hermosa Pantalla panorámica."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_image
msgid "Big-sized image"
msgstr "Imagen de gran tamaño"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_uom_factor_inv
msgid "Bigger Ratio"
msgstr "Relación Mayor"

#. module: product
#: selection:product.uom,uom_type:0
msgid "Bigger than the reference Unit of Measure"
msgstr "Mayor que la unidad de medida de referencia"

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_4
msgid "Black"
msgstr "Negro"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Bluetooth connectivity"
msgstr "Conectividad Bluetooth"

#. module: product
#: model:product.product,name:product.product_product_5b
#: model:product.template,name:product.product_product_5b_product_template
msgid "Bose Mini Bluetooth Speaker"
msgstr "Mini Parlante  Bluetooth Bose"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Bose Mini Bluetooth Speaker."
msgstr "Mini Parlante  Bluetooth Bose."

#. module: product
#: model:product.product,description_sale:product.product_product_5b
#: model:product.template,description_sale:product.product_product_5b_product_template
msgid "Bose's smallest portable Bluetooth speaker"
msgstr "El más pequeño Parlante portable Bose's"

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "Brilliance onscreen. And behind it."
msgstr "Brillo en la pantalla. Y detrás de él."

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid ""
"Buttons let you quickly play, pause, change songs, or adjust the volume.\n"
"                                    The smooth anodized aluminum design "
"makes iPod nano feel as good as it sounds.\n"
"                                    And iPod nano wouldn’t be iPod nano "
"without gorgeous, hard-to-choose-from color."
msgstr ""
"Botones que le permiten reproducir rápidamente, pausar, cambiar canciones o "
"ajustar el volumen. El diseño de aluminio anodizado liso hace que el iPod "
"nano se sienta tan bueno como parece. Y el iPod nano no sería iPod nano sin "
"un magnífico color, tan lindo que será díficil de elegir."

#. module: product
#: model:ir.model.fields,help:product.field_product_uom_active
msgid ""
"By unchecking the active field you can disable a unit of measure without "
"deleting it."
msgstr ""
"Si el campo activo se desmarca, permite ocultar una unidad de medida sin "
"eliminarla."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_product_price_list
msgid "Calculate Product Price per Unit Based on Pricelist Version."
msgstr "Calcular el Precio por Unidad basado en una Versión de LdP."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_rental
#: model:ir.model.fields,field_description:product.field_product_template_rental
msgid "Can be Rent"
msgstr "Puede ser Alquilado"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_sale_ok
#: model:ir.model.fields,field_description:product.field_product_template_sale_ok
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Can be Sold"
msgstr "Puede ser Vendido"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_product_price_list
msgid "Cancel"
msgstr "Cancelar"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category_type
#: model_terms:ir.ui.view,arch_db:product.product_category_form_view
msgid "Category Type"
msgstr "Tipo de Categoría"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_category_form_view
msgid "Category name"
msgstr "Nombre de la Categoría"

#. module: product
#: code:addons/product/pricelist.py:361
#, python-format
msgid "Category: %s"
msgstr "Categoría: %s"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Characteristics"
msgstr "Características"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Charges iPod/iPhone"
msgstr "Carga iPod/iPhone"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid ""
"Charging cradle recharges the battery and serves as a convenient\n"
"                                    home base for your speaker, and it lets "
"you play while it charges."
msgstr ""
"Base de carga recarga la batería y sirve como un cómoda base de operaciones "
"para su altavoz y le permite reproducir mientras se carga."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category_child_id
msgid "Child Categories"
msgstr "Categorías hijos"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_uom_categ_form_action
msgid "Click to add a new unit of measure category."
msgstr "Presione aquí para añadir una nueva categoría de unidad de medida."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_uom_form_action
msgid "Click to add a new unit of measure."
msgstr "Presione aquí para añadir una nueva unidad de medida."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_pricelist_action2
msgid "Click to create a pricelist."
msgstr "Clic para crear una lista de precios."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_normal_action
#: model_terms:ir.actions.act_window,help:product.product_normal_action_sell
#: model_terms:ir.actions.act_window,help:product.product_template_action
#: model_terms:ir.actions.act_window,help:product.product_template_action_product
#: model_terms:ir.actions.act_window,help:product.product_variant_action
msgid "Click to define a new product."
msgstr "Clic para definir un nuevo producto."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Codes"
msgstr "Códigos"

#. module: product
#: model:product.attribute,name:product.product_attribute_2
msgid "Color"
msgstr "Color"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_color
#: model:ir.model.fields,field_description:product.field_product_template_color
msgid "Color Index"
msgstr "Índice de Colores"

#. module: product
#: model:product.product,description_sale:product.product_product_6
#: model:product.template,description_sale:product.product_product_6_product_template
msgid ""
"Color: White\n"
"Capacity: 16GB\n"
"Connectivity: Wifi\n"
"Beautiful 7.9-inch display\n"
"Over 375,000 apps\n"
"Ultrafast wireless\n"
"iOS7\n"
"            "
msgstr ""
"Color: Blanco\n"
"Capacidad: 16 GB\n"
"Conectividad: Wifi\n"
"Hermosa pantalla 7,9 pulgadas\n"
"Más de 375.000 aplicaciones\n"
"Red inalámbrica ultrarápida\n"
"iOS7"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_company_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_company_id
#: model:ir.model.fields,field_description:product.field_product_product_company_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_company_id
#: model:ir.model.fields,field_description:product.field_product_template_company_id
msgid "Company"
msgstr "Compañía"

#. module: product
#: model:product.public.category,name:product.Components
msgid "Components"
msgstr "Componentes"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Compute Price"
msgstr "Precio del Computador"

#. module: product
#: model:product.product,name:product.product_product_16
#: model:product.template,name:product.product_product_16_product_template
msgid "Computer Case"
msgstr "Case del Computador"

#. module: product
#: model:product.product,name:product.product_product_3
#: model:product.template,name:product.product_product_3_product_template
msgid "Computer SC234"
msgstr "Computador SC234"

#. module: product
#: model:product.public.category,name:product.Computer_all_in_one
msgid "Computer all-in-one"
msgstr "Computador todo-en-uno"

#. module: product
#: model:product.public.category,name:product.sub_computers
msgid "Computers"
msgstr "Computadores"

#. module: product
#: code:addons/product/product.py:474
#, python-format
msgid "Consumable"
msgstr "Consumible"

#. module: product
#: model:ir.model.fields,help:product.field_product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"La conversión entre unidades de medida sólo puede ocurrir si pertenecen a la "
"misma categoría. La conversión se hará en base a los ratios definidos."

#. module: product
#: code:addons/product/product.py:122
#, python-format
msgid ""
"Conversion from Product UoM %s to Default UoM %s is not possible as they "
"both belong to different Category!."
msgstr ""
"¡La conversión de la UdM %s del producto a UdM por defecto %s no es posible "
"debido a que no pertenecen a la misma categoría!"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_history_cost
#: model:ir.model.fields,field_description:product.field_product_product_standard_price
#: model:ir.model.fields,field_description:product.field_product_template_standard_price
#: selection:product.pricelist.item,base:0
msgid "Cost"
msgstr "Costo"

#. module: product
#: model:ir.model.fields,help:product.field_product_product_standard_price
msgid ""
"Cost of the product template used for standard stock valuation in accounting "
"and used as a base price on purchase orders. Expressed in the default unit "
"of measure of the product."
msgstr ""
"El costo de la plantilla del producto será utilizado para la valoración de "
"inventario en la contabilidad y se utiliza como precio base en órdenes de "
"compra. Será expresado en la unidad de medida predeterminada del producto."

#. module: product
#: model:ir.model.fields,help:product.field_product_template_standard_price
msgid "Cost of the product, in the default unit of measure of the product."
msgstr ""
"Costo del producto, en la unidad de medida predeterminada del producto."

#. module: product
#: model:product.product,name:product.service_delivery
#: model:product.template,name:product.service_delivery_product_template
msgid "Cost-plus Contract"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_create_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_line_create_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_price_create_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_value_create_uid
#: model:ir.model.fields,field_description:product.field_product_category_create_uid
#: model:ir.model.fields,field_description:product.field_product_packaging_create_uid
#: model:ir.model.fields,field_description:product.field_product_price_history_create_uid
#: model:ir.model.fields,field_description:product.field_product_price_list_create_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist_create_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_create_uid
#: model:ir.model.fields,field_description:product.field_product_product_create_uid
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_create_uid
#: model:ir.model.fields,field_description:product.field_product_template_create_uid
#: model:ir.model.fields,field_description:product.field_product_uom_categ_create_uid
#: model:ir.model.fields,field_description:product.field_product_uom_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_create_date
#: model:ir.model.fields,field_description:product.field_product_attribute_line_create_date
#: model:ir.model.fields,field_description:product.field_product_attribute_price_create_date
#: model:ir.model.fields,field_description:product.field_product_attribute_value_create_date
#: model:ir.model.fields,field_description:product.field_product_category_create_date
#: model:ir.model.fields,field_description:product.field_product_packaging_create_date
#: model:ir.model.fields,field_description:product.field_product_price_history_create_date
#: model:ir.model.fields,field_description:product.field_product_price_list_create_date
#: model:ir.model.fields,field_description:product.field_product_pricelist_create_date
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_create_date
#: model:ir.model.fields,field_description:product.field_product_product_create_date
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_create_date
#: model:ir.model.fields,field_description:product.field_product_template_create_date
#: model:ir.model.fields,field_description:product.field_product_uom_categ_create_date
#: model:ir.model.fields,field_description:product.field_product_uom_create_date
msgid "Created on"
msgstr "Creado el"

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid ""
"Creating such a stunningly thin design took some equally stunning feats of "
"technological innovation. We refined,re-imagined,or re-engineered everything "
"about iMac from the inside out. The result is an advanced, elegant all-in-"
"one computer that’s as much a work of art as it is state of the art."
msgstr ""
"Crear un diseño delgado tan impresionante requirió de algunas hazañas "
"igualmente impresionantes de innovación tecnológica. Nosotros refinamos, "
"reimaginamos y rediseñamos todo lo relacionado con el iMac de adentro hacia "
"afuera. El resultado es un elegante equipo avanzado, todo-en-uno que más que "
"una obra de arte técnica."

#. module: product
#: model:ir.model,name:product.model_res_currency
#: model:ir.model.fields,field_description:product.field_product_pricelist_currency_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_currency_id
#: model:ir.model.fields,field_description:product.field_product_product_currency_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_currency_id
#: model:ir.model.fields,field_description:product.field_product_template_currency_id
msgid "Currency"
msgstr "Moneda"

#. module: product
#: model:product.product,name:product.product_product_5
#: model:product.template,name:product.product_product_5_product_template
msgid "Custom Computer (kit)"
msgstr "Computadora Personalizada (kit)"

#. module: product
#: model:product.product,description:product.product_product_27
#: model:product.template,description:product.product_product_27_product_template
msgid "Custom Laptop based on customer's requirement."
msgstr "Portátil personalizada de acuerdo con los requerimientos del cliente."

#. module: product
#: model:product.product,description:product.product_product_5
#: model:product.template,description:product.product_product_5_product_template
msgid "Custom computer shipped in kit."
msgstr "Equipo personalizado enviado para ser armado."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_partner_ref
msgid "Customer ref"
msgstr "Ref. cliente"

#. module: product
#: model:product.product,name:product.product_delivery_02
#: model:product.template,name:product.product_delivery_02_product_template
msgid "Datacard"
msgstr "Datacard"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_history_datetime
msgid "Date"
msgstr "Fecha"

#. module: product
#: model:product.uom,name:product.product_uom_day
msgid "Day(s)"
msgstr "Días"

#. module: product
#: model:ir.model.fields,help:product.field_product_product_uom_id
#: model:ir.model.fields,help:product.field_product_template_uom_id
msgid "Default Unit of Measure used for all stock operation."
msgstr ""
"Unidad de Medida por defecto usada para todas las operaciones de existencias."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_uom_po_id
#: model:ir.model.fields,help:product.field_product_template_uom_po_id
msgid ""
"Default Unit of Measure used for purchase orders. It must be in the same "
"category than the default unit of measure."
msgstr ""
"Unidad de medida por defecto utilizada para los pedidos de compra. Debe "
"estar en la misma categoría que la unidad de medida por defecto."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_delay
msgid "Delivery Lead Time"
msgstr "Plazo de Entrega"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_description
#: model:ir.model.fields,field_description:product.field_product_template_description
msgid "Description"
msgstr "Descripción"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Description for Quotations"
msgstr "Descripción para las Cotizaciones"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Description for Vendors"
msgstr "Descripción para Proveedores"

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid "Design. The thinnest iPod ever."
msgstr "Diseño. El más delgado iPod creado."

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute_sequence
#: model:ir.model.fields,help:product.field_product_attribute_value_sequence
msgid "Determine the display order"
msgstr "Determine el orden de visualización"

#. module: product
#: model:product.public.category,name:product.devices
msgid "Devices"
msgstr "Dispositivos"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_display_name
#: model:ir.model.fields,field_description:product.field_product_attribute_line_display_name
#: model:ir.model.fields,field_description:product.field_product_attribute_price_display_name
#: model:ir.model.fields,field_description:product.field_product_attribute_value_display_name
#: model:ir.model.fields,field_description:product.field_product_category_display_name
#: model:ir.model.fields,field_description:product.field_product_packaging_display_name
#: model:ir.model.fields,field_description:product.field_product_price_history_display_name
#: model:ir.model.fields,field_description:product.field_product_price_list_display_name
#: model:ir.model.fields,field_description:product.field_product_pricelist_display_name
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_display_name
#: model:ir.model.fields,field_description:product.field_product_product_display_name
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_display_name
#: model:ir.model.fields,field_description:product.field_product_template_display_name
#: model:ir.model.fields,field_description:product.field_product_uom_categ_display_name
#: model:ir.model.fields,field_description:product.field_product_uom_display_name
#: model:ir.model.fields,field_description:product.field_report_product_report_pricelist_display_name
msgid "Display Name"
msgstr "Nombre Público"

#. module: product
#: model:product.uom,name:product.product_uom_dozen
msgid "Dozen(s)"
msgstr "Docenas"

#. module: product
#: model:product.product,description_sale:product.consu_delivery_03
#: model:product.template,description_sale:product.consu_delivery_03_product_template
msgid ""
"Dvorak keyboard \n"
"            left-handed mouse"
msgstr ""
"Teclado Dvorak\n"
"ratón para zurdos"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_pricelist_action2
msgid ""
"Each rule include a set of applicability criteria (date range,\n"
"                product category...) and a computation that easily helps to "
"achieve\n"
"                any kind of pricing."
msgstr ""
"Cada regla incluye un conjunto de criterios de aplicabilidad (rango de "
"fechas,\n"
"categoría de producto, entre otros) y un cálculo que fácilmente le ayuda a "
"lograr\n"
"cualquier tipo de fijación de precios."

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Efficient, high-quality audio"
msgstr "Eficiente, audio de alta calidad"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_date_end
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_date_end
msgid "End Date"
msgstr "Fecha Final"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_date_end
msgid "End date for this vendor price"
msgstr "Fecha final para este precio del proveedor"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_date_end
msgid "Ending valid for the pricelist item validation"
msgstr "Vencimiento para la validación del ítem de la lista de precios"

#. module: product
#: constraint:product.category:0
msgid "Error ! You cannot create recursive categories."
msgstr "Error! No puede crear categorías recursivas."

#. module: product
#: constraint:product.attribute.line:0
msgid "Error ! You cannot use this attribute with the following value."
msgstr ""

#. module: product
#: constraint:product.product:0
msgid ""
"Error! It is not allowed to choose more than one value for a given attribute."
msgstr ""

#. module: product
#: constraint:product.pricelist.item:0
msgid "Error! The minimum margin should be lower than the maximum margin."
msgstr "¡Error! El margen mínimo debe ser menor que el margen máximo."

#. module: product
#: constraint:product.pricelist.item:0
msgid ""
"Error! You cannot assign the Main Pricelist as Other Pricelist in PriceList "
"Item!"
msgstr ""
"Error! No puede asignar la LdP Principal como Otra LdP en un elemento "
"PriceList!"

#. module: product
#: constraint:res.currency:0
msgid ""
"Error! You cannot define a rounding factor for the company's main currency "
"that is smaller than the decimal precision of 'Account'."
msgstr ""
"¡Error! No se puede definir un factor de redondeo para la moneda principal "
"de la empresa que es menor que la precisión decimal de \"Account\"."

#. module: product
#: constraint:decimal.precision:0
msgid ""
"Error! You cannot define the decimal precision of 'Account' as greater than "
"the rounding factor of the company's main currency"
msgstr ""
"¡Error! No se puede definir la precisión decimal de \"Account\" como mayor "
"que el factor de redondeo de la moneda principal de la empresa"

#. module: product
#: constraint:product.template:0
msgid ""
"Error: The default Unit of Measure and the purchase Unit of Measure must be "
"in the same category."
msgstr ""
"Error: La unidad de medida predeterminada y la Unidad de la compra de la "
"medida debe estar en la misma categoría."

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid ""
"Everything you love about iPad — the beautiful\n"
"                                    screen, fast Colors are vivid and text "
"is sharp on the iPad mini display.\n"
"                                    But what really makes it stand out is "
"its size. At 7.9 inches,\n"
"                                    it’s perfectly sized to deliver an "
"experience every bit as big as iPad."
msgstr ""
"Todo lo que te gusta de iPad - la hermosa\n"
"pantalla, colores sólidos vívidos y el texto nítido en la pantalla del iPad "
"mini.\n"
"Pero lo que realmente lo hace destacar es su tamaño. En 7.9 pulgadas,\n"
"está perfectamente dimensionado para ofrecer una experiencia casi tan grande "
"como el iPad."

#. module: product
#: model:product.product,website_description:product.product_product_4
#: model:product.product,website_description:product.product_product_4b
#: model:product.product,website_description:product.product_product_4c
#: model:product.product,website_description:product.product_product_4d
#: model:product.template,website_description:product.product_product_4_product_template
#: model:product.template,website_description:product.product_product_4b_product_template
#: model:product.template,website_description:product.product_product_4c_product_template
#: model:product.template,website_description:product.product_product_4d_product_template
msgid ""
"Everything you love about iPad — the beautiful\n"
"                                    screen, fast and fluid performance, "
"FaceTime and\n"
"                                    iSight cameras, thousands of amazing "
"apps, 10-hour\n"
"                                    battery life* — is everything you’ll "
"love about\n"
"                                    iPad mini, too. And you can hold it in "
"one hand."
msgstr ""
"Todo lo que te gusta de iPad - la pantalla hermosa, un rendimiento rápido y "
"fluido, FaceTime y cámaras iSight, miles de aplicaciones sorprendentes, "
"duración de la batería de 10 horas * - todo te va a encantar también en el "
"iPad mini. Y usted puede sostenerla en una mano."

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid ""
"Everything you love about iPad — the beautiful screen,\n"
"                                        fast and fluid performance, FaceTime "
"and iSight cameras, \n"
"                                        thousands of amazing apps, 10-hour "
"battery life* — is everything\n"
"                                        you’ll love about iPad mini, too. "
"And you can hold it in one hand."
msgstr ""
"Todo lo que te gusta de iPad - la pantalla hermosa, un rendimiento rápido y "
"fluido, FaceTime y cámaras iSight, miles de aplicaciones sorprendentes, "
"duración de la batería de 10 horas * - todo te va a encantar también en el "
"iPad mini. Y usted puede sostenerla en una mano."

#. module: product
#: model:product.product,description:product.product_product_2
#: model:product.template,description:product.product_product_2_product_template
msgid "Example of product to invoice based on delivery."
msgstr "Ejemplo de producto a facturar basado en los despachos."

#. module: product
#: model:product.product,description:product.service_order_01
#: model:product.template,description:product.service_order_01_product_template
msgid "Example of product to invoice on order."
msgstr "Ejemplo de producto a facturar en ordenes."

#. module: product
#: model:product.product,description:product.service_cost_01
#: model:product.template,description:product.service_cost_01_product_template
msgid "Example of products to invoice based on cost."
msgstr "Ejemplo de productos a facturar basados en costos."

#. module: product
#: model:product.product,description:product.product_product_1
#: model:product.template,description:product.product_product_1_product_template
msgid "Example of products to invoice based on delivery."
msgstr "Ejemplo de productos a facturar basado en despachos."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_name
#: model:ir.model.fields,help:product.field_product_pricelist_item_price
msgid "Explicit rule name for this pricelist line."
msgstr "Nombre de regla explícita para esta línea de LdP."

#. module: product
#: model:product.product,name:product.service_cost_01
#: model:product.template,name:product.service_cost_01_product_template
msgid "External Audit"
msgstr "Auditoría Externa"

#. module: product
#: model:product.public.category,name:product.External_Hard_Drive
msgid "External Hard Drive"
msgstr "Disco Externo"

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid "Fast connections.The world over."
msgstr "Conexiones veloces. El mundo sobre usted."

#. module: product
#: selection:product.pricelist.item,compute_price:0
msgid "Fix Price"
msgstr "Precio fijo"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_fixed_price
msgid "Fixed Price"
msgstr "Precio FIjo"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_min_quantity
msgid ""
"For the rule to apply, bought/sold quantity must be greater than or equal to "
"the minimum quantity specified in this field.\n"
"Expressed in the default unit of measure of the product."
msgstr ""
"Para aplicar la regla, la cantidad comprada/vendida debe ser mayor o igual a "
"la cantidad mínima fijada en este campo.\n"
"Expresada en la unidad de medida predeterminada del producto."

#. module: product
#: selection:product.pricelist.item,compute_price:0
msgid "Formula"
msgstr "Fórmula"

#. module: product
#: model:product.product,description_sale:product.product_product_7
#: model:product.template,description_sale:product.product_product_7_product_template
msgid ""
"Frequency: 5Hz to 21kHz\n"
"Impedance: 23 ohms\n"
"Sensitivity: 109 dB SPL/mW\n"
"Drivers: two-way balanced armature\n"
"Cable length: 1065 mm\n"
"Weight: 10.2 grams\n"
"            "
msgstr ""
"Frecuencia: 5Hz to 21kHz\n"
"Impendancia: 23 ohms\n"
"Sensitividad: 109 dB SPL/mW\n"
"Drivers: Armadura balanceada de dos vías\n"
"Tamaño del Cable: 1065 mm\n"
"Peso: 10.2 grams"

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "Friendly to the environment."
msgstr "Amistoso con el medio ambiente."

#. module: product
#: model:product.product,name:product.product_product_1
#: model:product.template,name:product.product_product_1_product_template
msgid "GAP Analysis Service"
msgstr "Servicio de Análisis GAP"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "General Information"
msgstr "Información General"

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid "Genius. Your own personal DJ."
msgstr "Genius. Su propio DJ personal."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_packaging_ids
#: model:ir.model.fields,help:product.field_product_template_packaging_ids
msgid ""
"Gives the different ways to package the same product. This has no impact on "
"the picking order and is mainly used if you use the EDI module."
msgstr ""
"Indica las diferentes formas de empaquetar el mismo producto. Esto no tiene "
"ningún impacto en la preparación de movimientos de inventario y se utiliza "
"principalmente si utiliza el módulo EDI."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_sequence
msgid ""
"Gives the order in which the pricelist items will be checked. The evaluation "
"gives highest priority to lowest sequence and stops as soon as a matching "
"item is found."
msgstr ""
"Indica el orden en que los elementos de la LdP serán comprobados. En la "
"evaluación se da máxima prioridad a la secuencia más baja y se detiene tan "
"pronto como se encuentra un elemento coincidente."

#. module: product
#: model:ir.model.fields,help:product.field_product_category_sequence
msgid "Gives the sequence order when displaying a list of product categories."
msgstr ""
"Indica el orden de secuencia cuando se muestra una lista de categorías de "
"producto."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_sequence
#: model:ir.model.fields,help:product.field_product_template_sequence
msgid "Gives the sequence order when displaying a product list"
msgstr ""
"Indica el orden de secuencia cuando se muestra una lista de categorías de "
"producto."

#. module: product
#: selection:product.pricelist.item,applied_on:0
msgid "Global"
msgstr "Global"

#. module: product
#: model:product.product,name:product.membership_0
#: model:product.template,name:product.membership_0_product_template
msgid "Gold Membership"
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_24
#: model:product.template,name:product.product_product_24_product_template
msgid "Graphics Card"
msgstr "Tarjeta gráfica"

#. module: product
#: model:product.product,name:product.product_product_17
#: model:product.template,name:product.product_product_17_product_template
msgid "HDD SH-1"
msgstr "HDD SH-1"

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid "Have Genius call the tunes."
msgstr "Tiene un Genius que llama las melodías."

#. module: product
#: model:product.public.category,name:product.Headset
msgid "Headset"
msgstr "Audífonos "

#. module: product
#: model:product.product,website_description:product.product_product_7
#: model:product.template,website_description:product.product_product_7_product_template
msgid "Hear, hear."
msgstr "Audífonos estándares"

#. module: product
#: model:product.product,description_sale:product.product_product_11
#: model:product.product,description_sale:product.product_product_11b
#: model:product.template,description_sale:product.product_product_11_product_template
#: model:product.template,description_sale:product.product_product_11b_product_template
msgid ""
"Height: 76.5 mm\n"
"Width:  39.6 mm\n"
"Depth:  5.4 mm\n"
"Weight: 31 grams"
msgstr ""
"Altura: 76.5 mm\n"
"Ancho:  39.6 mm\n"
"Profundidad:  5.4 mm\n"
"Peso: 31 gramos"

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "Highly rated designs."
msgstr "Diseños de alta valoración."

#. module: product
#: model:product.uom,name:product.product_uom_hour
msgid "Hour(s)"
msgstr "Hora(s)"

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid ""
"How did we make an already gorgeous widescreen display even better? By "
"making it 75 percent less reflective. And by re-architecting the LCD and "
"moving it right up against the cover glass. So you see your photos, games, "
"movies, and everything else in vivid, lifelike detail."
msgstr ""
"¿Cómo hacemos aún mejor una ya magnífica pantalla panorámica ? La hicimos 75 "
"por ciento menos reflectante. Y volviendo a la arquitectura de la pantalla "
"LCD y se refleja excelente en la cubierta de vidrio. Así que ya puede ver "
"tus fotos, juegos, películas y todo lo demás en vivo con detalles realistas."

#. module: product
#: model:ir.model.fields,help:product.field_product_uom_factor_inv
msgid ""
"How many times this Unit of Measure is bigger than the reference Unit of "
"Measure in this category:\n"
"1 * (this unit) = ratio * (reference unit)"
msgstr ""
"Cuántas veces es esta unidad de medida más grande comparada con la unidad de "
"medida de referencia de esta categoría:\n"
"1 * (esta unidad) = ratio * (unidad de referencia)"

#. module: product
#: model:ir.model.fields,help:product.field_product_uom_factor
msgid ""
"How much bigger or smaller this unit is compared to the reference Unit of "
"Measure for this category:\n"
"1 * (reference unit) = ratio * (this unit)"
msgstr ""
"Cuántas veces es esta unidad de medida más pequeña comparada con la unidad "
"de medida de referencia de esta categoría:\n"
"1 * (esta unidad) = ratio * (unidad de referencia)"

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid "How to get your groove on."
msgstr "¿Cómo obtener el mejor ritmo?"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_id
#: model:ir.model.fields,field_description:product.field_product_attribute_line_id
#: model:ir.model.fields,field_description:product.field_product_attribute_price_id
#: model:ir.model.fields,field_description:product.field_product_attribute_value_id
#: model:ir.model.fields,field_description:product.field_product_category_id
#: model:ir.model.fields,field_description:product.field_product_packaging_id
#: model:ir.model.fields,field_description:product.field_product_price_history_id
#: model:ir.model.fields,field_description:product.field_product_price_list_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_id
#: model:ir.model.fields,field_description:product.field_product_product_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_id
#: model:ir.model.fields,field_description:product.field_product_template_id
#: model:ir.model.fields,field_description:product.field_product_uom_categ_id
#: model:ir.model.fields,field_description:product.field_product_uom_id
#: model:ir.model.fields,field_description:product.field_report_product_report_pricelist_id
msgid "ID"
msgstr "ID"

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid "If it's made for iPad, it's made for iPad mini."
msgstr "Si es hecho para una iPad, es hecho también para una iPad mini."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_active
msgid ""
"If unchecked, it will allow you to hide the pricelist without removing it."
msgstr "Si no está marcado, la LdP podrá ocultarse sin eliminarla."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_active
#: model:ir.model.fields,help:product.field_product_template_active
msgid ""
"If unchecked, it will allow you to hide the product without removing it."
msgstr "Si no está marcado, el producto podrá ocultarse sin ser eliminado."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_image
msgid "Image"
msgstr "Imagen"

#. module: product
#: model:ir.model.fields,help:product.field_product_product_image
msgid ""
"Image of the product variant (Big-sized image of product template if false). "
"It is automatically resized as a 1024x1024px image, with aspect ratio "
"preserved."
msgstr ""
"Imagen de la variante del producto (Imagen grande de la plantilla del "
"producto en caso de que esté vacía). Es redimensionada automáticamente a una "
"imagen de 1024x1024px, manteniendo la proporción."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_image_medium
msgid ""
"Image of the product variant (Medium-sized image of product template if "
"false)."
msgstr ""
"Imagen de la variante del producto (Imagen mediana de la plantilla del "
"producto en caso de que esté vacía)."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_image_small
msgid ""
"Image of the product variant (Small-sized image of product template if "
"false)."
msgstr ""
"Imagen de la variante del producto (Imagen pequeña de la plantilla del "
"producto en caso de que esté vacía)."

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "Individually calibrated for true-to-life color."
msgstr "Calibrado individualmente para la fidelidad del color."

#. module: product
#: model:ir.model,name:product.model_product_supplierinfo
msgid "Information about a product vendor"
msgstr "Información acerca de un proveedor de producto"

#. module: product
#: model:product.product,website_description:product.product_product_7
#: model:product.template,website_description:product.product_product_7_product_template
msgid ""
"Inside each earpiece is a stainless steel mesh cap that protects the "
"precision acoustic\n"
"                                    components from dust and debris. You can "
"remove the caps for cleaning or replace\n"
"                                    them with an extra set that’s included "
"in the box."
msgstr ""
"Dentro de cada auricular existe un protector de acero inoxidable para "
"proteger el auricular acústico de polvo y basura. Puede quitar las tapas "
"para limpiar o reemplazarlo con un juego extra de protectores que se incluye "
"en la caja."

#. module: product
#: model:product.category,name:product.product_category_2
msgid "Internal"
msgstr "Interno"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_categ_id
#: model:ir.model.fields,field_description:product.field_product_template_categ_id
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Internal Category"
msgstr "Categoría Interna"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_code
#: model:ir.model.fields,field_description:product.field_product_product_default_code
#: model:ir.model.fields,field_description:product.field_product_template_default_code
msgid "Internal Reference"
msgstr "Referencia Interna"

#. module: product
#: model:ir.model.fields,help:product.field_product_product_barcode
msgid "International Article Number used for product identification."
msgstr "Número de Artículo Internacional usado para identificar el producto."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Inventory"
msgstr "Inventario"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_is_product_variant
#: model:ir.model.fields,field_description:product.field_product_template_is_product_variant
msgid "Is a product variant"
msgstr "¿Es una variante de un producto?"

#. module: product
#: model:product.product,website_description:product.product_product_7
#: model:product.template,website_description:product.product_product_7_product_template
msgid "Keep it clean."
msgstr "Mantenerlo limpio."

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "Key Features"
msgstr "Principales caracteristicas "

#. module: product
#: model:product.public.category,name:product.Keyboard_Mouse
msgid "Keyboard / Mouse"
msgstr "Teclado / Ratón"

#. module: product
#: model:product.product,name:product.product_product_27
#: model:product.template,name:product.product_product_27_product_template
msgid "Laptop Customized"
msgstr "Portátil Personalizado"

#. module: product
#: model:product.product,name:product.product_product_25
#: model:product.template,name:product.product_product_25_product_template
msgid "Laptop E5023"
msgstr "Portátil E5023"

#. module: product
#: model:product.public.category,name:product.laptops
msgid "Laptops"
msgstr "Portátiles"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute___last_update
#: model:ir.model.fields,field_description:product.field_product_attribute_line___last_update
#: model:ir.model.fields,field_description:product.field_product_attribute_price___last_update
#: model:ir.model.fields,field_description:product.field_product_attribute_value___last_update
#: model:ir.model.fields,field_description:product.field_product_category___last_update
#: model:ir.model.fields,field_description:product.field_product_packaging___last_update
#: model:ir.model.fields,field_description:product.field_product_price_history___last_update
#: model:ir.model.fields,field_description:product.field_product_price_list___last_update
#: model:ir.model.fields,field_description:product.field_product_pricelist___last_update
#: model:ir.model.fields,field_description:product.field_product_pricelist_item___last_update
#: model:ir.model.fields,field_description:product.field_product_product___last_update
#: model:ir.model.fields,field_description:product.field_product_supplierinfo___last_update
#: model:ir.model.fields,field_description:product.field_product_template___last_update
#: model:ir.model.fields,field_description:product.field_product_uom___last_update
#: model:ir.model.fields,field_description:product.field_product_uom_categ___last_update
#: model:ir.model.fields,field_description:product.field_report_product_report_pricelist___last_update
msgid "Last Modified on"
msgstr "Última Modificación el"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_line_write_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_price_write_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_value_write_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_write_uid
#: model:ir.model.fields,field_description:product.field_product_category_write_uid
#: model:ir.model.fields,field_description:product.field_product_packaging_write_uid
#: model:ir.model.fields,field_description:product.field_product_price_history_write_uid
#: model:ir.model.fields,field_description:product.field_product_price_list_write_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_write_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist_write_uid
#: model:ir.model.fields,field_description:product.field_product_product_write_uid
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_write_uid
#: model:ir.model.fields,field_description:product.field_product_template_write_uid
#: model:ir.model.fields,field_description:product.field_product_uom_categ_write_uid
#: model:ir.model.fields,field_description:product.field_product_uom_write_uid
msgid "Last Updated by"
msgstr "Actualizado por"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_line_write_date
#: model:ir.model.fields,field_description:product.field_product_attribute_price_write_date
#: model:ir.model.fields,field_description:product.field_product_attribute_value_write_date
#: model:ir.model.fields,field_description:product.field_product_attribute_write_date
#: model:ir.model.fields,field_description:product.field_product_category_write_date
#: model:ir.model.fields,field_description:product.field_product_packaging_write_date
#: model:ir.model.fields,field_description:product.field_product_price_history_write_date
#: model:ir.model.fields,field_description:product.field_product_price_list_write_date
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_write_date
#: model:ir.model.fields,field_description:product.field_product_pricelist_write_date
#: model:ir.model.fields,field_description:product.field_product_product_write_date
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_write_date
#: model:ir.model.fields,field_description:product.field_product_template_write_date
#: model:ir.model.fields,field_description:product.field_product_uom_categ_write_date
#: model:ir.model.fields,field_description:product.field_product_uom_write_date
msgid "Last Updated on"
msgstr "Actualizado"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_delay
msgid ""
"Lead time in days between the confirmation of the purchase order and the "
"receipt of the products in your warehouse. Used by the scheduler for "
"automatic computation of the purchase order planning."
msgstr ""
"El tiempo de entrega en días entre la confirmación de la orden de compra y "
"la recepción de los productos en su almacén. Utilizado por el programador "
"para el cálculo automático de la planificación de la orden de compra."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category_parent_left
msgid "Left Parent"
msgstr "Padre Izquierdo"

#. module: product
#: model:product.uom.categ,name:product.uom_categ_length
msgid "Length / Distance"
msgstr "Longitud / Distancia"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_attribute_line_ids
msgid "Lines"
msgstr "Líneas"

#. module: product
#: model:product.uom,name:product.product_uom_litre
msgid "Liter(s)"
msgstr "Litros"

#. module: product
#: model:product.product,name:product.consu_delivery_02
#: model:product.template,name:product.consu_delivery_02_product_template
msgid "Little server"
msgstr "Servidor pequeño"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_packaging_ids
#: model:ir.model.fields,field_description:product.field_product_template_packaging_ids
msgid "Logistical Units"
msgstr "Unidades de logística"

#. module: product
#: model:res.groups,name:product.group_uom
msgid "Manage Multiple Units of Measure"
msgstr "Manejar Varias Unidades de Medida"

#. module: product
#: model:res.groups,name:product.group_pricelist_item
msgid "Manage Pricelist Items"
msgstr "Administrar Líneas de Lista de Precios"

#. module: product
#: model:res.groups,name:product.group_stock_packaging
msgid "Manage Product Packaging"
msgstr "Administra el empaque del producto"

#. module: product
#: model:ir.model.fields,field_description:product.field_base_config_settings_group_product_variant
#: model:res.groups,name:product.group_product_variant
msgid "Manage Product Variants"
msgstr "Administra las variantes del producto"

#. module: product
#: model:res.groups,name:product.group_mrp_properties
msgid "Manage Properties of Product"
msgstr "Administra las propiedades del producto"

#. module: product
#: model:res.groups,name:product.group_uos
msgid "Manage Secondary Unit of Measure"
msgstr "Administra una segunda unidad de medida"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Max. Margin"
msgstr "Margen máximo"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_price_max_margin
msgid "Max. Price Margin"
msgstr "Margen precio máx."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_image_medium
#: model:ir.model.fields,field_description:product.field_product_template_image_medium
msgid "Medium-sized image"
msgstr "Imagen mediana"

#. module: product
#: model:ir.model.fields,help:product.field_product_template_image_medium
msgid ""
"Medium-sized image of the product. It is automatically resized as a "
"128x128px image, with aspect ratio preserved, only when the image exceeds "
"one of those sizes. Use this field in form views or some kanban views."
msgstr ""
"Imagen mediana del producto. Es automáticamente redimensionada a una imagen "
"de 128x128px, manteniendo la proporción, sólo cuando la imagen excede uno de "
"esos límites. Use este campo en los formularios o algunas vistas kanban."

#. module: product
#: model:product.attribute,name:product.product_attribute_1
msgid "Memory"
msgstr "Memoria"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Min. Margin"
msgstr "Margen mín."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_price_min_margin
msgid "Min. Price Margin"
msgstr "Margen precio mín."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_min_quantity
msgid "Min. Quantity"
msgstr "Cantidad mín."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_min_qty
msgid "Minimal Quantity"
msgstr "Cantidad mínima"

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "More energy efficient."
msgstr "Más eficiente energéticamente. "

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "More features."
msgstr "Más características."

#. module: product
#: model:product.product,name:product.product_product_20
#: model:product.template,name:product.product_product_20_product_template
msgid "Motherboard I9P57"
msgstr "Placa base I9P57"

#. module: product
#: model:product.product,name:product.product_product_10
#: model:product.template,name:product.product_product_10_product_template
msgid "Mouse, Optical"
msgstr "Mouse, Óptico"

#. module: product
#: model:product.product,name:product.product_product_12
#: model:product.template,name:product.product_product_12_product_template
msgid "Mouse, Wireless"
msgstr "Mouse, Inalambrico"

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid "Music. It's what beats inside."
msgstr "Música. Es lo que late en su interior."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_name
#: model:ir.model.fields,field_description:product.field_product_category_complete_name
#: model:ir.model.fields,field_description:product.field_product_category_name
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_name
#: model:ir.model.fields,field_description:product.field_product_product_name
#: model:ir.model.fields,field_description:product.field_product_template_name
#: model:ir.model.fields,field_description:product.field_product_uom_categ_name
msgid "Name"
msgstr "Nombre"

#. module: product
#: model:product.public.category,name:product.network
msgid "Network"
msgstr "Red"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "New Price ="
msgstr "Nuevo Precio ="

#. module: product
#: selection:product.category,type:0
msgid "Normal"
msgstr "Normal"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Notes"
msgstr "Notas"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "Other Information"
msgstr "Otra Información"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_base_pricelist_id
#: selection:product.pricelist.item,base:0
msgid "Other Pricelist"
msgstr "Otra LdP"

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid "Over 375,000 apps."
msgstr "Más de 375.000 aplicaciones."

#. module: product
#: model:ir.model,name:product.model_product_packaging
#: model_terms:ir.ui.view,arch_db:product.product_packaging_form_view
#: model_terms:ir.ui.view,arch_db:product.product_packaging_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Packaging"
msgstr "Empaquetado"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_packaging_name
msgid "Packaging Type"
msgstr "Tipo de Empaque"

#. module: product
#: model:ir.actions.act_window,name:product.action_packaging_view
msgid "Packagings"
msgstr "Empaques"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category_parent_id
msgid "Parent Category"
msgstr "Categoría Padre"

#. module: product
#: model:ir.model,name:product.model_res_partner
msgid "Partner"
msgstr "Asociado"

#. module: product
#: model:product.public.category,name:product.Pen_Drive
msgid "Pen Drive"
msgstr "Pen Drive"

#. module: product
#: selection:product.pricelist.item,compute_price:0
msgid "Percentage (discount)"
msgstr "Porcentaje (descuento)"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_percent_price
msgid "Percentage Price"
msgstr "Porcentaje del precio:"

#. module: product
#: model:product.category,name:product.product_category_5
msgid "Physical"
msgstr "Físico"

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid "Playlists. The perfect mix for every mood."
msgstr "Listas de reproducción. La mezcla perfecta para cada estado de ánimo."

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Plays where you play"
msgstr "Reproduce donde estes"

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid ""
"Powered by fourth-generation Intel Core processors, this iMac is the fastest "
"yet. Every model in the lineup comes standard with a quad-core Intel Core i5 "
"processor, starting at 2.7GHz and topping out at 3.4GHz."
msgstr ""
"Desarrollado para procesadores Intel Core de cuarta generación, este iMac es "
"el más rápido. Todos los modelos de la gama viene con un procesador Intel "
"Core i5 de cuatro núcleos, a partir de 2,7 GHz y llega al tope de 3.4GHz."

#. module: product
#: model:product.product,name:product.service_order_01
#: model:product.template,name:product.service_order_01_product_template
msgid "Prepaid Consulting"
msgstr "Consultoría Prepagada"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_price
#: model:ir.model.fields,field_description:product.field_product_product_price
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_price
#: model:ir.model.fields,field_description:product.field_product_template_price
#: model_terms:ir.ui.view,arch_db:product.product_normal_form_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Price"
msgstr "Precio"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Price Computation"
msgstr "Cálculo del precio"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_price_discount
msgid "Price Discount"
msgstr "Descuento precio"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_price_price_extra
msgid "Price Extra"
msgstr "Precio adicional"

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute_value_price_extra
msgid ""
"Price Extra: Extra price for the variant with this attribute value on sale "
"price. eg. 200 price extra, 1000 + 200 = 1200."
msgstr ""
"Precio adicional: Precio adicional para la variante con este valor de "
"atributo de precio de venta. Por ejemplo. 200 precio adicional, 1000 + 200 = "
"1200."

#. module: product
#: model:ir.actions.act_window,name:product.action_product_price_list
#: model:ir.model,name:product.model_product_price_list
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.report_pricelist
#: model_terms:ir.ui.view,arch_db:product.view_product_price_list
msgid "Price List"
msgstr "Lista de Precios"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_price_round
msgid "Price Rounding"
msgstr "Redondeo precio"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_price_surcharge
msgid "Price Surcharge"
msgstr "Sobrecarga precio"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_template_kanban_view
msgid "Price:"
msgstr "Precio:"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list_price_list
msgid "PriceList"
msgstr "Lista de Precios"

#. module: product
#: model:ir.actions.report.xml,name:product.action_report_pricelist
#: model:ir.model,name:product.model_product_pricelist
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_pricelist_id
#: model:ir.model.fields,field_description:product.field_product_product_pricelist_id
#: model:ir.model.fields,field_description:product.field_product_template_pricelist_id
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Pricelist"
msgstr "Lista de Precios"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_applied_on
msgid "Pricelist Item applicable on selected option"
msgstr "Líneas de lista de precios son aplicables a la opción selecionada "

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_ids
#: model:ir.model.fields,field_description:product.field_product_product_item_ids
#: model:ir.model.fields,field_description:product.field_product_template_item_ids
#: model_terms:ir.ui.view,arch_db:product.product_normal_form_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Pricelist Items"
msgstr "Líneas de la lista de precios"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_name
msgid "Pricelist Name"
msgstr "Lista de Precios"

#. module: product
#: model:ir.model,name:product.model_product_pricelist_item
msgid "Pricelist item"
msgstr "Elemento de la LdP"

#. module: product
#: model:ir.actions.act_window,name:product.product_pricelist_action2
#: model:ir.ui.menu,name:product.menu_product_pricelist_action2
#: model:ir.ui.menu,name:product.menu_product_pricelist_main
msgid "Pricelists"
msgstr "Lista de Precios"

#. module: product
#: model:res.groups,name:product.group_product_pricelist
msgid "Pricelists On Product"
msgstr "Lista de Precios en Productos"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_partner_property_form
msgid "Pricelists are managed on"
msgstr "Las LdP son gestionadas en"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Pricing"
msgstr "Precios"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_product_price_list
msgid "Print"
msgstr "Imprimir"

#. module: product
#: model:product.public.category,name:product.printer
msgid "Printer"
msgstr "Impresora"

#. module: product
#: model:product.product,name:product.product_product_22
#: model:product.template,name:product.product_product_22_product_template
msgid "Processor Core i5 2.70 Ghz"
msgstr "Procesador Core i5 2.70 Ghz"

#. module: product
#: model:ir.actions.act_window,name:product.product_normal_action
#: model:ir.model,name:product.model_product_product
#: model:ir.model.fields,field_description:product.field_product_packaging_product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_price_history_product_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_product_id
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_tree_view
#: selection:product.pricelist.item,applied_on:0
#: model:res.request.link,name:product.req_link_product
msgid "Product"
msgstr "Producto"

#. module: product
#: model:ir.model,name:product.model_product_attribute
msgid "Product Attribute"
msgstr "Atributo del Producto"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_price_value_id
msgid "Product Attribute Value"
msgstr "Valor de atributo del producto"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_attribute_line_ids
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line_ids
msgid "Product Attributes"
msgstr "Atributos del Producto"

#. module: product
#: model:ir.actions.act_window,name:product.product_category_action_form
#: model:ir.ui.menu,name:product.menu_product_category_action_form
#: model_terms:ir.ui.view,arch_db:product.product_category_list_view
#: model_terms:ir.ui.view,arch_db:product.product_category_search_view
msgid "Product Categories"
msgstr "Categorías de Producto"

#. module: product
#: model:ir.model,name:product.model_product_category
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_categ_id
msgid "Product Category"
msgstr "Categoría del Producto"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_product_manager
#: model:ir.model.fields,field_description:product.field_product_template_product_manager
msgid "Product Manager"
msgstr "Gerente de producto"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Product Name"
msgstr "Producto"

#. module: product
#: model:ir.model,name:product.model_product_template
#: model:ir.model.fields,field_description:product.field_product_attribute_line_product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_attribute_price_product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_product_product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_product_tmpl_id
#: model_terms:ir.ui.view,arch_db:product.product_search_form_view
msgid "Product Template"
msgstr "Plantilla del Producto"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_type
#: model:ir.model.fields,field_description:product.field_product_template_type
msgid "Product Type"
msgstr "Tipo de producto"

#. module: product
#: model:ir.model,name:product.model_product_uom
msgid "Product Unit of Measure"
msgstr "Unidad de Medida del Producto"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_product_id
#: model_terms:ir.ui.view,arch_db:product.product_normal_form_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
#: selection:product.pricelist.item,applied_on:0
msgid "Product Variant"
msgstr "Variantes de producto"

#. module: product
#: model:ir.actions.act_window,name:product.product_normal_action_sell
#: model:ir.actions.act_window,name:product.product_variant_action
#: model:ir.ui.menu,name:product.menu_products
#: model_terms:ir.ui.view,arch_db:product.product_product_tree_view
msgid "Product Variants"
msgstr "Variantes de Producto"

#. module: product
#: model:ir.model,name:product.model_product_uom_categ
msgid "Product uom categ"
msgstr "Categ. UdM de producto"

#. module: product
#: model:ir.actions.act_window,name:product.product_template_action
#: model:ir.actions.act_window,name:product.product_template_action_product
#: model:ir.model.fields,field_description:product.field_product_product_product_variant_ids
#: model:ir.model.fields,field_description:product.field_product_template_product_variant_ids
#: model:ir.ui.menu,name:product.menu_product_template_action
#: model:ir.ui.menu,name:product.prod_config_main
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Products"
msgstr "Productos"

#. module: product
#: model:ir.actions.report.xml,name:product.report_product_label
msgid "Products Labels"
msgstr "Etiquetas de productos"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_search
msgid "Products Price"
msgstr "Precio de Productos"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_tree
msgid "Products Price List"
msgstr "LdP de Productos"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_search
msgid "Products Price Search"
msgstr "Buscar precio productos"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Products your store in the inventory"
msgstr "Productos que almacena en el inventario"

#. module: product
#: code:addons/product/product.py:824
#, python-format
msgid "Products: "
msgstr "Productos:"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_lst_price
#: selection:product.pricelist.item,base:0
msgid "Public Price"
msgstr "Precio Publico"

#. module: product
#: model:product.pricelist,name:product.list0
msgid "Public Pricelist"
msgstr "LdP Publica"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_description_purchase
#: model:ir.model.fields,field_description:product.field_product_template_description_purchase
msgid "Purchase Description"
msgstr "Descripción de compra"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_uom_po_id
#: model:ir.model.fields,field_description:product.field_product_template_uom_po_id
msgid "Purchase Unit of Measure"
msgstr "Unidad de medida compra"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_qty
msgid "Quantity"
msgstr "Cantidad"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_packaging_qty
msgid "Quantity by Package"
msgstr "Cantidad por paquete"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list_qty1
msgid "Quantity-1"
msgstr "Cantidad-1"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list_qty2
msgid "Quantity-2"
msgstr "Cantidad-2"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list_qty3
msgid "Quantity-3"
msgstr "Cantidad-3"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list_qty4
msgid "Quantity-4"
msgstr "Cantidad-4"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list_qty5
msgid "Quantity-5"
msgstr "Cantidad-5"

#. module: product
#: model:product.product,name:product.product_product_13
#: model:product.template,name:product.product_product_13_product_template
msgid "RAM SR5"
msgstr "RAM SR5"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_uom_factor
msgid "Ratio"
msgstr "Proporción"

#. module: product
#: selection:product.uom,uom_type:0
msgid "Reference Unit of Measure for this category"
msgstr "Unidad de Medida referencial para esta categoría"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Remote control for power, volume, track seek"
msgstr "Control remoto para encendido, volumen, búsqueda de pistas"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category_parent_right
msgid "Right Parent"
msgstr "Padre Derecho"

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid ""
"Right from the start, apps made for iPad also work with iPad mini.\n"
"                                    They’re immersive, full-screen apps that "
"let you do almost anything\n"
"                                    you can imagine. And with automatic "
"updates,\n"
"                                    you're always getting the best "
"experience possible."
msgstr ""
"Desde el principio, las aplicaciones hechas para el iPad también funcionan "
"con el iPad Mini. Son aplicaciones de pantalla completa cuya inmersión te "
"permiten hacer casi cualquier cosa puedes imaginar. Y con las "
"actualizaciones automáticas, siempre estás obteniendo la mejor experiencia "
"posible."

#. module: product
#: model:product.product,website_description:product.product_product_4
#: model:product.product,website_description:product.product_product_4b
#: model:product.product,website_description:product.product_product_4c
#: model:product.product,website_description:product.product_product_4d
#: model:product.template,website_description:product.product_product_4_product_template
#: model:product.template,website_description:product.product_product_4b_product_template
#: model:product.template,website_description:product.product_product_4c_product_template
#: model:product.template,website_description:product.product_product_4d_product_template
msgid ""
"Right from the start, there’s a lot to love about\n"
"                                    iPad. It’s simple yet powerful. Thin and "
"light yet\n"
"                                    full-featured. It can do just about "
"everything and\n"
"                                    be just about anything."
msgstr ""
"Desde el principio, hay mucho que amarás del iPad. Es muy sencillo pero "
"potente. Delgado y ligero pero con todas las funciones. Se puede hacer casi "
"todo y ser casi cualquier cosa."

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid ""
"Right from the start, there’s a lot to love about iPad.\n"
"                                   It’s simple yet powerful. Thin and light "
"yet full-\n"
"                                   featured. It can do just about everything "
"and be just\n"
"                                   about anything.And because it’s so easy "
"to use, it’s\n"
"                                   easy to love."
msgstr ""
"Desde el principio, hay mucho que amarás del iPad. \n"
"Es muy sencillo pero potente. Delgado y ligero \n"
"pero con todas las funciones. Se puede hacer casi \n"
"todo y ser casi cualquier cosa. Es fácil de usar, es fácil\n"
"de amar."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Rounding Method"
msgstr "Método redondeo"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_uom_rounding
msgid "Rounding Precision"
msgstr "Precisión de redondeo"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Sale Conditions"
msgstr "Condiciones de Ventas"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_description_sale
#: model:ir.model.fields,field_description:product.field_product_template_description_sale
msgid "Sale Description"
msgstr "Descripción de venta"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_list_price
#: model:ir.model.fields,field_description:product.field_product_product_lst_price
#: model:ir.model.fields,field_description:product.field_product_template_list_price
msgid "Sale Price"
msgstr "Precio"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_partner_property_product_pricelist
msgid "Sale Pricelist"
msgstr "LdP Venta"

#. module: product
#: model:product.category,name:product.product_category_1
msgid "Saleable"
msgstr "Vendible"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Sales"
msgstr "Ventas"

#. module: product
#: model:res.groups,name:product.group_sale_pricelist
msgid "Sales Pricelists"
msgstr "LdP de Ventas"

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid ""
"Say you’re listening to a song you love and you want to stay in the mood.\n"
"                                        Just tap Genius. It finds other "
"songs on iPod nano that go great together\n"
"                                        and makes a Genius playlist for you. "
"For more song combinations\n"
"                                        you wouldn’t have thought of "
"yourself, create Genius Mixes in iTunes\n"
"                                        and sync the ones you like to iPod "
"nano. Then tap Genius Mixes and\n"
"                                        rediscover songs you haven’t heard "
"in a while — or find music you forgot you even had."
msgstr ""
"Digamos que estás escuchando una canción que te gusta y quieres permanecer "
"en el estado de ánimo.\n"
"Sólo tienes que pulsar Genius. Encuentra otras canciones en el iPod nano que "
"van muy bien juntos y hace una lista de reproducción Genius para ti. \n"
"Para más combinaciones de canciones, cree mezclas Genius en iTunes\n"
"y sincronice las que le gusten a iPod nano. A continuación, toque Mezclas "
"Genius y\n"
"redescubrir canciones que no han oído hablar de un tiempo - o encontrar la "
"música que usted se olvidó que aún tenía."

#. module: product
#: model:product.public.category,name:product.Screen
msgid "Screen"
msgstr "Pantalla"

#. module: product
#: model:ir.model.fields,help:product.field_product_product_categ_id
#: model:ir.model.fields,help:product.field_product_template_categ_id
msgid "Select category for the current product"
msgstr "Seleccione la categoría para el producto actual."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_sequence
#: model:ir.model.fields,field_description:product.field_product_attribute_value_sequence
#: model:ir.model.fields,field_description:product.field_product_category_sequence
#: model:ir.model.fields,field_description:product.field_product_packaging_sequence
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_sequence
#: model:ir.model.fields,field_description:product.field_product_product_sequence
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_sequence
#: model:ir.model.fields,field_description:product.field_product_template_sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: product
#: model:product.product,name:product.consu_delivery_01
#: model:product.public.category,name:product.server
#: model:product.template,name:product.consu_delivery_01_product_template
msgid "Server"
msgstr "Servidor"

#. module: product
#: code:addons/product/product.py:474
#, python-format
msgid "Service"
msgstr "Servicio"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model:product.category,name:product.product_category_3
#: model:product.public.category,name:product.services
msgid "Services"
msgstr "Servicios"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_price_round
msgid ""
"Sets the price so that it is a multiple of this value.\n"
"Rounding is applied after the discount and before the surcharge.\n"
"To have prices that end in 9.99, set rounding 10, surcharge -0.01"
msgstr ""
"Calcula el precio de modo que sea un múltiplo de este valor.\n"
"El redondeo se aplica después del descuento y antes del recargo.\n"
"Para que los precios terminen en 9,99, fije el redondeo a 10, y el recargo a "
"-0,01."

#. module: product
#: model:ir.model.fields,field_description:product.field_base_config_settings_company_share_product
msgid "Share product to all companies"
msgstr "Compartir el producto con todas las compañías"

#. module: product
#: model:ir.model.fields,help:product.field_base_config_settings_company_share_product
msgid ""
"Share your product to all companies defined in your instance.\n"
" * Checked : Product are visible for every company, even if a company is "
"defined on the partner.\n"
" * Unchecked : Each company can see only its product (product where company "
"is defined). Product not related to a company are visible for all companies."
msgstr ""
"Comparte su producto a todas las empresas definidas en la instancia.\n"
"* Activada: Los productos son visibles para todas las empresas, incluso si "
"una empresa se define en otra empresa.\n"
"* Desactivada: Cada empresa sólo puede ver sus productos (productos donde la "
"empresa sea definida). Productos no relacionados con una empresa son "
"visibles para todas las empresas."

#. module: product
#: model:product.product,name:product.membership_1
#: model:product.template,name:product.membership_1_product_template
msgid "Silver Membership"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Sleek, compact design"
msgstr "Sleek, diseño compacto"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_image_small
#: model:ir.model.fields,field_description:product.field_product_template_image_small
msgid "Small-sized image"
msgstr "Imagen pequeña"

#. module: product
#: model:ir.model.fields,help:product.field_product_template_image_small
msgid ""
"Small-sized image of the product. It is automatically resized as a 64x64px "
"image, with aspect ratio preserved. Use this field anywhere a small image is "
"required."
msgstr ""
"Imagen pequeña del producto. Es automáticamente redimensionada a una imagen "
"de 64x64px, manteniendo la proporción. Use este campo en cualquier parte "
"donde se necesite una imagen pequeña."

#. module: product
#: selection:product.uom,uom_type:0
msgid "Smaller than the reference Unit of Measure"
msgstr "Menor que la Unidad de Referencia de la Medida"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid ""
"Soft covers are available separately in blue, green or orange. Pick a color "
"to match your style."
msgstr ""
"Tapas blandas están disponibles por separado en azul, verde o naranja. Elija "
"un color para que coincida con su estilo."

#. module: product
#: model:product.category,name:product.product_category_4
#: model:product.public.category,name:product.Software
msgid "Software"
msgstr "Software"

#. module: product
#: model:product.public.category,name:product.Speakers
msgid "Speakers"
msgstr "Parlantes"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_categ_id
msgid ""
"Specify a product category if this rule only applies to products belonging "
"to this category or its children categories. Keep empty otherwise."
msgstr ""
"Especifique una categoría de producto si esta regla sólo se aplica a los "
"productos pertenecientes a esta categoría o sus categorías relacionadas. "
"Manténgalo desactivado si es lo contrario."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_product_id
msgid ""
"Specify a product if this rule only applies to one product. Keep empty "
"otherwise."
msgstr ""
"Especifique un producto si esta regla sólo se aplica a un solo producto. "
"Mantenga desactivada si es lo contrario."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_product_tmpl_id
msgid ""
"Specify a template if this rule only applies to one product template. Keep "
"empty otherwise."
msgstr ""
"Especifique una plantilla si esta regla sólo se aplica a una sola plantilla "
"de producto. Mantenga desactivada si es lo contrario."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_sale_ok
#: model:ir.model.fields,help:product.field_product_template_sale_ok
msgid "Specify if the product can be selected in a sales order line."
msgstr ""
"Especifique si el producto se puede seleccionar en una línea de órdenes de "
"venta."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_price_surcharge
msgid ""
"Specify the fixed amount to add or substract(if negative) to the amount "
"calculated with the discount."
msgstr ""
"Especifique la cantidad fija para sumar o restar (si es negativo) al valor "
"calculado con el descuento."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_price_max_margin
msgid "Specify the maximum amount of margin over the base price."
msgstr "Especifique la cantidad máxima de margen sobre el precio base."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_price_min_margin
msgid "Specify the minimum amount of margin over the base price."
msgstr "Especifique la cantidad mínima de margen sobre el precio base."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_date_start
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_date_start
msgid "Start Date"
msgstr "Fecha de Inicio"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_date_start
msgid "Start date for this vendor price"
msgstr "Fecha de inicio para este precio del proveedor"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_date_start
msgid "Starting date for the pricelist item validation"
msgstr "Starting date for the pricelist item validation"

#. module: product
#: model:ir.actions.act_window,name:product.product_supplierinfo_type_action
msgid "Supplier Pricelist"
msgstr "LdP Proveedor"

#. module: product
#: model:product.product,name:product.product_product_2
#: model:product.template,name:product.product_product_2_product_template
msgid "Support Contract (on timesheet)"
msgstr "Contrato de Soporte (en parte de horas)"

#. module: product
#: model:product.product,name:product.product_delivery_01
#: model:product.template,name:product.product_delivery_01_product_template
msgid "Switch, 24 ports"
msgstr "Switch, 24 puertos"

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid "Sync to your heart’s content."
msgstr "Sincronizado con el contenido de su corazón."

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid ""
"Tap to play your favorite songs. Or entire albums.\n"
"                                    Or everything by one artist. You can "
"even browse by genres or composers.\n"
"                                    Flip through your music: Album art looks "
"great on the bigger screen.\n"
"                                    Or to keep things fresh, give iPod nano "
"a shake and it shuffles to a different song in your music library."
msgstr ""
"Pulse para reproducir sus canciones favoritas. O álbumes enteros. O a un "
"artista determinado. Usted incluso puede navegar por géneros y compositores. "
"Recorrer su música: el arte del álbum se ve muy bien en la pantalla grande. "
"O para mantener las cosas frescas, de a su iPod nano una sacudida y cambia "
"hacia una canción diferente de su biblioteca de música."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_name_template
msgid "Template Name"
msgstr "Plantilla"

#. module: product
#: model:product.product,website_description:product.product_product_7
#: model:product.template,website_description:product.product_product_7_product_template
msgid ""
"The Apple In-Ear Headphones deliver a truly immersive sound experience by "
"drastically\n"
"                                    reducing unwanted outside noises. The "
"soft, silicone ear tips fit snugly and comfortably\n"
"                                    in your ear, creating a seal that "
"isolates your music from your surroundings.\n"
"                                    Three different sizes of ear tips are "
"included so you can find a perfect fit for each ear.\n"
"                                    Also included are a convenient carrying "
"case for the ear tips and a cable-control case\n"
"                                    for the headphones themselves."
msgstr ""
"Los auriculares Apple In-Ear proporcionan una experiencia de sonido "
"realmente envolvente por la drástica reducción de los ruidos externos no "
"deseados. Las puntas de silicona suave hacen que se sienta cómodos en el "
"oído, creando un sello que aísla la música desde su entorno. Tres tamaños "
"diferentes de protectores están incluidos para que pueda encontrar un ajuste "
"perfecto para cada oído. También se incluye una funda de transporte para los "
"protectores y un estuche para los cables de los auriculares."

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid ""
"The Bose® SoundLink® mini is Bose's smallest portable Bluetooth speaker. Its "
"ultra-compact size fits in the \n"
"                                    palm of your hand, yet gives you full, "
"natural sound wirelessly from your iPhone, iPad, or iPod. Grab it and go \n"
"                                    full-featured. It can do just about "
"everything and\n"
"                                    experience music just about anywhere."
msgstr ""
"El mini Bose® SoundLink® es más pequeño altavoz Bluetooth portátil de Bose. "
"Su tamaño ultra compacto cabe en la palma de la mano, sin embargo, le da un "
"sonido completo y natural de forma inalámbrica desde tu iPhone, iPad o iPod. "
"Viene con todas las funciones que puedas requerir. Se puede hacer casi todo "
"y vivir una experiencia musical vívida en cualquier lugar."

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid ""
"The SoundLink® Mini speaker is small and light enough\n"
"                                        to tuck into your bag. It weighs in "
"at just 1.5 pounds.\n"
"                                        Its low profile lets you place it "
"almost anywhere and\n"
"                                        provides a low center of gravity "
"that makes it nearly\n"
"                                        impossible to tip over."
msgstr ""
"El SoundLink® Mini altavoz es pequeño y bastante ligero para \n"
"meter en su bolsa. Pesa sólo 1.5 libras. Su diseño compacto le \n"
"permite colocarlo en casi en cualquier lugar y proporciona un bajo \n"
"centro de gravedad que hace que sea casi imposible de caer."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid ""
"The computed price is expressed in the default Unit of Measure of the "
"product."
msgstr ""
"El precio calculado es expresado en la Unidad de Medida predeterminada del "
"producto."

#. module: product
#: model:ir.model.fields,help:product.field_product_uom_rounding
msgid ""
"The computed quantity will be a multiple of this value. Use 1.0 for a Unit "
"of Measure that cannot be further split, such as a piece."
msgstr ""
"La cantidad calculada será un múltiplo de este valor. Use 1.0 para una "
"Unidad de Medida que no puede ser dividida, como una pieza."

#. module: product
#: sql_constraint:product.uom:0
msgid "The conversion ratio for a unit of measure cannot be 0!"
msgstr "¡El ratio de conversión para una unidad de medida no puede ser 0!"

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "The desktop. In its most advanced form ever"
msgstr "El escritorio. En su forma más avanzada jamás creada"

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging_sequence
msgid "The first in the sequence is the default one."
msgstr "El primero en la secuencia es el predeterminado."

#. module: product
#: model:product.product,website_description:product.product_product_4
#: model:product.product,website_description:product.product_product_4b
#: model:product.product,website_description:product.product_product_4c
#: model:product.product,website_description:product.product_product_4d
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_4_product_template
#: model:product.template,website_description:product.product_product_4b_product_template
#: model:product.template,website_description:product.product_product_4c_product_template
#: model:product.template,website_description:product.product_product_4d_product_template
#: model:product.template,website_description:product.product_product_6_product_template
msgid "The full iPad experience."
msgstr "La completa experiencia iPad."

#. module: product
#: model:product.product,website_description:product.product_product_9
#: model:product.template,website_description:product.product_product_9_product_template
msgid ""
"The incredibly thin Apple Wireless Keyboard uses Bluetooth technology,\n"
"                                    which makes it compatible with iPad. And "
"you’re free to type wherever\n"
"                                    you like — with the keyboard in front of "
"your iPad or on your lap."
msgstr ""
"El increíblemente delgado teclado inalámbrico Apple Wireless utiliza la "
"tecnología Bluetooth,lo que lo hace compatible con iPad. Y usted es libre de "
"escribir lo que sea\n"
"te gusta - con el teclado en frente de su iPad o en su regazo."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_min_qty
msgid ""
"The minimal quantity to purchase from this vendor, expressed in the vendor "
"Product Unit of Measure if not any, in the default unit of measure of the "
"product otherwise."
msgstr ""
"La cantidad mínima de compra de este proveedor, expresada en la Unidad de "
"Medida del proveedor del producto, si no existe, será expresada en la unidad "
"de medida predeterminada del producto."

#. module: product
#: code:addons/product/product.py:332
#, python-format
msgid ""
"The operation cannot be completed:\n"
"You are trying to delete an attribute value with a reference on a product "
"variant."
msgstr ""
"La operación no puede ser completada:\n"
"Está tratando de borrar un valor de atributo con una refeencia en una "
"variante de producto."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_price
msgid "The price to purchase a product"
msgstr "El precio a comprar de un producto"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_normal_action
#: model_terms:ir.actions.act_window,help:product.product_normal_action_sell
#: model_terms:ir.actions.act_window,help:product.product_variant_action
msgid ""
"The product form contains information to simplify the sale\n"
"                process: price, notes in the quotation, accounting data,\n"
"                procurement methods, etc."
msgstr ""
"El formulario del producto contiene información para simplificar\n"
" el proceso de venta: precio, descripción, información contable,\n"
" métodos de reposición, etc."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_template_action
msgid ""
"The product form contains information to simplify the sale process: price, "
"notes in the quotation, accounting data, procurement methods, etc."
msgstr ""
"El formulario del producto contiene información para simplificar el proceso "
"de venta: precio, descripción, información contable, métodos de reposición, "
"etc."

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid ""
"The rechargeable lithium-ion battery delivers up to seven hours of "
"playtime.\n"
"                                    And at home, you can listen even longer—"
"the charging cradle lets\n"
"                                    you listen while it charges."
msgstr ""
"La batería de iones de litio recargable ofrece hasta siete horas de "
"reproducción. Y en casa, puede escuchar incluso más tiempo gracias al "
"cargador que le permite se escuche mientras se carga."

#. module: product
#: model:product.product,description_sale:product.product_product_9
#: model:product.template,description_sale:product.product_product_9_product_template
msgid ""
"The sleek aluminium Apple Wireless Keyboard.\n"
"            "
msgstr "El elegante diseño en aluminio del Teclado Inalámbrico Apple."

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid ""
"The speaker has a range of about 30 feet, so you can enjoy\n"
"                                    the sound you want without wires. It "
"pairs easily with your\n"
"                                    smartphone, iPad® or other Bluetooth "
"device.\n"
"                                    And it remembers the most recent six "
"devices you've used,\n"
"                                    so reconnecting is even simpler."
msgstr ""
"El altavoz tiene un alcance de unos 30 pies, para que pueda disfrutar el "
"sonido que desee sin necesidad de cables. Se conecta fácilmente con su "
"smartphone, iPad® u otro dispositivo Bluetooth. Y memoriza los más recientes "
"seis dispositivos que ha utilizado, así que la reconexión es aún más simple."

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging_qty
msgid "The total number of products you can put by pallet or box."
msgstr "El número total de productos que puede poner por palet o caja."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_volume
#: model:ir.model.fields,help:product.field_product_template_volume
msgid "The volume in m3."
msgstr "El volumen en m3"

#. module: product
#: model:ir.model.fields,help:product.field_product_product_weight
#: model:ir.model.fields,help:product.field_product_template_weight
msgid "The weight of the contents in Kg, not including any packaging, etc."
msgstr "El peso de los contenidos en Kg, sin incluir cualquier envase, etc."

#. module: product
#: model:product.product,website_description:product.product_product_4
#: model:product.product,website_description:product.product_product_4b
#: model:product.product,website_description:product.product_product_4c
#: model:product.product,website_description:product.product_product_4d
#: model:product.template,website_description:product.product_product_4_product_template
#: model:product.template,website_description:product.product_product_4b_product_template
#: model:product.template,website_description:product.product_product_4c_product_template
#: model:product.template,website_description:product.product_product_4d_product_template
msgid "There is less of it, but no less to it."
msgstr "Hay menos de él, pero no menos a la misma."

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid "There's less of it, but no less to it."
msgstr "Hay menos de él, pero no menos a la misma."

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid ""
"There’s another way to get a good mix of music on iPod: Let Genius do the "
"work.\n"
"                                        Activate Genius in iTunes on your "
"computer, and it automatically finds songs that sound\n"
"                                        great together. Then it creates "
"Genius Mixes, which you can easily sync to your iPod.\n"
"                                        It’s the perfect way to rediscover "
"songs you haven’t listened to in forever."
msgstr ""
"Hay otra manera de conseguir una buena mezcla de música en el iPod: Dejar "
"que Genius haga el trabajo. Activa Genius en el iTunes de tu ordenador y "
"encontrará automáticamente las canciones que suenan muy bien juntas. "
"Entonces se crea  un Genius Mixes que puedes sincronizar fácilmente a su "
"iPod. Es la manera perfecta para volver descubrir canciones que no has "
"escuchado en mucho tiempo."

#. module: product
#: sql_constraint:product.attribute.value:0
msgid "This attribute value already exists !"
msgstr "¡El valor del atributo ya existe!"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_product_uom
msgid "This comes from the product form."
msgstr "Esto proviene del formulario del producto"

#. module: product
#: model:ir.model.fields,help:product.field_product_product_image_variant
msgid ""
"This field holds the image used as image for the product variant, limited to "
"1024x1024px."
msgstr ""
"Este campo guarda la imagen de la variante del producto, limitada a "
"1024x1024px."

#. module: product
#: model:ir.model.fields,help:product.field_product_template_image
msgid ""
"This field holds the image used as image for the product, limited to "
"1024x1024px."
msgstr "Este campo guarda la imagen del producto, limitada a 1024x1024px."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_qty
msgid "This is a quantity which is converted into Default Unit of Measure."
msgstr "Una cantidad que es convertida en la unidad de medida predeterminada."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_price_extra
msgid "This is the sum of the extra price of all attributes"
msgstr "Esta es la suma de los precios adicionales de todos los atributos"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "This note will be displayed on requests for quotation."
msgstr "Esta nota será mostrada en las solicitudes de cotización."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "This note will be displayed on the quotations."
msgstr "Esta nota será mostrada en las cotizaciones."

#. module: product
#: model:ir.model.fields,help:product.field_res_partner_property_product_pricelist
msgid ""
"This pricelist will be used, instead of the default one, for sales to the "
"current partner"
msgstr ""
"Esta LdP se utilizará, en lugar de la por defecto, para las ventas de la "
"empresa actual."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_product_code
msgid ""
"This vendor's product code will be used when printing a request for "
"quotation. Keep empty to use the internal one."
msgstr ""
"El código de producto de este proveedor se utilizará al imprimir una "
"solicitud de cotización. Manténgalo desactivado para usar la predeterminada."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_product_name
msgid ""
"This vendor's product name will be used when printing a request for "
"quotation. Keep empty to use the internal one."
msgstr ""
"El código de producto de este proveedor se utilizará al imprimir una "
"solicitud de cotización. Manténgalo desactivado para usar la predeterminada"

#. module: product
#: model:product.product,website_description:product.product_product_7
#: model:product.template,website_description:product.product_product_7_product_template
msgid "Two is better than one."
msgstr "Dos es mejor que uno."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_uom_uom_type
msgid "Type"
msgstr "Tipo"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid ""
"USB port allows for software update to ensure ongoing Bluetooth device "
"compatibility"
msgstr ""
"Puerto USB permite la actualización de software para asegurar la "
"compatibilidad de dispositivos Bluetooth en curso"

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid "Ultrafast wireless."
msgstr "Inalámbrico ultrarrápido."

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "Ultrathin design"
msgstr "Diseño ultraliviano"

#. module: product
#: model:product.uom.categ,name:product.product_uom_categ_unit
msgid "Unit"
msgstr "Unidad"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_uom_id
#: model:ir.model.fields,field_description:product.field_product_template_uom_id
#: model:ir.model.fields,field_description:product.field_product_uom_name
msgid "Unit of Measure"
msgstr "Unidad de Medida"

#. module: product
#: model:ir.actions.act_window,name:product.product_uom_categ_form_action
#: model:ir.ui.menu,name:product.menu_product_uom_categ_form_action
msgid "Unit of Measure Categories"
msgstr "Categorías de Unidades de Medida"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_uom_category_id
msgid "Unit of Measure Category"
msgstr "Categoría de la Unidad de Medida"

#. module: product
#: model:product.uom,name:product.product_uom_unit
msgid "Unit(s)"
msgstr "Unidades"

#. module: product
#: model:ir.actions.act_window,name:product.product_uom_form_action
#: model:ir.ui.menu,name:product.menu_product_uom_form_action
#: model:ir.ui.menu,name:product.next_id_16
#: model_terms:ir.ui.view,arch_db:product.product_uom_form_view
#: model_terms:ir.ui.view,arch_db:product.product_uom_tree_view
msgid "Units of Measure"
msgstr "Unidades de Medida"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_uom_categ_form_view
msgid "Units of Measure categories"
msgstr "Categorías de unidades de medida"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_uom_categ_form_action
msgid ""
"Units of measure belonging to the same category can be\n"
"                converted between each others. For example, in the category\n"
"                <i>'Time'</i>, you will have the following units of "
"measure:\n"
"                Hours, Days."
msgstr ""
"Las unidades de medida pertenecientes a la misma categoría pueden ser\n"
"convertido entre los demás. Por ejemplo, en la categoría\n"
"<i> 'Tiempo' </i>, tendrá las siguientes unidades de medida:\n"
"Horas, Días."

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Universal iPod docking station fits most iPod/iPhone models"
msgstr ""
"Soporte para iPod universal se adapta a la mayoría de los modelos de iPod / "
"iPhone"

#. module: product
#: model:product.product,website_description:product.product_product_7
#: model:product.template,website_description:product.product_product_7_product_template
msgid ""
"Unlike many small headphones, each earpiece of the Apple In-Ear Headphones\n"
"                                    contains two separate high-performance "
"drivers — a woofer to handle bass and\n"
"                                    mid-range sounds and a tweeter for high-"
"frequency audio. These dedicated\n"
"                                    drivers help ensure accurate, detailed "
"sound across the entire sonic spectrum.\n"
"                                    The result: you’re immersed in the music "
"and hear details you never knew existed.\n"
"                                    Even when listening to an old favorite, "
"you may feel like you’re hearing it for the first time."
msgstr ""
"A diferencia de muchos auriculares pequeños, cada auricular de los "
"auriculares de Apple In-Ear contiene dos conductores de alto rendimiento "
"independientes - un woofer para manejar bajos y sonidos de rango medio y un "
"tweeter para el audio de alta frecuencia. Estos conductores dedicados ayudan "
"a garantizar un sonido preciso y detallado en todo el espectro sonoro. El "
"resultado: usted está inmerso en la música y escucha detalles que no sabía "
"que existían. Incluso cuando se escucha uno de sus favoritos antiguos, usted "
"puede sentir como que estás escuchando por primera vez."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "Validity"
msgstr "Validez"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value_name
msgid "Value"
msgstr "Valor"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value_ids
msgid "Values"
msgstr "Valores"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_price_extra
msgid "Variant Extra Price"
msgstr "Extra precio en variantes"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_image_variant
msgid "Variant Image"
msgstr "Imagen de la Variante"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Variant Information"
msgstr "Información del Variante"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Variant Prices"
msgstr "Precios del Variante"

#. module: product
#: model:ir.actions.act_window,name:product.product_attribute_value_action
#: model_terms:ir.ui.view,arch_db:product.attribute_tree_view
#: model_terms:ir.ui.view,arch_db:product.variants_tree_view
msgid "Variant Values"
msgstr "Valor del Variante"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value_product_ids
#: model_terms:ir.ui.view,arch_db:product.product_template_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_template_only_form_view
msgid "Variants"
msgstr "Variantes"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_name
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "Vendor"
msgstr "Proveedor"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_tree_view
msgid "Vendor Information"
msgstr "Información del Vendedor"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_product_code
msgid "Vendor Product Code"
msgstr "Código de Producto en el Proveedor"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_product_name
msgid "Vendor Product Name"
msgstr "Nombre del Producto en el Proveedor"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_product_uom
msgid "Vendor Unit of Measure"
msgstr "Unidad de Medida del Proveedor"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_name
msgid "Vendor of this product"
msgstr "Proveedor de este producto"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_seller_ids
#: model:ir.model.fields,field_description:product.field_product_template_seller_ids
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Vendors"
msgstr "Proveedores"

#. module: product
#: selection:product.category,type:0
msgid "View"
msgstr "Ver"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_volume
#: model:ir.model.fields,field_description:product.field_product_template_volume
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model:product.uom.categ,name:product.product_uom_categ_vol
msgid "Volume"
msgstr "Volumen"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Volume control on main system"
msgstr "Sistema principal del control del volumen"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid ""
"Wall charger can be plugged into the cradle or directly into the speaker"
msgstr "Compartir el producto con todas las compañías"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_warranty
#: model:ir.model.fields,field_description:product.field_product_template_warranty
msgid "Warranty"
msgstr "Garantía"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_weight
#: model:ir.model.fields,field_description:product.field_product_template_weight
#: model:product.uom.categ,name:product.product_uom_categ_kgm
msgid "Weight"
msgstr "Peso"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Weights"
msgstr "Pesos"

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid "When one playlist isn’t enough."
msgstr "Cuando una lista de música no es suficiente."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_product_id
msgid ""
"When this field is filled in, the vendor data will only apply to the variant."
msgstr ""
"Cuando este campo es llenado, los datos ingresados por proveedor solo "
"aplicará a la variante."

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_3
msgid "White"
msgstr "Blanco"

#. module: product
#: model:product.product,website_description:product.product_product_4
#: model:product.product,website_description:product.product_product_4b
#: model:product.product,website_description:product.product_product_4c
#: model:product.product,website_description:product.product_product_4d
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_4_product_template
#: model:product.template,website_description:product.product_product_4b_product_template
#: model:product.template,website_description:product.product_product_4c_product_template
#: model:product.template,website_description:product.product_product_4d_product_template
#: model:product.template,website_description:product.product_product_6_product_template
msgid "Why you'll love an iPad."
msgstr "Porque tu amaras un iPad"

#. module: product
#: model:product.attribute,name:product.product_attribute_3
msgid "Wi-Fi"
msgstr "Wi-Fi"

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid ""
"With advanced Wi‑Fi that’s up to twice as fast as\n"
"                                   any previous-generation iPad and access "
"to fast\n"
"                                   cellular data networks around the world, "
"iPad mini\n"
"                                   lets you download content, stream video,\n"
"                                   and browse the web at amazing speeds."
msgstr ""
"Con una red Wi-Fi avanzada que es hasta dos veces más rápida que cualquier "
"generación anterior iPad y acceso a la rápida redes de datos móviles de todo "
"el mundo, el iPad mini le permite descargar los contenidos, secuencia de "
"vídeo y navegar por Internet a velocidades increíbles."

#. module: product
#: model:ir.model.fields,help:product.field_base_config_settings_group_product_variant
msgid ""
"Work with product variant allows you to define some variant of the same "
"products, an ease the product management in the ecommerce for example"
msgstr ""
"Trabajar con variantes de producto permite definir algunas variantes del "
"mismo poducto, y facilitar la administración de productos de comercio "
"electrónico por ejemplo"

#. module: product
#: model:product.uom.categ,name:product.uom_categ_wtime
msgid "Working Time"
msgstr "Horario de Trabajo"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_uom_form_action
msgid ""
"You must define a conversion rate between several Units of\n"
"                Measure within the same category."
msgstr ""
"Debe definir un tipo de conversión entre varias unidades de Medida en la "
"misma categoría."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_normal_action
#: model_terms:ir.actions.act_window,help:product.product_normal_action_sell
#: model_terms:ir.actions.act_window,help:product.product_variant_action
msgid ""
"You must define a product for everything you sell, whether it's\n"
"                a physical product, a consumable or a service you offer to\n"
"                customers."
msgstr ""
"Usted debe definir un producto para todo lo que venda, ya sea un producto "
"físico, un consumible o un servicio que le ofrece a los clientes."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_template_action
#, fuzzy
msgid ""
"You must define a product for everything you sell, whether it's a physical "
"product, a consumable or a service you offer to customers."
msgstr ""
"Usted debe definir un producto para todo lo que venda, ya sea un producto "
"físico, un consumible o un servicio que le ofrece a los clientes."

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid ""
"You probably have multiple playlists in iTunes on your computer.\n"
"                                        One for your commute. One for the "
"gym. Sync those playlists\n"
"                                        to iPod, and you can play the "
"perfect mix for whatever\n"
"                                        mood strikes you. VoiceOver tells "
"you the name of each playlist,\n"
"                                        so it’s easy to switch between them "
"and find the one you want without looking."
msgstr ""
"Probablemente tengas varias listas de reproducción en iTunes en tu "
"ordenador. Uno para su viaje. Uno para el gimnasio. Sincroniza esas listas "
"de reproducción al iPod, y se puede reproducir la mezcla perfecta para "
"conjugarlo con tu estado de ánimo. VoiceOver te dice el nombre de cada lista "
"de reproducción, por lo que es fácil de cambiar entre ellas y encontrar el "
"que usted desea sin mirar."

#. module: product
#: model:product.product,name:product.product_order_01
#: model:product.template,name:product.product_order_01_product_template
msgid "Zed+ Antivirus"
msgstr "Zed+ Antivirus"

#. module: product
#: model:ir.model,name:product.model_base_config_settings
msgid "base.config.settings"
msgstr "base.config.settings"

#. module: product
#: model:product.uom,name:product.product_uom_cm
msgid "cm"
msgstr "cm"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "days"
msgstr "días"

#. module: product
#: model:ir.model,name:product.model_decimal_precision
msgid "decimal.precision"
msgstr "decimal.precision"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_category_form_view
msgid "e.g. Lamps"
msgstr "ej. Lámparas"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "e.g. Odoo Enterprise Susbcription"
msgstr "ej. Suscripción Odoo Empresarial"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "e.g. USD Retailers"
msgstr "ej. Minoristas USD"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_uom_form_view
msgid "e.g: 1 * (reference unit) = ratio * (this unit)"
msgstr "ejemplo: 1 * (unidad de referencia) = ratio * (esta unidad)"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_uom_form_view
msgid "e.g: 1 * (this unit) = ratio * (reference unit)"
msgstr "ejemplo: 1 * (esta unidad) = ratio * (unidad de referencia)"

#. module: product
#: model:product.uom,name:product.product_uom_floz
msgid "fl oz"
msgstr "fl oz"

#. module: product
#: model:product.uom,name:product.product_uom_foot
msgid "foot(ft)"
msgstr "pies(pt)"

#. module: product
#: model:product.uom,name:product.product_uom_gal
msgid "gal(s)"
msgstr "gal(s)"

#. module: product
#: model:product.product,name:product.product_product_8
#: model:product.template,name:product.product_product_8_product_template
msgid "iMac"
msgstr "iMac"

#. module: product
#: model:product.product,name:product.product_product_6
#: model:product.template,name:product.product_product_6_product_template
msgid "iPad Mini"
msgstr "iPad Mini"

#. module: product
#: model:product.product,name:product.product_product_4
#: model:product.product,name:product.product_product_4b
#: model:product.product,name:product.product_product_4c
#: model:product.product,name:product.product_product_4d
#: model:product.template,name:product.product_product_4_product_template
#: model:product.template,name:product.product_product_4b_product_template
#: model:product.template,name:product.product_product_4c_product_template
#: model:product.template,name:product.product_product_4d_product_template
msgid "iPad Retina Display"
msgstr "iPad Retina Display"

#. module: product
#: model:product.product,name:product.product_product_11
#: model:product.product,name:product.product_product_11b
#: model:product.template,name:product.product_product_11_product_template
#: model:product.template,name:product.product_product_11b_product_template
msgid "iPod"
msgstr "iPod"

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid ""
"iTunes on your Mac or PC makes it easy to load up\n"
"                                        your iPod. Just choose the "
"playlists, audiobooks,\n"
"                                        podcasts, and other audio files you "
"want, then sync."
msgstr ""
"iTunes en tu Mac o PC hace que sea fácil de cargar hasta tu iPod. Sólo "
"tienes que elegir las listas de reproducción, audiolibros, podcasts y otros "
"archivos de audio que desea, para sincronizarlos."

#. module: product
#: model:product.uom,name:product.product_uom_inch
msgid "inch(es)"
msgstr "pulgada(s)"

#. module: product
#: model:product.uom,name:product.product_uom_kgm
msgid "kg"
msgstr "k"

#. module: product
#: model:product.uom,name:product.product_uom_km
msgid "km"
msgstr "km"

#. module: product
#: model:product.uom,name:product.product_uom_lb
msgid "lb(s)"
msgstr "lb(s)"

#. module: product
#: model:product.uom,name:product.product_uom_mile
msgid "mile(s)"
msgstr "milla(s)"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "months"
msgstr "meses"

#. module: product
#: model:product.uom,name:product.product_uom_oz
msgid "oz(s)"
msgstr "oz(s)"

#. module: product
#: model:ir.model,name:product.model_product_attribute_line
msgid "product.attribute.line"
msgstr "product.attribute.line"

#. module: product
#: model:ir.model,name:product.model_product_attribute_price
msgid "product.attribute.price"
msgstr "product.attribute.price"

#. module: product
#: model:ir.model,name:product.model_product_attribute_value
msgid "product.attribute.value"
msgstr "product.attribute.value"

#. module: product
#: model:ir.model,name:product.model_product_price_history
msgid "product.price.history"
msgstr "product.price.history"

#. module: product
#: model:product.uom,name:product.product_uom_qt
msgid "qt"
msgstr "cant"

#. module: product
#: model:product.product,description_sale:product.consu_delivery_02
#: model:product.template,description_sale:product.consu_delivery_02_product_template
msgid ""
"raid 1 \n"
"            512ECC ram"
msgstr ""
"raid 1 \n"
"            512ECC ram"

#. module: product
#: model:product.product,description_sale:product.consu_delivery_01
#: model:product.template,description_sale:product.consu_delivery_01_product_template
msgid ""
"raid 10 \n"
"            2048ECC ram"
msgstr ""
"raid 10 \n"
"            2048ECC ram"

#. module: product
#: model:ir.model,name:product.model_report_product_report_pricelist
msgid "report.product.report_pricelist"
msgstr "report.product.report_pricelist"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_partner_property_form
msgid "the parent company"
msgstr "la compañía principal"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "the product template"
msgstr "la plantilla del producto"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "to"
msgstr "a"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_history_company_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_compute_price
msgid "unknown"
msgstr "desconocido(a)"

#~ msgid ""
#~ "17\" Monitor\n"
#~ "6GB RAM\n"
#~ "Hi-Speed 234Q Processor\n"
#~ "QWERTY keyboard"
#~ msgstr ""
#~ "Monitor 17\"\n"
#~ "6GB RAM\n"
#~ "Procesador Hi-Speed 234Q\n"
#~ "Teclado QWERTY"

#~ msgid "30m RJ45 wire"
#~ msgstr "30m de cable RJ45"

#~ msgid "8-port Switch"
#~ msgstr "Switch de 8 puertos"

#~ msgid "Action Needed"
#~ msgstr "Acción Requerida"

#~ msgid "All in one hi-speed printer with fax and scanner."
#~ msgstr "impresora de alta velocidad todo en uno con fax y escáner."

#~ msgid "Blank CD"
#~ msgstr "CD Virgen"

#~ msgid "Blank DVD-RW"
#~ msgstr "DVD-RW Virgen"

#~ msgid "Date of the last message posted on the record."
#~ msgstr "Fecha del último mensaje publicado en el registro."

#~ msgid "Dell Inspiron Laptop without OS"
#~ msgstr "Dell Inspiron Laptop sin OS"

#~ msgid "Desktop Computer Table"
#~ msgstr "Computador de Escritorio"

#~ msgid "Desktop Lamp"
#~ msgstr "Lámpara de escritorio"

#~ msgid "End of Lifecycle"
#~ msgstr "Fin del ciclo de vida"

#~ msgid "Ergonomic Mouse"
#~ msgstr "Ratón Ergónomico "

#~ msgid "External Hard disk"
#~ msgstr "Disco Duro Externo"

#~ msgid "Followers"
#~ msgstr "Seguidores"

#~ msgid "Followers (Channels)"
#~ msgstr "Seguidores (Canales)"

#~ msgid "Followers (Partners)"
#~ msgstr "Seguidores (Asociados)"

#~ msgid "Full featured image editing software."
#~ msgstr "Software de edición de imágenes con todas las funciones."

#~ msgid "GrapWorks Software"
#~ msgstr "Software GrapWorks"

#~ msgid "Gross Weight"
#~ msgstr "Peso bruto"

#~ msgid "HDD SH-2 (mto)"
#~ msgstr "HDD SH-2 (mto)"

#~ msgid "HDD on Demand"
#~ msgstr "HDD bajo demanda"

#~ msgid ""
#~ "Hands free headset for laptop PC with in-line microphone and headphone "
#~ "plug."
#~ msgstr ""
#~ "Audífonos manos libre para portátiles con entrada para micrófono y "
#~ "auriculares."

#~ msgid "Headset USB"
#~ msgstr "Audífonos USB"

#~ msgid "Headset for laptop PC with USB connector."
#~ msgstr "Audífonos para laptops PC con conector USB"

#~ msgid "Headset standard"
#~ msgstr "Audífonos estándares"

#~ msgid "If checked new messages require your attention."
#~ msgstr "Si está marcado, hay nuevos mensajes que requieren su atención"

#~ msgid "If checked, new messages require your attention."
#~ msgstr "Si está marcado, los nuevos mensajes requerirán su atención."

#~ msgid "In Development"
#~ msgstr "En desarrollo"

#~ msgid "Ink Cartridge"
#~ msgstr "Cartucho de tinta"

#~ msgid "Is Follower"
#~ msgstr "Es Seguidor"

#~ msgid "Laptop S3450"
#~ msgstr "Portátil S3450"

#~ msgid "Last Message Date"
#~ msgstr "Fecha del Último Mensaje"

#~ msgid "Linutop"
#~ msgstr "Linutop"

#~ msgid "Messages"
#~ msgstr "Mensajes"

#~ msgid "Messages and communication history"
#~ msgstr "Historial de mensajes y de comunicación"

#~ msgid "Motherboard A20Z7"
#~ msgstr "Placa base A20Z7"

#~ msgid "Multimedia Speakers"
#~ msgstr "Parlantes Multimedia"

#~ msgid "Number of Actions"
#~ msgstr "Número de Acciones"

#~ msgid "Number of messages which requires an action"
#~ msgstr "Número de mensajes que requieren una acción"

#~ msgid "Number of unread messages"
#~ msgstr "Número de mensajes no leidos"

#~ msgid "Obsolete"
#~ msgstr "Obsoleto"

#~ msgid ""
#~ "Office Editing Software with word processing, spreadsheets, "
#~ "presentations, graphics, and databases..."
#~ msgstr ""
#~ "Software de Oficina con procesadores de texto, hojas de cálculo, "
#~ "presentaciones, gráficos y bases de datos ..."

#~ msgid "Office Suite"
#~ msgstr "Suite Office"

#~ msgid "On demand hard-disk having capacity based on requirement."
#~ msgstr ""
#~ "Discos duros bajo demanda con capacidad basada en los requerimientos."

#~ msgid "Pen drive, SP-2"
#~ msgstr "Pen drive, SP-2"

#~ msgid "Pen drive, SP-4"
#~ msgstr "Pen drive, SP-4"

#~ msgid "Printer, All-in-one"
#~ msgstr "Impresora, Todo-en-uno"

#~ msgid "Processor AMD 8-Core"
#~ msgstr "Procesador AMD 8-Core"

#~ msgid "RAM SR2 (kit)"
#~ msgstr "RAM SR2 (kit)"

#~ msgid "RAM SR3"
#~ msgstr "RAM SR3"

#~ msgid "Router R430"
#~ msgstr "Router R430"

#~ msgid "Status"
#~ msgstr "Estado"

#~ msgid "Toner Cartridge"
#~ msgstr "Cartucho de Tinta"

#~ msgid "TypeMatrix Dvorak Keyboard"
#~ msgstr "Teclado TypeMatrix Dvorak "

#~ msgid "USB Adapter"
#~ msgstr "Adaptador USB"

#~ msgid "Unread Messages"
#~ msgstr "Mensajes sin Leer"

#~ msgid "Unread Messages Counter"
#~ msgstr "Contador de Mensajes no Leídos"

#~ msgid "Webcam"
#~ msgstr "Webcam"

#~ msgid "Windows 7 Professional"
#~ msgstr "Windows 7 Professional"

#~ msgid "Windows Home Server 2011"
#~ msgstr "Windows Home Server 2011"
