<?xml version='1.0' encoding='UTF-8'?>
<Invoice xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2">
    <cbc:CustomizationID>urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0
    </cbc:CustomizationID>
    <cbc:ProfileID>urn:fdc:peppol.eu:2017:poacc:billing:01:1.0</cbc:ProfileID>
    <cbc:ID>INV/2017/01/0002</cbc:ID>
    <cbc:IssueDate>2017-01-01</cbc:IssueDate>
    <cbc:InvoiceTypeCode>380</cbc:InvoiceTypeCode>
    <cbc:Note>test narration</cbc:Note>
    <cbc:DocumentCurrencyCode>USD</cbc:DocumentCurrencyCode>
    <cbc:BuyerReference>Buyer</cbc:BuyerReference>
    <cac:OrderReference>
        <cbc:ID>test invoice origin</cbc:ID>
    </cac:OrderReference>
    <cac:AccountingSupplierParty>
        <cac:Party>
            <cbc:EndpointID schemeID="9925">BE0202239951</cbc:EndpointID>
            <cac:PartyName>
                <cbc:Name>partner_1</cbc:Name>
            </cac:PartyName>
            <cac:PostalAddress>
                <cbc:StreetName>Chauss&#233;e de Namur 40</cbc:StreetName>
                <cbc:CityName>Ramillies</cbc:CityName>
                <cbc:PostalZone>1367</cbc:PostalZone>
                <cac:Country>
                    <cbc:IdentificationCode>BE</cbc:IdentificationCode>
                </cac:Country>
            </cac:PostalAddress>
            <cac:PartyTaxScheme>
                <cbc:CompanyID>BE0202239951</cbc:CompanyID>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:PartyTaxScheme>
            <cac:PartyLegalEntity>
                <cbc:RegistrationName>partner_1</cbc:RegistrationName>
                <cbc:CompanyID>BE0202239951</cbc:CompanyID>
            </cac:PartyLegalEntity>
            <cac:Contact>
                <cbc:Name>partner_1</cbc:Name>
            </cac:Contact>
        </cac:Party>
    </cac:AccountingSupplierParty>
    <cac:AccountingCustomerParty>
        <cac:Party>
            <cbc:EndpointID>BE980737405</cbc:EndpointID>
            <cac:PartyName>
                <cbc:Name>Buyer</cbc:Name>
            </cac:PartyName>
            <cac:PartyTaxScheme>
                <cbc:CompanyID>BE980737405</cbc:CompanyID>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:PartyTaxScheme>
            <cac:PartyLegalEntity>
                <cbc:RegistrationName>Buyer</cbc:RegistrationName>
                <cbc:CompanyID>BE980737405</cbc:CompanyID>
            </cac:PartyLegalEntity>
            <cac:Contact>
                <cbc:Name>Buyer</cbc:Name>
                <cbc:Telephone>1111</cbc:Telephone>
                <cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
            </cac:Contact>
        </cac:Party>
    </cac:AccountingCustomerParty>
    <cac:PaymentMeans>
        <cbc:PaymentMeansCode name="credit transfer">30</cbc:PaymentMeansCode>
        <cbc:PaymentID>+++000/0000/08282+++</cbc:PaymentID>
        <cac:PayeeFinancialAccount>
            <cbc:ID>****************</cbc:ID>
        </cac:PayeeFinancialAccount>
    </cac:PaymentMeans>
    <cac:PaymentTerms>
        <cbc:Note>30% Advance End of Following Month</cbc:Note>
    </cac:PaymentTerms>
    <cac:TaxTotal>
        <cbc:TaxAmount currencyID="USD">-0.00</cbc:TaxAmount>
    </cac:TaxTotal>
    <cac:LegalMonetaryTotal>
        <cbc:LineExtensionAmount currencyID="USD">0.00</cbc:LineExtensionAmount>
        <cbc:TaxExclusiveAmount currencyID="USD">0.00</cbc:TaxExclusiveAmount>
        <cbc:TaxInclusiveAmount currencyID="USD">0.00</cbc:TaxInclusiveAmount>
        <cbc:PrepaidAmount currencyID="USD">0.00</cbc:PrepaidAmount>
        <cbc:PayableAmount currencyID="USD">0.00</cbc:PayableAmount>
    </cac:LegalMonetaryTotal>
    <cac:InvoiceLine>
        <cbc:ID>259</cbc:ID>
        <cbc:InvoicedQuantity unitCode="C62">1.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="USD">0.00</cbc:LineExtensionAmount>
        <cac:Item>
            <cbc:Description>product_a</cbc:Description>
            <cbc:Name>product_a</cbc:Name>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="USD">0.00</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>
</Invoice>
