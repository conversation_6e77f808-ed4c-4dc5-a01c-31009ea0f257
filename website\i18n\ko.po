# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON>, 2021
# <PERSON>, 2022
# <PERSON><PERSON>, 2023
# Wil Odoo, 2023
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-10 06:07+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: Sarah <PERSON>, 2023\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_image_gallery/options.js:0
#, python-format
msgid " Add Images"
msgstr "이미지 추가"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "\" alert with a"
msgstr "\" 다음으로 경고 "

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL from\" can not be empty."
msgstr "\"URL from\" 은 비워둘 수 없습니다."

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" can not be empty."
msgstr "'URL'은 비워 둘 수 없습니다."

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" cannot contain parameter %s which is not used in \"URL from\"."
msgstr "\"URL to\"는 \"URL from\"에서 사용되지 않는 %s 매개변수를 포함할 수 없습니다."

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" is invalid: %s"
msgstr "\"URL 대상\"이 유효하지 않습니다: %s"

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" must contain parameter %s used in \"URL from\"."
msgstr "\"URL to\"는 \"URL from\"에 사용된 %s 매개변수를 포함해야 합니다."

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" must start with a leading slash."
msgstr "\"URL\"은 슬래시로 시작해야 합니다."

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__page_count
msgid "# Visited Pages"
msgstr "# 방문한 페이지"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__visit_count
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "# Visits"
msgstr "# 방문수"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$10.50"
msgstr "$10.50"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$12.00"
msgstr "$12.00"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$15.50"
msgstr "$15.50"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$7.50"
msgstr "$7.50"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$9.00"
msgstr "$9.00"

#. module: website
#: code:addons/website/models/website.py:0
#: code:addons/website/models/website.py:0
#, python-format
msgid "%s (id:%s)"
msgstr "%s (id : %s)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "&amp;lt;/body&amp;gt;"
msgstr "&amp;lt;/body&amp;gt;"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "&amp;lt;head&amp;gt;"
msgstr "&amp;lt;head&amp;gt;"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "&gt;"
msgstr "&gt;"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "&nbsp;"
msgstr "&nbsp;"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
msgid "' did not match any pages."
msgstr "' 일치하는 페이지가 없습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
msgid "' did not match anything."
msgstr "' 와 일치하는 항목이 없습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
msgid ""
"' did not match anything.\n"
"                        Results are displayed for '"
msgstr ""
"' 와 일치하는 항목이 없습니다.\n"
"                        다음에 대한 결과가 표시됩니다 '"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "' to link to an anchor."
msgstr "' 앵커에 연결합니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"' to search a page.\n"
"                    '"
msgstr ""
"' 페이지를 검색합니다.\n"
"                    '"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "'%s' is not a correct date"
msgstr "'%s' 은(는) 올바른 날짜가 아닙니다"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "'%s' is not a correct datetime"
msgstr "'%s'은(는) 올바른 일시가 아닙니다"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_searchbar/000.xml:0
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
#, python-format
msgid "'. Showing results for '"
msgstr "'. 다음에 대한 결과 표시 중 '"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "(could be used in"
msgstr "(다음에서 사용할 수 있습니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.res_config_settings.xml:0
#, python-format
msgid ""
"(see e.g. Opinion\n"
"            04/2012 on Cookie Consent Exemption by the EU Art.29 WP)."
msgstr ""
"(see e.g. Opinion\n"
"            04/2012 on Cookie Consent Exemption by the EU Art.29 WP)."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "+ Field"
msgstr "+ 필드"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_header_contact_oe_structure_header_contact_1
#: model_terms:ir.ui.view,arch_db:website.template_header_hamburger_oe_structure_header_hamburger_3
#: model_terms:ir.ui.view,arch_db:website.template_header_sidebar_oe_structure_header_sidebar_1
#: model_terms:ir.ui.view,arch_db:website.template_header_vertical_oe_structure_header_vertical_2
msgid "+1 (650) 555-0111"
msgstr "+1 (650) 555-0111"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid ", .s_searchbar_input"
msgstr ", .s_searchbar_input"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid ", .s_website_form"
msgstr ", .s_website_form"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid ", author:"
msgstr ", 작성자 :"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
".\n"
"                                                The website will still work if you reject or discard those cookies."
msgstr ""
".\n"
"                                                쿠키를 거부하거나 폐기해도 웹 사이트는 계속 작동합니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid ""
".\n"
"            Changing its name will break these calls."
msgstr ""
".\n"
"            이름을 변경하면 이러한 호출이 중단됩니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "...and switch the timeline contents to fit your needs."
msgstr "...타임라인 콘텐츠를 필요에 맞게 전환합니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "/contactus"
msgstr "/contactus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "1 km"
msgstr "1 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "1/2 - 1/2"
msgstr "1/2 - 1/2"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "1/3 - 2/3"
msgstr "1/3 - 2/3"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "1/4 - 3/4"
msgstr "1/4 - 3/4"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "10 m"
msgstr "10 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "100 km"
msgstr "100 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "100 m"
msgstr "100 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "100%"
msgstr "100%"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "1000 km"
msgstr "1000 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "12"
msgstr "12"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "15 km"
msgstr "15 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "2 <span class=\"sr-only\">(current)</span>"
msgstr "2 <span class=\"sr-only\">(현재)</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "2 km"
msgstr "2 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "2.5 m"
msgstr "2.5 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "20 m"
msgstr "20 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "200 km"
msgstr "200 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "200 m"
msgstr "200 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "2000 km"
msgstr "2000 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "24x7 toll-free support"
msgstr "연중 무휴 무료 지원"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "25%"
msgstr "25%"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid ""
"250 Executive Park Blvd, Suite 3400 <br/> San Francisco CA 94134 <br/>United"
" States"
msgstr ""
"250 Executive Park Blvd, Suite 3400 <br/> San Francisco CA 94134 <br/>United"
" States"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
msgid ""
"250 Executive Park Blvd, Suite 3400 • San Francisco CA 94134 • United States"
msgstr ""
"250 Executive Park Blvd, Suite 3400 • San Francisco CA 94134 • United States"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "30 km"
msgstr "30 km"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__301
#, python-format
msgid "301 Moved permanently"
msgstr "301 영구 이동"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__302
#, python-format
msgid "302 Moved temporarily"
msgstr "302 임시 이동"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__308
msgid "308 Redirect / Rewrite"
msgstr "308 다시보내기 / 다시쓰기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "4 km"
msgstr "4 km"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "4 steps"
msgstr "4 steps"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "400 km"
msgstr "400 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "400 m"
msgstr "400 m"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__404
msgid "404 Not Found"
msgstr "404 찾지 못함"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "5 m"
msgstr "5 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "50 km"
msgstr "50 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "50 m"
msgstr "50 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "50%"
msgstr "50%"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
msgid "50,000+ companies run Odoo to grow their businesses."
msgstr "50,000개 이상의 기업이 비즈니스를 성장시키기 위해 Odoo를 운영하고 있습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "75%"
msgstr "75%"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "8 km"
msgstr "8 km"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_progress_bar/options.js:0
#, python-format
msgid "80% Development"
msgstr "80% 개발"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action
msgid "<b>50,000+ companies</b> run Odoo to grow their businesses."
msgstr "<b>50,000개 이상의 회사들</b>이 그들의 사업을 성장시키는데 Odoo를 사용합니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Add</b> the selected image."
msgstr "선택한 이미지 <b>추가하기</b>."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click Edit</b> to start designing your homepage."
msgstr "홈페이지 디자인을 시작 하려면 <b>편집을 클릭</b>하세요."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click on a snippet</b> to access its options menu."
msgstr "선택 사항 메뉴에 접근하려면 <b>스니펫을 클릭</b>하십시오."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click on a text</b> to start editing it."
msgstr "편집을 시작하려면 <b>텍스트를 클릭</b>하십시오."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click</b> on this column to access its options."
msgstr "선택 사항에 접근하려면 이 열을 <b>클릭</b>하십시오."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click</b> on this header to configure it."
msgstr "이 헤더를 <b>클릭</b>하여 구성하십시오."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click</b> on this option to change the %s of the block."
msgstr "블록의 %s를 변경하려면 이 선택 사항을 <b>클릭</b>하십시오."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid ""
"<b>Customize</b> any block through this menu. Try to change the background "
"color of this block."
msgstr "이 메뉴를 통해 블록을 <b>사용자 지정</b>합니다. 이 블록의 배경색을 변경하십시오."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid ""
"<b>Customize</b> any block through this menu. Try to change the background "
"image of this block."
msgstr "이 메뉴를 통해 블록을 <b>사용자 지정</b>합니다. 이 블록의 배경 이미지를 변경하십시오."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
msgid "<b>Designed</b> <br/>for Companies"
msgstr "기업을 위한 <b>설계</b><br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
msgid "<b>Designed</b> for companies"
msgstr "기업을 위한 <b>설계</b>"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Double click on an icon</b> to change it with one of your choice."
msgstr "원하는 <b>아이콘을 두 번 클릭</b>하여 변경합니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Double click on an image</b> to change it with one of your choice."
msgstr "<b>이미지를 두 번 클릭</b>하여 원하는 대로 변경합니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
msgid ""
"<b>My Company</b><br/>250 Executive Park Blvd, Suite 3400 <br/> San "
"Francisco CA 94134 <br/>United States"
msgstr ""
"<b>My Company</b><br/>250 Executive Park Blvd, Suite 3400 <br/> San "
"Francisco CA 94134 <br/>United States"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Select</b> a %s."
msgstr "%s를 <b>선택</b>하십시오."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Select</b> a Color Palette."
msgstr "색상 팔레트를 <b>선택</b>하세요."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Slide</b> this button to change the %s padding"
msgstr "%s 패딩을 변경하려면 이 버튼을 <b>밀어보세요</b>."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Slide</b> this button to change the column size."
msgstr "열 크기를 변경하려면 이 버튼을 <b>밀어보세요</b>."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_edit_robots
msgid ""
"<br/><br/>\n"
"                    Example of rule:<br/>"
msgstr ""
"<br/><br/>\n"
"                    규칙 예시:<br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"<font style=\"background-color: rgb(255, 255, 255);\">Good writing is "
"simple, but not simplistic.</font>"
msgstr ""
"<font style=\"background-color: rgb(255, 255, 255);\">좋은 글쓰기는 간단하지만 단순하지는 "
"않습니다.</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<font style=\"font-size: 14px;\">Created in 2021, the company is young and "
"dynamic. Discover the composition of the team and their skills.</font>"
msgstr ""
"<font style=\"font-size: 14px;\">2021년에 설립된 젊고 역동적인 회사입니다. 회사 내부 구성과 보유 기술에 "
"대해 알아보십시오.</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"<font style=\"font-size: 62px; background-color: rgb(255, 255, 255);\">Edit "
"this title</font>"
msgstr ""
"<font style=\"font-size: 62px; background-color: rgb(255, 255, 255);\">제목 "
"편집</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cover
msgid "<font style=\"font-size: 62px; font-weight: bold;\">Catchy Headline</font>"
msgstr "<font style=\"font-size: 62px; font-weight: bold;\">눈길을 끄는 헤드라인</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_picture
msgid "<font style=\"font-size: 62px;\">A punchy Headline</font>"
msgstr "<font style=\"font-size: 62px;\">아주 효과적인 헤드라인</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_banner
msgid "<font style=\"font-size: 62px;\">Sell Online. Easily.</font>"
msgstr "<font style=\"font-size: 62px;\">온라인 판매. 간편하게.</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "<font style=\"font-size: 62px;\">Slide Title</font>"
msgstr "<font style=\"font-size: 62px;\">슬라이드 제목</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup
msgid "<font style=\"font-size: 62px;\">Win $20</font>"
msgstr "<font style=\"font-size: 62px;\">승리 $20</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_title
msgid "<font style=\"font-size: 62px;\">Your Site Title</font>"
msgstr "<font style=\"font-size: 62px;\">사이트 제목</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card
msgid "<i class=\"fa fa-1x fa-clock-o mr8\"/><small>2 days ago</small>"
msgstr "<i class=\"fa fa-1x fa-clock-o mr8\"/><small>2일전</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_page
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid ""
"<i class=\"fa fa-1x fa-fw fa-envelope "
"mr-2\"/><span><EMAIL></span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
msgid ""
"<i class=\"fa fa-1x fa-fw fa-map-marker mr-2\"/>250 Executive Park Blvd, "
"Suite 3400 • San Francisco CA 94134 • United States"
msgstr ""
"<i class=\"fa fa-1x fa-fw fa-map-marker mr-2\"/>250 Executive Park Blvd, "
"Suite 3400 • San Francisco CA 94134 • United States"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            Create a Google Project and Get a Key"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            Google 프로젝트를 만들고 키를 얻으십시오"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            Enable billing on your Google Project"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            Google 프로젝트에서 청구 활성화"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            How to get my Client ID"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            클라이언트 ID를 얻는 방법"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            How to get my Measurement ID"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.report_viewhierarchy_children
msgid "<i class=\"fa fa-eye ml-2 text-muted\" title=\"Go to View\"/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.report_viewhierarchy_children
msgid "<i class=\"fa fa-files-o ml-2 text-muted\" title=\"Show Arch Diff\"/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-circle\"/> Circles"
msgstr "<i class=\"fa fa-fw fa-circle\"/> 동그라미"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-heart\"/> Hearts"
msgstr "<i class=\"fa fa-fw fa-heart\"/> 하트"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-refresh mr-1\"/> Replace Icon"
msgstr "<i class=\"fa fa-fw fa-refresh mr-1\"/> 아이콘 바꾸기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-square\"/> Squares"
msgstr "<i class=\"fa fa-fw fa-square\"/> 사각형"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-star\"/> Stars"
msgstr "<i class=\"fa fa-fw fa-star\"/> 별"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-thumbs-up\"/> Thumbs"
msgstr "<i class=\"fa fa-fw fa-thumbs-up\"/> 썸네일"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-circle text-danger\"/>\n"
"                            <span>Offline</span>"
msgstr ""
"<i class=\"fa fa-fw o_button_icon fa-circle text-danger\"/>\n"
"                            <span>오프라인</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-circle text-success\"/>\n"
"                            <span>Connected</span>"
msgstr ""
"<i class=\"fa fa-fw o_button_icon fa-circle text-success\"/>\n"
"                            <span>연결됨</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid ""
"<i class=\"fa fa-info-circle\"/> Edit the content below this line to adapt "
"the default <strong>Page not found</strong> page."
msgstr ""
"<i class=\"fa fa-info-circle\"/> 이 줄 아래 내용을 편집하면 기본값이 적용됩니다. <strong>페이지를 찾을"
" 수 없습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.protected_403
msgid ""
"<i class=\"fa fa-lock fa-2x\"/><br/>\n"
"                            <span class=\"mt-1\">A password is required to access this page.</span>"
msgstr ""
"<i class=\"fa fa-lock fa-2x\"/><br/>\n"
"                            <span class=\"mt-1\">이 페이지에 접근하려면 비밀번호가 필요합니다.</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.protected_403
msgid ""
"<i class=\"fa fa-lock fa-2x\"/><br/>\n"
"                            <span class=\"mt-1\">Wrong password</span>"
msgstr ""
"<i class=\"fa fa-lock fa-2x\"/><br/>\n"
"                            <span class=\"mt-1\">잘못된 비밀번호</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_page
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid ""
"<i class=\"fa fa-map-marker fa-fw mr-2\"/><span "
"class=\"o_force_ltr\">Chaussée de Namur 40</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_page
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid ""
"<i class=\"fa fa-phone fa-fw mr-2\"/><span class=\"o_force_ltr\">+ 32 81 81 "
"37 00</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.language_selector_add_language
msgid ""
"<i class=\"fa fa-plus-circle\"/>\n"
"        <span>Add a language...</span>"
msgstr ""
"<i class=\"fa fa-plus-circle\"/>\n"
"        <span>언어 추가</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "<i class=\"fa fa-th-large mr-2\"/> WEBSITE"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-calendar fa-fw mr-2\"/>\n"
"                            <b>Events</b>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-eye fa-fw mr-2\"/>\n"
"                            <b>About us</b>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-group fa-fw mr-2\"/>\n"
"                            <b>Partners</b>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-handshake-o fa-fw mr-2\"/>\n"
"                            <b>Services</b>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-headphones fa-fw mr-2\"/>\n"
"                            <b>Help center</b>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-map-o fa-fw mr-2\"/>\n"
"                            <b>Guides</b>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-newspaper-o fa-fw mr-2\"/>\n"
"                            <b>Our blog</b>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-star-o fa-fw mr-2\"/>\n"
"                            <b>Customers</b>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-tags fa-fw mr-2\"/>\n"
"                            <b>Products</b>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "<i class=\"s_mega_menu_thumbnails_icon fa fa-comments mr-2\"/> Contact us"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "<i class=\"s_mega_menu_thumbnails_icon fa fa-cube mr-2\"/> Free returns"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid ""
"<i class=\"s_mega_menu_thumbnails_icon fa fa-shopping-basket mr-2\"/> Pickup"
" in store"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid ""
"<i class=\"s_mega_menu_thumbnails_icon fa fa-truck mr-2\"/> Express delivery"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "<i title=\"Is the page SEO optimized?\" class=\"fa fa-search\"/>"
msgstr "<i title=\"Is the page SEO optimized?\" class=\"fa fa-search\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid ""
"<i title=\"Is the page included in the main menu?\" class=\"fa fa-thumb-"
"tack\"/>"
msgstr ""
"<i title=\"Is the page included in the main menu?\" class=\"fa fa-thumb-"
"tack\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "<i title=\"Is the page indexed by search engines?\" class=\"fa fa-globe\"/>"
msgstr "<i title=\"Is the page indexed by search engines?\" class=\"fa fa-globe\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "<i title=\"Is the page published?\" class=\"fa fa-eye\"/>"
msgstr "<i title=\"Is the page published?\" class=\"fa fa-eye\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<i>Instant setup, satisfied or reimbursed.</i>"
msgstr "<i> 즉시 설정, 만족 또는 환불됨. </i>"

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "<p>Attached files : </p>"
msgstr "<p>첨부 파일 : </p>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_header_magazine_oe_structure_header_magazine_1
msgid "<small class=\"s_share_title d-none\"><b>Follow us</b></small>"
msgstr "<small class=\"s_share_title d-none\"><b>함께하기</b></small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_header_contact_oe_structure_header_contact_1
msgid "<small class=\"s_share_title text-muted d-none\"><b>Follow us</b></small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<small class=\"text-muted\">\n"
"                                            <i class=\"fa fa-info\"/>: type some of the first chars after 'google' is enough, we'll guess the rest.\n"
"                                        </small>"
msgstr ""
"<small class=\"text-muted\">\n"
"                                            <i class=\"fa fa-info\"/>: 'google' 뒤에 첫 번째 문자 중 일부를 입력하면 나머지는 추측할 수 있습니다.\n"
"                                        </small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid ""
"<small id=\"emailHelp\" class=\"form-text text-muted\">Form field help "
"text</small>"
msgstr "<small id=\"emailHelp\" class=\"form-text text-muted\">양식 필드 도움말 텍스트</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid ""
"<small id=\"emailHelp\" class=\"form-text text-muted\">We'll never share "
"your email with anyone else.</small>"
msgstr ""
"<small id=\"emailHelp\" class=\"form-text text-muted\">귀하의 이메일은 다른 사람과 공유하지 "
"않습니다.</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<small>/ month</small>"
msgstr "<small>/ 월</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "<small>TABS</small>"
msgstr "<small>탭</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_header_magazine_oe_structure_header_magazine_1
msgid "<small>We help you grow your business</small>"
msgstr "<small>비즈니스 성장을 돕습니다</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "<span class=\"bg-white\"><b>2015</b></span>"
msgstr "<span class=\"bg-white\"><b>2015</b></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "<span class=\"bg-white\"><b>2018</b></span>"
msgstr "<span class=\"bg-white\"><b>2018</b></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "<span class=\"bg-white\"><b>2019</b></span>"
msgstr "<span class=\"bg-white\"><b>2019</b></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                <span class=\"sr-only\">Next</span>"
msgstr ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                <span class=\"sr-only\">다음</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                <span class=\"sr-only\">Previous</span>"
msgstr ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                <span class=\"sr-only\">이전</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid ""
"<span class=\"d-block p-2\">\n"
"                            <b><font style=\"font-size:14px;\">Discover our new products</font></b>\n"
"                        </span>"
msgstr ""
"<span class=\"d-block p-2\">\n"
"                            <b><font style=\"font-size:14px;\">신제품을 확인해보세요</font></b>\n"
"                        </span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid ""
"<span class=\"fa fa-check-circle\"/>\n"
"                                            <span>Your message has been sent <b>successfully</b></span>"
msgstr ""
"<span class=\"fa fa-check-circle\"/>\n"
"                                            <span>메시지가 <b>성공적으로</b> 전송되었습니다</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
msgid ""
"<span class=\"fa fa-chevron-left fa-2x text-white\"/>\n"
"                    <span class=\"sr-only\">Previous</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
msgid ""
"<span class=\"fa fa-chevron-right fa-2x text-white\"/>\n"
"                    <span class=\"sr-only\">Next</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "<span class=\"fa fa-pencil mr-2\"/>Edit"
msgstr "<span class=\"fa fa-pencil mr-2\"/>편집하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "<span class=\"fa fa-plus mr-2\"/>New"
msgstr "<span class=\"fa fa-plus mr-2\"/>신규"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "<span class=\"fa fa-sort fa-lg\" role=\"img\" aria-label=\"Sort\" title=\"Sort\"/>"
msgstr "<span class=\"fa fa-sort fa-lg\" role=\"img\" aria-label=\"Sort\" title=\"Sort\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.option_header_off_canvas
msgid "<span class=\"fa-2x\">×</span>"
msgstr "<span class=\"fa-2x\">×</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.language_selector_inline
msgid "<span class=\"list-inline-item\">|</span>"
msgstr "<span class=\"list-inline-item\">|</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<span class=\"mx-2\">/</span>"
msgstr "<span class=\"mx-2\">/</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.language_selector_inline
msgid ""
"<span class=\"o_add_language list-inline-item\" "
"groups=\"website.group_website_publisher\">|</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_copyright_company_name
msgid ""
"<span class=\"o_footer_copyright_name mr-2\">Copyright &amp;copy; Company "
"name</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Robots.txt</span>\n"
"                                    <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"o_form_label\">Robots.txt</span>\n"
"                                    <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Sitemap</span>\n"
"                                    <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"o_form_label\">사이트지도</span>\n"
"                                    <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_badge
msgid ""
"<span class=\"s_badge badge badge-secondary o_animable\" data-name=\"Badge\">\n"
"        <i class=\"fa fa-1x fa-fw fa-folder o_not-animable\"/>Category\n"
"    </span>"
msgstr ""
"<span class=\"s_badge badge badge-secondary o_animable\" data-name=\"Badge\">\n"
"        <i class=\"fa fa-1x fa-fw fa-folder o_not-animable\"/>범주\n"
"    </span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"s_blockquote_author\"><b>Iris DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>Iris DOE</b> • CEO of "
"MyCompany</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"s_blockquote_author\"><b>Jane DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>Jane DOE</b> • CEO of "
"MyCompany</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"s_blockquote_author\"><b>John DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>John DOE</b> • CEO of "
"MyCompany</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>125</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"
msgstr ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>125</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>35</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"
msgstr ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>35</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>65</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"
msgstr ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>65</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "<span class=\"s_number display-4\">12</span><br/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "<span class=\"s_number display-4\">37</span><br/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "<span class=\"s_number display-4\">45</span><br/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "<span class=\"s_number display-4\">8</span><br/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar
msgid "<span class=\"s_progress_bar_text\">80% Development</span>"
msgstr "<span class=\"s_progress_bar_text\">80% 개발</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid "<span class=\"s_website_form_label_content\">Email To</span>"
msgstr "<span class=\"s_website_form_label_content\">이메일 보내실 주소</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid "<span class=\"s_website_form_label_content\">Phone Number</span>"
msgstr "<span class=\"s_website_form_label_content\">전화번호</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid ""
"<span class=\"s_website_form_label_content\">Subject</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">제목</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid "<span class=\"s_website_form_label_content\">Your Company</span>"
msgstr "<span class=\"s_website_form_label_content\">회사</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid "<span class=\"s_website_form_label_content\">Your Question</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "<span class=\"sr-only\">Toggle Dropdown</span>"
msgstr "<span class=\"sr-only\">드롭다운 토글</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid ""
"<span title=\"Mobile preview\" role=\"img\" aria-label=\"Mobile preview\" "
"class=\"fa fa-mobile\"/>"
msgstr ""
"<span title=\"Mobile preview\" role=\"img\" aria-label=\"Mobile preview\" "
"class=\"fa fa-mobile\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_publisher
msgid ""
"<span/>\n"
"                    <span class=\"css_publish\">Unpublished</span>\n"
"                    <span class=\"css_unpublish\">Published</span>"
msgstr ""
"<span/>\n"
"                    <span class=\"css_publish\">게시 안함</span>\n"
"                    <span class=\"css_unpublish\">게시함</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "<span>Theme</span>"
msgstr "<span>테마</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"A CDN helps you serve your website’s content with high availability and high"
" performance to any visitor wherever they are located."
msgstr "CDN을 사용하면 웹 사이트의 내용을 어디에서나 방문자에게 고가용성 및 고성능으로 제공할 수 있습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart
msgid "A Chart Title"
msgstr "일람표 제목"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"A Google Map error occurred. Make sure to read the key configuration popup "
"carefully."
msgstr "Google 맵에서 오류가 발생했습니다. 키 설정 관련 팝업 내용을 자세히 확인하십시오."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "A Section Subtitle"
msgstr "영역 소제목"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card
msgid ""
"A card is a flexible and extensible content container. It includes options "
"for headers and footers, a wide variety of content, contextual background "
"colors, and powerful display options."
msgstr ""
"카드는 유연하고 확장 가능한 컨텐츠 컨테이너입니다. 머리글 및 바닥글 선택 사항, 다양한 내용, 상황에 맞는 배경색 및 강력한 표시 선택"
" 사항이 포함되어 있습니다."

#. module: website
#: model:ir.model.fields,help:website.field_ir_actions_server__website_published
#: model:ir.model.fields,help:website.field_ir_cron__website_published
msgid ""
"A code server action can be executed from the website, using a dedicated "
"controller. The address is <base>/website/action/<website_path>. Set this "
"field as True to allow users to run this action. If it is set to False the "
"action cannot be run through the website."
msgstr ""
"전용 제어기를 사용하여 코드 서버 조치를 웹 사이트에서 실행할 수 있습니다. 주소는 <base> / website / action / "
"<website_path>입니다. 이 필드를 True로 설정하면 사용자가 이 작업을 실행할 수 있습니다. False로 설정하면 웹 "
"사이트를 통해 작업을 실행할 수 없습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_color_blocks_2
msgid "A color block"
msgstr "색상 블럭"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_image_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_default_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_image_texts_image_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_mosaic_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_reversed_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_texts_image_texts_template
msgid "A great title"
msgstr "훌륭한 제목"

#. module: website
#: model:ir.model.fields,help:website.field_website_snippet_filter__field_names
msgid "A list of comma-separated field names"
msgstr "쉼표로 구분된 필드 이름 목록"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_stores_locator
msgid "A map and a listing of your stores"
msgstr "지도 및 매장 목록"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__visit_count
msgid ""
"A new visit is considered if last connection was more than 8 hours ago."
msgstr "마지막 연결이 8시간 이상 지난 경우 새로운 방문이 고려됩니다."

#. module: website
#: model:ir.model.constraint,message:website.constraint_website_visitor_partner_uniq
msgid "A partner is linked to only one visitor."
msgstr "협력사는 한 명의 방문자에게만 연결됩니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "A short description of this great feature."
msgstr "이 훌륭한 기능에 대한 간단한 설명입니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "A small explanation of this great <br/>feature, in clear words."
msgstr "이 훌륭한 <br/>기능에 대한 간단한 설명."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid ""
"A timeline is a graphical representation on which important events are "
"marked."
msgstr "타임라인은 중요한 이벤트가 표시되는 그래픽 표현입니다."

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__is_connected
msgid ""
"A visitor is considered as connected if his last page view was within the "
"last 5 minutes."
msgstr "마지막 페이지 조회가 지난 5분 이내인 경우 방문자는 연결된 것으로 간주됩니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#, python-format
msgid "API Key"
msgstr "API 키"

#. module: website
#: model:website.configurator.feature,name:website.feature_page_about_us
msgid "About Us"
msgstr "회사 소개"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.aboutus
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "About us"
msgstr "회사 소개"

#. module: website
#: code:addons/website/controllers/backend.py:0
#, python-format
msgid "Access Error"
msgstr "접근 에러"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__access_token
msgid "Access Token"
msgstr "사용 권한 토큰"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.protected_403
msgid "Access to this page"
msgstr "이 페이지에 대한 접근 권한"

#. module: website
#: model:ir.model.constraint,message:website.constraint_website_visitor_access_token_unique
msgid "Access token should be unique."
msgstr "액세스 토큰은 고유해야 합니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Accessories"
msgstr "기타"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Account &amp; Sales management"
msgstr "계정 및 영업 관리"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__redirect_type
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Action"
msgstr "추가 작업"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/res_config_settings.js:0
#, python-format
msgid "Activate anyway"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__active
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__active
#: model:ir.model.fields,field_description:website.field_website_page__active
#: model:ir.model.fields,field_description:website.field_website_rewrite__active
#: model:ir.model.fields,field_description:website.field_website_visitor__active
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Active"
msgstr "활성"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"Adapt these three columns to fit your design need. To duplicate, delete or "
"move columns, select the column and use the top icons to perform your "
"action."
msgstr ""
"이 세 개의 열을 설계 요구에 맞게 조정합니다. 열을 복제, 삭제 또는 이동하려면 열을 선택하고 상단 아이콘을 사용하여 작업을 "
"수행합니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/xml/website.seo.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#, python-format
msgid "Add"
msgstr "추가"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Add Features"
msgstr "기능 추가"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Add Item"
msgstr "아이템 추가"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "Add Media"
msgstr "미디어 추가"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Add Mega Menu Item"
msgstr "메가 메뉴 항목 추가하기"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Add Menu Item"
msgstr "메뉴 항목 추가하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog_add_product_widget
msgid "Add Product"
msgstr "상품 추가"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Add Row"
msgstr "행 추가"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Add Serie"
msgstr "시리즈 추가"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Add Slide"
msgstr "슬라이드 추가"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Add Tab"
msgstr "탭 추가하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline_options
msgid "Add Year"
msgstr "연도 추가"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Add a Google Font"
msgstr "구글 폰트 추가하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_picture
msgid "Add a caption to enhance the meaning of this image."
msgstr "이 이미지의 의미를 향상시키기 위해 캡션을 추가합니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_product_catalog/options.js:0
#, python-format
msgid "Add a description here"
msgstr "여기에 설명 추가하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Add a great slogan."
msgstr "멋진 슬로건 추가하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Add a menu description."
msgstr "메뉴 설명 추가하기"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Add a menu item"
msgstr "메뉴 항목 추가하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Add a new field after this one"
msgstr "이 필드 뒤에 새 필드 추가"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Add a new field at the end"
msgstr "마지막에 새 필드 추가"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Add features"
msgstr "기능 추가하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Add links to social media on your website"
msgstr "웹사이트에 소셜 미디어 링크 추가하기"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Add new %s"
msgstr "새 %s 추가"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Add to cart"
msgstr "장바구니에 담기"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Add to menu"
msgstr "메뉴에 추가하기"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"Adding a font requires a reload of the page. This will save all your "
"changes."
msgstr "글꼴을 추가하려면 페이지를 다시 로드해야 합니다. 이렇게하면 모든 변경 사항이 저장됩니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Address"
msgstr "주소"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Advertising &amp; Marketing"
msgstr "광고 및 마케팅"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__after
msgid "After"
msgstr "이후"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Alert"
msgstr "경고"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Align Bottom"
msgstr "아래로 정렬"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Align Middle"
msgstr "중간에 정렬"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Align Top"
msgstr "위로 정렬"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Alignment"
msgstr "정렬"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Aline Turner, CTO"
msgstr "Aline Turner, CTO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Aline is one of the iconic people in life who can say they love what they "
"do. She mentors 100+ in-house developers and looks after the community of "
"thousands of developers."
msgstr ""
"앨린은 자신이 하는 일을 사랑한다고 말할 수 있는 인생의 상징적인 사람들 중 하나입니다. 그녀는 100명 이상의 사내 개발자들을 지도하고"
" 수천 명의 개발자들의 공동체를 돌봅니다."

#. module: website
#: model:ir.model.fields.selection,name:website.selection__ir_ui_view__visibility__
msgid "All"
msgstr "전체"

#. module: website
#: model:ir.model,name:website.model_website_route
msgid "All Website Route"
msgstr "모든 웹 사이트 경로"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_qweb
msgid "All Websites"
msgstr "모든 웹사이트"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "All informations you need"
msgstr "필요한 모든 정보"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "All pages"
msgstr "전체 페이지"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_searchbar/000.xml:0
#, python-format
msgid "All results"
msgstr "전체 결과"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "All these icons are completely free for commercial use."
msgstr "이 모든 아이콘은 상업적 사용으로도 완전 무료입니다."

#. module: website
#: model:ir.model.fields,help:website.field_ir_ui_view__track
#: model:ir.model.fields,help:website.field_website_page__track
msgid "Allow to specify for one page of the website to be trackable or not"
msgstr "웹사이트 페이지의 추적여부를 정의하는 것을 승인합니다"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model__website_form_access
msgid "Allowed to use in forms"
msgstr "양식에서 사용 가능"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Already installed"
msgstr "이미 설치됨"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Alternate Image Text"
msgstr "이미지 텍스트 대체"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Alternate Text"
msgstr "텍스트 대체"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Alternate Text Image"
msgstr "텍스트 이미지 대체"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Alternate Text Image Text"
msgstr "텍스트 이미지 텍스트 대체"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"Although this Website may be linked to other websites, we are not, directly "
"or indirectly, implying any approval, association, sponsorship, endorsement,"
" or affiliation with any linked website, unless specifically stated herein."
msgstr ""
"이 웹사이트는 다른 웹사이트에 링크될 수 있지만, 여기에 구체적으로 명시되지 않는 한, 링크된 웹사이트와의 승인, 협회, 후원, 보증 "
"또는 제휴를 암시하는 것은 아닙니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Always Underlined"
msgstr "항상 밑줄"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Always Visible"
msgstr "항상 표시"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Always visible"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "Amazing pages"
msgstr "놀라운 페이지"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map
msgid "An address must be specified for a map to be embedded"
msgstr "지도를 삽입하려면 주소를 지정해야 합니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "An error has occured, the form has not been sent."
msgstr "오류가 발생하여 양식이 전송되지 않았습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "An error occurred while rendering the template"
msgstr "서식 파일을 만드는 중 오류가 발생했습니다."

#. module: website
#: model:ir.actions.client,name:website.backend_dashboard
#: model:ir.ui.menu,name:website.menu_website_google_analytics
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Analytics"
msgstr "분석"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Analytics cookies and privacy information."
msgstr "쿠키 및 개인 정보 분석."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Anchor copied to clipboard<br>Link: %s"
msgstr "앵커가 클립보드에 복사되었습니다. <br>링크 : %s"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Anchor name"
msgstr "앵커 이름"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_image_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_default_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_image_texts_image_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_mosaic_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_reversed_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_texts_image_texts_template
msgid "And a great subtitle"
msgstr "그리고 멋진 소제목"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Animate"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Animate text"
msgstr "텍스트 애니메이션"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Animated"
msgstr "생기있음"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Animation Delay"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Animation Duration"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Animation Launch"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_color_blocks_2
msgid "Another color block"
msgstr "다른 색상 블럭"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "Another feature"
msgstr "다른 기능"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__append
msgid "Append"
msgstr "추가"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_add_features
#: model:ir.ui.menu,name:website.menu_website_add_features
msgid "Apps"
msgstr "앱"

#. module: website
#: code:addons/website/controllers/main.py:0
#, python-format
msgid "Apps url"
msgstr "앱 URL"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__arch
msgid "Arch"
msgstr "보존"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_db
msgid "Arch Blob"
msgstr "Arch Blob"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_fs
msgid "Arch Filename"
msgstr "보관용 파일명"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__arch_fs
msgid "Arch Fs"
msgstr "보존 Fs"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Archived"
msgstr "보관됨"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__specific_user_account
msgid "Are newly created user accounts website specific"
msgstr "새로 생성된 사용자 계정별 고유 웹사이트"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "Are you sure you want to delete this page ?"
msgstr "이 페이지를 삭제 하시겠습니까?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Arrows"
msgstr "화살표"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
#, python-format
msgid "As promised, we will offer 4 free tickets to our next summit."
msgstr "약속대로 다음 회담에는 4장의 무료 입장권을 제공할 예정입니다."

#. module: website
#: model:ir.model,name:website.model_ir_asset
msgid "Asset"
msgstr "자산"

#. module: website
#: model:ir.model,name:website.model_web_editor_assets
msgid "Assets Utils"
msgstr "에셋 유틸"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__copy_ids
msgid "Assets using a copy of me"
msgstr "내 사본을 이용한 자산"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "At The End"
msgstr "마지막에"

#. module: website
#: model:ir.model,name:website.model_ir_attachment
msgid "Attachment"
msgstr "첨부 파일"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__copy_ids
msgid "Attachment using a copy of me"
msgstr "내 사본을 사용한 첨부파일"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Authenticate users, protect user data and allow the website to deliver the services users expects,\n"
"                                                such as maintaining the content of their cart, or allowing file uploads."
msgstr ""
"사용자를 인증하고, 사용자 데이터를 보호하며, 웹 사이트에서 장바구니 내용 유지 또는 파일 업로드 \n"
"                                                허용과 같은 사용자가 기대하는 서비스를 제공할 수 있도록 허용합니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_search
msgid "Author"
msgstr "작성자"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Auto"
msgstr "자동"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid ""
"Automatically opens the pop-up if the user stays on a page longer than the "
"specified time."
msgstr "사용자가 지정된 시간보다 페이지에 오래 머무르는 경우 자동으로 팝업을 엽니다."

#. module: website
#: model:ir.model.fields,field_description:website.field_website__auto_redirect_lang
msgid "Autoredirect Language"
msgstr "자동 리디렉션 언어"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Autosizing"
msgstr "자동 크기 변경"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_actions_server__website_published
#: model:ir.model.fields,field_description:website.field_ir_cron__website_published
msgid "Available on the Website"
msgstr "웹 사이트에서 사용 가능"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "BSgzTvR5L1GB9jriT451iTN4huVPxHmltG6T6eo"
msgstr "BSgzTvR5L1GB9jriT451iTN4huVPxHmltG6T6eo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "BTS Base Colors"
msgstr "BTS 기본 색상"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Backdrop"
msgstr "배경"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Background"
msgstr "배경"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/configurator_tour.js:0
#: code:addons/website/static/src/js/tours/configurator_tour.js:0
#, python-format
msgid "Background Shape"
msgstr "배경 모양"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Badge"
msgstr "뱃지"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Bags"
msgstr "가방"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Bar Horizontal"
msgstr "수평바"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Bar Vertical"
msgstr "수직바"

#. module: website
#: model:ir.model,name:website.model_base
msgid "Base"
msgstr "Base"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_base
msgid "Base View Architecture"
msgstr "기본 뷰 아키텍쳐"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_ui_view__mode__primary
msgid "Base view"
msgstr "기본 보기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Basic sales &amp; marketing for up to 2 users"
msgstr "최대 2명의 사용자를 위한 기본 영업 및 마케팅"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "Beautiful snippets"
msgstr "아름다운 스니펫"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Beef Carpaccio"
msgstr "카르파치오 소고기"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__before
msgid "Before"
msgstr "이전"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Beginner"
msgstr "초보자"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Below Each Other"
msgstr "서로 아래"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
msgid "Big"
msgstr "크게"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Big Icons Subtitles"
msgstr "큰 아이콘 소제목"

#. module: website
#: model:ir.model.fields,help:website.field_ir_model_fields__website_form_blacklisted
msgid "Blacklist this field for web forms"
msgstr "웹 양식에 대해 이 필드를 블랙리스트에 올림"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model_fields__website_form_blacklisted
msgid "Blacklisted in web forms"
msgstr "웹 양식에 블랙리스트로 올림"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Blazers"
msgstr "블레이저"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Block"
msgstr "블록"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Blockquote"
msgstr "인용 블록"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Blog"
msgstr "블로그"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Blog Post"
msgstr "블로그 게시물"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_news
msgid "Blogging and posting relevant content"
msgstr "관련 콘텐츠 블로그 및 게시"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Books"
msgstr "책"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Bootstrap-based templates"
msgstr "부트스트랩 기반 서식"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_widgets
#, python-format
msgid "Border"
msgstr "테두리"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Border Bottom"
msgstr "아래 테두리"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
msgid "Border Color"
msgstr "테두리 색상"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Border Radius"
msgstr "테두리 곡률"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Border Width"
msgstr "테두리 두께"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bordered"
msgstr "테두리화"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Bottom"
msgstr "맨 아래"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Bottom to Top"
msgstr "아래에서 위로"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bounce In"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bounce In-Down"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bounce In-Left"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bounce In-Right"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Boxed"
msgstr "상자화"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Boxes"
msgstr "박스"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Breadcrumb"
msgstr "사이트 이동 경로"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Build my website"
msgstr "내 웹사이트 만들기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Building blocks system"
msgstr "빌딩 블록 시스템"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Building your %s"
msgstr "%s 만들기"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Building your website..."
msgstr "웹사이트 만들기..."

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__bundle
msgid "Bundle"
msgstr "번들"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Button"
msgstr "버튼"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Button Position"
msgstr "버튼 위치"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Buttons"
msgstr "버튼"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#, python-format
msgid "By clicking on this banner, you give us permission to collect data."
msgstr "이 배너를 클릭하면 데이터 수집 권한을 부여하는 것입니다."

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__cdn_url
#: model:ir.model.fields,field_description:website.field_website__cdn_url
msgid "CDN Base URL"
msgstr "CDN 기본 URL"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__cdn_filters
#: model:ir.model.fields,field_description:website.field_website__cdn_filters
msgid "CDN Filters"
msgstr "CDN 필터"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "CTA"
msgstr "CTA"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__cache_key_expr
msgid "Cache Key Expr"
msgstr "캐시 키 익스프레스"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__cache_time
msgid "Cache Time"
msgstr "캐시 시간"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Call to Action"
msgstr "실행하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "Call us"
msgstr "전화하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Call-to-action"
msgstr "실행하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Camera"
msgstr "카메라"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_partner__can_publish
#: model:ir.model.fields,field_description:website.field_res_users__can_publish
#: model:ir.model.fields,field_description:website.field_website_page__can_publish
#: model:ir.model.fields,field_description:website.field_website_published_mixin__can_publish
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__can_publish
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__can_publish
msgid "Can Publish"
msgstr "게시 가능"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/dashboard.js:0
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: code:addons/website/static/src/js/menu/new_content.js:0
#: code:addons/website/static/src/js/utils.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/xml/website.xml:0
#: model_terms:ir.ui.view,arch_db:website.qweb_500
#: model_terms:ir.ui.view,arch_db:website.view_edit_robots
#: model_terms:ir.ui.view,arch_db:website.view_website_form_view_themes_modal
#, python-format
msgid "Cancel"
msgstr "취소"

#. module: website
#: code:addons/website/models/res_lang.py:0
#, python-format
msgid "Cannot deactivate a language that is currently used on a website."
msgstr "현재 웹 사이트에서 사용중인 언어는 비활성화할 수 없습니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/content/website_root.js:0
#, python-format
msgid "Cannot load google map."
msgstr "구글 지도를 불러올 수 없습니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Card"
msgstr "카드"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Card Body"
msgstr "카드 본문"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Card Footer"
msgstr "카드 꼬리말"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Card Header"
msgstr "카드 머리말"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
msgid "Card Style"
msgstr "카드 스타일"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Cards"
msgstr "카드"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_career
msgid "Career"
msgstr "채용 정보"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Case Studies"
msgstr "사례 연구"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_search
msgid "Category"
msgstr "카테고리"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Category of Cookie"
msgstr "쿠키 범주"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Center"
msgstr "중앙"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Centered"
msgstr "중앙 정렬"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Centered Logo"
msgstr "로고 중앙 정렬"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Change Icons"
msgstr "아이콘 변경"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Change theme in a few clicks, and browse through Odoo's catalog of\n"
"                            ready-to-use themes available in our app store."
msgstr ""
"몇 번의 클릭으로 테마를 변경하고 앱 스토어에서 바로 사용할 수 있는 \n"
"                            테마의 범주를 찾아보십시오."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"Changing the color palette will reset all your color customizations, are you"
" sure you want to proceed?"
msgstr "색상표를 변경하면 모든 색상 사용자 지정이 재설정됩니다. 계속하시겠습니까?"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"Changing theme requires to leave the editor. This will save all your "
"changes, are you sure you want to proceed? Be careful that changing the "
"theme will reset all your color customizations."
msgstr ""
"테마를 변경하려면 편집기를 종료해야 합니다. 그러면 변경 사항이 모두 저장됩니다. 계속하시겠습니까? 테마를 변경하면 모든 색상 사용자 "
"지정이 재설정됩니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#, python-format
msgid "Chart"
msgstr "차트"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_live_chat
msgid "Chat with visitors to improve traction"
msgstr "방문자와의 채팅으로 이용자 편의성 향상"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup
msgid "Check out now and get $20 off your first order."
msgstr "지금 체크아웃하시고 첫 주문에서 20달러를 할인받으세요."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/content/website_root.js:0
#, python-format
msgid "Check your configuration."
msgstr "설정을 확인하세요."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Check your connection and try again"
msgstr "인터넷 연결 상태를 확인하고 다시 시도해주십시오."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Checkbox"
msgstr "체크박스"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Cheese Onion Rings"
msgstr "치즈 양파 링"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Chefs Fresh Soup of the Day"
msgstr "오늘의 요리사 추천 신선한 수프"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__field_parent
msgid "Child Field"
msgstr "하위 필드"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__child_id
#: model_terms:ir.ui.view,arch_db:website.website_menus_form_view
msgid "Child Menus"
msgstr "하위 메뉴"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Children"
msgstr "하위"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Choose"
msgstr "선택"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_picture
msgid ""
"Choose a vibrant image and write an inspiring paragraph about it.<br/> It "
"does not have to be long, but it should reinforce your image."
msgstr ""
"생생한 이미지를 선택하고 그것에 대해 북돋아주는 문구를 씁니다. <br/>길어야 할 필요는 없지만 이미지를 보강할 수 있습니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Choose an anchor name"
msgstr "앵커 이름 선택"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#, python-format
msgid "Choose another theme"
msgstr "앵커 테마 선택"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Choose your favorite"
msgstr "마음에 드는 항목을 선택해보세요:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
msgid "Circle"
msgstr "원형"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Classic"
msgstr "기본"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Clean"
msgstr "정리"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "Clever Slogan"
msgstr "영리한 슬로건"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Click and change content directly from the front-end: no complex back\n"
"                            end to deal with."
msgstr ""
"컨텐츠를 직접 클릭하여 프런트 엔드에서 변경하십시오. 처리할 복잡한 \n"
"                            백엔드가 없습니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "Click here to go back to block tab."
msgstr "블록 탭으로 돌아가려면 여기를 클릭하십시오."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Click on"
msgstr "여기를 클릭하세요."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Click on the icon to adapt it <br/>to your purpose."
msgstr "아이콘을 클릭하여 목적에 맞게 <br/>조정하십시오."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Click to choose more images"
msgstr "더 많은 이미지를 선택하려면 클릭하십시오"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Click to select"
msgstr "클릭하여 선택"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Client ID"
msgstr "고객 ID"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Client Secret"
msgstr "고객 비밀키"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Clone this page"
msgstr "이 페이지 복사"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/xml/website.xml:0
#: model_terms:ir.ui.view,arch_db:website.qweb_500
#: model_terms:ir.ui.view,arch_db:website.s_popup
#: model_terms:ir.ui.view,arch_db:website.show_website_info
#, python-format
msgid "Close"
msgstr "닫기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Close Button Color"
msgstr "닫기 버튼 색상"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Clothes"
msgstr "의류"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
msgid "Code"
msgstr "코드"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Code Injection"
msgstr "코드 삽입"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
msgid "Collapse Icon"
msgstr "축소 아이콘"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_badge_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Color"
msgstr "색상"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_color_blocks_2
msgid ""
"Color blocks are a simple and effective way to <b>present and highlight your"
" content</b>. Choose an image or a color for the background. You can even "
"resize and duplicate the blocks to create your own layout. Add images or "
"icons to customize the blocks."
msgstr ""
"색 블록은 <b>콘텐츠를 쉽고 효과적으로 표시하고 강조 표시</b>할 수 있는 방법입니다. 배경 이미지 또는 색상을 선택합니다. 블록의 "
"크기를 조정하고 복제하여 자신만의 레이아웃을 만들 수도 있습니다. 이미지 또는 아이콘을 추가하여 블록을 사용자 정의합니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Color filter"
msgstr "색상 필터"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Colors"
msgstr "색상"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Columns"
msgstr "열"

#. module: website
#: model:ir.model.fields,help:website.field_website_configurator_feature__website_config_preselection
msgid ""
"Comma-separated list of website type/purpose for which this feature should "
"be pre-selected"
msgstr "이 기능을 사전 설정해야 하는 웹사이트 유형/목적의 쉼표로 구분된 목록"

#. module: website
#: model:ir.model,name:website.model_res_company
msgid "Companies"
msgstr "회사"

#. module: website
#: code:addons/website/models/website.py:0
#: model:ir.model.fields,field_description:website.field_website__company_id
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#, python-format
msgid "Company"
msgstr "회사"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Complete CRM for any size team"
msgstr "모든 규모의 팀을 위한 완벽한 CRM"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Components"
msgstr "구성 요소"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Computers"
msgstr "컴퓨터"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Computers &amp; Devices"
msgstr "컴퓨터 &amp; 기기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Conditionally"
msgstr "조건부로"

#. module: website
#: model:ir.model,name:website.model_res_config_settings
msgid "Config Settings"
msgstr "설정 구성"

#. module: website
#: model:ir.ui.menu,name:website.menu_website_global_configuration
msgid "Configuration"
msgstr "구성"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__configurator_done
msgid "Configurator Done"
msgstr "Configurator 완료"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_social_network
msgid "Configure Social Network"
msgstr "소셜 네트워크 구성"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/dashboard.js:0
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Connect Google Analytics"
msgstr "구글 웹 로그 분석 연결"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Connect with us"
msgstr "우리와 연결"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Connected"
msgstr "연결됨"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_google_search_console
msgid "Console Google Search"
msgstr "콘솔 구글 검색"

#. module: website
#: model:ir.model,name:website.model_res_partner
#: model:ir.model.fields,field_description:website.field_website_visitor__partner_id
#: model_terms:ir.ui.view,arch_db:website.s_tabs
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Contact"
msgstr "연락처"

#. module: website
#: code:addons/website/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website.header_call_to_action
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.s_banner
#, python-format
msgid "Contact Us"
msgstr "문의하기"

#. module: website
#: code:addons/website/models/website_visitor.py:0
#, python-format
msgid "Contact Visitor"
msgstr "방문자에게 문의"

#. module: website
#: model:website.menu,name:website.menu_contactus
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
#: model_terms:ir.ui.view,arch_db:website.s_cover
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#: model_terms:website.page,arch_db:website.contactus_page
msgid "Contact us"
msgstr "문의하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid ""
"Contact us about anything related to our company or services.<br/>\n"
"                                    We'll do our best to get back to you as soon as possible."
msgstr ""
"저희 회사 또는 저희의 서비스에 대해 궁금한 점이 있으시면 문의 주시기 바랍니다.<br/>최대한 빨리 답변드릴 수 있도록 하겠습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "Contact us anytime"
msgstr "언제든지 문의하세요"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Contact us for any issue or question"
msgstr "궁금하신 사항이 있으시면 언제든지 문의해주십시오"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Contacts"
msgstr "연락처"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Contains"
msgstr "포함"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_robots__content
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Content"
msgstr "내용"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__cdn_activated
#: model:ir.model.fields,field_description:website.field_website__cdn_activated
msgid "Content Delivery Network (CDN)"
msgstr "콘텐츠 전송 네트워크 (CDN)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Content Width"
msgstr "내용 폭"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/translator.xml:0
#, python-format
msgid "Content to translate"
msgstr "번역할 내용"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Continue"
msgstr "계속"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Continue reading <i class=\"fa fa-long-arrow-right align-middle ml-1\"/>"
msgstr "계속 읽기 <i class=\"fa fa-long-arrow-right align-middle ml-1\"/>"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
#: model_terms:ir.ui.view,arch_db:website.cookies_bar
#, python-format
msgid "Cookie Policy"
msgstr "쿠기 정책"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.res_config_settings.xml:0
#, python-format
msgid "Cookie bars may significantly impair the experience"
msgstr "쿠키 바는 경험을 크게 손상시킬 수 있습니다"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_cookies_bar
#: model:ir.model.fields,field_description:website.field_website__cookies_bar
msgid "Cookies Bar"
msgstr "쿠기 바"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Cookies are small bits of text sent by our servers to your computer or device when you access our services.\n"
"                            They are stored in your browser and later sent back to our servers so that we can provide contextual content.\n"
"                            Without cookies, using the web would be a much more frustrating experience.\n"
"                            We use them to support your activities on our website. For example, your session (so you don't have to login again) or your shopping cart.\n"
"                            <br/>\n"
"                            Cookies are also used to help us understand your preferences based on previous or current activity on our website (the pages you have\n"
"                            visited), your language and country, which enables us to provide you with improved services.\n"
"                            We also use cookies to help us compile aggregate data about site traffic and site interaction so that we can offer\n"
"                            better site experiences and tools in the future."
msgstr ""
"쿠키는 사용자가 서비스에 액세스할 때 서버가 컴퓨터 또는 장치로 보내는 작은 텍스트 조각입니다.\n"
"                             컨텍스트 콘텐츠를 제공할 수 있도록 브라우저에 저장했다가 나중에 서버로 다시 전송됩니다.\n"
"                             쿠키가 없다면, 웹을 사용하는 것은 훨씬 더 실망스러운 경험이 될 것입니다.\n"
"                             웹 사이트에서 사용자의 활동을 지원하는 데 사용합니다. 예를 들어 세션(다시 로그인할 필요 없음)이나 쇼핑 카트가 있습니다.\n"
"                              <br/>\n"
"                              또한 쿠키는 당사 웹 사이트(방문하신 페이지)의 이전 또는 현재 활동, 언어 및 국가를 기준으로 사용자의 선호도를 이해하는 데 \n"
"                              도움이 되며, 이를 통해 개선된 서비스를 제공할 수 있습니다.\n"
"                              또한 쿠키를 사용하여 사이트 트래픽 및 사이트 상호 작용에 대한 집계 데이터를 컴파일하여 \n"
"                              향후 더 나은 사이트 경험과 도구를 제공할 수 있습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Copyright"
msgstr "저작권"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#, python-format
msgid "Countdown ends in"
msgstr "카운트다운 종료"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
#, python-format
msgid "Countdown is over - Firework"
msgstr "카운트 다운 종료 - 불꽃 놀이"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__country_id
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Country"
msgstr "국가"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__country_flag
msgid "Country Flag"
msgstr "국기"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_country_group_ids
#: model:ir.model.fields,field_description:website.field_website__country_group_ids
msgid "Country Groups"
msgstr "국가 그룹"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Course"
msgstr "강좌"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.record_cover
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
msgid "Cover"
msgstr "표지"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
msgid "Cover Photo"
msgstr "표지 사진"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_cover_properties_mixin__cover_properties
msgid "Cover Properties"
msgstr "표지 속성"

#. module: website
#: model:ir.model,name:website.model_website_cover_properties_mixin
msgid "Cover Properties Website Mixin"
msgstr "표지 속성 웹사이트 혼합"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/utils.js:0
#: model_terms:ir.ui.view,arch_db:website.view_website_form_view_themes_modal
#, python-format
msgid "Create"
msgstr "작성"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid "Create Page"
msgstr "페이지 작성"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.brand_promotion
msgid "Create a"
msgstr "만들기"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Create a Google Project and Get a Key"
msgstr "구글 프로젝트 만들기 및 키 얻기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Create a New Website"
msgstr "새 웹사이트 만들기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Create a link to target this section"
msgstr "이 섹션을 대상으로 하는 링크 만들기"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Create new"
msgstr "새로 만들기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Create your page from scratch by dragging and dropping pre-made,\n"
"                            fully customizable building blocks."
msgstr ""
"미리 만들어진 완전히 사용자 정의 가능한 빌딩 블록을 끌어서 놓아 \n"
"                           처음부터 페이지를 만듭니다."

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__create_uid
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__create_uid
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__create_uid
#: model:ir.model.fields,field_description:website.field_theme_website_menu__create_uid
#: model:ir.model.fields,field_description:website.field_theme_website_page__create_uid
#: model:ir.model.fields,field_description:website.field_website__create_uid
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__create_uid
#: model:ir.model.fields,field_description:website.field_website_menu__create_uid
#: model:ir.model.fields,field_description:website.field_website_page__create_uid
#: model:ir.model.fields,field_description:website.field_website_rewrite__create_uid
#: model:ir.model.fields,field_description:website.field_website_robots__create_uid
#: model:ir.model.fields,field_description:website.field_website_route__create_uid
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__create_uid
#: model:ir.model.fields,field_description:website.field_website_visitor__create_uid
msgid "Created by"
msgstr "작성자"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Created in 2021, the company is young and dynamic. Discover the composition "
"of the team and their skills."
msgstr "2021년에 설립된 젊고 역동적인 회사입니다. 회사의 내부 구성과 보유하고 있는 기술에 대해 알아보십시오."

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__create_date
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__create_date
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__create_date
#: model:ir.model.fields,field_description:website.field_theme_website_menu__create_date
#: model:ir.model.fields,field_description:website.field_theme_website_page__create_date
#: model:ir.model.fields,field_description:website.field_website__create_date
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__create_date
#: model:ir.model.fields,field_description:website.field_website_menu__create_date
#: model:ir.model.fields,field_description:website.field_website_page__create_date
#: model:ir.model.fields,field_description:website.field_website_rewrite__create_date
#: model:ir.model.fields,field_description:website.field_website_robots__create_date
#: model:ir.model.fields,field_description:website.field_website_route__create_date
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__create_date
msgid "Created on"
msgstr "작성일자"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#, python-format
msgid "Custom"
msgstr "사용자 정의"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__custom_code_head
msgid "Custom <head> code"
msgstr "사용자 정의 <head> 코드"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "Custom Code"
msgstr "사용자 정의 코드"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Custom Key"
msgstr "사용자 정의 키"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Custom Url"
msgstr "사용자 정의 URL"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__custom_code_footer
msgid "Custom end of <body> code"
msgstr "사용자 정의 <body>코드 끝"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Custom end of body code"
msgstr "사용자 정의 body 코드 끝"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Custom field"
msgstr "사용자 지정 필드"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Custom head code"
msgstr "사용자 정의 head 코드"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__auth_signup_uninvited
#: model:ir.model.fields,field_description:website.field_website__auth_signup_uninvited
msgid "Customer Account"
msgstr "고객 계정"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Customers"
msgstr "고객"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Customization tool"
msgstr "맞춤화 도구"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Customize"
msgstr "맞춤화"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__customize_show
msgid "Customize Show"
msgstr "맞춤 쇼"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "D - H - M"
msgstr "D - H - M"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "D - H - M - S"
msgstr "D - H - M - S"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "DRAG BUILDING BLOCKS HERE"
msgstr "작성 블럭을 여기로 끌어오십시오"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
msgid "Danger"
msgstr "위험"

#. module: website
#: model:ir.ui.menu,name:website.menu_dashboard
msgid "Dashboard"
msgstr "현황판"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_line_widgets
msgid "Dashed"
msgstr "현황판에 게시"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Data"
msgstr "데이터"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Data Border"
msgstr "데이터 테두리"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Data Color"
msgstr "데이터 색상"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Dataset Border"
msgstr "데이터셋 테두리"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Dataset Color"
msgstr "데이터셋 색상"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
msgid "Date"
msgstr "일자"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Date &amp; Time"
msgstr "날짜 &amp; 시간"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#, python-format
msgid "Days"
msgstr "일"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Decimal Number"
msgstr "십진수"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Default"
msgstr "기본"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Default Access Rights"
msgstr "기본 사용 권한"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__default_lang_id
msgid "Default Language"
msgstr "기본 언어"

#. module: website
#: model:website.menu,name:website.main_menu
msgid "Default Main Menu"
msgstr "기본 주메뉴"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Default Reversed"
msgstr "기본값 반전"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_default_image
#: model:ir.model.fields,field_description:website.field_website__social_default_image
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Default Social Share Image"
msgstr "기본 소셜 공유 이미지"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Default Value"
msgstr "기본값"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_default_lang_id
msgid "Default language"
msgstr "기본 언어"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_default_lang_code
msgid "Default language code"
msgstr "기본 언어 코드"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Delay"
msgstr "지연"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Delete Blocks"
msgstr "블록 삭제"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Delete Menu Item"
msgstr "메뉴 항목 삭제"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Delete Page"
msgstr "페이지 삭제"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"Delete the above image or replace it with a picture that illustrates your "
"message. Click on the picture to change its <em>rounded corner</em> style."
msgstr ""
"위의 이미지를 삭제하거나 메시지를 삽입한 사진으로 바꾸꿉니다. <em>둥근 모서리</em> 스타일을 변경하려면 그림을 클릭합니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Delete this font"
msgstr "해당 글꼴 삭제하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Delete this page"
msgstr "해당 페이지 삭제하기"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"Deleting a font requires a reload of the page. This will save all your "
"changes and reload the page, are you sure you want to proceed?"
msgstr ""
"글꼴을 삭제하려면 페이지를 다시 로드해야 합니다. 그러면 변경 내용이 모두 저장되고 페이지가 다시 로드됩니다. 계속하시겠습니까?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "Deliveries"
msgstr "배송"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "Departments"
msgstr "부서"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Dependencies"
msgstr "의존성"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "Describe your field here."
msgstr "자신의 분야에 대해 설명해 주세요."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__description
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
#, python-format
msgid "Description"
msgstr "설명"

#. module: website
#: model:website.configurator.feature,description:website.feature_page_our_services
msgid "Description of your services offer"
msgstr "제공하는 서비스에 대해 설명해주세요."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog_options
msgid "Descriptions"
msgstr "설명"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Descriptive"
msgstr "설명"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Design"
msgstr "디자인"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Design features"
msgstr "디자인 기능"

#. module: website
#: model:website.configurator.feature,description:website.feature_page_pricing
msgid "Designed to drive conversion"
msgstr "전환을 유도하도록 디자인"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#, python-format
msgid "Desktop"
msgstr "데스크탑"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Desktop computers"
msgstr "데스크탑 컴퓨터"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Detail"
msgstr "세부정보"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Details"
msgstr "세부 정보"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Detect"
msgstr "자동 감지: "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Direction"
msgstr "방향"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__directive
msgid "Directive"
msgstr "지시어"

#. module: website
#: model:ir.actions.server,name:website.website_disable_unused_snippets_assets_ir_actions_server
#: model:ir.cron,cron_name:website.website_disable_unused_snippets_assets
#: model:ir.cron,name:website.website_disable_unused_snippets_assets
msgid "Disable unused snippets assets"
msgstr "사용하지 않는 코드조각 비활성화"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Disabled"
msgstr "비활성화"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Disappearing"
msgstr "사라짐"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Disappears"
msgstr "사라지기"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/js/menu/content.js:0
#: code:addons/website/static/src/js/menu/seo.js:0
#: code:addons/website/static/src/snippets/s_embed_code/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Discard"
msgstr "작성 취소"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Discard & Edit in backend"
msgstr "백엔트에서 편집 및 작성 취소"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Discover"
msgstr "알아보기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "Discover all the features"
msgstr "모든 기능 알아보기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
msgid "Discover more"
msgstr "더 알아보기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Discover our culture and our values"
msgstr "당사의 기업 문화와 핵심 가치를 알아보세요."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Discover our legal notice"
msgstr "당사의 법적 고지 및 정책에 대해 알아보세요."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Discover our realisations"
msgstr "실현 사례 알아보기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid "Discover our team"
msgstr "팀 소개"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Discrete"
msgstr "분리"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Discussion Group"
msgstr "토론 그룹"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_share_options
msgid "Disk"
msgstr "디스크"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Display"
msgstr "표시"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "Display Inline"
msgstr "인라인 표시"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__display_name
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__display_name
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__display_name
#: model:ir.model.fields,field_description:website.field_theme_website_menu__display_name
#: model:ir.model.fields,field_description:website.field_theme_website_page__display_name
#: model:ir.model.fields,field_description:website.field_website__display_name
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__display_name
#: model:ir.model.fields,field_description:website.field_website_menu__display_name
#: model:ir.model.fields,field_description:website.field_website_page__display_name
#: model:ir.model.fields,field_description:website.field_website_rewrite__display_name
#: model:ir.model.fields,field_description:website.field_website_robots__display_name
#: model:ir.model.fields,field_description:website.field_website_route__display_name
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__display_name
#: model:ir.model.fields,field_description:website.field_website_track__display_name
#: model:ir.model.fields,field_description:website.field_website_visitor__display_name
msgid "Display Name"
msgstr "표시명"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_cookies_bar
#: model:ir.model.fields,help:website.field_website__cookies_bar
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Display a customizable cookies bar on your website."
msgstr "웹 사이트에 사용자 지정 가능한 쿠키 막대를 표시합니다."

#. module: website
#: code:addons/website/models/ir_qweb_fields.py:0
#, python-format
msgid "Display the badges"
msgstr "배지 표시"

#. module: website
#: code:addons/website/models/ir_qweb_fields.py:0
#, python-format
msgid "Display the biography"
msgstr "이력 표시"

#. module: website
#: code:addons/website/models/ir_qweb_fields.py:0
#, python-format
msgid "Display the website description"
msgstr "웹사이트 설명 표시"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_logo
#: model:ir.model.fields,help:website.field_website__logo
msgid "Display this logo on the website."
msgstr "웹사이트에 해당 로고 표시"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Display this website when users visit this domain"
msgstr "해당 도메인으로 사용자가 방문할 때만 이 웹사이트 표시"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/res_config_settings.js:0
#, python-format
msgid "Do not activate"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Do you need specific information? Our specialists will help you with "
"pleasure."
msgstr "구체적인 정보가 필요하신가요? 저희의 직원이 기꺼이 도와드리겠습니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Do you want to edit the company data ?"
msgstr "회사 정보를 수정하시겠습니까?"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Do you want to install the \"%s\" App?"
msgstr "\"%s\" 앱을 설치하시겠습니까?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Documentation"
msgstr "문서화"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Doesn't contain"
msgstr "포함하지 않음"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Domain"
msgstr "도메인"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "Don't forget to update all links referring to this page."
msgstr "이 페이지를 참조하는 모든 링크를 업데이트하는 것을 잊지 마십시오."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/theme_preview_kanban.js:0
#, python-format
msgid "Don't worry, you can switch later."
msgstr "걱정하지 마세요. 나중에 전환할 수 있습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Donation"
msgstr "기부"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Donation Button"
msgstr "기부하기 버튼"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Dots"
msgstr "점"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_line_widgets
msgid "Dotted"
msgstr "점 찍힘"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_line_widgets
msgid "Double"
msgstr "두번"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Double click an icon to replace it with one of your choice."
msgstr "아이콘을 두 번 클릭하여 선택한 것으로 바꿉니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Doughnut"
msgstr "도넛"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid ""
"Drag the <b>%s</b> building block and drop it at the bottom of the page."
msgstr "<b>%s</b> 빌딩 블록을 드래그하여 페이지 하단에 위치시켜주세요."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Drag to the right to get a submenu"
msgstr "오른쪽으로 끌어서 하위 메뉴로 만듭니다"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Dresses"
msgstr "드레스"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Dropdown"
msgstr "드롭다운"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Dropdown menu"
msgstr "드롭다운 메뉴"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Due Date"
msgstr "만기일"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Duplicate"
msgstr "복제"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Duplicate Page"
msgstr "복제 페이지"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Duplicate blocks <br/>to add more steps."
msgstr "더 많은 단계를 추가하려면 블록을 복제<br/>합니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Duplicate blocks and columns to add more features."
msgstr "더 많은 기능을 추가하기 위해 블록 및 열 복제하기."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Dynamic Content"
msgstr "동적 콘텐츠"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_domain
#: model:ir.model.fields,help:website.field_website__domain
msgid "E.g. https://www.mydomain.com"
msgstr "예. https://www.mydomain.com"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Easily design your own Odoo templates thanks to clean HTML\n"
"                            structure and bootstrap CSS."
msgstr "깨끗한 HTML 구조와 부트스트랩 CSS 덕분에 자신만의 오두 템플릿을 쉽게 디자인할 수 있습니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#, python-format
msgid "Edit"
msgstr "편집하기"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#: code:addons/website/static/src/js/widgets/link_popover_widget.js:0
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Edit Menu"
msgstr "메뉴 편집하기"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Edit Menu Item"
msgstr "메뉴 항목 편집하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Edit Message"
msgstr "메시지 편집하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Edit Styles"
msgstr "스타일 편집하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Edit Top Menu"
msgstr "최상위 메뉴 편집하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Edit code in backend"
msgstr "백엔드에서 코드 편집하기"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_embed_code/options.js:0
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#, python-format
msgid "Edit embedded code"
msgstr "삽입된 코드 수정하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Edit in backend"
msgstr "백엔드에서 편집하기"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Edit my Analytics Client ID"
msgstr "개인 분석용 클라이언트 ID 편집하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Edit robots.txt"
msgstr "robots.txt 편집하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Edit video"
msgstr "동영상 편집하기"

#. module: website
#: model:res.groups,name:website.group_website_designer
msgid "Editor and Designer"
msgstr "편집 및 디자이너"

#. module: website
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "Either action_server_id or filter_id must be provided."
msgstr "action_server_id 또는 filter_id를 제공해야 합니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Electronics"
msgstr "전자 제품"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__email
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
msgid "Email"
msgstr "이메일"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Email address"
msgstr "이메일 주소"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Email support"
msgstr "지원 이메일"

#. module: website
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "Empty field name in %r"
msgstr "%r의 필드명이 비어 있습니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Enable billing on your Google Project"
msgstr "Google 프로젝트에서 청구 활성화"

#. module: website
#: model:ir.model.fields,help:website.field_ir_model__website_form_access
msgid "Enable the form builder feature for this model."
msgstr "이 모델에 대해 양식 작성기 기능을 사용하십시오."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Enable the right google map APIs in your google account"
msgstr "Google 계정에서 정확한 Google 지도 API를 사용하도록 설정하세요."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Enter an API Key"
msgstr "API 키를 입력하세요"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"Enter code that will be added before the </body> of every page of your site."
msgstr "사이트의 모든 페이지 앞에 추가할 코드를 입력</body>하십시오."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Enter code that will be added into every page of your site"
msgstr "사이트의 모든 페이지에 추가할 코드를 입력하십시오."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"Enter code that will be added into the <head> of every page of your site."
msgstr "사이트의 모든 페이지에 추가할 코드를 입력<head>하십시오."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Enter email"
msgstr "이메일 입력"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Equal Widths"
msgstr "폭 맞추기"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form.xml:0
#: code:addons/website/static/src/xml/website_form.xml:0
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Error"
msgstr "오류"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Essential oils"
msgstr "에센셜 오일"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Event"
msgstr "행사"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Event heading"
msgstr "행사 제목"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_event
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Events"
msgstr "행사"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Every Time"
msgstr "항상"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Everything"
msgstr "모두"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Examples"
msgstr "예시"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Expert"
msgstr "전문가"

#. module: website
#: model:website.configurator.feature,description:website.feature_page_privacy_policy
msgid "Explain how you protect privacy"
msgstr "개인정보 보호 정책에 대해 설명하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert
msgid ""
"Explain the benefits you offer. <br/>Don't write about products or services "
"here, write about solutions."
msgstr "당신이 제공하는 혜택을 설명하세요. <br/>여기에 제품이나 서비스에 대해 쓰지 말고 솔루션에 대해 써보세요."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Explore"
msgstr "탐색"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__cache_key_expr
msgid ""
"Expression (tuple) to evaluate the cached key. \n"
"E.g.: \"(request.params.get(\"currency\"), )\""
msgstr ""
"캐시된 키를 평가하는 식(튜플)입니다. \n"
"예 : \"(request.params.get(\"currency\"), )\""

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_ui_view__mode__extension
msgid "Extension View"
msgstr "확장 화면"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_actions_server__xml_id
#: model:ir.model.fields,field_description:website.field_ir_cron__xml_id
#: model:ir.model.fields,field_description:website.field_website_page__xml_id
msgid "External ID"
msgstr "외부 ID"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Extra Large"
msgstr "아주 크게"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Extra link"
msgstr "추가 링크"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Extra-Large"
msgstr "아주 크게"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Extra-Small"
msgstr "매우 작게"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "F.A.Q."
msgstr "F.A.Q."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Facebook"
msgstr "페이스북"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_facebook
#: model:ir.model.fields,field_description:website.field_website__social_facebook
msgid "Facebook Account"
msgstr "페이스북 계정"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade"
msgstr "희미하게"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade In"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade In-Down"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade In-Left"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade In-Right"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade In-Up"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade Out"
msgstr "서서히 사라지기"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Failed to install \"%s\""
msgstr "\"%s\" 설치에 실패했습니다"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Farm Friendly Chicken Supreme"
msgstr "농장 친화적인 치킨 슈프림"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__favicon
msgid "Favicon"
msgstr "파비콘"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature One"
msgstr "기능 1"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature Three"
msgstr "기능 3"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card
msgid "Feature Title"
msgstr "기능 제목"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature Two"
msgstr "기능 2"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__feature_url
msgid "Feature Url"
msgstr "Url 기능"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.snippets
#, python-format
msgid "Features"
msgstr "기능"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
msgid "Fetched elements"
msgstr "가져온 요소"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__field_names
msgid "Field Names"
msgstr "필드명"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model__website_form_default_field_id
msgid "Field for custom form data"
msgstr "사용자 정의 양식 데이터 필드"

#. module: website
#: model:ir.model,name:website.model_ir_model_fields
msgid "Fields"
msgstr "필드"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "File Upload"
msgstr "파일 업로드"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch_fs
msgid ""
"File from where the view originates.\n"
"                                                          Useful to (hard) reset broken views or to read arch from file in dev-xml mode."
msgstr ""
"화면이 유래된 곳의 파일입니다. 깨진 화면을(하드) 재설정하거나 dev-xml 모드에 있는 파일에서 보관본을 읽는 데 유용합니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Filet Mignon 8oz"
msgstr "필레 미뇽 8oz"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fill"
msgstr "채우기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Fill and justify"
msgstr "채우기 및 양쪽 맞춤"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__filter_id
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
msgid "Filter"
msgstr "필터"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Filter Intensity"
msgstr "필터 강도"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Find a store near you"
msgstr "가까운 매장 찾기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Find all information about our deliveries, express deliveries and all you "
"need to know to return a product."
msgstr "일반 배송, 특급 배송 및 반품에 필요한 모든 정보를 찾아보세요."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Find out how we were able helping them and set in place solutions adapted to"
" their needs."
msgstr "어떤 방식으로 그들을 돕고 꼭 맞는 솔루션을 마련했는지 알아보세요."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Find the perfect solution for you"
msgstr "나에게 꼭 맞는 솔루션 찾기"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__create_date
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "First Connection"
msgstr "첫 연결"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "First Feature"
msgstr "첫 번째 기능"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "First Menu"
msgstr "첫 번째 메뉴"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "First Time Only"
msgstr "처음 사용 시에만"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "First feature"
msgstr "첫 번째 기능"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "First list of Features"
msgstr "첫 번째 기능 목록"

#. module: website
#: model:ir.model.fields,help:website.field_ir_ui_view__first_page_id
#: model:ir.model.fields,help:website.field_website_page__first_page_id
msgid "First page linked to this view"
msgstr "이 화면에 연결된 첫 번째 페이지"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fit content"
msgstr "맞춤 콘텐츠"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fit text"
msgstr "문자 맞추기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Fixed"
msgstr "고정"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flag"
msgstr "신고하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flag and Text"
msgstr "신고 및 문자"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flash"
msgstr "플래시"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "Flat"
msgstr "고정 소수점"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flip-In-X"
msgstr "Flip-In-X"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flip-In-Y"
msgstr "Flip-In-Y"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Float"
msgstr "부동 소수점"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
#: model_terms:ir.ui.view,arch_db:website.template_header_boxed_oe_structure_header_boxed_1
#: model_terms:ir.ui.view,arch_db:website.template_header_hamburger_full_oe_structure_header_hamburger_full_1
#: model_terms:ir.ui.view,arch_db:website.template_header_hamburger_oe_structure_header_hamburger_3
#: model_terms:ir.ui.view,arch_db:website.template_header_sidebar_oe_structure_header_sidebar_1
#: model_terms:ir.ui.view,arch_db:website.template_header_vertical_oe_structure_header_vertical_1
msgid "Follow us"
msgstr "함께하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Follow your website traffic in Odoo."
msgstr "오두에서 웹 사이트 트래픽을 팔로우하세요."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Font"
msgstr "글꼴"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Font Size"
msgstr "글자 크기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Font family"
msgstr "글꼴 모음"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Font size"
msgstr "글자 크기"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__footer_visible
msgid "Footer Visible"
msgstr "바닥글 표시"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.res_config_settings.xml:0
#, python-format
msgid "For session cookies, authentification and analytics,"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Force your user to create an account per website"
msgstr "사용자가 웹 사이트별로 계정을 만들도록 합니다"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Form"
msgstr "양식"

#. module: website
#: model:ir.model.fields,help:website.field_ir_model__website_form_label
msgid ""
"Form action label. Ex: crm.lead could be 'Send an e-mail' and project.issue "
"could be 'Create an Issue'."
msgstr "양식 작업 꼬리표. 예 : crm.lead는 '이메일 보내기'이고 project.issue는 '문제 만들기'일 수 있습니다."

#. module: website
#: model:website.configurator.feature,name:website.feature_module_forum
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Forum"
msgstr "게시판"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Founder and chief visionary, Tony is the driving force behind the company. He loves\n"
"                                to keep his hands full by participating in the development of the software,\n"
"                                marketing, and customer experience strategies."
msgstr ""
"창업자이자 최고의 선각자인 토니는 이 회사의 원동력입니다. 그는 소프트웨어 개발, \n"
"                                마케팅 및 고객 경험 전략에 참여함으로써 자신의 역량을 최대한 \n"
"                                발휘하기를 좋아합니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Framed"
msgstr "액자"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website__auth_signup_uninvited__b2c
msgid "Free sign up"
msgstr "자유 가입"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
msgid "Friends' Faces"
msgstr "친구의 얼굴"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"From seminars to team building activities, we offer a wide choice of events "
"to organize."
msgstr "세미나부터 팀워크 활동까지 다양한 이벤트를 준비할 수 있습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Full"
msgstr "채우기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Full Screen"
msgstr "전체 화면"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Full Width"
msgstr "폭 채우기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Full screen"
msgstr "전체 화면"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Full-Width"
msgstr "폭 채우기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Furniture"
msgstr "가구"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#, python-format
msgid "G-XXXXXXXXXX"
msgstr "G-XXXXXXXXXX"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "GPS &amp; navigation"
msgstr "GPS &amp; 네비게이션"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Gaming"
msgstr "게이밍"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Get Delivered"
msgstr "납품 받기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Get access to all modules"
msgstr "모든 모듈에 접근하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Get access to all modules and features"
msgstr "모든 모듈 및 기능에 접근하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Get in touch"
msgstr "연락하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "GitHub"
msgstr "GitHub"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_github
#: model:ir.model.fields,field_description:website.field_website__social_github
msgid "GitHub Account"
msgstr "GitHub 계정"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_forum
msgid "Give visitors the information they need"
msgstr "방문자에게 필요한 정보 제공"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Glasses"
msgstr "안경"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Go To Page"
msgstr "해당 페이지로 이동"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Go to"
msgstr "이동하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_form_extend
msgid "Go to Page Manager"
msgstr "페이지 관리자로 이동하기"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#: code:addons/website/static/src/xml/website.backend.xml:0
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#, python-format
msgid "Go to Website"
msgstr "웹사이트로 이동하기"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "Go to the Theme tab"
msgstr "테마 탭으로 이동"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"Good copy starts with understanding how your product or service helps your "
"customers. Simple words communicate better than big words and pompous "
"language."
msgstr ""
"좋은 사본은 제품이나 서비스가 고객에게 어떻게 도움이 되는지 이해하는 것으로 시작됩니다. 간단한 단어는 큰 단어 나 욕설보다 더 잘 의사"
" 소통합니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "Good job! It's time to <b>Save</b> your work."
msgstr "잘했어요! 이제 작업을 <b>저장</b>할 시간입니다."

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_google_analytics
msgid "Google Analytics"
msgstr "구글 웹로그 분석"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_google_analytics_dashboard
msgid "Google Analytics Dashboard"
msgstr "구글 웹 로그 분석 현황판"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_analytics_key
#: model:ir.model.fields,field_description:website.field_website__google_analytics_key
msgid "Google Analytics Key"
msgstr "Google 웹 로그 분석 키"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid ""
"Google Analytics initialization failed. Maybe this domain is not whitelisted"
" in your Google Analytics project for this client ID."
msgstr ""
"Google 웹 로그 분석 초기화에 실패했습니다. 이 도메인은 이 고객 ID에 대한 Google 웹 로그 분석 프로젝트에서 허용되지 "
"않습니다."

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_management_client_id
#: model:ir.model.fields,field_description:website.field_website__google_management_client_id
msgid "Google Client ID"
msgstr "구글 고객 ID"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_management_client_secret
#: model:ir.model.fields,field_description:website.field_website__google_management_client_secret
msgid "Google Client Secret"
msgstr "구글 고객 비밀키"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Google Font address"
msgstr "구글 글꼴 주소"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Google Map"
msgstr "구글 지도"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Google Map API Key"
msgstr "구글 지도 API 키"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_google_maps
msgid "Google Maps"
msgstr "구글 지도"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_maps_api_key
#: model:ir.model.fields,field_description:website.field_website__google_maps_api_key
msgid "Google Maps API Key"
msgstr "구글 지도 API 키"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_search_console
#: model:ir.model.fields,field_description:website.field_website__google_search_console
msgid "Google Search Console"
msgstr "구글 검색 콘솔"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid ""
"Google deprecated both its \"Universal Analytics\" and \"Google Sign-In\" "
"API. It means that only accounts and keys created before 2020 will be able "
"to integrate their Analytics dashboard in Odoo (or any other website). This "
"will be possible only up to mid 2023. After that, those services won't work "
"anymore, at all."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__google_search_console
#: model:ir.model.fields,help:website.field_website__google_search_console
msgid "Google key, or Enable to access first reply"
msgstr "Google 키 또는 첫 번째 회신에 접근하려면 사용 가능으로"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Gray #{grayCode}"
msgstr "회색 #{grayCode}"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Grays"
msgstr "흑백 조절"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Great Value"
msgstr "훌륭한 가치"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid ""
"Great stories are <b>for everyone</b> even when only written <b>for just one"
" person</b>. If you try to write with a wide, general audience in mind, your"
" story will sound fake and lack emotion. No one will be interested. Write "
"for one person. If it’s genuine for the one, it’s genuine for the rest."
msgstr ""
"훌륭한 이야기는 <b>한 사람</b>만을 위해 쓰여질 때도 <b>모두를 위한 것</b>입니다. 만약 여러분이 넓고 일반적인 청중을 염두에"
" 두고 글을 쓰려고 한다면, 여러분의 이야기는 가짜처럼 들릴 것이고 감정이 부족할 것입니다. 아무도 관심을 갖지 않을 겁니다. 한 사람을"
" 위해 쓰세요. 진품이라면 나머지는 진품입니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid ""
"Great stories have a <b>personality</b>. Consider telling a great story that"
" provides personality. Writing a story with personality for potential "
"clients will assist with making a relationship connection. This shows up in "
"small quirks like word choices or phrases. Write from your point of view, "
"not from someone else's experience."
msgstr ""
"훌륭한 이야기에는 <b>개성</b>이 있습니다. 개성을 제공하는 훌륭한 이야기를 하는 것을 고려해보세요. 잠재 고객을 위한 개성이 담긴 "
"이야기를 쓰는 것은 관계를 맺는 데 도움이 될 것입니다. 이것은 단어 선택이나 구와 같은 작은 별명에 나타납니다. 다른 사람의 경험이 "
"아닌 당신의 관점에서 쓰세요."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Grid"
msgstr "그리드"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Group By"
msgstr "그룹별"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__groups_id
msgid "Groups"
msgstr "그룹"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "H1"
msgstr "H1"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "H2"
msgstr "H2"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "H4 Card title"
msgstr "H4 카드 제목"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "H5 Card subtitle"
msgstr "H5 카드 소제목"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "HTML/CSS/JS Editor"
msgstr "HTML/CSS/JS 편집기"

#. module: website
#: model:ir.model,name:website.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP 라우팅"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Half Screen"
msgstr "1/2 화면"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Half screen"
msgstr "1/2 화면"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Hamburger Full"
msgstr "햄버거 가득"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Hamburger Type"
msgstr "햄버거 유형"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Hamburger menu"
msgstr "햄버거 메뉴"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
#, python-format
msgid "Happy Odoo Anniversary!"
msgstr "Odoo 기념일을 축하합니다!"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__has_social_default_image
msgid "Has Social Default Image"
msgstr "소셜 기본 이미지 있음"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Header"
msgstr "머리글"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__header_color
msgid "Header Color"
msgstr "머리글 색상"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__header_overlay
msgid "Header Overlay"
msgstr "머리글 겹치기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Header Position"
msgstr "머리글 위치"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__header_visible
msgid "Header Visible"
msgstr "머리글 표시 가능"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 1"
msgstr "제목 1"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 2"
msgstr "제목 2"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 3"
msgstr "제목 3"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 4"
msgstr "제목 4"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 5"
msgstr "제목 5"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 6"
msgstr "제목 6"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Headline"
msgstr "제목"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Height"
msgstr "높이"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Height (Scrolled)"
msgstr "높이(스크롤)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Help center"
msgstr "헬프데스크"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/translator.xml:0
#, python-format
msgid "Here are the visuals used to help you translate efficiently:"
msgstr "다음은 효율적인 변환을 지원하는 데 사용되는 시각 자료입니다 :"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Here is an overview of the cookies that may be stored on your device when "
"you visit our website:"
msgstr "다음은 웹 사이트를 방문할 때 장치에 저장될 수 있는 쿠키 개요입니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Hidden"
msgstr "숨김"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_conditional_visibility
msgid "Hidden for"
msgstr "다음 항목에 대해 숨김"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Hidden on mobile"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Hide"
msgstr "숨기기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Hide For"
msgstr "숨기기"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Hide this page from search results"
msgstr "검색 결과에서 이 페이지 숨기기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "High"
msgstr "높음"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"Hint: How to use Google Map on your website (Contact Us page and as a "
"snippet)"
msgstr "힌트 : 웹 사이트에서 Google지도를 사용하는 방법 (문의 페이지 및 스니펫)"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"Hint: Type '/' to search an existing page and '#' to link to an anchor."
msgstr "힌트 : 기존 페이지를 검색하려면 '/'를 입력하고 앵커에 연결하려면 '#'을 입력하십시오."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#: model:website.menu,name:website.menu_home
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_tabs
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Home"
msgstr "홈"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Home <span class=\"sr-only\">(current)</span>"
msgstr "홈 <span class=\"sr-only\">(현재)</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Home audio"
msgstr "홈 오디오"

#. module: website
#. openerp-web
#: code:addons/website/models/website.py:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model:ir.model.fields,field_description:website.field_website__homepage_id
#: model:ir.model.fields,field_description:website.field_website_page__is_homepage
#: model_terms:ir.ui.view,arch_db:website.one_page_line
#, python-format
msgid "Homepage"
msgstr "홈페이지"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Hoodies"
msgstr "후드"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Horizontal"
msgstr "수평"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#, python-format
msgid "Hours"
msgstr "시간"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "How can we help?"
msgstr "어떻게 도와 드릴까요?"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "How to get my Client ID"
msgstr "내 클라이언트 ID를 얻는 방법"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "How to get my Measurement ID"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "Hybrid"
msgstr "하이브리드"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: model_terms:ir.ui.view,arch_db:website.cookies_bar
#, python-format
msgid "I agree"
msgstr "동의합니다"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "I want"
msgstr "제작 목적 "

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__id
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__id
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__id
#: model:ir.model.fields,field_description:website.field_theme_website_menu__id
#: model:ir.model.fields,field_description:website.field_theme_website_page__id
#: model:ir.model.fields,field_description:website.field_website__id
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__id
#: model:ir.model.fields,field_description:website.field_website_menu__id
#: model:ir.model.fields,field_description:website.field_website_page__id
#: model:ir.model.fields,field_description:website.field_website_rewrite__id
#: model:ir.model.fields,field_description:website.field_website_robots__id
#: model:ir.model.fields,field_description:website.field_website_route__id
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__id
#: model:ir.model.fields,field_description:website.field_website_track__id
#: model:ir.model.fields,field_description:website.field_website_visitor__id
msgid "ID"
msgstr "ID"

#. module: website
#: model:ir.model.fields,help:website.field_ir_actions_server__xml_id
#: model:ir.model.fields,help:website.field_ir_cron__xml_id
msgid "ID of the action if defined in a XML file"
msgstr "XML 파일에 정의된 작업의 ID입니다."

#. module: website
#: model:ir.model.fields,help:website.field_website_page__xml_id
msgid "ID of the view defined in xml file"
msgstr "xml 파일에 정의된 보기의 ID"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__iap_page_code
msgid "Iap Page Code"
msgstr "Iap 페이지 코드"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__icon
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "Icon"
msgstr "아이콘"

#. module: website
#: model:ir.model.fields,help:website.field_website__specific_user_account
msgid "If True, new accounts will be associated to the current website"
msgstr "True인 경우 새 계정이 현재 웹 사이트에 연결됩니다"

#. module: website
#: model:ir.model.fields,help:website.field_website_configurator_feature__menu_sequence
msgid "If set, a website menu will be created for the feature."
msgstr "설정시, 해당 기능을 위한 웹사이트 메뉴가 생성됩니다."

#. module: website
#: model:ir.model.fields,help:website.field_website_configurator_feature__menu_company
msgid ""
"If set, add the menu as a second level menu, as a child of \"Company\" menu."
msgstr "설정 시, '회사' 메뉴의 하위 메뉴이자 두 번째 메뉴 항목으로 추가됩니다."

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__social_default_image
#: model:ir.model.fields,help:website.field_website__social_default_image
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "If set, replaces the website logo as the default social share image."
msgstr "설정된 경우 웹 사이트 로고를 기본 소셜 공유 이미지로 바꿉니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid ""
"If this error is caused by a change of yours in the templates, you have the "
"possibility to reset the template to its <strong>factory settings</strong>."
msgstr "템플릿의 변경으로 인해 이 오류가 발생하는 경우 템플릿을 <strong>최초 설정</strong>으로 재설정할 수 있습니다."

#. module: website
#: model:ir.model.fields,help:website.field_website_page__groups_id
msgid ""
"If this field is empty, the view applies to all users. Otherwise, the view "
"applies to the users of those groups only."
msgstr "이 필드가 비어 있으면 화면이 모든 사용자에게 적용됩니다. 그렇지 않으면 화면이 해당 그룹의 사용자에게만 적용됩니다."

#. module: website
#: model:ir.model.fields,help:website.field_website_page__active
msgid ""
"If this view is inherited,\n"
"* if True, the view always extends its parent\n"
"* if False, the view currently does not extend its parent but can be enabled\n"
"         "
msgstr ""
"이 화면을 상속하면 다음과 같습니다.\n"
"* True면 화면이 항상 상위 항목을 확장합니다.\n"
"* False면 화면이 현재 상위 항목을 확장하지 않지만 활성화할 수 있습니다.\n"
"         "

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid ""
"If you discard the current edits, all unsaved changes will be lost. You can "
"cancel to return to edit mode."
msgstr "현재 편집 내용을 취소하면 저장되지 않은 모든 변경 사항이 손실됩니다. 취소하여 편집 모드로 돌아갈 수 있습니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_image_gallery/options.js:0
#: model:ir.model.fields,field_description:website.field_website_visitor__partner_image
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_header_brand
#, python-format
msgid "Image"
msgstr "이미지"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Image Cover"
msgstr "이미지 커버"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Image Menu"
msgstr "이미지 메뉴"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "Image Size"
msgstr "이미지 크기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Image Text Image"
msgstr "이미지 텍스트 이미지"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Images"
msgstr "이미지"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Images Spacing"
msgstr "이미지 여백"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Images Subtitles"
msgstr "이미지 소제목"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "In main menu"
msgstr "주 메뉴에서"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "In the meantime we invite you to visit our"
msgstr "그 동안 저희는 여러분을 초대합니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/translator.xml:0
#, python-format
msgid ""
"In this mode, you can only translate texts. To change the structure of the page, you must edit the master page.\n"
"        Each modification on the master page is automatically applied to all translated versions."
msgstr ""
"이 모드에서는 텍스트만 번역 할 수 있습니다. 페이지 구조를 변경하려면 마스터 페이지를 편집해야 합니다. \n"
"      마스터 페이지의 각 수정 사항은 모든 번역된 버전에 자동으로 적용됩니다."

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__include
msgid "Include"
msgstr "포함 사항"

#. module: website
#: code:addons/website/controllers/backend.py:0
#, python-format
msgid "Incorrect Client ID / Key"
msgstr "잘못된 클라이언트 ID / 키"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model_terms:ir.ui.view,arch_db:website.index_management
#: model_terms:ir.ui.view,arch_db:website.one_page_line
#, python-format
msgid "Indexed"
msgstr "색인화"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Indicators"
msgstr "지표"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Info"
msgstr "정보"

#. module: website
#: model:website.configurator.feature,description:website.feature_page_about_us
msgid "Info and stats about your company"
msgstr "회사 관련 정보 및 통계"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Information about the"
msgstr "정보"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__inherit_id
msgid "Inherit"
msgstr "상속"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__inherit_id
msgid "Inherited View"
msgstr "상속된 화면"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Inline"
msgstr "인라인"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Inner"
msgstr "내부"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Inner content"
msgstr "내부 컨텐츠"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Input Aligned"
msgstr "입력 정렬"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Input Type"
msgstr "입력 유형"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Inputs"
msgstr "입력"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a badge snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a blockquote snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a card snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a chart snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a progress bar snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a rating snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a share snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a text Highlight snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert an alert snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert an horizontal separator sippet."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Insert text styles like headers, bold, italic, lists, and fonts with\n"
"                            a simple WYSIWYG editor. Flexible and easy to use."
msgstr ""
"간단한 WYSIWYG 편집기를 사용하여 머리글, 굵게, 기울임꼴, 목록 및 글꼴과 같은 글자 스타일을 삽입합니다. 유연하고 사용하기 "
"쉽습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Inset"
msgstr "삽입"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Instagram"
msgstr "인스타그램"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_instagram
#: model:ir.model.fields,field_description:website.field_website__social_instagram
msgid "Instagram Account"
msgstr "인스타그램 계정"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Install"
msgstr "설치하기"

#. module: website
#: model:ir.model,name:website.model_base_language_install
msgid "Install Language"
msgstr "언어 설치"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Install new language"
msgstr "새 언어 설치하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Installed Applications"
msgstr "설치된 응용프로그램"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Installed Localizations / Account Charts"
msgstr "설치된 현지화/계정 차트"

#. module: website
#: model:ir.model.fields,help:website.field_website__theme_id
msgid "Installed theme"
msgstr "설치된 테마"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Installing \"%s\""
msgstr "\"%s\" 설치중"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Interaction History"
msgstr "상호 작용 기록"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Intuitive system"
msgstr "직관적인 시스템"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Invalid API Key. The following error was returned by Google:"
msgstr "잘못된 API 키입니다. Google에서 다음 오류를 반환했습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Iris Joe, CFO"
msgstr "Iris Joe, CFO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Iris, with her international experience, helps us easily understand the "
"numbers and improves them. She is determined to drive success and delivers "
"her professional acumen to bring the company to the next level."
msgstr ""
"아이리스는, 그녀의 국제적인 경험으로, 우리가 그 숫자들을 쉽게 이해하고 개선하도록 도와줍니다. 그녀는 성공을 위해 노력하고 있으며 "
"회사를 한 단계 도약시킬 수 있는 전문적 통찰력을 제공합니다."

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__website_indexed
msgid "Is Indexed"
msgstr "색인화 여부"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_module_module__is_installed_on_current_website
msgid "Is Installed On Current Website"
msgstr "현재 웹사이트에 설치할지 여부"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__is_mega_menu
msgid "Is Mega Menu"
msgstr "메가 메뉴 여부"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_partner__is_published
#: model:ir.model.fields,field_description:website.field_res_users__is_published
#: model:ir.model.fields,field_description:website.field_website_page__is_published
#: model:ir.model.fields,field_description:website.field_website_published_mixin__is_published
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__is_published
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__is_published
#: model_terms:ir.ui.view,arch_db:website.website_pages_tree_view
msgid "Is Published"
msgstr "게시 여부"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__is_visible
#: model:ir.model.fields,field_description:website.field_website_page__is_visible
msgid "Is Visible"
msgstr "표시 여부"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is after"
msgstr "다음"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is after or equal to"
msgstr "다음이거나 같음"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is before"
msgstr "이전"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is before or equal to"
msgstr "이전이거나 같음"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is between (included)"
msgstr "사이 (포함됨)"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__is_connected
msgid "Is connected ?"
msgstr "연결되었습니까?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is equal to"
msgstr "같음"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is greater than"
msgstr "보다 큼"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is greater than or equal to"
msgstr "같거나 큼"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is less than"
msgstr "보다 작음"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is less than or equal to"
msgstr "같거나 작음"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is not between (excluded)"
msgstr "사이가 아님(제외됨)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is not equal to"
msgstr "같지 않음"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#, python-format
msgid "Is not set"
msgstr "설정되지 않음"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#, python-format
msgid "Is set"
msgstr "설정됨"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"It appears you are in debug=assets mode, all theme customization options "
"require a page reload in this mode."
msgstr ""
"debug=assets 모드인 것 같습니다. 모든 테마 사용자 지정 옵션을 사용하려면 이 모드에서 페이지를 다시 로드해야 합니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid ""
"It appears your website is still using the old color system of\n"
"            Odoo 13.0 in some places. We made sure it is still working but\n"
"            we recommend you to try to use the new color system, which is\n"
"            still customizable."
msgstr ""
"당신의 웹사이트는 아직도 어떤 곳에서는 Odoo 13.0의 예전 \n"
"                컬러 시스템을 사용하고 있는 것 같습니다. \n"
"                계속 작동하지만 사용자 지정이 가능한 새 컬러 \n"
"                시스템을 사용하는 것이 좋습니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "It looks like your file is being called by"
msgstr "파일이 다음에서 호출된 것 같습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Item"
msgstr "항목"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Item 1"
msgstr "항목 1"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Item 2"
msgstr "항목 2"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options
msgid "Items per row"
msgstr "행당 항목"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.dynamic_snippet_carousel_options_template
msgid "Items per slide"
msgstr "슬라이드별 항목"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Jacket"
msgstr "재킷"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Jeans"
msgstr "청바지"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Job Offer"
msgstr "모집 부문"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
msgid "Join us and make your company a better place."
msgstr "우리와 함께 귀하의 회사를 더 발전시키기."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Keep empty to use default value"
msgstr "기본값을 사용하려면 비워 두십시오."

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_asset__key
#: model:ir.model.fields,field_description:website.field_ir_attachment__key
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__key
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__key
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__key
#: model:ir.model.fields,field_description:website.field_website_page__key
msgid "Key"
msgstr "키"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Keyboards"
msgstr "키보드"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Keyword"
msgstr "핵심어"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Keywords"
msgstr "핵심어"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Label"
msgstr "라벨"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model__website_form_label
msgid "Label for form action"
msgstr "양식 작업에 대한 꼬리표"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Labels Width"
msgstr "라벨 너비"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__lang_id
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Language"
msgstr "사용 언어"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Language Selector"
msgstr "언어 선택기"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__lang_id
msgid "Language from the website when visitor has been created"
msgstr "방문자가 생성될 때의 웹 사이트 언어"

#. module: website
#: model:ir.model,name:website.model_res_lang
#: model:ir.model.fields,field_description:website.field_res_config_settings__language_ids
#: model:ir.model.fields,field_description:website.field_website__language_ids
msgid "Languages"
msgstr "언어"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Languages available on your website"
msgstr "웹 사이트에서 사용 가능한 언어"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Laptops"
msgstr "랩탑"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Large"
msgstr "크게"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Last 7 Days"
msgstr "최근 7일"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Last Action"
msgstr "최근 작업"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__last_connection_datetime
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Last Connection"
msgstr "최근 접속"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "Last Feature"
msgstr "최근 기능"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Last Menu"
msgstr "최근 메뉴"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset____last_update
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment____last_update
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view____last_update
#: model:ir.model.fields,field_description:website.field_theme_website_menu____last_update
#: model:ir.model.fields,field_description:website.field_theme_website_page____last_update
#: model:ir.model.fields,field_description:website.field_website____last_update
#: model:ir.model.fields,field_description:website.field_website_configurator_feature____last_update
#: model:ir.model.fields,field_description:website.field_website_menu____last_update
#: model:ir.model.fields,field_description:website.field_website_page____last_update
#: model:ir.model.fields,field_description:website.field_website_rewrite____last_update
#: model:ir.model.fields,field_description:website.field_website_robots____last_update
#: model:ir.model.fields,field_description:website.field_website_route____last_update
#: model:ir.model.fields,field_description:website.field_website_snippet_filter____last_update
#: model:ir.model.fields,field_description:website.field_website_track____last_update
#: model:ir.model.fields,field_description:website.field_website_visitor____last_update
msgid "Last Modified on"
msgstr "최근 수정일"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Last Month"
msgstr "전 월"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
msgid "Last Page"
msgstr "최근 페이지"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__write_uid
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__write_uid
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__write_uid
#: model:ir.model.fields,field_description:website.field_theme_website_menu__write_uid
#: model:ir.model.fields,field_description:website.field_theme_website_page__write_uid
#: model:ir.model.fields,field_description:website.field_website__write_uid
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__write_uid
#: model:ir.model.fields,field_description:website.field_website_menu__write_uid
#: model:ir.model.fields,field_description:website.field_website_page__write_uid
#: model:ir.model.fields,field_description:website.field_website_rewrite__write_uid
#: model:ir.model.fields,field_description:website.field_website_robots__write_uid
#: model:ir.model.fields,field_description:website.field_website_route__write_uid
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__write_uid
#: model:ir.model.fields,field_description:website.field_website_visitor__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__write_date
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__write_date
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__write_date
#: model:ir.model.fields,field_description:website.field_theme_website_menu__write_date
#: model:ir.model.fields,field_description:website.field_theme_website_page__write_date
#: model:ir.model.fields,field_description:website.field_website__write_date
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__write_date
#: model:ir.model.fields,field_description:website.field_website_menu__write_date
#: model:ir.model.fields,field_description:website.field_website_page__write_date
#: model:ir.model.fields,field_description:website.field_website_rewrite__write_date
#: model:ir.model.fields,field_description:website.field_website_robots__write_date
#: model:ir.model.fields,field_description:website.field_website_route__write_date
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__write_date
#: model:ir.model.fields,field_description:website.field_website_visitor__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__last_visited_page_id
msgid "Last Visited Page"
msgstr "최근에 방문한 페이지"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Last Week"
msgstr "지난 주"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Last Year"
msgstr "지난 해"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__time_since_last_action
msgid "Last action"
msgstr "최근 작업"

#. module: website
#: code:addons/website/controllers/main.py:0
#, python-format
msgid "Last modified pages"
msgstr "최근 수정 페이지"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__last_connection_datetime
msgid "Last page view date"
msgstr "최근 페이지 조회 일자"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Latests news and case studies"
msgstr "최신 뉴스 및 사례 연구"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Layout"
msgstr "배치"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Layout Background"
msgstr "배치 배경"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Layout Background Color"
msgstr "배치 배경 색상"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "Learn more"
msgstr "추가 정보"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Left"
msgstr "왼쪽"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menu_image_menu
msgid "Left Menu"
msgstr "왼쪽 메뉴"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Legal"
msgstr "합법"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Legal Notice"
msgstr "법적 고지"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Legend"
msgstr "전설"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Let your customers follow <br/>and understand your process."
msgstr "고객이 프로세스를 따르고 <br/>이해하도록 합니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Let your customers log in to see their documents"
msgstr "고객이 로그인하여 문서를 확인할 수 있습니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Let's do it"
msgstr "그렇게 해 봅시다!"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Let's go!"
msgstr "시작합니다!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Library"
msgstr "라이브러리"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
msgid "Light"
msgstr "가볍게"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__limit
msgid "Limit"
msgstr "한도"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Limited customization"
msgstr "제한된 맞춤 설정"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Line"
msgstr "선"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline_options
msgid "Line Color"
msgstr "라인 색상"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Link"
msgstr "링크"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Link Anchor"
msgstr "링크 앵커"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Link Style"
msgstr "링크 스타일"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Link button"
msgstr "링크 버튼"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Link text"
msgstr "링크 문자"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "LinkedIn"
msgstr "링크드인"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_linkedin
#: model:ir.model.fields,field_description:website.field_website__social_linkedin
msgid "LinkedIn Account"
msgstr "링크드인 계정"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Linkedin"
msgstr "링크드인"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Links"
msgstr "링크"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Links Style"
msgstr "링크 스타일"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Links to other Websites"
msgstr "다른 웹사이트로 연결"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Little Icons"
msgstr "작은 아이콘"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_live_chat
msgid "Live Chat"
msgstr "실시간 채팅"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_kanban
msgid "Live Preview"
msgstr "실시간 미리보기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Livechat Widget"
msgstr "실시간 채팅 위젯"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#: code:addons/website/static/src/xml/website.background.video.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Loading..."
msgstr "불러오는 중..."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Logo"
msgstr "로고"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Logo Type"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Logo of MyCompany"
msgstr "회사 로고"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Logos"
msgstr "로고"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Long Text"
msgstr "긴 문자"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Low"
msgstr "낮음"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Magazine"
msgstr "잡지"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Main Course"
msgstr "주요 강좌"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__menu_id
msgid "Main Menu"
msgstr "주 메뉴"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#, python-format
msgid "Main actions"
msgstr "주요 활동"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Make sure billing is enabled"
msgstr "결제 기능이 활성화되어 있는지 확인하세요"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"Make sure to wait if errors keep being shown: sometimes enabling an API "
"allows to use it immediately but Google keeps triggering errors for a while"
msgstr ""
"오류가 계속 표시되는 경우에는 반드시 기다려주시기 바랍니다. API가 활성화되면 즉시 사용이 가능하지만 Google에서는 잠시동안 오류 "
"정보가 계속됩니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Make sure your settings are properly configured:"
msgstr "설정 항목이 올바르게 구성되었는지 확인하십시오."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Manage Pages"
msgstr "관리 페이지"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "Manage Your Pages"
msgstr "귀하의 페이지 관리"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Manage Your Website Pages"
msgstr "웹사이트 페이지 관리"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Manage this page"
msgstr "해당 페이지 관리"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Maps JavaScript API"
msgstr "JavaScript API를 매핑합니다"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Maps Static API"
msgstr "고정 API를 매핑합니다"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Mark Text"
msgstr "텍스트 표시"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Marked Fields"
msgstr "표시된 필드"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "Marker style"
msgstr "마커 스타일"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Marketplace"
msgstr "마켓 플레이스"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Masonry"
msgstr "Masonry"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Measurement ID"
msgstr "측정 ID"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "Media"
msgstr "매체"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Media heading"
msgstr "미디어 제목"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Medium"
msgstr "중간"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Mega Menu"
msgstr "메가 메뉴"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__mega_menu_classes
msgid "Mega Menu Classes"
msgstr "메가 메뉴 클래스"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__mega_menu_content
msgid "Mega Menu Content"
msgstr "메가 메뉴 내용"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Men"
msgstr "남성"

#. module: website
#: code:addons/website/models/website.py:0
#: model:ir.model.fields,field_description:website.field_website_menu__name
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Menu"
msgstr "메뉴"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__menu_company
msgid "Menu Company"
msgstr "회사 메뉴"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menu_image_menu
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Menu Item %s"
msgstr "메뉴 항목 %s"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Menu Label"
msgstr "메뉴 이름표"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__menu_sequence
msgid "Menu Sequence"
msgstr "메뉴 수너"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_menu__copy_ids
msgid "Menu using a copy of me"
msgstr "내 사본을 이용한 메뉴"

#. module: website
#: code:addons/website/models/website.py:0
#: model:ir.ui.menu,name:website.menu_website_menu_list
#, python-format
msgid "Menus"
msgstr "메뉴"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
msgid "Messages"
msgstr "메시지"

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "Metadata"
msgstr "메타데이타"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Mich Stark, COO"
msgstr "Mich Stark, COO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Mich loves taking on challenges. With his multi-year experience as "
"Commercial Director in the software industry, Mich has helped the company to"
" get where it is today. Mich is among the best minds."
msgstr ""
"Mich은 도전하는 것을 좋아합니다. 소프트웨어 업계에서 커머셜 디렉터로 다년간 근무한 경험이 있는 Mich는 이 회사가 현재 위치에 "
"오도록 도왔습니다. Mich은 가장 좋은 사람들 중 한 명이에요."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Middle"
msgstr "가운데"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Min-Height"
msgstr "최소-높이"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Minimalist"
msgstr "미니멀리스트"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#, python-format
msgid "Minutes"
msgstr "분"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#: model:ir.model.fields,field_description:website.field_website_visitor__mobile
#, python-format
msgid "Mobile"
msgstr "모바일"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Mobile Alignment"
msgstr "모바일 얼라인먼트"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Mobile menu"
msgstr "모바일 메뉴"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/mobile_view.js:0
#, python-format
msgid "Mobile preview"
msgstr "모바일 미리 보기"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__mode
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Mode"
msgstr "모드"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__model
msgid "Model"
msgstr "모델"

#. module: website
#: model:ir.model,name:website.model_ir_model_data
#: model:ir.model.fields,field_description:website.field_website_page__model_data_id
msgid "Model Data"
msgstr "모델 데이터"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__model_name
msgid "Model name"
msgstr "모델명"

#. module: website
#: model:ir.model,name:website.model_ir_model
msgid "Models"
msgstr "모델"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_updated
msgid "Modified Architecture"
msgstr "수정된 아키텍쳐"

#. module: website
#: model:ir.model,name:website.model_ir_module_module
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__module_id
msgid "Module"
msgstr "모듈"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Monitor Google Search results data"
msgstr "Google 검색 결과 데이터 모니터링"

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_0
msgid ""
"Monitor your visitors while they are browsing your website with the Odoo "
"Social app. Engage with them in just a click using a live chat request or a "
"push notification. If they have completed one of your forms, you can send "
"them an SMS, or call them right away while they are browsing your website."
msgstr ""
"Odoo 소셜 앱으로 웹 사이트를 검색하는 동안 방문자를 모니터링합니다. 실시간 채팅 요청 또는 푸시 알림을 사용하여 클릭 한 번으로 "
"참여하십시오. 양식 중 하나를 완료한 경우 사용자에게 SMS를 보내거나 웹 사이트를 검색하는 동안 즉시 전화를 걸 수 있습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Monitors"
msgstr "모니터"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_color_blocks_2
msgid "More Details"
msgstr "추가 세부사항"

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_4
msgid ""
"More than 90 shapes exist and their colors are picked to match your Theme."
msgstr "90개 이상의 도형이 있으며 테마에 맞추어 색상이 자동으로 선택됩니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "More than one group has been set on the view."
msgstr "화면에 둘 이상의 그룹이 설정되었습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Mosaic"
msgstr "모자이크"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Most searched topics related to your keyword, ordered by importance"
msgstr "중요도로 정렬된 핵심어와 관련하여 가장 많이 검색된 주제"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Mouse"
msgstr "마우스"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move Backward"
msgstr "뒤로 이동"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move Forward"
msgstr "이전으로 이동"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Move to first"
msgstr "첫 번째로 이동"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Move to last"
msgstr "끝으로 이동"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Move to next"
msgstr "다음으로 이동"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Move to previous"
msgstr "이전으로 이동"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Multi Menus"
msgstr "다중 메뉴"

#. module: website
#: model:ir.model,name:website.model_website_multi_mixin
msgid "Multi Website Mixin"
msgstr "다중 웹사이트 혼합"

#. module: website
#: model:ir.model,name:website.model_website_published_multi_mixin
msgid "Multi Website Published Mixin"
msgstr "다중 웹사이트 게시물 혼합"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__group_multi_website
#: model:res.groups,name:website.group_multi_website
msgid "Multi-website"
msgstr "다중 웹사이트"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Multimedia"
msgstr "멀티미디어"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Multiple Checkboxes"
msgstr "다중 확인란"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_qweb
msgid "Multiple tree exists for this view"
msgstr "이 화면에는 트리가 여러 개 있습니다"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_page
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid "My Company"
msgstr "내 회사"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.option_header_brand_name
#: model_terms:ir.ui.view,arch_db:website.option_header_off_canvas
msgid "My Website"
msgstr "내 웹사이트"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "MyCompany"
msgstr "내 회사"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__name
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__name
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__name
#: model:ir.model.fields,field_description:website.field_theme_website_menu__name
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__name
#: model:ir.model.fields,field_description:website.field_website_rewrite__name
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__name
#: model:ir.model.fields,field_description:website.field_website_visitor__name
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
#: model_terms:ir.ui.view,arch_db:website.menu_search
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
#, python-format
msgid "Name"
msgstr "이름"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Name (A-Z)"
msgstr "제품명 순 (A-Z)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Name (Z-A)"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Name and favicon of your website"
msgstr "귀하의 웹사이트 이름 및 즐겨찾기 아이콘"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_qweb
msgid "Name, id or key"
msgstr "이름, id 또는 키"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Narrow"
msgstr "좁게"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Navbar"
msgstr "검색바"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Need to pick up your order at one of our stores? Discover the nearest to "
"you."
msgstr "매장에서 직접 수령하고 싶으십니까? 가까운 매장을 찾아보세요."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Network Advertising Initiative opt-out page"
msgstr "Network Advertising Initiative 옵트 아웃 페이지"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Networks"
msgstr "네트워크"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "New"
msgstr "신규"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid ""
"New Google Analytics accounts and keys are now using Google Analytics 4 "
"which, for now, can't be integrated/embed in external websites."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "New Page"
msgstr "새 페이지"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_menu__new_window
#: model:ir.model.fields,field_description:website.field_website_menu__new_window
msgid "New Window"
msgstr "새 창"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "New collection"
msgstr "새 컬렉션"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup
msgid "New customer"
msgstr "신규 고객"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "New page"
msgstr "새 페이지"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_news
msgid "News"
msgstr "신규"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Newsletter"
msgstr "뉴스레터"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Newsletter Popup"
msgstr "뉴스레터 팝업"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Next"
msgstr "다음"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "No Animation"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "No Slide Effect"
msgstr "슬라이드 효과 없음"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_widget.xml:0
#, python-format
msgid "No Url"
msgstr "URL 없음"

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitors_action
msgid "No Visitors yet!"
msgstr "아직 방문자가 없습니다!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "No customization"
msgstr "맞춤 설정 없음"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "No matching record !"
msgstr "일치하는 레코드가 없습니다!"

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitor_page_action
msgid "No page views yet for this visitor"
msgstr "이 방문자가 본 페이지가 없습니다"

#. module: website
#: model_terms:ir.actions.act_window,help:website.visitor_partner_action
msgid "No partner linked for this visitor"
msgstr "이 방문자와 연결된 파트너가 없습니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "No result found, broaden your search."
msgstr "검색 결과가 없습니다. 검색 범위를 넓혀서 다시 시도하십시오."

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_searchbar/000.xml:0
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
#, python-format
msgid "No results found for '"
msgstr "' 에 대한 결과를 찾을 수 없습니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_searchbar/000.xml:0
#, python-format
msgid "No results found. Please try another search."
msgstr "검색 결과가 없습니다. 다른 검색을 시도하십시오."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "No support"
msgstr "지원 없음"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_header_brand
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "None"
msgstr "없음"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Normal"
msgstr "일반"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Not SEO optimized"
msgstr "SEO 최적화 안됨"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Not in main menu"
msgstr "주 메뉴에 없음"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Not indexed"
msgstr "색인화 안됨"

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_3
msgid ""
"Not only can you search for royalty-free illustrations, their colors are "
"also converted so that they always fit your Theme."
msgstr "저작권이 없는 일러스트를 검색할 수 있을 뿐만 아니라 테마에 잘 어울리도록 색상을 자동으로 변환할 수 있습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Not published"
msgstr "게시 안됨"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Not tracked"
msgstr "추적 안됨"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Note that some third-party services may install additional cookies on your "
"browser in order to identify you."
msgstr "일부 타사 서비스는 사용자를 식별하기 위해 브라우저에 추가 쿠키를 설치할 수 있습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Note: To hide this page, uncheck it from the top Customize menu."
msgstr "참고 : 이 페이지를 숨기려면 상단 사용자 지정 메뉴에서 이 페이지 선택을 해제하십시오."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Nothing"
msgstr "아무 것도 없음"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Number"
msgstr "숫자"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_language_count
msgid "Number of languages"
msgstr "언어 수"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "OR"
msgstr "또는"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Odoo Logo"
msgstr "Odoo 로고"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Odoo Menu"
msgstr "Odoo 메뉴"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Odoo Version"
msgstr "Odoo 버전"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Off-Canvas"
msgstr "오프 캔버스"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Office audio"
msgstr "사무용 오디오"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Office screens"
msgstr "사무용 스크린"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Offline"
msgstr "오프라인"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "Ok"
msgstr "확인"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "Ok, never show me this again"
msgstr "알겠습니다. 다시 보여주지 않아도 됩니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "On Click"
msgstr "클릭할 때"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "On Exit"
msgstr "종료할 때"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "On Hover"
msgstr "위에 띄울 때"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "On Success"
msgstr "성공 시"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "On Website"
msgstr "웹사이트 내"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website__auth_signup_uninvited__b2b
msgid "On invitation"
msgstr "초대한 사람만"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"Once the selection of available websites by domain is done, you can filter "
"by country group."
msgstr "도메인별로 사용 ​​가능한 웹 사이트를 선택하면 국가 그룹별로 필터링 할 수 있습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid ""
"Once the user closes the popup, it won't be shown again for that period of "
"time."
msgstr "사용자가 팝업을 닫으면 해당 기간 동안 다시 표시되지 않습니다."

#. module: website
#: code:addons/website/models/website_configurator_feature.py:0
#, python-format
msgid ""
"One and only one of the two fields 'page_view_id' and 'module_id' should be "
"set"
msgstr "'page_view_id' 및 'module_id' 두 필드 중 하나만 설정할 수 있습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Online"
msgstr "온라인"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__mode
msgid ""
"Only applies if this view inherits from an other one (inherit_id is not False/Null).\n"
"\n"
"* if extension (default), if this view is requested the closest primary view\n"
"is looked up (via inherit_id), then all views inheriting from it with this\n"
"view's model are applied\n"
"* if primary, the closest primary view is fully resolved (even if it uses a\n"
"different model than this one), then this view's inheritance specs\n"
"(<xpath/>) are applied, and the result is used as if it were this view's\n"
"actual arch.\n"
msgstr ""
"이 보기가 다른 보기에서 상속된 경우에만 적용됩니다(inherit_id는 False/Null이 아님).\n"
"\n"
"* 확장자(기본값)가 요청된 경우, 이 보기가 가장 가까운 주 보기를 조회할 경우(상속자_id를 통해), 이 보기에서 상속하는 모든 보기가 적용됩니다.\n"
"* 기본 보기가 완전히 해결된 경우(이 보기와 다른 모형을 사용하더라도) \n"
"이 보기의 상속 사양(<xpath/>)이 적용되고\n"
" 결과는 이 보기의 실제 아치인 것처럼 사용됩니다.\n"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Open Source ERP"
msgstr "오픈 소스 ERP"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Optimize SEO"
msgstr "SEO 최적화"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Optimize SEO of this page"
msgstr "이 페이지의 SEO 최적화"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Option 1"
msgstr "선택 사항 1"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Option 2"
msgstr "선택 사항 2"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Option 3"
msgstr "선택 사항 3"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Optional"
msgstr "선택 사항"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Order by"
msgstr "주문자"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Order now"
msgstr "지금 주문"

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "Other Information:"
msgstr "기타 정보 :"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Our Company"
msgstr "우리 회사"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid "Our References"
msgstr "우리의 참고 문헌"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Our seminars and trainings for you"
msgstr "고객 대상 세미나 및 교육"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "Our team"
msgstr "당사 팀"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "Our team will message you back as soon as possible."
msgstr "저희 팀이 최대한 빠른 시일 내에 연락 드리겠습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Outline"
msgstr "외곽선"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Outset"
msgstr "시초"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "Outstanding images"
msgstr "뛰어난 이미지"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Over The Content"
msgstr "컨텐츠 개요"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Paddings"
msgstr "패딩"

#. module: website
#: code:addons/website/models/website.py:0
#: code:addons/website/models/website.py:0
#: model:ir.model,name:website.model_website_page
#: model:ir.model.fields,field_description:website.field_ir_ui_view__page_ids
#: model:ir.model.fields,field_description:website.field_theme_website_menu__page_id
#: model:ir.model.fields,field_description:website.field_website_page__page_ids
#: model:ir.model.fields,field_description:website.field_website_track__page_id
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#, python-format
msgid "Page"
msgstr "페이지"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "Page <b>%s</b> contains a link to this page"
msgstr "<b>%s</b> 페이지에는 이 페이지에 대한 링크가 포함되어 있습니다."

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "Page <b>%s</b> is calling this file"
msgstr "<b>%s</b> 페이지는 이 파일을 호출합니다."

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_page__website_indexed
msgid "Page Indexed"
msgstr "페이지 색인 생성"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Page Layout"
msgstr "페이지 배치"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "Page Name"
msgstr "페이지 명"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Page Properties"
msgstr "페이지 속성"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Page Title"
msgstr "페이지 제목"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields,field_description:website.field_website_page__url
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
#, python-format
msgid "Page URL"
msgstr "페이지 URL"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__page_view_id
msgid "Page View"
msgstr "페이지 조회수"

#. module: website
#: model:ir.actions.act_window,name:website.website_visitor_view_action
#: model:ir.model.fields,field_description:website.field_website_visitor__visitor_page_count
#: model:ir.ui.menu,name:website.menu_visitor_view_menu
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Page Views"
msgstr "페이지 조회수"

#. module: website
#: model:ir.actions.act_window,name:website.website_visitor_page_action
msgid "Page Views History"
msgstr "페이지 조회수 이력"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Page Visibility"
msgstr "페이지 표시 가능"

#. module: website
#: model:ir.model.fields,help:website.field_website_configurator_feature__iap_page_code
msgid ""
"Page code used to tell IAP website_service for which page a snippet list "
"should be generated"
msgstr "스니펫 목록이 생성되어야 하는 페이지를 IAP website_service에 알리는 데 사용되는 코드입니다."

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_page__copy_ids
msgid "Page using a copy of me"
msgstr "내 사본을 사용하는 페이지"

#. module: website
#. openerp-web
#: code:addons/website/models/website.py:0
#: code:addons/website/models/website.py:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: model:ir.ui.menu,name:website.menu_website_pages_list
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
#, python-format
msgid "Pages"
msgstr "페이지"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Pagination"
msgstr "쪽수 매기기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Pants"
msgstr "바지"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid ""
"Paragraph text. Lorem <b>ipsum dolor sit amet</b>, consectetur adipiscing "
"elit. <i>Integer posuere erat a ante</i>."
msgstr ""
"Paragraph text. Lorem <b>ipsum dolor sit amet</b>, consectetur adipiscing "
"elit. <i>Integer posuere erat a ante</i>."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid ""
"Paragraph with <strong>bold</strong>, <span class=\"text-"
"muted\">muted</span> and <em>italic</em> texts"
msgstr ""
"<strong>굵게</strong>, <span class=\"text-muted\">옅게</span> 및 <em>기울임체</em>로 된"
" 단락"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Paragraph."
msgstr "절."

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_menu__parent_id
msgid "Parent"
msgstr "상위"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__parent_id
msgid "Parent Menu"
msgstr "상위 메뉴"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__parent_path
msgid "Parent Path"
msgstr "상위 경로"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__partner_id
msgid "Partner of the last logged in user."
msgstr "마지막으로 로그인한 사용자의 파트너"

#. module: website
#: model:ir.model.fields,help:website.field_website__partner_id
msgid "Partner-related data of the user"
msgstr "사용자의 협력사 관련 데이터"

#. module: website
#: model:ir.actions.act_window,name:website.visitor_partner_action
msgid "Partners"
msgstr "협력사"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Password"
msgstr "암호"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__path
msgid "Path"
msgstr "경로"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Pattern"
msgstr "패턴"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Pay"
msgstr "지불"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Phone Number"
msgstr "전화번호"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Phones"
msgstr "전화"

#. module: website
#: model:ir.actions.act_window,name:website.theme_install_kanban_action
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Pick a Theme"
msgstr "테마 선택하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Pie"
msgstr "파이"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Pill"
msgstr "필"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Pills"
msgstr "필"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Placeholder"
msgstr "자리 표시자"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Places API"
msgstr "API 배치"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Plain"
msgstr "평원"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/res_config_settings.js:0
#, python-format
msgid "Please confirm"
msgstr "확인해주세요"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "Please fill in the form correctly."
msgstr "양식을 정확하게 작성하십시오."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "Points of sale"
msgstr "POS"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Popup"
msgstr "팝업"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Portfolio"
msgstr "포트폴리오"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
msgid "Position"
msgstr "영역"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Post heading"
msgstr "제목 게시"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Postcard"
msgstr "엽서"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Preferences"
msgstr "환경 설정"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__prepend
msgid "Prepend"
msgstr "준비"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Preset"
msgstr "사전 설정"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "Press"
msgstr "입력"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/view_hierarchy.js:0
#, python-format
msgid "Press %s for next %s"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Preview"
msgstr "미리보기"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Previous"
msgstr "이전"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_prev
msgid "Previous View Architecture"
msgstr "이전 화면 구조"

#. module: website
#: model:website.configurator.feature,name:website.feature_page_pricing
#: model_terms:ir.ui.view,arch_db:website.pricing
msgid "Pricing"
msgstr "가격 책정"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Primary"
msgstr "기본"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Primary Style"
msgstr "기본 스타일"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Printers"
msgstr "프린트"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__priority
msgid "Priority"
msgstr "우선 순위"

#. module: website
#: code:addons/website/models/website.py:0
#: model:website.configurator.feature,name:website.feature_page_privacy_policy
#: model_terms:ir.ui.view,arch_db:website.privacy_policy
#, python-format
msgid "Privacy Policy"
msgstr "개인 정보 보호 정책"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog_add_product_widget
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Product"
msgstr "품목"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Products"
msgstr "품목"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Professional"
msgstr "전문용"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Professional themes"
msgstr "전문가용 테마"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "Profile"
msgstr "프로필"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Progress Bar"
msgstr "진행 표시 줄"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Progress Bar Color"
msgstr "진행 표시 줄 색상"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Progress Bar Style"
msgstr "진행 표시 줄 스타일"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Progress Bar Weight"
msgstr "진행 표시 줄 두께"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Projectors"
msgstr "프로젝터"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Promote"
msgstr "홍보"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Promote page on the web"
msgstr "웹페이지에 홍보 페이지"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Promotions"
msgstr "프로모션"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Public"
msgstr "공개"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__partner_id
msgid "Public Partner"
msgstr "일반 파트너"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__user_id
msgid "Public User"
msgstr "일반 사용자"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/button.js:0
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Publish"
msgstr "발행"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_career
msgid "Publish job offers and let people apply"
msgstr "채용 공고를 게시하고 사람들이 지원하도록 허용"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_event
msgid "Publish on-site and online events"
msgstr "현장 및 온라인 이벤트 게시하기"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/button.js:0
#: code:addons/website/static/src/js/backend/button.js:0
#: model_terms:ir.ui.view,arch_db:website.one_page_line
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.publish_short
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
#, python-format
msgid "Published"
msgstr "게시 됨"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields,field_description:website.field_website_page__date_publish
#, python-format
msgid "Publishing Date"
msgstr "게시 날짜"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Pulse"
msgstr "펄스"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Purpose"
msgstr "목적"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_highlight
msgid "Put the focus on what you have to say!"
msgstr "할 말에 집중하세요!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating
msgid "Quality"
msgstr "품질"

#. module: website
#: model:ir.model,name:website.model_ir_qweb
msgid "Qweb"
msgstr "Qweb"

#. module: website
#: model:ir.model,name:website.model_ir_qweb_field_contact
msgid "Qweb Field Contact"
msgstr "Qweb 연락처 필드"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Radar"
msgstr "레이더"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Radio Buttons"
msgstr "단일 선택 버튼"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Rating"
msgstr "평가"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Re-order"
msgstr "재주문"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Ready to build the"
msgstr "제작할 준비가 되셨나요?"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Recipient Email"
msgstr "이메일 수신인"

#. module: website
#: model:ir.model,name:website.model_ir_rule
msgid "Record Rule"
msgstr "레코드 규칙"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Redirect"
msgstr "리디렉션"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Redirect Old URL"
msgstr "기존 URL 리디렉션"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Redirect to URL in a new tab"
msgstr "새 탭에서 URL로 리디렉션"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
msgid "Redirection Type"
msgstr "리디렉션 유형"

#. module: website
#: model:ir.ui.menu,name:website.menu_website_rewrite
msgid "Redirects"
msgstr "리디렉션"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_rewrite_form
msgid "Refresh route's list"
msgstr "경로 목록 새로 고침"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Regular"
msgstr "정기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_form_view
msgid "Related Menu Items"
msgstr "관련 메뉴 항목"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__menu_ids
msgid "Related Menus"
msgstr "관련 메뉴"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__page_id
msgid "Related Page"
msgstr "관련 페이지"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Related keywords"
msgstr "관련 키워드"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Remember information about the preferred look or behavior of the website, "
"such as your preferred language or region."
msgstr "선호하는 언어 또는 지역과 같은 웹 사이트의 기본 설정 모양 또는 동작에 대한 정보를 기억하십시오."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__remove
#, python-format
msgid "Remove"
msgstr "제거"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Remove Row"
msgstr "행 삭제"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Remove Serie"
msgstr "시리즈 삭제"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Remove Slide"
msgstr "슬라이드 삭제"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Remove Tab"
msgstr "탭 제거"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Remove all"
msgstr "전체 제거"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_kanban
msgid "Remove theme"
msgstr "테마 제거"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "Rename Page To:"
msgstr "페이지 이름 바꾸기 :"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__replace
msgid "Replace"
msgstr "대체"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_embed_code
msgid "Replace this with your own HTML code"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Required"
msgstr "요청함"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Reset templates"
msgstr "서식 초기화"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Reset to initial version (hard reset)."
msgstr "초기 버전으로 재설정 (하드 리셋)."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Resources"
msgstr "자원"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#, python-format
msgid "Respecting your privacy is our priority."
msgstr "우리는 당신의 사생활을 존중하는 것이 우선입니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Restore previous version (soft reset)."
msgstr "이전 버전으로 복구(소프트 리셋)"

#. module: website
#: model:ir.model.fields,help:website.field_res_partner__website_id
#: model:ir.model.fields,help:website.field_res_users__website_id
#: model:ir.model.fields,help:website.field_website_multi_mixin__website_id
#: model:ir.model.fields,help:website.field_website_page__website_id
#: model:ir.model.fields,help:website.field_website_published_multi_mixin__website_id
#: model:ir.model.fields,help:website.field_website_snippet_filter__website_id
msgid "Restrict publishing to this website."
msgstr "이 웹 사이트로의 게시를 제한합니다."

#. module: website
#: model:res.groups,name:website.group_website_publisher
msgid "Restricted Editor"
msgstr "제한된 편집자"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__ir_ui_view__visibility__restricted_group
msgid "Restricted Group"
msgstr "제한된 그룹"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_rewrite_list
msgid "Rewrite"
msgstr "고쳐 쓰기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Right"
msgstr "오른쪽"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menu_image_menu
msgid "Right Menu"
msgstr "오른쪽 메뉴"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Ripple Effect"
msgstr "파급 효과"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Road"
msgstr "로드"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "RoadMap"
msgstr "로드맵"

#. module: website
#: code:addons/website/models/res_config_settings.py:0
#: model:ir.model.fields,field_description:website.field_website__robots_txt
#, python-format
msgid "Robots.txt"
msgstr "Robots.txt"

#. module: website
#: model:ir.model,name:website.model_website_robots
msgid "Robots.txt Editor"
msgstr "Robots.txt 편집기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"Robots.txt: This file tells to search engine crawlers which pages or files "
"they can or can't request from your site.<br/>"
msgstr ""
"Robots.txt : 이 파일은 검색 엔진 크롤러가 사용자의 사이트에서 요청할 수 있거나 요청할 수 없는 페이지 또는 파일을 "
"알려줍니다.<br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Rotate In"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Rotate In-Down-Left"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Rotate In-Down-Right"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_widgets
msgid "Round Corners"
msgstr "둥근 모서리"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Rounded"
msgstr "둥글게"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Rounded Miniatures"
msgstr "둥근 미니어처"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__route_id
#: model:ir.model.fields,field_description:website.field_website_route__path
msgid "Route"
msgstr "경로"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "SEO"
msgstr "검색엔진 최적화"

#. module: website
#: model:ir.model,name:website.model_website_seo_metadata
msgid "SEO metadata"
msgstr "검색 엔진 최적화 메타 데이터"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__is_seo_optimized
#: model:ir.model.fields,field_description:website.field_website_page__is_seo_optimized
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__is_seo_optimized
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "SEO optimized"
msgstr "SEO 최적화"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Same as desktop"
msgstr "데스크톱과 동일"

#. module: website
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "Sample %s"
msgstr "예제 %s"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Sample Icons"
msgstr "예제 아이콘"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Satellite"
msgstr "위성"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/dashboard.js:0
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/js/menu/content.js:0
#: code:addons/website/static/src/js/menu/seo.js:0
#: code:addons/website/static/src/snippets/s_embed_code/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: model_terms:ir.ui.view,arch_db:website.view_edit_robots
#, python-format
msgid "Save"
msgstr "저장"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Save & Reload"
msgstr "저장 및 다시 불러오기"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Save & copy"
msgstr "저장 및 복사"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Save the block to use it elsewhere"
msgstr "블록을 저장하여 다른 곳에서 사용"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "Score"
msgstr "점수"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Screens"
msgstr "화면"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_module_module__image_ids
msgid "Screenshots"
msgstr "스크린샷"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll"
msgstr "스크롤"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll Effect"
msgstr "스크롤 효과"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.option_footer_scrolltop
msgid "Scroll To Top"
msgstr "제일 위로 스크롤"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll Top Button"
msgstr "위로 스크롤 버튼"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll down button"
msgstr "아래로 스크롤 버튼"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Scroll down to next section"
msgstr "다음 섹션으로 스크롤"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.view_view_qweb
#: model_terms:ir.ui.view,arch_db:website.website_search_box
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Search"
msgstr "검색"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
msgid "Search Menus"
msgstr "검색 메뉴"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
msgid "Search Redirect"
msgstr "리디렉션 검색"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
msgid "Search Results"
msgstr "검색 결과"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Search Visitor"
msgstr "방문자 검색"

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_1
msgid ""
"Search in the media dialogue when you need photos to illustrate your "
"website. Odoo's integration with Unsplash, featuring millions of royalty "
"free and high quality photos, makes it possible for you to get the perfect "
"picture, in just a few clicks."
msgstr ""
"웹 사이트를 설명하는 데 필요한 사진이 필요한 경우 미디어 대화 상자에서 검색하십시오. 수백만 장의 로열티 무료 및 고품질 사진을 "
"특징으로 하는 Unsplash와 Odoo의 통합 덕분에 몇 번의 클릭만으로 완벽한 사진을 얻을 수 있습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_searchbar
msgid "Search on our website"
msgstr "웹사이트에서 검색"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Search within"
msgstr "에서 검색"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_search_box
msgid "Search..."
msgstr "검색..."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Second Feature"
msgstr "두 번째 기능"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Second Menu"
msgstr "두 번째 메뉴"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "Second feature"
msgstr "두 번째 기능"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Second list of Features"
msgstr "기능의 두 번째 목록"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Secondary"
msgstr "2차"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Secondary Style"
msgstr "2차 스타일"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#, python-format
msgid "Seconds"
msgstr "두번 째"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
msgid "Section Subtitle"
msgstr "영역 소제목"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Select a Menu"
msgstr "메뉴 선택"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Select a website to load its settings."
msgstr "설정을 불러올 웹사이트를 선택하십시오."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Select an image for social share"
msgstr "소셜 공유를 위한 이미지 선택"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Select and delete blocks <br/>to remove some steps."
msgstr "일부 단계를 제거하려면 블록을 선택하고 삭제<br/>합니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Select and delete blocks to remove features."
msgstr "기능을 제거하려면 블록을 선택하고 삭제하십시오."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Select one font on"
msgstr "글꼴 하나 선택하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Select the Website to Configure"
msgstr "구성할 웹사이트 선택하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Selection"
msgstr "선택"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_shop
msgid "Sell more with an eCommerce"
msgstr "e커머스를 활용하여 매출을 증대시킵니다"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Send Email"
msgstr "이메일 보내기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "Send us a message"
msgstr "우리에게 메시지 보내기"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__seo_name
#: model:ir.model.fields,field_description:website.field_website_page__seo_name
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__seo_name
msgid "Seo name"
msgstr "Seo 이름"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "Separate email addresses with a comma."
msgstr "이메일 주소는 쉼표로 구분하십시오"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Separated link"
msgstr "분리된 링크"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Separator"
msgstr "구분자"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__sequence
#: model:ir.model.fields,field_description:website.field_theme_website_menu__sequence
#: model:ir.model.fields,field_description:website.field_website__sequence
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__sequence
#: model:ir.model.fields,field_description:website.field_website_menu__sequence
#: model:ir.model.fields,field_description:website.field_website_page__priority
#: model:ir.model.fields,field_description:website.field_website_rewrite__sequence
msgid "Sequence"
msgstr "순서"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Serve font from Google servers"
msgstr "Google 서버에서 글꼴 제공"

#. module: website
#: model:ir.model,name:website.model_ir_actions_server
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__action_server_id
msgid "Server Action"
msgstr "서버작업"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Service"
msgstr "서비스"

#. module: website
#: model:website.configurator.feature,name:website.feature_page_our_services
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.our_services
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Services"
msgstr "서비스"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Session &amp; Security"
msgstr "세션 및 보안"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_configuration
#: model:ir.ui.menu,name:website.menu_website_website_settings
msgid "Settings"
msgstr "설정"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Settings on this page will apply to this website"
msgstr "이 페이지의 설정은 이 웹 사이트에 적용됩니다"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Shadow"
msgstr "그림자"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Shadows"
msgstr "음영"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Shake"
msgstr "흔들림"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: model_terms:ir.ui.view,arch_db:website.s_share
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#, python-format
msgid "Share"
msgstr "공유"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_elearning
msgid "Share knowledge publicly or for a fee"
msgstr "공개적으로 또는 유료로 지식 공유하기"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_success_stories
msgid "Share your best case studies"
msgstr "모범 사례 공유"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Shoes"
msgstr "신발"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_shop
msgid "Shop"
msgstr "쇼핑"

#. module: website
#: model:ir.model.fields,help:website.field_website__auto_redirect_lang
msgid "Should users be redirected to their browser's language"
msgstr "사용자가 브라우저 언어로 리디렉션되어야 함"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__customize_show
msgid "Show As Optional Inherit"
msgstr "선택사항으로 상속 보기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Show Header"
msgstr "머리글 표시하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Show Message"
msgstr "메시지 표시하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Show Message and hide countdown"
msgstr "메시지 표시 및 카운트다운 숨기기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Show Message and keep countdown"
msgstr "메시지 표시 및 카운트다운 계속 보이기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Show Sign In"
msgstr "로그인 표시하기"

#. module: website
#: model:ir.actions.act_window,name:website.action_show_viewhierarchy
msgid "Show View Hierarchy"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Show in Top Menu"
msgstr "최상위 메뉴에 표시하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_qweb
msgid "Show inactive views"
msgstr "비활성 뷰 표시"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Show on"
msgstr "표시하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Show reCaptcha Policy"
msgstr "보안 문자 정책 보기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Sidebar"
msgstr "사이드바"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Sign in"
msgstr "로그인"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields.selection,name:website.selection__ir_ui_view__visibility__connected
#, python-format
msgid "Signed In"
msgstr "로그인함"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"Sitemap.xml: Help search engine crawlers to find out what pages are present "
"and which have recently changed, and to crawl your site accordingly. This "
"file is automatically generated by Odoo."
msgstr ""
"Sitemap.xml : 엔진 크롤러를 검색하여 현재 존재하는 페이지와 최근에 변경된 페이지를 확인하고 그에 따라 사이트를 탐색할 수 "
"있도록 도와줍니다. 이 파일은 Odoo에서 자동으로 생성됩니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Size"
msgstr "사이즈"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Skip and start from scratch"
msgstr "건너뛰고 처음부터 다시 시작"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slide"
msgstr "슬라이드"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Slide Down"
msgstr "슬라이드 아래로"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slide Hover"
msgstr "슬라이드 띄우기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Slide Left"
msgstr "슬라이드 왼쪽"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Slide Right"
msgstr "슬라이드 오른쪽"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Slide Up"
msgstr "슬라이드 위로"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slideout Effect"
msgstr "슬라이드아웃 효과"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.dynamic_snippet_carousel_options_template
msgid "Slider Speed"
msgstr "슬라이더 속도"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Slideshow"
msgstr "슬라이드쇼"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slogan"
msgstr "슬로건"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Small"
msgstr "작게"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
msgid "Small Header"
msgstr "작은 머리글"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid ""
"Small text. Lorem <b>ipsum dolor sit amet</b>, consectetur adipiscing elit. "
"<i>Integer posuere erat a ante</i>."
msgstr ""
"작은 글자. Lorem <b>ipsum dolor sit amet</b>, consectetur adipiscing elit. "
"<i>Integer posuere erat a ante</i>."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Smartphones"
msgstr "스마트폰"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Social Media"
msgstr "소셜미디어"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Social Preview"
msgstr "소셜 미리보기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_line_widgets
msgid "Solid"
msgstr "고체"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Some Users"
msgstr "일부 사용자"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Something else here"
msgstr "다른 것"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Something went wrong."
msgstr "문제가 발생했습니다"

#. module: website
#: code:addons/website/controllers/main.py:0
#, python-format
msgid "Sort by Name"
msgstr "이름으로 정렬"

#. module: website
#: code:addons/website/controllers/main.py:0
#, python-format
msgid "Sort by Url"
msgstr "URL로 정렬"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Sound"
msgstr "소리"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid ""
"Speakers from all over the world will join our experts to give inspiring "
"talks on various topics. Stay on top of the latest business management "
"trends &amp; technologies"
msgstr ""
"전세계의 연사들이 다양한 주제에 대한 고무적인 강연을 하기 위해 우리의 전문가들과 함께 할 것입니다. 최신 비즈니스 관리 동향 및 기술을"
" 지속적으로 활용하십시오."

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__specific_user_account
#: model:ir.model.fields,field_description:website.field_website__specific_user_account
msgid "Specific User Account"
msgstr "특정 사용자 계정"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
msgid "Specify a search term."
msgstr "검색어를 입력하세요"

#. module: website
#: model:ir.model.fields,help:website.field_ir_model__website_form_default_field_id
msgid ""
"Specify the field which will contain meta and custom form fields datas."
msgstr "메타 및 사용자 정의 양식 필드 데이터를 포함 할 필드를 지정하십시오."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Speed"
msgstr "속도"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Spring collection has arrived !"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_share_options
msgid "Square"
msgstr "정사각형"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Squared Miniatures"
msgstr "정사각형 미니어처"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Stacked"
msgstr "쌓였음"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Standard"
msgstr "표준"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
msgid "Start Button"
msgstr "시작 버튼"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#, python-format
msgid "Start Now"
msgstr "지금 시작하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Start now"
msgstr "지금 시작하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
#: model_terms:ir.ui.view,arch_db:website.s_tabs
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "Start with the customer – find out what they want and give it to them."
msgstr "고객과 함께 시작하세요. – 고객이 원하는 것이 무엇인지 알아보고 고객에게 제공하십시오."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "Start your journey"
msgstr "여행을 시작하십시오"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Starter"
msgstr "시작기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Status Colors"
msgstr "상태 색상"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Stay informed of our latest news and discover what will happen in the next "
"weeks."
msgstr "최신 소식을 확인하여 업데이트 되는 정보를 빠르게 받아보실 수 있습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
msgid "Sticky"
msgstr "스티커"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Storage"
msgstr "저장 공간"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_stores_locator
msgid "Stores Locator"
msgstr "매장 찾기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "Storytelling is powerful.<br/> It draws readers in and engages them."
msgstr "스토리 텔링은 강력합니다. <br/>독자를 끌어 들여 참여시킵니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Stretch to Equal Height"
msgstr "같은 높이로 늘리기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Striped"
msgstr "줄무늬"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Structure"
msgstr "구조"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Style"
msgstr "스타일"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Styling"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Sub Menus"
msgstr "하위 메뉴"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Subject"
msgstr "제목"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.s_website_form
#: model_terms:website.page,arch_db:website.contactus_page
msgid "Submit"
msgstr "제출"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Submit sitemap to Google"
msgstr "Google에 사이트맵 제출"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form.xml:0
#: code:addons/website/static/src/xml/website_form.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Success"
msgstr "성공"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_success_stories
msgid "Success Stories"
msgstr "성공 스토리"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Suggestions"
msgstr "제안"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Surrounded"
msgstr "둘러쌓인"

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "Suspicious activity detected by Google reCaptcha."
msgstr "구글 reCaptcha에서 의심스러운 활동이 감지되었습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Switch Theme"
msgstr "테마 변경"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "T-shirts"
msgstr "티셔츠"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#, python-format
msgid "TIP: Once loaded, follow the"
msgstr "사용팁: 완료 후에는 다음의"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid ""
"TIP: Once loaded, follow the\n"
"                    <span class=\"o_tooltip o_tooltip_visible bottom o_animated position-relative\"/>\n"
"                    <br/>pointer to build the perfect page in 7 steps."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "TRANSLATE"
msgstr "번역"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Tablets"
msgstr "태블릿"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Tabs"
msgstr "탭"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Tabs color"
msgstr "탭 색상"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Tada"
msgstr "Tada"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__target
msgid "Target"
msgstr "목표"

#. module: website
#: model:ir.model.fields,help:website.field_ir_asset__key
msgid ""
"Technical field used to resolve multiple assets in a multi-website "
"environment."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_ir_attachment__key
msgid ""
"Technical field used to resolve multiple attachments in a multi-website "
"environment."
msgstr "다중 웹 사이트 환경에서 여러 첨부 파일을 해결하는 데 사용되는 기술 필드."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Technical name:"
msgstr "기술적 명칭 :"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Telephone"
msgstr "전화"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Televisions"
msgstr "티비"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Tell what's the value for the <br/>customer for this feature."
msgstr "이 기능에 대한 <br/>고객의 가치를 알려줍니다."

#. module: website
#: code:addons/website/models/website.py:0
#: code:addons/website/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Template"
msgstr "템플릿"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "Template <b>%s (id:%s)</b> contains a link to this page"
msgstr "<b>%s (id : %s)</b> 서식은 이 페이지의 링크를 포함하고 있습니다."

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "Template <b>%s (id:%s)</b> is calling this file"
msgstr "<b>%s (id : %s)</b> 서식은 이 파일을 호출합니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Template fallback"
msgstr "서식 대체"

#. module: website
#: code:addons/website/models/website.py:0
#: code:addons/website/models/website.py:0
#, python-format
msgid "Templates"
msgstr "템플릿(서식)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "Terms of Services"
msgstr "서비스 약관"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Terms of service"
msgstr "서비스 약관"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "Terrain"
msgstr "지형"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_edit_robots
msgid "Test your robots.txt with Google Search Console"
msgstr "Google Search 콘솔로 robots.txt를 테스트하세요"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_header_brand
msgid "Text"
msgstr "문자"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Text Alignment"
msgstr "문자 정렬"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Text Color"
msgstr "글꼴 색상"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: model_terms:ir.ui.view,arch_db:website.s_text_highlight
#, python-format
msgid "Text Highlight"
msgstr "텍스트 강조 표시"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Text Image Text"
msgstr "텍스트 이미지 텍스트"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Text Inline"
msgstr "인라인 텍스트"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "Text Position"
msgstr "텍스트 위치"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Text muted. Lorem <b>ipsum dolor sit amet</b>, consectetur."
msgstr "Text muted. Lorem <b>ipsum dolor sit amet</b>, consectetur."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "Thank You For Your Feedback"
msgstr "의견을 보내 주셔서 감사합니다"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid "Thank You!"
msgstr "감사합니다!"

#. module: website
#: code:addons/website/controllers/backend.py:0
#, python-format
msgid "The Google Analytics Client ID or Key you entered seems incorrect."
msgstr "입력한 Google 웹 로그 분석 클라이언트 ID 또는 키가 올바르지 않은 것 같습니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "The chosen name already exists"
msgstr "선택한 이름이 이미 존재합니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "The company this website belongs to"
msgstr "이 웹 사이트가 속한 회사"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid ""
"The current text selection cannot be animated. Try clearing the format and "
"try again."
msgstr "현재 텍스트 선택 항목에 애니메이션을 적용할 수 없습니다. 형식을 지우고 다시 시도해 보세요."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid ""
"The description will be generated by search engines based on page content "
"unless you specify one."
msgstr "설명은 사용자가 지정하지 않는 한 페이지 내용을 기준으로 검색 엔진에서 생성됩니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid ""
"The description will be generated by social media based on page content "
"unless you specify one."
msgstr "설명은 사용자가 지정하지 않는 한 페이지 내용을 기준으로 소셜 미디어에서 생성됩니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form.xml:0
#, python-format
msgid "The form has been sent successfully."
msgstr "양식이 성공적으로 전송되었습니다."

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "The form's specified model does not exist"
msgstr "서식에 지정된 모델이 존재하지 않습니다."

#. module: website
#: model:ir.model.fields,help:website.field_res_partner__website_url
#: model:ir.model.fields,help:website.field_res_users__website_url
#: model:ir.model.fields,help:website.field_website_page__website_url
#: model:ir.model.fields,help:website.field_website_published_mixin__website_url
#: model:ir.model.fields,help:website.field_website_published_multi_mixin__website_url
#: model:ir.model.fields,help:website.field_website_snippet_filter__website_url
msgid "The full URL to access the document through the website."
msgstr "웹사이트를 통해 문서에 접근 하는 전체 URL입니다."

#. module: website
#: model:ir.model.fields,help:website.field_ir_actions_server__website_url
#: model:ir.model.fields,help:website.field_ir_cron__website_url
msgid "The full URL to access the server action through the website."
msgstr "웹사이트를 통해 서버 작업에 접근하는 전체 URL입니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "The installation of an App is already in progress."
msgstr "앱 설치가 이미 진행 중입니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "The language of the keyword and related keywords."
msgstr "키워드 및 관련 키워드의 언어"

#. module: website
#: model:ir.model.fields,help:website.field_website_snippet_filter__limit
msgid "The limit is the maximum number of records retrieved"
msgstr "검색할 수 있는 최대 기록 수의 한도입니다."

#. module: website
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "The limit must be between 1 and 16."
msgstr "제한값은 1에서 16 사이여야 합니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "The message will be visible once the countdown ends"
msgstr "카운트다운이 종료되면 메시지가 표시됩니다"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "The selected templates will be reset to their factory settings."
msgstr "선택한 서식을 최초 설정으로 초기화합니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid "The team"
msgstr "팀"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "The title will take a default value unless you specify one."
msgstr "제목을 지정하지 않으면 제목이 기본값을 사용합니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"The website will not work properly if you reject or discard those cookies."
msgstr "쿠키를 허용하지 않거나 삭제하면 웹사이트가 제대로 동작하지 않을 수 있습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "The website will still work if you reject or discard those cookies."
msgstr "쿠키를 허용하지 않거나 삭제해도 웹사이트 기능은 계속 사용할 수 있습니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: model:ir.model.fields,field_description:website.field_website__theme_id
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.theme_view_search
#, python-format
msgid "Theme"
msgstr "테마"

#. module: website
#: model:ir.model,name:website.model_theme_ir_asset
msgid "Theme Asset"
msgstr "테마 Asset"

#. module: website
#: model:ir.model,name:website.model_theme_ir_attachment
msgid "Theme Attachments"
msgstr "테마 첨부파일"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
#, python-format
msgid "Theme Colors"
msgstr "테마 색상"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Theme Options"
msgstr "테마 옵션"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_asset__theme_template_id
#: model:ir.model.fields,field_description:website.field_ir_attachment__theme_template_id
#: model:ir.model.fields,field_description:website.field_ir_ui_view__theme_template_id
#: model:ir.model.fields,field_description:website.field_website_menu__theme_template_id
#: model:ir.model.fields,field_description:website.field_website_page__theme_template_id
msgid "Theme Template"
msgstr "테마 서식"

#. module: website
#: model:ir.model,name:website.model_theme_ir_ui_view
msgid "Theme UI View"
msgstr "테마 UI 화면"

#. module: website
#: model:ir.model,name:website.model_theme_utils
msgid "Theme Utils"
msgstr "테마 유틸"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
msgid "There are currently no pages for this website."
msgstr "현재 이 웹사이트의 페이지가 없습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "There are currently no pages for your website."
msgstr "귀하의 웹 사이트에 페이지가 없습니다."

#. module: website
#: code:addons/website/models/website_visitor.py:0
#, python-format
msgid "There are no contact and/or no email linked to this visitor."
msgstr "이 방문자의 연락처 및/또는 이메일이 없습니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "There is no data currently available."
msgstr "사용가능한 데이타가 없습니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "There is no field available for this option."
msgstr "이 옵션에 사용 가능한 필드가 없습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"There is no website available for this company. You could create a new one."
msgstr "이 회사에 사용할 수있는 웹 사이트가 없습니다. 새로 만들 수 있습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"These terms of service (\"Terms\", \"Agreement\") are an agreement between "
"the website (\"Website operator\", \"us\", \"we\" or \"our\") and you "
"(\"User\", \"you\" or \"your\"). This Agreement sets forth the general terms"
" and conditions of your use of this website and any of its products or "
"services (collectively, \"Website\" or \"Services\")."
msgstr ""
"본 서비스 약관(이하 \"약관\"이라 함)은 웹 사이트(\"웹사이트 운영자\", \"우리\" 또는 \"우리\")와 사용자(\"사용자\","
" \"당신\" 또는 \"당신의\") 간의 계약입니다. 본 계약에서는 본 웹 사이트 및 해당 제품 또는 서비스(총괄, \"웹 사이트\" "
"또는 \"서비스\")를 사용하는 데 대한 일반적인 약관을 설명합니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "They trust us since years"
msgstr "오랜 기간 동안 저희를 신뢰해왔습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Thick"
msgstr "두께"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Thin"
msgstr "얇게"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Third Feature"
msgstr "제 3의 기능"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Third Menu"
msgstr "세 번째 메뉴"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__favicon
#: model:ir.model.fields,help:website.field_website__favicon
msgid "This field holds the image used to display a favicon on the website."
msgstr "이 필드에는 웹 사이트에 즐겨 찾기 아이콘을 표시하는 데 사용되는 이미지가 있습니다."

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch_base
msgid "This field is the same as `arch` field without translations"
msgstr "이 필드는 번역이 없는 `arch` 필드와 같습니다"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_default_lang_code
msgid "This field is used to set/get locales for user"
msgstr "이 필드는 사용자에 대한 로컬 설정을 설정하거나 가져 오는 데 사용됩니다."

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch
msgid ""
"This field should be used when accessing view arch. It will use translation.\n"
"                               Note that it will read `arch_db` or `arch_fs` if in dev-xml mode."
msgstr ""
"이 필드는 뷰 아치에 접근할 때 사용해야 합니다. 변환을 사용합니다.\n"
"                               dev-xml 모드로 읽으면 'arch_db' 또는 'arch_fs'를 읽습니다."

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch_db
msgid "This field stores the view arch."
msgstr "이 필드는 뷰 아치를 저장합니다."

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch_prev
msgid ""
"This field will save the current `arch_db` before writing on it.\n"
"                                                                         Useful to (soft) reset a broken view."
msgstr ""
"이 필드는 쓰기 전에 현재 `arch_db`를 저장합니다.\n"
"                                                                         화면이 깨진 경우 재설정(소프트)하는 데 유용합니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"This font already exists, you can only add it as a local font to replace the"
" server version."
msgstr "이미 존재하고 있는 폰트입니다. 서버 버전을 대체하려면 로컬 폰트만 추가할 수 있습니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "This font is hosted and served to your visitors by Google servers"
msgstr "Google 서버에서 이 글꼴을 방문자에게 호스팅 및 제공합니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "This is a \""
msgstr "이것은 \""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_banner
msgid ""
"This is a simple hero unit, a simple jumbotron-style component for calling "
"extra attention to featured content or information."
msgstr "점보트론을 이용하면, 웹사이트의 중요한 내용 또는 공지사항을 강조할 수 있습니다."

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "This message has been posted on your website!"
msgstr "이 메시지가 귀하의 웹 사이트에 게시되었습니다!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "This page"
msgstr "이 페이지"

#. module: website
#: code:addons/website/models/website_visitor.py:0
#, python-format
msgid "This operator is not supported"
msgstr "이 연산자는 지원되지 않습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid ""
"This page does not exist, but you can create it as you are editor of this "
"site."
msgstr "존재하지 않는 페이지지만 이 사이트의 편집자 권한으로 새로 만들어 낼 수 있습니다."

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "This page is in the menu <b>%s</b>"
msgstr "<b>%s</b> 메뉴에 이 페이지가 있습니다"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "This page will be published on {{ date_formatted }}"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "This translation is not editable."
msgstr "이 번역 항목은 편집할 수 없습니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid ""
"This value will be escaped to be compliant with all major browsers and used "
"in url. Keep it empty to use the default name of the record."
msgstr ""
"해당 값은 모든 브라우저에서 호환이 가능하도록 이스케이프 처리되어 URL에 사용됩니다. 기록에 자동으로 지정되는 이름을 사용하려면 비워 "
"두십시오."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.report_viewhierarchy_children
msgid "This view arch has been modified"
msgstr "뷰 아치가 수정되었습니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid ""
"Those accounts should now check their Analytics dashboard in the Google "
"platform directly."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Thumbnails"
msgstr "미리보기 이미지"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__time_since_last_action
msgid "Time since last page view. E.g.: 2 minutes ago"
msgstr "마지막 페이지 조회 이후의 시간입니다. 예 : 2분 전"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__cache_time
msgid "Time to cache the page. (0 = no cache)"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
#, python-format
msgid "Time's up! You can now visit"
msgstr "시간이 다 됐습니다! 이제 방문하실 수 있습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
msgid "Timeline"
msgstr "활동 기록"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__timezone
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Timezone"
msgstr "시간대"

#. module: website
#: model:digest.tip,name:website.digest_tip_website_4
#: model_terms:digest.tip,tip_description:website.digest_tip_website_4
msgid "Tip: Add shapes to energize your Website"
msgstr "팁: 웹사이트에 도형을 추가하여 활기를 불어넣을 수 있습니다"

#. module: website
#: model:digest.tip,name:website.digest_tip_website_0
#: model_terms:digest.tip,tip_description:website.digest_tip_website_0
msgid "Tip: Engage with visitors to convert them into leads"
msgstr "팁: 방문자의 참여를 유도하여 새로운 영업기회를 만들 수 있습니다."

#. module: website
#: model:digest.tip,name:website.digest_tip_website_2
#: model_terms:digest.tip,tip_description:website.digest_tip_website_2
msgid "Tip: Search Engine Optimization (SEO)"
msgstr "팁: 검색 엔진 최적화(SEO)"

#. module: website
#: model:digest.tip,name:website.digest_tip_website_3
#: model_terms:digest.tip,tip_description:website.digest_tip_website_3
msgid "Tip: Use illustrations to spice up your website"
msgstr "팁: 일러스트를 사용하여 웹사이트를 멋지게 꾸밀 수 있습니다."

#. module: website
#: model:digest.tip,name:website.digest_tip_website_1
#: model_terms:digest.tip,tip_description:website.digest_tip_website_1
msgid "Tip: Use royalty-free photos"
msgstr "팁: 저작권이 없는 사진을 사용하십시오"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Title"
msgstr "제목"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_share_options
msgid "Title Position"
msgstr "제목 위치"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"To add a fourth column, reduce the size of these three columns using the "
"right icon of each block. Then, duplicate one of the columns to create a new"
" one as a copy."
msgstr ""
"네 번째 열을 추가하려면 각 블록의 오른쪽 아이콘을 사용하여 이 세 열의 크기를 줄이십시오. 그런 다음 열 중 하나를 복제하여 복사본으로"
" 새 열을 만듭니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "To be successful your content needs to be useful to your readers."
msgstr "성공적인 콘텐츠는 독자에게 유용한 정보를 제공해야 합니다."

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_2
msgid ""
"To get more visitors, you should target keywords that are often searched in "
"Google. With the built-in SEO tool, once you define a few keywords, Odoo "
"will recommend you the best keywords to target. Then adapt your title and "
"description accordingly to boost your traffic."
msgstr ""
"더 많은 방문자를 확보하려면 Google에서 자주 검색되는 키워드를 타깃으로 정해야 합니다. SEO 툴에서 몇 가지 키워드를 입력하면 "
"Odoo가 관련성 있는 최적의 키워드를 추천해 줍니다. 타깃 키워드를 선택한 다음 제목과 설명을 적절히 조정하여 더 많은 트래픽 유입을 "
"유도해보세요."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"To send invitations in B2B mode, open a contact or select several ones in "
"list view and click on 'Portal Access Management' option in the dropdown "
"menu *Action*."
msgstr ""
"B2B 모드로 초대장을 보내려면 연락처보기를 열거 나 목록보기에서 연락처를 선택하고 드롭 다운 메뉴 *작업*에서 '포탈 사용 권한 관리'"
" 선택 사항을 클릭하십시오."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Toggle"
msgstr "토글"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.option_header_off_canvas
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Toggle navigation"
msgstr "탐색 전환"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Tony Fred, CEO"
msgstr "Tony Fred, CEO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Tooltip"
msgstr "툴팁"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Top"
msgstr "상단"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Top Menu"
msgstr "상단 메뉴"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "Top Menu for Website %s"
msgstr "%s 웹사이트 최상단 메뉴"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Top to Bottom"
msgstr "상단에서 하단"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Tops"
msgstr "상단"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__page_count
msgid "Total number of tracked page visited"
msgstr "방문한 추적 페이지 총 수"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__visitor_page_count
msgid "Total number of visits on tracked pages"
msgstr "추적된 페이지의 총 방문수"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__track
#: model:ir.model.fields,field_description:website.field_website_page__track
msgid "Track"
msgstr "과정"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/track_page.xml:0
#, python-format
msgid "Track Visitor"
msgstr "세션 방문자"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Track visits in Google Analytics"
msgstr "Google 웹 로그 분석에서 방문 추적"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Tracked"
msgstr "추적함"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Transition"
msgstr "화면 전환"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "Translate Attribute"
msgstr "특성 변환"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "Translate Selection Option"
msgstr "선택 옵션 번역"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "Translate header in the text. Menu is generated automatically."
msgstr "텍스트의 머리글을 번역합니다. 메뉴가 자동으로 생성됩니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/translator.xml:0
#, python-format
msgid "Translated content"
msgstr "번역된 내용"

#. module: website
#: model:ir.model,name:website.model_ir_translation
msgid "Translation"
msgstr "번역하기"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "Translation Info"
msgstr "번역 정보"

#. module: website
#: model:ir.model.fields,help:website.field_website__configurator_done
msgid "True if configurator has been completed or ignored"
msgstr "환경 설정이 완료되었거나 무시된 경우 True"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Tuna and Salmon Burger"
msgstr "참치 연어 버거"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Turn every feature into a benefit for your reader."
msgstr "모든 기능을 독자를 위한 혜택으로 바꿉니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Twitter"
msgstr "트위터"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_twitter
#: model:ir.model.fields,field_description:website.field_website__social_twitter
msgid "Twitter Account"
msgstr "트위터 계정"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Twitter Scroller"
msgstr "트위터 스크롤러"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__type
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Type"
msgstr "유형"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Type '"
msgstr "유형 '"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid ""
"Type '<i class=\"confirm_word\">yes</i>' in the box below if you want to "
"confirm."
msgstr "확인하려면 아래 상자에 '<i class=\"confirm_word\">예</i>'를 입력합니다."

#. module: website
#: model:ir.model.fields,help:website.field_website_rewrite__redirect_type
msgid ""
"Type of redirect/Rewrite:\n"
"\n"
"        301 Moved permanently: The browser will keep in cache the new url.\n"
"        302 Moved temporarily: The browser will not keep in cache the new url and ask again the next time the new url.\n"
"        404 Not Found: If you want remove a specific page/controller (e.g. Ecommerce is installed, but you don't want /shop on a specific website)\n"
"        308 Redirect / Rewrite: If you want rename a controller with a new url. (Eg: /shop -> /garden - Both url will be accessible but /shop will automatically be redirected to /garden)\n"
"    "
msgstr ""
"리디렉션/다시 쓰기 유형 :\n"
"\n"
"        301 영구적으로 이동 : 브라우저가 새 URL을 캐시에 보관합니다.\n"
"302 일시적으로 이동 : 브라우저가 새 URL을 캐시에 보관하지 않고 다음 번에 새유형 URL을 다시 묻습니다. \n"
"404 찾을 수 없음 : 특정 페이지/컨트롤러를 제거하려는 경우 (예 : 전자 상거래가 설치되어 있지만 특정 웹 사이트에서 /shop을 원하지 않는 경우)\n"
"308 리디렉션/다시쓰기 : 새로운 URL로 컨트롤러의 이름을 바꾸려면. (예 : /shop-> /garden - 두 URL 모두 액세스 할 수 있지만 /shop은 자동으로 /garden으로 리디렉션됩니다)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "URL"
msgstr "URL"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__url_from
#: model_terms:ir.ui.view,arch_db:website.view_website_rewrite_form
msgid "URL from"
msgstr "다음의 URL"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__cdn_filters
#: model:ir.model.fields,help:website.field_website__cdn_filters
msgid "URL matching those filters will be rewritten using the CDN Base URL"
msgstr "해당 필터와 일치하는 URL은 CDN 기본 URL을 사용하여 다시 작성됩니다."

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__url_to
msgid "URL to"
msgstr "다음에 대한 URL"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Unalterable unique identifier"
msgstr "변경이 불가능한 고유 식별자"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Underline On Hover"
msgstr "마우스를 올리면 밑줄로 표시"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Understand how visitors engage with our website, via Google Analytics.\n"
"                                                Learn more about"
msgstr ""
"Google Analytics를 통해, 웹사이트로 방문자가 유입되는 방식을 검토합니다\n"
"                                                자세히 알아보기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.index_management
msgid "Unindexed"
msgstr "색인되지 않음"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Unlimited CRM power and support"
msgstr "무제한 CRM 기능 및 지원"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Unlimited customization"
msgstr "무제한 맞춤 설정"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/button.js:0
#, python-format
msgid "Unpublish"
msgstr "게시 취소"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/button.js:0
#: code:addons/website/static/src/js/backend/button.js:0
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.publish_short
#, python-format
msgid "Unpublished"
msgstr "게시 안 함"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Unregistered"
msgstr "등록되지 않음"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_kanban
msgid "Update theme"
msgstr "테마 업데이트"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Upload"
msgstr "올리기"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "Uploaded file is too large."
msgstr "업로드한 파일의 용량이 너무 큽니다."

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__url
#: model:ir.model.fields,field_description:website.field_theme_website_menu__url
#: model:ir.model.fields,field_description:website.field_theme_website_page__url
#: model:ir.model.fields,field_description:website.field_website_menu__url
#: model:ir.model.fields,field_description:website.field_website_track__url
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
#: model_terms:ir.ui.view,arch_db:website.menu_search
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
msgid "Url"
msgstr "Url"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__country_flag
msgid "Url of static flag image"
msgstr "정적 플래그 이미지의 URL"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
msgid "Urls & Pages"
msgstr "URL과 페이지"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Use Google Map on your website ("
msgstr "귀하의 사이트에 구글 지도 사용하기 ("

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Use Google Map on your website (Contact Us page, snippets, etc)."
msgstr "Google 지도를 웹사이트에서 사용합니다 (문의 페이지, 스니펫 등)."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Use a CDN to optimize the availability of your website's content"
msgstr "CDN을 사용하여 웹 사이트 컨텐츠의 가용성을 최적화하십시오"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_default_share_image
msgid "Use a image by default for sharing"
msgstr "공유 시 이미지를 기본으로 사용"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Use as Homepage"
msgstr "홈페이지로 사용"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Use of Cookies"
msgstr "쿠기 사용"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid ""
"Use this component for creating a list of featured elements to which you "
"want to bring attention."
msgstr "이 구성 요소를 사용하여 강조하고 싶은 주요 기능의 목록을 만들 수 있습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid ""
"Use this snippet to build various types of components that feature a left- "
"or right-aligned image alongside textual content. Duplicate the element to "
"create a list that fits your needs."
msgstr ""
"스니펫을 활용하여 텍스트와 함께 좌우로 정렬된 이미지를 포함한 다양한 유형의 구성 요소를 만들 수 있습니다. 구성 요소를 복사하여 필요에"
" 맞게 목록을 만들어보십시오."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"Use this snippet to presents your content in a slideshow-like format. Don't "
"write about products or services here, write about solutions."
msgstr ""
"이 스니펫을 사용하여 슬라이드쇼와 같은 형식으로 콘텐츠를 표시할 수 있습니다. 제품이나 서비스에 대한 내용보다는 솔루션에 대한 설명을 "
"작성하길 권장합니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_kanban
msgid "Use this theme"
msgstr "이 테마 사용하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid ""
"Use this timeline as a part of your resume, to show your visitors what "
"you've done in the past."
msgstr "타임라인을 이력서처럼 사용하여 방문자에게 과거 이력을 효과적으로 보여줄 수 있습니다."

#. module: website
#: model:ir.model.fields,help:website.field_ir_model__website_form_key
msgid "Used in FormBuilder Registry"
msgstr "FormBuilder 레지스트리에서 사용"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Used in page content"
msgstr "페이지 내용에 사용"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Used in page description"
msgstr "페이지 설명에 사용"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Used in page first level heading"
msgstr "페이지 1 단계 제목에 사용"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Used in page second level heading"
msgstr "페이지 2 단계 제목에 사용"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Used in page title"
msgstr "페이지 제목에 사용"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Used to collect information about your interactions with the website, the pages you've seen,\n"
"                                                and any specific marketing campaign that brought you to the website."
msgstr ""
"이용자의 웹사이트 이용 형태, 웹사이트 방문 기록,\n"
"                                                이용자를 해당 웹사이트로 유입시킨 마케팅 캠페인에 대한 정보를 수집하는데 활용됩니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Used to make advertising more engaging to users and more valuable to publishers and advertisers,\n"
"                                                such as providing more relevant ads when you visit other websites that display ads or to improve reporting on ad campaign performance."
msgstr ""
"이용자가 타 웹사이트를 방문할 때 더 관련성 있는 광고를 노출하거나 광고 성과에 대한 보고를 개선하여\n"
"                                                광고주가 더욱 가치 있고 효과적인 광고를 만들고 방문자에게 맞춤형 광고를 제공하도록 활용됩니다."

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_country_group_ids
#: model:ir.model.fields,help:website.field_website__country_group_ids
msgid "Used when multiple websites have the same domain."
msgstr "여러 웹 사이트가 동일한 도메인을 가질 때 사용됩니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Useful Links"
msgstr "유용한 링크"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "Useful options"
msgstr "유용한 옵션"

#. module: website
#: model:ir.model.fields,help:website.field_website_menu__group_ids
msgid "User need to be at least in one of these groups to see the menu"
msgstr "메뉴를 보려면 사용자가 이 그룹 중 하나 이상에 속해야 합니다."

#. module: website
#: model:ir.model,name:website.model_res_users
msgid "Users"
msgstr "사용자"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Utilities &amp; Typography"
msgstr "유틸리티 및 타이포그래피"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Value"
msgstr "가치"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Vert. Alignment"
msgstr "수평 정렬"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Vertical"
msgstr "수직선"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Vertical Alignment"
msgstr "수평 정렬"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Video"
msgstr "동영상"

#. module: website
#: model:ir.model,name:website.model_ir_ui_view
#: model:ir.model.fields,field_description:website.field_theme_website_page__view_id
#: model:ir.model.fields,field_description:website.field_website_page__view_id
msgid "View"
msgstr "화면"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch
msgid "View Architecture"
msgstr "뷰 아키텍쳐"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__name
msgid "View Name"
msgstr "화면 이름"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__type
msgid "View Type"
msgstr "화면 유형"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__mode
msgid "View inheritance mode"
msgstr "상속 모드 화면"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__copy_ids
msgid "Views using a copy of me"
msgstr "내 사본을 이용한 화면"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__inherit_children_ids
msgid "Views which inherit from this one"
msgstr "이것을 상속한 화면"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields,field_description:website.field_ir_ui_view__visibility
#: model:ir.model.fields,field_description:website.field_website_page__visibility
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Visibility"
msgstr "표시"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__visibility_password
#: model:ir.model.fields,field_description:website.field_website_page__visibility_password
#: model_terms:ir.ui.view,arch_db:website.view_view_form_extend
msgid "Visibility Password"
msgstr "비밀번호 표시"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__visibility_password_display
#: model:ir.model.fields,field_description:website.field_website_page__visibility_password_display
msgid "Visibility Password Display"
msgstr "비밀번호 보이기 표시"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__group_ids
msgid "Visible Groups"
msgstr "표시 가능 그룹"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_conditional_visibility
msgid "Visible for"
msgstr "다음에서 표시 가능"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Visible for Everyone"
msgstr "모든 사람에게 표시"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Visible for Logged In"
msgstr "로그인한 경우에만 표시"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Visible for Logged Out"
msgstr "로그아웃한 경우 표시"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_partner__website_published
#: model:ir.model.fields,field_description:website.field_res_users__website_published
#: model:ir.model.fields,field_description:website.field_website_page__website_published
#: model:ir.model.fields,field_description:website.field_website_published_mixin__website_published
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__website_published
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__website_published
msgid "Visible on current website"
msgstr "현재 웹 사이트에 공개"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Visible on mobile"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Visible only if"
msgstr "다음과 같은 경우에만 표시됩니다"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_track__visit_datetime
msgid "Visit Date"
msgstr "방문일"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
#, python-format
msgid "Visit our Facebook page to know if you are one of the lucky winners."
msgstr "페이스북 페이지를 방문하여 행운의 당첨자를 확인하세요."

#. module: website
#: model:ir.model,name:website.model_website_track
#: model:ir.model.fields,field_description:website.field_website_visitor__page_ids
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Visited Pages"
msgstr "방문한 페이지"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__website_track_ids
msgid "Visited Pages History"
msgstr "방문한 페이지 이력"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_track__visitor_id
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Visitor"
msgstr "방문자"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_graph
msgid "Visitor Page Views"
msgstr "방문자 페이지 조회수"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_tree
msgid "Visitor Page Views History"
msgstr "방문자 페이지 조회수 이력"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_track_view_graph
msgid "Visitor Views"
msgstr "방문자 조회수"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_track_view_tree
msgid "Visitor Views History"
msgstr "방문자 조회수 이력"

#. module: website
#: model:ir.actions.act_window,name:website.website_visitors_action
#: model:ir.model.fields,field_description:website.field_res_partner__visitor_ids
#: model:ir.model.fields,field_description:website.field_res_users__visitor_ids
#: model:ir.ui.menu,name:website.menu_visitor_sub_menu
#: model:ir.ui.menu,name:website.website_visitor_menu
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_graph
msgid "Visitors"
msgstr "방문자"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#, python-format
msgid "Visits"
msgstr "방문수"

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitor_view_action
msgid ""
"Wait for visitors to come to your website to see the pages they viewed."
msgstr "방문자가 웹 사이트를 방문하여 방문자가 본 페이지를 볼 때까지 기다립니다."

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitors_action
msgid ""
"Wait for visitors to come to your website to see their history and engage "
"with them."
msgstr "방문자가 웹사이트에 방문하여 기록을 확인하고 참여하기를 기다립니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Warning"
msgstr "경고"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Watches"
msgstr "시계"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid ""
"We are a team of passionate people whose goal is to improve everyone's life through disruptive products. We build great products to solve your business problems.\n"
"                            <br/><br/>Our products are designed for small to medium size companies willing to optimize their performance."
msgstr ""
"저희 회사는 모든 사람의 삶을 개선시킬 수 있는 혁신적인 제품을 만드는 것을 목표로 열정적인 사람들이 모인 팀입니다.  고객님이 비즈니스 중에 겪는 문제를 해결할 수 있는 훌륭한 제품을 만듭니다. \n"
"                            <br/><br/>저희 제품은 최고의 성과를 목표로 하는 중소기업을 위해 만들어졌습니다. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
msgid ""
"We are a team of passionate people whose goal is to improve everyone's life "
"through disruptive products. We build great products to solve your business "
"problems. Our products are designed for small to medium size companies "
"willing to optimize their performance."
msgstr ""
"저희는 모든 사람들이 더 나은 삶을 영위할 수 있도록 혁신 제품을 제작하고자 하는 열정을 가지고 구성된 팀입니다. 고객님이 비즈니스를 "
"운영하는 중 겪을 수 있는 문제를 해결할 훌륭한 제품을 만들고 있습니다. 저희 제품은 최고의 성과를 목표로 하는 중소기업을 위해 "
"설계되었습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
msgid ""
"We are a team of passionate people whose goal is to improve everyone's "
"life.<br/>Our services are designed for small to medium size companies."
msgstr ""
"저희는 모든 사람들이 더 나은 삶을 영위할 수 있도록 혁신 제품을 제작하고자 하는 열정을 가지고 구성된 팀입니다.<br/>저희의 서비스는"
" 중소기업을 위해 설계되었습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar
msgid "We are almost done!"
msgstr "거의 끝났습니다!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid "We are in good company."
msgstr "우리는 좋은 회사에 있습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"We do not currently support Do Not Track signals, as there is no industry "
"standard for compliance."
msgstr "관련된 업계 표준 규정이 제시되지 않아 현재 추적 금지 신호는 지원되지 않습니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "We found these ones:"
msgstr "우리는 이것들을 발견했습니다 :"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_header_slogan_oe_structure_header_slogan_1
msgid "We help <b>you</b> grow your business"
msgstr "<b>당신의</b> 비즈니스 성장을 돕습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"We may not be able to provide the best service to you if you reject those "
"cookies, but the website will work."
msgstr "쿠키를 허용하지 않는 경우에도 웹사이트는 정상적으로 동작하지만, 일부 기능은 사용하지 못할 수 있습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "We offer tailor-made products according to your needs and your budget."
msgstr "고객의 니즈와 예산에 따라 맞춤형 제품을 제공합니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: model_terms:ir.ui.view,arch_db:website.cookies_bar
#, python-format
msgid "We use cookies to provide you a better user experience."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#, python-format
msgid ""
"We use them to store info about your habits on our website. It will helps us"
" to provide you the very best experience and customize what you see."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid "We will get back to you shortly."
msgstr "빠른시간내에  연락 드리겠습니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "We'll set you up and running in"
msgstr "설정과 실행에 필요한 과정은 이것 뿐입니다 "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
msgid "Web Visitors"
msgstr "웹사이트 방문자"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/xml/website.backend.xml:0
#: model:ir.actions.act_url,name:website.action_website
#: model:ir.model,name:website.model_website
#: model:ir.model.fields,field_description:website.field_ir_asset__website_id
#: model:ir.model.fields,field_description:website.field_ir_attachment__website_id
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_id
#: model:ir.model.fields,field_description:website.field_res_company__website_id
#: model:ir.model.fields,field_description:website.field_res_partner__website_id
#: model:ir.model.fields,field_description:website.field_res_users__website_id
#: model:ir.model.fields,field_description:website.field_website_menu__website_id
#: model:ir.model.fields,field_description:website.field_website_multi_mixin__website_id
#: model:ir.model.fields,field_description:website.field_website_page__website_id
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__website_id
#: model:ir.model.fields,field_description:website.field_website_rewrite__website_id
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__website_id
#: model:ir.model.fields,field_description:website.field_website_visitor__website_id
#: model:ir.ui.menu,name:website.menu_website_configuration
#: model_terms:ir.ui.view,arch_db:website.menu_search
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.view_server_action_search_website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
#, python-format
msgid "Website"
msgstr "웹사이트"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_company_id
msgid "Website Company"
msgstr "웹사이트 회사"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__website_config_preselection
msgid "Website Config Preselection"
msgstr "웹사이트 환경 설정 사전 선택"

#. module: website
#: model:ir.actions.act_url,name:website.start_configurator_act_url
msgid "Website Configurator"
msgstr "웹사이트 환경 설정"

#. module: website
#: model:ir.model,name:website.model_website_configurator_feature
msgid "Website Configurator Feature"
msgstr "웹사이트 환경 설정 기능"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_domain
#: model:ir.model.fields,field_description:website.field_website__domain
msgid "Website Domain"
msgstr "웹사이트 도메인"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__favicon
msgid "Website Favicon"
msgstr "웹사이트 즐겨찾기 아이콘"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model__website_form_key
msgid "Website Form Key"
msgstr "웹 사이트 양식 키"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.ir_model_view
msgid "Website Forms"
msgstr "웹사이트 양식"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_logo
#: model:ir.model.fields,field_description:website.field_website__logo
#, python-format
msgid "Website Logo"
msgstr "웹사이트 로고"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_menu
#: model:ir.model,name:website.model_website_menu
msgid "Website Menu"
msgstr "웹사이트 메뉴"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_menus_form_view
msgid "Website Menus Settings"
msgstr "웹사이트 메뉴 설정"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_name
#: model:ir.model.fields,field_description:website.field_website__name
msgid "Website Name"
msgstr "웹사이트 이름"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__first_page_id
#: model:ir.model.fields,field_description:website.field_website_page__first_page_id
msgid "Website Page"
msgstr "웹사이트 페이지"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_form_view
msgid "Website Page Settings"
msgstr "웹사이트 페이지 설정"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_pages_list
#: model_terms:ir.ui.view,arch_db:website.website_pages_tree_view
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Website Pages"
msgstr "웹사이트 페이지"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_actions_server__website_path
#: model:ir.model.fields,field_description:website.field_ir_cron__website_path
msgid "Website Path"
msgstr "웹사이트 경로"

#. module: website
#: model:ir.model,name:website.model_website_published_mixin
msgid "Website Published Mixin"
msgstr "웹사이트 게시물 혼합"

#. module: website
#: model:ir.model,name:website.model_website_searchable_mixin
msgid "Website Searchable Mixin"
msgstr "웹사이트 검색 가능 믹스인"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: model_terms:ir.ui.view,arch_db:website.view_website_form
#, python-format
msgid "Website Settings"
msgstr "웹사이트 설정"

#. module: website
#: model:ir.model,name:website.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "웹사이트 스니펫 필터"

#. module: website
#: model:ir.model,name:website.model_theme_website_menu
msgid "Website Theme Menu"
msgstr "웹사이트 타마 메뉴"

#. module: website
#: model:ir.model,name:website.model_theme_website_page
msgid "Website Theme Page"
msgstr "웹사이트 테마 페이지"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Website Title"
msgstr "웹사이트 제목"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_attachment__website_url
#: model:ir.model.fields,field_description:website.field_res_partner__website_url
#: model:ir.model.fields,field_description:website.field_res_users__website_url
#: model:ir.model.fields,field_description:website.field_website_page__website_url
#: model:ir.model.fields,field_description:website.field_website_published_mixin__website_url
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__website_url
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__website_url
msgid "Website URL"
msgstr "웹 사이트 URL"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_actions_server__website_url
#: model:ir.model.fields,field_description:website.field_ir_cron__website_url
msgid "Website Url"
msgstr "웹사이트 URL"

#. module: website
#: model:ir.model,name:website.model_website_visitor
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Website Visitor"
msgstr "웹사이트 방문자"

#. module: website
#: code:addons/website/models/website_visitor.py:0
#, python-format
msgid "Website Visitor #%s"
msgstr "웹사이트 방문자 #%s"

#. module: website
#: model:ir.actions.server,name:website.website_visitor_cron_ir_actions_server
#: model:ir.cron,cron_name:website.website_visitor_cron
#: model:ir.cron,name:website.website_visitor_cron
msgid "Website Visitor : Archive old visitors"
msgstr "웹사이트 방문자 : 기존 방문자 보관"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"Website may use cookies to personalize and facilitate maximum navigation of "
"the User by this site. The User may configure his / her browser to notify "
"and reject the installation of the cookies sent by us."
msgstr ""
"웹 사이트는 쿠키를 사용하여이 사이트에서 사용자를 개인화하고 최대한 탐색할 수 있습니다. 사용자는 당사가 보낸 쿠키의 설치를 알리고 "
"거부하도록 브라우저를 구성할 수 있습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_tree
msgid "Website menu"
msgstr "웹사이트 메뉴"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_description
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_description
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_description
msgid "Website meta description"
msgstr "웹사이트 메타 설명"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_keywords
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_keywords
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_keywords
msgid "Website meta keywords"
msgstr "웹사이트 메타 핵심어"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_title
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_title
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_title
msgid "Website meta title"
msgstr "웹사이트 메타 제목"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_og_img
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_og_img
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_og_img
msgid "Website opengraph image"
msgstr "웹사이트 오픈그래프 이미지"

#. module: website
#: model:ir.model,name:website.model_website_rewrite
msgid "Website rewrite"
msgstr "웹사이트 재작성"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_rewrite_form
msgid "Website rewrite Settings"
msgstr "웹사이트 재작성 설정"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.action_website_rewrite_tree
msgid "Website rewrites"
msgstr "웹사이트 재작성"

#. module: website
#: model:ir.actions.server,name:website.ir_actions_server_website_google_analytics
msgid "Website: Analytics"
msgstr "웹사이트: 분석"

#. module: website
#: model:ir.actions.server,name:website.ir_actions_server_website_dashboard
msgid "Website: Dashboard"
msgstr "웹사이트 : 현황판"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_list
#: model:ir.ui.menu,name:website.menu_website_websites_list
#: model_terms:ir.ui.view,arch_db:website.view_website_tree
msgid "Websites"
msgstr "웹 사이트"

#. module: website
#: model:ir.model.fields,field_description:website.field_base_language_install__website_ids
msgid "Websites to translate"
msgstr "웹사이트 번역"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Websites-shared page"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Welcome to your"
msgstr "다음에 오신 것을 환영합니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "What you see is what you get"
msgstr "화면에 표시된 그대로 출력됨"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Width"
msgstr "폭"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__ir_ui_view__visibility__password
msgid "With Password"
msgstr "패스워드 사용"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Women"
msgstr "여성"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid ""
"Would you like to save before being redirected? Unsaved changes will be "
"discarded."
msgstr "페이지 이동 전에 저장하시겠습니까? 저장하지 않은 변경 사항은 삭제됩니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"Write a quote here from one of your customers. Quotes are a great way to "
"build confidence in your products or services."
msgstr "고객 중 한 명이 여기에 견적을 작성합니다. 따옴표는 제품이나 서비스에 대한 자신감을 키울 수 있는 좋은 방법입니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "Write one or two paragraphs describing your product or services."
msgstr "제품이나 서비스에 대한 설명을 한두 개의 단락으로 작성하십시오."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid ""
"Write one or two paragraphs describing your product or services. To be "
"successful your content needs to be useful to your readers."
msgstr "제품이나 서비스에 대한 설명을 한두 개의 단락으로 작성하십시오. 방문자에게 유용한 콘텐츠일 수록 성공 확률이 높아집니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cover
msgid ""
"Write one or two paragraphs describing your product, services or a specific "
"feature.<br/> To be successful your content needs to be useful to your "
"readers."
msgstr ""
"제품, 서비스 또는 특정 기능을 설명하는 하나 또는 두 개의 단락을 작성하십시오. <br/>성공하려면 컨텐츠가 독자에게 유용해야 합니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid ""
"Write what the customer would like to know, <br/>not what you want to show."
msgstr "고객이 알고 싶은 것을 쓰십시오. <br/>당신이 보여주고 싶은 것이 아닙니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline_options
msgid "Year"
msgstr "년"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/translator.xml:0
#, python-format
msgid "You are about to enter the translation mode."
msgstr "번역 모드로 변경됩니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"You can choose to have your computer warn you each time a cookie is being sent, or you can choose to turn off all cookies.\n"
"                            Each browser is a little different, so look at your browser's Help menu to learn the correct way to modify your cookies."
msgstr ""
"쿠키가 전송될 때마다 알림이 가도록 하거나 모든 쿠키 사용을 중단할 수 있도록 선택할 수 있습니다.\n"
"                            사용하시는  브라우저의 도움말 메뉴에서 쿠키를 수정하는 올바른 방법을 알아보세요."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "You can edit colors and backgrounds to highlight features."
msgstr "색상 및 배경을 편집하여 기능을 강조할 수 있습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "You can edit, duplicate..."
msgstr "수정하거나, 복사할 수 있습니다..."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"You can have 2 websites with same domain AND a condition on country group to"
" select wich website use."
msgstr "동일한 도메인을 가진 2 개의 웹 사이트와 국가 그룹에 조건이 있는 웹 사이트를 사용하여 웹 사이트를 사용할 수 있습니다."

#. module: website
#: code:addons/website/models/res_users.py:0
#: model:ir.model.constraint,message:website.constraint_res_users_login_key
#, python-format
msgid "You can not have two users with the same login!"
msgstr "동일한 로그인으로 두 명의 사용자를 가질 수 없습니다!"

#. module: website
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "You can only use template prefixed by dynamic_filter_template_ "
msgstr "dynamic_filter_template_ 템플릿만 사용할 수 있습니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "You can't duplicate a model field."
msgstr "모델 필드를 복제할 수 없습니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "You can't duplicate the submit button of the form."
msgstr "서식의 제출 버튼은 복사할 수 없습니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "You can't remove a field that is required by the model itself."
msgstr "모델 자체에 필요한 필드는 제거할 수 없습니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "You can't remove the submit button of the form"
msgstr "서식의 제출 버튼은 삭제할 수 없습니다"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid ""
"You cannot delete default website %s. Try to change its settings instead"
msgstr "기본 웹사이트 %s는 삭제할 수 없습니다. 대신 설정 사항을 변경하십시오."

#. module: website
#: code:addons/website/controllers/backend.py:0
#, python-format
msgid "You do not have sufficient rights to perform that action."
msgstr "해당 작업을 수행 할 수 있는 권한이 없습니다."

#. module: website
#: code:addons/website/models/mixins.py:0
#, python-format
msgid "You do not have the rights to publish/unpublish"
msgstr "게시/게시취소 권한이 없습니다"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "You do not seem to have access to this Analytics Account."
msgstr "이 웹 로그 분석 계정에 접근할 수 없는 것 같습니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid ""
"You have hidden this page from search results. It won't be indexed by search"
" engines."
msgstr "검색 결과에서 이 페이지를 숨겼습니다. 검색 엔진에 의해 색인이 생성되지 않습니다."

#. module: website
#: code:addons/website/models/res_config_settings.py:0
#, python-format
msgid "You haven't defined your domain"
msgstr "도메인을 정의하지 않았습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "You may opt-out of a third-party's use of cookies by visiting the"
msgstr "제3자의 쿠키 사용을 거부할 수 있습니다."

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "You must keep at least one website."
msgstr "하나 이상의 웹 사이트를 유지해야 합니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "You need to log in to your Google Account before:"
msgstr "먼저 Google 계정에 로그인해야 합니다. : "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"You should carefully review the legal statements and other conditions of use"
" of any website which you access through a link from this Website. Your "
"linking to any other off-site pages or other websites is at your own risk."
msgstr ""
"이 웹 사이트의 링크를 통해 액세스하는 웹 사이트의 법적 진술 및 기타 사용 조건을 주의 깊게 검토해야 합니다. 다른 오프 사이트 "
"페이지나 다른 웹사이트로의 링크는 귀하의 책임입니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_searchbar
msgid "You will get results from blog posts, products, etc"
msgstr "블로그 게시물, 품목 등에서 결과를 얻을 수 있습니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "You'll be able to create your pages later on."
msgstr "나중에 페이지를 생성할 수도 있습니다 "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "YouTube"
msgstr "유튜브"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Your Client ID:"
msgstr "귀하의 클라이언트 ID : "

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Your Company"
msgstr "나의 회사"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_template
msgid ""
"Your Dynamic Snippet will be displayed here... This message is displayed "
"because you did not provided both a filter and a template to use.<br/>"
msgstr "동적 구조화된 스니펫이 여기에 표시됩니다... 사용할 필터와 템플릿이 제공되지 않아 이 메시지가 표시됩니다.<br/>"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Your Email"
msgstr "이메일"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Your Measurement ID:"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Your Name"
msgstr "성명"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Your Question"
msgstr "나의 문의"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid "Your description looks too long."
msgstr "설명이 너무 깁니다."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid "Your description looks too short."
msgstr "설명이 너무 짧습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Your experience may be degraded if you discard those cookies, but the "
"website will still work."
msgstr "쿠키를 삭제하면 웹사이트는 계속 작동하지만 이용자의 사용 환경이 저하될 수 있습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
msgid "Your search '"
msgstr "검색 '"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "Your title"
msgstr "귀하의 직함"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_youtube
#: model:ir.model.fields,field_description:website.field_website__social_youtube
msgid "Youtube Account"
msgstr "Youtube 계정"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Zoom"
msgstr "줌"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Zoom In"
msgstr "확대"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Zoom In-Down"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Zoom In-Left"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Zoom In-Right"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Zoom Out"
msgstr "축소"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"__gads (Google)<br/>\n"
"                                            __gac (Google)"
msgstr ""
"__gads (Google)<br/>\n"
"                                            __gac (Google)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"_ga (Google)<br/>\n"
"                                            _gat (Google)<br/>\n"
"                                            _gid (Google)<br/>\n"
"                                            _gac_* (Google)"
msgstr ""
"_ga (Google)<br/>\n"
"                                            _gat (Google)<br/>\n"
"                                            _gid (Google)<br/>\n"
"                                            _gac_* (Google)"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "a blog"
msgstr "블로그"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "a business website"
msgstr "비즈니스 웹사이트"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "a new image"
msgstr "새 이미지"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "a pre-made Palette"
msgstr "색상 팔레트 모음"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "an elearning platform"
msgstr "온라인 학습 플랫폼"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "an event website"
msgstr "행사용 웹사이트"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "an online store"
msgstr "온라인 스토어"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "and"
msgstr "그리고"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "and copy paste the address of the font page here."
msgstr "또한 여기에 글꼴 페이지 주소를 복사하여 붙여 넣으십시오."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "big"
msgstr "크게"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "breadcrumb"
msgstr "사이트 이동 경로"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "btn-outline-primary"
msgstr "btn-outline-primary"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "btn-outline-secondary"
msgstr "btn-outline-secondary"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "btn-primary"
msgstr "btn-primary"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "btn-secondary"
msgstr "btn-secondary"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "business"
msgstr "경영을 위한 것입니다"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "celebration, launch"
msgstr "기념, 출시"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "chart, table, diagram, pie"
msgstr "차트, 표, 도표, 원형 차트"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "cite"
msgstr "인용"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "columns, description"
msgstr "열, 설명"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "common answers, common questions"
msgstr "일반적인 답변, 공통 질문"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "content"
msgstr "콘텐츠"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "customers, clients"
msgstr "고객, 클라이언트"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "days"
msgstr "일"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "develop the brand"
msgstr "브랜드 개발"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "e.g. /my-awesome-page"
msgstr "예. /my-awesome-page"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "e.g. About Us"
msgstr "예 : 회사 소개"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "e.g. De Brouckere, Brussels, Belgium"
msgstr "e.g. 브루케르, 브뤼셀, 벨기에"

#. module: website
#: model:ir.ui.menu,name:website.menu_website_dashboard
msgid "eCommerce Dashboard"
msgstr "전자 상거래 현황판"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_elearning
msgid "eLearning"
msgstr "온라인 학습 관리"

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "email"
msgstr "이메일"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "esc"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "evolution, growth"
msgstr "발전, 성장"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "fonts.google.com"
msgstr "fonts.google.com"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "for my"
msgstr "이며 저의"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "found(s)"
msgstr "발견"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.brand_promotion
msgid "free website"
msgstr "무료 웹사이트"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "from Logo"
msgstr "로고 업로드하기"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "frontend_lang (Odoo)"
msgstr "frontend_lang (Odoo)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "gallery, carousel"
msgstr "갤러리, 캐러셀"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "get leads"
msgstr "잠재고객 확보"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "google1234567890123456.html"
msgstr "google1234567890123456.html"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "heading, h1"
msgstr "제목, h1"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "hero, jumbotron"
msgstr "hero, jumbotron"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "https://fonts.google.com/specimen/Roboto"
msgstr "https://fonts.google.com/specimen/Roboto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "https://www.odoo.com"
msgstr "https://www.odoo.com"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "iPhone"
msgstr "아이폰"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"im_livechat_previous_operator_pid (Odoo)<br/>\n"
"                                            utm_campaign (Odoo)<br/>\n"
"                                            utm_source (Odoo)<br/>\n"
"                                            utm_medium (Odoo)"
msgstr ""
"im_livechat_previous_operator_pid (Odoo)<br/>\n"
"                                            utm_campaign (Odoo)<br/>\n"
"                                            utm_source (Odoo)<br/>\n"
"                                            utm_medium (Odoo)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "image, media, illustration"
msgstr "이미지, 미디어, 일러스트레이션"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "in the top right corner to start designing."
msgstr "버튼을 눌러 디자인을 새롭게 꾸밀 수 있습니다."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#: model_terms:ir.ui.view,arch_db:website.template_header_contact_oe_structure_header_contact_1
#: model_terms:ir.ui.view,arch_db:website.template_header_hamburger_oe_structure_header_hamburger_3
#: model_terms:ir.ui.view,arch_db:website.template_header_sidebar_oe_structure_header_sidebar_1
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "inform customers"
msgstr "고객 안내"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "instance of Odoo, the"
msgstr "Odoo의 경우, "

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_ui_view__inherit_id__ir_ui_view
msgid "ir.ui.view"
msgstr "ir.ui.view"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "link"
msgstr "링크"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "masonry, grid"
msgstr "벽돌 레이아웃, 그리드"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "menu, pricing"
msgstr "메뉴, 요금"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "no value"
msgstr "밸류 없음"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "o-color-"
msgstr "o-color-"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.res_config_settings.xml:0
#, python-format
msgid ""
"of\n"
"            your visitors. We recommend you avoid them unless you have\n"
"            verified with a legal advisor that you absolutely need cookie\n"
"            consent in your country."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "or Edit Master"
msgstr "또는 마스터 편집"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "organization, structure"
msgstr "조직, 구조"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "page, snippets, ...)"
msgstr "페이지, 스니펫, ...)"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "perfect website?"
msgstr "완벽한 웹사이트"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_form_preview
msgid "phone"
msgstr "전화"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "placeholder"
msgstr "플레이스홀더"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#, python-format
msgid "pointer to build the perfect page in 7 steps."
msgstr "포인터를 따라가시면 완벽한 웹사이트를 7단계만에 제작하실 수 있습니다. "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "pricing"
msgstr "가격 책정"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "promotion, characteristic, quality"
msgstr "프로모션, 특징, 품질"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "results"
msgstr "결과"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "rows"
msgstr "행"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "schedule appointments"
msgstr "일정 예약하기"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "sell more"
msgstr "판매 증진"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "separator, divider"
msgstr "구분 기호, 디바이더"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "session_id (Odoo)<br/>"
msgstr "session_id (Odoo)<br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "statistics, stats, KPI"
msgstr "통계, 지표, KPI"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "testimonials"
msgstr "추천글"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "text link"
msgstr "텍스트 링크"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_ui_view__inherit_id__theme_ir_ui_view
msgid "theme.ir.ui.view"
msgstr "theme.ir.ui.view"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
#, python-format
msgid "this page"
msgstr "해당 페이지"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "to exit full screen"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "true"
msgstr "O"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "valuation, rank"
msgstr "평가, 순위"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_id
#, python-format
msgid "website"
msgstr "웹 사이트"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "with the main objective to"
msgstr "주요 목적은 다음을 위한 것입니다 "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "yes"
msgstr "예"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.res_config_settings.xml:0
#, python-format
msgid "you do not need to ask for the consent"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "⌙ Active"
msgstr "⌙ 활성"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Background"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "⌙ Blur"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Buttons"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "⌙ Color"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Colors"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Country"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "⌙ Delay"
msgstr "⌙ 지연"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
msgid "⌙ Desktop"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "⌙ Display"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Headings"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "⌙ Height"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Hue"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "⌙ Inactive"
msgstr "⌙ 비활성"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "⌙ Intensity"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Label"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Languages"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Large"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
msgid "⌙ Mobile"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Off-Canvas Logo"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "⌙ Offset (X, Y)"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "⌙ Page Anchor"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "⌙ Parallax"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "⌙ Position"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Saturation"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog_options
msgid "⌙ Separator"
msgstr "⌙ 구분선"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Small"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Spacing"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "⌙ Spread"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "⌙ Style"
msgstr "⌙ 스타일"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ UTM Campaign"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ UTM Medium"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ UTM Source"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Users"
msgstr "⌙ 사용자"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "⌙ Video"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Width"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "└ Height"
msgstr ""
