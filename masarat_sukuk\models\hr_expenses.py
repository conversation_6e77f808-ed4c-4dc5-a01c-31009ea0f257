from odoo import fields, models, api, _
from odoo.exceptions import UserError, ValidationError


class InheritHrExpense(models.Model):
    _inherit = 'hr.expense'

    payment_method = fields.Selection([('cash', 'Cash'), ('check', 'Check')])
    suke_id = fields.Many2one('sukuk.management.page')

    def action_move_create(self):
        '''
        main function that is called when trying to create the accounting entries related to an expense
        '''
        move_group_by_sheet = self._get_account_move_by_sheet()

        move_line_values_by_expense = self._get_account_move_line_values()

        ###### added by abdalwahed at 2023-06-21 to link the suke with expenses

        for ele in move_line_values_by_expense.values():
            for elem in ele:
                expens_id = self.env['hr.expense'].search([('id','=',elem['expense_id'])])
                if expens_id.payment_method == 'check' and not expens_id.suke_id:
                    raise ValidationError('This type of Expenses needs to be linked with Check!')
                if expens_id.suke_id:
                    suke = ' check' + '-' + str(expens_id.suke_id.bank_id.name) + '-' + str(expens_id.suke_id.branch_id.name) + '- Serial No:' + str(expens_id.suke_id.serial_no)
                    elem['name'] = elem['name']+suke
        ############################################################################

        for expense in self:
            # get the account move of the related sheet
            move = move_group_by_sheet[expense.sheet_id.id]

            # get move line values
            move_line_values = move_line_values_by_expense.get(expense.id)

            # link move lines to move, and move to expense sheet
            move.write({'line_ids': [(0, 0, line) for line in move_line_values]})
            expense.sheet_id.write({'account_move_id': move.id})

            if expense.payment_mode == 'company_account':
                expense.sheet_id.paid_expense_sheets()

        # post the moves
        for move in move_group_by_sheet.values():
            move._post()

        return move_group_by_sheet



