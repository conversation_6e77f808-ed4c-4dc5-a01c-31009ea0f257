# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * bus
# 
# Translators:
# <PERSON>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:17+0000\n"
"PO-Revision-Date: 2018-09-21 13:17+0000\n"
"Last-Translator: <PERSON>, 2018\n"
"Language-Team: Serbian (https://www.transifex.com/odoo/teams/41243/sr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: bus
#: sql_constraint:bus.presence:0
msgid "A user can only have one IM status."
msgstr ""

#. module: bus
#: selection:bus.presence,status:0
msgid "Away"
msgstr ""

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__channel
msgid "Channel"
msgstr "Канал"

#. module: bus
#: model:ir.model,name:bus.model_bus_bus
msgid "Communication Bus"
msgstr ""

#. module: bus
#: model:ir.model,name:bus.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__create_uid
msgid "Created by"
msgstr ""

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__create_date
msgid "Created on"
msgstr "Kreiran"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__display_name
#: model:ir.model.fields,field_description:bus.field_bus_presence__display_name
msgid "Display Name"
msgstr ""

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__id
#: model:ir.model.fields,field_description:bus.field_bus_presence__id
msgid "ID"
msgstr "ID"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__status
#: model:ir.model.fields,field_description:bus.field_res_partner__im_status
#: model:ir.model.fields,field_description:bus.field_res_users__im_status
msgid "IM Status"
msgstr ""

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus____last_update
#: model:ir.model.fields,field_description:bus.field_bus_presence____last_update
msgid "Last Modified on"
msgstr ""

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__last_poll
msgid "Last Poll"
msgstr ""

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__last_presence
msgid "Last Presence"
msgstr ""

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__write_uid
msgid "Last Updated by"
msgstr ""

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__write_date
msgid "Last Updated on"
msgstr ""

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__message
msgid "Message"
msgstr "Poruka"

#. module: bus
#: selection:bus.presence,status:0
msgid "Offline"
msgstr ""

#. module: bus
#: selection:bus.presence,status:0
msgid "Online"
msgstr ""

#. module: bus
#: model:ir.model,name:bus.model_bus_presence
msgid "User Presence"
msgstr ""

#. module: bus
#: model:ir.model,name:bus.model_res_users
#: model:ir.model.fields,field_description:bus.field_bus_presence__user_id
msgid "Users"
msgstr "Korisnici"

#. module: bus
#: code:addons/bus/controllers/main.py:41
#, python-format
msgid "bus.Bus not available in test mode"
msgstr ""
