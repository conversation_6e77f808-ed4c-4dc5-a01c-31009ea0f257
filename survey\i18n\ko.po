# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* survey
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON><PERSON> CHOI <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON>, 2021
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:18+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_4
msgid "$100"
msgstr "$100"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_1
msgid "$20"
msgstr "$20"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_5
msgid "$200"
msgstr "$200"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_6
msgid "$300"
msgstr "$300"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_2
msgid "$50"
msgstr "$50"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_3
msgid "$80"
msgstr "$80"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "% completed"
msgstr "% 완료"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (사본)"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "%s certification passed"
msgstr "%s 인증 통과"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "%s challenge certification"
msgstr "%s 인증 도전"

#. module: survey
#: model:ir.actions.report,print_report_name:survey.certification_report
msgid "'Certification - %s' % (object.survey_id.display_name)"
msgstr "'%s - 인증서' % (object.survey_id.display_name)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug2
msgid "10 kg"
msgstr "10 kg"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug2
msgid "100 years"
msgstr "100 년"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q1_sug3
msgid "1055"
msgstr "1055"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug3
msgid "116 years"
msgstr "116 년"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q1_sug1
msgid "1227"
msgstr "1227"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug4
msgid "127 years"
msgstr "127 년"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q1_sug2
msgid "1324"
msgstr "1324"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q1_sug1
msgid "1450 km"
msgstr "1450 km"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug3
msgid "16.2 kg"
msgstr "16.2 kg"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q1_sug2
msgid "3700 km"
msgstr "3700 km"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "403: Forbidden"
msgstr "403 : 접근 금지"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug4
msgid "47 kg"
msgstr "47 kg"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "4812"
msgstr "4812"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug1
msgid "5.7 kg"
msgstr "5.7 kg"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q1_sug3
msgid "6650 km"
msgstr "6650 km"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug1
msgid "99 years"
msgstr "99 년"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid ""
"<b>Certificate</b>\n"
"                            <br/>"
msgstr ""
"<b>인증서</b>\n"
"                            <br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid ""
"<br/>\n"
"                        <span>Score:</span>"
msgstr ""
"<br/>\n"
"                        <span>점수:</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid "<br/>by"
msgstr "<br/>작성자"

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p4_q6
msgid ""
"<div class=\"text-center\">\n"
"                <div class=\"media_iframe_video\" data-oe-expression=\"//www.youtube.com/embed/7y4T6yv5L1k?autoplay=0&amp;rel=0\" style=\"width: 50%;\">\n"
"                    <div class=\"css_editable_mode_display\"/>\n"
"                    <div class=\"media_iframe_video_size\" contenteditable=\"false\"/>\n"
"                    <iframe src=\"//www.youtube.com/embed/7y4T6yv5L1k?autoplay=0&amp;rel=0\" frameborder=\"0\" contenteditable=\"false\"/>\n"
"                </div><br/>\n"
"            </div>\n"
"        "
msgstr ""
"<div class=\"text-center\">\n"
"                <div class=\"media_iframe_video\" data-oe-expression=\"//www.youtube.com/embed/7y4T6yv5L1k?autoplay=0&amp;rel=0\" style=\"width: 50%;\">\n"
"                    <div class=\"css_editable_mode_display\"/>\n"
"                    <div class=\"media_iframe_video_size\" contenteditable=\"false\"/>\n"
"                    <iframe src=\"//www.youtube.com/embed/7y4T6yv5L1k?autoplay=0&amp;rel=0\" frameborder=\"0\" contenteditable=\"false\"/>\n"
"                </div><br/>\n"
"            </div>\n"
"        "

#. module: survey
#: model:mail.template,body_html:survey.mail_template_certification
msgid ""
"<div style=\"background:#F0F0F0;color:#515166;padding:10px 0px;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"    <table style=\"width:600px;margin:5px auto;\">\n"
"        <tbody>\n"
"            <tr><td>\n"
"                <!-- We use the logo of the company that created the survey (to handle multi company cases) -->\n"
"                <a href=\"/\"><img t-attf-src=\"/logo.png?company={{ object.survey_id.create_uid.company_id.id }}\" style=\"vertical-align:baseline;max-width:100px;\"/></a>\n"
"            </td><td style=\"text-align:right;vertical-align:middle;\">\n"
"                    Certification: <t t-out=\"object.survey_id.display_name or ''\">Feedback Form</t>\n"
"            </td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"    <table style=\"width:600px;margin:0px auto;background:white;border:1px solid #e1e1e1;\">\n"
"        <tbody>\n"
"            <tr><td style=\"padding:15px 20px 10px 20px;\">\n"
"                <p>Dear <span t-out=\"object.partner_id.name or 'participant'\">participant</span></p>\n"
"                <p>\n"
"                    Here is, in attachment, your certification document for\n"
"                        <strong t-out=\"object.survey_id.display_name or ''\">Feedback Form</strong>\n"
"                </p>\n"
"                <p>Congratulations for succeeding the test!</p>\n"
"            </td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"</div>\n"
"            "
msgstr ""

#. module: survey
#: model:mail.template,body_html:survey.mail_template_user_input_invite
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or 'participant'\">participant</t><br/><br/>\n"
"        <t t-if=\"object.survey_id.certification\">\n"
"            You have been invited to take a new certification.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            We are conducting a survey and your response would be appreciated.\n"
"        </t>\n"
"        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"            <a t-att-href=\"(object.get_start_url())\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                <t t-if=\"object.survey_id.certification\">\n"
"                    Start Certification\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    Start Survey\n"
"                </t>\n"
"            </a>\n"
"        </div>\n"
"        <t t-if=\"object.deadline\">\n"
"            Please answer the survey for <t t-out=\"format_date(object.deadline) or ''\">05/05/2021</t>.<br/><br/>\n"
"        </t>\n"
"        Thank you for your participation.\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-2x\" role=\"img\" aria-label=\"Numeric\" title=\"Numeric\">123..</i>"
msgstr "<i class=\"fa fa-2x\" role=\"img\" aria-label=\"Numeric\" title=\"Numeric\">123..</i>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-align-justify fa-4x\" role=\"img\" aria-label=\"Multiple "
"lines\" title=\"Multiple Lines\"/>"
msgstr ""
"<i class=\"fa fa-align-justify fa-4x\" role=\"img\" aria-label=\"Multiple "
"lines\" title=\"Multiple Lines\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_matrix
msgid "<i class=\"fa fa-bar-chart\"/> Graph"
msgstr "<i class=\"fa fa-bar-chart\"/> 그래프"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "<i class=\"fa fa-bar-chart\"/> Results"
msgstr "<i class=\"fa fa-bar-chart\"/> 결과"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-check-square-o fa-lg\"/> answer"
msgstr "<i class=\"fa fa-check-square-o fa-lg\"/> 답변"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-circle-o  fa-lg\"/> answer"
msgstr "<i class=\"fa fa-circle-o  fa-lg\"/> 답변"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-circle-o fa-lg\" role=\"img\" aria-label=\"Not checked\" "
"title=\"Not checked\"/>"
msgstr ""
"<i class=\"fa fa-circle-o fa-lg\" role=\"img\" aria-label=\"Not checked\" "
"title=\"Not checked\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "<i class=\"fa fa-close\"/> Close"
msgstr "<i class=\"fa fa-close\"/> 마감됨 "

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-dot-circle-o fa-lg\" role=\"img\" aria-label=\"Checked\" "
"title=\"Checked\"/>"
msgstr ""
"<i class=\"fa fa-dot-circle-o fa-lg\" role=\"img\" aria-label=\"Checked\" "
"title=\"Checked\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-dot-circle-o fa-lg\"/> answer"
msgstr "<i class=\"fa fa-dot-circle-o fa-lg\"/> 답변"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid ""
"<i class=\"fa fa-fw fa-trophy\" role=\"img\" aria-label=\"Download certification\" title=\"Download certification\"/>\n"
"                                        Download certification"
msgstr ""
"<i class=\"fa fa-fw fa-trophy\" role=\"img\" aria-label=\"Download certification\" title=\"Download certification\"/>\n"
"                                                인증서 내려받기"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date
msgid "<i class=\"fa fa-list-alt\"/> All Data"
msgstr "<i class=\"fa fa-list-alt\"/> 모든 데이터"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
#: model_terms:ir.ui.view,arch_db:survey.question_result_matrix
msgid "<i class=\"fa fa-list-alt\"/> Data"
msgstr "<i class=\"fa fa-list-alt\"/> 데이터"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date
msgid "<i class=\"fa fa-list-ol\"/> Most Common"
msgstr "<i class=\"fa fa-list-ol\"/> 가장 일반적인"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-minus fa-4x\" role=\"img\" aria-label=\"Single Line\" "
"title=\"Single Line\"/>"
msgstr ""
"<i class=\"fa fa-minus fa-4x\" role=\"img\" aria-label=\"Single Line\" "
"title=\"Single Line\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-square-o fa-lg\"/> answer"
msgstr "<i class=\"fa fa-square-o fa-lg\"/> 답변"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "<i class=\"fa fa-times\"/> Clear All Filters"
msgstr "<i class=\"fa fa-times\"/> 모든 필터 제거"

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p4_q3
msgid ""
"<p>\n"
"                <img class=\"img-fluid o_we_custom_image d-block mx-auto\" src=\"/survey/static/img/coniferous.jpg\"/><br/>\n"
"            </p>\n"
"        "
msgstr ""
"<p>\n"
"                <img class=\"img-fluid o_we_custom_image d-block mx-auto\" src=\"/survey/static/img/coniferous.jpg\"/><br/>\n"
"            </p>\n"
"        "

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p4_q4
msgid ""
"<p>\n"
"                <img class=\"img-fluid o_we_custom_image d-block mx-auto\" src=\"/survey/static/img/pinus_sylvestris.jpg\" style=\"width: 100%;\"/><br/>\n"
"            </p>\n"
"        "
msgstr ""
"<p>\n"
"                <img class=\"img-fluid o_we_custom_image d-block mx-auto\" src=\"/survey/static/img/pinus_sylvestris.jpg\" style=\"width: 100%;\"/><br/>\n"
"            </p>\n"
"        "

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p4
msgid ""
"<p>\n"
"                We like to say that the apple doesn't fall far from the tree, so here are trees.\n"
"            </p>\n"
"        "
msgstr ""
"<p>\n"
"                사과는 나무에서 멀리 떨어진 곳에서 얻을 수 없습니다. 여기 나무가 있습니다.\n"
"            </p>\n"
"        "

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p3
msgid "<p>An apple a day keeps the doctor away.</p>"
msgstr "<p>하루 하나의 사과가 건강의 비결입니다.</p>"

#. module: survey
#: model:survey.survey,description:survey.survey_demo_burger_quiz
msgid ""
"<p>Choose your favourite subject and show how good you are. Ready ?</p>"
msgstr ""

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p1_q4
msgid "<p>Just to categorize your answers, don't worry.</p>"
msgstr "<p>답변을 카테고리화 합니다. 걱정할 것 없습니다.</p>"

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p1
msgid ""
"<p>Some general information about you. It will be used internally for "
"statistics only.</p>"
msgstr ""

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p2
msgid "<p>Some questions about our company. Do you really know us?</p>"
msgstr ""

#. module: survey
#: model:survey.question,description:survey.vendor_certification_page_3
msgid "<p>Test your knowledge of our policies.</p>"
msgstr "<p>우리 정책에 대한 지식을 테스트합니다.</p>"

#. module: survey
#: model:survey.question,description:survey.vendor_certification_page_2
msgid "<p>Test your knowledge of our prices.</p>"
msgstr "<p>우리 가격에 대한 지식을 테스트합니다.</p>"

#. module: survey
#: model:survey.question,description:survey.vendor_certification_page_1
msgid "<p>Test your knowledge of your products!</p>"
msgstr "<p>품목에 대한 지식을 테스트합니다!</p>"

#. module: survey
#: model:survey.survey,description:survey.vendor_certification
msgid "<p>Test your vendor skills!</p>"
msgstr ""

#. module: survey
#: model:survey.question,description:survey.survey_feedback_p1
msgid ""
"<p>This section is about general information about you. Answering them helps"
" qualifying your answers.</p>"
msgstr ""

#. module: survey
#: model:survey.question,description:survey.survey_feedback_p2
msgid "<p>This section is about our eCommerce experience itself.</p>"
msgstr ""

#. module: survey
#: model:survey.survey,description:survey.survey_demo_quiz
msgid ""
"<p>This small quiz will test your knowledge about our Company. Be prepared "
"!</p>"
msgstr ""

#. module: survey
#: model:survey.survey,description:survey.survey_feedback
msgid ""
"<p>This survey allows you to give a feedback about your experience with our products.\n"
"    Filling it helps us improving your experience.</p>"
msgstr ""

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p5
msgid "<p>We may be interested by your input.</p>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<span attrs=\"{'invisible': [('is_time_limited', '=', False)]}\"> "
"seconds</span>"
msgstr "<span attrs=\"{'invisible': [('is_time_limited', '=', False)]}\">초</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid ""
"<span class=\"badge badge-primary only_left_radius\"><i class=\"fa fa-"
"filter\" role=\"img\" aria-label=\"Filter\" title=\"Filter\"/></span>"
msgstr ""
"<span class=\"badge badge-primary only_left_radius\"><i class=\"fa fa-"
"filter\" role=\"img\" aria-label=\"Filter\" title=\"Filter\"/></span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date
msgid "<span class=\"badge badge-secondary only_left_radius\">Average </span>"
msgstr "<span class=\"badge badge-secondary only_left_radius\">평균 </span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date
msgid "<span class=\"badge badge-secondary only_left_radius\">Maximum </span>"
msgstr "<span class=\"badge badge-secondary only_left_radius\">최고 </span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date
msgid "<span class=\"badge badge-secondary only_left_radius\">Minimum </span>"
msgstr "<span class=\"badge badge-secondary only_left_radius\">최저 </span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "<span class=\"fa fa-filter\"/>  Filters"
msgstr "<span class=\"fa fa-filter\"/>  필터"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"<span class=\"font-weight-bold text-muted ml-2 d-none d-md-inline\"> or "
"press Enter</span>"
msgstr ""
"<span class=\"font-weight-bold text-muted ml-2 d-none d-md-inline\"> 또는 엔터키를"
" 누르세요</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"<span class=\"font-weight-bold text-muted ml-2 d-none d-md-inline\">or press"
" Enter</span>"
msgstr ""
"<span class=\"font-weight-bold text-muted ml-2 d-none d-md-inline\"> 또는 엔터키를"
" 누르세요</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.res_partner_view_form
msgid ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_company_count', '&lt;', 2)]}\">Certifications</span>\n"
"                        <span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_company_count', '&gt;', 1)]}\">Certification</span>"
msgstr ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_company_count', '&lt;', 2)]}\">인증서</span>\n"
"                        <span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_company_count', '&gt;', 1)]}\">인증서</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.res_partner_view_form
msgid ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_count', '&lt;', 2)]}\">Certifications</span>\n"
"                        <span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_count', '&gt;', 1)]}\">Certification</span>"
msgstr ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_count', '&lt;', 2)]}\">인증서</span>\n"
"                        <span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_count', '&gt;', 1)]}\">인증서</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid ""
"<span class=\"o_survey_enter font-weight-bold text-muted ml-2\">or press "
"Enter</span>"
msgstr ""
"<span class=\"o_survey_enter font-weight-bold text-muted ml-2\"> 또는 엔터키를 "
"누르세요</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_selection_key
msgid ""
"<span class=\"o_survey_key text-center position-absolute bg-white rounded-"
"left py-0 pl-2\"><span class=\"text-primary text-center text-center w-100 "
"position-relative\">Key</span></span>"
msgstr ""
"<span class=\"o_survey_key text-center position-absolute bg-white rounded-"
"left py-0 pl-2\"><span class=\"text-primary text-center text-center w-100 "
"position-relative\">키</span></span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid ""
"<span class=\"o_survey_session_answer_count\">0</span>\n"
"                                     /"
msgstr ""
"<span class=\"o_survey_session_answer_count\">0</span>\n"
"                                     /"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid ""
"<span class=\"o_survey_session_error_invalid_code d-none\">Code is incorrect.</span>\n"
"                                    <span class=\"o_survey_session_error_closed d-none\">Session is finished.</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "<span class=\"text-muted\">Answers</span>"
msgstr "<span class=\"text-muted\">답변</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "<span class=\"text-muted\">Success</span>"
msgstr "<span class=\"text-muted\">성공</span>"

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p3_q1
msgid ""
"<span>\"Red\" is not a category, I know what you are trying to do ;)</span>"
msgstr "<span>\"빨간색\"은 카테고리가 아닙니다. 무엇을 시도하려고 하시는진 알겠어요 ;)</span>"

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p3_q6
msgid "<span>Best time to do it, is the right time to do it.</span>"
msgstr "<span>무엇인가를 하기 가장 좋은 시기는 바로 지금입니다.</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid "<span>Date</span>"
msgstr "<span>날짜</span>"

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p5_q1
msgid ""
"<span>If you don't like us, please try to be as objective as "
"possible.</span>"
msgstr "<span>마음에 들지 않더라도, 최대한 객관적으로 답변해주세요. </span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_retake
msgid "<span>Number of attemps left</span>:"
msgstr "<span>남은 시도 횟수</span> :"

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p2_q1
msgid "<span>Our famous Leader !</span>"
msgstr ""

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p3_q3
msgid "<span>Our sales people have an advantage, but you can do it !</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "<span>Right answer:</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "<span>Time limit for this survey: </span>"
msgstr "<span>이 설문에 대한 시간 제한 : </span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_open
msgid "<span>Waiting for attendees...</span>"
msgstr "<span>참가자 기다리는 중...</span>"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q4
msgid "A \"Citrus\" could give you ..."
msgstr "\"시트러스\" 가 제공하는 것은 ..."

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#, python-format
msgid "A label must be attached to only one question."
msgstr "꼬리표는 하나의 질문에만 첨부해야 합니다."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_positive_len_max
#: model:ir.model.constraint,message:survey.constraint_survey_question_positive_len_min
msgid "A length must be positive!"
msgstr "길이는 양수여야 합니다!"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_4
msgid "A little bit overpriced"
msgstr "조금 비싼"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_5
msgid "A lot overpriced"
msgstr "너무 비싼"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question_answer__answer_score
msgid ""
"A positive score indicates a correct choice; a negative or null score "
"indicates a wrong answer"
msgstr "양수 점수는 올바른 선택을 나타냅니다. 음수 또는 null 점수는 오답을 나타냅니다."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid "A problem has occurred"
msgstr "문제가 발생했습니다."

#. module: survey
#: code:addons/survey/models/survey_user_input.py:0
#, python-format
msgid "A question can either be skipped or answered, not both."
msgstr "질문은 대답하거나 건너뛸 수 있지만, 둘 다 할 수는 없습니다."

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2
msgid "About our ecommerce"
msgstr "전자 상거래 정보"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1
msgid "About you"
msgstr "본인 소개"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_access_mode
#: model:ir.model.fields,field_description:survey.field_survey_survey__access_mode
msgid "Access Mode"
msgstr "액세스 모드"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__access_token
msgid "Access Token"
msgstr "사용 권한 토큰"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_access_token_unique
msgid "Access token should be unique"
msgstr "접근 토큰은 고유해야 합니다"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_needaction
msgid "Action Needed"
msgstr "조치 필요"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__active
msgid "Active"
msgstr "활성"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_ids
msgid "Activities"
msgstr "활동"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "활동 예외 장식"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_state
msgid "Activity State"
msgstr "활동 상태"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_type_icon
msgid "Activity Type Icon"
msgstr "활동 유형 아이콘"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid ""
"Add a list of email of recipients (will not be converted into contacts). "
"Separated by commas, semicolons or newline..."
msgstr "수신자 이메일 목록을 추가합니다 (연락처로 변환되지 않음). 쉼표, 세미콜론 또는 개행 문자로 구분됩니다 ..."

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Add a new survey"
msgstr "새로운 설문 조사 추가하기"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Add a question"
msgstr "질문 추가하기"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Add a section"
msgstr "섹션 추가하기"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Add existing contacts..."
msgstr "기존 연락처 추가하기..."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__emails
msgid "Additional emails"
msgstr "추가 이메일"

#. module: survey
#: model:res.groups,name:survey.group_survey_manager
msgid "Administrator"
msgstr "관리자"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug1
msgid "Africa"
msgstr "아프리카"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q6
msgid ""
"After watching this video, will you swear that you are not going to "
"procrastinate to trim your hedge this year ?"
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col3
msgid "Agree"
msgstr "동의"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_scored_date_have_answers
msgid ""
"All \"Is a scored question = True\" and \"Question Type: Date\" questions "
"need an answer"
msgstr "모든 \"Is a scored question = True\" 및 \"Question Type: Date\" 질문에는 답변이 필요합니다."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_scored_datetime_have_answers
msgid ""
"All \"Is a scored question = True\" and \"Question Type: Datetime\" "
"questions need an answer"
msgstr ""
"모든 \"Is a scored question = True\" 및 \"Question Type: Datetime\" 질문에는 답변이 "
"필요합니다."

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_selection__all
msgid "All questions"
msgstr "모든 질문"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "All surveys"
msgstr "모든 설문조사"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Allow Comments"
msgstr "댓글 허용"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug2
msgid "Amenhotep"
msgstr "아멘호테프"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_user_input_unique_token
msgid "An access token must be unique!"
msgstr "접근 토큰은 고유해야 합니다!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_positive_answer_score
msgid "An answer score for a non-multiple choice question cannot be negative!"
msgstr "객관식 문제가 아닌 답의 점수는 음수가 될 수 없습니다!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_leaderboard
msgid "Anonymous"
msgstr "익명"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "Answer"
msgstr "답변"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__answer_type
msgid "Answer Type"
msgstr "답변 유형"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__deadline
msgid "Answer deadline"
msgstr "답변 마감시한"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__triggering_answer_id
msgid "Answer that will trigger the display of the current question."
msgstr "현재의 질문을 표시하기 위한 답변입니다. "

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "Answered"
msgstr "답변함"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__user_input_line_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__user_input_line_ids
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Answers"
msgstr "답변"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_answer_count
msgid "Answers Count"
msgstr "답변수"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__access_mode__public
msgid "Anyone with the link"
msgstr "링크가 있는 사람"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_gamification_challenge__challenge_category
msgid "Appears in"
msgstr "다음에 표시"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug1
msgid "Apple Trees"
msgstr "사과나무"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_row1
msgid "Apples"
msgstr "사과"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Archived"
msgstr "보관됨"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug4
msgid "Art & Culture"
msgstr "예술 & 문화"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug1
msgid "Arthur B. McDonald"
msgstr "아서 B. 맥도날드"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug2
msgid "Asia"
msgstr "아시아"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_attachment_count
msgid "Attachment Count"
msgstr "첨부 파일 수"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__attachment_ids
msgid "Attachments"
msgstr "첨부 파일"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__attempts_number
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Attempt n°"
msgstr "시도 n°"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_done_count
msgid "Attempts"
msgstr "시도"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Attempts Limit"
msgstr "시도 제한"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_user_input__nickname
msgid ""
"Attendee nickname, mainly used to identify him in the survey session "
"leaderboard."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Attendees are answering the question..."
msgstr "참석자들이 질문에 답변하는 중입니다..."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_speed_rating
msgid "Attendees get more points if they answer quickly"
msgstr "빨리 답변할수록 더 많은 점수를 얻을 수 있습니다."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__author_id
msgid "Author"
msgstr "작성자"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__author_id
msgid "Author of the message."
msgstr "문구 작성자."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__certification_mail_template_id
msgid ""
"Automated email sent to the user when he succeeds the certification, "
"containing his certification document."
msgstr "인증 문서가 포함된 인증 성공시 사용자에게 자동 이메일이 발송됩니다."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug3
msgid "Autumn"
msgstr "가을"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_duration_avg
msgid "Average Duration"
msgstr "평균 기간"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__answer_duration_avg
msgid "Average duration of the survey (in hours)"
msgstr "설문조사의 평균 기간 (시간)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_score_avg
msgid "Avg Score %"
msgstr "평균 점수 %"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug3
msgid "Avicii"
msgstr "아비치"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Back Button"
msgstr "뒤로가기 버튼"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__background_image
msgid "Background Image"
msgstr "배경 이미지"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "Badge"
msgstr "뱃지"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug3
msgid "Baobab Trees"
msgstr "바오밥 나무"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q2_sug1
msgid "Bees"
msgstr "꿀벌"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug4
msgid "Bricks"
msgstr "벽돌"

#. module: survey
#: model:survey.survey,title:survey.survey_demo_burger_quiz
msgid "Burger Quiz"
msgstr "버거 퀴즈"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_3
msgid "Cabinet with Doors"
msgstr "문이 있는 캐비닛"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_row1
msgid "Cactus"
msgstr "선인장"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__can_edit_body
msgid "Can Edit Body"
msgstr "본문 편집 가능"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4_q3
msgid "Can Humans ever directly see a photon ?"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Cancel"
msgstr "취소"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Candidates"
msgstr "응시자"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_tree
msgid "Certification"
msgstr "인증서"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_badge_id
msgid "Certification Badge"
msgstr "인증 배지"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_badge_id_dummy
msgid "Certification Badge "
msgstr "인증 배지"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "Certification Badge is not configured for the survey %(survey_name)s"
msgstr "이 설문 조사에는 인증서 배지가 설정되어있지 않습니다 %(survey_name)s"

#. module: survey
#: model:mail.template,report_name:survey.mail_template_certification
msgid "Certification Document"
msgstr "인증서"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid "Certification Failed"
msgstr "인증 불합격"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Certification Template"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid "Certification n°"
msgstr "인증서 n°"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_report_layout
msgid "Certification template"
msgstr "인증서 서식"

#. module: survey
#: model:mail.template,subject:survey.mail_template_certification
msgid "Certification: {{ object.survey_id.display_name }}"
msgstr "인증서: {{ object.survey_id.display_name }}"

#. module: survey
#: model:ir.actions.report,name:survey.certification_report
#: model:ir.model.fields.selection,name:survey.selection__gamification_challenge__challenge_category__certification
msgid "Certifications"
msgstr "인증"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_res_partner__certifications_count
#: model:ir.model.fields,field_description:survey.field_res_users__certifications_count
msgid "Certifications Count"
msgstr "인증 숫자"

#. module: survey
#: model:ir.actions.act_window,name:survey.res_partner_action_certifications
msgid "Certifications Succeeded"
msgstr "인증 성공함"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Certified"
msgstr "인증함"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_1
msgid "Chair floor protection"
msgstr "바닥 보호용 의자 덮개"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_header
msgid "Chart"
msgstr "차트"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__is_attempts_limited
#: model:ir.model.fields,help:survey.field_survey_user_input__is_attempts_limited
msgid "Check this option if you want to limit the number of attempts per user"
msgstr "사용자 당 시도 횟수를 제한하려면 이 옵션을 확인하십시오."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug2
msgid "China"
msgstr "중국"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Choices"
msgstr "선택"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__classic_blue
msgid "Classic Blue"
msgstr "클래식 블루"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__classic_gold
msgid "Classic Gold"
msgstr "클래식 골드"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__classic_purple
msgid "Classic Purple"
msgstr "클래식 퍼플"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_row3
msgid "Clementine"
msgstr "클레멘타인"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug4
msgid "Cliff Burton"
msgstr "클리프 버튼"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_form_view
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Close"
msgstr "닫기"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Close Live Session"
msgstr "실시간 세션 닫기"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_1
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Color"
msgstr "색상"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__color
msgid "Color Index"
msgstr "색상표"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_comments
msgid "Comment"
msgstr "댓글"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comment_count_as_answer
msgid "Comment Field is an Answer Choice"
msgstr "댓글 필드가 선택 답변입니다."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comments_message
msgid "Comment Message"
msgstr "댓글 메시지"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_res_partner__certifications_company_count
#: model:ir.model.fields,field_description:survey.field_res_users__certifications_company_count
msgid "Company Certifications Count"
msgstr "회사 인증 수"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__state__done
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Completed"
msgstr "완료됨"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Compose Email"
msgstr "이메일 작성"

#. module: survey
#: code:addons/survey/models/survey_user_input.py:0
#, python-format
msgid "Computing score requires a question in arguments."
msgstr "계산 점수에는 논증 질문이 필요합니다."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_conditional
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Conditional Display"
msgstr "조건부 표시"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_3
msgid "Conference chair"
msgstr "회의 의자"

#. module: survey
#: model_terms:gamification.badge,description:survey.vendor_certification_badge
msgid "Congratulations, you are now official vendor of MyCompany"
msgstr "축하합니다, 이제 MyCompany의 공식 공급업체가 되었습니다"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "Congratulations, you have passed the test!"
msgstr "축하합니다. 테스트를 통과했습니다!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Constraints"
msgstr "제약 사항"

#. module: survey
#: model:ir.model,name:survey.model_res_partner
msgid "Contact"
msgstr "연락처"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__has_conditional_questions
msgid "Contains conditional questions"
msgstr "조건부 질문 포함"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__body
msgid "Contents"
msgstr "컨텐츠"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Continue"
msgstr "계속"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid "Continue here"
msgstr "여기부터 계속"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug4
msgid "Cookies"
msgstr "쿠키"

#. module: survey
#. openerp-web
#: code:addons/survey/static/src/js/survey_session_manage.js:0
#, python-format
msgid "Copied !"
msgstr "복사되었습니다 !"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug3
msgid "Cornaceae"
msgstr "층층나무과"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_1
msgid "Corner Desk Right Sit"
msgstr "코너 데스크 오른쪽 시트"

#. module: survey
#. openerp-web
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__answer_is_correct
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
#, python-format
msgid "Correct"
msgstr "정답"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Correct Answer"
msgstr "정확한 답변"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_datetime
msgid "Correct date and time answer for this question."
msgstr "이 질문에 정확한 날짜와 시간을 답해주세요"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_date
msgid "Correct date answer"
msgstr "정확한 날짜 답변"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_date
msgid "Correct date answer for this question."
msgstr "이 질문에 정확한 날짜를 답해주세요"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_datetime
msgid "Correct datetime answer"
msgstr "정확한 일시 답변"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_numerical_box
msgid "Correct number answer for this question."
msgstr "이 질문에 정확한 숫자를 답해주세요"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_numerical_box
msgid "Correct numerical answer"
msgstr "정확한 숫자 정답"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_3
msgid "Correctly priced"
msgstr "정확한 가격"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug3
msgid "Cosmic rays"
msgstr "우주방사선선"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Create Live Session"
msgstr "실시간 세션 만들기"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_question__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_survey__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__create_uid
msgid "Created by"
msgstr "작성자"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__create_date
#: model:ir.model.fields,field_description:survey.field_survey_question__create_date
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__create_date
#: model:ir.model.fields,field_description:survey.field_survey_survey__create_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input__create_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__create_date
msgid "Created on"
msgstr "작성일자"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "Creating test token is not allowed for you."
msgstr "테스트 토큰을 만들 수 없습니다."

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid ""
"Creating token for anybody else than employees is not allowed for internal "
"surveys."
msgstr "내부 설문 조사는 직원 이외의 사람을 위한 토큰을 만들 수 없습니다."

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "Creating token for closed/archived surveys is not allowed."
msgstr "보관되었거나 비공개로 진행된 설문 조사에 대한 토큰을 만들 수 없습니다."

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid ""
"Creating token for external people is not allowed for surveys requesting "
"authentication."
msgstr "외부인을 위한 토큰 생성은 인증을 요청하는 설문 조사에 허용되지 않습니다."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_question_id
msgid "Current Question"
msgstr "현재 질문"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_question_start_time
msgid "Current Question Start Time"
msgstr "현재 질문 시작 시간"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_start_time
msgid "Current Session Start Time"
msgstr "현재 세션 시작 시간"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__is_time_limited
msgid "Currently only supported for live sessions."
msgstr "현재 실시간 세션에서만 지원됩니다."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid ""
"Customers will receive a new token and be able to completely retake the "
"survey."
msgstr "고객은 새 토큰을 받고 설문 조사를 완전히 다시 받을 수 있습니다."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Customers will receive the same token."
msgstr "고객은 동일한 토큰을 받게 됩니다."

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_5
msgid "Customizable Lamp"
msgstr "맞춤형 램프"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__date
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__date
msgid "Date"
msgstr "일자"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_date
msgid "Date answer"
msgstr "답변 날짜"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__datetime
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__datetime
msgid "Datetime"
msgstr "날짜시간"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_datetime
msgid "Datetime answer"
msgstr "날짜시간 답변"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_user_input__deadline
msgid "Datetime until customer can open the survey and submit answers"
msgstr "고객이 설문 조사를 열고 답변을 제출할 때까지의 날짜시간"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__deadline
msgid "Deadline"
msgstr "마감 시한"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__null_value
msgid "Default Value"
msgstr "기본값"

#. module: survey
#: model:ir.model.fields,help:survey.field_gamification_challenge__challenge_category
msgid "Define the visibility of the challenge through menus"
msgstr "메뉴를 통한 도전의 가시성 정의"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Delete"
msgstr "삭제"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__description
#: model:ir.model.fields,field_description:survey.field_survey_survey__description
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Description"
msgstr "설명"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Design easily your survey, send invitations and analyze answers."
msgstr "설문 조사를 쉽게 설계하고 초대장을 보내고 답변을 분석하십시오."

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_2
msgid "Desk Combination"
msgstr "책상 조합"

#. module: survey
#: model:ir.actions.act_window,name:survey.survey_user_input_line_action
#: model:ir.ui.menu,name:survey.menu_survey_response_line_form
msgid "Detailed Answers"
msgstr "자세한 답변"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col2
msgid "Disagree"
msgstr "동의 안함"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Display"
msgstr "표시"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__display_name
#: model:ir.model.fields,field_description:survey.field_survey_question__display_name
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__display_name
#: model:ir.model.fields,field_description:survey.field_survey_survey__display_name
#: model:ir.model.fields,field_description:survey.field_survey_user_input__display_name
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__display_name
msgid "Display Name"
msgstr "표시명"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__allow_value_image
msgid ""
"Display images in addition to answer label. Valid only for simple / multiple"
" choice questions."
msgstr ""

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_1
msgid "Do we sell Acoustic Bloc Screens?"
msgstr "어쿠스틱 블록 스크린을 판매합니까?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2_q3
msgid "Do you have any other comments, questions, or concerns ?"
msgstr "다른 의견, 질문 또는 우려 사항이 있습니까?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_5
msgid "Do you think we have missing products in our catalog? (not rated)"
msgstr "상품안내서에 누락된 제품이 있다고 생각하십니까? (평가되지 않음)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q2_sug2
msgid "Dogs"
msgstr "강아지"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q1
msgid "Dogwood is from which family of trees ?"
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug1
msgid "Douglas Fir"
msgstr "더글라스 퍼 나무"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_4
msgid "Drawer"
msgstr "서랍장"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Dropdown menu"
msgstr "드롭다운 메뉴"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_form_view
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Edit Survey"
msgstr "설문 조사 편집"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid "Edit in backend"
msgstr "백엔드에서 편집하기"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__email
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Email"
msgstr "이메일"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_mail_template_id
msgid "Email Template"
msgstr "이메일 서식"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__email_from
msgid "Email address of the sender."
msgstr "발신자의 이메일 주소입니다."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__description_done
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "End Message"
msgstr "메시지 종료"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__end_datetime
msgid "End date and time"
msgstr "종료 날짜와 시간"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "Enter Session Code"
msgstr "세션 코드 입력"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__constr_error_msg
msgid "Error message"
msgstr "오류 메시지"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug3
msgid "Europe"
msgstr "유럽"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug3
msgid "European Yew"
msgstr "유럽 주목 나무"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Except Test Entries"
msgstr "시험 항목 제외"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_partner_ids
msgid "Existing Partner"
msgstr "기존 파트너"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_emails
msgid "Existing emails"
msgstr "기존 이메일"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug2
msgid "Eyjafjallajökull (Iceland)"
msgstr "Eyjafjallajökull (아이슬란드)"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_2
msgid "Fanta"
msgstr "판타"

#. module: survey
#: model:survey.survey,title:survey.survey_feedback
msgid "Feedback Form"
msgstr "피드백 양식"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_row2
msgid "Ficus"
msgstr "고무나무"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__model_object_field
msgid "Field"
msgstr "필드"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "Filter question"
msgstr "질문 필터"

#. module: survey
#. openerp-web
#: code:addons/survey/static/src/js/survey_session_manage.js:0
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
#, python-format
msgid "Final Leaderboard"
msgstr "최종 점수획득판"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__copyvalue
msgid ""
"Final placeholder expression, to be copy-pasted in the desired template "
"field."
msgstr "최종 자리 표시자 표현. 원하는 서식 필드에 복사하여 붙여 넣을 수 있습니다."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "Finished surveys"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_follower_ids
msgid "Followers"
msgstr "팔로워"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_partner_ids
msgid "Followers (Partners)"
msgstr "팔로워 (파트너)"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "멋진 아이콘 폰트 예 : fa-tasks"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__text_box
msgid "Free Text"
msgstr "자유글"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_text_box
msgid "Free Text answer"
msgstr "자유글 답변"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__email_from
msgid "From"
msgstr "출발"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q4
msgid "From which continent is native the Scots pine (pinus sylvestris) ?"
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q1_sug1
msgid "Fruits"
msgstr "과일"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3
msgid "Fruits and vegetables"
msgstr "과일 및 야채"

#. module: survey
#: model:ir.model,name:survey.model_gamification_badge
msgid "Gamification Badge"
msgstr "업적 배지"

#. module: survey
#: model:ir.model,name:survey.model_gamification_challenge
msgid "Gamification Challenge"
msgstr "도전 과제 업적"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug1
msgid "Geography"
msgstr "지리"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_give_badge
msgid "Give Badge"
msgstr "배지 제공"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2_q3
msgid "Give the list of all types of wood we sell."
msgstr "우리가 판매하는 모든 목재 목록을 제공하십시오."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_open
msgid "Go to"
msgstr "이동하기"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug1
msgid "Good"
msgstr "좋음"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug4
msgid "Good value for money"
msgstr "돈을 위해 좋은 가치"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug2
msgid "Grapefruits"
msgstr "자몽"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "Graph"
msgstr "그래프"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Group By"
msgstr "그룹별"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_mode
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Handle existing"
msgstr "기존 처리"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q3_sug1
msgid "Hard"
msgstr "어려움"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__has_message
msgid "Has Message"
msgstr "메시지가 있습니다"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_2
msgid "Height"
msgstr "높이"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug3
msgid "Hemiunu"
msgstr "헤미우누"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug1
msgid "High quality"
msgstr "고품질"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug2
msgid "History"
msgstr "기록"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q3
msgid "How frequently do you buy products online ?"
msgstr "얼마나 자주 온라인으로 제품을 구매합니까?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2_q1
msgid "How long is the White Nile river?"
msgstr "백나일강의 길이가 얼마나 되나요?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_6
msgid ""
"How many chairs do you think we should aim to sell in a year (not rated)?"
msgstr "의자 항목의 연간 판매 목표 개수는 몇 개로 설정해야 한다고 생각하십니까 (미평가)? "

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_1
msgid "How many days is our money-back guarantee?"
msgstr "환불 보장 기간이 며칠입니까?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q4
msgid "How many times did you order products on our website ?"
msgstr "당사 웹 사이트에서 제품을 몇 번 주문 했습니까?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_4
msgid "How many versions of the Corner Desk do we have?"
msgstr "우리는 몇 개의 버전의 코너 데스크를 가지고 있습니까?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3_q3
msgid "How many years did the 100 years war last ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2_question_1
msgid "How much do we sell our Cable Management Box?"
msgstr "케이블 관리 박스는 얼마입니까?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q5
msgid "How often should you water those plants"
msgstr "이 식물들에게 얼마나 자주 물을 주어야합니까?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q4
msgid "How old are you ?"
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug4
msgid ""
"I actually don't like thinking. I think people think I like to think a lot. "
"And I don't. I do not like to think at all."
msgstr ""
"저는 생각하는 것을 좋아하지 않습니다. 제 생각에는 사람들은 제가 생각을 많이 한다고 생각하는 것 같아요. 저는 그렇지 않습니다. 저는 "
"생각하는것을 전혀 좋아하지 않아요."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug2
msgid ""
"I am fascinated by air. If you remove the air from the sky, all the birds "
"would fall to the ground. And all the planes, too."
msgstr "저는 공기에 매료되었습니다. 하늘에 공기가 없다면 모든 새는 땅으로 떨어질 것입니다. 모든 비행기도 마찬가지고요."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row5
msgid "I have added products to my wishlist"
msgstr "위시리스트에 제품을 추가했습니다"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug4
msgid "I have no idea, I'm a dog!"
msgstr "저는 전혀 몰라요, 저는 개입니다!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug3
msgid "I've been noticing gravity since I was very young !"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__id
#: model:ir.model.fields,field_description:survey.field_survey_question__id
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__id
#: model:ir.model.fields,field_description:survey.field_survey_survey__id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__id
msgid "ID"
msgstr "ID"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_exception_icon
msgid "Icon"
msgstr "아이콘"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "예외 활동을 표시하기 위한 아이콘"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__access_token
msgid "Identification token"
msgstr "식별 토큰"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__progression_mode
msgid ""
"If Number is selected, it will display the number of questions answered on "
"the total number of question to answer."
msgstr "숫자를 선택하면 답변해야 할 질문의 수와 응답한 질문의 수가 표시됩니다."

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_3
msgid ""
"If a customer purchases a 1 year warranty on 6 January 2020, when do we "
"expect the warranty to expire?"
msgstr "2020년 1월 6일에 고객이 1년 보증을 구매했다면 보증 만료일은 언제입니까?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_2
msgid ""
"If a customer purchases a product on 6 January 2020, what is the latest day "
"we expect to ship it?"
msgstr "고객이 2020년 1월 6일에 제품을 구매한 경우 제품이 배송될 것으로 예상되는 가장 늦은 날짜는 언제입니까?"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_needaction
#: model:ir.model.fields,help:survey.field_survey_survey__message_unread
msgid "If checked, new messages require your attention."
msgstr "만약 선택하였으면, 신규 메시지에 주의를 기울여야 합니다."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_error
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "이 옵션을 선택하면 일부 정보가 전달 오류를 생성합니다."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__save_as_email
msgid ""
"If checked, this option will save the user's answer as its email address."
msgstr "이 옵션을 선택하면 사용자의 답변은 이메일 주소로 저장됩니다."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__save_as_nickname
msgid "If checked, this option will save the user's answer as its nickname."
msgstr "이 옵션을 선택하면 사용자의 답변은 닉네임으로 저장됩니다."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__is_conditional
msgid ""
"If checked, this question will be displayed only \n"
"        if the specified conditional answer have been selected in a previous question"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__users_can_go_back
msgid "If checked, users can go back to previous pages."
msgstr "선택하면 사용자는 이전 페이지로 돌아갈 수 있습니다."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__survey_users_login_required
#: model:ir.model.fields,help:survey.field_survey_survey__users_login_required
msgid ""
"If checked, users have to login before answering even with a valid token."
msgstr "이 옵션을 선택하면 유효한 토큰으로도 응답하기 전에 로그인해야 합니다."

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p1
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p1_q1
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p2
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p2_q1
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p2_q2
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p2_q3
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p3
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p3_q1
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p3_q2
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p3_q3
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p4
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p4_q1
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p4_q2
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p4_q3
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p5
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p5_q1
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p5_q2
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p5_q3
#: model:survey.question,comments_message:survey.survey_demo_quiz_p1
#: model:survey.question,comments_message:survey.survey_demo_quiz_p1_q1
#: model:survey.question,comments_message:survey.survey_demo_quiz_p1_q2
#: model:survey.question,comments_message:survey.survey_demo_quiz_p1_q3
#: model:survey.question,comments_message:survey.survey_demo_quiz_p1_q4
#: model:survey.question,comments_message:survey.survey_demo_quiz_p2
#: model:survey.question,comments_message:survey.survey_demo_quiz_p2_q1
#: model:survey.question,comments_message:survey.survey_demo_quiz_p2_q2
#: model:survey.question,comments_message:survey.survey_demo_quiz_p2_q3
#: model:survey.question,comments_message:survey.survey_demo_quiz_p3
#: model:survey.question,comments_message:survey.survey_demo_quiz_p3_q1
#: model:survey.question,comments_message:survey.survey_demo_quiz_p3_q2
#: model:survey.question,comments_message:survey.survey_demo_quiz_p3_q3
#: model:survey.question,comments_message:survey.survey_demo_quiz_p3_q4
#: model:survey.question,comments_message:survey.survey_demo_quiz_p3_q5
#: model:survey.question,comments_message:survey.survey_demo_quiz_p3_q6
#: model:survey.question,comments_message:survey.survey_demo_quiz_p4
#: model:survey.question,comments_message:survey.survey_demo_quiz_p4_q1
#: model:survey.question,comments_message:survey.survey_demo_quiz_p4_q2
#: model:survey.question,comments_message:survey.survey_demo_quiz_p4_q3
#: model:survey.question,comments_message:survey.survey_demo_quiz_p4_q4
#: model:survey.question,comments_message:survey.survey_demo_quiz_p4_q5
#: model:survey.question,comments_message:survey.survey_demo_quiz_p4_q6
#: model:survey.question,comments_message:survey.survey_demo_quiz_p5
#: model:survey.question,comments_message:survey.survey_demo_quiz_p5_q1
#: model:survey.question,comments_message:survey.survey_feedback_p1
#: model:survey.question,comments_message:survey.survey_feedback_p1_q1
#: model:survey.question,comments_message:survey.survey_feedback_p1_q2
#: model:survey.question,comments_message:survey.survey_feedback_p1_q3
#: model:survey.question,comments_message:survey.survey_feedback_p1_q4
#: model:survey.question,comments_message:survey.survey_feedback_p2
#: model:survey.question,comments_message:survey.survey_feedback_p2_q1
#: model:survey.question,comments_message:survey.survey_feedback_p2_q2
#: model:survey.question,comments_message:survey.survey_feedback_p2_q3
#: model:survey.question,comments_message:survey.vendor_certification_page_1
#: model:survey.question,comments_message:survey.vendor_certification_page_1_question_1
#: model:survey.question,comments_message:survey.vendor_certification_page_1_question_2
#: model:survey.question,comments_message:survey.vendor_certification_page_1_question_3
#: model:survey.question,comments_message:survey.vendor_certification_page_1_question_4
#: model:survey.question,comments_message:survey.vendor_certification_page_1_question_5
#: model:survey.question,comments_message:survey.vendor_certification_page_2
#: model:survey.question,comments_message:survey.vendor_certification_page_2_question_1
#: model:survey.question,comments_message:survey.vendor_certification_page_2_question_2
#: model:survey.question,comments_message:survey.vendor_certification_page_2_question_3
#: model:survey.question,comments_message:survey.vendor_certification_page_3
#: model:survey.question,comments_message:survey.vendor_certification_page_3_question_1
#: model:survey.question,comments_message:survey.vendor_certification_page_3_question_2
#: model:survey.question,comments_message:survey.vendor_certification_page_3_question_3
#: model:survey.question,comments_message:survey.vendor_certification_page_3_question_4
#: model:survey.question,comments_message:survey.vendor_certification_page_3_question_5
#: model:survey.question,comments_message:survey.vendor_certification_page_3_question_6
#, python-format
msgid "If other, please specify:"
msgstr "다른 경우 다음을 지정하십시오 :"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__questions_selection
msgid ""
"If randomized is selected, add the number of random questions next to the "
"section."
msgstr "무작위 선택을 선택한 경우 섹션 옆에 무작위 질문 수를 추가하십시오."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__questions_selection
msgid ""
"If randomized is selected, you can configure the number of random questions "
"by section. This mode is ignored in live session."
msgstr "무작위 질문을 선택한 경우 영역별로 질문 수를 다르게 구성할 수 있습니다.  실시간 세션에서 이 모드는 무시됩니다. "

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "If you wish, you can"
msgstr "원한다면 할 수 있습니다."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__value_image
msgid "Image"
msgstr "이미지"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__allow_value_image
msgid "Images on answers"
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug1
msgid "Imhotep"
msgstr "임호텝"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug6
msgid "Impractical"
msgstr "실용적이지 않은"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__session_state__in_progress
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__state__in_progress
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "In Progress"
msgstr "진행 중"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q5
msgid "In the list below, select all the coniferous."
msgstr "아래 목록에서 침엽수를 모두 선택하십시오."

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q2
msgid "In which country did the bonsai technique develop ?"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__is_scored_question
msgid ""
"Include this question as part of quiz scoring. Requires an answer and answer"
" score to be taken into account."
msgstr "이 문제는 정답 및 답안 점수를 고려해서 채점에 포함됩니다. "

#. module: survey
#. openerp-web
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
#, python-format
msgid "Incorrect"
msgstr "잘못됨"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug7
msgid "Ineffective"
msgstr "효과적인"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_email
msgid "Input must be an email"
msgstr "이메일을 입력해야 합니다."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__invite_token
msgid "Invite token"
msgstr "초대 토큰"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__access_mode__token
msgid "Invited people only"
msgstr "초대된 사람 전용"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__is_mail_template_editor
msgid "Is Editor"
msgstr "편집자입니다 "

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_is_follower
msgid "Is Follower"
msgstr "팔로워임"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification
msgid "Is a Certification"
msgstr "인증서임"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__is_correct
msgid "Is a correct answer"
msgstr "정답 여부"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_page
msgid "Is a page?"
msgstr "페이지?"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__is_session_answer
msgid "Is in a Session"
msgstr "세션 중임"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_user_input__is_session_answer
msgid "Is that user input part of a survey session or not."
msgstr "사용자 입력이 설문조사 세션의 일부인지 여부"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q3
msgid "Is the wood of a coniferous hard or soft ?"
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug4
msgid "Istanbul"
msgstr "이스탄불"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row2
msgid "It is easy to find the product that I want"
msgstr "원하는 제품을 쉽게 찾을 수 있습니다"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug3
msgid "Iznogoud"
msgstr "이즈노고드"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug1
msgid ""
"I’ve never really wanted to go to Japan. Simply because I don’t like eating "
"fish. And I know that’s very popular out there in Africa."
msgstr ""
"사실 전 일본에 가고 싶었던 적이 없어요. 저는 생선을 좋아하지 않기 때문입니다. 그렇지만 생선이 아프리카에서 인기가 많다는 것을 잘 "
"알고 있습니다."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug1
msgid "Japan"
msgstr "일본"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "Join Session"
msgstr "세션 참가하기"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug2
msgid "Kim Jong-hyun"
msgstr "김종현"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug1
msgid "Kurt Cobain"
msgstr "커트 코베인"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__sequence
msgid "Label Sequence order"
msgstr "주문 순서 꼬리표"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__matrix_row_ids
msgid "Labels used for proposed choices: rows of matrix"
msgstr "제안된 선택에 사용되는 꼬리표 : 행렬의 행"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__suggested_answer_ids
msgid ""
"Labels used for proposed choices: simple choice, multiple choice and columns"
" of matrix"
msgstr "제안된 선택에 사용된 꼬리표 : 간단한 선택, 다중 선택 및 행렬 열"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__lang
msgid "Language"
msgstr "사용 언어"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_4
msgid "Large Desk"
msgstr "큰 책상"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite____last_update
#: model:ir.model.fields,field_description:survey.field_survey_question____last_update
#: model:ir.model.fields,field_description:survey.field_survey_question_answer____last_update
#: model:ir.model.fields,field_description:survey.field_survey_survey____last_update
#: model:ir.model.fields,field_description:survey.field_survey_user_input____last_update
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line____last_update
msgid "Last Modified on"
msgstr "최근 수정일"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_question__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_survey__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__write_date
#: model:ir.model.fields,field_description:survey.field_survey_question__write_date
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__write_date
#: model:ir.model.fields,field_description:survey.field_survey_survey__write_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input__write_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__last_displayed_page_id
msgid "Last displayed question/page"
msgstr "마지막으로 표시된 질문/페이지"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Late Activities"
msgstr "지연된 활동"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__questions_layout
msgid "Layout"
msgstr "배치"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Leaderboard"
msgstr "점수획득판"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_4
msgid "Legs"
msgstr "다리"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug2
msgid "Lemon Trees"
msgstr "레몬 트리"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__is_attempts_limited
#: model:ir.model.fields,field_description:survey.field_survey_user_input__is_attempts_limited
msgid "Limited number of attempts"
msgstr "시도 횟수 제한"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Live Session"
msgstr "실시간 세션"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Live Sessions"
msgstr "실시간 세션"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_users_login_required
#: model:ir.model.fields,field_description:survey.field_survey_survey__users_login_required
msgid "Login Required"
msgstr "로그인 필요"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_auth_required
msgid "Login required"
msgstr "로그인 필요"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__template_id
msgid "Mail Template"
msgstr "메일 서식"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_main_attachment_id
msgid "Main Attachment"
msgstr "주요 첨부 파일"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__constr_mandatory
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Mandatory Answer"
msgstr "필수 응답"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__matrix
msgid "Matrix"
msgstr "행렬"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__matrix_row_ids
msgid "Matrix Rows"
msgstr "행렬의 행"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__matrix_subtype
msgid "Matrix Type"
msgstr "행렬 유형"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_date
msgid "Max date cannot be smaller than min date!"
msgstr "최대 날짜가 최소 날짜보다 작을 수는 없습니다!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_datetime
msgid "Max datetime cannot be smaller than min datetime!"
msgstr "최대 날짜 시간은 최소 날짜 시간보다 작을 수 없습니다!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_length
msgid "Max length cannot be smaller than min length!"
msgstr "최대 길이가 최소 길이보다 작을 수는 없습니다!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_float
msgid "Max value cannot be smaller than min value!"
msgstr "최대값이 최소값보다 작을 수는 없습니다!"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_date
msgid "Maximum Date"
msgstr "최대 날짜"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_datetime
msgid "Maximum Datetime"
msgstr "최대 날짜시간"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_length_max
msgid "Maximum Text Length"
msgstr "최대 문자 길이"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_float_value
msgid "Maximum value"
msgstr "최대값"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "Maybe you were looking for"
msgstr "아마 당신이 찾고 있던"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_error
msgid "Message Delivery error"
msgstr "메시지 전송 오류"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_ids
msgid "Messages"
msgstr "메시지"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_date
msgid "Minimum Date"
msgstr "최소 날짜"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_datetime
msgid "Minimum Datetime"
msgstr "최소 날짜시간"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_length_min
msgid "Minimum Text Length"
msgstr "최소 문자 길이"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_float_value
msgid "Minimum value"
msgstr "최소값"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "Missed"
msgstr "놓침"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__modern_blue
msgid "Modern Blue"
msgstr "모던 블루"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__modern_gold
msgid "Modern Gold"
msgstr "모던 골드"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__modern_purple
msgid "Modern Purple"
msgstr "모던 퍼플"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q2_sug3
msgid "Mooses"
msgstr "이끼"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug4
msgid "Mount Elbrus (Russia)"
msgstr "엘브루스 산 (러시아)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug3
msgid "Mount Etna (Italy - Sicily)"
msgstr "에트나 산 (이탈리아 - 시칠리아)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug1
msgid "Mount Teide (Spain - Tenerife)"
msgstr "테이데 산 (스페인 - 테네리페)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug4
msgid "Mountain Pine"
msgstr "마운틴 파인"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__text_box
msgid "Multiple Lines Text Box"
msgstr "여러 줄 텍스트 상자"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Multiple choice with multiple answers"
msgstr "다중 답변을 위해 다중 선택"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Multiple choice with one answer"
msgstr "단일 답변을 위한 다중 선택"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__multiple_choice
msgid "Multiple choice: multiple answers allowed"
msgstr "다중 선택 : 복수 응답 허용"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__simple_choice
msgid "Multiple choice: only one answer"
msgstr "다중 선택 : 단일 답변"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__matrix_subtype__multiple
msgid "Multiple choices per row"
msgstr "행 당 다중 선택"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "내 활동 마감일"

#. module: survey
#: model:gamification.badge,name:survey.vendor_certification_badge
msgid "MyCompany Vendor"
msgstr "MyCompany 공급업체"

#. module: survey
#: model:survey.survey,title:survey.vendor_certification
msgid "MyCompany Vendor Certification"
msgstr "MyCompany 공급업체 인증"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "New"
msgstr "신규"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug3
msgid "New York"
msgstr "뉴욕"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_invite__existing_mode__new
msgid "New invite"
msgstr "신규 초대"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "다음 활동 캘린더 행사"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "다음 활동 마감일"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_summary
msgid "Next Activity Summary"
msgstr "다음 활동 요약"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_type_id
msgid "Next Activity Type"
msgstr "다음 활동 유형"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__nickname
msgid "Nickname"
msgstr "닉네임"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q6_sug2
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_1_choice_1
msgid "No"
msgstr "아니오"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "No attempts left."
msgstr "시도가 남지 않았습니다."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid "No question yet, come back later."
msgstr "아직 질문이 없습니다, 나중에 다시 시도해주세요."

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_question_form
msgid "No questions found"
msgstr "질문이 없습니다"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__no_scoring
msgid "No scoring"
msgstr "점수 없음"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.survey_question_answer_action
msgid "No survey labels found"
msgstr "설문 조사 꼬리표가 없습니다."

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.survey_user_input_line_action
msgid "No user input lines found"
msgstr "사용자 입력 내역이 없습니다"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q3_sug2
msgid "No, it's to small for the human eye."
msgstr "아니요, 인간의 눈에는 너무 작아요."

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_user_input
msgid "Nobody has replied to your surveys yet"
msgstr "아직 귀하의 설문에 응답한 사람이 없습니다"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug2
msgid "Norway Spruce"
msgstr "독일가문ㄴ비"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug2
msgid "Not Good, Not Bad"
msgstr "나쁘지 않아요"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__state__new
msgid "Not started yet"
msgstr "아직 시작 안함"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__progression_mode__number
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__numerical_box
msgid "Number"
msgstr "숫자"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_needaction_counter
msgid "Number of Actions"
msgstr "작업 수"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__attempts_limit
#: model:ir.model.fields,field_description:survey.field_survey_user_input__attempts_limit
msgid "Number of attempts"
msgstr "시도 횟수"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__column_nb
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Number of columns"
msgstr "단수"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_5
msgid "Number of drawers"
msgstr "서랍 수"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_error_counter
msgid "Number of errors"
msgstr "오류 횟수"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "조치가 필요한 메시지 수"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "전송 오류 메시지 수"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_unread_counter
msgid "Number of unread messages"
msgstr "읽지 않은 메시지 수"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__numerical_box
msgid "Numerical Value"
msgstr "숫자 값"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_numerical_box
msgid "Numerical answer"
msgstr "답변 수"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date
msgid "Occurrence"
msgstr "발생"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_5
msgid "Office Chair Black"
msgstr "사무실용 검정색 의자"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug1
msgid "Once a day"
msgstr "하루에 한 번"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_sug1
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug3
msgid "Once a month"
msgstr "한달에 한 번"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_sug2
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug2
msgid "Once a week"
msgstr "일주일에 한 번"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug4
msgid "Once a year"
msgstr "일년에 한 번"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__matrix_subtype__simple
msgid "One choice per row"
msgstr "행 당 하나 선택"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_layout__page_per_question
msgid "One page per question"
msgstr "질문 당 하나의 페이지"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_layout__page_per_section
msgid "One page per section"
msgstr "섹션 당 하나의 페이지"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_layout__one_page
msgid "One page with all the questions"
msgstr "모든 질문이 담긴 한 페이지"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "Only survey users can manage sessions."
msgstr "설문 조사 사용자만 세션을 관리할 수 있습니다."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Open Session Manager"
msgstr "세션 관리자 열기"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"이메일 전송 시 번역 기능 (ISO code) 을 선택할 수 있습니다. 설정하지 않을 경우, 영어 버전을 사용합니다. 적합한 언어를 "
"제공하기 위한 다음과 같이 예시용 메시지를 사용하도록 합니다. {{ object.partner_id.lang }}."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__null_value
msgid "Optional value to use if the target field is empty"
msgstr "대상 필드가 비어있을 경우에 사용할 선택 값"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Options"
msgstr "옵션"

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/models/survey_question.py:0
#, python-format
msgid "Other (see comments)"
msgstr "기타 (의견 참조)"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2
msgid "Our Company in a few questions ..."
msgstr "몇 가지 질문으로 보는 우리 회사 ..."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__mail_server_id
msgid "Outgoing mail server"
msgstr "보내는 메일 서버"

#. module: survey
#. openerp-web
#: code:addons/survey/static/src/js/survey_result.js:0
#, python-format
msgid "Overall Performance"
msgstr "전반적인 실적"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug5
msgid "Overpriced"
msgstr "고가"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__page_id
msgid "Page"
msgstr "페이지"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__page_ids
msgid "Pages"
msgstr "페이지"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug4
msgid "Papyrus"
msgstr "파피루스"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "Partial"
msgstr "부분"

#. module: survey
#. openerp-web
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
#, python-format
msgid "Partially"
msgstr "부분적으로"

#. module: survey
#: model:mail.template,subject:survey.mail_template_user_input_invite
msgid "Participate to {{ object.survey_id.display_name }} survey"
msgstr "{{ object.survey_id.display_name }} 설문조사에 참여"

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_user_input
#: model:ir.ui.menu,name:survey.menu_survey_type_form1
#: model:ir.ui.menu,name:survey.survey_menu_user_inputs
msgid "Participations"
msgstr "참여"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__partner_id
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Partner"
msgstr "파트너"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
#, python-format
msgid "Passed"
msgstr "합격함"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Pay attention to the host screen until the next question."
msgstr "다음 질문이 나올 때까지 주최자의 화면에 집중해주세요."

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__progression_mode__percent
msgid "Percentage"
msgstr "비율"

#. module: survey
#. openerp-web
#: code:addons/survey/static/src/js/survey_result.js:0
#, python-format
msgid "Performance by Section"
msgstr "섹션별 성능"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q6_sug3
msgid "Perhaps"
msgstr "아마도"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug2
msgid "Peter W. Higgs"
msgstr "피터 W. 히기스"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p1_q1
msgid "Pick a subject"
msgstr "주제 선택"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "Pie Graph"
msgstr "파이 차트"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug1
msgid "Pinaceae"
msgstr "침엽수"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__copyvalue
msgid "Placeholder Expression"
msgstr "사전 표시 문자 표현"

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid "Please enter at least one valid recipient."
msgstr "적어도 한 명 이상의 수신자를 입력하십시오."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid ""
"Please make sure you have at least one question in your survey. You also "
"need at least one section if you chose the \"Page per section\" layout.<br/>"
msgstr ""
"설문 조사에 질문이 하나 이상 있는지 확인하십시오. \"섹션 당 페이지\" 배치를 선택한 경우 하나 이상의 섹션이 필요합니다.<br/>"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3
msgid "Policies"
msgstr "방침"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug1
msgid "Pomelos"
msgstr "포멜로"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug8
msgid "Poor quality"
msgstr "저품질"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__predefined_question_ids
msgid "Predefined Questions"
msgstr "미리 정의된 질문"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Preview"
msgstr "미리보기"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2
msgid "Prices"
msgstr "가격"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Print"
msgstr "인쇄"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1
msgid "Products"
msgstr "품목"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__progression_mode
msgid "Progression Mode"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__question_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__question_id
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Question"
msgstr "질문"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__matrix_question_id
msgid "Question (as matrix row)"
msgstr "질문 (행렬의 행)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_question_answer_count
msgid "Question Answers Count"
msgstr "질답 수"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Question Time Limit"
msgstr "질문 시간 제한"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__question_time_limit_reached
msgid "Question Time Limit Reached"
msgstr "질문 제한 시간에 도달했습니다"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__question_type
msgid "Question Type"
msgstr "질문 유형"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__triggering_question_id
msgid ""
"Question containing the triggering answer to display the current question."
msgstr "이 답변이 선택된 답을 했을 때 현재의 질문을 표시합니다."

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_question_form
#: model:ir.model.fields,field_description:survey.field_survey_question__question_ids
#: model:ir.model.fields,field_description:survey.field_survey_survey__question_ids
#: model:ir.ui.menu,name:survey.menu_survey_question_form1
#: model:ir.ui.menu,name:survey.survey_menu_questions
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Questions"
msgstr "질문"

#. module: survey
#: model:survey.survey,title:survey.survey_demo_quiz
msgid "Quiz about our Company"
msgstr "우리 회사에 대한 퀴즈"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_success
msgid "Quizz Passed"
msgstr "퀴즈 통과"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Quizz passed"
msgstr "퀴즈 통과"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__random_questions_count
msgid "Random questions count"
msgstr "무작위 질문 수"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_selection__random
msgid "Randomized per section"
msgstr "섹션 당 무작위"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__session_state__ready
msgid "Ready"
msgstr "준비"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__partner_ids
msgid "Recipients"
msgstr "수신인"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_count
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Registered"
msgstr "등록함"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__render_model
msgid "Rendering Model"
msgstr "렌더링 모델"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Reopen"
msgstr "다시 열기"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_text
msgid "Resend Comment"
msgstr "의견 재전송"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Resend Invitation"
msgstr "초대장 재전송"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_invite__existing_mode__resend
msgid "Resend invite"
msgstr "초대장 재전송"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__user_id
msgid "Responsible"
msgstr "담당자"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_user_id
msgid "Responsible User"
msgstr "담당 사용자"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "Results Overview"
msgstr "결과 개요"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_retake
msgid "Retry"
msgstr "재시도"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_speed_rating
msgid "Reward quick answers"
msgstr "빠른 답변에 대한 보상"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "Rewards for challenges"
msgstr "도전 과제에 대한 포상"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "Right answer:"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "Right answers:"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__matrix_row_id
msgid "Row answer"
msgstr "행 대답"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row1"
msgstr "행1"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row2"
msgstr "행2"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row3"
msgstr "행3"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Rows"
msgstr "행"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS 전송 에러"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug4
msgid "Salicaceae"
msgstr "버드나무과"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__save_as_email
msgid "Save as user email"
msgstr "사용자 이메일로 저장"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__save_as_nickname
msgid "Save as user nickname"
msgstr "사용자 닉네임으로 저장"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug3
msgid "Sciences"
msgstr "과학"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_score
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__answer_score
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "Score"
msgstr "점수"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_percentage
msgid "Score (%)"
msgstr "점수 (%)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__answer_score
msgid "Score for this choice"
msgstr "이 선택에 대한 점수"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_score
msgid "Score value for a correct answer to this question."
msgstr "이 질문에 대한 답변의 점수입니다."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_scored_question
msgid "Scored"
msgstr "득점"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__scoring_type
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_type
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Scoring"
msgstr "채점"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scoring_type
msgid "Scoring Type"
msgstr "채점 유형"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__scoring_with_answers
msgid "Scoring with answers at the end"
msgstr "끝에 답변으로 채점"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__scoring_without_answers
msgid "Scoring without answers at the end"
msgstr "마지막에 답이 없는 채점"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_search
msgid "Search Label"
msgstr "꼬리표 검색"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Search Question"
msgstr "질문 검색"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Search Survey"
msgstr "설문 조사 검색"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
msgid "Search User input lines"
msgstr "사용자 입력 내역 검색"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__page_id
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Section"
msgstr "구분"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__question_and_page_ids
msgid "Sections and Questions"
msgstr "섹션과 질문"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "See results"
msgstr "결과 보기"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_3
msgid "Select all the available customizations for our Customizable Desk"
msgstr "사용자 지정 가능한 데스크톱에 사용할 수 있는 모든 사용자 지정을 선택합니다."

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_2
msgid "Select all the existing products"
msgstr "기존 제품을 모두 선택하십시오"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2_question_2
msgid "Select all the products that sell for $100 or more"
msgstr "100달러 이상에 판매되는 모든 제품 선택"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__model_object_field
msgid ""
"Select target field from the related document model.\n"
"If it is a relationship field you will be able to select a target field at the destination of the relationship."
msgstr ""
"관련 문서 모델에서 대상 필드를 선택하십시오.\n"
"만약 관계 필드인 경우 관계 대상에서 필드를 선택할 수 있습니다."

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q3
msgid "Select trees that made more than 20K sales this year"
msgstr "올해 20만 건 이상의 매출을 올린 나무를 선택하십시오."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__questions_selection
#: model:ir.model.fields,field_description:survey.field_survey_survey__questions_selection
msgid "Selection"
msgstr "선택"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Send"
msgstr "보내기"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__sequence
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__question_sequence
msgid "Sequence"
msgstr "순서"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_code
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Session Code"
msgstr "세션 코드"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_link
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Session Link"
msgstr "세션 링크"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_state
msgid "Session State"
msgstr "세션 상태"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_session_code_unique
msgid "Session code should be unique"
msgstr "세션 코드는 고유해야 합니다"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug1
msgid "Shanghai"
msgstr "상하이"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Share"
msgstr "공유"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comments_allowed
msgid "Show Comments Field"
msgstr "의견 필드 표시"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_show_leaderboard
msgid "Show Session Leaderboard"
msgstr "세션 점수획득판 보기"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Show all records which has next action date is before today"
msgstr "다음 행동 날짜가 오늘 이전 인 모든 기록보기"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__char_box
msgid "Single Line Text Box"
msgstr "한줄 텍스트 박스"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__skipped
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "Skipped"
msgstr "건너뛰기"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q3_sug2
msgid "Soft"
msgstr "부드러움"

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid "Some emails you just entered are incorrect: %s"
msgstr "방금 입력한 일부 이메일이 잘못되었습니다 : %s"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_header
msgid "Sorry, no one answered this survey yet."
msgstr "죄송합니다, 아직 아무도 이 설문에 답변하지 않았습니다."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Sorry, you have not been fast enough."
msgstr "죄송합니다, 조금만 빠르게 진행해주세요."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug4
msgid "South America"
msgstr "남아메리카"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug4
msgid "South Korea"
msgstr "대한민국"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q1_sug3
msgid "Space stations"
msgstr "우주 정거장"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug1
msgid "Spring"
msgstr "봄"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p1
msgid "Start"
msgstr "시작"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "Start Certification"
msgstr "인증 시작"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "Start Survey"
msgstr "설문 조사 시작"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__start_datetime
msgid "Start date and time"
msgstr "시작 날짜와 시간"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__state
msgid "Status"
msgstr "상태"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"활동에 기조한 상태입니다\n"
"기한초과: 이미 기한이 지났습니다\n"
"오늘: 활동 날짜가 오늘입니다\n"
"계획: 향후 활동입니다."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_row2
msgid "Strawberries"
msgstr "딸기"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__sub_model_object_field
msgid "Sub-field"
msgstr "보조 필드"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__sub_object
msgid "Sub-model"
msgstr "보조 모델"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__subject
msgid "Subject"
msgstr "제목"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Subject..."
msgstr "제목..."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Submit"
msgstr "제출"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__success_count
msgid "Success"
msgstr "성공"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__scoring_success_min
msgid "Success %"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__success_ratio
msgid "Success Ratio"
msgstr "성공률"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "Success rate:"
msgstr "성공률 :"

#. module: survey
#: model:ir.actions.act_window,name:survey.survey_question_answer_action
#: model:ir.ui.menu,name:survey.menu_survey_label_form1
msgid "Suggested Values"
msgstr "제안된 값"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__suggested_answer_id
msgid "Suggested answer"
msgstr "제안된 답변"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__value
msgid "Suggested value"
msgstr "제안된 값"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__suggestion
msgid "Suggestion"
msgstr "제안"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug2
msgid "Summer"
msgstr "여름"

#. module: survey
#: model:ir.model,name:survey.model_survey_survey
#: model:ir.model.fields,field_description:survey.field_gamification_badge__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_question__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__survey_id
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_tree
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Survey"
msgstr "설문 조사"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_response_line_view_tree
msgid "Survey Answer Line"
msgstr "설문 조사 답변 내역"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_gamification_badge__survey_ids
msgid "Survey Ids"
msgstr "설문 조사 ID"

#. module: survey
#: model:ir.model,name:survey.model_survey_invite
msgid "Survey Invitation Wizard"
msgstr "설문 조사 초대 마법사"

#. module: survey
#: model:ir.model,name:survey.model_survey_question_answer
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_tree
msgid "Survey Label"
msgstr "설문 조사 제목"

#. module: survey
#: model:ir.model,name:survey.model_survey_question
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_tree
msgid "Survey Question"
msgstr "설문 조사 질문"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Survey Time Limit"
msgstr "설문조사 시간 제한"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__survey_time_limit_reached
msgid "Survey Time Limit Reached"
msgstr "설문조사 시간 제한에 도달했습니다"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__title
msgid "Survey Title"
msgstr "설문 조사 제목"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_start_url
msgid "Survey URL"
msgstr "설문조사 URL"

#. module: survey
#: model:ir.model,name:survey.model_survey_user_input
msgid "Survey User Input"
msgstr "설문 조사 사용자 입력"

#. module: survey
#: model:ir.model,name:survey.model_survey_user_input_line
msgid "Survey User Input Line"
msgstr "설문 조사 사용자 입력 내역"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_tree
msgid "Survey User inputs"
msgstr "설문 조사 사용자 입력"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_matrix
msgid "Survey filter"
msgstr "설문 조사 필터"

#. module: survey
#: model:ir.actions.server,name:survey.survey_action_server_clean_test_answers
msgid "Survey: Clean test answers"
msgstr "설문 조사 : 연습용 답변 제거"

#. module: survey
#: model:mail.template,name:survey.mail_template_user_input_invite
msgid "Survey: Invite"
msgstr "설문조사: 초대"

#. module: survey
#: model:mail.template,name:survey.mail_template_certification
msgid "Survey: Send certification by email"
msgstr ""

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_form
#: model:ir.ui.menu,name:survey.menu_survey_form
#: model:ir.ui.menu,name:survey.menu_surveys
msgid "Surveys"
msgstr "설문 조사"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug3
msgid "Takaaki Kajita"
msgstr "Takaaki Kajita"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Test"
msgstr "테스트"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Test Entries"
msgstr "시험 항목"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__test_entry
msgid "Test Entry"
msgstr "시험 항목"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__char_box
msgid "Text"
msgstr "문자"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_char_box
msgid "Text answer"
msgstr "답변글"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "Thank you!"
msgstr "감사합니다!"

#. module: survey
#: code:addons/survey/models/survey_user_input.py:0
#, python-format
msgid "The answer must be in the right type"
msgstr "오른쪽 유형의 답변이어야 합니다."

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p1
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p1_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p2
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p2_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p2_q2
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p2_q3
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p3
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p3_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p3_q2
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p3_q3
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p4
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p4_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p4_q2
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p4_q3
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p5
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p5_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p5_q2
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p5_q3
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p1
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p1_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p1_q2
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p1_q3
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p1_q4
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p2
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p2_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p2_q2
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p2_q3
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p3
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p3_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p3_q2
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p3_q3
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p3_q4
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p3_q5
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p3_q6
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p4
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p4_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p4_q2
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p4_q3
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p4_q4
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p4_q5
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p4_q6
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p5
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p5_q1
#: model:survey.question,validation_error_msg:survey.survey_feedback_p1
#: model:survey.question,validation_error_msg:survey.survey_feedback_p1_q1
#: model:survey.question,validation_error_msg:survey.survey_feedback_p1_q2
#: model:survey.question,validation_error_msg:survey.survey_feedback_p1_q3
#: model:survey.question,validation_error_msg:survey.survey_feedback_p1_q4
#: model:survey.question,validation_error_msg:survey.survey_feedback_p2
#: model:survey.question,validation_error_msg:survey.survey_feedback_p2_q1
#: model:survey.question,validation_error_msg:survey.survey_feedback_p2_q2
#: model:survey.question,validation_error_msg:survey.survey_feedback_p2_q3
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_1
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_1_question_1
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_1_question_2
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_1_question_3
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_1_question_4
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_1_question_5
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_2
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_2_question_1
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_2_question_2
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_2_question_3
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_3
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_3_question_1
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_3_question_2
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_3_question_3
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_3_question_4
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_3_question_5
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_3_question_6
#, python-format
msgid "The answer you entered is not valid."
msgstr "입력하신 답변이 유효하지 않습니다."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_attempts_limit_check
msgid ""
"The attempts limit needs to be a positive number if the survey has a limited"
" number of attempts."
msgstr "설문 조사에 제한된 횟수의 시도가 있는 경우 시도 횟수는 양수여야 합니다."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_badge_uniq
msgid "The badge for each survey should be unique!"
msgstr "각 설문 조사의 배지는 고유해야 합니다!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row4
msgid "The checkout process is clear and secure"
msgstr "체크아웃 과정은 명확하고 안전합니다"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_question_id
msgid "The current question of the survey session."
msgstr "설문조사 세션의 현재 질문입니다."

#. module: survey
#. openerp-web
#: code:addons/survey/static/src/js/survey_form.js:0
#, python-format
msgid "The date you selected is greater than the maximum date: "
msgstr "선택한 날짜가 최대 날짜보다 큽니다."

#. module: survey
#. openerp-web
#: code:addons/survey/static/src/js/survey_form.js:0
#, python-format
msgid "The date you selected is lower than the minimum date: "
msgstr "선택한 날짜가 최소 날짜보다 작습니다."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__description
msgid ""
"The description will be displayed on the home page of the survey. You can "
"use this to give the purpose and guidelines to your candidates before they "
"start it."
msgstr "설명은 설문 조사의 홈 페이지에 표시됩니다. 이를 사용하여 지원자가 시작하기 전에 목적과 지침을 제공할 수 있습니다."

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid "The following customers have already received an invite"
msgstr "다음 고객은 이미 초대를 받았습니다"

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid "The following emails have already received an invite"
msgstr "다음 이메일은 이미 초대를 받았습니다"

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid ""
"The following recipients have no user account: %s. You should create user "
"accounts for them or allow external signup in configuration."
msgstr "다음 수신자에게는 사용자 계정이 없습니다 : %s. 사용자 계정을 만들거나 환경 설정시 외부 가입을 허용해야 합니다."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row1
msgid "The new layout and design is fresh and up-to-date"
msgstr "새로운 배치와 디자인은 신선하고 최신입니다"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "The page you were looking for could not be authorized."
msgstr "찾으려는 페이지를 승인할 수 없습니다."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_scoring_success_min_check
msgid "The percentage of success has to be defined between 0 and 100."
msgstr "성공률은 0에서 100 사이 숫자로 설정되어야 합니다."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_time_limited
msgid "The question is limited in time"
msgstr "질문의 시간은 제한되어 있습니다"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "The session will begin automatically when the host starts."
msgstr "주최자가 시작하면 세션이 자동으로 시작됩니다."

#. module: survey
#: code:addons/survey/controllers/main.py:0
#, python-format
msgid "The survey has already started."
msgstr "설문조사가 이미 시작되었습니다."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__is_time_limited
msgid "The survey is limited in time"
msgstr "설문은 시간이 제한되어 있습니다"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_question_start_time
msgid ""
"The time at which the current question has started, used to handle the timer"
" for attendees."
msgstr "현재 질문이 시작된 시간으로, 참가자의 타이머를 설정하는 데 사용됩니다."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_time_limit_check
msgid ""
"The time limit needs to be a positive number if the survey is time limited."
msgstr "설문이 시간 제한인 경우 시간 제한은 양수여야 합니다."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row3
msgid "The tool to compare the products is useful to make a choice"
msgstr "제품을 비교하는 도구는 선택하는 데 유용합니다"

#. module: survey
#: code:addons/survey/controllers/main.py:0
#, python-format
msgid "The user has not succeeded the certification"
msgstr "사용자가 인증에 성공하지 못했습니다"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid "There was an error during the validation of the survey."
msgstr "이 문제를 확인하는 중에 오류가 발생했습니다."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__column_nb
msgid ""
"These options refer to col-xx-[12|6|4|3|2] classes in Bootstrap for "
"dropdown-based simple and multiple choice questions."
msgstr "이러한 옵션은 col-xx-[12|6|4|3|2] 클래스에 따라 드롭다운 기반의 단순하고 객관적인 질문을 합니다."

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/tests/test_survey.py:0
#, python-format
msgid "This answer must be an email address"
msgstr "이 답변은 이메일 주소여야 합니다."

#. module: survey
#. openerp-web
#: code:addons/survey/static/src/js/survey_form.js:0
#, python-format
msgid "This answer must be an email address."
msgstr "답변은 이메일 주소여야 합니다."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
msgid "This certificate is presented to"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid ""
"This certificate is presented to\n"
"                                <br/>"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_code
msgid ""
"This code will be used by your attendees to reach your session. Feel free to"
" customize it however you like!"
msgstr "참가자가 세션에 접속하는 데 사용되는 코드입니다. 원하는 방식으로 자유롭게 설정할 수 있습니다!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_form_view
msgid "This is a test survey."
msgstr "이것은 시험용 설문 조사입니다."

#. module: survey
#. openerp-web
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/static/src/js/survey_form.js:0
#: code:addons/survey/tests/test_survey.py:0
#, python-format
msgid "This is not a date"
msgstr "이것은 날짜가 아닙니다."

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/tests/test_survey.py:0
#, python-format
msgid "This is not a number"
msgstr "이것은 숫자가 아닙니다"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__emails
msgid ""
"This list of emails of recipients will not be converted in contacts.        "
"Emails must be separated by commas, semicolons or newline."
msgstr "이 수신자의 이메일 목록은 연락처에서 변환되지 않습니다. 이메일은 쉼표, 세미콜론 또는 줄 바꿈으로 구분해야합니다."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__description_done
msgid "This message will be displayed when survey is completed"
msgstr "이 메시지는 설문 조사가 완료되면 표시됩니다."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_tree
msgid "This question depends on another question's answer."
msgstr ""

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p1
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p1_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p2
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p2_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p2_q2
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p2_q3
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p3
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p3_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p3_q2
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p3_q3
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p4
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p4_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p4_q2
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p4_q3
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p5
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p5_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p5_q2
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p5_q3
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p1
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p1_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p1_q2
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p1_q3
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p1_q4
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p2
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p2_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p2_q2
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p2_q3
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p3
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p3_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p3_q2
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p3_q3
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p3_q4
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p3_q5
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p3_q6
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p4
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p4_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p4_q2
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p4_q3
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p4_q4
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p4_q5
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p4_q6
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p5
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p5_q1
#: model:survey.question,constr_error_msg:survey.survey_feedback_p1
#: model:survey.question,constr_error_msg:survey.survey_feedback_p1_q1
#: model:survey.question,constr_error_msg:survey.survey_feedback_p1_q2
#: model:survey.question,constr_error_msg:survey.survey_feedback_p1_q3
#: model:survey.question,constr_error_msg:survey.survey_feedback_p1_q4
#: model:survey.question,constr_error_msg:survey.survey_feedback_p2
#: model:survey.question,constr_error_msg:survey.survey_feedback_p2_q1
#: model:survey.question,constr_error_msg:survey.survey_feedback_p2_q2
#: model:survey.question,constr_error_msg:survey.survey_feedback_p2_q3
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_1
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_1_question_1
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_1_question_2
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_1_question_3
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_1_question_4
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_1_question_5
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_2
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_2_question_1
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_2_question_2
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_2_question_3
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_3
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_3_question_1
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_3_question_2
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_3_question_3
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_3_question_4
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_3_question_5
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_3_question_6
#, python-format
msgid "This question requires an answer."
msgstr "이 질문에 답변을 해야 합니다."

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid ""
"This survey does not allow external people to participate. You should create"
" user accounts or update survey access mode accordingly."
msgstr ""
"이 설문 조사는 외부인의 참여를 허용하지 않습니다. 이에 따라 사용자 계정을 만들거나 설문 조사 액세스 모드를 업데이트해야 합니다."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_closed_expired
msgid "This survey is now closed. Thank you for your interest !"
msgstr "이 설문 조사는 종료되었습니다. 관심을 가져 주셔서 감사합니다 !"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_auth_required
msgid "This survey is open only to registered people. Please"
msgstr "이 설문 조사는 등록된 사람에게만 열려 있습니다."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__time_limit
msgid "Time limit (minutes)"
msgstr "시간 제한 (분)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__time_limit
msgid "Time limit (seconds)"
msgstr "시간 제한 (초)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__title
msgid "Title"
msgstr "제목"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid ""
"To take this survey, please close all other tabs on <strong class=\"text-"
"danger\"/>."
msgstr "이 설문조사에 참여하려면 <strong class=\"text-danger\"/>의 모든 탭을 닫아주십시오."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Today Activities"
msgstr "오늘 활동"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug2
msgid "Tokyo"
msgstr "도쿄"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_total
msgid "Total Score"
msgstr "총 점수"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col4
msgid "Totally agree"
msgstr "전적으로 동의함"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col1
msgid "Totally disagree"
msgstr "전적으로 동의하지 않음"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4
msgid "Trees"
msgstr "나무"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__triggering_answer_id
msgid "Triggering Answer"
msgstr "연결된 답변"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__triggering_question_id
msgid "Triggering Question"
msgstr "연결된 질문"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Type"
msgstr "유형"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "레코드에 있는 예외 활동의 유형입니다."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__suggested_answer_ids
msgid "Types of answers"
msgstr "답변 유형"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug2
msgid "Ulmaceae"
msgstr "느릅나무과"

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid "Unable to post message, please configure the sender's email address."
msgstr "메시지를 게시할 수 없습니다. 보낸 사람의 이메일 주소를 구성하십시오."

#. module: survey
#. openerp-web
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
#, python-format
msgid "Unanswered"
msgstr "미답변"

#. module: survey
#: code:addons/survey/models/survey_user_input.py:0
#, python-format
msgid "Uncategorized"
msgstr "미분류"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_2
msgid "Underpriced"
msgstr "저렴한 가격"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "Unfortunately, you have failed the test."
msgstr "불행히도 테스트에 실패했습니다."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug3
msgid "Unique"
msgstr "독특한"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_unread
msgid "Unread Messages"
msgstr "읽지 않은 메세지"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_unread_counter
msgid "Unread Messages Counter"
msgstr "읽지 않은 메세지 수"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Upcoming Activities"
msgstr "예정된 활동"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__description
msgid ""
"Use this field to add additional explanations about your question or to "
"illustrate it with pictures or a video"
msgstr "이 필드를 사용하면 질문에 대한 설명을 추가하거나 사진 또는 동영상을 보여줄 수 있습니다."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__random_questions_count
msgid ""
"Used on randomized sections to take X random questions from all the "
"questions of that section."
msgstr "무작위 섹션에서 사용되어 해당 섹션의 모든 질문에서 무작위로 X 개의 질문을 받습니다."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug2
msgid "Useful"
msgstr "유능한"

#. module: survey
#: model:res.groups,name:survey.group_survey_user
msgid "User"
msgstr "사용자"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "User Choice"
msgstr "사용자 선택"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__user_input_id
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
msgid "User Input"
msgstr "사용자 입력"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date
#: model_terms:ir.ui.view,arch_db:survey.question_result_text
msgid "User Responses"
msgstr "사용자 응답"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_form
msgid "User input line details"
msgstr "사용자 입력 내역 세부 사항"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__user_input_ids
msgid "User responses"
msgstr "사용자 응답"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__users_can_go_back
msgid "Users can go back"
msgstr "사용자는 되돌아 갈 수 있습니다"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_users_can_signup
#: model:ir.model.fields,field_description:survey.field_survey_survey__users_can_signup
msgid "Users can signup"
msgstr "사용자는 가입할 수 있습니다"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_required
msgid "Validate entry"
msgstr "입력 승인"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_error_msg
msgid "Validation Error message"
msgstr "유효성 검사 오류 메시지"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q1_sug2
msgid "Vegetables"
msgstr "채소"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_1
msgid "Very underpriced"
msgstr "매우 저렴한 가격"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug3
msgid "Vietnam"
msgstr "베트남"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"We have registered your answer! Please wait for the host to go to the next "
"question."
msgstr "답변이 등록되었습니다! 주최자가 다음 질문으로 넘어갈 때까지 기다려 주세요."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__website_message_ids
msgid "Website Messages"
msgstr "웹사이트 메시지"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__website_message_ids
msgid "Website communication history"
msgstr "웹사이트 대화 이력"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_5
msgid ""
"What day and time do you think most customers are most likely to call "
"customer service (not rated)?"
msgstr "대부분의 고객이 고객센터에 전화할 가능성이 가장 높은 요일과 시간은 언제라고 생각하나요 (평가되지 않음)?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_4
msgid ""
"What day to you think is best for us to start having an annual sale (not "
"rated)?"
msgstr "연간 세일을 시작하기에 가장 좋은 날짜는 언제라고 생각하시나요(평가되지 않음)?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2_q2
msgid "What do you think about our new eCommerce ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2_question_3
msgid "What do you think about our prices (not rated)?"
msgstr "가격에 대해 어떻게 생각하십니까?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p5_q1
msgid "What do you think about this survey ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2_q2
msgid "What is the biggest city in the world ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q1
msgid "What is your email ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q2
msgid "What is your nickname ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4_q2
msgid "What is, approximately, the critical mass of plutonium-239 ?"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__sub_model_object_field
msgid ""
"When a relationship field is selected as first field, this field lets you "
"select the target field within the destination document model (sub-model)."
msgstr "관계 필드를 첫번째 필드로 선택하면 이 필드로 대상 문서 모델(보조 모델) 중에서 대상 필드를 선택할 수 있습니다."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__sub_object
msgid ""
"When a relationship field is selected as first field, this field shows the "
"document model the relationship goes to."
msgstr "관계 필드를 첫번째 필드로 선택하면 이 필드는 관계로 이동해서 문서 모델을 보여줍니다."

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3_q1
msgid "When did Genghis Khan die ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2_q2
msgid "When did precisely Marc Demo crop its first apple tree ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q6
msgid "When do you harvest those fruits"
msgstr "과일을 수확하시는 시기가 언제입니까"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2_q1
msgid "When is Mitchell Admin born ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q2
msgid "When is your date of birth ?"
msgstr "생년월일은 언제입니까?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q3
msgid "Where are you from ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q1
msgid "Where do you live ?"
msgstr "사는 곳이 어디인가요?"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_show_leaderboard
msgid ""
"Whether or not we want to show the attendees leaderboard for this survey."
msgstr "이 설문조사의 참가자 순위를 표시할지 여부를 설정합니다."

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5_q1
msgid "Which Musician is not in the 27th Club ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q1
msgid "Which category does a tomato belong to"
msgstr "토마토는 어느 카테고리에 속하나요?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2_q3
msgid "Which is the highest volcano in Europe ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2_q1
msgid "Which of the following words would you use to describe our products ?"
msgstr "다음 중 어떤 단어를 사용하여 제품을 설명 하시겠습니까?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q2
msgid "Which of the following would you use to pollinate"
msgstr "꽃을 수분시키기 위해 다음 중 무엇을 사용하시겠습니까?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5_q2
msgid "Which painting/drawing was not made by Pablo Picasso ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5_q3
msgid "Which quote is from Jean-Claude Van Damme"
msgstr "다음 중 장 클로드 반담의 명언은 무엇입니까?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1
msgid "Who are you ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3_q2
msgid "Who is the architect of the Great Pyramid of Giza ?"
msgstr ""

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4_q1
msgid ""
"Who received a Nobel prize in Physics for the discovery of neutrino "
"oscillations, which shows that neutrinos have mass ?"
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_3
msgid "Width"
msgstr "폭"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug4
msgid "Willard S. Boyle"
msgstr "Willard S. Boyle"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug4
msgid "Winter"
msgstr "겨울"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"YYYY-MM-DD\n"
"                                        <i class=\"fa fa-calendar fa-2x\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"
msgstr ""
"YYYY-MM-DD\n"
"                                        <i class=\"fa fa-calendar fa-2x\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"YYYY-MM-DD hh:mm:ss\n"
"                                        <i class=\"fa fa-calendar fa-2x\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"
msgstr ""
"YYYY-MM-DD hh:mm:ss\n"
"                                        <i class=\"fa fa-calendar fa-2x\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q6_sug1
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_1_choice_2
msgid "Yes"
msgstr "예"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q3_sug1
msgid "Yes, that's the only thing a human eye can see."
msgstr "네, 사람의 눈으로 볼 수 있는 것은 그것뿐입니다."

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid ""
"You can create surveys for different purposes: customer opinion, services "
"feedback, recruitment interviews, employee's periodical evaluations, "
"marketing campaigns, etc."
msgstr ""
"고객 의견, 서비스 피드백, 채용 인터뷰, 직원의 정기 평가, 마케팅 캠페인 등 다양한 목적으로 설문 조사를 작성할 수 있습니다."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_certification_check
msgid ""
"You can only create certifications for surveys that have a scoring "
"mechanism."
msgstr "점수 매기기 메커니즘이 있는 설문 조사에 대해서만 인증을 만들 수 있습니다."

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#, python-format
msgid ""
"You cannot delete questions from surveys \"%(survey_names)s\" while live "
"sessions are in progress."
msgstr "실시간 세션이 진행 중일 때는 설문조사 \"%(survey_names)s\"의 문항을 삭제할 수 없습니다."

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid ""
"You cannot send an invitation for a \"One page per section\" survey if the "
"survey has no sections."
msgstr "설문조사에 섹션이 없는 경우 \"섹션당 한 페이지\" 설문조사에 대한 초대를 보낼 수 없습니다."

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid ""
"You cannot send an invitation for a \"One page per section\" survey if the "
"survey only contains empty sections."
msgstr "설문조사에 비어있는 섹션만 있는 경우 '섹션당 한 페이지' 설문조사에 대한 초대를 보낼 수 없습니다."

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "You cannot send an invitation for a survey that has no questions."
msgstr "질문이 없는 설문 조사 초대장은 보낼 수 없습니다."

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "You cannot send invitations for closed surveys."
msgstr "마감된 설문 조사 초대장은 보낼 수 없습니다."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "You received the badge"
msgstr "배지를 받았습니다"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "You scored"
msgstr "귀하의 점수"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p5
msgid "Your feeling"
msgstr "당신의 기분"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "ans"
msgstr "답변"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "answered"
msgstr "답변 완료됨"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "attempts"
msgstr "시도"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "e.g. No one can solve challenges like you do"
msgstr "예. 그 누구도 당신만큼 문제를 잘 해결할 수 없습니다."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "e.g. Problem Solver"
msgstr "e.g. 문제 해결사"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "e.g. Satisfaction Survey"
msgstr "예 : 만족도 조사"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid ""
"for successfully completing\n"
"                                <br/>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_auth_required
msgid "log in"
msgstr "로그인"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "minutes"
msgstr "분"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "of"
msgstr "of"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid "of achievement"
msgstr "의 달성"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "or press CTRL+Enter"
msgstr "또는 CTRL+엔터 키를 눌러주세요"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "or press Enter"
msgstr "또는 엔터키를 누르십시오"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "pages"
msgstr "페이지"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "review your answers"
msgstr "귀하의 답변 검토"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_closed_expired
msgid "survey expired"
msgstr "설문 조사 만료됨"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid "survey is empty"
msgstr "설문조사가 비어있습니다"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "this page"
msgstr "해당 페이지"
