# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* crm_iap_lead
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:16+0000\n"
"PO-Revision-Date: 2019-08-26 09:09+0000\n"
"Language-Team: Luxembourgish (https://www.transifex.com/odoo/teams/41243/lb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: crm_iap_lead
#: code:addons/crm_iap_lead/models/crm_iap_lead_mining_request.py:0
#, python-format
msgid "%d credits will be consumed to find %d companies."
msgstr ""

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.lead_message_template
msgid "(Time Now)"
msgstr ""

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.lead_message_template
msgid "<b>Contacts</b>"
msgstr ""

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.lead_message_template
msgid "<b>Phone :</b>"
msgstr ""

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.lead_message_template
msgid "<b>Timezone : </b>"
msgstr ""

#. module: crm_iap_lead
#: model:mail.template,body_html:crm_iap_lead.lead_generation_no_credits
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p>Dear,</p>\n"
"    <p>There is no more credits on your IAP Lead Generation account.<br/>\n"
"    You can charge your IAP Lead Generation account in the settings of the CRM app.<br/></p>\n"
"    <p>Best regards,</p>\n"
"    <p>Odoo S.A.</p>\n"
"</div>"
msgstr ""

#. module: crm_iap_lead
#: code:addons/crm_iap_lead/models/crm_iap_lead_mining_request.py:0
#, python-format
msgid ""
"<p class=\"o_view_nocontent_empty_folder\">\n"
"            No leads found\n"
"        </p><p>\n"
"            No leads could be generated according to your search criteria\n"
"        </p>"
msgstr ""

#. module: crm_iap_lead
#: code:addons/crm_iap_lead/models/crm_iap_lead_mining_request.py:0
#, python-format
msgid ""
"<p class=\"o_view_nocontent_empty_folder\">\n"
"            No opportunities found\n"
"        </p><p>\n"
"            No opportunities could be generated according to your search criteria\n"
"        </p>"
msgstr ""

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_form
msgid "<span class=\"o_stat_text\">Leads</span>"
msgstr ""

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_form
msgid "<span class=\"o_stat_text\">Opportunities</span>"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_238
msgid "Automobiles & Components"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_157
msgid "Banks"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model,name:crm_iap_lead.model_crm_iap_lead_mining_request
msgid "CRM Lead Mining Request"
msgstr ""

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_form
msgid "Cancel"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_162
msgid "Capital Goods"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_industry__color
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_role__color
msgid "Color Index"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_163
msgid "Commercial & Professional Services"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields.selection,name:crm_iap_lead.selection__crm_iap_lead_mining_request__search_type__companies
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_form
msgid "Companies"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields.selection,name:crm_iap_lead.selection__crm_iap_lead_mining_request__search_type__people
msgid "Companies and their Contacts"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__company_size_max
msgid "Company Size Max"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_167
msgid "Construction Materials"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_30
msgid "Consumer Discretionary"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_239
msgid "Consumer Durables & Apparel"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_150
msgid "Consumer Services"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_33
msgid "Consumer Staples"
msgstr ""

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_form
msgid "Contacts"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__country_ids
msgid "Countries"
msgstr ""

#. module: crm_iap_lead
#: code:addons/crm_iap_lead/models/crm_iap_lead_mining_request.py:0
#, python-format
msgid "Create a Lead Mining Request"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_helpers__create_uid
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_industry__create_uid
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__create_uid
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_role__create_uid
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_seniority__create_uid
msgid "Created by"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_helpers__create_date
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_industry__create_date
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__create_date
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_role__create_date
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_seniority__create_date
msgid "Created on"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_helpers__display_name
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_industry__display_name
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__display_name
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_role__display_name
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_seniority__display_name
msgid "Display Name"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_151
msgid "Diversified Consumer Services"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_158
msgid "Diversified Financial Services"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_159
msgid "Diversified Financials"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields.selection,name:crm_iap_lead.selection__crm_iap_lead_mining_request__state__done
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_search
msgid "Done"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields.selection,name:crm_iap_lead.selection__crm_iap_lead_mining_request__state__draft
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_search
msgid "Draft"
msgstr ""

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.lead_message_template
msgid "Email"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_156
msgid "Energy Equipment & Services"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__error
#: model:ir.model.fields.selection,name:crm_iap_lead.selection__crm_iap_lead_mining_request__state__error
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_search
msgid "Error"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__contact_filter_type
msgid "Filter on"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__filter_on_size
msgid "Filter on Size"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_153
msgid "Food & Staples Retailing"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_154
msgid "Food, Beverage & Tobacco"
msgstr ""

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_form
msgid "From"
msgstr ""

#. module: crm_iap_lead
#. openerp-web
#: code:addons/crm_iap_lead/static/src/xml/leads_tree_generate_leads_views.xml:0
#, python-format
msgid "Generate Leads"
msgstr ""

#. module: crm_iap_lead
#: code:addons/crm_iap_lead/models/crm_iap_lead_mining_request.py:0
#, python-format
msgid "Generate new leads based on their country, industry, size, etc."
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__lead_ids
msgid "Generated Lead / Opportunity"
msgstr ""

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_search
msgid "Group By"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_160
msgid "Health Care Equipment & Services"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model,name:crm_iap_lead.model_crm_iap_lead_helpers
msgid "Helper methods for crm_iap_lead modules"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_155
msgid "Household & Personal Products"
msgstr ""

#. module: crm_iap_lead
#: model:mail.template,subject:crm_iap_lead.lead_generation_no_credits
msgid "IAP Lead Generation Notification"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_helpers__id
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_industry__id
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__id
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_role__id
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_seniority__id
msgid "ID"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_168
msgid "Independent Power and Renewable Electricity Producers"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_146
msgid "Industrials"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__industry_ids
msgid "Industries"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model,name:crm_iap_lead.model_crm_iap_lead_industry
msgid "Industry Tag"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_69
msgid "Insurance"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_helpers____last_update
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_industry____last_update
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request____last_update
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_role____last_update
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_seniority____last_update
msgid "Last Modified on"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_helpers__write_uid
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_industry__write_uid
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__write_uid
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_role__write_uid
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_seniority__write_uid
msgid "Last Updated by"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_helpers__write_date
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_industry__write_date
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__write_date
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_role__write_date
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_seniority__write_date
msgid "Last Updated on"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields.selection,name:crm_iap_lead.selection__crm_iap_lead_mining_request__lead_type__lead
msgid "Lead"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__lead_contacts_credits
msgid "Lead Contacts Credits"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__lead_credits
msgid "Lead Credits"
msgstr ""

#. module: crm_iap_lead
#: model:ir.ui.menu,name:crm_iap_lead.crm_menu_lead_generation
msgid "Lead Generation"
msgstr ""

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_form
msgid "Lead Information"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_lead__lead_mining_request_id
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_search
msgid "Lead Mining Request"
msgstr ""

#. module: crm_iap_lead
#: model:ir.actions.act_window,name:crm_iap_lead.crm_iap_lead_mining_request_action
#: model:ir.ui.menu,name:crm_iap_lead.crm_iap_lead_mining_request_menu_action
msgid "Lead Mining Requests"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__lead_total_credits
msgid "Lead Total Credits"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model,name:crm_iap_lead.model_crm_lead
msgid "Lead/Opportunity"
msgstr ""

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_search
msgid "Leads"
msgstr ""

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_form
msgid ""
"Make sure you know if you have to be GDPR compliant for storing personal "
"data."
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_148
msgid "Materials"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_86
msgid "Media"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_seniority__name
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.lead_message_template
msgid "Name"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.constraint,message:crm_iap_lead.constraint_crm_iap_lead_seniority_name_uniq
msgid "Name already exists!"
msgstr ""

#. module: crm_iap_lead
#: code:addons/crm_iap_lead/models/crm_iap_lead_mining_request.py:0
#: code:addons/crm_iap_lead/models/crm_iap_lead_mining_request.py:0
#: code:addons/crm_iap_lead/models/crm_iap_lead_mining_request.py:0
#: code:addons/crm_iap_lead/models/crm_iap_lead_mining_request.py:0
#, python-format
msgid "New"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__contact_number
msgid "Number of Contacts"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__leads_count
msgid "Number of Generated Leads"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__lead_number
msgid "Number of Leads"
msgstr ""

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_search
msgid "Opportunities"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields.selection,name:crm_iap_lead.selection__crm_iap_lead_mining_request__lead_type__opportunity
msgid "Opportunity"
msgstr ""

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.lead_message_template
msgid "Opportunity created by Odoo Lead Generation"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__role_ids
msgid "Other Roles"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model,name:crm_iap_lead.model_crm_iap_lead_role
msgid "People Role"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model,name:crm_iap_lead.model_crm_iap_lead_seniority
msgid "People Seniority"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_161
msgid "Pharmaceuticals, Biotechnology & Life Sciences"
msgstr ""

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.lead_message_template
msgid "Phone"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__preferred_role_id
msgid "Preferred Role"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_114
msgid "Real Estate"
msgstr ""

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_form
msgid "Request"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__name
msgid "Request Number"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_152
msgid "Retailing"
msgstr ""

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_form
msgid "Retry"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_industry__reveal_id
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_role__reveal_id
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_seniority__reveal_id
msgid "Reveal"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_lead__reveal_id
msgid "Reveal ID"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields.selection,name:crm_iap_lead.selection__crm_iap_lead_mining_request__contact_filter_type__role
msgid "Role"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_role__name
msgid "Role Name"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.constraint,message:crm_iap_lead.constraint_crm_iap_lead_role_name_uniq
msgid "Role name already exists!"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__team_id
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_search
msgid "Sales Team"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__user_id
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_search
msgid "Salesperson"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_164
msgid "Semiconductors & Semiconductor Equipment"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__seniority_id
#: model:ir.model.fields.selection,name:crm_iap_lead.selection__crm_iap_lead_mining_request__contact_filter_type__seniority
msgid "Seniority"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__company_size_min
msgid "Size"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_165
msgid "Software & Services"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__state_ids
msgid "States"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__state
msgid "Status"
msgstr ""

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_form
msgid "Submit"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_industry__name
msgid "Tag Name"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.constraint,message:crm_iap_lead.constraint_crm_iap_lead_industry_name_uniq
msgid "Tag name already exists!"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__tag_ids
msgid "Tags"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__search_type
msgid "Target"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_166
msgid "Technology Hardware & Equipment"
msgstr ""

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.lead_message_template
msgid "Technology Used :"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_149
msgid "Telecommunication Services"
msgstr ""

#. module: crm_iap_lead
#: code:addons/crm_iap_lead/models/crm_iap_lead_mining_request.py:0
#, python-format
msgid "This makes a total of %d credits for this request."
msgstr ""

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.lead_message_template
msgid "Title"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_136
msgid "Transportation"
msgstr ""

#. module: crm_iap_lead
#: model:ir.model.fields,field_description:crm_iap_lead.field_crm_iap_lead_mining_request__lead_type
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_search
msgid "Type"
msgstr ""

#. module: crm_iap_lead
#: code:addons/crm_iap_lead/models/crm_iap_lead_mining_request.py:0
#, python-format
msgid ""
"Up to %d additional credits will be consumed to identify %d contacts per "
"company."
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.industry,name:crm_iap_lead.crm_iap_lead_industry_138
msgid "Utilities"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_1
msgid "ceo"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_2
msgid "communications"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_3
msgid "consulting"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_4
msgid "customer_service"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.seniority,name:crm_iap_lead.crm_iap_lead_seniority_1
msgid "director"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_5
msgid "education"
msgstr ""

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_form
msgid "employees"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_6
msgid "engineering"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.seniority,name:crm_iap_lead.crm_iap_lead_seniority_2
msgid "executive"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_7
msgid "finance"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_8
msgid "founder"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_9
msgid "health_professional"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_10
msgid "human_resources"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_11
msgid "information_technology"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_12
msgid "legal"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.seniority,name:crm_iap_lead.crm_iap_lead_seniority_3
msgid "manager"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_13
msgid "marketing"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_14
msgid "operations"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_15
msgid "owner"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_16
msgid "president"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_17
msgid "product"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_18
msgid "public_relations"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_19
msgid "real_estate"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_20
msgid "recruiting"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_21
msgid "research"
msgstr ""

#. module: crm_iap_lead
#: model:crm.iap.lead.role,name:crm_iap_lead.crm_iap_lead_role_22
msgid "sale"
msgstr ""

#. module: crm_iap_lead
#: model_terms:ir.ui.view,arch_db:crm_iap_lead.crm_iap_lead_mining_request_form
msgid "to"
msgstr ""
