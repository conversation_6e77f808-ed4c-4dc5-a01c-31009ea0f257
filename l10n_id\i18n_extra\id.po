# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_id
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-12-03 12:38+0000\n"
"PO-Revision-Date: 2019-12-03 12:38+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: id_ID\n"
"Plural-Forms: \n"

#. module: l10n_id
#: model:account.tax.template,name:l10n_id.tax_PT1
#: model:account.tax.template,name:l10n_id.tax_ST1
msgid "10%"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_121001
msgid "Account Receivable"
msgstr "Piutang Usaha"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_1210011
msgid "Account Receivable (PoS)"
msgstr "Piutang Usaha (PoS)"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_511006
msgid "Accrued Payable Bank"
msgstr "BYMHD Bank"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_511008
msgid "Accrued Payable Business License"
msgstr "BYMHD Izin Usaha"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_511010
msgid "Accrued Payable Education"
msgstr "BYMHD Pendidikan dan Latihan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_511001
msgid "Accrued Payable Electricity"
msgstr "BYMHD Listrik"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_511011
msgid "Accrued Payable Health Insurance/BPJS"
msgstr "BYMHD Jaminan Kesehatan/BPJS"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_511009
msgid "Accrued Payable Insurance"
msgstr "BYMHD Asuransi"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_511002
msgid "Accrued Payable Jamsostek"
msgstr "BYMHD Jamsostek"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_511007
msgid "Accrued Payable PBB"
msgstr "BYMHD PBB"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_511005
msgid "Accrued Payable Security Management"
msgstr "BYMHD Jasa Pengelola Keamanan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_511004
msgid "Accrued Payable Telp & Internet"
msgstr "BYMHD Telepon"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_511003
msgid "Accrued Payable Water"
msgstr "BYMHD Air"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_228101
msgid "Accumulation Building Depreciation"
msgstr "Akumulasi Penyusutan Bangunan Kantor"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_228105
msgid "Accumulation Office Furniture Depreciation"
msgstr "Akumulasi Penyusutan Furnitur Kantor"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_228103
msgid "Accumulation Office Supplies Depreciation"
msgstr "Akumulasi Penyusutan Peralatan Kantor"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_228104
msgid "Accumulation Software Depreciation"
msgstr "Akumulasi Penyusutan Software"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_228102
msgid "Accumulation Vehicle Depreciation"
msgstr "Akumulasi Penyusutan Kendaraan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_811001
msgid "Advance Sales"
msgstr "Uang Muka Penjualan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_211003
msgid "Advertising"
msgstr "Advertising"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_511010
msgid "Asset Maintenance Costs"
msgstr "Biaya Pemeliharaan & Perawatan Aset"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_3_110001
msgid "Authorized Capital"
msgstr "Modal Dasar"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_112005
msgid "BCA"
msgstr "BCA"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_112004
msgid "BNI"
msgstr "BNI"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_112006
msgid "BNI Giro"
msgstr "BNI Giro"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_9_110002
msgid "Bank Administration Expense"
msgstr "Beban Administrasi Bank"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_511002
msgid "Bank Administration Fees"
msgstr "Biaya Administrasi Bank"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_211001
msgid "Bank Loan"
msgstr "Hutang Bank"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_811003
msgid "Bonus Point"
msgstr "Poin Bonus"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130012
msgid "Book, Office Stationery, Accessories Inventory"
msgstr "Persediaan Buku, ATK, Asesoris"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_511006
msgid "Building Maintenance Costs"
msgstr "Biaya Pemeliharaan & Perawatan Gedung"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_141001
msgid "Building Rent"
msgstr "Sewa Bangunan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_112002
msgid "Business Mandiri"
msgstr "Mandiri Bisnis"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_3_121001
msgid "Capital Reserves"
msgstr "Cadangan Modal"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_111002
msgid "Cash in Hand"
msgstr "Kas Belum Disetor"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130007
msgid "Cigarette Inventory"
msgstr "Persediaan Rokok"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_311016
msgid "Cleaning Equipment"
msgstr "Perlengkapan Kebersihan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130014
msgid "Cleaning Supplies Inventory"
msgstr "Persediaan Perlengkapan Kebersihan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_511003
msgid "Consultant Fees"
msgstr "Biaya Konsultan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_5_100001
msgid "Cost of Goods Sold"
msgstr "Harga Pokok Penjualan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_811002
msgid "Customer Deposit"
msgstr "Deposit Customer"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_8_110002
msgid "Deposit Income"
msgstr "Pendapatan Deposit"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_311004
msgid "Donation"
msgstr "Sumbangan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_180000
msgid "Down Payment"
msgstr "Uang Muka Pembelian"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130004
msgid "Dried Goods Inventory"
msgstr "Persediaan Keringan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130009
msgid "Drink Inventory"
msgstr "Persediaan Minuman"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_311001
msgid "Drinking Water"
msgstr "Air Minum"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_311008
msgid "Electricity"
msgstr "Listrik"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_511007
msgid "Electricity, Telephone, and Internet Installation Maintenance Costs"
msgstr "Biaya Perawatan Instalasi Listrik, telepon, internet"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130016
msgid "Electronic Inventory"
msgstr "Persediaan Elektronik"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_110008
msgid "Employee Birthday Benefit"
msgstr "Tunjangan Ulang Tahun Karyawan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_110002
msgid "Employee Bonus / Benefits"
msgstr "Tunjangan/ Bonus Karyawan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_110003
msgid "Employee Health Benefits"
msgstr "Tunjangan Kesehatan Karyawan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_121002
msgid "Employee Liabilities"
msgstr "Piutang Karyawan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_110004
msgid "Employee Meal (Catering)"
msgstr "Pangan karyawan (catering)"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_110005
msgid "Employee Overtime Pay"
msgstr "Lembur Karyawan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_110001
msgid "Employee Salary"
msgstr "Gaji Karyawan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_211002
msgid "Event"
msgstr "Event"

#. module: l10n_id
#: model:account.tax.template,name:l10n_id.tax_PT0
#: model:account.tax.template,name:l10n_id.tax_ST2
msgid "Exempt"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_311002
msgid "Exercise Necessities"
msgstr "Keperluan Olahraga"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130013
msgid "Fashion & Textile Inventory"
msgstr "Persediaan Fashion & Textil"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_311013
msgid "First Aid Kit"
msgstr "P3K"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130002
msgid "Fish Inventory"
msgstr "Persediaan Ikan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130008
msgid "Food Inventory"
msgstr "Persediaan Makanan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_8_110003
msgid "Foreign Exchange Gain"
msgstr "Keuntungan Selisih Kurs"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_9_110003
msgid "Foreign Exchange Loss"
msgstr "Kerugian Selisih Kurs"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_211001
msgid "Free Gift"
msgstr "Free Gift"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130006
msgid "Fresh Drink Inventory"
msgstr "Persediaan Minuman Segar"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130005
msgid "Fruit Inventory"
msgstr "Persediaan Buah"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_8_110009
msgid "Gain on Sale of Fixed Assets"
msgstr "Keuntungan Atas Penjualan Aktiva Tetap"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_511009
msgid "Guest Accomodation"
msgstr "Akomodasi Tamu"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_3_900000
msgid "Historical Balance"
msgstr "Historical Balance"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130015
msgid "House Supplies Inventory"
msgstr "Persediaan Perlengkapan Rumah Tangga"

#. module: l10n_id
#: model:account.chart.template,name:l10n_id.l10n_id_chart
msgid "Indonesian Account Chart Template"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_9_110001
msgid "Interest Expense"
msgstr "Beban Bunga"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_8_110001
msgid "Interest Income"
msgstr "Pendapatan Bunga"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_900000
msgid "Interim Stock"
msgstr "Stok Interim"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_311005
msgid "Internet"
msgstr "Internet"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_411003
msgid "Jilid & Photocopy"
msgstr "Jilid & Photocopy"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_411004
msgid "Job Recruitment Advertisement"
msgstr "Iklan Lowongan Kerja"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_311015
msgid "K3 (Fire Extinguisher)"
msgstr "K3 (Pemadam Kebakaran)"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_311011
msgid "Kitchen Necessities"
msgstr "Keperluan Dapur"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_221001
#: model:account.account.template,name:l10n_id.a_6_710001
msgid "Land"
msgstr "Tanah"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_211002
msgid "Leasing Deposit"
msgstr "Hutang Leasing"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_511001
msgid "Licensing Fees"
msgstr "Biaya Perizinan"

#. module: l10n_id
msgid "Liquidity Transfer"
msgstr "Liquidity Transfer"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_9_110009
msgid "Loss on Sale of Fixed Assets"
msgstr "Kerugian Atas Penjualan Aktiva Tetap"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_112007
msgid "Mandiri Giro"
msgstr "Mandiri Giro"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_110009
msgid "Maternity Benefit"
msgstr "Tunjangan Melahirkan Karyawan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130001
msgid "Meat Inventory"
msgstr "Persediaan Daging"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_311003
msgid "Monthly Fee"
msgstr "Iuran Bulanan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_112003
msgid "Muamalat"
msgstr "Muamalat"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_221002
#: model:account.account.template,name:l10n_id.a_6_710002
msgid "Office Building"
msgstr "Bangunan Kantor"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_311012
msgid "Office Equipment"
msgstr "Perlengkapan Kantor"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_221006
#: model:account.account.template,name:l10n_id.a_6_710006
msgid "Office Furniture"
msgstr "Furnitur Kantor"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_411001
msgid "Office Stationery"
msgstr "Alat Tulis Kantor"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_221004
#: model:account.account.template,name:l10n_id.a_6_710004
msgid "Office Supplies"
msgstr "Peralatan Kantor"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_3_151002
msgid "Ongoing Profit & Loss"
msgstr "Laba Rugi Tahun Berjalan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_900000
msgid "Other Expenses"
msgstr "Biaya Lain-lain"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_8_110004
msgid "Other Income"
msgstr "Pendapatan lainnya"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130018
msgid "Other Inventory"
msgstr "Persediaan Lainnya"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_311014
msgid "Other Necessities"
msgstr "Keperluan Lain-lain"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_211004
msgid "Other Receivable"
msgstr "Piutang lainnya"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_311018
msgid "Owner Necessities"
msgstr "Keperluan Owner"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_211003
msgid "Owner Receivable"
msgstr "Piutang Owner"

#. module: l10n_id
#: model:account.tax.template,description:l10n_id.tax_PT0
msgid "PT0"
msgstr ""

#. module: l10n_id
#: model:account.tax.template,description:l10n_id.tax_PT1
msgid "PT1"
msgstr ""

#. module: l10n_id
#: model:account.tax.template,description:l10n_id.tax_PT2
msgid "PT2"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_3_110002
msgid "Paid Capital"
msgstr "Modal Yang Disetor"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_3_151001
msgid "Past Profit & Loss"
msgstr "Laba Rugi Tahun Lalu"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_112001
msgid "Personal Mandiri"
msgstr "Mandiri Personal"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_111001
msgid "Petty Cash"
msgstr "Kas Kecil"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_311006
msgid "Phone"
msgstr "Telepon"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_411002
msgid "Post Necessities"
msgstr "Keperluan Pos"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_110010
msgid "Pph 21 Benefit"
msgstr "Tunjangan PPH Pasal 21"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_141003
msgid "Prepaid Advertisement-Free"
msgstr "Beban Iklan Dibayar Dimuka"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_141002
msgid "Prepaid Insurance"
msgstr "Asuransi Dibayar Dimuka"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_311007
msgid "Prepaid Phone Bills"
msgstr "Pulsa"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_151001
msgid "Prepaid Tax Pph 22"
msgstr "Pajak Dibayar Dimuka PPH 22"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_151002
msgid "Prepaid Tax Pph 23"
msgstr "Pajak Dibayar Dimuka PPH 23"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_151003
msgid "Prepaid Tax Pph 25"
msgstr "Pajak Dibayar Dimuka PPH 25"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_3_110004
msgid "Prive (Personal Retrieval)"
msgstr "Prive (Pengambilan Pribadi)"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130010
msgid "Processed Food Inventory"
msgstr "Persediaan Makanan Olahan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_511004
msgid "Rental Costs"
msgstr "Biaya Sewa"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_311010
msgid "Research & Development"
msgstr "Research & Development"

#. module: l10n_id
#: model:account.tax.template,description:l10n_id.tax_ST0
msgid "ST0"
msgstr ""

#. module: l10n_id
#: model:account.tax.template,description:l10n_id.tax_ST1
msgid "ST1"
msgstr ""

#. module: l10n_id
#: model:account.tax.template,description:l10n_id.tax_ST2
msgid "ST2"
msgstr ""

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_110004
msgid "Salary Deposit"
msgstr "Hutang Gaji"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_4_100001
msgid "Sales"
msgstr "Penjualan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_4_200007
msgid "Sales Discount"
msgstr "Discount Penjualan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_4_200006
msgid "Sales Refund"
msgstr "Retur Penjualan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_110006
msgid "Security Service Fee"
msgstr "Fee Jasa Keamanan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_110002
msgid "Shareholder Deposit"
msgstr "Hutang Pemegang Saham"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_511011
msgid "Shipping Costs"
msgstr "Biaya Pengiriman Dokumen/Barang"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_211004
msgid "Shipping Merchandise"
msgstr "Pengiriman Barang Dagang"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_221005
#: model:account.account.template,name:l10n_id.a_6_710005
msgid "Software"
msgstr "Software"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_411005
msgid "Stamp"
msgstr "Materai"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_121004
msgid "Tax Payable 4 (2)"
msgstr "Hutang Pajak Pasal 4 (2)"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_121001
msgid "Tax Payable Pph 21"
msgstr "Hutang Pajak PPh 21"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_121002
msgid "Tax Payable Pph 23"
msgstr "Hutang Pajak PPh 23"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_121003
msgid "Tax Payable Pph 25"
msgstr "Hutang Pajak PPh 25"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_121005
msgid "Tax Payable Pph 29"
msgstr "Hutang Pajak PPh 29"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_511008
msgid "Taxes"
msgstr "Pajak"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_110003
msgid "Third-Party Deposit"
msgstr "Hutang Pihak Ketiga"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130011
msgid "Toiletries Inventory"
msgstr "Persediaan Toiletries"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130017
msgid "Toys Inventory"
msgstr "Persediaan Mainan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_110001
msgid "Trade Receivable"
msgstr "Hutang Usaha"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_3_110003
msgid "Unpaid Capital"
msgstr "Modal Yang Belum Disetor"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_122101
msgid "VAT Purchase"
msgstr "PPN Pembelian"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_2_122102
msgid "VAT Sales"
msgstr "PPN Penjualan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_130003
msgid "Vegetables Inventory"
msgstr "Persediaan Sayuran"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_1_221003
#: model:account.account.template,name:l10n_id.a_6_710003
msgid "Vehicle"
msgstr "Kendaraan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_611001
msgid "Vehicle Fuel"
msgstr "BBM kendaraan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_611005
msgid "Vehicle Insurance"
msgstr "Asuransi Kendaraan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_611003
msgid "Vehicle Parking & Toll Fee"
msgstr "Parkir & tol kendaraan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_611002
msgid "Vehicle Service"
msgstr "Service kendaraan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_611004
msgid "Vehicle Taxes"
msgstr "Pajak Kendaraan"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_311009
msgid "Water (PDAM)"
msgstr "PDAM"

#. module: l10n_id
#: model:account.account.template,name:l10n_id.a_6_110007
msgid "Work Uniform"
msgstr "Pakaian Kerja"
