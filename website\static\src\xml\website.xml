<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="website.prompt">
        <div role="dialog" class="modal o_technical_modal" tabindex="-1">
                <div class="modal-dialog">
                <div class="modal-content">
                    <header class="modal-header" t-if="window_title">
                        <h3 class="modal-title"><t t-esc="window_title"/></h3>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">×</button>
                    </header>
                    <main class="modal-body">
                        <form role="form" t-att-id="id">
                            <div class="form-group row mb0">
                                <label for="page-name" class="col-md-3 col-form-label">
                                    <t t-esc="field_name"/>
                                </label>
                                <div class="col-md-9">
                                    <input t-if="field_type == 'input'" type="text" class="form-control" required="required"/>
                                    <textarea t-if="field_type == 'textarea'" class="form-control" required="required" rows="5"></textarea>
                                    <select t-if="field_type == 'select'" class="form-control"></select>
                                </div>
                            </div>
                        </form>
                    </main>
                    <footer class="modal-footer">
                        <button type="button" class="btn btn-primary btn-continue"><t t-esc="btn_primary_title"/></button>
                        <button type="button" class="btn btn-secondary" data-dismiss="modal" aria-label="Cancel"><t t-esc="btn_secondary_title"/></button>
                    </footer>
                </div>
            </div>
        </div>
    </t>

    <t t-name="website.dependencies">
        <p class="text-warning">Don't forget to update all links referring to this page.</p>
        <t t-if="dependencies and _.keys(dependencies).length">
            <p class="text-warning">We found these ones:</p>
            <div t-foreach="dependencies" t-as="type" class="mb16">
                <a class="collapsed fa fa-caret-right" data-toggle="collapse" t-attf-href="#collapseDependencies#{type_index}" aria-expanded="false" t-attf-aria-controls="collapseDependencies#{type_index}">
                    <t t-esc="type"/>&amp;nbsp;
                    <span class="text-muted"><t t-esc="type_value.length"/> found(s)</span>
                </a>
                <div t-attf-id="collapseDependencies#{type_index}" class="collapse" aria-expanded="false">
                    <ul>
                        <li t-foreach="type_value" t-as="error">
                            <a t-if="!_.contains(['', '#', false], error.link)" t-att-href="error.link">
                                <t t-out="error.text"/>
                            </a>
                            <t t-else="">
                                <t t-out="error.text"/>
                            </t>
                        </li>
                    </ul>
                </div>
            </div>
        </t>
    </t>

    <div t-name="website.delete_page">
        <p>Are you sure you want to delete this page ?</p>
        <t t-call="website.dependencies"/>
    </div>

    <div t-name="website.rename_page">
        <div class="card">
            <div class="card-body">
                <form>
                    <div class="form-group row mb0">
                        <label for="new_name" class="col-form-label col-md-4">Rename Page To:</label>
                        <div class="col-md-8">
                            <input type="text" class="form-control" id="new_name" placeholder="e.g. About Us"/>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <t t-call="website.dependencies"/>
    </div>

    <div t-name="website.duplicate_page_action_dialog">
        <div class="form-group row">
            <label class="col-form-label col-md-3" for="page_name">Page Name</label>
            <div class="col-md-9">
                <input type="text" class="form-control" id="page_name"/>
            </div>
        </div>
    </div>

    <t t-name="website.oe_applications_menu">
        <t t-as="menu" t-foreach="menu_data.children">
            <a role="menuitem" class="dropdown-item"
               t-att-data-action-id="menu.action ? menu.action.split(',')[1] : undefined"
               t-att-data-action-model="menu.action ? menu.action.split(',')[0] : undefined"
               t-att-data-menu="menu.id"
               t-att-data-menu-xmlid="menu.xmlid"
               t-att-href="_.str.sprintf('/web#menu_id=%s&amp;action=%s', menu.id, menu.action ? menu.action.split(',')[1] : '')">
                <span class="oe_menu_text" t-esc="menu.name"/>
            </a>
        </t>
    </t>

    <div t-name="website.fullscreen_indication" class="o_fullscreen_indication">
        <p>Press <span>esc</span> to exit full screen</p>
    </div>
</templates>
