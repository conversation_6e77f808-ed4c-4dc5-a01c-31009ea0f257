# -*- coding: utf-8 -*-
from odoo import tools, api, fields, models
from datetime import datetime
from odoo.exceptions import ValidationError


class HrTrailContractAssisment(models.Model):
    _name = "hr.contract.assessment"
    _rec_name = 'assessment_name'

    current_user_id = fields.Many2one('res.users', 'معد التقييم', default=lambda self: self.env.user)## the one how cuducted the evaluation
    employee_id = fields.Many2one('hr.employee', string='اسم الموظف', readonly=True)

    department_id = fields.Many2one('hr.department', compute='_compute_employee_contract',string='الادارة',readonly=False)
    contract_id = fields.Many2one('hr.contract')
    job_id = fields.Many2one('hr.job', compute='_compute_employee_contract',string='الوظيفة', store=True, readonly=False)
    date_start = fields.Date('تاريخ البدأ')
    trial_date_end = fields.Date('تاريخ انتهاء الفترة التجريبية')

    assessment_name = fields.Char(compute='_compute_assessment_name')
    assessment_elements_ids = fields.One2many('hr.contract.assessment.items', 'contract_assessment_id')
    total_result = fields.Float(string='الإجمالي', compute='_compute_total_result')

    admission_criteria_ids = fields.One2many('hr.contract.admission.criteria','contract_assessment_id',string="معايير القبول")
    obstacles_ids = fields.One2many('hr.contract.obstacles','contract_assessment_id',string="معوقات")
    recommendations_ids = fields.One2many('hr.contract.recommendations','contract_assessment_id',string="توصيات")

    state = fields.Selection([
        ('draft','مسودة'),
        ('continue_probationary_period','اقتراح استمراره بعد فترة الاختبار'),
        ('another_test_period','اقترح فترة اختبار اخرى'),
        ('termination_contract','اقتراح إنهاء التعاقد قبل مرور فترة الاختبار')
    ], string="الحالة",default='draft')

    def makecontinue(self):
        self.state = 'continue_probationary_period'
    def makeanother(self):
        self.state = 'another_test_period'
    def maketermination(self):
        self.state = 'termination_contract'
    def cancel(self):
        self.state = 'draft'


    @api.depends('assessment_elements_ids')
    def _compute_total_result(self):
        self.total_result = 0
        if self.assessment_elements_ids:
            total_item_value = 0
            for e in self.assessment_elements_ids:
                total_item_value+=e.item_value
                self.total_result+=e.item_score

            if self.total_result > total_item_value:
                raise ValidationError('There is an Error With Your Assessments Scores !')

    @api.depends('employee_id')
    def _compute_assessment_name(self):
        for elem in self:
            elem.assessment_name = str(elem.employee_id.name)+'-Trial-Period-Assessment'

    @api.depends('employee_id')
    def _compute_employee_contract(self):
        for contract in self.filtered('employee_id'):
            contract.job_id = contract.employee_id.job_id
            contract.department_id = contract.employee_id.department_id

    @api.model
    def default_get(self, fields):
        res = super(HrTrailContractAssisment, self).default_get(fields)
        employ_id = self._context.get('active_id')
        res['contract_id'] = self._context.get('active_id')
        res['employee_id'] = self.env['hr.contract'].search([('id', '=', employ_id)]).employee_id.id
        llist = self.env['hr.contract.assessment.element'].search([])
        default_assessment_list = []
        i=1
        for ele in llist:
            default_assessment_list.append((0,0, {
                'seq_number': i,
                'item_name': ele.item_name,
                'item_value': ele.item_value,
                'item_score': 0,
                'item_note': ''
            }))
            i+=1

        res['assessment_elements_ids'] = default_assessment_list
        return res

    @api.model
    def create(self, vals):

        default_assessment_list = self.env['hr.contract.assessment.element'].search([])
        elems_total_vals = 0
        i=0
        for elem in default_assessment_list:
            vals['assessment_elements_ids'][i][2].setdefault('seq_number',i+1)
            vals['assessment_elements_ids'][i][2]['item_value']=elem.item_value
            elems_total_vals += elem.item_value
            vals['assessment_elements_ids'][i][2]['item_name']=elem.item_name
            i+=1
        return super(HrTrailContractAssisment, self).create(vals)



class HrTrailContractAssismentItems(models.Model):
    ## HR Sitting Trail Assessment Elements
    _name = 'hr.contract.assessment.element'
    item_name = fields.Char(string='اسم العنصر')
    item_value = fields.Float(string='قيمة العنصر')


class HrTrailContractAssismentItems(models.Model):
    _name = 'hr.contract.assessment.items'
    contract_assessment_id = fields.Many2one('hr.contract.assessment')
    seq_number = fields.Integer(string='الرقم')
    item_name = fields.Char(string='اسم العنصر')
    item_value = fields.Float(string='قيمة العنصر')
    item_score = fields.Float(string='النتيجة')
    item_note = fields.Char(string='ملاحظات')


class AdmissionCriteria(models.Model):
    #######معاير القبول :
    _name = 'hr.contract.admission.criteria'
    contract_assessment_id = fields.Many2one('hr.contract.assessment')
    item_name = fields.Char(string='عنصر')

class Obstacles(models.Model):
    # معوقات تعوق بمستوي الاداء المطلوب ( تذكر )
    _name = 'hr.contract.obstacles'
    contract_assessment_id = fields.Many2one('hr.contract.assessment')
    item_name = fields.Char(string='عنصر')

class Recommendations(models.Model):
    # التوصيات
    _name = 'hr.contract.recommendations'
    contract_assessment_id = fields.Many2one('hr.contract.assessment')
    item_name = fields.Char(string='عنصر')
