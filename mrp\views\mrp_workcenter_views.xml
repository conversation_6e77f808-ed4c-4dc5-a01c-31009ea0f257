<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="act_product_mrp_production_workcenter" model="ir.actions.act_window">
            <field name="context">{'search_default_confirmed': 1}</field>
            <field name="name">Manufacturing Orders</field>
            <field name="res_model">mrp.production</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="domain">[('bom_id', '!=', False), ('bom_id.operation_ids.workcenter_id', '=', active_id)]</field>
        </record>

        <record id="action_work_orders" model="ir.actions.act_window">
            <field name="name">Work Orders</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">mrp.workorder</field>
            <field name="view_mode">tree,form,gantt,pivot,graph,calendar</field>
            <field name="search_view_id" ref="view_mrp_production_work_order_search"/>
            <field name="domain">[('state', 'not in', ('done', 'cancel'))]</field>
            <field name="context">{'search_default_workcenter_id': active_id}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No work orders to do!
                </p><p>
                    Work orders are operations to do as part of a manufacturing order.
                    Operations are defined in the bill of materials or added in the manufacturing order directly.
                </p>
            </field>
        </record>

        <!-- Work Centers -->
        <record id="mrp_workcenter_tree_view" model="ir.ui.view">
            <field name="name">mrp.workcenter.tree</field>
            <field name="model">mrp.workcenter</field>
            <field name="arch" type="xml">
                <tree string="Work Center" multi_edit="1">
                    <field name="sequence" widget="handle"/>
                    <field name="name" optional="show"/>
                    <field name="code" optional="show"/>
                    <field name="tag_ids" widget="many2many_tags" options="{'no_create': True, 'color_field': 'color'}" optional="show"/>
                    <field name="alternative_workcenter_ids" widget="many2many_tags" optional="show"/>
                    <field name="productive_time" optional="hide"/>
                    <field name="costs_hour" optional="show"/>
                    <field name="capacity" optional="show"/>
                    <field name="time_efficiency" optional="show"/>
                    <field name="oee_target" optional="show"/>
                    <field name="time_start" optional="hide"/>
                    <field name="time_stop" optional="hide"/>
                    <field name="company_id" groups="base.group_multi_company" optional="hide"/>
                    <field name="active" invisible="1"/>
                </tree>
            </field>
        </record>

        <record id="mrp_workcenter_view_kanban" model="ir.ui.view">
            <field name="name">mrp.workcenter.kanban</field>
            <field name="model">mrp.workcenter</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile">
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_content oe_kanban_global_click">
                                <div class="row">
                                    <div class="col-12">
                                        <strong><field name="name"/></strong>
                                    </div>
                                    <div class="col-12">
                                        <span>Code <field name="code"/></span>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record id="oee_pie_view" model="ir.ui.view">
            <field name="name">mrp.workcenter.productivity.graph</field>
            <field name="model">mrp.workcenter.productivity</field>
            <field name="priority">20</field>
            <field name="arch" type="xml">
                <graph string="Workcenter Productivity" type="pie" sample="1">
                    <field name="loss_id"/>
                    <field name="duration" type="measure" string="Duration (minutes)"/>
                </graph>
            </field>
        </record>
        <record model="ir.actions.act_window" id="mrp_workcenter_productivity_report_oee">
            <field name="name">Overall Equipment Effectiveness</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">mrp.workcenter.productivity</field>
            <field name="view_id" eval="oee_pie_view"/>
            <field name="view_mode">graph,pivot,tree,form</field>
            <field name="domain">[('workcenter_id','=',active_id)]</field>
            <field name="context">{'search_default_thismonth':True}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Overall Equipment Effectiveness: no working or blocked time
                </p>
            </field>
        </record>
        <record model="ir.actions.act_window" id="mrp_workcenter_productivity_report_blocked">
            <field name="name">Productivity Losses</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">mrp.workcenter.productivity</field>
            <field name="view_mode">tree,form,graph,pivot</field>
            <field name="context">{'search_default_availability': '1',
                                   'search_default_performance': '1',
                                   'search_default_quality': '1',
                                   'default_workcenter_id': active_id,
                                   'search_default_workcenter_id': [active_id]}</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_empty_folder">
                No productivity loss for this equipment
              </p>
            </field>
        </record>

        <record model="ir.actions.act_window" id="mrp_workorder_workcenter_report">
            <field name="name">Work Orders Performance</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">mrp.workorder</field>
            <field name="domain">[('workcenter_id','=', active_id),('state','=','done')]</field>
            <field name="view_mode">graph,pivot,tree,form,gantt</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a new work orders performance
              </p>
            </field>
        </record>

        <record model="ir.actions.act_window" id="mrp_workorder_report">
            <field name="name">Work Orders</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">mrp.workorder</field>
            <field name="domain">[]</field>
            <field name="context">{'search_default_workcenter': 1,
                                   'search_default_ready': True,
                                   'search_default_waiting': True,
                                   'search_default_pending': True,
                                   'search_default_progress': True,}</field>
            <field name="view_mode">graph,pivot,tree,form,gantt</field>
            <field name="search_view_id" ref="view_mrp_production_work_order_search"/>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a new work orders performance
              </p>
            </field>
        </record>

        <!-- Workcenter Kanban view-->
        <record model="ir.ui.view" id="mrp_workcenter_kanban">
            <field name="name">mrp.workcenter.kanban</field>
            <field name="model">mrp.workcenter</field>
            <field name="arch" type="xml">
                <kanban class="oe_background_grey o_kanban_dashboard o_workcenter_kanban" create="0" sample="1">
                    <field name="name"/>
                    <field name="color"/>
                    <field name="workorder_count"/>
                    <field name="working_state"/>
                    <field name="oee_target"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="#{!selection_mode ? kanban_color(record.color.raw_value) : ''}">
                                <div t-attf-class="o_kanban_card_header o_kanban_record_top">
                                    <div class="o_kanban_record_headings o_kanban_card_header_title">
                                        <span class="o_primary ml8" style="display: inline-block">
                                            <field name="name"/>
                                        </span>
                                    </div>
                                    <div class="o_kanban_manage_button_section">
                                        <a class="o_kanban_manage_toggle_button" href="#"><i class="fa fa-ellipsis-v" role="img" aria-label="Manage" title="Manage"/></a>
                                    </div>
                                </div>
                                <div class="container o_kanban_card_content">
                                    <div class="row mb16">
                                        <div class="col-6 o_kanban_primary_left">
                                            <div class="btn-group" name="o_wo">
                                            <t t-if="record.workorder_count.raw_value &gt; 0">
                                                <button class="btn btn-primary" name="action_work_order" type="object" context="{'search_default_ready': 1, 'search_default_progress': 1}">
                                                    <span>WORK ORDERS</span>
                                                </button>
                                            </t>
                                            <t  t-if="record.workorder_count.raw_value &lt;= 0">
                                                <button class="btn btn-warning" name="%(act_product_mrp_production_workcenter)d" type="action">
                                                    <span>PLAN ORDERS</span>
                                                </button>
                                            </t>
                                            </div>
                                        </div>
                                        <div class="col-6 o_kanban_primary_right">
                                            <div class="row" t-if="record.workorder_ready_count.raw_value &gt; 0">
                                                <div class="col-8">
                                                    <a name="action_work_order" type="object" context="{'search_default_ready': 1}">
                                                        To Launch
                                                    </a>
                                                </div>
                                                <div class="col-4 text-right">
                                                    <field name="workorder_ready_count"/>
                                                </div>
                                            </div>
                                            <div class="row" t-if="record.workorder_progress_count.raw_value &gt; 0">
                                                <div class="col-8">
                                                    <a name="action_work_order" type="object" context="{'search_default_progress': 1}">
                                                        In Progress
                                                    </a>
                                                </div>
                                                <div class="col-4 text-right">
                                                    <field name="workorder_progress_count"/>
                                                </div>
                                            </div>
                                            <div class="row" t-if="record.workorder_late_count.raw_value &gt; 0">
                                                <div class="col-8">
                                                    <a name="action_work_order" type="object" context="{'search_default_late': 1}">
                                                        Late
                                                    </a>
                                                </div>
                                                <div class="col-4 text-right">
                                                    <field name="workorder_late_count"/>
                                                </div>
                                            </div>
                                            <div class="row" t-if="record.oee.raw_value &gt; 0">
                                                <div class="col-6">
                                                    <a name="%(mrp_workcenter_productivity_report_oee)d" type="action">
                                                        OEE
                                                    </a>
                                                </div>
                                                <div class="col-6 text-right">
                                                    <span t-att-class="record.oee_target.raw_value and (record.oee.raw_value &lt; record.oee_target.raw_value) and 'text-danger' or (record.oee.raw_value &gt; record.oee_target.raw_value) and 'text-success' or 'text-warning'">
                                                        <strong>
                                                            <field name="oee" widget="integer"/>%
                                                        </strong>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-12 o_kanban_primary_left" style="position:absolute; bottom:10px;" name="wc_stages">
                                            <a name="%(act_mrp_block_workcenter)d" type="action" class="o_status float-right"
                                                title="No workorder currently in progress. Click to mark work center as blocked."
                                                aria-label="No workorder currently in progress. Click to mark work center as blocked."
                                                attrs="{'invisible': [('working_state','in',('blocked','done'))]}"/>
                                            <a name="unblock" type="object" class=" o_status o_status_red float-right"
                                                title="Workcenter blocked, click to unblock."
                                                aria-label="Workcenter blocked, click to unblock."
                                                attrs="{'invisible': [('working_state','in',('normal','done'))]}"/>
                                            <a name="%(act_mrp_block_workcenter)d" type="action" class="o_status o_status_green float-right"
                                                title="Work orders in progress. Click to block work center."
                                                aria-label="Work orders in progress. Click to block work center."
                                                attrs="{'invisible': [('working_state','in',('normal','blocked'))]}"/>
                                        </div>
                                    </div>
                                </div><div class="container o_kanban_card_manage_pane dropdown-menu" role="menu">
                                    <div class="row">
                                        <div class="col-6 o_kanban_card_manage_section o_kanban_manage_view">
                                            <div role="menuitem" class="o_kanban_card_manage_title">
                                                <span>Actions</span>
                                            </div>
                                            <div role="menuitem" name="plan_order">
                                                <a name="action_work_order" type="object">Plan Orders</a>
                                            </div>
                                        </div>
                                        <div class="col-6 o_kanban_card_manage_section o_kanban_manage_new">
                                            <div role="menuitem" class="o_kanban_card_manage_title">
                                                <span>Reporting</span>
                                            </div>
                                            <div role="menuitem">
                                                <a name="%(mrp_workcenter_productivity_report_oee)d" type="action">OEE</a>
                                            </div>
                                            <div role="menuitem">
                                                <a name="%(mrp_workorder_workcenter_report)d" type="action" context="{'search_default_thisyear':True}">
                                                    Performance
                                                </a>
                                            </div>
                                            <div role="menuitem">
                                                <a name="action_work_order" type="object" context="{'search_default_waiting': 1}">Waiting Availability</a>
                                            </div>
                                        </div>
                                    </div>

                                    <div t-if="widget.editable" class="o_kanban_card_manage_settings row">
                                        <div role="menuitem" aria-haspopup="true" class="col-8">
                                            <ul role="menu" class="oe_kanban_colorpicker" data-field="color"/>
                                        </div>
                                        <div role="menuitem" class="text-right">
                                            <a type="edit">Settings</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record id="mrp_workcenter_view" model="ir.ui.view">
            <field name="name">mrp.workcenter.form</field>
            <field name="model">mrp.workcenter</field>
            <field name="arch" type="xml">
                <form string="Work Center">
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <field name="routing_line_ids" invisible="1"/>
                            <button string="Operations" type="object"
                                name="action_show_operations"
                                attrs="{'invisible': [('routing_line_ids', '=', [])]}"
                                context="{'default_workcenter_id': active_id}"
                                class="oe_stat_button" icon="fa-cog"/>
                            <button name="%(mrp_workcenter_productivity_report_oee)d" type="action" class="oe_stat_button" icon="fa-pie-chart">
                                <div class="o_field_widget o_stat_info">
                                    <span class="o_stat_value"><field name="oee" widget="statinfo" nolabel="1"/>%</span>
                                    <span class="o_stat_text">OEE</span>
                                </div>
                            </button>
                            <button name="%(mrp_workcenter_productivity_report_blocked)d" type="action" class="oe_stat_button" icon="fa-bar-chart">
                                <div class="o_field_widget o_stat_info">
                                    <span class="o_stat_value"><field name="blocked_time" widget="statinfo" nolabel="1"/> Hours</span>
                                    <span class="o_stat_text">Lost</span>
                                </div>
                            </button>
                            <button name="%(action_mrp_workcenter_load_report_graph)d" type="action" class="oe_stat_button" icon="fa-bar-chart"
                                context="{'search_default_workcenter_id': id,
                                          'search_default_ready': True,
                                          'search_default_waiting': True,
                                          'search_default_pending': True,
                                          'search_default_progress': True}">
                                <div class="o_field_widget o_stat_info">
                                    <span class="o_stat_value"><field name="workcenter_load" widget="statinfo" nolabel="1"/> Minutes</span>
                                    <span class="o_stat_text">Load</span>
                                </div>
                            </button>
                            <button name="%(mrp_workorder_report)d" type="action" class="oe_stat_button" icon="fa-bar-chart" context="{'search_default_workcenter_id': id, 'search_default_thisyear': True}">
                                <div class="o_field_widget o_stat_info">
                                    <span class="o_stat_value"><field name="performance" widget="statinfo" nolabel="1"/>%</span>
                                    <span class="o_stat_text">Performance</span>
                                </div>
                            </button>
                        </div>
                        <widget name="web_ribbon" title="Archived" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                        <group>
                            <group>
                                <field name="active" invisible="1"/>
                                <field name="name" string="Work Center Name" required="True"/>
                                <field name="tag_ids" widget="many2many_tags" options="{'color_field': 'color'}"/>
                                <field
                                    name="alternative_workcenter_ids"
                                    widget="many2many_tags"
                                    context="{'default_company_id': company_id}"
                                />
                            </group>
                            <group>
                                <field name="code"/>
                                <field name="resource_calendar_id" required="1"/>
                                <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                            </group>
                        </group>
                        <notebook>
                            <page string="General Information" name="general_info">
                                <group>
                                    <group string="Production Information" name="capacity">
                                        <label for="time_efficiency"/>
                                        <div class="o_row">
                                            <field name="time_efficiency"/> %
                                        </div>
                                        <field name="capacity"/>

                                        <label for="oee_target"/>
                                        <div class="o_row">
                                            <field name="oee_target"/> %
                                        </div>
                                    </group>
                                    <group string="Costing Information" name="costing">
                                        <field name="costs_hour"/>
                                    </group>
                                    <group>
                                        <label for="time_start"/>
                                        <div>
                                            <field name="time_start" widget="float_time" class="oe_inline"/> minutes
                                        </div>
                                        <label for="time_stop"/>
                                        <div>
                                            <field name="time_stop" widget="float_time" class="oe_inline"/> minutes
                                        </div>
                                    </group>
                                </group>
                                <separator string="Description"/>
                                <field name="note" nolabel="1" placeholder="Description of the work center..."/>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="view_mrp_workcenter_search" model="ir.ui.view">
            <field name="name">mrp.workcenter.search</field>
            <field name="model">mrp.workcenter</field>
            <field name="arch" type="xml">
                <search string="Search for mrp workcenter">
                    <field name="name" string="Work Center" filter_domain="['|', ('name', 'ilike', self), ('code', 'ilike', self)]"/>
                    <filter name="archived" string="Archived" domain="[('active', '=', False)]"/>
                    <group expand="0" string="Group By...">
                        <filter string="Company" name="company" domain="[]" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="mrp_workcenter_action" model="ir.actions.act_window">
            <field name="name">Work Centers</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">mrp.workcenter</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="view_id" ref="mrp_workcenter_tree_view"/>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('mrp_workcenter_tree_view')}),
                (0, 0, {'view_mode': 'kanban', 'view_id': ref('mrp_workcenter_view_kanban')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('mrp_workcenter_view')})]"/>
            <field name="search_view_id" ref="view_mrp_workcenter_search"/>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a new work center
              </p><p>
                Manufacturing operations are processed at Work Centers. A Work Center can be composed of
                workers and/or machines, they are used for costing, scheduling, capacity planning, etc.
              </p>
            </field>
        </record>

        <record id="mrp_workcenter_kanban_action" model="ir.actions.act_window">
            <field name="name">Work Centers Overview</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">mrp.workcenter</field>
            <field name="view_mode">kanban,form</field>
            <field name="view_id" ref="mrp_workcenter_kanban"/>
            <field name="search_view_id" ref="view_mrp_workcenter_search"/>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a new work center
              </p><p>
                Manufacturing operations are processed at Work Centers. A Work Center can be composed of
                workers and/or machines, they are used for costing, scheduling, capacity planning, etc.
                They can be defined via the configuration menu.
              </p>
            </field>
        </record>

        <menuitem id="menu_view_resource_search_mrp"
            action="mrp_workcenter_action"
            groups="group_mrp_routings"
            parent="menu_mrp_configuration"
            sequence="90"/>

    <record id="oee_loss_form_view" model="ir.ui.view">
        <field name="name">mrp.workcenter.productivity.loss.form</field>
        <field name="model">mrp.workcenter.productivity.loss</field>
        <field name="arch" type="xml">
            <form string="Workcenter Productivity Loss">
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="loss_id" options="{'no_open': True, 'no_create': True}"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="oee_loss_tree_view" model="ir.ui.view">
        <field name="name">mrp.workcenter.productivity.loss.tree</field>
        <field name="model">mrp.workcenter.productivity.loss</field>
        <field name="arch" type="xml">
            <tree string="Workcenter Productivity Loss" editable='bottom'>
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="loss_type" string="Category"/>
            </tree>
        </field>
    </record>

    <record id="view_mrp_workcenter_productivity_loss_kanban" model="ir.ui.view">
        <field name="name">mrp.workcenter.productivity.loss.kanban</field>
        <field name="model">mrp.workcenter.productivity.loss</field>
        <field name="arch" type="xml">
            <kanban>
                <field name="name"/>
                <field name="manual"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_global_click">
                            <div>
                                <strong>Reason: </strong><field name="name"/>
                            </div>
                            <div>
                                <strong>Effectiveness Category: </strong><field name="loss_type"/>
                            </div>
                            <div>
                                <strong>Is a Blocking Reason? </strong>
                                <span class="float-right" title="Is a Blocking Reason?">
                                    <field name="manual" widget="boolean"/>
                                </span>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="oee_loss_search_view" model="ir.ui.view">
        <field name="name">mrp.workcenter.productivity.loss.search</field>
        <field name="model">mrp.workcenter.productivity.loss</field>
        <field name="arch" type="xml">
            <search string="Operations">
                <field name="name"/>
            </search>
        </field>
    </record>

    <record id="oee_search_view" model="ir.ui.view">
        <field name="name">mrp.workcenter.productivity.search</field>
        <field name="model">mrp.workcenter.productivity</field>
        <field name="arch" type="xml">
            <search string="Operations">
                <field name="workcenter_id"/>
                <field name="loss_id"/>
                <separator/>
                <filter name="availability" string="Availability Losses" domain="[('loss_type','=','availability')]"/>
                <filter name="performance" string="Performance Losses" domain="[('loss_type','=','performance')]"/>
                <filter name="quality" string="Quality Losses" domain="[('loss_type','=','quality')]"/>
                <filter name="productive" string="Fully Productive" domain="[('loss_type','=','productive')]"/>
                <filter name="filter_date_start" string="Date" date="date_start"/>
                <separator/>
                <group expand='0' string='Group by...'>
                    <filter string="User" name="user" context="{'group_by': 'create_uid'}"/>
                    <filter string='Workcenter' name="workcenter_group" context="{'group_by': 'workcenter_id'}"/>
                    <filter string="Loss Reason" name="loss_group" context="{'group_by': 'loss_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="oee_form_view" model="ir.ui.view">
        <field name="name">mrp.workcenter.productivity.form</field>
        <field name="model">mrp.workcenter.productivity</field>
        <field name="priority">5</field>
        <field name="arch" type="xml">
            <form string="Workcenter Productivity">
                <group>
                    <group>
                        <field name="production_id"/>
                        <field  name="workorder_id"/>
                        <field name="workcenter_id"/>
                        <field name="loss_id"/>
                        <field name="company_id" invisible="1"/>
                    </group><group>
                        <field name="date_start"/>
                        <field name="date_end"/>
                        <field name="duration"/>
                        <field name="company_id"/>
                    </group>
                    <field name="description"/>
                </group>
            </form>
        </field>
    </record>

    <record id="oee_tree_view" model="ir.ui.view">
        <field name="name">mrp.workcenter.productivity.tree</field>
        <field name="model">mrp.workcenter.productivity</field>
        <field name="arch" type="xml">
            <tree string="Workcenter Productivity">
                <field name="date_start"/>
                <field name="date_end"/>
                <field name="workcenter_id"/>
                <field name="user_id"/>
                <field name="loss_id"/>
                <field name="duration" string="Duration (minutes)" sum="Duration"/>
                <field name="company_id" groups="base.group_multi_company"/>
            </tree>
        </field>
    </record>

    <record id="oee_graph_view" model="ir.ui.view">
        <field name="name">mrp.workcenter.productivity.graph</field>
        <field name="model">mrp.workcenter.productivity</field>
        <field name="arch" type="xml">
            <graph string="Workcenter Productivity" sample="1">
                <field name="workcenter_id"/>
                <field name="loss_id"/>
                <field name="duration" type="measure" string="Duration (minutes)"/>
            </graph>
        </field>
    </record>

    <record id="oee_pivot_view" model="ir.ui.view">
        <field name="name">mrp.workcenter.productivity.pivot</field>
        <field name="model">mrp.workcenter.productivity</field>
        <field name="arch" type="xml">
            <pivot string="Workcenter Productivity" sample="1">
                <field name="date_start" type="row" interval="day"/>
                <field name="loss_type" type="col"/>
                <field name="duration" type="measure" string="Duration (minutes)"/>
            </pivot>
        </field>
    </record>

    <record model="ir.actions.act_window" id="mrp_workcenter_productivity_report">
        <field name="name">Overall Equipment Effectiveness</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">mrp.workcenter.productivity</field>
        <field name="view_mode">graph,pivot,tree,form</field>
        <field name="domain">[]</field>
        <field name="context">{'search_default_workcenter_group': 1, 'search_default_loss_group': 2, 'create':False,'edit':False}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Overall Equipment Effectiveness: no working or blocked time
            </p>
        </field>
    </record>

    <menuitem id="menu_mrp_workcenter_productivity_report"
          parent="menu_mrp_reporting"
          action="mrp_workcenter_productivity_report"
          groups="group_mrp_routings"
          sequence="12"/>
    </data>
</odoo>
