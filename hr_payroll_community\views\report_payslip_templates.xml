<?xml version="1.0" encoding="utf-8"?>
<odoo>
<template id="report_payslip">
    <t t-call="web.html_container">
        <t t-foreach="docs" t-as="o">
            <t t-call="web.external_layout">
                <div class="page">
                    <h2>Pay Slip</h2>
                    <p t-field="o.name"/>

                    <table class="table table-sm table-bordered">
                        <tr>
                            <td><strong>Name</strong></td>
                            <td><span t-field="o.employee_id"/></td>
                            <td><strong>Designation</strong></td>
                            <td><span t-field="o.employee_id.job_id"/></td>
                        </tr>
                        <tr>
                            <td><strong>Address</strong></td>
                            <td colspan="3">
                                <div t-field="o.employee_id.address_home_id"
                                    t-options='{"widget": "contact", "fields": ["address", "name", "phone"], "no_marker": True, "phone_icons": True}'/>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Email</strong></td>
                            <td><span t-field="o.employee_id.work_email"/></td>
                            <td><strong>Identification No</strong></td>
                            <td><span t-field="o.employee_id.identification_id"/></td>
                        </tr>
                        <tr>
                            <td><strong>Reference</strong></td>
                            <td><span t-field="o.number"/></td>
                            <td><strong>Bank Account</strong></td>
                            <td><span t-field="o.employee_id.bank_account_id"/></td>
                        </tr>
                        <tr>
                            <td><strong>Date From</strong></td>
                            <td><span t-field="o.date_from"/></td>
                            <td><strong>Date To</strong></td>
                            <td><span t-field="o.date_to"/></td>
                        </tr>
                    </table>

                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Code</th>
                                <th>Name</th>
                                <th>Quantity/rate</th>
                                <th>Amount</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
                                <tr t-foreach="o.line_ids.filtered(lambda line: line.appears_on_payslip)" t-as="line">
                                <td><span t-field="line.code"/></td>
                                <td><span t-field="line.name"/></td>
                                <td><span t-field="line.quantity"/></td>
                                <td><span t-esc="line.amount"
                                          t-options='{"widget": "monetary", "display_currency": o.company_id.currency_id}'/></td>
                                <td><span t-esc="line.total"
                                          t-options='{"widget": "monetary", "display_currency": o.company_id.currency_id}'/></td>

                            </tr>
                        </tbody>
                    </table>

                    <p class="text-right"><strong>Authorized signature</strong></p>
                </div>
            </t>
        </t>
    </t>
</template>
</odoo>
