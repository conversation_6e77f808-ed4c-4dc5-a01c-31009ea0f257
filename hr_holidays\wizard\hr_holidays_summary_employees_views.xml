<?xml version="1.0" encoding="utf-8"?>
<odoo>

        <record id="view_hr_holidays_summary_employee" model="ir.ui.view">
            <field name="name">hr.holidays.summary.employee.form</field>
            <field name="model">hr.holidays.summary.employee</field>
            <field name="arch" type="xml">
                <form string="Time Off Summary">
                    <group col="4" colspan="6">
                        <field name="date_from"/>
                        <newline/>
                        <field name="holiday_type"/>
                        <newline/>
                        <field name="emp" invisible="True"/>
                    </group>
                    <footer>
                        <button name="print_report" string="Print" type="object" class="btn-primary" data-hotkey="q"/>
                        <button string="Cancel" class="btn-secondary" special="cancel" data-hotkey="z" />
                    </footer>
                </form>
            </field>
        </record>

        <record id="action_hr_holidays_summary_employee" model="ir.actions.act_window">
            <field name="name">Time Off Summary</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">hr.holidays.summary.employee</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
            <field name="binding_model_id" ref="hr.model_hr_employee" />
            <field name="binding_type">report</field>
        </record>

</odoo>
