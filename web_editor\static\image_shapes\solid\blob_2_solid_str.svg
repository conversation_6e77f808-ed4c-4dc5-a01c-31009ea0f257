<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="800" height="600">
  <defs>
    <clipPath id="clip-path" clipPathUnits="objectBoundingBox">
      <use xlink:href="#filterPath" fill="none"></use>
    </clipPath>
    <path id="filterPath"
      d="M0.094,0.7176h0a0.3363,0.4178,0,0,1-0.0377-0.278h0A0.3365,0.418,0,0,1,0.1346,0.237C0.1753,0.1795,0.23,0.1405,0.2895,0.1203a0.3908,0.4855,0,0,0,0.1094-0.0619,0.2819,0.3502,0,0,1,0.1027-0.0518,0.3366,0.4182,0,0,1,0.1348,0.004h0c0.2176,0.0621,0.3421,0.3474,0.2648,0.6076-0.0759,0.2558-0.2172,0.3864-0.429,0.3817A0.4436,0.5511,0,0,1,0.094,0.7176Z">
    </path>
  </defs><svg viewBox="17.459999084472656 52.0099983215332 265.08001708984375 192" preserveAspectRatio="none">
    <polygon class="background" points="263.34 213.81 32.64 244.01 17.46 52.01 282.54 78.24 263.34 213.81"
      fill="#3AADAA"></polygon>
  </svg><svg viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
    <use xlink:href="#filterPath" fill="darkgrey"></use>
  </svg>
  <image xlink:href="" clip-path="url(#clip-path)"></image>
</svg>
