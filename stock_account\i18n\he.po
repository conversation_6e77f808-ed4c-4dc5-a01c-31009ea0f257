# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_account
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2021
# <PERSON><PERSON>, 2022
# <PERSON>tem <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2023
# yael terner, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: yael terner, 2023\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid " Product cost updated from %(previous)s to %(new_cost)s."
msgstr " עלות המוצר התעדכנה מ %(previous)s ל %(new_cost)s."

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid "%(user)s changed cost from %(previous)s to %(new_price)s - %(product)s"
msgstr "%(user)s שינה את המחיר מ %(previous)s ל %(new_price)s - %(product)s"

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid ""
"%(user)s changed stock valuation from  %(previous)s to %(new_value)s - "
"%(product)s"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid ""
")\n"
"                            <small class=\"mx-2 font-italic\">Use a negative added value to record a decrease in the product value</small>"
msgstr ""
")\n"
"                            <small class=\"mx-2 font-italic\">השתמש בערך שלילי כדי להפחית את שווי המוצר</small>"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form
msgid "<b>Set other input/output accounts on specific </b>"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "<span>Product</span>"
msgstr "<span>מוצר</span>"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "<span>Quantity</span>"
msgstr "<span>כמות</span>"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "<span>SN/LN</span>"
msgstr "<span>מספר סידורי/מספר אצווה</span>"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_chart_template
msgid "Account Chart Template"
msgstr "תבנית לוח חשבונות"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_move__account_move_ids
msgid "Account Move"
msgstr "תנועת חשבון"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form
msgid "Account Stock Properties"
msgstr "מאפייני חשבון מלאי"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_quant__accounting_date
#: model:ir.model.fields,field_description:stock_account.field_stock_request_count__accounting_date
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__date
msgid "Accounting Date"
msgstr "תאריך ערך"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_move_form_inherit
msgid "Accounting Entries"
msgstr "רשומות חשבונאיות"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_location_form_inherit
msgid "Accounting Information"
msgstr "מידע חשבונאי"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Add Manual Valuation"
msgstr "הוסף הערכת שווי ידנית"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
msgid ""
"Add additional cost (transport, customs, ...) in the value of the product."
msgstr "הוסף עלות נוספת (הובלה, מכס, ...) בערך המוצר."

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Added Value"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__added_value
msgid "Added value"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_res_config_settings__module_stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
msgid ""
"Affect landed costs on reception operations and split them among products to"
" update their cost price."
msgstr ""
"להשפיע על נחת עלויות פעולות הקבלה ולפצל אותם בין מוצרים כדי לעדכן את מחיר "
"העלות שלהם."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_move__analytic_account_line_id
msgid "Analytic Account Line"
msgstr "שורת חשבון אנליטית"

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_valuation__real_time
msgid "Automated"
msgstr "אוטומטי"

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_cost_method__average
msgid "Average Cost (AVCO)"
msgstr "עלות ממוצעת "

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Cancel"
msgstr "בטל"

#. module: stock_account
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"Cannot find a stock input account for the product %s. You must define one on"
" the product category, or on the location, before processing this operation."
msgstr ""
"לא ניתן למצוא חשבון קלט מלאי עבור המוצר %s. עליך להגדיר אחד בקטגוריה של "
"המוצר, או  במיקום, לפני עיבוד פעולה זו."

#. module: stock_account
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"Cannot find a stock output account for the product %s. You must define one "
"on the product category, or on the location, before processing this "
"operation."
msgstr ""
"לא ניתן למצוא חשבון פלט מלאי עבור המוצר %s. עליך להגדיר אחד בקטגוריה של "
"המוצר,או במיקום, לפני עיבוד פעולה זו."

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"Changing your cost method is an important change that will impact your "
"inventory valuation. Are you sure you want to make that change?"
msgstr ""
"שינוי שיטת העלות שלך הוא שינוי חשוב שישפיע על הערכת שווי המלאי שלך. האם אתה "
"בטוח שברצונך לבצע שינוי זה?"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__company_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__company_id
msgid "Company"
msgstr "חברה"

#. module: stock_account
#: model:ir.model,name:stock_account.model_res_config_settings
msgid "Config Settings"
msgstr "הגדר הגדרות"

#. module: stock_account
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"Configuration error. Please configure the price difference account on the "
"product or its category to process this operation."
msgstr ""
"שגיאת תצורה. אנא הגדר את חשבון ההפרש במחיר במוצר או בקטגוריה שלו כדי לבצע את"
" הפעולה."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_cost_method
#: model:ir.model.fields,field_description:stock_account.field_product_product__cost_method
#: model:ir.model.fields,field_description:stock_account.field_product_template__cost_method
msgid "Costing Method"
msgstr "שיטת התמחור"

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid "Costing method change for product category %s: from %s to %s."
msgstr "שינוי שיטת תמחיר עבור קטגוריית מוצרים %s: מ %s ל %s."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__account_id
msgid "Counterpart Account"
msgstr "חשבון העמית"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_account_input_categ_id
msgid ""
"Counterpart journal items for all incoming stock moves will be posted in this account, unless there is a specific valuation account\n"
"                set on the source location. This is the default value for all products in this category. It can also directly be set on each product."
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_picking__country_code
msgid "Country Code"
msgstr "קוד ארץ"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__create_uid
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__create_date
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_quant__currency_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__currency_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__currency_id
msgid "Currency"
msgstr "מטבע"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__current_quantity_svl
msgid "Current Quantity"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__current_value_svl
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Current Value"
msgstr "ערך נוכחי"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_form
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Date"
msgstr "תאריך"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_quant__accounting_date
msgid ""
"Date at which the accounting entries will be created in case of automated "
"inventory valuation. If empty, the inventory date will be used."
msgstr ""
"תאריך שבו שהרישומים החשבונאיים נוצרו במקרה של הערכת שווי מלאי אוטומטית. אם "
"ריק, תאריך המלאי יהיה בשימוש."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer__uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "יחידת מידה ברירת מחדל המשמשת לביצוע כל פעולות המלאי."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__description
msgid "Description"
msgstr "תיאור"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_res_config_settings__group_lot_on_invoice
msgid "Display Lots & Serial Numbers on Invoices"
msgstr "הצג מספרי אצוות ומספרים סידוריים בחשבוניות"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__display_name
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: stock_account
#: model:res.groups,name:stock_account.group_lot_on_invoice
msgid "Display Serial & Lot Number on Invoices"
msgstr "הצג מספר סידורי ומספר אצווה בחשבוניות"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
msgid "Documentation"
msgstr "תיעוד"

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"Due to a change of product category (from %s to %s), the costing method"
"                                has changed for product template %s: from %s"
" to %s."
msgstr ""
"עקב שינוי קטגוריית המוצר (מ-%s ל %s), שיטת התמחור השתנתה עבור תבנית מוצר %s:"
" מ %s ל %s."

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_cost_method__fifo
msgid "First In First Out (FIFO)"
msgstr "ראשון נכנס ראשון יוצא (FIFO)"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Group by..."
msgstr "קבץ לפי..."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__id
msgid "ID"
msgstr "תעודה מזהה"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_location
msgid "Inventory Locations"
msgstr "מקומות המלאי"

#. module: stock_account
#: code:addons/stock_account/__init__.py:0
#: code:addons/stock_account/__init__.py:0
#: code:addons/stock_account/models/account_chart_template.py:0
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_valuation
#: model:ir.model.fields,field_description:stock_account.field_product_product__valuation
#: model:ir.model.fields,field_description:stock_account.field_product_template__valuation
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__property_valuation
#: model:ir.ui.menu,name:stock_account.menu_valuation
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form_stock
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
#, python-format
msgid "Inventory Valuation"
msgstr "הערכת שווי המלאי"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_account_move_line__is_anglo_saxon_line
msgid "Is Anglo Saxon Line"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__account_journal_id
msgid "Journal"
msgstr "יומן"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_move
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__account_move_id
msgid "Journal Entry"
msgstr "פקודת יומן"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_move_line
msgid "Journal Item"
msgstr "פקודת יומן"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_res_config_settings__module_stock_landed_costs
msgid "Landed Costs"
msgstr "עלויות הוצאות הובלה"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer____last_update
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation____last_update
msgid "Last Modified on"
msgstr "שינוי אחרון ב"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__write_uid
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__write_date
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__stock_valuation_layer_id
msgid "Linked To"
msgstr "מקושר ל"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
msgid "Lots &amp; Serial numbers will appear on the invoice"
msgstr "אצוות ומספרים סידוריים יופיעו בחשבונית"

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_valuation__manual_periodic
msgid "Manual"
msgstr "ידני"

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid "Manual Stock Valuation: %s."
msgstr ""

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid "Manual Stock Valuation: No Reason Given."
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_valuation
#: model:ir.model.fields,help:stock_account.field_product_product__valuation
#: model:ir.model.fields,help:stock_account.field_product_template__valuation
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer_revaluation__property_valuation
msgid ""
"Manual: The accounting entries to value the inventory are not posted automatically.\n"
"        Automated: An accounting entry is automatically created to value the inventory when a product enters or leaves the company.\n"
"        "
msgstr ""
"ידני: הרישומים החשבונאיים להערכת המלאי אינם מתפרסמים באופן אוטומטי.\n"
"אוטומטי: רישום חשבונאי נוצר באופן אוטומטי כדי להעריך את המלאי כאשר מוצר נכנס או עוזב את החברה."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__new_value
msgid "New value"
msgstr "ערך חדש"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__new_value_by_qty
msgid "New value by quantity"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_product_product_replenishment
msgid "On Hand Value"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_form
msgid "Other Info"
msgstr "מידע נוסף"

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_product
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__product_id
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Product"
msgstr "מוצר"

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_category
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__categ_id
msgid "Product Category"
msgstr "קטגורית מוצר"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "תנועות המוצר (תנועת שורת מלאי)"

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
#, python-format
msgid "Product Revaluation"
msgstr "הערכת שווי מחודשת למוצר"

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_template
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__product_tmpl_id
msgid "Product Template"
msgstr "תבנית מוצר "

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid "Product value manually modified (from %s to %s)"
msgstr "ערך מוצר שונה באופן ידני (מ %s ל %s)"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__quantity
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer__quantity
msgid "Quantity"
msgstr "כמות"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__quantity_svl
msgid "Quantity Svl"
msgstr ""

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_quant
msgid "Quants"
msgstr "אנליסטים כמותיים"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__reason
msgid "Reason"
msgstr "סיבה"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer_revaluation__reason
msgid "Reason of the revaluation"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__product_id
msgid "Related product"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__remaining_qty
msgid "Remaining Qty"
msgstr "כמות שנותרה"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__remaining_value
msgid "Remaining Value"
msgstr "ערך הנותרים"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_return_picking
msgid "Return Picking"
msgstr "החזר ליקוט"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_return_picking_line
msgid "Return Picking Line"
msgstr "החזר שורת ליקוט"

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid "Revaluation of %s"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Revalue"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer__categ_id
msgid "Select category for the current product"
msgstr "בחר קטגוריה למוצר הנוכחי"

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_cost_method__standard
msgid "Standard Price"
msgstr "מחיר סטנדרטי"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_cost_method
#: model:ir.model.fields,help:stock_account.field_product_product__cost_method
#: model:ir.model.fields,help:stock_account.field_product_template__cost_method
msgid ""
"Standard Price: The products are valued at their standard cost defined on the product.\n"
"        Average Cost (AVCO): The products are valued at weighted average cost.\n"
"        First In First Out (FIFO): The products are valued supposing those that enter the company first will also leave it first.\n"
"        "
msgstr ""
"מחיר סטנדרטי: המוצרים מוערכים לפי העלות הסטנדרטית המוגדרת במוצר.\n"
"עלות ממוצעת (AVCO): המוצרים מוערכים בעלות הממוצעת המשוקללת.\n"
"נכנס ראשון יוצא ראשון (FIFO): ההמוצרים מוערכים בהנחה שמי שנכנס לחברה תחילה יעזוב אותה קודם."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_account_input_categ_id
msgid "Stock Input Account"
msgstr "חשבון מלאי נכנס"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_journal
msgid "Stock Journal"
msgstr "יומן מלאי"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_move
#: model:ir.model.fields,field_description:stock_account.field_account_bank_statement_line__stock_move_id
#: model:ir.model.fields,field_description:stock_account.field_account_move__stock_move_id
#: model:ir.model.fields,field_description:stock_account.field_account_payment__stock_move_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__stock_move_id
msgid "Stock Move"
msgstr "תנועת מלאי"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_account_output_categ_id
msgid "Stock Output Account"
msgstr "חשבון מלאי יוצא"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_quantity_history
msgid "Stock Quantity History"
msgstr "היסטורית כמות מלאי "

#. module: stock_account
#: model:ir.model,name:stock_account.model_report_stock_report_product_product_replenishment
msgid "Stock Replenishment Report"
msgstr ""

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_request_count
msgid "Stock Request an Inventory Count"
msgstr ""

#. module: stock_account
#: model:ir.actions.act_window,name:stock_account.stock_valuation_layer_action
msgid "Stock Valuation"
msgstr "הערכת שווי מלאי "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_valuation_account_id
msgid "Stock Valuation Account"
msgstr "חשבון הערכת שווי מלאי "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_location__valuation_in_account_id
msgid "Stock Valuation Account (Incoming)"
msgstr "חשבון הערכת שווי מלאי (נכנס)"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_location__valuation_out_account_id
msgid "Stock Valuation Account (Outgoing)"
msgstr "חשבון הערכת שווי מלאי (יוצא)"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_valuation_layer
#: model:ir.model.fields,field_description:stock_account.field_account_bank_statement_line__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_account_move__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_account_payment__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_product_product__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_stock_move__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__stock_valuation_layer_ids
msgid "Stock Valuation Layer"
msgstr "שכבת הערכת שווי מלאי"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_account_move_line__is_anglo_saxon_line
msgid "Technical field used to retrieve the anglo-saxon lines."
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_picking__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"קוד ארץ ISO בשני תווים. \n"
"ניתן להשתמש בשדה זה לחיפוש מהיר."

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"The Stock Input and/or Output accounts cannot be the same as the Stock "
"Valuation account."
msgstr ""

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"The action leads to the creation of a journal entry, for which you don't "
"have the access rights."
msgstr ""

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid "The added value doesn't have any impact on the stock valuation"
msgstr ""

#. module: stock_account
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"The move lines are not in a consistent state: some are entering and other "
"are leaving the company."
msgstr ""
"שורות התנועה אינן נמצאות במצב עקבי: חלקן נכנסות ואחרות עוזבות את החברה."

#. module: stock_account
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"The move lines are not in a consistent states: they are doing an "
"intercompany in a single step while they should go through the intercompany "
"transit location."
msgstr ""
"שורות התנועה אינן נמצאות במצב עקבי: הן מבצעות תנועה בין חברתית בצעד בודד "
"בזמן שהם צריכות לעבור דרך מיקום מעבר בין חברתי."

#. module: stock_account
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"The move lines are not in a consistent states: they do not share the same "
"origin or destination company."
msgstr "שורות התנועה אין במצב עקבי: הן אינן חולקות את אותו מקור או יעד חברה."

#. module: stock_account
#: model_terms:ir.actions.act_window,help:stock_account.stock_valuation_layer_action
msgid ""
"There is no valuation layers. Valuation layers are created when some product"
" moves should impact the valuation of the stock."
msgstr ""
"אין שכבות הערכת שווי. שכבות הערכת שווי נוצרות כאשר תנועות מוצר מסוימות "
"אמורות להשפיע על הערכת שווי המלאי."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__value
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Total Value"
msgstr "ערך כולל"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_picking
msgid "Transfer"
msgstr "העברה"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_move__to_refund
#: model:ir.model.fields,help:stock_account.field_stock_return_picking_line__to_refund
msgid ""
"Trigger a decrease of the delivered/received quantity in the associated Sale"
" Order/Purchase Order"
msgstr "הפעל הפחתה בכמות שנמסרה / שהתקבלה בהזמנת לקוח/ הזמנת רכש"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__unit_cost
msgid "Unit Value"
msgstr "ערך יחידה"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__uom_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__product_uom_name
msgid "Unit of Measure"
msgstr "יחידת מידה"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_move__to_refund
#: model:ir.model.fields,field_description:stock_account.field_stock_return_picking_line__to_refund
msgid "Update quantities on SO/PO"
msgstr "עדכן כמויות ב- הזמנת לקוח / הזמנת רכש"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_location__valuation_in_account_id
msgid ""
"Used for real-time inventory valuation. When set on a virtual location (non "
"internal type), this account will be used to hold the value of products "
"being moved from an internal location into this location, instead of the "
"generic Stock Output Account set on the product. This has no effect for "
"internal locations."
msgstr ""
"ממשמש עבור הערכת מלאי בזמן אמת. כאשר מוגדר מיקום וירטואלי (סוג שאינו פנימי),"
" חשבון זה משמש להחזיק את ערך המוצרים המועברים ממיקום פנימי למיקום זה, במקום "
"בחשבון המלאי הכללי שנקבע עבור המוצר. אין לכך השפעה על מיקומים פנימיים."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_location__valuation_out_account_id
msgid ""
"Used for real-time inventory valuation. When set on a virtual location (non "
"internal type), this account will be used to hold the value of products "
"being moved out of this location and into an internal location, instead of "
"the generic Stock Output Account set on the product. This has no effect for "
"internal locations."
msgstr ""
"משמש עבור הערכת מלאי בזמן אמת. כאשר הוא מוגדר במיקום וירטואלי (סוג שאינו "
"פנימי), חשבון זה ישמש לשמירה על ערך המוצרים שמועברים מחוץ למיקום זה ולמיקומו"
" הפנימי, במקום בחשבון הפלט הכללי של המלאי שמוגדר למוצר. אין לכך השפעה על "
"מיקומים פנימיים."

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_form
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_picking
msgid "Valuation"
msgstr "הערכת שווי"

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid "Valuation method change for product category %s: from %s to %s."
msgstr "שינוי שיטת הערכת שווי עבור קטגוריית מוצרים %s: מ %s ל %s."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_quant__value
msgid "Value"
msgstr "ערך"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__value_svl
msgid "Value Svl"
msgstr ""

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid "Warning"
msgstr "אזהרה"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_valuation_account_id
msgid ""
"When automated inventory valuation is enabled on a product, this account "
"will hold the current value of the products."
msgstr ""
"כאשר הערכת מלאי אוטומטית מופעלת על מוצר, חשבון זה יחזיק בשווי הנוכחי של "
"המוצרים."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_account_output_categ_id
msgid ""
"When doing automated inventory valuation, counterpart journal items for all outgoing stock moves will be posted in this account,\n"
"                unless there is a specific valuation account set on the destination location. This is the default value for all products in this category.\n"
"                It can also directly be set on each product."
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_journal
msgid ""
"When doing automated inventory valuation, this is the Accounting Journal in "
"which entries will be automatically posted when stock moves are processed."
msgstr ""

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_valuation_layer_revaluation
msgid "Wizard model to reavaluate a stock inventory for a product"
msgstr ""

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid "You cannot revalue a product with a standard cost method."
msgstr ""

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid "You cannot revalue a product with an empty or negative stock."
msgstr ""

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"You cannot update the cost of a product in automated valuation as it leads "
"to the creation of a journal entry, for which you don't have the access "
"rights."
msgstr ""

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"You don't have any input valuation account defined on your product category."
" You must define one before processing this operation."
msgstr ""

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"You don't have any stock input account defined on your product category. You"
" must define one before processing this operation."
msgstr ""

#. module: stock_account
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"You don't have any stock journal defined on your product category, check if "
"you have installed a chart of accounts."
msgstr ""
"אין תיעוד ביומן המלאי המוגדר בקטגוריית המוצרים שלך, בדוק אם התקנת טבלת "
"חשבונות."

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#: code:addons/stock_account/models/product.py:0
#: code:addons/stock_account/models/product.py:0
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"You don't have any stock valuation account defined on your product category."
" You must define one before processing this operation."
msgstr ""
"אין לך חשבון הערכה של מלאי המוגדר בקטגוריית המוצרים שלך. עליך להגדיר אחד "
"לפני עיבוד פעולה זו."

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid "You must set a counterpart account on your product category."
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "by"
msgstr "על ידי"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "for"
msgstr "ל"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form
msgid "locations"
msgstr ""

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid ""
"'The value of a stock valuation layer cannot be negative. Landed cost could "
"be use to correct a specific transfer."
msgstr ""
