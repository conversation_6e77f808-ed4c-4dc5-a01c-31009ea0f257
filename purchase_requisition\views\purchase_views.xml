<?xml version="1.0"?>
<odoo>

    <record id="purchase_order_form_inherit" model="ir.ui.view">
        <field name="name">purchase.order.form.inherit</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_form"/>
        <field name="arch" type="xml">
            <field name="partner_id" position="replace">
                <field name="is_quantity_copy" invisible="1"/>
                <field name="partner_id" widget="res_partner_many2one" context="{'res_partner_search_mode': 'supplier', 'show_vat': True}" attrs="{'readonly': ['|', ('is_quantity_copy', '=', 'none'), ('state', 'in', ['purchase', 'done', 'cancel'])]}" placeholder="Name, TIN, Email, or Reference" force_save="1"/>
                </field>
                <field name="partner_ref" position="after">
                <field name="requisition_id" domain="[('state', 'in', ('in_progress', 'open', 'ongoing')), ('vendor_id', 'in', (partner_id, False)), ('company_id', '=', company_id)]"/>
            </field>
        </field>
    </record>

    <record id="purchase_order_search_inherit" model="ir.ui.view">
        <field name="name">purchase.order.list.select.inherit</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.view_purchase_order_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='approved']" position="after">
                <filter string="Requisition" name="requisition" domain="[('requisition_id', '!=', False)]"  help="Purchase Orders with requisition"/>
            </xpath>
        </field>
    </record>

</odoo>
