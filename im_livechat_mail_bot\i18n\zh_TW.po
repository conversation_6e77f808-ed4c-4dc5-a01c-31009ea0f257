# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* im_livechat_mail_bot
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: im_livechat_mail_bot
#: code:addons/im_livechat_mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Good, you can customize canned responses in the live chat "
"application.<br/><br/><b>It's the end of this overview</b>, enjoy "
"discovering Odoo!"
msgstr "太好了，您可以在線上客服模組中設制罐頭回應。<br/><br/><b>到這裡為教學瀏覽的尾聲</b>，請享受發現Odoo！"

#. module: im_livechat_mail_bot
#: model:ir.model,name:im_livechat_mail_bot.model_mail_bot
msgid "Mail Bot"
msgstr "信件機器人"

#. module: im_livechat_mail_bot
#: code:addons/im_livechat_mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Not sure what you are doing. Please, type <span "
"class=\"o_odoobot_command\">:</span> and wait for the propositions. Select "
"one of them and press enter."
msgstr ""
"不肯定你想做甚麼。請鍵入「<span "
"class=\"o_odoobot_command\">:</span>」並等待建議顯示，選取其中一項，然後按輸入鍵。"

#. module: im_livechat_mail_bot
#: model:ir.model.fields,field_description:im_livechat_mail_bot.field_res_users__odoobot_state
msgid "OdooBot Status"
msgstr "OdooBot 狀態"

#. module: im_livechat_mail_bot
#: model:ir.model.fields.selection,name:im_livechat_mail_bot.selection__res_users__odoobot_state__onboarding_canned
msgid "Onboarding canned"
msgstr "新手簡介預製"

#. module: im_livechat_mail_bot
#: code:addons/im_livechat_mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"That's me! 🎉<br/>Try typing <span class=\"o_odoobot_command\">:</span> to "
"use canned responses."
msgstr ""

#. module: im_livechat_mail_bot
#: model:ir.model,name:im_livechat_mail_bot.model_res_users
msgid "Users"
msgstr "使用者"
