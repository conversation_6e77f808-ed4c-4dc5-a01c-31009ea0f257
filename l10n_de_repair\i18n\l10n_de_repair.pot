# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_de_repair
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-22 09:50+0000\n"
"PO-Revision-Date: 2021-07-22 09:50+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_de_repair
#: model:ir.model.fields,field_description:l10n_de_repair.field_repair_order__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_de_repair
#: model:ir.model.fields,field_description:l10n_de_repair.field_repair_order__id
msgid "ID"
msgstr ""

#. module: l10n_de_repair
#: model:ir.model.fields,field_description:l10n_de_repair.field_repair_order__l10n_de_document_title
msgid "L10N De Document Title"
msgstr ""

#. module: l10n_de_repair
#: model:ir.model.fields,field_description:l10n_de_repair.field_repair_order__l10n_de_template_data
msgid "L10N De Template Data"
msgstr ""

#. module: l10n_de_repair
#: model:ir.model.fields,field_description:l10n_de_repair.field_repair_order____last_update
msgid "Last Modified on"
msgstr ""

#. module: l10n_de_repair
#: code:addons/l10n_de_repair/models/repair.py:0
#, python-format
msgid "Lot/Serial Number"
msgstr ""

#. module: l10n_de_repair
#: code:addons/l10n_de_repair/models/repair.py:0
#, python-format
msgid "Printing Date"
msgstr ""

#. module: l10n_de_repair
#: code:addons/l10n_de_repair/models/repair.py:0
#, python-format
msgid "Product to Repair"
msgstr ""

#. module: l10n_de_repair
#: code:addons/l10n_de_repair/models/repair.py:0
#: model:ir.model,name:l10n_de_repair.model_repair_order
#, python-format
msgid "Repair Order"
msgstr ""

#. module: l10n_de_repair
#: code:addons/l10n_de_repair/models/repair.py:0
#, python-format
msgid "Repair Quotation"
msgstr ""

#. module: l10n_de_repair
#: code:addons/l10n_de_repair/models/repair.py:0
#, python-format
msgid "Warranty"
msgstr ""
