<?xml version='1.0' encoding='utf-8'?>
<odoo>
    <data>
        <record id="digest_tip_mrp_0" model="digest.tip">
            <field name="name">Tip: Use tablets in the shop to control manufacturing</field>
            <field name="sequence">600</field>
            <field name="group_id" ref="mrp.group_mrp_user" />
            <field name="tip_description" type="html">
<div>
    <p class="tip_title">Tip: Use tablets in the shop to control manufacturing</p>
    <p class="tip_content">With the Odoo work center control panel, your worker can start work orders in the shop and follow instructions of the worksheet. Quality tests are perfectly integrated into the process. Workers can trigger feedback loops, maintenance alerts, scrap products, etc.</p>
    <img src="/mrp/static/src/img/mrp-tablet.png" style="margin-top: 20px; max-width: 580px" width="100%" />
</div>
            </field>
        </record>
    </data>
</odoo>
