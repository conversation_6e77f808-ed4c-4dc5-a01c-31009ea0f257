<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_bo" model="res.partner">
        <field name="name">BO Company</field>
        <field name="vat"></field>
        <field name="street">Calle Chovena</field>
        <field name="city">Municipio Santa Cruz de la Sierra</field>
        <field name="country_id" ref="base.bo"/>
        
        <field name="zip">6495</field>
        <field name="phone">+591 71234567</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.boexample.com</field>
    </record>

    <record id="demo_company_bo" model="res.company">
        <field name="name">BO Company</field>
        <field name="partner_id" ref="partner_demo_company_bo"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_bo')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_bo.demo_company_bo'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_bo.bo_chart_template')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_bo.demo_company_bo')"/>
    </function>
</odoo>
