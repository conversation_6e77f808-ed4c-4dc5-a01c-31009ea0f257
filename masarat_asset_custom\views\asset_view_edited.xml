<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="view_account_asset_asset_formx1" model="ir.ui.view">
        <field name="name">account.asset.asset.extend</field>
        <field name="model">account.asset.asset</field>
        <field name="inherit_id" ref="base_accounting_kit.view_account_asset_asset_form"/>
        <field name="arch" type="xml">
            <field name="date" position="after">
                <field name="account_analytic_id"/>
            </field>
            <field name="move_check" position="replace">
                <field name="move_check" attrs="{'invisible': [('parent_state', '!=', 'open')]}" readonly="1"/>
            </field>
        </field>
    </record>
</odoo>
