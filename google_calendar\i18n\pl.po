# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* google_calendar
# 
# Translators:
# <PERSON><PERSON><PERSON> <stre<PERSON><PERSON>@gmail.com>, 2021
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON> <w<PERSON><PERSON>@gmail.com>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <tade<PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: Tadeusz Karpiński <<EMAIL>>, 2023\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: google_calendar
#: code:addons/google_calendar/models/google_sync.py:0
#, python-format
msgid "%(id)s and %(length)s following"
msgstr "%(id)s i %(length)s następujące"

#. module: google_calendar
#: code:addons/google_calendar/models/calendar.py:0
#, python-format
msgid "%(reminder_type)s - %(duration)s Days"
msgstr "%(reminder_type)s - %(duration)s dni"

#. module: google_calendar
#: code:addons/google_calendar/models/calendar.py:0
#, python-format
msgid "%(reminder_type)s - %(duration)s Hours"
msgstr "%(reminder_type)s - %(duration)s godzin"

#. module: google_calendar
#: code:addons/google_calendar/models/calendar.py:0
#, python-format
msgid "%(reminder_type)s - %(duration)s Minutes"
msgstr "%(reminder_type)s - %(duration)s minut"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/xml/base_calendar.xml:0
#, python-format
msgid "&nbsp;Google"
msgstr ""

#. module: google_calendar
#: code:addons/google_calendar/models/calendar.py:0
#, python-format
msgid "(No title)"
msgstr "(Brak tytułu)"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__active
#: model:ir.model.fields,field_description:google_calendar.field_calendar_recurrence__active
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_sync__active
msgid "Active"
msgstr "Aktywne"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid ""
"An administrator needs to configure Google Synchronization before you can "
"use it!"
msgstr ""
"Administrator musi skonfigurować Synchronizację Google, zanim będziesz mógł "
"jej używać!"

#. module: google_calendar
#: code:addons/google_calendar/models/google_credentials.py:0
#, python-format
msgid ""
"An error occurred while generating the token. Your authorization code may be"
" invalid or has already expired [%s]. You should check your Client ID and "
"secret on the Google APIs plateform or try to stop and restart your calendar"
" synchronisation."
msgstr ""
"Podczas generowania tokena wystąpił błąd. Twój kod autoryzacji może być "
"nieprawidłowy lub już wygasł [%s]. Powinieneś sprawdzić swój identyfikator i"
" sekret klienta na platformie Google API lub spróbować zatrzymać i ponownie "
"uruchomić synchronizację kalendarza."

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid "Are you sure you want to delete this record ?"
msgstr "Jesteś pewien, że chcesz usunąć ten rekord?"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_attendee
msgid "Calendar Attendee Information"
msgstr "Kalendarzowa informacja dla uczestników"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_event
msgid "Calendar Event"
msgstr "Wydarzenie w kalendarzu"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__calendar_cal_id
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_cal_id
msgid "Calendar ID"
msgstr "ID kalendarza"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.google_calendar_reset_account_view_form
msgid "Cancel"
msgstr "Anuluj"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "Client ID"
msgstr "Identyfikator klienta"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "Client Secret"
msgstr "Sekret klienta"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings__cal_client_id
msgid "Client_id"
msgstr "ID klienta"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings__cal_client_secret
msgid "Client_key"
msgstr "Klucz klienta"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_res_config_settings
msgid "Config Settings"
msgstr "Ustawienia konfiguracji"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid "Configuration"
msgstr "Konfiguracja"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.google_calendar_reset_account_view_form
msgid "Confirm"
msgstr "Potwierdź"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid "Confirmation"
msgstr "Potwierdzenie"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__create_uid
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__create_uid
msgid "Created by"
msgstr "Utworzył(a)"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__create_date
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__create_date
msgid "Created on"
msgstr "Data utworzenia"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/xml/google_calendar_popover.xml:0
#, python-format
msgid "Delete"
msgstr "Usuń"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__delete_odoo
msgid "Delete from Odoo"
msgstr "Usunięte z Odoo"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__delete_both
msgid "Delete from both"
msgstr "Usunięte z obu"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__delete_google
msgid "Delete from the current Google Calendar account"
msgstr "Usuń z bieżącego konta Kalendarza Google"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__display_name
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__display_name
msgid "Display Name"
msgstr "Nazwa wyświetlana"

#. module: google_calendar
#: code:addons/google_calendar/models/calendar.py:0
#, python-format
msgid "Email"
msgstr "E-mail"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_recurrence
msgid "Event Recurrence Rule"
msgstr "Reguła powtarzania zdarzeń"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/xml/base_calendar.xml:0
#, python-format
msgid "Google"
msgstr "Google"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_cal_account_id
msgid "Google Cal Account"
msgstr ""

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_users_form
msgid "Google Calendar"
msgstr "Kalendarz Google"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_google_calendar_credentials
msgid "Google Calendar Account Data"
msgstr "Dane konta kalendarza Google"

#. module: google_calendar
#: model:ir.actions.act_window,name:google_calendar.google_calendar_reset_account_action
#: model:ir.model,name:google_calendar.model_google_calendar_account_reset
msgid "Google Calendar Account Reset"
msgstr "Reset konta Kalendarza Google"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__google_id
msgid "Google Calendar Event Id"
msgstr "ID wydarzenia w Kalendarzu Google"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_recurrence__google_id
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_sync__google_id
msgid "Google Calendar Id"
msgstr "ID Kalendarza Google"

#. module: google_calendar
#: model:ir.actions.server,name:google_calendar.ir_cron_sync_all_cals_ir_actions_server
#: model:ir.cron,cron_name:google_calendar.ir_cron_sync_all_cals
#: model:ir.cron,name:google_calendar.ir_cron_sync_all_cals
msgid "Google Calendar: synchronization"
msgstr "Kalendarz Google: Synchronizacja"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__synchronization_stopped
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_synchronization_stopped
msgid "Google Synchronization stopped"
msgstr "Zatrzymano synchronizację z Google"

#. module: google_calendar
#: code:addons/google_calendar/models/google_sync.py:0
#, python-format
msgid "Google gave the following explanation: %s"
msgstr "Google podało następujące wyjaśnienie: %s"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__id
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__id
msgid "ID"
msgstr "ID"

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_calendar_event__active
msgid ""
"If the active field is set to false, it will allow you to hide the event "
"alarm information without removing it."
msgstr ""
"Jeśli przestawisz pole Aktywne na False, to ukryjesz alarm zdarzenia bez "
"usuwania go. "

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_google_calendar_credentials__calendar_cal_id
#: model:ir.model.fields,help:google_calendar.field_res_users__google_calendar_cal_id
msgid ""
"Last Calendar ID who has been synchronized. If it is changed, we remove all "
"links between GoogleID and Odoo Google Internal ID"
msgstr ""
"Ostatnie ID kalendarza zostało zsynchronizowane. Jeśli zostanie zmienione, "
"usuniemy wszystkie powiązania między ID Google i wewnętrznym ID Odoo Google."

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset____last_update
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials____last_update
msgid "Last Modified on"
msgstr "Data ostatniej modyfikacji"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__write_uid
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__write_uid
msgid "Last Updated by"
msgstr "Ostatnio aktualizowane przez"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__write_date
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__write_date
msgid "Last Updated on"
msgstr "Data ostatniej aktualizacji"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__dont_delete
msgid "Leave them untouched"
msgstr "Zostaw je nietknięte"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__need_sync
#: model:ir.model.fields,field_description:google_calendar.field_calendar_recurrence__need_sync
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_sync__need_sync
msgid "Need Sync"
msgstr "Potrzebna synchronizacja"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__calendar_sync_token
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_sync_token
msgid "Next Sync Token"
msgstr "Następny token synchronizacji"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__sync_policy
msgid "Next Synchronization"
msgstr "Następna synchronizacja"

#. module: google_calendar
#: code:addons/google_calendar/models/calendar.py:0
#, python-format
msgid "Notification"
msgstr "Powiadomienie"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid "Redirection"
msgstr "Przekierowanie"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__calendar_rtoken
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_rtoken
msgid "Refresh Token"
msgstr "Token odświeżania"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_users_form
msgid "Reset Account"
msgstr "Zresetuj konto"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.google_calendar_reset_account_view_form
msgid "Reset Google Calendar Account"
msgstr "Zresetuj konto Kalendarza Google"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid "Success"
msgstr "Powodzenie"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_google_calendar_sync
msgid "Synchronize a record with Google Calendar"
msgstr "Zsynchronizuj rekord z Kalendarzem Google"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__sync_policy__all
msgid "Synchronize all existing events"
msgstr "Zsynchronizuj wszystkie istniejące wydarzenia"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__sync_policy__new
msgid "Synchronize only new events"
msgstr "Zsynchronizuj tylko nowe wydarzenia"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid ""
"The Google Synchronization needs to be configured before you can use it, do "
"you want to do it now?"
msgstr ""
"Synchronizacja Google musi zostać skonfigurowana przed użyciem, czy chcesz "
"to zrobić teraz?"

#. module: google_calendar
#: code:addons/google_calendar/models/google_credentials.py:0
#, python-format
msgid "The account for the Google Calendar service is not configured."
msgstr "Konto usługi Kalendarza Google nie jest skonfigurowane."

#. module: google_calendar
#: code:addons/google_calendar/models/google_sync.py:0
#, python-format
msgid ""
"The following event could not be synced with Google Calendar. </br>It will "
"not be synced as long at it is not updated.</br>%(reason)s"
msgstr ""
"Następujące wydarzenie nie może być zsynchronizowane z Kalendarzem Google. "
"</br> Nie będzie ono synchronizowane tak długo, jak długo nie będzie "
"aktualizowane.</br>%(reason)s"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid "The synchronization with Google calendar was successfully stopped."
msgstr "Synchronizacja z Kalendarzem Google została pomyślnie zatrzymana."

#. module: google_calendar
#: model:ir.model.constraint,message:google_calendar.constraint_res_users_google_token_uniq
msgid "The user has already a google account"
msgstr "Użytkownik ma już konto Google"

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_google_calendar_account_reset__delete_policy
msgid "This will only affect events for which the user is the owner"
msgstr ""
"Będzie to miało wpływ tylko na wydarzenia, których właścicielem jest "
"użytkownik"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__calendar_token_validity
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_token_validity
msgid "Token Validity"
msgstr "Ważność tokena"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__user_id
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__user_ids
msgid "User"
msgstr "Użytkownik"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__calendar_token
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_token
msgid "User token"
msgstr "Token użytkownika"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__delete_policy
msgid "User's Existing Events"
msgstr "Istniejące wydarzenia użytkownika"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_res_users
msgid "Users"
msgstr "Użytkownicy"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid ""
"You are about to stop the synchronization of your calendar with Google. Are "
"you sure you want to continue?"
msgstr ""
"Za chwilę zatrzymasz synchronizację swojego kalendarza z Google. Jesteś "
"pewien, że chcesz kontynuować?"

#. module: google_calendar
#. openerp-web
#: code:addons/google_calendar/static/src/js/google_calendar.js:0
#, python-format
msgid "You will be redirected to Google to authorize access to your calendar!"
msgstr ""
"Zostaniesz przekierowany do Google, aby autoryzować dostęp do swojego "
"kalendarza!"

#. module: google_calendar
#: code:addons/google_calendar/models/google_sync.py:0
#: code:addons/google_calendar/models/google_sync.py:0
#, python-format
msgid "undefined time"
msgstr "nieokreślony czas"

#. module: google_calendar
#: code:addons/google_calendar/models/google_sync.py:0
#, python-format
msgid ""
"you don't seem to have permission to modify this event on Google Calendar"
msgstr ""
"wydaje się, że nie masz uprawnień do modyfikacji tego wydarzenia w "
"Kalendarzu Google"
