# -*- coding: utf-8 -*-
#############################################################################
#
#    Cybrosys Technologies Pvt. Ltd.
#
#    Copyright (C) 2019-TODAY Cybrosys Technologies(<https://www.cybrosys.com>)
#    Author: Cybrosys Techno Solutions(<https://www.cybrosys.com>)
#
#    You can modify it under the terms of the GNU LESSER
#    GENERAL PUBLIC LICENSE (LGPL v3), Version 3.
#
#    This program is distributed in the hope that it will be useful,
#    but WITHOUT ANY WARRANTY; without even the implied warranty of
#    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#    GNU LESSER GENERAL PUBLIC LICENSE (LGPL v3) for more details.
#
#    You should have received a copy of the GNU LESSER GENERAL PUBLIC LICENSE
#    (LGPL v3) along with this program.
#    If not, see <http://www.gnu.org/licenses/>.
#
#############################################################################
from datetime import date

from odoo import fields, models


class DayBookWizard(models.TransientModel):
    _name = 'account.day.book.report'
    _description = 'Account Day Book Report'

    company_id = fields.Many2one(
        'res.company', string='Company', readonly=True,
        default=lambda self: self.env.company)
    journal_ids = fields.Many2many(
        'account.journal', string='Journals', required=True,
        default=lambda self: self.env['account.journal'].search([]))
    target_move = fields.Selection(
        [('posted', 'All Posted Entries'), ('all', 'All Entries')],
        string='Target Moves', required=True, default='posted')
    account_ids = fields.Many2many(
        'account.account', 'account_report_daybook_account_rel', 'report_id',
        'account_id', string='Accounts')
    date_from = fields.Date(string='Start Date', default=date.today(),
                            required=True)
    date_to = fields.Date(string='End Date', default=date.today(),
                          required=True)

    def _build_contexts(self, data):
        return {
            'journal_ids': data.get('form', {}).get(
                'journal_ids', False) or False,
            'state': data.get('form', {}).get('target_move', '') or '',
            'date_from': data.get('form', {}).get('date_from', False) or False,
            'date_to': data.get('form', {}).get('date_to', False) or False,
            'strict_range': True if data.get('form', {}).get(
                'date_from', False) else False
        }

    def check_report(self):
        self.ensure_one()
        data = {
            'ids': self.env.context.get('active_ids', []),
            'model': self.env.context.get('active_model', 'ir.ui.menu'),
            'form': self.read(['date_from', 'date_to', 'journal_ids',
                               'target_move', 'account_ids'])[0]
        }
        used_context = self._build_contexts(data)
        data['form']['used_context'] = dict(
            used_context, lang=self.env.context.get('lang') or 'en_US')
        return self.env.ref(
            'base_accounting_kit.day_book_pdf_report').report_action(
            self, data=data)
