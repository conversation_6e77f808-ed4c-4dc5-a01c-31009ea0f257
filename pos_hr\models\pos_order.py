# -*- coding: utf-8 -*-
from odoo import models, fields, api


class PosOrder(models.Model):
    _inherit = "pos.order"

    employee_id = fields.Many2one('hr.employee', help="Person who uses the cash register. It can be a reliever, a student or an interim employee.", states={'done': [('readonly', True)], 'invoiced': [('readonly', True)]})
    cashier = fields.Char(string="Cashier", compute="_compute_cashier", store=True)

    @api.model
    def _order_fields(self, ui_order):
        order_fields = super(<PERSON>s<PERSON><PERSON><PERSON>, self)._order_fields(ui_order)
        order_fields['employee_id'] = ui_order.get('employee_id')
        return order_fields

    @api.depends('employee_id', 'user_id')
    def _compute_cashier(self):
        for order in self:
            if order.employee_id:
                order.cashier = order.employee_id.name
            else:
                order.cashier = order.user_id.name

    def _export_for_ui(self, order):
        result = super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self)._export_for_ui(order)
        result.update({
            'employee_id': order.employee_id.id,
        })
        return result

    def _get_fields_for_draft_order(self):
        fields = super(Pos<PERSON><PERSON><PERSON>, self)._get_fields_for_draft_order()
        fields.append('employee_id')
        return fields
