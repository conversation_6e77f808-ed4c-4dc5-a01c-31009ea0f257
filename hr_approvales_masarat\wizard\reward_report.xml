<?xml version="1.0"?>
<odoo>

    <record id="hr_reward_report_form" model="ir.ui.view">
        <field name="name">hr.masarat.reward.report.form</field>
        <field name="model">hr.masarat.reward.report</field>
        <field name="arch" type="xml">
            <form string="تقرير المكافئة">
                <group>
                    <group>
                        <field name="employee_id"
                               attrs="{'required':[('all_employee','=',False)],'invisible':[('all_employee','=',True)]}"/>
                        <field name="all_employee"/>
                    </group>
                    <group>
                        <field name="date_start" required="1"/>
                        <field name="date_end" required="1"/>
                    </group>
                </group>
                <footer>
                    <button name="get_report_action" type="object" string="انشاء"
                            class="btn-primary"/>
                    <button string="الغاء" class="btn-secondary" special="cancel"/>
                </footer>

            </form>
        </field>
    </record>


    <record id="action_reward_report_form" model="ir.actions.act_window">
        <field name="name">تقرير المكافئة</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">hr.masarat.reward.report</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="hr_reward_report_form"/>
        <field name="target">new</field>
    </record>


    <menuitem
            id="menu_reward_report_report"
            name="تقرير المكافئة"
            parent="hr_approvales_masarat.menu_masarat_approvale_report"
            groups="hr_approvales_masarat.group_hr_approvales_masarat"
            action="action_reward_report_form"
            sequence="6"/>


    <template id="reward_report_id_all">
        <t t-call="web.html_container">
            <t t-call="web.external_layout">
                <t t-set="index" t-value="0"/>
                <div class="page">
                    <h4 style="text-align: center;">
                        <strong>تقرير المكافئة</strong>
                    </h4>
                    <ul dir="rtl" class="nav" style="display: flex; justify-content: center;">
                        <li>
                            <h5 style="text-align: center;">للفترة من :
                                <span t-esc="start_date"/>
                            </h5>
                        </li>
                        <li>
                            <h5 style="text-align: center;">الى :
                                <span t-esc="end_date"/>
                            </h5>
                        </li>
                    </ul>
                    <br/>
                    <br/>
                    <br/>

                    <table style="font-size:16px; width: 100%; border-collapse: collapse; border-style: solid; border: 1px solid;">
                        <thead>
                            <tr style="text-align: center; height: 20px; font-weight:bold;">
                                <th style="width: 15%; text-align: center; border: 1px solid;">مجموع القيم الكلي</th>
                                <th style="width: 15%; text-align: center; border: 1px solid;">مجموع قيم المكافئات المقبولة</th>
                                <th style="width: 15%; text-align: center; border: 1px solid;">مجموع قيم المكافئات المرفوضة</th>
                                <th style="width: 15%; text-align: center; border: 1px solid;">اسم الموظف</th>
                                <th style="width: 5%; text-align: center; border: 1px solid;">#</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr t-foreach="employees_dict" t-as="line" style="text-align: center;">
                                <td style="border: 1px solid;">
                                    <span t-esc="str(employees_dict[line]['total'])"/>
                                </td>
                                <td style="border: 1px solid;">
                                    <span t-esc="str(employees_dict[line]['gm_approval'])"/>
                                </td>
                                <td style="border: 1px solid;">
                                    <span t-esc="str(employees_dict[line]['gm_refuse'])"/>
                                </td>
                                <td style="border: 1px solid;">
                                    <span t-esc="employees_dict[line]['name']"/>
                                </td>
                                <td style="border: 1px solid;">
                                    <t t-set="index" t-value="index + 1"/>
                                    <span t-esc="index"/>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <br/>
                </div>
            </t>
        </t>
    </template>

    <template id="reward_report_id_e">
        <t t-call="web.html_container">
            <t t-call="web.external_layout">
                <t t-set="index" t-value="1"/>
                <div class="page">
                    <h4 style="text-align: center;">
                        <strong>تقرير المكافئة </strong>
                        <span t-esc="employee_name"/>
                    </h4>
                    <ul dir="rtl" class="nav" style="display: flex; justify-content: center;">
                        <li>
                            <h5 style="text-align: center;">للفترة من :
                                <span t-esc="start_date"/>
                            </h5>
                        </li>
                        <li>
                            <h5 style="text-align: center;">الى :
                                <span t-esc="end_date"/>
                            </h5>
                        </li>
                    </ul>
                    <br/>
                    <br/>
                    <table style="font-size:16px; width: 100%; border-collapse: collapse; border-style: solid; border: 1px solid;">
                        <thead>
                            <tr style="text-align: center; height: 20px; font-weight:bold;">
                                <th style="width: 30%; text-align: center; border: 1px solid;">الحالة</th>
                                <th style="width: 20%; text-align: center; border: 1px solid; font-size:12px">قيمة المكافأة</th>
                                <th style="width: 25%; text-align: center; border: 1px solid;">سبب المكافأة</th>
                                <th style="width: 20%; text-align: center; border: 1px solid; font-size:12px">تاريخ المكافأة</th>
                                <th style="width: 5%; text-align: center; border: 1px solid; font-size:12px">#</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr t-foreach="employees_dict" t-as="line" style="text-align: center;">
                                <td style="border: 1px solid;">
                                    <span t-esc="line['state']"/>
                                </td>
                                <td style="border: 1px solid;">
                                    <span t-esc="line['reward_amount']"/>
                                </td>
                                <td style="border: 1px solid;">
                                    <span t-esc="line['reward_reason']"/>
                                </td>
                                <td style="border: 1px solid;">
                                    <span t-esc="line['request_date']"/>
                                </td>
                                <td style="border: 1px solid;">
                                    <span t-esc="index"/>
                                    <t t-set="index" t-value="index + 1"/>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <br/>
                </div>
            </t>
        </t>
    </template>

    <template id="reward_report_id">
        <t t-if="all_employee">
            <t t-call="hr_approvales_masarat.reward_report_id_all"></t>
        </t>
        <t t-else="">
            <t t-call="hr_approvales_masarat.reward_report_id_e"></t>
        </t>
    </template>

    <report
            id="reward_report_x1"
            string="Masarat Reward Report"
            model="hr.employee"
            report_type="qweb-pdf"
            name="hr_approvales_masarat.reward_report_id"
            file="hr_approvales_masarat.reward_report_id"
            menu="False"/>


</odoo>