# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON><PERSON> <naniwa.ma<PERSON><PERSON>@gmail.com>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# Andy <PERSON>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON> (Quartile) <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-03-10 09:48+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: Yoshi Tashiro (Quartile) <<EMAIL>>, 2023\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/sale.js:0
#, python-format
msgid " / Month"
msgstr " / 月"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard___data_fetched
msgid " Data Fetched"
msgstr " フェッチされたデータ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__sale_order_count
msgid "# Sale Orders"
msgstr "販売オーダ数"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__nbr
msgid "# of Lines"
msgstr "行数"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_payment_transaction__sale_order_ids_nbr
msgid "# of Sales Orders"
msgstr "販売オーダ数"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: sale
#: model:ir.actions.report,print_report_name:sale.action_report_pro_forma_invoice
msgid "'PRO-FORMA - %s' % (object.name)"
msgstr "PRO-FORMA - %s' % (object.name)"

#. module: sale
#: model:ir.actions.report,print_report_name:sale.action_report_saleorder
msgid ""
"(object.state in ('draft', 'sent') and 'Quotation - %s' % (object.name)) or "
"'Order - %s' % (object.name)"
msgstr ""
"(object.state in ('draft', 'sent') and 'Quotation - %s' % (object.name)) or "
"'Order - %s' % (object.name)"

#. module: sale
#: model:product.product,description_sale:sale.product_product_4e
#: model:product.product,description_sale:sale.product_product_4f
msgid "160x80cm, with large legs."
msgstr "160x80cm、大きな脚付き。"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid ""
"<b>Send the quote</b> to yourself and check what the customer will receive."
msgstr "<b>見積もり</b>を自分に<b>送信し</b>、顧客が受け取るものを確認します。"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "<b>Set a price</b>."
msgstr "<b>価格を設定します</b>。"

#. module: sale
#: model:mail.template,body_html:sale.mail_template_sale_confirmation
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 12px;\">\n"
"        Hello,\n"
"        <br/><br/>\n"
"        <t t-set=\"transaction\" t-value=\"object.get_portal_last_transaction()\"/>\n"
"        Your order <strong t-out=\"object.name or ''\">S00049</strong> amounting in <strong t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</strong>\n"
"        <t t-if=\"object.state == 'sale' or (transaction and transaction.state in ('done', 'authorized'))\">\n"
"            has been confirmed.<br/>\n"
"            Thank you for your trust!\n"
"        </t>\n"
"        <t t-elif=\"transaction and transaction.state == 'pending'\">\n"
"            is pending. It will be confirmed when the payment is received.\n"
"            <t t-if=\"object.reference\">\n"
"                Your payment reference is <strong t-out=\"object.reference or ''\"/>.\n"
"            </t>\n"
"        </t>\n"
"        <br/><br/>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <br/><br/>\n"
"    </p>\n"
"<t t-if=\"hasattr(object, 'website_id') and object.website_id\">\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"            <tr style=\"border-bottom: 2px solid #dee2e6;\">\n"
"                <td style=\"width: 150px;\"><strong>Products</strong></td>\n"
"                <td/>\n"
"                <td width=\"15%\" align=\"center\"><strong>Quantity</strong></td>\n"
"                <td width=\"20%\" align=\"right\"><strong>\n"
"                <t t-if=\"object.user_id.has_group('account.group_show_line_subtotals_tax_excluded')\">\n"
"                    VAT Excl.\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    VAT Incl.\n"
"                </t>\n"
"                </strong></td>\n"
"            </tr>\n"
"        </table>\n"
"        <t t-foreach=\"object.order_line\" t-as=\"line\">\n"
"            <t t-if=\"(not hasattr(line, 'is_delivery') or not line.is_delivery) and line.display_type in ['line_section', 'line_note']\">\n"
"                <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                    <t t-set=\"loop_cycle_number\" t-value=\"0\"/>\n"
"                    <tr t-att-style=\"'background-color: #f2f2f2' if loop_cycle_number % 2 == 0 else 'background-color: #ffffff'\">\n"
"                        <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number + 1\"/>\n"
"                        <td colspan=\"4\">\n"
"                            <t t-if=\"line.display_type == 'line_section'\">\n"
"                                <strong t-out=\"line.name or ''\">Taking care of Trees Course</strong>\n"
"                            </t>\n"
"                            <t t-elif=\"line.display_type == 'line_note'\">\n"
"                                <i t-out=\"line.name or ''\">Taking care of Trees Course</i>\n"
"                            </t>\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            </t>\n"
"            <t t-elif=\"(not hasattr(line, 'is_delivery') or not line.is_delivery)\">\n"
"                <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                    <t t-set=\"loop_cycle_number\" t-value=\"0\"/>\n"
"                    <tr t-att-style=\"'background-color: #f2f2f2' if loop_cycle_number % 2 == 0 else 'background-color: #ffffff'\">\n"
"                        <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number + 1\"/>\n"
"                        <td style=\"width: 150px;\">\n"
"                            <img t-attf-src=\"/web/image/product.product/{{ line.product_id.id }}/image_128\" style=\"width: 64px; height: 64px; object-fit: contain;\" alt=\"Product image\"/>\n"
"                        </td>\n"
"                        <td align=\"left\" t-out=\"line.product_id.name or ''\">\tTaking care of Trees Course</td>\n"
"                        <td width=\"15%\" align=\"center\" t-out=\"line.product_uom_qty or ''\">1</td>\n"
"                        <td width=\"20%\" align=\"right\"><strong>\n"
"                        <t t-if=\"object.user_id.has_group('account.group_show_line_subtotals_tax_excluded')\">\n"
"                            <t t-out=\"format_amount(line.price_reduce_taxexcl, object.currency_id) or ''\">$ 10.00</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <t t-out=\"format_amount(line.price_reduce_taxinc, object.currency_id) or ''\">$ 10.00</t>\n"
"                        </t>\n"
"                        </strong></td>\n"
"                    </tr>\n"
"                </table>\n"
"            </t>\n"
"        </t>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\" t-if=\"hasattr(object, 'carrier_id') and object.carrier_id\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><strong>Delivery:</strong></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_delivery, object.currency_id) or ''\">$ 0.00</td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%;\" align=\"right\"><strong>SubTotal:</strong></td>\n"
"                <td style=\"width: 10%;\" align=\"right\" t-out=\"format_amount(object.amount_untaxed, object.currency_id) or ''\">$ 10.00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\" t-else=\"\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><strong>SubTotal:</strong></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_untaxed, object.currency_id) or ''\">$ 10.00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%;\" align=\"right\"><strong>Taxes:</strong></td>\n"
"                <td style=\"width: 10%;\" align=\"right\" t-out=\"format_amount(object.amount_tax, object.currency_id) or ''\">$ 0.00</td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"width: 60%\"/>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><strong>Total:</strong></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div t-if=\"object.partner_invoice_id\" style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td style=\"padding-top: 10px;\">\n"
"                    <strong>Bill to:</strong>\n"
"                    <t t-out=\"object.partner_invoice_id.street or ''\">1201 S Figueroa St</t>\n"
"                    <t t-out=\"object.partner_invoice_id.city or ''\">Los Angeles</t>\n"
"                    <t t-out=\"object.partner_invoice_id.state_id.name or ''\">California</t>\n"
"                    <t t-out=\"object.partner_invoice_id.zip or ''\">90015</t>\n"
"                    <t t-out=\"object.partner_invoice_id.country_id.name or ''\">United States</t>\n"
"                </td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td>\n"
"                    <strong>Payment Method:</strong>\n"
"                    <t t-if=\"transaction.token_id\">\n"
"                        <t t-out=\"transaction.token_id.name or ''\"/>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        <t t-out=\"transaction.acquirer_id.sudo().name or ''\"/>\n"
"                    </t>\n"
"                    (<t t-out=\"format_amount(transaction.amount, object.currency_id) or ''\">$ 10.00</t>)\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div t-if=\"object.partner_shipping_id and not object.only_services\" style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td>\n"
"                    <br/>\n"
"                    <strong>Ship to:</strong>\n"
"                    <t t-out=\"object.partner_shipping_id.street or ''\">1201 S Figueroa St</t>\n"
"                    <t t-out=\"object.partner_shipping_id.city or ''\">Los Angeles</t>\n"
"                    <t t-out=\"object.partner_shipping_id.state_id.name or ''\">California</t>\n"
"                    <t t-out=\"object.partner_shipping_id.zip or ''\">90015</t>\n"
"                    <t t-out=\"object.partner_shipping_id.country_id.name or ''\">United States</t>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <table t-if=\"hasattr(object, 'carrier_id') and object.carrier_id\" width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td>\n"
"                    <strong>Shipping Method:</strong>\n"
"                    <t t-out=\"object.carrier_id.name or ''\"/>\n"
"                    <t t-if=\"object.carrier_id.fixed_price == 0.0\">\n"
"                        (Free)\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        (<t t-out=\"format_amount(object.carrier_id.fixed_price, object.currency_id) or ''\">$ 10.00</t>)\n"
"                    </t>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"</t>\n"
"</div>"
msgstr ""

#. module: sale
#: model:mail.template,body_html:sale.email_template_edi_sale
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        <t t-set=\"doc_name\" t-value=\"'quotation' if object.state in ('draft', 'sent') else 'order'\"/>\n"
"        Hello,\n"
"        <br/><br/>\n"
"        Your\n"
"        <t t-if=\"ctx.get('proforma')\">\n"
"            Pro forma invoice for <t t-out=\"doc_name or ''\">quotation</t> <strong t-out=\"object.name or ''\">S00052</strong>\n"
"            <t t-if=\"object.origin\">\n"
"                (with reference: <t t-out=\"object.origin or ''\"/> )\n"
"            </t>\n"
"            amounting in <strong t-out=\"format_amount(object.amount_total, object.pricelist_id.currency_id) or ''\">$ 10.00</strong> is available.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            <t t-out=\"doc_name or ''\">quotation</t> <strong t-out=\"object.name or ''\"/>\n"
"            <t t-if=\"object.origin\">\n"
"                (with reference: <t t-out=\"object.origin or ''\">S00052</t> )\n"
"            </t>\n"
"            amounting in <strong t-out=\"format_amount(object.amount_total, object.pricelist_id.currency_id) or ''\">$ 10.00</strong> is ready for review.\n"
"        </t>\n"
"        <br/><br/>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <br/>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-comment\"/> Contact us to get a new quotation."
msgstr "<i class=\"fa fa-comment\"/> 新しくお見積が必要な場合はお知らせください。"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-comment\"/> Feedback"
msgstr "<i class=\"fa fa-comment\"/> フィードバック"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-comment\"/> Send message"
msgstr "<i class=\"fa fa-comment\"/> メッセージ送信"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-download\"/> Download"
msgstr "<i class=\"fa fa-download\"/>ダウンロード"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "<i class=\"fa fa-fw fa-check\" role=\"img\" aria-label=\"Done\" title=\"Done\"/>Done"
msgstr "<i class=\"fa fa-fw fa-check\" role=\"img\" aria-label=\"Done\" title=\"Done\"/>完了"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> <b>Paid</b>"
msgstr "<i class=\"fa fa-fw fa-check\"/> <b>支払済</b>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<i class=\"fa fa-fw fa-clock-o\"/> <b>Waiting Payment</b>"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/> <b>支払待ち</b>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "<i class=\"fa fa-fw fa-clock-o\"/> Expired"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/> 期限切れ"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "<i class=\"fa fa-fw fa-remove\"/> Cancelled"
msgstr "<i class=\"fa fa-fw fa-remove\"/> 取消済"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_lead_partner_kanban_view
msgid ""
"<i class=\"fa fa-fw fa-usd\" role=\"img\" aria-label=\"Sale orders\" "
"title=\"Sales orders\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-usd\" role=\"img\" aria-label=\"Sale orders\" "
"title=\"Sales orders\"/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-print\"/> Print"
msgstr "<i class=\"fa fa-print\"/> 印刷"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-times\"/> Reject"
msgstr "<i class=\"fa fa-times\"/> 拒否"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<small><b class=\"text-muted\">This offer expires on</b></small>"
msgstr "<small><b class=\"text-muted\">このオファーの有効期限は</b></small>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<small><b class=\"text-muted\">Your advantage</b></small>"
msgstr "<small><b class=\"text-muted\">特別価格</b></small>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid ""
"<span attrs=\"{'invisible': [('advance_payment_method', '!=', "
"'percentage')]}\" class=\"oe_inline\">%</span>"
msgstr ""
"<span attrs=\"{'invisible': [('advance_payment_method', '!=', "
"'percentage')]}\" class=\"oe_inline\">%</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid ""
"<span class=\"d-none d-md-inline\">Sales Order #</span>\n"
"                            <span class=\"d-block d-md-none\">Ref.</span>"
msgstr ""
"<span class=\"d-none d-md-inline\">販売オーダ番号</span>\n"
"<span class=\"d-block d-md-none\">Ref.</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Confirmation Email</span>"
msgstr "<span class=\"o_form_label\">確認メール</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Down Payments</span>"
msgstr "<span class=\"o_form_label\">前受金</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"<span class=\"o_stat_text\">Customer</span>\n"
"                                <span class=\"o_stat_text\">Preview</span>"
msgstr ""
"<span class=\"o_stat_text\">顧客</span>\n"
"                                <span class=\"o_stat_text\">プレビュー</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
msgid "<span class=\"o_stat_text\">Sold</span>"
msgstr "<span class=\"o_stat_text\">販売</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_form
msgid "<span class=\"oe_read_only\">/ Month</span>"
msgstr "<span class=\"oe_read_only\">/ 月</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid ""
"<span groups=\"account.group_show_line_subtotals_tax_excluded\">Amount</span>\n"
"                                <span groups=\"account.group_show_line_subtotals_tax_included\">Total Price</span>"
msgstr ""
"<span groups=\"account.group_show_line_subtotals_tax_excluded\">税抜金額</span>\n"
"<span groups=\"account.group_show_line_subtotals_tax_included\">税込金額</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid ""
"<span groups=\"account.group_show_line_subtotals_tax_excluded\">Amount</span>\n"
"                            <span groups=\"account.group_show_line_subtotals_tax_included\">Total Price</span>"
msgstr ""
"<span groups=\"account.group_show_line_subtotals_tax_excluded\">金額</span>\n"
"<span groups=\"account.group_show_line_subtotals_tax_included\">合計価格</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<span>Accepted on the behalf of:</span>"
msgstr "<span>次の組織を代表:</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<span>By paying this proposal, I agree to the following terms:</span>"
msgstr "<span>この提案へ支払うことにより、私は以下の条件に同意します。</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<span>By signing this proposal, I agree to the following terms:</span>"
msgstr "<span>この提案への署名をもち、次の条件に同意します:</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<span>Disc.%</span>"
msgstr "<span>値引%</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<span>For an amount of:</span>"
msgstr "<span>金額:</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<span>Pro-Forma Invoice # </span>"
msgstr "<span>お見積もり ＃</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<span>Taxes</span>"
msgstr "<span>税</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<span>With payment terms:</span>"
msgstr "<span>支払い条件付き:</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong class=\"d-block mb-1\">Invoices</strong>"
msgstr "<strong class=\"d-block mb-1\">請求書</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong class=\"d-block mb-1\">Shipping Address:</strong>"
msgstr "<strong class=\"d-block mb-1\">出荷先:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong class=\"mr16\">Subtotal</strong>"
msgstr "<strong class=\"mr16\">小計</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong class=\"text-muted\">Salesperson</strong>"
msgstr "<strong class=\"text-muted\">販売担当者</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong>Expiration Date:</strong>"
msgstr "<strong>有効期日:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Expiration:</strong>"
msgstr "<strong>有効期日:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Fiscal Position Remark:</strong>"
msgstr "<strong>財務状態備考:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid ""
"<strong>No suitable payment option could be found.</strong><br/>\n"
"                                        If you believe that it is an error, please contact the website administrator."
msgstr ""
"<strong>No suitable payment option could be found.</strong><br/>\n"
"                                        エラーと思われる場合は、サイトの管理者までお問い合わせください。"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong>Order Date:</strong>"
msgstr "<strong>発注日:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong>Quotation Date:</strong>"
msgstr "<strong>見積日:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Salesperson:</strong>"
msgstr "<strong>販売担当者:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_invoice_document_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Shipping Address:</strong>"
msgstr "<strong>出荷先:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Signature</strong>"
msgstr "<strong>署名</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong>Thank You!</strong><br/>"
msgstr "<strong>ありがとうございました!</strong><br/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong>This offer expired!</strong>"
msgstr "<strong>このオファーは期限切れです。</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong>This quotation has been canceled.</strong>"
msgstr "<strong>この見積は取り消されました。</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Your Reference:</strong>"
msgstr "<strong>お客様参照:</strong>"

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_sale_order_date_order_conditional_required
msgid "A confirmed sales order requires a confirmation date."
msgstr "確認済みの販売注文には確認日が必要です。"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__advance_payment_method
msgid ""
"A standard invoice is issued with all the order lines ready for invoicing,"
"         according to their invoicing policy (based on ordered or delivered "
"quantity)."
msgstr "標準の請求書は、請求ポリシーに従って(注文数量または納品数量に基づいて)、すべての注文明細に請求の準備ができた状態で発行されます。"

#. module: sale
#: model:res.groups,name:sale.group_warning_sale
msgid "A warning can be set on a product or a customer (Sale)"
msgstr "プロダクトまたは取引先に警告設定可(販売)"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Ability to select a package type in sales orders and to force a quantity "
"that is a multiple of the number of units per package."
msgstr "販売オーダにて梱包タイプを選択することで、梱包入数の倍数の数量を強制できるになります。"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Accept &amp; Pay"
msgstr "承認 &amp; 支払"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Accept &amp; Sign"
msgstr "承認して署名する"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__access_warning
msgid "Access warning"
msgstr "アクセス警告"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism :\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"製品構成に応じて、出荷数量はメカニズムによって自動的に計算できます。\n"
"-手動:数量はライン上で手動で設定されます\n"
"-分析から費用:数量は転記された費用からの数量合計です\n"
"-タイムシート:数量は時間の合計ですこの販売ラインにリンクされたタスクに記録された\n"
"-在庫移動:数量は確認済みのピッキングから取得されます"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__acc_number
msgid "Account Number"
msgstr "口座番号"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__deposit_account_id
msgid "Account used for deposits"
msgstr "預金用口座"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_accrued_revenue_entry
msgid "Accrued Revenue Entry"
msgstr "未収収益仕訳"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_needaction
msgid "Action Needed"
msgstr "要アクション"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_ids
msgid "Activities"
msgstr "活動"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "例外の活動を示す文字装飾"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_state
msgid "Activity State"
msgstr "活動状態"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_type_icon
msgid "Activity Type Icon"
msgstr "活動種別アイコン"

#. module: sale
#: model:ir.actions.act_window,name:sale.mail_activity_type_action_config_sale
#: model:ir.ui.menu,name:sale.sale_menu_config_activity_type
msgid "Activity Types"
msgstr "活動タイプ"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add a note"
msgstr "メモ追加"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add a product"
msgstr "プロダクト追加"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add a section"
msgstr "セクション追加"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Add several variants to an order from a grid"
msgstr "グリッドビューからオーダに複数のバリアントを追加"

#. module: sale
#: model:res.groups,name:sale.group_delivery_invoice_address
msgid "Addresses in Sales Orders"
msgstr "販売オーダ内のアドレス"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Allows you to send Pro-Forma Invoice to your customers"
msgstr "顧客に見積送状を送る"

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__group_proforma_sales
msgid "Allows you to send pro-forma invoice."
msgstr "見積送状を送る"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_amazon
msgid "Amazon Sync"
msgstr "Amazon連携"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_undiscounted
msgid "Amount Before Discount"
msgstr "割引前の金額"

#. module: sale
#: code:addons/sale/models/payment_transaction.py:0
#, python-format
msgid "Amount Mismatch (%s)"
msgstr "不一致の量(%s)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__quotations_amount
msgid "Amount of quotations to invoice"
msgstr "請求対象見積金額"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid ""
"An order is to upsell when delivered quantities are above initially\n"
"                ordered quantities, and the invoicing policy is based on ordered quantities."
msgstr "請求方針でオーダ数量を請求が設定され、納品数量が最初に注文された数量を上回ったときに、アップセルするオーダです。"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__analytic_account_id
#: model:ir.model.fields,field_description:sale.field_sale_order__analytic_account_id
#: model:ir.model.fields,field_description:sale.field_sale_report__analytic_account_id
msgid "Analytic Account"
msgstr "分析勘定"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__qty_delivered_method__analytic
msgid "Analytic From Expenses"
msgstr "経費からの分析"

#. module: sale
#: model:ir.model,name:sale.model_account_analytic_line
msgid "Analytic Line"
msgstr "分析行"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__analytic_tag_ids
msgid "Analytic Tags"
msgstr "分析タグ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__analytic_line_ids
msgid "Analytic lines"
msgstr "分析行"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Apply manual discounts on sales order lines or display discounts computed "
"from pricelists (option to activate in the pricelist configuration)."
msgstr "受注明細に手動割引を適用するか、価格表から計算された割引を表示します(価格表構成でアクティブ化するオプション)。"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"Are you sure you want to void the authorized transaction? This action can't "
"be undone."
msgstr "承認されたトランザクションを無効にしますか?このアクションは元に戻せません。"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid ""
"As an example, if you sell pre-paid hours of services, Odoo recommends you\n"
"                to sell extra hours when all ordered hours have been consumed."
msgstr ""
"たとえば、プリペイド式の時間単位サービスを販売する場合、Odooは、注文されたすべての時間が消費されたときに超過時間を販売することをお勧めします。"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__expense_policy__cost
msgid "At cost"
msgstr "原価で"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_attachment_count
msgid "Attachment Count"
msgstr "添付数"

#. module: sale
#: model:ir.model,name:sale.model_product_attribute_value
msgid "Attribute Value"
msgstr "属性値"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Attributes"
msgstr "属性"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__authorized_transaction_ids
msgid "Authorized Transactions"
msgstr "承認されたトランザクション"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__automatic_invoice
msgid "Automatic Invoice"
msgstr "自動請求"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Automatic email sent after the customer has signed or paid online"
msgstr "顧客がオンラインで署名または支払い後に送信される自動メール"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__journal_name
msgid "Bank Name"
msgstr "銀行名"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__payment_acquirer__so_reference_type__partner
msgid "Based on Customer ID"
msgstr "顧客IDに基づく"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__payment_acquirer__so_reference_type__so_name
msgid "Based on Document Reference"
msgstr "ドキュメントリファレンスに基づく"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__sale_line_warn__block
#: model:ir.model.fields.selection,name:sale.selection__res_partner__sale_warn__block
msgid "Blocking Message"
msgstr "阻止メッセージ"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Boost your sales with two kinds of discount programs: promotions and coupon "
"codes. Specific conditions can be set (products, customers, minimum purchase"
" amount, period). Rewards can be discounts (% or amount) or free products."
msgstr ""
"プロモーションとクーポンコードの2種類の割引プログラムで売り上げを伸ばしましょう:特定の条件を設定できます(製品、顧客、最小購入額、期間)。報酬は、割引(%または金額)または無料の製品にすることができます。"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_move__campaign_id
#: model:ir.model.fields,field_description:sale.field_sale_order__campaign_id
#: model:ir.model.fields,field_description:sale.field_sale_report__campaign_id
msgid "Campaign"
msgstr "キャンペーン"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_updatable
msgid "Can Edit Product"
msgstr "プロダクトを編集できます"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Cancel"
msgstr "取消"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Cancel Sales Order"
msgstr "受注のキャンセル"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__cancel
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__cancel
msgid "Cancelled"
msgstr "取消済"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Capture Transaction"
msgstr "トランザクションをキャプチャする"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom_category_id
msgid "Category"
msgstr "カテゴリ"

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team__use_quotations
msgid ""
"Check this box if you send quotations to your customers rather than "
"confirming orders straight away."
msgstr "注文をすぐに確認するのではなく、顧客に見積もりを送信する場合は、このチェックボックスをオンにします。"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_order_confirmation_step
msgid "Choose between electronic signatures or online payments."
msgstr "電子署名またはオンライン支払を選択"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_open_sale_onboarding_payment_acquirer_wizard
msgid "Choose how to confirm quotations"
msgstr "見積の確認方法を選択"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Click here to add some products or services to your quotation."
msgstr "ここをクリックして見積にプロダクトやサービスを追加してください。"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
msgid "Click to define an invoicing target"
msgstr "クリックして請求対象を定義します"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Close"
msgstr "閉じる"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_quotation_onboarding_state__closed
msgid "Closed"
msgstr "クローズ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_payment_acquirer__so_reference_type
msgid "Communication"
msgstr "コミュニケーション"

#. module: sale
#: model:ir.model,name:sale.model_res_company
msgid "Companies"
msgstr "会社"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__company_id
#: model:ir.model.fields,field_description:sale.field_sale_order__company_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__company_id
#: model:ir.model.fields,field_description:sale.field_sale_report__company_id
#: model:ir.model.fields,field_description:sale.field_utm_campaign__company_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Company"
msgstr "会社"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr "DHLで配送費用計算し出荷"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with Easypost"
msgstr "Easypostで配送費用計算し出荷"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr "FedExで配送費用計算し出荷"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr "UPSで配送費用計算し出荷"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr "USPSで配送費用計算し出荷"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr "bpostで配送費用計算し出荷"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs on orders"
msgstr "オーダで配送費用を計算"

#. module: sale
#: model:ir.model,name:sale.model_res_config_settings
msgid "Config Settings"
msgstr "コンフィグ設定"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_config
msgid "Configuration"
msgstr "設定"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Confirm"
msgstr "確認"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__confirmation_mail_template_id
msgid "Confirmation Email Template"
msgstr "確認メールテンプレート"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Connectors"
msgstr "コネクター"

#. module: sale
#: model:ir.model,name:sale.model_res_partner
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Contact"
msgstr "連絡先"

#. module: sale
#: model_terms:product.product,website_description:sale.product_product_4e
#: model_terms:product.product,website_description:sale.product_product_4f
msgid "Contact Us"
msgstr "お問い合わせ"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr "単位間の変換は同じカテゴリに属している場合のみ可能です。変換は比率に基づいて行われます。"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_coupon
msgid "Coupons & Promotions"
msgstr "クーポン & プロモーション"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
msgid "Create Date"
msgstr "作成日"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__advance_payment_method
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Create Invoice"
msgstr "請求書作成"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_invoice_salesteams
msgid "Create a customer invoice"
msgstr "顧客請求書を作成しましょう。"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.product_template_action
msgid "Create a new product"
msgstr "プロダクト新規作成"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.act_res_partner_2_sale_order
#: model_terms:ir.actions.act_window,help:sale.action_orders
#: model_terms:ir.actions.act_window,help:sale.action_orders_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations_with_onboarding
#: model_terms:ir.actions.act_window,help:sale.action_sale_order_form_view
msgid "Create a new quotation, the first step of a new sale!"
msgstr "販売の第一歩、見積を作成しましょう。"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Create and View Invoice"
msgstr "請求書作成・照会"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_view_sale_advance_payment_inv
msgid "Create invoices"
msgstr "請求書作成"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_invoice_salesteams
msgid ""
"Create invoices, register payments and keep track of the discussions with "
"your customers."
msgstr "請求書を作成し、支払や顧客とのコミュニケーションを管理しましょう。"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_line__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__create_uid
msgid "Created by"
msgstr "作成者"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__create_date
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__create_date
#: model:ir.model.fields,field_description:sale.field_sale_order_line__create_date
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__create_date
msgid "Created on"
msgstr "作成日"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__create_date
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Creation Date"
msgstr "作成日"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__date_order
msgid ""
"Creation date of draft/sent orders,\n"
"Confirmation date of confirmed orders."
msgstr "ドラフト/送信済み注文の作成日、確認済み注文の確認日。"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_acquirer_onboarding_wizard__payment_method__stripe
msgid "Credit & Debit card (via Stripe)"
msgstr "クレジット＆デビットカード（Stripe経由）"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__currency_id
#: model:ir.model.fields,field_description:sale.field_sale_order__currency_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__currency_id
#: model:ir.model.fields,field_description:sale.field_utm_campaign__currency_id
msgid "Currency"
msgstr "通貨"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__currency_rate
msgid "Currency Rate"
msgstr "為替レート"

#. module: sale
#: model:product.attribute.value,name:sale.product_attribute_value_7
#: model:product.template.attribute.value,name:sale.product_4_attribute_1_value_3
msgid "Custom"
msgstr "カスタム"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_custom_attribute_value_ids
msgid "Custom Values"
msgstr "カスタム値"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_acquirer_onboarding_wizard__payment_method__manual
msgid "Custom payment instructions"
msgstr "カスタム支払案内"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__order_partner_id
#: model:ir.model.fields,field_description:sale.field_sale_report__partner_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Customer"
msgstr "顧客"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__auth_signup_uninvited
msgid "Customer Account"
msgstr "顧客アカウント"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_sale_delivery_address
msgid "Customer Addresses"
msgstr "顧客アドレス"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__country_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Customer Country"
msgstr "顧客国"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__commercial_partner_id
msgid "Customer Entity"
msgstr "顧客事業体"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__industry_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Customer Industry"
msgstr "顧客業界"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__access_url
msgid "Customer Portal URL"
msgstr "顧客ポータルURL"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__client_order_ref
msgid "Customer Reference"
msgstr "顧客参照"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Customer Signature"
msgstr "顧客署名"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__deposit_taxes_id
msgid "Customer Taxes"
msgstr "顧客税"

#. module: sale
#: model:ir.ui.menu,name:sale.res_partner_menu
msgid "Customers"
msgstr "顧客"

#. module: sale
#: model:product.product,name:sale.product_product_4e
#: model:product.product,name:sale.product_product_4f
msgid "Customizable Desk"
msgstr "カスタマイズ可能なデスク"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.onboarding_quotation_layout_step
msgid "Customize"
msgstr "カスタマイズ"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.onboarding_quotation_layout_step
msgid "Customize the look of your quotations."
msgstr "見積書デザインをカスタマイズ"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Customize your quotes and orders."
msgstr "見積もりと注文をカスタマイズします。"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_dhl
msgid "DHL Express Connector"
msgstr "DHL Expressコネクタ"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Date"
msgstr "日付"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__date_order
msgid "Date Order"
msgstr "オーダ日"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__signed_on
msgid "Date of the signature."
msgstr "署名の日付。"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__create_date
msgid "Date on which sales order is created."
msgstr "販売オーダが作成された日。"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Date:"
msgstr "日付:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__deduct_down_payments
msgid "Deduct down payments"
msgstr "前受金を差し引く"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Default Limit:"
msgstr "デフォルト期間:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__use_quotation_validity_days
msgid "Default Quotation Validity"
msgstr "デフォルト見積有効期間"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__quotation_validity_days
#: model:ir.model.fields,field_description:sale.field_res_config_settings__quotation_validity_days
msgid "Default Quotation Validity (Days)"
msgstr "デフォルト見積有効期間(日)"

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__deposit_default_product_id
msgid "Default product used for payment advances"
msgstr "先払いで使用されるデフォルトプロダクト"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Deliver Content by Email"
msgstr "Eメールで内容を配信"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Delivered"
msgstr "配送済"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_delivered_manual
msgid "Delivered Manually"
msgstr "手動で配信"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_delivered
msgid "Delivered Quantity"
msgstr "配送済数量"

#. module: sale
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid "Delivered Quantity: %s"
msgstr "納品数量:%s"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__invoice_policy__delivery
msgid "Delivered quantities"
msgstr "配送済数量"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__partner_shipping_id
#: model:ir.model.fields,field_description:sale.field_account_move__partner_shipping_id
#: model:ir.model.fields,field_description:sale.field_account_payment__partner_shipping_id
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_shipping_id
msgid "Delivery Address"
msgstr "配送先"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__commitment_date
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Delivery Date"
msgstr "配送日"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery
msgid "Delivery Methods"
msgstr "配送方法"

#. module: sale
#: model:ir.model.fields,help:sale.field_account_bank_statement_line__partner_shipping_id
#: model:ir.model.fields,help:sale.field_account_move__partner_shipping_id
#: model:ir.model.fields,help:sale.field_account_payment__partner_shipping_id
msgid "Delivery address for current invoice."
msgstr "現在の請求書の配達先住所。"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__expected_date
msgid ""
"Delivery date you can promise to the customer, computed from the minimum "
"lead time of the order lines in case of Service products. In case of "
"shipping, the shipping policy of the order will be taken into account to "
"either use the minimum or maximum lead time of the order lines."
msgstr ""
"サービス製品の場合、注文ラインの最小リードタイムから計算された、顧客に約束できる納期。配送の場合、注文の配送ポリシーが考慮され、注文ラインの最小または最大のリードタイムが使用されます。"

#. module: sale
#: model:product.product,name:sale.advance_product_0
#: model:product.template,name:sale.advance_product_0_product_template
msgid "Deposit"
msgstr "デポジット"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__deposit_default_product_id
msgid "Deposit Product"
msgstr "頭金プロダクト"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__name
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Description"
msgstr "説明"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Disc.%"
msgstr "値引%"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__discount
msgid "Discount %"
msgstr "値引%"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__discount
msgid "Discount (%)"
msgstr "値引 (%)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__discount_amount
msgid "Discount Amount"
msgstr "値引額"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__display_name
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order_line__display_name
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__display_name
#: model:ir.model.fields,field_description:sale.field_sale_report__display_name
msgid "Display Name"
msgstr "表示名"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__display_type
msgid "Display Type"
msgstr "表示タイプ"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/sale_order_view.js:0
#, python-format
msgid "Do you want to apply this discount to all order lines?"
msgstr "この割引をすべての注文ラインに適用しますか?"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Documentation"
msgstr "ドキュメント"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_order_confirmation_state__done
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_sample_quotation_state__done
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_quotation_onboarding_state__done
msgid "Done"
msgstr "完了"

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid "Down Payment"
msgstr "前受金"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__amount
msgid "Down Payment Amount"
msgstr "前受金額"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__fixed_amount
msgid "Down Payment Amount (Fixed)"
msgstr "前受金(固定)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__product_id
msgid "Down Payment Product"
msgstr "前受金プロダクト"

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid "Down Payment: %s"
msgstr "前受金:%s"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Down Payments"
msgstr "前受金"

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid "Down payment"
msgstr "頭金"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_advance_payment_inv__advance_payment_method__fixed
msgid "Down payment (fixed amount)"
msgstr "前受金 (固定金額)"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_advance_payment_inv__advance_payment_method__percentage
msgid "Down payment (percentage)"
msgstr "前受金 (パーセント)"

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid "Down payment of %s%%"
msgstr "%s%% の前受金"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__is_downpayment
msgid ""
"Down payments are made when creating invoices from a sales order. They are "
"not copied when duplicating a sales order."
msgstr "前受金は、受注から請求書を作成するときに行われます。受注を複製する場合、これらはコピーされません。"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Download"
msgstr "ダウンロード"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__draft
msgid "Draft Quotation"
msgstr "ドラフト見積"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
msgid "Draft invoices for this order will be cancelled."
msgstr "この注文のドラフト請求書はキャンセルされます。"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_easypost
msgid "Easypost Connector"
msgstr "Easypostコネクタ"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/product_configurator_widget.js:0
#: code:addons/sale/static/src/js/product_configurator_widget.js:0
#, python-format
msgid "Edit Configuration"
msgstr "構成の編集"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_acquirer_onboarding_wizard__payment_method__digital_signature
msgid "Electronic signature"
msgstr "電子署名"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__paypal_email_account
msgid "Email"
msgstr "Eメール"

#. module: sale
#: model:ir.model,name:sale.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Eメール構成ウィザード"

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__confirmation_mail_template_id
msgid "Email sent to the customer once the order is paid."
msgstr "注文が支払われると、顧客に電子メールが送信されます。"

#. module: sale
#: model_terms:product.product,website_description:sale.product_product_4e
#: model_terms:product.product,website_description:sale.product_product_4f
msgid "Ergonomic"
msgstr "エルゴノミック"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__expected_date
msgid "Expected Date"
msgstr "予定日"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Expected:"
msgstr "予想:"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__expense_policy
#: model:ir.model.fields,help:sale.field_product_template__expense_policy
msgid ""
"Expenses and vendor bills can be re-invoiced to a customer.With this option,"
" a validated expense can be re-invoice to a customer at its cost or sales "
"price."
msgstr "経費とベンダーの請求書は顧客に再請求できます。このオプションを使用すると、検証済みの経費をそのコストまたは販売価格で顧客に再請求できます。"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__validity_date
msgid "Expiration"
msgstr "有効期日"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Extended Filters"
msgstr "拡張フィルタ"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/product_configurator_widget.js:0
#: code:addons/sale/static/src/js/product_configurator_widget.js:0
#, python-format
msgid "External Link"
msgstr "外部リンク"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_no_variant_attribute_value_ids
msgid "Extra Values"
msgstr "追加の値"

#. module: sale
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid "Extra line with %s"
msgstr "%sの追加明細"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_fedex
msgid "FedEx Connector"
msgstr "FedExコネクタ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__fiscal_position_id
msgid "Fiscal Position"
msgstr "会計ポジション"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__fiscal_position_id
msgid ""
"Fiscal positions are used to adapt taxes and accounts for particular "
"customers or sales orders/invoices.The default value comes from the "
"customer."
msgstr "会計ポジションは、特定の顧客または受注/請求書の税金と勘定を調整するために使用されます。デフォルト値は顧客から取得されます。"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_follower_ids
msgid "Followers"
msgstr "フォロワー"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_partner_ids
msgid "Followers (Partners)"
msgstr "フォロワー (取引先)"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesomeのアイコン 例. fa-tasks"

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_sale_order_line_non_accountable_null_fields
msgid "Forbidden values on non-accountable sale order line"
msgstr "説明責任のない販売注文明細の禁止値"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_config_settings__auth_signup_uninvited__b2c
msgid "Free sign up"
msgstr "自由にサインアップ"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_account_invoice_report_salesteam
msgid ""
"From this report, you can have an overview of the amount invoiced to your "
"customer. The search tool can also be used to personalise your Invoices "
"reports and so, match this analysis to your needs."
msgstr ""
"このレポートから、お客様に請求される金額の概要を知ることができます。 "
"検索ツールを使用して請求書のレポートをパーソナライズすることもできます。この分析を要件に合わせることができます。"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__invoiced
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__invoiced
msgid "Fully Invoiced"
msgstr "完全請求済"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Future Activities"
msgstr "将来の活動"

#. module: sale
#: model:ir.model,name:sale.model_payment_link_wizard
msgid "Generate Sales Payment Link"
msgstr "販売支払いリンクを生成する"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_sale_order_generate_link
msgid "Generate a Payment Link"
msgstr "支払用リンクを生成"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Generate the invoice automatically when the online payment is confirmed"
msgstr "オンライン支払が完了したときに請求書を自動生成"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Get warnings in orders for products or customers"
msgstr "プロダクトや顧客にオーダ用の警告を設定"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Good job, let's continue."
msgstr "よくできました。続けましょう。"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Grant discounts on sales order lines"
msgstr "販売オーダラインに値引適用"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__weight
msgid "Gross Weight"
msgstr "総重量"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Group By"
msgstr "グループ化"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__has_message
msgid "Has Message"
msgstr "メッセージあり"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__show_update_pricelist
msgid "Has Pricelist Changed"
msgstr "価格表が変更されました"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__has_down_payments
msgid "Has down payments"
msgstr "前受金がある"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "History"
msgstr "履歴"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__id
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__id
#: model:ir.model.fields,field_description:sale.field_sale_order__id
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__id
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__id
#: model:ir.model.fields,field_description:sale.field_sale_report__id
msgid "ID"
msgstr "ID"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_exception_icon
msgid "Icon"
msgstr "アイコン"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "例外的なアクティビティを示唆するアイコン"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_needaction
#: model:ir.model.fields,help:sale.field_sale_order__message_unread
msgid "If checked, new messages require your attention."
msgstr "チェックされている場合は、新しいメッセージに注意が必要です。"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_has_error
#: model:ir.model.fields,help:sale.field_sale_order__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "チェックした場合、一部のメッセージで配信エラーが発生しています。"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_auto_done_setting
msgid ""
"If the sale is locked, you can not modify it anymore. However, you will "
"still be able to invoice or deliver."
msgstr "販売がロックされている場合は、これ以上修正することはできません。 ただし、引き続き請求や配達が可能です。"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_packaging__sales
msgid "If true, the packaging can be used for sales orders"
msgstr "Trueの場合、梱包を販売オーダに使用できます"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__pricelist_id
msgid "If you change the pricelist, only newly added lines will be affected."
msgstr "価格表を変更すると、新しく追加された行のみが影響を受けます。"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Import Amazon orders and sync deliveries"
msgstr "Amazonオーダインポートおよび運送同期"

#. module: sale
#: code:addons/sale/models/product_template.py:0
#, python-format
msgid "Import Template for Products"
msgstr "プロダクトテンプレートをインポート"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Incl. tax)"
msgstr "税込)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__deposit_account_id
msgid "Income Account"
msgstr "収益勘定"

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Invalid order."
msgstr "順序が無効です。"

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Invalid signature data."
msgstr "無効な署名データ。"

#. module: sale
#: code:addons/sale/models/account_move.py:0
#, python-format
msgid "Invoice %s paid"
msgstr "請求書 %s 支払済"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_invoice_id
msgid "Invoice Address"
msgstr "請求先"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__display_invoice_alert
msgid "Invoice Alert"
msgstr "請求書アラート"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_salesteam_invoice_confirmed
msgid "Invoice Confirmed"
msgstr "請求書確認済"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__invoice_count
msgid "Invoice Count"
msgstr "請求書数"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_salesteam_invoice_created
msgid "Invoice Created"
msgstr "請求書作成済"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__invoice_mail_template_id
msgid "Invoice Email Template"
msgstr "請求書Eメールテンプレート"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__invoice_lines
msgid "Invoice Lines"
msgstr "請求書行"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Invoice Sales Order"
msgstr "販売オーダから請求作成"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__invoice_status
#: model:ir.model.fields,field_description:sale.field_sale_order_line__invoice_status
msgid "Invoice Status"
msgstr "請求書ステータス"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_template_sale_form_view
msgid "Invoice after delivery, based on quantities delivered, not ordered."
msgstr "オーダ数量ではなく配送済数量を配送後に請求。"

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team__invoiced
msgid ""
"Invoice revenue for the current month. This is the amount the sales channel "
"has invoiced this month. It is used to compute the progression ratio of the "
"current and target revenue on the kanban view."
msgstr ""
"今月の請求書収入。これは、販売チャネルが今月請求した金額です。これは、カンバンビューでの現在の収益と目標収益の進行率を計算するために使用されます。"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_config_settings__default_invoice_policy__delivery
msgid "Invoice what is delivered"
msgstr "配送済数量を請求"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_config_settings__default_invoice_policy__order
msgid "Invoice what is ordered"
msgstr "オーダ数量を請求"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoiced"
msgstr "請求済"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_invoiced
msgid "Invoiced Quantity"
msgstr "請求済数量"

#. module: sale
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid "Invoiced Quantity: %s"
msgstr "請求数量:%s"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__invoiced
msgid "Invoiced This Month"
msgstr "今月請求済"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_invoice_salesteams
#: model:ir.model.fields,field_description:sale.field_sale_order__invoice_ids
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoices"
msgstr "請求書"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_account_invoice_report_salesteam
msgid "Invoices Analysis"
msgstr "請求書分析"

#. module: sale
#: model:ir.model,name:sale.model_account_invoice_report
msgid "Invoices Statistics"
msgstr "請求書の統計情報"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid ""
"Invoices will be created in draft so that you can review\n"
"                        them before validation."
msgstr "請求書はドラフトで作成されるので、検証前に確認することができます。"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoicing"
msgstr "請求"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Invoicing Address:"
msgstr "請求先:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__invoice_policy
#: model:ir.model.fields,field_description:sale.field_product_template__invoice_policy
#: model:ir.model.fields,field_description:sale.field_res_config_settings__default_invoice_policy
msgid "Invoicing Policy"
msgstr "請求方針"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__invoiced_target
msgid "Invoicing Target"
msgstr "請求ターゲット"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Invoicing and Shipping Address:"
msgstr "請求・出荷先:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_is_follower
msgid "Is Follower"
msgstr "フォロー中　"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__is_downpayment
msgid "Is a down payment"
msgstr "前受金ですか"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__is_expense
msgid "Is expense"
msgstr "費用です"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__is_expired
msgid "Is expired"
msgstr "有効期限が切れています"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__is_expense
msgid ""
"Is true if the sales order line comes from an expense or a vendor bills"
msgstr "受注明細が経費またはベンダーの請求書からのものである場合はtrue"

#. module: sale
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid ""
"It is forbidden to modify the following fields in a locked order:\n"
"%s"
msgstr "次のフィールドをロックされた順序で変更することは禁止されています:%s"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "It is not allowed to confirm an order in the following states: %s"
msgstr "次の状態では注文を確認できません:%s"

#. module: sale
#: model:ir.model,name:sale.model_account_move
msgid "Journal Entry"
msgstr "仕訳"

#. module: sale
#: model:ir.model,name:sale.model_account_move_line
msgid "Journal Item"
msgstr "仕訳明細"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_order_confirmation_state__just_done
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_sample_quotation_state__just_done
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_quotation_onboarding_state__just_done
msgid "Just done"
msgstr "完了"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales____last_update
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv____last_update
#: model:ir.model.fields,field_description:sale.field_sale_order____last_update
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel____last_update
#: model:ir.model.fields,field_description:sale.field_sale_order_line____last_update
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard____last_update
#: model:ir.model.fields,field_description:sale.field_sale_report____last_update
msgid "Last Modified on"
msgstr "最終更新日"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_line__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order_line__write_date
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Late Activities"
msgstr "遅れた活動"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__customer_lead
msgid "Lead Time"
msgstr "リードタイム"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Let your customers log in to see their documents"
msgstr "顧客がログインして自分のドキュメントを参照できるようにする"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Let's send the quote."
msgstr "見積もりを送ってみましょう。"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Lets keep electronic signature for now."
msgstr "今のところ電子署名を保持しましょう。"

#. module: sale
#: model_terms:product.product,website_description:sale.product_product_4e
#: model_terms:product.product,website_description:sale.product_product_4f
msgid "Locally handmade"
msgstr "地元産ハンドメイド"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_auto_done_setting
msgid "Lock"
msgstr "ロック"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_auto_done_setting
#: model:res.groups,name:sale.group_auto_done_setting
msgid "Lock Confirmed Sales"
msgstr "確認済の販売をロック"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__done
msgid "Locked"
msgstr "ロック済"

#. module: sale
#: model_terms:product.product,website_description:sale.product_product_4e
#: model_terms:product.product,website_description:sale.product_product_4f
msgid ""
"Looking for a custom bamboo stain to match existing furniture? Contact us "
"for a quote."
msgstr "既存の家具に合う特注の竹用塗料をお探しですか？お見積もりはこちらから。"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Looks good. Let's continue."
msgstr "良さげですね。次に進みましょう。"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.onboarding_quotation_layout_step
msgid "Looks great!"
msgstr "素晴らしい!"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_main_attachment_id
msgid "Main Attachment"
msgstr "主な添付"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Manage promotion &amp; coupon programs"
msgstr "プロモーションおよびクーポンプログラムを管理"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__qty_delivered_method__manual
msgid "Manual"
msgstr "手動"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__manual
msgid "Manual Payment"
msgstr "手動支払"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__service_type__manual
msgid "Manually set quantities on order"
msgstr "オーダに数量を手動で設定"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__service_type
#: model:ir.model.fields,help:sale.field_product_template__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""
"注文時に手動で数量を設定:分析アカウントを作成せずに、手動で入力した数量に基づいて請求します。\n"
"契約上のタイムシート:関連するタイムシートで追跡された時間に基づく請求書。\n"
"タスクを作成して時間を追跡する:販売注文の検証でタスクを作成し、労働時間を追跡します。"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_margin
msgid "Margins"
msgstr "マージン"

#. module: sale
#: model:ir.actions.server,name:sale.model_sale_order_action_quotation_sent
msgid "Mark Quotation as Sent"
msgstr "見積もりを送信済みとしてマーク"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.account_invoice_form
msgid "Marketing"
msgstr "マーケティング"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_move__medium_id
#: model:ir.model.fields,field_description:sale.field_sale_order__medium_id
#: model:ir.model.fields,field_description:sale.field_sale_report__medium_id
msgid "Medium"
msgstr "媒体"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__paypal_seller_account
msgid "Merchant Account ID"
msgstr "販売者アカウントID"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_has_error
msgid "Message Delivery error"
msgstr "メッセージ配信エラー"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_warn_msg
#: model:ir.model.fields,field_description:sale.field_res_users__sale_warn_msg
msgid "Message for Sales Order"
msgstr "販売オーダのメッセージ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__sale_line_warn_msg
#: model:ir.model.fields,field_description:sale.field_product_template__sale_line_warn_msg
msgid "Message for Sales Order Line"
msgstr "販売オーダ明細のメッセージ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_ids
msgid "Messages"
msgstr "メッセージ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__manual_name
msgid "Method"
msgstr "償却方法"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "納品数量を更新する方法"

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_sale_order_line_accountable_required_fields
msgid "Missing required fields on accountable sale order line."
msgstr "説明責任のある販売注文明細に必須フィールドがありません。"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "活動の締切"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "My Orders"
msgstr "自分のオーダ"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
msgid "My Quotations"
msgstr "自分の見積"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "My Sales Order Lines"
msgstr "自分の販売オーダ明細"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__signed_by
msgid "Name of the person that signed the SO."
msgstr "販売オーダの署名者の名前"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "New"
msgstr "新規"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_quotation_form
msgid "New Quotation"
msgstr "新規見積"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "次の活動カレンダーイベント"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "次の活動期限"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_summary
msgid "Next Activity Summary"
msgstr "次の活動サマリ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_type_id
msgid "Next Activity Type"
msgstr "次の活動タイプ"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__expense_policy__no
msgid "No"
msgstr "いいえ"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__sale_line_warn__no-message
#: model:ir.model.fields.selection,name:sale.selection__res_partner__sale_warn__no-message
msgid "No Message"
msgstr "メッセージなし"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "No longer edit orders once confirmed"
msgstr "確認済オーダは編集しない"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_to_invoice
msgid "No orders to invoice found"
msgstr "請求対象オーダがありません"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid "No orders to upsell found."
msgstr "アップセルの注文は見つかりませんでした。"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/variant_mixin.js:0
#, python-format
msgid "Not available with %s"
msgstr "%sでは使用できません"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_order_confirmation_state__not_done
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_sample_quotation_state__not_done
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_quotation_onboarding_state__not_done
msgid "Not done"
msgstr "未完了"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__display_type__line_note
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Note"
msgstr "ノート"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__no
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__no
msgid "Nothing to Invoice"
msgstr "請求対象なし"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Now, we'll create a sample quote."
msgstr "次に、サンプル見積もりを作成します。"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Number"
msgstr "番号"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_needaction_counter
msgid "Number of Actions"
msgstr "アクションの数"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__customer_lead
msgid ""
"Number of days between the order confirmation and the shipping of the "
"products to the customer"
msgstr "注文確認と顧客への製品の出荷までの日数"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_has_error_counter
msgid "Number of errors"
msgstr "エラー数"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "アクションを必要とするメッセージの数"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "配信エラーのメッセージ数"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__quotations_count
msgid "Number of quotations to invoice"
msgstr "請求対象見積数"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__sales_to_invoice_count
msgid "Number of sales to invoice"
msgstr "請求対象販売数"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_unread_counter
msgid "Number of unread messages"
msgstr "未読メッセージ件数"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_config_settings__auth_signup_uninvited__b2b
msgid "On invitation"
msgstr "招待制"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.act_res_partner_2_sale_order
#: model_terms:ir.actions.act_window,help:sale.action_orders_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations_with_onboarding
msgid ""
"Once the quotation is confirmed by the customer, it becomes a sales "
"order.<br> You will be able to create an invoice and collect the payment."
msgstr "見積を顧客が承認すると、販売オーダになります。<br>そこから請求書を作成し、支払を受けることができます。"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders
#: model_terms:ir.actions.act_window,help:sale.action_sale_order_form_view
msgid ""
"Once the quotation is confirmed, it becomes a sales order.<br> You will be "
"able to create an invoice and collect the payment."
msgstr "見積が確定すると、受注となります。<br>請求書を作成し、支払いを回収することができます。"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__portal_confirmation_pay
#: model:ir.model.fields,field_description:sale.field_res_config_settings__portal_confirmation_pay
#: model:ir.model.fields,field_description:sale.field_sale_order__require_payment
msgid "Online Payment"
msgstr "オンライン支払"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__portal_confirmation_sign
#: model:ir.model.fields,field_description:sale.field_res_config_settings__portal_confirmation_sign
#: model:ir.model.fields,field_description:sale.field_sale_order__require_signature
msgid "Online Signature"
msgstr "オンライン署名"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Only draft orders can be marked as sent directly."
msgstr "直接送信済みとしてマークできるのはドラフト注文のみです。"

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_product_attribute_custom_value_sol_custom_value_unique
msgid ""
"Only one Custom Value is allowed per Attribute Value per Sales Order Line."
msgstr "受注明細ごとの属性値ごとに許可されるカスタム値は1つだけです。"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Open Sales app to send your first quotation in a few clicks."
msgstr "セールスアプリを開いて、数回クリックするだけで最初の見積もりを送信します。"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Order"
msgstr "オーダ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__order_id
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Order #"
msgstr "オーダ番号"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_order_confirmation_step
msgid "Order Confirmation"
msgstr "オーダ確認"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__count
msgid "Order Count"
msgstr "オーダ数"

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#: code:addons/sale/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale.field_sale_order__date_order
#: model:ir.model.fields,field_description:sale.field_sale_report__date
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#, python-format
msgid "Order Date"
msgstr "オーダ日"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__order_line
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Order Lines"
msgstr "オーダ明細"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__name
#: model:ir.model.fields,field_description:sale.field_sale_order__name
#: model:ir.model.fields,field_description:sale.field_sale_order_line__order_id
#: model:ir.model.fields,field_description:sale.field_sale_report__name
msgid "Order Reference"
msgstr "オーダ参照"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__state
msgid "Order Status"
msgstr "オーダステータス"

#. module: sale
#: model:mail.activity.type,name:sale.mail_act_sale_upsell
msgid "Order Upsell"
msgstr "オーダアップセル"

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Order signed by %s"
msgstr "%sにより署名されたオーダ"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
msgid "Order to Invoice"
msgstr "請求対象オーダ"

#. module: sale
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid "Ordered Quantity: %(old_qty)s -> %(new_qty)s"
msgstr "注文数量 %(old_qty)s -> %(new_qty)s"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__invoice_policy
#: model:ir.model.fields,help:sale.field_product_template__invoice_policy
msgid ""
"Ordered Quantity: Invoice quantities ordered by the customer.\n"
"Delivered Quantity: Invoice quantities delivered to the customer."
msgstr "注文数量:顧客が注文した請求書の数量。納品数量:顧客に納品された請求書の数量。"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__invoice_policy__order
msgid "Ordered quantities"
msgstr "オーダ数量"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_order
#: model:ir.ui.menu,name:sale.sale_order_menu
msgid "Orders"
msgstr "オーダ"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders_to_invoice
#: model:ir.ui.menu,name:sale.menu_sale_order_invoice
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
msgid "Orders to Invoice"
msgstr "請求対象オーダ"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders_upselling
#: model:ir.ui.menu,name:sale.menu_sale_order_upselling
msgid "Orders to Upsell"
msgstr "アップセル対象オーダ"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Other Info"
msgstr "その他情報"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_acquirer_onboarding_wizard__payment_method__other
msgid "Other payment acquirer"
msgstr "他の決済サービス"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__paypal_pdt_token
msgid "PDT Identity Token"
msgstr "PDTIDトークン"

#. module: sale
#: model:ir.actions.report,name:sale.action_report_pro_forma_invoice
msgid "PRO-FORMA Invoice"
msgstr "見積送状"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_packaging_id
msgid "Packaging"
msgstr "梱包"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_packaging_qty
msgid "Packaging Quantity"
msgstr "梱包数量"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__partner_id
msgid "Partner"
msgstr "取引先"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__country_id
msgid "Partner Country"
msgstr "取引国"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Pay Now"
msgstr "今支払う"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Pay with"
msgstr "お支払"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__other
msgid "Pay with another payment acquirer"
msgstr "他の決済サービスでの支払"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__paypal
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_acquirer_onboarding_wizard__payment_method__paypal
msgid "PayPal"
msgstr "PayPal"

#. module: sale
#: model:ir.model,name:sale.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "決済サービス"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Payment Acquirers"
msgstr "決済サービス"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__manual_post_msg
msgid "Payment Instructions"
msgstr "お支払い方法"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__payment_method
msgid "Payment Method"
msgstr "支払方法"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__reference
msgid "Payment Ref."
msgstr "支払参照"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__payment_term_id
msgid "Payment Terms"
msgstr "支払条件"

#. module: sale
#: model:ir.model,name:sale.model_payment_transaction
msgid "Payment Transaction"
msgstr "決済トランザクション"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Payment terms"
msgstr "支払条件"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__paypal_user_type
msgid "Paypal User Type"
msgstr "PayPalユーザタイプ"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Please define an accounting sales journal for the company %s (%s)."
msgstr "会社%s(%s)の会計販売仕訳帳を定義してください。"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/sale.js:0
#, python-format
msgid "Please enter an integer value"
msgstr "整数値を入力してください"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__access_url
msgid "Portal Access URL"
msgstr "ポータルアクセスURL"

#. module: sale
#: model_terms:product.product,website_description:sale.product_product_4e
#: model_terms:product.product,website_description:sale.product_product_4f
msgid ""
"Press a button and watch your desk glide effortlessly from sitting to "
"standing height in seconds."
msgstr "ボタンを押すだけで、座位から立位まで数秒で楽に調整することができます。"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_reduce
msgid "Price Reduce"
msgstr "価格低下"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_reduce_taxexcl
msgid "Price Reduce Tax excl"
msgstr "税抜価格"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_reduce_taxinc
msgid "Price Reduce Tax inc"
msgstr "税込価格"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__price_subtotal
msgid "Price Subtotal"
msgstr "価格小計"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__pricelist_id
#: model:ir.model.fields,field_description:sale.field_sale_order__pricelist_id
#: model:ir.model.fields,field_description:sale.field_sale_report__pricelist_id
msgid "Pricelist"
msgstr "価格リスト"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_pricelist_main
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "価格リスト"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Pricing"
msgstr "価格設定"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Print"
msgstr "印刷"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_proforma_sales
msgid "Pro-Forma Invoice"
msgstr "見積送状"

#. module: sale
#: model:res.groups,name:sale.group_proforma_sales
msgid "Pro-forma Invoices"
msgstr "見積送状"

#. module: sale
#: model:ir.model,name:sale.model_product_product
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__product_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_id
#: model:ir.model.fields,field_description:sale.field_sale_report__product_tmpl_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Product"
msgstr "プロダクト"

#. module: sale
#: model:ir.model,name:sale.model_product_attribute
msgid "Product Attribute"
msgstr "プロダクト属性"

#. module: sale
#: model:ir.model,name:sale.model_product_attribute_custom_value
msgid "Product Attribute Custom Value"
msgstr "製品属性カスタム値"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Product Catalog"
msgstr "プロダクトカタログ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__categ_id
#: model:ir.model.fields,field_description:sale.field_sale_report__categ_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Product Category"
msgstr "プロダクトカテゴリ"

#. module: sale
#: model:ir.model,name:sale.model_product_packaging
msgid "Product Packaging"
msgstr "製品包装"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__product_qty
msgid "Product Quantity"
msgstr "プロダクト数量"

#. module: sale
#: model:ir.model,name:sale.model_product_template
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__product_tmpl_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_template_id
msgid "Product Template"
msgstr "プロダクトテンプレート"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom_readonly
msgid "Product Uom Readonly"
msgstr "製品Uom読み取り専用"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__product_id
msgid "Product Variant"
msgstr "プロダクトバリアント"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product
#: model:ir.ui.menu,name:sale.menu_products
msgid "Product Variants"
msgstr "プロダクトバリアント"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Product prices have been recomputed according to pricelist <b>%s<b> "
msgstr "価格表<b>%s</b>に従って製品価格が再計算されました"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Product used for down payments"
msgstr "前受金用プロダクト"

#. module: sale
#: model:ir.actions.act_window,name:sale.product_template_action
#: model:ir.ui.menu,name:sale.menu_product_template_action
#: model:ir.ui.menu,name:sale.prod_config_main
#: model:ir.ui.menu,name:sale.product_menu_catalog
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Products"
msgstr "プロダクト"

#. module: sale
#: model:ir.model,name:sale.model_report_sale_report_saleproforma
msgid "Proforma Report"
msgstr "見積送状レポート"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Qty"
msgstr "数量"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_delivered
msgid "Qty Delivered"
msgstr "配送済数量"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_invoiced
msgid "Qty Invoiced"
msgstr "請求済数量"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__product_uom_qty
msgid "Qty Ordered"
msgstr "オーダ数量"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_to_deliver
msgid "Qty To Deliver"
msgstr "配送予定数量"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_to_invoice
msgid "Qty To Invoice"
msgstr "未請求数量"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Quantities to invoice from sales orders"
msgstr "販売オーダからの請求対象数量"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom_qty
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Quantity"
msgstr "数量"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__visible_qty_configurator
#: model:ir.model.fields,field_description:sale.field_product_template__visible_qty_configurator
msgid "Quantity visible in configurator"
msgstr "コンフィギュレータに表示される数量"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Quantity:"
msgstr "数量:"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__draft
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
#, python-format
msgid "Quotation"
msgstr "見積"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Quotation #"
msgstr "見積書 #"

#. module: sale
#: model:ir.actions.report,name:sale.action_report_saleorder
msgid "Quotation / Order"
msgstr "見積 / オーダ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_utm_campaign__quotation_count
msgid "Quotation Count"
msgstr "見積もり数"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Quotation Date"
msgstr "見積日"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.onboarding_quotation_layout_step
msgid "Quotation Layout"
msgstr "見積レイアウト"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__sent
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__sent
msgid "Quotation Sent"
msgstr "見積提出済"

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_res_company_check_quotation_validity_days
msgid "Quotation Validity is required and must be greater than 0."
msgstr "見積もりの 有効性が必要であり、0より大きくなければなりません。"

#. module: sale
#: model:mail.message.subtype,description:sale.mt_order_confirmed
msgid "Quotation confirmed"
msgstr "見積確認済"

#. module: sale
#: model:mail.message.subtype,description:sale.mt_order_sent
#: model:mail.message.subtype,name:sale.mt_order_sent
#: model:mail.message.subtype,name:sale.mt_salesteam_order_sent
msgid "Quotation sent"
msgstr "見積送信"

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Quotation viewed by customer %s"
msgstr "顧客%sが見積を参照しました。"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_quotations
#: model:ir.actions.act_window,name:sale.action_quotations_salesteams
#: model:ir.actions.act_window,name:sale.action_quotations_with_onboarding
#: model:ir.model.fields,field_description:sale.field_crm_team__use_quotations
#: model:ir.ui.menu,name:sale.menu_sale_quotations
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_menu_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Quotations"
msgstr "見積"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Quotations &amp; Orders"
msgstr "見積とオーダ"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_quotation_salesteam
msgid "Quotations Analysis"
msgstr "見積分析"

#. module: sale
#: model:ir.actions.act_window,name:sale.act_res_partner_2_sale_order
msgid "Quotations and Sales"
msgstr "見積・受注"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__expense_policy
#: model:ir.model.fields,field_description:sale.field_product_template__expense_policy
msgid "Re-Invoice Expenses"
msgstr "経費の請求"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__visible_expense_policy
#: model:ir.model.fields,field_description:sale.field_product_template__visible_expense_policy
msgid "Re-Invoice Policy visible"
msgstr "再請求書ポリシーが表示されます"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Recompute all prices based on this pricelist"
msgstr "この価格表に基づいてすべての価格を再計算します"

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Reference"
msgstr "参照"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__origin
msgid "Reference of the document that generated this sales order request."
msgstr "この販売オーダ要求を生成したドキュメントの参照。"

#. module: sale
#: model:ir.model,name:sale.model_account_payment_register
msgid "Register Payment"
msgstr "支払登録"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_advance_payment_inv__advance_payment_method__delivered
msgid "Regular invoice"
msgstr "通常の請求書"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Reject This Quotation"
msgstr "この見積もりを拒否する"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_report
msgid "Reporting"
msgstr "レポーティング"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__require_signature
msgid ""
"Request a online signature to the customer in order to confirm orders "
"automatically."
msgstr "注文を自動的に確認するために、顧客にオンライン署名を要求します。"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Request an online payment to confirm orders"
msgstr "オーダの確認にオンライン支払を要求"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__require_payment
msgid ""
"Request an online payment to the customer in order to confirm orders "
"automatically."
msgstr "注文を自動的に確認するために、顧客にオンライン支払いを要求します。"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Request an online signature to confirm orders"
msgstr "オーダの確認にオンライン署名を要求"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Requested date is too soon."
msgstr "リクエスト日が早すぎます。"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_user_id
msgid "Responsible User"
msgstr "担当者"

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team__invoiced_target
msgid ""
"Revenue target for the current month (untaxed total of confirmed invoices)."
msgstr "今月の収益目標(確認済みの請求書の非課税合計)。"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_kanban
msgid "Revenues"
msgstr "収益"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_utm_campaign__invoiced_amount
msgid "Revenues generated by the campaign"
msgstr "キャンペーンによって生み出された収益"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mail_notification_paynow_online
msgid "Review, Accept &amp; Pay Quotation"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mail_notification_paynow_online
msgid "Review, Accept &amp; Sign Quotation"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mail_notification_paynow_online
msgid "Review, Sign &amp; Pay Quotation"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS配信エラー"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__order_id
msgid "Sale Order"
msgstr "販売オーダ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_order_count
#: model:ir.model.fields,field_description:sale.field_res_users__sale_order_count
msgid "Sale Order Count"
msgstr "販売オーダ数"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_warning_sale
msgid "Sale Order Warnings"
msgstr "販売オーダ警告"

#. module: sale
#: model:ir.model,name:sale.model_sale_payment_acquirer_onboarding_wizard
msgid "Sale Payment acquire onboarding wizard"
msgstr "販売支払いはオンボーディングウィザードを取得します"

#. module: sale
#: model:utm.source,name:sale.utm_source_sale_order_0
msgid "Sale Promotion 1"
msgstr "販売促進1"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sale Warnings"
msgstr "販売警告"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_onboarding_payment_method
msgid "Sale onboarding selected payment method"
msgstr "選択した支払い方法のオンボーディング販売"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_packaging__sales
#: model:ir.ui.menu,name:sale.menu_report_product_all
#: model:ir.ui.menu,name:sale.sale_menu_root
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Sales"
msgstr "販売"

#. module: sale
#: model:ir.model,name:sale.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr "受注前払請求書"

#. module: sale
#: code:addons/sale/models/crm_team.py:0
#: model:ir.actions.act_window,name:sale.action_order_report_all
#: model:ir.actions.act_window,name:sale.action_order_report_so_salesteam
#: model:ir.actions.act_window,name:sale.report_all_channels_sales_action
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_graph
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_pivot
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#, python-format
msgid "Sales Analysis"
msgstr "販売分析"

#. module: sale
#: model:ir.model,name:sale.model_sale_report
msgid "Sales Analysis Report"
msgstr "販売分析レポート"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__done
msgid "Sales Done"
msgstr "販売完了"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#: model:ir.actions.act_window,name:sale.action_sale_order_form_view
#: model:ir.model,name:sale.model_sale_order
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_order_ids
#: model:ir.model.fields,field_description:sale.field_res_users__sale_order_ids
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__sale
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#, python-format
msgid "Sales Order"
msgstr "販売オーダ"

#. module: sale
#: model:ir.model,name:sale.model_sale_order_cancel
msgid "Sales Order Cancel"
msgstr "受注キャンセル"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_order_confirmed
#: model:mail.message.subtype,name:sale.mt_salesteam_order_confirmed
msgid "Sales Order Confirmed"
msgstr "確定販売オーダ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_analytic_line__so_line
#: model_terms:ir.ui.view,arch_db:sale.sale_order_line_view_form_readonly
msgid "Sales Order Item"
msgstr "販売オーダ項目"

#. module: sale
#: model:ir.model,name:sale.model_sale_order_line
#: model:ir.model.fields,field_description:sale.field_product_attribute_custom_value__sale_order_line_id
#: model:ir.model.fields,field_description:sale.field_product_product__sale_line_warn
#: model:ir.model.fields,field_description:sale.field_product_template__sale_line_warn
msgid "Sales Order Line"
msgstr "販売オーダ明細"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_move_line__sale_line_ids
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Sales Order Lines"
msgstr "販売オーダ明細"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Sales Order Lines ready to be invoiced"
msgstr "請求可能な販売オーダ明細"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Sales Order Lines related to a Sales Order of mine"
msgstr "自分の販売オーダの明細"

#. module: sale
#: code:addons/sale/models/payment_transaction.py:0
#: model_terms:ir.ui.view,arch_db:sale.transaction_form_inherit_sale
#, python-format
msgid "Sales Order(s)"
msgstr "販売オーダ"

#. module: sale
#: model:mail.template,name:sale.mail_template_sale_confirmation
msgid "Sales Order: Confirmation Email"
msgstr "販売オーダ：確認メール"

#. module: sale
#: model:mail.template,name:sale.email_template_edi_sale
msgid "Sales Order: Send by email"
msgstr "販売オーダ：メールによる送信"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders
#: model:ir.actions.act_window,name:sale.action_orders_salesteams
#: model:ir.actions.act_window,name:sale.action_orders_to_invoice_salesteams
#: model:ir.model.fields,field_description:sale.field_payment_transaction__sale_order_ids
#: model:ir.ui.menu,name:sale.menu_sales_config
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_menu_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_activity
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_calendar
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_graph
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_pivot
msgid "Sales Orders"
msgstr "販売オーダ"

#. module: sale
#: model:ir.model,name:sale.model_crm_team
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__team_id
#: model:ir.model.fields,field_description:sale.field_account_invoice_report__team_id
#: model:ir.model.fields,field_description:sale.field_account_move__team_id
#: model:ir.model.fields,field_description:sale.field_account_payment__team_id
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__team_id
#: model:ir.model.fields,field_description:sale.field_sale_order__team_id
#: model:ir.model.fields,field_description:sale.field_sale_report__team_id
#: model_terms:ir.ui.view,arch_db:sale.account_invoice_groupby_inherit
#: model_terms:ir.ui.view,arch_db:sale.view_account_invoice_report_search_inherit
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Sales Team"
msgstr "販売チーム"

#. module: sale
#: model:ir.ui.menu,name:sale.report_sales_team
#: model:ir.ui.menu,name:sale.sales_team_config
msgid "Sales Teams"
msgstr "販売チーム"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_warn
#: model:ir.model.fields,field_description:sale.field_res_users__sale_warn
msgid "Sales Warnings"
msgstr "販売警告"

#. module: sale
#: model:ir.model,name:sale.model_report_all_channels_sales
msgid "Sales by Channel (All in One)"
msgstr "チャネル別売上高(オールインワン)"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__expense_policy__sales_price
msgid "Sales price"
msgstr "販売価格"

#. module: sale
#: code:addons/sale/models/crm_team.py:0
#, python-format
msgid "Sales: Untaxed Total"
msgstr "売上高:非課税合計"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__user_id
#: model:ir.model.fields,field_description:sale.field_sale_order__user_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__salesman_id
#: model:ir.model.fields,field_description:sale.field_sale_report__user_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Salesperson"
msgstr "販売担当者"

#. module: sale
#: code:addons/sale/models/res_company.py:0
#, python-format
msgid "Sample Order Line"
msgstr "サンプルオーダ明細"

#. module: sale
#: code:addons/sale/models/res_company.py:0
#, python-format
msgid "Sample Product"
msgstr "サンプルプロダクト"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_sample_quotation_step
msgid "Sample Quotation"
msgstr "見積サンプル"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Search Sales Order"
msgstr "販売オーダ検索"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__display_type__line_section
msgid "Section"
msgstr "セクション"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Section Name (eg. Products, Services)"
msgstr "セクション名 (例 商品、サービス)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__access_token
msgid "Security Token"
msgstr "セキュリティトークン"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Select a product, or create a new one on the fly."
msgstr "製品を選択するか、その場で新しい製品を作成します。"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Select product attributes and optional products from the sales order"
msgstr "受注からプロダクト属性とオプションプロダクトを選択"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Select specific invoice and delivery addresses"
msgstr "請求先および出荷先アドレスを指定"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__sale_line_warn
#: model:ir.model.fields,help:sale.field_product_template__sale_line_warn
#: model:ir.model.fields,help:sale.field_res_partner__sale_warn
#: model:ir.model.fields,help:sale.field_res_users__sale_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"警告オプションを選択するとユーザにメッセージを通知します。メッセージをブロックを選択するとメッセージとともに例外が発生しその流れがブロックされます。メッセージは次の項目で記述する必要があります。"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sell and purchase products in different units of measure"
msgstr "複数の異なる単位での販売/購買"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sell products by multiple of unit # per package"
msgstr "荷姿あたりのユニット数の倍数でプロダクトを販売"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sell variants of a product using attributes (size, color, etc.)"
msgstr "属性（サイズ、色など）を使用してプロダクトのバリエーションを販売"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Send PRO-FORMA Invoice"
msgstr "見積送状を送付"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Send a product-specific email once the invoice is validated"
msgstr "請求書検証の際にプロダクトに関するEメールを送信"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_sample_quotation_step
msgid "Send a quotation to test the customer portal."
msgstr "見積書を送付して顧客ポータルを試す"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Send by Email"
msgstr "Eメールで送信"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_sample_quotation_step
msgid "Send sample"
msgstr "サンプルを送付"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Sending an email is useful if you need to share specific information or "
"content about a product (instructions, rules, links, media, etc.). Create "
"and set the email template from the product detail form (in Sales tab)."
msgstr ""
"プロダクトに関する特定の情報や内容(取扱説明、規則、リンク、メディア等)をシェアしたい場合、メール配信が役立ちます。プロダクト詳細フォーム(販売タブ)から、Eメールテンプレートを作成してください。"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__sequence
msgid "Sequence"
msgstr "付番"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Set a default validity on your quotations"
msgstr "見積にデフォルトの有効期間を設定"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/sale.js:0
#, python-format
msgid "Set an invoicing target: "
msgstr "請求対象の設定:"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Set multiple prices per product, automated discounts, etc."
msgstr "プロダクトに複数の価格や自動値引等を設定"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_order_confirmation_step
msgid "Set payments"
msgstr "支払方法を設定"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Set to Quotation"
msgstr "見積に設定"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_sale_config_settings
#: model:ir.ui.menu,name:sale.menu_sale_general_settings
msgid "Settings"
msgstr "管理設定"

#. module: sale
#: model:ir.actions.server,name:sale.model_sale_order_action_share
msgid "Share"
msgstr "共有"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Shipping"
msgstr "配送"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Show all records which has next action date is before today"
msgstr "次のアクションの日付が今日より前のすべてのレコードを表示する"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Show margins on orders"
msgstr "オーダにマージンを表示"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Sign &amp; Pay"
msgstr "署名して支払"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__digital_signature
msgid "Sign online"
msgstr "オンラインで署名"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__signature
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Signature"
msgstr "署名"

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Signature is missing."
msgstr "署名がありません。"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__signature
msgid "Signature received through the portal."
msgstr "ポータル経由で署名されました。"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__signed_by
msgid "Signed By"
msgstr "署名者"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__signed_on
msgid "Signed On"
msgstr "署名日"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__sales_count
#: model:ir.model.fields,field_description:sale.field_product_template__sales_count
msgid "Sold"
msgstr "販売額"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
msgid "Sold in the last 365 days"
msgstr "過去365日で販売"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_move__source_id
#: model:ir.model.fields,field_description:sale.field_sale_order__source_id
#: model:ir.model.fields,field_description:sale.field_sale_report__source_id
msgid "Source"
msgstr "情報源"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__origin
msgid "Source Document"
msgstr "参照元"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_product_email_template
msgid "Specific Email"
msgstr "プロダクト別Eメール"

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Stage"
msgstr "ステージ"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Start by checking your company's data."
msgstr "会社のデータをチェックすることからはじめします。"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_onboarding_order_confirmation_state
msgid "State of the onboarding confirmation order step"
msgstr "オンボーディング確認注文ステップの状態"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_onboarding_sample_quotation_state
msgid "State of the onboarding sample quotation step"
msgstr "オンボーディングサンプル見積もりステップの状態"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_quotation_onboarding_state
msgid "State of the sale onboarding panel"
msgstr "販売のオンボーディングパネルの状態"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__state
#: model:ir.model.fields,field_description:sale.field_sale_report__state
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Status"
msgstr "ステータス"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"活動に基づく状態\n"
"延滞：期限は既に過ぎました\n"
"当日：活動日は本日です\n"
"予定：将来の活動。"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__stripe
msgid "Stripe"
msgstr "Stripe"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__stripe_publishable_key
msgid "Stripe Publishable Key"
msgstr "ストライプ公開APIキー"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__stripe_secret_key
msgid "Stripe Secret Key"
msgstr "ストライプシークレットキー"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_subtotal
msgid "Subtotal"
msgstr "小計"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
msgid "Sum of Total"
msgstr "合計"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
msgid "Sum of Untaxed Total"
msgstr "税抜合計"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__tag_ids
#: model:ir.ui.menu,name:sale.menu_tag_config
msgid "Tags"
msgstr "タグ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__tax_country_id
msgid "Tax Country"
msgstr "税金の国"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Tax Total"
msgstr "税額合計"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__tax_totals_json
msgid "Tax Totals Json"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_tax
#: model:ir.model.fields,field_description:sale.field_sale_order_line__tax_id
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Taxes"
msgstr "税"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__deposit_taxes_id
msgid "Taxes used for deposits"
msgstr "預金に使用される税金"

#. module: sale
#: code:addons/sale/models/crm_team.py:0
#, python-format
msgid ""
"Team %(team_name)s has %(sale_order_count)s active sale orders. Consider "
"canceling them or archiving the team instead."
msgstr ""
"チーム%(team_name)s には "
"%(sale_order_count)s有効な販売オーダがあります。キャンセルまたはチームをアーカイブすることを検討して下さい。"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__show_update_pricelist
msgid ""
"Technical Field, True if the pricelist was changed;\n"
" this will then display a recomputation button"
msgstr "技術分野、価格表が変更された場合はTrue。これにより、再計算ボタンが表示されます"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__display_type
msgid "Technical field for UX purpose."
msgstr "UXの目的のためのテクニカルフィールド。"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__tax_country_id
msgid ""
"Technical field to filter the available taxes depending on the fiscal "
"country and fiscal position."
msgstr "税法上の居住地国及び財政状態によって使用可能な税を絞り込む用の技術フィールド。"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid ""
"Tell us why you are refusing this quotation, this will help us improve our "
"services."
msgstr "なぜこの見積を拒否しているのかを教えてください。これは私たちのサービスを改善するのに役立ちます。"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__terms_type
msgid "Terms & Conditions format"
msgstr "取引条件フォーマット"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Terms & Conditions: %s"
msgstr "諸条件: %s"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Terms &amp; Conditions"
msgstr "諸条件"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Terms &amp; Conditions:"
msgstr "諸条件"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__note
msgid "Terms and conditions"
msgstr "諸条件"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Terms and conditions..."
msgstr "諸条件..."

#. module: sale
#: code:addons/sale/models/account_move_line.py:0
#, python-format
msgid ""
"The Sales Order %s linked to the Analytic Account %s is cancelled. You "
"cannot register an expense on a cancelled Sales Order."
msgstr "販売注文%s(分析アカウント%sに紐づく)はキャンセルされます。キャンセルされた販売注文に経費を登録することはできません。"

#. module: sale
#: code:addons/sale/models/account_move_line.py:0
#, python-format
msgid ""
"The Sales Order %s linked to the Analytic Account %s is currently locked. "
"You cannot register an expense on a locked Sales Order. Please create a new "
"SO linked to this Analytic Account."
msgstr ""
"販売注文%s(分析アカウント%sに紐づく)は現在ロックされています。ロックされた販売注文に経費を登録することはできません。この分析アカウントにリンクされた新しいSOを作成してください。"

#. module: sale
#: code:addons/sale/models/account_move_line.py:0
#, python-format
msgid ""
"The Sales Order %s linked to the Analytic Account %s must be validated "
"before registering expenses."
msgstr "販売注文%s(分析アカウント%sに紐づく)は、経費を登録する前に検証する必要があります。"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__analytic_account_id
msgid "The analytic account related to a sales order."
msgstr "販売オーダに紐づく分析勘定"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid ""
"The delivery date is sooner than the expected date.You may be unable to "
"honor the delivery date."
msgstr "納期が予定日より早いため、納期を守れない場合があります。"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__fixed_amount
msgid "The fixed amount to be invoiced in advance, taxes excluded."
msgstr "事前に請求される定額(税抜き)。"

#. module: sale
#: code:addons/sale/models/product_template.py:0
#, python-format
msgid ""
"The following products cannot be restricted to the company %s because they have already been used in quotations or sales orders in another company:\n"
"%s\n"
"You can archive these products and recreate them with your company restriction instead, or leave them as shared product."
msgstr ""
"次の製品は、別の会社の見積または受注ですでに使用されているため、会社%sに制限することはできません。\n"
"%s\n"
"これらの製品をアーカイブして、代わりに会社の制限付きで再作成するか、共有製品のままにすることができます。"

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__automatic_invoice
msgid ""
"The invoice is generated automatically and available in the customer portal when the transaction is confirmed by the payment acquirer.\n"
"The invoice is marked as paid and the payment is registered in the payment journal defined in the configuration of the payment acquirer.\n"
"This mode is advised if you issue the final invoice at the order and not after the delivery."
msgstr ""
"請求書は自動的に生成され、支払い取得者がトランザクションを確認すると、カスタマーポータルで利用できます。\n"
"請求書は支払済みとしてマークされ、支払は支払アクワイアラーの構成で定義された支払仕訳帳に登録されます。\n"
"このモードは、納品後ではなく注文時に最終請求書を発行する場合に推奨されます。"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"The margin is computed as the sum of product sales prices minus the cost set"
" in their detail form."
msgstr "マージンは、製品の販売価格の合計から詳細形式で設定されたコストを差し引いたものとして計算されます。"

#. module: sale
#: model_terms:product.product,website_description:sale.product_product_4e
#: model_terms:product.product,website_description:sale.product_product_4f
msgid ""
"The minimum height is 65 cm, and for standing work the maximum height "
"position is 125 cm."
msgstr "最低位は65cmで、立位での作業は125cmまで。"

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "The order is not in a state requiring customer signature."
msgstr "このオーダは顧客署名が必要なステータスではありません。"

#. module: sale
#: code:addons/sale/models/payment_transaction.py:0
#, python-format
msgid ""
"The order was not confirmed despite response from the acquirer (%s): order "
"total is %r but acquirer replied with %r."
msgstr "取得者(%s)からの応答にもかかわらず、注文は確定されませんでした。注文の合計は%rですが、取得者は%rで応答しました。"

#. module: sale
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid "The ordered quantity has been updated."
msgstr "注文数量が更新されました。"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__reference
msgid "The payment communication of this sale order."
msgstr "この販売注文の支払い通信。"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__amount
msgid "The percentage of amount to be invoiced in advance, taxes excluded."
msgstr "事前に請求される金額の割合。税金は含まれていません。"

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid ""
"The product used to invoice a down payment should be of type 'Service'. "
"Please use another product or update this product."
msgstr "前受金を請求するために使用される製品は、タイプが 'サービス'である必要があります。 他の製品を使用するか、この製品を更新してください。"

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid ""
"The product used to invoice a down payment should have an invoice policy set"
" to \"Ordered quantities\". Please update your deposit product to be able to"
" create a deposit invoice."
msgstr ""
"前受金を請求するために使用される製品では、請求書ポリシーが「注文数量」に設定されている必要があります。 "
"預金明細書を作成できるように預金商品を更新してください。"

#. module: sale
#: code:addons/sale/controllers/portal.py:0
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "The provided parameters are invalid."
msgstr "指定されたパラメータが無効です。"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__currency_rate
msgid ""
"The rate of the currency to the currency of rate 1 applicable at the date of"
" the order"
msgstr "注文日に適用されるレート1の通貨に対する通貨のレート"

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid "The value of the down payment amount must be positive."
msgstr "前受金の金額は正の値でなければなりません。"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "There are currently no orders for your account."
msgstr "現在、あなたのアカウントの注文はありません。"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "There are currently no quotations for your account."
msgstr "現在、アカウントの見積もりはありません。"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid ""
"There is nothing to invoice!\n"
"\n"
"Reason(s) of this behavior could be:\n"
"- You should deliver your products before invoicing them.\n"
"- You should modify the invoicing policy of your product: Open the product, go to the \"Sales\" tab and modify invoicing policy from \"delivered quantities\" to \"ordered quantities\". For Services, you should modify the Service Invoicing Policy to 'Prepaid'."
msgstr ""
"請求するものは何もありません！\n"
"この動作の理由は次のとおりです。\n"
"-請求する前にプロダクトを納品する必要があります。\n"
"-プロダクトの請求ポリシーを変更する必要があります：プロダクトを開き、'販売'タブに移動して、請求ポリシーを'納品数量'から'オーダ数量'に変更します。"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"This default value is applied to any new product created. This can be "
"changed in the product detail form."
msgstr "このデフォルト値は、作成されたすべての新製品に適用されます。これは、製品詳細フォームで変更できます。"

#. module: sale
#: model:ir.model.fields,help:sale.field_account_move__campaign_id
#: model:ir.model.fields,help:sale.field_sale_order__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr "これは、Fall_Drive、Christmas_Specialなど、さまざまなキャンペーンの取り組みを追跡するのに役立つ名前です。"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__commitment_date
msgid ""
"This is the delivery date promised to the customer. If set, the delivery "
"order will be scheduled based on this date rather than product lead times."
msgstr ""
"これは、お客様に約束された納期です。設定されている場合、配送オーダーは、製品のリードタイムではなく、この日付に基づいてスケジュールされます。"

#. module: sale
#: model:ir.model.fields,help:sale.field_account_move__medium_id
#: model:ir.model.fields,help:sale.field_sale_order__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr "これは、はがき、電子メール、バナー広告などの配信方法です。"

#. module: sale
#: model:ir.model.fields,help:sale.field_account_move__source_id
#: model:ir.model.fields,help:sale.field_sale_order__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr "これはリンクのソースです。たとえば、検索エンジン、別のドメイン、またはメーリングリストの名前です。"

#. module: sale
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid ""
"This product is packaged by %(pack_size).2f %(pack_name)s. You should sell "
"%(quantity).2f %(unit)s."
msgstr ""
"このプロダクトは %(pack_size).2f %(pack_name)sで梱包されています。以下で販売する必要があります： "
"%(quantity).2f %(unit)s"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_all
msgid ""
"This report performs analysis on your quotations and sales orders. Analysis "
"check your sales revenues and sort it by different group criteria (salesman,"
" partner, product, etc.) Use this report to perform analysis on sales not "
"having invoiced yet. If you want to analyse your turnover, you should use "
"the Invoice Analysis report in the Accounting application."
msgstr ""
"このレポートは見積と受注上の分析を実行します。分析は受注高をチェックし別のグループ基準 (販売員、パートナ、製品など) "
"により並べ替えます。まだ請求されていない受注の分析を実行するためにこのレポートを使います。総売上高を分析したい場合は、会計アプリケーションの中の請求書分析レポートを使用する必要があります。"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_quotation_salesteam
msgid ""
"This report performs analysis on your quotations. Analysis check your sales "
"revenues and sort it by different group criteria (salesman, partner, "
"product, etc.) Use this report to perform analysis on sales not having "
"invoiced yet. If you want to analyse your turnover, you should use the "
"Invoice Analysis report in the Accounting application."
msgstr ""
"このレポートはあなたの見積の分析を行います。 "
"分析によって販売収益が確認され、さまざまなグループ基準(セールスマン、取引先、製品など)によってソートされます。このレポートを使用して、請求がまだ行われていない売上を分析します。"
" 売上高を分析する場合は、会計アプリケーションで請求書分析レポートを使用する必要があります。"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_so_salesteam
msgid ""
"This report performs analysis on your sales orders. Analysis check your "
"sales revenues and sort it by different group criteria (salesman, partner, "
"product, etc.) Use this report to perform analysis on sales not having "
"invoiced yet. If you want to analyse your turnover, you should use the "
"Invoice Analysis report in the Accounting application."
msgstr ""
"このレポートは、受注の分析を実行します。 "
"分析によって販売収益が確認され、さまざまなグループ基準(セールスマン、取引先、製品など)によってソートされます。このレポートを使用して、請求がまだ行われていない売上を分析します。"
" 売上高を分析する場合は、会計アプリケーションで請求書分析レポートを使用する必要があります。"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "This will update all unit prices based on the currently set pricelist."
msgstr "これにより、現在設定されている価格表に基づいてすべての単価が更新されます。"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__to_invoice
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__to_invoice
#: model:ir.ui.menu,name:sale.menu_sale_invoicing
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "To Invoice"
msgstr "請求対象"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_to_invoice
msgid "To Invoice Quantity"
msgstr "請求対象数量"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
msgid "To Upsell"
msgstr "アップセル対象"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"To send invitations in B2B mode, open a contact or select several ones in "
"list view and click on 'Portal Access Management' option in the dropdown "
"menu *Action*."
msgstr ""
"B2Bモードで招待状を送信するには、連絡先を開くか、リストビューで複数の連絡先を選択して、ドロップダウンメニューの'アクション'で[ポータルアクセス管理]オプションをクリックします。"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid ""
"To speed up order confirmation, we can activate electronic signatures or "
"payments."
msgstr "注文確認をスピードアップするために、電子署名または支払いを有効にすることができます。"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Today Activities"
msgstr "本日の活動"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__price_total
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_total
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_total
#: model:ir.model.fields,field_description:sale.field_sale_report__price_total
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Total"
msgstr "合計"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_tax
msgid "Total Tax"
msgstr "税合計"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Total Tax Excluded"
msgstr "除外された合計税"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Total Tax Included"
msgstr "税込合計"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__service_type
#: model:ir.model.fields,field_description:sale.field_product_template__service_type
msgid "Track Service"
msgstr "サービス追跡"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Tracking"
msgstr "追跡"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__transaction_ids
msgid "Transactions"
msgstr "取引明細書"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__type_name
msgid "Type Name"
msgstr "タイプ名"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "記録上の例外アクティビティのタイプ。"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_ups
msgid "UPS Connector"
msgstr "UPSコネクタ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_usps
msgid "USPS Connector"
msgstr "USPSコネクタ"

#. module: sale
#: model:ir.model,name:sale.model_utm_campaign
msgid "UTM Campaign"
msgstr "UTMキャンペーン"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_unit
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Unit Price"
msgstr "単価"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Unit Price:"
msgstr "単価:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom
#: model:ir.model.fields,field_description:sale.field_sale_report__product_uom
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Unit of Measure"
msgstr "単位"

#. module: sale
#: model:product.product,uom_name:sale.advance_product_0
#: model:product.product,uom_name:sale.product_product_4e
#: model:product.product,uom_name:sale.product_product_4f
#: model:product.template,uom_name:sale.advance_product_0_product_template
msgid "Units"
msgstr "個"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_uom_form_action
#: model:ir.ui.menu,name:sale.next_id_16
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Units of Measure"
msgstr "単位"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_uom_categ_form_action
msgid "Units of Measure Categories"
msgstr "単位カテゴリ"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_auto_done_setting
msgid "Unlock"
msgstr "ロック解除"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_unread
msgid "Unread Messages"
msgstr "未読メッセージ"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_unread_counter
msgid "Unread Messages Counter"
msgstr "未読メッセージカウンター"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_untaxed
msgid "Untaxed Amount"
msgstr "税抜金額"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__untaxed_amount_invoiced
msgid "Untaxed Amount Invoiced"
msgstr "請求済金額(税抜)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__untaxed_amount_to_invoice
#: model:ir.model.fields,field_description:sale.field_sale_report__untaxed_amount_to_invoice
msgid "Untaxed Amount To Invoice"
msgstr "未請求金額(税抜)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__untaxed_amount_invoiced
msgid "Untaxed Invoiced Amount"
msgstr "請求済金額(税抜)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__price_subtotal
msgid "Untaxed Total"
msgstr "税抜合計"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "UoM"
msgstr "単位"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Update Prices"
msgstr "価格の更新"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Upsell %(order)s for customer %(customer)s"
msgstr "アップセル %(order)s 顧客%(customer)sのため"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__upselling
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__upselling
msgid "Upselling Opportunity"
msgstr "アップセリングの機会"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "Valid Until"
msgstr "有効期日"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Validate Order"
msgstr "注文を検証"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Variant Grid Entry"
msgstr "バリアントグリッド入力"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mail_notification_paynow_online
msgid "View Quotation"
msgstr "見積もりを見る"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Void Transaction"
msgstr "無効な取引"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__volume
msgid "Volume"
msgstr "容積"

#. module: sale
#: code:addons/sale/models/sale_order_line.py:0
#: model:ir.model.fields.selection,name:sale.selection__product_template__sale_line_warn__warning
#: model:ir.model.fields.selection,name:sale.selection__res_partner__sale_warn__warning
#, python-format
msgid "Warning"
msgstr "警告"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid "Warning for %s"
msgstr "%s の警告です。"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
msgid "Warning on the Sales Order"
msgstr "販売オーダにて警告"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
msgid "Warning when Selling this Product"
msgstr "このプロダクトの販売時に警告"

#. module: sale
#: model_terms:product.product,website_description:sale.product_product_4e
#: model_terms:product.product,website_description:sale.product_product_4f
msgid ""
"We pay special attention to detail, which is why our desks are of a superior"
" quality."
msgstr "当社は細部までこだわることで、優れた品質のデスクを提供しています。"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__website_message_ids
msgid "Website Messages"
msgstr "ウェブサイトメッセージ"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__website_message_ids
msgid "Website communication history"
msgstr "ウェブサイトコミュニケーション履歴"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Write a company name to create one, or see suggestions."
msgstr "会社名を書いて作成するか、提案を参照してください。"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_template_sale_form_view
msgid "You can invoice them before they are delivered."
msgstr "配送前に請求できます。"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid ""
"You can not delete a sent quotation or a confirmed sales order. You must "
"first cancel it."
msgstr "送信された見積または確認済みの受注を削除することはできません。最初にキャンセルする必要があります。"

#. module: sale
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid ""
"You can not remove an order line once the sales order is confirmed.\n"
"You should rather set the quantity to 0."
msgstr ""
"販売オーダ確認後は明細を削除できません。\n"
"必要に応じて数量を0に変更してください。"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_to_invoice
msgid ""
"You can select all orders and invoice them in batch,<br>\n"
"                or check every order and invoice them one by one."
msgstr "複数のオーダを選択して、まとめて請求書を作成することができます。"

#. module: sale
#: model:ir.model.fields,help:sale.field_payment_acquirer__so_reference_type
msgid ""
"You can set here the communication type that will appear on sales orders.The"
" communication will be given to the customer when they choose the payment "
"method."
msgstr "ここで、受注に表示される通信タイプを設定できます。顧客が支払い方法を選択すると、通信が行われます。"

#. module: sale
#: code:addons/sale/models/product_product.py:0
#: code:addons/sale/models/product_template.py:0
#, python-format
msgid ""
"You cannot change the product's type because it is already used in sales "
"orders."
msgstr "商品の種類は、すでに販売注文で使用されているため、変更することができません。"

#. module: sale
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid ""
"You cannot change the type of a sale order line. Instead you should delete "
"the current line and create a new line of the proper type."
msgstr "受注明細のタイプを変更することはできません。代わりに、現在の行を削除して、適切なタイプの新しい行を作成する必要があります。"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.product_template_action
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                    whether it's a storable product, a consumable or a service."
msgstr "販売または購買するもの(在庫品、消耗品、サービス品等)は、プロダクトとして登録する必要があります。"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your feedback..."
msgstr "あなたのフィードバック..."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order has been confirmed."
msgstr "オーダが確認されました。"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order has been signed but still needs to be paid to be confirmed."
msgstr "ご注文は署名されていますが、確認のために支払う必要があります。"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order has been signed."
msgstr "ご注文は署名済みです。"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order is not in a state to be rejected."
msgstr "ご注文は拒否される状態ではありません。"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid ""
"Your quotation contains products from company %(product_company)s whereas your quotation belongs to company %(quote_company)s. \n"
" Please change the company of your quotation or remove the products from other companies (%(bad_products)s)."
msgstr ""
"見積もりには会社%(product_company)sの製品が含まれていますが、見積もりは会社%(quote_company)sに属しています。\n"
"見積もりの 会社を変更するか、他の会社から製品を削除してください(%(bad_products)s)。"

#. module: sale
#: model:ir.actions.server,name:sale.send_invoice_cron_ir_actions_server
#: model:ir.cron,cron_name:sale.send_invoice_cron
#: model:ir.cron,name:sale.send_invoice_cron
msgid "automatic invoicing: send ready invoice"
msgstr "自動請求：準備済の請求書を送る"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_bpost
msgid "bpost Connector"
msgstr "bpostコネクタ"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "close"
msgstr "閉じる"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "days"
msgstr "日"

#. module: sale
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "sale order"
msgstr "販売注文"

#. module: sale
#: model:mail.template,report_name:sale.email_template_edi_sale
#: model:mail.template,report_name:sale.mail_template_sale_confirmation
msgid "{{ (object.name or '').replace('/','_') }}"
msgstr ""

#. module: sale
#: model:mail.template,subject:sale.mail_template_sale_confirmation
msgid ""
"{{ object.company_id.name }} {{ (object.get_portal_last_transaction().state "
"== 'pending') and 'Pending Order' or 'Order' }} (Ref {{ object.name or 'n/a'"
" }})"
msgstr ""
"{{ object.company_id.name }} {{ (object.get_portal_last_transaction().state "
"== 'pending') and 'Pending Order' or 'Order' }} (Ref {{ object.name or 'n/a'"
" }})"

#. module: sale
#: model:mail.template,subject:sale.email_template_edi_sale
msgid ""
"{{ object.company_id.name }} {{ object.state in ('draft', 'sent') and "
"(ctx.get('proforma') and 'Proforma' or 'Quotation') or 'Order' }} (Ref {{ "
"object.name or 'n/a' }})"
msgstr ""
"{{ object.company_id.name }} {{ object.state in ('draft', 'sent') and "
"(ctx.get('proforma') and 'Proforma' or 'Quotation') or 'Order' }} (Ref {{ "
"object.name or 'n/a' }})"
