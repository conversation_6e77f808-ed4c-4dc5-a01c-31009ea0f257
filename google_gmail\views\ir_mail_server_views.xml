<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="ir_mail_server_view_form" model="ir.ui.view">
        <field name="name">ir.mail_server.view.form.inherit.gmail</field>
        <field name="model">ir.mail_server</field>
        <field name="inherit_id" ref="base.ir_mail_server_form"/>
        <field name="arch" type="xml">
            <field name="smtp_host" position="before">
                <field name="use_google_gmail_service" string="Gmail"/>
            </field>
            <field name="smtp_user" position="after">
                <field name="google_gmail_uri" invisible="1"/>
                <field name="google_gmail_refresh_token" invisible="1"/>
                <div></div>
                <div attrs="{'invisible': [('use_google_gmail_service', '=', False)]}">
                    <span attrs="{'invisible': ['|', ('use_google_gmail_service', '=', False), ('google_gmail_refresh_token', '=', False)]}"
                        class="badge badge-success">
                        Gmail Token Valid
                    </span>
                    <button type="object"
                        name="open_google_gmail_uri" class="btn-link px-0"
                        attrs="{'invisible': ['|', '|', ('google_gmail_uri', '=', False), ('use_google_gmail_service', '=', False), ('google_gmail_refresh_token', '!=', False)]}">
                        <i class="fa fa-arrow-right"/>
                        Connect your Gmail account
                    </button>
                    <button type="object"
                        name="open_google_gmail_uri" class="btn-link px-0"
                        attrs="{'invisible': ['|', '|', ('google_gmail_uri', '=', False), ('use_google_gmail_service', '=', False), ('google_gmail_refresh_token', '=', False)]}">
                        <i class="fa fa-cog"/>
                        Edit Settings
                    </button>
                    <div class="alert alert-warning" role="alert"
                        attrs="{'invisible': ['|', ('use_google_gmail_service', '=', False), ('google_gmail_uri', '!=', False)]}">
                        Setup your Gmail API credentials in the general settings to link a Gmail account.
                    </div>
                </div>
            </field>
            <field name="smtp_pass" position="attributes">
                <attribute name="attrs">{'invisible' : [('use_google_gmail_service', '=', True)]}</attribute>
            </field>
        </field>
    </record>
</odoo>
