.s_popup[data-vcss='001']  {
    .modal-content {
        min-height: $font-size-lg * 2;
        max-height: none;
        border: 0;
        border-radius: 0;
        box-shadow: $modal-content-box-shadow-sm-up;
    }

    .modal-dialog {
        height: auto;
        min-height: 100%;
        @include media-breakpoint-down(xs) {
            min-height: calc(100% - 2 * #{$modal-dialog-margin});
        }
    }

    // Close icon
    .s_popup_close {
        z-index: $zindex-modal;
        @include o-position-absolute(0, 0);
        width: $font-size-lg * 2;
        height: $font-size-lg * 2;
        line-height: $font-size-lg * 2;
        @include o-bg-color(color-yiq(o-color('primary')), o-color('primary'), $with-extras: false);
        box-shadow: $box-shadow-sm;
        cursor: pointer;
        font-size: $font-size-lg;
        text-align: center;
    }

    // Size option - Full
    .s_popup_size_full {
        padding: 0 !important;
        max-width: 100%;

        > .modal-content {
            // Use the backdrop color as background-color
            background-color: transparent;
            box-shadow: none;
            border-radius: 0;
        }
    }

    // Position option - Middle
    .s_popup_middle .modal-dialog {
        align-items: center;
    }

    // Position option - Top/Bottom
    .s_popup_top,
    .s_popup_bottom {
        .modal-dialog {
            margin-right: 0;
            &:not(.s_popup_size_full) {
                padding: $spacer !important;
            }
        }
    }
    .s_popup_top .modal-dialog {
        align-items: flex-start;
    }
    .s_popup_bottom .modal-dialog {
        align-items: flex-end;
    }

    // No backdrop
    .s_popup_no_backdrop {
        // If the popup is taller than the page you should not be able to
        // interact with the elements behind, otherwise you should (when there
        // is no backdrop).
        &:not(.s_popup_overflow_page) {
            pointer-events: none;
        }

        .modal-content {
            pointer-events: auto;
        }
    }
}
