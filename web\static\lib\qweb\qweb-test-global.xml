<?xml version="1.0" encoding="utf-8"?>

<templates>
    <t t-name="_callee-asc"><Año t-att-falló="'agüero'" t-raw="0"/></t>
    <t t-name="_callee-uses-foo"><span t-esc="foo">foo default</span></t>
    <t t-name="_callee-asc-toto"><div t-raw="toto">toto default</div></t>

    <t t-name="caller">
        <t t-foreach="[4,5,6]" t-as="value">
            <span t-esc="value"/>
            <t t-call="_callee-asc">
                <t t-call="_callee-uses-foo">
                    <t t-set="foo" t-value="'aaa'"/>
                </t>
                <t t-call="_callee-uses-foo"/>
                <t t-set="foo" t-value="'bbb'"/>
                <t t-call="_callee-uses-foo"/>
            </t>
        </t>
        <t t-call="_callee-asc-toto"/>
        <t t-set="toto"><t t-set="truc" t-value="'bbb'"/><i t-att-notruc="not truc or None" t-att-truc="bool(truc)">i</i></t>
        <t t-call="_callee-asc-toto"/>
    </t>

    <result id="caller"><![CDATA[
        <span>4</span>
            <Año falló="agüero">
                <span>aaa</span>
                <span>foo default</span>
                
                <span>bbb</span>
            </Año>
        
            <span>5</span>
            <Año falló="agüero">
                <span>aaa</span>
                <span>foo default</span>
                
                <span>bbb</span>
            </Año>
        
            <span>6</span>
            <Año falló="agüero">
                <span>aaa</span>
                <span>foo default</span>
                
                <span>bbb</span>
            </Año>
        
        <div>toto default</div>
        
        <div><i truc="True">i</i></div>
    ]]></result>
</templates>
