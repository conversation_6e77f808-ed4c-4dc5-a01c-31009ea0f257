<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="view_account_journal_form" model="ir.ui.view">
            <field name="name">account.journal.form.l10n_sa_edi</field>
            <field name="model">account.journal</field>
            <field name="inherit_id" ref="account.view_account_journal_form"/>
            <field name="arch" type="xml">
                <xpath expr="//notebook" position="inside">
                    <field name="l10n_sa_csr" invisible="1"/>
                    <field name="l10n_sa_compliance_csid_json" invisible="1"/>
                    <field name="l10n_sa_production_csid_json" invisible="1"/>
                    <field name="l10n_sa_compliance_checks_passed" invisible="1"/>
                    <page name="zatca_einvoicing" string="ZATCA" attrs="{'invisible': ['|', ('country_code', '!=', 'SA'), ('type', '!=', 'sale')]}">
                        <group>
                            <group>
                                <field name="l10n_sa_serial_number"/>
                            </group>
                        </group>
                        <p>
                            <b>
                                In order to be able to submit Invoices to ZATCA, the following steps need to be completed:
                            </b>
                            <ol class="mt-2 mb-4">
                                <li>
                                    Set a Serial Number for your device
                                    <i class="fa fa-check text-success ml-1"
                                       attrs="{'invisible': [('l10n_sa_serial_number', '=', False)]}"/>
                                </li>
                                <li>
                                    Request a Compliance Certificate (CCSID)
                                    <i class="fa fa-check text-success ml-1"
                                       attrs="{'invisible': [('l10n_sa_compliance_csid_json', '=', False)]}"/>
                                </li>
                                <li>
                                    Complete the Compliance Checks
                                    <i class="fa fa-check text-success ml-1"
                                       attrs="{'invisible': [('l10n_sa_compliance_checks_passed', '=', False)]}"/>
                                </li>
                                <li>
                                    Request a Production Certificate (PCSID)
                                    <i class="fa fa-check text-success ml-1"
                                       attrs="{'invisible': [('l10n_sa_production_csid_json', '=', False)]}"/>
                                </li>
                            </ol>
                        </p>
                        <div class="alert alert-info d-flex justify-content-between align-items-center" role="alert"
                             attrs="{'invisible':['|', ('l10n_sa_csr_errors', '!=', False), ('l10n_sa_compliance_csid_json', '!=', False)]}">
                            <p class="mb-0">
                                Onboard the Journal by completing each step
                            </p>
                            <button name="%(l10n_sa_edi_otp_wizard_act_window)d" type="action" icon="fa-key"
                                    class="btn-info ">
                                Onboard Journal
                            </button>
                        </div>
                        <div class="alert alert-danger d-flex flex-column align-items-end" role="alert"
                             attrs="{'invisible':['|', '|', ('l10n_sa_csr_errors', '=', False), ('l10n_sa_compliance_csid_json', '!=', False), ('l10n_sa_production_csid_json', '!=', False)]}">
                            <div class="w-100">
                                <h4 role="alert" class="alert-heading">Journal could not be onboarded. Please make sure the Company VAT/Identification Number are correct.</h4>
                                <field name="l10n_sa_csr_errors" nolabel="1" readonly="1"/>
                                <hr/>
                            </div>
                            <button name="%(l10n_sa_edi_otp_wizard_act_window)d" type="action" icon="fa-key"
                                    class="btn-danger">
                                Onboard Journal
                            </button>
                        </div>
                        <div class="alert alert-info d-flex justify-content-between align-items-center" role="alert"
                             attrs="{'invisible':['|', ('l10n_sa_compliance_checks_passed', '=', False), ('l10n_sa_production_csid_json', '=', False)]}">
                            <p class="mb-0">
                                The Production certificate is valid until
                                <field name="l10n_sa_production_csid_validity" readonly="1" nolabel="1"
                                       class="font-weight-bold"/>
                            </p>
                            <div>
                                <button name="%(l10n_sa_edi_otp_wizard_act_window)d" type="action" icon="fa-refresh"
                                        class="btn-info" context="{'default_l10n_sa_renewal': True}">
                                    Renew Production CSID
                                </button>
                                <button name="%(l10n_sa_edi_otp_wizard_act_window)d" type="action" icon="fa-refresh" class="btn-warning ml-2"
                                    confirm="Are you sure you wish to re-onboard the Journal?">
                                    Re-Onboard
                                </button>
                            </div>
                        </div>
                    </page>
                </xpath>
            </field>
        </record>

    </data>
</odoo>