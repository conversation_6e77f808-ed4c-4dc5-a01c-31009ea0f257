<odoo>
    <data>
        <record id="l10n_ke_inherit_account_move_form" model="ir.ui.view">
            <field name="name">l10n.ke.inherit.account.move.form</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_move_form"/>
            <field name="priority" eval="40"/>
            <field name="arch" type="xml">
                <xpath expr="//header/button[@name='action_post']" position="after">
                    <field name="l10n_ke_cu_qrcode" invisible="1"/>
                    <button name="l10n_ke_action_cu_post" type="object"
                            class="oe_highlight"
                            groups="account.group_account_manager"
                            string="Send To Fiscal Device"
                            attrs="{'invisible': ['|', '|', '|', ('country_code', '!=', 'KE'), ('l10n_ke_cu_qrcode', '!=', False), ('state', '!=', 'posted'), ('move_type', 'not in', ['out_invoice', 'out_refund'])]}"/>
                </xpath>
                <xpath expr="//group[@id='header_right_group']" position="inside">
                    <field name="l10n_ke_cu_invoice_number" attrs="{'invisible': [('country_code', '!=', 'KE')]}" readonly="1"/>
                </xpath>
                <notebook position="inside">
                    <page string="Tremol GO3 Fiscal Device" attrs="{'invisible': [('country_code', '!=', 'KE')]}">
                        <group>
                            <group>
                                <field name="l10n_ke_cu_qrcode" widget="url" readonly="1"/>
                                <field name="l10n_ke_cu_serial_number" readonly="1"/>
                                <field name="l10n_ke_cu_datetime" readonly="1"/>
                            </group>
                        </group>
                    </page>
                </notebook>
            </field>
        </record>

        <record id="l10n_ke_inherit_account_move_tree_view" model="ir.ui.view">
            <field name="name">l10n.ke.inherit.account.move.tree</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_out_invoice_tree" />
            <field name="arch" type="xml">
                <field name="state" position="after">
                    <field name="l10n_ke_cu_invoice_number" optional="hide"/>
                </field>
            </field>
        </record>

        <record id="action_send_invoices_to_device" model="ir.actions.server">
            <field name="name">Send to fiscal device</field>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="binding_model_id" ref="account.model_account_move"/>
            <field name="binding_view_types">list</field>
            <field name="state">code</field>
            <field name="code">
                action = records.l10n_ke_action_cu_post()
            </field>
        </record>

        <record id="l10n_ke_inherit_account_move_search_view" model="ir.ui.view">
            <field name="name">l10n.ke.inherit.account.move.search</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_account_invoice_filter" />
            <field name="arch" type="xml">
                <xpath expr="//field[@name='journal_id']" position="after">
                    <field name="l10n_ke_cu_invoice_number" string="Kenya CU Invoice Number" filter_domain="[('l10n_ke_cu_invoice_number', 'ilike', self')]" />
                </xpath>
                <xpath expr="//filter[@name='cancel']" position="after">
                    <separator/>
                    <filter name="l10n_ke_edi_to_send" string="To Send to TIMS" domain="[('l10n_ke_cu_invoice_number', '=', False), ('state', '=', 'posted'), ('move_type', 'in', ['out_invoice', 'out_refund']), ('country_code', '=', 'KE')]"/>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
