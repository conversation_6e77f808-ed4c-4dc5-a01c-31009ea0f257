<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.project.timesheet.holidays</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="hr_timesheet.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@id='module_project_timesheet_holidays']" position="replace">
                <div attrs="{'invisible': [('module_project_timesheet_holidays','=',False)]}">
                    <div class="row mt16">
                        <div class="w-100">
                            <label string="Project" for="internal_project_id" class="col-2 col-lg-3"/>
                            <field name="internal_project_id" context="{'active_test': False}" class="oe_inline ml16"/>
                        </div>
                        <div class="w-100">
                            <label string="Task" for="leave_timesheet_task_id" class="col-2 col-lg-3"/>
                            <field name="leave_timesheet_task_id" context="{'active_test': False, 'default_project_id': internal_project_id}" class="oe_inline ml16"/>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>

</odoo>
