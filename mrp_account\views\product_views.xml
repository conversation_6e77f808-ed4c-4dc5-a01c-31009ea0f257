<?xml version="1.0"?>
<odoo>
        <record id="product_product_ext_form_view2" model="ir.ui.view">
            <field name="name">product_extended.product.form.view</field>
            <field name="model">product.template</field>
            <field name="priority">3</field>
            <field name="inherit_id" ref="product.product_template_only_form_view" />
            <field name="groups_id" eval="[(4, ref('mrp.group_mrp_manager'))]"/>
            <field name="arch" type="xml">
                <xpath expr="//div[@name='standard_price_uom']" position="inside">
                    <field name="cost_method" invisible="1"/>
                    <field name="valuation" invisible="1"/>
                    <button name="button_bom_cost"
                        string="Compute Price from BoM" type="object"
                        attrs="{'invisible': ['|', ('bom_count', '=', 0), '&amp;', ('valuation', '=', 'real_time'), ('cost_method', '=', 'fifo')]}"
                        help="Compute the price of the product using products and operations of related bill of materials, for manufactured products only."
                        class="oe_link oe_read_only pt-0"/>
                </xpath>
            </field>
        </record>

        <record id="product_product_view_form_normal_inherit_extended" model="ir.ui.view">
            <field name="name">product.product.view.form.normal.inherit.extended</field>
            <field name="model">product.product</field>
            <field name="priority">4</field>
            <field name="inherit_id" ref="product.product_normal_form_view"/>
            <field name="groups_id" eval="[(4, ref('mrp.group_mrp_manager'))]"/>
            <field name="arch" type="xml">
                <xpath expr="//div[@name='standard_price_uom']" position="inside">
                    <field name="cost_method" invisible="1"/>
                    <field name="valuation" invisible="1"/>
                    <button name="button_bom_cost"
                        string="Compute Price from BoM" type="object"
                        attrs="{'invisible': ['|', ('bom_count', '=', 0), '&amp;', ('valuation', '=', 'real_time'), ('cost_method', '=', 'fifo')]}"
                        help="Compute the price of the product using products and operations of related bill of materials, for manufactured products only."
                        class="oe_link oe_read_only pt-0"
                        colspan="2"/>
                </xpath>
            </field>
        </record>

        <record id="product_variant_easy_edit_view_bom_inherit" model="ir.ui.view">
            <field name="name">product.product.product.view.form.easy.bom.inherit</field>
            <field name="model">product.product</field>
            <field name="inherit_id" ref="product.product_variant_easy_edit_view"/>
            <field name="groups_id" eval="[(4, ref('mrp.group_mrp_manager'))]"/>
            <field name="arch" type="xml">
                <data>
                <xpath expr="//field[@name='standard_price']" position="replace">
                    <field name="bom_count" invisible="1"/>
                    <field name="cost_method" invisible="1"/>
                    <field name="valuation" invisible="1"/>
                    <label for="standard_price"/>
                    <div>
                        <field name="standard_price" widget='monetary' options="{'currency_field': 'cost_currency_id'}"/>
                        <button name="button_bom_cost"
                            string="Compute Price from BoM"
                            type="object"
                            attrs="{'invisible': ['|', ('bom_count', '=', 0), '&amp;', ('valuation', '=', 'real_time'), ('cost_method', '=', 'fifo')]}"
                            help="Compute the price of the product using products and operations of related bill of materials, for manufactured products only."
                            class="oe_link oe_read_only pt-0"/>
                    </div>
                </xpath>
                </data>
            </field>
        </record>

        <record id="action_compute_price_bom_template" model="ir.actions.server">
            <field name="name">Compute Price from BoM</field>
            <field name="model_id" ref="product.model_product_template"/>
            <field name="binding_model_id" ref="product.model_product_template"/>
            <field name="binding_view_types">list</field>
            <field name="state">code</field>
            <field name="code">
            if records:
                records.action_bom_cost()
            </field>
        </record>

        <record id="action_compute_price_bom_product" model="ir.actions.server">
            <field name="name">Compute Price from BoM</field>
            <field name="model_id" ref="product.model_product_product"/>
            <field name="binding_model_id" ref="product.model_product_product"/>
            <field name="binding_view_types">list</field>
            <field name="state">code</field>
            <field name="code">
            if records:
                records.action_bom_cost()
            </field>
        </record>
</odoo>
