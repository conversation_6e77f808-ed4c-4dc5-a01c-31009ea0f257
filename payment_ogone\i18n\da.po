# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_ogone
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:49+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON>, 2021\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_acquirer__ogone_userid
msgid "API User ID"
msgstr "API bruger-ID"

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_acquirer__ogone_password
msgid "API User Password"
msgstr "API bruger adgangskode"

#. module: payment_ogone
#: code:addons/payment_ogone/models/payment_acquirer.py:0
#, python-format
msgid "Could not establish the connection to the API."
msgstr ""

#. module: payment_ogone
#: code:addons/payment_ogone/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr ""

#. module: payment_ogone
#: model:account.payment.method,name:payment_ogone.payment_method_ogone
#: model:ir.model.fields.selection,name:payment_ogone.selection__payment_acquirer__provider__ogone
msgid "Ogone"
msgstr "Ogone"

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_acquirer__ogone_pspid
msgid "PSPID"
msgstr "PSPID"

#. module: payment_ogone
#: model:ir.model,name:payment_ogone.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Betalingsindløser"

#. module: payment_ogone
#: model:ir.model,name:payment_ogone.model_account_payment_method
msgid "Payment Methods"
msgstr "Betalingsmetoder"

#. module: payment_ogone
#: model:ir.model,name:payment_ogone.model_payment_token
msgid "Payment Token"
msgstr "Betalingstoken"

#. module: payment_ogone
#: model:ir.model,name:payment_ogone.model_payment_transaction
msgid "Payment Transaction"
msgstr "Betalingstransaktion"

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_acquirer__provider
msgid "Provider"
msgstr "Udbyder"

#. module: payment_ogone
#: code:addons/payment_ogone/models/payment_transaction.py:0
#, python-format
msgid "Received data with invalid payment status: %s"
msgstr ""

#. module: payment_ogone
#: code:addons/payment_ogone/controllers/main.py:0
#, python-format
msgid ""
"Received data with invalid signature. expected: %(exp)s ; received: %(rec)s ; data:\n"
"%(data)s"
msgstr ""

#. module: payment_ogone
#: code:addons/payment_ogone/models/payment_transaction.py:0
#, python-format
msgid "Received feedback data with unknown type: %s"
msgstr ""

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_acquirer__ogone_shakey_in
msgid "SHA Key IN"
msgstr "SHA Key IN"

#. module: payment_ogone
#: model:ir.model.fields,field_description:payment_ogone.field_payment_acquirer__ogone_shakey_out
msgid "SHA Key OUT"
msgstr "SHA Nøgle UD"

#. module: payment_ogone
#: code:addons/payment_ogone/models/payment_token.py:0
#, python-format
msgid "Saved payment methods cannot be restored once they have been archived."
msgstr ""

#. module: payment_ogone
#: code:addons/payment_ogone/models/payment_transaction.py:0
#, python-format
msgid "Storing your payment details is necessary for future use."
msgstr ""

#. module: payment_ogone
#: model:ir.model.fields,help:payment_ogone.field_payment_acquirer__ogone_userid
msgid "The ID solely used to identify the API user with Ogone"
msgstr ""

#. module: payment_ogone
#: model:ir.model.fields,help:payment_ogone.field_payment_acquirer__ogone_pspid
msgid "The ID solely used to identify the account with Ogone"
msgstr ""

#. module: payment_ogone
#: model:ir.model.fields,help:payment_ogone.field_payment_acquirer__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr ""

#. module: payment_ogone
#: code:addons/payment_ogone/models/payment_acquirer.py:0
#, python-format
msgid "The communication with the API failed."
msgstr ""

#. module: payment_ogone
#: code:addons/payment_ogone/models/payment_transaction.py:0
#, python-format
msgid "The payment has been declined: %s"
msgstr ""

#. module: payment_ogone
#: code:addons/payment_ogone/models/payment_transaction.py:0
#, python-format
msgid "The transaction is not linked to a token."
msgstr ""
