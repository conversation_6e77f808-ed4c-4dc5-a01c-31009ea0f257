# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * base_geolocalize
# 
# Translators:
# <PERSON>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-18 09:49+0000\n"
"PO-Revision-Date: 2018-09-18 09:49+0000\n"
"Last-Translator: <PERSON>, 2018\n"
"Language-Team: Serbian (https://www.transifex.com/odoo/teams/41243/sr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "<span class=\"oe_inline\"> ( On  </span>"
msgstr ""

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "<span> : Lat : </span>"
msgstr ""

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "<span> ;  Long:  </span>"
msgstr ""

#. module: base_geolocalize
#: code:addons/base_geolocalize/models/res_partner.py:19
#, python-format
msgid ""
"API key for GeoCoding (Places) required.\n"
"\n"
"                          Save this key in System Parameters with key: google.api_key_geocode, value: <your api key>\n"
"                          Visit https://developers.google.com/maps/documentation/geocoding/get-api-key for more information.\n"
"                          "
msgstr ""

#. module: base_geolocalize
#: code:addons/base_geolocalize/models/res_partner.py:28
#, python-format
msgid ""
"Cannot contact geolocation servers. Please make sure that your Internet "
"connection is up and running (%s)."
msgstr ""

#. module: base_geolocalize
#: model:ir.model,name:base_geolocalize.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_res_partner__partner_latitude
#: model:ir.model.fields,field_description:base_geolocalize.field_res_users__partner_latitude
msgid "Geo Latitude"
msgstr ""

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_res_partner__partner_longitude
#: model:ir.model.fields,field_description:base_geolocalize.field_res_users__partner_longitude
msgid "Geo Longitude"
msgstr ""

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Geolocate"
msgstr ""

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Geolocation"
msgstr ""

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_res_partner__date_localization
#: model:ir.model.fields,field_description:base_geolocalize.field_res_users__date_localization
msgid "Geolocation Date"
msgstr ""

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Partner Assignation"
msgstr ""
