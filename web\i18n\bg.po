# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * web
#
# Translators:
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2016-07-19 19:21+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Bulgarian (http://www.transifex.com/odoo/odoo-9/language/"
"bg/)\n"
"Language: bg\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"#-#-#-#-#  bg.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"#-#-#-#-#  bg.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_common.js:866
#, python-format
msgid " & Close"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:467
#, python-format
msgid " selected records"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/view_manager.js:168
#, python-format
msgid " view couldn't be loaded"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_filters.js:115
#: code:addons/web/static/src/js/views/search_filters.js:290
#, python-format
msgid "%(field)s %(operator)s"
msgstr "%(field)s %(operator)s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_filters.js:116
#, python-format
msgid "%(field)s %(operator)s \"%(value)s\""
msgstr "%(field)s %(operator)s \"%(value)s\""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list_view.js:1587
#, python-format
msgid "%(page)d/%(page_count)d"
msgstr "%(page)d/%(page_count)d"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/view_manager.js:66
#, python-format
msgid "%(view_type)s view"
msgstr "%(view_type)s Изглед "

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_view.js:450
#, python-format
msgid "%d / %d"
msgstr "%d / %d"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/core.js:134
#, python-format
msgid "%d days ago"
msgstr "Преди %d дена"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/core.js:132
#, python-format
msgid "%d hours ago"
msgstr "Преди %d часа"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/core.js:130
#, python-format
msgid "%d minutes ago"
msgstr "Преди %d минути"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/core.js:136
#, python-format
msgid "%d months ago"
msgstr "Преди %d месеца"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/core.js:138
#, python-format
msgid "%d years ago"
msgstr "Преди %d години "

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list_view.js:440
#, python-format
msgid "%d-%d of %d"
msgstr "%d-%d of %d"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list_view.js:1496
#, python-format
msgid "%s (%d)"
msgstr "%s (%d)"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.webclient_bootstrap
msgid "&lt;!--[if lte IE 9]&gt;"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.webclient_bootstrap
msgid "&lt;![endif]--&gt;"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/formats.js:177
#, python-format
msgid "'%s' is not a correct date"
msgstr "\"%s\" не е правилна дата"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/time.js:193
#, python-format
msgid "'%s' is not a correct date, datetime nor time"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/formats.js:169
#, python-format
msgid "'%s' is not a correct datetime"
msgstr "\"%s\" не е правилна дата и час"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/formats.js:146
#, python-format
msgid "'%s' is not a correct float"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/formats.js:134
#, python-format
msgid "'%s' is not a correct integer"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/formats.js:182
#, python-format
msgid "'%s' is not a correct time"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/time.js:205
#, python-format
msgid "'%s' is not convertible to date, datetime nor time"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/formats.js:77
#, python-format
msgid "(%d records)"
msgstr "%d записи"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:106
#, python-format
msgid "(Community Edition)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:100
#, python-format
msgid "(no string)"
msgstr "(няма низ)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:34
#, python-format
msgid "(nolabel)"
msgstr "(без етикет)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1059
#, python-format
msgid "...Upload in progress..."
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.menu_secondary
msgid "<span class=\"oe_logo_edit\">Edit Company data</span>"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.menu_secondary
msgid "<span>Odoo</span>"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/user_menu.js:79
#: code:addons/web/static/src/xml/base.xml:84
#, python-format
msgid "About"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/crash_manager.js:25
#, python-format
msgid "Access Denied"
msgstr "Отказан достъп"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/crash_manager.js:21
#, python-format
msgid "Access Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1515
#, python-format
msgid "Access to all Enterprise Apps"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/sidebar.js:24
#, python-format
msgid "Action"
msgstr "Действие"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_relational_widgets.js:1125
#, python-format
msgid "Action Button"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:120
#, python-format
msgid "Action ID:"
msgstr "ID на действието"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:685
#, python-format
msgid "Activate"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:96
#, python-format
msgid "Activate the developer mode"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:682
#, python-format
msgid "Active"
msgstr "Активен"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_relational_widgets.js:1384
#: code:addons/web/static/src/xml/base.xml:1106
#: code:addons/web/static/src/xml/base.xml:1339
#, python-format
msgid "Add"
msgstr "Добавяне"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1212
#, python-format
msgid "Add Custom Filter"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1213
#, python-format
msgid "Add a condition"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_relational_widgets.js:1024
#, python-format
msgid "Add an item"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1223
#, python-format
msgid "Add custom group"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:325
#, python-format
msgid "Add..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_relational_widgets.js:1325
#, python-format
msgid "Add: "
msgstr "Добавяне: "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1119
#, python-format
msgid "Advanced Search..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/dialog.js:169
#, python-format
msgid "Alert"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:562
#, python-format
msgid "All users"
msgstr "Всички потребители"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1520
#, python-format
msgid "And more"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1213
#: code:addons/web/static/src/xml/base.xml:1233
#, python-format
msgid "Apply"
msgstr "Приложи"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:689
#: code:addons/web/static/src/js/views/list_view.js:307
#, python-format
msgid "Archive"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:690
#, python-format
msgid "Archived"
msgstr "Архивирани"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_menus.js:243
#, python-format
msgid "Are you sure that you want to remove this filter?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:297
#, python-format
msgid "Attachment :"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1323
#, python-format
msgid "Available fields"
msgstr "Налични полета"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:614
#, python-format
msgid "Bar Chart"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list_view.js:1955
#, python-format
msgid "Binary file"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1519
#, python-format
msgid "Bugfixes guarantee"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:97
#, python-format
msgid "Button"
msgstr "Бутон"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:112
#, python-format
msgid "Button Type:"
msgstr "Тип на бутона:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/utils.js:285
#, python-format
msgid "Bytes,Kb,Mb,Gb,Tb,Pb,Eb,Zb,Yb"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list_common.js:134
#, python-format
msgid "Can't convert value %s to context"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:359
#, python-format
msgid "Can't send email to invalid e-mail address"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/crash_manager.js:182
#: code:addons/web/static/src/js/framework/dialog.js:183
#: code:addons/web/static/src/js/views/form_common.js:1031
#: code:addons/web/static/src/js/views/form_relational_widgets.js:44
#: code:addons/web/static/src/js/views/form_widgets.js:1706
#: code:addons/web/static/src/xml/base.xml:60
#, python-format
msgid "Cancel"
msgstr "Откажи"

#. module: web
#. openerp-web
#: code:addons/web/controllers/main.py:748
#: code:addons/web/controllers/main.py:750
#: code:addons/web/controllers/main.py:756
#: code:addons/web/controllers/main.py:757
#: code:addons/web/static/src/js/widgets/change_password.js:15
#: code:addons/web/static/src/xml/base.xml:59
#, python-format
msgid "Change Password"
msgstr "Смяна на парола"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:71
#, python-format
msgid "Change default:"
msgstr "Промяна по подразбиране:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:472
#, python-format
msgid "Change selection "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:963
#: code:addons/web/static/src/xml/base.xml:1017
#: code:addons/web/static/src/xml/base.xml:1019
#, python-format
msgid "Clear"
msgstr "Изчистване"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/crash_manager.js:135
#: code:addons/web/static/src/js/web_client.js:132
#, python-format
msgid "Client Error"
msgstr "Грешка в клиента"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_common.js:860
#: code:addons/web/static/src/js/views/form_view.js:1194
#: code:addons/web/static/src/js/widgets/data_export.js:41
#, python-format
msgid "Close"
msgstr "Затвори"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:536
#, python-format
msgid "Condition:"
msgstr "Състояние:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:53
#, python-format
msgid "Confirm New Password:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/dialog.js:194
#, python-format
msgid "Confirmation"
msgstr "Потвърждение"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:59
#, python-format
msgid "Context:"
msgstr "Контекст:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:116
#, python-format
msgid "Copyright © 2004-2015 Odoo S.A."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:1379
#, python-format
msgid "Could not display the selected image."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list_view.js:1109
#, python-format
msgid "Could not find id in dataset"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/utils.js:180
#, python-format
msgid "Could not serialize XML"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph_view.js:90
#: code:addons/web/static/src/js/views/graph_widget.js:21
#: code:addons/web/static/src/js/views/pivot_view.js:186
#: code:addons/web/static/src/xml/base.xml:627
#: code:addons/web/static/src/xml/base.xml:659
#, python-format
msgid "Count"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_common.js:1034
#: code:addons/web/static/src/js/views/form_relational_widgets.js:30
#: code:addons/web/static/src/js/views/list_view.js:48
#: code:addons/web/static/src/js/views/list_view.js:2033
#: code:addons/web/static/src/xml/base.xml:501
#, python-format
msgid "Create"
msgstr "Създаване"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_common.js:225
#, python-format
msgid "Create \"<strong>%s</strong>\""
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_relational_widgets.js:27
#, python-format
msgid "Create a %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_common.js:236
#, python-format
msgid "Create and Edit..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_relational_widgets.js:40
#, python-format
msgid "Create and edit"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_common.js:281
#: code:addons/web/static/src/js/views/form_relational_widgets.js:1083
#, python-format
msgid "Create: "
msgstr "Създаване: "

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:302
#, python-format
msgid "Created by :"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:248
#, python-format
msgid "Creation Date:"
msgstr "Задаване на дата:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:244
#, python-format
msgid "Creation User:"
msgstr "Създаване на потребител:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_common.js:1067
#: code:addons/web/static/src/js/views/search_menus.js:175
#, python-format
msgid "Custom Filter"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Database"
msgstr "База данни"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:110
#, python-format
msgid "Database expiration:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:673
#, python-format
msgid "Day"
msgstr "Ден"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:683
#, python-format
msgid "Deactivate"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:519
#, python-format
msgid "Default:"
msgstr "По подразбиране:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_view.js:214
#: code:addons/web/static/src/js/views/list_view.js:309
#: code:addons/web/static/src/xml/base.xml:1408
#, python-format
msgid "Delete"
msgstr "Изтриване"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:313
#, python-format
msgid "Delete this attachment"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1066
#, python-format
msgid "Delete this file"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_common.js:860
#: code:addons/web/static/src/xml/base.xml:425
#: code:addons/web/static/src/xml/base.xml:507
#, python-format
msgid "Discard"
msgstr "Откажи"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/sidebar.js:205
#, python-format
msgid "Do you really want to delete this attachment ?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_view.js:803
#, python-format
msgid "Do you really want to delete this record?"
msgstr "Сигурни ли сте, че искате да изтриете този запис?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list_view.js:629
#, python-format
msgid "Do you really want to remove these records?"
msgstr "Сигурни ли сте, че искате да изтриете тези записи?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:82
#, python-format
msgid "Documentation"
msgstr "Документация"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:63
#, python-format
msgid "Domain:"
msgstr "Домейн:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/misc.js:16
#, python-format
msgid "Don't leave yet,<br />it's still loading..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:1305
#: code:addons/web/static/src/js/views/list_view.js:1955
#, python-format
msgid "Download"
msgstr "Изтегляне"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list_view.js:1971
#, python-format
msgid "Download \"%s\""
msgstr "Изтегляне \"%s\""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot_view.js:122
#, python-format
msgid "Download xls"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_view.js:215
#, python-format
msgid "Duplicate"
msgstr "Дублиране"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:359
#, python-format
msgid "E-mail Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:219
#: code:addons/web/static/src/xml/base.xml:496
#: code:addons/web/static/src/xml/base.xml:962
#, python-format
msgid "Edit"
msgstr "Редакция"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:221
#, python-format
msgid "Edit Action"
msgstr "Редактиране на действието"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:220
#, python-format
msgid "Edit SearchView"
msgstr "Редактиране изгледа за търсене"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/debug_manager.js:179
#: code:addons/web/static/src/xml/base.xml:222
#, python-format
msgid "Edit Workflow"
msgstr "Редактиране работен поток"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Email"
msgstr "Имейл"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/website.tour.xml:25
#, python-format
msgid "End This Tutorial"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/view_manager.js:168
#: code:addons/web/static/src/js/views/search_menus.js:83
#: code:addons/web/static/src/js/views/search_menus.js:90
#, python-format
msgid "Error"
msgstr "Грешка"

#. module: web
#: code:addons/web/controllers/main.py:757
#, python-format
msgid "Error, password not changed !"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:462
#, python-format
msgid "Error: Bad domain"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/pyeval.js:993
#, python-format
msgid "Evaluation Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:648
#, python-format
msgid "Expand all"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list_view.js:306
#: code:addons/web/static/src/xml/base.xml:1299
#, python-format
msgid "Export"
msgstr "Изнасяне"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:51
#, python-format
msgid "Export Data"
msgstr "Експорт на данни"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1317
#, python-format
msgid "Export Formats"
msgstr "Формати за експорт"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:42
#, python-format
msgid "Export To File"
msgstr "Експорт към файл"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1311
#, python-format
msgid "Export Type:"
msgstr "Тип на експорта:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1314
#, python-format
msgid "Export all Data"
msgstr "Експорт на всички данни"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/view_manager.js:381
#, python-format
msgid "Failed to evaluate search criterions"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1237
#, python-format
msgid "Favorites"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_view.js:1358
#, python-format
msgid "Field '%s' specified in view could not be found."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:39
#, python-format
msgid "Field:"
msgstr "Поле:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/debug_manager.js:140
#: code:addons/web/static/src/xml/base.xml:215
#, python-format
msgid "Fields View Get"
msgstr "Изглед на полета"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1325
#, python-format
msgid "Fields to export"
msgstr "Полета за експорт"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:1220
#, python-format
msgid "File Upload"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:1200
#, python-format
msgid "File upload"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_inputs.js:550
#, python-format
msgid "Filter"
msgstr "Филтър"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_menus.js:83
#, python-format
msgid "Filter name is required."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_inputs.js:494
#, python-format
msgid "Filter on: %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_menus.js:90
#, python-format
msgid "Filter with same name already exists."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1209
#, python-format
msgid "Filters"
msgstr "Филтри"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:647
#, python-format
msgid "Flip axis"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot_view.js:808
#, python-format
msgid ""
"For Excel compatibility, data cannot be exported if there is more than 256 "
"columns.\n"
"\n"
"Tip: try to flip axis, filter further or reduce the number of measures."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:119
#, python-format
msgid "For more information visit"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_view.js:34
#: code:addons/web/static/src/js/views/form_view.js:361
#, python-format
msgid "Form"
msgstr "Форма"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:118
#, python-format
msgid "GNU Lesser General Public License"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1512
#, python-format
msgid "Get this feature and much more with Odoo Enterprise!"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/crash_manager.js:24
#, python-format
msgid "Global Business Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph_view.js:18
#, python-format
msgid "Graph"
msgstr "Графика"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list_view.js:464
#, python-format
msgid "Group"
msgstr "Група"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_inputs.js:695
#: code:addons/web/static/src/xml/base.xml:1219
#, python-format
msgid "Group By"
msgstr "Групиране по"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_inputs.js:670
#, python-format
msgid "Group by: %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/priority.js:50
#, python-format
msgid "High"
msgstr "Висок"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:232
#, python-format
msgid "ID:"
msgstr "ИД:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:1379
#, python-format
msgid "Image"
msgstr "Изображение"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1313
#, python-format
msgid "Import-Compatible Export"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:684
#, python-format
msgid "Inactive"
msgstr "Неактивен"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph_widget.js:198
#: code:addons/web/static/src/js/views/graph_widget.js:205
#, python-format
msgid "Invalid data"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/debug_manager.js:111
#: code:addons/web/static/src/xml/base.xml:213
#, python-format
msgid "JS Tests"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:764
#, python-format
msgid "Languages"
msgstr "Езици"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:256
#, python-format
msgid "Latest Modification Date:"
msgstr "Последна дата на модификация:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:252
#, python-format
msgid "Latest Modification by:"
msgstr "Последна модификация от:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:225
#, python-format
msgid "Leave Debug Mode"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:118
#, python-format
msgid "Licenced under the terms of"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:615
#, python-format
msgid "Line Chart"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list_view.js:39
#, python-format
msgid "List"
msgstr "Списък"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/loading.js:12
#: code:addons/web/static/src/js/widgets/loading.js:46
#, python-format
msgid "Loading"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/loading.js:44
#, python-format
msgid "Loading (%d)"
msgstr "Зареждане (%d)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/misc.js:13
#: code:addons/web/static/src/xml/base.xml:5
#, python-format
msgid "Loading..."
msgstr "Зареждане..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/pyeval.js:997
#, python-format
msgid ""
"Local evaluation failure\n"
"%s\n"
"\n"
"%s"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Log in"
msgstr "Вход"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:88
#, python-format
msgid "Log out"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/priority.js:49
#, python-format
msgid "Low"
msgstr "Нисък"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_inputs.js:450
#, python-format
msgid "M2O search fields do not currently handle multiple default values"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Manage Databases"
msgstr "Управление на бази данни"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/debug_manager.js:146
#: code:addons/web/static/src/xml/base.xml:216
#, python-format
msgid "Manage Filters"
msgstr "Управление на филтри"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/misc.js:19
#, python-format
msgid "Maybe you should consider reloading the application by pressing F5..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:619
#, python-format
msgid "Measure"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:651
#, python-format
msgid "Measures"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/debug_manager.js:94
#, python-format
msgid "Metadata (%s)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:116
#, python-format
msgid "Method:"
msgstr "Метод:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/crash_manager.js:22
#, python-format
msgid "Missing Record"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1517
#, python-format
msgid "Mobile support"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/debug_manager.js:134
#, python-format
msgid "Model %s fields"
msgstr "Модел %s полета"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:307
#, python-format
msgid "Modified by :"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:67
#, python-format
msgid "Modifiers:"
msgstr "Модификатори:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:675
#, python-format
msgid "Month"
msgstr "Месец"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:947
#, python-format
msgid "More"
msgstr "Повече"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.menu
msgid "More <b class=\"caret\"/>"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1343
#, python-format
msgid "Move Down"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1342
#, python-format
msgid "Move Up"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:87
#, python-format
msgid "My Odoo.com account"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1362
#, python-format
msgid "Name"
msgstr "Име"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1422
#, python-format
msgid "Name:"
msgstr "Име:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_view.js:366
#, python-format
msgid "New"
msgstr "Нов"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:48
#, python-format
msgid "New Password:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1516
#, python-format
msgid "New design"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_inputs.js:330
#, python-format
msgid "No"
msgstr "Не"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:240
#, python-format
msgid "No Update:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph_widget.js:95
#, python-format
msgid ""
"No data available for this chart. Try to add some records, or make sure that "
"there is no active filter in the search bar."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:691
#, python-format
msgid ""
"No data available for this pivot table.  Try to add some records, or make "
"sure\n"
"                that there is at least one measure and no active filter in "
"the search bar."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_view.js:124
#, python-format
msgid "No data provided."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph_widget.js:94
#, python-format
msgid "No data to display"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:690
#, python-format
msgid "No data to display."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/debug_manager.js:89
#, python-format
msgid "No metadata available"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_common.js:245
#, python-format
msgid "No results to show..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:481
#, python-format
msgid "No selected record"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_relational_widgets.js:329
#, python-format
msgid "No value found for the field %s for value %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/utils.js:144
#, python-format
msgid "Node [%s] is not a JSONified XML node"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/priority.js:48
#, python-format
msgid "Normal"
msgstr "Нормален"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:688
#, python-format
msgid "Not Archived"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph_widget.js:238
#, python-format
msgid "Not enough data points"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:885
#, python-format
msgid "Not shown in kanban"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:43
#, python-format
msgid "Object:"
msgstr "Обект:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/dialog.js:37
#: model_terms:ir.ui.view,arch_db:web.layout
#, python-format
msgid "Odoo"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:101
#, python-format
msgid "Odoo (Formerly OpenERP)"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/apps.js:189
#, python-format
msgid "Odoo Apps will be available soon"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:1717
#, python-format
msgid "Odoo Enterprise"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:117
#, python-format
msgid "Odoo S.A."
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.qunit_suite
msgid "Odoo Web Tests"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:117
#, python-format
msgid "Odoo is a trademark of"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:119
#, python-format
msgid "Odoo.com"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:702
#, python-format
msgid "Off"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/dialog.js:41
#: code:addons/web/static/src/js/framework/dialog.js:159
#: code:addons/web/static/src/js/framework/dialog.js:177
#: code:addons/web/static/src/xml/base.xml:1396
#, python-format
msgid "Ok"
msgstr "Ok"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:43
#, python-format
msgid "Old Password:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:700
#, python-format
msgid "On"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:75
#, python-format
msgid "On change:"
msgstr "При промяна:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/progress_bar.js:79
#, python-format
msgid "Only Integer Value should be valid."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:555
#, python-format
msgid "Only you"
msgstr "Само ти"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:198
#, python-format
msgid "Open Debug Menu"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_relational_widgets.js:153
#: code:addons/web/static/src/js/views/form_relational_widgets.js:936
#: code:addons/web/static/src/js/views/form_relational_widgets.js:1107
#: code:addons/web/static/src/js/views/form_relational_widgets.js:1341
#, python-format
msgid "Open: "
msgstr "Отвори: "

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Password"
msgstr "Парола"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:616
#, python-format
msgid "Pie Chart"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph_widget.js:206
#, python-format
msgid ""
"Pie chart cannot display all zero numbers.. Try to change your domain to "
"display positive results"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph_widget.js:199
#, python-format
msgid ""
"Pie chart cannot mix positive and negative numbers. Try to change your "
"domain to only display positive results"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot_view.js:24
#, python-format
msgid "Pivot"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:166
#, python-format
msgid "Please enter save field list name"
msgstr "Моля, въведете име на списъка с избраните полета"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1356
#, python-format
msgid "Please note that only the selected ids will be exported."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1353
#, python-format
msgid ""
"Please pay attention that all records matching your search filter will be "
"exported. Not only the selected ids."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:416
#, python-format
msgid "Please select fields to export..."
msgstr "Изберете полета за експорт..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/data_export.js:403
#, python-format
msgid "Please select fields to save export list..."
msgstr "Моля, изберете полета за запис в списъка за експорт..."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.menu_secondary
msgid "Powered by"
msgstr ""

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Powered by <span>Odoo</span>"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:86
#, python-format
msgid "Preferences"
msgstr "Предпочитания"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/sidebar.js:23
#, python-format
msgid "Print"
msgstr "Печат"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:223
#, python-format
msgid "Print Workflow"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:694
#, python-format
msgid "Production Environment"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:676
#, python-format
msgid "Quarter"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:79
#, python-format
msgid "Relation:"
msgstr "Отношение:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1340
#, python-format
msgid "Remove"
msgstr "Премахване"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1341
#, python-format
msgid "Remove All"
msgstr "Премахване на всичко"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_relational_widgets.js:328
#, python-format
msgid "Render"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:389
#, python-format
msgid "Resource Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_common.js:866
#: code:addons/web/static/src/xml/base.xml:422
#: code:addons/web/static/src/xml/base.xml:506
#: code:addons/web/static/src/xml/base.xml:1249
#, python-format
msgid "Save"
msgstr "Запазване"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_common.js:881
#, python-format
msgid "Save & New"
msgstr "Запази и Нов"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1010
#: code:addons/web/static/src/xml/base.xml:1012
#, python-format
msgid "Save As"
msgstr "Запиши като"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:1235
#, python-format
msgid "Save As..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1394
#, python-format
msgid "Save as:"
msgstr "Запис като:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1241
#, python-format
msgid "Save current search"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_view.js:1195
#, python-format
msgid "Save default"
msgstr "Запази по подразбиране"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1327
#, python-format
msgid "Save fields list"
msgstr "Запазване списък с полета"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1400
#, python-format
msgid "Saved exports:"
msgstr "Записани експорти:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_inputs.js:359
#, python-format
msgid "Search %(field)s at: %(value)s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_inputs.js:191
#: code:addons/web/static/src/js/views/search_inputs.js:210
#: code:addons/web/static/src/js/views/search_inputs.js:401
#, python-format
msgid "Search %(field)s for: %(value)s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1117
#, python-format
msgid "Search Again"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_common.js:211
#, python-format
msgid "Search More..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_common.js:281
#, python-format
msgid "Search: "
msgstr "Търсене: "

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:469
#, python-format
msgid "See selection "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_common.js:1039
#: code:addons/web/static/src/xml/base.xml:1004
#, python-format
msgid "Select"
msgstr "Избор"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Select <i class=\"fa fa-database\"/>"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:493
#, python-format
msgid "Select records..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_common.js:1074
#, python-format
msgid "Selected domain"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:493
#, python-format
msgid "Selected records"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:83
#, python-format
msgid "Selection:"
msgstr "Избор:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_view.js:1192
#, python-format
msgid "Set Default"
msgstr "Установяване по подразбиране"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:211
#, python-format
msgid "Set Defaults"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list_view.js:1017
#, python-format
msgid "Setting 'id' attribute on existing record %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1247
#, python-format
msgid "Share with all users"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/apps.js:189
#, python-format
msgid "Showing locally available modules"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:55
#, python-format
msgid "Size:"
msgstr "Размер:"

#. module: web
#: code:addons/web/controllers/main.py:1062
#, python-format
msgid "Something horrible happened"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:107
#, python-format
msgid "Special:"
msgstr "Специален:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/misc.js:14
#, python-format
msgid "Still loading..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/misc.js:15
#, python-format
msgid "Still loading...<br />Please be patient."
msgstr "Зареждането продължава.. <br /> Моля, бъдете търпеливи."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:83
#, python-format
msgid "Support"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:701
#, python-format
msgid "Switch Off"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:703
#, python-format
msgid "Switch On"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:697
#, python-format
msgid "Switch to production environment"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:695
#, python-format
msgid "Switch to test environment"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/misc.js:18
#, python-format
msgid "Take a minute to get a coffee,<br />because it's loading..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:218
#, python-format
msgid "Technical Translation"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:696
#, python-format
msgid "Test Environment"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:462
#, python-format
msgid "The domain is wrong."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:1235
#, python-format
msgid "The field is empty, there's nothing to save !"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_view.js:954
#, python-format
msgid "The following fields are invalid:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list_view_editable.js:124
#, python-format
msgid ""
"The line has been modified, your changes will be discarded. Are you sure you "
"want to discard the changes ?"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:750
#, python-format
msgid "The new password and its confirmation must be identical."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_relational_widgets.js:1126
#, python-format
msgid "The o2m record must be saved before an action can be used"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:756
#, python-format
msgid ""
"The old password you provided is incorrect, your password was not changed."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_view.js:361
#, python-format
msgid "The record could not be found in the database."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_view.js:825
#, python-format
msgid ""
"The record has been modified, your changes will be discarded. Are you sure "
"you want to leave this page ?"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:1199
#, python-format
msgid "The selected file exceed the maximum file size of %s."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_relational_widgets.js:1550
#, python-format
msgid ""
"The type of the field '%s' must be a many2many field with a relation to 'ir."
"attachment' model."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:1220
#, python-format
msgid "There was a problem while uploading your file"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_menus.js:242
#, python-format
msgid ""
"This filter is global and will be removed for everybody if you continue."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:389
#, python-format
msgid "This resource is empty"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1304
#, python-format
msgid ""
"This wizard will export all data that matches the current search criteria to "
"a CSV file.\n"
"            You can export all data or only the fields that can be "
"reimported after modification."
msgstr ""
"Този съветник ще експортира всички данни по избрания критерии в CSV файл.\n"
"            Вие можете да експортирате всички данни или само полетата които "
"са реимпортирани след модификация."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:971
#, python-format
msgid ""
"Timezone Mismatch : The timezone of your browser doesn't match the selected "
"one. The time in Odoo is displayed according to your field timezone."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:210
#, python-format
msgid "Toggle Form Layout Outline"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/pivot_view.js:516
#: code:addons/web/static/src/js/views/pivot_view.js:545
#, python-format
msgid "Total"
msgstr "Общо"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/web_client.js:134
#, python-format
msgid "Traceback:"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/tree_view.js:18
#, python-format
msgid "Tree"
msgstr "Дървовиден преглед"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/crash_manager.js:48
#, python-format
msgid "Trying to reconnect... "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:47
#, python-format
msgid "Type:"
msgstr "Тип:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:691
#: code:addons/web/static/src/js/views/list_view.js:308
#, python-format
msgid "Unarchive"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/graph_widget.js:79
#: code:addons/web/static/src/js/views/list_view.js:1471
#: code:addons/web/static/src/js/views/list_view.js:1477
#: code:addons/web/static/src/js/views/list_view.js:1489
#: code:addons/web/static/src/js/views/pivot_view.js:498
#, python-format
msgid "Undefined"
msgstr "Неопределен"

#. module: web
#: code:addons/web/controllers/main.py:877
#, python-format
msgid "Underscore prefixed methods cannot be remotely called"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:699
#, python-format
msgid "Unhandled widget"
msgstr "Неподдържана приставка"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:911
#, python-format
msgid "Unknown"
msgstr "Неизвестно"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/data.js:1082
#, python-format
msgid "Unknown field %s in domain %s"
msgstr "Непознато поле %s в домейн %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list_view.js:1171
#, python-format
msgid "Unknown m2m command %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/pyeval.js:961
#, python-format
msgid "Unknown nonliteral type "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/data.js:1074
#, python-format
msgid "Unknown operator %s in domain %s"
msgstr "Непознат оператор %s в домейн %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list_view.js:367
#, python-format
msgid "Unlimited"
msgstr "Неограничен"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/data.js:1120
#, python-format
msgid "Unsupported operator %s in domain %s"
msgstr "Неподдържан оператор %s в домейн %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1505
#, python-format
msgid "Update translations"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_widgets.js:1700
#, python-format
msgid "Upgrade now"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1518
#, python-format
msgid "Upgrade to future versions"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:966
#: code:addons/web/static/src/xml/base.xml:1027
#, python-format
msgid "Uploading ..."
msgstr "Качване(зареждане) ..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_relational_widgets.js:1650
#: code:addons/web/static/src/js/widgets/sidebar.js:168
#, python-format
msgid "Uploading Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/sidebar.js:196
#, python-format
msgid "Uploading..."
msgstr "Качване(зареждане) ..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1244
#, python-format
msgid "Use by default"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/crash_manager.js:23
#, python-format
msgid "Validation Error"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:105
#, python-format
msgid "Version"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/priority.js:51
#, python-format
msgid "Very High"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list_view.js:2033
#: code:addons/web/static/src/xml/base.xml:219
#, python-format
msgid "View"
msgstr "Изглед"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:214
#, python-format
msgid "View Fields"
msgstr "Изглед Полета"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:208
#, python-format
msgid "View Metadata"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_relational_widgets.js:757
#, python-format
msgid "View type '%s' is not supported in X2Many."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/crash_manager.js:19
#: code:addons/web/static/src/js/framework/crash_manager.js:20
#: code:addons/web/static/src/js/framework/crash_manager.js:119
#: code:addons/web/static/src/js/views/form_view.js:830
#: code:addons/web/static/src/js/views/list_view.js:760
#: code:addons/web/static/src/js/widgets/sidebar.js:126
#, python-format
msgid "Warning"
msgstr "Предупреждение"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:674
#, python-format
msgid "Week"
msgstr "Седмица"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_view.js:1362
#, python-format
msgid "Widget type '%s' is not implemented"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base_common.xml:51
#, python-format
msgid "Widget:"
msgstr "Приставка:"

#. module: web
#: code:addons/web/controllers/main.py:491
#, python-format
msgid "Wrong login/password"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/progress_bar.js:79
#, python-format
msgid "Wrong value entered!"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:236
#, python-format
msgid "XML ID:"
msgstr "XML ID:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:677
#, python-format
msgid "Year"
msgstr "Година"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_inputs.js:329
#: code:addons/web/static/src/xml/base_common.xml:71
#, python-format
msgid "Yes"
msgstr "Да"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/crash_manager.js:53
#, python-format
msgid "You are back online"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/form_relational_widgets.js:49
#, python-format
msgid "You are creating a new %s, are you sure it does not exist yet?"
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:748
#, python-format
msgid "You cannot leave any password empty."
msgstr "Не може да оставите полето за парола празно."

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1503
#, python-format
msgid "You have updated"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/misc.js:17
#, python-format
msgid "You may not believe it,<br />but the application is actually loading..."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/widgets/sidebar.js:126
#, python-format
msgid "You must choose at least one record."
msgstr "Трябва да изберете поне един запис."

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/list_view.js:760
#, python-format
msgid "You must select at least one record."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/crash_manager.js:69
#, python-format
msgid "Your Odoo session expired. Please refresh the current web page."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/core.js:133
#, python-format
msgid "a day ago"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/core.js:129
#, python-format
msgid "about a minute ago"
msgstr "преди около минута"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/core.js:135
#, python-format
msgid "about a month ago"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/core.js:137
#, python-format
msgid "about a year ago"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/core.js:131
#, python-format
msgid "about an hour ago"
msgstr "преди около час"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_filters.js:160
#, python-format
msgid "contains"
msgstr "съдържа"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_filters.js:161
#, python-format
msgid "doesn't contain"
msgstr "не съдържа"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_filters.js:177
#: code:addons/web/static/src/js/views/search_filters.js:211
#: code:addons/web/static/src/js/views/search_filters.js:240
#, python-format
msgid "greater than"
msgstr "по-голямо от"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_filters.js:179
#: code:addons/web/static/src/js/views/search_filters.js:213
#: code:addons/web/static/src/js/views/search_filters.js:242
#, python-format
msgid "greater than or equal to"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_filters.js:232
#: code:addons/web/static/src/js/views/search_filters.js:267
#, python-format
msgid "is"
msgstr "е"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_filters.js:162
#: code:addons/web/static/src/js/views/search_filters.js:175
#: code:addons/web/static/src/js/views/search_filters.js:209
#: code:addons/web/static/src/js/views/search_filters.js:238
#, python-format
msgid "is equal to"
msgstr "е равно на"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_filters.js:286
#, python-format
msgid "is false"
msgstr "е грешно"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_filters.js:268
#, python-format
msgid "is not"
msgstr "не е"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_filters.js:163
#: code:addons/web/static/src/js/views/search_filters.js:176
#: code:addons/web/static/src/js/views/search_filters.js:210
#: code:addons/web/static/src/js/views/search_filters.js:239
#, python-format
msgid "is not equal to"
msgstr "не е равно на"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_filters.js:165
#: code:addons/web/static/src/js/views/search_filters.js:182
#: code:addons/web/static/src/js/views/search_filters.js:216
#: code:addons/web/static/src/js/views/search_filters.js:245
#: code:addons/web/static/src/js/views/search_filters.js:270
#, python-format
msgid "is not set"
msgstr "не е установен"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_filters.js:164
#: code:addons/web/static/src/js/views/search_filters.js:181
#: code:addons/web/static/src/js/views/search_filters.js:215
#: code:addons/web/static/src/js/views/search_filters.js:244
#: code:addons/web/static/src/js/views/search_filters.js:269
#, python-format
msgid "is set"
msgstr "е установен"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_filters.js:285
#, python-format
msgid "is true"
msgstr "е вярно"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/utils.js:303
#, python-format
msgid "kMGTPE"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_filters.js:178
#: code:addons/web/static/src/js/views/search_filters.js:212
#: code:addons/web/static/src/js/views/search_filters.js:241
#, python-format
msgid "less than"
msgstr "по-малко от"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/framework/core.js:128
#, python-format
msgid "less than a minute ago"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_filters.js:180
#: code:addons/web/static/src/js/views/search_filters.js:214
#: code:addons/web/static/src/js/views/search_filters.js:243
#, python-format
msgid "less than or equal to"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_inputs.js:229
#, python-format
msgid "not a valid integer"
msgstr "не е валидно цяло число"

#. module: web
#. openerp-web
#: code:addons/web/static/src/js/views/search_inputs.js:244
#, python-format
msgid "not a valid number"
msgstr "не е валиден номер(брой)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/xml/base.xml:1254
#: code:addons/web/static/src/xml/website.tour.xml:14
#, python-format
msgid "or"
msgstr "или"

#~ msgid "&lt;!DOCTYPE html&gt;"
#~ msgstr "&lt;!DOCTYPE html&gt;"

#~ msgid "Quantity"
#~ msgstr "Количество"
