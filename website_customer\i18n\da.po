# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_customer
# 
# Translators:
# <PERSON>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON>, 2021\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_tag_list
msgid "<span class=\"fa fa-1x fa-tags\"/> All"
msgstr "<span class=\"fa fa-1x fa-tags\"/> Alle"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country
msgid ""
"<span class=\"fa fa-external-link\" role=\"img\" aria-label=\"External "
"link\" title=\"External link\"/>"
msgstr ""
"<span class=\"fa fa-external-link\" role=\"img\" aria-label=\"External "
"link\" title=\"External link\"/>"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__active
msgid "Active"
msgstr "Aktiv"

#. module: website_customer
#: code:addons/website_customer/controllers/main.py:0
#, python-format
msgid "All Countries"
msgstr "Alle lande"

#. module: website_customer
#: code:addons/website_customer/controllers/main.py:0
#, python-format
msgid "All Industries"
msgstr "Alle Brancher"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.res_partner_tag_view_search
msgid "Archived"
msgstr "Arkiveret"

#. module: website_customer
#: model:ir.model.fields,help:website_customer.field_res_partner_tag__classname
msgid "Bootstrap class to customize the color"
msgstr "Bootstrap class til at tilpasse farven"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__can_publish
msgid "Can Publish"
msgstr "Kan udgives "

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__name
msgid "Category Name"
msgstr "Kategorinavn"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__classname
msgid "Class"
msgstr "Class"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country
msgid "Close"
msgstr "Luk"

#. module: website_customer
#: model:ir.model,name:website_customer.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: website_customer
#: model_terms:ir.actions.act_window,help:website_customer.action_partner_tag_form
msgid "Create a new contact tag"
msgstr "Opret et nyt kontakt tag"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__create_uid
msgid "Created by"
msgstr "Oprettet af"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__create_date
msgid "Created on"
msgstr "Oprettet den"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__display_name
msgid "Display Name"
msgstr "Vis navn"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__id
msgid "ID"
msgstr "ID"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.implemented_by_block
msgid "Implemented By"
msgstr "Implementeret af"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__is_published
msgid "Is Published"
msgstr "Er udgivet"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag____last_update
msgid "Last Modified on"
msgstr "Sidst ændret den"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__write_uid
msgid "Last Updated by"
msgstr "Sidst opdateret af"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__write_date
msgid "Last Updated on"
msgstr "Sidst opdateret den"

#. module: website_customer
#: model_terms:ir.actions.act_window,help:website_customer.action_partner_tag_form
msgid ""
"Manage contact tags to better classify them for tracking and analysis "
"purposes."
msgstr ""
"Håndter kontakt tags for bedre at klassificere dem til registrerings- og "
"analyseformål."

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "No result found"
msgstr "Ingen resultater fundet"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.details
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Our References"
msgstr "Vores referencer"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.view_partner_tag_form
msgid "Partner Tag"
msgstr "Partner Tag"

#. module: website_customer
#: model:ir.model,name:website_customer.model_res_partner_tag
msgid ""
"Partner Tags - These tags can be used on website to find customers by "
"sector, or ..."
msgstr ""
"Partner tags - Disse tags kan bruges på hjemmesiden til at finde kunder "
"efter sektor, eller ..."

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__partner_ids
msgid "Partners"
msgstr "Partnere"

#. module: website_customer
#: code:addons/website_customer/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website_customer.references_block
#, python-format
msgid "References"
msgstr "Reference"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country_list
msgid "References by Country"
msgstr "Referencer pr. land"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_industry_list
msgid "References by Industries"
msgstr "Referencer per Branche"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_tag_list
msgid "References by Tag"
msgstr "Referencer pr. tag"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Search"
msgstr "Søg"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.res_partner_tag_view_search
msgid "Search Partner Tag"
msgstr "Søg partner tag"

#. module: website_customer
#: model:ir.model.fields,help:website_customer.field_res_partner_tag__website_url
msgid "The full URL to access the document through the website."
msgstr "Den fulde URL for at få adgang til dokumentet via hjemmesiden."

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Trusted by millions worldwide"
msgstr "Betroet af millioner over hele verden"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__website_published
msgid "Visible on current website"
msgstr "Synlig på denne hjemmeside"

#. module: website_customer
#: model:ir.model,name:website_customer.model_website
msgid "Website"
msgstr "Hjemmeside"

#. module: website_customer
#: model:ir.actions.act_window,name:website_customer.action_partner_tag_form
#: model:ir.ui.menu,name:website_customer.menu_partner_tag_form
#: model_terms:ir.ui.view,arch_db:website_customer.view_partner_tag_list
#: model_terms:ir.ui.view,arch_db:website_customer.view_partners_form_website
msgid "Website Tags"
msgstr "Website tags"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__website_url
msgid "Website URL"
msgstr "Hjemmeside URL"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner__website_tag_ids
#: model:ir.model.fields,field_description:website_customer.field_res_users__website_tag_ids
msgid "Website tags"
msgstr "Website tags"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country
msgid "World Map"
msgstr "Verdenskort"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.implemented_by_block
msgid "reference(s))"
msgstr "Reference(r))"
