# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * pos_mercury
#
# Translators:
# <PERSON> <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:06+0000\n"
"PO-Revision-Date: 2016-01-27 10:49+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: English (United Kingdom) (http://www.transifex.com/odoo/"
"odoo-9/language/en_GB/)\n"
"Language: en_GB\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/pos_mercury.xml:75
#, python-format
msgid "&nbsp;&nbsp;APPROVAL CODE:"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:186
#, python-format
msgid "128 bit CryptoAPI failed"
msgstr ""

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"<i>Mercury Configurations</i> define what Mercury account will be used when\n"
"                                processing credit card transactions in the "
"Point Of Sale. Setting up a Mercury\n"
"                                configuration will enable you to allow "
"payments with various credit cards\n"
"                                (eg. Visa, MasterCard, Discovery, American "
"Express, ...). After setting up this\n"
"                                configuration you should associate it with a "
"Point Of Sale payment method."
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/pos_mercury.xml:46
#, python-format
msgid "ABOVE AMOUNT PURSUANT"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/pos_mercury.xml:60
#, python-format
msgid "APPROVAL CODE:"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:190
#, python-format
msgid "All Connections Failed"
msgstr ""

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.mercury_return
#: model_terms:ir.ui.view,arch_db:pos_mercury.mercury_transaction
msgid "Allow"
msgstr ""

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Bank Statement Line"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/pos_mercury.xml:45
#, python-format
msgid "CARDHOLDER WILL PAY CARD ISSUER"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_account_bank_statement_line_mercury_card_brand
msgid "Card Brand"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_account_bank_statement_line_mercury_card_number
#: model:ir.model.fields,field_description:pos_mercury.field_account_bank_statement_line_mercury_prefixed_card_number
msgid "Card Number"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_account_bank_statement_line_mercury_card_owner_name
msgid "Card Owner Name"
msgstr ""

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_tree
msgid "Card Reader"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:196
#, python-format
msgid "Clear Text Request Not Supported"
msgstr ""

#. module: pos_mercury
#: model_terms:ir.actions.act_window,help:pos_mercury.action_configuration_form
msgid "Click to configure your card reader."
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:164
#, python-format
msgid "Connect Canceled"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:161
#, python-format
msgid "Connection Lost"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:185
#, python-format
msgid "Control failed to find branded serial (password lookup failed)"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:202
#, python-format
msgid "Could Not Encrypt Response- Call Provider"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration_create_uid
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction_create_uid
msgid "Created by"
msgstr "Created by"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration_create_date
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction_create_date
msgid "Created on"
msgstr "Created on"

#. module: pos_mercury
#: code:addons/pos_mercury/models/pos_mercury.py:17
#, python-format
msgid "Credit Card"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:178
#, python-format
msgid "Disconnecting Socket"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration_display_name
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction_display_name
msgid "Display Name"
msgstr "Display Name"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:180
#, python-format
msgid "Duplicate Serial Number Detected"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/pos_mercury.xml:7
#, python-format
msgid "Electronic Payment"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:173
#, python-format
msgid "Empty Command String"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:198
#, python-format
msgid "Error Occurred While Decrypting Request"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:188
#, python-format
msgid "Failed to start Event Thread."
msgstr ""

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"For quickly handling orders: just swiping a credit card when on the payment "
"screen\n"
"                                (without having pressed anything else) will "
"charge the full amount of the order to\n"
"                                the card."
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:170
#, python-format
msgid "General Failure"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:155
#, python-format
msgid "Global API Not Initialized"
msgstr "Global API Not Initialised"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:192
#, python-format
msgid "Global Response Length Error (Too Short)"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:194
#, python-format
msgid "Global String Error"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:262
#, python-format
msgid "Go to payment screen to use cards"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:393
#, python-format
msgid "Handling transaction..."
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration_id
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction_id
msgid "ID"
msgstr "ID"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_mercury_configuration_merchant_id
msgid "ID of the merchant to authenticate him on the payment provider server"
msgstr ""

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"If you don't already have a Mercury account, contact Mercury at +1 (800) "
"846-4472\n"
"                                to create one."
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:184
#, python-format
msgid "In Process with server"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:165
#, python-format
msgid "Initialize Failed"
msgstr "Initialise Failed"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:172
#, python-format
msgid "Insufficient Fields"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:183
#, python-format
msgid "Internal Server Error – Call Provider"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:206
#, python-format
msgid "Invalid Account Number"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:210
#, python-format
msgid "Invalid Authorization Amount"
msgstr "Invalid Authorisation Amount"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:208
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:209
#, python-format
msgid "Invalid Authorization Code"
msgstr "Invalid Authorisation Code"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:225
#, python-format
msgid "Invalid Batch Item Count"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:224
#, python-format
msgid "Invalid Batch Number"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:230
#, python-format
msgid "Invalid Card Type"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:211
#, python-format
msgid "Invalid Cash Back Amount"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:199
#, python-format
msgid "Invalid Check Digit"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:219
#, python-format
msgid "Invalid Check Type"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:171
#, python-format
msgid "Invalid Command Format"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:218
#, python-format
msgid "Invalid Date of Birth"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:216
#, python-format
msgid "Invalid Derived Key Data"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:227
#, python-format
msgid "Invalid Driver’s License"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:207
#, python-format
msgid "Invalid Expiration Date"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:212
#, python-format
msgid "Invalid Gratuity Amount"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:226
#, python-format
msgid "Invalid MICR Input Type"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:214
#, python-format
msgid "Invalid Magnetic Stripe Data"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:205
#, python-format
msgid "Invalid Memo"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:222
#, python-format
msgid "Invalid Merchant ID"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:204
#, python-format
msgid "Invalid Operator ID"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:215
#, python-format
msgid "Invalid PIN Block Data"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:229
#, python-format
msgid "Invalid Pass Data"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:213
#, python-format
msgid "Invalid Purchase Amount"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:220
#, python-format
msgid "Invalid Routing Number"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:228
#, python-format
msgid "Invalid Sequence Number"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:217
#, python-format
msgid "Invalid State Code"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:223
#, python-format
msgid "Invalid TStream Type"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:221
#, python-format
msgid "Invalid TranCode"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:203
#, python-format
msgid "Invalid Transaction Type"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_account_bank_statement_line_mercury_invoice_no
msgid "Invoice number from Mercury Pay"
msgstr ""

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_account_journal
msgid "Journal"
msgstr "Journal"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration___last_update
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction___last_update
msgid "Last Modified on"
msgstr "Last Modified on"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration_write_uid
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction_write_uid
msgid "Last Updated by"
msgstr "Last Updated by"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration_write_date
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction_write_date
msgid "Last Updated on"
msgstr "Last Updated on"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.mercury_transaction
msgid "MagneSafe"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration_merchant_id
msgid "Merchant ID"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:200
#, python-format
msgid "Merchant ID Missing"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration_merchant_pwd
msgid "Merchant Password"
msgstr ""

#. module: pos_mercury
#: model:ir.actions.act_window,name:pos_mercury.action_configuration_form
#: model:ir.ui.menu,name:pos_mercury.menu_pos_pos_mercury_config
msgid "Mercury Configurations"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_account_journal_pos_mercury_config_id
msgid "Mercury configuration"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_account_bank_statement_line_mercury_invoice_no
msgid "Mercury invoice number"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_account_bank_statement_line_mercury_record_no
msgid "Mercury record number"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_account_bank_statement_line_mercury_ref_no
msgid "Mercury reference number"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration_name
msgid "Name"
msgstr "Name"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_mercury_configuration_name
msgid "Name of this Mercury configuration"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:334
#, python-format
msgid "No response from Mercury (Mercury down?)"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:336
#, python-format
msgid "No response from server (connected to network?)"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:414
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:562
#, python-format
msgid "Odoo error while processing transaction."
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:639
#, python-format
msgid "One credit card swipe already pending."
msgstr ""

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.mercury_common
msgid "OneTime"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:460
#, python-format
msgid "Partially approved"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:181
#, python-format
msgid "Password Failed (Client / Server)"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:176
#, python-format
msgid "Password Failed – Disconnecting"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:174
#, python-format
msgid "Password Verified"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:182
#, python-format
msgid "Password failed (Challenge / Response)"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_mercury_configuration_merchant_pwd
msgid ""
"Password of the merchant to authenticate him on the payment provider server"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_account_bank_statement_line_mercury_record_no
msgid "Payment record number from Mercury Pay"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_account_bank_statement_line_mercury_ref_no
msgid "Payment reference number from Mercury Pay"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:407
#, python-format
msgid "Please setup your Mercury merchant account."
msgstr ""

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_order
msgid "Point of Sale"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:175
#, python-format
msgid "Queue Full"
msgstr ""

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.mercury_transaction
msgid "RecordNumberRequested"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:179
#, python-format
msgid "Refused ‘Max Connections’"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:540
#, python-format
msgid "Reversal failed, sending VoidSale..."
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:576
#, python-format
msgid "Reversal succeeded"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:543
#, python-format
msgid "Sending reversal..."
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:191
#, python-format
msgid "Server Login Failed"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:160
#, python-format
msgid "Socket Connection Failed"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:159
#, python-format
msgid "Socket Creation Failed"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:157
#, python-format
msgid "Socket Error sending request"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:158
#, python-format
msgid "Socket already open or in use"
msgstr ""

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.mercury_transaction
msgid "Swiped"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:177
#, python-format
msgid "System Going Offline"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:162
#, python-format
msgid "TCP/IP Failed to Initialize"
msgstr "TCP/IP Failed to Initialise"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/pos_mercury.xml:47
#, python-format
msgid "TO CARDHOLDER AGREEMENT"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:201
#, python-format
msgid "TStream Type Missing"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_account_bank_statement_line_mercury_card_brand
msgid "The brand of the payment card (e.g. Visa, AMEX, ...)"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_account_bank_statement_line_mercury_prefixed_card_number
msgid "The card number used for the payment."
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_account_journal_pos_mercury_config_id
msgid "The configuration of Mercury used for this journal"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_account_bank_statement_line_mercury_card_number
msgid "The last 4 numbers of the card used to pay"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_account_bank_statement_line_mercury_card_owner_name
msgid "The name of the card owner"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:358
#, python-format
msgid ""
"This can be caused by a badly executed swipe or by not having your keyboard "
"layout set to US QWERTY (not US International)."
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:187
#, python-format
msgid "Threaded Auth Started Expect Response"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:163
#, python-format
msgid "Time Out waiting for server response"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:169
#, python-format
msgid "Timeout error"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:156
#, python-format
msgid "Timeout on Response"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:152
#, python-format
msgid "Transaction approved"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:193
#, python-format
msgid "Unable to Parse Response from Global (Indistinguishable)"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:166
#, python-format
msgid "Unknown Error"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:197
#, python-format
msgid "Unrecognized Request Format"
msgstr "Unrecognised Request Format"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"Using the Mercury integration in the Point Of Sale is easy: just press the\n"
"                                associated payment method. After that the "
"amount can be adjusted (eg. for cashback)\n"
"                                just like on any other payment line. "
"Whenever the payment line is set up, a card\n"
"                                can be swiped through the card reader device."
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:584
#, python-format
msgid "VoidSale succeeded"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/pos_mercury.xml:17
#, python-format
msgid "WAITING FOR SWIPE"
msgstr ""

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"We currently support the MagTek Dynamag card reader device. It can be "
"connected\n"
"                                directly to the Point Of Sale device or it "
"can be connected to the POSBox."
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:195
#, python-format
msgid "Weak Encryption Request Not Supported"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/pos_mercury.js:189
#, python-format
msgid "XML Parse Error"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/pos_mercury.xml:50
#, python-format
msgid "X______________________________"
msgstr ""

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_barcode_rule
msgid "barcode.rule"
msgstr ""

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_mercury_configuration
msgid "pos_mercury.configuration"
msgstr ""

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_mercury_mercury_transaction
msgid "pos_mercury.mercury_transaction"
msgstr ""
