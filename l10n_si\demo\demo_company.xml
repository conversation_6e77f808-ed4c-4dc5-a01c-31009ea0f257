<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_si" model="res.partner">
        <field name="name">SI Company</field>
        <field name="vat">SI33331464</field>
        <field name="street">39 Jamova cesta</field>
        <field name="city">Ljubljana</field>
        <field name="country_id" ref="base.si"/>
        
        <field name="zip">1102</field>
        <field name="phone">+386 31 234 567</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.siexample.com</field>
    </record>

    <record id="demo_company_si" model="res.company">
        <field name="name">SI Company</field>
        <field name="partner_id" ref="partner_demo_company_si"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_si')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_si.demo_company_si'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_si.gd_chart')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_si.demo_company_si')"/>
    </function>
</odoo>
