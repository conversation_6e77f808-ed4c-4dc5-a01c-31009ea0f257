# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_mercury
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:18+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.res_config_settings_view_form_inherit_pos_mercury
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>Buy a card reader"
msgstr "<i class=\"fa fa-fw fa-arrow-right\"/>شراء قارئ بطاقات"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"<i>Vantiv Configurations</i> define what Vantiv account will be used when\n"
"                                processing credit card transactions in the Point Of Sale. Setting up a Vantiv\n"
"                                configuration will enable you to allow payments with various credit cards\n"
"                                (eg. Visa, MasterCard, Discovery, American Express, ...). After setting up this\n"
"                                configuration you should associate it with a Point Of Sale payment method."
msgstr ""
"<i>تهيئات Vantiv</i> تحديد أي حسابات Vantiv سوف يتم استخدامها عند\n"
"                                معالجة معاملات البطاقة الائتمانية في نقطة البيع. ضبط تهيئة Vantiv\n"
"                                سيمكنك من السماح بالمدفوعات عن طريق مختلف البطاقات الائتمانية\n"
"                                (مثال: Visa ،MasterCard ،Discovery ،American Express، ...). بعد ضبط\n"
"                                هذه التهيئة، يجب ربطها بطريقة الدفع في نقطة البيع. "

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "ABOVE AMOUNT PURSUANT"
msgstr "وفقاً للمبلغ أعلاه "

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "APPROVAL CODE:"
msgstr "رمز الموافقة: "

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_barcode_rule
msgid "Barcode Rule"
msgstr "قاعدة الباركود"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "CARDHOLDER WILL PAY CARD ISSUER"
msgstr "سيدفع حامل البطاقة للجهة المصدرة للبطاقة "

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentTransactionPopup.js:0
#, python-format
msgid "Cancel"
msgstr "إلغاء "

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_card_brand
msgid "Card Brand"
msgstr "نوع البطاقة"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_card_number
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_order
msgid "Card Number"
msgstr "رقم البطاقة"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_prefixed_card_number
msgid "Card Number Prefix"
msgstr "بادئة رقم البطاقة"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_card_owner_name
msgid "Card Owner Name"
msgstr "اسم مالك البطاقة"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_tree
msgid "Card Reader"
msgstr "قارئ البطاقة "

#. module: pos_mercury
#: model_terms:ir.actions.act_window,help:pos_mercury.action_configuration_form
msgid "Configure your card reader"
msgstr "تهيئة قارئ بطاقاتك "

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Could not read card"
msgstr "لم نتمكن من قراءة البطاقة"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__create_uid
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__create_date
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: pos_mercury
#: model:ir.model.fields.selection,name:pos_mercury.selection__barcode_rule__type__credit
msgid "Credit Card"
msgstr "البطاقة الائتمانية"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid ""
"Credit card refunds are not supported. Instead select your credit card "
"payment method, click 'Validate' and refund the original charge manually "
"through the Vantiv backend."
msgstr ""
"عمليات استرداد الأموال في البطاقة الائتمانية غير مدعومة. بدلًا من ذلك، اختر "
"طريقة الدفع عن طريق البطاقة الائتمانية واضغط على 'تصديق' واسترد قيمة الرسوم "
"الأصلية يدوياً من خلال الواجهة الخلفية لـVantiv. "

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__display_name
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"For quickly handling orders: just swiping a credit card when on the payment screen\n"
"                                (without having pressed anything else) will charge the full amount of the order to\n"
"                                the card."
msgstr ""
"للتعامل مع الطلبات بسرعة: فقط تمرير البطاقة الائتمانية عندما تكون في شاشة الدفع\n"
"                                (دون الضغط على أي شيء) سيتم خصم إجمالي قيمة الطلب\n"
"                                من البطاقة. "

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/ProductScreen.js:0
#, python-format
msgid "Go to payment screen to use cards"
msgstr "اذهب إلى شاشة الدفع لاستخدام البطاقات "

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Handling transaction..."
msgstr "جاري إتمام المعاملة..."

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__id
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__id
msgid "ID"
msgstr "المُعرف"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_mercury_configuration__merchant_id
msgid "ID of the merchant to authenticate him on the payment provider server"
msgstr "معرف التاجر لمصادقته على خادم موفر الدفع"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"If you don't already have a Vantiv account, contact Vantiv at +1 (800) 846-4472\n"
"                                to create one."
msgstr ""
"إذا لم يكن لديك حساب Vantiv بالفعل، تواصل مع Vantiv على +1 (800) 846-4472\n"
"                                لإنشاء واحد. "

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_invoice_no
msgid "Invoice number from Vantiv Pay"
msgstr "رقم الفاتورة من Vantiv Pay "

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration____last_update
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__write_uid
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__write_date
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__merchant_id
msgid "Merchant ID"
msgstr "معرف التاجر"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__merchant_pwd
msgid "Merchant Password"
msgstr "كلمة مرور التاجر"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__name
msgid "Name"
msgstr "الاسم"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_mercury_configuration__name
msgid "Name of this Vantiv configuration"
msgstr "اسم تهيئة Vantiv هذه "

#. module: pos_mercury
#: code:addons/pos_mercury/models/pos_mercury_transaction.py:0
#, python-format
msgid "No Vantiv configuration associated with the payment method."
msgstr "لا توجد تهيئة Vantiv مرتبطة بطريقة الدفع. "

#. module: pos_mercury
#: code:addons/pos_mercury/models/pos_mercury_transaction.py:0
#, python-format
msgid "No opened point of sale session for user %s found."
msgstr "لم نتمكن من إيجاد جلسة نقطة بيع مفتوحة للمستخدم %s."

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "No response from Vantiv (Vantiv down?)"
msgstr "ليس هناك رد من Vantiv (Vantiv متوقف؟) "

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "No response from server (connected to network?)"
msgstr "ليس هناك رد من الخادم (هل أنت متصل بالشبكة؟) "

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Odoo error while processing transaction."
msgstr "حدث خطأ بأودو أثناء معالجة المعاملة."

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentTransactionPopup.js:0
#, python-format
msgid "Ok"
msgstr "موافق"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentTransactionPopup.js:0
#, python-format
msgid "Online Payment"
msgstr "الدفع عبر الإنترنت "

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Partially approved"
msgstr "تمت الموافقة جزئياً "

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_mercury_configuration__merchant_pwd
msgid ""
"Password of the merchant to authenticate him on the payment provider server"
msgstr "كلمة مرور التاجر المطلوبة لمصادقة اتصاله بخادم مزود الدفع "

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Pay with: "
msgstr "الدفع باستخدام: "

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_record_no
msgid "Payment record number from Vantiv Pay"
msgstr "رقم سجل الدفع من Vantiv Pay "

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_ref_no
msgid "Payment reference number from Vantiv Pay"
msgstr "رقم مرجع الدفع من Vantiv Pay "

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Please setup your Vantiv merchant account."
msgstr "يرجى إعداد حساب تاجر Vantiv الخاص بك. "

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_order
msgid "Point of Sale Orders"
msgstr "طلبات نقطة البيع "

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "طرق الدفع في نقطة البيع "

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_payment
msgid "Point of Sale Payments"
msgstr "مدفوعات نقطة البيع "

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_mercury_configuration
msgid "Point of Sale Vantiv Configuration"
msgstr "تهيئة Vantiv في نقطة البيع "

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_mercury_mercury_transaction
msgid "Point of Sale Vantiv Transaction"
msgstr "معاملة Vantiv في نقطة البيع "

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Refunds not supported"
msgstr "عمليات استرداد الأموال غير مدعومة "

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Reversal failed, sending VoidSale..."
msgstr "فشلت عملية العكس، جاري إرسال VoidSale... "

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Reversal succeeded"
msgstr "نجحت عملية العكس "

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Sending reversal..."
msgstr "جاري إرسال عملية العكس... "

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "TO CARDHOLDER AGREEMENT"
msgstr "لاتفاقية حامل البطاقة"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_card_brand
msgid "The brand of the payment card (e.g. Visa, AMEX, ...)"
msgstr "العلامة التجارية للبطاقة المستخدمة للدفع (مثال: Visa ،AMEX، ...) "

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_prefixed_card_number
msgid "The card number used for the payment."
msgstr "رقم البطاقة المستخدمة للدفع. "

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment_method__pos_mercury_config_id
msgid "The configuration of Vantiv used for this journal"
msgstr "تهيئة Vantiv المستخدمة في دفتر اليومية هذا "

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_card_number
msgid "The last 4 numbers of the card used to pay"
msgstr "آخر 4 أرقام من البطاقة المستخدمة للدفع "

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_card_owner_name
msgid "The name of the card owner"
msgstr "اسم مالك البطاقة"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid ""
"This can be caused by a badly executed swipe or by not having your keyboard "
"layout set to US QWERTY (not US International)."
msgstr ""
"يمكن أن يرجع هذا لتمرير بطاقة بشكل خاطئ أو بسبب اختيار نظام QWERTY الأمريكي "
"كمخطط لوحة مفاتيحك (لا النظام الأمريكي الدولي)."

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Transaction approved"
msgstr "تمت الموافقة على المعاملة "

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_barcode_rule__type
msgid "Type"
msgstr "النوع"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"Using the Vantiv integration in the Point Of Sale is easy: just press the\n"
"                                associated payment method. After that the amount can be adjusted (eg. for cashback)\n"
"                                just like on any other payment line. Whenever the payment line is set up, a card\n"
"                                can be swiped through the card reader device."
msgstr ""
"استخدام تكامل Vantiv في نقطة البيع أمر سهل جداً: فقط قم بالضغط على\n"
"                                طريقة الدفع المرتبطة. بعد ذلك، يمكن تعديل المبلغ (على سبيل المثال، استعادة المبلغ)\n"
"                                تماماً كأي بند دفع آخر. متى ما تم ضبط بند الدفع، سيكون بالإمكان تمرير\n"
"                                البطاقة عن طريق جهاز قراءة البطاقات. "

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.res_config_settings_view_form_inherit_pos_mercury
msgid "Vantiv Accounts"
msgstr "حسابات Vantiv"

#. module: pos_mercury
#: model:ir.actions.act_window,name:pos_mercury.action_configuration_form
#: model:ir.ui.menu,name:pos_mercury.menu_pos_pos_mercury_config
msgid "Vantiv Configurations"
msgstr "تهيئات Vantiv "

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment_method__pos_mercury_config_id
msgid "Vantiv Credentials"
msgstr "بيانات اعتماد Vantiv "

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_invoice_no
msgid "Vantiv invoice number"
msgstr "رقم فاتورة Vantiv "

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_record_no
msgid "Vantiv record number"
msgstr "رقم سجل Vantiv "

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_ref_no
msgid "Vantiv reference number"
msgstr "رقم مرجع Vantiv "

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "VoidSale succeeded"
msgstr "نجح VoidSale "

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/PaymentScreenPaymentLines.xml:0
#, python-format
msgid "WAITING FOR SWIPE"
msgstr "بانتظار تمرير البطاقة "

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"We currently support the MagTek Dynamag card reader device. It can be connected\n"
"                                directly to the Point Of Sale device or it can be connected to the IoTBox."
msgstr ""
"نحن ندعم حالياً جهاز قارئ البطاقات MagTek Dynamag. يمكن توصيله\n"
"                                مباشرةً بجهاز نقطة البيع أو بجهاز IoT. "

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "X______________________________"
msgstr "X______________________________"
