<?xml version="1.0"?>
<odoo>

    <record id="sukuk_page_view_tree" model="ir.ui.view">
        <field name="name">sukuk.management.page.tree</field>
        <field name="model">sukuk.management.page</field>
        <field name="arch" type="xml">
            <tree create="false" delete="false" edit="false">
<!--            <tree>-->
                <field name="state" widget="badge" decoration-info="state == 'draft'"
                       decoration-warning="state == 'cancel'"
                       decoration-success="state == 'confirmed'"/>
                <field name="account_no_id"/>
                <field name="bank_id"/>
                <field name="branch_id"/>
                <field name="wallet_name"/>
                <field name="suke_book_number"/>
                <field name="serial_no"/>
                <field name="user_id"/>
                <field name="person_signature"/>
                <field name="paid_to_bank_id"/>
                <field name="paid_to_branch_id"/>
                <field name="partner_paid_to"/>
                <field name="time_stamp"/>
                <field name="amount"/>
            </tree>
        </field>
    </record>

    <record id="sukuk_page_view_form" model="ir.ui.view">
        <field name="name">sukuk.management.page.form</field>
        <field name="model">sukuk.management.page</field>
        <field name="arch" type="xml">
            <form create="false" delete="false" edit="false">
                <header>
                    <button name="make_confirmed" type="object" string="تأكيد" class="oe_highlight"
                            attrs="{'invisible':['|',('state','in',('confirmed','cancel')),('person_signature_visible','=',False)]}"/>
                    <button name="make_draft" type="object" string="ارجاع كمسودة"
                            attrs="{'invisible':['|',('state','in',('confirmed','draft')),('person_signature_visible','=',False)]}"/>
                    <button name="make_cancel" type="object" string="الغاء"
                            attrs="{'invisible':['|',('state','=','cancel'),('person_signature_visible','=',False)]}"/>
                    <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h2>
                            <div class="oe_inline">
                                <field name="bank_id" class="oe_inline"/>
                                /
                                <field name="branch_id" class="oe_inline"/>
                            </div>

                        </h2>
                    </div>
                    <group>
                        <group string="معلومات الطلب">
                            <field name="person_signature_visible" invisible="1"/>
                            <field name="user_id"/>
                            <field name="person_signature"/>
                            <field name="time_stamp"/>
                        </group>
                        <group string="معلومات الصك">
                            <field name="account_no_id"/>
                            <field name="suke_book_number"/>
                            <field name="serial_no"/>
                            <field name="amount"/>
                        </group>
                        <group string="معلومات الجهة المدفوع لها">
                            <div class="oe_inline">
                                <field name="paid_to_bank_id" class="oe_inline" attrs="{'invisible':[('paid_to_bank_id','=',False)]}"/>
                                /
                                <field name="paid_to_branch_id" class="oe_inline" attrs="{'branch_id':[('paid_to_branch_id','=',False)]}"/>
                                <field name="partner_paid_to" class="oe_inline" attrs="{'invisible':[('partner_paid_to','=',False)]}"/>
                            </div>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>


    <record id="action_sukuk_page_view" model="ir.actions.act_window">
        <field name="name">صكوك صادرة</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">sukuk.management.page</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                There is nothing yet!
            </p>
        </field>
    </record>


    <menuitem id="sukuk_page_menu"
              name="صكوك صادرة"
              parent="sukuk_management_main_menu"
              action="action_sukuk_page_view"
              sequence="3"/>


</odoo>
